/* YS-API V3.0 智能日志系统样式 */

.smart-log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    border-bottom: 2px solid var(--border-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.log-status-info {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 14px;
}

.status-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.status-value {
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 13px;
}

.status-connected {
    background: rgba(74, 222, 128, 0.2);
    color: var(--success-color);
    border: 1px solid rgba(74, 222, 128, 0.3);
}

.status-disconnected {
    background: rgba(248, 113, 113, 0.2);
    color: var(--danger-color);
    border: 1px solid rgba(248, 113, 113, 0.3);
}

.status-connecting {
    background: rgba(251, 191, 36, 0.2);
    color: var(--warning-color);
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.status-degraded {
    background: rgba(251, 191, 36, 0.2);
    color: var(--warning-color);
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.message-count, .log-level {
    color: var(--text-secondary);
    font-size: 12px;
    padding: 2px 6px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.log-controls {
    display: flex;
    gap: 8px;
}

.log-controls .btn {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background: var(--secondary-color);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.log-controls .btn:hover {
    background: var(--secondary-hover);
    transform: translateY(-1px);
}

.log-controls .btn.btn-warning {
    background: var(--warning-color);
    color: #212529;
}

.log-controls .btn.btn-warning:hover {
    background: var(--warning-hover);
}

.log-controls .btn.btn-info {
    background: var(--primary-color);
    color: white;
}

.log-controls .btn.btn-info:hover {
    background: var(--primary-hover);
}

.log-controls .btn.btn-primary {
    background: var(--success-color);
    color: white;
}

.log-controls .btn.btn-primary:hover {
    background: var(--success-hover);
}

.smart-log-container {
    background: #0a0a0a;
    border: 2px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    height: 400px;
    overflow-y: auto;
    padding: 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    scroll-behavior: smooth;
    position: relative;
}

.smart-log-container::-webkit-scrollbar {
    width: 8px;
}

.smart-log-container::-webkit-scrollbar-track {
    background: #1a1a1a;
}

.smart-log-container::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

.smart-log-container::-webkit-scrollbar-thumb:hover {
    background: var(--primary-hover);
}

.log-entry {
    margin: 0;
    padding: 6px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    transition: background 0.2s ease;
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.log-entry:hover {
    background: rgba(255, 255, 255, 0.03);
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    color: var(--text-secondary);
    font-size: 11px;
    min-width: 80px;
    font-weight: 500;
    opacity: 0.8;
}

.log-message {
    flex: 1;
    word-break: break-word;
    line-height: 1.5;
}

/* 日志级别样式 */
.log-entry.log-info {
    color: var(--text-primary);
}

.log-entry.log-success {
    color: var(--success-color);
    background: rgba(74, 222, 128, 0.05);
}

.log-entry.log-warning {
    color: var(--warning-color);
    background: rgba(251, 191, 36, 0.05);
}

.log-entry.log-error {
    color: var(--danger-color);
    background: rgba(248, 113, 113, 0.05);
}

.log-entry.log-debug {
    color: var(--text-secondary);
    font-style: italic;
    opacity: 0.8;
}

/* 特殊状态样式 */
.log-entry.status-change {
    border-left: 3px solid var(--primary-color);
    background: rgba(96, 165, 250, 0.05);
}

.log-entry.connection-event {
    border-left: 3px solid var(--success-color);
}

.log-entry.sync-event {
    border-left: 3px solid var(--warning-color);
}

.log-entry.error-event {
    border-left: 3px solid var(--danger-color);
}

/* 动画效果 */
.log-entry.new-entry {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .smart-log-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .log-status-info {
        justify-content: space-between;
        flex-wrap: wrap;
    }
    
    .log-controls {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .log-controls .btn {
        flex: 1;
        min-width: 80px;
    }
    
    .log-time {
        min-width: 60px;
        font-size: 10px;
    }
    
    .smart-log-container {
        font-size: 12px;
    }
}

/* 深色主题优化 */
@media (prefers-color-scheme: dark) {
    .smart-log-container {
        background: #000000;
    }
    
    .log-entry:hover {
        background: rgba(255, 255, 255, 0.05);
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .log-entry {
        border-bottom: 2px solid var(--border-color);
    }
    
    .log-entry.log-error {
        background: rgba(248, 113, 113, 0.1);
    }
    
    .log-entry.log-warning {
        background: rgba(251, 191, 36, 0.1);
    }
    
    .log-entry.log-success {
        background: rgba(74, 222, 128, 0.1);
    }
}

/* 打印样式 */
@media print {
    .smart-log-header {
        display: none;
    }
    
    .smart-log-container {
        height: auto;
        border: none;
        background: white;
        color: black;
    }
    
    .log-entry {
        border-bottom: 1px solid #ccc;
        color: black;
    }
    
    .log-time {
        color: #666;
    }
} 