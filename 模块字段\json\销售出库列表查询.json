[{"success": true, "code": 200, "message": "", "data": {"fieldVersion": 20230210, "appCode": "", "tokenSet": false, "tokenDoc": "", "tenantId": 0, "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "id": "05a2a46b90984a8ba115d6a2061f8720", "name": "销售出库列表查询", "apiClassifyId": "c13e2ce641b8440ea7eba7d11ed46c52", "apiClassifyName": "销售出库单", "apiClassifyCode": "", "parentApiClassifies": "", "functionId": "", "openMode": 0, "description": "销售出库列表查询", "auth": true, "bodyPassthrough": false, "healthExam": false, "healthStatus": true, "responseResultPassthrough": false, "contentType": "application/json", "returnPassthrough": "", "completeProxyUrl": "/yonbip/scm/salesout/list", "connectUrl": "/bill/list", "sort": 20, "handler": "openapi", "httpRequestType": "POST", "openApi": true, "preset": false, "productId": "710a0be3edff4f9092e35f63fd3b9bae", "productCode": "scm", "proxyUrl": "/yonbip/scm/salesout/list", "requestParamsDemo": "Url: /yonbip/scm/salesout/list?access_token=访问令牌 Body: { \"pageIndex\": 1, \"code\": \"\", \"pageSize\": 10, \"vouchdate\": \"2021-04-19\", \"stockOrg\": [], \"salesOrg\": [], \"invoiceOrg\": [], \"invoiceCust\": [], \"upcode\": \"\", \"department\": [], \"operator\": [], \"warehouse\": [], \"stockMgr\": [], \"cust\": [], \"product_cName_ManageClass\":[], \"bustype.name\":\"\", \"isSum\": false, \"simpleVOs\": [ { \"op\": \"between\", \"value2\": \"2021-04-19 23:59:59\", \"value1\": \"2021-04-19 00:00:00\", \"field\": \"pubts\" } ] }", "requestProtocol": "HTTP", "serviceHttpMethod": "POST", "publishStatus": true, "approvalMsg": "", "rpcAppName": "", "rpcServiceName": "", "rpcMethodName": "", "rpcServiceUrl": "", "ma": false, "gmtCreate": "2020-01-16 18:32:32", "gmtUpdate": "2025-02-12 10:31:26.000", "address": "https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/salesout/list", "productName": "采购供应", "productClassifyId": "yonsuite", "productClassifyCode": "yonbip", "productClassifyName": "用友 YonBIP", "paramDTOS": {"paramDTOS": [{"id": 2200117965505953805, "name": "isdefault", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": 1856804444898852873, "array": false, "paramDesc": "该参数可忽略不管", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953806, "name": "pageIndex", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": 1856804444898852874, "array": false, "paramDesc": "页号", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": 1, "required": true, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953807, "name": "code", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": 1856804444898852875, "array": false, "paramDesc": "单据编号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953808, "name": "pageSize", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": 1856804444898852876, "array": false, "paramDesc": "每页行数", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": 10, "required": true, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953809, "name": "vouchdate", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": 1856804444898852877, "array": false, "paramDesc": "单据日期 区间格式，2021-05-06|2021-05-06 23:00:00。若传入单个时间如：2021-05-06，则查询该时间之后，到当前时间之间的单据", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953810, "name": "stockOrg", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": 1856804444898852878, "array": false, "paramDesc": "库存组织id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953811, "name": "salesOrg", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": 1856804444898852879, "array": false, "paramDesc": "销售组织id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953812, "name": "invoiceOrg", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": 1856804444898852880, "array": false, "paramDesc": "开票组织ID", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953813, "name": "invoiceCust", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": 1856804444898852881, "array": false, "paramDesc": "开票客户id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953814, "name": "upcode", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": 1856804444898852882, "array": false, "paramDesc": "来源单据号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953815, "name": "department", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": 1856804444898852883, "array": false, "paramDesc": "部门id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953816, "name": "operator", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": 1856804444898852884, "array": false, "paramDesc": "业务员id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953817, "name": "warehouse", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": 1856804444898852885, "array": false, "paramDesc": "仓库id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953818, "name": "stockMgr", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": 1856804444898852886, "array": false, "paramDesc": "库管员id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 13, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953819, "name": "cust", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": 1856804444898852887, "array": false, "paramDesc": "客户id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 14, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953820, "name": "product_cName", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": 1856804444898852888, "array": false, "paramDesc": "物料id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 15, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953821, "name": "bustype.name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": 1856804444898852889, "array": false, "paramDesc": "交易类型名称", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 16, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953822, "name": "product_cName_ManageClass", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": 1856804444898852890, "array": false, "paramDesc": "物料分类id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 17, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953823, "name": "isSum", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": 1856804444898852891, "array": false, "paramDesc": "查询表头", "paramType": "boolean", "requestParamType": "BodyParam", "path": "", "example": false, "fullName": "", "ytenantId": "", "paramOrder": 18, "bizType": "", "baseType": true, "defaultValue": false, "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953800, "name": "simpleVOs", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": {"children": [{"id": 2200117965505953801, "name": "op", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953800, "defParamId": 1856804444898852893, "array": false, "paramDesc": "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953802, "name": "value2", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953800, "defParamId": 1856804444898852894, "array": false, "paramDesc": "值2(条件)如：\"2021-04-19 23:59:59\"", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953803, "name": "value1", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953800, "defParamId": 1856804444898852895, "array": false, "paramDesc": "值1(条件)如： \"2021-04-19 00:00:00\"", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200117965505953804, "name": "field", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953800, "defParamId": 1856804444898852896, "array": false, "paramDesc": "属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码details.product.cCode、表头自定义项headItem.define1、表体自定义项details.bodyItem.define1等)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "defParamId": 1856804444898852892, "array": true, "paramDesc": "扩展查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 19, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "queryParamDTOS": "", "ysApi": false, "presetTokenApi": false, "applyFlag": false, "cover": false, "paramMapDTOS": {"paramMapDTOS": [{"id": 2200117965505953829, "name": "isdefault", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": "", "array": false, "paramDesc": "该参数可忽略不管", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "isdefault", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953830, "name": "pageIndex", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": "", "array": false, "paramDesc": "页号", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "pageIndex", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953831, "name": "code", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": "", "array": false, "paramDesc": "单据编号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "code", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953832, "name": "pageSize", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": "", "array": false, "paramDesc": "每页行数", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "pageSize", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953833, "name": "vouchdate", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": "", "array": false, "paramDesc": "单据日期 区间格式，2021-05-06|2021-05-06 23:00:00。若传入单个时间如：2021-05-06，则查询该时间之后，到当前时间之间的单据", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "aggregatedValueObject": false, "mapName": "vouchdate", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953834, "name": "stockOrg", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": "", "array": false, "paramDesc": "库存组织id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "aggregatedValueObject": false, "mapName": "stockOrg", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953835, "name": "salesOrg", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": "", "array": false, "paramDesc": "销售组织id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "aggregatedValueObject": false, "mapName": "salesOrg", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953836, "name": "invoiceOrg", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": "", "array": false, "paramDesc": "开票组织ID", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "aggregatedValueObject": false, "mapName": "invoiceOrg", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953837, "name": "invoiceCust", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": "", "array": false, "paramDesc": "开票客户id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "aggregatedValueObject": false, "mapName": "invoiceCust", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953838, "name": "upcode", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": "", "array": false, "paramDesc": "来源单据号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "aggregatedValueObject": false, "mapName": "upcode", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953839, "name": "department", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": "", "array": false, "paramDesc": "部门id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "aggregatedValueObject": false, "mapName": "department", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953840, "name": "operator", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": "", "array": false, "paramDesc": "业务员id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "aggregatedValueObject": false, "mapName": "operator", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953841, "name": "warehouse", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": "", "array": false, "paramDesc": "仓库id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "aggregatedValueObject": false, "mapName": "warehouse", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953842, "name": "stockMgr", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": "", "array": false, "paramDesc": "库管员id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "aggregatedValueObject": false, "mapName": "stockMgr", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953843, "name": "cust", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": "", "array": false, "paramDesc": "客户id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 14, "baseType": true, "aggregatedValueObject": false, "mapName": "cust", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953844, "name": "product_cName", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": "", "array": false, "paramDesc": "物料id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "aggregatedValueObject": false, "mapName": "product_cName", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953845, "name": "bustype.name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": "", "array": false, "paramDesc": "交易类型名称", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "aggregatedValueObject": false, "mapName": "bustype.name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953846, "name": "product_cName_ManageClass", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": "", "defParamId": "", "array": false, "paramDesc": "物料分类id", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "aggregatedValueObject": false, "mapName": "product_cName_ManageClass", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953847, "name": "isSum", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": "", "array": false, "paramDesc": "查询表头", "paramType": "boolean", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "aggregatedValueObject": false, "mapName": "isSum", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "boolean", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953824, "name": "simpleVOs", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": {"children": [{"id": 2200117965505953825, "name": "op", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953824, "defParamId": "", "array": false, "paramDesc": "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "op", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953826, "name": "value2", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953824, "defParamId": "", "array": false, "paramDesc": "值2(条件)如：\"2021-04-19 23:59:59\"", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "value2", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953827, "name": "value1", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953824, "defParamId": "", "array": false, "paramDesc": "值1(条件)如： \"2021-04-19 00:00:00\"", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "value1", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200117965505953828, "name": "field", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953824, "defParamId": "", "array": false, "paramDesc": "属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码details.product.cCode、表头自定义项headItem.define1、表体自定义项details.bodyItem.define1等)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "field", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "defParamId": "", "array": false, "paramDesc": "扩展查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "aggregatedValueObject": false, "mapName": "simpleVOs", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "paramReturnDTOS": {"paramReturnDTOS": [{"id": 2200117974095888415, "name": "code", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": 1856804444898852921, "array": false, "paramDesc": "返回码，调用成功时返回200", "paramType": "string", "requestParamType": "", "path": "", "example": 200, "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888416, "name": "message", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "defParamId": 1856804444898852922, "array": false, "paramDesc": "调用失败时的错误信息", "paramType": "string", "requestParamType": "", "path": "", "example": "操作成功", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953848, "name": "data", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": "", "children": {"children": [{"id": 2200117974095888408, "name": "pageIndex", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953848, "defParamId": 1856804444898852924, "array": false, "paramDesc": "当前页", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888409, "name": "pageSize", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953848, "defParamId": 1856804444898852925, "array": false, "paramDesc": "分页大小", "paramType": "long", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888410, "name": "recordCount", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953848, "defParamId": 1856804444898852926, "array": false, "paramDesc": "总记录数", "paramType": "long", "requestParamType": "", "path": "", "example": 26, "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953849, "name": "recordList", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953848, "children": {"children": [{"id": 2200117965505953850, "name": "cR<PERSON><PERSON>ve<PERSON><PERSON><PERSON>", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852928, "array": false, "paramDesc": "收货地址", "paramType": "string", "requestParamType": "", "path": "", "example": 1111, "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953851, "name": "oriTax", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852929, "array": false, "paramDesc": "税率", "paramType": "double", "requestParamType": "", "path": "", "example": 0.19, "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953852, "name": "details_stockUnitId", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852930, "array": false, "paramDesc": "库存单位主键", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953853, "name": "product_cCode", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852931, "array": false, "paramDesc": "物料编码", "paramType": "string", "requestParamType": "", "path": "", "example": "hy母件002", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953854, "name": "details_taxId", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852932, "array": false, "paramDesc": "税目主键", "paramType": "string", "requestParamType": "", "path": "", "example": "8b99f589-bc47-4c8a-bfqw-13d78caa20b0", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953855, "name": "natCurrency", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852933, "array": false, "paramDesc": "本币主键", "paramType": "string", "requestParamType": "", "path": "", "example": "G001ZM0000DEFAULTCURRENCT00000000001", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953856, "name": "sourcesys", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852934, "array": false, "paramDesc": "来源单据领域", "paramType": "string", "requestParamType": "", "path": "", "example": "<PERSON><PERSON><PERSON><PERSON>", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953857, "name": "tradeRouteID", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1869251492046176264, "array": false, "paramDesc": "贸易路径id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953858, "name": "stockUnitId_Precision", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852935, "array": false, "paramDesc": "库存单位精度", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953859, "name": "id", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852936, "array": false, "paramDesc": "主键", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953860, "name": "status_mobile_row", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852937, "array": false, "paramDesc": "单据状态", "paramType": "long", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953861, "name": "invoiceTitle", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852938, "array": false, "paramDesc": "发票抬头", "paramType": "string", "requestParamType": "", "path": "", "example": "123抬头", "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953862, "name": "details_priceUOM", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852939, "array": false, "paramDesc": "计价单位主键", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953863, "name": "natSum", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852940, "array": false, "paramDesc": "本币含税金额", "paramType": "long", "requestParamType": "", "path": "", "example": 4, "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953864, "name": "isEndTrade", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1869251492046176265, "array": false, "paramDesc": "是否末级", "paramType": "short", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 14, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953865, "name": "warehouse", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852941, "array": false, "paramDesc": "仓库主键", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953866, "name": "srcBillType", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852942, "array": false, "paramDesc": "来源单据类型", "paramType": "string", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953867, "name": "diliver<PERSON>tatus", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852943, "array": false, "paramDesc": "发货状态", "paramType": "string", "requestParamType": "", "path": "", "example": "DELIVERING", "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953868, "name": "warehouse_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852944, "array": false, "paramDesc": "仓库名称", "paramType": "string", "requestParamType": "", "path": "", "example": "调入仓库B", "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953869, "name": "natCurrency_priceDigit", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852945, "array": false, "paramDesc": "本币精度", "paramType": "long", "requestParamType": "", "path": "", "example": 3, "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953870, "name": "exchRateType", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852946, "array": false, "paramDesc": "汇率类型枚举值", "paramType": "string", "requestParamType": "", "path": "", "example": "sfaju9kr", "fullName": "", "ytenantId": "", "paramOrder": 20, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953871, "name": "tradeRouteLineno", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1869251492046176266, "array": false, "paramDesc": "站点", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 21, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953872, "name": "invExchRate", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852947, "array": false, "paramDesc": "单位转换率", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 22, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953873, "name": "product_defaultAlbumId", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852948, "array": false, "paramDesc": "物料图片", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 23, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953874, "name": "impactStockTiming", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 2200117965505953798, "array": false, "paramDesc": "更新存量传财务时机,0:保存;1:审核", "paramType": "string", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 24, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2200117965505953875, "name": "status", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852949, "array": false, "paramDesc": "单据状态；0开立，1已审核，3审核中", "paramType": "long", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 25, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953876, "name": "currency_moneyDigit", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852950, "array": false, "paramDesc": "币种精度", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 26, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953877, "name": "invoiceCust_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852951, "array": false, "paramDesc": "开票客户名称", "paramType": "string", "requestParamType": "", "path": "", "example": "张三啊", "fullName": "", "ytenantId": "", "paramOrder": 27, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953878, "name": "details_productsku", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852952, "array": false, "paramDesc": "物料KSU主键", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 28, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953879, "name": "salesOrg", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852953, "array": false, "paramDesc": "销售组织主键", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 29, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953880, "name": "invoiceOrg_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852954, "array": false, "paramDesc": "开票组织名称", "paramType": "string", "requestParamType": "", "path": "", "example": "hy组织001", "fullName": "", "ytenantId": "", "paramOrder": 30, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953881, "name": "tradeRoute_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1878166108965961735, "array": false, "paramDesc": "贸易路径", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 31, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953882, "name": "productsku_cName", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852955, "array": false, "paramDesc": "物料SKU名称", "paramType": "string", "requestParamType": "", "path": "", "example": "hy母件002", "fullName": "", "ytenantId": "", "paramOrder": 32, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953883, "name": "vouchdate", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852956, "array": false, "paramDesc": "单据日期", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-06-01 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 33, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953884, "name": "invPriceExchRate", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852957, "array": false, "paramDesc": "计价单位转换率", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 34, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953885, "name": "currency", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852958, "array": false, "paramDesc": "原币主键", "paramType": "string", "requestParamType": "", "path": "", "example": "G001ZM0000DEFAULTCURRENCT00000000001", "fullName": "", "ytenantId": "", "paramOrder": 35, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953886, "name": "pubts", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852959, "array": false, "paramDesc": "时间戳", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-06-02 15:10:23", "fullName": "", "ytenantId": "", "paramOrder": 36, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953887, "name": "org_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852960, "array": false, "paramDesc": "发货组织名称", "paramType": "string", "requestParamType": "", "path": "", "example": "hy组织001", "fullName": "", "ytenantId": "", "paramOrder": 37, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953888, "name": "cReceiveMobile", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852961, "array": false, "paramDesc": "收货电话", "paramType": "string", "requestParamType": "", "path": "", "example": 4353, "fullName": "", "ytenantId": "", "paramOrder": 38, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953889, "name": "createDate", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852962, "array": false, "paramDesc": "创建日期", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-06-01 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 39, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953890, "name": "creator", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852963, "array": false, "paramDesc": "创建人", "paramType": "string", "requestParamType": "", "path": "", "example": "rtduanhy", "fullName": "", "ytenantId": "", "paramOrder": 40, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953891, "name": "oriSum", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852964, "array": false, "paramDesc": "原币含税金额", "paramType": "long", "requestParamType": "", "path": "", "example": 4, "fullName": "", "ytenantId": "", "paramOrder": 41, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953892, "name": "exchRateType_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852965, "array": false, "paramDesc": "汇率类型名称", "paramType": "string", "requestParamType": "", "path": "", "example": "基准汇率", "fullName": "", "ytenantId": "", "paramOrder": 42, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953893, "name": "accountOrg", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852966, "array": false, "paramDesc": "会计主体主键", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 43, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953894, "name": "stsalesOutExchangeInfo_d_key", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852967, "array": false, "paramDesc": "逻辑字段冗余", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 44, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953895, "name": "cR<PERSON><PERSON>ver", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852968, "array": false, "paramDesc": "收货人", "paramType": "string", "requestParamType": "", "path": "", "example": 43543, "fullName": "", "ytenantId": "", "paramOrder": 45, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953896, "name": "details_id", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852969, "array": false, "paramDesc": "子表主键", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 46, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953897, "name": "priceQty", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852970, "array": false, "paramDesc": "计价数量", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 47, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953898, "name": "createTime", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852971, "array": false, "paramDesc": "创建时间", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-06-01 20:24:08", "fullName": "", "ytenantId": "", "paramOrder": 48, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953899, "name": "taxUnitPriceTag", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852972, "array": false, "paramDesc": "价格含税标志", "paramType": "boolean", "requestParamType": "", "path": "", "example": true, "fullName": "", "ytenantId": "", "paramOrder": 49, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953900, "name": "details_product", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852973, "array": false, "paramDesc": "物料主键", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 50, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953901, "name": "taxNum", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852974, "array": false, "paramDesc": "纳税识别号", "paramType": "string", "requestParamType": "", "path": "", "example": **********, "fullName": "", "ytenantId": "", "paramOrder": 51, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953902, "name": "department_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852975, "array": false, "paramDesc": "部门名称", "paramType": "string", "requestParamType": "", "path": "", "example": "XX部门", "fullName": "", "ytenantId": "", "paramOrder": 52, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953903, "name": "operator_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852976, "array": false, "paramDesc": "业务员名称", "paramType": "string", "requestParamType": "", "path": "", "example": "某某", "fullName": "", "ytenantId": "", "paramOrder": 53, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953904, "name": "invoiceAddress", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852977, "array": false, "paramDesc": "营业地址", "paramType": "string", "requestParamType": "", "path": "", "example": "某地区街道", "fullName": "", "ytenantId": "", "paramOrder": 54, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953905, "name": "operator", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852978, "array": false, "paramDesc": "业务员主键", "paramType": "long", "requestParamType": "", "path": "", "example": ***********, "fullName": "", "ytenantId": "", "paramOrder": 55, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953906, "name": "bankAccount", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852979, "array": false, "paramDesc": "银行账号", "paramType": "string", "requestParamType": "", "path": "", "example": ***********, "fullName": "", "ytenantId": "", "paramOrder": 56, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953907, "name": "subBankName", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852980, "array": false, "paramDesc": "开户支行", "paramType": "string", "requestParamType": "", "path": "", "example": "某某支行", "fullName": "", "ytenantId": "", "paramOrder": 57, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953908, "name": "bankName", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852981, "array": false, "paramDesc": "开户银行", "paramType": "string", "requestParamType": "", "path": "", "example": "某银行", "fullName": "", "ytenantId": "", "paramOrder": 58, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953909, "name": "invoiceTelephone", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852982, "array": false, "paramDesc": "营业电话", "paramType": "string", "requestParamType": "", "path": "", "example": *********, "fullName": "", "ytenantId": "", "paramOrder": 59, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953910, "name": "department", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852983, "array": false, "paramDesc": "部门主键", "paramType": "string", "requestParamType": "", "path": "", "example": *********, "fullName": "", "ytenantId": "", "paramOrder": 60, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953911, "name": "cust", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852984, "array": false, "paramDesc": "客户主键", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 61, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953912, "name": "invoiceUpcType", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852985, "array": false, "paramDesc": "发票类型", "paramType": "string", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 62, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953913, "name": "natMoney", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852986, "array": false, "paramDesc": "本币无税金额", "paramType": "double", "requestParamType": "", "path": "", "example": 3.81, "fullName": "", "ytenantId": "", "paramOrder": 63, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953914, "name": "currency_priceDigit", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852987, "array": false, "paramDesc": "币种精度", "paramType": "long", "requestParamType": "", "path": "", "example": 3, "fullName": "", "ytenantId": "", "paramOrder": 64, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953915, "name": "invoiceOrg", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852988, "array": false, "paramDesc": "开票客户主键", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 65, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953916, "name": "stockUnit_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852989, "array": false, "paramDesc": "库存单位名称", "paramType": "string", "requestParamType": "", "path": "", "example": "件", "fullName": "", "ytenantId": "", "paramOrder": 66, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953917, "name": "collaborationPolineno", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1869304715280908289, "array": false, "paramDesc": "协同来源单据行号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 67, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953918, "name": "bustype_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852990, "array": false, "paramDesc": "交易类型名称", "paramType": "string", "requestParamType": "", "path": "", "example": "销售出库", "fullName": "", "ytenantId": "", "paramOrder": 68, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953919, "name": "modifier", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852991, "array": false, "paramDesc": "修改人", "paramType": "string", "requestParamType": "", "path": "", "example": "rtduanhy", "fullName": "", "ytenantId": "", "paramOrder": 69, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953920, "name": "firstupcode", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852992, "array": false, "paramDesc": "源头单据编码", "paramType": "string", "requestParamType": "", "path": "", "example": "UO-test20210601000012", "fullName": "", "ytenantId": "", "paramOrder": 70, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953921, "name": "source", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852993, "array": false, "paramDesc": "来源单据类型", "paramType": "string", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 71, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953922, "name": "natTax", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852994, "array": false, "paramDesc": "本币税额", "paramType": "double", "requestParamType": "", "path": "", "example": 0.19, "fullName": "", "ytenantId": "", "paramOrder": 72, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953923, "name": "subQty", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852995, "array": false, "paramDesc": "件数", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 73, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953924, "name": "taxItems", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852996, "array": false, "paramDesc": "税率显示值", "paramType": "string", "requestParamType": "", "path": "", "example": "5%", "fullName": "", "ytenantId": "", "paramOrder": 74, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953925, "name": "modifyTime", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852997, "array": false, "paramDesc": "修改时间", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-06-02 15:10:23", "fullName": "", "ytenantId": "", "paramOrder": 75, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953926, "name": "product_cName", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852998, "array": false, "paramDesc": "物料名称", "paramType": "string", "requestParamType": "", "path": "", "example": "hy母件002", "fullName": "", "ytenantId": "", "paramOrder": 76, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953927, "name": "invoiceTitleType", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898852999, "array": false, "paramDesc": "发票抬头类型", "paramType": "string", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 77, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953928, "name": "receiveContacterPhone", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853000, "array": false, "paramDesc": "收货人联系电话", "paramType": "string", "requestParamType": "", "path": "", "example": ***********, "fullName": "", "ytenantId": "", "paramOrder": 78, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953929, "name": "modifyInvoiceType", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853001, "array": false, "paramDesc": "发票类型可改标志", "paramType": "string", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 79, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953930, "name": "natCurrencyName", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853002, "array": false, "paramDesc": "本币名称", "paramType": "string", "requestParamType": "", "path": "", "example": "人民币", "fullName": "", "ytenantId": "", "paramOrder": 80, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953931, "name": "salesOrg_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853003, "array": false, "paramDesc": "销售组织名称", "paramType": "string", "requestParamType": "", "path": "", "example": "hy组织001", "fullName": "", "ytenantId": "", "paramOrder": 81, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953932, "name": "modifyDate", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853004, "array": false, "paramDesc": "修改日期", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-06-02 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 82, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953933, "name": "unitName", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853005, "array": false, "paramDesc": "主计量名称", "paramType": "string", "requestParamType": "", "path": "", "example": "件", "fullName": "", "ytenantId": "", "paramOrder": 83, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953934, "name": "contactName", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853006, "array": false, "paramDesc": "联系人名称", "paramType": "string", "requestParamType": "", "path": "", "example": "张三", "fullName": "", "ytenantId": "", "paramOrder": 84, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953935, "name": "srcBillNO", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853007, "array": false, "paramDesc": "来源单据号", "paramType": "string", "requestParamType": "", "path": "", "example": "******************", "fullName": "", "ytenantId": "", "paramOrder": 85, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953936, "name": "oriUnitPrice", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853008, "array": false, "paramDesc": "原币无税单价", "paramType": "double", "requestParamType": "", "path": "", "example": 1.905, "fullName": "", "ytenantId": "", "paramOrder": 86, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953937, "name": "taxCode", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853009, "array": false, "paramDesc": "税目编码", "paramType": "string", "requestParamType": "", "path": "", "example": "VAT5", "fullName": "", "ytenantId": "", "paramOrder": 87, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953938, "name": "barCode", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853010, "array": false, "paramDesc": "单据码", "paramType": "string", "requestParamType": "", "path": "", "example": "st_salesout|****************", "fullName": "", "ytenantId": "", "paramOrder": 88, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953939, "name": "unit_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853011, "array": false, "paramDesc": "主计量名称冗余", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 89, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953940, "name": "taxRate", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853012, "array": false, "paramDesc": "税率", "paramType": "long", "requestParamType": "", "path": "", "example": 5, "fullName": "", "ytenantId": "", "paramOrder": 90, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953941, "name": "unit", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853013, "array": false, "paramDesc": "库存单位", "paramType": "string", "requestParamType": "", "path": "", "example": "件", "fullName": "", "ytenantId": "", "paramOrder": 91, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953942, "name": "productsku_cCode", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853014, "array": false, "paramDesc": "物料SKU编码", "paramType": "string", "requestParamType": "", "path": "", "example": "hy母件002", "fullName": "", "ytenantId": "", "paramOrder": 92, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953943, "name": "natCurrency_moneyDigit", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853015, "array": false, "paramDesc": "本币精度", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 93, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953944, "name": "accountOrg_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853016, "array": false, "paramDesc": "会计主体名称", "paramType": "string", "requestParamType": "", "path": "", "example": "hy组织001", "fullName": "", "ytenantId": "", "paramOrder": 94, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953945, "name": "taxId", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853017, "array": false, "paramDesc": "税目编码", "paramType": "string", "requestParamType": "", "path": "", "example": "VAT5", "fullName": "", "ytenantId": "", "paramOrder": 95, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953946, "name": "invoiceCust", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853018, "array": false, "paramDesc": "开票客户主键", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 96, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953947, "name": "qty", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853019, "array": false, "paramDesc": "数量", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 97, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953948, "name": "unit_Precision", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853020, "array": false, "paramDesc": "主计量精度", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 98, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953949, "name": "oriTaxUnitPrice", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853021, "array": false, "paramDesc": "原币含税单价", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 99, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953950, "name": "oriMoney", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853022, "array": false, "paramDesc": "原币无税金额", "paramType": "double", "requestParamType": "", "path": "", "example": 3.81, "fullName": "", "ytenantId": "", "paramOrder": 100, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953951, "name": "contactsPieces", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853023, "array": false, "paramDesc": "应发件数", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 101, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953952, "name": "contactsQuantity", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853024, "array": false, "paramDesc": "应发数量", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 102, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953953, "name": "natUnitPrice", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853025, "array": false, "paramDesc": "本币无税单价", "paramType": "double", "requestParamType": "", "path": "", "example": 1.905, "fullName": "", "ytenantId": "", "paramOrder": 103, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953954, "name": "code", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853026, "array": false, "paramDesc": "单据编码", "paramType": "string", "requestParamType": "", "path": "", "example": "XSCK20210601000001", "fullName": "", "ytenantId": "", "paramOrder": 104, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953955, "name": "receiveAccountingBasis", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853027, "array": false, "paramDesc": "立账开票依据", "paramType": "string", "requestParamType": "", "path": "", "example": "voucher_delivery", "fullName": "", "ytenantId": "", "paramOrder": 105, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953956, "name": "logistics", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853028, "array": false, "paramDesc": "物料单号", "paramType": "string", "requestParamType": "", "path": "", "example": "XSCK20210601000001", "fullName": "", "ytenantId": "", "paramOrder": 106, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953957, "name": "exchRate", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853029, "array": false, "paramDesc": "汇率", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 107, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953958, "name": "currencyName", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853030, "array": false, "paramDesc": "币种名称", "paramType": "string", "requestParamType": "", "path": "", "example": "人民币", "fullName": "", "ytenantId": "", "paramOrder": 108, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953959, "name": "cust_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853031, "array": false, "paramDesc": "客户名称", "paramType": "string", "requestParamType": "", "path": "", "example": "张三啊", "fullName": "", "ytenantId": "", "paramOrder": 109, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953960, "name": "org", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853032, "array": false, "paramDesc": "库存组织主键", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 110, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953961, "name": "priceUOM_name", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853033, "array": false, "paramDesc": "计价单位名称", "paramType": "string", "requestParamType": "", "path": "", "example": "件", "fullName": "", "ytenantId": "", "paramOrder": 111, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953962, "name": "bustype", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853034, "array": false, "paramDesc": "交易类型主键", "paramType": "string", "requestParamType": "", "path": "", "example": ***************, "fullName": "", "ytenantId": "", "paramOrder": 112, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953963, "name": "receiveId", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853035, "array": false, "paramDesc": "收货地址主键", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 113, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953964, "name": "upcode", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853036, "array": false, "paramDesc": "来源单据号", "paramType": "string", "requestParamType": "", "path": "", "example": "******************", "fullName": "", "ytenantId": "", "paramOrder": 114, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953965, "name": "saleStyle", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853037, "array": false, "paramDesc": "商品售卖类型", "paramType": "string", "requestParamType": "", "path": "", "example": "SALE", "fullName": "", "ytenantId": "", "paramOrder": 115, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953966, "name": "iLogisticId", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853038, "array": false, "paramDesc": "物流公司", "paramType": "long", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 116, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953967, "name": "status_mobile", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853039, "array": false, "paramDesc": "单据状态", "paramType": "long", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 117, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953968, "name": "natTaxUnitPrice", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853040, "array": false, "paramDesc": "本币含税单价", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 118, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953972, "name": "out_sys_id", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853044, "array": false, "paramDesc": "外部来源线索", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 122, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953973, "name": "out_sys_code", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853045, "array": false, "paramDesc": "外部来源编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 123, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953974, "name": "out_sys_version", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853046, "array": false, "paramDesc": "外部来源版本", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 124, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953975, "name": "out_sys_type", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853047, "array": false, "paramDesc": "外部来源类型", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 125, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953976, "name": "out_sys_rowno", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853048, "array": false, "paramDesc": "外部来源行号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 126, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953977, "name": "out_sys_lineid", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1856804444898853049, "array": false, "paramDesc": "外部来源行", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 127, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953978, "name": "collaborationPocode", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1871361523558383623, "array": false, "paramDesc": "协同来源单据号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 128, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953979, "name": "collaborationPoid", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1871361523558383624, "array": false, "paramDesc": "协同来源单据id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 129, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953980, "name": "collaborationPodetailid", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1871361523558383625, "array": false, "paramDesc": "协同来源单据子表id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 130, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953981, "name": "collaborationSource", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1871361523558383626, "array": false, "paramDesc": "协同来源类型, 0:无来源、st_purinrecord:采购入库单、1:发货单、2:销售订单、3:退货单、tradeorder:电商订单、refundorder:电商退换货订单、retailvouch:零售单、mallvouch:商城发货单", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 131, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953982, "name": "salesOutsExtend!coUpcode", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1871361523558383627, "array": false, "paramDesc": "协同源头单据号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 132, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953983, "name": "salesOutsExtend!coSourceid", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1871361523558383628, "array": false, "paramDesc": "协同源头单据头id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 133, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953984, "name": "salesOutsExtend!coSourceLineNo", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1871361523558383629, "array": false, "paramDesc": "协同源头行号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 134, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117965505953985, "name": "salesOutsExtend!coSourceType", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953849, "defParamId": 1871361523558383630, "array": false, "paramDesc": "协同源头单据类型(upu.st_purchaseorder:采购订单,productionorder.po_subcontract_order:委外订单)", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 135, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1856804444898852927, "array": true, "paramDesc": "返回数据列表", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888393, "name": "sumRecordList", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953848, "children": {"children": [{"id": 2200117974095888394, "name": "totalPieces", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853051, "array": false, "paramDesc": "合计件数", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888395, "name": "oriSum", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853052, "array": false, "paramDesc": "合计金额", "paramType": "long", "requestParamType": "", "path": "", "example": 51222, "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888396, "name": "invoiceOriSum", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853053, "array": false, "paramDesc": "合计开票金额", "paramType": "long", "requestParamType": "", "path": "", "example": 27922, "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888397, "name": "saleReturnQty", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853054, "array": false, "paramDesc": "合计退货数量", "paramType": "long", "requestParamType": "", "path": "", "example": 34, "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888398, "name": "natSum", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853055, "array": false, "paramDesc": "合计本币金额", "paramType": "long", "requestParamType": "", "path": "", "example": 51222, "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888399, "name": "subQty", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853056, "array": false, "paramDesc": "合计副计量数量", "paramType": "long", "requestParamType": "", "path": "", "example": 492, "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888400, "name": "totalQuantity", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853057, "array": false, "paramDesc": "合计数量", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888401, "name": "priceQty", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853058, "array": false, "paramDesc": "合计计价数量", "paramType": "long", "requestParamType": "", "path": "", "example": 492, "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888402, "name": "qty", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853059, "array": false, "paramDesc": "合计数量", "paramType": "long", "requestParamType": "", "path": "", "example": 492, "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888403, "name": "oriMoney", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853060, "array": false, "paramDesc": "合计原币无税金额", "paramType": "double", "requestParamType": "", "path": "", "example": 49705.27, "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888404, "name": "invoiceQty", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853061, "array": false, "paramDesc": "合计开票数量", "paramType": "long", "requestParamType": "", "path": "", "example": 298, "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888405, "name": "contactsPieces", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853062, "array": false, "paramDesc": "合计应发件量", "paramType": "long", "requestParamType": "", "path": "", "example": 481, "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888406, "name": "contactsQuantity", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853063, "array": false, "paramDesc": "合计应发数量", "paramType": "long", "requestParamType": "", "path": "", "example": 481, "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888407, "name": "natMoney", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117974095888393, "defParamId": 1856804444898853064, "array": false, "paramDesc": "合计本币无税金额", "paramType": "double", "requestParamType": "", "path": "", "example": 49705.27, "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1856804444898853050, "array": true, "paramDesc": "合计信息", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888411, "name": "pageCount", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953848, "defParamId": 1856804444898853065, "array": false, "paramDesc": "总页数", "paramType": "long", "requestParamType": "", "path": "", "example": 3, "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888412, "name": "beginPageIndex", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953848, "defParamId": 1856804444898853066, "array": false, "paramDesc": "开始页页号", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888413, "name": "endPageIndex", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953848, "defParamId": 1856804444898853067, "array": false, "paramDesc": "最终页页号", "paramType": "long", "requestParamType": "", "path": "", "example": 3, "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200117974095888414, "name": "pubts", "apiId": "05a2a46b90984a8ba115d6a2061f8720", "parentId": 2200117965505953848, "defParamId": 1856804444898853068, "array": false, "paramDesc": "时间戳", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-06-02 16:37:29", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1856804444898852923, "array": false, "paramDesc": "调用成功时的返回数据", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-02-12 10:30:41.000", "gmtUpdate": "2025-02-12 10:30:41.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "returnFormatType": "JSON", "paramConstDTOS": "", "paramConstMapDTOS": "", "apiDemoReturnDTOS": {"apiDemoReturnDTOS": [{"id": 2200117974095888422, "apiId": "05a2a46b90984a8ba115d6a2061f8720", "content": "{ \"code\": \"200\", \"message\": \"操作成功\", \"data\": { \"pageIndex\": 1, \"pageSize\": 10, \"recordCount\": 26, \"recordList\": [ { \"cReceiveAddress\": \"1111\", \"oriTax\": 0.19, \"details_stockUnitId\": ****************, \"product_cCode\": \"hy母件002\", \"details_taxId\": \"8b99f589-bc47-4c8a-bfqw-13d78caa20b0\", \"natCurrency\": \"G001ZM0000DEFAULTCURRENCT00000000001\", \"sourcesys\": \"udinghuo\", \"tradeRouteID\": 0, \"stockUnitId_Precision\": 2, \"id\": ****************, \"status_mobile_row\": 0, \"invoiceTitle\": \"123抬头\", \"details_priceUOM\": ****************, \"natSum\": 4, \"isEndTrade\": 0, \"warehouse\": ****************, \"srcBillType\": \"1\", \"diliverStatus\": \"DELIVERING\", \"warehouse_name\": \"调入仓库B\", \"natCurrency_priceDigit\": 3, \"exchRateType\": \"sfaju9kr\", \"tradeRouteLineno\": \"\", \"invExchRate\": 1, \"product_defaultAlbumId\": \"\", \"impactStockTiming\": \"0\", \"status\": 0, \"currency_moneyDigit\": 2, \"invoiceCust_name\": \"张三啊\", \"details_productsku\": ****************, \"salesOrg\": \"****************\", \"invoiceOrg_name\": \"hy组织001\", \"tradeRoute_name\": \"\", \"productsku_cName\": \"hy母件002\", \"vouchdate\": \"2021-06-01 00:00:00\", \"invPriceExchRate\": 1, \"currency\": \"G001ZM0000DEFAULTCURRENCT00000000001\", \"pubts\": \"2021-06-02 15:10:23\", \"org_name\": \"hy组织001\", \"cReceiveMobile\": \"4353\", \"createDate\": \"2021-06-01 00:00:00\", \"creator\": \"rtduanhy\", \"oriSum\": 4, \"exchRateType_name\": \"基准汇率\", \"accountOrg\": \"****************\", \"stsalesOutExchangeInfo_d_key\": ****************, \"cReceiver\": \"43543\", \"details_id\": ****************, \"priceQty\": 2, \"createTime\": \"2021-06-01 20:24:08\", \"taxUnitPriceTag\": true, \"details_product\": ****************, \"taxNum\": \"**********\", \"department_name\": \"XX部门\", \"operator_name\": \"某某\", \"invoiceAddress\": \"某地区街道\", \"operator\": ***********, \"bankAccount\": \"***********\", \"subBankName\": \"某某支行\", \"bankName\": \"某银行\", \"invoiceTelephone\": \"*********\", \"department\": \"*********\", \"cust\": ****************, \"invoiceUpcType\": \"0\", \"natMoney\": 3.81, \"currency_priceDigit\": 3, \"invoiceOrg\": \"****************\", \"stockUnit_name\": \"件\", \"collaborationPolineno\": \"\", \"bustype_name\": \"销售出库\", \"modifier\": \"rtduanhy\", \"firstupcode\": \"UO-test20210601000012\", \"source\": \"1\", \"natTax\": 0.19, \"subQty\": 2, \"taxItems\": \"5%\", \"modifyTime\": \"2021-06-02 15:10:23\", \"product_cName\": \"hy母件002\", \"invoiceTitleType\": \"0\", \"receiveContacterPhone\": \"***********\", \"modifyInvoiceType\": \"1\", \"natCurrencyName\": \"人民币\", \"salesOrg_name\": \"hy组织001\", \"modifyDate\": \"2021-06-02 00:00:00\", \"unitName\": \"件\", \"contactName\": \"张三\", \"srcBillNO\": \"******************\", \"oriUnitPrice\": 1.905, \"taxCode\": \"VAT5\", \"barCode\": \"st_salesout|****************\", \"unit_name\": ****************, \"taxRate\": 5, \"unit\": \"件\", \"productsku_cCode\": \"hy母件002\", \"natCurrency_moneyDigit\": 2, \"accountOrg_name\": \"hy组织001\", \"taxId\": \"VAT5\", \"invoiceCust\": ****************, \"qty\": 2, \"unit_Precision\": 2, \"oriTaxUnitPrice\": 2, \"oriMoney\": 3.81, \"contactsPieces\": 2, \"contactsQuantity\": 2, \"natUnitPrice\": 1.905, \"code\": \"XSCK20210601000001\", \"receiveAccountingBasis\": \"voucher_delivery\", \"logistics\": \"XSCK20210601000001\", \"exchRate\": 1, \"currencyName\": \"人民币\", \"cust_name\": \"张三啊\", \"org\": \"****************\", \"priceUOM_name\": \"件\", \"bustype\": \"***************\", \"receiveId\": ****************, \"upcode\": \"******************\", \"saleStyle\": \"SALE\", \"iLogisticId\": 0, \"status_mobile\": 0, \"natTaxUnitPrice\": 2, \"salesOutDefineCharacter\": {}, \"salesOutsDefineCharacter\": {}, \"salesOutsCharacteristics\": {}, \"out_sys_id\": \"\", \"out_sys_code\": \"\", \"out_sys_version\": \"\", \"out_sys_type\": \"\", \"out_sys_rowno\": \"\", \"out_sys_lineid\": \"\", \"collaborationPocode\": \"\", \"collaborationPoid\": 0, \"collaborationPodetailid\": 0, \"collaborationSource\": \"\", \"salesOutsExtend!coUpcode\": \"\", \"salesOutsExtend!coSourceid\": 0, \"salesOutsExtend!coSourceLineNo\": \"\", \"salesOutsExtend!coSourceType\": \"\" } ], \"sumRecordList\": [ { \"totalPieces\": 2, \"oriSum\": 51222, \"invoiceOriSum\": 27922, \"saleReturnQty\": 34, \"natSum\": 51222, \"subQty\": 492, \"totalQuantity\": 2, \"priceQty\": 492, \"qty\": 492, \"oriMoney\": 49705.27, \"invoiceQty\": 298, \"contactsPieces\": 481, \"contactsQuantity\": 481, \"natMoney\": 49705.27 } ], \"pageCount\": 3, \"beginPageIndex\": 1, \"endPageIndex\": 3, \"pubts\": \"2021-06-02 16:37:29\" } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2200117974095888423, "apiId": "05a2a46b90984a8ba115d6a2061f8720", "content": "{\"code\":999,\"message\":\"列表查询失败\"}", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "apiDemoReturnDTOList": {"apiDemoReturnDTOList": [{"id": 2200117974095888422, "apiId": "05a2a46b90984a8ba115d6a2061f8720", "content": "{ \"code\": \"200\", \"message\": \"操作成功\", \"data\": { \"pageIndex\": 1, \"pageSize\": 10, \"recordCount\": 26, \"recordList\": [ { \"cReceiveAddress\": \"1111\", \"oriTax\": 0.19, \"details_stockUnitId\": ****************, \"product_cCode\": \"hy母件002\", \"details_taxId\": \"8b99f589-bc47-4c8a-bfqw-13d78caa20b0\", \"natCurrency\": \"G001ZM0000DEFAULTCURRENCT00000000001\", \"sourcesys\": \"udinghuo\", \"tradeRouteID\": 0, \"stockUnitId_Precision\": 2, \"id\": ****************, \"status_mobile_row\": 0, \"invoiceTitle\": \"123抬头\", \"details_priceUOM\": ****************, \"natSum\": 4, \"isEndTrade\": 0, \"warehouse\": ****************, \"srcBillType\": \"1\", \"diliverStatus\": \"DELIVERING\", \"warehouse_name\": \"调入仓库B\", \"natCurrency_priceDigit\": 3, \"exchRateType\": \"sfaju9kr\", \"tradeRouteLineno\": \"\", \"invExchRate\": 1, \"product_defaultAlbumId\": \"\", \"impactStockTiming\": \"0\", \"status\": 0, \"currency_moneyDigit\": 2, \"invoiceCust_name\": \"张三啊\", \"details_productsku\": ****************, \"salesOrg\": \"****************\", \"invoiceOrg_name\": \"hy组织001\", \"tradeRoute_name\": \"\", \"productsku_cName\": \"hy母件002\", \"vouchdate\": \"2021-06-01 00:00:00\", \"invPriceExchRate\": 1, \"currency\": \"G001ZM0000DEFAULTCURRENCT00000000001\", \"pubts\": \"2021-06-02 15:10:23\", \"org_name\": \"hy组织001\", \"cReceiveMobile\": \"4353\", \"createDate\": \"2021-06-01 00:00:00\", \"creator\": \"rtduanhy\", \"oriSum\": 4, \"exchRateType_name\": \"基准汇率\", \"accountOrg\": \"****************\", \"stsalesOutExchangeInfo_d_key\": ****************, \"cReceiver\": \"43543\", \"details_id\": ****************, \"priceQty\": 2, \"createTime\": \"2021-06-01 20:24:08\", \"taxUnitPriceTag\": true, \"details_product\": ****************, \"taxNum\": \"**********\", \"department_name\": \"XX部门\", \"operator_name\": \"某某\", \"invoiceAddress\": \"某地区街道\", \"operator\": ***********, \"bankAccount\": \"***********\", \"subBankName\": \"某某支行\", \"bankName\": \"某银行\", \"invoiceTelephone\": \"*********\", \"department\": \"*********\", \"cust\": ****************, \"invoiceUpcType\": \"0\", \"natMoney\": 3.81, \"currency_priceDigit\": 3, \"invoiceOrg\": \"****************\", \"stockUnit_name\": \"件\", \"collaborationPolineno\": \"\", \"bustype_name\": \"销售出库\", \"modifier\": \"rtduanhy\", \"firstupcode\": \"UO-test20210601000012\", \"source\": \"1\", \"natTax\": 0.19, \"subQty\": 2, \"taxItems\": \"5%\", \"modifyTime\": \"2021-06-02 15:10:23\", \"product_cName\": \"hy母件002\", \"invoiceTitleType\": \"0\", \"receiveContacterPhone\": \"***********\", \"modifyInvoiceType\": \"1\", \"natCurrencyName\": \"人民币\", \"salesOrg_name\": \"hy组织001\", \"modifyDate\": \"2021-06-02 00:00:00\", \"unitName\": \"件\", \"contactName\": \"张三\", \"srcBillNO\": \"******************\", \"oriUnitPrice\": 1.905, \"taxCode\": \"VAT5\", \"barCode\": \"st_salesout|****************\", \"unit_name\": ****************, \"taxRate\": 5, \"unit\": \"件\", \"productsku_cCode\": \"hy母件002\", \"natCurrency_moneyDigit\": 2, \"accountOrg_name\": \"hy组织001\", \"taxId\": \"VAT5\", \"invoiceCust\": ****************, \"qty\": 2, \"unit_Precision\": 2, \"oriTaxUnitPrice\": 2, \"oriMoney\": 3.81, \"contactsPieces\": 2, \"contactsQuantity\": 2, \"natUnitPrice\": 1.905, \"code\": \"XSCK20210601000001\", \"receiveAccountingBasis\": \"voucher_delivery\", \"logistics\": \"XSCK20210601000001\", \"exchRate\": 1, \"currencyName\": \"人民币\", \"cust_name\": \"张三啊\", \"org\": \"****************\", \"priceUOM_name\": \"件\", \"bustype\": \"***************\", \"receiveId\": ****************, \"upcode\": \"******************\", \"saleStyle\": \"SALE\", \"iLogisticId\": 0, \"status_mobile\": 0, \"natTaxUnitPrice\": 2, \"salesOutDefineCharacter\": {}, \"salesOutsDefineCharacter\": {}, \"salesOutsCharacteristics\": {}, \"out_sys_id\": \"\", \"out_sys_code\": \"\", \"out_sys_version\": \"\", \"out_sys_type\": \"\", \"out_sys_rowno\": \"\", \"out_sys_lineid\": \"\", \"collaborationPocode\": \"\", \"collaborationPoid\": 0, \"collaborationPodetailid\": 0, \"collaborationSource\": \"\", \"salesOutsExtend!coUpcode\": \"\", \"salesOutsExtend!coSourceid\": 0, \"salesOutsExtend!coSourceLineNo\": \"\", \"salesOutsExtend!coSourceType\": \"\" } ], \"sumRecordList\": [ { \"totalPieces\": 2, \"oriSum\": 51222, \"invoiceOriSum\": 27922, \"saleReturnQty\": 34, \"natSum\": 51222, \"subQty\": 492, \"totalQuantity\": 2, \"priceQty\": 492, \"qty\": 492, \"oriMoney\": 49705.27, \"invoiceQty\": 298, \"contactsPieces\": 481, \"contactsQuantity\": 481, \"natMoney\": 49705.27 } ], \"pageCount\": 3, \"beginPageIndex\": 1, \"endPageIndex\": 3, \"pubts\": \"2021-06-02 16:37:29\" } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2200117974095888423, "apiId": "05a2a46b90984a8ba115d6a2061f8720", "content": "{\"code\":999,\"message\":\"列表查询失败\"}", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "routingStgy": 0, "routingStgyList": "", "apiDemoReturnDTO": {"id": 2200117974095888422, "apiId": "05a2a46b90984a8ba115d6a2061f8720", "content": "{ \"code\": \"200\", \"message\": \"操作成功\", \"data\": { \"pageIndex\": 1, \"pageSize\": 10, \"recordCount\": 26, \"recordList\": [ { \"cReceiveAddress\": \"1111\", \"oriTax\": 0.19, \"details_stockUnitId\": ****************, \"product_cCode\": \"hy母件002\", \"details_taxId\": \"8b99f589-bc47-4c8a-bfqw-13d78caa20b0\", \"natCurrency\": \"G001ZM0000DEFAULTCURRENCT00000000001\", \"sourcesys\": \"udinghuo\", \"tradeRouteID\": 0, \"stockUnitId_Precision\": 2, \"id\": ****************, \"status_mobile_row\": 0, \"invoiceTitle\": \"123抬头\", \"details_priceUOM\": ****************, \"natSum\": 4, \"isEndTrade\": 0, \"warehouse\": ****************, \"srcBillType\": \"1\", \"diliverStatus\": \"DELIVERING\", \"warehouse_name\": \"调入仓库B\", \"natCurrency_priceDigit\": 3, \"exchRateType\": \"sfaju9kr\", \"tradeRouteLineno\": \"\", \"invExchRate\": 1, \"product_defaultAlbumId\": \"\", \"impactStockTiming\": \"0\", \"status\": 0, \"currency_moneyDigit\": 2, \"invoiceCust_name\": \"张三啊\", \"details_productsku\": ****************, \"salesOrg\": \"****************\", \"invoiceOrg_name\": \"hy组织001\", \"tradeRoute_name\": \"\", \"productsku_cName\": \"hy母件002\", \"vouchdate\": \"2021-06-01 00:00:00\", \"invPriceExchRate\": 1, \"currency\": \"G001ZM0000DEFAULTCURRENCT00000000001\", \"pubts\": \"2021-06-02 15:10:23\", \"org_name\": \"hy组织001\", \"cReceiveMobile\": \"4353\", \"createDate\": \"2021-06-01 00:00:00\", \"creator\": \"rtduanhy\", \"oriSum\": 4, \"exchRateType_name\": \"基准汇率\", \"accountOrg\": \"****************\", \"stsalesOutExchangeInfo_d_key\": ****************, \"cReceiver\": \"43543\", \"details_id\": ****************, \"priceQty\": 2, \"createTime\": \"2021-06-01 20:24:08\", \"taxUnitPriceTag\": true, \"details_product\": ****************, \"taxNum\": \"**********\", \"department_name\": \"XX部门\", \"operator_name\": \"某某\", \"invoiceAddress\": \"某地区街道\", \"operator\": ***********, \"bankAccount\": \"***********\", \"subBankName\": \"某某支行\", \"bankName\": \"某银行\", \"invoiceTelephone\": \"*********\", \"department\": \"*********\", \"cust\": ****************, \"invoiceUpcType\": \"0\", \"natMoney\": 3.81, \"currency_priceDigit\": 3, \"invoiceOrg\": \"****************\", \"stockUnit_name\": \"件\", \"collaborationPolineno\": \"\", \"bustype_name\": \"销售出库\", \"modifier\": \"rtduanhy\", \"firstupcode\": \"UO-test20210601000012\", \"source\": \"1\", \"natTax\": 0.19, \"subQty\": 2, \"taxItems\": \"5%\", \"modifyTime\": \"2021-06-02 15:10:23\", \"product_cName\": \"hy母件002\", \"invoiceTitleType\": \"0\", \"receiveContacterPhone\": \"***********\", \"modifyInvoiceType\": \"1\", \"natCurrencyName\": \"人民币\", \"salesOrg_name\": \"hy组织001\", \"modifyDate\": \"2021-06-02 00:00:00\", \"unitName\": \"件\", \"contactName\": \"张三\", \"srcBillNO\": \"******************\", \"oriUnitPrice\": 1.905, \"taxCode\": \"VAT5\", \"barCode\": \"st_salesout|****************\", \"unit_name\": ****************, \"taxRate\": 5, \"unit\": \"件\", \"productsku_cCode\": \"hy母件002\", \"natCurrency_moneyDigit\": 2, \"accountOrg_name\": \"hy组织001\", \"taxId\": \"VAT5\", \"invoiceCust\": ****************, \"qty\": 2, \"unit_Precision\": 2, \"oriTaxUnitPrice\": 2, \"oriMoney\": 3.81, \"contactsPieces\": 2, \"contactsQuantity\": 2, \"natUnitPrice\": 1.905, \"code\": \"XSCK20210601000001\", \"receiveAccountingBasis\": \"voucher_delivery\", \"logistics\": \"XSCK20210601000001\", \"exchRate\": 1, \"currencyName\": \"人民币\", \"cust_name\": \"张三啊\", \"org\": \"****************\", \"priceUOM_name\": \"件\", \"bustype\": \"***************\", \"receiveId\": ****************, \"upcode\": \"******************\", \"saleStyle\": \"SALE\", \"iLogisticId\": 0, \"status_mobile\": 0, \"natTaxUnitPrice\": 2, \"salesOutDefineCharacter\": {}, \"salesOutsDefineCharacter\": {}, \"salesOutsCharacteristics\": {}, \"out_sys_id\": \"\", \"out_sys_code\": \"\", \"out_sys_version\": \"\", \"out_sys_type\": \"\", \"out_sys_rowno\": \"\", \"out_sys_lineid\": \"\", \"collaborationPocode\": \"\", \"collaborationPoid\": 0, \"collaborationPodetailid\": 0, \"collaborationSource\": \"\", \"salesOutsExtend!coUpcode\": \"\", \"salesOutsExtend!coSourceid\": 0, \"salesOutsExtend!coSourceLineNo\": \"\", \"salesOutsExtend!coSourceType\": \"\" } ], \"sumRecordList\": [ { \"totalPieces\": 2, \"oriSum\": 51222, \"invoiceOriSum\": 27922, \"saleReturnQty\": 34, \"natSum\": 51222, \"subQty\": 492, \"totalQuantity\": 2, \"priceQty\": 492, \"qty\": 492, \"oriMoney\": 49705.27, \"invoiceQty\": 298, \"contactsPieces\": 481, \"contactsQuantity\": 481, \"natMoney\": 49705.27 } ], \"pageCount\": 3, \"beginPageIndex\": 1, \"endPageIndex\": 3, \"pubts\": \"2021-06-02 16:37:29\" } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, "apiDemoReturnDTOError": {"id": 2200117974095888423, "apiId": "05a2a46b90984a8ba115d6a2061f8720", "content": "{\"code\":999,\"message\":\"列表查询失败\"}", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}, "errorCodeDTOS": {"errorCodeDTOS": {"id": 2200117974095888419, "apiId": "05a2a46b90984a8ba115d6a2061f8720", "errorCode": 999, "errorMessage": "列表查询失败", "errorType": "API", "errorcodeDesc": "检查查询条件和单据编码是否正确", "gmtCreate": "2025-02-12 10:30:42.000", "gmtUpdate": "2025-02-12 10:30:42.000", "apiName": "", "edit": false, "defErrorId": 1856804444898853217, "ytenantId": "", "displayCodeId": ""}}, "displayCodeApiConfigDTOS": "", "tokenPlugin": "", "paramParsePlugin": "", "authPlugin": {"id": "09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "", "name": "友户通token认证业务扩展插件", "configurable": false, "description": "YonsuitBusinessExtendPlugin", "pluginType": "auth", "pluginTypeName": "业务扩展插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": false, "gmtCreate": "2020-05-22 00:00:00", "gmtUpdate": "2020-05-22 00:00:00", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "05a2a46b90984a8ba115d6a2061f8720", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "resultParsePlugin": {"id": "w181ed01-1e9b-4350-b994-71a66f062522", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "resultParse", "name": "UCG标准返回值解析插件", "configurable": false, "description": "符合UCG标准的返回值会自动解析，不符合的会自动略过", "pluginType": "resultParse", "pluginTypeName": "返回值解析插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.result.UCGResultParsePlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": true, "gmtCreate": "2019-08-19 00:00:00", "gmtUpdate": "", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "05a2a46b90984a8ba115d6a2061f8720", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "mapReturnPluginConfig": "", "billNo": "st_salesout", "domain": "ustock", "apiCategory": "", "docUrl": "", "pathMatch": 0, "createUser": "", "createUserName": "", "approvalStatus": 4, "publishTime": "2025-03-04 20:44:38", "pathJoin": true, "timeOut": 30, "tokenPluginName": "", "authPluginName": "", "resultPluginName": "", "apiDemoReturnRightDemo": "", "apiDemoReturnErrorDemo": "", "mock": false, "mockTimeout": "", "customUrl": "/salesout/list", "fixedUrl": "/yonbip/scm", "apiCode": "05a2a46b90984a8ba115d6a2061f8720", "tokenCheckType": 0, "enableMulti": false, "multiField": "", "idempotent": "non", "bidirectionalSSL": "", "ucgSchema": "HTTPS", "updateUserId": "4dee9132-848b-4d75-9734-ec7364b39cee", "updateUserName": 18631471883, "paramIsForce": "", "userIDPassthrough": true, "applyUser": "", "applyMsg": "", "dr": 0, "microServiceCode": "domain.yonbip-scm-stock", "applicationCode": "yonbip-scm-stock", "privacyCategory": 2, "privacyLevel": 4, "apiDesigned": 0, "serviceType": 0, "integrateSchemeCode": "", "integrateSchemeName": "", "integrateObjectCode": "", "integrateObjectName": "", "integrateObjectCreatedType": "", "returnIntegObjId": "", "returnIntegObjName": "", "apiIntegrateDTOList": "", "apiRouteInfoDTOList": "", "arrayParam": false, "fileSize": "", "cc": true, "paramTransferMode": 2, "ytenantId": 0, "statusConf": "", "scene": 1, "version": "", "bizObjUri": "", "bizObjOperationType": "", "apiDefId": 1856804444898852871, "paramExtBizObjCode": "", "paramExtBizObjName": "", "paramExtRequest": 1, "paramExtResponse": 1, "paramExtInExtendKey": 1, "openScene": 1, "integrationScene": "", "apiType": "", "paramMark": "", "integrateSysId": "", "integrateSysName": "", "integrateSysCode": "", "dataZoneSetting": false, "reqDataZoneSetting": false, "respDataZoneSetting": false, "reqDataAllQuery": false, "reqDataAllBody": false, "respDataAllBody": false, "chargeStatus": 1, "beforeSpeed": 60, "afterSpeed": 120, "speedStatus": false, "reqDataRefPath": "", "respDataRefPath": "", "pubHistory": {"pubHistory": {"id": 2215277808488808453, "apiId": "05a2a46b90984a8ba115d6a2061f8720", "apiName": "销售出库列表查询", "applyReason": "UKC-142902审核更新存量，增加更新存量传财务时机反参描述", "publishUserName": "", "version": 20250304204438, "operationTime": "2025-03-04", "gmtCreate": "", "gmtUpdate": "", "changes": {"changes": [{"changePosition": "baseInfo", "newList": "", "updateList": {"updateList": {"changeProperty": "enableMulti", "oldValue": "", "newValue": false}}, "deleteList": ""}, {"changePosition": "paramDTOS", "newList": "", "updateList": {"updateList": [{"changeProperty": "isdefault", "oldValue": "{\"id\":\"2098131056030908859\",\"name\":\"isdefault\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908482\",\"array\":false,\"paramDesc\":\"该参数可忽略不管\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_isdefault\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953805\",\"name\":\"isdefault\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852873\",\"array\":false,\"paramDesc\":\"该参数可忽略不管\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_isdefault\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "pageIndex", "oldValue": "{\"id\":\"2098131056030908860\",\"name\":\"pageIndex\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908483\",\"array\":false,\"paramDesc\":\"页号\",\"paramType\":\"int\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_pageIndex\",\"example\":\"\",\"fullName\":\"\",\"required\":true,\"defaultValue\":\"1\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953806\",\"name\":\"pageIndex\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852874\",\"array\":false,\"paramDesc\":\"页号\",\"paramType\":\"int\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_pageIndex\",\"example\":\"\",\"fullName\":\"\",\"required\":true,\"defaultValue\":\"1\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "code", "oldValue": "{\"id\":\"2098131056030908861\",\"name\":\"code\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908484\",\"array\":false,\"paramDesc\":\"单据编号\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_code\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953807\",\"name\":\"code\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852875\",\"array\":false,\"paramDesc\":\"单据编号\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_code\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "pageSize", "oldValue": "{\"id\":\"2098131056030908862\",\"name\":\"pageSize\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908485\",\"array\":false,\"paramDesc\":\"每页行数\",\"paramType\":\"int\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_pageSize\",\"example\":\"\",\"fullName\":\"\",\"required\":true,\"defaultValue\":\"10\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953808\",\"name\":\"pageSize\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852876\",\"array\":false,\"paramDesc\":\"每页行数\",\"paramType\":\"int\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_pageSize\",\"example\":\"\",\"fullName\":\"\",\"required\":true,\"defaultValue\":\"10\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "vouchdate", "oldValue": "{\"id\":\"2098131056030908863\",\"name\":\"vouchdate\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908486\",\"array\":false,\"paramDesc\":\"单据日期 区间格式，2021-05-06|2021-05-06 23:00:00。若传入单个时间如：2021-05-06，则查询该时间之后，到当前时间之间的单据\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_vouchdate\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953809\",\"name\":\"vouchdate\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852877\",\"array\":false,\"paramDesc\":\"单据日期 区间格式，2021-05-06|2021-05-06 23:00:00。若传入单个时间如：2021-05-06，则查询该时间之后，到当前时间之间的单据\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_vouchdate\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "stockOrg", "oldValue": "{\"id\":\"2098131056030908864\",\"name\":\"stockOrg\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908487\",\"array\":false,\"paramDesc\":\"库存组织id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_stockOrg\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953810\",\"name\":\"stockOrg\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852878\",\"array\":false,\"paramDesc\":\"库存组织id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_stockOrg\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "salesOrg", "oldValue": "{\"id\":\"2098131056030908865\",\"name\":\"salesOrg\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908488\",\"array\":false,\"paramDesc\":\"销售组织id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_salesOrg\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953811\",\"name\":\"salesOrg\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852879\",\"array\":false,\"paramDesc\":\"销售组织id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_salesOrg\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "invoiceOrg", "oldValue": "{\"id\":\"2098131056030908866\",\"name\":\"invoiceOrg\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908489\",\"array\":false,\"paramDesc\":\"开票组织ID\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_invoiceOrg\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953812\",\"name\":\"invoiceOrg\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852880\",\"array\":false,\"paramDesc\":\"开票组织ID\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_invoiceOrg\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "invoiceCust", "oldValue": "{\"id\":\"2098131056030908867\",\"name\":\"invoiceCust\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908490\",\"array\":false,\"paramDesc\":\"开票客户id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_invoiceCust\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953813\",\"name\":\"invoiceCust\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852881\",\"array\":false,\"paramDesc\":\"开票客户id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_invoiceCust\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "upcode", "oldValue": "{\"id\":\"2098131056030908868\",\"name\":\"upcode\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908491\",\"array\":false,\"paramDesc\":\"来源单据号\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_upcode\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953814\",\"name\":\"upcode\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852882\",\"array\":false,\"paramDesc\":\"来源单据号\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_upcode\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "department", "oldValue": "{\"id\":\"2098131056030908869\",\"name\":\"department\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908492\",\"array\":false,\"paramDesc\":\"部门id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_department\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953815\",\"name\":\"department\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852883\",\"array\":false,\"paramDesc\":\"部门id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_department\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "operator", "oldValue": "{\"id\":\"2098131056030908870\",\"name\":\"operator\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908493\",\"array\":false,\"paramDesc\":\"业务员id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_operator\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953816\",\"name\":\"operator\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852884\",\"array\":false,\"paramDesc\":\"业务员id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_operator\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "warehouse", "oldValue": "{\"id\":\"2098131056030908871\",\"name\":\"warehouse\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908494\",\"array\":false,\"paramDesc\":\"仓库id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_warehouse\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953817\",\"name\":\"warehouse\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852885\",\"array\":false,\"paramDesc\":\"仓库id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_warehouse\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "stockMgr", "oldValue": "{\"id\":\"2098131056030908872\",\"name\":\"stockMgr\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908495\",\"array\":false,\"paramDesc\":\"库管员id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_stockMgr\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953818\",\"name\":\"stockMgr\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852886\",\"array\":false,\"paramDesc\":\"库管员id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_stockMgr\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "cust", "oldValue": "{\"id\":\"2098131056030908873\",\"name\":\"cust\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908496\",\"array\":false,\"paramDesc\":\"客户id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_cust\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2200117965505953819\",\"name\":\"cust\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852887\",\"array\":false,\"paramDesc\":\"客户id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_cust\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "product_cName", "oldValue": "{\"id\":\"2098131056030908874\",\"name\":\"product_cName\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908497\",\"array\":false,\"paramDesc\":\"物料id\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_product_cName\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2200117965505953820\",\"name\":\"product_cName\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852888\",\"array\":false,\"paramDesc\":\"物料id\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_product_cName\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "bustype.name", "oldValue": "{\"id\":\"2098131056030908875\",\"name\":\"bustype.name\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908498\",\"array\":false,\"paramDesc\":\"交易类型名称\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_bustype.name\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2200117965505953821\",\"name\":\"bustype.name\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852889\",\"array\":false,\"paramDesc\":\"交易类型名称\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_bustype.name\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "product_cName_ManageClass", "oldValue": "{\"id\":\"2098131056030908876\",\"name\":\"product_cName_ManageClass\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908499\",\"array\":false,\"paramDesc\":\"物料分类id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_product_cName_ManageClass\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2200117965505953822\",\"name\":\"product_cName_ManageClass\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852890\",\"array\":false,\"paramDesc\":\"物料分类id\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_product_cName_ManageClass\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "isSum", "oldValue": "{\"id\":\"2098131056030908877\",\"name\":\"isSum\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908500\",\"array\":false,\"paramDesc\":\"查询表头\",\"paramType\":\"boolean\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_isSum\",\"example\":\"false\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"false\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2200117965505953823\",\"name\":\"isSum\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852891\",\"array\":false,\"paramDesc\":\"查询表头\",\"paramType\":\"boolean\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_isSum\",\"example\":\"false\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"false\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "simpleVOs", "oldValue": "{\"id\":\"2098131056030908854\",\"name\":\"simpleVOs\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"2098131056030908501\",\"array\":true,\"paramDesc\":\"扩展查询条件\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2200117965505953800\",\"name\":\"simpleVOs\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"defParamId\":\"1856804444898852892\",\"array\":true,\"paramDesc\":\"扩展查询条件\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "op", "oldValue": "{\"id\":\"2098131056030908855\",\"name\":\"op\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"parentId\":\"2098131056030908854\",\"defParamId\":\"2098131056030908502\",\"array\":false,\"paramDesc\":\"比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.op\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2200117965505953801\",\"name\":\"op\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"parentId\":\"2200117965505953800\",\"defParamId\":\"1856804444898852893\",\"array\":false,\"paramDesc\":\"比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.op\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "value2", "oldValue": "{\"id\":\"2098131056030908856\",\"name\":\"value2\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"parentId\":\"2098131056030908854\",\"defParamId\":\"2098131056030908503\",\"array\":false,\"paramDesc\":\"值2(条件)如：\\\"2021-04-19 23:59:59\\\"\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.value2\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2200117965505953802\",\"name\":\"value2\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"parentId\":\"2200117965505953800\",\"defParamId\":\"1856804444898852894\",\"array\":false,\"paramDesc\":\"值2(条件)如：\\\"2021-04-19 23:59:59\\\"\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.value2\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "value1", "oldValue": "{\"id\":\"2098131056030908857\",\"name\":\"value1\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"parentId\":\"2098131056030908854\",\"defParamId\":\"2098131056030908504\",\"array\":false,\"paramDesc\":\"值1(条件)如： \\\"2021-04-19 00:00:00\\\"\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.value1\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2200117965505953803\",\"name\":\"value1\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"parentId\":\"2200117965505953800\",\"defParamId\":\"1856804444898852895\",\"array\":false,\"paramDesc\":\"值1(条件)如： \\\"2021-04-19 00:00:00\\\"\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.value1\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "field", "oldValue": "{\"id\":\"2098131056030908858\",\"name\":\"field\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"parentId\":\"2098131056030908854\",\"defParamId\":\"2098131056030908505\",\"array\":false,\"paramDesc\":\"属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码details.product.cCode、表头自定义项headItem.define1、表体自定义项details.bodyItem.define1等)\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.field\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2200117965505953804\",\"name\":\"field\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"parentId\":\"2200117965505953800\",\"defParamId\":\"1856804444898852896\",\"array\":false,\"paramDesc\":\"属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码details.product.cCode、表头自定义项headItem.define1、表体自定义项details.bodyItem.define1等)\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.field\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}]}, "deleteList": ""}, {"changePosition": "paramReturnDTOS", "newList": {"newList": {"changeProperty": "impactStockTiming", "oldValue": "", "newValue": "{\"id\":\"2200117965505953874\",\"name\":\"impactStockTiming\",\"apiId\":\"05a2a46b90984a8ba115d6a2061f8720\",\"parentId\":\"2200117965505953849\",\"defParamId\":\"2200117965505953798\",\"array\":false,\"paramDesc\":\"更新存量传财务时机,0:保存;1:审核\",\"paramType\":\"string\",\"path\":\"null_data.recordList.impactStockTiming\",\"example\":\"0\"}"}}, "updateList": "", "deleteList": ""}]}}}, "deprecated": 0, "recommendedApiId": "", "recommendedApiName": "", "domainAppCode": "", "multiVersion": 0, "apiTag": ""}}, {"success": true, "code": 200, "message": "", "data": {"id": 2108770660671029249, "name": "用友YonBIP", "type": "integrateSys", "sort": 0, "enable": 0, "children": {"children": {"id": "SCC", "name": "供应链云", "type": 1, "sort": 0, "enable": 0, "children": {"children": {"id": "MM", "name": "采购供应", "type": 2, "sort": 0, "enable": 0, "children": {"children": {"id": "ST", "name": "库存管理", "type": 3, "sort": 0, "enable": 0, "children": {"children": {"id": "ustock.st_salesout", "name": "销售出库单", "type": 4, "sort": 0, "enable": 0, "children": "", "parentId": "", "productId": "", "code": "ustock.st_salesout", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "ST", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "MM", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "SCC", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "current_yonbip_default_sys", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "isOrigin": 0, "hasChildren": 0, "order": 0}}, {"success": true, "code": 200, "message": "", "data": {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:39:47", "gmtUpdate": "2025-07-26 17:39:47", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}}, {"success": true, "code": 200, "message": "", "data": [{"id": "eaf95e8b-6292-444b-bf96-a88e6a9391f5", "name": "SF04", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "发票号（表体）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:39:56", "gmtUpdate": "2025-07-26 17:39:56", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "c28aa2d2-a8a9-4b93-94fb-385ec48ad931", "name": "SF05", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "发货单日期", "paramType": "Date", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "date", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:39:56", "gmtUpdate": "2025-07-26 17:39:56", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "6c24a623-0d0c-4bc7-8ad4-afe5279d28ad", "name": "SF06", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "发货单号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:39:56", "gmtUpdate": "2025-07-26 17:39:56", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "4f3b2a36-4ee4-430a-83b4-e6e1f7c5155f", "name": "XS15", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "顾客订单号（订单表体）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:39:56", "gmtUpdate": "2025-07-26 17:39:56", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "047d4c0e-31a5-48c3-b586-0637b4dc7780", "name": "XS33", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "业务员", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:39:56", "gmtUpdate": "2025-07-26 17:39:56", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 50, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "47ac2996-3cac-4a22-bd41-42a66d08afa8", "name": "XS37", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "含税标准报价特征", "paramType": "Decimal", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "number", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:39:56", "gmtUpdate": "2025-07-26 17:39:56", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 24, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:39:56", "gmtUpdate": "2025-07-26 17:39:56", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": [{"id": "72113971-ae4c-4188-bc55-44b6173f4e0b", "name": "XS15", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "顾客订单号（订单表体）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:40:06", "gmtUpdate": "2025-07-26 17:40:06", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "b946709d-f4d9-4a43-a551-f55beee7f3d5", "name": "XXX0111", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求分类项", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:40:06", "gmtUpdate": "2025-07-26 17:40:06", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:40:06", "gmtUpdate": "2025-07-26 17:40:06", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}]