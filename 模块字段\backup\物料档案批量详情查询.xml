<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>1847241897789620228</id>
<name>物料档案批量详情查询</name>
<apiClassifyId>a503dfd1a91a4d2083a96e8ea04394ec</apiClassifyId>
<apiClassifyName>物料档案</apiClassifyName>
<apiClassifyCode>productcenter.pc_product</apiClassifyCode>
<parentApiClassifies/>
<functionId/>
<openMode>0</openMode>
<description>特征物料档案批量详情查询，批量详情查询最多支持10条数据</description>
<auth>true</auth>
<bodyPassthrough>false</bodyPassthrough>
<healthExam>false</healthExam>
<healthStatus>true</healthStatus>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/digitalModel/product/batchdetailnew</completeProxyUrl>
<connectUrl>/openApi/product/listProductDetail</connectUrl>
<sort>300</sort>
<handler>openapi</handler>
<httpRequestType>POST</httpRequestType>
<openApi>true</openApi>
<preset>true</preset>
<productId>4d12434dd5de42c2b6fffd093e31e074</productId>
<productCode>digitalModel</productCode>
<proxyUrl>/yonbip/digitalModel/product/batchdetailnew</proxyUrl>
<requestParamsDemo>Url: /yonbip/digitalModel/product/batchdetailnew?access_token=访问令牌 Body: [{ "id": 15309767***********, "productCode": "code1", "orgId": "2538984869630976", "orgCode": "XMGS" }]</requestParamsDemo>
<requestProtocol>HTTP</requestProtocol>
<serviceHttpMethod>POST</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2023-10-25 23:21:00.000</gmtCreate>
<gmtUpdate>2025-07-17 18:32:11.000</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/digitalModel/product/batchdetailnew</address>
<productName>数字化建模</productName>
<productClassifyId>yonsuite</productClassifyId>
<productClassifyCode>yonbip</productClassifyCode>
<productClassifyName>用友YonBIP</productClassifyName>
<paramDTOS>
<paramDTOS>
<id>2268509272355110924</id>
<name>id</name>
<apiId>1847241897789620228</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料档案id</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>15309767***********</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2268509272355110926</id>
<name>productCode</name>
<apiId>1847241897789620228</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料编码(物料id和编码二选一，同时填入以id为准)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>code1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2268509272355110928</id>
<name>orgId</name>
<apiId>1847241897789620228</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>组织id(组织id和编码二选一必填，同时填入时以id为准) 示例：666666"</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2538984869630976</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2268509272355110930</id>
<name>orgCode</name>
<apiId>1847241897789620228</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>组织编码(组织id和编码二选一必填，同时填入时以id为准) 示例：666666</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>XMGS</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>2268509272355111506</id>
<name>id</name>
<apiId>1847241897789620228</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料档案id</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>id</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2268509272355111508</id>
<name>productCode</name>
<apiId>1847241897789620228</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料编码(物料id和编码二选一，同时填入以id为准)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>productCode</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2268509272355111510</id>
<name>orgId</name>
<apiId>1847241897789620228</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>组织id(组织id和编码二选一必填，同时填入时以id为准) 示例：666666"</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>orgId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2268509272355111512</id>
<name>orgCode</name>
<apiId>1847241897789620228</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>组织编码(组织id和编码二选一必填，同时填入时以id为准) 示例：666666</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>orgCode</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>2268509272355110932</id>
<name>code</name>
<apiId>1847241897789620228</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>返回码，调用成功时返回200</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>200</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2268509272355110934</id>
<name>message</name>
<apiId>1847241897789620228</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>调用失败时的错误信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>操作成功</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2268509272355110936</id>
<name>data</name>
<apiId>1847241897789620228</apiId>
<parentId/>
<children>
<children>
<id>2268509272355110942</id>
<name>detail</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<children>
<children>
<id>2268509272355110944</id>
<name>id</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料详情id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1530976723880902700</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110946</id>
<name>productId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>15309767***********</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110950</id>
<name>productApplyRangeId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>适用范围id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1530976723880902700</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110952</id>
<name>purchaseUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>采购单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110954</id>
<name>purchaseUnitCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>采购单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>KGM</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110956</id>
<name>purchaseUnitName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>采购单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110958</id>
<name>purchasePriceUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>采购计价单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110960</id>
<name>purchasePriceUnitCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>采购计价单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>KGM</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110962</id>
<name>purchasePriceUnitName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>采购计价单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110964</id>
<name>stockUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110966</id>
<name>stockUnitCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>KGM</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110968</id>
<name>stockUnitName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110970</id>
<name>batchUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>批发销售单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110972</id>
<name>batchUnitCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>批发销售单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>KGM</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110974</id>
<name>batchUnitName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>批发销售单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110976</id>
<name>onlineUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>线上零售单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110978</id>
<name>onlineUnitCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>线上零售单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>KGM</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110980</id>
<name>onlineUnitName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>线上零售单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110982</id>
<name>offlineUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>线下零售单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110984</id>
<name>offlineUnitCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>线下零售单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>KGM</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110986</id>
<name>offlineUnitName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>线下零售单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110988</id>
<name>requireUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>要货单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110990</id>
<name>requireUnitCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>要货单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>KGM</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110992</id>
<name>requireUnitName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>要货单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110994</id>
<name>batchPriceUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>批发计价单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110996</id>
<name>batchPriceUnitCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>批发计价单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>KGM</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355110998</id>
<name>batchPriceUnitName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>批发计价单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111000</id>
<name>inspectionUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>检验单位</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111002</id>
<name>inspectionUnitCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>检验单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>KGM</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111004</id>
<name>inspectionUnitName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>检验单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111006</id>
<name>batchPrice</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>批发价</paramDesc>
<paramType>Decimal</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111008</id>
<name>markPrice</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>建议零售价</paramDesc>
<paramType>Decimal</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111010</id>
<name>lowestMarkPrice</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>最低零售价</paramDesc>
<paramType>Decimal</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111012</id>
<name>salePrice</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>线上零售价</paramDesc>
<paramType>Decimal</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111014</id>
<name>marketPrice</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>市场价</paramDesc>
<paramType>Decimal</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111016</id>
<name>primeCosts</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>采购参考价</paramDesc>
<paramType>Decimal</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111018</id>
<name>settleAccountsRate</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>结算费率</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111020</id>
<name>displayPrice</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>线上显示价格, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111022</id>
<name>batchManage</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>批次管理, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111024</id>
<name>expiryDateManage</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>有效期管理, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111026</id>
<name>serialNoManage</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>序列号管理, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111028</id>
<name>safetyStock</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>安全库存</paramDesc>
<paramType>Decimal</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111030</id>
<name>highestStock</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>最高库存</paramDesc>
<paramType>Decimal</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111032</id>
<name>lowestStock</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>最低库存</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111034</id>
<name>ropStock</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>再订货点</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111036</id>
<name>canSale</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>B2B是否可售, true:是，false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111038</id>
<name>minOrderQuantity</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>起订量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111040</id>
<name>deliveryDays</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>交货周期</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111042</id>
<name>enableCyclePurchase</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>启用周期购，true：是、false：否。</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111044</id>
<name>enableDeposit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>启用定金业务, true:是、false:否。</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111046</id>
<name>depositDealPayType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>定金设置方式, 0:固定金额、1:成交金额百分比</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111048</id>
<name>enableModifyDeposit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>订单改价时可修改定金, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111050</id>
<name>depositPayType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>支付尾款方式, 0:线上支付尾款、1:线下支付尾款</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111052</id>
<name>metaDescription</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<children>
<children>
<id>2268509272355111054</id>
<name>simplifiedName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111052</parentId>
<defParamId/>
<array>false</array>
<paramDesc>简体中文</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>搜索简介</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>搜索简介</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-05-15 14:07:18.598</gmtCreate>
<gmtUpdate>2025-05-15 14:07:18.598</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111056</id>
<name>baseSaleCount</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>初始销量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111058</id>
<name>enableContractManagement</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>ECN管控，true：是，false：否。</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111060</id>
<name>allowStorePurchase</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>允许门店自采, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111062</id>
<name>priceChangeAllowed</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>允许开单改价, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111064</id>
<name>saleInOfflineStore</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>允许门店销售, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111066</id>
<name>offlineStoreOrder</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>允许门店要货, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111068</id>
<name>offlineStoreReturn</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>允许门店退货, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111070</id>
<name>weighingOrNot</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>是否称重, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>62</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111072</id>
<name>process</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>加工, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>63</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111074</id>
<name>material</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>材料, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>64</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111076</id>
<name>retailPriceDimension</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>零售价取价维度, 1:物料、2:物料SKU</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>65</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111078</id>
<name>deliverQuantityChange</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>交货数量改变时, 1:单价不变重算金额、2:金额不变重算单价</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>66</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111080</id>
<name>noTaxCostPrice</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>参考成本</paramDesc>
<paramType>Decimal</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>67</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111082</id>
<name>checkByBatch</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>按批次核算, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>68</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111084</id>
<name>accountingByItem</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>按单品核算</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>69</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111086</id>
<name>storeOffAndOffState</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>商城上架, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>70</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111088</id>
<name>orderLoadAndUnloadStatus</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>订货上架, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>71</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111090</id>
<name>mallUpCount</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>商城上架数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>72</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111092</id>
<name>mallDownCount</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>商城下架数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>73</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111094</id>
<name>orderUpCount</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>U订货上架数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>74</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111096</id>
<name>orderDownCount</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>U订货下架数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>75</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111098</id>
<name>tenant</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>租户ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>76</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111100</id>
<name>saleChannel</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>销售渠道, 1:销售批发、2:线上零售、3:线下零售、4:微分销</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1,2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>77</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111102</id>
<name>barCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>条形码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>条形码</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>78</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111104</id>
<name>stopStatus</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>启用状态，true代表停用，false代表启用</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>79</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111106</id>
<name>checkFree</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>按规格核算, 0:不按规格核算、1:指定规格核算、2:按SKU核算、</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>80</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111108</id>
<name>canOrder</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>可预约, true:是、false:否、</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>81</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111110</id>
<name>onlyOrder</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>仅预约, true:是、false:否、</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>82</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111112</id>
<name>orderAdvanceTime</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>预约提前期, 0:无、1:一天、2:两天、3:三天、4:一周、5:两周、6:一月、</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>83</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111114</id>
<name>valueManageType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>价值管理模式, 99:费用、0:存货核算、1:固定资产</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>84</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111116</id>
<name>costValuation</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>成本计价方法, 0:先进先出法、1:移动平均法、2:全月平均法</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>85</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111118</id>
<name>checkByCost</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>按费用核算, true:是、false:否、yCost</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>86</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111120</id>
<name>materialCost</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>ma材料费用化, true:是、false:否、terialCost</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>87</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111122</id>
<name>planDefaultAttribute</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划默认属性, 1:采购，3:自制，5：委外。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>88</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111124</id>
<name>planMethod</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划方法, 0：MRP/LRP、1:：N-不计划、10：库存计划、5：MPS。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>89</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111126</id>
<name>keySubPart</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>关键子件, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>90</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111128</id>
<name>supplyDemandPolicy</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>供需策略, 0:PE、1:LP、</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>91</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111130</id>
<name>fixedLeadTime</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>固定提前期</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>92</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111132</id>
<name>supplyType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>供应类型, 0：领用、1：入库倒冲、2：不发料、5：开工倒冲、6：工序完工倒冲 、7：完工倒冲。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>93</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111134</id>
<name>produceDepartment</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产部门ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>94</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111136</id>
<name>produceDepartmentCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产部门编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0002</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>95</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111138</id>
<name>produceDepartmentName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产部门名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>销售部门</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>96</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111140</id>
<name>manufacturePlanner</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划员id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>*****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>97</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111142</id>
<name>manufacturePlannerCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划员编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>A000001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>98</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111144</id>
<name>manufacturePlannerName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划员名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>甘甘</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>99</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111146</id>
<name>engineeringDrawingNo</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工程图号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>123</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>100</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111148</id>
<name>planProduceLimit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划下达超量上限</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>101</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111150</id>
<name>utility</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>公用工程, true:是、false:否、</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>102</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111152</id>
<name>weigh</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>是否过磅, true:是、false:否、</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>103</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111154</id>
<name>productVendor</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>供应商id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>104</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111156</id>
<name>productVendorCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>供应商编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>**********</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>105</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111158</id>
<name>productVendorName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>供应商名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商测试</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>106</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111160</id>
<name>productBuyer</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>采购员id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>*****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>107</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111162</id>
<name>productBuyerCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>采购员编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>A000001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>108</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111164</id>
<name>productBuyerName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>采购员名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>甘甘</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>109</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111166</id>
<name>maxPrimeCosts</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>最高进价</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>110</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111168</id>
<name>requestOrderLimit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>请购订货超量上限</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>111</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111170</id>
<name>enableSubscribe</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>启用预订业务, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>112</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111172</id>
<name>recommend</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>推荐物料, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>113</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111174</id>
<name>erpOuterCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>商家商品外部编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>商家商品外部编码</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>114</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111176</id>
<name>saleStyle</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>销售方式, 1:现金购买、2:积分兑换</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>115</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111178</id>
<name>shortName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料简称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>物料简称</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>116</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111180</id>
<name>mnemonicCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>助记码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>助记码</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>117</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111182</id>
<name>receiptName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<children>
<children>
<id>2268509272355111184</id>
<name>simplifiedName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111182</parentId>
<defParamId/>
<array>false</array>
<paramDesc>简体中文</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>开票名称</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>开票名称</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>118</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-05-15 14:07:18.618</gmtCreate>
<gmtUpdate>2025-05-15 14:07:18.618</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111186</id>
<name>incomeTaxRates</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>进项税率id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2538775356445954</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>119</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111188</id>
<name>outputTaxRate</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>销项税率id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2538775356445954</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>120</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111190</id>
<name>produceUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>121</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111192</id>
<name>produceUnitCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>KGM</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>122</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111194</id>
<name>produceUnitName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>123</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111196</id>
<name>warehouseManager</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库管员id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>*****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>124</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111198</id>
<name>warehouseManagerCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库管员编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>A000001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>125</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111200</id>
<name>warehouseManagerName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库管员名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>甘甘</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>126</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111202</id>
<name>deliveryWarehouse</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>发货仓库id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1519760519882866700</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>127</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111204</id>
<name>deliveryWarehouseCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>发货仓库编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>000133</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>128</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111206</id>
<name>deliveryWarehouseName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>发货仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>rewrw</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>129</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111208</id>
<name>returnWarehouse</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>退货仓库id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1519760519882866700</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>130</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111210</id>
<name>returnWarehouseCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>退货仓库编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>000133</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>131</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111212</id>
<name>returnWarehouseName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>退货仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>rewrw</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>132</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111214</id>
<name>inStoreExcessLimit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>入库超量上限</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>133</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111216</id>
<name>outStoreExcessLimit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>出库超量上限</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>134</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111218</id>
<name>storageLossRate</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>保管损耗率</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>135</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111220</id>
<name>allowNegativeInventory</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>允许负库存, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>136</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111222</id>
<name>scanCountUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>扫码计数单位，0：主计量单位，1：库存单位。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>137</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111224</id>
<name>exemption</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>免检, true:是、false:否、</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>138</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111226</id>
<name>warehousingByResult</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>根据检验结果入库，true代表是，false代表否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>139</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111228</id>
<name>salesReturnsExemption</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>销售退货免检, true:是、false:否、</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>140</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111230</id>
<name>returnsWarehousingByResult</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>退货根据检验结果入库, true:是、false:否、</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>141</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111232</id>
<name>periodicalInspection</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>定期检验, true:是、false:否、</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>142</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111234</id>
<name>displayName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>显示名称</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>143</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111236</id>
<name>titleMemo</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>卖点</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>144</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111238</id>
<name>barcodeManage</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>条码管理, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>145</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111240</id>
<name>receiptWarehouse</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>收货仓库id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1519760519882866700</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>146</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111242</id>
<name>receiptWarehouseCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>收货仓库编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>000133</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>147</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111244</id>
<name>receiptWarehouseName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>收货仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>rewrw</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>148</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111246</id>
<name>BOMType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料BOM类型，0：标准件，5：计划件。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>149</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111248</id>
<name>batchRule</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>批量规则,0：直接批量、5：经济批量、10：固定批量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>150</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111250</id>
<name>fixedQuantity</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>固定批量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>151</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111252</id>
<name>prepareFeed</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>是否长周期备料，true：是，false：否。</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>152</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111254</id>
<name>specialMaterials</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>是否专用料，true：是，false：否。</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>153</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111256</id>
<name>virtualPart</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>是否虚拟件，true：是，false：否。</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>154</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111258</id>
<name>demandConsolidation</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料需求合并，0：空，5：是，10：否。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>155</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111260</id>
<name>demandConsolidationType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>需求合并类型，0：固定，10：动态。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>156</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111262</id>
<name>demandConsolidationUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>需求合并时格，0：日，10：月，15：月。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>157</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111264</id>
<name>demandConsolidationNumber</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>需求合并时格数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>158</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111266</id>
<name>demandConsolidationDateType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>需求合并日，0：需求首日，1：期间首日。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>159</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111268</id>
<name>reservation</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>可预留，true：是，false：否。</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>160</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111270</id>
<name>lossType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>损耗类型，0：无损耗，5：固定损耗，10：变动损耗。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>161</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111272</id>
<name>ECNControl</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>ECN管控，true：是，false：否。</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>162</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111274</id>
<name>ytenantId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>友互通id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0000KWJZLURO8TQU4Y0000</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>163</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111276</id>
<name>inspectionType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>检验，1代表是，0代表否</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>164</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111278</id>
<name>logisticsRelated</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物流相关，true：是，false：否。</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>165</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111280</id>
<name>weighingMode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>称重方式，1：是，0：否。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>166</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111282</id>
<name>reviewGrossWeight</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>复核毛重，true：是，false：否。</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>167</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111284</id>
<name>specialCarTransport</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>专车运输，true：是，false：否。</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>168</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111286</id>
<name>orgId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>创建组织ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2538984869630976</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>169</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111288</id>
<name>businessAttribute</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>业务属性, 1:采购、7:销售、3:自制、2:委外</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1,7</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>170</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111290</id>
<name>businessAttributePurchase</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>业务属性-采购，1: 是，0：否。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>171</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111292</id>
<name>businessAttributeSale</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>业务属性-销售，1: 是，0：否。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>172</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111294</id>
<name>businessAttributeSelfCreate</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>业务属性-自制，1: 是，0：否。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>173</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111296</id>
<name>businessAttributeOutSourcing</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>业务属性-委外，1: 是，0：否。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>174</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111298</id>
<name>testRule</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>检验规则，0代表按物料检验，1代表按检验项目检验</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>175</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111300</id>
<name>enableStockPeriodRecheck</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>启用库存周期复检，1:启用，0: 停用。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>176</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111302</id>
<name>enableStockExpireCheck</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>启用库存临期检验，1：是，0：否。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>177</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111304</id>
<name>enableSparePartsManagement</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>启用备件管理，1：是，0：否。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>178</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111306</id>
<name>fullSetInspection</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>齐套检查，1：是，0：否。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>179</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111308</id>
<name>directProduction</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>是否直接生产，1：是，0：否。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>180</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111310</id>
<name>costItems</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>费用项目id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1978437901431078916</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>181</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111312</id>
<name>costItemsCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>费用项目编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>ZDH_FYXM</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>182</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111314</id>
<name>costItemsName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>费用项目名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>自动化_费用项目</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>183</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111316</id>
<name>manageByInventory</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>按项目管理库存（0表示不开启，1表示开启）</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>184</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111318</id>
<name>checkByProject</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>按项目核算（0表示不开启，1表示开启）</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>185</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111320</id>
<name>checkBySalesOrders</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>按销售订单核算（0表示不开启，1表示开启）</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>186</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111322</id>
<name>checkByClient</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>按客户核算（0表示不开启，1表示开启）</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>187</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111324</id>
<name>checkByOutsourcing</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>按委外商核算（0表示不开启，1表示开启）</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>188</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111326</id>
<name>atpInspection</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>ATP检查（0表示否，1表示是）</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>189</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111328</id>
<name>doublePick</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110942</parentId>
<defParamId/>
<array>false</array>
<paramDesc>领料倍量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>190</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>19</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>物料详情</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-05-15 14:07:18.590</gmtCreate>
<gmtUpdate>2025-05-15 14:07:18.590</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111330</id>
<name>productOrges</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<children>
<children>
<id>2268509272355111332</id>
<name>orgId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111330</parentId>
<defParamId/>
<array>false</array>
<paramDesc>组织ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2538984869630976</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111334</id>
<name>orgType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111330</parentId>
<defParamId/>
<array>false</array>
<paramDesc>组织类型，1：普通组织，2：商家组织，3：客户组织。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>物料分配的组织。</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-05-15 14:07:18.684</gmtCreate>
<gmtUpdate>2025-05-15 14:07:18.684</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111336</id>
<name>realProductAttribute</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料性质，1：实物物料，2：虚拟物料</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111338</id>
<name>code</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>code1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111340</id>
<name>name</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<children>
<children>
<id>2268509272355111342</id>
<name>simplifiedName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111340</parentId>
<defParamId/>
<array>false</array>
<paramDesc>简体中文</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>simplifiedName1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-05-15 14:07:18.709</gmtCreate>
<gmtUpdate>2025-05-15 14:07:18.709</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111344</id>
<name>model</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>型号</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111346</id>
<name>keywords</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>关键字</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111348</id>
<name>productClass</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>商品分类id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2541495026652160</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111350</id>
<name>productClassCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>商品分类编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>000002</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111352</id>
<name>productClassName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>商品分类名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>黄金</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111354</id>
<name>manageClass</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料分类id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2541494503052544</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111356</id>
<name>manageClassCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料分类编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>000002</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111358</id>
<name>manageClassName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料分类名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>黄金</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111360</id>
<name>purchaseClass</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>采购分类ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1546426425542180900</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111362</id>
<name>purchaseClassCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>采购分类编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>PTO001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111364</id>
<name>purchaseClassName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>采购分类名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>PTO商品</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111366</id>
<name>productTemplate</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料模板id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2541493655655424</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111368</id>
<name>brand</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>品牌id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2570445127045635</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111370</id>
<name>brandCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>品牌编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>品牌2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111372</id>
<name>brandName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>品牌名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>品牌2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111374</id>
<name>placeOfOrigin</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>产地</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>产地</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111376</id>
<name>manufacturer</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产厂商</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>生产厂商</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111378</id>
<name>productLine</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>产品线ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2570288159150336</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111380</id>
<name>shareDescription</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>分享说明</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111382</id>
<name>unit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量单位ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111384</id>
<name>unitCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>KGM</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111386</id>
<name>unitName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111388</id>
<name>taxClass</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>税收分类码id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1010105000000000000</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111390</id>
<name>orgId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>组织ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2538984869630976</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111392</id>
<name>defaultSKUId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>默认SKUID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1530976723880902700</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111394</id>
<name>id</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>15309767***********</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111396</id>
<name>tenant</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>租户id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111398</id>
<name>erpCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>外部编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>外部编码</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111400</id>
<name>deleted</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>是否删除，1：是，0：否。</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111402</id>
<name>createTime</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>创建时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2022-08-25 20:05:48</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111404</id>
<name>createDate</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>创建日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2022-08-25 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111406</id>
<name>creator</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>创建人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>YonSuite默认用户</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111408</id>
<name>realProductAttributeType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>实物物料属性，1：普通物料，2：实体卡券，3：实体储值卡，20：描述性物料，4：设备</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111410</id>
<name>weight</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>毛重</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111412</id>
<name>weightUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>毛重单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111414</id>
<name>weightUnitCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>毛重单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>KGM</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111416</id>
<name>weightUnitName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>毛重单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111418</id>
<name>volume</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>体积</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111420</id>
<name>volumeUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>体积单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2571526900585472</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111422</id>
<name>unitUseType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>设置规则, 1:使用物料模板的计量单位、2:使用物料自己的计量单位</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111424</id>
<name>enableAssistUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>启用辅计量, true:启用</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111426</id>
<name>creatorId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>创建人</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2538773096593408</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111428</id>
<name>registrationManager</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>注册证管理, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111430</id>
<name>authorizationManager</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>授权书管理, true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111432</id>
<name>planClass</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划分类id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2570383093256707</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111434</id>
<name>planClassCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划分类编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>wxt0009</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111436</id>
<name>planClassName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划分类名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>计划分类9</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111438</id>
<name>ytenantId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>友互通id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0000KWJZLURO8TQU4Y0000</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111440</id>
<name>transType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料类型ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2538775354987782</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111442</id>
<name>transTypeCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料类型编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>SYCSR002</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111444</id>
<name>transTypeName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料类型名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>通用物料</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111446</id>
<name>productFamily</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>产品族，1：是，0：否。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111448</id>
<name>salesAndOperations</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>参与SOP，1：是，0：否。</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111450</id>
<name>productBarCodes</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<children>
<children>
<id>2268509272355111452</id>
<name>id</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111450</parentId>
<defParamId/>
<array>false</array>
<paramDesc>多条码id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1680013386205102085</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111454</id>
<name>barCode</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111450</parentId>
<defParamId/>
<array>false</array>
<paramDesc>条码值</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>"123"</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>物料多条码</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-05-15 14:07:18.724</gmtCreate>
<gmtUpdate>2025-05-15 14:07:18.724</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111456</id>
<name>productAssistUnitExchanges</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<children>
<children>
<id>2268509272355111458</id>
<name>id</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111456</parentId>
<defParamId/>
<array>false</array>
<paramDesc>辅计量换算对照id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1680014013270327299</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111460</id>
<name>productId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111456</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1679287639833313282</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111462</id>
<name>unitExchangeType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111456</parentId>
<defParamId/>
<array>false</array>
<paramDesc>换算方式，0固定换算，1浮动换算</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111464</id>
<name>assistUnitCount</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111456</parentId>
<defParamId/>
<array>false</array>
<paramDesc>辅计量数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111466</id>
<name>assistUnit</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111456</parentId>
<defParamId/>
<array>false</array>
<paramDesc>辅计量单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1476511169941864456</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111468</id>
<name>mainUnitCount</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111456</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>23</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>物料辅计量换算对照</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-05-15 14:07:18.746</gmtCreate>
<gmtUpdate>2025-05-15 14:07:18.746</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111470</id>
<name>productAttachments</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<children>
<children>
<id>2268509272355111472</id>
<name>productId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111470</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>15309767***********</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111474</id>
<name>folder</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111470</parentId>
<defParamId/>
<array>false</array>
<paramDesc>附件地址</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>https://apcom-file-pub-npro.obs.cn-north-4.myhuaweicloud.com/iuap-apcom-file-public/iuap-apcom-file/0000L7PIX0Z2ARJ3MD0000/2024052219/78b79e63-39b2-4a7c-a15c-0a25ec2cd109.jpg</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111476</id>
<name>fileName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111470</parentId>
<defParamId/>
<array>false</array>
<paramDesc>附件名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>鲜花10.jpg</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>物料附件</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>62</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-05-15 14:07:18.776</gmtCreate>
<gmtUpdate>2025-05-15 14:07:18.776</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111478</id>
<name>productAlbums</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<children>
<children>
<id>2268509272355111480</id>
<name>productId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111478</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>15309767***********</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111482</id>
<name>folder</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111478</parentId>
<defParamId/>
<array>false</array>
<paramDesc>图片地址</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>https://apcom-file-pub-npro.obs.cn-north-4.myhuaweicloud.com/iuap-apcom-file-public/iuap-apcom-file/0000L7PIX0Z2ARJ3MD0000/2024052219/dbb52e2b-8afb-48fb-8b8e-ef062a536b27.jpeg</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111484</id>
<name>imgName</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111478</parentId>
<defParamId/>
<array>false</array>
<paramDesc>图片名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>鲜花1.jpeg</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>物料图片</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>63</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-05-15 14:07:18.797</gmtCreate>
<gmtUpdate>2025-05-15 14:07:18.797</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111486</id>
<name>imgBusinessId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料图片业务id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>df6a101d-d11f-4971-95d4-1a9db4df9e1e</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>64</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111488</id>
<name>videoBusinessId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料视频业务id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>e78c2cd4-7685-65a5-c396-bc1354de7c25</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>65</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111490</id>
<name>homepageBusinessId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料首页图片业务id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>c16b0bf4-2941-43a4-b382-ab1167de5b17</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>66</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111492</id>
<name>productTags</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<children>
<children>
<id>2268509272355111494</id>
<name>id</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111492</parentId>
<defParamId/>
<array>false</array>
<paramDesc>标签分配关系id</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1913058646531506185</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>19</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111496</id>
<name>tagId</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355111492</parentId>
<defParamId/>
<array>false</array>
<paramDesc>标签id</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1837964897149255683</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>19</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>物料标签</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>67</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-05-15 14:07:18.836</gmtCreate>
<gmtUpdate>2025-05-15 14:07:18.836</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111498</id>
<name>optionalType</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>选配方式（0表示特征选配，1表示组件选配）</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>68</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111500</id>
<name>length</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>长</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>10.5</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>69</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111502</id>
<name>width</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>宽</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>10.5</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>70</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2268509272355111504</id>
<name>height</name>
<apiId>1847241897789620228</apiId>
<parentId>2268509272355110936</parentId>
<defParamId/>
<array>false</array>
<paramDesc>高</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>10.5</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>71</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>调用成功时的返回数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-05-15 14:07:18.582</gmtCreate>
<gmtUpdate>2025-05-15 14:07:18.582</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2268509272355110920</id>
<apiId>1847241897789620228</apiId>
<content>{ "code": "200", "message": "操作成功", "data": [ { "productCharacterDef": {}, "productPropCharacterDefine": {}, "detail": { "id": 1530976723880902700, "productId": 15309767***********, "productExtendCharacterDef": {}, "productApplyRangeId": 1530976723880902700, "purchaseUnit": ****************, "purchaseUnitCode": "KGM", "purchaseUnitName": "千克", "purchasePriceUnit": ****************, "purchasePriceUnitCode": "KGM", "purchasePriceUnitName": "千克", "stockUnit": ****************, "stockUnitCode": "KGM", "stockUnitName": "千克", "batchUnit": ****************, "batchUnitCode": "KGM", "batchUnitName": "千克", "onlineUnit": ****************, "onlineUnitCode": "KGM", "onlineUnitName": "千克", "offlineUnit": ****************, "offlineUnitCode": "KGM", "offlineUnitName": "千克", "requireUnit": ****************, "requireUnitCode": "KGM", "requireUnitName": "千克", "batchPriceUnit": ****************, "batchPriceUnitCode": "KGM", "batchPriceUnitName": "千克", "inspectionUnit": ****************, "inspectionUnitCode": "KGM", "inspectionUnitName": "千克", "batchPrice": 0, "markPrice": 100, "lowestMarkPrice": 0, "salePrice": 100, "marketPrice": 100, "primeCosts": 100, "settleAccountsRate": 10, "displayPrice": true, "batchManage": true, "expiryDateManage": false, "serialNoManage": false, "safetyStock": 100, "highestStock": 100, "lowestStock": 100, "ropStock": 100, "canSale": true, "minOrderQuantity": 100, "deliveryDays": 1, "enableCyclePurchase": false, "enableDeposit": false, "depositDealPayType": 0, "enableModifyDeposit": false, "depositPayType": 0, "metaDescription": { "simplifiedName": "搜索简介" }, "baseSaleCount": 100, "enableContractManagement": false, "allowStorePurchase": true, "priceChangeAllowed": false, "saleInOfflineStore": true, "offlineStoreOrder": true, "offlineStoreReturn": true, "weighingOrNot": false, "process": false, "material": false, "retailPriceDimension": 1, "deliverQuantityChange": 1, "noTaxCostPrice": 100, "checkByBatch": false, "accountingByItem": false, "storeOffAndOffState": false, "orderLoadAndUnloadStatus": false, "mallUpCount": 0, "mallDownCount": 1, "orderUpCount": 0, "orderDownCount": 1, "tenant": ****************, "saleChannel": "1,2", "barCode": "条形码", "stopStatus": false, "checkFree": 0, "canOrder": false, "onlyOrder": false, "orderAdvanceTime": 0, "valueManageType": 0, "costValuation": 0, "checkByCost": false, "materialCost": false, "planDefaultAttribute": 1, "planMethod": 0, "keySubPart": false, "supplyDemandPolicy": 0, "fixedLeadTime": 10, "supplyType": 0, "produceDepartment": "****************", "produceDepartmentCode": "0002", "produceDepartmentName": "销售部门", "manufacturePlanner": "*****************", "manufacturePlannerCode": "A000001", "manufacturePlannerName": "甘甘", "engineeringDrawingNo": "123", "planProduceLimit": 1, "utility": false, "weigh": false, "productVendor": ****************, "productVendorCode": "**********", "productVendorName": "供应商测试", "productBuyer": "*****************", "productBuyerCode": "A000001", "productBuyerName": "甘甘", "maxPrimeCosts": 100, "requestOrderLimit": 100, "enableSubscribe": false, "recommend": false, "erpOuterCode": "商家商品外部编码", "saleStyle": "1", "shortName": "物料简称", "mnemonicCode": "助记码", "receiptName": { "simplifiedName": "开票名称" }, "incomeTaxRates": "2538775356445954", "outputTaxRate": "2538775356445954", "produceUnit": ****************, "produceUnitCode": "KGM", "produceUnitName": "千克", "warehouseManager": "*****************", "warehouseManagerCode": "A000001", "warehouseManagerName": "甘甘", "deliveryWarehouse": 1519760519882866700, "deliveryWarehouseCode": "000133", "deliveryWarehouseName": "rewrw", "returnWarehouse": 1519760519882866700, "returnWarehouseCode": "000133", "returnWarehouseName": "rewrw", "inStoreExcessLimit": 10, "outStoreExcessLimit": 10, "storageLossRate": 10, "allowNegativeInventory": true, "scanCountUnit": 0, "exemption": false, "warehousingByResult": false, "salesReturnsExemption": false, "returnsWarehousingByResult": false, "periodicalInspection": false, "displayName": {}, "titleMemo": {}, "barcodeManage": true, "receiptWarehouse": 1519760519882866700, "receiptWarehouseCode": "000133", "receiptWarehouseName": "rewrw", "BOMType": 0, "batchRule": 0, "fixedQuantity": 100, "prepareFeed": false, "specialMaterials": false, "virtualPart": false, "demandConsolidation": 0, "demandConsolidationType": 0, "demandConsolidationUnit": 0, "demandConsolidationNumber": 1, "demandConsolidationDateType": 0, "reservation": false, "lossType": 0, "ECNControl": false, "ytenantId": "0000KWJZLURO8TQU4Y0000", "inspectionType": 0, "logisticsRelated": false, "weighingMode": 0, "reviewGrossWeight": false, "specialCarTransport": false, "orgId": "2538984869630976", "businessAttribute": "1,7", "businessAttributePurchase": 1, "businessAttributeSale": 1, "businessAttributeSelfCreate": 0, "businessAttributeOutSourcing": 0, "testRule": 1, "enableStockPeriodRecheck": 0, "enableStockExpireCheck": 0, "enableSparePartsManagement": 0, "fullSetInspection": 0, "directProduction": 0, "costItems": "1978437901431078916", "costItemsCode": "ZDH_FYXM", "costItemsName": "自动化_费用项目", "manageByInventory": 0, "checkByProject": 0, "checkBySalesOrders": 0, "checkByClient": 0, "checkByOutsourcing": 0, "atpInspection": 0, "doublePick": 10 }, "productOrges": [ { "orgId": "2538984869630976", "orgType": 1 } ], "realProductAttribute": 1, "code": "code1", "name": { "simplifiedName": "simplifiedName1" }, "model": {}, "keywords": {}, "productClass": 2541495026652160, "productClassCode": "000002", "productClassName": "黄金", "manageClass": 2541494503052544, "manageClassCode": "000002", "manageClassName": "黄金", "purchaseClass": 1546426425542180900, "purchaseClassCode": "PTO001", "purchaseClassName": "PTO商品", "productTemplate": 2541493655655424, "brand": 2570445127045635, "brandCode": "品牌2", "brandName": "品牌2", "placeOfOrigin": "产地", "manufacturer": "生产厂商", "productLine": 2570288159150336, "shareDescription": {}, "unit": ****************, "unitCode": "KGM", "unitName": "千克", "taxClass": "1010105000000000000", "orgId": "2538984869630976", "defaultSKUId": 1530976723880902700, "id": 15309767***********, "tenant": ****************, "erpCode": "外部编码", "deleted": false, "createTime": "2022-08-25 20:05:48", "createDate": "2022-08-25 00:00:00", "creator": "YonSuite默认用户", "realProductAttributeType": 1, "weight": 1, "weightUnit": ****************, "weightUnitCode": "KGM", "weightUnitName": "千克", "volume": 2, "volumeUnit": 2571526900585472, "unitUseType": 2, "enableAssistUnit": false, "creatorId": 2538773096593408, "registrationManager": false, "authorizationManager": false, "planClass": 2570383093256707, "planClassCode": "wxt0009", "planClassName": "计划分类9", "ytenantId": "0000KWJZLURO8TQU4Y0000", "transType": "2538775354987782", "transTypeCode": "SYCSR002", "transTypeName": "通用物料", "productFamily": 0, "salesAndOperations": 0, "productBarCodes": [ { "id": 1680013386205102085, "barCode": "\"123\"" } ], "productAssistUnitExchanges": [ { "id": 1680014013270327299, "productId": 1679287639833313282, "unitExchangeType": 0, "assistUnitCount": 1, "assistUnit": 1476511169941864456, "mainUnitCount": 23 } ], "productAttachments": [ { "productId": 15309767***********, "folder": "https://apcom-file-pub-npro.obs.cn-north-4.myhuaweicloud.com/iuap-apcom-file-public/iuap-apcom-file/0000L7PIX0Z2ARJ3MD0000/2024052219/78b79e63-39b2-4a7c-a15c-0a25ec2cd109.jpg", "fileName": "鲜花10.jpg" } ], "productAlbums": [ { "productId": 15309767***********, "folder": "https://apcom-file-pub-npro.obs.cn-north-4.myhuaweicloud.com/iuap-apcom-file-public/iuap-apcom-file/0000L7PIX0Z2ARJ3MD0000/2024052219/dbb52e2b-8afb-48fb-8b8e-ef062a536b27.jpeg", "imgName": "鲜花1.jpeg" } ], "imgBusinessId": "df6a101d-d11f-4971-95d4-1a9db4df9e1e", "videoBusinessId": "e78c2cd4-7685-65a5-c396-bc1354de7c25", "homepageBusinessId": "c16b0bf4-2941-43a4-b382-ab1167de5b17", "productTags": [ { "id": 1913058646531506185, "tagId": 1837964897149255683 } ], "optionalType": 0, "length": 10.5, "width": 10.5, "height": 10.5 } ] }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2268509272355110921</id>
<apiId>1847241897789620228</apiId>
<content>{"code":"999","message":"服务端逻辑异常"}</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2268509272355110920</id>
<apiId>1847241897789620228</apiId>
<content>{ "code": "200", "message": "操作成功", "data": [ { "productCharacterDef": {}, "productPropCharacterDefine": {}, "detail": { "id": 1530976723880902700, "productId": 15309767***********, "productExtendCharacterDef": {}, "productApplyRangeId": 1530976723880902700, "purchaseUnit": ****************, "purchaseUnitCode": "KGM", "purchaseUnitName": "千克", "purchasePriceUnit": ****************, "purchasePriceUnitCode": "KGM", "purchasePriceUnitName": "千克", "stockUnit": ****************, "stockUnitCode": "KGM", "stockUnitName": "千克", "batchUnit": ****************, "batchUnitCode": "KGM", "batchUnitName": "千克", "onlineUnit": ****************, "onlineUnitCode": "KGM", "onlineUnitName": "千克", "offlineUnit": ****************, "offlineUnitCode": "KGM", "offlineUnitName": "千克", "requireUnit": ****************, "requireUnitCode": "KGM", "requireUnitName": "千克", "batchPriceUnit": ****************, "batchPriceUnitCode": "KGM", "batchPriceUnitName": "千克", "inspectionUnit": ****************, "inspectionUnitCode": "KGM", "inspectionUnitName": "千克", "batchPrice": 0, "markPrice": 100, "lowestMarkPrice": 0, "salePrice": 100, "marketPrice": 100, "primeCosts": 100, "settleAccountsRate": 10, "displayPrice": true, "batchManage": true, "expiryDateManage": false, "serialNoManage": false, "safetyStock": 100, "highestStock": 100, "lowestStock": 100, "ropStock": 100, "canSale": true, "minOrderQuantity": 100, "deliveryDays": 1, "enableCyclePurchase": false, "enableDeposit": false, "depositDealPayType": 0, "enableModifyDeposit": false, "depositPayType": 0, "metaDescription": { "simplifiedName": "搜索简介" }, "baseSaleCount": 100, "enableContractManagement": false, "allowStorePurchase": true, "priceChangeAllowed": false, "saleInOfflineStore": true, "offlineStoreOrder": true, "offlineStoreReturn": true, "weighingOrNot": false, "process": false, "material": false, "retailPriceDimension": 1, "deliverQuantityChange": 1, "noTaxCostPrice": 100, "checkByBatch": false, "accountingByItem": false, "storeOffAndOffState": false, "orderLoadAndUnloadStatus": false, "mallUpCount": 0, "mallDownCount": 1, "orderUpCount": 0, "orderDownCount": 1, "tenant": ****************, "saleChannel": "1,2", "barCode": "条形码", "stopStatus": false, "checkFree": 0, "canOrder": false, "onlyOrder": false, "orderAdvanceTime": 0, "valueManageType": 0, "costValuation": 0, "checkByCost": false, "materialCost": false, "planDefaultAttribute": 1, "planMethod": 0, "keySubPart": false, "supplyDemandPolicy": 0, "fixedLeadTime": 10, "supplyType": 0, "produceDepartment": "****************", "produceDepartmentCode": "0002", "produceDepartmentName": "销售部门", "manufacturePlanner": "*****************", "manufacturePlannerCode": "A000001", "manufacturePlannerName": "甘甘", "engineeringDrawingNo": "123", "planProduceLimit": 1, "utility": false, "weigh": false, "productVendor": ****************, "productVendorCode": "**********", "productVendorName": "供应商测试", "productBuyer": "*****************", "productBuyerCode": "A000001", "productBuyerName": "甘甘", "maxPrimeCosts": 100, "requestOrderLimit": 100, "enableSubscribe": false, "recommend": false, "erpOuterCode": "商家商品外部编码", "saleStyle": "1", "shortName": "物料简称", "mnemonicCode": "助记码", "receiptName": { "simplifiedName": "开票名称" }, "incomeTaxRates": "2538775356445954", "outputTaxRate": "2538775356445954", "produceUnit": ****************, "produceUnitCode": "KGM", "produceUnitName": "千克", "warehouseManager": "*****************", "warehouseManagerCode": "A000001", "warehouseManagerName": "甘甘", "deliveryWarehouse": 1519760519882866700, "deliveryWarehouseCode": "000133", "deliveryWarehouseName": "rewrw", "returnWarehouse": 1519760519882866700, "returnWarehouseCode": "000133", "returnWarehouseName": "rewrw", "inStoreExcessLimit": 10, "outStoreExcessLimit": 10, "storageLossRate": 10, "allowNegativeInventory": true, "scanCountUnit": 0, "exemption": false, "warehousingByResult": false, "salesReturnsExemption": false, "returnsWarehousingByResult": false, "periodicalInspection": false, "displayName": {}, "titleMemo": {}, "barcodeManage": true, "receiptWarehouse": 1519760519882866700, "receiptWarehouseCode": "000133", "receiptWarehouseName": "rewrw", "BOMType": 0, "batchRule": 0, "fixedQuantity": 100, "prepareFeed": false, "specialMaterials": false, "virtualPart": false, "demandConsolidation": 0, "demandConsolidationType": 0, "demandConsolidationUnit": 0, "demandConsolidationNumber": 1, "demandConsolidationDateType": 0, "reservation": false, "lossType": 0, "ECNControl": false, "ytenantId": "0000KWJZLURO8TQU4Y0000", "inspectionType": 0, "logisticsRelated": false, "weighingMode": 0, "reviewGrossWeight": false, "specialCarTransport": false, "orgId": "2538984869630976", "businessAttribute": "1,7", "businessAttributePurchase": 1, "businessAttributeSale": 1, "businessAttributeSelfCreate": 0, "businessAttributeOutSourcing": 0, "testRule": 1, "enableStockPeriodRecheck": 0, "enableStockExpireCheck": 0, "enableSparePartsManagement": 0, "fullSetInspection": 0, "directProduction": 0, "costItems": "1978437901431078916", "costItemsCode": "ZDH_FYXM", "costItemsName": "自动化_费用项目", "manageByInventory": 0, "checkByProject": 0, "checkBySalesOrders": 0, "checkByClient": 0, "checkByOutsourcing": 0, "atpInspection": 0, "doublePick": 10 }, "productOrges": [ { "orgId": "2538984869630976", "orgType": 1 } ], "realProductAttribute": 1, "code": "code1", "name": { "simplifiedName": "simplifiedName1" }, "model": {}, "keywords": {}, "productClass": 2541495026652160, "productClassCode": "000002", "productClassName": "黄金", "manageClass": 2541494503052544, "manageClassCode": "000002", "manageClassName": "黄金", "purchaseClass": 1546426425542180900, "purchaseClassCode": "PTO001", "purchaseClassName": "PTO商品", "productTemplate": 2541493655655424, "brand": 2570445127045635, "brandCode": "品牌2", "brandName": "品牌2", "placeOfOrigin": "产地", "manufacturer": "生产厂商", "productLine": 2570288159150336, "shareDescription": {}, "unit": ****************, "unitCode": "KGM", "unitName": "千克", "taxClass": "1010105000000000000", "orgId": "2538984869630976", "defaultSKUId": 1530976723880902700, "id": 15309767***********, "tenant": ****************, "erpCode": "外部编码", "deleted": false, "createTime": "2022-08-25 20:05:48", "createDate": "2022-08-25 00:00:00", "creator": "YonSuite默认用户", "realProductAttributeType": 1, "weight": 1, "weightUnit": ****************, "weightUnitCode": "KGM", "weightUnitName": "千克", "volume": 2, "volumeUnit": 2571526900585472, "unitUseType": 2, "enableAssistUnit": false, "creatorId": 2538773096593408, "registrationManager": false, "authorizationManager": false, "planClass": 2570383093256707, "planClassCode": "wxt0009", "planClassName": "计划分类9", "ytenantId": "0000KWJZLURO8TQU4Y0000", "transType": "2538775354987782", "transTypeCode": "SYCSR002", "transTypeName": "通用物料", "productFamily": 0, "salesAndOperations": 0, "productBarCodes": [ { "id": 1680013386205102085, "barCode": "\"123\"" } ], "productAssistUnitExchanges": [ { "id": 1680014013270327299, "productId": 1679287639833313282, "unitExchangeType": 0, "assistUnitCount": 1, "assistUnit": 1476511169941864456, "mainUnitCount": 23 } ], "productAttachments": [ { "productId": 15309767***********, "folder": "https://apcom-file-pub-npro.obs.cn-north-4.myhuaweicloud.com/iuap-apcom-file-public/iuap-apcom-file/0000L7PIX0Z2ARJ3MD0000/2024052219/78b79e63-39b2-4a7c-a15c-0a25ec2cd109.jpg", "fileName": "鲜花10.jpg" } ], "productAlbums": [ { "productId": 15309767***********, "folder": "https://apcom-file-pub-npro.obs.cn-north-4.myhuaweicloud.com/iuap-apcom-file-public/iuap-apcom-file/0000L7PIX0Z2ARJ3MD0000/2024052219/dbb52e2b-8afb-48fb-8b8e-ef062a536b27.jpeg", "imgName": "鲜花1.jpeg" } ], "imgBusinessId": "df6a101d-d11f-4971-95d4-1a9db4df9e1e", "videoBusinessId": "e78c2cd4-7685-65a5-c396-bc1354de7c25", "homepageBusinessId": "c16b0bf4-2941-43a4-b382-ab1167de5b17", "productTags": [ { "id": 1913058646531506185, "tagId": 1837964897149255683 } ], "optionalType": 0, "length": 10.5, "width": 10.5, "height": 10.5 } ] }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2268509272355110921</id>
<apiId>1847241897789620228</apiId>
<content>{"code":"999","message":"服务端逻辑异常"}</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>2268509272355110920</id>
<apiId>1847241897789620228</apiId>
<content>{ "code": "200", "message": "操作成功", "data": [ { "productCharacterDef": {}, "productPropCharacterDefine": {}, "detail": { "id": 1530976723880902700, "productId": 15309767***********, "productExtendCharacterDef": {}, "productApplyRangeId": 1530976723880902700, "purchaseUnit": ****************, "purchaseUnitCode": "KGM", "purchaseUnitName": "千克", "purchasePriceUnit": ****************, "purchasePriceUnitCode": "KGM", "purchasePriceUnitName": "千克", "stockUnit": ****************, "stockUnitCode": "KGM", "stockUnitName": "千克", "batchUnit": ****************, "batchUnitCode": "KGM", "batchUnitName": "千克", "onlineUnit": ****************, "onlineUnitCode": "KGM", "onlineUnitName": "千克", "offlineUnit": ****************, "offlineUnitCode": "KGM", "offlineUnitName": "千克", "requireUnit": ****************, "requireUnitCode": "KGM", "requireUnitName": "千克", "batchPriceUnit": ****************, "batchPriceUnitCode": "KGM", "batchPriceUnitName": "千克", "inspectionUnit": ****************, "inspectionUnitCode": "KGM", "inspectionUnitName": "千克", "batchPrice": 0, "markPrice": 100, "lowestMarkPrice": 0, "salePrice": 100, "marketPrice": 100, "primeCosts": 100, "settleAccountsRate": 10, "displayPrice": true, "batchManage": true, "expiryDateManage": false, "serialNoManage": false, "safetyStock": 100, "highestStock": 100, "lowestStock": 100, "ropStock": 100, "canSale": true, "minOrderQuantity": 100, "deliveryDays": 1, "enableCyclePurchase": false, "enableDeposit": false, "depositDealPayType": 0, "enableModifyDeposit": false, "depositPayType": 0, "metaDescription": { "simplifiedName": "搜索简介" }, "baseSaleCount": 100, "enableContractManagement": false, "allowStorePurchase": true, "priceChangeAllowed": false, "saleInOfflineStore": true, "offlineStoreOrder": true, "offlineStoreReturn": true, "weighingOrNot": false, "process": false, "material": false, "retailPriceDimension": 1, "deliverQuantityChange": 1, "noTaxCostPrice": 100, "checkByBatch": false, "accountingByItem": false, "storeOffAndOffState": false, "orderLoadAndUnloadStatus": false, "mallUpCount": 0, "mallDownCount": 1, "orderUpCount": 0, "orderDownCount": 1, "tenant": ****************, "saleChannel": "1,2", "barCode": "条形码", "stopStatus": false, "checkFree": 0, "canOrder": false, "onlyOrder": false, "orderAdvanceTime": 0, "valueManageType": 0, "costValuation": 0, "checkByCost": false, "materialCost": false, "planDefaultAttribute": 1, "planMethod": 0, "keySubPart": false, "supplyDemandPolicy": 0, "fixedLeadTime": 10, "supplyType": 0, "produceDepartment": "****************", "produceDepartmentCode": "0002", "produceDepartmentName": "销售部门", "manufacturePlanner": "*****************", "manufacturePlannerCode": "A000001", "manufacturePlannerName": "甘甘", "engineeringDrawingNo": "123", "planProduceLimit": 1, "utility": false, "weigh": false, "productVendor": ****************, "productVendorCode": "**********", "productVendorName": "供应商测试", "productBuyer": "*****************", "productBuyerCode": "A000001", "productBuyerName": "甘甘", "maxPrimeCosts": 100, "requestOrderLimit": 100, "enableSubscribe": false, "recommend": false, "erpOuterCode": "商家商品外部编码", "saleStyle": "1", "shortName": "物料简称", "mnemonicCode": "助记码", "receiptName": { "simplifiedName": "开票名称" }, "incomeTaxRates": "2538775356445954", "outputTaxRate": "2538775356445954", "produceUnit": ****************, "produceUnitCode": "KGM", "produceUnitName": "千克", "warehouseManager": "*****************", "warehouseManagerCode": "A000001", "warehouseManagerName": "甘甘", "deliveryWarehouse": 1519760519882866700, "deliveryWarehouseCode": "000133", "deliveryWarehouseName": "rewrw", "returnWarehouse": 1519760519882866700, "returnWarehouseCode": "000133", "returnWarehouseName": "rewrw", "inStoreExcessLimit": 10, "outStoreExcessLimit": 10, "storageLossRate": 10, "allowNegativeInventory": true, "scanCountUnit": 0, "exemption": false, "warehousingByResult": false, "salesReturnsExemption": false, "returnsWarehousingByResult": false, "periodicalInspection": false, "displayName": {}, "titleMemo": {}, "barcodeManage": true, "receiptWarehouse": 1519760519882866700, "receiptWarehouseCode": "000133", "receiptWarehouseName": "rewrw", "BOMType": 0, "batchRule": 0, "fixedQuantity": 100, "prepareFeed": false, "specialMaterials": false, "virtualPart": false, "demandConsolidation": 0, "demandConsolidationType": 0, "demandConsolidationUnit": 0, "demandConsolidationNumber": 1, "demandConsolidationDateType": 0, "reservation": false, "lossType": 0, "ECNControl": false, "ytenantId": "0000KWJZLURO8TQU4Y0000", "inspectionType": 0, "logisticsRelated": false, "weighingMode": 0, "reviewGrossWeight": false, "specialCarTransport": false, "orgId": "2538984869630976", "businessAttribute": "1,7", "businessAttributePurchase": 1, "businessAttributeSale": 1, "businessAttributeSelfCreate": 0, "businessAttributeOutSourcing": 0, "testRule": 1, "enableStockPeriodRecheck": 0, "enableStockExpireCheck": 0, "enableSparePartsManagement": 0, "fullSetInspection": 0, "directProduction": 0, "costItems": "1978437901431078916", "costItemsCode": "ZDH_FYXM", "costItemsName": "自动化_费用项目", "manageByInventory": 0, "checkByProject": 0, "checkBySalesOrders": 0, "checkByClient": 0, "checkByOutsourcing": 0, "atpInspection": 0, "doublePick": 10 }, "productOrges": [ { "orgId": "2538984869630976", "orgType": 1 } ], "realProductAttribute": 1, "code": "code1", "name": { "simplifiedName": "simplifiedName1" }, "model": {}, "keywords": {}, "productClass": 2541495026652160, "productClassCode": "000002", "productClassName": "黄金", "manageClass": 2541494503052544, "manageClassCode": "000002", "manageClassName": "黄金", "purchaseClass": 1546426425542180900, "purchaseClassCode": "PTO001", "purchaseClassName": "PTO商品", "productTemplate": 2541493655655424, "brand": 2570445127045635, "brandCode": "品牌2", "brandName": "品牌2", "placeOfOrigin": "产地", "manufacturer": "生产厂商", "productLine": 2570288159150336, "shareDescription": {}, "unit": ****************, "unitCode": "KGM", "unitName": "千克", "taxClass": "1010105000000000000", "orgId": "2538984869630976", "defaultSKUId": 1530976723880902700, "id": 15309767***********, "tenant": ****************, "erpCode": "外部编码", "deleted": false, "createTime": "2022-08-25 20:05:48", "createDate": "2022-08-25 00:00:00", "creator": "YonSuite默认用户", "realProductAttributeType": 1, "weight": 1, "weightUnit": ****************, "weightUnitCode": "KGM", "weightUnitName": "千克", "volume": 2, "volumeUnit": 2571526900585472, "unitUseType": 2, "enableAssistUnit": false, "creatorId": 2538773096593408, "registrationManager": false, "authorizationManager": false, "planClass": 2570383093256707, "planClassCode": "wxt0009", "planClassName": "计划分类9", "ytenantId": "0000KWJZLURO8TQU4Y0000", "transType": "2538775354987782", "transTypeCode": "SYCSR002", "transTypeName": "通用物料", "productFamily": 0, "salesAndOperations": 0, "productBarCodes": [ { "id": 1680013386205102085, "barCode": "\"123\"" } ], "productAssistUnitExchanges": [ { "id": 1680014013270327299, "productId": 1679287639833313282, "unitExchangeType": 0, "assistUnitCount": 1, "assistUnit": 1476511169941864456, "mainUnitCount": 23 } ], "productAttachments": [ { "productId": 15309767***********, "folder": "https://apcom-file-pub-npro.obs.cn-north-4.myhuaweicloud.com/iuap-apcom-file-public/iuap-apcom-file/0000L7PIX0Z2ARJ3MD0000/2024052219/78b79e63-39b2-4a7c-a15c-0a25ec2cd109.jpg", "fileName": "鲜花10.jpg" } ], "productAlbums": [ { "productId": 15309767***********, "folder": "https://apcom-file-pub-npro.obs.cn-north-4.myhuaweicloud.com/iuap-apcom-file-public/iuap-apcom-file/0000L7PIX0Z2ARJ3MD0000/2024052219/dbb52e2b-8afb-48fb-8b8e-ef062a536b27.jpeg", "imgName": "鲜花1.jpeg" } ], "imgBusinessId": "df6a101d-d11f-4971-95d4-1a9db4df9e1e", "videoBusinessId": "e78c2cd4-7685-65a5-c396-bc1354de7c25", "homepageBusinessId": "c16b0bf4-2941-43a4-b382-ab1167de5b17", "productTags": [ { "id": 1913058646531506185, "tagId": 1837964897149255683 } ], "optionalType": 0, "length": 10.5, "width": 10.5, "height": 10.5 } ] }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>2268509272355110921</id>
<apiId>1847241897789620228</apiId>
<content>{"code":"999","message":"服务端逻辑异常"}</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS>
<errorCodeDTOS>
<id>2268509272355110922</id>
<apiId>1847241897789620228</apiId>
<errorCode>999</errorCode>
<errorMessage>服务端逻辑异常</errorMessage>
<errorType>API</errorType>
<errorcodeDesc>检查入参是否填写正确，参数值是否真实存在，仍提示该信息请联系开发人员。</errorcodeDesc>
<gmtCreate>2024-07-10 11:01:12.000</gmtCreate>
<gmtUpdate>2024-07-10 11:01:12.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<defErrorId/>
<ytenantId>0</ytenantId>
<displayCodeId/>
</errorCodeDTOS>
</errorCodeDTOS>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>友户通token认证业务扩展插件</name>
<configurable>false</configurable>
<description>YonsuitBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>1847241897789620228</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin>
<id>w181ed01-1e9b-4350-b994-71a66f017555</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code>resultParse</code>
<name>返回参数转换插件</name>
<configurable>false</configurable>
<description>解决返回值中带！的，转换为json</description>
<pluginType>resultParse</pluginType>
<pluginTypeName>返回值解析插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.result.ResultMapTransferParsePlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>true</visible>
<gmtCreate>2020-07-29 00:00:00</gmtCreate>
<gmtUpdate/>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>1847241897789620228</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</resultParsePlugin>
<mapReturnPluginConfig/>
<billNo>pc_product</billNo>
<domain>productcenter</domain>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser>56839516-fd08-4835-9cf3-711a236b0001</createUser>
<createUserName/>
<approvalStatus>1</approvalStatus>
<publishTime>2025-07-17 18:34:11</publishTime>
<pathJoin>true</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout/>
<customUrl>product/batchdetailnew</customUrl>
<fixedUrl>/yonbip/digitalModel/</fixedUrl>
<apiCode>1847241897789620228</apiCode>
<tokenCheckType>0</tokenCheckType>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL/>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>aed157b6-2987-4310-9fba-993d068de0ce</updateUserId>
<updateUserName>18310975128</updateUserName>
<paramIsForce>true</paramIsForce>
<userIDPassthrough>false</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.iuap-apdoc-material</microServiceCode>
<applicationCode>iuap-apdoc-material</applicationCode>
<privacyCategory>1</privacyCategory>
<privacyLevel>1</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType>0</serviceType>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType/>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>true</arrayParam>
<fileSize/>
<cc>true</cc>
<paramTransferMode>2</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId/>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>1</paramExtRequest>
<paramExtResponse>1</paramExtResponse>
<paramExtInExtendKey>1</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene/>
<apiType/>
<paramMark/>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>false</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>false</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>1</chargeStatus>
<beforeSpeed>150</beforeSpeed>
<afterSpeed>300</afterSpeed>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath/>
<pubHistory>
<pubHistory>
<id>2315403563550375939</id>
<apiId>1847241897789620228</apiId>
<apiName>物料档案批量详情查询</apiName>
<applyReason/>
<publishUserName/>
<version>20250717183411</version>
<operationTime>2025-07-17</operationTime>
<gmtCreate/>
<gmtUpdate/>
<changes>
<changes>
<changePosition>baseInfo</changePosition>
<newList/>
<updateList>
<updateList>
<changeProperty>name</changeProperty>
<oldValue>特征物料档案批量详情查询</oldValue>
<newValue>物料档案批量详情查询</newValue>
</updateList>
</updateList>
<deleteList/>
</changes>
<changes>
<changePosition>paramReturnDTOS</changePosition>
<newList>
<newList>
<changeProperty>length</changeProperty>
<oldValue/>
<newValue>{"id":"2268509272355111500","name":"length","apiId":"1847241897789620228","parentId":"2268509272355110936","array":false,"paramDesc":"长","paramType":"number","path":"null_data.length","example":"10.5","ytenantId":"0"}</newValue>
</newList>
<newList>
<changeProperty>width</changeProperty>
<oldValue/>
<newValue>{"id":"2268509272355111502","name":"width","apiId":"1847241897789620228","parentId":"2268509272355110936","array":false,"paramDesc":"宽","paramType":"number","path":"null_data.width","example":"10.5","ytenantId":"0"}</newValue>
</newList>
<newList>
<changeProperty>height</changeProperty>
<oldValue/>
<newValue>{"id":"2268509272355111504","name":"height","apiId":"1847241897789620228","parentId":"2268509272355110936","array":false,"paramDesc":"高","paramType":"number","path":"null_data.height","example":"10.5","ytenantId":"0"}</newValue>
</newList>
</newList>
<updateList/>
<deleteList/>
</changes>
</changes>
</pubHistory>
</pubHistory>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode>productcenter.pc_product</domainAppCode>
<multiVersion>0</multiVersion>
<apiTag>3</apiTag>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>2108770660671029249</id>
<name>用友YonBIP</name>
<type>integrateSys</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>PFC</id>
<name>数字化建模</name>
<type>1</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>PF</id>
<name>数字化建模</name>
<type>2</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>GZTBDM</id>
<name>基础数据</name>
<type>3</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>productcenter.pc_product</id>
<name>物料档案</name>
<type>4</type>
<sort>0</sort>
<enable>0</enable>
<children/>
<parentId/>
<productId/>
<code>productcenter.pc_product</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>GZTBDM</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>PF</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>PFC</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>current_yonbip_default_sys</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<isOrigin>0</isOrigin>
<hasChildren>0</hasChildren>
<order>0</order>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>6f861278-c3cc-42d4-9ff3-191090b0a6cc</id>
<name>WL01</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>参考料号1</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:58:27</gmtCreate>
<gmtUpdate>2025-07-26 17:58:27</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>91997999-b696-43f3-99b9-8b4e4d9054ed</id>
<name>WL02</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>参考料号2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:58:27</gmtCreate>
<gmtUpdate>2025-07-26 17:58:27</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>7c720a55-b2b6-4063-b79d-247c8fa81b4a</id>
<name>WL03</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>U9料品编码</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:58:27</gmtCreate>
<gmtUpdate>2025-07-26 17:58:27</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>06e44362-1816-45ff-94c9-c4cf4a84d091</id>
<name>WL04</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>客户型号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:58:27</gmtCreate>
<gmtUpdate>2025-07-26 17:58:27</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>b6d95179-87c0-468f-848e-0221577ad134</id>
<name>WL05</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>销售产品名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:58:27</gmtCreate>
<gmtUpdate>2025-07-26 17:58:27</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>ca409e82-7106-4918-afd8-4fb1985df5f8</id>
<name>WL06</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>料品每箱台数</paramDesc>
<paramType>Decimal</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>number</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:58:27</gmtCreate>
<gmtUpdate>2025-07-26 17:58:27</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>24</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>ac513417-dead-4b9f-9823-4dd5301fefa7</id>
<name>WL07</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>目录编号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:58:27</gmtCreate>
<gmtUpdate>2025-07-26 17:58:27</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:58:27</gmtCreate>
<gmtUpdate>2025-07-26 17:58:27</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>bd1598d1-3c17-4fd5-8501-94453b7dc479</id>
<name>C_001</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>颜色</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName>bd.customerdoc_Color.Color</fullName>
<ytenantId/>
<paramOrder/>
<bizType>quote</bizType>
<baseType>false</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:58:36</gmtCreate>
<gmtUpdate>2025-07-26 17:58:36</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>true</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:58:36</gmtCreate>
<gmtUpdate>2025-07-26 17:58:36</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>ffeabc0e-ef62-464e-9a2f-df9b4972eb68</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:58:45</gmtCreate>
<gmtUpdate>2025-07-26 17:58:45</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>efac0f6f-1598-4bd5-ac88-91393c04fd56</id>
<name>WL08</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>销售员</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName>bd.staff.StaffNew</fullName>
<ytenantId/>
<paramOrder/>
<bizType>quote</bizType>
<baseType>false</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:59:03</gmtCreate>
<gmtUpdate>2025-07-26 17:59:03</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>true</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>24c52b42-6695-4414-ac8f-5e9bed4e5e2b</id>
<name>WL09</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>跟单员</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName>bd.staff.StaffNew</fullName>
<ytenantId/>
<paramOrder/>
<bizType>quote</bizType>
<baseType>false</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:59:03</gmtCreate>
<gmtUpdate>2025-07-26 17:59:03</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>true</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:59:03</gmtCreate>
<gmtUpdate>2025-07-26 17:59:03</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>ff859cd8-100f-4a65-a0d4-f211886b57e9</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:59:17</gmtCreate>
<gmtUpdate>2025-07-26 17:59:17</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>