import ast
from dataclasses import dataclass
from pathlib import Path

import structlog

"""
YS-API V3.0 代码质量检查工具
提供AST分析、类型注解检查、复杂度分析、文档覆盖率检查
"""


logger = structlog.get_logger()


@dataclass
class FunctionInfo:
    """函数信息"""

    name: str
    line_number: int
    parameters: List[str]
    return_type: Optional[str]
    has_type_annotations: bool
    has_docstring: bool
    complexity: int
    lines_of_code: int


@dataclass
class ClassInfo:
    """类信息"""

    name: str
    line_number: int
    methods: List[FunctionInfo]
    has_docstring: bool
    lines_of_code: int


@dataclass
class ModuleInfo:
    """模块信息"""

    name: str
    file_path: str
    functions: List[FunctionInfo]
    classes: List[ClassInfo]
    imports: List[str]
    has_docstring: bool
    lines_of_code: int


@dataclass
class QualityMetrics:
    """质量指标"""

    type_annotation_coverage: float
    docstring_coverage: float
    average_complexity: float
    max_complexity: int
    total_functions: int
    total_classes: int
    total_lines: int


class CodeQualityChecker:
    """代码质量检查器"""

    def __init___(self):
    """TODO: Add function description."""
    self.complexity_threshold = 10  # 复杂度阈值
    self.max_lines_per_function = 50  # 函数最大行数

    def analyze_file(self, file_path: str) -> ModuleInfo:
        """
        分析单个文件

        Args:
            file_path: 文件路径

        Returns:
            ModuleInfo: 模块信息
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            tree = ast.parse(content)
            analyzer = ModuleAnalyzer(file_path)
            analyzer.visit(tree)

            return analyzer.get_module_info()

        except Exception:
            logger.error("分析文件失败", file_path=file_path, error=str(e))
            return ModuleInfo(
                name=Path(file_path).stem,
                file_path=file_path,
                functions=[],
                classes=[],
                imports=[],
                has_docstring=False,
                lines_of_code=0,
            )

    def analyze_directory(
        self, directory_path: str, file_pattern: str = "*.py"
    ) -> Dict[str, ModuleInfo]:
        """
        分析目录中的所有Python文件

        Args:
            directory_path: 目录路径
            file_pattern: 文件模式

        Returns:
            Dict[str, ModuleInfo]: 模块信息字典
        """
        try:
            directory = Path(directory_path)
            modules = {}

            for file_path in directory.rglob(file_pattern):
                if file_path.is_file():
                    module_info = self.analyze_file(str(file_path))
                    modules[module_info.name] = module_info

            return modules

        except Exception:
            logger.error("分析目录失败", directory_path=directory_path, error=str(e))
            return {}

    def calculate_quality_metrics(
        self, modules: Dict[str, ModuleInfo]
    ) -> QualityMetrics:
        """
        计算质量指标

        Args:
            modules: 模块信息字典

        Returns:
            QualityMetrics: 质量指标
        """
        try:
            total_functions = 0
            total_classes = 0
            total_lines = 0
            functions_with_types = 0
            functions_with_docs = 0
            total_complexity = 0
            max_complexity = 0

            for module_info in modules.values():
                total_lines += module_info.lines_of_code

                # 统计函数
                for func in module_info.functions:
                    total_functions += 1
                    if func.has_type_annotations:
                        functions_with_types += 1
                    if func.has_docstring:
                        functions_with_docs += 1
                    total_complexity += func.complexity
                    max_complexity = max(max_complexity, func.complexity)

                # 统计类
                for cls in module_info.classes:
                    total_classes += 1
                    if cls.has_docstring:
                        functions_with_docs += 1

                    for method in cls.methods:
                        total_functions += 1
                        if method.has_type_annotations:
                            functions_with_types += 1
                        if method.has_docstring:
                            functions_with_docs += 1
                        total_complexity += method.complexity
                        max_complexity = max(max_complexity, method.complexity)

            # 计算覆盖率
            type_annotation_coverage = (
                (functions_with_types / total_functions * 100)
                if total_functions > 0
                else 0
            )
            docstring_coverage = (
                (functions_with_docs / total_functions * 100)
                if total_functions > 0
                else 0
            )
            average_complexity = (
                total_complexity /
                total_functions if total_functions > 0 else 0)

            return QualityMetrics(
                type_annotation_coverage=type_annotation_coverage,
                docstring_coverage=docstring_coverage,
                average_complexity=average_complexity,
                max_complexity=max_complexity,
                total_functions=total_functions,
                total_classes=total_classes,
                total_lines=total_lines,
            )

        except Exception:
            logger.error("计算质量指标失败", error=str(e))
            return QualityMetrics(0, 0, 0, 0, 0, 0, 0)

    def generate_quality_report(
            self, modules: Dict[str, ModuleInfo]) -> Dict[str, Any]:
        """
        生成质量报告

        Args:
            modules: 模块信息字典

        Returns:
            Dict[str, Any]: 质量报告
        """
        try:
            metrics = self.calculate_quality_metrics(modules)

            # 识别问题
            issues = self._identify_issues(modules)

            # 生成改进建议
            suggestions = self._generate_suggestions(metrics, issues)

            return {
                'summary': {
                    'total_modules': len(modules),
                    'total_functions': metrics.total_functions,
                    'total_classes': metrics.total_classes,
                    'total_lines': metrics.total_lines,
                },
                'metrics': {
                    'type_annotation_coverage': f"{metrics.type_annotation_coverage:.1f}%",
                    'docstring_coverage': f"{metrics.docstring_coverage:.1f}%",
                    'average_complexity': f"{metrics.average_complexity:.1f}",
                    'max_complexity': metrics.max_complexity,
                },
                'issues': issues,
                'suggestions': suggestions,
                'modules': {
                    name: {
                        'functions': len(module.functions),
                        'classes': len(module.classes),
                        'lines': module.lines_of_code,
                        'has_docstring': module.has_docstring,
                    }
                    for name, module in modules.items()
                },
            }

        except Exception:
            logger.error("生成质量报告失败", error=str(e))
            return {}

    def _identify_issues(
            self, modules: Dict[str, ModuleInfo]) -> List[Dict[str, Any]]:
        """识别代码问题"""
        issues = []

        try:
            for module_name, module_info in modules.items():
                # 检查函数问题
                for func in module_info.functions:
                    if func.complexity > self.complexity_threshold:
                        issues.append(
                            {
                                'type': 'high_complexity',
                                'severity': 'warning',
                                'module': module_name,
                                'function': func.name,
                                'line': func.line_number,
                                'message': f"函数复杂度过高: {func.complexity} (阈值: {self.complexity_threshold})",
                            })

                    if func.lines_of_code > self.max_lines_per_function:
                        issues.append(
                            {
                                'type': 'long_function',
                                'severity': 'warning',
                                'module': module_name,
                                'function': func.name,
                                'line': func.line_number,
                                'message': f"函数过长: {func.lines_of_code} 行 (阈值: {self.max_lines_per_function})",
                            })

                    if not func.has_type_annotations:
                        issues.append(
                            {
                                'type': 'missing_type_annotations',
                                'severity': 'info',
                                'module': module_name,
                                'function': func.name,
                                'line': func.line_number,
                                'message': "缺少类型注解",
                            }
                        )

                    if not func.has_docstring:
                        issues.append(
                            {
                                'type': 'missing_docstring',
                                'severity': 'info',
                                'module': module_name,
                                'function': func.name,
                                'line': func.line_number,
                                'message': "缺少文档字符串",
                            }
                        )

                # 检查类问题
                for cls in module_info.classes:
                    if not cls.has_docstring:
                        issues.append(
                            {
                                'type': 'missing_docstring',
                                'severity': 'info',
                                'module': module_name,
                                'class': cls.name,
                                'line': cls.line_number,
                                'message': "类缺少文档字符串",
                            }
                        )

                    # 检查类方法
                    for method in cls.methods:
                        if method.complexity > self.complexity_threshold:
                            issues.append(
                                {
                                    'type': 'high_complexity',
                                    'severity': 'warning',
                                    'module': module_name,
                                    'class': cls.name,
                                    'method': method.name,
                                    'line': method.line_number,
                                    'message': f"方法复杂度过高: {method.complexity} (阈值: {self.complexity_threshold})",
                                })

            return issues

        except Exception:
            logger.error("识别代码问题失败", error=str(e))
            return []

    def _generate_suggestions(
        self, metrics: QualityMetrics, issues: List[Dict[str, Any]]
    ) -> List[str]:
        """生成改进建议"""
        suggestions = []

        try:
            # 基于指标的建议
            if metrics.type_annotation_coverage < 80:
                suggestions.append(
                    f"类型注解覆盖率较低 ({metrics.type_annotation_coverage:.1f}%)，建议为函数参数和返回值添加类型注解"
                )

            if metrics.docstring_coverage < 70:
                suggestions.append(
                    f"文档字符串覆盖率较低 ({metrics.docstring_coverage:.1f}%)，建议为函数和类添加文档字符串"
                )

            if metrics.average_complexity > 5:
                suggestions.append(
                    f"平均复杂度较高 ({metrics.average_complexity:.1f})，建议简化复杂函数"
                )

            if metrics.max_complexity > 15:
                suggestions.append(
                    f"存在高复杂度函数 (最大复杂度: {metrics.max_complexity})，建议重构这些函数"
                )

            # 基于问题的建议
            high_complexity_count = len(
                [i for i in issues if i['type'] == 'high_complexity']
            )
            if high_complexity_count > 0:
                suggestions.append(
                    f"发现 {high_complexity_count} 个高复杂度函数，建议拆分或简化"
                )

            long_function_count = len(
                [i for i in issues if i['type'] == 'long_function']
            )
            if long_function_count > 0:
                suggestions.append(
                    f"发现 {long_function_count} 个过长函数，建议拆分或提取方法"
                )

            missing_types_count = len(
                [i for i in issues if i['type'] == 'missing_type_annotations']
            )
            if missing_types_count > 0:
                suggestions.append(
                    f"发现 {missing_types_count} 个函数缺少类型注解，建议添加类型注解"
                )

            missing_docs_count = len(
                [i for i in issues if i['type'] == 'missing_docstring']
            )
            if missing_docs_count > 0:
                suggestions.append(
                    f"发现 {missing_docs_count} 个函数/类缺少文档字符串，建议添加文档"
                )

            return suggestions

        except Exception:
            logger.error("生成改进建议失败", error=str(e))
            return []


class ModuleAnalyzer(ast.NodeVisitor):
    """模块分析器"""

    def __init___(self, file_path: str):
    """TODO: Add function description."""
    self.file_path = file_path
    self.module_name = Path(file_path).stem
    self.functions = []
    self.classes = []
    self.imports = []
     self.has_docstring = False
      self.lines_of_code = 0

       # 计算行数
       try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.lines_of_code = len(f.readlines())
        except Exception:
            self.lines_of_code = 0

    def visit_Module(self, node):
        """访问模块节点"""
        # 检查模块文档字符串
        if (
            node.body
            and isinstance(node.body[0], ast.Expr)
            and isinstance(node.body[0].value, ast.Str)
        ):
            self.has_docstring = True

        self.generic_visit(node)

    def visit_Import(self, node):
        """访问导入节点"""
        for alias in node.names:
            self.imports.append(alias.name)
        self.generic_visit(node)

    def visit_ImportFrom(self, node):
        """访问from导入节点"""
        module = node.module or ""
        for alias in node.names:
            self.imports.append(f"{module}.{alias.name}")
        self.generic_visit(node)

    def visit_FunctionDef(self, node):
        """访问函数定义节点"""
        function_info = self._analyze_function(node)
        self.functions.append(function_info)
        self.generic_visit(node)

    def visit_AsyncFunctionDef(self, node):
        """访问异步函数定义节点"""
        function_info = self._analyze_function(node)
        self.functions.append(function_info)
        self.generic_visit(node)

    def visit_ClassDef(self, node):
        """访问类定义节点"""
        class_info = self._analyze_class(node)
        self.classes.append(class_info)
        self.generic_visit(node)

    def _analyze_function(self, node) -> FunctionInfo:
        """分析函数"""
        # 获取参数
        parameters = []
        for arg in node.args.args:
            parameters.append(arg.arg)

        # 检查类型注解
        has_type_annotations = False
        return_type = None

        # 检查参数类型注解
        for arg in node.args.args:
            if arg.annotation:
                has_type_annotations = True
                break

        # 检查返回值类型注解
        if node.returns:
            has_type_annotations = True
            return_type = (
                ast.unparse(node.returns)
                if hasattr(ast, 'unparse')
                else str(node.returns)
            )

        # 检查文档字符串
        has_docstring = False
        if (
            node.body
            and isinstance(node.body[0], ast.Expr)
            and isinstance(node.body[0].value, ast.Str)
        ):
            has_docstring = True

        # 计算复杂度
        complexity = self._calculate_complexity(node)

        # 计算行数
        lines_of_code = self._calculate_lines_of_code(node)

        return FunctionInfo(
            name=node.name,
            line_number=node.lineno,
            parameters=parameters,
            return_type=return_type,
            has_type_annotations=has_type_annotations,
            has_docstring=has_docstring,
            complexity=complexity,
            lines_of_code=lines_of_code,
        )

    def _analyze_class(self, node) -> ClassInfo:
        """分析类"""
        methods = []

        # 分析类方法
        for item in node.body:
            if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                method_info = self._analyze_function(item)
                methods.append(method_info)

        # 检查类文档字符串
        has_docstring = False
        if (
            node.body
            and isinstance(node.body[0], ast.Expr)
            and isinstance(node.body[0].value, ast.Str)
        ):
            has_docstring = True

        # 计算类行数
        lines_of_code = self._calculate_lines_of_code(node)

        return ClassInfo(
            name=node.name,
            line_number=node.lineno,
            methods=methods,
            has_docstring=has_docstring,
            lines_of_code=lines_of_code,
        )

    def _calculate_complexity(self, node) -> int:
        """计算复杂度"""
        complexity = 1  # 基础复杂度

        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, ast.With):
                complexity += 1
            elif isinstance(child, ast.Try):
                complexity += 1

        return complexity

    def _calculate_lines_of_code(self, node) -> int:
        """计算代码行数"""
        try:
            return node.end_lineno - node.lineno + 1
        except Exception:
            return 0

    def get_module_info(self) -> ModuleInfo:
        """获取模块信息"""
        return ModuleInfo(
            name=self.module_name,
            file_path=self.file_path,
            functions=self.functions,
            classes=self.classes,
            imports=self.imports,
            has_docstring=self.has_docstring,
            lines_of_code=self.lines_of_code,
        )


class DocumentationGenerator:
    """文档生成器"""

    def __init___(self):
    """TODO: Add function description."""
    pass

    def generate_function_doc(self, function_info: FunctionInfo) -> str:
        """生成函数文档"""
        try:
            doc_lines = []
            doc_lines.append(f"def {function_info.name}(")

            # 参数文档
            for param in function_info.parameters:
                doc_lines.append(f"    {param}: 参数描述")

            # 返回值文档
            if function_info.return_type:
                doc_lines.append(f") -> {function_info.return_type}:")
            else:
                doc_lines.append("):")

            doc_lines.append('    """')
            doc_lines.append('    函数描述')
            doc_lines.append('    ')

            # 参数说明
            for param in function_info.parameters:
                doc_lines.append(f'    Args:')
                doc_lines.append(f'        {param}: 参数描述')
                doc_lines.append('    ')

            # 返回值说明
            if function_info.return_type:
                doc_lines.append('    Returns:')
                doc_lines.append('        返回值描述')
                doc_lines.append('    ')

            doc_lines.append('    """')
            doc_lines.append('    pass')

            return '\n'.join(doc_lines)

        except Exception:
            logger.error(
                "生成函数文档失败", function_name=function_info.name, error=str(e)
            )
            return ""

    def generate_class_doc(self, class_info: ClassInfo) -> str:
        """生成类文档"""
        try:
            doc_lines = []
            doc_lines.append(f"class {class_info.name}:")
            doc_lines.append('    """')
            doc_lines.append('    类描述')
            doc_lines.append('    ')
            doc_lines.append('    Attributes:')
            doc_lines.append('        attribute_name: 属性描述')
            doc_lines.append('    """')
            doc_lines.append('    ')

            # 生成方法文档
            for method in class_info.methods:
                method_doc = self.generate_function_doc(method)
                if method_doc:
                    doc_lines.append(method_doc)
                    doc_lines.append('    ')

            return '\n'.join(doc_lines)

        except Exception:
            logger.error("生成类文档失败", class_name=class_info.name, error=str(e))
            return ""

    def generate_module_doc(self, module_info: ModuleInfo) -> str:
        """生成模块文档"""
        try:
            doc_lines = []
            doc_lines.append(f'"""')
            doc_lines.append(f'{module_info.name} 模块')
            doc_lines.append('')
            doc_lines.append('模块描述')
            doc_lines.append('')
            doc_lines.append('Classes:')
            for cls in module_info.classes:
                doc_lines.append(f'    {cls.name}: 类描述')
            doc_lines.append('')
            doc_lines.append('Functions:')
            for func in module_info.functions:
                doc_lines.append(f'    {func.name}: 函数描述')
            doc_lines.append('"""')

            return '\n'.join(doc_lines)

        except Exception:
            logger.error(
                "生成模块文档失败",
                module_name=module_info.name,
                error=str(e))
            return ""
