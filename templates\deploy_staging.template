#!/bin/bash
# YS-API V3.0 Staging Deployment Script

set -e

echo "🚀 Starting YS-API V3.0 Staging Deployment..."

# Configuration
STAGING_SERVER="staging.ysapi.com"
STAGING_USER="deploy"
APP_DIR="/opt/ysapi"
BACKUP_DIR="/opt/ysapi-backups"

# Create backup
echo "📦 Creating backup..."
ssh ${STAGING_USER}@${STAGING_SERVER} "
    sudo mkdir -p ${BACKUP_DIR}
    sudo cp -r ${APP_DIR} ${BACKUP_DIR}/ysapi-$(date +%Y%m%d-%H%M%S)
"

# Stop application
echo "⏹️ Stopping application..."
ssh ${STAGING_USER}@${STAGING_SERVER} "
    sudo systemctl stop ysapi
    sudo docker-compose -f ${APP_DIR}/docker-compose.yml down || true
"

# Deploy new version
echo "📋 Deploying new version..."
rsync -avz --exclude='.git' --exclude='logs/' . ${STAGING_USER}@${STAGING_SERVER}:${APP_DIR}/

# Update environment
ssh ${STAGING_USER}@${STAGING_SERVER} "
    cd ${APP_DIR}
    sudo docker-compose pull
    sudo docker-compose build
    sudo docker-compose up -d
    sudo systemctl start ysapi
"

# Health check
echo "🏥 Performing health check..."
sleep 30
if curl -f http://${STAGING_SERVER}:8000/health; then
    echo "✅ Staging deployment successful!"
else
    echo "❌ Staging deployment failed!"
    exit 1
fi

echo "🎉 Staging deployment completed!"
