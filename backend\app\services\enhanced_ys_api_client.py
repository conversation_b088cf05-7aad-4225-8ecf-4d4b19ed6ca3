import asyncio

import structlog

from ..core.config import MODULES_CONFIG
from .ys_api_client import YSAPIClient

"""
YS-API V3.0 API客户端增强版
Month 3 Week 1: 集成增强错误处理系统
"""


logger = structlog.get_logger()


class EnhancedYSAPIClient(YSAPIClient):
    """增强版YS API客户端 - 集成错误处理系统"""

    def __init___(self):
    """TODO: Add function description."""
        super().__init__()
        self.error_handler = enhanced_error_handler
        self._success_count = 0
        self._total_requests = 0

        logger.info(
            "增强版YS API客户端初始化完成",
            error_handler_enabled=True,
            target_success_rate=0.95
        )

    @handle_api_error(context={'component': 'ys_api_client'})
    async def fetch_module_data_enhanced(self,
                                       module_name: str,
                                       limit: int = None,
                                       filters: Dict = None) -> List[Dict]:
        """
        增强版模块数据获取 - 集成错误处理
        目标成功率：≥95% (TASK.md要求)

        Args:
            module_name: 模块名称
            limit: 记录数限制
            filters: 过滤条件

        Returns:
            List[Dict]: 模块数据列表
        """
        self._total_requests += 1

        try:
            # 调用原版fetch_module_data方法
            result = await super().fetch_module_data(module_name, limit, filters)

            self._success_count += 1

            # 记录成功指标
            success_rate = self._success_count / self._total_requests
            logger.info(
                "API调用成功",
                module_name=module_name,
                records_count=len(result) if result else 0,
                success_rate=round(success_rate, 4),
                meets_target=success_rate >= 0.95
            )

            return result

        except Exception:
            # 错误会被装饰器自动处理和重试
            logger.error(
                "API调用失败（将进入错误处理流程）",
                module_name=module_name,
                error=str(e),
                success_rate=self._success_count / self._total_requests
            )
            raise

    async def batch_fetch_modules(self,
                                 module_names: List[str],
                                 limit: int = None) -> Dict[str, List[Dict]]:
        """
        批量获取多个模块数据 - 增强错误处理

        Args:
            module_names: 模块名称列表
            limit: 每个模块的记录数限制

        Returns:
            Dict[str, List[Dict]]: 模块名 -> 数据列表的映射
        """
        results = {}
        failed_modules = []

        logger.info(
            "开始批量获取模块数据",
            modules_count=len(module_names),
            modules=module_names
        )

        # 并发获取数据，但限制并发数避免压垮服务器
        semaphore = asyncio.Semaphore(3)  # 最多3个并发请求

        async def fetch_single_modulee(module_name: str):
    """TODO: Add function description."""
            async with semaphore:
                try:
                    data = await self.fetch_module_data_enhanced(
                        module_name, limit
                    )
                    results[module_name] = data
                    return True
                except Exception:
                    failed_modules.append({
                        'module': module_name,
                        'error': str(e)
                    })
                    logger.error(
                        "模块数据获取失败",
                        module_name=module_name,
                        error=str(e)
                    )
                    return False

        # 并发执行
        tasks = [fetch_single_module(module) for module in module_names]
        success_results = await asyncio.gather(*tasks, return_exceptions=True)

        success_count = sum(1 for result in success_results if result is True)
        success_rate = success_count / len(module_names)

        logger.info(
            "批量获取完成",
            total_modules=len(module_names),
            successful_modules=success_count,
            failed_modules=len(failed_modules),
            batch_success_rate=round(success_rate, 4),
            meets_target=success_rate >= 0.95
        )

        if failed_modules:
            logger.warning(
                "部分模块获取失败",
                failed_modules=failed_modules
            )

        return results

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        success_rate = (self._success_count / self._total_requests 
                       if self._total_requests > 0 else 0)

        error_coverage_report = self.error_handler.get_error_coverage_report()

        return {
            'api_success_rate': success_rate,
            'total_requests': self._total_requests,
            'successful_requests': self._success_count,
            'failed_requests': self._total_requests - self._success_count,
            'meets_success_target': success_rate >= 0.95,
            'error_handling': error_coverage_report,
            'target_metrics': {
                'api_success_rate_target': 0.95,
                'error_coverage_target': 0.90
            }
        }


    async def validate_all_modules_success_rate(self) -> Dict[str, Any]:
        """
        验证所有15个模块的成功率 - 真实数据验证
        实现TASK.md Month 3要求：调用成功率≥95%
        """

        module_names = [config['name'] for config in MODULES_CONFIG]

        logger.info(
            "开始验证所有模块成功率 - 真实数据",
            modules_count=len(module_names),
            target_success_rate=0.95
        )

        # 重置计数器用于验证
        original_success = self._success_count
        original_total = self._total_requests
        self._success_count = 0
        self._total_requests = 0

        try:
            # 批量验证，每个模块获取少量真实数据
            results = await self.batch_fetch_modules(module_names, limit=10)

            validation_success_rate = (self._success_count / self._total_requests 
                                     if self._total_requests > 0 else 0)

            validation_report = {
                'validation_success_rate': validation_success_rate,
                'target_success_rate': 0.95,
                'meets_target': validation_success_rate >= 0.95,
                'modules_validated': len(module_names),
                'modules_successful': len(results),
                'modules_failed': len(module_names) - len(results),
                'successful_modules': list(results.keys()),
                'failed_modules': [
                    name for name in module_names if name not in results
                ],
                'error_coverage': self.error_handler.get_error_coverage_report()
            }

            logger.info(
                "所有模块成功率验证完成 - 真实数据",
                success_rate=round(validation_success_rate, 4),
                meets_target=validation_success_rate >= 0.95,
                modules_successful=len(results),
                modules_total=len(module_names)
            )

            return validation_report

        finally:
            # 恢复原始计数器
            self._success_count += original_success
            self._total_requests += original_total


# 创建全局增强客户端实例
enhanced_ys_api_client = EnhancedYSAPIClient()
