# YS-API V3.0 数据处理系统

> **🎯 核心目标**: 用友云ERP数据同步与字段配置管理系统

## 📖 ⭐ 必读文档 (避免开发混乱)

1. **[数据流程标准规范](./docs/数据流程标准规范.md)** - 五步数据流程，核心规范文档
2. **[开发快速参考手册](./docs/开发快速参考手册.md)** - 开发时对照查阅，防止流程错误
3. **[项目架构文档](./docs/项目架构文档.md)** - 完整项目架构和模块说明

## 🔄 核心数据流程 (五步法)

```
步骤1: API原始数据 → 基准文件 (不修改原始结构)
步骤2: 基准文件 + JSON字典 → 增强元数据 (获取中文名称)
步骤3: 智能字段分类 → 用户配置 (排除技术字段)
步骤4: 用户配置 → 数据库表 (只用is_required=true字段)
步骤5: API数据 + 配置 → ETL写入 (类型转换清洗)
```

## 🚀 快速启动

### 标准启动方式 ✅ 
**在项目根目录下执行以下命令** - 端口已标准化

启动步骤：
1. **启动后端**: `python backend/start_server_fixed.py` (端口8050)
2. **启动前端**: `python frontend/start_frontend_fixed.py` (端口8060)

### 服务地址 (已标准化)
- **后端服务**: http://localhost:8050 (固定，禁止改动)
- **前端界面**: http://localhost:8060 (固定，禁止改动)
- **API文档**: http://localhost:8050/docs

### 停止服务
- 在相应启动窗口中按 `Ctrl+C`
- 或关闭启动窗口

### 启动验证
- 运行 `python verify_startup.py` 验证启动配置
- 运行 `quick_verify.bat` 快速验证修复

### 故障排除 🔧
如果遇到启动错误：
1. **PortManager错误**：检查方法调用参数是否正确
2. **ErrorHandler错误**：确认前端JS文件路径正确
3. **端口占用**：使用 `python scripts/port_manager.py` 检查端口状态
4. **依赖缺失**：运行 `pip install -r backend/requirements.txt`

## � 关键目录

```
v3/
├── docs/                    # 📖 核心文档 (必读)
│   ├── 数据流程标准规范.md    # ⭐ 五步流程规范
│   └── 开发快速参考手册.md    # ⚡ 开发检查清单
├── config/
│   ├── baselines/           # 步骤1: API原始数据
│   └── user_configs/        # 步骤3: 用户字段配置
├── 模块字段/json/           # 步骤2: 中文名称字典
├── month2_database/         # 步骤4: 数据库表管理
└── backend/app/services/    # 步骤5: ETL处理服务
```

## 🎯 开发检查清单

开发前请确认：
- [ ] 已阅读[数据流程标准规范](./docs/数据流程标准规范.md)
- [ ] 已了解五步数据处理流程
- [ ] 已知晓[开发快速参考手册](./docs/开发快速参考手册.md)位置

开发中请对照：
- [ ] 基准文件保持API原始结构 (步骤1)
- [ ] JSON匹配获取中文名称 (步骤2)  
- [ ] 智能排除技术字段 (步骤3)
- [ ] 数据库表使用中文列名 (步骤4)
- [ ] ETL只处理is_required=true字段 (步骤5)

## 🔧 核心服务

| 服务 | 功能 | 位置 |
|------|------|------|
| FieldExtractor | 步骤1: 字段提取 | `services/field_extractor.py` |
| EnhancedJSONFieldMatcher | 步骤2: JSON匹配 | `services/enhanced_json_field_matcher.py` |
| IntelligentFieldMapper | 步骤3: 智能映射 | `services/intelligent_field_mapper.py` |
| TableCreationManager | 步骤4: 表创建 | `month2_database/table_creation/manager.py` |
| DataWriteManager | 步骤5: 数据写入 | `services/data_write_manager.py` |

## �📋 项目规则

### ⚠️ 重要规则
1. **遵循五步数据流程** - 任何数据处理必须按标准流程执行
2. **使用标准化端口** - 后端8050，前端8060，禁止修改
3. **不允许使用模拟数据** - 确保数据真实性
4. **不可以随意建立测试文件** - 保持项目整洁
5. **每次开始前阅读文档** - 确保了解最新状态
6. **开发时对照检查清单** - 防止流程错误

## 🏗️ 项目结构
- `backend/`: FastAPI后端应用 (端口8050)
- `frontend/`: 前端静态文件 (端口8060)
- `config/`: 配置文件目录
- `docs/`: 项目文档
- `logs/`: 日志文件
- `.env`: 环境配置文件 (PORT=8060)

## 🔧 功能特性
- **数据同步**: 14个业务模块的数据同步 (已移除库存管理、物料档案)
- **字段配置**: 智能字段映射和配置
- **数据库管理**: 自动表创建和数据管理
- **进度监控**: 实时同步进度和状态监控
- **错误处理**: 完善的错误处理和日志记录
- **端口标准化**: 固定端口配置，避免冲突

## 🖥️ 使用说明

### 第一次使用
1. **启动后端**: `python backend/start_server_fixed.py` (端口8050)
2. **启动前端**: `python frontend/start_frontend_fixed.py` (端口8060) 
3. 访问 http://localhost:8060 查看前端界面
4. 访问 http://localhost:8050/docs 查看API文档

### 日常使用
1. **后端服务**: `python backend/start_server_fixed.py`
2. **前端服务**: `python frontend/start_frontend_fixed.py`
3. 使用前端界面进行数据同步操作
4. **停止服务**: 在各自窗口按 `Ctrl+C`

## 📚 相关文档
- 详细功能说明请查看 `docs/` 目录
- 问题排查请查看相关问题解决文档
- 更新日志请查看各个功能完成报告

## 💡 注意事项
- 确保Python环境已正确安装
- 首次启动可能需要安装依赖包
- 使用标准化端口：后端8050，前端8060
- 服务运行时不要关闭启动窗口
- 环境配置已标准化，禁止修改端口配置

---
更新时间: 2025-08-06
版本: V3.0 (端口标准化版) 