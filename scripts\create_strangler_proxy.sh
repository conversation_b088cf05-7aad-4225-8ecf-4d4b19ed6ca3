#!/bin/bash
# 创建绞杀者代理层脚本

echo "🔄 开始创建绞杀者代理层..."

# 创建代理层目录
mkdir -p backend/app/strangler_proxy
mkdir -p backend/app/v2

# 创建绞杀者代理类
cat > backend/app/strangler_proxy/proxy.py << 'EOF'
"""
绞杀者模式代理层
逐步将旧系统流量切换到新系统
"""

from flask import request, jsonify, current_app
import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

class StranglerProxy:
    def __init__(self):
        self.migration_config = self.load_migration_config()
        self.logger = logging.getLogger(__name__)
        
    def load_migration_config(self) -> Dict[str, Any]:
        """加载模块迁移配置"""
        config_path = "config/migration_status.json"
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return self.create_default_config()
    
    def create_default_config(self) -> Dict[str, Any]:
        """创建默认迁移配置"""
        modules = [
            "材料出库单列表查询", "采购订单列表", "采购入库单列表",
            "产品入库单列表查询", "请购单列表查询", "生产订单列表查询",
            "委外订单列表", "委外入库列表查询", "委外申请列表查询", 
            "物料档案批量详情查询", "现存量报表查询", "销售出库列表查询",
            "销售订单", "需求计划", "业务日志"
        ]
        
        config = {
            "global_settings": {
                "enable_strangling": True,
                "default_traffic_split": 0,  # 默认0%流量到新系统
                "monitoring_enabled": True
            },
            "modules": {}
        }
        
        for module in modules:
            config["modules"][module] = {
                "migrated": False,
                "traffic_split": 0,  # 0-100，新系统流量百分比
                "last_updated": datetime.now().isoformat(),
                "migration_stage": "not_started"  # not_started, in_progress, completed
            }
        
        self.save_migration_config(config)
        return config
    
    def save_migration_config(self, config: Dict[str, Any]):
        """保存迁移配置"""
        os.makedirs("config", exist_ok=True)
        with open("config/migration_status.json", 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def route_request(self, module_name: str, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """根据迁移状态路由请求"""
        module_config = self.migration_config.get("modules", {}).get(module_name, {})
        
        # 检查是否启用绞杀模式
        if not self.migration_config.get("global_settings", {}).get("enable_strangling", False):
            return self.call_legacy_system(module_name, request_data)
        
        # 获取流量分配百分比
        traffic_split = module_config.get("traffic_split", 0)
        
        # 强制路由参数检查
        force_version = request_data.get("__force_version")
        if force_version == "new":
            return self.call_new_system(module_name, request_data)
        elif force_version == "old":
            return self.call_legacy_system(module_name, request_data)
        
        # 基于流量分配路由
        import random
        if random.randint(1, 100) <= traffic_split:
            try:
                return self.call_new_system(module_name, request_data)
            except Exception as e:
                self.logger.error(f"新系统调用失败，降级到旧系统: {e}")
                return self.call_legacy_system(module_name, request_data)
        else:
            return self.call_legacy_system(module_name, request_data)
    
    def call_new_system(self, module_name: str, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """调用新系统接口"""
        try:
            from app.v2.handlers import get_handler
            handler = get_handler(module_name)
            result = handler.process(request_data)
            
            # 记录调用日志
            self.log_request(module_name, "new", True, result)
            return result
            
        except ImportError:
            self.logger.warning(f"新系统处理器未找到: {module_name}")
            raise Exception(f"新系统处理器未实现: {module_name}")
        except Exception as e:
            self.log_request(module_name, "new", False, str(e))
            raise
    
    def call_legacy_system(self, module_name: str, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """调用旧系统接口"""
        try:
            from app.legacy.handlers import get_legacy_handler
            handler = get_legacy_handler(module_name)
            result = handler.process(request_data)
            
            # 记录调用日志
            self.log_request(module_name, "legacy", True, result)
            return result
            
        except ImportError:
            # 如果没有专门的legacy handlers，尝试调用原有逻辑
            from app.services import get_service
            service = get_service(module_name)
            result = service.process(request_data)
            
            self.log_request(module_name, "legacy", True, result)
            return result
        except Exception as e:
            self.log_request(module_name, "legacy", False, str(e))
            raise
    
    def log_request(self, module_name: str, system: str, success: bool, result: Any):
        """记录请求日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "module": module_name,
            "system": system,
            "success": success,
            "result_size": len(str(result)) if success else 0,
            "error": str(result) if not success else None
        }
        
        # 确保日志目录存在
        os.makedirs("logs/strangling", exist_ok=True)
        
        # 写入日志文件
        log_file = f"logs/strangling/{datetime.now().strftime('%Y-%m-%d')}.jsonl"
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
    
    def update_traffic_split(self, module_name: str, new_split: int):
        """更新模块流量分配"""
        if module_name in self.migration_config.get("modules", {}):
            self.migration_config["modules"][module_name]["traffic_split"] = new_split
            self.migration_config["modules"][module_name]["last_updated"] = datetime.now().isoformat()
            
            if new_split == 100:
                self.migration_config["modules"][module_name]["migration_stage"] = "completed"
            elif new_split > 0:
                self.migration_config["modules"][module_name]["migration_stage"] = "in_progress"
            
            self.save_migration_config(self.migration_config)
            self.logger.info(f"模块 {module_name} 流量分配更新为 {new_split}%")

# 全局代理实例
strangler_proxy = StranglerProxy()
EOF

# 创建Flask路由集成
cat > backend/app/strangler_proxy/routes.py << 'EOF'
"""
绞杀者代理路由
"""

from flask import Blueprint, request, jsonify
from .proxy import strangler_proxy

# 创建蓝图
strangler_bp = Blueprint('strangler', __name__, url_prefix='/api')

@strangler_bp.route('/<module_name>', methods=['POST'])
def proxy_route(module_name):
    """代理路由入口"""
    try:
        request_data = request.get_json() or {}
        
        # 添加请求元数据
        request_data.update({
            "__remote_addr": request.remote_addr,
            "__user_agent": request.headers.get('User-Agent'),
            "__timestamp": request_data.get("__timestamp") or time.time()
        })
        
        result = strangler_proxy.route_request(module_name, request_data)
        return jsonify({
            "success": True,
            "data": result,
            "metadata": {
                "module": module_name,
                "proxy_version": "1.0"
            }
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e),
            "metadata": {
                "module": module_name,
                "proxy_version": "1.0"
            }
        }), 500

@strangler_bp.route('/proxy/status', methods=['GET'])
def proxy_status():
    """获取代理状态"""
    config = strangler_proxy.migration_config
    
    stats = {
        "total_modules": len(config.get("modules", {})),
        "migrated_modules": len([m for m in config.get("modules", {}).values() if m.get("migrated", False)]),
        "in_progress_modules": len([m for m in config.get("modules", {}).values() if m.get("migration_stage") == "in_progress"]),
        "traffic_splits": {name: module.get("traffic_split", 0) for name, module in config.get("modules", {}).items()}
    }
    
    return jsonify({
        "success": True,
        "data": stats,
        "config": config
    })

@strangler_bp.route('/proxy/traffic/<module_name>', methods=['PUT'])
def update_traffic(module_name):
    """更新模块流量分配"""
    try:
        data = request.get_json()
        new_split = data.get("traffic_split")
        
        if new_split is None or not (0 <= new_split <= 100):
            return jsonify({
                "success": False,
                "error": "traffic_split must be between 0 and 100"
            }), 400
        
        strangler_proxy.update_traffic_split(module_name, new_split)
        
        return jsonify({
            "success": True,
            "message": f"流量分配已更新: {module_name} -> {new_split}%"
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
EOF

# 创建代理层初始化脚本
cat > backend/app/strangler_proxy/__init__.py << 'EOF'
"""
绞杀者代理层初始化
"""

from .proxy import strangler_proxy
from .routes import strangler_bp

__all__ = ['strangler_proxy', 'strangler_bp']
EOF

# 创建监控脚本
cat > scripts/monitor_strangling.py << 'EOF'
#!/usr/bin/env python3
"""
绞杀进度监控脚本
"""

import json
import os
from datetime import datetime, timedelta
import glob

def analyze_strangling_logs():
    """分析绞杀日志"""
    log_dir = "logs/strangling"
    if not os.path.exists(log_dir):
        print("⚠️ 绞杀日志目录不存在")
        return
    
    # 读取最近7天的日志
    stats = {
        "total_requests": 0,
        "new_system_requests": 0,
        "legacy_system_requests": 0,
        "success_rate": 0,
        "modules": {}
    }
    
    for log_file in glob.glob(f"{log_dir}/*.jsonl"):
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                try:
                    entry = json.loads(line.strip())
                    module = entry["module"]
                    system = entry["system"]
                    success = entry["success"]
                    
                    stats["total_requests"] += 1
                    
                    if system == "new":
                        stats["new_system_requests"] += 1
                    else:
                        stats["legacy_system_requests"] += 1
                    
                    if module not in stats["modules"]:
                        stats["modules"][module] = {
                            "total": 0,
                            "new_system": 0,
                            "legacy_system": 0,
                            "success": 0,
                            "errors": 0
                        }
                    
                    stats["modules"][module]["total"] += 1
                    if system == "new":
                        stats["modules"][module]["new_system"] += 1
                    else:
                        stats["modules"][module]["legacy_system"] += 1
                    
                    if success:
                        stats["modules"][module]["success"] += 1
                    else:
                        stats["modules"][module]["errors"] += 1
                        
                except json.JSONDecodeError:
                    continue
    
    # 计算成功率
    if stats["total_requests"] > 0:
        total_success = sum(module["success"] for module in stats["modules"].values())
        stats["success_rate"] = (total_success / stats["total_requests"]) * 100
    
    return stats

def generate_report():
    """生成绞杀进度报告"""
    stats = analyze_strangling_logs()
    
    # 读取迁移配置
    config_path = "config/migration_status.json"
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    else:
        config = {"modules": {}}
    
    # 生成报告
    report = f"""
# 绞杀者进度报告

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 总体统计

- 总请求数: {stats["total_requests"]}
- 新系统请求: {stats["new_system_requests"]} ({stats["new_system_requests"]/max(stats["total_requests"], 1)*100:.1f}%)
- 旧系统请求: {stats["legacy_system_requests"]} ({stats["legacy_system_requests"]/max(stats["total_requests"], 1)*100:.1f}%)
- 整体成功率: {stats["success_rate"]:.2f}%

## 模块迁移状态

| 模块名称 | 流量分配 | 迁移阶段 | 请求数 | 成功率 |
|---------|---------|----------|--------|--------|
"""
    
    for module_name, module_config in config.get("modules", {}).items():
        module_stats = stats["modules"].get(module_name, {})
        total_requests = module_stats.get("total", 0)
        success_requests = module_stats.get("success", 0)
        success_rate = (success_requests / max(total_requests, 1)) * 100
        
        traffic_split = module_config.get("traffic_split", 0)
        migration_stage = module_config.get("migration_stage", "not_started")
        
        report += f"| {module_name} | {traffic_split}% | {migration_stage} | {total_requests} | {success_rate:.1f}% |\n"
    
    # 保存报告
    os.makedirs("reports", exist_ok=True)
    report_path = f"reports/strangling_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"✅ 报告已生成: {report_path}")
    return report_path

if __name__ == "__main__":
    generate_report()
EOF

echo "✅ 绞杀者代理层创建完成！"
echo "📁 代理层文件:"
echo "  - backend/app/strangler_proxy/proxy.py"
echo "  - backend/app/strangler_proxy/routes.py"
echo "  - backend/app/strangler_proxy/__init__.py"
echo "📊 监控脚本: scripts/monitor_strangling.py"
