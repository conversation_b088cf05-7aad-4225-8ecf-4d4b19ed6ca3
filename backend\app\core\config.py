import configparser
from pathlib import Path
from typing import Optional

import structlog
from pydantic_settings import BaseSettings

"""
YS-API V3.0 系统配置
基于Pydantic的配置管理
"""


logger = structlog.get_logger()

"""
YS-API V3.0 应用配置模块
集成V2的config.ini配置，提供统一的配置管理
"""


class Settings(BaseSettings):
    """应用配置"""

    # 应用基础信息
    APP_NAME: str = "YS-API V3.0"
    APP_VERSION: str = "3.0.0"
    DEBUG: bool = False

    # 服务器配置
    HOST: str = "0.0.0.0"
    # 固定后端端口 8050，禁止改动
    PORT: int = 8050

    # 数据库配置 - 读取V2的config.ini格式
    DATABASE_SERVER: str = "localhost"
    DATABASE_NAME: str = "YSAPI"
    DATABASE_USER: Optional[str] = None
    DATABASE_PASSWORD: Optional[str] = None
    DATABASE_DRIVER: str = "ODBC Driver 17 for SQL Server"

    # 用友云API配置
    YS_API_BASE_URL: str = ""
    YS_API_TOKEN: str = ""
    YS_API_SECRET: str = ""
    YS_TENANT_ID: str = ""
    YS_ORG_ID: str = ""
    YS_API_TIMEOUT: int = 30

    # Redis配置（缓存）
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int = 0

    # 任务调度配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/1"

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"  # json | text

    # 同步配置
    SYNC_BATCH_SIZE: int = 1000
    SYNC_MAX_RETRIES: int = 3
    SYNC_RETRY_DELAY: int = 5

    # 字段配置
    FIELD_CONFIG_DIR: str = "config/field_configs"
    MD_DOCS_DIR: str = "md文档"

    @field_validator("DATABASE_SERVER", mode="before")
    @classmethod
    def validate_database_server(cls, v):
        """验证数据库服务器配置"""
        if not v:
            return "localhost"
        return v

    @property
    def database_url(self) -> str:
        """构建数据库连接字符串"""
        if self.DATABASE_USER and self.DATABASE_PASSWORD:
            # 用户名密码认证
            return (
                f"mssql+pyodbc://{self.DATABASE_USER}:{self.DATABASE_PASSWORD}"
                f"@{self.DATABASE_SERVER}/{self.DATABASE_NAME}"
                f"?driver={self.DATABASE_DRIVER.replace(' ', '+')}"
                "&trusted_connection=no"
            )
        else:
            # Windows集成认证
            return (
                f"mssql+pyodbc://{self.DATABASE_SERVER}/{self.DATABASE_NAME}"
                f"?driver={self.DATABASE_DRIVER.replace(' ', '+')}"
                "&trusted_connection=yes"
            )

    @property
    def redis_url(self) -> str:
        """构建Redis连接字符串"""
        if self.REDIS_PASSWORD:
            return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"

    model_config = ConfigDict(
        env_file=".env",
        case_sensitive=True,
        extra="allow",  # 允许额外字段以兼容config.ini
    )


def load_config_from_ini(ini_file_path: str = "config.ini") -> dict:
    """
    从V2的config.ini文件加载配置
    保持向后兼容性
    """

    config = {}

    try:
        # 智能查找config.ini文件路径
        possible_paths = [
            ini_file_path,  # 传入的路径
            "config.ini",  # 当前目录
            "../config.ini",  # 上级目录
            "../../config.ini",  # 上上级目录
            str(
                Path(__file__).parent.parent.parent.parent / "config.ini"
            ),  # 项目根目录
        ]

        config_found = False

        parser = configparser.ConfigParser()

        for path in possible_paths:
            try:
                if Path(path).exists():
                    parser.read(path, encoding="utf-8")
                    config_found = True
                    logger.info(f"✅ 成功加载配置文件: {path}")
                    break
            except Exception:
                continue

        if not config_found:
            logger.info(f"⚠️ 警告: 无法找到config.ini文件，尝试路径: {possible_paths}")
            logger.info("使用默认数据库配置...")
            return {
                "DATABASE_SERVER": "localhost",
                "DATABASE_NAME": "YSAPI",
                "DATABASE_USER": "sa",
                "DATABASE_PASSWORD": "123456",
                "DATABASE_DRIVER": "ODBC Driver 17 for SQL Server",
            }

        # 数据库配置
        if "database" in parser:
            db_config = parser["database"]
            config.update(
                {
                    "DATABASE_SERVER": db_config.get("server", "localhost"),
                    "DATABASE_NAME": db_config.get("database", "YSAPI"),
                    "DATABASE_USER": db_config.get("username"),
                    "DATABASE_PASSWORD": db_config.get("password"),
                    "DATABASE_DRIVER": db_config.get(
                        "driver", "ODBC Driver 17 for SQL Server"
                    ),
                }
            )

        # API配置
        if "api" in parser:
            api_config = parser["api"]
            config.update(
                {
                    "YS_API_BASE_URL": api_config.get("yonbipgateway", ""),
                    # 使用appkey作为token
                    "YS_API_TOKEN": api_config.get("appkey", ""),
                    "YS_API_TIMEOUT": int(api_config.get("timeout", 30)),
                    "YS_API_SECRET": api_config.get("appsecret", ""),
                    "YS_TENANT_ID": api_config.get("tenantid", ""),
                    "YS_ORG_ID": api_config.get("orgid", ""),
                }
            )

    except Exception:
        logger.info(f"Warning: 无法读取config.ini文件: {e}")
        logger.info("使用默认配置...")

    return config


# 加载配置
config_from_ini = load_config_from_ini()

# 创建设置实例
settings = Settings(**config_from_ini)


def get_settings() -> Settings:
    """获取设置实例"""
    return settings


# 模块列表配置 - 基于V2适配器文件中的真实API端点
MODULES_CONFIG = [
    {
        "name": "purchase_order",
        "display": "采购订单",
        "table_name": "purchase_order",
        "api_endpoint": "/yonbip/scm/purchaseorder/list",  # 待确认
        "md_file": "采购订单列表查询.md",
        "is_active": True,
    },
    {
        "name": "sales_order",
        "display": "销售订单",
        "table_name": "sales_order",
        "api_endpoint": "/yonbip/sd/voucherorder/list",  # 来自V2 sales_order.py
        "md_file": "销售订单列表查询.md",
        "is_active": True,
    },
    {
        "name": "production_order",
        "display": "生产订单",
        "table_name": "production_order",
        "api_endpoint": "/yonbip/mfg/productionorder/list",  # 来自V2 production_order.py
        "md_file": "生产订单列表查询.md",
        "is_active": True,
    },
    {
        "name": "subcontract_order",
        "display": "委外订单",
        "table_name": "subcontract_order",
        "api_endpoint": "/yonbip/mfg/subcontractorder/list",  # 来自V2 subcontract_order.py
        "md_file": "委外订单列表.md",
        "is_active": True,
    },
    {
        "name": "applyorder",
        "display": "请购单",
        "table_name": "applyorder",
        "api_endpoint": "/yonbip/scm/applyorder/list",  # 来自V2 applyorder.py
        "md_file": "请购单列表查询.md",
        "is_active": True,
    },
    {
        "name": "subcontract_requisition",
        "display": "委外请购",
        "table_name": "subcontract_requisition",
        # 来自V2 subcontract_requisition.py
        "api_endpoint": "/yonbip/mfg/subcontractrequisition/list",
        "md_file": "委外申请列表查询.md",
        "is_active": True,
    },
    {
        "name": "product_receipt",
        "display": "产品入库单",
        "table_name": "product_receipt",
        "api_endpoint": "/yonbip/scm/storeprorecord/list",  # 来自V2 product_receipt.py
        "md_file": "产品入库单列表.md",
        "is_active": True,
    },
    {
        "name": "purchase_receipt",
        "display": "采购入库",
        "table_name": "purchase_receipt",
        "api_endpoint": "/yonbip/scm/purinrecord/list",  # 来自V2 purchase_receipt.py
        "md_file": "采购入库列表查询.md",
        "is_active": True,
    },
    {
        "name": "subcontract_receipt",
        "display": "委外入库",
        "table_name": "subcontract_receipt",
        "api_endpoint": "/yonbip/scm/osminrecord/list",  # 来自V2 subcontract_receipt.py
        "md_file": "委外入库列表查询.md",
        "is_active": True,
    },
    {
        "name": "materialout",
        "display": "材料出库单",
        "table_name": "materialout",
        "api_endpoint": "/yonbip/scm/materialout/list",  # 保持原有，需要确认
        "md_file": "材料出库单列表查询.md",
        "is_active": True,
    },
    {
        "name": "sales_out",
        "display": "销售出库",
        "table_name": "sales_out",
        "api_endpoint": "/yonbip/scm/salesout/list",  # 保持原有，需要确认
        "md_file": "销售出库列表查询.md",
        "is_active": True,
    },
    {
        "name": "requirements_planning",
        "display": "需求计划",
        "table_name": "requirements_planning",
        # 来自V2 requirements_planning.py
        "api_endpoint": "/yonbip/mfg/requirementsplanning/getPlanOrderList",
        "md_file": "需求计划.md",
        "is_active": True,
    },
]
