材料出库列表查询
发布时间:2024-09-28 01:59:34
材料出库列表查询

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	动态域名，获取方式详见 获取租户所在数据中心域名
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/materialout/list
请求方式	POST
ContentType	application/json
应用场景	开放API
API类别	
事务和幂等性	无
限流次数	40次/分钟
购
多语	不支持
2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
pageIndex	int	否	是	页号    默认值: 1
pageSize	int	否	是	每页行数    默认值: 10
open_vouchdate_begin	string	否	否	单据开始时间
open_vouchdate_end	string	否	否	单据结束时间
bustype_name	string	否	否	交易类型
stockOrg	long	是	否	库存组织id
stockOrg_code	string	是	否	库存组织编码
stockOrg_name	string	否	否	库存组织名称
product_cName	string	是	否	物料ID
product.productClass.name	string	是	否	物料分类ID
simpleVOs	object	是	否	扩展查询条件
field	string	否	否	属性名(条件),子表加前缀[materOuts.];materOuts.upcoded为来源单据号
op	string	否	否	条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)
value1	string	否	否	值1(条件),单条件时仅使用这个配置
value2	string	否	否	值2(条件),单条件时此配置无效
3. 请求示例
Url: /yonbip/scm/materialout/list?access_token=访问令牌
Body: {
	"pageIndex": 0,
	"pageSize": 0,
	"open_vouchdate_begin": "",
	"open_vouchdate_end": "",
	"bustype_name": "",
	"stockOrg": [
		0
	],
	"stockOrg_code": [
		""
	],
	"stockOrg_name": "",
	"product_cName": [
		""
	],
	"product.productClass.name": [
		""
	],
	"simpleVOs": [
		{
			"field": "",
			"op": "",
			"value1": "",
			"value2": ""
		}
	]
}
4. 返回值参数
名称	类型	数组	描述
code	string	否	返回码，调用成功时返回200
message	string	否	调用失败时的错误信息
data	object	否	调用成功时的返回数据
salesOrgId	string	否	销售组织id
saleDepartmentId	string	否	销售部门id
transactionTypeId	string	否	交易类型id
settlementOrgId	string	否	开票组织id
sumRecordList	object	是	sum合计信息
totalPieces	string	否	合计价格
totalQuantity	string	否	合计数量
subQty	string	否	件数
qty	string	否	数量
pageIndex	int	否	页码
pageSize	int	否	每页条数
pageCount	int	否	总页数
beginPageIndex	int	否	开始页码
endPageIndex	int	否	结束页码
recordCount	int	否	总条数
pubts	string	否	时间戳,格式为:yyyy-MM-dd HH:mm:ss
recordList	object	是	返回结果对象
currency	string	否	币种id
materOuts_product	string	否	物料id
materOuts_unit	string	否	主计量
materOuts_productsku	string	否	物料sku
vouchdate	string	否	单据日期
code	string	否	单据编号
org	string	否	库存组织IDid
org_code	string	否	库存组织编码
org_name	string	否	库存组织名称
store	string	否	门店id
bustype	string	否	业务类型id
bustype_name	string	否	交易类型名称
store_name	string	否	门店名称
department_name	string	否	部门名称
department	string	否	部门id
warehouse	string	否	仓库id
warehouse_name	string	否	仓库名称
stockMgr_name	string	否	库管员名称
stockMgr	string	否	库管员IDid
memo	string	否	备注
bustype_extend_attrs_json	string	否	出库类型
accountOrg_name	string	否	会计主体名称
accountOrg	string	否	会计主体id
totalPieces	string	否	整单件数
exchangestatus	string	否	交换状态
status	string	否	单据状态, 0:未提交、1:已提交、
totalQuantity	int	否	整单数量
srcbill	string	否	来源单据id
creator	string	否	创建人
srcbillno	string	否	来源单据
srcBillType	string	否	来源上级单据类型, productionorder.po_production_order_ustock:生产订单材料、upu.st_purchaseorder:采购订单、upu.pu_arrivalorder:到货订单、productionorder.po_production_order:生产订单产品、st_storeprorecord:产品入库单、st_storecheckplan:盘点倒冲、po_production_order:生产订单、2:计划订单、3:销售订单、
createTime	string	否	创建时间,格式为:yyyy-MM-dd HH:mm:ss
modifier	string	否	修改人
modifyTime	string	否	修改时间,格式为:yyyy-MM-dd HH:mm:ss
auditor	string	否	提交人
auditTime	string	否	提交时间,格式为:yyyy-MM-dd HH:mm:ss
id	string	否	主表id
pubts	string	否	时间戳,格式为:yyyy-MM-dd HH:mm:ss
tplid	string	否	模板id
materOuts	object	否	以下字段名需要拼接materOuts!
id	string	否	子表id
product_cCode	string	否	物料编码
product_cName	string	否	物料名称
productsku_cCode	string	否	sku编码
productsku_cName	string	否	sku名称
productClass_code	string	否	物料分类编码
propertiesValue	string	否	规格
batchno	string	否	批次号
invaliddate	string	否	有效期至
qty	int	否	数量
product_unitName	string	否	计量单位
subQty	int	否	件数
stockUnitId	string	否	库存单位id
stockUnit_name	string	否	库存单位
project_code	string	否	项目编码
project_name	string	否	项目名称
natUnitPrice	int	否	单价
natMoney	int	否	金额
natCurrency_priceDigit	string	否	币种单价精度
natCurrency_moneyDigit	string	否	币种金额精度
unit_code	string	否	主计量编码
unit_Precision	string	否	主计量精度
stockUnitId_Precision	string	否	库存单位精度
materialOutsCharacteristics	特征组
st.materialout.MaterialOuts	否	自由项特征组
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
materialOutsDefineCharacter	特征组
st.materialout.MaterialOuts	否	子表自定义项特征组
XS11	string	否	需求分类号test
id	string	否	特征id,主键,新增时无需填写,修改时必填
materialOutDefineCharacter	特征组
st.materialout.MaterialOut	否	主表自定义项特征组
TL001	string	否	退料理由
id	string	否	特征id,主键,新增时无需填写,修改时必填
isWip	string	否	是否在制品
costAccountingMethod	string	否	委外成本核算方式：0 按委外入库核算成本，1 按委外订单核算成本
bodyParallel	object	否	材料出库子表平行表（st.materialout.MaterialOutsParallel）
wipOpSn	string	否	在制品工序顺序号
wipOperationId	string	否	在制品工序ID
odyParallel_wipOperationCode	string	否	在制品工序编码
bodyParallel_wipOperationName	string	否	在制品工序名称
out_sys_id	string	否	外部来源线索
out_sys_code	string	否	外部来源编码
out_sys_version	string	否	外部来源版本
out_sys_type	string	否	外部来源类型
out_sys_rowno	string	否	外部来源行号
out_sys_lineid	string	否	外部来源行
5. 正确返回示例
{
	"code": "",
	"message": "",
	"data": {
		"sumRecordList": [
			{
				"totalPieces": "",
				"totalQuantity": "",
				"subQty": "",
				"qty": ""
			}
		],
		"pageIndex": 0,
		"pageSize": 0,
		"pageCount": 0,
		"beginPageIndex": 0,
		"endPageIndex": 0,
		"recordCount": 0,
		"pubts": "",
		"recordList": [
			{
				"currency": "",
				"materOuts_product": "",
				"materOuts_unit": "",
				"materOuts_productsku": "",
				"vouchdate": "",
				"code": "",
				"org": "",
				"org_code": "",
				"org_name": "",
				"store": "",
				"bustype": "",
				"bustype_name": "",
				"store_name": "",
				"department_name": "",
				"department": "",
				"warehouse": "",
				"warehouse_name": "",
				"stockMgr_name": "",
				"stockMgr": "",
				"memo": "",
				"bustype_extend_attrs_json": "",
				"accountOrg_name": "",
				"accountOrg": "",
				"totalPieces": "",
				"exchangestatus": "",
				"status": "",
				"totalQuantity": 0,
				"srcbill": "",
				"creator": "",
				"srcbillno": "",
				"srcBillType": "",
				"createTime": "",
				"modifier": "",
				"modifyTime": "",
				"auditor": "",
				"auditTime": "",
				"id": "",
				"pubts": "",
				"tplid": "",
				"materOuts": {
					"id": ""
				},
				"product_cCode": "",
				"product_cName": "",
				"productsku_cCode": "",
				"productsku_cName": "",
				"productClass_code": "",
				"propertiesValue": "",
				"batchno": "",
				"invaliddate": "",
				"qty": 0,
				"product_unitName": "",
				"subQty": 0,
				"stockUnitId": "",
				"stockUnit_name": "",
				"project_code": "",
				"project_name": "",
				"natUnitPrice": 0,
				"natMoney": 0,
				"natCurrency_priceDigit": "",
				"natCurrency_moneyDigit": "",
				"unit_code": "",
				"unit_Precision": "",
				"stockUnitId_Precision": "",
				"materialOutsCharacteristics": {
					"XS15": "",
					"XXX0111": "",
					"id": ""
				},
				"materialOutsDefineCharacter": {
					"XS11": "",
					"id": ""
				},
				"materialOutDefineCharacter": {
					"TL001": "",
					"id": ""
				},
				"isWip": "",
				"costAccountingMethod": "",
				"bodyParallel": {
					"wipOpSn": "",
					"wipOperationId": ""
				},
				"odyParallel_wipOperationCode": "",
				"bodyParallel_wipOperationName": "",
				"out_sys_id": "",
				"out_sys_code": "",
				"out_sys_version": "",
				"out_sys_type": "",
				"out_sys_rowno": "",
				"out_sys_lineid": ""
			}
		]
	}
}
6. 错误返回码
错误码	错误信息	描述
7. 错误返回示例
{
 "code": "999",
 "message": "No enum constant org.imeta.core.base.ConditionOperator.2"