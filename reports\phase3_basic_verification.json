{"timestamp": "2025-08-06T03:21:38.863171", "verification_results": {"backend_startup": {"start_server.py": "存在", "start_simple.py": "存在", "dependencies": "满足"}, "frontend_startup": {"unified-field-config.html": "存在", "database-v2.html": "存在", "excel-translation.html": "存在", "field-config-manual.html": "存在", "http_server": "成功"}, "database_connection": {"db_file": "存在", "connection": "成功，5个表", "consistency_check": "异常: usage: database_dual_writer_simple.py [-h] [--check] [--legacy-only]\ndatabase_dual_writer_simple.py: error: unrecognized arguments: --test\n"}, "api_health": {"endpoints": ["/health", "/api/health", "/api/modules", "/api/field_config"], "base_urls": ["http://localhost:8000", "http://localhost:5000"], "status": "需要手动启动后端服务"}}, "summary": {"total_checks": 14, "passed_checks": 10, "success_rate": 71.42857142857143}}