<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>12406993fdf24559969621f65e25b53f</id>
<name>销售订单列表查询</name>
<apiClassifyId>2027941076108574729</apiClassifyId>
<apiClassifyName>销售订单</apiClassifyName>
<apiClassifyCode/>
<parentApiClassifies/>
<functionId/>
<openMode>0</openMode>
<description>用于批量分页查询销售订单数据的接口，销售订单列表查询与单据列表界面默认查询方案返回结果一致</description>
<auth>true</auth>
<bodyPassthrough>false</bodyPassthrough>
<healthExam>false</healthExam>
<healthStatus>true</healthStatus>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/sd/voucherorder/list</completeProxyUrl>
<connectUrl>/bill/list</connectUrl>
<sort>20</sort>
<handler>openapi</handler>
<httpRequestType>POST</httpRequestType>
<openApi>true</openApi>
<preset>false</preset>
<productId>2027939375301525505</productId>
<productCode>xsfw</productCode>
<proxyUrl>/yonbip/sd/voucherorder/list</proxyUrl>
<requestParamsDemo>Url: /yonbip/sd/voucherorder/list?access_token=访问令牌 Body: { "pageIndex": 1, "pageSize": 10, "code": "UO-20220513000001", "nextStatusName": "CONFIRMORDER", "open_orderDate_begin": "2022-05-13 00:00:00", "open_orderDate_end": "2022-05-13 00:00:00", "open_hopeReceiveDate_begin": "2022-05-13 00:00:00", "open_hopeReceiveDate_end": "2022-05-13 00:00:00", "open_vouchdate_begin": "2022-05-13 00:00:00", "open_vouchdate_end": "2022-05-13 00:00:00", "isSum": false, "simpleVOs": [ { "op": "eq", "value1": "UO-20220513000001", "field": "code" } ], "queryOrders": [ { "field": "vouchdate", "order": "asc" } ] }</requestParamsDemo>
<requestProtocol>HTTP</requestProtocol>
<serviceHttpMethod>POST</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2020-01-16 16:22:26</gmtCreate>
<gmtUpdate>2025-02-18 14:13:08.000</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/sd/voucherorder/list</address>
<productName>销售服务</productName>
<productClassifyId>yonsuite</productClassifyId>
<productClassifyCode>yonbip</productClassifyCode>
<productClassifyName>用友 YonBIP</productClassifyName>
<paramDTOS>
<paramDTOS>
<id>2201709826387476507</id>
<name>pageIndex</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId>1795124488263696391</defParamId>
<array>false</array>
<paramDesc>页号</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>1</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag>[pageIndex]</paramTag>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2201709826387476508</id>
<name>pageSize</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId>1795124488263696392</defParamId>
<array>false</array>
<paramDesc>每页行数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>10</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>10</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag>[pageSize]</paramTag>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2201709826387476509</id>
<name>code</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId>1795124488263696393</defParamId>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>UO-20220513000001</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2201709826387476510</id>
<name>nextStatusName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId>1795124488263696394</defParamId>
<array>false</array>
<paramDesc>订单状态, CONFIRMORDER:开立、DELIVERY_PART:部分发货、DELIVERY_TAKE_PART:部分发货待收货、DELIVERGOODS:待发货、TAKEDELIVERY:待收货、ENDORDER:已完成、OPPOSE:已取消、APPROVING:审批中、</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>CONFIRMORDER</example>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2201709826387476511</id>
<name>open_orderDate_begin</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId>1795124488263696395</defParamId>
<array>false</array>
<paramDesc>制单日期开始时间,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-05-13 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2201709826387476512</id>
<name>open_orderDate_end</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId>1795124488263696396</defParamId>
<array>false</array>
<paramDesc>制单结束时间,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-05-13 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2201709826387476513</id>
<name>open_hopeReceiveDate_begin</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId>1795124488263696397</defParamId>
<array>false</array>
<paramDesc>期望收货开始时间,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-05-13 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2201709826387476514</id>
<name>open_hopeReceiveDate_end</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId>1795124488263696398</defParamId>
<array>false</array>
<paramDesc>期望收货截止,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-05-13 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2201709826387476515</id>
<name>open_vouchdate_begin</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId>1795124488263696399</defParamId>
<array>false</array>
<paramDesc>单据开始时间,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-05-13 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2201709826387476516</id>
<name>open_vouchdate_end</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId>1795124488263696400</defParamId>
<array>false</array>
<paramDesc>单据截止时间,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-05-13 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2201709826387476517</id>
<name>isSum</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId>1795124488263696401</defParamId>
<array>false</array>
<paramDesc>查询表头</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>false</example>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>false</defaultValue>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2201709826387476498</id>
<name>simpleVOs</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<children>
<children>
<id>2201709826387476499</id>
<name>op</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476498</parentId>
<defParamId>1795124488263696403</defParamId>
<array>false</array>
<paramDesc>比较符(eq:等于;neq:不等于;lt:小于;gt:大于;like:模糊匹配;between:介于)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>eq</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2201709826387476500</id>
<name>value1</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476498</parentId>
<defParamId>1795124488263696404</defParamId>
<array>false</array>
<paramDesc>查询条件值1</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>UO-20220513000001</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2201709826387476501</id>
<name>field</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476498</parentId>
<defParamId>1795124488263696405</defParamId>
<array>false</array>
<paramDesc>查询条件字段名</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>code</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2201709826387476502</id>
<name>logicOp</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476498</parentId>
<defParamId>1795124488263696406</defParamId>
<array>false</array>
<paramDesc>分级逻辑符(and,or)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2201709826387476503</id>
<name>value2</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476498</parentId>
<defParamId>1795124488263696407</defParamId>
<array>false</array>
<paramDesc>查询条件值2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>1795124488263696402</defParamId>
<array>true</array>
<paramDesc>查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2201709826387476504</id>
<name>queryOrders</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<children>
<children>
<id>2201709826387476505</id>
<name>field</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476504</parentId>
<defParamId>1795124488263696409</defParamId>
<array>false</array>
<paramDesc>排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：orderDetails.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)    示例：id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>vouchdate</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2201709826387476506</id>
<name>order</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476504</parentId>
<defParamId>1795124488263696410</defParamId>
<array>false</array>
<paramDesc>顺序:正序(asc);倒序(desc) 示例：asc</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>asc</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>1795124488263696408</defParamId>
<array>true</array>
<paramDesc>排序字段</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>2201709826387476527</id>
<name>pageIndex</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>页号</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageIndex</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2201709826387476528</id>
<name>pageSize</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>每页行数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageSize</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2201709826387476529</id>
<name>code</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>code</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2201709826387476530</id>
<name>nextStatusName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>订单状态, CONFIRMORDER:开立、DELIVERY_PART:部分发货、DELIVERY_TAKE_PART:部分发货待收货、DELIVERGOODS:待发货、TAKEDELIVERY:待收货、ENDORDER:已完成、OPPOSE:已取消、APPROVING:审批中、</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>nextStatusName</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2201709826387476531</id>
<name>open_orderDate_begin</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>制单日期开始时间,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_orderDate_begin</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2201709826387476532</id>
<name>open_orderDate_end</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>制单结束时间,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_orderDate_end</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2201709826387476533</id>
<name>open_hopeReceiveDate_begin</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>期望收货开始时间,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_hopeReceiveDate_begin</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2201709826387476534</id>
<name>open_hopeReceiveDate_end</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>期望收货截止,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_hopeReceiveDate_end</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2201709826387476535</id>
<name>open_vouchdate_begin</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据开始时间,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_vouchdate_begin</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2201709826387476536</id>
<name>open_vouchdate_end</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据截止时间,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_vouchdate_end</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2201709826387476537</id>
<name>isSum</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>查询表头</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>isSum</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>boolean</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2201709826387476518</id>
<name>simpleVOs</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<children>
<children>
<id>2201709826387476519</id>
<name>op</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476518</parentId>
<defParamId/>
<array>false</array>
<paramDesc>比较符(eq:等于;neq:不等于;lt:小于;gt:大于;like:模糊匹配;between:介于)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>op</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709826387476520</id>
<name>value1</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476518</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值1</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value1</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709826387476521</id>
<name>field</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476518</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件字段名</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709826387476522</id>
<name>logicOp</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476518</parentId>
<defParamId/>
<array>false</array>
<paramDesc>分级逻辑符(and,or)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>logicOp</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709826387476523</id>
<name>value2</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476518</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value2</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>simpleVOs</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2201709826387476524</id>
<name>queryOrders</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<children>
<children>
<id>2201709826387476525</id>
<name>field</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476524</parentId>
<defParamId/>
<array>false</array>
<paramDesc>排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：orderDetails.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)    示例：id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709826387476526</id>
<name>order</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709826387476524</parentId>
<defParamId/>
<array>false</array>
<paramDesc>顺序:正序(asc);倒序(desc) 示例：asc</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>order</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>排序字段</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>queryOrders</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-14 13:59:18.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>2201709834977411412</id>
<name>code</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId>1795124488263696431</defParamId>
<array>false</array>
<paramDesc>返回码，调用成功时返回200</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2201709834977411413</id>
<name>message</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<defParamId>1795124488263696432</defParamId>
<array>false</array>
<paramDesc>调用失败时的错误信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2201709834977411081</id>
<name>data</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId/>
<children>
<children>
<id>2201709834977411406</id>
<name>pageIndex</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411081</parentId>
<defParamId>1795124488263696434</defParamId>
<array>false</array>
<paramDesc>页号</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411407</id>
<name>pageSize</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411081</parentId>
<defParamId>1795124488263696435</defParamId>
<array>false</array>
<paramDesc>每页记录数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411408</id>
<name>recordCount</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411081</parentId>
<defParamId>1795124488263696436</defParamId>
<array>false</array>
<paramDesc>总共记录数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>276</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411082</id>
<name>recordList</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411081</parentId>
<children>
<children>
<id>2201709834977411147</id>
<name>code</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696438</defParamId>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411148</id>
<name>vouchdate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696439</defParamId>
<array>false</array>
<paramDesc>单据日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411149</id>
<name>id</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696440</defParamId>
<array>false</array>
<paramDesc>主体ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411150</id>
<name>parentOrderNo</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696441</defParamId>
<array>false</array>
<paramDesc>父级订单号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411151</id>
<name>salesOrgId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>2201709826387476487</defParamId>
<array>false</array>
<paramDesc>销售组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411152</id>
<name>salesOrgId_name</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696443</defParamId>
<array>false</array>
<paramDesc>销售组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411153</id>
<name>saleDepartmentId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>2201709826387476488</defParamId>
<array>false</array>
<paramDesc>销售部门id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411154</id>
<name>transactionTypeId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>2201709826387476486</defParamId>
<array>false</array>
<paramDesc>交易类型id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411155</id>
<name>transactionTypeId_name</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696446</defParamId>
<array>false</array>
<paramDesc>交易类型名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411156</id>
<name>agentId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696447</defParamId>
<array>false</array>
<paramDesc>客户id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411157</id>
<name>agentId_name</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696448</defParamId>
<array>false</array>
<paramDesc>客户名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411158</id>
<name>receiveContacter</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696449</defParamId>
<array>false</array>
<paramDesc>客户联系人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411159</id>
<name>receiveContacterPhone</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696450</defParamId>
<array>false</array>
<paramDesc>客户联系人电话</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411160</id>
<name>receievInvoiceMobile</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696451</defParamId>
<array>false</array>
<paramDesc>收票手机号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411161</id>
<name>receievInvoiceEmail</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696452</defParamId>
<array>false</array>
<paramDesc>收票邮箱</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411162</id>
<name>purchaseNo</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696453</defParamId>
<array>false</array>
<paramDesc>客户采购订单号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411163</id>
<name>corpContact</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696454</defParamId>
<array>false</array>
<paramDesc>销售业务员id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411164</id>
<name>corpContactUserName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696455</defParamId>
<array>false</array>
<paramDesc>销售业务员</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411165</id>
<name>settlementOrgId_name</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696456</defParamId>
<array>false</array>
<paramDesc>开票组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411166</id>
<name>corpContactUserErpCode</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696457</defParamId>
<array>false</array>
<paramDesc>业务员erp编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411083</id>
<name>orderPrices</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<children>
<children>
<id>2201709834977411084</id>
<name>currency</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>1795124488263696459</defParamId>
<array>false</array>
<paramDesc>币种id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411085</id>
<name>currency_priceDigit</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>1795124488263696460</defParamId>
<array>false</array>
<paramDesc>原币单价精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411086</id>
<name>currency_moneyDigit</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>1795124488263696461</defParamId>
<array>false</array>
<paramDesc>原币金额精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411087</id>
<name>originalName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>1795124488263696462</defParamId>
<array>false</array>
<paramDesc>币种</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411088</id>
<name>natCurrency</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>1795124488263696463</defParamId>
<array>false</array>
<paramDesc>本币pk</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411089</id>
<name>natCurrency_priceDigit</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>1795124488263696464</defParamId>
<array>false</array>
<paramDesc>本币单价精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411090</id>
<name>natCurrency_moneyDigit</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>1795124488263696465</defParamId>
<array>false</array>
<paramDesc>本币金额精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411091</id>
<name>domesticCode</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>1795124488263696466</defParamId>
<array>false</array>
<paramDesc>本币简称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411092</id>
<name>domesticName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>1795124488263696467</defParamId>
<array>false</array>
<paramDesc>本币</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411093</id>
<name>exchRate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>1795124488263696468</defParamId>
<array>false</array>
<paramDesc>汇率</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411094</id>
<name>exchangeRateType_name</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>1795124488263696469</defParamId>
<array>false</array>
<paramDesc>汇率类型名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411095</id>
<name>exchangeRateType</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>1795124488263696470</defParamId>
<array>false</array>
<paramDesc>汇率类型Idid</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411096</id>
<name>ctTplId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>2021969645626458112</defParamId>
<array>false</array>
<paramDesc>合同模板id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411097</id>
<name>ctTplCode</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>2021969645626458113</defParamId>
<array>false</array>
<paramDesc>合同模板编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411098</id>
<name>ctTplName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>2021969645626458114</defParamId>
<array>false</array>
<paramDesc>合同模板</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411099</id>
<name>signFileId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>2154249965436141569</defParamId>
<array>false</array>
<paramDesc>待签署合同文件</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411100</id>
<name>signStatus</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411083</parentId>
<defParamId>2158794487499325440</defParamId>
<array>false</array>
<paramDesc>电子签署状态</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1795124488263696458</defParamId>
<array>false</array>
<paramDesc>订单价格</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411167</id>
<name>payMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696471</defParamId>
<array>false</array>
<paramDesc>合计含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411168</id>
<name>orderPayMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696472</defParamId>
<array>false</array>
<paramDesc>商品实付金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411169</id>
<name>realMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696473</defParamId>
<array>false</array>
<paramDesc>应收金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411170</id>
<name>orderRealMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696474</defParamId>
<array>false</array>
<paramDesc>商品应付金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411171</id>
<name>statusCode</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696475</defParamId>
<array>false</array>
<paramDesc>订单当前状态码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411172</id>
<name>nextStatus</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696476</defParamId>
<array>false</array>
<paramDesc>订单状态, CONFIRMORDER:开立、DELIVERY_PART:部分发货、DELIVERY_TAKE_PART:部分发货待收货、DELIVERGOODS:待发货、TAKEDELIVERY:待收货、ENDORDER:已完成、OPPOSE:已取消、APPROVING:审批中、</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411173</id>
<name>currentStatus</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696477</defParamId>
<array>false</array>
<paramDesc>当前状态位置</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411174</id>
<name>payStatusCode</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696478</defParamId>
<array>false</array>
<paramDesc>付款状态, NOTPAYMENT:未付款、PARTPAYMENT:部分付款、CONFIRMPAYMENT:部分付款待确认、CONFIRMPAYMENT_ALL:付款待确认、FINISHPAYMENT:付款完成、</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411175</id>
<name>settlementOrgId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>2201709826387476489</defParamId>
<array>false</array>
<paramDesc>开票组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411176</id>
<name>lockIn</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696480</defParamId>
<array>false</array>
<paramDesc>标记锁</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411177</id>
<name>confirmDate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696481</defParamId>
<array>false</array>
<paramDesc>订单确认时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411178</id>
<name>payDate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696482</defParamId>
<array>false</array>
<paramDesc>订单付款时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411179</id>
<name>orderPayType</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696483</defParamId>
<array>false</array>
<paramDesc>支付方式</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411180</id>
<name>settlement</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696484</defParamId>
<array>false</array>
<paramDesc>结算方式id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411181</id>
<name>shippingChoiceId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696485</defParamId>
<array>false</array>
<paramDesc>发运方式id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411182</id>
<name>sendDate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696486</defParamId>
<array>false</array>
<paramDesc>预计发货日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411183</id>
<name>hopeReceiveDate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696487</defParamId>
<array>false</array>
<paramDesc>期望收货日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411184</id>
<name>opposeMemo</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696488</defParamId>
<array>false</array>
<paramDesc>驳回批注</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411185</id>
<name>haveDelivery</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696489</defParamId>
<array>false</array>
<paramDesc>是否存在发货单</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411186</id>
<name>occupyInventory</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696490</defParamId>
<array>false</array>
<paramDesc>库存占用时机标识</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411187</id>
<name>separatePromotionType</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696491</defParamId>
<array>false</array>
<paramDesc>拆单规则标识</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411188</id>
<name>reight</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696492</defParamId>
<array>false</array>
<paramDesc>运费</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411189</id>
<name>synSourceOrg</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696493</defParamId>
<array>false</array>
<paramDesc>协同来源组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411190</id>
<name>synSourceTenant</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696494</defParamId>
<array>false</array>
<paramDesc>协同来源租户</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411191</id>
<name>synSourceOrg_name</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696495</defParamId>
<array>false</array>
<paramDesc>协同来源组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411192</id>
<name>totalMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696496</defParamId>
<array>false</array>
<paramDesc>总金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411193</id>
<name>tagName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696497</defParamId>
<array>false</array>
<paramDesc>采购组织弹框</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411194</id>
<name>rebateCashMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696498</defParamId>
<array>false</array>
<paramDesc>抵现金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411195</id>
<name>particularlyMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696499</defParamId>
<array>false</array>
<paramDesc>特殊优惠</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411196</id>
<name>promotionMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696500</defParamId>
<array>false</array>
<paramDesc>促销</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411197</id>
<name>unConfirmPrice</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696501</defParamId>
<array>false</array>
<paramDesc>未审核的金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411198</id>
<name>confirmPrice</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696502</defParamId>
<array>false</array>
<paramDesc>已支付金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411199</id>
<name>bizId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696503</defParamId>
<array>false</array>
<paramDesc>商家id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411200</id>
<name>bizName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696504</defParamId>
<array>false</array>
<paramDesc>商家名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411201</id>
<name>agentRelationId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696505</defParamId>
<array>false</array>
<paramDesc>客户交易关系id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411202</id>
<name>points</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696506</defParamId>
<array>false</array>
<paramDesc>积分</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411203</id>
<name>pubts</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696507</defParamId>
<array>false</array>
<paramDesc>时间戳,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411204</id>
<name>pubuts</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696508</defParamId>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411205</id>
<name>orderInvoice</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696509</defParamId>
<array>false</array>
<paramDesc>发票信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411206</id>
<name>orderShippingAddress</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696510</defParamId>
<array>false</array>
<paramDesc>收货地址信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411207</id>
<name>orderErp</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696511</defParamId>
<array>false</array>
<paramDesc>订单erp</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411208</id>
<name>deliveryDate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696512</defParamId>
<array>false</array>
<paramDesc>交货日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>62</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411209</id>
<name>isWfControlled</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696513</defParamId>
<array>false</array>
<paramDesc>是否审批流控制</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>63</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411210</id>
<name>verifystate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696514</defParamId>
<array>false</array>
<paramDesc>审批状态</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>64</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411211</id>
<name>status</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696515</defParamId>
<array>false</array>
<paramDesc>状态</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>65</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411101</id>
<name>headItem</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<children>
<children>
<id>2201709834977411102</id>
<name>id</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411101</parentId>
<defParamId>1795124488263696518</defParamId>
<array>false</array>
<paramDesc>表头自定义项id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411103</id>
<name>define1</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411101</parentId>
<defParamId>1795124488263696519</defParamId>
<array>false</array>
<paramDesc>表头自定义项1-60</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1795124488263696517</defParamId>
<array>false</array>
<paramDesc>表头自定义项</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>67</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411213</id>
<name>isFinishDelivery</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696520</defParamId>
<array>false</array>
<paramDesc>订单是否发完货</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>68</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411214</id>
<name>productId_pbatchName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696521</defParamId>
<array>false</array>
<paramDesc>商品包装单位</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>69</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411215</id>
<name>idKey</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696522</defParamId>
<array>false</array>
<paramDesc>行标识</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>70</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411216</id>
<name>productId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696523</defParamId>
<array>false</array>
<paramDesc>商品id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>71</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411217</id>
<name>priceMark</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696524</defParamId>
<array>false</array>
<paramDesc>价格标识</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>72</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411218</id>
<name>isBatchManage</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696525</defParamId>
<array>false</array>
<paramDesc>是否批次管理</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>73</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411219</id>
<name>isExpiryDateManage</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696526</defParamId>
<array>false</array>
<paramDesc>是否有效期管理</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>74</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411220</id>
<name>expireDateNo</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696527</defParamId>
<array>false</array>
<paramDesc>保质期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>75</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411221</id>
<name>expireDateUnit</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696528</defParamId>
<array>false</array>
<paramDesc>保质期单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>76</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411222</id>
<name>skuId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696529</defParamId>
<array>false</array>
<paramDesc>商品SKUid</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>77</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411223</id>
<name>erpCode</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696530</defParamId>
<array>false</array>
<paramDesc>skuERP编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>78</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411224</id>
<name>orderProductType</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696531</defParamId>
<array>false</array>
<paramDesc>商品售卖类型, SALE:销售品、GIFT:赠品、MARKUP:加价购、REBATE:返利商品、</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>79</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411225</id>
<name>productCode</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696532</defParamId>
<array>false</array>
<paramDesc>商品编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>80</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411226</id>
<name>productName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696533</defParamId>
<array>false</array>
<paramDesc>商品名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>81</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411227</id>
<name>skuCode</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696534</defParamId>
<array>false</array>
<paramDesc>SKU编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>82</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411228</id>
<name>specDescription</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696535</defParamId>
<array>false</array>
<paramDesc>规格描述</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>83</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411229</id>
<name>projectId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696536</defParamId>
<array>false</array>
<paramDesc>项目id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>84</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411230</id>
<name>unitExchangeType</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696537</defParamId>
<array>false</array>
<paramDesc>浮动（计价）</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>85</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411231</id>
<name>unitExchangeTypePrice</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696538</defParamId>
<array>false</array>
<paramDesc>浮动（销售）</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>86</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411232</id>
<name>productAuxUnitName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696539</defParamId>
<array>false</array>
<paramDesc>销售单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>87</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411233</id>
<name>invExchRate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696540</defParamId>
<array>false</array>
<paramDesc>销售换算率</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>88</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411234</id>
<name>subQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696541</defParamId>
<array>false</array>
<paramDesc>销售数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>89</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411235</id>
<name>productUnitName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696542</defParamId>
<array>false</array>
<paramDesc>计价单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>90</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411236</id>
<name>invPriceExchRate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696543</defParamId>
<array>false</array>
<paramDesc>计价换算率</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>91</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411237</id>
<name>priceQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696544</defParamId>
<array>false</array>
<paramDesc>计价数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>92</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411238</id>
<name>qtyName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696545</defParamId>
<array>false</array>
<paramDesc>主计量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>93</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411239</id>
<name>qty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696546</defParamId>
<array>false</array>
<paramDesc>数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>94</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411240</id>
<name>variantconfigctsId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>2027927770307756033</defParamId>
<array>false</array>
<paramDesc>选配结果清单id</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>5235234243</example>
<fullName/>
<ytenantId/>
<paramOrder>95</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>20</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411104</id>
<name>orderDetailPrices</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<children>
<children>
<id>2201709834977411105</id>
<name>saleCost_orig_taxfree</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696548</defParamId>
<array>false</array>
<paramDesc>原币无税合计</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411106</id>
<name>oriUnitPrice</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696549</defParamId>
<array>false</array>
<paramDesc>无税成交价</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411107</id>
<name>oriMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696550</defParamId>
<array>false</array>
<paramDesc>无税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411108</id>
<name>oriTax</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696551</defParamId>
<array>false</array>
<paramDesc>税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411109</id>
<name>natSum</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696552</defParamId>
<array>false</array>
<paramDesc>本币含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411110</id>
<name>natTaxUnitPrice</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696553</defParamId>
<array>false</array>
<paramDesc>本币含税单价</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411111</id>
<name>natMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696554</defParamId>
<array>false</array>
<paramDesc>本币无税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411112</id>
<name>natUnitPrice</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696555</defParamId>
<array>false</array>
<paramDesc>本币无税单价</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411113</id>
<name>natTax</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696556</defParamId>
<array>false</array>
<paramDesc>本币税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411114</id>
<name>orderDetailId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696557</defParamId>
<array>false</array>
<paramDesc>订单详情id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411115</id>
<name>salePrice_orig_taxfree</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696558</defParamId>
<array>false</array>
<paramDesc>无税单价</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411116</id>
<name>rebateMoneyOrigTaxfree</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696559</defParamId>
<array>false</array>
<paramDesc>无税分摊返利</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411117</id>
<name>particularlyMoneyOrigTaxfree</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696560</defParamId>
<array>false</array>
<paramDesc>无税特殊优惠</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411118</id>
<name>promotionMoneyOrigTaxfree</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696561</defParamId>
<array>false</array>
<paramDesc>无税促销优惠</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411119</id>
<name>pointsMoneyOrigTaxfree</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696562</defParamId>
<array>false</array>
<paramDesc>无税积分抵扣</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411120</id>
<name>saleCost_domestic</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696563</defParamId>
<array>false</array>
<paramDesc>报价本币含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411121</id>
<name>salePrice_domestic</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696564</defParamId>
<array>false</array>
<paramDesc>报价本币含税单价</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411122</id>
<name>rebateMoneyDomestic</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696565</defParamId>
<array>false</array>
<paramDesc>本币分摊返利</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411123</id>
<name>particularlyMoneyDomestic</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696566</defParamId>
<array>false</array>
<paramDesc>本币特殊优惠</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411124</id>
<name>promotionMoneyDomestic</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696567</defParamId>
<array>false</array>
<paramDesc>本币促销优惠</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411125</id>
<name>pointsMoneyDomestic</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696568</defParamId>
<array>false</array>
<paramDesc>本币积分抵扣</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411126</id>
<name>saleCost_domestic_taxfree</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696569</defParamId>
<array>false</array>
<paramDesc>报价本币无税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411127</id>
<name>salePrice_domestic_taxfree</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696570</defParamId>
<array>false</array>
<paramDesc>报价本币无税单价</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411128</id>
<name>rebateMoneyDomesticTaxfree</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696571</defParamId>
<array>false</array>
<paramDesc>本币无税分摊返利</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411129</id>
<name>particularlyMoneyDomesticTaxfree</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696572</defParamId>
<array>false</array>
<paramDesc>本币无税特殊优惠</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411130</id>
<name>promotionMoneyDomesticTaxfree</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696573</defParamId>
<array>false</array>
<paramDesc>本币无税促销优惠</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411131</id>
<name>pointsMoneyDomesticTaxfree</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696574</defParamId>
<array>false</array>
<paramDesc>本币无税积分抵扣</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411132</id>
<name>id</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1795124488263696575</defParamId>
<array>false</array>
<paramDesc>主体ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411133</id>
<name>prepayInvRvnRecogBkgMeth</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>1980432948212006912</defParamId>
<array>false</array>
<paramDesc>预收款开票应收入账方式 (1: 预收款开票-税额记应收 ; 2 : 预收款开票-全额记应收 )</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411134</id>
<name>checkByRevenueManagement</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>2019234704966287361</defParamId>
<array>false</array>
<paramDesc>收入管理核算</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411135</id>
<name>revPerformObligation</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>2019234704966287362</defParamId>
<array>false</array>
<paramDesc>已生成收入履约义务</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411136</id>
<name>serviceStartDate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>2019234704966287363</defParamId>
<array>false</array>
<paramDesc>服务起始日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411137</id>
<name>serviceEndDate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>2019234704966287364</defParamId>
<array>false</array>
<paramDesc>服务结束日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411138</id>
<name>optionalQuotationId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>2027927770307756034</defParamId>
<array>false</array>
<paramDesc>报价配置清单ID</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>4323234234</example>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>20</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411139</id>
<name>optionalQuotationId_code</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>2027927770307756035</defParamId>
<array>false</array>
<paramDesc>报价配置清单编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>A123123</example>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411140</id>
<name>variantconfigctsCode</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>2027927770307756036</defParamId>
<array>false</array>
<paramDesc>配置号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>B234234</example>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411141</id>
<name>variantconfigctsVersion</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>2027927770307756037</defParamId>
<array>false</array>
<paramDesc>配置清单版本</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1.0</example>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411142</id>
<name>calBase</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411104</parentId>
<defParamId>2116415229158490117</defParamId>
<array>false</array>
<paramDesc>计算基准</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1795124488263696547</defParamId>
<array>false</array>
<paramDesc>订单商品金额</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>96</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible/>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411241</id>
<name>stockName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696576</defParamId>
<array>false</array>
<paramDesc>发货仓库</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>97</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411242</id>
<name>stockOrgId_name</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696577</defParamId>
<array>false</array>
<paramDesc>库存组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>98</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411243</id>
<name>consignTime</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696578</defParamId>
<array>false</array>
<paramDesc>计划发货日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>99</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411244</id>
<name>projectId_name</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696579</defParamId>
<array>false</array>
<paramDesc>项目名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>100</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411245</id>
<name>projectId_code</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696580</defParamId>
<array>false</array>
<paramDesc>项目编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>101</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411246</id>
<name>noTaxSalePrice</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696581</defParamId>
<array>false</array>
<paramDesc>无税报价</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>102</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411247</id>
<name>salePrice</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696582</defParamId>
<array>false</array>
<paramDesc>含税报价</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>103</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411248</id>
<name>taxId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696583</defParamId>
<array>false</array>
<paramDesc>数目税率id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>104</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411249</id>
<name>costCurrencyName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696584</defParamId>
<array>false</array>
<paramDesc>成本币种</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>105</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411250</id>
<name>costAmt</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696585</defParamId>
<array>false</array>
<paramDesc>成本金额</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>106</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411251</id>
<name>costPrice</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696586</defParamId>
<array>false</array>
<paramDesc>成本价</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>107</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411252</id>
<name>noTaxSaleCost</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696587</defParamId>
<array>false</array>
<paramDesc>报价无税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>108</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411253</id>
<name>saleCost</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696588</defParamId>
<array>false</array>
<paramDesc>报价含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>109</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411254</id>
<name>stockId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696589</defParamId>
<array>false</array>
<paramDesc>仓库ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>110</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411255</id>
<name>oriTaxUnitPrice</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696590</defParamId>
<array>false</array>
<paramDesc>含税成交价</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>111</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411256</id>
<name>lineno</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696591</defParamId>
<array>false</array>
<paramDesc>行号</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>112</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411257</id>
<name>orderDetails_stockOrgId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696592</defParamId>
<array>false</array>
<paramDesc>库存组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>113</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411258</id>
<name>oriSum</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696593</defParamId>
<array>false</array>
<paramDesc>含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>114</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411259</id>
<name>transactionTypeId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>2201709826387476486</defParamId>
<array>false</array>
<paramDesc>交易类型id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>115</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411260</id>
<name>taxRate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696595</defParamId>
<array>false</array>
<paramDesc>税率</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>116</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411261</id>
<name>taxItems</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696596</defParamId>
<array>false</array>
<paramDesc>税目</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>117</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411262</id>
<name>taxCode</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696597</defParamId>
<array>false</array>
<paramDesc>税目税率编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>118</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411263</id>
<name>rebateMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696598</defParamId>
<array>false</array>
<paramDesc>返利分摊金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>119</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411264</id>
<name>pointsMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696599</defParamId>
<array>false</array>
<paramDesc>积分</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>120</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411265</id>
<name>shoppingCartId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696600</defParamId>
<array>false</array>
<paramDesc>购物车id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>121</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411266</id>
<name>groupId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696601</defParamId>
<array>false</array>
<paramDesc>分组Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>122</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411267</id>
<name>rebateReturnProductId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696602</defParamId>
<array>false</array>
<paramDesc>返货单商品id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>123</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411268</id>
<name>mutualActivities</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696603</defParamId>
<array>false</array>
<paramDesc>活动的对象,用于校验互斥活动</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>124</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411269</id>
<name>activities</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696604</defParamId>
<array>false</array>
<paramDesc>包含的类型,用于校验互斥</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>125</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411270</id>
<name>salesOrgId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>2201709826387476487</defParamId>
<array>false</array>
<paramDesc>销售组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>126</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411271</id>
<name>sendPayMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696606</defParamId>
<array>false</array>
<paramDesc>累计已发货含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>127</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411272</id>
<name>saleDepartmentId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>2201709826387476488</defParamId>
<array>false</array>
<paramDesc>销售部门id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>128</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411273</id>
<name>invoiceQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696608</defParamId>
<array>false</array>
<paramDesc>累计开票数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>129</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411274</id>
<name>settlementOrgId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>2201709826387476489</defParamId>
<array>false</array>
<paramDesc>财务组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>130</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411275</id>
<name>invoiceOriSum</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696610</defParamId>
<array>false</array>
<paramDesc>累计开票含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>131</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411276</id>
<name>bizProductId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696611</defParamId>
<array>false</array>
<paramDesc>商家商品id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>132</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411277</id>
<name>takeQuantity</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696612</defParamId>
<array>false</array>
<paramDesc>已审核收数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>133</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411278</id>
<name>bizSkuId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696613</defParamId>
<array>false</array>
<paramDesc>商家skuid</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>134</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411279</id>
<name>takeSalePayMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696614</defParamId>
<array>false</array>
<paramDesc>已审核收金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>135</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411280</id>
<name>orderDetailPrice</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696615</defParamId>
<array>false</array>
<paramDesc>订单金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>136</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411281</id>
<name>sendQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696616</defParamId>
<array>false</array>
<paramDesc>累计已发货数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>137</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411282</id>
<name>closedSumMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696617</defParamId>
<array>false</array>
<paramDesc>关闭总金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>138</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411283</id>
<name>closedRowCount</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696618</defParamId>
<array>false</array>
<paramDesc>行关闭数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>139</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411284</id>
<name>iDeleted</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696619</defParamId>
<array>false</array>
<paramDesc>是否删除</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>140</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411285</id>
<name>iOrgId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696620</defParamId>
<array>false</array>
<paramDesc>组织ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>141</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411286</id>
<name>memo</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696621</defParamId>
<array>false</array>
<paramDesc>备注</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>142</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411287</id>
<name>createDate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696622</defParamId>
<array>false</array>
<paramDesc>创建日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>143</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411288</id>
<name>creatorId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696623</defParamId>
<array>false</array>
<paramDesc>创建人</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>144</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411289</id>
<name>auditorId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696624</defParamId>
<array>false</array>
<paramDesc>审核人ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>145</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411290</id>
<name>auditDate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696625</defParamId>
<array>false</array>
<paramDesc>审批日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>146</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411291</id>
<name>closerId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696626</defParamId>
<array>false</array>
<paramDesc>关闭人ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>147</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411292</id>
<name>closeDate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696627</defParamId>
<array>false</array>
<paramDesc>关闭日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>148</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411293</id>
<name>modifierId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696628</defParamId>
<array>false</array>
<paramDesc>修改人id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>149</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411294</id>
<name>modifyDate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696629</defParamId>
<array>false</array>
<paramDesc>修改日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>150</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411295</id>
<name>cCreator</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696630</defParamId>
<array>false</array>
<paramDesc>创建人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>151</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411296</id>
<name>iProductAuxUnitId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696631</defParamId>
<array>false</array>
<paramDesc>销售单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>152</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411297</id>
<name>iProductUnitId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696632</defParamId>
<array>false</array>
<paramDesc>计价单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>153</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411298</id>
<name>masterUnitId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696633</defParamId>
<array>false</array>
<paramDesc>主计量单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>154</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411299</id>
<name>purUOM_Precision</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696634</defParamId>
<array>false</array>
<paramDesc>销售单位精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>155</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411300</id>
<name>priceUOM_Precision</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696635</defParamId>
<array>false</array>
<paramDesc>计价单位精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>156</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411301</id>
<name>unit_Precision</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696636</defParamId>
<array>false</array>
<paramDesc>主计量单位精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>157</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411302</id>
<name>cBizName</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696637</defParamId>
<array>false</array>
<paramDesc>供应商名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>158</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411303</id>
<name>orderDetailId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696638</defParamId>
<array>false</array>
<paramDesc>主体ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>159</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411304</id>
<name>orderId</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696639</defParamId>
<array>false</array>
<paramDesc>订单ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>160</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411143</id>
<name>bodyItem</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<children>
<children>
<id>2201709834977411144</id>
<name>id</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411143</parentId>
<defParamId>1795124488263696643</defParamId>
<array>false</array>
<paramDesc>表体自定义项id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411145</id>
<name>define1</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411143</parentId>
<defParamId>1795124488263696644</defParamId>
<array>false</array>
<paramDesc>表体自定义项1-60</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411146</id>
<name>define60</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411143</parentId>
<defParamId>1795124488263696645</defParamId>
<array>false</array>
<paramDesc>表体自定义项60</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1795124488263696642</defParamId>
<array>false</array>
<paramDesc>表体自定义项</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>163</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411307</id>
<name>creator</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696646</defParamId>
<array>false</array>
<paramDesc>创建人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>164</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411308</id>
<name>createTime</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696647</defParamId>
<array>false</array>
<paramDesc>创建时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>165</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411309</id>
<name>auditor</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696648</defParamId>
<array>false</array>
<paramDesc>审批人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>166</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411310</id>
<name>auditTime</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696649</defParamId>
<array>false</array>
<paramDesc>审批时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>167</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411311</id>
<name>closeTime</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696650</defParamId>
<array>false</array>
<paramDesc>关闭时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>168</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411312</id>
<name>closer</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696651</defParamId>
<array>false</array>
<paramDesc>关闭人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>169</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411313</id>
<name>modifier</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696652</defParamId>
<array>false</array>
<paramDesc>修改人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>170</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411314</id>
<name>modifyTime</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696653</defParamId>
<array>false</array>
<paramDesc>修改时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>171</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411315</id>
<name>bmake_st_salesout</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696654</defParamId>
<array>false</array>
<paramDesc>流程入库</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>172</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411316</id>
<name>bmake_voucher_delivery</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696655</defParamId>
<array>false</array>
<paramDesc>流程发货</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>173</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411317</id>
<name>bizFlow</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696656</defParamId>
<array>false</array>
<paramDesc>流程ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>174</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411318</id>
<name>bmake_voucher_saleinvoice</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696657</defParamId>
<array>false</array>
<paramDesc>流程开票</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>175</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411319</id>
<name>isFlowCoreBill</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696658</defParamId>
<array>false</array>
<paramDesc>是否流程核心单据</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>176</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411320</id>
<name>bizFlow_version</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696659</defParamId>
<array>false</array>
<paramDesc>版本信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>177</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411321</id>
<name>batchNo</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696660</defParamId>
<array>false</array>
<paramDesc>批次号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>178</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411322</id>
<name>productDate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696661</defParamId>
<array>false</array>
<paramDesc>生产日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>179</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411323</id>
<name>invalidDate</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1795124488263696662</defParamId>
<array>false</array>
<paramDesc>有效期至</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>180</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411324</id>
<name>isAdvRecInv</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1820299482027261960</defParamId>
<array>false</array>
<paramDesc>预收款开票</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>181</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411325</id>
<name>advRecInvMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1820299482027261961</defParamId>
<array>false</array>
<paramDesc>累计预收款开票金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>182</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411326</id>
<name>advRecInvQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1820299482027261962</defParamId>
<array>false</array>
<paramDesc>累计预收款开票数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>183</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411327</id>
<name>natAdvRecInvMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1820299482027261963</defParamId>
<array>false</array>
<paramDesc>累计本币预收款开票金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>184</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411328</id>
<name>advRecInvTaxMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1820299482027261964</defParamId>
<array>false</array>
<paramDesc>累计预收款开票税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>185</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411329</id>
<name>natAdvRecInvTaxMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1820299482027261965</defParamId>
<array>false</array>
<paramDesc>累计本币预收款开票税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>186</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411330</id>
<name>offsetAdvRecInvMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1820299482027261966</defParamId>
<array>false</array>
<paramDesc>累计冲抵预收款开票金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>187</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411331</id>
<name>offsetAdvRecInvQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1820299482027261967</defParamId>
<array>false</array>
<paramDesc>累计冲抵预收款开票数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>188</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411332</id>
<name>offsetNatAdvRecInvMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1820299482027261968</defParamId>
<array>false</array>
<paramDesc>累计冲抵本币预收款开票金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>189</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411333</id>
<name>offsetAdvRecInvTaxMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1820299482027261969</defParamId>
<array>false</array>
<paramDesc>累计冲抵预收款开票税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>190</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411334</id>
<name>offsetNatAdvRecInvTaxMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1820299482027261970</defParamId>
<array>false</array>
<paramDesc>累计冲抵本币预收款开票税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>191</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411335</id>
<name>tradeRouteID</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1852294265387876360</defParamId>
<array>false</array>
<paramDesc>贸易路径ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>192</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411336</id>
<name>tradeRouteID_code</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1852294265387876361</defParamId>
<array>false</array>
<paramDesc>贸易路径编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>193</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411337</id>
<name>tradeRouteID_name</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1852294265387876362</defParamId>
<array>false</array>
<paramDesc>贸易路径</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>194</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411338</id>
<name>isEndTrade</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1852294265387876363</defParamId>
<array>false</array>
<paramDesc>是否末级站点</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>195</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411339</id>
<name>tradeRouteLineno</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1852294265387876364</defParamId>
<array>false</array>
<paramDesc>站点</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>196</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411340</id>
<name>collaborationPocode</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1852294265387876365</defParamId>
<array>false</array>
<paramDesc>协同来源单据号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>197</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411341</id>
<name>collaborationPodetailid</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1852294265387876366</defParamId>
<array>false</array>
<paramDesc>协同来源单据子表id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>198</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411342</id>
<name>collaborationPoid</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1852294265387876367</defParamId>
<array>false</array>
<paramDesc>协同来源单据主表id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>199</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411343</id>
<name>collaborationPorowno</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1852294265387876368</defParamId>
<array>false</array>
<paramDesc>协同来源单据行号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>200</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411344</id>
<name>collaborationSource</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1852294265387876369</defParamId>
<array>false</array>
<paramDesc>协同来源单据类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>201</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411345</id>
<name>entireDeliveryStatus</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1975410507932237835</defParamId>
<array>false</array>
<paramDesc>整单发货状态</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>202</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>6</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411346</id>
<name>entireIssueStatus</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1975410507932237836</defParamId>
<array>false</array>
<paramDesc>整单出库状态</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>203</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>6</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411347</id>
<name>entireInvoiceStatus</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1975410507932237837</defParamId>
<array>false</array>
<paramDesc>整单发票状态</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>204</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>6</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411348</id>
<name>entireSignConfirmStatus</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1975410507932237838</defParamId>
<array>false</array>
<paramDesc>整单签收状态</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>205</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>6</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411349</id>
<name>deliveryStatus</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1975410507932237839</defParamId>
<array>false</array>
<paramDesc>发货状态</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>206</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>6</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411350</id>
<name>issueStatus</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1975410507932237840</defParamId>
<array>false</array>
<paramDesc>出库状态</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>207</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>6</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411351</id>
<name>invoiceStatus</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1975410507932237841</defParamId>
<array>false</array>
<paramDesc>发票状态</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>208</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>6</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411352</id>
<name>signConfirmStatus</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411082</parentId>
<defParamId>1975410507932237842</defParamId>
<array>false</array>
<paramDesc>签收状态</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>209</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>6</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1795124488263696437</defParamId>
<array>true</array>
<paramDesc>记录列表</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag>respData</paramTag>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411353</id>
<name>sumRecordList</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411081</parentId>
<children>
<children>
<id>2201709834977411365</id>
<name>noTaxSaleCost</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696664</defParamId>
<array>false</array>
<paramDesc>合计报价无税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411366</id>
<name>orderRealMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696665</defParamId>
<array>false</array>
<paramDesc>合计商品应付金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411367</id>
<name>realMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696666</defParamId>
<array>false</array>
<paramDesc>合计应收金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411368</id>
<name>orderPurchaseQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696667</defParamId>
<array>false</array>
<paramDesc>合计累计采购数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411369</id>
<name>sendQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696668</defParamId>
<array>false</array>
<paramDesc>合计累计已发货数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411370</id>
<name>subQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696669</defParamId>
<array>false</array>
<paramDesc>合计销售数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411371</id>
<name>collectMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696670</defParamId>
<array>false</array>
<paramDesc>合计累计收款金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411372</id>
<name>sendPriceQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696671</defParamId>
<array>false</array>
<paramDesc>合计累计已发货计价数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411373</id>
<name>coordinationQuantity</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696672</defParamId>
<array>false</array>
<paramDesc>合计社会化协同量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411374</id>
<name>totalOutStockOriTaxMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696673</defParamId>
<array>false</array>
<paramDesc>合计累计出库金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411375</id>
<name>prodCost</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696674</defParamId>
<array>false</array>
<paramDesc>合计商品报价金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411376</id>
<name>totalOutStockOriMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696675</defParamId>
<array>false</array>
<paramDesc>合计累计出库计价数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411377</id>
<name>totalOutStockPriceQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696676</defParamId>
<array>false</array>
<paramDesc>合计累计出库计价数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411378</id>
<name>orderPayMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696677</defParamId>
<array>false</array>
<paramDesc>合计商品实付金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411379</id>
<name>payMoneyOrigTaxfree</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696678</defParamId>
<array>false</array>
<paramDesc>合计合计无税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411380</id>
<name>auditCount</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696679</defParamId>
<array>false</array>
<paramDesc>合计累计发货已审数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411381</id>
<name>oriSum</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696680</defParamId>
<array>false</array>
<paramDesc>合计含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411382</id>
<name>returnQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696681</defParamId>
<array>false</array>
<paramDesc>合计退货数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411383</id>
<name>totalOutStockQuantity</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696682</defParamId>
<array>false</array>
<paramDesc>合计累计出库数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411384</id>
<name>saleCost</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696683</defParamId>
<array>false</array>
<paramDesc>合计报价含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411385</id>
<name>cashRebateMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696684</defParamId>
<array>false</array>
<paramDesc>合计返利直接抵现</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411386</id>
<name>rebateMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696685</defParamId>
<array>false</array>
<paramDesc>合计返利分摊金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411387</id>
<name>orderRebateMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696686</defParamId>
<array>false</array>
<paramDesc>合计返利整单折扣</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411388</id>
<name>orderDetails_rebateMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696687</defParamId>
<array>false</array>
<paramDesc>合计返利分摊金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411389</id>
<name>priceQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696688</defParamId>
<array>false</array>
<paramDesc>合计计价数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411390</id>
<name>qty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696689</defParamId>
<array>false</array>
<paramDesc>合计数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411391</id>
<name>sendPayMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696690</defParamId>
<array>false</array>
<paramDesc>合计发货金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411392</id>
<name>cusDiscountMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696691</defParamId>
<array>false</array>
<paramDesc>合计客户扣额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411393</id>
<name>invoiceQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696692</defParamId>
<array>false</array>
<paramDesc>合计累计开票数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411394</id>
<name>returnPreSendQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696693</defParamId>
<array>false</array>
<paramDesc>合计退货待发数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411395</id>
<name>totalOutStockSubQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1795124488263696694</defParamId>
<array>false</array>
<paramDesc>合计累计出库件数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411354</id>
<name>orderDetailPrices</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<children>
<children>
<id>2201709834977411355</id>
<name>oriTax</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411354</parentId>
<defParamId>1795124488263696696</defParamId>
<array>false</array>
<paramDesc>合计税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411356</id>
<name>natTax</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411354</parentId>
<defParamId>1795124488263696697</defParamId>
<array>false</array>
<paramDesc>合计本币税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411357</id>
<name>natSum</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411354</parentId>
<defParamId>1795124488263696698</defParamId>
<array>false</array>
<paramDesc>合计本币含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411358</id>
<name>natMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411354</parentId>
<defParamId>1795124488263696699</defParamId>
<array>false</array>
<paramDesc>合计本币无税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411359</id>
<name>oriMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411354</parentId>
<defParamId>1795124488263696700</defParamId>
<array>false</array>
<paramDesc>合计无税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411360</id>
<name>lineDiscountMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411354</parentId>
<defParamId>1795124488263696701</defParamId>
<array>false</array>
<paramDesc>合计扣额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411361</id>
<name>totalOutStockConfirmQuantity</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411354</parentId>
<defParamId>2201709826387476490</defParamId>
<array>false</array>
<paramDesc>累计出库确认数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411362</id>
<name>totalOutStockConfirmSubQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411354</parentId>
<defParamId>2201709826387476491</defParamId>
<array>false</array>
<paramDesc>累计出库确认件数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1795124488263696695</defParamId>
<array>false</array>
<paramDesc>订单详情金额</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411363</id>
<name>orderPrices</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<children>
<children>
<id>2201709834977411364</id>
<name>totalNatTax</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411363</parentId>
<defParamId>1795124488263696703</defParamId>
<array>false</array>
<paramDesc>合计本币总税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1795124488263696702</defParamId>
<array>false</array>
<paramDesc>订单金额</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411396</id>
<name>advRecInvMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1807048107764482060</defParamId>
<array>false</array>
<paramDesc>累计预收款开票金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411397</id>
<name>advRecInvQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1807048107764482061</defParamId>
<array>false</array>
<paramDesc>累计预收款开票数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411398</id>
<name>natAdvRecInvMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1807048107764482062</defParamId>
<array>false</array>
<paramDesc>累计预收款开票本币金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411399</id>
<name>advRecInvTaxMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1807048107764482063</defParamId>
<array>false</array>
<paramDesc>累计预收款开票税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411400</id>
<name>natAdvRecInvTaxMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1807048107764482064</defParamId>
<array>false</array>
<paramDesc>累计预收款开票本币税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411401</id>
<name>offsetAdvRecInvMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1807048107764482065</defParamId>
<array>false</array>
<paramDesc>累计冲抵预收款开票金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411402</id>
<name>offsetAdvRecInvQty</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1807048107764482066</defParamId>
<array>false</array>
<paramDesc>累计冲抵预收款开票数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411403</id>
<name>offsetNatAdvRecInvMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1807048107764482067</defParamId>
<array>false</array>
<paramDesc>累计冲抵预收款开票本币金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411404</id>
<name>offsetAdvRecInvTaxMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1807048107764482068</defParamId>
<array>false</array>
<paramDesc>累计冲抵预收款开票税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411405</id>
<name>offsetNatAdvRecInvTaxMoney</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411353</parentId>
<defParamId>1807048107764482069</defParamId>
<array>false</array>
<paramDesc>累计冲抵预收款开票本币税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1795124488263696663</defParamId>
<array>true</array>
<paramDesc>合计</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411409</id>
<name>pageCount</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411081</parentId>
<defParamId>1795124488263696704</defParamId>
<array>false</array>
<paramDesc>总共记录数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411410</id>
<name>beginPageIndex</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411081</parentId>
<defParamId>1795124488263696705</defParamId>
<array>false</array>
<paramDesc>页码列表的开始索引</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2201709834977411411</id>
<name>endPageIndex</name>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<parentId>2201709834977411081</parentId>
<defParamId>1795124488263696706</defParamId>
<array>false</array>
<paramDesc>页码列表的结束索引</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1795124488263696433</defParamId>
<array>false</array>
<paramDesc>调用成功时的返回数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>*************345664</id>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 1, "pageSize": 1, "recordCount": 276, "recordList": [ { "code": "", "vouchdate": "", "id": 0, "parentOrderNo": "", "salesOrgId": "", "salesOrgId_name": "", "saleDepartmentId": "", "transactionTypeId": "", "transactionTypeId_name": "", "agentId": 0, "agentId_name": "", "receiveContacter": "", "receiveContacterPhone": "", "receievInvoiceMobile": "", "receievInvoiceEmail": "", "purchaseNo": "", "corpContact": "", "corpContactUserName": "", "settlementOrgId_name": "", "corpContactUserErpCode": "", "orderPrices": { "currency": "", "currency_priceDigit": 0, "currency_moneyDigit": 0, "originalName": "", "natCurrency": "", "natCurrency_priceDigit": 0, "natCurrency_moneyDigit": 0, "domesticCode": "", "domesticName": "", "exchRate": 0, "exchangeRateType_name": "", "exchangeRateType": "", "ctTplId": "", "ctTplCode": "", "ctTplName": "", "signFileId": "", "signStatus": "" }, "payMoney": 0, "orderPayMoney": 0, "realMoney": 0, "orderRealMoney": 0, "statusCode": "", "nextStatus": "", "currentStatus": 0, "payStatusCode": "", "settlementOrgId": "", "lockIn": true, "confirmDate": "", "payDate": "", "orderPayType": "", "settlement": "", "shippingChoiceId": "", "sendDate": "", "hopeReceiveDate": "", "opposeMemo": "", "haveDelivery": true, "occupyInventory": "", "separatePromotionType": "", "reight": 0, "synSourceOrg": "", "synSourceTenant": "", "synSourceOrg_name": "", "totalMoney": 0, "tagName": "", "rebateCashMoney": 0, "particularlyMoney": 0, "promotionMoney": 0, "unConfirmPrice": 0, "confirmPrice": 0, "bizId": "", "bizName": "", "agentRelationId": 0, "points": "", "pubts": "", "pubuts": "", "orderInvoice": "", "orderShippingAddress": "", "orderErp": "", "deliveryDate": "", "isWfControlled": true, "verifystate": 0, "status": 0, "orderDefineCharacter": {}, "headItem": { "id": "", "define1": "" }, "isFinishDelivery": true, "productId_pbatchName": 0, "idKey": "", "productId": 0, "priceMark": true, "isBatchManage": true, "isExpiryDateManage": true, "expireDateNo": "", "expireDateUnit": "", "skuId": 0, "erpCode": "", "orderProductType": "", "productCode": "", "productName": "", "skuCode": "", "specDescription": "", "projectId": "", "unitExchangeType": 0, "unitExchangeTypePrice": 0, "productAuxUnitName": "", "invExchRate": 0, "subQty": 0, "productUnitName": "", "invPriceExchRate": 0, "priceQty": 0, "qtyName": "", "qty": 0, "variantconfigctsId": 5235234243, "orderDetailPrices": { "saleCost_orig_taxfree": 0, "oriUnitPrice": 0, "oriMoney": 0, "oriTax": 0, "natSum": 0, "natTaxUnitPrice": 0, "natMoney": 0, "natUnitPrice": 0, "natTax": 0, "orderDetailId": 0, "salePrice_orig_taxfree": 0, "rebateMoneyOrigTaxfree": 0, "particularlyMoneyOrigTaxfree": 0, "promotionMoneyOrigTaxfree": 0, "pointsMoneyOrigTaxfree": 0, "saleCost_domestic": 0, "salePrice_domestic": 0, "rebateMoneyDomestic": 0, "particularlyMoneyDomestic": 0, "promotionMoneyDomestic": 0, "pointsMoneyDomestic": 0, "saleCost_domestic_taxfree": 0, "salePrice_domestic_taxfree": 0, "rebateMoneyDomesticTaxfree": 0, "particularlyMoneyDomesticTaxfree": 0, "promotionMoneyDomesticTaxfree": 0, "pointsMoneyDomesticTaxfree": 0, "id": 0, "prepayInvRvnRecogBkgMeth": "2", "checkByRevenueManagement": "", "revPerformObligation": "", "serviceStartDate": "", "serviceEndDate": "", "optionalQuotationId": 4323234234, "optionalQuotationId_code": "A123123", "variantconfigctsCode": "B234234", "variantconfigctsVersion": "1.0", "calBase": "0" }, "stockName": "", "stockOrgId_name": "", "consignTime": "", "projectId_name": "", "projectId_code": "", "noTaxSalePrice": 0, "salePrice": 0, "taxId": "", "costCurrencyName": "", "costAmt": 0, "costPrice": 0, "noTaxSaleCost": 0, "saleCost": 0, "stockId": "", "oriTaxUnitPrice": 0, "lineno": 0, "orderDetails_stockOrgId": "", "oriSum": 0, "taxRate": 0, "taxItems": "", "taxCode": "", "rebateMoney": 0, "pointsMoney": 0, "shoppingCartId": "", "groupId": 0, "rebateReturnProductId": "", "mutualActivities": "", "activities": "", "sendPayMoney": 0, "invoiceQty": 0, "invoiceOriSum": 0, "bizProductId": 0, "takeQuantity": 0, "bizSkuId": "", "takeSalePayMoney": 0, "orderDetailPrice": 0, "sendQty": 0, "closedSumMoney": 0, "closedRowCount": 0, "iDeleted": true, "iOrgId": "", "memo": "", "createDate": "", "creatorId": 0, "auditorId": "", "auditDate": "", "closerId": "", "closeDate": "", "modifierId": "", "modifyDate": "", "cCreator": "", "iProductAuxUnitId": 0, "iProductUnitId": 0, "masterUnitId": 0, "purUOM_Precision": 0, "priceUOM_Precision": 0, "unit_Precision": 0, "cBizName": "", "orderDetailId": 0, "orderId": 0, "orderDetailCharacteristics": {}, "orderDetailDefineCharacter": {}, "bodyItem": { "id": 0, "define1": "", "define60": "" }, "creator": "", "createTime": "", "auditor": "", "auditTime": "", "closeTime": "", "closer": "", "modifier": "", "modifyTime": "", "bmake_st_salesout": "", "bmake_voucher_delivery": "", "bizFlow": 0, "bmake_voucher_saleinvoice": "", "isFlowCoreBill": true, "bizFlow_version": "", "batchNo": "", "productDate": "", "invalidDate": "", "isAdvRecInv": true, "advRecInvMoney": 0, "advRecInvQty": 0, "natAdvRecInvMoney": 0, "advRecInvTaxMoney": 0, "natAdvRecInvTaxMoney": 0, "offsetAdvRecInvMoney": 0, "offsetAdvRecInvQty": 0, "offsetNatAdvRecInvMoney": 0, "offsetAdvRecInvTaxMoney": 0, "offsetNatAdvRecInvTaxMoney": 0, "tradeRouteID": "", "tradeRouteID_code": "", "tradeRouteID_name": "", "isEndTrade": 0, "tradeRouteLineno": "", "collaborationPocode": "", "collaborationPodetailid": "", "collaborationPoid": "", "collaborationPorowno": "", "collaborationSource": "", "entireDeliveryStatus": 0, "entireIssueStatus": 0, "entireInvoiceStatus": 0, "entireSignConfirmStatus": 0, "deliveryStatus": 0, "issueStatus": 0, "invoiceStatus": 0, "signConfirmStatus": 0 } ], "sumRecordList": [ { "noTaxSaleCost": 0, "orderRealMoney": 0, "realMoney": 0, "orderPurchaseQty": 0, "sendQty": 0, "subQty": 0, "collectMoney": 0, "sendPriceQty": 0, "coordinationQuantity": 0, "totalOutStockOriTaxMoney": 0, "prodCost": 0, "totalOutStockOriMoney": 0, "totalOutStockPriceQty": 0, "orderPayMoney": 0, "payMoneyOrigTaxfree": 0, "auditCount": 0, "oriSum": 0, "returnQty": 0, "totalOutStockQuantity": 0, "saleCost": 0, "cashRebateMoney": 0, "rebateMoney": 0, "orderRebateMoney": 0, "orderDetails_rebateMoney": 0, "priceQty": 0, "qty": 0, "sendPayMoney": 0, "cusDiscountMoney": 0, "invoiceQty": 0, "returnPreSendQty": 0, "totalOutStockSubQty": 0, "orderDetailPrices": { "oriTax": 0, "natTax": 0, "natSum": 0, "natMoney": 0, "oriMoney": 0, "lineDiscountMoney": 0 }, "orderPrices": { "totalNatTax": 0 }, "advRecInvMoney": 0, "advRecInvQty": 0, "natAdvRecInvMoney": 0, "advRecInvTaxMoney": 0, "natAdvRecInvTaxMoney": 0, "offsetAdvRecInvMoney": 0, "offsetAdvRecInvQty": 0, "offsetNatAdvRecInvMoney": 0, "offsetAdvRecInvTaxMoney": 0, "offsetNatAdvRecInvTaxMoney": 0 } ], "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0 } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-02-14 13:59:20.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:20.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>*************345665</id>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<content>{ "code": 999, "message": "服务端逻辑异常" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-02-14 13:59:20.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:20.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>*************345664</id>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 1, "pageSize": 1, "recordCount": 276, "recordList": [ { "code": "", "vouchdate": "", "id": 0, "parentOrderNo": "", "salesOrgId": "", "salesOrgId_name": "", "saleDepartmentId": "", "transactionTypeId": "", "transactionTypeId_name": "", "agentId": 0, "agentId_name": "", "receiveContacter": "", "receiveContacterPhone": "", "receievInvoiceMobile": "", "receievInvoiceEmail": "", "purchaseNo": "", "corpContact": "", "corpContactUserName": "", "settlementOrgId_name": "", "corpContactUserErpCode": "", "orderPrices": { "currency": "", "currency_priceDigit": 0, "currency_moneyDigit": 0, "originalName": "", "natCurrency": "", "natCurrency_priceDigit": 0, "natCurrency_moneyDigit": 0, "domesticCode": "", "domesticName": "", "exchRate": 0, "exchangeRateType_name": "", "exchangeRateType": "", "ctTplId": "", "ctTplCode": "", "ctTplName": "", "signFileId": "", "signStatus": "" }, "payMoney": 0, "orderPayMoney": 0, "realMoney": 0, "orderRealMoney": 0, "statusCode": "", "nextStatus": "", "currentStatus": 0, "payStatusCode": "", "settlementOrgId": "", "lockIn": true, "confirmDate": "", "payDate": "", "orderPayType": "", "settlement": "", "shippingChoiceId": "", "sendDate": "", "hopeReceiveDate": "", "opposeMemo": "", "haveDelivery": true, "occupyInventory": "", "separatePromotionType": "", "reight": 0, "synSourceOrg": "", "synSourceTenant": "", "synSourceOrg_name": "", "totalMoney": 0, "tagName": "", "rebateCashMoney": 0, "particularlyMoney": 0, "promotionMoney": 0, "unConfirmPrice": 0, "confirmPrice": 0, "bizId": "", "bizName": "", "agentRelationId": 0, "points": "", "pubts": "", "pubuts": "", "orderInvoice": "", "orderShippingAddress": "", "orderErp": "", "deliveryDate": "", "isWfControlled": true, "verifystate": 0, "status": 0, "orderDefineCharacter": {}, "headItem": { "id": "", "define1": "" }, "isFinishDelivery": true, "productId_pbatchName": 0, "idKey": "", "productId": 0, "priceMark": true, "isBatchManage": true, "isExpiryDateManage": true, "expireDateNo": "", "expireDateUnit": "", "skuId": 0, "erpCode": "", "orderProductType": "", "productCode": "", "productName": "", "skuCode": "", "specDescription": "", "projectId": "", "unitExchangeType": 0, "unitExchangeTypePrice": 0, "productAuxUnitName": "", "invExchRate": 0, "subQty": 0, "productUnitName": "", "invPriceExchRate": 0, "priceQty": 0, "qtyName": "", "qty": 0, "variantconfigctsId": 5235234243, "orderDetailPrices": { "saleCost_orig_taxfree": 0, "oriUnitPrice": 0, "oriMoney": 0, "oriTax": 0, "natSum": 0, "natTaxUnitPrice": 0, "natMoney": 0, "natUnitPrice": 0, "natTax": 0, "orderDetailId": 0, "salePrice_orig_taxfree": 0, "rebateMoneyOrigTaxfree": 0, "particularlyMoneyOrigTaxfree": 0, "promotionMoneyOrigTaxfree": 0, "pointsMoneyOrigTaxfree": 0, "saleCost_domestic": 0, "salePrice_domestic": 0, "rebateMoneyDomestic": 0, "particularlyMoneyDomestic": 0, "promotionMoneyDomestic": 0, "pointsMoneyDomestic": 0, "saleCost_domestic_taxfree": 0, "salePrice_domestic_taxfree": 0, "rebateMoneyDomesticTaxfree": 0, "particularlyMoneyDomesticTaxfree": 0, "promotionMoneyDomesticTaxfree": 0, "pointsMoneyDomesticTaxfree": 0, "id": 0, "prepayInvRvnRecogBkgMeth": "2", "checkByRevenueManagement": "", "revPerformObligation": "", "serviceStartDate": "", "serviceEndDate": "", "optionalQuotationId": 4323234234, "optionalQuotationId_code": "A123123", "variantconfigctsCode": "B234234", "variantconfigctsVersion": "1.0", "calBase": "0" }, "stockName": "", "stockOrgId_name": "", "consignTime": "", "projectId_name": "", "projectId_code": "", "noTaxSalePrice": 0, "salePrice": 0, "taxId": "", "costCurrencyName": "", "costAmt": 0, "costPrice": 0, "noTaxSaleCost": 0, "saleCost": 0, "stockId": "", "oriTaxUnitPrice": 0, "lineno": 0, "orderDetails_stockOrgId": "", "oriSum": 0, "taxRate": 0, "taxItems": "", "taxCode": "", "rebateMoney": 0, "pointsMoney": 0, "shoppingCartId": "", "groupId": 0, "rebateReturnProductId": "", "mutualActivities": "", "activities": "", "sendPayMoney": 0, "invoiceQty": 0, "invoiceOriSum": 0, "bizProductId": 0, "takeQuantity": 0, "bizSkuId": "", "takeSalePayMoney": 0, "orderDetailPrice": 0, "sendQty": 0, "closedSumMoney": 0, "closedRowCount": 0, "iDeleted": true, "iOrgId": "", "memo": "", "createDate": "", "creatorId": 0, "auditorId": "", "auditDate": "", "closerId": "", "closeDate": "", "modifierId": "", "modifyDate": "", "cCreator": "", "iProductAuxUnitId": 0, "iProductUnitId": 0, "masterUnitId": 0, "purUOM_Precision": 0, "priceUOM_Precision": 0, "unit_Precision": 0, "cBizName": "", "orderDetailId": 0, "orderId": 0, "orderDetailCharacteristics": {}, "orderDetailDefineCharacter": {}, "bodyItem": { "id": 0, "define1": "", "define60": "" }, "creator": "", "createTime": "", "auditor": "", "auditTime": "", "closeTime": "", "closer": "", "modifier": "", "modifyTime": "", "bmake_st_salesout": "", "bmake_voucher_delivery": "", "bizFlow": 0, "bmake_voucher_saleinvoice": "", "isFlowCoreBill": true, "bizFlow_version": "", "batchNo": "", "productDate": "", "invalidDate": "", "isAdvRecInv": true, "advRecInvMoney": 0, "advRecInvQty": 0, "natAdvRecInvMoney": 0, "advRecInvTaxMoney": 0, "natAdvRecInvTaxMoney": 0, "offsetAdvRecInvMoney": 0, "offsetAdvRecInvQty": 0, "offsetNatAdvRecInvMoney": 0, "offsetAdvRecInvTaxMoney": 0, "offsetNatAdvRecInvTaxMoney": 0, "tradeRouteID": "", "tradeRouteID_code": "", "tradeRouteID_name": "", "isEndTrade": 0, "tradeRouteLineno": "", "collaborationPocode": "", "collaborationPodetailid": "", "collaborationPoid": "", "collaborationPorowno": "", "collaborationSource": "", "entireDeliveryStatus": 0, "entireIssueStatus": 0, "entireInvoiceStatus": 0, "entireSignConfirmStatus": 0, "deliveryStatus": 0, "issueStatus": 0, "invoiceStatus": 0, "signConfirmStatus": 0 } ], "sumRecordList": [ { "noTaxSaleCost": 0, "orderRealMoney": 0, "realMoney": 0, "orderPurchaseQty": 0, "sendQty": 0, "subQty": 0, "collectMoney": 0, "sendPriceQty": 0, "coordinationQuantity": 0, "totalOutStockOriTaxMoney": 0, "prodCost": 0, "totalOutStockOriMoney": 0, "totalOutStockPriceQty": 0, "orderPayMoney": 0, "payMoneyOrigTaxfree": 0, "auditCount": 0, "oriSum": 0, "returnQty": 0, "totalOutStockQuantity": 0, "saleCost": 0, "cashRebateMoney": 0, "rebateMoney": 0, "orderRebateMoney": 0, "orderDetails_rebateMoney": 0, "priceQty": 0, "qty": 0, "sendPayMoney": 0, "cusDiscountMoney": 0, "invoiceQty": 0, "returnPreSendQty": 0, "totalOutStockSubQty": 0, "orderDetailPrices": { "oriTax": 0, "natTax": 0, "natSum": 0, "natMoney": 0, "oriMoney": 0, "lineDiscountMoney": 0 }, "orderPrices": { "totalNatTax": 0 }, "advRecInvMoney": 0, "advRecInvQty": 0, "natAdvRecInvMoney": 0, "advRecInvTaxMoney": 0, "natAdvRecInvTaxMoney": 0, "offsetAdvRecInvMoney": 0, "offsetAdvRecInvQty": 0, "offsetNatAdvRecInvMoney": 0, "offsetAdvRecInvTaxMoney": 0, "offsetNatAdvRecInvTaxMoney": 0 } ], "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0 } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-02-14 13:59:20.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:20.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>*************345665</id>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<content>{ "code": 999, "message": "服务端逻辑异常" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-02-14 13:59:20.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:20.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>*************345664</id>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 1, "pageSize": 1, "recordCount": 276, "recordList": [ { "code": "", "vouchdate": "", "id": 0, "parentOrderNo": "", "salesOrgId": "", "salesOrgId_name": "", "saleDepartmentId": "", "transactionTypeId": "", "transactionTypeId_name": "", "agentId": 0, "agentId_name": "", "receiveContacter": "", "receiveContacterPhone": "", "receievInvoiceMobile": "", "receievInvoiceEmail": "", "purchaseNo": "", "corpContact": "", "corpContactUserName": "", "settlementOrgId_name": "", "corpContactUserErpCode": "", "orderPrices": { "currency": "", "currency_priceDigit": 0, "currency_moneyDigit": 0, "originalName": "", "natCurrency": "", "natCurrency_priceDigit": 0, "natCurrency_moneyDigit": 0, "domesticCode": "", "domesticName": "", "exchRate": 0, "exchangeRateType_name": "", "exchangeRateType": "", "ctTplId": "", "ctTplCode": "", "ctTplName": "", "signFileId": "", "signStatus": "" }, "payMoney": 0, "orderPayMoney": 0, "realMoney": 0, "orderRealMoney": 0, "statusCode": "", "nextStatus": "", "currentStatus": 0, "payStatusCode": "", "settlementOrgId": "", "lockIn": true, "confirmDate": "", "payDate": "", "orderPayType": "", "settlement": "", "shippingChoiceId": "", "sendDate": "", "hopeReceiveDate": "", "opposeMemo": "", "haveDelivery": true, "occupyInventory": "", "separatePromotionType": "", "reight": 0, "synSourceOrg": "", "synSourceTenant": "", "synSourceOrg_name": "", "totalMoney": 0, "tagName": "", "rebateCashMoney": 0, "particularlyMoney": 0, "promotionMoney": 0, "unConfirmPrice": 0, "confirmPrice": 0, "bizId": "", "bizName": "", "agentRelationId": 0, "points": "", "pubts": "", "pubuts": "", "orderInvoice": "", "orderShippingAddress": "", "orderErp": "", "deliveryDate": "", "isWfControlled": true, "verifystate": 0, "status": 0, "orderDefineCharacter": {}, "headItem": { "id": "", "define1": "" }, "isFinishDelivery": true, "productId_pbatchName": 0, "idKey": "", "productId": 0, "priceMark": true, "isBatchManage": true, "isExpiryDateManage": true, "expireDateNo": "", "expireDateUnit": "", "skuId": 0, "erpCode": "", "orderProductType": "", "productCode": "", "productName": "", "skuCode": "", "specDescription": "", "projectId": "", "unitExchangeType": 0, "unitExchangeTypePrice": 0, "productAuxUnitName": "", "invExchRate": 0, "subQty": 0, "productUnitName": "", "invPriceExchRate": 0, "priceQty": 0, "qtyName": "", "qty": 0, "variantconfigctsId": 5235234243, "orderDetailPrices": { "saleCost_orig_taxfree": 0, "oriUnitPrice": 0, "oriMoney": 0, "oriTax": 0, "natSum": 0, "natTaxUnitPrice": 0, "natMoney": 0, "natUnitPrice": 0, "natTax": 0, "orderDetailId": 0, "salePrice_orig_taxfree": 0, "rebateMoneyOrigTaxfree": 0, "particularlyMoneyOrigTaxfree": 0, "promotionMoneyOrigTaxfree": 0, "pointsMoneyOrigTaxfree": 0, "saleCost_domestic": 0, "salePrice_domestic": 0, "rebateMoneyDomestic": 0, "particularlyMoneyDomestic": 0, "promotionMoneyDomestic": 0, "pointsMoneyDomestic": 0, "saleCost_domestic_taxfree": 0, "salePrice_domestic_taxfree": 0, "rebateMoneyDomesticTaxfree": 0, "particularlyMoneyDomesticTaxfree": 0, "promotionMoneyDomesticTaxfree": 0, "pointsMoneyDomesticTaxfree": 0, "id": 0, "prepayInvRvnRecogBkgMeth": "2", "checkByRevenueManagement": "", "revPerformObligation": "", "serviceStartDate": "", "serviceEndDate": "", "optionalQuotationId": 4323234234, "optionalQuotationId_code": "A123123", "variantconfigctsCode": "B234234", "variantconfigctsVersion": "1.0", "calBase": "0" }, "stockName": "", "stockOrgId_name": "", "consignTime": "", "projectId_name": "", "projectId_code": "", "noTaxSalePrice": 0, "salePrice": 0, "taxId": "", "costCurrencyName": "", "costAmt": 0, "costPrice": 0, "noTaxSaleCost": 0, "saleCost": 0, "stockId": "", "oriTaxUnitPrice": 0, "lineno": 0, "orderDetails_stockOrgId": "", "oriSum": 0, "taxRate": 0, "taxItems": "", "taxCode": "", "rebateMoney": 0, "pointsMoney": 0, "shoppingCartId": "", "groupId": 0, "rebateReturnProductId": "", "mutualActivities": "", "activities": "", "sendPayMoney": 0, "invoiceQty": 0, "invoiceOriSum": 0, "bizProductId": 0, "takeQuantity": 0, "bizSkuId": "", "takeSalePayMoney": 0, "orderDetailPrice": 0, "sendQty": 0, "closedSumMoney": 0, "closedRowCount": 0, "iDeleted": true, "iOrgId": "", "memo": "", "createDate": "", "creatorId": 0, "auditorId": "", "auditDate": "", "closerId": "", "closeDate": "", "modifierId": "", "modifyDate": "", "cCreator": "", "iProductAuxUnitId": 0, "iProductUnitId": 0, "masterUnitId": 0, "purUOM_Precision": 0, "priceUOM_Precision": 0, "unit_Precision": 0, "cBizName": "", "orderDetailId": 0, "orderId": 0, "orderDetailCharacteristics": {}, "orderDetailDefineCharacter": {}, "bodyItem": { "id": 0, "define1": "", "define60": "" }, "creator": "", "createTime": "", "auditor": "", "auditTime": "", "closeTime": "", "closer": "", "modifier": "", "modifyTime": "", "bmake_st_salesout": "", "bmake_voucher_delivery": "", "bizFlow": 0, "bmake_voucher_saleinvoice": "", "isFlowCoreBill": true, "bizFlow_version": "", "batchNo": "", "productDate": "", "invalidDate": "", "isAdvRecInv": true, "advRecInvMoney": 0, "advRecInvQty": 0, "natAdvRecInvMoney": 0, "advRecInvTaxMoney": 0, "natAdvRecInvTaxMoney": 0, "offsetAdvRecInvMoney": 0, "offsetAdvRecInvQty": 0, "offsetNatAdvRecInvMoney": 0, "offsetAdvRecInvTaxMoney": 0, "offsetNatAdvRecInvTaxMoney": 0, "tradeRouteID": "", "tradeRouteID_code": "", "tradeRouteID_name": "", "isEndTrade": 0, "tradeRouteLineno": "", "collaborationPocode": "", "collaborationPodetailid": "", "collaborationPoid": "", "collaborationPorowno": "", "collaborationSource": "", "entireDeliveryStatus": 0, "entireIssueStatus": 0, "entireInvoiceStatus": 0, "entireSignConfirmStatus": 0, "deliveryStatus": 0, "issueStatus": 0, "invoiceStatus": 0, "signConfirmStatus": 0 } ], "sumRecordList": [ { "noTaxSaleCost": 0, "orderRealMoney": 0, "realMoney": 0, "orderPurchaseQty": 0, "sendQty": 0, "subQty": 0, "collectMoney": 0, "sendPriceQty": 0, "coordinationQuantity": 0, "totalOutStockOriTaxMoney": 0, "prodCost": 0, "totalOutStockOriMoney": 0, "totalOutStockPriceQty": 0, "orderPayMoney": 0, "payMoneyOrigTaxfree": 0, "auditCount": 0, "oriSum": 0, "returnQty": 0, "totalOutStockQuantity": 0, "saleCost": 0, "cashRebateMoney": 0, "rebateMoney": 0, "orderRebateMoney": 0, "orderDetails_rebateMoney": 0, "priceQty": 0, "qty": 0, "sendPayMoney": 0, "cusDiscountMoney": 0, "invoiceQty": 0, "returnPreSendQty": 0, "totalOutStockSubQty": 0, "orderDetailPrices": { "oriTax": 0, "natTax": 0, "natSum": 0, "natMoney": 0, "oriMoney": 0, "lineDiscountMoney": 0 }, "orderPrices": { "totalNatTax": 0 }, "advRecInvMoney": 0, "advRecInvQty": 0, "natAdvRecInvMoney": 0, "advRecInvTaxMoney": 0, "natAdvRecInvTaxMoney": 0, "offsetAdvRecInvMoney": 0, "offsetAdvRecInvQty": 0, "offsetNatAdvRecInvMoney": 0, "offsetAdvRecInvTaxMoney": 0, "offsetNatAdvRecInvTaxMoney": 0 } ], "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0 } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-02-14 13:59:20.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:20.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>*************345665</id>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<content>{ "code": 999, "message": "服务端逻辑异常" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-02-14 13:59:20.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:20.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS>
<errorCodeDTOS>
<id>2201709834977411417</id>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<errorCode>999</errorCode>
<errorMessage>服务端逻辑异常</errorMessage>
<errorType>API</errorType>
<errorcodeDesc>请检查传入数据的正确性</errorcodeDesc>
<gmtCreate>2025-02-14 13:59:19.000</gmtCreate>
<gmtUpdate>2025-02-14 13:59:19.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<defErrorId>1795124488263696983</defErrorId>
<ytenantId/>
<displayCodeId/>
</errorCodeDTOS>
</errorCodeDTOS>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>友户通token认证业务扩展插件</name>
<configurable>false</configurable>
<description>YonsuitBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>12406993fdf24559969621f65e25b53f</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin>
<id>w181ed01-1e9b-4350-b994-71a66f017555</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code>resultParse</code>
<name>返回参数转换插件</name>
<configurable>false</configurable>
<description>解决返回值中带！的，转换为json</description>
<pluginType>resultParse</pluginType>
<pluginTypeName>返回值解析插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.result.ResultMapTransferParsePlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>true</visible>
<gmtCreate>2020-07-29 00:00:00</gmtCreate>
<gmtUpdate/>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>12406993fdf24559969621f65e25b53f</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</resultParsePlugin>
<mapReturnPluginConfig/>
<billNo>voucher_orderlist</billNo>
<domain>udinghuo</domain>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser/>
<createUserName/>
<approvalStatus>1</approvalStatus>
<publishTime>2025-02-18 14:13:14</publishTime>
<pathJoin>true</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout/>
<customUrl>/voucherorder/list</customUrl>
<fixedUrl>/yonbip/sd</fixedUrl>
<apiCode>12406993fdf24559969621f65e25b53f</apiCode>
<tokenCheckType>0</tokenCheckType>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL>false</bidirectionalSSL>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>10b8cef0-0433-423d-aba0-68ff09d6617b</updateUserId>
<updateUserName>zy</updateUserName>
<paramIsForce/>
<userIDPassthrough>true</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.yonbip-scm-scmsa</microServiceCode>
<applicationCode>yonbip-scm-scmsa</applicationCode>
<privacyCategory>1</privacyCategory>
<privacyLevel>1</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType>0</serviceType>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType/>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>false</arrayParam>
<fileSize/>
<cc>true</cc>
<paramTransferMode>1</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId>1795124488263696390</apiDefId>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>1</paramExtRequest>
<paramExtResponse>1</paramExtResponse>
<paramExtInExtendKey>1</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene>0</integrationScene>
<apiType>3</apiType>
<paramMark>
<request>
<request>
<id>*************345666</id>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<paramCode>pageIndex</paramCode>
<refCode>pageIndex</refCode>
<paramPosition>request</paramPosition>
<ytenantId>0</ytenantId>
<creator>10b8cef0-0433-423d-aba0-68ff09d6617b</creator>
<createTime>2025-02-14T05:59:20.000+00:00</createTime>
<modifier>00001951-7ca3-47ac-a462-d5a66e3e6724</modifier>
<pubts>2025-02-14T05:59:20.000+00:00</pubts>
<modifyTime>2025-02-14T05:59:20.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</request>
<request>
<id>*************345667</id>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<paramCode>pageSize</paramCode>
<refCode>pageSize</refCode>
<paramPosition>request</paramPosition>
<ytenantId>0</ytenantId>
<creator>10b8cef0-0433-423d-aba0-68ff09d6617b</creator>
<createTime>2025-02-14T05:59:20.000+00:00</createTime>
<modifier>00001951-7ca3-47ac-a462-d5a66e3e6724</modifier>
<pubts>2025-02-14T05:59:20.000+00:00</pubts>
<modifyTime>2025-02-14T05:59:20.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</request>
<request>
<id>*************345668</id>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<paramCode>lastUpdateTime</paramCode>
<refCode/>
<paramPosition>request</paramPosition>
<ytenantId>0</ytenantId>
<creator>10b8cef0-0433-423d-aba0-68ff09d6617b</creator>
<createTime>2025-02-14T05:59:20.000+00:00</createTime>
<modifier>00001951-7ca3-47ac-a462-d5a66e3e6724</modifier>
<pubts>2025-02-14T05:59:20.000+00:00</pubts>
<modifyTime>2025-02-14T05:59:20.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</request>
<request>
<id>*************345669</id>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<paramCode>thisSyncTime</paramCode>
<refCode/>
<paramPosition>request</paramPosition>
<ytenantId>0</ytenantId>
<creator>10b8cef0-0433-423d-aba0-68ff09d6617b</creator>
<createTime>2025-02-14T05:59:20.000+00:00</createTime>
<modifier>00001951-7ca3-47ac-a462-d5a66e3e6724</modifier>
<pubts>2025-02-14T05:59:20.000+00:00</pubts>
<modifyTime>2025-02-14T05:59:20.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</request>
</request>
<response>
<response>
<id>*************345670</id>
<apiId>12406993fdf24559969621f65e25b53f</apiId>
<paramCode>respData</paramCode>
<refCode>data.recordList</refCode>
<paramPosition>response</paramPosition>
<ytenantId>0</ytenantId>
<creator>10b8cef0-0433-423d-aba0-68ff09d6617b</creator>
<createTime>2025-02-14T05:59:20.000+00:00</createTime>
<modifier>00001951-7ca3-47ac-a462-d5a66e3e6724</modifier>
<pubts>2025-02-14T05:59:20.000+00:00</pubts>
<modifyTime>2025-02-14T05:59:20.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</response>
</response>
</paramMark>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>true</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>true</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>2</chargeStatus>
<beforeSpeed/>
<afterSpeed/>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath>data.recordList</respDataRefPath>
<pubHistory/>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode/>
<multiVersion>0</multiVersion>
<apiTag/>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>ae625f9d-f5fe-4c4a-9ef6-6c1683ebf4bc</id>
<name>A086</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>账款到期日</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>date</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>e53672ae-d2be-42df-9362-2aaeb191232e</id>
<name>AE86</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>销售订单收款协议</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>22ea53ec-6ee7-4e79-b3d0-002913a3c8eb</id>
<name>SF0047</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>总体积m³</paramDesc>
<paramType>Decimal</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>number</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>24</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>58798163-451a-4fab-8913-f4f09ebb9fcc</id>
<name>U9004</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>U9销售订单号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>100</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>d8b8b7f9-b8a3-48fe-aa7a-875c83865db4</id>
<name>XS01</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>成交方式</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName>bd.customerdoc_ITrade.ITrade</fullName>
<ytenantId/>
<paramOrder/>
<bizType>quote</bizType>
<baseType>false</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>true</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>2eb80f4f-a241-4954-b6ce-99e2a64221e7</id>
<name>XS017</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>商标授权</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>switch</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>312bc313-961d-4643-a2c3-12a18b6d8a71</id>
<name>XS02</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>装运港</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName>bd.customerdoc_port.port</fullName>
<ytenantId/>
<paramOrder/>
<bizType>quote</bizType>
<baseType>false</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>true</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>43efe170-a590-4a55-80a2-7d055967b9e4</id>
<name>XS03</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>目的港</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName>bd.customerdoc_port.port</fullName>
<ytenantId/>
<paramOrder/>
<bizType>quote</bizType>
<baseType>false</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>true</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>622f3202-2c18-4b08-95de-919e4795b247</id>
<name>XS04</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>溢短装</paramDesc>
<paramType>Decimal</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>number</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>24</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>80376695-ae66-4043-a7de-a7fa7a21d530</id>
<name>XS05</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>投保比例</paramDesc>
<paramType>Decimal</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>number</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>24</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>0ef5710b-2e95-4434-a027-549c14aa6d1f</id>
<name>XS06</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>基本险别</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName>bd.customerdoc_BasicRisks.BasicRisks</fullName>
<ytenantId/>
<paramOrder/>
<bizType>quote</bizType>
<baseType>false</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>true</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5cdd3a57-aaa9-4aa5-a43d-31b4544d7f59</id>
<name>XS07</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>允许转运</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>switch</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>130cfbdf-1410-4319-b50c-5200612f43d5</id>
<name>XS08</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>允许分批</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>switch</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>75a2c6cd-9f2f-4264-935a-1ba303bd0e3c</id>
<name>XS09</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>最迟装运期</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>date</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>7c6bfded-3e78-4195-b996-a269fe6943a6</id>
<name>XS10</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>贸易方式</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName>bd.customerdoc_TradeMode.TradeMode</fullName>
<ytenantId/>
<paramOrder/>
<bizType>quote</bizType>
<baseType>false</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>true</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>6c212922-a41e-4163-8e9d-a492c75259bd</id>
<name>XS12</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>合同号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>0741db86-61e5-4e3b-b52b-4260fa7c91ca</id>
<name>XS13</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>商标</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>6c364254-0466-4f0a-bf7f-6ee7e216dfc7</id>
<name>XS14</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>装柜</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>f96fe395-a924-42b9-91a6-5e3f0c5700ad</id>
<name>XS16</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>商标国外注册</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>switch</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>da1d6179-0a78-4245-9a07-a2cdc81ca4bf</id>
<name>XS17</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>付款方式</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>52108c90-c3dd-4cdd-87c0-8c45ffef0f30</id>
<name>XS18</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>首次</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>switch</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>c0d29c11-b2be-4d9f-b1a4-e1f7653e413c</id>
<name>XS19</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>见附件</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>switch</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>92e91dd8-51d4-4b19-bbf5-438f4e7e778b</id>
<name>XS20</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>首次未确定</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>switch</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>d5dc45e2-0225-4e11-96f8-151e4d9253b6</id>
<name>XS21</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>首次未确定-预计时间</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>date</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>16207474-334e-4877-a466-d8c9c5ff0122</id>
<name>XS22</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>返单</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>switch</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>42f8056d-37f2-40d0-9bc0-87366bebc0a2</id>
<name>XS23</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>确定无变更</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>switch</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>ca8f3f80-9c8d-485a-96db-554e39dd8024</id>
<name>XS24</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>返单未确定</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>switch</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>4c6d34cf-2e17-406d-adbd-3df840b6ddef</id>
<name>XS25</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>返单未确定-预计时间</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>date</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>bd6f1152-3434-43df-b519-425bc47e2cfc</id>
<name>XS26</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>确定变更</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>switch</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>582c264a-2651-48a3-a794-4cbeeb4ed982</id>
<name>XS27</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单头备注1</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>6b9430a1-3a6c-4092-8675-5e963ab393b1</id>
<name>XS28</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单头备注2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>9f00fa48-2cad-476e-b860-24906119e8e3</id>
<name>XS29</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单头备注3</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>d15907a6-8359-4604-b04d-60fe535408b3</id>
<name>XS30</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单头备注4</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>7cf247f6-d3ad-46e5-9dda-dd53a356b230</id>
<name>XS31</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>更改次数</paramDesc>
<paramType>Decimal</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>number</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>24</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>f04cc929-4da2-495d-b6d2-03799496d323</id>
<name>XS32</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>车牌号码</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>24e6a29a-4933-499f-8bf4-edda76faf015</id>
<name>XS34</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>商标中国注册</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>switch</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>8169aa87-31eb-4a65-a7c1-206f8211e00c</id>
<name>XS35</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>海关报备</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>switch</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:35</gmtCreate>
<gmtUpdate>2025-07-26 17:36:35</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>ffeabc0e-ef62-464e-9a2f-df9b4972eb68</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:36:44</gmtCreate>
<gmtUpdate>2025-07-26 17:36:44</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>