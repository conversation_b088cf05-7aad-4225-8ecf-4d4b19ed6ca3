#!/bin/bash
# 屎山代码识别脚本

echo "🔍 开始识别屎山代码..."

# 创建分析目录
mkdir -p analysis/shit_mountain

# 1. 超长函数识别（函数名超过50字符）
echo "📋 识别超长函数..."
grep -r "def [^(]{50,}" backend/ > analysis/shit_mountain/shit_functions.txt || echo "未发现超长函数"

# 2. 超复杂条件识别（if语句超过80字符）
echo "📋 识别复杂条件..."
grep -r "if [^:]{80,}:" backend/ > analysis/shit_mountain/shit_conditions.txt || echo "未发现复杂条件"

# 3. 巨型类识别（超过500行）
echo "📋 识别巨型类..."
find backend/ -name "*.py" -exec wc -l {} + | awk '$1 > 500 {print $2 ": " $1 " lines"}' > analysis/shit_mountain/giant_classes.txt

# 4. 深度嵌套识别（超过5层缩进）
echo "📋 识别深度嵌套..."
find backend/ -name "*.py" -exec grep -n "^[[:space:]]\{20,\}" {} + > analysis/shit_mountain/deep_nesting.txt || echo "未发现深度嵌套"

# 5. 重复代码识别
echo "📋 识别重复代码..."
if command -v jscpd &> /dev/null; then
    jscpd backend/ --min-lines 10 --output analysis/shit_mountain/duplicate_code_report.html
else
    echo "⚠️ jscpd未安装，跳过重复代码检测"
fi

# 6. 无用代码识别
echo "📋 识别无用代码..."
if command -v vulture &> /dev/null; then
    vulture backend/ --min-confidence 90 > analysis/shit_mountain/unused_code.txt
else
    echo "⚠️ vulture未安装，跳过无用代码检测"
fi

# 7. 安全问题扫描
echo "📋 扫描安全问题..."
if command -v semgrep &> /dev/null; then
    semgrep --config=auto backend/ --json > analysis/shit_mountain/security_issues.json
else
    echo "⚠️ semgrep未安装，跳过安全扫描"
fi

# 生成汇总报告
echo "📊 生成屎山代码汇总报告..."
cat > analysis/shit_mountain/summary.md << EOF
# 屎山代码识别报告

生成时间: $(date)

## 识别结果统计

### 超长函数
\`\`\`
$(wc -l < analysis/shit_mountain/shit_functions.txt) 个超长函数
\`\`\`

### 复杂条件
\`\`\`
$(wc -l < analysis/shit_mountain/shit_conditions.txt) 个复杂条件
\`\`\`

### 巨型类
\`\`\`
$(wc -l < analysis/shit_mountain/giant_classes.txt) 个巨型类
\`\`\`

### 深度嵌套
\`\`\`
$(wc -l < analysis/shit_mountain/deep_nesting.txt) 处深度嵌套
\`\`\`

## 详细报告文件
- 超长函数: analysis/shit_mountain/shit_functions.txt
- 复杂条件: analysis/shit_mountain/shit_conditions.txt  
- 巨型类: analysis/shit_mountain/giant_classes.txt
- 深度嵌套: analysis/shit_mountain/deep_nesting.txt
- 重复代码: analysis/shit_mountain/duplicate_code_report.html
- 无用代码: analysis/shit_mountain/unused_code.txt
- 安全问题: analysis/shit_mountain/security_issues.json
EOF

echo "✅ 屎山代码识别完成！"
echo "📄 查看报告: cat analysis/shit_mountain/summary.md"
