{"categories": {"core": [], "important": [{"path": "scripts\\port_manager.py", "score": 0.55, "dependencies": 28, "type": "python", "imports": ["subprocess", "n", "self.logger", "levelname", "os", "logging", "logging.FileHandler", "asctime", "nfrom", "Path", "logging.getLogger", "self.log_file", "s", "handlers", "def", "time", "message", "import", "pathlib", "socket", "nimport", "self", "logging.StreamHandler", "sys", "format", "is_port_in_use", "encoding", "__name__"], "internal_deps": []}, {"path": "backend\\app\\services\\auto_recovery_manager_enhanced.py", "score": 0.5375, "dependencies": 19, "type": "python", "imports": ["subprocess", "logging.handlers", "os", "logging", "datetime", "structlog", "win32event", "signal", "redis", "fcntl", "win32api", "time", "collections", "pathlib", "contextlib", "asyncio", "random", "sys", "psutil"], "internal_deps": []}, {"path": "backend\\app\\services\\enhanced_ys_api_client.py", "score": 0.5375, "dependencies": 3, "type": "python", "imports": ["asyncio", "structlog", ".ys_api_client"], "internal_deps": []}, {"path": "core\\app\\services\\auto_recovery_manager_enhanced.py", "score": 0.5375, "dependencies": 19, "type": "python", "imports": ["subprocess", "logging.handlers", "os", "logging", "datetime", "structlog", "win32event", "signal", "redis", "fcntl", "win32api", "time", "collections", "pathlib", "contextlib", "asyncio", "random", "sys", "psutil"], "internal_deps": []}, {"path": "backend\\app\\services\\data_write_manager.py", "score": 0.5, "dependencies": 16, "type": "python", "imports": ["asyncio", "time", "structlog", "decimal", "sqlalchemy.ext.asyncio", ".ys_api_client", "pyodbc", "pathlib", ".realtime_log_service", ".sync_status_manager", "re", "json", "datetime", "logging", ".field_config_service", "httpx"], "internal_deps": []}, {"path": "backend\\app\\services\\integrated_ys_api_client.py", "score": 0.5, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\services\\ys_api_client.py", "score": 0.5, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "core\\app\\services\\data_write_manager.py", "score": 0.5, "dependencies": 16, "type": "python", "imports": ["asyncio", "time", "structlog", "decimal", "sqlalchemy.ext.asyncio", ".ys_api_client", "pyodbc", "pathlib", ".realtime_log_service", ".sync_status_manager", "re", "json", "datetime", "logging", ".field_config_service", "httpx"], "internal_deps": []}, {"path": "migration\\week3_analysis\\refactored\\endpoint_manager.py", "score": 0.5, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}], "optional": [{"path": "backend\\app\\services\\config_persistence_service.py", "score": 0.4625, "dependencies": 5, "type": "python", "imports": ["shutil", "structlog", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "core\\app\\services\\config_persistence_service.py", "score": 0.4625, "dependencies": 5, "type": "python", "imports": ["shutil", "structlog", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "month2_config\\config_rollback\\manager.py", "score": 0.4625, "dependencies": 5, "type": "python", "imports": ["time", "difflib", "json", "<PERSON><PERSON><PERSON>", "pathlib"], "internal_deps": []}, {"path": "backend\\app\\services\\auto_sync_scheduler.py", "score": 0.44999999999999996, "dependencies": 12, "type": "python", "imports": ["asyncio", "time", "structlog", ".data_write_manager", "sys", "json", ".retry_helper", "os", "pathlib", ".auto_recovery_manager_enhanced", "traceback", "psutil"], "internal_deps": []}, {"path": "backend\\app\\services\\field_config_service.py", "score": 0.44999999999999996, "dependencies": 4, "type": "python", "imports": ["pathlib", ".enhanced_json_field_matcher", "json", "structlog"], "internal_deps": []}, {"path": "backend\\app\\services\\__init__.py", "score": 0.44999999999999996, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "core\\app\\services\\auto_sync_scheduler.py", "score": 0.44999999999999996, "dependencies": 12, "type": "python", "imports": ["asyncio", "time", "structlog", ".data_write_manager", "sys", "json", ".retry_helper", "os", "pathlib", ".auto_recovery_manager_enhanced", "traceback", "psutil"], "internal_deps": []}, {"path": "core\\app\\services\\field_config_service.py", "score": 0.44999999999999996, "dependencies": 4, "type": "python", "imports": ["pathlib", ".enhanced_json_field_matcher", "json", "structlog"], "internal_deps": []}, {"path": "core\\app\\services\\__init__.py", "score": 0.44999999999999996, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "core\\backend\\app\\services\\__init__.py", "score": 0.44999999999999996, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "month2_config\\two_step_save\\manager.py", "score": 0.44999999999999996, "dependencies": 4, "type": "python", "imports": ["pathlib", "json", "<PERSON><PERSON><PERSON>", "time"], "internal_deps": []}, {"path": "backend\\app\\services\\database_table_manager.py", "score": 0.4375, "dependencies": 11, "type": "python", "imports": ["shutil", "contextlib", "asyncio", "time", "structlog", "pathlib", "sqlite3", "dataclasses", "json", "os", "datetime"], "internal_deps": []}, {"path": "core\\app\\services\\database_table_manager.py", "score": 0.4375, "dependencies": 11, "type": "python", "imports": ["shutil", "contextlib", "asyncio", "time", "structlog", "pathlib", "sqlite3", "dataclasses", "json", "os", "datetime"], "internal_deps": []}, {"path": "install_windows_service.py", "score": 0.425, "dependencies": 10, "type": "python", "imports": ["asyncio", "win32event", "start_production_enhanced", "pathlib", "sys", "win32serviceutil", "servicemanager", "os", "logging", "win32service"], "internal_deps": []}, {"path": "backend\\app\\services\\zero_downtime_implementation.py", "score": 0.4125, "dependencies": 9, "type": "python", "imports": ["asyncio", "string", "structlog", "sqlalchemy", "sqlalchemy.ext.asyncio", "random", "backend.app.services.ys_api_client", "pathlib", ".sync_status_manager"], "internal_deps": []}, {"path": "core\\app\\services\\zero_downtime_implementation.py", "score": 0.4125, "dependencies": 9, "type": "python", "imports": ["asyncio", "string", "structlog", "sqlalchemy", "sqlalchemy.ext.asyncio", "random", "backend.app.services.ys_api_client", "pathlib", ".sync_status_manager"], "internal_deps": []}, {"path": "backend\\start_server_fixed.py", "score": 0.4, "dependencies": 22, "type": "python", "imports": ["fastapi.middleware.cors", "n", "CORSMiddleware", "json", "os", "logging", "nfrom", "Response", "fastapi.responses", "Path", "<PERSON><PERSON><PERSON>", "u<PERSON><PERSON>", "xml.etree.ElementTree", "fastapi.staticfiles", "import", "port_manager", "pathlib", "FastAPI", "nimport", "sys", "StaticFiles", "PortManager"], "internal_deps": []}, {"path": "backend\\app\\services\\database_manager.py", "score": 0.38749999999999996, "dependencies": 7, "type": "python", "imports": ["structlog", "time", "configparser", "dataclasses", "threading", "os", "pathlib"], "internal_deps": []}, {"path": "backend\\app\\services\\fast_sync_service.py", "score": 0.38749999999999996, "dependencies": 7, "type": "python", "imports": ["asyncio", "time", "structlog", ".ys_api_client", ".data_write_manager", "collections", "datetime"], "internal_deps": []}, {"path": "core\\app\\services\\database_manager.py", "score": 0.38749999999999996, "dependencies": 7, "type": "python", "imports": ["structlog", "time", "configparser", "dataclasses", "threading", "os", "pathlib"], "internal_deps": []}, {"path": "core\\app\\services\\fast_sync_service.py", "score": 0.38749999999999996, "dependencies": 7, "type": "python", "imports": ["asyncio", "time", "structlog", ".ys_api_client", ".data_write_manager", "collections", "datetime"], "internal_deps": []}, {"path": "backend\\app\\services\\advanced_data_transformer.py", "score": 0.375, "dependencies": 6, "type": "python", "imports": ["structlog", "enum", "dataclasses", "re", "json", "datetime"], "internal_deps": []}, {"path": "backend\\app\\services\\enhanced_authenticator.py", "score": 0.375, "dependencies": 6, "type": "python", "imports": ["base64", "structlog", "time", "hmac", "urllib.parse", "<PERSON><PERSON><PERSON>"], "internal_deps": []}, {"path": "backend\\app\\services\\excel_field_matcher_pretranslated.py", "score": 0.375, "dependencies": 6, "type": "python", "imports": ["structlog", "difflib", "dataclasses", "re", "json", "os"], "internal_deps": []}, {"path": "backend\\app\\services\\field_extractor_service.py", "score": 0.375, "dependencies": 6, "type": "python", "imports": ["time", "structlog", "re", "json", "os", "pathlib"], "internal_deps": []}, {"path": "backend\\app\\services\\material_master_scheduler.py", "score": 0.375, "dependencies": 6, "type": "python", "imports": ["structlog", "apscheduler.triggers.cron", ".data_write_manager", "json", "apscheduler.schedulers.asyncio", "pathlib"], "internal_deps": []}, {"path": "backend\\app\\services\\token_service.py", "score": 0.375, "dependencies": 6, "type": "python", "imports": ["base64", "time", "hmac", "urllib.parse", "<PERSON><PERSON><PERSON>", "requests"], "internal_deps": []}, {"path": "backend\\app\\services\\unified_field_service.py", "score": 0.375, "dependencies": 6, "type": "python", "imports": ["asyncio", "structlog", "sys", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "backend\\app\\week4\\concurrent\\async_task_manager.py", "score": 0.375, "dependencies": 6, "type": "python", "imports": ["asyncio", "time", "structlog", "enum", "collections", "uuid"], "internal_deps": []}, {"path": "core\\app\\services\\excel_field_matcher_pretranslated.py", "score": 0.375, "dependencies": 6, "type": "python", "imports": ["structlog", "difflib", "dataclasses", "re", "json", "os"], "internal_deps": []}, {"path": "core\\app\\services\\field_extractor_service.py", "score": 0.375, "dependencies": 6, "type": "python", "imports": ["time", "structlog", "re", "json", "os", "pathlib"], "internal_deps": []}, {"path": "core\\app\\services\\token_service.py", "score": 0.375, "dependencies": 6, "type": "python", "imports": ["base64", "time", "hmac", "urllib.parse", "<PERSON><PERSON><PERSON>", "requests"], "internal_deps": []}, {"path": "scripts\\port_manager_clean.py", "score": 0.375, "dependencies": 6, "type": "python", "imports": ["subprocess", "time", "socket", "pathlib", "os", "logging"], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\field_config_api.py", "score": 0.36250000000000004, "dependencies": 5, "type": "python", "imports": ["structlog", "json", "datetime", "pathlib", "pydantic"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\field_config_api.py", "score": 0.36250000000000004, "dependencies": 5, "type": "python", "imports": ["structlog", "json", "datetime", "pathlib", "pydantic"], "internal_deps": []}, {"path": "backend\\app\\services\\enhanced_error_handler.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["asyncio", "time", "structlog", "enum", "dataclasses"], "internal_deps": []}, {"path": "backend\\app\\services\\enhanced_response_processor.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["time", "structlog", ".response_format_standardizer", ".advanced_data_transformer", "datetime"], "internal_deps": []}, {"path": "backend\\app\\services\\excel_field_matcher.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["structlog", "difflib", "dataclasses", "re", "os"], "internal_deps": []}, {"path": "backend\\app\\services\\intelligent_field_mapper.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["structlog", "os", "re", "json", "datetime"], "internal_deps": []}, {"path": "backend\\app\\services\\maintenance_manager.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["asyncio", "time", "structlog", "schedule", "os"], "internal_deps": []}, {"path": "backend\\app\\services\\realtime_log_service.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["asyncio", "structlog", "collections", "json", "datetime"], "internal_deps": []}, {"path": "backend\\app\\services\\response_format_standardizer.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["structlog", "enum", "re", "json", "datetime"], "internal_deps": []}, {"path": "backend\\app\\services\\retry_helper.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["functools", "asyncio", "time", "structlog", "random"], "internal_deps": []}, {"path": "backend\\app\\services\\robust_json_parser.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["structlog", "dataclasses", "re", "json", "pathlib"], "internal_deps": []}, {"path": "core\\app\\services\\excel_field_matcher.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["structlog", "difflib", "dataclasses", "re", "os"], "internal_deps": []}, {"path": "core\\app\\services\\intelligent_field_mapper.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["structlog", "os", "re", "json", "datetime"], "internal_deps": []}, {"path": "core\\app\\services\\maintenance_manager.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["asyncio", "time", "structlog", "schedule", "os"], "internal_deps": []}, {"path": "core\\app\\services\\retry_helper.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["functools", "asyncio", "time", "structlog", "random"], "internal_deps": []}, {"path": "core\\app\\services\\robust_json_parser.py", "score": 0.3625, "dependencies": 5, "type": "python", "imports": ["structlog", "dataclasses", "re", "json", "pathlib"], "internal_deps": []}, {"path": "backend\\app\\api\\__init__.py", "score": 0.35, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\__init__.py", "score": 0.35, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\core\\database_manager.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["contextlib", "asyncio", "structlog", "sqlalchemy"], "internal_deps": []}, {"path": "backend\\app\\services\\data_processor.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": [".field_config_service", "re", "structlog", "datetime"], "internal_deps": []}, {"path": "backend\\app\\services\\data_quality_inspector.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["dataclasses", "enum", "re", "structlog"], "internal_deps": []}, {"path": "backend\\app\\services\\log_service.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["datetime", "glob", "structlog", "os"], "internal_deps": []}, {"path": "backend\\app\\services\\md_parser.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["dataclasses", "re", "structlog", "os"], "internal_deps": []}, {"path": "backend\\app\\services\\sync_status_manager.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["asyncio", "time", "structlog", "collections"], "internal_deps": []}, {"path": "backend\\app\\services\\task_service.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["datetime", "glob", "structlog", "os"], "internal_deps": []}, {"path": "backend\\app\\services\\unified_field_manager.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["pathlib", "json", "structlog", "datetime"], "internal_deps": []}, {"path": "core\\app\\api\\__init__.py", "score": 0.35, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "core\\app\\api\\v1\\__init__.py", "score": 0.35, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "core\\app\\core\\database_manager.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["contextlib", "asyncio", "structlog", "sqlalchemy"], "internal_deps": []}, {"path": "core\\app\\services\\data_processor.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": [".field_config_service", "re", "structlog", "datetime"], "internal_deps": []}, {"path": "core\\app\\services\\log_service.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["datetime", "glob", "structlog", "os"], "internal_deps": []}, {"path": "core\\app\\services\\md_parser.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["dataclasses", "re", "structlog", "os"], "internal_deps": []}, {"path": "core\\app\\services\\sync_status_manager.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["asyncio", "time", "structlog", "collections"], "internal_deps": []}, {"path": "core\\app\\services\\task_service.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["datetime", "glob", "structlog", "os"], "internal_deps": []}, {"path": "core\\app\\services\\unified_field_manager.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["pathlib", "json", "structlog", "datetime"], "internal_deps": []}, {"path": "core\\backend\\app\\api\\__init__.py", "score": 0.35, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "core\\backend\\app\\api\\v1\\__init__.py", "score": 0.35, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "month2_database\\table_creation\\manager.py", "score": 0.35, "dependencies": 4, "type": "python", "imports": ["sqlite3", "re", "xml.etree.ElementTree", "pathlib"], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\config.py", "score": 0.3375, "dependencies": 3, "type": "python", "imports": ["<PERSON><PERSON><PERSON>", "structlog", "typing"], "internal_deps": []}, {"path": "backend\\app\\services\\business_translation_rules.py", "score": 0.33749999999999997, "dependencies": 3, "type": "python", "imports": ["dataclasses", "re", "structlog"], "internal_deps": []}, {"path": "backend\\app\\services\\database_enhancement.py", "score": 0.33749999999999997, "dependencies": 3, "type": "python", "imports": ["types", "structlog", "sqlalchemy"], "internal_deps": []}, {"path": "backend\\app\\services\\enhanced_json_field_matcher.py", "score": 0.33749999999999997, "dependencies": 3, "type": "python", "imports": ["json", "structlog", "os"], "internal_deps": []}, {"path": "backend\\app\\services\\field_validation_service.py", "score": 0.33749999999999997, "dependencies": 3, "type": "python", "imports": ["dataclasses", "re", "structlog"], "internal_deps": []}, {"path": "backend\\app\\services\\field_value_mapping_service.py", "score": 0.33749999999999997, "dependencies": 3, "type": "python", "imports": ["pathlib", "json", "structlog"], "internal_deps": []}, {"path": "backend\\app\\services\\status_mapping_service.py", "score": 0.33749999999999997, "dependencies": 3, "type": "python", "imports": ["pathlib", "json", "structlog"], "internal_deps": []}, {"path": "core\\app\\services\\business_translation_rules.py", "score": 0.33749999999999997, "dependencies": 3, "type": "python", "imports": ["dataclasses", "re", "structlog"], "internal_deps": []}, {"path": "core\\app\\services\\enhanced_json_field_matcher.py", "score": 0.33749999999999997, "dependencies": 3, "type": "python", "imports": ["json", "structlog", "os"], "internal_deps": []}, {"path": "core\\app\\services\\field_validation_service.py", "score": 0.33749999999999997, "dependencies": 3, "type": "python", "imports": ["dataclasses", "re", "structlog"], "internal_deps": []}, {"path": "core\\app\\services\\field_value_mapping_service.py", "score": 0.33749999999999997, "dependencies": 3, "type": "python", "imports": ["pathlib", "json", "structlog"], "internal_deps": []}, {"path": "core\\app\\services\\status_mapping_service.py", "score": 0.33749999999999997, "dependencies": 3, "type": "python", "imports": ["pathlib", "json", "structlog"], "internal_deps": []}, {"path": "new-system\\modules\\purchase_order\\service.py", "score": 0.33749999999999997, "dependencies": 3, "type": "python", "imports": ["sqlalchemy.orm", "database", "datetime"], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\unified_field_config.py", "score": 0.325, "dependencies": 2, "type": "python", "imports": ["structlog", "fastapi.responses"], "internal_deps": []}, {"path": "backend\\app\\services\\field_analysis_service.py", "score": 0.325, "dependencies": 2, "type": "python", "imports": ["re", "structlog"], "internal_deps": []}, {"path": "backend\\app\\services\\monitor_service.py", "score": 0.325, "dependencies": 2, "type": "python", "imports": ["structlog", "psutil"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\config.py", "score": 0.325, "dependencies": 2, "type": "python", "imports": ["structlog", "typing"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\unified_field_config.py", "score": 0.325, "dependencies": 2, "type": "python", "imports": ["structlog", "fastapi.responses"], "internal_deps": []}, {"path": "core\\app\\services\\field_analysis_service.py", "score": 0.325, "dependencies": 2, "type": "python", "imports": ["re", "structlog"], "internal_deps": []}, {"path": "core\\app\\services\\monitor_service.py", "score": 0.325, "dependencies": 2, "type": "python", "imports": ["structlog", "psutil"], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\excel_translation.py", "score": 0.30000000000000004, "dependencies": 8, "type": "python", "imports": ["batch_pretranslation", "structlog", "tempfile", "fastapi.responses", "sys", "re", "os", "pathlib"], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\monitor.py", "score": 0.30000000000000004, "dependencies": 8, "type": "python", "imports": ["time", "structlog", "sqlalchemy", "configparser", "pyodbc", "json", "os", "datetime"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\excel_translation.py", "score": 0.30000000000000004, "dependencies": 8, "type": "python", "imports": ["batch_pretranslation", "structlog", "tempfile", "fastapi.responses", "sys", "re", "os", "pathlib"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\monitor.py", "score": 0.30000000000000004, "dependencies": 8, "type": "python", "imports": ["time", "structlog", "sqlalchemy", "configparser", "pyodbc", "json", "os", "datetime"], "internal_deps": []}, {"path": "refactor_data_write_manager.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "refactor_fast_sync_service.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\services\\async_task_manager.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\services\\concurrent_processor.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\services\\connection_pool_optimizer.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\services\\database_connection_pool.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\services\\enhanced_connection_pool.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\services\\enhanced_response_parser.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\services\\enhanced_transaction_manager.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\services\\load_balancer.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\services\\standard_request_builder.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\services\\transaction_manager.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\services\\week3_validation_suite.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\core\\component-manager.js", "score": 0.3, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\core\\initialization-manager.js", "score": 0.3, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "migration\\week2_analysis\\refactored\\refactored_fast_sync_service.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\产品入库单列表查询\\service.py", "score": 0.3, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}], "junk": [{"path": "backend\\app\\api\\v1\\sync.py", "score": 0.2875, "dependencies": 7, "type": "python", "imports": ["asyncio", "structlog", "sqlalchemy.ext.asyncio", "typing", "datetime", "uuid", "traceback"], "internal_deps": []}, {"path": "core\\app\\main.py", "score": 0.2875, "dependencies": 11, "type": "python", "imports": ["fastapi.middleware.cors", "codecs", "fastapi.responses", "fastapi.staticfiles", "pathlib", "sys", "json", "os", "logging", "u<PERSON><PERSON>", "datetime"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\sync.py", "score": 0.2875, "dependencies": 7, "type": "python", "imports": ["asyncio", "structlog", "sqlalchemy.ext.asyncio", "typing", "datetime", "uuid", "traceback"], "internal_deps": []}, {"path": "backend\\app\\main.py", "score": 0.275, "dependencies": 10, "type": "python", "imports": ["fastapi.middleware.cors", "codecs", "fastapi.responses", "fastapi.staticfiles", "pathlib", "sys", "json", "os", "logging", "datetime"], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\enhanced_sync.py", "score": 0.2625, "dependencies": 5, "type": "python", "imports": ["asyncio", "time", "structlog", "sqlalchemy.ext.asyncio", "datetime"], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\realtime_logs.py", "score": 0.2625, "dependencies": 5, "type": "python", "imports": ["asyncio", "structlog", "fastapi.responses", "json", "datetime"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\enhanced_sync.py", "score": 0.2625, "dependencies": 5, "type": "python", "imports": ["asyncio", "time", "structlog", "sqlalchemy.ext.asyncio", "datetime"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\realtime_logs.py", "score": 0.2625, "dependencies": 5, "type": "python", "imports": ["asyncio", "structlog", "fastapi.responses", "json", "datetime"], "internal_deps": []}, {"path": "backend\\start_simple.py", "score": 0.25, "dependencies": 8, "type": "python", "imports": ["fastapi.responses", "<PERSON><PERSON><PERSON>", "fastapi.staticfiles", "pathlib", "json", "logging", "u<PERSON><PERSON>", "xml.etree.ElementTree"], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\database.py", "score": 0.25, "dependencies": 4, "type": "python", "imports": ["<PERSON><PERSON><PERSON>", "structlog", "typing", "datetime"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\database.py", "score": 0.23750000000000002, "dependencies": 3, "type": "python", "imports": ["structlog", "typing", "datetime"], "internal_deps": []}, {"path": "frontend\\start_frontend_clean.py", "score": 0.2375, "dependencies": 7, "type": "python", "imports": ["webbrowser", "time", "socketserver", "port_manager", "sys", "pathlib", "http.server"], "internal_deps": []}, {"path": "run_comprehensive_check.py", "score": 0.22499999999999998, "dependencies": 6, "type": "python", "imports": ["configparser", "sqlite3", "requests", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\auth.py", "score": 0.21250000000000002, "dependencies": 1, "type": "python", "imports": ["<PERSON><PERSON><PERSON>"], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\database_health.py", "score": 0.21250000000000002, "dependencies": 1, "type": "python", "imports": ["structlog"], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\maintenance.py", "score": 0.21250000000000002, "dependencies": 1, "type": "python", "imports": ["structlog"], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\sync_status.py", "score": 0.21250000000000002, "dependencies": 1, "type": "python", "imports": ["structlog"], "internal_deps": []}, {"path": "backend\\app\\api\\v1\\tasks.py", "score": 0.21250000000000002, "dependencies": 1, "type": "python", "imports": ["structlog"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\auth.py", "score": 0.21250000000000002, "dependencies": 1, "type": "python", "imports": ["<PERSON><PERSON><PERSON>"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\database_health.py", "score": 0.21250000000000002, "dependencies": 1, "type": "python", "imports": ["structlog"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\maintenance.py", "score": 0.21250000000000002, "dependencies": 1, "type": "python", "imports": ["structlog"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\sync_status.py", "score": 0.21250000000000002, "dependencies": 1, "type": "python", "imports": ["structlog"], "internal_deps": []}, {"path": "core\\app\\api\\v1\\tasks.py", "score": 0.21250000000000002, "dependencies": 1, "type": "python", "imports": ["structlog"], "internal_deps": []}, {"path": "backend\\start_server_clean.py", "score": 0.2125, "dependencies": 5, "type": "python", "imports": ["pathlib", "port_manager", "sys", "logging", "u<PERSON><PERSON>"], "internal_deps": []}, {"path": "backend\\start_server.py", "score": 0.2, "dependencies": 4, "type": "python", "imports": ["logging", "u<PERSON><PERSON>", "sys", "pathlib"], "internal_deps": []}, {"path": "core\\main.py", "score": 0.2, "dependencies": 4, "type": "python", "imports": ["logging", "u<PERSON><PERSON>", "sys", "pathlib"], "internal_deps": []}, {"path": "start_month1_validation.py", "score": 0.1875, "dependencies": 3, "type": "python", "imports": ["pathlib", "subprocess", "time"], "internal_deps": []}, {"path": "start_month2.py", "score": 0.175, "dependencies": 2, "type": "python", "imports": ["pathlib", "time"], "internal_deps": []}, {"path": "backend\\app\\week4\\__init__.py", "score": 0.175, "dependencies": 2, "type": "python", "imports": ["dataclasses", "structlog"], "internal_deps": []}, {"path": "backend\\app\\core\\config.py", "score": 0.1625, "dependencies": 5, "type": "python", "imports": ["structlog", "typing", "pydantic_settings", "configparser", "pathlib"], "internal_deps": []}, {"path": "frontend\\start_frontend_fixed.py", "score": 0.1625, "dependencies": 1, "type": "python", "imports": ["port_manager"], "internal_deps": []}, {"path": "scripts\\migrate_purchase_order.py", "score": 0.1625, "dependencies": 13, "type": "python", "imports": ["pytest", "sqlalchemy.orm", "time", ".service", "database", "pathlib", "new_system.modules.purchase_order.service", "sys", "json", "fastapi.testclient", "os", "datetime", "sqlalchemy.ext.declarative"], "internal_deps": []}, {"path": "config_environmentizer.py", "score": 0.15000000000000002, "dependencies": 4, "type": "python", "imports": ["logging", "pathlib", "configparser", "os"], "internal_deps": []}, {"path": "config_environmentizer_clean.py", "score": 0.15000000000000002, "dependencies": 4, "type": "python", "imports": ["logging", "pathlib", "configparser", "os"], "internal_deps": []}, {"path": "core\\app\\core\\config.py", "score": 0.15000000000000002, "dependencies": 4, "type": "python", "imports": ["pathlib", "pydantic_settings", "configparser", "typing"], "internal_deps": []}, {"path": "backend\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\core\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\schemas\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\week4\\concurrent\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "core\\app\\core\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "core\\app\\schemas\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "core\\backend\\start_server.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "core\\backend\\app\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "core\\backend\\app\\core\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "frontend\\start_frontend.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\purchase_order\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\业务日志\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\产品入库单列表查询\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\委外入库列表查询\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\委外申请列表查询\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\委外订单列表\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\物料档案批量详情查询\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\生产订单列表查询\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\请购单列表查询\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\销售出库列表查询\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\销售订单\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\需求计划\\__init__.py", "score": 0.15, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "scripts\\database_dual_writer.py", "score": 0.15, "dependencies": 12, "type": "python", "imports": ["redis", "asyncio", "time", "psycopg2.pool", "psycopg2.extras", "psycopg2", "pathlib", "sqlite3", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "json", "logging"], "internal_deps": []}, {"path": "scripts\\add_api_config.py", "score": 0.1375, "dependencies": 3, "type": "python", "imports": ["logging", "glob", "os"], "internal_deps": []}, {"path": "violent_cleanup.py", "score": 0.125, "dependencies": 10, "type": "python", "imports": ["shutil", "subprocess", "time", "typing", "sys", "re", "json", "os", "pathlib", "signal"], "internal_deps": []}, {"path": "project_health_check.py", "score": 0.1125, "dependencies": 9, "type": "python", "imports": ["subprocess", "configparser", "pathlib", "sqlite3", "requests", "sys", "os", "datetime", "importlib.util"], "internal_deps": []}, {"path": "backend\\app\\schemas\\config.py", "score": 0.1125, "dependencies": 1, "type": "python", "imports": ["datetime"], "internal_deps": []}, {"path": "core\\app\\schemas\\config.py", "score": 0.1125, "dependencies": 1, "type": "python", "imports": ["datetime"], "internal_deps": []}, {"path": "scripts\\rollback_batch_writes.py", "score": 0.1125, "dependencies": 9, "type": "python", "imports": ["pyodbc", "pathlib", "<PERSON><PERSON><PERSON><PERSON>", "sys", "re", "json", "app.core.config", "logging", "traceback"], "internal_deps": []}, {"path": "build_production_package.py", "score": 0.1, "dependencies": 8, "type": "python", "imports": ["shutil", "subprocess", "platform", "pathlib", "sys", "json", "os", "logging"], "internal_deps": []}, {"path": "quick_health_check.py", "score": 0.1, "dependencies": 8, "type": "python", "imports": ["pathlib", "sqlite3", "sys", "json", "os", "logging", "psutil", "datetime"], "internal_deps": []}, {"path": "backend\\app\\strangler_proxy\\config.py", "score": 0.1, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\app\\week4\\concurrent\\connection_pool_optimizer.py", "score": 0.1, "dependencies": 8, "type": "python", "imports": ["asyncio", "time", "structlog", "enum", "pymssql", "collections", "sqlite3", "threading"], "internal_deps": []}, {"path": "backend\\app\\week4\\concurrent\\load_balancer.py", "score": 0.1, "dependencies": 8, "type": "python", "imports": ["asyncio", "time", "structlog", "enum", "statistics", "random", "<PERSON><PERSON><PERSON>", "threading"], "internal_deps": []}, {"path": "frontend\\unified-field-config-fix.js", "score": 0.1, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\api-config-fix.js", "score": 0.1, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\api-config.js", "score": 0.1, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\user-config-save.js", "score": 0.1, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\common\\elk-integration-config.js", "score": 0.1, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "auto_fix_comprehensive_issues.py", "score": 0.0875, "dependencies": 7, "type": "python", "imports": ["typing", "configparser", "pathlib", "re", "json", "datetime", "logging"], "internal_deps": []}, {"path": "cicd_pipeline_builder.py", "score": 0.0875, "dependencies": 7, "type": "python", "imports": ["yaml", "pathlib", "sys", "json", "os", "logging", "datetime"], "internal_deps": []}, {"path": "cicd_pipeline_builder_optimized.py", "score": 0.0875, "dependencies": 7, "type": "python", "imports": ["yaml", "pathlib", "sys", "json", "os", "logging", "datetime"], "internal_deps": []}, {"path": "port_locker.py", "score": 0.0875, "dependencies": 7, "type": "python", "imports": ["subprocess", "time", "socket", "sys", "json", "os", "pathlib"], "internal_deps": []}, {"path": "production_readiness_report.py", "score": 0.0875, "dependencies": 7, "type": "python", "imports": ["time", "pathlib", "sqlite3", "json", "os", "logging", "datetime"], "internal_deps": []}, {"path": "backend\\app\\main_original.py", "score": 0.0875, "dependencies": 7, "type": "python", "imports": ["contextlib", "fastapi.middleware.cors", "structlog", "fastapi.staticfiles", ".api.v1", "os", "pathlib"], "internal_deps": []}, {"path": "backend\\app\\core\\database_connection_pool.py", "score": 0.0875, "dependencies": 7, "type": "python", "imports": ["contextlib", "asyncio", "structlog", "time", ".config", "pyodbc", "dataclasses"], "internal_deps": []}, {"path": "core\\app\\main_original.py", "score": 0.0875, "dependencies": 7, "type": "python", "imports": ["contextlib", "fastapi.middleware.cors", "structlog", "fastapi.staticfiles", ".api.v1", "os", "pathlib"], "internal_deps": []}, {"path": "core\\app\\core\\database_connection_pool.py", "score": 0.0875, "dependencies": 7, "type": "python", "imports": ["contextlib", "asyncio", "structlog", "time", ".config", "pyodbc", "dataclasses"], "internal_deps": []}, {"path": "tools\\md_to_json_converter.py", "score": 0.0875, "dependencies": 7, "type": "python", "imports": ["typing", "sys", "re", "json", "os", "logging", "app.core.config"], "internal_deps": []}, {"path": "execute_task_checklist.py", "score": 0.075, "dependencies": 6, "type": "python", "imports": ["configparser", "pathlib", "re", "datetime", "logging", "ast"], "internal_deps": []}, {"path": "file_sentinel.py", "score": 0.075, "dependencies": 6, "type": "python", "imports": ["typing", "<PERSON><PERSON><PERSON><PERSON>", "json", "<PERSON><PERSON><PERSON>", "datetime", "pathlib"], "internal_deps": []}, {"path": "systematic_duplicate_detector.py", "score": 0.075, "dependencies": 6, "type": "python", "imports": ["collections", "pathlib", "re", "json", "<PERSON><PERSON><PERSON>", "logging"], "internal_deps": []}, {"path": "backend\\app\\core\\exceptions.py", "score": 0.075, "dependencies": 6, "type": "python", "imports": ["asyncio", "time", "structlog", "enum", "dataclasses", "traceback"], "internal_deps": []}, {"path": "core\\app\\core\\exceptions.py", "score": 0.075, "dependencies": 6, "type": "python", "imports": ["asyncio", "time", "structlog", "enum", "dataclasses", "traceback"], "internal_deps": []}, {"path": "scripts\\auto_migration.py", "score": 0.075, "dependencies": 6, "type": "python", "imports": ["shutil", "<PERSON><PERSON><PERSON><PERSON>", "re", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "scripts\\auto_migration_pipeline.py", "score": 0.075, "dependencies": 6, "type": "python", "imports": ["asyncio", "<PERSON><PERSON><PERSON><PERSON>", "sys", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "scripts\\database_dual_writer_simple.py", "score": 0.075, "dependencies": 6, "type": "python", "imports": ["sqlite3", "<PERSON><PERSON><PERSON><PERSON>", "sys", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "scripts\\quick_batch_migrate.py", "score": 0.075, "dependencies": 6, "type": "python", "imports": ["subprocess", "os", "sys", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "scripts\\simple_migrator.py", "score": 0.075, "dependencies": 6, "type": "python", "imports": ["asyncio", "<PERSON><PERSON><PERSON><PERSON>", "sys", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "auto_project_cleanup.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["shutil", "pathlib", "json", "datetime", "logging"], "internal_deps": []}, {"path": "batch_code_quality_fix.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["typing", "re", "os", "pathlib", "ast"], "internal_deps": []}, {"path": "fix_all_flake8.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["subprocess", "sys", "re", "os", "pathlib"], "internal_deps": []}, {"path": "fix_issues.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["subprocess", "sqlite3", "sys", "os", "pathlib"], "internal_deps": []}, {"path": "machine_cleanup.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["shutil", "subprocess", "json", "<PERSON><PERSON><PERSON>", "os"], "internal_deps": []}, {"path": "pollution_guard.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["time", "sys", "os", "pathlib", "signal"], "internal_deps": []}, {"path": "verify_module.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["time", "sys", "json", "pathlib", "xml.etree.ElementTree"], "internal_deps": []}, {"path": "backend\\app\\core\\optimized_retry.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["functools", "asyncio", "time", "random", "logging"], "internal_deps": []}, {"path": "backend\\app\\middleware\\access_log.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["time", "structlog", "typing", "starlette.middleware.base", "uuid"], "internal_deps": []}, {"path": "backend\\app\\week4\\concurrent\\concurrent_processor.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["structlog", "time", "enum", "threading", "datetime"], "internal_deps": []}, {"path": "core\\app\\core\\optimized_retry.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["functools", "asyncio", "time", "random", "logging"], "internal_deps": []}, {"path": "core\\app\\middleware\\access_log.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["time", "structlog", "typing", "starlette.middleware.base", "uuid"], "internal_deps": []}, {"path": "scripts\\health_check.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["subprocess", "sys", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "scripts\\migrate_产品入库单列表查询.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["<PERSON><PERSON><PERSON>", "json", "datetime", "pathlib", "pydantic"], "internal_deps": []}, {"path": "scripts\\migrate_产品入库单列表查询_fixed.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["<PERSON><PERSON><PERSON>", "json", "datetime", "pathlib", "pydantic"], "internal_deps": []}, {"path": "scripts\\migrate_生产订单列表查询.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["<PERSON><PERSON><PERSON>", "json", "datetime", "pathlib", "pydantic"], "internal_deps": []}, {"path": "scripts\\migrate_请购单列表查询.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["<PERSON><PERSON><PERSON>", "json", "datetime", "pathlib", "pydantic"], "internal_deps": []}, {"path": "scripts\\module_tracker.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["<PERSON><PERSON><PERSON><PERSON>", "sys", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "scripts\\module_tracker_simple.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["<PERSON><PERSON><PERSON><PERSON>", "sys", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "scripts\\ultra_simple_migrate.py", "score": 0.0625, "dependencies": 5, "type": "python", "imports": ["subprocess", "sys", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "analyze_dependencies.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["re", "json", "collections", "os"], "internal_deps": []}, {"path": "batch_validate_modules.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["pathlib", "subprocess", "json", "time"], "internal_deps": []}, {"path": "check_naming_conflicts.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["pathlib", "sys", "re", "collections"], "internal_deps": []}, {"path": "fix_sonarqube_issues.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["pathlib", "sys", "re", "os"], "internal_deps": []}, {"path": "fix_task_issues.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["pathlib", "ast", "re", "datetime"], "internal_deps": []}, {"path": "fix_xml_files.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["pathlib", "sys", "re", "xml.etree.ElementTree"], "internal_deps": []}, {"path": "function_duplicate_checker.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["pathlib", "ast", "collections", "sys"], "internal_deps": []}, {"path": "quick_health_check_fixed.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["py_compile", "sys", "json", "os"], "internal_deps": []}, {"path": "remaining_issues_fixer.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["logging", "pathlib", "re", "datetime"], "internal_deps": []}, {"path": "smart_duplicate_cleaner.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["logging", "shutil", "json", "pathlib"], "internal_deps": []}, {"path": "smart_file_creator.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["pathlib", "file_sentinel", "sys", "datetime"], "internal_deps": []}, {"path": "sonarqube_cleanup.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["pathlib", "shutil", "re", "os"], "internal_deps": []}, {"path": "verify_fixes.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["pathlib", "inspect", "port_manager", "sys"], "internal_deps": []}, {"path": "backend\\app\\core\\code_quality.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["dataclasses", "ast", "structlog", "pathlib"], "internal_deps": []}, {"path": "backend\\app\\core\\database.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["sqlalchemy.orm", ".database_manager", "structlog", ".config"], "internal_deps": []}, {"path": "core\\app\\core\\code_quality.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["dataclasses", "ast", "structlog", "pathlib"], "internal_deps": []}, {"path": "core\\app\\core\\database.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["sqlalchemy.orm", ".database_manager", "structlog", ".config"], "internal_deps": []}, {"path": "scripts\\batch_init_modules.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["pathlib", "subprocess", "json", "datetime"], "internal_deps": []}, {"path": "scripts\\final_status_check.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["pathlib", "sys", "json", "datetime"], "internal_deps": []}, {"path": "scripts\\migrate_采购入库单列表.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["datetime", "pathlib", "json", "os"], "internal_deps": []}, {"path": "scripts\\next_module.py", "score": 0.05, "dependencies": 4, "type": "python", "imports": ["pathlib", "subprocess", "json", "sys"], "internal_deps": []}, {"path": "final_batch.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["pathlib", "subprocess", "sys"], "internal_deps": []}, {"path": "fix_build_script.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["logging", "re", "pathlib"], "internal_deps": []}, {"path": "fix_execute_task_script.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["logging", "re", "pathlib"], "internal_deps": []}, {"path": "flake8_stats.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["subprocess", "re", "collections"], "internal_deps": []}, {"path": "generate_stats.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["pathlib", "json", "os"], "internal_deps": []}, {"path": "quick_syntax_fix.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["subprocess", "re", "os"], "internal_deps": []}, {"path": "verify_startup_clean.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["pathlib", "sys", "os"], "internal_deps": []}, {"path": "backend\\app\\schemas\\monitor.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": [".base", "pydantic", "datetime"], "internal_deps": []}, {"path": "backend\\app\\week4\\integration\\week4_adapter.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["contextlib", "asyncio", "structlog"], "internal_deps": []}, {"path": "core\\health_check.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["sys", "time", "requests"], "internal_deps": []}, {"path": "core\\app\\schemas\\monitor.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": [".base", "pydantic", "datetime"], "internal_deps": []}, {"path": "dev-tools\\cleanup\\code_cleaner.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["pathlib", "re", "os"], "internal_deps": []}, {"path": "dev-tools\\mock\\mock_utils.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["pathlib", "json", "os"], "internal_deps": []}, {"path": "scripts\\batch_initialize_next.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["pathlib", "json", "datetime"], "internal_deps": []}, {"path": "scripts\\clean_debug_code.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["logging", "re", "pathlib"], "internal_deps": []}, {"path": "scripts\\quick_migrate.py", "score": 0.0375, "dependencies": 3, "type": "python", "imports": ["datetime", "subprocess", "sys"], "internal_deps": []}, {"path": "analyze_project_stats.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["collections", "os"], "internal_deps": []}, {"path": "batch_flake8_fix.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["glob", "subprocess"], "internal_deps": []}, {"path": "check_code_quality.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["subprocess", "os"], "internal_deps": []}, {"path": "final_verification.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["pathlib", "subprocess"], "internal_deps": []}, {"path": "three_step_fix.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["subprocess", "time"], "internal_deps": []}, {"path": "verify_startup.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["pathlib", "sys"], "internal_deps": []}, {"path": "backend\\app\\schemas\\base.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["pydantic", "datetime"], "internal_deps": []}, {"path": "backend\\app\\schemas\\realtime_log.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["pydantic", "enum"], "internal_deps": []}, {"path": "core\\app\\schemas\\base.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["pydantic", "datetime"], "internal_deps": []}, {"path": "core\\app\\schemas\\realtime_log.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["pydantic", "enum"], "internal_deps": []}, {"path": "frontend\\js\\element-plus.js", "score": 0.025, "dependencies": 2, "type": "javascript", "imports": ["util", "vue"], "internal_deps": []}, {"path": "new-system\\modules\\purchase_order\\models.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["sqlalchemy.ext.declarative", "datetime"], "internal_deps": []}, {"path": "new-system\\modules\\purchase_order\\routes.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": [".service", "datetime"], "internal_deps": []}, {"path": "scripts\\clean_hardcoded_data.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["pathlib", "re"], "internal_deps": []}, {"path": "scripts\\complete_modules.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["subprocess", "sys"], "internal_deps": []}, {"path": "scripts\\graveyard_safety_analyzer.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["pathlib", "json"], "internal_deps": []}, {"path": "scripts\\manual_cleanup.py", "score": 0.025, "dependencies": 2, "type": "python", "imports": ["pathlib", "shutil"], "internal_deps": []}, {"path": "start_quick_test.py", "score": 0.015, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\backup\\modules\\物料档案批量详情查询\\__init__.py", "score": 0.015, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\backup\\modules\\现存量报表查询\\__init__.py", "score": 0.015, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "check_sonarqube_status.py", "score": 0.0125, "dependencies": 1, "type": "python", "imports": ["pathlib"], "internal_deps": []}, {"path": "quick_status_check.py", "score": 0.0125, "dependencies": 1, "type": "python", "imports": ["subprocess"], "internal_deps": []}, {"path": "backend\\app\\schemas\\database.py", "score": 0.0125, "dependencies": 1, "type": "python", "imports": ["pydantic"], "internal_deps": []}, {"path": "backend\\app\\schemas\\sync.py", "score": 0.0125, "dependencies": 1, "type": "python", "imports": ["datetime"], "internal_deps": []}, {"path": "core\\app\\schemas\\database.py", "score": 0.0125, "dependencies": 1, "type": "python", "imports": ["pydantic"], "internal_deps": []}, {"path": "core\\app\\schemas\\sync.py", "score": 0.0125, "dependencies": 1, "type": "python", "imports": ["datetime"], "internal_deps": []}, {"path": "new-system\\modules\\purchase_order\\schema.py", "score": 0.0125, "dependencies": 1, "type": "python", "imports": ["datetime"], "internal_deps": []}, {"path": "new-system\\modules\\产品入库单列表查询\\api.py", "score": 0.0125, "dependencies": 1, "type": "python", "imports": ["<PERSON><PERSON><PERSON>"], "internal_deps": []}, {"path": "new-system\\modules\\产品入库单列表查询\\models.py", "score": 0.0125, "dependencies": 1, "type": "python", "imports": ["pydantic"], "internal_deps": []}, {"path": "tests\\test_md_to_json_converter.py", "score": 0.008749999999999999, "dependencies": 7, "type": "python", "imports": ["pytest", "tempfile", "tools.md_to_json_converter", "sys", "os", "pathlib", "unittest.mock"], "internal_deps": []}, {"path": "scripts\\cleanup_test_code.py", "score": 0.0075, "dependencies": 6, "type": "python", "imports": ["os", "sys", "re", "json", "datetime", "pathlib"], "internal_deps": []}, {"path": "tests\\test_rollback_scripts.py", "score": 0.0075, "dependencies": 6, "type": "python", "imports": ["pytest", "scripts.rollback_batch_writes", "pyodbc", "sys", "datetime", "pathlib"], "internal_deps": []}, {"path": "unused_import_checker.py", "score": 0.0025000000000000005, "dependencies": 2, "type": "python", "imports": ["ast", "os"], "internal_deps": []}, {"path": "ai_code_review_system.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "automation_validator.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "cicd_builder_simple.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "comprehensive_code_fixer.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "comprehensive_production_test.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "core_mvp_extractor.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "database_enhancement_demo.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "day3_final_test.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "day4_proxy_builder.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "day5_final_validator.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "final_project_fixer.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "mvp_launcher.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "production_test_runner.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "project_health_checker.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "proxy_strangler.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "python_version_diagnostic.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "quick_test.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "real_data_full_test.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "refactor_ys_api_client.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "setup_ide_security_plugins.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "setup_test_env.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "shit_mountain_mapper.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "status_check.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "systematic_migration_starter.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "system_integration_final.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "test_anti_duplicate_system.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "test_backend.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "test_day3_core.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "test_day3_offline.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "test_day4_proxy.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "test_frontend.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "test_imports.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "test_modules_direct.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "test_mvp.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "test_proxy.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "universal_code_quality_fixer.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "verify_enhancements.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "verify_field_fetching.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "week1_completion_report.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "week1_completion_week2_planning.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "week1_quick_start.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "week1_week2_integration_demo.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "week2_completion_report.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "week3_integration_final.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "backend\\test_backend_automation.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "frontend\\field-deduplication-enhancer.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\input-validation-enhancer.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\api-unified.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\baseline-save.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\element-plus-icons.iife.min.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\field-list-display.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\field-statistics.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\notification-system.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\performance-optimizer.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\realtime-log.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\unified-components.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\vue.global.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\common\\api-client.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\common\\error-handler.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\common\\field-renderer.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\common\\field-utils.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\common\\smart-logger.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\common\\smart-retry-optimizer.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\common\\standard-error-logger.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\common\\test-data.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\common\\user-error-notifier.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\common\\validation-utils.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\core\\app-bootstrap.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\core\\component-migration-tool.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "frontend\\js\\core\\page-migration-assistant.js", "score": 0.0, "dependencies": 0, "type": "javascript", "imports": [], "internal_deps": []}, {"path": "migration\\week1_analysis\\tests\\test_batch_processor.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "migration\\week1_analysis\\tests\\test_data_writer_core.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "migration\\week2_analysis\\refactored\\conflict_resolver.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "migration\\week2_analysis\\refactored\\performance_monitor.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "migration\\week2_analysis\\refactored\\sync_scheduler.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "migration\\week3_analysis\\refactored\\data_transformer.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "migration\\week3_analysis\\refactored\\request_builder.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "migration\\week3_analysis\\refactored\\response_parser.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\backup\\modules\\现存量报表查询\\api.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\业务日志\\api.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\委外订单列表\\api.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\委外订单列表\\models.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\生产订单列表查询\\api.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\生产订单列表查询\\models.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\请购单列表查询\\api.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\请购单列表查询\\models.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\销售出库列表查询\\api.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\销售订单\\api.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "new-system\\modules\\需求计划\\api.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "scripts\\diagnose_migration.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "scripts\\fix_css_paths.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "scripts\\fix_migrated_paths.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "scripts\\module_functional_test.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "scripts\\phase3_verification.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "scripts\\reliable_server.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "scripts\\test_elk_connection.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "scripts\\test_server.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "scripts\\validate_deployment.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "scripts\\verify_fixes.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "tests\\locust_stress_test.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "tests\\simple_test.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "tests\\test_baseline_api.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "tests\\test_week2_validation.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "tests\\week2_completion_report.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "tests\\module_migration\\test_generator.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}, {"path": "tools\\error_handling_load_test.py", "score": 0.0, "dependencies": 0, "type": "python", "imports": [], "internal_deps": []}]}, "refactoring_plan": {"timestamp": "2025-08-06T22:57:34.510165", "summary": {"total_files": 411, "core_files": 0, "important_files": 9, "optional_files": 112, "junk_files": 290}, "actions": {"keep": [], "refactor": [{"file": "scripts\\port_manager.py", "reason": "重要文件 (得分: 0.55)", "action": "refactor_secondary"}, {"file": "backend\\app\\services\\auto_recovery_manager_enhanced.py", "reason": "重要文件 (得分: 0.54)", "action": "refactor_secondary"}, {"file": "backend\\app\\services\\enhanced_ys_api_client.py", "reason": "重要文件 (得分: 0.54)", "action": "refactor_secondary"}, {"file": "core\\app\\services\\auto_recovery_manager_enhanced.py", "reason": "重要文件 (得分: 0.54)", "action": "refactor_secondary"}, {"file": "backend\\app\\services\\data_write_manager.py", "reason": "重要文件 (得分: 0.50)", "action": "refactor_secondary"}, {"file": "backend\\app\\services\\integrated_ys_api_client.py", "reason": "重要文件 (得分: 0.50)", "action": "refactor_secondary"}, {"file": "backend\\app\\services\\ys_api_client.py", "reason": "重要文件 (得分: 0.50)", "action": "refactor_secondary"}, {"file": "core\\app\\services\\data_write_manager.py", "reason": "重要文件 (得分: 0.50)", "action": "refactor_secondary"}, {"file": "migration\\week3_analysis\\refactored\\endpoint_manager.py", "reason": "重要文件 (得分: 0.50)", "action": "refactor_secondary"}], "review": [{"file": "backend\\app\\services\\config_persistence_service.py", "reason": "可选文件 (得分: 0.46)", "action": "manual_review"}, {"file": "core\\app\\services\\config_persistence_service.py", "reason": "可选文件 (得分: 0.46)", "action": "manual_review"}, {"file": "month2_config\\config_rollback\\manager.py", "reason": "可选文件 (得分: 0.46)", "action": "manual_review"}, {"file": "backend\\app\\services\\auto_sync_scheduler.py", "reason": "可选文件 (得分: 0.45)", "action": "manual_review"}, {"file": "backend\\app\\services\\field_config_service.py", "reason": "可选文件 (得分: 0.45)", "action": "manual_review"}, {"file": "backend\\app\\services\\__init__.py", "reason": "可选文件 (得分: 0.45)", "action": "manual_review"}, {"file": "core\\app\\services\\auto_sync_scheduler.py", "reason": "可选文件 (得分: 0.45)", "action": "manual_review"}, {"file": "core\\app\\services\\field_config_service.py", "reason": "可选文件 (得分: 0.45)", "action": "manual_review"}, {"file": "core\\app\\services\\__init__.py", "reason": "可选文件 (得分: 0.45)", "action": "manual_review"}, {"file": "core\\backend\\app\\services\\__init__.py", "reason": "可选文件 (得分: 0.45)", "action": "manual_review"}, {"file": "month2_config\\two_step_save\\manager.py", "reason": "可选文件 (得分: 0.45)", "action": "manual_review"}, {"file": "backend\\app\\services\\database_table_manager.py", "reason": "可选文件 (得分: 0.44)", "action": "manual_review"}, {"file": "core\\app\\services\\database_table_manager.py", "reason": "可选文件 (得分: 0.44)", "action": "manual_review"}, {"file": "install_windows_service.py", "reason": "可选文件 (得分: 0.42)", "action": "manual_review"}, {"file": "backend\\app\\services\\zero_downtime_implementation.py", "reason": "可选文件 (得分: 0.41)", "action": "manual_review"}, {"file": "core\\app\\services\\zero_downtime_implementation.py", "reason": "可选文件 (得分: 0.41)", "action": "manual_review"}, {"file": "backend\\start_server_fixed.py", "reason": "可选文件 (得分: 0.40)", "action": "manual_review"}, {"file": "backend\\app\\services\\database_manager.py", "reason": "可选文件 (得分: 0.39)", "action": "manual_review"}, {"file": "backend\\app\\services\\fast_sync_service.py", "reason": "可选文件 (得分: 0.39)", "action": "manual_review"}, {"file": "core\\app\\services\\database_manager.py", "reason": "可选文件 (得分: 0.39)", "action": "manual_review"}, {"file": "core\\app\\services\\fast_sync_service.py", "reason": "可选文件 (得分: 0.39)", "action": "manual_review"}, {"file": "backend\\app\\services\\advanced_data_transformer.py", "reason": "可选文件 (得分: 0.38)", "action": "manual_review"}, {"file": "backend\\app\\services\\enhanced_authenticator.py", "reason": "可选文件 (得分: 0.38)", "action": "manual_review"}, {"file": "backend\\app\\services\\excel_field_matcher_pretranslated.py", "reason": "可选文件 (得分: 0.38)", "action": "manual_review"}, {"file": "backend\\app\\services\\field_extractor_service.py", "reason": "可选文件 (得分: 0.38)", "action": "manual_review"}, {"file": "backend\\app\\services\\material_master_scheduler.py", "reason": "可选文件 (得分: 0.38)", "action": "manual_review"}, {"file": "backend\\app\\services\\token_service.py", "reason": "可选文件 (得分: 0.38)", "action": "manual_review"}, {"file": "backend\\app\\services\\unified_field_service.py", "reason": "可选文件 (得分: 0.38)", "action": "manual_review"}, {"file": "backend\\app\\week4\\concurrent\\async_task_manager.py", "reason": "可选文件 (得分: 0.38)", "action": "manual_review"}, {"file": "core\\app\\services\\excel_field_matcher_pretranslated.py", "reason": "可选文件 (得分: 0.38)", "action": "manual_review"}, {"file": "core\\app\\services\\field_extractor_service.py", "reason": "可选文件 (得分: 0.38)", "action": "manual_review"}, {"file": "core\\app\\services\\token_service.py", "reason": "可选文件 (得分: 0.38)", "action": "manual_review"}, {"file": "scripts\\port_manager_clean.py", "reason": "可选文件 (得分: 0.38)", "action": "manual_review"}, {"file": "backend\\app\\api\\v1\\field_config_api.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "core\\app\\api\\v1\\field_config_api.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "backend\\app\\services\\enhanced_error_handler.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "backend\\app\\services\\enhanced_response_processor.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "backend\\app\\services\\excel_field_matcher.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "backend\\app\\services\\intelligent_field_mapper.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "backend\\app\\services\\maintenance_manager.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "backend\\app\\services\\realtime_log_service.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "backend\\app\\services\\response_format_standardizer.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "backend\\app\\services\\retry_helper.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "backend\\app\\services\\robust_json_parser.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "core\\app\\services\\excel_field_matcher.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "core\\app\\services\\intelligent_field_mapper.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "core\\app\\services\\maintenance_manager.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "core\\app\\services\\retry_helper.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "core\\app\\services\\robust_json_parser.py", "reason": "可选文件 (得分: 0.36)", "action": "manual_review"}, {"file": "backend\\app\\api\\__init__.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "backend\\app\\api\\v1\\__init__.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "backend\\app\\core\\database_manager.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "backend\\app\\services\\data_processor.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "backend\\app\\services\\data_quality_inspector.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "backend\\app\\services\\log_service.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "backend\\app\\services\\md_parser.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "backend\\app\\services\\sync_status_manager.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "backend\\app\\services\\task_service.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "backend\\app\\services\\unified_field_manager.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "core\\app\\api\\__init__.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "core\\app\\api\\v1\\__init__.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "core\\app\\core\\database_manager.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "core\\app\\services\\data_processor.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "core\\app\\services\\log_service.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "core\\app\\services\\md_parser.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "core\\app\\services\\sync_status_manager.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "core\\app\\services\\task_service.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "core\\app\\services\\unified_field_manager.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "core\\backend\\app\\api\\__init__.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "core\\backend\\app\\api\\v1\\__init__.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "month2_database\\table_creation\\manager.py", "reason": "可选文件 (得分: 0.35)", "action": "manual_review"}, {"file": "backend\\app\\api\\v1\\config.py", "reason": "可选文件 (得分: 0.34)", "action": "manual_review"}, {"file": "backend\\app\\services\\business_translation_rules.py", "reason": "可选文件 (得分: 0.34)", "action": "manual_review"}, {"file": "backend\\app\\services\\database_enhancement.py", "reason": "可选文件 (得分: 0.34)", "action": "manual_review"}, {"file": "backend\\app\\services\\enhanced_json_field_matcher.py", "reason": "可选文件 (得分: 0.34)", "action": "manual_review"}, {"file": "backend\\app\\services\\field_validation_service.py", "reason": "可选文件 (得分: 0.34)", "action": "manual_review"}, {"file": "backend\\app\\services\\field_value_mapping_service.py", "reason": "可选文件 (得分: 0.34)", "action": "manual_review"}, {"file": "backend\\app\\services\\status_mapping_service.py", "reason": "可选文件 (得分: 0.34)", "action": "manual_review"}, {"file": "core\\app\\services\\business_translation_rules.py", "reason": "可选文件 (得分: 0.34)", "action": "manual_review"}, {"file": "core\\app\\services\\enhanced_json_field_matcher.py", "reason": "可选文件 (得分: 0.34)", "action": "manual_review"}, {"file": "core\\app\\services\\field_validation_service.py", "reason": "可选文件 (得分: 0.34)", "action": "manual_review"}, {"file": "core\\app\\services\\field_value_mapping_service.py", "reason": "可选文件 (得分: 0.34)", "action": "manual_review"}, {"file": "core\\app\\services\\status_mapping_service.py", "reason": "可选文件 (得分: 0.34)", "action": "manual_review"}, {"file": "new-system\\modules\\purchase_order\\service.py", "reason": "可选文件 (得分: 0.34)", "action": "manual_review"}, {"file": "backend\\app\\api\\v1\\unified_field_config.py", "reason": "可选文件 (得分: 0.33)", "action": "manual_review"}, {"file": "backend\\app\\services\\field_analysis_service.py", "reason": "可选文件 (得分: 0.33)", "action": "manual_review"}, {"file": "backend\\app\\services\\monitor_service.py", "reason": "可选文件 (得分: 0.33)", "action": "manual_review"}, {"file": "core\\app\\api\\v1\\config.py", "reason": "可选文件 (得分: 0.33)", "action": "manual_review"}, {"file": "core\\app\\api\\v1\\unified_field_config.py", "reason": "可选文件 (得分: 0.33)", "action": "manual_review"}, {"file": "core\\app\\services\\field_analysis_service.py", "reason": "可选文件 (得分: 0.33)", "action": "manual_review"}, {"file": "core\\app\\services\\monitor_service.py", "reason": "可选文件 (得分: 0.33)", "action": "manual_review"}, {"file": "backend\\app\\api\\v1\\excel_translation.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "backend\\app\\api\\v1\\monitor.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "core\\app\\api\\v1\\excel_translation.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "core\\app\\api\\v1\\monitor.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "refactor_data_write_manager.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "refactor_fast_sync_service.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "backend\\app\\services\\async_task_manager.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "backend\\app\\services\\concurrent_processor.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "backend\\app\\services\\connection_pool_optimizer.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "backend\\app\\services\\database_connection_pool.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "backend\\app\\services\\enhanced_connection_pool.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "backend\\app\\services\\enhanced_response_parser.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "backend\\app\\services\\enhanced_transaction_manager.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "backend\\app\\services\\load_balancer.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "backend\\app\\services\\standard_request_builder.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "backend\\app\\services\\transaction_manager.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "frontend\\js\\core\\component-manager.js", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "frontend\\js\\core\\initialization-manager.js", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "migration\\week2_analysis\\refactored\\refactored_fast_sync_service.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}, {"file": "new-system\\modules\\产品入库单列表查询\\service.py", "reason": "可选文件 (得分: 0.30)", "action": "manual_review"}], "delete": [{"file": "backend\\app\\api\\v1\\sync.py", "reason": "低价值文件 (得分: 0.29)", "action": "safe_delete"}, {"file": "core\\app\\main.py", "reason": "低价值文件 (得分: 0.29)", "action": "safe_delete"}, {"file": "core\\app\\api\\v1\\sync.py", "reason": "低价值文件 (得分: 0.29)", "action": "safe_delete"}, {"file": "backend\\app\\main.py", "reason": "低价值文件 (得分: 0.28)", "action": "safe_delete"}, {"file": "backend\\app\\api\\v1\\enhanced_sync.py", "reason": "低价值文件 (得分: 0.26)", "action": "safe_delete"}, {"file": "backend\\app\\api\\v1\\realtime_logs.py", "reason": "低价值文件 (得分: 0.26)", "action": "safe_delete"}, {"file": "core\\app\\api\\v1\\enhanced_sync.py", "reason": "低价值文件 (得分: 0.26)", "action": "safe_delete"}, {"file": "core\\app\\api\\v1\\realtime_logs.py", "reason": "低价值文件 (得分: 0.26)", "action": "safe_delete"}, {"file": "backend\\start_simple.py", "reason": "低价值文件 (得分: 0.25)", "action": "safe_delete"}, {"file": "backend\\app\\api\\v1\\database.py", "reason": "低价值文件 (得分: 0.25)", "action": "safe_delete"}, {"file": "core\\app\\api\\v1\\database.py", "reason": "低价值文件 (得分: 0.24)", "action": "safe_delete"}, {"file": "frontend\\start_frontend_clean.py", "reason": "低价值文件 (得分: 0.24)", "action": "safe_delete"}, {"file": "run_comprehensive_check.py", "reason": "低价值文件 (得分: 0.22)", "action": "safe_delete"}, {"file": "backend\\app\\api\\v1\\auth.py", "reason": "低价值文件 (得分: 0.21)", "action": "safe_delete"}, {"file": "backend\\app\\api\\v1\\database_health.py", "reason": "低价值文件 (得分: 0.21)", "action": "safe_delete"}, {"file": "backend\\app\\api\\v1\\maintenance.py", "reason": "低价值文件 (得分: 0.21)", "action": "safe_delete"}, {"file": "backend\\app\\api\\v1\\sync_status.py", "reason": "低价值文件 (得分: 0.21)", "action": "safe_delete"}, {"file": "backend\\app\\api\\v1\\tasks.py", "reason": "低价值文件 (得分: 0.21)", "action": "safe_delete"}, {"file": "core\\app\\api\\v1\\auth.py", "reason": "低价值文件 (得分: 0.21)", "action": "safe_delete"}, {"file": "core\\app\\api\\v1\\database_health.py", "reason": "低价值文件 (得分: 0.21)", "action": "safe_delete"}, {"file": "core\\app\\api\\v1\\maintenance.py", "reason": "低价值文件 (得分: 0.21)", "action": "safe_delete"}, {"file": "core\\app\\api\\v1\\sync_status.py", "reason": "低价值文件 (得分: 0.21)", "action": "safe_delete"}, {"file": "core\\app\\api\\v1\\tasks.py", "reason": "低价值文件 (得分: 0.21)", "action": "safe_delete"}, {"file": "backend\\start_server_clean.py", "reason": "低价值文件 (得分: 0.21)", "action": "safe_delete"}, {"file": "backend\\start_server.py", "reason": "低价值文件 (得分: 0.20)", "action": "safe_delete"}, {"file": "core\\main.py", "reason": "低价值文件 (得分: 0.20)", "action": "safe_delete"}, {"file": "start_month1_validation.py", "reason": "低价值文件 (得分: 0.19)", "action": "safe_delete"}, {"file": "start_month2.py", "reason": "低价值文件 (得分: 0.17)", "action": "safe_delete"}, {"file": "backend\\app\\week4\\__init__.py", "reason": "低价值文件 (得分: 0.17)", "action": "safe_delete"}, {"file": "backend\\app\\core\\config.py", "reason": "低价值文件 (得分: 0.16)", "action": "safe_delete"}, {"file": "frontend\\start_frontend_fixed.py", "reason": "低价值文件 (得分: 0.16)", "action": "safe_delete"}, {"file": "scripts\\migrate_purchase_order.py", "reason": "低价值文件 (得分: 0.16)", "action": "safe_delete"}, {"file": "config_environmentizer.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "config_environmentizer_clean.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "core\\app\\core\\config.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "backend\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "backend\\app\\core\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "backend\\app\\schemas\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "backend\\app\\week4\\concurrent\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "core\\app\\core\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "core\\app\\schemas\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "core\\backend\\start_server.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "core\\backend\\app\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "core\\backend\\app\\core\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "frontend\\start_frontend.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "new-system\\modules\\purchase_order\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "new-system\\modules\\业务日志\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "new-system\\modules\\产品入库单列表查询\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "new-system\\modules\\委外入库列表查询\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "new-system\\modules\\委外申请列表查询\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "new-system\\modules\\委外订单列表\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "new-system\\modules\\物料档案批量详情查询\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "new-system\\modules\\生产订单列表查询\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "new-system\\modules\\请购单列表查询\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "new-system\\modules\\销售出库列表查询\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "new-system\\modules\\销售订单\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "new-system\\modules\\需求计划\\__init__.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "scripts\\database_dual_writer.py", "reason": "低价值文件 (得分: 0.15)", "action": "safe_delete"}, {"file": "scripts\\add_api_config.py", "reason": "低价值文件 (得分: 0.14)", "action": "safe_delete"}, {"file": "violent_cleanup.py", "reason": "低价值文件 (得分: 0.12)", "action": "safe_delete"}, {"file": "project_health_check.py", "reason": "低价值文件 (得分: 0.11)", "action": "safe_delete"}, {"file": "backend\\app\\schemas\\config.py", "reason": "低价值文件 (得分: 0.11)", "action": "safe_delete"}, {"file": "core\\app\\schemas\\config.py", "reason": "低价值文件 (得分: 0.11)", "action": "safe_delete"}, {"file": "scripts\\rollback_batch_writes.py", "reason": "低价值文件 (得分: 0.11)", "action": "safe_delete"}, {"file": "build_production_package.py", "reason": "低价值文件 (得分: 0.10)", "action": "safe_delete"}, {"file": "quick_health_check.py", "reason": "低价值文件 (得分: 0.10)", "action": "safe_delete"}, {"file": "backend\\app\\strangler_proxy\\config.py", "reason": "低价值文件 (得分: 0.10)", "action": "safe_delete"}, {"file": "backend\\app\\week4\\concurrent\\connection_pool_optimizer.py", "reason": "低价值文件 (得分: 0.10)", "action": "safe_delete"}, {"file": "backend\\app\\week4\\concurrent\\load_balancer.py", "reason": "低价值文件 (得分: 0.10)", "action": "safe_delete"}, {"file": "frontend\\unified-field-config-fix.js", "reason": "低价值文件 (得分: 0.10)", "action": "safe_delete"}, {"file": "frontend\\js\\api-config-fix.js", "reason": "低价值文件 (得分: 0.10)", "action": "safe_delete"}, {"file": "frontend\\js\\api-config.js", "reason": "低价值文件 (得分: 0.10)", "action": "safe_delete"}, {"file": "frontend\\js\\user-config-save.js", "reason": "低价值文件 (得分: 0.10)", "action": "safe_delete"}, {"file": "frontend\\js\\common\\elk-integration-config.js", "reason": "低价值文件 (得分: 0.10)", "action": "safe_delete"}, {"file": "auto_fix_comprehensive_issues.py", "reason": "低价值文件 (得分: 0.09)", "action": "safe_delete"}, {"file": "cicd_pipeline_builder.py", "reason": "低价值文件 (得分: 0.09)", "action": "safe_delete"}, {"file": "cicd_pipeline_builder_optimized.py", "reason": "低价值文件 (得分: 0.09)", "action": "safe_delete"}, {"file": "port_locker.py", "reason": "低价值文件 (得分: 0.09)", "action": "safe_delete"}, {"file": "production_readiness_report.py", "reason": "低价值文件 (得分: 0.09)", "action": "safe_delete"}, {"file": "backend\\app\\main_original.py", "reason": "低价值文件 (得分: 0.09)", "action": "safe_delete"}, {"file": "backend\\app\\core\\database_connection_pool.py", "reason": "低价值文件 (得分: 0.09)", "action": "safe_delete"}, {"file": "core\\app\\main_original.py", "reason": "低价值文件 (得分: 0.09)", "action": "safe_delete"}, {"file": "core\\app\\core\\database_connection_pool.py", "reason": "低价值文件 (得分: 0.09)", "action": "safe_delete"}, {"file": "tools\\md_to_json_converter.py", "reason": "低价值文件 (得分: 0.09)", "action": "safe_delete"}, {"file": "execute_task_checklist.py", "reason": "低价值文件 (得分: 0.07)", "action": "safe_delete"}, {"file": "file_sentinel.py", "reason": "低价值文件 (得分: 0.07)", "action": "safe_delete"}, {"file": "systematic_duplicate_detector.py", "reason": "低价值文件 (得分: 0.07)", "action": "safe_delete"}, {"file": "backend\\app\\core\\exceptions.py", "reason": "低价值文件 (得分: 0.07)", "action": "safe_delete"}, {"file": "core\\app\\core\\exceptions.py", "reason": "低价值文件 (得分: 0.07)", "action": "safe_delete"}, {"file": "scripts\\auto_migration.py", "reason": "低价值文件 (得分: 0.07)", "action": "safe_delete"}, {"file": "scripts\\auto_migration_pipeline.py", "reason": "低价值文件 (得分: 0.07)", "action": "safe_delete"}, {"file": "scripts\\database_dual_writer_simple.py", "reason": "低价值文件 (得分: 0.07)", "action": "safe_delete"}, {"file": "scripts\\quick_batch_migrate.py", "reason": "低价值文件 (得分: 0.07)", "action": "safe_delete"}, {"file": "scripts\\simple_migrator.py", "reason": "低价值文件 (得分: 0.07)", "action": "safe_delete"}, {"file": "auto_project_cleanup.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "batch_code_quality_fix.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "fix_all_flake8.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "fix_issues.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "machine_cleanup.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "pollution_guard.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "verify_module.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "backend\\app\\core\\optimized_retry.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "backend\\app\\middleware\\access_log.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "backend\\app\\week4\\concurrent\\concurrent_processor.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "core\\app\\core\\optimized_retry.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "core\\app\\middleware\\access_log.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "scripts\\health_check.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "scripts\\migrate_产品入库单列表查询.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "scripts\\migrate_产品入库单列表查询_fixed.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "scripts\\migrate_生产订单列表查询.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "scripts\\migrate_请购单列表查询.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "scripts\\module_tracker.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "scripts\\module_tracker_simple.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "scripts\\ultra_simple_migrate.py", "reason": "低价值文件 (得分: 0.06)", "action": "safe_delete"}, {"file": "analyze_dependencies.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "batch_validate_modules.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "check_naming_conflicts.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "fix_sonarqube_issues.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "fix_task_issues.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "fix_xml_files.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "function_duplicate_checker.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "quick_health_check_fixed.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "remaining_issues_fixer.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "smart_duplicate_cleaner.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "smart_file_creator.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "sonarqube_cleanup.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "verify_fixes.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "backend\\app\\core\\code_quality.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "backend\\app\\core\\database.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "core\\app\\core\\code_quality.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "core\\app\\core\\database.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "scripts\\batch_init_modules.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "scripts\\final_status_check.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "scripts\\migrate_采购入库单列表.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "scripts\\next_module.py", "reason": "低价值文件 (得分: 0.05)", "action": "safe_delete"}, {"file": "final_batch.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "fix_build_script.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "fix_execute_task_script.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "flake8_stats.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "generate_stats.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "quick_syntax_fix.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "verify_startup_clean.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "backend\\app\\schemas\\monitor.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "backend\\app\\week4\\integration\\week4_adapter.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "core\\health_check.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "core\\app\\schemas\\monitor.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "dev-tools\\cleanup\\code_cleaner.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "dev-tools\\mock\\mock_utils.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "scripts\\batch_initialize_next.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "scripts\\clean_debug_code.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "scripts\\quick_migrate.py", "reason": "低价值文件 (得分: 0.04)", "action": "safe_delete"}, {"file": "analyze_project_stats.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "batch_flake8_fix.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "check_code_quality.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "final_verification.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "three_step_fix.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "verify_startup.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "backend\\app\\schemas\\base.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "backend\\app\\schemas\\realtime_log.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "core\\app\\schemas\\base.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "core\\app\\schemas\\realtime_log.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "frontend\\js\\element-plus.js", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "new-system\\modules\\purchase_order\\models.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "new-system\\modules\\purchase_order\\routes.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "scripts\\clean_hardcoded_data.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "scripts\\complete_modules.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "scripts\\graveyard_safety_analyzer.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "scripts\\manual_cleanup.py", "reason": "低价值文件 (得分: 0.03)", "action": "safe_delete"}, {"file": "start_quick_test.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "new-system\\backup\\modules\\物料档案批量详情查询\\__init__.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "new-system\\backup\\modules\\现存量报表查询\\__init__.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "check_sonarqube_status.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "quick_status_check.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "backend\\app\\schemas\\database.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "backend\\app\\schemas\\sync.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "core\\app\\schemas\\database.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "core\\app\\schemas\\sync.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "new-system\\modules\\purchase_order\\schema.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "new-system\\modules\\产品入库单列表查询\\api.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "new-system\\modules\\产品入库单列表查询\\models.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "tests\\test_md_to_json_converter.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "scripts\\cleanup_test_code.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "tests\\test_rollback_scripts.py", "reason": "低价值文件 (得分: 0.01)", "action": "safe_delete"}, {"file": "unused_import_checker.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "ai_code_review_system.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "automation_validator.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "cicd_builder_simple.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "comprehensive_code_fixer.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "comprehensive_production_test.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "core_mvp_extractor.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "database_enhancement_demo.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "day3_final_test.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "day4_proxy_builder.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "day5_final_validator.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "final_project_fixer.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "mvp_launcher.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "production_test_runner.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "project_health_checker.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "proxy_strangler.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "python_version_diagnostic.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "quick_test.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "real_data_full_test.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "refactor_ys_api_client.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "setup_ide_security_plugins.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "setup_test_env.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "shit_mountain_mapper.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "status_check.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "systematic_migration_starter.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "system_integration_final.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "test_anti_duplicate_system.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "test_backend.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "test_day3_core.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "test_day3_offline.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "test_day4_proxy.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "test_frontend.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "test_imports.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "test_modules_direct.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "test_mvp.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "test_proxy.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "universal_code_quality_fixer.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "verify_enhancements.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "verify_field_fetching.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "week1_completion_report.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "week1_completion_week2_planning.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "week1_quick_start.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "week1_week2_integration_demo.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "week2_completion_report.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "week3_integration_final.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "backend\\test_backend_automation.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\field-deduplication-enhancer.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\input-validation-enhancer.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\api-unified.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\baseline-save.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\element-plus-icons.iife.min.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\field-list-display.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\field-statistics.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\notification-system.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\performance-optimizer.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\realtime-log.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\unified-components.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\vue.global.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\common\\api-client.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\common\\error-handler.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\common\\field-renderer.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\common\\field-utils.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\common\\smart-logger.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\common\\smart-retry-optimizer.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\common\\standard-error-logger.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\common\\test-data.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\common\\user-error-notifier.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\common\\validation-utils.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\core\\app-bootstrap.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\core\\component-migration-tool.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "frontend\\js\\core\\page-migration-assistant.js", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "migration\\week1_analysis\\tests\\test_batch_processor.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "migration\\week1_analysis\\tests\\test_data_writer_core.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "migration\\week2_analysis\\refactored\\conflict_resolver.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "migration\\week2_analysis\\refactored\\performance_monitor.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "migration\\week2_analysis\\refactored\\sync_scheduler.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "migration\\week3_analysis\\refactored\\data_transformer.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "migration\\week3_analysis\\refactored\\request_builder.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "migration\\week3_analysis\\refactored\\response_parser.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "new-system\\backup\\modules\\现存量报表查询\\api.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "new-system\\modules\\业务日志\\api.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "new-system\\modules\\委外订单列表\\api.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "new-system\\modules\\委外订单列表\\models.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "new-system\\modules\\生产订单列表查询\\api.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "new-system\\modules\\生产订单列表查询\\models.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "new-system\\modules\\请购单列表查询\\api.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "new-system\\modules\\请购单列表查询\\models.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "new-system\\modules\\销售出库列表查询\\api.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "new-system\\modules\\销售订单\\api.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "new-system\\modules\\需求计划\\api.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "scripts\\diagnose_migration.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "scripts\\fix_css_paths.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "scripts\\fix_migrated_paths.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "scripts\\module_functional_test.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "scripts\\phase3_verification.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "scripts\\reliable_server.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "scripts\\test_elk_connection.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "scripts\\test_server.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "scripts\\validate_deployment.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "scripts\\verify_fixes.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "tests\\locust_stress_test.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "tests\\simple_test.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "tests\\test_baseline_api.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "tests\\test_week2_validation.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "tests\\week2_completion_report.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "tests\\module_migration\\test_generator.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}, {"file": "tools\\error_handling_load_test.py", "reason": "低价值文件 (得分: 0.00)", "action": "safe_delete"}]}, "priority_order": [{"file": "scripts\\port_manager.py", "priority": 2, "score": 0.55}, {"file": "backend\\app\\services\\auto_recovery_manager_enhanced.py", "priority": 2, "score": 0.5375}, {"file": "backend\\app\\services\\enhanced_ys_api_client.py", "priority": 2, "score": 0.5375}, {"file": "core\\app\\services\\auto_recovery_manager_enhanced.py", "priority": 2, "score": 0.5375}, {"file": "backend\\app\\services\\data_write_manager.py", "priority": 2, "score": 0.5}, {"file": "backend\\app\\services\\integrated_ys_api_client.py", "priority": 2, "score": 0.5}, {"file": "backend\\app\\services\\ys_api_client.py", "priority": 2, "score": 0.5}, {"file": "core\\app\\services\\data_write_manager.py", "priority": 2, "score": 0.5}, {"file": "migration\\week3_analysis\\refactored\\endpoint_manager.py", "priority": 2, "score": 0.5}]}, "metadata": {"analyzer_version": "1.0", "analysis_time": "2025-08-06T22:57:34.511164", "workspace": "d:\\OneDrive\\Desktop\\YS-API程序\\v3"}}