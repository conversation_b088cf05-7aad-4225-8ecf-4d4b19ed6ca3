# SonarQube 排除规则配置
# 用于减少不必要的代码质量检查错误

# 排除测试和临时文件
sonar.exclusions=**/test_*.py,**/*_test.py,**/temp_cleanup/**,**/__pycache__/**,**/node_modules/**

# 排除第三方库和生成的文件
sonar.exclusions=**/libs/**,**/vendor/**,**/dist/**,**/build/**,**/element-plus.js,**/vue.global.js

# 排除开发工具文件
sonar.exclusions=**/week*.py,**/validate_*.py,**/verify_*.py,**/analyze_*.py,**/fix_*.py,**/cleanup_*.py

# 排除文档和配置文件
sonar.exclusions=**/*.md,**/*.txt,**/*.json,**/*.xml,**/*.ini,**/*.bat

# Python 特定排除
sonar.python.coverage.reportPaths=coverage.xml
sonar.python.xunit.reportPath=pytest.xml

# JavaScript 特定排除
sonar.javascript.file.suffixes=.js,.jsx
sonar.typescript.file.suffixes=.ts,.tsx
