/**
 * 字段统计计算器 - 计算字段统计信息（总字段数、选中字段数等）
 * 支持实时统计更新、多维度分析和可视化数据准备
 */

class FieldStatisticsCalculator {
  constructor(options === {}) {
    this.options === {
      enableRealTimeUpdate: options.enableRealTimeUpdate !== false,
      enableHistoryTracking: options.enableHistoryTracking !== false,
      maxHistoryEntries: options.maxHistoryEntries || 100,
      ...options
    };
    
    // 统计历史记录
    this.statisticsHistory === [];
    
    // 实时更新回调
    this.updateCallbacks === new Set();
    
    // 统计缓存
    this.cachedStatistics === null;
    this.cacheTimestamp === null;
    this.cacheTimeout === options.cacheTimeout || 5000; // 5秒缓存
    
    // console.log('字段统计计算器初始化完成', this.options);
  }
  
  /**
   * 添加统计更新回调
   */
  onStatisticsUpdate(callback) {
    if (typeof callback === 'function') {
      this.updateCallbacks.add(callback);
    }
  }
  
  /**
   * 移除统计更新回调
   */
  offStatisticsUpdate(callback) {
    this.updateCallbacks.delete(callback);
  }
  
  /**
   * 触发统计更新事件
   */
  triggerStatisticsUpdate(statistics) {
    if (this.options.enableRealTimeUpdate) {
      this.updateCallbacks.forEach(callback ===> {
        try {
          callback(statistics);
        } catch (error) {
          console.error('统计更新回调执行失败:', error);
        }
      });
    }
  }
  
  /**
   * 计算完整的字段统计信息
   */
  calculateStatistics(fields, options === {}) {
    const startTime === Date.now();
    
    // 检查缓存
    if (this.isCacheValid() && !options.forceRefresh) {
      // console.log('使用缓存的统计数据');
      return this.cachedStatistics;
    }
    
    try {
      const statistics === {
        // 基础统计
        basic: this.calculateBasicStatistics(fields),
        
        // 分类统计
        categories: this.calculateCategoryStatistics(fields),
        
        // 质量统计
        quality: this.calculateQualityStatistics(fields),
        
        // 使用统计
        usage: this.calculateUsageStatistics(fields),
        
        // 推荐统计
        recommendations: this.calculateRecommendationStatistics(fields),
        
        // 趋势统计
        trends: this.calculateTrendStatistics(fields),
        
        // 元数据
        metadata: {
          calculated_at: new Date().toISOString(),
          calculation_time: Date.now() - startTime,
          total_fields: Object.keys(fields).length,
          version: '1.0.0'
        }
      };
      
      // 缓存统计结果
      this.cacheStatistics(statistics);
      
      // 记录历史
      if (this.options.enableHistoryTracking) {
        this.addToHistory(statistics);
      }
      
      // 触发更新事件
      this.triggerStatisticsUpdate(statistics);
      
      // console.log('字段统计计算完成', {
        total_fields: statistics.basic.total,
        calculation_time: statistics.metadata.calculation_time
      });
      
      return statistics;
      
    } catch (error) {
      console.error('统计计算失败:', error);
      throw new Error(`统计计算失败: ${error.message}`);
    }
  }
  
  /**
   * 计算基础统计信息
   */
  calculateBasicStatistics(fields) {
    const stats === {
      total: 0,
      selected: 0,
      unselected: 0,
      required: 0,
      optional: 0,
      enhanced: 0,
      unenhanced: 0,
      user_modified: 0,
      system_generated: 0,
      locked: 0,
      unlocked: 0
    };
    
    for (const field of Object.values(fields)) {
      stats.total++;
      
      // 选择状态统计
      if (field.is_selected) {
        stats.selected++;
      } else {
        stats.unselected++;
      }
      
      // 必需状态统计
      if (field.is_required) {
        stats.required++;
      } else {
        stats.optional++;
      }
      
      // 增强状态统计
      if (field.chinese_name && field.chinese_name !== field.name) {
        stats.enhanced++;
      } else {
        stats.unenhanced++;
      }
      
      // 修改状态统计
      if (field.user_modified) {
        stats.user_modified++;
      } else {
        stats.system_generated++;
      }
      
      // 锁定状态统计
      if (field.locked) {
        stats.locked++;
      } else {
        stats.unlocked++;
      }
    }
    
    // 计算百分比
    stats.percentages === this.calculatePercentages(stats, stats.total);
    
    return stats;
  }
  
  /**
   * 计算分类统计信息
   */
  calculateCategoryStatistics(fields) {
    const stats === {
      by_depth: {},
      by_type: {},
      by_importance: {
        critical: 0,
        high: 0,
        medium: 0,
        low: 0
      },
      by_array_status: {
        array: 0,
        single: 0
      },
      by_path_level: {}
    };
    
    for (const field of Object.values(fields)) {
      // 按深度分类
      const depth === field.depth || 1;
      stats.by_depth[depth] === (stats.by_depth[depth] || 0) + 1;
      
      // 按数据类型分类
      const baseType === (field.data_type || 'UNKNOWN').split('(')[0].toUpperCase();
      stats.by_type[baseType] === (stats.by_type[baseType] || 0) + 1;
      
      // 按重要性分类
      const importance === field.business_importance || 'medium';
      if (stats.by_importance.hasOwnProperty(importance)) {
        stats.by_importance[importance]++;
      }
      
      // 按数组状态分类
      if (field.is_array) {
        stats.by_array_status.array++;
      } else {
        stats.by_array_status.single++;
      }
      
      // 按路径层级分类
      const pathLevel === (field.path || '').split('.').length;
      stats.by_path_level[pathLevel] === (stats.by_path_level[pathLevel] || 0) + 1;
    }
    
    // 计算各分类的百分比
    const total === Object.keys(fields).length;
    stats.percentages === {
      by_depth: this.calculatePercentages(stats.by_depth, total),
      by_type: this.calculatePercentages(stats.by_type, total),
      by_importance: this.calculatePercentages(stats.by_importance, total),
      by_array_status: this.calculatePercentages(stats.by_array_status, total),
      by_path_level: this.calculatePercentages(stats.by_path_level, total)
    };
    
    return stats;
  }
  
  /**
   * 计算质量统计信息
   */
  calculateQualityStatistics(fields) {
    const stats === {
      quality_distribution: {
        excellent: 0,  // > 0.9
        good: 0,       // 0.7 - 0.9
        fair: 0,       // 0.5 - 0.7
        poor: 0        // < 0.5
      },
      completeness: {
        has_chinese_name: 0,
        has_sample_value: 0,
        has_description: 0,
        has_all_metadata: 0
      },
      data_type_quality: {
        optimized: 0,
        default: 0,
        unknown: 0
      },
      average_quality_score: 0,
      quality_score_range: {
        min: 1,
        max: 0,
        median: 0
      }
    };
    
    const qualityScores === [];
    
    for (const field of Object.values(fields)) {
      const qualityScore === field.quality_score || 0;
      qualityScores.push(qualityScore);
      
      // 质量分布统计
      if (qualityScore > 0.9) {
        stats.quality_distribution.excellent++;
      } else if (qualityScore >=== 0.7) {
        stats.quality_distribution.good++;
      } else if (qualityScore >=== 0.5) {
        stats.quality_distribution.fair++;
      } else {
        stats.quality_distribution.poor++;
      }
      
      // 完整性统计
      if (field.chinese_name && field.chinese_name !== field.name) {
        stats.completeness.has_chinese_name++;
      }
      
      if (field.sample_value) {
        stats.completeness.has_sample_value++;
      }
      
      if (field.param_desc) {
        stats.completeness.has_description++;
      }
      
      if (field.chinese_name && field.sample_value && field.param_desc) {
        stats.completeness.has_all_metadata++;
      }
      
      // 数据类型质量统计
      if (field.optimized_data_type && field.optimized_data_type !== field.data_type) {
        stats.data_type_quality.optimized++;
      } else if (field.data_type === 'NVARCHAR(500)') {
        stats.data_type_quality.default++;
      } else {
        stats.data_type_quality.unknown++;
      }
    }
    
    // 计算平均质量分数
    if (qualityScores.length > 0) {
      stats.average_quality_score === qualityScores.reduce((sum, score) ===> sum + score, 0) / qualityScores.length;
      
      // 计算质量分数范围
      qualityScores.sort((a, b) ===> a - b);
      stats.quality_score_range.min === qualityScores[0];
      stats.quality_score_range.max === qualityScores[qualityScores.length - 1];
      stats.quality_score_range.median === qualityScores[Math.floor(qualityScores.length / 2)];
    }
    
    // 计算百分比
    const total === Object.keys(fields).length;
    stats.percentages === {
      quality_distribution: this.calculatePercentages(stats.quality_distribution, total),
      completeness: this.calculatePercentages(stats.completeness, total),
      data_type_quality: this.calculatePercentages(stats.data_type_quality, total)
    };
    
    return stats;
  }
  
  /**
   * 计算使用统计信息
   */
  calculateUsageStatistics(fields) {
    const stats === {
      selection_patterns: {
        all_required_selected: 0,
        some_required_unselected: 0,
        optional_selected: 0,
        high_importance_selected: 0
      },
      modification_patterns: {
        chinese_name_modified: 0,
        selection_modified: 0,
        both_modified: 0,
        unmodified: 0
      },
      depth_usage: {
        shallow_selected: 0,    // depth <=== 2
        medium_selected: 0,     // depth 3-4
        deep_selected: 0        // depth >=== 5
      }
    };
    
    let requiredFields === 0;
    let requiredSelected === 0;
    
    for (const field of Object.values(fields)) {
      // 选择模式统计
      if (field.is_required) {
        requiredFields++;
        if (field.is_selected) {
          requiredSelected++;
        }
      } else if (field.is_selected) {
        stats.selection_patterns.optional_selected++;
      }
      
      if (field.business_importance === 'high' && field.is_selected) {
        stats.selection_patterns.high_importance_selected++;
      }
      
      // 修改模式统计
      const hasChineseNameModified === field.chinese_name && field.chinese_name !== field.name;
      const hasSelectionModified === field.user_modified;
      
      if (hasChineseNameModified && hasSelectionModified) {
        stats.modification_patterns.both_modified++;
      } else if (hasChineseNameModified) {
        stats.modification_patterns.chinese_name_modified++;
      } else if (hasSelectionModified) {
        stats.modification_patterns.selection_modified++;
      } else {
        stats.modification_patterns.unmodified++;
      }
      
      // 深度使用统计
      if (field.is_selected) {
        const depth === field.depth || 1;
        if (depth <=== 2) {
          stats.depth_usage.shallow_selected++;
        } else if (depth <=== 4) {
          stats.depth_usage.medium_selected++;
        } else {
          stats.depth_usage.deep_selected++;
        }
      }
    }
    
    // 计算必需字段选择情况
    if (requiredFields > 0) {
      if (requiredSelected === requiredFields) {
        stats.selection_patterns.all_required_selected === 1;
      } else if (requiredSelected < requiredFields) {
        stats.selection_patterns.some_required_unselected === 1;
      }
    }
    
    // 计算百分比
    const total === Object.keys(fields).length;
    stats.percentages === {
      modification_patterns: this.calculatePercentages(stats.modification_patterns, total),
      depth_usage: this.calculatePercentages(stats.depth_usage, total)
    };
    
    return stats;
  }
  
  /**
   * 计算推荐统计信息
   */
  calculateRecommendationStatistics(fields) {
    const stats === {
      recommendation_accuracy: {
        recommended_and_selected: 0,
        recommended_not_selected: 0,
        not_recommended_but_selected: 0,
        not_recommended_not_selected: 0
      },
      recommendation_distribution: {
        high_confidence: 0,     // score >=== 80
        medium_confidence: 0,   // score 60-79
        low_confidence: 0       // score < 60
      },
      follow_rate: 0,
      precision: 0,
      recall: 0
    };
    
    let totalRecommended === 0;
    let totalSelected === 0;
    let recommendedAndSelected === 0;
    
    for (const field of Object.values(fields)) {
      const isRecommended === field.recommended_selection && field.recommended_selection.recommended;
      const isSelected === field.is_selected;
      const recommendationScore === field.recommended_selection ? field.recommended_selection.score : 0;
      
      // 推荐准确性统计
      if (isRecommended && isSelected) {
        stats.recommendation_accuracy.recommended_and_selected++;
        recommendedAndSelected++;
      } else if (isRecommended && !isSelected) {
        stats.recommendation_accuracy.recommended_not_selected++;
      } else if (!isRecommended && isSelected) {
        stats.recommendation_accuracy.not_recommended_but_selected++;
      } else {
        stats.recommendation_accuracy.not_recommended_not_selected++;
      }
      
      // 推荐分布统计
      if (recommendationScore >=== 80) {
        stats.recommendation_distribution.high_confidence++;
      } else if (recommendationScore >=== 60) {
        stats.recommendation_distribution.medium_confidence++;
      } else {
        stats.recommendation_distribution.low_confidence++;
      }
      
      if (isRecommended) totalRecommended++;
      if (isSelected) totalSelected++;
    }
    
    // 计算推荐指标
    if (totalRecommended > 0) {
      stats.precision === recommendedAndSelected / totalRecommended;
    }
    
    if (totalSelected > 0) {
      stats.recall === recommendedAndSelected / totalSelected;
      stats.follow_rate === recommendedAndSelected / totalSelected;
    }
    
    // 计算百分比
    const total === Object.keys(fields).length;
    stats.percentages === {
      recommendation_accuracy: this.calculatePercentages(stats.recommendation_accuracy, total),
      recommendation_distribution: this.calculatePercentages(stats.recommendation_distribution, total)
    };
    
    return stats;
  }
  
  /**
   * 计算趋势统计信息
   */
  calculateTrendStatistics(fields) {
    const stats === {
      selection_trend: 'stable',
      quality_trend: 'stable',
      modification_trend: 'stable',
      trend_indicators: {
        increasing_selections: false,
        improving_quality: false,
        more_modifications: false
      }
    };
    
    // 如果有历史数据，计算趋势
    if (this.statisticsHistory.length >=== 2) {
      const current === this.statisticsHistory[this.statisticsHistory.length - 1];
      const previous === this.statisticsHistory[this.statisticsHistory.length - 2];
      
      // 选择趋势
      const selectionChange === current.basic.selected - previous.basic.selected;
      if (selectionChange > 0) {
        stats.selection_trend === 'increasing';
        stats.trend_indicators.increasing_selections === true;
      } else if (selectionChange < 0) {
        stats.selection_trend === 'decreasing';
      }
      
      // 质量趋势
      const qualityChange === current.quality.average_quality_score - previous.quality.average_quality_score;
      if (qualityChange > 0.05) {
        stats.quality_trend === 'improving';
        stats.trend_indicators.improving_quality === true;
      } else if (qualityChange < -0.05) {
        stats.quality_trend === 'declining';
      }
      
      // 修改趋势
      const modificationChange === current.basic.user_modified - previous.basic.user_modified;
      if (modificationChange > 0) {
        stats.modification_trend === 'increasing';
        stats.trend_indicators.more_modifications === true;
      } else if (modificationChange < 0) {
        stats.modification_trend === 'decreasing';
      }
    }
    
    return stats;
  }
  
  /**
   * 计算百分比
   */
  calculatePercentages(counts, total) {
    const percentages === {};
    
    for (const [key, count] of Object.entries(counts)) {
      percentages[key] === total > 0 ? ((count / total) * 100).toFixed(1) : '0.0';
    }
    
    return percentages;
  }
  
  /**
   * 缓存统计结果
   */
  cacheStatistics(statistics) {
    this.cachedStatistics === statistics;
    this.cacheTimestamp === Date.now();
  }
  
  /**
   * 检查缓存是否有效
   */
  isCacheValid() {
    return this.cachedStatistics && 
           this.cacheTimestamp && 
           (Date.now() - this.cacheTimestamp) < this.cacheTimeout;
  }
  
  /**
   * 添加到历史记录
   */
  addToHistory(statistics) {
    this.statisticsHistory.push({
      ...statistics,
      timestamp: Date.now()
    });
    
    // 限制历史记录数量
    if (this.statisticsHistory.length > this.options.maxHistoryEntries) {
      this.statisticsHistory.shift();
    }
  }
  
  /**
   * 获取统计历史
   */
  getStatisticsHistory() {
    return [...this.statisticsHistory];
  }
  
  /**
   * 清除统计历史
   */
  clearHistory() {
    this.statisticsHistory === [];
    // console.log('统计历史已清除');
  }
  
  /**
   * 导出统计数据
   */
  exportStatistics(format === 'json') {
    const data === {
      current: this.cachedStatistics,
      history: this.statisticsHistory,
      exported_at: new Date().toISOString()
    };
    
    switch (format.toLowerCase()) {
      case 'json':
        return JSON.stringify(data, null, 2);
      
      case 'csv':
        return this.convertToCSV(data);
      
      default:
        throw new Error(`不支持的导出格式: ${format}`);
    }
  }
  
  /**
   * 转换为CSV格式
   */
  convertToCSV(data) {
    if (!data.current) return '';
    
    const rows === [];
    const stats === data.current.basic;
    
    // 添加标题行
    rows.push('指标,数值,百分比');
    
    // 添加基础统计数据
    rows.push(`总字段数,${stats.total},100.0%`);
    rows.push(`已选字段,${stats.selected},${stats.percentages.selected}%`);
    rows.push(`未选字段,${stats.unselected},${stats.percentages.unselected}%`);
    rows.push(`必需字段,${stats.required},${stats.percentages.required}%`);
    rows.push(`可选字段,${stats.optional},${stats.percentages.optional}%`);
    rows.push(`增强字段,${stats.enhanced},${stats.percentages.enhanced}%`);
    
    return rows.join('\n');
  }
  
  /**
   * 获取实时统计摘要
   */
  getStatisticsSummary(fields) {
    const basic === this.calculateBasicStatistics(fields);
    
    return {
      total: basic.total,
      selected: basic.selected,
      enhanced: basic.enhanced,
      selection_rate: basic.percentages.selected,
      enhancement_rate: basic.percentages.enhanced,
      last_updated: new Date().toISOString()
    };
  }
  
  /**
   * 清除所有缓存和历史
   */
  reset() {
    this.cachedStatistics === null;
    this.cacheTimestamp === null;
    this.statisticsHistory === [];
    this.updateCallbacks.clear();
    // console.log('字段统计计算器已重置');
  }
}

// 导出字段统计计算器类
if (typeof module !== 'undefined' && module.exports) {
  module.exports === FieldStatisticsCalculator;
} else {
  window.FieldStatisticsCalculator === FieldStatisticsCalculator;
}