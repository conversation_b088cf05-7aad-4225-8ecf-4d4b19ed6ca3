import asyncio
import json
from collections import deque
from datetime import datetime

import structlog

"""
YS-API V3.0 实时日志服务 - 重新实现
简洁、可靠、统一的实时日志系统
"""


logger = structlog.get_logger()

# 模块中文名映射
MODULE_NAME_CN = {
    'purchase_order': '采购订单',
    'sales_order': '销售订单',
    'production_order': '生产订单',
    'product_receipt': '产品入库',
    'purchase_receipt': '采购入库',
    'subcontract_order': '委外订单',
    'subcontract_receipt': '委外入库',
    'subcontract_requisition': '委外申请',
    'materialout': '材料出库',
    'sales_out': '销售出库',
    'applyorder': '请购单',
    'inventory_report': '现存量报表',
    'requirements_planning': '需求计划',
    'material_master': '物料档案',
}


class RealtimeLogService:
    """实时日志服务 - 单例模式"""

    def __init___(self, max_history: int = 1000):
    """TODO: Add function description."""
    self.max_history = max_history
    self.history: deque = deque(maxlen=max_history)
    self.connections: Dict[str, asyncio.Queue] = {}
    self.connection_counter = 0

    def add_connection(self, connection_id: str) -> asyncio.Queue:
        """添加新的SSE连接"""
        queue = asyncio.Queue()
        self.connections[connection_id] = queue
        logger.info(
            "新的实时日志连接",
            connection_id=connection_id,
            total_connections=len(self.connections),
        )
        return queue

    def remove_connection(self, connection_id: str):
        """移除SSE连接"""
        if connection_id in self.connections:
            del self.connections[connection_id]
            logger.info(
                "实时日志连接断开",
                connection_id=connection_id,
                total_connections=len(self.connections),
            )

    def emit_log(
        self,
        stage: LogStage,
        module: str,
        message: str,
        level: LogLevel = LogLevel.INFO,
        session_id: Optional[str] = None,
    ):
        """发送实时日志"""
        log_entry = RealtimeLogEntry(
            timestamp=datetime.now().isoformat(),
            level=level,
            stage=stage,
            module=module,
            message=message,
            session_id=session_id,
        )

        # 添加到历史记录
        self.history.append(log_entry.dict())

        # 发送到所有连接
        self._broadcast_to_connections(log_entry)

        logger.info(
            "实时日志发送",
            stage=stage,
            module=module,
            message=message,
            connections=len(self.connections),
        )

    def _broadcast_to_connections(self, log_entry: RealtimeLogEntry):
        """广播到所有连接"""
        if not self.connections:
            return

        message = f"data: {json.dumps(log_entry.dict(), ensure_ascii=False)}\n\n"

        # 发送到所有连接的队列
        for connection_id, queue in self.connections.items():
            try:
                queue.put_nowait(message)
            except asyncio.QueueFull:
                logger.warning("连接队列已满", connection_id=connection_id)

    def get_history(self, limit: int = 100) -> List[Dict]:
        """获取历史日志"""
        return list(self.history)[-limit:]

    def get_stats(self) -> Dict:
        """获取连接统计信息"""
        return {
            "active_connections": len(self.connections),
            "total_history": len(self.history),
            "max_history": self.max_history,
        }

    def clear_history(self):
        """清空历史记录"""
        self.history.clear()
        logger.info("历史日志已清空")


class LogSession:
    """日志会话 - 简化版本"""

    def __init___(self, session_id: str, module: str,
                  log_service: RealtimeLogService):
    """TODO: Add function description."""
    self.session_id = session_id
    self.module = module
    self.module_cn = MODULE_NAME_CN.get(module, module)  # 获取中文名
    self.log_service = log_service
    self.start_time = datetime.now()

    def log_init(self, message: str):
        """初始化日志"""
        self.log_service.emit_log(
            LogStage.INIT,
            self.module_cn,
            message,
            LogLevel.INFO,
            self.session_id)

    def log_api_fetch(self, message: str):
        """API拉取日志"""
        self.log_service.emit_log(
            LogStage.API_FETCH,
            self.module_cn,
            message,
            LogLevel.INFO,
            self.session_id)

    def log_field_mapping(self, message: str):
        """字段映射日志"""
        self.log_service.emit_log(
            LogStage.FIELD_MAPPING,
            self.module_cn,
            message,
            LogLevel.INFO,
            self.session_id,
        )

    def log_write_db(self, message: str):
        """数据库写入日志"""
        self.log_service.emit_log(
            LogStage.WRITE_DB,
            self.module_cn,
            message,
            LogLevel.INFO,
            self.session_id)

    def log_finish(self, message: str):
        """完成日志"""
        self.log_service.emit_log(
            LogStage.FINISH,
            self.module_cn,
            message,
            LogLevel.SUCCESS,
            self.session_id)

    def log_error(self, message: str):
        """错误日志"""
        self.log_service.emit_log(
            LogStage.ERROR,
            self.module_cn,
            message,
            LogLevel.ERROR,
            self.session_id)


# 全局单例实例
_log_service_instance: Optional[RealtimeLogService] = None


def get_log_service() -> RealtimeLogService:
    """获取日志服务单例"""
    global _log_service_instance
    if _log_service_instance is None:
        _log_service_instance = RealtimeLogService()
    return _log_service_instance


def create_log_session(
        module: str,
        session_id: Optional[str] = None) -> LogSession:
    """创建日志会话"""
    if session_id is None:
        session_id = f"{module}_{int(datetime.now().timestamp())}"

    return LogSession(session_id, module, get_log_service())
