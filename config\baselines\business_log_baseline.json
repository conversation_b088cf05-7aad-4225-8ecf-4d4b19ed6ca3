{"module_name": "business_log", "display_name": "业务日志", "version": "2.0.0", "source": "json_parser", "total_fields": 36, "created_at": "2025-07-28T20:12:24.832968", "last_updated": "2025-07-28T20:12:24.832968", "fields": {"status": {"api_field_name": "status", "chinese_name": "状态码", "data_type": "NVARCHAR(500)", "param_desc": "状态码", "path": "status", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "数据", "data_type": "NVARCHAR(MAX)", "param_desc": "数据", "path": "data", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "number": {"api_field_name": "number", "chinese_name": "页码", "data_type": "NVARCHAR(500)", "param_desc": "页码", "path": "data.number", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalPages": {"api_field_name": "totalPages", "chinese_name": "总页数", "data_type": "NVARCHAR(500)", "param_desc": "总页数", "path": "data.totalPages", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "content": {"api_field_name": "content", "chinese_name": "日志内容", "data_type": "NVARCHAR(500)", "param_desc": "日志内容", "path": "content", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "busiObjCode": {"api_field_name": "busiObjCode", "chinese_name": "业务对象编码", "data_type": "NVARCHAR(500)", "param_desc": "业务对象编码", "path": "busiObjCode", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "busiObjName": {"api_field_name": "busiObjName", "chinese_name": "业务对象名称", "data_type": "NVARCHAR(500)", "param_desc": "业务对象名称", "path": "busiObjName", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "operationDate": {"api_field_name": "operationDate", "chinese_name": "操作时间", "data_type": "NVARCHAR(500)", "param_desc": "操作时间", "path": "data.content.operationDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operResult": {"api_field_name": "operResult", "chinese_name": "操作结果", "data_type": "NVARCHAR(500)", "param_desc": "操作结果", "path": "data.content.operResult", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "operCode": {"api_field_name": "operCode", "chinese_name": "操作编码", "data_type": "NVARCHAR(500)", "param_desc": "操作编码", "path": "data.content.operCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "detail": {"api_field_name": "detail", "chinese_name": "操作详情", "data_type": "NVARCHAR(500)", "param_desc": "操作详情", "path": "data.content.detail", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "busiObjTypeName": {"api_field_name": "busiObjTypeName", "chinese_name": "业务对象所属类型名称", "data_type": "NVARCHAR(500)", "param_desc": "业务对象所属类型名称", "path": "data.content.busiObjTypeName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "busiObjId": {"api_field_name": "busiObjId", "chinese_name": "业务对象ID", "data_type": "NVARCHAR(500)", "param_desc": "业务对象ID", "path": "data.content.busiObjId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operationName": {"api_field_name": "operationName", "chinese_name": "操作名称", "data_type": "NVARCHAR(500)", "param_desc": "操作名称", "path": "data.content.operationName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "busiObjTypeCode": {"api_field_name": "busiObjTypeCode", "chinese_name": "业务对象所属类型编码", "data_type": "NVARCHAR(500)", "param_desc": "业务对象所属类型编码", "path": "data.content.busiObjTypeCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "tenantId": {"api_field_name": "tenantId", "chinese_name": "租户id", "data_type": "NVARCHAR(500)", "param_desc": "租户id", "path": "tenantId", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "newBusiObj": {"api_field_name": "newBusiObj", "chinese_name": "新业务对象", "data_type": "NVARCHAR(500)", "param_desc": "新业务对象", "path": "data.content.newBusiObj", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "sysId": {"api_field_name": "sysId", "chinese_name": "系统标识", "data_type": "NVARCHAR(500)", "param_desc": "系统标识", "path": "data.content.sysId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operator": {"api_field_name": "operator", "chinese_name": "操作人(id)，多个用逗号分隔", "data_type": "NVARCHAR(500)", "param_desc": "操作人(id)，多个用逗号分隔", "path": "operator", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "businessId": {"api_field_name": "businessId", "chinese_name": "主键（在es中作为索引）", "data_type": "NVARCHAR(500)", "param_desc": "主键（在es中作为索引）", "path": "data.content.businessId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operatorName": {"api_field_name": "operatorName", "chinese_name": "操作人名称", "data_type": "NVARCHAR(500)", "param_desc": "操作人名称", "path": "data.content.operatorName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "ip": {"api_field_name": "ip", "chinese_name": "ip地址", "data_type": "NVARCHAR(500)", "param_desc": "ip地址", "path": "data.content.ip", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalElements": {"api_field_name": "totalElements", "chinese_name": "总条数", "data_type": "NVARCHAR(500)", "param_desc": "总条数", "path": "data.totalElements", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "displayCode": {"api_field_name": "displayCode", "chinese_name": "异常码", "data_type": "NVARCHAR(500)", "param_desc": "异常码", "path": "displayCode", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "level": {"api_field_name": "level", "chinese_name": "异常等级", "data_type": "NVARCHAR(500)", "param_desc": "异常等级", "path": "level", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "busiObjType": {"api_field_name": "busiObjType", "chinese_name": "业务对象类型", "data_type": "NVARCHAR(500)", "param_desc": "业务对象类型", "path": "busiObjType", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "startDate": {"api_field_name": "startDate", "chinese_name": "开始时间（时间戳）", "data_type": "NVARCHAR(500)", "param_desc": "开始时间（时间戳）", "path": "startDate", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "endDate": {"api_field_name": "endDate", "chinese_name": "结束时间（时间戳）", "data_type": "NVARCHAR(500)", "param_desc": "结束时间（时间戳）", "path": "endDate", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "page": {"api_field_name": "page", "chinese_name": "页码", "data_type": "BIGINT", "param_desc": "页码", "path": "page", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "size": {"api_field_name": "size", "chinese_name": "每页数量", "data_type": "BIGINT", "param_desc": "每页数量", "path": "size", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operNameResid": {"api_field_name": "operNameResid", "chinese_name": "操作类型", "data_type": "NVARCHAR(500)", "param_desc": "操作类型", "path": "operNameResid", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}}}