<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>field-config-manual.html - 已迁移到新架构</title>

  <!-- 新架构核心文件 -->
  <script src="../js/core/component-manager.js"></script>
  <script src="../js/core/app-bootstrap.js"></script>
  <script src="../js/api-config-fix.js"></script>

  <!-- 所需组件 -->
  <script src="js/common/api-client.js"></script>
  <script src="js/common/field-utils.js"></script>
  <script src="js/common/validation-utils.js"></script>
  <script src="js/common/error-handler.js"></script>
  <script src="js/notification-system.js"></script>
  <script src="../js/field-deduplication-enhancer.js"></script>

  <!-- 自定义样式 -->
  <style>
    /* 基础样式重置 */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    html,
    body {
      height: 100%;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
    }

    /* 主容器 */
    .container {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;
      gap: 20px;
    }

    /* 页面标题 */
    .page-header {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      padding: 24px 32px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      text-align: center;
    }

    .page-title {
      font-size: 28px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 8px;
    }

    .page-subtitle {
      font-size: 16px;
      color: #7f8c8d;
      font-weight: 400;
    }

    /* 模块选择区域 */
    .module-selection {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      padding: 24px 32px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .section-icon {
      font-size: 24px;
    }

    .module-controls {
      display: flex;
      gap: 16px;
      align-items: center;
      flex-wrap: wrap;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      min-width: 200px;
    }

    .form-label {
      font-size: 14px;
      font-weight: 500;
      color: #555;
    }

    .form-select {
      padding: 12px 16px;
      border: 2px solid #e1e8ed;
      border-radius: 12px;
      font-size: 16px;
      background: white;
      transition: all 0.3s ease;
      outline: none;
    }

    .form-select:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-select.has-selection {
      border-color: #27ae60;
      background-color: #f8fff9;
    }

    .form-select.invalid {
      border-color: #e74c3c;
      background-color: #fdf2f2;
    }

    .form-select:disabled {
      background-color: #f8f9fa;
      color: #6c757d;
      cursor: not-allowed;
      opacity: 0.6;
    }

    /* 模块选择器状态指示器 */
    .module-status {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
      font-size: 14px;
    }

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      display: inline-block;
    }

    .status-indicator.valid {
      background-color: #27ae60;
    }

    .status-indicator.invalid {
      background-color: #e74c3c;
    }

    .status-indicator.loading {
      background-color: #f39c12;
      animation: pulse 1.5s ease-in-out infinite;
    }

    /* 加载控制区域 */
    .load-control {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      padding: 24px 32px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .load-button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 16px 32px;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 180px;
      justify-content: center;
    }

    .load-button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .load-button:disabled {
      background: #bdc3c7;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .load-button:active:not(:disabled) {
      transform: translateY(0);
    }

    .cancel-button {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
    }

    .cancel-button:hover:not(:disabled) {
      background: linear-gradient(135deg, #c0392b 0%, #a93226 100%) !important;
      box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
    }

    /* 进度显示系统 */
    .progress-container {
      display: none;
      margin-top: 20px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid rgba(102, 126, 234, 0.2);
      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.1);
      transition: all 0.3s ease;
    }

    .progress-container.show {
      display: block;
      animation: progressSlideIn 0.4s ease-out;
    }

    .progress-container.hide {
      animation: progressSlideOut 0.3s ease-in;
    }

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .progress-title {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .progress-stage-icon {
      font-size: 20px;
      animation: progressPulse 2s ease-in-out infinite;
    }

    .progress-percentage {
      font-size: 18px;
      font-weight: 700;
      color: #667eea;
      background: rgba(102, 126, 234, 0.1);
      padding: 4px 12px;
      border-radius: 20px;
      min-width: 60px;
      text-align: center;
      transition: all 0.3s ease;
    }

    .progress-main {
      margin-bottom: 16px;
    }

    .progress-text {
      font-size: 16px;
      font-weight: 500;
      color: #2c3e50;
      margin-bottom: 12px;
      transition: color 0.3s ease;
    }

    .progress-bar-container {
      width: 100%;
      height: 12px;
      background: rgba(102, 126, 234, 0.1);
      border-radius: 6px;
      overflow: hidden;
      position: relative;
      box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
      background-size: 200% 100%;
      width: 0%;
      border-radius: 6px;
      position: relative;
      transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      animation: progressShimmer 2s ease-in-out infinite;
    }

    .progress-bar::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
      animation: progressGlow 1.5s ease-in-out infinite;
    }

    .progress-details {
      margin-top: 12px;
      padding: 12px;
      background: rgba(102, 126, 234, 0.05);
      border-radius: 8px;
      border-left: 3px solid #667eea;
    }

    .progress-details-text {
      font-size: 14px;
      color: #5a6c7d;
      line-height: 1.4;
      margin-bottom: 8px;
    }

    .progress-stage-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #7f8c8d;
    }

    .progress-elapsed-time {
      font-family: 'Courier New', monospace;
    }

    .progress-estimated-time {
      font-style: italic;
    }

    /* 进度阶段指示器 */
    .progress-stages {
      display: flex;
      justify-content: space-between;
      margin-top: 16px;
      padding: 0 4px;
    }

    .progress-stage {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      position: relative;
    }

    .progress-stage:not(:last-child)::after {
      content: '';
      position: absolute;
      top: 12px;
      right: -50%;
      width: 100%;
      height: 2px;
      background: rgba(102, 126, 234, 0.2);
      z-index: 1;
    }

    .progress-stage.completed::after {
      background: #667eea;
    }

    .progress-stage-dot {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgba(102, 126, 234, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #7f8c8d;
      transition: all 0.3s ease;
      z-index: 2;
      position: relative;
    }

    .progress-stage.completed .progress-stage-dot {
      background: #667eea;
      color: white;
      transform: scale(1.1);
    }

    .progress-stage.active .progress-stage-dot {
      background: #667eea;
      color: white;
      animation: progressPulse 1.5s ease-in-out infinite;
      transform: scale(1.2);
    }

    .progress-stage-label {
      font-size: 11px;
      color: #7f8c8d;
      margin-top: 6px;
      text-align: center;
      transition: color 0.3s ease;
    }

    .progress-stage.completed .progress-stage-label,
    .progress-stage.active .progress-stage-label {
      color: #667eea;
      font-weight: 500;
    }

    /* 进度动画 */
    @keyframes progressSlideIn {
      from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
      }

      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    @keyframes progressSlideOut {
      from {
        opacity: 1;
        transform: translateY(0) scale(1);
      }

      to {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
      }
    }

    @keyframes progressShimmer {
      0% {
        background-position: -200% 0;
      }

      100% {
        background-position: 200% 0;
      }
    }

    @keyframes progressGlow {

      0%,
      100% {
        opacity: 0;
        transform: translateX(-100%);
      }

      50% {
        opacity: 1;
        transform: translateX(100%);
      }
    }

    @keyframes progressPulse {

      0%,
      100% {
        opacity: 1;
        transform: scale(1);
      }

      50% {
        opacity: 0.7;
        transform: scale(1.05);
      }
    }

    /* 进度完成状态 */
    .progress-container.completed {
      border-color: #27ae60;
      box-shadow: 0 4px 20px rgba(39, 174, 96, 0.2);
    }

    .progress-container.completed .progress-bar {
      background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
    }

    .progress-container.completed .progress-percentage {
      color: #27ae60;
      background: rgba(39, 174, 96, 0.1);
    }

    /* 进度错误状态 */
    .progress-container.error {
      border-color: #e74c3c;
      box-shadow: 0 4px 20px rgba(231, 76, 60, 0.2);
    }

    .progress-container.error .progress-bar {
      background: linear-gradient(90deg, #e74c3c 0%, #c0392b 100%);
    }

    .progress-container.error .progress-percentage {
      color: #e74c3c;
      background: rgba(231, 76, 60, 0.1);
    }

    /* 响应式进度显示 */
    @media (max-width: 768px) {
      .progress-container {
        padding: 16px;
      }

      .progress-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .progress-stages {
        flex-wrap: wrap;
        gap: 8px;
      }

      .progress-stage {
        flex: none;
        min-width: 80px;
      }

      .progress-stage:not(:last-child)::after {
        display: none;
      }
    }

    /* 字段显示区域 */
    .field-display {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      padding: 24px 32px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      flex: 1;
      display: none;
    }

    .field-statistics {
      display: flex;
      gap: 24px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .stat-item {
      background: rgba(102, 126, 234, 0.1);
      padding: 12px 20px;
      border-radius: 12px;
      text-align: center;
      min-width: 120px;
    }

    .stat-number {
      font-size: 24px;
      font-weight: 700;
      color: #667eea;
      display: block;
    }

    .stat-label {
      font-size: 14px;
      color: #7f8c8d;
      margin-top: 4px;
    }

    .field-list {
      max-height: 400px;
      overflow-y: auto;
      border: 1px solid #e1e8ed;
      border-radius: 12px;
      background: white;
    }

    /* 增强字段列表容器样式 */
    .enhanced-field-list-container {
      min-height: 500px;
      border: 1px solid #e1e8ed;
      border-radius: 12px;
      background: white;
      display: flex;
      flex-direction: column;
    }

    /* 确保字段列表组件占满容器 */
    .enhanced-field-list-container>* {
      flex: 1;
    }

    /* 简单字段列表样式 */
    .simple-field-list {
      max-height: 400px;
      overflow-y: auto;
    }

    .simple-field-list .field-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.2s ease;
    }

    .simple-field-list .field-item:hover {
      background-color: #f8f9fa;
    }

    .simple-field-list .field-info {
      flex: 1;
      display: grid;
      grid-template-columns: 2fr 2fr 2fr 1fr;
      gap: 12px;
      align-items: center;
    }

    .simple-field-list .field-name {
      font-weight: 600;
      color: #2c3e50;
      font-size: 14px;
    }

    .simple-field-list .field-sample {
      color: #7f8c8d;
      font-size: 13px;
      font-family: 'Courier New', monospace;
    }

    .simple-field-list .field-chinese {
      color: #27ae60;
      font-size: 14px;
      font-weight: 500;
    }

    .simple-field-list .field-type {
      color: #8e44ad;
      font-size: 12px;
      font-weight: 500;
    }

    .error-state,
    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #7f8c8d;
    }

    .error-icon,
    .empty-state-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .error-text,
    .empty-state-text {
      font-size: 16px;
    }

    .field-item {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #f8f9fa;
      transition: background-color 0.2s ease;
    }

    .field-item:hover {
      background-color: #f8f9fa;
    }

    .field-item:last-child {
      border-bottom: none;
    }

    .field-info {
      flex: 1;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 16px;
      align-items: center;
    }

    .field-name {
      font-weight: 600;
      color: #2c3e50;
      font-size: 14px;
    }

    .field-sample {
      color: #7f8c8d;
      font-size: 14px;
      font-family: 'Courier New', monospace;
    }

    .field-chinese {
      color: #27ae60;
      font-size: 14px;
      font-weight: 500;
    }

    /* 保存操作区域 */
    .save-operations {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      padding: 24px 32px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      display: none;
    }

    .save-buttons {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
    }

    .save-button {
      flex: 1;
      min-width: 200px;
      padding: 16px 24px;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }

    .save-baseline {
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
    }

    .save-user-config {
      background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
      color: white;
    }

    .save-button:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .save-button:disabled {
      background: #bdc3c7;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    /* 通知消息 */
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 16px 24px;
      border-radius: 12px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      z-index: 10000;
      opacity: 0;
      transform: translateX(100%);
      transition: all 0.3s ease;
      max-width: 400px;
      word-wrap: break-word;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .notification.show {
      opacity: 1;
      transform: translateX(0);
    }

    .notification.success {
      background: rgba(40, 167, 69, 0.9);
    }

    .notification.error {
      background: rgba(220, 53, 69, 0.9);
    }

    .notification.warning {
      background: rgba(255, 193, 7, 0.9);
      color: #212529;
    }

    .notification.info {
      background: rgba(0, 123, 255, 0.9);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .container {
        padding: 16px;
        gap: 16px;
      }

      .page-header,
      .module-selection,
      .load-control,
      .field-display,
      .save-operations {
        padding: 20px;
      }

      .page-title {
        font-size: 24px;
      }

      .module-controls {
        flex-direction: column;
        align-items: stretch;
      }

      .form-group {
        min-width: auto;
      }

      .field-info {
        grid-template-columns: 1fr;
        gap: 8px;
      }

      .save-buttons {
        flex-direction: column;
      }

      .save-button {
        min-width: auto;
      }

      .field-statistics {
        justify-content: center;
      }
    }

    /* 无障碍访问支持 */
    .sr-only {
      position: absolute;
      width: 1px;
      height: 1px;
      padding: 0;
      margin: -1px;
      overflow: hidden;
      clip: rect(0, 0, 0, 0);
      white-space: nowrap;
      border: 0;
    }

    /* 焦点样式 */
    .load-button:focus,
    .save-button:focus,
    .form-select:focus {
      outline: 2px solid #667eea;
      outline-offset: 2px;
    }

    /* 动画效果 */
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .fade-in {
      animation: fadeIn 0.5s ease-out;
    }

    /* 加载状态动画 */
    @keyframes pulse {

      0%,
      100% {
        opacity: 1;
      }

      50% {
        opacity: 0.5;
      }
    }

    .loading {
      animation: pulse 1.5s ease-in-out infinite;
    }

    /* 空状态样式 */
    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #7f8c8d;
    }

    .empty-state-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .empty-state-text {
      font-size: 16px;
      margin-bottom: 8px;
    }

    .empty-state-subtext {
      font-size: 14px;
      opacity: 0.7;
    }
  </style>
  <link rel="stylesheet" href="../css/notification-system.css">
  <link rel="stylesheet" href="../css/error-handler.css">
  <link rel="stylesheet" href="../css/baseline-save.css">
  <link rel="stylesheet" href="../css/user-config-save.css">
</head>

<body>
  <!-- 迁移标识 -->
  <div
    style="position: fixed; top: 10px; right: 10px; background: #4CAF50; color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px; z-index: 9999;">
    ✅ 已迁移到新架构
  </div>

  <!-- 原始页面内容 -->
  <div class="container">
    <!-- 页面标题 -->
    <header class="page-header">
      <h1 class="page-title">YS-API 字段配置中心</h1>
      <p class="page-subtitle">手动加载模式 - 精确控制字段配置加载和保存过程</p>
    </header>

    <!-- 模块选择区域 -->
    <section class="module-selection">
      <h2 class="section-title">
        <span class="section-icon">📋</span>
        模块选择
      </h2>
      <div class="module-controls">
        <div class="form-group">
          <label for="moduleSelect" class="form-label">选择业务模块</label>
          <select id="moduleSelect" class="form-select" aria-describedby="moduleHelp">
            <option value="">请选择模块...</option>
          </select>
          <div id="moduleStatus" class="module-status" style="display: none;">
            <span id="statusIndicator" class="status-indicator"></span>
            <span id="statusText"></span>
          </div>
          <div id="moduleHelp" class="sr-only">选择要配置字段的业务模块</div>
        </div>
      </div>
    </section>

    <!-- 加载控制区域 -->
    <section class="load-control">
      <h2 class="section-title">
        <span class="section-icon">🚀</span>
        加载控制
      </h2>
      <button id="loadButton" class="load-button" disabled aria-describedby="loadHelp">
        <span>📥</span>
        加载字段配置
      </button>
      <div id="loadHelp" class="sr-only">点击加载选中模块的字段配置数据</div>

      <!-- 增强进度显示系统 -->
      <div id="progressContainer" class="progress-container" role="progressbar" aria-live="polite" aria-valuemin="0"
        aria-valuemax="100">
        <div class="progress-header">
          <div class="progress-title">
            <span id="progressStageIcon" class="progress-stage-icon">⏳</span>
            <span id="progressText" class="progress-text">准备中...</span>
          </div>
          <span id="progressPercentage" class="progress-percentage">0%</span>
        </div>

        <div class="progress-main">
          <div class="progress-bar-container">
            <div id="progressBar" class="progress-bar"></div>
          </div>
        </div>

        <div id="progressDetails" class="progress-details">
          <div id="progressDetailsText" class="progress-details-text">正在初始化加载过程...</div>
          <div class="progress-stage-info">
            <span id="progressElapsedTime" class="progress-elapsed-time">已用时: 0s</span>
            <span id="progressEstimatedTime" class="progress-estimated-time">预计剩余: --</span>
          </div>
        </div>

        <!-- 进度阶段指示器 -->
        <div class="progress-stages">
          <div class="progress-stage" data-stage="preparing">
            <div class="progress-stage-dot">1</div>
            <div class="progress-stage-label">准备中</div>
          </div>
          <div class="progress-stage" data-stage="fetching">
            <div class="progress-stage-dot">2</div>
            <div class="progress-stage-label">获取数据</div>
          </div>
          <div class="progress-stage" data-stage="processing">
            <div class="progress-stage-dot">3</div>
            <div class="progress-stage-label">处理数据</div>
          </div>
          <div class="progress-stage" data-stage="rendering">
            <div class="progress-stage-dot">4</div>
            <div class="progress-stage-label">渲染界面</div>
          </div>
          <div class="progress-stage" data-stage="complete">
            <div class="progress-stage-dot">✓</div>
            <div class="progress-stage-label">完成</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 字段显示区域 -->
    <section id="fieldDisplay" class="field-display">
      <h2 class="section-title">
        <span class="section-icon">📊</span>
        字段信息
      </h2>

      <!-- 字段统计 -->
      <div class="field-statistics">
        <div class="stat-item">
          <span id="totalFields" class="stat-number">0</span>
          <div class="stat-label">总字段数</div>
        </div>
        <div class="stat-item">
          <span id="selectedFields" class="stat-number">0</span>
          <div class="stat-label">已选字段</div>
        </div>
        <div class="stat-item">
          <span id="enhancedFields" class="stat-number">0</span>
          <div class="stat-label">增强字段</div>
        </div>
      </div>

      <!-- 字段列表容器 -->
      <div id="fieldList" class="enhanced-field-list-container" role="region" aria-label="字段列表">
        <!-- 字段列表显示组件将在这里初始化 -->
      </div>
    </section>

    <!-- 保存操作区域 -->
    <section id="saveOperations" class="save-operations">
      <h2 class="section-title">
        <span class="section-icon">💾</span>
        保存配置
      </h2>
      <div class="save-buttons">
        <button id="saveBaseline" class="save-button save-baseline" aria-describedby="baselineHelp">
          <span>📄</span>
          保存到基准文件
        </button>
        <button id="saveUserConfig" class="save-button save-user-config" aria-describedby="configHelp">
          <span>⚙️</span>
          保存用户配置
        </button>
      </div>
      <div id="baselineHelp" class="sr-only">保存原始API字段数据到基准文件</div>
      <div id="configHelp" class="sr-only">保存增强处理后的用户字段配置</div>
    </section>
  </div>

  <!-- 引入JavaScript组件 -->








  <!-- 引入通知系统 -->
  <!-- 引入错误处理系统 -->
  <!-- 引入性能优化器 -->
  <!-- 引入增强字段列表显示组件 -->
  <!-- 引入基准文件保存组件 -->
  <!-- 引入用户配置保存组件 -->

  <!-- 新架构初始化脚本 -->
  <script>
    document.addEventListener('DOMContentLoaded', async function () {
      console.log('🚀 初始化新架构页面: field-config-manual.html');

      // 启动应用
      await window.startApp({
        environment: 'production',
        features: {
          "validation": true,
          "fieldDeduplication": true
        }
      });

      // 获取所需组件
      const apiClient = window.ComponentManager.get('apiClient');
      const fieldUtils = window.ComponentManager.get('fieldUtils');
      const validator = window.ComponentManager.get('validationUtils');
      const enhancer = window.ComponentManager.get('fieldDeduplicationEnhancer');

      // 调用原始初始化逻辑
      if (typeof initializePage === 'function') {
        initializePage();
      }

      console.log('✅ 页面初始化完成: field-config-manual.html');
    });

    // 原始自定义脚本（已适配新架构）
    // 全局变量
    let currentModule = '';
    let rawFieldData = [];
    let enhancedFieldData = [];
    let isLoading = false;
    let moduleSelector = null;
    let errorHandler = null;

    // 模块选择器组件类
    class ModuleSelector {
      constructor(selectElement, options = {}) {
        this.selectElement = selectElement;
        this.selectedModule = '';
        this.availableModules = [];
        this.disabled = false;
        this.onModuleChange = options.onModuleChange || (() => { });
        this.onValidationChange = options.onValidationChange || (() => { });

        this.init();
      }

      async init() {
        try {
          await this.loadAvailableModules();
          this.renderModuleOptions();
          this.bindEvents();
          this.validateSelection();
        } catch (error) {
          console.error('模块选择器初始化失败:', error);
          showNotification('模块选择器初始化失败', 'error');
        }
      }

      async loadAvailableModules() {
        try {
          const response = await fetch(`${API_BASE_URL}/api/v1/field-config/modules`);
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          if (!data.success) {
            throw new Error(data.message || '获取模块列表失败');
          }

          // 转换为模块选项格式
          this.availableModules = Object.entries(data.data.modules).map(([value, label]) => ({
            value,
            label,
            description: this.getModuleDescription(value)
          }));

        } catch (error) {
          console.warn('从API获取模块列表失败，使用默认列表:', error);
          // 使用默认模块映射作为后备
          this.availableModules = Object.entries(this.getDefaultModuleMap()).map(([value, label]) => ({
            value,
            label,
            description: this.getModuleDescription(value)
          }));
        }
      }

      getDefaultModuleMap() {
        return {
          sales_order: '销售订单',
          purchase_order: '采购订单',
          production_order: '生产订单',
          subcontract_order: '委外订单',
          applyorder: '请购单',
          subcontract_requisition: '委外请购',
          product_receipt: '产品入库单',
          purchase_receipt: '采购入库',
          subcontract_receipt: '委外入库',
          materialout: '材料出库单',
          sales_out: '销售出库',
          inventory: '现存量',
          inventory_report: '现存量报表',
          requirements_planning: '需求计划',
          material_master: '物料档案'
        };
      }

      getModuleDescription(moduleValue) {
        const descriptions = {
          sales_order: '管理销售订单的字段配置',
          purchase_order: '管理采购订单的字段配置',
          production_order: '管理生产订单的字段配置',
          subcontract_order: '管理委外订单的字段配置',
          applyorder: '管理请购单的字段配置',
          subcontract_requisition: '管理委外请购的字段配置',
          product_receipt: '管理产品入库单的字段配置',
          purchase_receipt: '管理采购入库的字段配置',
          subcontract_receipt: '管理委外入库的字段配置',
          materialout: '管理材料出库单的字段配置',
          sales_out: '管理销售出库的字段配置',
          inventory: '管理现存量的字段配置',
          inventory_report: '管理现存量报表的字段配置',
          requirements_planning: '管理需求计划的字段配置',
          material_master: '管理物料档案的字段配置'
        };
        return descriptions[moduleValue] || '业务模块字段配置';
      }

      renderModuleOptions() {
        // 清空现有选项
        this.selectElement.innerHTML = '<option value="">请选择模块...</option>';

        // 添加模块选项
        this.availableModules.forEach(module => {
          const option = document.createElement('option');
          option.value = module.value;
          option.textContent = module.label;
          option.title = module.description;
          this.selectElement.appendChild(option);
        });

        // 更新ARIA属性
        this.selectElement.setAttribute('aria-label', '选择业务模块');
        this.selectElement.setAttribute('aria-describedby', 'moduleHelp');
      }

      bindEvents() {
        this.selectElement.addEventListener('change', (event) => {
          this.handleModuleChange(event.target.value);
        });

        // 添加键盘导航支持
        this.selectElement.addEventListener('keydown', (event) => {
          if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            this.selectElement.focus();
          }
        });
      }

      handleModuleChange(newValue) {
        const previousModule = this.selectedModule;
        this.selectedModule = newValue;

        // 验证选择
        const isValid = this.validateSelection();

        // 触发变化事件
        this.onModuleChange({
          selectedModule: this.selectedModule,
          previousModule: previousModule,
          moduleInfo: this.getSelectedModuleInfo(),
          isValid: isValid
        });

        // 更新UI状态
        this.updateUIState();

        // 记录选择
        this.logModuleSelection();
      }

      validateSelection() {
        const isValid = Boolean(this.selectedModule &&
          this.availableModules.some(m => m.value === this.selectedModule));

        // 更新验证状态
        this.selectElement.classList.toggle('invalid', !isValid && Boolean(this.selectedModule));

        // 触发验证变化事件
        this.onValidationChange({
          isValid: isValid,
          selectedModule: this.selectedModule,
          errorMessage: isValid ? null : '请选择有效的模块'
        });

        return isValid;
      }

      updateUIState() {
        // 更新选择器样式
        if (this.selectedModule) {
          this.selectElement.classList.add('has-selection');
        } else {
          this.selectElement.classList.remove('has-selection');
        }

        // 更新ARIA状态
        this.selectElement.setAttribute('aria-invalid', String(!this.validateSelection()));
      }

      getSelectedModuleInfo() {
        if (!this.selectedModule) return null;

        return this.availableModules.find(m => m.value === this.selectedModule) || null;
      }

      logModuleSelection() {
        if (this.selectedModule) {
          const moduleInfo = this.getSelectedModuleInfo();
          console.log('模块选择变化:', {
            module: this.selectedModule,
            label: moduleInfo?.label,
            timestamp: new Date().toISOString()
          });
        }
      }

      // 公共方法
      setDisabled(disabled) {
        this.disabled = disabled;
        this.selectElement.disabled = disabled;
        this.selectElement.setAttribute('aria-disabled', String(disabled));
      }

      getSelectedModule() {
        return this.selectedModule;
      }

      setSelectedModule(moduleValue) {
        if (this.availableModules.some(m => m.value === moduleValue)) {
          this.selectElement.value = moduleValue;
          this.handleModuleChange(moduleValue);
        }
      }

      reset() {
        this.selectElement.value = '';
        this.handleModuleChange('');
      }

      getAvailableModules() {
        return [...this.availableModules];
      }
    }

    // 模块映射（保持向后兼容）
    const moduleMap = {
      sales_order: '销售订单',
      purchase_order: '采购订单',
      production_order: '生产订单',
      subcontract_order: '委外订单',
      applyorder: '请购单',
      subcontract_requisition: '委外请购',
      product_receipt: '产品入库单',
      purchase_receipt: '采购入库',
      subcontract_receipt: '委外入库',
      materialout: '材料出库单',
      sales_out: '销售出库',
      inventory: '现存量',
      inventory_report: '现存量报表',
      requirements_planning: '需求计划',
      material_master: '物料档案'
    };

    // API基础URL - 修复端口配置
    const API_BASE_URL = 'http://localhost:5000';  // 后端服务端口
    const userId = 'Alice';

    // 初始化页面
    async function initializePage() {
      try {
        // 确保ErrorHandler类可用
        if (typeof ErrorHandler === 'undefined') {
          throw new Error('ErrorHandler类未加载，请检查error-handler.js文件');
        }

        // 初始化错误处理器
        errorHandler = new ErrorHandler({
          enableLogging: true,
          enableUserGuidance: true,
          enableRecovery: true,
          maxRetryAttempts: 3,
          retryDelay: 1000
        });

        // 设置全局错误处理器
        window.ErrorHandler = ErrorHandler;
        window.errorHandler = errorHandler;

        initializeNotificationSystem();
        initializeModuleSelector();
        initializeLoadController();
        initializeFieldListDisplay();
        initializeBaselineSave();
        initializeUserConfigSave();
        bindEventListeners();
        showSuccess('页面加载完成，请选择模块开始配置');
      } catch (error) {
        console.error('页面初始化失败:', error);
        if (errorHandler) {
          await errorHandler.handleError(error, {
            source: 'page_initialization',
            operation: 'initialize'
          });
        } else {
          showError('页面初始化失败，请刷新页面重试');
        }
      }
    }

    // 初始化模块选择器
    function initializeModuleSelector() {
      const moduleSelectElement = document.getElementById('moduleSelect');

      moduleSelector = new ModuleSelector(moduleSelectElement, {
        onModuleChange: handleModuleSelectionChange,
        onValidationChange: handleModuleValidationChange
      });
    }

    // 初始化加载控制器
    function initializeLoadController() {
      loadController = new LoadController({
        timeoutDuration: 30000, // 30秒超时
        maxRetries: 3,
        onLoadStart: () => {
          console.log('开始加载字段配置');
          // 禁用模块选择器防止加载过程中切换
          if (moduleSelector) {
            moduleSelector.setDisabled(true);
          }
        },
        onLoadProgress: (progress) => {
          console.log('加载进度更新:', progress);
        },
        onLoadComplete: (result) => {
          console.log('加载完成:', result);
          // 重新启用模块选择器
          if (moduleSelector) {
            moduleSelector.setDisabled(false);
          }
        },
        onLoadError: (errorInfo) => {
          console.error('加载错误:', errorInfo);
          // 重新启用模块选择器
          if (moduleSelector) {
            moduleSelector.setDisabled(false);
          }
        },
        onLoadCancel: () => {
          console.log('加载已取消');
          // 重新启用模块选择器
          if (moduleSelector) {
            moduleSelector.setDisabled(false);
          }
        }
      });
    }

    // 初始化基准保存组件
    function initializeBaselineSave() {
      baselineSaveComponent = new BaselineSaveComponent({
        onSaveStart: () => {
          console.log('基准文件保存开始');
          const saveButton = document.getElementById('saveBaseline');
          saveButton.disabled = true;
          saveButton.classList.add('saving');
          saveButton.innerHTML = '<span class="loading">⏳</span>保存中...';
        },
        onSaveProgress: (progress) => {
          console.log('基准保存进度:', progress);
        },
        onSaveComplete: (result) => {
          console.log('基准文件保存完成:', result);
          const saveButton = document.getElementById('saveBaseline');
          saveButton.disabled = false;
          saveButton.classList.remove('saving');
          saveButton.classList.add('save-success');
          saveButton.innerHTML = '<span>✅</span>保存成功';

          showNotification(`基准文件保存成功：${result.fieldsCount} 个字段`, 'success');

          // 恢复按钮状态
          setTimeout(() => {
            saveButton.classList.remove('save-success');
            saveButton.innerHTML = '<span>📄</span>保存到基准文件';
          }, 3000);
        },
        onSaveError: (errorInfo) => {
          console.error('基准文件保存失败:', errorInfo);
          const saveButton = document.getElementById('saveBaseline');
          saveButton.disabled = false;
          saveButton.classList.remove('saving');
          saveButton.classList.add('save-error');
          saveButton.innerHTML = '<span>❌</span>保存失败';

          showNotification(`基准文件保存失败：${errorInfo.message}`, 'error');

          // 恢复按钮状态
          setTimeout(() => {
            saveButton.classList.remove('save-error');
            saveButton.innerHTML = '<span>📄</span>保存到基准文件';
          }, 3000);
        }
      });
    }

    // 初始化用户配置保存组件
    function initializeUserConfigSave() {
      userConfigSaveComponent = new UserConfigSaveComponent({
        onSaveStart: () => {
          console.log('用户配置保存开始');
          const saveButton = document.getElementById('saveUserConfig');
          saveButton.disabled = true;
          saveButton.classList.add('saving');
          saveButton.innerHTML = '<span class="loading">⏳</span>保存中...';
        },
        onSaveProgress: (progress) => {
          console.log('用户配置保存进度:', progress);
        },
        onSaveComplete: (result) => {
          console.log('用户配置保存完成:', result);
          const saveButton = document.getElementById('saveUserConfig');
          saveButton.disabled = false;
          saveButton.classList.remove('saving');
          saveButton.classList.add('save-success');
          saveButton.innerHTML = '<span>✅</span>保存成功';

          showNotification(`用户配置保存成功：${result.selectedCount}/${result.fieldsCount} 个字段`, 'success');

          // 恢复按钮状态
          setTimeout(() => {
            saveButton.classList.remove('save-success');
            saveButton.innerHTML = '<span>⚙️</span>保存用户配置';
          }, 3000);
        },
        onSaveError: (errorInfo) => {
          console.error('用户配置保存失败:', errorInfo);
          const saveButton = document.getElementById('saveUserConfig');
          saveButton.disabled = false;
          saveButton.classList.remove('saving');
          saveButton.classList.add('save-error');
          saveButton.innerHTML = '<span>❌</span>保存失败';

          showNotification(`用户配置保存失败：${errorInfo.message}`, 'error');

          // 恢复按钮状态
          setTimeout(() => {
            saveButton.classList.remove('save-error');
            saveButton.innerHTML = '<span>⚙️</span>保存用户配置';
          }, 3000);
        }
      });
    }

    // 绑定事件监听器
    function bindEventListeners() {
      const saveBaseline = document.getElementById('saveBaseline');
      const saveUserConfig = document.getElementById('saveUserConfig');

      // 使用新的基准保存组件
      saveBaseline.addEventListener('click', () => handleBaselineSave());
      saveUserConfig.addEventListener('click', () => handleSaveConfig('userconfig'));
    }

    // 处理模块选择变化（新的处理函数）
    function handleModuleSelectionChange(changeInfo) {
      currentModule = changeInfo.selectedModule;

      if (changeInfo.isValid && currentModule) {
        // 更新加载控制器状态
        if (loadController) {
          loadController.setCanLoad(true);
        }

        hideFieldDisplay();
        hideSaveOperations();

        const moduleInfo = changeInfo.moduleInfo;
        showNotification(`已选择模块：${moduleInfo.label}`, 'info');

        // 记录模块选择
        console.log('模块选择更新:', {
          module: currentModule,
          label: moduleInfo.label,
          description: moduleInfo.description
        });
      } else {
        // 更新加载控制器状态
        if (loadController) {
          loadController.setCanLoad(false);
        }

        hideFieldDisplay();
        hideSaveOperations();

        if (changeInfo.selectedModule && !changeInfo.isValid) {
          showNotification('请选择有效的模块', 'warning');
        }
      }

      // 更新加载按钮的ARIA标签
      updateLoadButtonAccessibility();
    }

    // 处理模块验证变化
    function handleModuleValidationChange(validationInfo) {
      const moduleSelect = document.getElementById('moduleSelect');
      const moduleStatus = document.getElementById('moduleStatus');
      const statusIndicator = document.getElementById('statusIndicator');
      const statusText = document.getElementById('statusText');

      if (!validationInfo.isValid && validationInfo.selectedModule) {
        moduleSelect.setAttribute('aria-invalid', 'true');

        // 显示错误状态
        moduleStatus.style.display = 'flex';
        statusIndicator.className = 'status-indicator invalid';
        statusText.textContent = validationInfo.errorMessage;
        statusText.style.color = '#e74c3c';

        showNotification(validationInfo.errorMessage, 'warning');
      } else if (validationInfo.isValid && validationInfo.selectedModule) {
        moduleSelect.setAttribute('aria-invalid', 'false');

        // 显示成功状态
        moduleStatus.style.display = 'flex';
        statusIndicator.className = 'status-indicator valid';
        statusText.textContent = '模块选择有效';
        statusText.style.color = '#27ae60';
      } else {
        moduleSelect.setAttribute('aria-invalid', 'false');

        // 隐藏状态指示器
        moduleStatus.style.display = 'none';
      }
    }

    // 更新加载按钮的无障碍访问属性
    function updateLoadButtonAccessibility() {
      const loadButton = document.getElementById('loadButton');
      const moduleInfo = moduleSelector?.getSelectedModuleInfo();

      if (moduleInfo) {
        loadButton.setAttribute('aria-label', `加载 ${moduleInfo.label} 的字段配置`);
        loadButton.setAttribute('title', `点击加载 ${moduleInfo.label} 模块的字段配置数据`);
      } else {
        loadButton.setAttribute('aria-label', '加载字段配置（请先选择模块）');
        loadButton.setAttribute('title', '请先选择模块后再点击加载');
      }
    }

    // 增强进度显示系统类
    class ProgressDisplay {
      constructor(options = {}) {
        this.visible = false;
        this.currentStage = 'idle';
        this.percentage = 0;
        this.message = '';
        this.details = '';
        this.startTime = null;
        this.stageStartTime = null;
        this.autoHideDelay = options.autoHideDelay || 2000;
        this.showDetails = options.showDetails !== false;
        this.onCancel = options.onCancel || null;

        // 进度阶段配置
        this.stages = {
          preparing: {
            percentage: 30,
            icon: '⚙️',
            message: '准备中...',
            details: '正在初始化加载过程',
            estimatedDuration: 1000
          },
          fetching: {
            percentage: 70,
            icon: '📡',
            message: '获取数据中...',
            details: '正在从服务器获取字段数据',
            estimatedDuration: 3000
          },
          processing: {
            percentage: 85,
            icon: '⚡',
            message: '处理数据中...',
            details: '正在解析和增强字段数据',
            estimatedDuration: 1500
          },
          rendering: {
            percentage: 95,
            icon: '🎨',
            message: '渲染界面中...',
            details: '正在更新用户界面',
            estimatedDuration: 800
          },
          complete: {
            percentage: 100,
            icon: '✅',
            message: '完成！',
            details: '所有操作已成功完成',
            estimatedDuration: 0
          }
        };

        this.initializeElements();
        this.bindEvents();
      }

      initializeElements() {
        this.container = document.getElementById('progressContainer');
        this.stageIcon = document.getElementById('progressStageIcon');
        this.progressText = document.getElementById('progressText');
        this.progressPercentage = document.getElementById('progressPercentage');
        this.progressBar = document.getElementById('progressBar');
        this.progressDetailsText = document.getElementById('progressDetailsText');
        this.progressElapsedTime = document.getElementById('progressElapsedTime');
        this.progressEstimatedTime = document.getElementById('progressEstimatedTime');
        this.stageElements = document.querySelectorAll('.progress-stage');

        // 确保所有元素都存在
        if (!this.container) {
          console.error('Progress container not found');
          return;
        }
      }

      bindEvents() {
        // 添加键盘支持
        if (this.container) {
          this.container.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.onCancel) {
              this.onCancel();
            }
          });
        }
      }

      show(options = {}) {
        if (!this.container) return;

        this.visible = true;
        this.startTime = Date.now();
        this.stageStartTime = Date.now();

        // 重置状态
        this.container.className = 'progress-container show';
        this.container.style.display = 'block';

        // 设置初始状态
        if (options.stage) {
          this.updateStage(options.stage, options.customMessage, options.customDetails);
        } else {
          this.updateStage('preparing');
        }

        // 开始时间更新
        this.startTimeUpdater();

        console.log('Progress display shown');
      }

      hide(delay = null) {
        if (!this.container || !this.visible) return;

        const hideDelay = delay !== null ? delay : this.autoHideDelay;

        setTimeout(() => {
          if (this.container) {
            this.container.classList.add('hide');

            setTimeout(() => {
              if (this.container) {
                this.container.style.display = 'none';
                this.container.className = 'progress-container';
                this.visible = false;
                this.stopTimeUpdater();
              }
            }, 300);
          }
        }, hideDelay);

        console.log(`Progress display will hide in ${hideDelay}ms`);
      }

      updateStage(stage, customMessage = null, customDetails = null) {
        if (!this.stages[stage] || !this.container) return;

        const stageConfig = this.stages[stage];
        this.currentStage = stage;
        this.stageStartTime = Date.now();

        // 更新进度百分比
        this.updateProgress(stageConfig.percentage);

        // 更新图标
        if (this.stageIcon) {
          this.stageIcon.textContent = stageConfig.icon;
        }

        // 更新消息
        const message = customMessage || stageConfig.message;
        if (this.progressText) {
          this.progressText.textContent = message;
        }

        // 更新详细信息
        const details = customDetails || stageConfig.details;
        if (this.progressDetailsText && this.showDetails) {
          this.progressDetailsText.textContent = details;
        }

        // 更新阶段指示器
        this.updateStageIndicators(stage);

        // 更新容器状态
        this.updateContainerState(stage);

        // 更新ARIA属性
        this.updateAccessibility(message, stageConfig.percentage);

        console.log(`Progress stage updated: ${stage} (${stageConfig.percentage}%)`);
      }

      updateProgress(percentage, smooth = true) {
        this.percentage = Math.max(0, Math.min(100, percentage));

        if (this.progressBar) {
          if (smooth) {
            this.progressBar.style.transition = 'width 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
          } else {
            this.progressBar.style.transition = 'none';
          }
          this.progressBar.style.width = `${this.percentage}%`;
        }

        if (this.progressPercentage) {
          this.progressPercentage.textContent = `${Math.round(this.percentage)}%`;
        }

        // 更新ARIA值
        if (this.container) {
          this.container.setAttribute('aria-valuenow', Math.round(this.percentage));
        }
      }

      updateStageIndicators(currentStage) {
        const stageOrder = ['preparing', 'fetching', 'processing', 'rendering', 'complete'];
        const currentIndex = stageOrder.indexOf(currentStage);

        this.stageElements.forEach((element, index) => {
          const stage = element.getAttribute('data-stage');
          const stageIndex = stageOrder.indexOf(stage);

          element.classList.remove('active', 'completed');

          if (stageIndex < currentIndex) {
            element.classList.add('completed');
          } else if (stageIndex === currentIndex) {
            element.classList.add('active');
          }
        });
      }

      updateContainerState(stage) {
        if (!this.container) return;

        // 移除之前的状态类
        this.container.classList.remove('completed', 'error');

        // 添加新的状态类
        if (stage === 'complete') {
          this.container.classList.add('completed');
        }
      }

      updateAccessibility(message, percentage) {
        if (!this.container) return;

        this.container.setAttribute('aria-valuetext', `${message} ${Math.round(percentage)}%`);
        this.container.setAttribute('aria-label', `加载进度: ${message}`);
      }

      startTimeUpdater() {
        this.timeUpdaterInterval = setInterval(() => {
          this.updateElapsedTime();
          this.updateEstimatedTime();
        }, 1000);
      }

      stopTimeUpdater() {
        if (this.timeUpdaterInterval) {
          clearInterval(this.timeUpdaterInterval);
          this.timeUpdaterInterval = null;
        }
      }

      updateElapsedTime() {
        if (!this.startTime || !this.progressElapsedTime) return;

        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        this.progressElapsedTime.textContent = `已用时: ${elapsed}s`;
      }

      updateEstimatedTime() {
        if (!this.startTime || !this.progressEstimatedTime) return;

        const elapsed = Date.now() - this.startTime;
        const progress = this.percentage / 100;

        if (progress > 0.1) {
          const estimated = Math.ceil((elapsed / progress - elapsed) / 1000);
          this.progressEstimatedTime.textContent = `预计剩余: ${Math.max(0, estimated)}s`;
        } else {
          this.progressEstimatedTime.textContent = '预计剩余: --';
        }
      }

      setError(message, details = '') {
        if (!this.container) return;

        this.container.classList.add('error');

        if (this.stageIcon) {
          this.stageIcon.textContent = '❌';
        }

        if (this.progressText) {
          this.progressText.textContent = message || '发生错误';
        }

        if (this.progressDetailsText && details) {
          this.progressDetailsText.textContent = details;
        }

        this.updateAccessibility(message || '发生错误', this.percentage);

        console.log('Progress display error state set:', message);
      }

      // 公共方法
      isVisible() {
        return this.visible;
      }

      getCurrentStage() {
        return this.currentStage;
      }

      getProgress() {
        return {
          stage: this.currentStage,
          percentage: this.percentage,
          message: this.message,
          details: this.details,
          visible: this.visible
        };
      }

      reset() {
        this.currentStage = 'idle';
        this.percentage = 0;
        this.message = '';
        this.details = '';
        this.startTime = null;
        this.stageStartTime = null;

        if (this.container) {
          this.container.className = 'progress-container';
          this.container.style.display = 'none';
        }

        this.stopTimeUpdater();
        this.visible = false;
      }

      // 便捷方法
      showPreparing(message, details) {
        this.show({ stage: 'preparing', customMessage: message, customDetails: details });
      }

      showFetching(message, details) {
        this.updateStage('fetching', message, details);
      }

      showProcessing(message, details) {
        this.updateStage('processing', message, details);
      }

      showRendering(message, details) {
        this.updateStage('rendering', message, details);
      }

      showComplete(message, details) {
        this.updateStage('complete', message, details);
        setTimeout(() => this.hide(), this.autoHideDelay);
      }
    }

    // 手动加载控制器类
    class LoadController {
      constructor(options = {}) {
        this.isLoading = false;
        this.canLoad = false;
        this.loadProgress = {
          stage: 'idle',
          percentage: 0,
          message: '',
          details: ''
        };
        this.abortController = null;
        this.loadTimeout = null;
        this.timeoutDuration = options.timeoutDuration || 30000; // 30秒超时
        this.maxRetries = options.maxRetries || 3;
        this.retryCount = 0;
        this.onLoadStart = options.onLoadStart || (() => { });
        this.onLoadProgress = options.onLoadProgress || (() => { });
        this.onLoadComplete = options.onLoadComplete || (() => { });
        this.onLoadError = options.onLoadError || (() => { });
        this.onLoadCancel = options.onLoadCancel || (() => { });

        // 初始化增强进度显示系统
        this.progressDisplay = new ProgressDisplay({
          autoHideDelay: 2000,
          showDetails: true,
          onCancel: () => this.cancelLoad()
        });

        this.initializeElements();
        this.bindEvents();
      }

      initializeElements() {
        this.loadButton = document.getElementById('loadButton');

        // 添加取消按钮
        if (!document.getElementById('cancelButton')) {
          const cancelButton = document.createElement('button');
          cancelButton.id = 'cancelButton';
          cancelButton.className = 'load-button cancel-button';
          cancelButton.innerHTML = '<span>❌</span>取消加载';
          cancelButton.style.display = 'none';
          cancelButton.style.marginLeft = '16px';
          cancelButton.style.background = 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)';

          this.loadButton.parentNode.appendChild(cancelButton);
          this.cancelButton = cancelButton;
        } else {
          this.cancelButton = document.getElementById('cancelButton');
        }
      }

      bindEvents() {
        this.loadButton.addEventListener('click', () => this.handleLoadClick());
        this.cancelButton.addEventListener('click', () => this.cancelLoad());

        // 键盘支持
        this.loadButton.addEventListener('keydown', (event) => {
          if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            this.handleLoadClick();
          }
        });

        this.cancelButton.addEventListener('keydown', (event) => {
          if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            this.cancelLoad();
          }
        });
      }

      async handleLoadClick() {
        if (!this.canLoad || this.isLoading) {
          console.warn('加载条件不满足:', { canLoad: this.canLoad, isLoading: this.isLoading });
          return;
        }

        try {
          await this.startLoad();
        } catch (error) {
          if (error.name === 'AbortError') {
            console.log('加载被用户取消');
          } else {
            console.error('加载过程中发生错误:', error);
            this.handleLoadError(error);
          }
        }
      }

      async startLoad() {
        // 防止重复加载
        if (this.isLoading) {
          throw new Error('加载已在进行中');
        }

        // 验证加载条件
        if (!currentModule) {
          throw new Error('请先选择模块');
        }

        this.isLoading = true;
        this.retryCount = 0;
        this.abortController = new AbortController();

        // 设置超时
        this.loadTimeout = setTimeout(() => {
          this.abortController.abort();
          this.handleLoadError(new Error('加载超时，请检查网络连接'));
        }, this.timeoutDuration);

        // 更新UI状态
        this.updateLoadingState(true);
        this.progressDisplay.show();
        this.onLoadStart();

        try {
          await this.executeLoad();
          this.handleLoadSuccess();
        } catch (error) {
          if (error.name === 'AbortError') {
            this.handleLoadCancel();
          } else {
            throw error;
          }
        } finally {
          this.cleanup();
        }
      }

      async executeLoad() {
        // 准备阶段
        const moduleInfo = moduleSelector?.getSelectedModuleInfo();
        const moduleName = moduleInfo?.label || moduleMap[currentModule] || currentModule;

        this.progressDisplay.showPreparing('准备中...', '正在初始化加载过程');

        // 检查是否被取消
        this.checkAborted();

        // 模拟准备阶段延迟
        await this.delay(800);

        // 获取数据阶段
        this.progressDisplay.showFetching('获取数据中...', `正在从服务器获取 ${moduleName} 字段数据`);

        // 执行API请求
        const response = await this.fetchWithTimeout(
          `${API_BASE_URL}/api/v1/field-config/modules/${currentModule}/fields?max_depth=10&user_id=${userId}`,
          {
            signal: this.abortController.signal,
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            }
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.message || '获取字段数据失败');
        }

        this.checkAborted();

        // 处理数据阶段
        this.progressDisplay.showProcessing('处理数据中...', '正在解析和增强字段数据');

        // 处理数据
        await this.processFieldData(data);

        this.checkAborted();

        // 渲染界面阶段
        this.progressDisplay.showRendering('渲染界面中...', '正在更新用户界面');

        // 渲染界面
        await this.renderResults();

        // 完成阶段
        this.progressDisplay.showComplete('完成！', `成功加载 ${rawFieldData.length} 个字段配置`);
      }

      async processFieldData(data) {
        // 存储原始数据
        rawFieldData = Object.values(data.data?.fields || {});

        // 检查是否被取消
        this.checkAborted();

        // 模拟数据处理延迟（分阶段显示进度）
        await this.delay(500);

        // 增强处理
        enhancedFieldData = rawFieldData.map((field, index) => {
          // 每处理一定数量的字段更新一次进度
          if (index % Math.max(1, Math.floor(rawFieldData.length / 10)) === 0) {
            const progress = 85 + (index / rawFieldData.length) * 10; // 85-95%
            this.progressDisplay.updateProgress(progress);
          }

          return {
            ...field,
            chinese_name: field.chinese_name || generateChineseName(field.api_field_name || field.api_name),
            data_type: field.data_type || inferDataType(field.sample_value),
            etl_score: field.etl_score || Math.random() * 100,
            is_selected: field.is_selected || false,
            locked: field.locked || false
          };
        });

        // 最终处理延迟
        await this.delay(300);
      }

      async renderResults() {
        // 渲染字段列表
        renderFieldList();

        // 模拟渲染延迟
        await this.delay(300);

        // 更新统计信息
        updateStatistics();

        // 显示界面元素
        showFieldDisplay();
        showSaveOperations();

        // 最终渲染延迟
        await this.delay(200);
      }

      async fetchWithTimeout(url, options = {}) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeoutDuration);

        try {
          const response = await fetch(url, {
            ...options,
            signal: options.signal || controller.signal
          });
          clearTimeout(timeoutId);
          return response;
        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }
      }

      checkAborted() {
        if (this.abortController.signal.aborted) {
          throw new Error('操作已被取消');
        }
      }

      delay(ms) {
        return new Promise((resolve, reject) => {
          const timeoutId = setTimeout(resolve, ms);

          // 如果被取消，清除延迟并拒绝
          this.abortController.signal.addEventListener('abort', () => {
            clearTimeout(timeoutId);
            reject(new Error('操作已被取消'));
          });
        });
      }

      updateProgress(stage, percentage, message, details = '') {
        this.loadProgress = {
          stage,
          percentage,
          message,
          details
        };

        // 触发进度回调
        this.onLoadProgress(this.loadProgress);
      }

      updateLoadingState(loading) {
        this.isLoading = loading;

        if (loading) {
          this.loadButton.disabled = true;
          this.loadButton.innerHTML = '<span class="loading">⏳</span>加载中...';
          this.loadButton.setAttribute('aria-busy', 'true');

          this.cancelButton.style.display = 'inline-flex';
          this.cancelButton.disabled = false;
        } else {
          this.loadButton.disabled = !this.canLoad;
          this.loadButton.innerHTML = this.canLoad ? '<span>📥</span>重新加载' : '<span>📥</span>请先选择模块';
          this.loadButton.setAttribute('aria-busy', 'false');

          this.cancelButton.style.display = 'none';
        }
      }



      handleLoadSuccess() {
        const moduleInfo = moduleSelector?.getSelectedModuleInfo();
        const moduleName = moduleInfo?.label || moduleMap[currentModule] || currentModule;

        showNotification(`成功加载 ${moduleName} 字段配置`, 'success');
        this.onLoadComplete({
          module: currentModule,
          rawFieldCount: rawFieldData.length,
          enhancedFieldCount: enhancedFieldData.length
        });

        console.log('字段加载成功:', {
          module: currentModule,
          rawFields: rawFieldData.length,
          enhancedFields: enhancedFieldData.length,
          timestamp: new Date().toISOString()
        });
      }

      handleLoadError(error) {
        console.error('加载失败:', error);

        // 检查是否可以重试
        if (this.retryCount < this.maxRetries && !this.abortController.signal.aborted) {
          this.retryCount++;
          const retryMessage = `加载失败，正在重试 (${this.retryCount}/${this.maxRetries})...`;

          // 显示重试状态
          this.progressDisplay.updateStage('preparing', '重试中...', retryMessage);
          showNotification(retryMessage, 'warning');

          setTimeout(() => {
            if (!this.abortController.signal.aborted) {
              this.executeLoad().catch(err => this.handleLoadError(err));
            }
          }, 2000);

          return;
        }

        // 显示错误信息
        const errorMessage = this.getErrorMessage(error);
        this.progressDisplay.setError('加载失败', errorMessage);
        showNotification(`加载失败：${errorMessage}`, 'error');

        this.onLoadError({
          error,
          message: errorMessage,
          retryCount: this.retryCount
        });

        // 延迟隐藏进度显示
        setTimeout(() => {
          this.progressDisplay.hide();
        }, 3000);
      }

      handleLoadCancel() {
        console.log('加载被用户取消');

        // 显示取消状态
        this.progressDisplay.updateStage('preparing', '已取消', '用户取消了加载操作');
        showNotification('加载已取消', 'info');

        this.onLoadCancel();

        // 延迟隐藏进度显示
        setTimeout(() => {
          this.progressDisplay.hide();
        }, 1500);
      }

      getErrorMessage(error) {
        if (error.name === 'AbortError') {
          return '操作已取消';
        }

        if (error.message.includes('fetch')) {
          return '网络连接失败，请检查网络设置';
        }

        if (error.message.includes('timeout')) {
          return '请求超时，请稍后重试';
        }

        if (error.message.includes('HTTP 404')) {
          return '请求的资源不存在';
        }

        if (error.message.includes('HTTP 500')) {
          return '服务器内部错误';
        }

        return error.message || '未知错误';
      }

      cancelLoad() {
        if (!this.isLoading) return;

        console.log('用户取消加载操作');

        if (this.abortController) {
          this.abortController.abort();
        }

        this.cleanup();
        this.updateLoadingState(false);
      }

      cleanup() {
        if (this.loadTimeout) {
          clearTimeout(this.loadTimeout);
          this.loadTimeout = null;
        }

        if (this.abortController) {
          this.abortController = null;
        }

        this.isLoading = false;
        this.updateLoadingState(false);
      }

      // 公共方法
      setCanLoad(canLoad) {
        this.canLoad = canLoad;

        if (!this.isLoading) {
          this.loadButton.disabled = !canLoad;
          this.loadButton.innerHTML = canLoad ? '<span>📥</span>加载字段配置' : '<span>📥</span>请先选择模块';
        }
      }

      getLoadState() {
        return {
          isLoading: this.isLoading,
          canLoad: this.canLoad,
          progress: { ...this.loadProgress },
          retryCount: this.retryCount
        };
      }

      reset() {
        this.cancelLoad();
        this.loadProgress = {
          stage: 'idle',
          percentage: 0,
          message: '',
          details: ''
        };
        this.retryCount = 0;
        this.progressDisplay.reset();
      }
    }

    // 全局加载控制器实例
    let loadController = null;

    // 全局基准保存组件实例
    let baselineSaveComponent = null;

    // 全局用户配置保存组件实例
    let userConfigSaveComponent = null;

    // 处理字段加载（保持向后兼容）
    async function handleLoadFields() {
      if (loadController) {
        await loadController.handleLoadClick();
      }
    }

    // 加载字段数据
    async function loadFieldData() {
      updateProgress(10, '准备中...', '正在初始化加载过程');

      try {
        updateProgress(30, '获取数据中...', `正在从服务器获取 ${moduleMap[currentModule]} 字段数据`);

        const response = await fetch(`${API_BASE_URL}/api/v1/field-config/modules/${currentModule}/fields?max_depth=10&user_id=${userId}`);

        if (!response.ok) {
          const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
          error.status = response.status;
          throw error;
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.message || '获取字段数据失败');
        }

        updateProgress(70, '处理数据中...', '正在解析和处理字段数据');

        // 存储原始数据
        rawFieldData = Object.values(data.data?.fields || {});

        // 模拟增强处理（实际应该调用JSON匹配器）
        enhancedFieldData = rawFieldData.map(field => ({
          ...field,
          chinese_name: field.chinese_name || generateChineseName(field.api_field_name || field.api_name),
          data_type: field.data_type || inferDataType(field.sample_value),
          etl_score: field.etl_score || Math.random() * 100,
          is_selected: field.is_selected || false,
          locked: field.locked || false
        }));

        updateProgress(90, '渲染界面中...', '正在更新用户界面');

        renderFieldList();
        updateStatistics();

        updateProgress(100, '完成！', '字段数据加载完成');

      } catch (error) {
        // 使用错误处理器处理错误
        if (errorHandler) {
          const result = await errorHandler.handleError(error, {
            source: 'field_data_loading',
            operation: 'loadFieldData',
            module: currentModule,
            retryFunction: () => loadFieldData(),
            fallbackData: {
              fields: [],
              message: '使用空数据，请重试加载'
            }
          });

          if (result.success && result.result && result.result.fields) {
            // 使用备用数据
            rawFieldData = result.result.fields;
            enhancedFieldData = rawFieldData;
            renderFieldList();
            updateStatistics();
            return;
          }
        }

        throw new Error(`加载字段数据失败: ${error.message}`);
      }
    }

    // 生成中文名称（简单示例）
    function generateChineseName(apiName) {
      const nameMap = {
        'code': '编码',
        'name': '名称',
        'date': '日期',
        'time': '时间',
        'amount': '金额',
        'quantity': '数量',
        'price': '价格',
        'status': '状态',
        'type': '类型',
        'id': 'ID'
      };

      for (const [key, value] of Object.entries(nameMap)) {
        if (apiName.toLowerCase().includes(key)) {
          return value;
        }
      }

      return apiName;
    }

    // 推断数据类型
    function inferDataType(sampleValue) {
      if (!sampleValue) return 'string';

      if (/^\d+$/.test(sampleValue)) return 'integer';
      if (/^\d+\.\d+$/.test(sampleValue)) return 'decimal';
      if (/^\d{4}-\d{2}-\d{2}/.test(sampleValue)) return 'date';
      if (/^(true|false)$/i.test(sampleValue)) return 'boolean';

      return 'string';
    }

    // 全局字段列表显示组件实例
    let fieldListDisplayComponent = null;

    // 初始化增强字段列表显示组件
    function initializeFieldListDisplay() {
      const fieldListContainer = document.getElementById('fieldList');

      if (fieldListDisplayComponent) {
        fieldListDisplayComponent.destroy();
      }

      // 使用增强的字段列表显示组件，集成性能优化功能
      fieldListDisplayComponent = new EnhancedFieldListDisplay(fieldListContainer, {
        // 基础功能
        enableVirtualScroll: true,
        enableSearch: true,

        // 性能优化配置
        enablePerformanceMode: true,
        enableSmartRendering: true,
        enableDataCache: true,

        // 虚拟滚动配置
        itemHeight: 60,
        bufferSize: 5,
        overscan: 3,

        // 动画配置
        enableAnimations: !window.matchMedia('(prefers-reduced-motion: reduce)').matches,
        animationDuration: 300,

        // 缓存配置
        cacheSize: 1000,
        enableFilter: true,
        enableSelection: true,
        enableEditing: true,
        visibleItems: 15,
        itemHeight: 60,

        // 回调函数
        onFieldSelect: (info) => {
          console.log('字段被选择:', info);
          // 可以在这里添加字段选择的处理逻辑
        },

        onFieldEdit: (info) => {
          console.log('字段被编辑:', info);
          // 更新enhancedFieldData中的对应字段
          const fieldIndex = enhancedFieldData.findIndex(f =>
            (f.name || f.api_field_name) === info.fieldName
          );
          if (fieldIndex !== -1) {
            enhancedFieldData[fieldIndex] = { ...info.field };
          }

          // 更新统计信息
          updateStatistics();
        },

        onSelectionChange: (info) => {
          console.log('选择状态变化:', info);

          // 更新enhancedFieldData中的选择状态
          info.selectedFields.forEach(fieldName => {
            const field = enhancedFieldData.find(f =>
              (f.name || f.api_field_name) === fieldName
            );
            if (field) {
              field.is_selected = true;
            }
          });

          // 清除未选择的字段
          enhancedFieldData.forEach(field => {
            const fieldName = field.name || field.api_field_name;
            if (!info.selectedFields.includes(fieldName)) {
              field.is_selected = false;
            }
          });

          // 更新统计信息
          updateStatistics();
        },

        onFilterChange: (info) => {
          console.log('过滤器变化:', info);
          // 可以在这里添加过滤变化的处理逻辑
        }
      });

      console.log('字段列表显示组件已初始化');
    }

    // 渲染字段列表（简化版本）
    function renderFieldList() {
      try {
        const fieldListContainer = document.getElementById('fieldList');

        if (enhancedFieldData.length === 0) {
          fieldListContainer.innerHTML = '<div class="empty-state"><div class="empty-state-icon">📭</div><div class="empty-state-text">暂无字段数据</div></div>';
          return;
        }

        // 创建简单的字段列表HTML
        let html = '<div class="simple-field-list">';

        enhancedFieldData.forEach((field, index) => {
          const fieldName = field.name || field.api_field_name;
          const chineseName = field.chinese_name || fieldName;
          const sampleValue = field.sample_value || '';
          const dataType = field.data_type || 'Unknown';

          html += `
                    <div class="field-item" data-field="${fieldName}">
                      <div class="field-info">
                        <div class="field-name">${fieldName}</div>
                        <div class="field-sample">${sampleValue}</div>
                        <div class="field-chinese">${chineseName}</div>
                        <div class="field-type">${dataType}</div>
                      </div>
                    </div>
                  `;
        });

        html += '</div>';
        fieldListContainer.innerHTML = html;

        console.log('字段列表已渲染:', {
          total: enhancedFieldData.length,
          container: fieldListContainer
        });

        // 显示字段显示区域
        document.getElementById('fieldDisplay').style.display = 'block';

      } catch (error) {
        console.error('渲染字段列表失败:', error);
        const fieldListContainer = document.getElementById('fieldList');
        fieldListContainer.innerHTML = '<div class="error-state"><div class="error-icon">❌</div><div class="error-text">字段列表渲染失败</div></div>';
      }
    }

    // 更新统计信息
    function updateStatistics() {
      const totalFields = enhancedFieldData.length;
      const selectedFields = enhancedFieldData.filter(f => f.is_selected).length;
      const enhancedFields = enhancedFieldData.filter(f => f.chinese_name && f.chinese_name !== f.api_field_name).length;

      document.getElementById('totalFields').textContent = totalFields;
      document.getElementById('selectedFields').textContent = selectedFields;
      document.getElementById('enhancedFields').textContent = enhancedFields;
    }

    // 处理基准文件保存
    async function handleBaselineSave() {
      if (!currentModule || !rawFieldData || rawFieldData.length === 0) {
        showNotification('没有可保存的基准数据', 'warning');
        return;
      }

      if (baselineSaveComponent && baselineSaveComponent.isLoading()) {
        showNotification('基准文件保存正在进行中', 'info');
        return;
      }

      try {
        await baselineSaveComponent.saveBaseline(currentModule, rawFieldData, userId);
      } catch (error) {
        console.error('基准文件保存失败:', error);
        // 错误处理已在组件回调中处理
      }
    }

    // 处理用户配置保存
    async function handleUserConfigSave() {
      if (!currentModule || !enhancedFieldData || Object.keys(enhancedFieldData).length === 0) {
        showNotification('没有可保存的用户配置数据', 'warning');
        return;
      }

      if (userConfigSaveComponent && userConfigSaveComponent.isLoading()) {
        showNotification('用户配置保存正在进行中', 'info');
        return;
      }

      try {
        await userConfigSaveComponent.saveUserConfig(currentModule, enhancedFieldData, userId);
      } catch (error) {
        console.error('用户配置保存失败:', error);
        // 错误处理已在组件回调中处理
      }
    }

    // 处理用户配置保存
    async function handleSaveConfig(type) {
      if (type === 'baseline') {
        // 使用新的基准保存组件
        return handleBaselineSave();
      }

      if (type === 'userconfig') {
        // 使用新的用户配置保存组件
        return handleUserConfigSave();
      }
    }



    // 显示进度
    function showProgress() {
      const progressContainer = document.getElementById('progressContainer');
      progressContainer.style.display = 'block';
      progressContainer.classList.add('fade-in');
    }

    // 隐藏进度
    function hideProgress() {
      const progressContainer = document.getElementById('progressContainer');
      setTimeout(() => {
        progressContainer.style.display = 'none';
        progressContainer.classList.remove('fade-in');
      }, 1000);
    }

    // 更新进度
    function updateProgress(percentage, text, details = '') {
      const progressBar = document.getElementById('progressBar');
      const progressText = document.getElementById('progressText');
      const progressPercentage = document.getElementById('progressPercentage');
      const progressDetails = document.getElementById('progressDetails');

      progressBar.style.width = `${percentage}%`;
      progressText.textContent = text;
      progressPercentage.textContent = `${percentage}%`;
      progressDetails.textContent = details;

      // 更新ARIA属性
      const progressContainer = document.getElementById('progressContainer');
      progressContainer.setAttribute('aria-valuenow', percentage);
      progressContainer.setAttribute('aria-valuetext', `${text} ${percentage}%`);
    }

    // 显示字段显示区域
    function showFieldDisplay() {
      const fieldDisplay = document.getElementById('fieldDisplay');
      fieldDisplay.style.display = 'block';
      fieldDisplay.classList.add('fade-in');
    }

    // 隐藏字段显示区域
    function hideFieldDisplay() {
      const fieldDisplay = document.getElementById('fieldDisplay');
      fieldDisplay.style.display = 'none';
      fieldDisplay.classList.remove('fade-in');
    }

    // 显示保存操作区域
    function showSaveOperations() {
      const saveOperations = document.getElementById('saveOperations');
      saveOperations.style.display = 'block';
      saveOperations.classList.add('fade-in');
    }

    // 隐藏保存操作区域
    function hideSaveOperations() {
      const saveOperations = document.getElementById('saveOperations');
      saveOperations.style.display = 'none';
      saveOperations.classList.remove('fade-in');
    }

    // 初始化通知系统
    let notificationSystem;

    function initializeNotificationSystem() {
      notificationSystem = new NotificationSystem({
        maxNotifications: 5,
        defaultDuration: 5000,
        position: 'top-right',
        enableSound: false,
        enablePersistence: true
      });

      // 监听通知事件
      document.addEventListener('notificationSystem:notificationAdded', function (event) {
        console.log('通知已添加:', event.detail.notification.message);
      });

      document.addEventListener('notificationSystem:notificationDismissed', function (event) {
        console.log('通知已移除:', event.detail.notification.message);
      });

      document.addEventListener('notificationSystem:notificationClicked', function (event) {
        console.log('通知被点击:', event.detail.notification.message);
      });
    }

    // 错误处理
    window.addEventListener('error', function (e) {
      if (e.message && e.message.includes('runtime.lastError')) {
        console.log('浏览器扩展通信错误，已忽略');
        return false;
      }
      console.error('页面错误:', e);
      if (typeof showError === 'function') {
        showError('页面发生错误，请刷新重试', {
          actions: [{
            label: '刷新页面',
            handler: () => window.location.reload()
          }]
        });
      }
    });

    window.addEventListener('unhandledrejection', async function (e) {
      if (e.reason && e.reason.message && e.reason.message.includes('runtime.lastError')) {
        console.log('浏览器扩展Promise错误，已忽略');
        e.preventDefault();
        return;
      }

      console.error('未处理的Promise错误:', e);

      // 使用错误处理器处理未捕获的Promise错误
      if (errorHandler) {
        await errorHandler.handleError(e.reason || new Error('未处理的Promise错误'), {
          source: 'unhandled_promise',
          event: e
        });
      } else if (typeof showError === 'function') {
        showError('系统发生错误，请刷新重试', {
          actions: [{
            label: '刷新页面',
            handler: () => window.location.reload()
          }]
        });
      }

      e.preventDefault();
    });

    // 性能监控和报告
    let performanceReportInterval = null;

    function startPerformanceMonitoring() {
      // 每5秒输出一次性能报告
      performanceReportInterval = setInterval(() => {
        if (fieldListDisplayComponent && fieldListDisplayComponent.getPerformanceReport) {
          const report = fieldListDisplayComponent.getPerformanceReport();
          console.log('字段列表性能报告:', report);

          // 如果性能过低，显示警告
          if (report.overall && report.overall.fps < 30) {
            console.warn('检测到性能问题，FPS:', report.overall.fps);
            showNotification('检测到性能问题，已启用优化模式', 'warning');
          }
        }
      }, 5000);
    }

    function stopPerformanceMonitoring() {
      if (performanceReportInterval) {
        clearInterval(performanceReportInterval);
        performanceReportInterval = null;
      }
    }

    // 添加性能优化控制面板（开发模式）
    function addPerformanceControls() {
      if (!window.location.search.includes('debug=true')) return;

      const controlPanel = document.createElement('div');
      controlPanel.style.cssText = `
                position: fixed;
                top: 10px;
                left: 10px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
                z-index: 10000;
                font-family: monospace;
              `;

      controlPanel.innerHTML = `
                <div>性能优化控制面板</div>
                <button onclick="toggleVirtualScroll()">切换虚拟滚动</button>
                <button onclick="toggleAnimations()">切换动画</button>
                <button onclick="clearCache()">清空缓存</button>
                <button onclick="showPerformanceReport()">性能报告</button>
              `;

      document.body.appendChild(controlPanel);

      // 添加控制函数到全局
      window.toggleVirtualScroll = () => {
        if (fieldListDisplayComponent) {
          fieldListDisplayComponent.options.enableVirtualScroll = !fieldListDisplayComponent.options.enableVirtualScroll;
          fieldListDisplayComponent.renderFields();
          console.log('虚拟滚动:', fieldListDisplayComponent.options.enableVirtualScroll);
        }
      };

      window.toggleAnimations = () => {
        if (fieldListDisplayComponent) {
          fieldListDisplayComponent.options.enableAnimations = !fieldListDisplayComponent.options.enableAnimations;
          console.log('动画效果:', fieldListDisplayComponent.options.enableAnimations);
        }
      };

      window.clearCache = () => {
        if (fieldListDisplayComponent && fieldListDisplayComponent.dataCache) {
          fieldListDisplayComponent.dataCache.clear();
          console.log('缓存已清空');
        }
      };

      window.showPerformanceReport = () => {
        if (fieldListDisplayComponent && fieldListDisplayComponent.getPerformanceReport) {
          const report = fieldListDisplayComponent.getPerformanceReport();
          console.table(report);
          alert(JSON.stringify(report, null, 2));
        }
      };
    }

    // 页面卸载时的清理
    window.addEventListener('beforeunload', () => {
      stopPerformanceMonitoring();
      if (fieldListDisplayComponent) {
        fieldListDisplayComponent.destroy();
      }
    });

    // 页面可见性变化处理
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // 页面隐藏时暂停性能监控
        stopPerformanceMonitoring();
      } else {
        // 页面显示时恢复性能监控
        startPerformanceMonitoring();
      }
    });

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', () => {
      initializePage();
      startPerformanceMonitoring();
      addPerformanceControls();
    });

    // JavaScript加载完成检查
    function checkDependencies() {
      const requiredComponents = [
        'NotificationSystem',
        'ErrorHandler',
        'FieldListDisplay',
        'BaselineSave',
        'UserConfigSave'
      ];

      const missing = requiredComponents.filter(component => typeof window[component] === 'undefined');

      if (missing.length > 0) {
        console.warn('缺少组件:', missing);
        // 延迟重试
        setTimeout(checkDependencies, 100);
        return false;
      }

      console.log('✅ 所有组件加载完成');
      return true;
    }

    // 确保在DOM加载完成后检查依赖
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', function () {
        checkDependencies();
      });
    } else {
      checkDependencies();

    }
  </script>
</body>

</html>