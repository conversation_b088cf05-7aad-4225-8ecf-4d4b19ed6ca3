# 第三阶段：深入系统测试和功能验证计划

**创建时间**: 2025年8月6日  
**阶段目标**: 确保迁移后的系统功能完整性、性能稳定性和数据一致性  
**预估工期**: 7-14天（与graveyard清除策略同步）  

## 🎯 核心验证目标

### 1. 功能完整性验证
- ✅ 所有15个模块的业务逻辑验证
- ✅ API接口响应格式一致性检查
- ✅ 数据库读写操作验证
- ✅ 错误处理机制验证

### 2. 性能基准测试
- ✅ 响应时间对比（新系统 vs 旧系统基准）
- ✅ 并发处理能力测试
- ✅ 内存使用情况监控
- ✅ 数据库查询性能分析

### 3. 数据一致性验证
- ✅ 新旧系统数据输出对比
- ✅ 边界条件测试
- ✅ 异常情况处理验证

## 🚀 系统启动指南

### 前端启动命令
```bash
# 方法1: 直接启动前端页面
cd frontend
# 使用内置服务器启动（推荐用于测试）
python -m http.server 8080
# 访问: http://localhost:8080/field-config.html

# 方法2: 使用Node.js（如果需要）
# 检查package.json是否存在
npm install  # 如果有依赖
npm start    # 如果配置了启动脚本
```

### 后端启动命令
```bash
# 方法1: 直接启动API服务器
cd backend
python start_server.py
# 默认端口: 5000

# 方法2: 简化启动
python start_simple.py
# 轻量级服务器，适合测试

# 方法3: 使用gunicorn（生产环境推荐）
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Docker环境启动（完整环境）
```bash
# 启动完整的绞杀环境
.\start_strangler.bat

# 或者单独启动服务
docker-compose -f docker-compose.strangler.yml up -d
```

## 📋 详细测试计划

### 阶段1: 基础启动验证（第1天）
| 验证项目 | 测试命令 | 预期结果 | 验证状态 |
|---------|----------|----------|----------|
| 后端服务启动 | `python backend/start_server.py` | 端口5000响应200 | 🔄 待验证 |
| 前端页面加载 | `访问 http://localhost:8080` | 页面正常显示 | 🔄 待验证 |
| 数据库连接 | `python scripts/database_dual_writer_simple.py --test` | 连接成功 | 🔄 待验证 |
| API健康检查 | `curl http://localhost:5000/api/health` | 返回健康状态 | 🔄 待验证 |

### 阶段2: 模块功能验证（第2-8天）
每个模块需要验证以下4个方面：

#### 模块验证模板
```bash
# 以"材料出库单列表查询"为例
# 1. API接口测试
curl -X POST http://localhost:5000/api/material_outbound_list \
  -H "Content-Type: application/json" \
  -d '{"test": "real_data"}'

# 2. 前端界面测试
# 访问对应的前端页面，测试完整流程

# 3. 数据库验证
python scripts/verify_module_data.py --module "材料出库单列表查询"

# 4. 性能基准测试
python scripts/performance_benchmark.py --module "材料出库单列表查询"
```

| 模块名称 | API测试 | 前端测试 | 数据验证 | 性能测试 | 状态 |
|---------|---------|----------|----------|----------|------|
| 材料出库单列表查询 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 采购订单列表 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 采购入库单列表 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 产品入库单列表查询 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 请购单列表查询 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 生产订单列表查询 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 委外订单列表 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 委外入库列表查询 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 委外申请列表查询 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 物料档案批量详情查询 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 现存量报表查询 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 销售出库列表查询 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 销售订单 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 需求计划 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |
| 业务日志 | ⏳ | ⏳ | ⏳ | ⏳ | 🔄 待测试 |

### 阶段3: 压力测试与优化（第9-12天）
| 测试类型 | 测试工具 | 测试目标 | 验证状态 |
|---------|----------|----------|----------|
| 并发测试 | Apache Bench | 100并发用户 | 🔄 待测试 |
| 负载测试 | Locust | 持续1小时负载 | 🔄 待测试 |
| 内存分析 | py-spy | 内存泄漏检测 | 🔄 待测试 |
| 数据库性能 | SQL Profile | 慢查询优化 | 🔄 待测试 |

### 阶段4: 生产就绪验证（第13-14天）
| 验证项目 | 验证方法 | 通过标准 | 验证状态 |
|---------|----------|----------|----------|
| 部署脚本 | 运行部署命令 | 无错误执行 | 🔄 待验证 |
| 监控系统 | Prometheus/Grafana | 所有指标正常 | 🔄 待验证 |
| 备份恢复 | 模拟故障恢复 | 30秒内恢复 | 🔄 待验证 |
| 文档完整性 | 检查所有文档 | 覆盖所有功能 | 🔄 待验证 |

## 🧹 测试代码和模拟代码清理策略

### 清理原则
1. **保留有价值的测试代码**: 单元测试、集成测试保留
2. **删除临时测试文件**: mock数据、debug代码删除
3. **清理模拟代码**: 硬编码测试数据、临时接口删除

### 清理检查清单
```bash
# 1. 扫描测试文件
find . -name "*test*" -type f | grep -E "\.(py|js|html)$" > test_files_scan.txt

# 2. 扫描模拟数据
find . -name "*mock*" -o -name "*dummy*" -o -name "*fake*" | grep -v node_modules > mock_files_scan.txt

# 3. 扫描临时文件
find . -name "*temp*" -o -name "*tmp*" -o -name "*.backup" > temp_files_scan.txt

# 4. 检查硬编码测试数据
grep -r "test_data\|dummy_data\|mock_response" --include="*.py" . > hardcoded_test_data.txt
```

| 文件类型 | 处理策略 | 执行时机 | 状态 |
|---------|----------|----------|------|
| 单元测试文件 | 保留并整理 | 功能验证通过后 | 🔄 待处理 |
| 集成测试文件 | 保留并优化 | 功能验证通过后 | 🔄 待处理 |
| Mock数据文件 | 删除或归档 | 真实数据测试通过后 | 🔄 待处理 |
| Debug调试代码 | 删除 | 每个模块验证后 | 🔄 待处理 |
| 临时测试接口 | 删除 | 对应功能验证后 | 🔄 待处理 |
| 硬编码测试数据 | 替换为配置 | 真实数据测试通过后 | 🔄 待处理 |

## 🛠️ 验证工具和脚本

### 自动化验证脚本
```bash
# 一键启动验证流程
python scripts/phase3_verification.py

# 模块功能验证
python scripts/module_functional_test.py --module "材料出库单列表查询"

# 性能基准测试
python scripts/performance_benchmark.py --all

# 数据一致性验证
python scripts/data_consistency_check.py --compare

# 清理测试代码
python scripts/cleanup_test_code.py --dry-run  # 预览
python scripts/cleanup_test_code.py --execute  # 执行
```

### 监控和报告
```bash
# 生成测试报告
python scripts/generate_test_report.py

# 实时监控
python scripts/real_time_monitor.py

# 性能分析报告
python scripts/performance_analysis.py --export
```

## ⚠️ 风险控制和回滚策略

### 风险点识别
1. **功能回归**: 新系统功能与旧系统不一致
2. **性能降级**: 新系统性能不如旧系统
3. **数据错误**: 数据处理逻辑变化导致结果错误
4. **集成问题**: 前后端集成出现问题

### 回滚策略
```bash
# 紧急回滚到安全点
git checkout v0-shit-mountain
docker run -d legacy:safe

# 部分回滚（单个模块）
python scripts/rollback_module.py --module "有问题的模块名"

# 数据回滚
python scripts/data_rollback.py --timestamp "2025-08-06T00:00:00"
```

## 📊 验证成功标准

### 通过标准
- ✅ 所有15个模块功能验证100%通过
- ✅ 性能测试：响应时间≤原系统的120%
- ✅ 压力测试：无内存泄漏，稳定运行24小时
- ✅ 数据一致性：新旧系统输出100%一致
- ✅ 清理完成：删除90%以上的测试代码和模拟代码

### 阶段完成条件
1. **功能验证**: 所有模块真实数据测试通过
2. **性能验证**: 满足性能基准要求
3. **清理完成**: 测试代码和模拟代码清理完成
4. **文档更新**: 更新所有相关文档
5. **生产就绪**: 部署脚本和监控系统验证通过

---

**下一步执行建议**:
1. 首先运行基础启动验证
2. 逐个模块进行功能验证
3. 并行进行性能测试和代码清理
4. 最后进行生产就绪验证

**预计时间轴**:
- 第1天: 基础启动验证
- 第2-8天: 模块功能验证（每天2个模块）
- 第9-10天: 性能测试和优化
- 第11-12天: 代码清理
- 第13-14天: 生产就绪验证
