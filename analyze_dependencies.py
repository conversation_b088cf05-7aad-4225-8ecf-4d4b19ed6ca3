#!/usr/bin/env python3
"""
简单的依赖关系分析 - 替代madge
分析Python和JavaScript文件的导入依赖关系
"""

import json
import os
import re
from collections import defaultdict


def extract_python_imports(file_path):
    """提取Python文件的导入依赖"""
    imports = []
    try:
        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            content = f.read()

        # 匹配 import xxx
        import_pattern = r"^import\s+(\w+(?:\.\w+)*)"
        imports.extend(re.findall(import_pattern, content, re.MULTILINE))

        # 匹配 from xxx import yyy
        from_pattern = r"^from\s+(\w+(?:\.\w+)*)\s+import"
        imports.extend(re.findall(from_pattern, content, re.MULTILINE))

        # 匹配相对导入 from .xxx import yyy
        relative_pattern = r"^from\s+\.(\w+(?:\.\w+)*)\s+import"
        relative_imports = re.findall(relative_pattern, content, re.MULTILINE)
        imports.extend([f".{imp}" for imp in relative_imports])

    except Exception:
        pass

    return list(set(imports))


def extract_js_imports(file_path):
    """提取JavaScript文件的导入依赖"""
    imports = []
    try:
        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            content = f.read()

        # 匹配 import xxx from 'yyy'
        import_pattern = r"import\s+.*?\s+from\s+['\"]([^'\"]+)['\"]"
        imports.extend(re.findall(import_pattern, content))

        # 匹配 require('xxx')
        require_pattern = r"require\s*\(\s*['\"]([^'\"]+)['\"]\s*\)"
        imports.extend(re.findall(require_pattern, content))

        # 匹配 import('xxx')
        dynamic_import_pattern = r"import\s*\(\s*['\"]([^'\"]+)['\"]\s*\)"
        imports.extend(re.findall(dynamic_import_pattern, content))

    except Exception:
        pass

    return list(set(imports))


def analyze_file_dependencies(root_path):
    """分析文件依赖关系"""
    dependencies = defaultdict(list)
    file_stats = {}

    for root, dirs, files in os.walk(root_path):
        # 跳过不必要的目录
        dirs[:] = [
            d
            for d in dirs
            if d not in {".git", "__pycache__", "node_modules", ".vscode", "logs"}
        ]

        for file in files:
            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, root_path)

            if file.endswith(".py"):
                imports = extract_python_imports(file_path)
                dependencies[rel_path] = imports
                file_stats[rel_path] = {
                    "type": "python",
                    "imports": len(imports),
                    "size": os.path.getsize(file_path),
                }
            elif file.endswith(".js"):
                imports = extract_js_imports(file_path)
                dependencies[rel_path] = imports
                file_stats[rel_path] = {
                    "type": "javascript",
                    "imports": len(imports),
                    "size": os.path.getsize(file_path),
                }

    return dependencies, file_stats


def generate_dependency_report(root_path):
    """生成依赖关系报告"""
    print("🔍 正在分析文件依赖关系...")

    dependencies, file_stats = analyze_file_dependencies(root_path)

    # 生成报告
    report = []
    report.append("=" * 60)
    report.append("📊 YS-API V3.0 依赖关系分析")
    report.append("=" * 60)
    current_time = __import__("datetime").datetime.now()
    timestamp = current_time.strftime("%Y-%m-%d %H:%M:%S")
    report.append(f"📅 生成时间: {timestamp}")
    report.append("")

    # 统计概览
    python_files = [
        f for f,
        stats in file_stats.items() if stats["type"] == "python"]
    js_files = [f for f, stats in file_stats.items() if stats["type"]
                == "javascript"]

    report.append("📋 文件类型分布:")
    report.append(f"- Python文件: {len(python_files)} 个")
    report.append(f"- JavaScript文件: {len(js_files)} 个")
    report.append("")

    # 高依赖文件 (导入超过10个模块的文件)
    high_dependency_files = [
        (f, stats["imports"])
        for f, stats in file_stats.items()
        if stats["imports"] > 10
    ]
    if high_dependency_files:
        report.append("🔥 高依赖文件 (导入>10个模块):")
        report.append("-" * 80)
        report.append(f"{'文件路径':<60} {'导入数量':<10}")
        report.append("-" * 80)

        sorted_files = sorted(
            high_dependency_files,
            key=lambda x: x[1],
            reverse=True)
        for file_path, import_count in sorted_files:
            report.append(f"{file_path:<60} {import_count:<10}")
        report.append("")

    # 内部依赖分析 (相对导入)
    internal_deps = {}
    for file_path, imports in dependencies.items():
        internal_imports = [imp for imp in imports if imp.startswith(
            ".") or "/" in imp or "\\" in imp]
        if internal_imports:
            internal_deps[file_path] = internal_imports

    if internal_deps:
        report.append("🔗 内部依赖关系:")
        report.append("-" * 80)
        for file_path, imports in sorted(internal_deps.items()):
            report.append(f"{file_path}:")
            for imp in imports[:5]:  # 只显示前5个
                report.append(f"  ├─ {imp}")
            if len(imports) > 5:
                report.append(f"  └─ ... 还有 {len(imports) - 5} 个依赖")
            report.append("")

    # 第三方库使用统计
    external_libs = defaultdict(int)
    for file_path, imports in dependencies.items():
        for imp in imports:
            if not imp.startswith(".") and "/" not in imp and "\\" not in imp:
                # 只取第一部分作为库名
                lib_name = imp.split(".")[0]
                external_libs[lib_name] += 1

    # 过滤掉标准库
    standard_libs = {
        "os",
        "sys",
        "json",
        "datetime",
        "collections",
        "re",
        "pathlib",
        "time",
        "threading",
        "logging",
    }
    third_party_libs = {
        lib: count for lib,
        count in external_libs.items() if lib not in standard_libs}

    if third_party_libs:
        report.append("📦 第三方库使用统计:")
        report.append("-" * 40)
        sorted_libs = sorted(
            third_party_libs.items(), key=lambda x: x[1], reverse=True
        )[:15]
        for lib, count in sorted_libs:
            report.append(f"{lib:<25} {count:>3} 次")
        report.append("")

    # 孤立文件 (没有导入任何模块的文件)
    isolated_files = [
        f for f,
        stats in file_stats.items() if stats["imports"] == 0]
    if isolated_files:
        report.append("🏝️ 孤立文件 (无导入依赖):")
        report.append("-" * 60)
        for file_path in sorted(isolated_files)[:10]:  # 只显示前10个
            report.append(f"- {file_path}")
        if len(isolated_files) > 10:
            report.append(f"... 还有 {len(isolated_files) - 10} 个孤立文件")
        report.append("")

    report.append("🎯 重构建议:")
    report.append("1. 优先重构高依赖文件，减少耦合")
    report.append("2. 检查孤立文件是否为无用代码")
    report.append("3. 梳理内部依赖关系，建立清晰的模块边界")
    report.append("4. 评估第三方库的必要性")

    return "\n".join(report), dependencies, file_stats


if __name__ == "__main__":
    root_path = "."
    report_text, deps_data, stats_data = generate_dependency_report(root_path)

    # 保存文本报告
    with open("dependencies.txt", "w", encoding="utf-8") as f:
        f.write(report_text)

    # 保存JSON数据
    dependency_analysis = {
        "generation_time": __import__("datetime").datetime.now().isoformat(),
        "dependencies": deps_data,
        "file_stats": stats_data,
        "summary": {
            "total_files_analyzed": len(stats_data),
            "python_files": len(
                [f for f, s in stats_data.items() if s["type"] == "python"]
            ),
            "javascript_files": len(
                [f for f, s in stats_data.items() if s["type"] == "javascript"]
            ),
            "high_dependency_files": len(
                [f for f, s in stats_data.items() if s["imports"] > 10]
            ),
        },
    }

    with open("dependency_analysis.json", "w", encoding="utf-8") as f:
        json.dump(dependency_analysis, f, indent=2, ensure_ascii=False)

    print("✅ 依赖分析完成！")
    print("📄 文本报告: dependencies.txt")
    print("📊 详细数据: dependency_analysis.json")
    print("")
    print("📋 快速摘要:")
    print(f"- 分析文件数: {dependency_analysis['summary']['total_files_analyzed']}")
    print(f"- Python文件: {dependency_analysis['summary']['python_files']}")
    js_files_count = dependency_analysis["summary"]["javascript_files"]
    print(f"- JavaScript文件: {js_files_count}")
    high_dep_count = dependency_analysis["summary"]["high_dependency_files"]
    print(f"- 高依赖文件: {high_dep_count}")
