/**
 * YS-API V3.1 统一API配置
 * 简化版本，解决前端报错问题
 */

// 统一配置
const API_CONFIG === {
    BASE_URL: 'http://localhost:8000/api/v1',
    TIMEOUT: 30000,
    HEADERS: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
};

// 统一的API调用函数
async function apiCall(endpoint, options === {}) {
    const url === `${API_CONFIG.BASE_URL}${endpoint}`;
    const defaultOptions === {
        method: 'GET',
        headers: API_CONFIG.HEADERS,
        timeout: API_CONFIG.TIMEOUT
    };
    
    const finalOptions === { ...defaultOptions, ...options };
    
    try {
        const response === await fetch(url, finalOptions);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result === await response.json();
        return result;
        
    } catch (error) {
        console.error(`❌ API调用失败: ${finalOptions.method} ${url}`, error);
        
        // 友好的错误提示
        let userMessage === '操作失败';
        if (error.message.includes('Failed to fetch')) {
            userMessage === '网络连接失败，请检查服务器是否启动';
        } else if (error.message.includes('500')) {
            userMessage === '服务器内部错误，请稍后重试';
        } else if (error.message.includes('404')) {
            userMessage === 'API接口不存在';
        } else if (error.message.includes('timeout')) {
            userMessage === '请求超时，请稍后重试';
        }
        
        // 如果有全局错误处理函数，调用它
        if (typeof window !== 'undefined' && window.showToast) {
            window.showToast(userMessage, 'error');
        }
        
        throw error;
    }
}

// 简化的API服务类
class UnifiedAPI {
    // 健康检查
    static async healthCheck() {
        return await apiCall('/monitor/health');
    }
    
    // 同步相关
    static async getSyncStatus() {
        return await apiCall('/monitor/sync-status');
    }
    
    static async startFullSync() {
        return await apiCall('/sync/write/batch', {
            method: 'POST',
            body: JSON.stringify({
                modules: ['purchase_order', 'purchase_receipt', 'sales_order', 'sales_out', 
                         'production_order', 'product_receipt', 'subcontract_order', 
                         'subcontract_receipt', 'subcontract_requisition', 'materialout', 
                         'material_master', 'inventory_report', 'requirements_planning', 
                         'applyorder', 'inventory'],
                record_limit: null,
                force_recreate_tables: false
            })
        });
    }
    
    static async stopSync() {
        return await apiCall('/monitor/stop-sync', { method: 'POST' });
    }
    
    static async syncSingleModule(moduleName) {
        return await apiCall('/sync/write/single', {
            method: 'POST',
            body: JSON.stringify({
                module_name: moduleName,
                record_limit: null,
                force_recreate_table: false,
                clear_existing_data: false
            })
        });
    }
    
    // 数据库相关
    static async getDatabaseStatus() {
        return await apiCall('/database/status');
    }
    
    static async getTablesStatus() {
        return await apiCall('/database/tables/status');
    }
    
    static async getTables() {
        return await apiCall('/database/tables');
    }
    
    static async createAllTables() {
        return await apiCall('/database/tables/create-all', {
            method: 'POST',
            body: JSON.stringify({
                drop_if_exists: false
            })
        });
    }
    
    static async resetDatabase() {
        return await apiCall('/monitor/database/reset', { method: 'POST' });
    }
    
    static async getTableInfo(tableName) {
        return await apiCall(`/database/tables/${tableName}/info`);
    }
    
    // 模块相关
    static async getModules() {
        return await apiCall('/config/modules');
    }
    
    // 任务相关
    static async getTaskStatus() {
        return await apiCall('/tasks/auto-sync/status');
    }
    
    static async saveTaskConfig(config) {
        return await apiCall('/tasks/auto-sync/config', {
            method: 'POST',
            body: JSON.stringify(config)
        });
    }
    
    static async startTask() {
        return await apiCall('/tasks/auto-sync/start', { method: 'POST' });
    }
    
    static async stopTask() {
        return await apiCall('/tasks/auto-sync/stop', { method: 'POST' });
    }
    
    // 日志相关
    static createLogStream() {
        return new EventSource(`${API_CONFIG.BASE_URL}/logs/stream`);
    }
    
    // 批量API测试
    static async testAllAPIs() {
        // 测试数据已移除,
            { name: '同步状态', fn: () ===> this.getSyncStatus() },
            { name: '模块列表', fn: () ===> this.getModules() },
            { name: '表状态', fn: () ===> this.getTablesStatus() },
            { name: '任务状态', fn: () ===> this.getTaskStatus() }
        ];
        
        const results === [];
        for (const test of tests) {
            try {
                await test.fn();
                results.push({ name: test.name, success: true });
            } catch (error) {
                results.push({ name: test.name, success: false, error: error.message });
            }
        }
        
        return results;
    }
}

// 模块名称映射
const MODULE_NAMES === {
    'purchase_order': '采购订单',
    'purchase_receipt': '采购入库',
    'sales_order': '销售订单',
    'sales_out': '销售出库',
    'production_order': '生产订单',
    'product_receipt': '产品入库',
    'subcontract_order': '委外订单',
    'subcontract_receipt': '委外入库',
    'subcontract_requisition': '委外申请',
    'materialout': '材料出库',
    'material_master': '物料档案',
    'inventory_report': '现存量报表',
    'requirements_planning': '需求计划',
    'applyorder': '请购单',
    'inventory': '现存量查询'
};

// 导出到全局
if (typeof window !== 'undefined') {
    window.UnifiedAPI === UnifiedAPI;
    window.MODULE_NAMES === MODULE_NAMES;
    window.apiCall === apiCall;
}

// Node.js 环境支持
if (typeof module !== 'undefined' && module.exports) {
    module.exports === {
        UnifiedAPI,
        MODULE_NAMES,
        apiCall,
        API_CONFIG
    };
} 