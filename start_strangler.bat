@echo off
REM 屎山绞杀环境启动脚本 - Windows版
echo ========================================
echo   屎山代码绞杀环境启动 - Windows版
echo ========================================

echo.
echo 🔍 检查环境依赖...

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装或未启动
    echo 请先安装Docker Desktop并启动
    pause
    exit /b 1
)

echo ✅ Docker已就绪

REM 检查Python依赖
echo.
echo 🐍 检查Python依赖...
python -m pip show psycopg2-binary >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安装Python依赖...
    python -m pip install psycopg2-binary redis asyncio
)

python -m pip show prometheus-client >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安装Prometheus客户端...
    python -m pip install prometheus-client
)

echo ✅ Python依赖已就绪

REM 启动Docker环境
echo.
echo 🐳 启动Docker环境...
if exist docker-compose.strangler.yml (
    docker-compose -f docker-compose.strangler.yml up -d
    echo ✅ Docker环境已启动
) else (
    echo ❌ 找不到docker-compose.strangler.yml文件
    echo 正在创建基础Docker环境配置...
    call :create_basic_docker_compose
    docker-compose -f docker-compose.strangler.yml up -d
)

REM 等待服务启动
echo.
echo ⏳ 等待服务启动...
timeout /t 5 /nobreak >nul

REM 检查服务状态
echo.
echo 📊 检查服务状态...
docker-compose -f docker-compose.strangler.yml ps

echo.
echo 🎉 屎山绞杀环境启动完成！
echo.
echo 📊 访问监控面板:
echo   Prometheus: http://localhost:9090
echo   Grafana:    http://localhost:3000
echo.
echo 🔧 运行健康检查:
echo   python scripts/health_check.py
echo.
pause
exit /b 0

:create_basic_docker_compose
echo version: '3.8' > docker-compose.strangler.yml
echo. >> docker-compose.strangler.yml
echo services: >> docker-compose.strangler.yml
echo   prometheus: >> docker-compose.strangler.yml
echo     image: prom/prometheus:latest >> docker-compose.strangler.yml
echo     ports: >> docker-compose.strangler.yml
echo       - "9090:9090" >> docker-compose.strangler.yml
echo. >> docker-compose.strangler.yml
echo   grafana: >> docker-compose.strangler.yml
echo     image: grafana/grafana:latest >> docker-compose.strangler.yml
echo     ports: >> docker-compose.strangler.yml
echo       - "3000:3000" >> docker-compose.strangler.yml
echo ✅ 基础Docker配置已创建
goto :eof
