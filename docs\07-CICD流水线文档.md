# YS-API V3.0 CI/CD 流水线文档

## 概述

本文档描述了 YS-API V3.0 项目的完整 CI/CD 流水线配置和使用方法。

## 流水线组件

### 1. GitHub Actions

- **文件位置**: `.github/workflows/main.yml`
- **触发条件**: 
  - 推送到 `main` 或 `develop` 分支
  - 向 `main` 分支创建 Pull Request
- **流水线步骤**:
  1. 代码检出
  2. Python 环境设置
  3. 依赖安装
  4. 单元测试
  5. 代码质量检查
  6. 安全扫描
  7. Docker 镜像构建
  8. 部署到暂存环境

### 2. Jenkins 流水线

- **文件位置**: `Jenkinsfile`
- **功能**: 企业级 CI/CD 流水线
- **阶段**:
  - 代码检出
  - 环境准备
  - 代码质量检查（并行执行）
  - 测试
  - Docker 镜像构建
  - 生产环境部署

### 3. Docker 容器化

- **Dockerfile**: 生产环境镜像配置
- **docker-compose.yml**: 多服务编排
- **特性**:
  - 多阶段构建
  - 安全用户权限
  - 健康检查
  - 日志管理

## 部署脚本

### Linux/Mac 部署

```bash
# 执行部署
./scripts/deploy.sh

# 回滚到上一版本
./scripts/rollback.sh
```

### Windows 部署

```cmd
REM 执行部署
scripts\deploy.bat
```

## 监控配置

### Prometheus 监控

- **配置文件**: `monitoring/prometheus.yml`
- **访问地址**: http://localhost:9090
- **监控指标**:
  - API 请求速率
  - 响应时间
  - 错误率
  - 系统资源使用情况

### Grafana 仪表板

- **访问地址**: http://localhost:3000
- **默认账号**: admin / admin123
- **仪表板**: YS-API V3.0 监控仪表板

### 启动监控服务

```bash
# 启动 Prometheus 和 Grafana
docker-compose -f monitoring/docker-compose.monitoring.yml up -d
```

## 环境配置

### 开发环境

1. 安装依赖
```bash
cd backend
pip install -r requirements.txt
```

2. 启动开发服务器
```bash
python start_server.py
```

### 生产环境

1. 使用 Docker Compose
```bash
docker-compose up -d
```

2. 验证部署
```bash
curl http://localhost:5000/health
```

## 质量门禁

### 代码质量检查

- **工具**: flake8
- **配置**: `.flake8`
- **标准**: PEP 8

### 安全扫描

- **工具**: bandit, safety
- **范围**: Python 代码安全漏洞

### 测试覆盖率

- **工具**: pytest + coverage
- **要求**: 覆盖率 > 80%

## 故障排除

### 常见问题

1. **Docker 镜像构建失败**
   - 检查 Dockerfile 语法
   - 确认依赖文件存在
   - 查看构建日志

2. **服务启动失败**
   - 检查端口占用
   - 查看容器日志
   - 验证配置文件

3. **健康检查失败**
   - 确认服务已完全启动
   - 检查网络连接
   - 验证健康检查端点

### 日志查看

```bash
# 查看应用日志
docker-compose logs ys-api

# 查看所有服务日志
docker-compose logs

# 实时跟踪日志
docker-compose logs -f
```

## 维护指南

### 定期任务

1. **清理旧镜像**
```bash
docker image prune -a
```

2. **备份数据库**
```bash
cp backend/ysapi.db backup/ysapi_$(date +%Y%m%d).db
```

3. **更新依赖**
```bash
pip list --outdated
pip install --upgrade package_name
```

## 安全最佳实践

1. **容器安全**
   - 使用非 root 用户
   - 最小化镜像大小
   - 定期更新基础镜像

2. **网络安全**
   - 使用 HTTPS
   - 配置防火墙
   - 限制端口暴露

3. **数据安全**
   - 加密敏感数据
   - 定期备份
   - 访问控制

## 更新记录

- **2025-08-03**: 初始版本创建
- **最后更新**: 2025-08-03
