[{"success": true, "code": 200, "message": "", "data": {"fieldVersion": 20230210, "appCode": "", "tokenSet": false, "tokenDoc": "", "tenantId": 0, "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "id": "4474f0703e674ba3b3325f7133e4f990", "name": "委外申请单列表查询", "apiClassifyId": "192c27598b21412594f2c6e32b617803", "apiClassifyName": "委外申请单", "apiClassifyCode": "productionorder.po_subcontract_requisition_card", "parentApiClassifies": "", "functionId": "", "openMode": 0, "description": "提供委外申请单列表查询接口，可以通过接口按列表查询委外申请单", "auth": true, "bodyPassthrough": false, "healthExam": false, "healthStatus": true, "responseResultPassthrough": false, "contentType": "application/json", "returnPassthrough": "", "completeProxyUrl": "/yonbip/mfg/subcontractrequisition/list", "connectUrl": "/api/list", "sort": 20, "handler": "openapi", "httpRequestType": "POST", "openApi": true, "preset": false, "productId": "4a176d6a681a4ebdbd053262493b5dff", "productCode": "", "proxyUrl": "/yonbip/mfg/subcontractrequisition/list", "requestParamsDemo": "Url: /yonbip/mfg/subcontractrequisition/list?access_token=访问令牌 Body: { \"pageIndex\": 1, \"pageSize\": 10, \"orgId\": [ 1866605942198527 ], \"code\": \"WWSQ202105010001\", \"vouchdate\": \"2021-03-02|2021-03-02 23:59:59\", \"transTypeId\": [ 1866605942198526 ], \"status\": 0, \"materialId\": [ 1866605942198785 ], \"productId\": [ 1866605942198650 ], \"requisitionDate\": \"2021-03-02|2021-03-02 23:59:59\", \"departmentId\": [ 186660594219847 ], \"operatorId\": [ 186660594218648 ], \"simple\": { \"open_pubts_begin\": \"2022-01-01 00:00:00\", \"open_pubts_end\": \"2022-01-01 10:00:00\" }, \"simpleVOs\": [ { \"field\": \"\", \"op\": \"\", \"value1\": \"\", \"value2\": \"\", \"logicOp\": \"and\", \"conditions\": [ { \"field\": \"\", \"op\": \"\", \"value1\": \"\", \"value2\": \"\" } ] } ] }", "requestProtocol": "HTTP", "serviceHttpMethod": "POST", "publishStatus": true, "approvalMsg": "", "rpcAppName": "", "rpcServiceName": "", "rpcMethodName": "", "rpcServiceUrl": "", "ma": false, "gmtCreate": "2021-12-28 09:44:24.000", "gmtUpdate": "2024-05-31 13:56:07.000", "address": "https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/mfg/subcontractrequisition/list", "productName": "", "productClassifyId": "", "productClassifyCode": "", "productClassifyName": "", "paramDTOS": {"paramDTOS": [{"id": 2009482629538643985, "name": "pageIndex", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112522, "array": false, "paramDesc": "页号 默认值:1", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": 1, "required": true, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643986, "name": "pageSize", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112523, "array": false, "paramDesc": "每页行数 默认值:10", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": 10, "fullName": "", "ytenantId": 0, "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": 10, "required": true, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643987, "name": "orgId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112524, "array": true, "paramDesc": "组织ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[\"1866605942198527\"]", "fullName": "", "ytenantId": 0, "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643988, "name": "code", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112525, "array": false, "paramDesc": "委外申请单号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "WWSQ202105010001", "fullName": "", "ytenantId": 0, "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643989, "name": "vouchdate", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112526, "array": false, "paramDesc": "单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "2021-03-02|2021-03-02 23:59:59", "fullName": "", "ytenantId": 0, "paramOrder": 4, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643990, "name": "transTypeId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112527, "array": true, "paramDesc": "交易类型ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[\"1866605942198526\"]", "fullName": "", "ytenantId": 0, "paramOrder": 5, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643991, "name": "status", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112528, "array": false, "paramDesc": "申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。", "paramType": "short", "requestParamType": "BodyParam", "path": "", "example": 0, "fullName": "", "ytenantId": 0, "paramOrder": 6, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643992, "name": "materialId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112529, "array": true, "paramDesc": "制造物料ID", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "[1866605942198785]", "fullName": "", "ytenantId": 0, "paramOrder": 7, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643993, "name": "productId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112530, "array": true, "paramDesc": "物料ID", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "[1866605942198650]", "fullName": "", "ytenantId": 0, "paramOrder": 8, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643994, "name": "requisitionDate", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112531, "array": false, "paramDesc": "需求日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "2021-03-02|2021-03-02 23:59:59", "fullName": "", "ytenantId": 0, "paramOrder": 9, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643995, "name": "departmentId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112532, "array": true, "paramDesc": "需求部门ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[\"186660594219847\"]", "fullName": "", "ytenantId": 0, "paramOrder": 10, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643996, "name": "operatorId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112533, "array": true, "paramDesc": "需求人ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[\"186660594218648\"]", "fullName": "", "ytenantId": 0, "paramOrder": 11, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643971, "name": "simple", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "children": {"children": [{"id": 2009482629538643972, "name": "open_pubts_begin", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643971, "defParamId": 2009368443538112535, "array": false, "paramDesc": "时间戳，开始时间", "paramType": "date", "requestParamType": "BodyParam", "path": "", "example": "2022-01-01 00:00:00", "fullName": "", "ytenantId": 0, "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "yyyy-MM-dd HH:mm:ss", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643973, "name": "open_pubts_end", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643971, "defParamId": 2009368443538112536, "array": false, "paramDesc": "时间戳，结束时间", "paramType": "date", "requestParamType": "BodyParam", "path": "", "example": "2022-01-01 10:00:00", "fullName": "", "ytenantId": 0, "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "yyyy-MM-dd HH:mm:ss", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "defParamId": 2009368443538112534, "array": false, "paramDesc": "扩展参数", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 12, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643974, "name": "simpleVOs", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "children": {"children": [{"id": 2009482629538643980, "name": "field", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643974, "defParamId": 2009368443538112538, "array": false, "paramDesc": "属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643981, "name": "op", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643974, "defParamId": 2009368443538112539, "array": false, "paramDesc": "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643982, "name": "value1", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643974, "defParamId": 2009368443538112540, "array": false, "paramDesc": "查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643983, "name": "value2", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643974, "defParamId": 2009368443538112541, "array": false, "paramDesc": "查询条件值2", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643984, "name": "logicOp", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643974, "defParamId": 2009368443538112542, "array": false, "paramDesc": "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "and", "fullName": "", "ytenantId": 0, "paramOrder": 4, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643975, "name": "conditions", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643974, "children": {"children": [{"id": 2009482629538643976, "name": "field", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643975, "defParamId": 2009368443538112544, "array": false, "paramDesc": "属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643977, "name": "op", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643975, "defParamId": 2009368443538112545, "array": false, "paramDesc": "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643978, "name": "value1", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643975, "defParamId": 2009368443538112546, "array": false, "paramDesc": "查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2009482629538643979, "name": "value2", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643975, "defParamId": 2009368443538112547, "array": false, "paramDesc": "查询条件值2", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "defParamId": 2009368443538112543, "array": true, "paramDesc": "下级查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 5, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "defParamId": 2009368443538112537, "array": true, "paramDesc": "扩展查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 13, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "queryParamDTOS": "", "ysApi": false, "presetTokenApi": false, "applyFlag": false, "cover": false, "paramMapDTOS": {"paramMapDTOS": [{"id": 2009482629538644011, "name": "pageIndex", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": "", "array": false, "paramDesc": "页号 默认值:1", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "pageIndex", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644012, "name": "pageSize", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": "", "array": false, "paramDesc": "每页行数 默认值:10", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "pageSize", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644013, "name": "orgId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": "", "array": false, "paramDesc": "组织ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "orgId", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644014, "name": "code", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": "", "array": false, "paramDesc": "委外申请单号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "code", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644015, "name": "vouchdate", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": "", "array": false, "paramDesc": "单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 4, "baseType": true, "aggregatedValueObject": false, "mapName": "vouchdate", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644016, "name": "transTypeId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": "", "array": false, "paramDesc": "交易类型ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 5, "baseType": true, "aggregatedValueObject": false, "mapName": "transTypeId", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644017, "name": "status", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": "", "array": false, "paramDesc": "申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。", "paramType": "short", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 6, "baseType": true, "aggregatedValueObject": false, "mapName": "status", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "short", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644018, "name": "materialId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": "", "array": false, "paramDesc": "制造物料ID", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 7, "baseType": true, "aggregatedValueObject": false, "mapName": "materialId", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644019, "name": "productId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": "", "array": false, "paramDesc": "物料ID", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 8, "baseType": true, "aggregatedValueObject": false, "mapName": "productId", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644020, "name": "requisitionDate", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 9, "baseType": true, "aggregatedValueObject": false, "mapName": "requisitionDate", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644021, "name": "departmentId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求部门ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 10, "baseType": true, "aggregatedValueObject": false, "mapName": "departmentId", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644022, "name": "operatorId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求人ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 11, "baseType": true, "aggregatedValueObject": false, "mapName": "operatorId", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538643997, "name": "simple", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "children": {"children": [{"id": 2009482629538643998, "name": "open_pubts_begin", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643997, "defParamId": "", "array": false, "paramDesc": "时间戳，开始时间", "paramType": "date", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "open_pubts_begin", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "date", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538643999, "name": "open_pubts_end", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538643997, "defParamId": "", "array": false, "paramDesc": "时间戳，结束时间", "paramType": "date", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "open_pubts_end", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "date", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "defParamId": "", "array": false, "paramDesc": "扩展参数", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 12, "baseType": true, "aggregatedValueObject": false, "mapName": "simple", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644000, "name": "simpleVOs", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "children": {"children": [{"id": 2009482629538644006, "name": "field", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644000, "defParamId": "", "array": false, "paramDesc": "属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "field", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644007, "name": "op", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644000, "defParamId": "", "array": false, "paramDesc": "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "op", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644008, "name": "value1", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644000, "defParamId": "", "array": false, "paramDesc": "查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "value1", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644009, "name": "value2", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644000, "defParamId": "", "array": false, "paramDesc": "查询条件值2", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "value2", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644010, "name": "logicOp", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644000, "defParamId": "", "array": false, "paramDesc": "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 4, "baseType": true, "aggregatedValueObject": false, "mapName": "logicOp", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644001, "name": "conditions", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644000, "children": {"children": [{"id": 2009482629538644002, "name": "field", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644001, "defParamId": "", "array": false, "paramDesc": "属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "field", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644003, "name": "op", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644001, "defParamId": "", "array": false, "paramDesc": "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "op", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644004, "name": "value1", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644001, "defParamId": "", "array": false, "paramDesc": "查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "value1", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2009482629538644005, "name": "value2", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644001, "defParamId": "", "array": false, "paramDesc": "查询条件值2", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "value2", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "defParamId": "", "array": false, "paramDesc": "下级查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 5, "baseType": true, "aggregatedValueObject": false, "mapName": "conditions", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "defParamId": "", "array": false, "paramDesc": "扩展查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 13, "baseType": true, "aggregatedValueObject": false, "mapName": "simpleVOs", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "paramReturnDTOS": {"paramReturnDTOS": [{"id": 2009482629538644076, "name": "code", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112574, "array": false, "paramDesc": "返回码，成功时返回200", "paramType": "long", "requestParamType": "", "path": "", "example": 200, "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644077, "name": "message", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "defParamId": 2009368443538112575, "array": false, "paramDesc": "接口返回信息", "paramType": "string", "requestParamType": "", "path": "", "example": "操作成功", "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644023, "name": "data", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": "", "children": {"children": [{"id": 2009482629538644070, "name": "pageIndex", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644023, "defParamId": 2009368443538112577, "array": false, "paramDesc": "当前页", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644071, "name": "pageSize", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644023, "defParamId": 2009368443538112578, "array": false, "paramDesc": "页大小", "paramType": "long", "requestParamType": "", "path": "", "example": 20, "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644072, "name": "recordCount", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644023, "defParamId": 2009368443538112579, "array": false, "paramDesc": "记录总数", "paramType": "long", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644024, "name": "recordList", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644023, "children": {"children": [{"id": 2009482629538644048, "name": "id", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112581, "array": false, "paramDesc": "单据ID", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644049, "name": "orgId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112582, "array": false, "paramDesc": "需求组织ID", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644050, "name": "orgName", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112583, "array": false, "paramDesc": "需求组织", "paramType": "string", "requestParamType": "", "path": "", "example": "资产管理公司", "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644051, "name": "code", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112584, "array": false, "paramDesc": "委外申请单号", "paramType": "string", "requestParamType": "", "path": "", "example": "WWSQ202112230004", "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644052, "name": "vouchdate", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112585, "array": false, "paramDesc": "单据日期", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-12-23 00:00:00", "fullName": "", "ytenantId": 0, "paramOrder": 4, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644053, "name": "status", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112586, "array": false, "paramDesc": "申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。", "paramType": "short", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 5, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644054, "name": "transTypeId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112587, "array": false, "paramDesc": "交易类型ID", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": 0, "paramOrder": 6, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644055, "name": "transTypeName", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112588, "array": false, "paramDesc": "交易类型", "paramType": "string", "requestParamType": "", "path": "", "example": "标准委外", "fullName": "", "ytenantId": 0, "paramOrder": 7, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644056, "name": "transTypeExtendAttrsJson", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112589, "array": false, "paramDesc": "交易类型扩展属性", "paramType": "string", "requestParamType": "", "path": "", "example": "{\"specialType\":\"none\",\"businessType\":\"wholeOutsourcing\"}", "fullName": "", "ytenantId": 0, "paramOrder": 8, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644057, "name": "departmentId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112590, "array": false, "paramDesc": "需求部门ID", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": 0, "paramOrder": 9, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644058, "name": "departmentName", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112591, "array": false, "paramDesc": "需求部门", "paramType": "string", "requestParamType": "", "path": "", "example": "仓储部", "fullName": "", "ytenantId": 0, "paramOrder": 10, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644059, "name": "operatorId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112592, "array": false, "paramDesc": "需求人ID", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": 0, "paramOrder": 11, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644060, "name": "operatorName", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112593, "array": false, "paramDesc": "需求人名称", "paramType": "string", "requestParamType": "", "path": "", "example": "张三", "fullName": "", "ytenantId": 0, "paramOrder": 12, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644061, "name": "sourceType", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112594, "array": false, "paramDesc": "来源类别。0：手工添加，1：计划订单。", "paramType": "string", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 13, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644063, "name": "isWfControlled", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112596, "array": false, "paramDesc": "是否审批流控制：false-否，true-是。", "paramType": "boolean", "requestParamType": "", "path": "", "example": false, "fullName": "", "ytenantId": 0, "paramOrder": 15, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644064, "name": "verifystate", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112597, "array": false, "paramDesc": "审批状态：0-开立，1-已提交，2-已审批，-1-驳回", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": 0, "paramOrder": 16, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644065, "name": "pubts", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "defParamId": 2009368443538112598, "array": false, "paramDesc": "时间戳", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-12-23 20:14:33", "fullName": "", "ytenantId": 0, "paramOrder": 17, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644025, "name": "subcontractRequisitionProduct", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644024, "children": {"children": [{"id": 2009482629538644026, "name": "id", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112600, "array": false, "paramDesc": "主键", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644027, "name": "materialId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112601, "array": false, "paramDesc": "制造物料ID", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644028, "name": "productId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112602, "array": false, "paramDesc": "物料ID", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644029, "name": "outsourceOrgId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112603, "array": false, "paramDesc": "委外组织ID", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644030, "name": "rcvOrgId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112604, "array": false, "paramDesc": "收货组织ID", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": 0, "paramOrder": 4, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644031, "name": "productCode", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112605, "array": false, "paramDesc": "物料编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 5, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644032, "name": "productName", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112606, "array": false, "paramDesc": "物料名称", "paramType": "string", "requestParamType": "", "path": "", "example": "P10", "fullName": "", "ytenantId": 0, "paramOrder": 6, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644033, "name": "demandQuantityDU", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112607, "array": false, "paramDesc": "需求件数", "paramType": "long", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": 0, "paramOrder": 7, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644034, "name": "demandUnitId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112608, "array": false, "paramDesc": "需求单位ID", "paramType": "long", "requestParamType": "", "path": "", "example": 2325529461018880, "fullName": "", "ytenantId": 0, "paramOrder": 8, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644035, "name": "demandUnitName", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112609, "array": false, "paramDesc": "需求单位", "paramType": "string", "requestParamType": "", "path": "", "example": "袋", "fullName": "", "ytenantId": 0, "paramOrder": 9, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644036, "name": "demandUnitTruncationType", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112610, "array": false, "paramDesc": "需求单位舍位", "paramType": "long", "requestParamType": "", "path": "", "example": 4, "fullName": "", "ytenantId": 0, "paramOrder": 10, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644037, "name": "demandUnitPrecision", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112611, "array": false, "paramDesc": "需求单位精度", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 11, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644038, "name": "demandQuantityMU", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112612, "array": false, "paramDesc": "需求数量", "paramType": "long", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": 0, "paramOrder": 12, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644039, "name": "mainUnitId", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112613, "array": false, "paramDesc": "需求主单位ID", "paramType": "long", "requestParamType": "", "path": "", "example": 2325529461018880, "fullName": "", "ytenantId": 0, "paramOrder": 13, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644040, "name": "mainUnitName", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112614, "array": false, "paramDesc": "需求主计量单位", "paramType": "string", "requestParamType": "", "path": "", "example": "袋", "fullName": "", "ytenantId": 0, "paramOrder": 14, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644041, "name": "mainUnitTruncationType", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112615, "array": false, "paramDesc": "需求主计量单位舍位", "paramType": "long", "requestParamType": "", "path": "", "example": 4, "fullName": "", "ytenantId": 0, "paramOrder": 15, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644042, "name": "mainUnitPrecision", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112616, "array": false, "paramDesc": "需求主计量单位精度", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 16, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644043, "name": "requisitionDate", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112617, "array": false, "paramDesc": "需求日期", "paramType": "string", "requestParamType": "", "path": "", "example": "2022-01-12 00:00:00", "fullName": "", "ytenantId": 0, "paramOrder": 17, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644044, "name": "rcvOrgName", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112618, "array": false, "paramDesc": "收货组织", "paramType": "string", "requestParamType": "", "path": "", "example": "资产管理公司", "fullName": "", "ytenantId": 0, "paramOrder": 18, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644045, "name": "outsourceOrgName", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644025, "defParamId": 2009368443538112619, "array": false, "paramDesc": "委外组织", "paramType": "string", "requestParamType": "", "path": "", "example": "资产管理公司", "fullName": "", "ytenantId": 0, "paramOrder": 19, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 2009368443538112599, "array": false, "paramDesc": "行信息", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 18, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 2009368443538112580, "array": true, "paramDesc": "返回数据对象", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644066, "name": "sumRecordList", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644023, "children": {"children": {"id": 2009482629538644067, "name": "subcontractRequisitionProduct", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644066, "children": {"children": [{"id": 2009482629538644068, "name": "demandQuantityDU", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644067, "defParamId": 2009368443538112624, "array": false, "paramDesc": "需求件数", "paramType": "double", "requestParamType": "", "path": "", "example": 5533.5333, "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644069, "name": "demandQuantityMU", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644067, "defParamId": 2009368443538112625, "array": false, "paramDesc": "需求数量", "paramType": "string", "requestParamType": "", "path": "", "example": 6632.3, "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 2009368443538112623, "array": false, "paramDesc": "产品行数据", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}}, "defParamId": 2009368443538112622, "array": true, "paramDesc": "合计字段集合", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 4, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644073, "name": "pageCount", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644023, "defParamId": 2009368443538112626, "array": false, "paramDesc": "总页数", "paramType": "long", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": 0, "paramOrder": 5, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644074, "name": "beginPageIndex", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644023, "defParamId": 2009368443538112627, "array": false, "paramDesc": "开始页码", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 6, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2009482629538644075, "name": "endPageIndex", "apiId": "4474f0703e674ba3b3325f7133e4f990", "parentId": 2009482629538644023, "defParamId": 2009368443538112628, "array": false, "paramDesc": "结束页码", "paramType": "long", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": 0, "paramOrder": 7, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 2009368443538112576, "array": false, "paramDesc": "接口返回数据", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "returnFormatType": "JSON", "paramConstDTOS": "", "paramConstMapDTOS": "", "apiDemoReturnDTOS": {"apiDemoReturnDTOS": [{"id": 2009482629538644086, "apiId": "4474f0703e674ba3b3325f7133e4f990", "content": "{ \"code\": 200, \"message\": \"操作成功\", \"data\": { \"pageIndex\": 1, \"pageSize\": 20, \"recordCount\": 1, \"recordList\": [ { \"id\": ****************, \"orgId\": \"****************\", \"orgName\": \"资产管理公司\", \"code\": \"WWSQ202112230004\", \"vouchdate\": \"2021-12-23 00:00:00\", \"status\": 1, \"transTypeId\": \"****************\", \"transTypeName\": \"标准委外\", \"transTypeExtendAttrsJson\": \"{specialType:none,businessType:wholeOutsourcing}\", \"departmentId\": \"****************\", \"departmentName\": \"仓储部\", \"operatorId\": \"****************\", \"operatorName\": \"张三\", \"sourceType\": \"1\", \"isWfControlled\": false, \"verifystate\": 2, \"pubts\": \"2021-12-23 20:14:33\", \"subcontractRequisitionProduct\": { \"id\": \"\", \"materialId\": \"****************\", \"productId\": \"****************\", \"outsourceOrgId\": \"****************\", \"rcvOrgId\": \"****************\", \"productCode\": \"\", \"productName\": \"P10\", \"demandQuantityDU\": 10, \"demandUnitId\": 2325529461018880, \"demandUnitName\": \"袋\", \"demandUnitTruncationType\": 4, \"demandUnitPrecision\": 1, \"demandQuantityMU\": 10, \"mainUnitId\": 2325529461018880, \"mainUnitName\": \"袋\", \"mainUnitTruncationType\": 4, \"mainUnitPrecision\": 1, \"requisitionDate\": \"2022-01-12 00:00:00\", \"rcvOrgName\": \"资产管理公司\", \"outsourceOrgName\": \"资产管理公司\" } } ], \"sumRecordList\": [ { \"subcontractRequisitionProduct\": { \"demandQuantityDU\": 5533.5333, \"demandQuantityMU\": \"6632.3\" } } ], \"pageCount\": 1, \"beginPageIndex\": 1, \"endPageIndex\": 1 } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "edit": false, "ytenantId": 0, "right": true}, {"id": 2009482629538644087, "apiId": "4474f0703e674ba3b3325f7133e4f990", "content": "{ \"code\": \"999\", \"message\": \"非法的时间： 11111\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "edit": false, "ytenantId": 0, "right": false}]}, "apiDemoReturnDTOList": {"apiDemoReturnDTOList": [{"id": 2009482629538644086, "apiId": "4474f0703e674ba3b3325f7133e4f990", "content": "{ \"code\": 200, \"message\": \"操作成功\", \"data\": { \"pageIndex\": 1, \"pageSize\": 20, \"recordCount\": 1, \"recordList\": [ { \"id\": ****************, \"orgId\": \"****************\", \"orgName\": \"资产管理公司\", \"code\": \"WWSQ202112230004\", \"vouchdate\": \"2021-12-23 00:00:00\", \"status\": 1, \"transTypeId\": \"****************\", \"transTypeName\": \"标准委外\", \"transTypeExtendAttrsJson\": \"{specialType:none,businessType:wholeOutsourcing}\", \"departmentId\": \"****************\", \"departmentName\": \"仓储部\", \"operatorId\": \"****************\", \"operatorName\": \"张三\", \"sourceType\": \"1\", \"isWfControlled\": false, \"verifystate\": 2, \"pubts\": \"2021-12-23 20:14:33\", \"subcontractRequisitionProduct\": { \"id\": \"\", \"materialId\": \"****************\", \"productId\": \"****************\", \"outsourceOrgId\": \"****************\", \"rcvOrgId\": \"****************\", \"productCode\": \"\", \"productName\": \"P10\", \"demandQuantityDU\": 10, \"demandUnitId\": 2325529461018880, \"demandUnitName\": \"袋\", \"demandUnitTruncationType\": 4, \"demandUnitPrecision\": 1, \"demandQuantityMU\": 10, \"mainUnitId\": 2325529461018880, \"mainUnitName\": \"袋\", \"mainUnitTruncationType\": 4, \"mainUnitPrecision\": 1, \"requisitionDate\": \"2022-01-12 00:00:00\", \"rcvOrgName\": \"资产管理公司\", \"outsourceOrgName\": \"资产管理公司\" } } ], \"sumRecordList\": [ { \"subcontractRequisitionProduct\": { \"demandQuantityDU\": 5533.5333, \"demandQuantityMU\": \"6632.3\" } } ], \"pageCount\": 1, \"beginPageIndex\": 1, \"endPageIndex\": 1 } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "edit": false, "ytenantId": 0, "right": true}, {"id": 2009482629538644087, "apiId": "4474f0703e674ba3b3325f7133e4f990", "content": "{ \"code\": \"999\", \"message\": \"非法的时间： 11111\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "edit": false, "ytenantId": 0, "right": false}]}, "routingStgy": 0, "routingStgyList": "", "apiDemoReturnDTO": {"id": 2009482629538644086, "apiId": "4474f0703e674ba3b3325f7133e4f990", "content": "{ \"code\": 200, \"message\": \"操作成功\", \"data\": { \"pageIndex\": 1, \"pageSize\": 20, \"recordCount\": 1, \"recordList\": [ { \"id\": ****************, \"orgId\": \"****************\", \"orgName\": \"资产管理公司\", \"code\": \"WWSQ202112230004\", \"vouchdate\": \"2021-12-23 00:00:00\", \"status\": 1, \"transTypeId\": \"****************\", \"transTypeName\": \"标准委外\", \"transTypeExtendAttrsJson\": \"{specialType:none,businessType:wholeOutsourcing}\", \"departmentId\": \"****************\", \"departmentName\": \"仓储部\", \"operatorId\": \"****************\", \"operatorName\": \"张三\", \"sourceType\": \"1\", \"isWfControlled\": false, \"verifystate\": 2, \"pubts\": \"2021-12-23 20:14:33\", \"subcontractRequisitionProduct\": { \"id\": \"\", \"materialId\": \"****************\", \"productId\": \"****************\", \"outsourceOrgId\": \"****************\", \"rcvOrgId\": \"****************\", \"productCode\": \"\", \"productName\": \"P10\", \"demandQuantityDU\": 10, \"demandUnitId\": 2325529461018880, \"demandUnitName\": \"袋\", \"demandUnitTruncationType\": 4, \"demandUnitPrecision\": 1, \"demandQuantityMU\": 10, \"mainUnitId\": 2325529461018880, \"mainUnitName\": \"袋\", \"mainUnitTruncationType\": 4, \"mainUnitPrecision\": 1, \"requisitionDate\": \"2022-01-12 00:00:00\", \"rcvOrgName\": \"资产管理公司\", \"outsourceOrgName\": \"资产管理公司\" } } ], \"sumRecordList\": [ { \"subcontractRequisitionProduct\": { \"demandQuantityDU\": 5533.5333, \"demandQuantityMU\": \"6632.3\" } } ], \"pageCount\": 1, \"beginPageIndex\": 1, \"endPageIndex\": 1 } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "edit": false, "ytenantId": 0, "right": true}, "apiDemoReturnDTOError": {"id": 2009482629538644087, "apiId": "4474f0703e674ba3b3325f7133e4f990", "content": "{ \"code\": \"999\", \"message\": \"非法的时间： 11111\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "edit": false, "ytenantId": 0, "right": false}, "errorCodeDTOS": {"errorCodeDTOS": {"id": 2009482629538644083, "apiId": "4474f0703e674ba3b3325f7133e4f990", "errorCode": 999, "errorMessage": "取决于错误类型，不同错误信息不同", "errorType": "API", "errorcodeDesc": "", "gmtCreate": "2024-05-31 13:49:27.000", "gmtUpdate": "2024-05-31 13:49:27.000", "apiName": "", "edit": false, "defErrorId": 2009368443538112684, "ytenantId": 0, "displayCodeId": ""}}, "displayCodeApiConfigDTOS": "", "tokenPlugin": "", "paramParsePlugin": "", "authPlugin": {"id": "09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "", "name": "友户通token认证业务扩展插件", "configurable": false, "description": "YonsuitBusinessExtendPlugin", "pluginType": "auth", "pluginTypeName": "业务扩展插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": false, "gmtCreate": "2020-05-22 00:00:00", "gmtUpdate": "2020-05-22 00:00:00", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "4474f0703e674ba3b3325f7133e4f990", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "resultParsePlugin": {"id": "w181ed01-1e9b-4350-b994-71a66f017555", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "resultParse", "name": "返回参数转换插件", "configurable": false, "description": "解决返回值中带！的，转换为json", "pluginType": "resultParse", "pluginTypeName": "返回值解析插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.result.ResultMapTransferParsePlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": true, "gmtCreate": "2020-07-29 00:00:00", "gmtUpdate": "", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "4474f0703e674ba3b3325f7133e4f990", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "mapReturnPluginConfig": "", "billNo": "", "domain": "", "apiCategory": "", "docUrl": "", "pathMatch": 0, "createUser": "acbbf6ea-0b66-481c-b6dc-204a76346adf", "createUserName": "", "approvalStatus": 1, "publishTime": "2025-06-24 20:53:16", "pathJoin": true, "timeOut": 30, "tokenPluginName": "", "authPluginName": "", "resultPluginName": "", "apiDemoReturnRightDemo": "", "apiDemoReturnErrorDemo": "", "mock": false, "mockTimeout": "", "customUrl": "subcontractrequisition/list", "fixedUrl": "/yonbip/mfg/", "apiCode": "4474f0703e674ba3b3325f7133e4f990", "tokenCheckType": 0, "enableMulti": false, "multiField": "", "idempotent": "non", "bidirectionalSSL": "", "ucgSchema": "HTTPS", "updateUserId": "acbbf6ea-0b66-481c-b6dc-204a76346adf", "updateUserName": "昵某-13662080373", "paramIsForce": true, "userIDPassthrough": true, "applyUser": "", "applyMsg": "", "dr": 0, "microServiceCode": "domain.yonbip-mm-mfpo", "applicationCode": "yonbip-mm-mfpo", "privacyCategory": 1, "privacyLevel": 1, "apiDesigned": 0, "serviceType": 0, "integrateSchemeCode": "", "integrateSchemeName": "", "integrateObjectCode": "", "integrateObjectName": "", "integrateObjectCreatedType": "", "returnIntegObjId": "", "returnIntegObjName": "", "apiIntegrateDTOList": "", "apiRouteInfoDTOList": "", "arrayParam": false, "fileSize": "", "cc": true, "paramTransferMode": 2, "ytenantId": 0, "statusConf": "", "scene": 1, "version": "", "bizObjUri": "", "bizObjOperationType": "", "apiDefId": 2009368443538112520, "paramExtBizObjCode": "", "paramExtBizObjName": "", "paramExtRequest": 1, "paramExtResponse": 1, "paramExtInExtendKey": 1, "openScene": 1, "integrationScene": "", "apiType": "", "paramMark": "", "integrateSysId": "", "integrateSysName": "", "integrateSysCode": "", "dataZoneSetting": false, "reqDataZoneSetting": false, "respDataZoneSetting": false, "reqDataAllQuery": false, "reqDataAllBody": false, "respDataAllBody": false, "chargeStatus": 1, "beforeSpeed": 40, "afterSpeed": 80, "speedStatus": false, "reqDataRefPath": "", "respDataRefPath": "", "pubHistory": {"pubHistory": {"id": 2298405328530702345, "apiId": "4474f0703e674ba3b3325f7133e4f990", "apiName": "委外申请单列表查询", "applyReason": "", "publishUserName": "", "version": 20250624205316, "operationTime": "2025-06-24", "gmtCreate": "", "gmtUpdate": "", "changes": {"changes": [{"changePosition": "baseInfo", "newList": "", "updateList": {"updateList": {"changeProperty": "enableMulti", "oldValue": "", "newValue": false}}, "deleteList": ""}, {"changePosition": "paramDTOS", "newList": "", "updateList": {"updateList": [{"changeProperty": "pageIndex", "oldValue": "{\"id\":\"2046156179320602817\",\"name\":\"pageIndex\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602639\",\"array\":false,\"paramDesc\":\"页号 默认值:1\",\"paramType\":\"int\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_pageIndex\",\"example\":\"1\",\"fullName\":\"\",\"required\":true,\"defaultValue\":\"1\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2009482629538643985\",\"name\":\"pageIndex\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112522\",\"array\":false,\"paramDesc\":\"页号 默认值:1\",\"paramType\":\"int\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_pageIndex\",\"example\":\"1\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":true,\"defaultValue\":\"1\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "pageSize", "oldValue": "{\"id\":\"2046156179320602818\",\"name\":\"pageSize\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602640\",\"array\":false,\"paramDesc\":\"每页行数 默认值:10\",\"paramType\":\"int\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_pageSize\",\"example\":\"10\",\"fullName\":\"\",\"required\":true,\"defaultValue\":\"10\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2009482629538643986\",\"name\":\"pageSize\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112523\",\"array\":false,\"paramDesc\":\"每页行数 默认值:10\",\"paramType\":\"int\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_pageSize\",\"example\":\"10\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":true,\"defaultValue\":\"10\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "orgId", "oldValue": "{\"id\":\"2046156179320602819\",\"name\":\"orgId\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602641\",\"array\":true,\"paramDesc\":\"组织ID\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_orgId\",\"example\":\"[\\\"1866605942198527\\\"]\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2009482629538643987\",\"name\":\"orgId\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112524\",\"array\":true,\"paramDesc\":\"组织ID\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_orgId\",\"example\":\"[\\\"1866605942198527\\\"]\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "code", "oldValue": "{\"id\":\"2046156179320602820\",\"name\":\"code\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602642\",\"array\":false,\"paramDesc\":\"委外申请单号\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_code\",\"example\":\"WWSQ202105010001\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643988\",\"name\":\"code\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112525\",\"array\":false,\"paramDesc\":\"委外申请单号\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_code\",\"example\":\"WWSQ202105010001\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "vouchdate", "oldValue": "{\"id\":\"2046156179320602821\",\"name\":\"vouchdate\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602643\",\"array\":false,\"paramDesc\":\"单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_vouchdate\",\"example\":\"2021-03-02|2021-03-02 23:59:59\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643989\",\"name\":\"vouchdate\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112526\",\"array\":false,\"paramDesc\":\"单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_vouchdate\",\"example\":\"2021-03-02|2021-03-02 23:59:59\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "transTypeId", "oldValue": "{\"id\":\"2046156179320602822\",\"name\":\"transTypeId\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602644\",\"array\":true,\"paramDesc\":\"交易类型ID\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_transTypeId\",\"example\":\"[\\\"1866605942198526\\\"]\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2009482629538643990\",\"name\":\"transTypeId\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112527\",\"array\":true,\"paramDesc\":\"交易类型ID\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_transTypeId\",\"example\":\"[\\\"1866605942198526\\\"]\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "status", "oldValue": "{\"id\":\"2046156179320602823\",\"name\":\"status\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602645\",\"array\":false,\"paramDesc\":\"申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。\",\"paramType\":\"short\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_status\",\"example\":\"0\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2009482629538643991\",\"name\":\"status\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112528\",\"array\":false,\"paramDesc\":\"申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。\",\"paramType\":\"short\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_status\",\"example\":\"0\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "materialId", "oldValue": "{\"id\":\"2046156179320602824\",\"name\":\"materialId\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602646\",\"array\":true,\"paramDesc\":\"制造物料ID\",\"paramType\":\"long\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_materialId\",\"example\":\"[1866605942198785]\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2009482629538643992\",\"name\":\"materialId\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112529\",\"array\":true,\"paramDesc\":\"制造物料ID\",\"paramType\":\"long\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_materialId\",\"example\":\"[1866605942198785]\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "productId", "oldValue": "{\"id\":\"2046156179320602825\",\"name\":\"productId\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602647\",\"array\":true,\"paramDesc\":\"物料ID\",\"paramType\":\"long\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_productId\",\"example\":\"[1866605942198650]\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643993\",\"name\":\"productId\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112530\",\"array\":true,\"paramDesc\":\"物料ID\",\"paramType\":\"long\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_productId\",\"example\":\"[1866605942198650]\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "requisitionDate", "oldValue": "{\"id\":\"2046156179320602826\",\"name\":\"requisitionDate\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602648\",\"array\":false,\"paramDesc\":\"需求日期（区间，格式2021-03-02|2021-03-02 23:59:59）\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_requisitionDate\",\"example\":\"2021-03-02|2021-03-02 23:59:59\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643994\",\"name\":\"requisitionDate\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112531\",\"array\":false,\"paramDesc\":\"需求日期（区间，格式2021-03-02|2021-03-02 23:59:59）\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_requisitionDate\",\"example\":\"2021-03-02|2021-03-02 23:59:59\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "departmentId", "oldValue": "{\"id\":\"2046156179320602827\",\"name\":\"departmentId\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602649\",\"array\":true,\"paramDesc\":\"需求部门ID\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_departmentId\",\"example\":\"[\\\"186660594219847\\\"]\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643995\",\"name\":\"departmentId\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112532\",\"array\":true,\"paramDesc\":\"需求部门ID\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_departmentId\",\"example\":\"[\\\"186660594219847\\\"]\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "operatorId", "oldValue": "{\"id\":\"2046156179320602828\",\"name\":\"operatorId\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602650\",\"array\":true,\"paramDesc\":\"需求人ID\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_operatorId\",\"example\":\"[\\\"186660594218648\\\"]\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643996\",\"name\":\"operatorId\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112533\",\"array\":true,\"paramDesc\":\"需求人ID\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_operatorId\",\"example\":\"[\\\"186660594218648\\\"]\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "simple", "oldValue": "{\"id\":\"2046156179320602803\",\"name\":\"simple\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602651\",\"array\":false,\"paramDesc\":\"扩展参数\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple\",\"example\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true}", "newValue": "{\"id\":\"2009482629538643971\",\"name\":\"simple\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112534\",\"array\":false,\"paramDesc\":\"扩展参数\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple\",\"example\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"}, {"changeProperty": "open_pubts_begin", "oldValue": "{\"id\":\"2046156179320602804\",\"name\":\"open_pubts_begin\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2046156179320602803\",\"defParamId\":\"2046156179320602652\",\"array\":false,\"paramDesc\":\"时间戳，开始时间\",\"paramType\":\"date\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.open_pubts_begin\",\"example\":\"2022-01-01 00:00:00\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"format\":\"yyyy-MM-dd HH:mm:ss\"}", "newValue": "{\"id\":\"2009482629538643972\",\"name\":\"open_pubts_begin\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2009482629538643971\",\"defParamId\":\"2009368443538112535\",\"array\":false,\"paramDesc\":\"时间戳，开始时间\",\"paramType\":\"date\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.open_pubts_begin\",\"example\":\"2022-01-01 00:00:00\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"format\":\"yyyy-MM-dd HH:mm:ss\"}"}, {"changeProperty": "open_pubts_end", "oldValue": "{\"id\":\"2046156179320602805\",\"name\":\"open_pubts_end\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2046156179320602803\",\"defParamId\":\"2046156179320602653\",\"array\":false,\"paramDesc\":\"时间戳，结束时间\",\"paramType\":\"date\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.open_pubts_end\",\"example\":\"2022-01-01 10:00:00\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"format\":\"yyyy-MM-dd HH:mm:ss\"}", "newValue": "{\"id\":\"2009482629538643973\",\"name\":\"open_pubts_end\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2009482629538643971\",\"defParamId\":\"2009368443538112536\",\"array\":false,\"paramDesc\":\"时间戳，结束时间\",\"paramType\":\"date\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.open_pubts_end\",\"example\":\"2022-01-01 10:00:00\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"format\":\"yyyy-MM-dd HH:mm:ss\"}"}, {"changeProperty": "simpleVOs", "oldValue": "{\"id\":\"2046156179320602806\",\"name\":\"simpleVOs\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2046156179320602654\",\"array\":true,\"paramDesc\":\"扩展查询条件\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643974\",\"name\":\"simpleVOs\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"defParamId\":\"2009368443538112537\",\"array\":true,\"paramDesc\":\"扩展查询条件\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs\",\"example\":\"\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "field", "oldValue": "{\"id\":\"2046156179320602812\",\"name\":\"field\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2046156179320602806\",\"defParamId\":\"2046156179320602655\",\"array\":false,\"paramDesc\":\"属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.field\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643980\",\"name\":\"field\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2009482629538643974\",\"defParamId\":\"2009368443538112538\",\"array\":false,\"paramDesc\":\"属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.field\",\"example\":\"\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "op", "oldValue": "{\"id\":\"2046156179320602813\",\"name\":\"op\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2046156179320602806\",\"defParamId\":\"2046156179320602656\",\"array\":false,\"paramDesc\":\"比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.op\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643981\",\"name\":\"op\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2009482629538643974\",\"defParamId\":\"2009368443538112539\",\"array\":false,\"paramDesc\":\"比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.op\",\"example\":\"\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "value1", "oldValue": "{\"id\":\"2046156179320602814\",\"name\":\"value1\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2046156179320602806\",\"defParamId\":\"2046156179320602657\",\"array\":false,\"paramDesc\":\"查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.value1\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643982\",\"name\":\"value1\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2009482629538643974\",\"defParamId\":\"2009368443538112540\",\"array\":false,\"paramDesc\":\"查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.value1\",\"example\":\"\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "value2", "oldValue": "{\"id\":\"2046156179320602815\",\"name\":\"value2\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2046156179320602806\",\"defParamId\":\"2046156179320602658\",\"array\":false,\"paramDesc\":\"查询条件值2\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.value2\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643983\",\"name\":\"value2\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2009482629538643974\",\"defParamId\":\"2009368443538112541\",\"array\":false,\"paramDesc\":\"查询条件值2\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.value2\",\"example\":\"\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "logicOp", "oldValue": "{\"id\":\"2046156179320602816\",\"name\":\"logicOp\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2046156179320602806\",\"defParamId\":\"2046156179320602659\",\"array\":false,\"paramDesc\":\"逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.logicOp\",\"example\":\"and\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643984\",\"name\":\"logicOp\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2009482629538643974\",\"defParamId\":\"2009368443538112542\",\"array\":false,\"paramDesc\":\"逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.logicOp\",\"example\":\"and\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "conditions", "oldValue": "{\"id\":\"2046156179320602807\",\"name\":\"conditions\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2046156179320602806\",\"defParamId\":\"2046156179320602660\",\"array\":true,\"paramDesc\":\"下级查询条件\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643975\",\"name\":\"conditions\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2009482629538643974\",\"defParamId\":\"2009368443538112543\",\"array\":true,\"paramDesc\":\"下级查询条件\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions\",\"example\":\"\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "field", "oldValue": "{\"id\":\"2046156179320602808\",\"name\":\"field\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2046156179320602807\",\"defParamId\":\"2046156179320602661\",\"array\":false,\"paramDesc\":\"属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions.field\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643976\",\"name\":\"field\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2009482629538643975\",\"defParamId\":\"2009368443538112544\",\"array\":false,\"paramDesc\":\"属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions.field\",\"example\":\"\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "op", "oldValue": "{\"id\":\"2046156179320602809\",\"name\":\"op\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2046156179320602807\",\"defParamId\":\"2046156179320602662\",\"array\":false,\"paramDesc\":\"逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions.op\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643977\",\"name\":\"op\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2009482629538643975\",\"defParamId\":\"2009368443538112545\",\"array\":false,\"paramDesc\":\"逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions.op\",\"example\":\"\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "value1", "oldValue": "{\"id\":\"2046156179320602810\",\"name\":\"value1\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2046156179320602807\",\"defParamId\":\"2046156179320602663\",\"array\":false,\"paramDesc\":\"查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions.value1\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643978\",\"name\":\"value1\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2009482629538643975\",\"defParamId\":\"2009368443538112546\",\"array\":false,\"paramDesc\":\"查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions.value1\",\"example\":\"\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}, {"changeProperty": "value2", "oldValue": "{\"id\":\"2046156179320602811\",\"name\":\"value2\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2046156179320602807\",\"defParamId\":\"2046156179320602664\",\"array\":false,\"paramDesc\":\"查询条件值2\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions.value2\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}", "newValue": "{\"id\":\"2009482629538643979\",\"name\":\"value2\",\"apiId\":\"4474f0703e674ba3b3325f7133e4f990\",\"parentId\":\"2009482629538643975\",\"defParamId\":\"2009368443538112547\",\"array\":false,\"paramDesc\":\"查询条件值2\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions.value2\",\"example\":\"\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"}]}, "deleteList": ""}]}}}, "deprecated": 0, "recommendedApiId": "", "recommendedApiName": "", "domainAppCode": "productionorder.po_subcontract_requisition_card", "multiVersion": 0, "apiTag": ""}}, {"success": true, "code": 200, "message": "", "data": [{"id": "816bd168-7b09-42fe-9074-3653bd6172ce", "name": "U9005", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "U9委外申请单号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:33:14", "gmtUpdate": "2025-07-26 17:33:14", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 100, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "c751949a-eaf1-4f78-9599-3b09ff0762ad", "name": "WWSQ01", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "客户", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "aa.merchant.Merchant", "ytenantId": "", "paramOrder": "", "bizType": "quote", "baseType": false, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:33:14", "gmtUpdate": "2025-07-26 17:33:14", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": true, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:33:14", "gmtUpdate": "2025-07-26 17:33:14", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": {"id": "8e30ab16-f7e7-4c01-958e-4bf2affbd79e", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "long", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:33:21", "gmtUpdate": "2025-07-26 17:33:21", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}}, {"success": true, "code": 200, "message": "", "data": [{"id": "4e549813-f0b8-4d2e-8dbf-f31810aca911", "name": "WW0555", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "委外申请单参考需求日期", "paramType": "Date", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "date", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:33:44", "gmtUpdate": "2025-07-26 17:33:44", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "f1151657-7698-4761-93c4-76b5a4c1b711", "name": "XS11", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求分类号test", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:33:44", "gmtUpdate": "2025-07-26 17:33:44", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "959cc247-5968-4914-bd90-4c56c3751ffe", "name": "XS15", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "顾客订单号（订单表体）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:33:44", "gmtUpdate": "2025-07-26 17:33:44", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:33:44", "gmtUpdate": "2025-07-26 17:33:44", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": [{"id": "72113971-ae4c-4188-bc55-44b6173f4e0b", "name": "XS15", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "顾客订单号（订单表体）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:33:55", "gmtUpdate": "2025-07-26 17:33:55", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "b946709d-f4d9-4a43-a551-f55beee7f3d5", "name": "XXX0111", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求分类项", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:33:55", "gmtUpdate": "2025-07-26 17:33:55", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:33:55", "gmtUpdate": "2025-07-26 17:33:55", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}]