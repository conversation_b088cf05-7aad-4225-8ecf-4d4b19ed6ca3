@echo off
chcp 65001 >nul
echo ========================================
echo      YS-API V3.0 统一启动脚本
echo ========================================
echo.

echo 🚀 启动前端服务器 (端口8080)...
start "前端服务器" cmd /k "cd /d "%~dp0frontend" && python -m http.server 8080"

timeout /t 3 >nul

echo 🚀 启动后端API服务器 (端口8000)...
start "后端API服务器" cmd /k "cd /d "%~dp0backend" && python start_simple.py"

echo.
echo ✅ 服务器启动完成!
echo 📱 前端访问地址: http://localhost:8080
echo 📡 后端API地址: http://localhost:8000
echo 🔧 健康检查: http://localhost:8000/health
echo.
echo 按任意键关闭此窗口...
pause >nul
