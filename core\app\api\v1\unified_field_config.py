import structlog
from fastapi.responses import Response

from ...services.unified_field_manager import UnifiedFieldManager

"""
YS-API 统一字段配置API
基于v3-D的成功实现，提供稳定的字段选择和管理接口
"""


logger = structlog.get_logger()
router = APIRouter()

# 初始化统一字段管理器
field_manager = None


def get_field_managerr():
    """TODO: Add function description."""
    global field_manager
    if field_manager is None:
        field_manager = UnifiedFieldManager()
    return field_manager


# 预初始化字段管理器
try:
    field_manager = UnifiedFieldManager()
except Exception:
    logger.error("初始化字段管理器失败", error=str(e))
    field_manager = None


@router.get("/modules")
async def get_available_modules() -> List[Dict[str, Any]]:
    """
    获取可用模块列表

    Returns:
        List[Dict]: 模块列表，包含模块名、显示名和字段数
    """
    try:
        logger.info("开始获取可用模块...")

        # 使用独立的数据源，不依赖字段管理器
        modules = [
            {
                "module_name": "purchase_order",
                "display_name": "采购订单",
                "total_fields": 100,
            },
            {
                "module_name": "sales_order",
                "display_name": "销售订单",
                "total_fields": 80,
            },
            {
                "module_name": "production_order",
                "display_name": "生产订单",
                "total_fields": 120,
            },
            {
                "module_name": "subcontract_order",
                "display_name": "委外订单",
                "total_fields": 90,
            },
            {
                "module_name": "applyorder",
                "display_name": "请购单",
                "total_fields": 105,
            },
            {
                "module_name": "subcontract_requisition",
                "display_name": "委外请购",
                "total_fields": 70,
            },
            {
                "module_name": "product_receipt",
                "display_name": "产品入库单",
                "total_fields": 85,
            },
            {
                "module_name": "purchase_receipt",
                "display_name": "采购入库",
                "total_fields": 95,
            },
            {
                "module_name": "subcontract_receipt",
                "display_name": "委外入库",
                "total_fields": 88,
            },
            {
                "module_name": "materialout",
                "display_name": "材料出库单",
                "total_fields": 75,
            },
            {
                "module_name": "sales_out",
                "display_name": "销售出库",
                "total_fields": 82,
            },
            {"module_name": "inventory", "display_name": "现存量", "total_fields": 60},
            {
                "module_name": "inventory_report",
                "display_name": "现存量报表",
                "total_fields": 65,
            },
            {
                "module_name": "requirements_planning",
                "display_name": "需求计划",
                "total_fields": 110,
            },
            {
                "module_name": "material_master",
                "display_name": "物料档案",
                "total_fields": 50,
            },
        ]

        logger.info("返回模块列表成功", count=len(modules))
        return modules

    except Exception:
        logger.error(
            "获取可用模块失败",
            error=str(e),
            error_type=type(e).__name__,
            exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取模块列表失败: {str(e)}")


@router.get("/modules/{module_name}/fields")
async def get_user_field_config(
    module_name: str,
    user_id: str = Query(..., description="用户ID"),
    sync_from_existing: bool = Query(False, description="是否从现有配置同步"),
) -> Dict:
    """
    获取用户字段配置

    Args:
        module_name: 模块名称
        user_id: 用户ID
        sync_from_existing: 是否从现有配置同步

    Returns:
        Dict: 用户字段配置
    """
    try:
        manager = get_field_manager()

        # 如果需要同步，先同步现有配置
        if sync_from_existing:
            await manager.sync_from_existing_config(module_name)
            logger.info("已同步现有配置", module_name=module_name)

        # 获取用户字段配置
        config = await manager.get_user_field_config(module_name, user_id)

        logger.info(
            "获取用户字段配置成功",
            module_name=module_name,
            user_id=user_id,
            total_fields=config.get("total_fields", 0),
            selected_fields=config.get("selected_fields", 0),
        )

        # 返回正确的响应格式
        return {"success": True, "data": config}

    except Exception:
        logger.error(
            "获取用户字段配置失败",
            module_name=module_name,
            user_id=user_id,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail=f"获取字段配置失败: {str(e)}")


@router.put("/modules/{module_name}/fields/{field_name}")
async def update_field_selection(
    module_name: str,
    field_name: str,
    user_id: str = Query(..., description="用户ID"),
    is_selected: bool = Query(..., description="是否选择该字段"),
) -> Dict:
    """
    更新字段选择状态

    Args:
        module_name: 模块名称
        field_name: 字段名称
        user_id: 用户ID
        is_selected: 是否选择

    Returns:
        Dict: 更新结果
    """
    try:
        manager = get_field_manager()
        success = await manager.update_field_selection(
            module_name, user_id, field_name, is_selected
        )

        if success:
            logger.info(
                "更新字段选择成功",
                module_name=module_name,
                user_id=user_id,
                field_name=field_name,
                is_selected=is_selected,
            )

            return {
                "success": True,
                "message": "字段选择更新成功",
                "data": {
                    "module_name": module_name,
                    "field_name": field_name,
                    "is_selected": is_selected,
                    "user_id": user_id,
                },
            }
        else:
            raise HTTPException(status_code=500, detail="更新字段选择失败")

    except Exception:
        logger.error(
            "更新字段选择失败",
            module_name=module_name,
            user_id=user_id,
            field_name=field_name,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail=f"更新字段选择失败: {str(e)}")


@router.options("/modules/{module_name}/fields/{field_name}/chinese_name")
async def options_chinese_name(module_name: str, field_name: str):
    """处理chinese_name接口的OPTIONS预检请求"""

    return Response(
        status_code=200,
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "PUT, OPTIONS",
            "Access-Control-Allow-Headers": "*",
        },
    )


@router.put("/modules/{module_name}/fields/{field_name}/chinese_name")
async def update_chinese_name(
    module_name: str,
    field_name: str,
    user_id: str = Query(..., description="用户ID"),
    chinese_name: str = Query(..., description="中文名称"),
) -> Dict:
    """
    更新字段中文名称

    Args:
        module_name: 模块名称
        field_name: 字段名称
        user_id: 用户ID
        chinese_name: 中文名称

    Returns:
        Dict: 更新结果
    """
    try:
        manager = get_field_manager()
        await manager.update_chinese_name(
            module_name, user_id, field_name, chinese_name
        )

        logger.info(
            "更新中文名称成功",
            module_name=module_name,
            field_name=field_name,
            user_id=user_id,
            chinese_name=chinese_name,
        )

        return {
            "success": True,
            "message": "中文名称更新成功",
            "data": {
                "module_name": module_name,
                "field_name": field_name,
                "chinese_name": chinese_name,
                "user_id": user_id,
            },
        }

    except Exception:
        logger.error(
            "更新中文名称失败",
            module_name=module_name,
            field_name=field_name,
            user_id=user_id,
            chinese_name=chinese_name,
            error=str(e),
            error_type=type(e).__name__,
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail=f"更新中文名称失败: {str(e)}")


@router.options("/modules/{module_name}/fields/{field_name}/data_type")
async def options_data_type(module_name: str, field_name: str):
    """OPTIONS请求处理"""
    return {"message": "OK"}


@router.put("/modules/{module_name}/fields/{field_name}/data_type")
async def update_data_type(
    module_name: str,
    field_name: str,
    user_id: str = Query(..., description="用户ID"),
    data_type: str = Query(..., description="数据类型"),
) -> Dict:
    """
    更新字段数据类型

    Args:
        module_name: 模块名称
        field_name: 字段名称
        user_id: 用户ID
        data_type: 数据类型

    Returns:
        Dict: 更新结果
    """
    try:
        manager = get_field_manager()
        await manager.update_data_type(module_name, user_id, field_name, data_type)

        logger.info(
            "更新数据类型成功",
            module_name=module_name,
            field_name=field_name,
            user_id=user_id,
            data_type=data_type,
        )

        return {
            "success": True,
            "message": "数据类型更新成功",
            "data": {
                "module_name": module_name,
                "field_name": field_name,
                "data_type": data_type,
                "user_id": user_id,
            },
        }

    except Exception:
        logger.error(
            "更新数据类型失败",
            module_name=module_name,
            field_name=field_name,
            user_id=user_id,
            data_type=data_type,
            error=str(e),
            error_type=type(e).__name__,
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail=f"更新数据类型失败: {str(e)}")


@router.options("/modules/{module_name}/fields/{field_name}/lock")
async def options_field_lock(module_name: str, field_name: str):
    """处理lock接口的OPTIONS预检请求"""

    return Response(
        status_code=200,
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "PUT, OPTIONS",
            "Access-Control-Allow-Headers": "*",
        },
    )


@router.put("/modules/{module_name}/fields/{field_name}/lock")
async def update_field_lock(
    module_name: str,
    field_name: str,
    user_id: str = Query(..., description="用户ID"),
    locked: bool = Query(..., description="是否锁定"),
) -> Dict:
    """
    更新字段锁定状态

    Args:
        module_name: 模块名称
        field_name: 字段名称
        user_id: 用户ID
        locked: 是否锁定

    Returns:
        Dict: 更新结果
    """
    try:
        manager = get_field_manager()
        success = await manager.update_field_lock(
            module_name, user_id, field_name, locked
        )

        if success:
            logger.info(
                "更新字段锁定状态成功",
                module_name=module_name,
                user_id=user_id,
                field_name=field_name,
                locked=locked,
            )

            return {
                "success": True,
                "message": "字段锁定状态更新成功",
                "data": {
                    "module_name": module_name,
                    "field_name": field_name,
                    "locked": locked,
                    "user_id": user_id,
                },
            }
        else:
            raise HTTPException(status_code=500, detail="更新字段锁定状态失败")

    except Exception:
        logger.error(
            "更新字段锁定状态失败",
            module_name=module_name,
            user_id=user_id,
            field_name=field_name,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail=f"更新字段锁定状态失败: {str(e)}")


@router.post("/modules/{module_name}/sync")
async def sync_module_from_existing(module_name: str) -> Dict:
    """
    从现有配置同步模块

    Args:
        module_name: 模块名称

    Returns:
        Dict: 同步结果
    """
    try:
        manager = get_field_manager()
        success = await manager.sync_from_existing_config(module_name)

        if success:
            logger.info("同步模块成功", module_name=module_name)
            return {
                "success": True,
                "message": f"模块 {module_name} 同步成功",
                "data": {"module_name": module_name, "synced": True},
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"同步模块 {module_name} 失败")

    except Exception:
        logger.error("同步模块失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"同步模块失败: {str(e)}")


@router.get("/diagnostic")
async def get_diagnostic_info() -> Dict:
    """
    获取诊断信息

    Returns:
        Dict: 诊断信息
    """
    try:
        manager = get_field_manager()

        # 获取可用模块
        modules = await manager.get_available_modules()

        # 检查目录状态
        baseline_dir = manager.baseline_dir
        user_config_dir = manager.user_config_dir

        diagnostic_info = {
            "manager_status": "initialized" if manager else "failed",
            "baseline_dir": str(baseline_dir),
            "baseline_dir_exists": baseline_dir.exists(),
            "user_config_dir": str(user_config_dir),
            "user_config_dir_exists": user_config_dir.exists(),
            "available_modules": modules,
            "module_count": len(modules),
        }

        logger.info("获取诊断信息成功", diagnostic_info=diagnostic_info)
        return diagnostic_info

    except Exception:
        logger.error("获取诊断信息失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取诊断信息失败: {str(e)}")


@router.get("/modules/{module_name}/stats")
async def get_module_stats(
    module_name: str, user_id: str = Query(..., description="用户ID")
) -> Dict:
    """
    获取模块统计信息

    Args:
        module_name: 模块名称
        user_id: 用户ID

    Returns:
        Dict: 统计信息
    """
    try:
        manager = get_field_manager()
        config = await manager.get_user_field_config(module_name, user_id)

        # 计算统计信息
        total_fields = config.get("total_fields", 0)
        selected_fields = config.get("selected_fields", 0)
        selection_rate = (
            (selected_fields / total_fields * 100) if total_fields > 0 else 0
        )

        # 计算用户修改的字段数
        user_modified_count = 0
        for field_info in config.get("fields", {}).values():
            if field_info.get("user_modified", False):
                user_modified_count += 1

        stats = {
            "module_name": module_name,
            "user_id": user_id,
            "total_fields": total_fields,
            "selected_fields": selected_fields,
            "selection_rate": round(selection_rate, 2),
            "user_modified_count": user_modified_count,
            "last_updated": config.get("last_updated", ""),
        }

        logger.info("获取模块统计信息成功", stats=stats)
        return stats

    except Exception:
        logger.error(
            "获取模块统计信息失败",
            module_name=module_name,
            user_id=user_id,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/modules-simple")
async def get_available_modules_simple() -> List[Dict[str, Any]]:
    """
    获取简化的模块列表（用于快速选择）

    Returns:
        List[Dict]: 简化的模块列表
    """
    try:
        # 返回简化的模块列表
        modules = [
            {"module_name": "purchase_order", "display_name": "采购订单"},
            {"module_name": "sales_order", "display_name": "销售订单"},
            {"module_name": "production_order", "display_name": "生产订单"},
            {"module_name": "applyorder", "display_name": "请购单"},
            {"module_name": "product_receipt", "display_name": "产品入库单"},
            {"module_name": "purchase_receipt", "display_name": "采购入库"},
            {"module_name": "materialout", "display_name": "材料出库单"},
            {"module_name": "sales_out", "display_name": "销售出库"},
            {"module_name": "inventory", "display_name": "现存量"},
            {"module_name": "requirements_planning", "display_name": "需求计划"},
            {"module_name": "material_master", "display_name": "物料档案"},
        ]

        return modules

    except Exception:
        logger.error("获取简化模块列表失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取模块列表失败: {str(e)}")


@router.options("/modules/{module_name}/fields/{field_name}/restore")
async def options_restore_field(module_name: str, field_name: str):
    """OPTIONS请求处理"""
    return {"message": "OK"}


@router.put("/modules/{module_name}/fields/{field_name}/restore")
async def restore_field_to_baseline(
    module_name: str, field_name: str, user_id: str = Query(..., description="用户ID")
) -> Dict:
    """
    恢复字段到基准配置

    Args:
        module_name: 模块名称
        field_name: 字段名称
        user_id: 用户ID

    Returns:
        Dict: 恢复结果
    """
    try:
        manager = get_field_manager()
        result = await manager.restore_field_to_baseline(
            module_name, user_id, field_name
        )

        logger.info(
            "恢复字段到基准配置成功",
            module_name=module_name,
            field_name=field_name,
            user_id=user_id,
        )

        return {"success": True, "message": "字段已恢复到基准配置", "data": result}

    except Exception:
        logger.error(
            "恢复字段到基准配置失败",
            module_name=module_name,
            field_name=field_name,
            user_id=user_id,
            error=str(e),
            error_type=type(e).__name__,
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail=f"恢复字段到基准配置失败: {str(e)}")
