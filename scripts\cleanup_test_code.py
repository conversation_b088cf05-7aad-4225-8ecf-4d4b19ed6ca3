import json
import os
import re
import sys
from datetime import datetime
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代码和模拟代码清理检查脚本
Test Code and Mock Code Cleanup Analyzer
"""


class TestCodeAnalyzer:
    def __init___(self):
    """TODO: Add function description."""
    self.project_root = Path(__file__).parent.parent
    self.scan_results = {}
    self.cleanup_recommendations = {}

    def print_headerr(self, title):
    """TODO: Add function description."""
    print("\n" + "=" * 60)
    print(f"🧹 {title}")
    print("=" * 60)

    def print_stepp(self, step, status="🔍"):
    """TODO: Add function description."""
    print(f"{status} {step}")

    def scan_test_files(self) -> Dict[str, List[str]]:
        """扫描测试文件"""
        self.print_header("扫描测试文件")

        test_patterns = [
            "*test*.py",
            "*test*.js",
            "*test*.html",
            "test_*.py",
            "*_test.py",
            "*Test*.py"
        ]

        test_files = []
        for pattern in test_patterns:
            files = list(self.project_root.rglob(pattern))
            test_files.extend([str(f) for f in files])

        # 去重
        test_files = list(set(test_files))

        # 分类
        categorized = {
            "unit_tests": [],
            "integration_tests": [],
            "debug_tests": [],
            "temp_tests": []
        }

        for file_path in test_files:
            file_name = Path(file_path).name.lower()

            if "unit" in file_name or "unittest" in file_name:
                categorized["unit_tests"].append(file_path)
            elif "integration" in file_name or "e2e" in file_name:
                categorized["integration_tests"].append(file_path)
            elif "debug" in file_name or "temp" in file_name or "tmp" in file_name:
                categorized["debug_tests"].append(file_path)
            else:
                categorized["temp_tests"].append(file_path)

        for category, files in categorized.items():
            count = len(files)
            self.print_step(f"{category}: {count} 个文件")
            if count > 0 and count <= 5:  # 只显示少量文件
                for file_path in files:
                    rel_path = Path(file_path).relative_to(self.project_root)
                    print(f"    {rel_path}")

        self.scan_results["test_files"] = categorized
        return categorized

    def scan_mock_files(self) -> Dict[str, List[str]]:
        """扫描模拟数据和代码文件"""
        self.print_header("扫描模拟数据和代码文件")

        mock_patterns = [
            "*mock*",
            "*dummy*",
            "*fake*",
            "*stub*"
        ]

        mock_files = []
        for pattern in mock_patterns:
            files = list(self.project_root.rglob(pattern))
            mock_files.extend([str(f) for f in files if f.is_file()])

        # 去重并分类
        categorized = {
            "mock_data": [],
            "mock_modules": [],
            "dummy_files": []
        }

        for file_path in mock_files:
            file_name = Path(file_path).name.lower()

            if file_name.endswith(('.json', '.xml', '.csv', '.xlsx')):
                categorized["mock_data"].append(file_path)
            elif file_name.endswith(('.py', '.js')):
                categorized["mock_modules"].append(file_path)
            else:
                categorized["dummy_files"].append(file_path)

        for category, files in categorized.items():
            count = len(files)
            self.print_step(f"{category}: {count} 个文件")
            if count > 0 and count <= 5:
                for file_path in files:
                    rel_path = Path(file_path).relative_to(self.project_root)
                    print(f"    {rel_path}")

        self.scan_results["mock_files"] = categorized
        return categorized

    def scan_hardcoded_test_data(
            self) -> Dict[str, List[Tuple[str, int, str]]]:
        """扫描硬编码的测试数据"""
        self.print_header("扫描硬编码测试数据")

        # 定义测试数据模式
        test_data_patterns = [
            r'test_data\s*=',
            r'dummy_data\s*=',
            r'mock_response\s*=',
            r'fake_\w+\s*=',
            r'debug_\w+\s*=',
            r'"test_.*?"',
            r"'test_.*?'",
            r'TEST_.*\s*=',
            r'MOCK_.*\s*='
        ]

        hardcoded_data = {
            "python_files": [],
            "javascript_files": [],
            "config_files": []
        }

        # 扫描Python文件
        for py_file in self.project_root.rglob("*.py"):
            if py_file.is_file():
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    for pattern in test_data_patterns:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            line_num = content[:match.start()].count('\n') + 1
                            line_content = content.split(
                                '\n')[line_num - 1].strip()

                            hardcoded_data["python_files"].append((
                                str(py_file.relative_to(self.project_root)),
                                line_num,
                                line_content[:100]  # 截取前100字符
                            ))
                except (UnicodeDecodeError, PermissionError):
                    continue

        # 扫描JavaScript文件
        for js_file in self.project_root.rglob("*.js"):
            if js_file.is_file():
                try:
                    with open(js_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    for pattern in test_data_patterns:
                        matches = re.finditer(pattern, content, re.IGNORECASE)
                        for match in matches:
                            line_num = content[:match.start()].count('\n') + 1
                            line_content = content.split(
                                '\n')[line_num - 1].strip()

                            hardcoded_data["javascript_files"].append((
                                str(js_file.relative_to(self.project_root)),
                                line_num,
                                line_content[:100]
                            ))
                except (UnicodeDecodeError, PermissionError):
                    continue

        # 输出结果
        total_matches = sum(len(files) for files in hardcoded_data.values())
        self.print_step(f"发现 {total_matches} 处硬编码测试数据")

        for category, matches in hardcoded_data.items():
            if matches:
                self.print_step(f"{category}: {len(matches)} 处")
                # 只显示前5个匹配
                for i, (file_path, line_num, content) in enumerate(
                        matches[:5]):
                    print(f"    {file_path}:{line_num} - {content}")
                if len(matches) > 5:
                    print(f"    ... 还有 {len(matches) - 5} 处")

        self.scan_results["hardcoded_data"] = hardcoded_data
        return hardcoded_data

    def scan_temp_and_debug_files(self) -> Dict[str, List[str]]:
        """扫描临时文件和调试文件"""
        self.print_header("扫描临时文件和调试文件")

        temp_patterns = [
            "*.tmp",
            "*.temp",
            "*.backup",
            "*.bak",
            "*~",
            "*.log",
            "debug_*",
            "temp_*",
            "tmp_*"
        ]

        temp_files = []
        for pattern in temp_patterns:
            files = list(self.project_root.rglob(pattern))
            temp_files.extend([str(f) for f in files if f.is_file()])

        # 分类
        categorized = {
            "log_files": [],
            "backup_files": [],
            "temp_files": [],
            "debug_files": []
        }

        for file_path in temp_files:
            file_name = Path(file_path).name.lower()

            if file_name.endswith('.log'):
                categorized["log_files"].append(file_path)
            elif any(ext in file_name for ext in ['.backup', '.bak', '~']):
                categorized["backup_files"].append(file_path)
            elif any(prefix in file_name for prefix in ['debug_', 'debug']):
                categorized["debug_files"].append(file_path)
            else:
                categorized["temp_files"].append(file_path)

        for category, files in categorized.items():
            count = len(files)
            self.print_step(f"{category}: {count} 个文件")
            if count > 0 and count <= 3:
                for file_path in files:
                    rel_path = Path(file_path).relative_to(self.project_root)
                    print(f"    {rel_path}")

        self.scan_results["temp_files"] = categorized
        return categorized

    def generate_cleanup_recommendations(self):
        """生成清理建议"""
        self.print_header("生成清理建议")

        recommendations = {
            "immediate_delete": [],  # 可立即删除
            "review_before_delete": [],  # 需要审查后删除
            "keep_but_organize": [],  # 保留但重新组织
            "convert_to_config": []  # 转换为配置文件
        }

        # 分析测试文件
        test_files = self.scan_results.get("test_files", {})
        if test_files.get("debug_tests"):
            recommendations["immediate_delete"].extend(
                test_files["debug_tests"])
        if test_files.get("temp_tests"):
            recommendations["review_before_delete"].extend(
                test_files["temp_tests"])
        if test_files.get("unit_tests"):
            recommendations["keep_but_organize"].extend(
                test_files["unit_tests"])

        # 分析模拟文件
        mock_files = self.scan_results.get("mock_files", {})
        if mock_files.get("dummy_files"):
            recommendations["immediate_delete"].extend(
                mock_files["dummy_files"])
        if mock_files.get("mock_data"):
            recommendations["review_before_delete"].extend(
                mock_files["mock_data"])

        # 分析临时文件
        temp_files = self.scan_results.get("temp_files", {})
        recommendations["immediate_delete"].extend(
            temp_files.get("temp_files", []))
        recommendations["immediate_delete"].extend(
            temp_files.get("debug_files", []))
        recommendations["review_before_delete"].extend(
            temp_files.get("backup_files", []))

        # 分析硬编码数据
        hardcoded_data = self.scan_results.get("hardcoded_data", {})
        for category, matches in hardcoded_data.items():
            for file_path, line_num, content in matches:
                recommendations["convert_to_config"].append({
                    "file": file_path,
                    "line": line_num,
                    "content": content,
                    "type": "hardcoded_data"
                })

        self.cleanup_recommendations = recommendations

        # 输出建议
        for category, items in recommendations.items():
            if items:
                category_name = {
                    "immediate_delete": "立即删除",
                    "review_before_delete": "审查后删除",
                    "keep_but_organize": "保留并整理",
                    "convert_to_config": "转换为配置"
                }[category]

                self.print_step(f"{category_name}: {len(items)} 项")

        return recommendations

    def generate_cleanup_script(self, dry_run: bool = True):
        """生成清理脚本"""
        self.print_header("生成清理脚本")

        script_lines = [
            "#!/bin/bash",
            "# 测试代码和模拟数据清理脚本",
            f"# 生成时间: {datetime.now().isoformat()}",
            "",
            "set -e",
            "",
            "echo '🧹 开始清理测试代码和模拟数据'",
            ""
        ]

        # 添加立即删除的命令
        immediate_delete = self.cleanup_recommendations.get(
            "immediate_delete", [])
        if immediate_delete:
            script_lines.append("echo '🗑️ 删除临时文件和调试文件...'")
            for file_path in immediate_delete:
                rel_path = Path(file_path).relative_to(self.project_root)
                if dry_run:
                    script_lines.append(f"echo '  删除: {rel_path}'")
                    script_lines.append(f"# rm \"{rel_path}\"")
                else:
                    script_lines.append(
                        f"rm \"{rel_path}\" 2>/dev/null || true")
            script_lines.append("")

        # 添加需要审查的文件列表
        review_files = self.cleanup_recommendations.get(
            "review_before_delete", [])
        if review_files:
            script_lines.append("echo '⚠️ 以下文件需要手动审查后决定是否删除:'")
            for file_path in review_files:
                rel_path = Path(file_path).relative_to(self.project_root)
                script_lines.append(f"echo '  审查: {rel_path}'")
            script_lines.append("")

        # 添加需要整理的文件
        organize_files = self.cleanup_recommendations.get(
            "keep_but_organize", [])
        if organize_files:
            script_lines.append("echo '📁 以下文件建议重新组织:'")
            script_lines.append("mkdir -p tests/unit tests/integration")
            for file_path in organize_files:
                rel_path = Path(file_path).relative_to(self.project_root)
                script_lines.append(f"echo '  整理: {rel_path}'")
            script_lines.append("")

        script_lines.append("echo '✅ 清理完成'")

        # 保存脚本
        script_file = self.project_root / "scripts" / "cleanup_test_code.sh"
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(script_lines))

        self.print_step(f"清理脚本已生成: {script_file}")

        if dry_run:
            self.print_step("当前为预览模式，实际删除命令已注释", "⚠️")
            self.print_step("要执行实际清理，请使用: --execute 参数")

        return script_file

    def run_full_analysis(self, dry_run: bool = True):
        """运行完整分析"""
        print("🧹 测试代码和模拟代码清理分析")
        print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 执行各项扫描
        self.scan_test_files()
        self.scan_mock_files()
        self.scan_hardcoded_test_data()
        self.scan_temp_and_debug_files()

        # 生成建议
        self.generate_cleanup_recommendations()

        # 生成脚本
        script_file = self.generate_cleanup_script(dry_run)

        # 生成报告
        self.generate_analysis_report()

        return self.scan_results, self.cleanup_recommendations

    def generate_analysis_report(self):
        """生成分析报告"""
        self.print_header("清理分析报告")

        # 统计
        total_test_files = sum(
            len(files) for files in self.scan_results.get(
                "test_files", {}).values())
        total_mock_files = sum(
            len(files) for files in self.scan_results.get(
                "mock_files", {}).values())
        total_temp_files = sum(
            len(files) for files in self.scan_results.get(
                "temp_files", {}).values())
        total_hardcoded = sum(
            len(matches) for matches in self.scan_results.get(
                "hardcoded_data", {}).values())

        print(f"📊 扫描结果统计:")
        print(f"  - 测试文件: {total_test_files} 个")
        print(f"  - 模拟文件: {total_mock_files} 个")
        print(f"  - 临时文件: {total_temp_files} 个")
        print(f"  - 硬编码数据: {total_hardcoded} 处")

        # 清理建议统计
        immediate_count = len(
            self.cleanup_recommendations.get(
                "immediate_delete", []))
        review_count = len(
            self.cleanup_recommendations.get(
                "review_before_delete", []))
        organize_count = len(
            self.cleanup_recommendations.get(
                "keep_but_organize", []))
        config_count = len(
            self.cleanup_recommendations.get(
                "convert_to_config", []))

        print(f"\n📋 清理建议:")
        print(f"  - 立即删除: {immediate_count} 项")
        print(f"  - 审查后删除: {review_count} 项")
        print(f"  - 保留并整理: {organize_count} 项")
        print(f"  - 转换为配置: {config_count} 项")

        # 保存报告
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "scan_results": self.scan_results,
            "cleanup_recommendations": self.cleanup_recommendations,
            "summary": {
                "total_test_files": total_test_files,
                "total_mock_files": total_mock_files,
                "total_temp_files": total_temp_files,
                "total_hardcoded": total_hardcoded,
                "immediate_delete": immediate_count,
                "review_delete": review_count,
                "organize": organize_count,
                "convert_config": config_count
            }
        }

        report_file = self.project_root / "reports" / "test_code_cleanup_analysis.json"
        report_file.parent.mkdir(exist_ok=True)

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)

        print(f"\n📄 详细报告已保存: {report_file}")


def main():
    """主函数"""

    dry_run = True
    if len(sys.argv) > 1 and sys.argv[1] == "--execute":
        dry_run = False
        print("⚠️ 警告：将执行实际删除操作！")
        confirm = input("确认继续吗？(y/N): ")
        if confirm.lower() != 'y':
            print("操作已取消")
            return

    analyzer = TestCodeAnalyzer()
    scan_results, recommendations = analyzer.run_full_analysis(dry_run)

    print(f"\n🏁 清理分析完成")
    print("📋 下一步建议:")
    print("  1. 审查生成的清理脚本")
    print("  2. 手动检查需要审查的文件")
    print("  3. 确认后执行: python scripts/cleanup_test_code.py --execute")
    print("  4. 运行功能测试确认系统正常")


if __name__ == "__main__":
    main()
