# YS-API V3.0 Windows 部署脚本
param(
    [string]$Environment = "staging"
)

Write-Host "🚀 Starting YS-API V3.0 Deployment to $Environment..." -ForegroundColor Green

# 配置
$AppDir = "C:\ysapi"
$BackupDir = "C:\ysapi-backups"
$ServiceName = "YSAPI"

# 创建备份
Write-Host "📦 Creating backup..." -ForegroundColor Yellow
$BackupPath = "$BackupDir\ysapi-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
if (Test-Path $AppDir) {
    New-Item -ItemType Directory -Force -Path $BackupDir
    Copy-Item -Path $AppDir -Destination $BackupPath -Recurse
}

# 停止服务
Write-Host "⏹️ Stopping service..." -ForegroundColor Yellow
Stop-Service -Name $ServiceName -ErrorAction SilentlyContinue

# 部署文件
Write-Host "📋 Deploying files..." -ForegroundColor Yellow
if (Test-Path $AppDir) {
    Remove-Item -Path $AppDir -Recurse -Force
}
Copy-Item -Path . -Destination $AppDir -Recurse -Exclude ".git","logs","*.log"

# 启动服务
Write-Host "🔄 Starting service..." -ForegroundColor Yellow
Start-Service -Name $ServiceName

# 健康检查
Write-Host "🏥 Health check..." -ForegroundColor Yellow
Start-Sleep -Seconds 30
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/health" -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Deployment successful!" -ForegroundColor Green
    } else {
        throw "Health check failed"
    }
} catch {
    Write-Host "❌ Deployment failed!" -ForegroundColor Red
    # 回滚
    Stop-Service -Name $ServiceName -ErrorAction SilentlyContinue
    Remove-Item -Path $AppDir -Recurse -Force
    Copy-Item -Path $BackupPath -Destination $AppDir -Recurse
    Start-Service -Name $ServiceName
    exit 1
}

Write-Host "🎉 Deployment completed!" -ForegroundColor Green
