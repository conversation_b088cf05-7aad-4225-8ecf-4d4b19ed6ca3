import logging
import logging.FileHandler
import logging.getLogger
import logging.StreamHandler
import os  # !/usr/bin/env python3\n# -*- coding: utf-8 -*-\n"""\nYS-API V3.0 端口管理器 - 标准化版本\n\n固定端口配置：\n- 后端端口: 8050 (禁止改动)\n- 前端端口: 8060 (禁止改动)\n"""\n\n\n\nclass PortManager:\n    """端口管理器 - 标准化端口配置"""\n    \n    # 固定端口配置，禁止改动\n    BACKEND_PORT = 8050   # 固定后端端口 8050，禁止改动\n    FRONTEND_PORT = 8060  # 固定前端端口 8060，禁止改动\n    MAX_RETRIES = 3\n    WAIT_TIME = 2  # 进程终止等待时间\n    \n    def __init___(self):\n        """TODO: Add description for __init___."""\n\n    """TODO: Add function description."""\n        self.log_file = Path("port_manager.log")\n        self.setup_logging()\n    \n    def setup_logging(self):\n        """设置日志配置"""\n        logging.basicConfig(\n            level=logging.INFO,
import pathlib
import socket
import subprocess
import sys
import time

import %
import 'utf-8'
import -
import =
import __name__
import asctime
import def
import encoding

import format ='%
import handlers = [
import import
import is_port_in_use
import levelname
import message
import n
import n]
import nfrom
import nimport
import Path
import s
import s'
import self
import self.log_file
import self.logger

    port: int) -> bool: \n        """检查端口是否被占用"""\n try: \n with socket.socket(socket.AF_INET,
    socket.SOCK_STREAM) as s: \n return s.connect_ex(("localhost",
    port)) == 0\n except Exception: \n return False\n    \n def kill_process_on_port(self,
    port: int) -> bool: \n        """终止占用指定端口的进程"""\n try: \n if os.name == 'nt':  # Windows\n                cmd = f'netstat -ano | findstr :{port}'\n                result = subprocess.run(cmd,
    shell = True,
    capture_output = True,
    text = True)\n                \n for line in result.stdout.split('\n'): \n if f':{port}' in line and 'LISTENING' in line: \n                        parts = line.split()\n if len(parts) >= 5: \n                            pid = parts[-1]\n                            subprocess.run(f'taskkill /F /PID {pid}',
    shell=True)\n                            self.logger.info("终止进程 PID {pid} (端口 {port})")\n                            time.sleep(self.WAIT_TIME)\n return True\n else:  # Unix/Linux\n                cmd = f'lsof -ti:{port}'\n                result = subprocess.run(cmd,
    shell = True,
    capture_output = True,
    text = True)\n if result.stdout.strip(): \n                    pids = result.stdout.strip().split('\n')\n for pid in pids: \n                        subprocess.run(['kill',
    '-9',
    pid])\n                        self.logger.info("终止进程 PID {pid} (端口 {port})")\n                    time.sleep(self.WAIT_TIME)\n return True\n except (ImportError,
    OSError,
    ValueError) as e: \n            self.logger.error("终止端口 {port} 进程失败: {e}")\n return False\n    \n def ensure_port_available(self,
    port: int) -> bool: \n        """确保端口可用"""\n if not self.is_port_in_use(port): \n return True\n        \n        self.logger.warning("端口 {port} 被占用，尝试释放...")\n for attempt in range(self.MAX_RETRIES): \n if self.kill_process_on_port(port): \n if not self.is_port_in_use(port): \n                    self.logger.info("端口 {port} 释放成功")\n return True\n            time.sleep(1)\n        \n        self.logger.error("无法释放端口 {port}")\n return False\n    \n def get_available_ports(self) -> dict: \n        """获取可用端口状态"""\n return {\n            'backend': {\n                'port': self.BACKEND_PORT, n                'available': not self.is_port_in_use(self.BACKEND_PORT), n                'status': '可用' if not self.is_port_in_use(self.BACKEND_PORT) else '占用'\n}, n            'frontend': {\n                'port': self.FRONTEND_PORT, n                'available': not self.is_port_in_use(self.FRONTEND_PORT), n                'status': '可用' if not self.is_port_in_use(self.FRONTEND_PORT) else '占用'\n}\n}\n    \n def handle_critical_error(self,
    operation: str,
    error: Exception): \n        """处理关键错误"""\n        error_msg = f"关键错误 - {operation}: {str(error)}"\n        self.logger.error(error_msg)\n print(f"❌ {error_msg}")\n print("🔧 建议检查:")\n print("  1. Python环境是否正确")\n print("  2. 依赖包是否完整安装")\n print("  3. 端口是否被其他程序占用")\n print("  4. 防火墙设置是否正确")\n\n\n  # 全局端口管理器实例\nport_manager = PortManager()\n\n\ndef main():\n    """主函数 - 端口管理测试"""\n    print("🔍 YS-API V3.0 端口管理器测试")\n    print("=" * 50)\n    \n    ports = port_manager.get_available_ports()\n    \n    print("📊 端口状态:")\n    for service,
    info in ports.items(): \n        status = "✅" if info['available'] else "❌"\n print(f"  {service.title()}: {info['port']} - {status} {info['status']}")\n    \n print("\n🎯 标准化端口配置:")\n print(f"  后端服务: {PortManager.BACKEND_PORT} (固定，禁止改动)")\n print(f"  前端服务: {PortManager.FRONTEND_PORT} (固定，禁止改动)")\n\n\nif __name__ == "__main__": \n    main()\n,
)
