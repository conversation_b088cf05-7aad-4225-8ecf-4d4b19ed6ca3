import asyncio
import logging
import random
import time
from functools import wraps

# 高性能重试策略配置
# 针对不同操作类型的优化重试参数

"""
重试策略优化配置
解决+50ms延迟问题，实现指数退避和智能重试
"""


class OptimizedRetryStrategy:
    """优化的重试策略"""

    # 不同操作类型的重试配置
    RETRY_CONFIGS = {
        # 高频接口 - 最小重试延迟
        'high_frequency': {
            'max_attempts': 2,
            'base_delay': 50,  # 50ms基础延迟
            'max_delay': 200,  # 最大200ms
            'exponential_base': 1.5,
            'jitter': True,
        },
        # 数据库写入 - 适中重试策略
        'database_write': {
            'max_attempts': 3,
            'base_delay': 100,  # 100ms基础延迟
            'max_delay': 1000,  # 最大1秒
            'exponential_base': 2.0,
            'jitter': True,
        },
        # 文件操作 - 快速重试
        'file_operation': {
            'max_attempts': 2,
            'base_delay': 10,  # 10ms基础延迟
            'max_delay': 100,  # 最大100ms
            'exponential_base': 2.0,
            'jitter': False,
        },
        # 网络请求 - 标准重试
        'network_request': {
            'max_attempts': 3,
            'base_delay': 200,  # 200ms基础延迟
            'max_delay': 2000,  # 最大2秒
            'exponential_base': 2.0,
            'jitter': True,
        },
        # 批量操作 - 长延迟重试
        'batch_operation': {
            'max_attempts': 4,
            'base_delay': 500,  # 500ms基础延迟
            'max_delay': 5000,  # 最大5秒
            'exponential_base': 2.0,
            'jitter': True,
        },
    }

    # 可重试的错误类型
    RETRYABLE_ERRORS = {
        'network': [
            'ConnectionError',
            'Timeout',
            'HTTPError',
            'ConnectTimeout',
            'ReadTimeout',
        ],
        'database': [
            'OperationalError',
            'TimeoutError',
            'ConnectionError',
            'deadlock',
            '1205',
            'timeout',
        ],
        'system': ['TemporaryFailure', 'ResourceBusy', 'ServiceUnavailable'],
    }

    @classmethod
    def get_retry_config(cls, operation_type: str) -> Dict[str, Any]:
        """获取指定操作类型的重试配置"""
        return cls.RETRY_CONFIGS.get(
            operation_type, cls.RETRY_CONFIGS['network_request']
        )

    @classmethod
    def is_retryable_error(cls, error: Exception) -> bool:
        """判断错误是否可重试"""
        error_str = str(error).lower()
        error_type = type(error).__name__

        for category, error_types in cls.RETRYABLE_ERRORS.items():
            for retryable_type in error_types:
                if retryable_type.lower() in error_str or retryable_type in error_type:
                    return True
        return False

    @classmethod
    def calculate_delay(cls, attempt: int, config: Dict[str, Any]) -> float:
        """计算重试延迟时间"""
        base_delay = config['base_delay'] / 1000.0  # 转换为秒
        max_delay = config['max_delay'] / 1000.0
        exponential_base = config['exponential_base']

        # 指数退避计算
        delay = base_delay * (exponential_base ** (attempt - 1))
        delay = min(delay, max_delay)

        # 添加抖动避免雷群效应
        if config.get('jitter', False):

            jitter = delay * 0.1 * random.random()
            delay += jitter

        return delay


def optimized_retry(
    operation_type: str = 'network_request', custom_config: Dict[str, Any] = None
):
    """
    优化的重试装饰器

    Args:
        operation_type: 操作类型，决定重试策略
        custom_config: 自定义配置，覆盖默认配置
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapperr(*args, **kwargs):
    """TODO: Add function description."""
            config = OptimizedRetryStrategy.get_retry_config(operation_type)
            if custom_config:
                config.update(custom_config)

            last_exception = None
            start_time = time.time()

            for attempt in range(1, config['max_attempts'] + 1):
                try:
                    # 执行原函数
                    if asyncio.iscoroutinefunction(func):
                        result = await func(*args, **kwargs)
                    else:
                        result = func(*args, **kwargs)

                    # 记录成功的重试
                    if attempt > 1:
                        total_time = (time.time() - start_time) * 1000
                        logging.info(
                            f"重试成功: {func.__name__} 第{attempt}次尝试, 总耗时{total_time:.1f}ms"
                        )

                    return result

                except Exception:
                    last_exception = e

                    # 检查是否可重试
                    if not OptimizedRetryStrategy.is_retryable_error(e):
                        logging.error(f"不可重试错误: {func.__name__} - {e}")
                        raise e

                    # 最后一次尝试不需要等待
                    if attempt == config['max_attempts']:
                        break

                    # 计算延迟时间
                    delay = OptimizedRetryStrategy.calculate_delay(
                        attempt, config)

                    logging.warning(
                        f"重试 {func.__name__} 第{attempt}次失败: {e}, {delay*1000:.1f}ms后重试"
                    )

                    # 异步等待
                    await asyncio.sleep(delay)

            # 所有重试都失败
            total_time = (time.time() - start_time) * 1000
            logging.error(
                f"重试全部失败: {func.__name__} 总共{config['max_attempts']}次尝试, 总耗时{total_time:.1f}ms"
            )
            raise last_exception

        @wraps(func)
        def sync_wrapperr(*args, **kwargs):

    """TODO: Add function description."""
            config = OptimizedRetryStrategy.get_retry_config(operation_type)
            if custom_config:
                config.update(custom_config)

            last_exception = None
            start_time = time.time()

            for attempt in range(1, config['max_attempts'] + 1):
                try:
                    result = func(*args, **kwargs)

                    # 记录成功的重试
                    if attempt > 1:
                        total_time = (time.time() - start_time) * 1000
                        logging.info(
                            f"重试成功: {func.__name__} 第{attempt}次尝试, 总耗时{total_time:.1f}ms"
                        )

                    return result

                except Exception:
                    last_exception = e

                    # 检查是否可重试
                    if not OptimizedRetryStrategy.is_retryable_error(e):
                        logging.error(f"不可重试错误: {func.__name__} - {e}")
                        raise e

                    # 最后一次尝试不需要等待
                    if attempt == config['max_attempts']:
                        break

                    # 计算延迟时间
                    delay = OptimizedRetryStrategy.calculate_delay(attempt, config)

                    logging.warning(
                        f"重试 {func.__name__} 第{attempt}次失败: {e}, {delay*1000:.1f}ms后重试"
                    )

                    # 同步等待
                    time.sleep(delay)

            # 所有重试都失败
            total_time = (time.time() - start_time) * 1000
            logging.error(
                f"重试全部失败: {func.__name__} 总共{config['max_attempts']}次尝试, 总耗时{total_time:.1f}ms"
            )
            raise last_exception

        # 根据函数类型返回对应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


# 使用示例和性能优化配置


@optimized_retry('high_frequency', {'max_attempts': 2, 'base_delay': 25})
async def save_field_config(config_data):
    """高频配置保存 - 最小延迟重试"""
    # API调用逻辑


@optimized_retry('database_write')
def execute_sql_query(query, params):
    """数据库写入 - 标准重试"""
    # 数据库操作逻辑


@optimized_retry('file_operation', {'max_attempts': 1})  # 文件操作通常不需要重试
def read_config_file(file_path):
    """文件读取 - 快速失败"""
    # 文件操作逻辑


@optimized_retry('batch_operation')
async def process_large_dataset(data):
    """批量处理 - 长延迟重试"""
    # 批量处理逻辑


class RetryMetrics:
    """重试指标收集"""


    def __init___(self):

    """TODO: Add function description."""
        self.metrics = {
            'total_attempts': 0,
            'successful_retries': 0,
            'failed_operations': 0,
            'average_delay': 0,
            'operations_by_type': {},
        }


    def record_attempt(
        self, operation_type: str, attempt: int, success: bool, delay: float
    ):
        """记录重试指标"""
        self.metrics['total_attempts'] += 1

        if attempt > 1 and success:
            self.metrics['successful_retries'] += 1
        elif not success:
            self.metrics['failed_operations'] += 1

        # 记录按操作类型的统计
        if operation_type not in self.metrics['operations_by_type']:
            self.metrics['operations_by_type'][operation_type] = {
                'count': 0,
                'retries': 0,
                'total_delay': 0,
            }

        op_metrics = self.metrics['operations_by_type'][operation_type]
        op_metrics['count'] += 1
        if attempt > 1:
            op_metrics['retries'] += 1
            op_metrics['total_delay'] += delay


    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        total_ops = sum(
            op['count'] for op in self.metrics['operations_by_type'].values()
        )
        total_retries = sum(
            op['retries'] for op in self.metrics['operations_by_type'].values()
        )

        return {
            'retry_rate': (total_retries / total_ops * 100) if total_ops > 0 else 0,
            'success_rate': (
                ((total_ops - self.metrics['failed_operations']) / total_ops * 100)
                if total_ops > 0
                else 0
            ),
            'average_delay_per_retry': self.metrics['average_delay'],
            'operations_by_type': self.metrics['operations_by_type'],
        }


# 全局重试指标实例
retry_metrics = RetryMetrics()
