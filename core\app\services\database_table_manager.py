import asyncio
import json
import os
import shutil
import sqlite3
import time
from contextlib import asynccontextmanager
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

import structlog

from ..core.config import get_settings

"""
YS-API V3.0 数据库表管理器 - 优化版
提供完整的数据库表管理功能
包括：表状态检查、表创建、健康监控、配置管理
"""


# 配置结构化日志
logger = structlog.get_logger(__name__)


@dataclass
class TableInfo:
    """表信息数据类"""

    name: str
    exists: bool = False
    fields_count: int = 0
    rows_count: int = 0
    status: str = "unknown"
    last_checked: Optional[str] = None
    error_message: Optional[str] = None


@dataclass
class DatabaseHealth:
    """数据库健康状态数据类"""

    is_healthy: bool = False
    connection_successful: bool = False
    database_exists: bool = False
    database_accessible: bool = False
    error_messages: List[str] = None
    check_time: Optional[str] = None

    def __post_init___(self):
    """TODO: Add function description."""
        if self.error_messages is None:
            self.error_messages = []


class DatabaseTableManager:
    """数据库表管理器 - 优化版本

    提供完整的数据库表管理功能：
    - 表状态检查和监控
    - 表创建和删除
    - 数据库健康检查
    - 配置文件管理
    - 连接池管理
    """

    def __init__(self, db_path: Optional[str] = None):
        """初始化数据库表管理器

        Args:
            db_path: 数据库文件路径，如果不提供则使用默认配置
        """
        self._connection_pool = {}
        self._last_health_check = None
        self._health_cache_duration = 30  # 健康检查缓存30秒

        try:
            # 尝试加载设置
            self.settings = self._load_settings()

            # 设置配置目录
            self.config_dir = self._determine_config_dir()

            # 设置数据库路径
            self.db_path = db_path or self._determine_db_path()

            # 验证配置
            self._validate_configuration()

            # 初始化数据库
            self._ensure_database_exists()

            logger.info(
                "数据库表管理器初始化成功",
                db_path=self.db_path,
                config_dir=str(self.config_dir),
            )

        except Exception:
            logger.error(f"数据库表管理器初始化失败: {str(e)}", error=str(e))
            # 设置默认配置以防止完全失败
            self._setup_fallback_configuration()

    def _load_settings(self):
        """加载应用设置"""
        try:

            return get_settings()
        except ImportError:
            logger.warning("无法导入设置模块，使用默认配置")
            return None
        except Exception:
            logger.warning(f"加载设置失败: {str(e)}")
            return None

    def _determine_config_dir(self) -> Path:
        """确定配置目录路径"""
        try:
            # 从当前文件位置计算v3根目录
            current_file = Path(__file__)
            v3_root = current_file.parent.parent.parent.parent
            config_dir = v3_root / "config"

            # 验证目录存在
            if config_dir.exists():
                return config_dir
            else:
                # 创建配置目录
                config_dir.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建配置目录: {config_dir}")
                return config_dir

        except Exception:
            logger.warning(f"无法确定配置目录: {str(e)}")
            # 返回默认路径
            return Path("config")

    def _determine_db_path(self) -> str:
        """确定数据库文件路径"""
        try:
            if self.settings and hasattr(self.settings, 'DATABASE_URL'):
                return self.settings.DATABASE_URL

            # 使用默认数据库路径
            db_dir = self.config_dir.parent / "backend"
            db_path = db_dir / "ysapi.db"

            return str(db_path)

        except Exception:
            logger.warning(f"无法确定数据库路径: {str(e)}")
            return "ysapi.db"

    def _validate_configuration(self):
        """验证配置有效性"""
        errors = []

        # 检查配置目录
        if not self.config_dir.exists():
            errors.append(f"配置目录不存在: {self.config_dir}")

        # 检查数据库路径
        db_dir = Path(self.db_path).parent
        if not db_dir.exists():
            try:
                db_dir.mkdir(parents=True, exist_ok=True)
                logger.info(f"创建数据库目录: {db_dir}")
            except Exception:
                errors.append(f"无法创建数据库目录: {db_dir}, 错误: {str(e)}")

        if errors:
            raise ValueError(f"配置验证失败: {'; '.join(errors)}")

    def _setup_fallback_configuration(self):
        """设置备用配置"""
        self.settings = None
        self.config_dir = Path("config")
        self.db_path = "ysapi.db"
        logger.warning("使用备用配置")

    def _ensure_database_exists(self):
        """确保数据库文件存在"""
        try:
            db_path = Path(self.db_path)
            if not db_path.exists():
                # 创建数据库文件
                db_path.parent.mkdir(parents=True, exist_ok=True)

                # 创建空数据库
                with sqlite3.connect(str(db_path)) as conn:
                    conn.execute("SELECT 1").fetchone()

                logger.info(f"创建数据库文件: {db_path}")
        except Exception:
            logger.error(f"创建数据库文件失败: {str(e)}")

    @asynccontextmanager
    async def get_connection(self):
        """获取数据库连接（异步上下文管理器）"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 启用字典式访问
            yield conn
        except Exception:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()

    async def get_database_tables_status(self) -> Dict[str, Any]:
        """获取数据库表状态信息

        Returns:
            包含所有表状态信息的字典
        """
        try:
            logger.info("获取数据库表状态")
            start_time = time.time()

            # 获取所有表信息
            tables_info = await self._get_all_tables_info()

            # 计算统计信息
            total_tables = len(tables_info)
            existing_tables = sum(
    1 for info in tables_info.values() if info.exists)
            total_records = sum(
                info.rows_count for info in tables_info.values() if info.exists
            )

            # 检查配置文件
            config_status = await self._check_config_files_status()

            execution_time = round(time.time() - start_time, 3)

            result = {
                "success": True,
                "message": "数据库表状态查询成功",
                "tables": {
                    name: {
                        "exists": info.exists,
                        "rows_count": info.rows_count,
                        "fields_count": info.fields_count,
                        "status": info.status,
                        "last_checked": info.last_checked,
                        "error_message": info.error_message,
                    }
                    for name, info in tables_info.items()
                },
                "summary": {
                    "total_tables": total_tables,
                    "existing_tables": existing_tables,
                    "total_records": total_records,
                    "execution_time_seconds": execution_time,
                },
                "config_status": config_status,
                "timestamp": datetime.now().isoformat(),
            }

            logger.info(
                "数据库表状态查询完成",
                total_tables=total_tables,
                existing_tables=existing_tables,
                execution_time=execution_time,
            )

            return result

        except Exception:
            logger.error(f"获取数据库表状态失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取数据库表状态失败: {str(e)}",
                "tables": {},
                "summary": {
                    "total_tables": 0,
                    "existing_tables": 0,
                    "total_records": 0,
                    "execution_time_seconds": 0,
                },
                "config_status": {},
                "timestamp": datetime.now().isoformat(),
                "error_detail": str(e),
            }

    async def _get_all_tables_info(self) -> Dict[str, TableInfo]:
        """获取所有表的信息"""
        tables_info = {}

        try:
            async with self.get_connection() as conn:
                # 获取所有表名
                cursor = conn.execute(
                    """
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                """
                )

                existing_tables = [row[0] for row in cursor.fetchall()]

                # 获取预期的表名（从配置或预定义列表）
                expected_tables = await self._get_expected_tables()

                # 合并所有表名
                all_tables = set(existing_tables + expected_tables)

                # 为每个表获取详细信息
                for table_name in all_tables:
                    info = await self._get_single_table_info(table_name, conn)
                    tables_info[table_name] = info

        except Exception:
            logger.error(f"获取表信息失败: {str(e)}")
            # 返回预期表的基本信息
            expected_tables = await self._get_expected_tables()
            for table_name in expected_tables:
                tables_info[table_name] = TableInfo(
                    name=table_name, exists=False, status="error", error_message=str(e)
                )

        return tables_info

    async def _get_single_table_info(self, table_name: str, conn) -> TableInfo:
        """获取单个表的详细信息"""
        try:
            # 检查表是否存在
            cursor = conn.execute(
                """
                SELECT name FROM sqlite_master
                WHERE type='table' AND name=?
            """,
                (table_name,),
            )

            exists = cursor.fetchone() is not None

            if not exists:
                return TableInfo(
    name=table_name,
    exists=False,
     status="not_exists")

            # 获取行数
            cursor = conn.execute(f"SELECT COUNT(*) FROM `{table_name}`")
            rows_count = cursor.fetchone()[0]

            # 获取字段数
            cursor = conn.execute(f"PRAGMA table_info(`{table_name}`)")
            fields_count = len(cursor.fetchall())

            # 确定状态
            status = "ready" if rows_count >= 0 else "empty"

            return TableInfo(
                name=table_name,
                exists=True,
                fields_count=fields_count,
                rows_count=rows_count,
                status=status,
                last_checked=datetime.now().isoformat(),
            )

        except Exception:
            logger.error(f"获取表 {table_name} 信息失败: {str(e)}")
            return TableInfo(
                name=table_name, exists=False, status="error", error_message=str(e)
            )

    async def _get_expected_tables(self) -> List[str]:
        """获取预期的表名列表"""
        try:
            # 从配置文件获取模块列表
            modules_config_path = self.config_dir / "modules.json"
            if modules_config_path.exists():
                with open(modules_config_path, 'r', encoding='utf-8') as f:
                    modules_config = json.load(f)
                    return list(modules_config.get('modules', {}).keys())

            # 备用：预定义的表名列表
            return [
                "sales_order",
                "purchase_order",
                "material_master",
                "inventory_report",
                "production_order",
                "outsourcing_order",
                "outsourcing_application",
                "material_receipt",
                "sales_delivery",
                "purchase_receipt",
                "material_request",
            ]

        except Exception:
            logger.warning(f"获取预期表名失败: {str(e)}")
            return ["sales_order", "purchase_order", "material_master"]

    async def _check_config_files_status(self) -> Dict[str, Any]:
        """检查配置文件状态"""
        try:
            config_status = {}

            # 检查主要配置文件
            config_files = [
                "modules.json",
                "monitoring_config.json",
                "auto_sync_config.json",
            ]

            for file_name in config_files:
                file_path = self.config_dir / file_name
                config_status[file_name] = {
                    "exists": file_path.exists(),
                    "size": file_path.stat().st_size if file_path.exists() else 0,
                    "last_modified": (
                        datetime.fromtimestamp(
    file_path.stat().st_mtime).isoformat()
                        if file_path.exists()
                        else None
                    ),
                }

            return config_status

        except Exception:
            logger.error(f"检查配置文件状态失败: {str(e)}")
            return {}

    async def check_table_exists(self, table_name: str) -> bool:
        """检查表是否存在

        Args:
            table_name: 表名

        Returns:
            bool: 表是否存在
        """
        try:
            logger.debug(f"检查表存在性: {table_name}")

            async with self.get_connection() as conn:
                cursor = conn.execute(
                    """
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name=? COLLATE NOCASE
                """,
                    (table_name,),
                )

                exists = cursor.fetchone() is not None

                logger.debug(f"表存在性检查完成: {table_name}", exists=exists)

                return exists

        except Exception:
            logger.error(f"检查表存在性失败: {table_name}, 错误: {str(e)}")
            return False

    async def get_table_info(self, table_name: str) -> Optional[Dict]:
        """获取表信息

        Args:
            table_name: 表名

        Returns:
            Dict: 表信息字典，如果失败则返回None
        """
        try:
            logger.debug(f"获取表信息: {table_name}")

            async with self.get_connection() as conn:
                table_info = await self._get_single_table_info(table_name, conn)

                # 转换为字典格式
                result = {
                    "table_name": table_info.name,
                    "exists": table_info.exists,
                    "fields_count": table_info.fields_count,
                    "rows_count": table_info.rows_count,
                    "status": table_info.status,
                    "last_checked": table_info.last_checked,
                    "error_message": table_info.error_message,
                }

                # 如果表存在，获取额外信息
                if table_info.exists:
                    # 获取字段详情
                    cursor = conn.execute(f"PRAGMA table_info(`{table_name}`)")
                    fields = [
                        {
                            "name": row[1],
                            "type": row[2],
                            "not_null": bool(row[3]),
                            "default_value": row[4],
                            "primary_key": bool(row[5]),
                        }
                        for row in cursor.fetchall()
                    ]
                    result["fields"] = fields

                    # 获取索引信息
                    cursor = conn.execute(f"PRAGMA index_list(`{table_name}`)")
                    indexes = [
                        {"name": row[1], "unique": bool(row[2])}
                        for row in cursor.fetchall()
                    ]
                    result["indexes"] = indexes

                logger.debug(
                    f"表信息获取完成: {table_name}",
                    exists=table_info.exists,
                    fields_count=table_info.fields_count,
                )

                return result

        except Exception:
            logger.error(f"获取表信息失败: {table_name}, 错误: {str(e)}")
            return {
                "table_name": table_name,
                "exists": False,
                "status": "error",
                "error_message": str(e),
                "last_checked": datetime.now().isoformat(),
            }

    async def create_table_from_config(
        self, module_name: str, drop_if_exists: bool = False
    ) -> Dict:
        """从字段配置创建数据库表

        Args:
            module_name: 模块名称
            drop_if_exists: 如果表存在是否删除重建

        Returns:
            Dict: 创建结果
        """
        try:
            logger.info(f"开始创建表: {module_name}")
            start_time = time.time()

            # 加载字段配置
            config_data = await self._load_field_config(module_name)
            if not config_data:
                return {
                    "success": False,
                    "message": f"找不到模块 {module_name} 的字段配置",
                    "module_name": module_name,
                }

            # 检查表是否已存在
            table_exists = await self.check_table_exists(module_name)

            if table_exists:
                if drop_if_exists:
                    drop_result = await self.drop_table_if_exists(module_name)
                    if not drop_result.get("success", False):
                        return {
                            "success": False,
                            "message": f"删除现有表失败: {drop_result.get('message', '未知错误')}",
                            "module_name": module_name,
                        }
                else:
                    return {
                        "success": False,
                        "message": f"表 {module_name} 已存在，请设置 drop_if_exists=True 来重建",
                        "module_name": module_name,
                        "table_exists": True,
                    }

            # 生成CREATE TABLE语句
            create_sql = await self._generate_create_table_sql(module_name, config_data)

            # 执行表创建
            async with self.get_connection() as conn:
                conn.execute(create_sql)
                conn.commit()

                # 创建索引
                await self._create_table_indexes(conn, module_name, config_data)

                # 验证表创建成功
                verification = await self._verify_table_creation(
                    conn, module_name, config_data
                )

            execution_time = round(time.time() - start_time, 3)

            result = {
                "success": True,
                "message": f"表创建成功: {module_name}",
                "table_name": module_name,
                "module_name": module_name,
                "total_fields": verification["total_fields"],
                "chinese_name_fields": verification["chinese_name_fields"],
                "api_name_fields": verification["api_name_fields"],
                "execution_time_seconds": execution_time,
                "created_at": datetime.now().isoformat(),
                "verification": verification,
            }

            logger.info(
                f"表创建成功: {module_name}",
                total_fields=verification["total_fields"],
                execution_time=execution_time,
            )

            return result

        except Exception:
            logger.error(f"创建表失败: {module_name}, 错误: {str(e)}")
            return {
                "success": False,
                "message": f"创建表失败: {str(e)}",
                "module_name": module_name,
                "error_detail": str(e),
                "created_at": datetime.now().isoformat(),
            }

    async def _load_field_config(self, module_name: str) -> Optional[Dict]:
        """加载模块的字段配置"""
        try:
            # 尝试多种配置文件路径
            possible_paths = [
                self.config_dir / f"{module_name}.json",
                self.config_dir / "data" / f"{module_name}.json",
                self.config_dir / "baselines" / f"{module_name}.json",
            ]

            for config_path in possible_paths:
                if config_path.exists():
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                        logger.debug(f"加载配置文件: {config_path}")
                        return config_data

            logger.warning(f"找不到模块 {module_name} 的配置文件")
            return None

        except Exception:
            logger.error(f"加载字段配置失败: {module_name}, 错误: {str(e)}")
            return None

    async def _generate_create_table_sql(
        self, table_name: str, config_data: Dict
    ) -> str:
        """生成CREATE TABLE SQL语句"""
        fields = config_data.get("fields", [])
        if not fields:
            raise ValueError(f"配置数据中没有找到字段定义")

        # 生成字段定义
        field_definitions = []

        # 添加主键ID字段
        field_definitions.append("id INTEGER PRIMARY KEY AUTOINCREMENT")

        for field in fields:
            field_name = field.get("api_name") or field.get(
                "original_name", "")
            field_type = self._convert_to_sqlite_type(
                field.get("data_type", "TEXT"))

            if not field_name:
                continue

            # 构建字段定义
            definition = f"`{field_name}` {field_type}"

            # 添加NOT NULL约束（可选）
            if field.get("required", False):
                definition += " NOT NULL"

            # 添加默认值（可选）
            default_value = field.get("default_value")
            if default_value is not None:
                if field_type in ["TEXT", "VARCHAR"]:
                    definition += f" DEFAULT '{default_value}'"
                else:
                    definition += f" DEFAULT {default_value}"

            field_definitions.append(definition)

        # 添加元数据字段
        field_definitions.extend(
            [
                "created_at DATETIME DEFAULT CURRENT_TIMESTAMP",
                "updated_at DATETIME DEFAULT CURRENT_TIMESTAMP",
                "data_source VARCHAR(50) DEFAULT 'api'",
            ]
        )

        # 生成完整的CREATE TABLE语句
        create_sql = f"""
        CREATE TABLE `{table_name}` (
            {',\n            '.join(field_definitions)}
        )
        """

        return create_sql

    def _convert_to_sqlite_type(self, data_type: str) -> str:
        """将字段数据类型转换为SQLite类型"""
        type_mapping = {
            "STRING": "TEXT",
            "VARCHAR": "VARCHAR(255)",
            "CHAR": "CHAR(50)",
            "TEXT": "TEXT",
            "INTEGER": "INTEGER",
            "INT": "INTEGER",
            "BIGINT": "BIGINT",
            "DECIMAL": "DECIMAL(18,2)",
            "NUMERIC": "DECIMAL(18,2)",
            "FLOAT": "REAL",
            "DOUBLE": "REAL",
            "BOOLEAN": "BOOLEAN",
            "BOOL": "BOOLEAN",
            "DATE": "DATE",
            "DATETIME": "DATETIME",
            "DATETIME2": "DATETIME",
            "TIMESTAMP": "TIMESTAMP",
            "TIME": "TIME",
            "JSON": "TEXT",
            "BLOB": "BLOB",
        }

        return type_mapping.get(data_type.upper(), "TEXT")

    async def _create_table_indexes(
    self,
    conn,
    table_name: str,
     config_data: Dict):
        """为表创建索引"""
        try:
            fields = config_data.get("fields", [])

            for field in fields:
                field_name = field.get("api_name") or field.get(
                    "original_name", "")
                if not field_name:
                    continue

                # 为重要字段创建索引
                importance = field.get("business_importance", "").lower()
                if importance in ["critical", "high"]:
                    index_name = f"idx_{table_name}_{field_name}"
                    index_sql = f"CREATE INDEX IF NOT EXISTS `{index_name}` ON `{table_name}` (`{field_name}`)"
                    conn.execute(index_sql)

            # 为时间戳字段创建索引
            timestamp_indexes = [
                f"CREATE INDEX IF NOT EXISTS `idx_{table_name}_created_at` ON `{table_name}` (`created_at`)",
                f"CREATE INDEX IF NOT EXISTS `idx_{table_name}_updated_at` ON `{table_name}` (`updated_at`)",
            ]

            for index_sql in timestamp_indexes:
                conn.execute(index_sql)

            conn.commit()

        except Exception:
            logger.warning(f"创建索引失败: {table_name}, 错误: {str(e)}")

    async def _verify_table_creation(
        self, conn, table_name: str, config_data: Dict
    ) -> Dict:
        """验证表创建结果"""
        try:
            # 检查表是否存在
            cursor = conn.execute(
                """
                SELECT name FROM sqlite_master
                WHERE type='table' AND name=?
            """,
                (table_name,),
            )

            if not cursor.fetchone():
                raise ValueError(f"表 {table_name} 创建后验证失败")

            # 获取字段信息
            cursor = conn.execute(f"PRAGMA table_info(`{table_name}`)")
            created_fields = cursor.fetchall()

            # 统计字段
            config_fields = config_data.get("fields", [])
            total_config_fields = len(config_fields)
            chinese_name_fields = sum(
    1 for f in config_fields if f.get("chinese_name"))
            api_name_fields = sum(
    1 for f in config_fields if f.get("api_name"))

            return {
                "total_fields": len(created_fields),
                "config_fields": total_config_fields,
                "chinese_name_fields": chinese_name_fields,
                "api_name_fields": api_name_fields,
                "verification_passed": True,
                # 字段名列表
                "created_fields": [field[1] for field in created_fields],
            }

        except Exception:
            logger.error(f"表创建验证失败: {table_name}, 错误: {str(e)}")
            return {
                "total_fields": 0,
                "config_fields": 0,
                "chinese_name_fields": 0,
                "api_name_fields": 0,
                "verification_passed": False,
                "error_message": str(e),
            }

    async def create_all_tables(self, drop_if_exists: bool = False) -> Dict:
        """创建所有模块的数据库表

        Args:
            drop_if_exists: 如果表存在是否删除重建

        Returns:
            Dict: 批量创建结果
        """
        try:
            logger.info("开始批量创建所有表")
            start_time = time.time()

            # 获取所有模块
            modules = await self._get_expected_tables()
            if not modules:
                return {
                    "success": False,
                    "message": "没有找到任何模块配置",
                    "total_modules": 0,
                    "success_count": 0,
                    "failed_count": 0,
                }

            results = {}
            success_count = 0
            failed_count = 0

            # 并行创建表（限制并发数）
            semaphore = asyncio.Semaphore(3)  # 最多3个并发

            async def create_single_tablee(module_name):
    """TODO: Add function description."""
                async with semaphore:
                    return await self.create_table_from_config(
                        module_name, drop_if_exists
                    )

            # 执行并行创建
            tasks = [create_single_table(module) for module in modules]
            table_results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            for i, result in enumerate(table_results):
                module_name = modules[i]

                if isinstance(result, Exception):
                    results[module_name] = {
                        "success": False,
                        "message": f"创建失败: {str(result)}",
                        "error_detail": str(result),
                    }
                    failed_count += 1
                else:
                    results[module_name] = result
                    if result.get("success", False):
                        success_count += 1
                    else:
                        failed_count += 1

            execution_time = round(time.time() - start_time, 3)
            total_modules = len(modules)

            # 生成总结报告
            summary_message = (
                f"批量创建完成: 成功 {success_count} 个，失败 {failed_count} 个"
            )

            result = {
                "success": failed_count == 0,
                "message": summary_message,
                "total_modules": total_modules,
                "success_count": success_count,
                "failed_count": failed_count,
                "execution_time_seconds": execution_time,
                "results": results,
                "created_at": datetime.now().isoformat(),
            }

            logger.info(
                "批量创建表完成",
                total_modules=total_modules,
                success_count=success_count,
                failed_count=failed_count,
                execution_time=execution_time,
            )

            return result

        except Exception:
            logger.error(f"批量创建表失败: {str(e)}")
            return {
                "success": False,
                "message": f"批量创建失败: {str(e)}",
                "total_modules": 0,
                "success_count": 0,
                "failed_count": 0,
                "error_detail": str(e),
                "created_at": datetime.now().isoformat(),
            }


    async def drop_table_if_exists(self, table_name: str) -> Dict:
        """删除表

        Args:
            table_name: 表名

        Returns:
            Dict: 删除结果
        """
        try:
            logger.info(f"删除表: {table_name}")

            # 检查表是否存在
            exists = await self.check_table_exists(table_name)

            if not exists:
                return {
                    "success": True,
                    "message": f"表不存在，无需删除: {table_name}",
                    "table_name": table_name,
                    "dropped": False,
                    "reason": "table_not_exists",
                }

            # 获取表信息（用于记录）
            table_info = await self.get_table_info(table_name)
            rows_count = table_info.get("rows_count", 0) if table_info else 0

            # 删除表
            async with self.get_connection() as conn:
                # 首先删除相关索引
                cursor = conn.execute(f"PRAGMA index_list(`{table_name}`)")
                indexes = [row[1] for row in cursor.fetchall()]

                for index_name in indexes:
                    if not index_name.startswith('sqlite_'):  # 跳过系统索引
                        try:
                            conn.execute(f"DROP INDEX IF EXISTS `{index_name}`")
                        except Exception:
                            logger.warning(
                                f"删除索引失败: {index_name}, 错误: {str(e)}"
                            )

                # 删除表
                conn.execute(f"DROP TABLE IF EXISTS `{table_name}`")
                conn.commit()

                # 验证删除成功
                verification = not await self.check_table_exists(table_name)

            result = {
                "success": verification,
                "message": (
                    f"表删除成功: {table_name}"
                    if verification
                    else f"表删除验证失败: {table_name}"
                ),
                "table_name": table_name,
                "dropped": verification,
                "rows_deleted": rows_count,
                "indexes_deleted": len(indexes),
                "deleted_at": datetime.now().isoformat(),
            }

            logger.info(
                f"表删除完成: {table_name}",
                dropped=verification,
                rows_deleted=rows_count,
            )

            return result

        except Exception:
            logger.error(f"删除表失败: {table_name}, 错误: {str(e)}")
            return {
                "success": False,
                "message": f"删除表失败: {str(e)}",
                "table_name": table_name,
                "dropped": False,
                "error_detail": str(e),
                "deleted_at": datetime.now().isoformat(),
            }


    async def drop_all_tables(self, confirm: bool = False) -> Dict:
        """删除所有表（危险操作）

        Args:
            confirm: 确认删除（必须为True才执行）

        Returns:
            Dict: 删除结果
        """
        if not confirm:
            return {
                "success": False,
                "message": "危险操作：删除所有表需要显式确认 (confirm=True)",
                "total_tables": 0,
                "deleted_count": 0,
            }

        try:
            logger.warning("开始删除所有表")
            start_time = time.time()

            # 获取所有存在的表
            async with self.get_connection() as conn:
                cursor = conn.execute(
                    """
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                """
                )
                existing_tables = [row[0] for row in cursor.fetchall()]

            if not existing_tables:
                return {
                    "success": True,
                    "message": "没有需要删除的表",
                    "total_tables": 0,
                    "deleted_count": 0,
                }

            # 逐个删除表
            results = {}
            deleted_count = 0

            for table_name in existing_tables:
                result = await self.drop_table_if_exists(table_name)
                results[table_name] = result

                if result.get("success", False) and result.get("dropped", False):
                    deleted_count += 1

            execution_time = round(time.time() - start_time, 3)

            result = {
                "success": deleted_count == len(existing_tables),
                "message": f"删除完成: 成功删除 {deleted_count}/{len(existing_tables)} 个表",
                "total_tables": len(existing_tables),
                "deleted_count": deleted_count,
                "execution_time_seconds": execution_time,
                "results": results,
                "deleted_at": datetime.now().isoformat(),
            }

            logger.warning(
                "删除所有表完成",
                total_tables=len(existing_tables),
                deleted_count=deleted_count,
            )

            return result

        except Exception:
            logger.error(f"删除所有表失败: {str(e)}")
            return {
                "success": False,
                "message": f"删除所有表失败: {str(e)}",
                "error_detail": str(e),
            }


    async def check_database_health(self) -> Dict:
        """检查数据库健康状态

        Returns:
            Dict: 数据库健康状态信息
        """
        try:
            logger.info("开始数据库健康检查")
            start_time = time.time()

            # 检查缓存
            if self._last_health_check:
                cache_age = time.time() - self._last_health_check.get("timestamp", 0)
                if cache_age < self._health_cache_duration:
                    logger.debug("使用缓存的健康检查结果")
                    return self._last_health_check.get("result", {})

            health = DatabaseHealth()
            error_messages = []

            # 1. 检查数据库文件存在性
            try:
                db_path = Path(self.db_path)
                health.database_exists = db_path.exists()

                if not health.database_exists:
                    error_messages.append(f"数据库文件不存在: {self.db_path}")
                else:
                    # 检查文件权限
                    health.database_accessible = os.access(
                        self.db_path, os.R_OK | os.W_OK
                    )
                    if not health.database_accessible:
                        error_messages.append("数据库文件无读写权限")

            except Exception:
                error_messages.append(f"检查数据库文件失败: {str(e)}")

            # 2. 检查数据库连接
            try:
                async with self.get_connection() as conn:
                    # 执行简单查询测试连接
                    cursor = conn.execute("SELECT 1")
                    cursor.fetchone()
                    health.connection_successful = True

                    # 检查数据库完整性
                    cursor = conn.execute("PRAGMA integrity_check")
                    integrity_result = cursor.fetchone()[0]

                    if integrity_result != "ok":
                        error_messages.append(
                            f"数据库完整性检查失败: {integrity_result}"
                        )

                    # 获取数据库统计信息
                    stats = await self._get_database_statistics(conn)

            except Exception:
                health.connection_successful = False
                error_messages.append(f"数据库连接失败: {str(e)}")
                stats = {}

            # 3. 检查关键表状态
            table_health = {}
            try:
                expected_tables = await self._get_expected_tables()
                for table_name in expected_tables[:5]:  # 检查前5个重要表
                    table_info = await self.get_table_info(table_name)
                    table_health[table_name] = {
                        "exists": (
                            table_info.get("exists", False) if table_info else False
                        ),
                        "status": (
                            table_info.get("status", "unknown")
                            if table_info
                            else "unknown"
                        ),
                    }
            except Exception:
                error_messages.append(f"检查表状态失败: {str(e)}")

            # 4. 检查配置文件
            config_health = {}
            try:
                config_health = await self._check_config_files_health()
            except Exception:
                error_messages.append(f"检查配置文件失败: {str(e)}")

            # 5. 检查磁盘空间
            disk_health = {}
            try:
                disk_health = await self._check_disk_space()
            except Exception:
                error_messages.append(f"检查磁盘空间失败: {str(e)}")

            # 设置健康状态
            health.error_messages = error_messages
            health.is_healthy = (
                health.database_exists
                and health.database_accessible
                and health.connection_successful
                and len(error_messages) == 0
            )
            health.check_time = datetime.now().isoformat()

            execution_time = round(time.time() - start_time, 3)

            # 构建结果
            result = {
                "database_exists": health.database_exists,
                "database_accessible": health.database_accessible,
                "connection_successful": health.connection_successful,
                "is_healthy": health.is_healthy,
                "error_messages": health.error_messages,
                "check_time": health.check_time,
                "execution_time_seconds": execution_time,
                "status": "healthy" if health.is_healthy else "unhealthy",
                "database_path": self.db_path,
                "statistics": stats,
                "table_health": table_health,
                "config_health": config_health,
                "disk_health": disk_health,
            }

            # 更新缓存
            self._last_health_check = 
                {"timestamp": time.time(),
                "result": result
            }

            logger.info(
                "数据库健康检查完成",
                is_healthy=health.is_healthy,
                error_count=len(error_messages),
                execution_time=execution_time,
            )

            return result

        except Exception:
            logger.error(f"数据库健康检查失败: {str(e)}")
            return {
                "database_exists": False,
                "database_accessible": False,
                "connection_successful": False,
                "is_healthy": False,
                "error_messages": [f"健康检查异常: {str(e)}"],
                "check_time": datetime.now().isoformat(),
                "status": "unhealthy",
                "error_detail": str(e),
            }


    async def _get_database_statistics(self, conn) -> Dict:
        """获取数据库统计信息"""
        try:
            stats = {}

            # 数据库大小
            cursor = conn.execute("PRAGMA page_count")
            page_count = cursor.fetchone()[0]
            cursor = conn.execute("PRAGMA page_size")
            page_size = cursor.fetchone()[0]
            stats["database_size_bytes"] = page_count * page_size
            stats["database_size_mb"] = round(
                stats["database_size_bytes"] / 1024 / 1024, 2
            )

            # 表数量
            cursor = conn.execute(
                """
                SELECT COUNT(*) FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """
            )
            stats["table_count"] = cursor.fetchone()[0]

            # 索引数量
            cursor = conn.execute(
                """
                SELECT COUNT(*) FROM sqlite_master 
                WHERE type='index' AND name NOT LIKE 'sqlite_%'
            """
            )
            stats["index_count"] = cursor.fetchone()[0]

            # SQLite版本
            cursor = conn.execute("SELECT sqlite_version()")
            stats["sqlite_version"] = cursor.fetchone()[0]

            return stats

        except Exception:
            logger.warning(f"获取数据库统计信息失败: {str(e)}")
            return {}


    async def _check_config_files_health(self) -> Dict:
        """检查配置文件健康状态"""
        try:
            config_health = 
                {"config_dir_exists": self.config_dir.exists(),
                "files": {}
            }

            if not config_health["config_dir_exists"]:
                return config_health

            # 检查关键配置文件
            important_files = [
                "modules.json",
                "monitoring_config.json",
                "auto_sync_config.json",
            ]

            for file_name in important_files:
                file_path = self.config_dir / file_name
                file_health = {
                    "exists": file_path.exists(),
                    "readable": False,
                    "valid_json": False,
                    "size_bytes": 0,
                }

                if file_health["exists"]:
                    try:
                        file_health["size_bytes"] = file_path.stat().st_size
                        file_health["readable"] = os.access(file_path, os.R_OK)

                        # 验证JSON格式
                        if file_health["readable"]:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                json.load(f)
                            file_health["valid_json"] = True

                    except Exception:
                        file_health["error"] = str(e)

                config_health["files"][file_name] = file_health

            return config_health

        except Exception:
            logger.warning(f"检查配置文件健康状态失败: {str(e)}")
            return {"error": str(e)}


    async def _check_disk_space(self) -> Dict:
        """检查磁盘空间"""
        try:
            db_path = Path(self.db_path)
            disk_usage = os.statvfs(db_path.parent) if hasattr(os, 'statvfs') else None

            if disk_usage:
                total_space = disk_usage.f_frsize * disk_usage.f_blocks
                free_space = disk_usage.f_frsize * disk_usage.f_bavail
                used_space = total_space - free_space

                return {
                    "total_space_bytes": total_space,
                    "free_space_bytes": free_space,
                    "used_space_bytes": used_space,
                    "free_space_mb": round(free_space / 1024 / 1024, 2),
                    "used_space_mb": round(used_space / 1024 / 1024, 2),
                    "usage_percentage": round((used_space / total_space) * 100, 2),
                    "low_space_warning": free_space
                    < 100 * 1024 * 1024,  # 低于100MB警告
                }
            else:
                # Windows平台备用方案

                total, used, free = shutil.disk_usage(db_path.parent)

                return {
                    "total_space_bytes": total,
                    "free_space_bytes": free,
                    "used_space_bytes": used,
                    "free_space_mb": round(free / 1024 / 1024, 2),
                    "used_space_mb": round(used / 1024 / 1024, 2),
                    "usage_percentage": round((used / total) * 100, 2),
                    "low_space_warning": free < 100 * 1024 * 1024,
                }

        except Exception:
            logger.warning(f"检查磁盘空间失败: {str(e)}")
            return {"error": str(e)}

    # 工具方法


    def get_status(self) -> Dict:
        """获取管理器状态信息"""
        return {
            "class_name": self.__class__.__name__,
            "db_path": self.db_path,
            "config_dir": str(self.config_dir),
            "health_cache_duration": self._health_cache_duration,
            "last_health_check": (
                self._last_health_check.get("timestamp")
                if self._last_health_check
                else None
            ),
            "connection_pool_size": len(self._connection_pool),
            "initialized": True,
        }


    def clear_health_cache(self):
        """清除健康检查缓存"""
        self._last_health_check = None
        logger.info("健康检查缓存已清除")


    async def cleanup(self):
        """清理资源"""
        try:
            # 关闭连接池中的连接
            for conn in self._connection_pool.values():
                if conn:
                    conn.close()

            self._connection_pool.clear()
            self._last_health_check = None

            logger.info("数据库表管理器资源清理完成")

        except Exception:
            logger.error(f"资源清理失败: {str(e)}")


# 全局实例（单例模式）
_database_table_manager_instance = None


def get_database_table_manager(db_path: Optional[str] = None) -> DatabaseTableManager:
    """获取数据库表管理器实例（单例模式）

    Args:
        db_path: 数据库路径，仅在首次创建时有效

    Returns:
        DatabaseTableManager: 管理器实例
    """
    global _database_table_manager_instance

    if _database_table_manager_instance is None:
        _database_table_manager_instance = DatabaseTableManager(db_path)

    return _database_table_manager_instance
