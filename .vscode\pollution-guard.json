{"name": "YS-API Pollution Guard", "version": "1.0.0", "description": "实时检测和阻止AI生成的污染代码", "settings": {"pollution.detection.enabled": true, "pollution.signatures": ["Auto-generated by", "AI_generated", "ShadowClass", "AI_Refactor", "TODO: Add function description", "__init___", "extends AI_Base", "newException", "require is not defined"], "pollution.actions": {"onDetection": "warn", "blockSave": true, "autoCleanup": false}}, "rules": [{"id": "no-ai-generated", "severity": "error", "message": "🚨 检测到AI生成的代码，请手动审查", "pattern": "(?i)(auto-generated|ai_generated|shadowclass)"}, {"id": "no-broken-init", "severity": "error", "message": "❌ 错误的__init___方法，应为__init__", "pattern": "__init___"}, {"id": "no-ai-inheritance", "severity": "warning", "message": "⚠️ 检测到AI基类继承，请检查架构", "pattern": "extends AI_Base"}]}