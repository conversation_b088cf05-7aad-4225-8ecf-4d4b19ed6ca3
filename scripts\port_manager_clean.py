#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 端口管理器 - 标准化版本

固定端口配置：
- 后端端口: 8050 (禁止改动)
- 前端端口: 8060 (禁止改动)
"""

import logging
import os
import socket
import subprocess
import time
from pathlib import Path


class PortManager:
    """端口管理器 - 标准化端口配置"""

    # 固定端口配置，禁止改动
    BACKEND_PORT = 8050  # 固定后端端口 8050，禁止改动
    FRONTEND_PORT = 8060  # 固定前端端口 8060，禁止改动
    MAX_RETRIES = 3
    WAIT_TIME = 2  # 进程终止等待时间

    def __init__(self):
        """初始化端口管理器"""
        self.log_file = Path("port_manager.log")
        self.setup_logging()

    def setup_logging(self):
        """设置日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler(self.log_file, encoding="utf-8"),
                logging.StreamHandler(),
            ],
        )
        self.logger = logging.getLogger(__name__)

    def is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                return s.connect_ex(("localhost", port)) == 0
        except Exception:
            return False

    def kill_process_on_port(self, port: int) -> bool:
        """终止占用指定端口的进程"""
        try:
            if os.name == "nt":  # Windows
                cmd = f"netstat -ano | findstr :{port}"
                result = subprocess.run(
                    cmd, shell=True, capture_output=True, text=True)

                for line in result.stdout.split("\n"):
                    if f":{port}" in line and "LISTENING" in line:
                        parts = line.split()
                        if len(parts) >= 5:
                            pid = parts[-1]
                            subprocess.run(
                                f"taskkill /F /PID {pid}", shell=True)
                            self.logger.info("终止进程 PID %s (端口 %d)", pid, port)
                            time.sleep(self.WAIT_TIME)
                            return True
            else:  # Unix/Linux
                cmd = f"lsof -ti:{port}"
                result = subprocess.run(
                    cmd, shell=True, capture_output=True, text=True)
                if result.stdout.strip():
                    pids = result.stdout.strip().split("\n")
                    for pid in pids:
                        subprocess.run(["kill", "-9", pid])
                        self.logger.info("终止进程 PID %s (端口 %d)", pid, port)
                    time.sleep(self.WAIT_TIME)
                    return True
        except (ImportError, OSError, ValueError) as e:
            self.logger.error("终止端口 %d 进程失败: %s", port, e)
        return False

    def ensure_port_available(self, port: int) -> bool:
        """确保端口可用"""
        if not self.is_port_in_use(port):
            return True

        self.logger.warning("端口 %d 被占用，尝试释放...", port)
        for attempt in range(self.MAX_RETRIES):
            if self.kill_process_on_port(port):
                if not self.is_port_in_use(port):
                    self.logger.info("端口 %d 释放成功", port)
                    return True
            time.sleep(1)

        self.logger.error("无法释放端口 %d", port)
        return False

    def get_available_ports(self) -> dict:
        """获取可用端口状态"""
        return {
            "backend": {
                "port": self.BACKEND_PORT,
                "available": not self.is_port_in_use(self.BACKEND_PORT),
                "status": (
                    "可用" if not self.is_port_in_use(self.BACKEND_PORT) else "占用"
                ),
            },
            "frontend": {
                "port": self.FRONTEND_PORT,
                "available": not self.is_port_in_use(self.FRONTEND_PORT),
                "status": (
                    "可用" if not self.is_port_in_use(self.FRONTEND_PORT) else "占用"
                ),
            },
        }

    def handle_critical_error(self, operation: str, error: Exception):
        """处理关键错误"""
        error_msg = f"关键错误 - {operation}: {str(error)}"
        self.logger.error(error_msg)
        print(f"❌ {error_msg}")
        print("🔧 建议检查:")
        print("  1. Python环境是否正确")
        print("  2. 依赖包是否完整安装")
        print("  3. 端口是否被其他程序占用")
        print("  4. 防火墙设置是否正确")


# 全局端口管理器实例
port_manager = PortManager()


def main():
    """主函数 - 端口管理测试"""
    print("🔍 YS-API V3.0 端口管理器测试")
    print("=" * 50)

    ports = port_manager.get_available_ports()

    print("📊 端口状态:")
    for service, info in ports.items():
        status = "✅" if info["available"] else "❌"
        print(
            f"  {service.title()}: {info['port']} - {status} {info['status']}")

    print("\n🎯 标准化端口配置:")
    print(f"  后端服务: {PortManager.BACKEND_PORT} (固定，禁止改动)")
    print(f"  前端服务: {PortManager.FRONTEND_PORT} (固定，禁止改动)")


if __name__ == "__main__":
    main()
