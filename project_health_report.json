{"check_date": "2025-08-02T19:45:41.238216", "project_root": "d:\\OneDrive\\Desktop\\YS-API程序\\v3", "statistics": {"total_files": 453, "html_files": 23, "js_files": 48, "py_files": 100, "critical": 57, "warning": 1, "info": 97}, "issues": [{"severity": "critical", "file": "frontend\\database-v2.html", "line": 0, "type": "double_initialization", "description": "发现2个DOMContentLoaded监听器，可能导致重复初始化", "suggestion": "合并初始化逻辑到单一入口点", "timestamp": "2025-08-02T19:45:41.129341"}, {"severity": "critical", "file": "frontend\\field-config-manual.html", "line": 0, "type": "double_initialization", "description": "发现2个DOMContentLoaded监听器，可能导致重复初始化", "suggestion": "合并初始化逻辑到单一入口点", "timestamp": "2025-08-02T19:45:41.130297"}, {"severity": "critical", "file": "frontend\\migrated\\database-v2.html", "line": 0, "type": "double_initialization", "description": "发现3个DOMContentLoaded监听器，可能导致重复初始化", "suggestion": "合并初始化逻辑到单一入口点", "timestamp": "2025-08-02T19:45:41.132428"}, {"severity": "critical", "file": "frontend\\migrated\\excel-translation.html", "line": 0, "type": "mixed_initialization", "description": "同时使用AppBootstrap和startApp初始化方法", "suggestion": "选择统一的初始化方法", "timestamp": "2025-08-02T19:45:41.132890"}, {"severity": "critical", "file": "frontend\\migrated\\field-config-manual.html", "line": 0, "type": "double_initialization", "description": "发现3个DOMContentLoaded监听器，可能导致重复初始化", "suggestion": "合并初始化逻辑到单一入口点", "timestamp": "2025-08-02T19:45:41.133719"}, {"severity": "critical", "file": "frontend\\migrated\\field-config-manual.html", "line": 0, "type": "mixed_initialization", "description": "同时使用AppBootstrap和startApp初始化方法", "suggestion": "选择统一的初始化方法", "timestamp": "2025-08-02T19:45:41.133775"}, {"severity": "critical", "file": "frontend\\migrated\\maintenance.html", "line": 0, "type": "double_initialization", "description": "发现2个DOMContentLoaded监听器，可能导致重复初始化", "suggestion": "合并初始化逻辑到单一入口点", "timestamp": "2025-08-02T19:45:41.133988"}, {"severity": "critical", "file": "frontend\\migrated\\unified-field-config.html", "line": 0, "type": "mixed_initialization", "description": "同时使用AppBootstrap和startApp初始化方法", "suggestion": "选择统一的初始化方法", "timestamp": "2025-08-02T19:45:41.134399"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\database-v2.html", "line": 0, "type": "double_initialization", "description": "发现3个DOMContentLoaded监听器，可能导致重复初始化", "suggestion": "合并初始化逻辑到单一入口点", "timestamp": "2025-08-02T19:45:41.136288"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\field-config-manual.html", "line": 0, "type": "double_initialization", "description": "发现3个DOMContentLoaded监听器，可能导致重复初始化", "suggestion": "合并初始化逻辑到单一入口点", "timestamp": "2025-08-02T19:45:41.136997"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\field-config-manual.html", "line": 0, "type": "mixed_initialization", "description": "同时使用AppBootstrap和startApp初始化方法", "suggestion": "选择统一的初始化方法", "timestamp": "2025-08-02T19:45:41.137050"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\maintenance.html", "line": 0, "type": "double_initialization", "description": "发现2个DOMContentLoaded监听器，可能导致重复初始化", "suggestion": "合并初始化逻辑到单一入口点", "timestamp": "2025-08-02T19:45:41.137309"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\maintenance.html", "line": 0, "type": "mixed_initialization", "description": "同时使用AppBootstrap和startApp初始化方法", "suggestion": "选择统一的初始化方法", "timestamp": "2025-08-02T19:45:41.137333"}, {"severity": "critical", "file": "frontend\\database-v2.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/smart-logger.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\frontend\\js\\smart-logger.js", "timestamp": "2025-08-02T19:45:41.144513"}, {"severity": "critical", "file": "frontend\\field-config-manual.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: /js/unified-components.js", "suggestion": "创建文件或修正路径: D:\\js\\unified-components.js", "timestamp": "2025-08-02T19:45:41.145613"}, {"severity": "critical", "file": "frontend\\migrated\\field-config-manual.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/field-deduplication-enhancer.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\frontend\\js\\field-deduplication-enhancer.js", "timestamp": "2025-08-02T19:45:41.152995"}, {"severity": "critical", "file": "frontend\\migrated\\unified-field-config.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/field-deduplication-enhancer.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\frontend\\js\\field-deduplication-enhancer.js", "timestamp": "2025-08-02T19:45:41.155133"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\method-fix-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/core/component-manager.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\core\\component-manager.js", "timestamp": "2025-08-02T19:45:41.155537"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\method-fix-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/core/app-bootstrap.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\core\\app-bootstrap.js", "timestamp": "2025-08-02T19:45:41.155749"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\method-fix-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/common/api-client.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\api-client.js", "timestamp": "2025-08-02T19:45:41.155957"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\method-fix-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/common/field-utils.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\field-utils.js", "timestamp": "2025-08-02T19:45:41.156158"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\method-fix-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/common/validation-utils.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\validation-utils.js", "timestamp": "2025-08-02T19:45:41.156360"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\method-fix-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/common/error-handler.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\error-handler.js", "timestamp": "2025-08-02T19:45:41.156568"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\method-fix-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/notification-system.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\notification-system.js", "timestamp": "2025-08-02T19:45:41.156733"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\method-fix-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: field-deduplication-enhancer.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\field-deduplication-enhancer.js", "timestamp": "2025-08-02T19:45:41.156881"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\new-architecture-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/core/component-manager.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\core\\component-manager.js", "timestamp": "2025-08-02T19:45:41.157535"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\new-architecture-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/core/app-bootstrap.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\core\\app-bootstrap.js", "timestamp": "2025-08-02T19:45:41.157732"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\new-architecture-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/core/component-migration-tool.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\core\\component-migration-tool.js", "timestamp": "2025-08-02T19:45:41.157946"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\new-architecture-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/common/api-client.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\api-client.js", "timestamp": "2025-08-02T19:45:41.158144"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\new-architecture-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/common/field-utils.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\field-utils.js", "timestamp": "2025-08-02T19:45:41.158337"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\new-architecture-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/common/validation-utils.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\validation-utils.js", "timestamp": "2025-08-02T19:45:41.158520"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\new-architecture-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/common/error-handler.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\error-handler.js", "timestamp": "2025-08-02T19:45:41.158694"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\new-architecture-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: js/notification-system.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\notification-system.js", "timestamp": "2025-08-02T19:45:41.158836"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\new-architecture-test.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: field-deduplication-enhancer.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\field-deduplication-enhancer.js", "timestamp": "2025-08-02T19:45:41.158972"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\database-v2.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/core/component-manager.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\core\\component-manager.js", "timestamp": "2025-08-02T19:45:41.160068"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\database-v2.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/core/app-bootstrap.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\core\\app-bootstrap.js", "timestamp": "2025-08-02T19:45:41.160283"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\database-v2.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/api-config-fix.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\api-config-fix.js", "timestamp": "2025-08-02T19:45:41.160450"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\database-v2.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/common/api-client.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\api-client.js", "timestamp": "2025-08-02T19:45:41.160638"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\database-v2.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/common/validation-utils.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\validation-utils.js", "timestamp": "2025-08-02T19:45:41.160823"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\database-v2.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/common/error-handler.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\error-handler.js", "timestamp": "2025-08-02T19:45:41.161011"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\database-v2.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/notification-system.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\notification-system.js", "timestamp": "2025-08-02T19:45:41.161171"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\field-config-manual.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/core/component-manager.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\core\\component-manager.js", "timestamp": "2025-08-02T19:45:41.161924"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\field-config-manual.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/core/app-bootstrap.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\core\\app-bootstrap.js", "timestamp": "2025-08-02T19:45:41.162119"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\field-config-manual.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/api-config-fix.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\api-config-fix.js", "timestamp": "2025-08-02T19:45:41.162276"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\field-config-manual.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/common/api-client.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\api-client.js", "timestamp": "2025-08-02T19:45:41.162455"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\field-config-manual.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/common/field-utils.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\field-utils.js", "timestamp": "2025-08-02T19:45:41.162644"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\field-config-manual.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/common/validation-utils.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\validation-utils.js", "timestamp": "2025-08-02T19:45:41.162817"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\field-config-manual.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/common/error-handler.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\error-handler.js", "timestamp": "2025-08-02T19:45:41.162989"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\field-config-manual.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/notification-system.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\notification-system.js", "timestamp": "2025-08-02T19:45:41.163135"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\field-config-manual.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/field-deduplication-enhancer.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\field-deduplication-enhancer.js", "timestamp": "2025-08-02T19:45:41.163281"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\maintenance.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/core/component-manager.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\core\\component-manager.js", "timestamp": "2025-08-02T19:45:41.163693"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\maintenance.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/core/app-bootstrap.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\core\\app-bootstrap.js", "timestamp": "2025-08-02T19:45:41.164101"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\maintenance.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/api-config-fix.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\api-config-fix.js", "timestamp": "2025-08-02T19:45:41.164467"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\maintenance.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/common/api-client.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\api-client.js", "timestamp": "2025-08-02T19:45:41.164926"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\maintenance.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/common/validation-utils.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\validation-utils.js", "timestamp": "2025-08-02T19:45:41.165173"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\maintenance.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/common/error-handler.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\common\\error-handler.js", "timestamp": "2025-08-02T19:45:41.165464"}, {"severity": "critical", "file": "cleanup_backup\\20250802_194215\\frontend\\migrated\\maintenance.html", "line": 0, "type": "missing_dependency", "description": "缺失依赖文件: ../js/notification-system.js", "suggestion": "创建文件或修正路径: D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cleanup_backup\\20250802_194215\\frontend\\js\\notification-system.js", "timestamp": "2025-08-02T19:45:41.165632"}, {"severity": "warning", "file": ".", "line": 0, "type": "inconsistent_api_config", "description": "发现不一致的API配置: ['https://api.example.com', 'http://localhost:8000']", "suggestion": "统一API配置到单一配置文件", "timestamp": "2025-08-02T19:45:41.187128"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: readme.md 在 2 个位置", "suggestion": "检查文件是否真的重复: ['README.md', 'dev-tools\\\\mock\\\\README.md']", "timestamp": "2025-08-02T19:45:41.217780"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: __init__.py 在 7 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\__init__.py', 'backend\\\\app\\\\__init__.py', 'backend\\\\app\\\\api\\\\__init__.py', 'backend\\\\app\\\\core\\\\__init__.py', 'backend\\\\app\\\\schemas\\\\__init__.py', 'backend\\\\app\\\\services\\\\__init__.py', 'backend\\\\app\\\\api\\\\v1\\\\__init__.py']", "timestamp": "2025-08-02T19:45:41.217889"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: modules.json 在 2 个位置", "suggestion": "检查文件是否真的重复: ['config\\\\modules.json', 'backend\\\\app\\\\config\\\\modules.json']", "timestamp": "2025-08-02T19:45:41.217922"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: database-v2.html 在 3 个位置", "suggestion": "检查文件是否真的重复: ['frontend\\\\database-v2.html', 'frontend\\\\migrated\\\\database-v2.html', 'cleanup_backup\\\\20250802_194215\\\\frontend\\\\migrated\\\\database-v2.html']", "timestamp": "2025-08-02T19:45:41.217968"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: excel-translation.html 在 2 个位置", "suggestion": "检查文件是否真的重复: ['frontend\\\\excel-translation.html', 'frontend\\\\migrated\\\\excel-translation.html']", "timestamp": "2025-08-02T19:45:41.217994"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: field-config-manual.html 在 3 个位置", "suggestion": "检查文件是否真的重复: ['frontend\\\\field-config-manual.html', 'frontend\\\\migrated\\\\field-config-manual.html', 'cleanup_backup\\\\20250802_194215\\\\frontend\\\\migrated\\\\field-config-manual.html']", "timestamp": "2025-08-02T19:45:41.218035"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: maintenance.html 在 3 个位置", "suggestion": "检查文件是否真的重复: ['frontend\\\\maintenance.html', 'frontend\\\\migrated\\\\maintenance.html', 'cleanup_backup\\\\20250802_194215\\\\frontend\\\\migrated\\\\maintenance.html']", "timestamp": "2025-08-02T19:45:41.218075"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: unified-field-config.html 在 2 个位置", "suggestion": "检查文件是否真的重复: ['frontend\\\\unified-field-config.html', 'frontend\\\\migrated\\\\unified-field-config.html']", "timestamp": "2025-08-02T19:45:41.218100"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 业务日志.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\业务日志.xml', '模块字段\\\\backup\\\\业务日志.xml']", "timestamp": "2025-08-02T19:45:41.218130"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 产品入库单列表查询.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\产品入库单列表查询.xml', '模块字段\\\\backup\\\\产品入库单列表查询.xml']", "timestamp": "2025-08-02T19:45:41.218154"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 委外入库列表查询.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\委外入库列表查询.xml', '模块字段\\\\backup\\\\委外入库列表查询.xml']", "timestamp": "2025-08-02T19:45:41.218177"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 委外申请列表查询.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\委外申请列表查询.xml', '模块字段\\\\backup\\\\委外申请列表查询.xml']", "timestamp": "2025-08-02T19:45:41.218200"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 委外订单列表.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\委外订单列表.xml', '模块字段\\\\backup\\\\委外订单列表.xml']", "timestamp": "2025-08-02T19:45:41.218224"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 材料出库单列表查询.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\材料出库单列表查询.xml', '模块字段\\\\backup\\\\材料出库单列表查询.xml']", "timestamp": "2025-08-02T19:45:41.218247"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 物料档案批量详情查询.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\物料档案批量详情查询.xml', '模块字段\\\\backup\\\\物料档案批量详情查询.xml']", "timestamp": "2025-08-02T19:45:41.218270"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 现存量报表查询.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\现存量报表查询.xml', '模块字段\\\\backup\\\\现存量报表查询.xml']", "timestamp": "2025-08-02T19:45:41.218293"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 生产订单列表查询.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\生产订单列表查询.xml', '模块字段\\\\backup\\\\生产订单列表查询.xml']", "timestamp": "2025-08-02T19:45:41.218316"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 请购单列表查询.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\请购单列表查询.xml', '模块字段\\\\backup\\\\请购单列表查询.xml']", "timestamp": "2025-08-02T19:45:41.218338"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 采购入库单列表.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\采购入库单列表.xml', '模块字段\\\\backup\\\\采购入库单列表.xml']", "timestamp": "2025-08-02T19:45:41.218361"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 采购订单列表.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\采购订单列表.xml', '模块字段\\\\backup\\\\采购订单列表.xml']", "timestamp": "2025-08-02T19:45:41.218384"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 销售出库列表查询.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\销售出库列表查询.xml', '模块字段\\\\backup\\\\销售出库列表查询.xml']", "timestamp": "2025-08-02T19:45:41.218407"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 销售订单.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\销售订单.xml', '模块字段\\\\backup\\\\销售订单.xml']", "timestamp": "2025-08-02T19:45:41.218433"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: 需求计划.xml 在 2 个位置", "suggestion": "检查文件是否真的重复: ['模块字段\\\\需求计划.xml', '模块字段\\\\backup\\\\需求计划.xml']", "timestamp": "2025-08-02T19:45:41.218458"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: element-plus.css 在 2 个位置", "suggestion": "检查文件是否真的重复: ['frontend\\\\css\\\\element-plus.css', 'frontend\\\\static\\\\css\\\\element-plus.css']", "timestamp": "2025-08-02T19:45:41.218491"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: realtime-log.css 在 2 个位置", "suggestion": "检查文件是否真的重复: ['frontend\\\\css\\\\realtime-log.css', 'frontend\\\\static\\\\css\\\\realtime-log.css']", "timestamp": "2025-08-02T19:45:41.218520"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: smart-logger.css 在 2 个位置", "suggestion": "检查文件是否真的重复: ['frontend\\\\css\\\\smart-logger.css', 'frontend\\\\static\\\\css\\\\smart-logger.css']", "timestamp": "2025-08-02T19:45:41.218547"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: element-plus-icons.iife.min.js 在 2 个位置", "suggestion": "检查文件是否真的重复: ['frontend\\\\js\\\\element-plus-icons.iife.min.js', 'frontend\\\\static\\\\js\\\\element-plus-icons.iife.min.js']", "timestamp": "2025-08-02T19:45:41.218575"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: element-plus.js 在 2 个位置", "suggestion": "检查文件是否真的重复: ['frontend\\\\js\\\\element-plus.js', 'frontend\\\\static\\\\js\\\\element-plus.js']", "timestamp": "2025-08-02T19:45:41.218616"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: vue.global.js 在 2 个位置", "suggestion": "检查文件是否真的重复: ['frontend\\\\js\\\\vue.global.js', 'frontend\\\\static\\\\js\\\\vue.global.js']", "timestamp": "2025-08-02T19:45:41.218643"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: sales_order.json 在 2 个位置", "suggestion": "检查文件是否真的重复: ['config\\\\data\\\\user_field_config\\\\Alice\\\\sales_order.json', 'config\\\\data\\\\user_field_config\\\\TestUser\\\\sales_order.json']", "timestamp": "2025-08-02T19:45:41.218681"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: config.py 在 3 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\core\\\\config.py', 'backend\\\\app\\\\schemas\\\\config.py', 'backend\\\\app\\\\api\\\\v1\\\\config.py']", "timestamp": "2025-08-02T19:45:41.218735"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: database.py 在 3 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\core\\\\database.py', 'backend\\\\app\\\\schemas\\\\database.py', 'backend\\\\app\\\\api\\\\v1\\\\database.py']", "timestamp": "2025-08-02T19:45:41.218781"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: database_manager.py 在 2 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\core\\\\database_manager.py', 'backend\\\\app\\\\services\\\\database_manager.py']", "timestamp": "2025-08-02T19:45:41.218811"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: monitor.py 在 2 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\schemas\\\\monitor.py', 'backend\\\\app\\\\api\\\\v1\\\\monitor.py']", "timestamp": "2025-08-02T19:45:41.218843"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: sync.py 在 2 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\schemas\\\\sync.py', 'backend\\\\app\\\\api\\\\v1\\\\sync.py']", "timestamp": "2025-08-02T19:45:41.218874"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: database_manager.cpython-310.pyc 在 2 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\services\\\\__pycache__\\\\database_manager.cpython-310.pyc', 'backend\\\\app\\\\core\\\\__pycache__\\\\database_manager.cpython-310.pyc']", "timestamp": "2025-08-02T19:45:41.218911"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: __init__.cpython-310.pyc 在 5 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\services\\\\__pycache__\\\\__init__.cpython-310.pyc', 'backend\\\\app\\\\schemas\\\\__pycache__\\\\__init__.cpython-310.pyc', 'backend\\\\app\\\\core\\\\__pycache__\\\\__init__.cpython-310.pyc', 'backend\\\\app\\\\api\\\\__pycache__\\\\__init__.cpython-310.pyc', 'backend\\\\app\\\\api\\\\v1\\\\__pycache__\\\\__init__.cpython-310.pyc']", "timestamp": "2025-08-02T19:45:41.218993"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: __init__.cpython-313.pyc 在 5 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\services\\\\__pycache__\\\\__init__.cpython-313.pyc', 'backend\\\\app\\\\schemas\\\\__pycache__\\\\__init__.cpython-313.pyc', 'backend\\\\app\\\\core\\\\__pycache__\\\\__init__.cpython-313.pyc', 'backend\\\\app\\\\api\\\\__pycache__\\\\__init__.cpython-313.pyc', 'backend\\\\app\\\\api\\\\v1\\\\__pycache__\\\\__init__.cpython-313.pyc']", "timestamp": "2025-08-02T19:45:41.219075"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: config.cpython-310.pyc 在 3 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\schemas\\\\__pycache__\\\\config.cpython-310.pyc', 'backend\\\\app\\\\core\\\\__pycache__\\\\config.cpython-310.pyc', 'backend\\\\app\\\\api\\\\v1\\\\__pycache__\\\\config.cpython-310.pyc']", "timestamp": "2025-08-02T19:45:41.219126"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: config.cpython-313.pyc 在 3 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\schemas\\\\__pycache__\\\\config.cpython-313.pyc', 'backend\\\\app\\\\core\\\\__pycache__\\\\config.cpython-313.pyc', 'backend\\\\app\\\\api\\\\v1\\\\__pycache__\\\\config.cpython-313.pyc']", "timestamp": "2025-08-02T19:45:41.219177"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: monitor.cpython-310.pyc 在 2 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\schemas\\\\__pycache__\\\\monitor.cpython-310.pyc', 'backend\\\\app\\\\api\\\\v1\\\\__pycache__\\\\monitor.cpython-310.pyc']", "timestamp": "2025-08-02T19:45:41.219212"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: monitor.cpython-313.pyc 在 2 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\schemas\\\\__pycache__\\\\monitor.cpython-313.pyc', 'backend\\\\app\\\\api\\\\v1\\\\__pycache__\\\\monitor.cpython-313.pyc']", "timestamp": "2025-08-02T19:45:41.219247"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: sync.cpython-310.pyc 在 2 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\schemas\\\\__pycache__\\\\sync.cpython-310.pyc', 'backend\\\\app\\\\api\\\\v1\\\\__pycache__\\\\sync.cpython-310.pyc']", "timestamp": "2025-08-02T19:45:41.219282"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: sync.cpython-313.pyc 在 2 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\schemas\\\\__pycache__\\\\sync.cpython-313.pyc', 'backend\\\\app\\\\api\\\\v1\\\\__pycache__\\\\sync.cpython-313.pyc']", "timestamp": "2025-08-02T19:45:41.219317"}, {"severity": "info", "file": ".", "line": 0, "type": "duplicate_files", "description": "发现重复文件名: database.cpython-310.pyc 在 2 个位置", "suggestion": "检查文件是否真的重复: ['backend\\\\app\\\\core\\\\__pycache__\\\\database.cpython-310.pyc', 'backend\\\\app\\\\api\\\\v1\\\\__pycache__\\\\database.cpython-310.pyc']", "timestamp": "2025-08-02T19:45:41.219352"}, {"severity": "info", "file": "backend\\start_simple.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.225484"}, {"severity": "info", "file": "backend\\__init__.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.225595"}, {"severity": "info", "file": "tools\\error_handling_load_test.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.227170"}, {"severity": "info", "file": "backend\\app\\main.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.227848"}, {"severity": "info", "file": "backend\\app\\main_original.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.228074"}, {"severity": "info", "file": "backend\\app\\__init__.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.228196"}, {"severity": "info", "file": "backend\\app\\api\\__init__.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.228283"}, {"severity": "info", "file": "backend\\app\\core\\code_quality.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.228479"}, {"severity": "info", "file": "backend\\app\\core\\config.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.228611"}, {"severity": "info", "file": "backend\\app\\core\\database.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.228713"}, {"severity": "info", "file": "backend\\app\\core\\database_connection_pool.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.228843"}, {"severity": "info", "file": "backend\\app\\core\\database_manager.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.228935"}, {"severity": "info", "file": "backend\\app\\core\\exceptions.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.229063"}, {"severity": "info", "file": "backend\\app\\core\\optimized_retry.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.229165"}, {"severity": "info", "file": "backend\\app\\core\\__init__.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.229234"}, {"severity": "info", "file": "backend\\app\\middleware\\access_log.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.229436"}, {"severity": "info", "file": "backend\\app\\schemas\\base.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.229573"}, {"severity": "info", "file": "backend\\app\\schemas\\config.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.229687"}, {"severity": "info", "file": "backend\\app\\schemas\\database.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.229770"}, {"severity": "info", "file": "backend\\app\\schemas\\monitor.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.229850"}, {"severity": "info", "file": "backend\\app\\schemas\\realtime_log.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.229932"}, {"severity": "info", "file": "backend\\app\\schemas\\sync.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.230034"}, {"severity": "info", "file": "backend\\app\\schemas\\__init__.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.230103"}, {"severity": "info", "file": "backend\\app\\services\\config_persistence_service.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.230618"}, {"severity": "info", "file": "backend\\app\\services\\database_manager.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.230926"}, {"severity": "info", "file": "backend\\app\\services\\database_table_manager.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.231427"}, {"severity": "info", "file": "backend\\app\\services\\data_processor.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.231642"}, {"severity": "info", "file": "backend\\app\\services\\data_write_manager.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.232195"}, {"severity": "info", "file": "backend\\app\\services\\fast_sync_service.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.233042"}, {"severity": "info", "file": "backend\\app\\services\\field_analysis_service.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.233223"}, {"severity": "info", "file": "backend\\app\\services\\field_extractor_service.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.233499"}, {"severity": "info", "file": "backend\\app\\services\\field_validation_service.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.233612"}, {"severity": "info", "file": "backend\\app\\services\\intelligent_field_mapper.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.233894"}, {"severity": "info", "file": "backend\\app\\services\\material_master_scheduler.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.234248"}, {"severity": "info", "file": "backend\\app\\services\\md_parser.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.234411"}, {"severity": "info", "file": "backend\\app\\services\\realtime_log_service.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.234566"}, {"severity": "info", "file": "backend\\app\\services\\token_service.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.235080"}, {"severity": "info", "file": "backend\\app\\services\\unified_field_manager.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.235247"}, {"severity": "info", "file": "backend\\app\\services\\ys_api_client.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.235845"}, {"severity": "info", "file": "backend\\app\\services\\zero_downtime_implementation.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.236128"}, {"severity": "info", "file": "backend\\app\\services\\__init__.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.236234"}, {"severity": "info", "file": "backend\\app\\api\\v1\\auth.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.236322"}, {"severity": "info", "file": "backend\\app\\api\\v1\\config.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.236507"}, {"severity": "info", "file": "backend\\app\\api\\v1\\database.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.236616"}, {"severity": "info", "file": "backend\\app\\api\\v1\\database_health.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.236701"}, {"severity": "info", "file": "backend\\app\\api\\v1\\enhanced_sync.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.236810"}, {"severity": "info", "file": "backend\\app\\api\\v1\\monitor.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.237286"}, {"severity": "info", "file": "backend\\app\\api\\v1\\realtime_logs.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.237385"}, {"severity": "info", "file": "backend\\app\\api\\v1\\sync.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.237727"}, {"severity": "info", "file": "backend\\app\\api\\v1\\tasks.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.237978"}, {"severity": "info", "file": "backend\\app\\api\\v1\\unified_field_config.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.238120"}, {"severity": "info", "file": "backend\\app\\api\\v1\\__init__.py", "line": 1, "type": "missing_encoding", "description": "Python文件缺少编码声明", "suggestion": "在文件开头添加 # -*- coding: utf-8 -*-", "timestamp": "2025-08-02T19:45:41.238200"}], "summary": {"total_issues": 155, "critical_issues": 57, "warnings": 1, "info_items": 97, "health_score": 0}}