#!/bin/bash
# YS-API V3.0 回滚脚本

set -e

echo "🔄 开始回滚操作..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 检查备份目录
if [ ! -d "backup" ]; then
    echo -e "${RED}❌ 没有找到备份目录${NC}"
    exit 1
fi

# 获取最新的备份文件
LATEST_BACKUP=$(ls -t backup/deployment_*.tar.gz 2>/dev/null | head -n1)

if [ -z "$LATEST_BACKUP" ]; then
    echo -e "${RED}❌ 没有找到备份文件${NC}"
    exit 1
fi

echo -e "${YELLOW}找到备份文件: $LATEST_BACKUP${NC}"

# 停止当前服务
echo -e "${YELLOW}停止当前服务...${NC}"
docker-compose down

# 恢复备份
echo -e "${YELLOW}恢复备份...${NC}"
tar -xzf "$LATEST_BACKUP"

# 重新启动服务
echo -e "${YELLOW}重新启动服务...${NC}"
docker-compose up -d

# 等待服务启动
sleep 30

# 健康检查
for i in {1..10}; do
    if curl -f http://localhost:5000/health &>/dev/null; then
        echo -e "${GREEN}✅ 回滚成功！${NC}"
        exit 0
    fi
    echo "等待服务响应... ($i/10)"
    sleep 10
done

echo -e "${RED}❌ 回滚失败！${NC}"
exit 1
