import ast
import os

# !/usr/bin/env python3
"""
无用import检测器
"""


class UnusedImportChecker:
    def __init__(self):
        """初始化无用import检测器"""
        self.issues = []

    def check_file(self, filepath):
        """检查单个文件的无用import"""
        try:
            with open(filepath, "r", encoding="utf-8") as f:
                content = f.read()

            tree = ast.parse(content)
            imports = set()
            used_names = set()

            # 收集所有import
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.add(alias.asname or alias.name)
                elif isinstance(node, ast.ImportFrom):
                    for alias in node.names:
                        imports.add(alias.asname or alias.name)
                elif isinstance(node, ast.Name):
                    used_names.add(node.id)
                elif isinstance(node, ast.Attribute):
                    # 处理 module.function 形式
                    if isinstance(node.value, ast.Name):
                        used_names.add(node.value.id)

            # 找出未使用的import
            unused = imports - used_names
            if unused:
                self.issues.append(
                    {
                        "file": filepath,
                        "unused_imports": list(unused),
                        "total_imports": len(imports),
                        "unused_count": len(unused),
                    }
                )

        except Exception:
            # 忽略解析错误的文件
            pass

    def check_project(self):
        """检查整个项目"""
        for root, dirs, files in os.walk("."):
            # 跳过一些目录
            dirs[:] = [d for d in dirs if not d.startswith(
                ".") and d != "__pycache__"]

            for file in files:
                if file.endswith(".py"):
                    filepath = os.path.join(root, file)
                    self.check_file(filepath)

        return self.issues


def main():
    """主函数"""
    print("🔍 检查无用import...")

    checker = UnusedImportChecker()
    issues = checker.check_project()

    if not issues:
        print("✅ 未发现明显的无用import")
        return

    print(f"📊 发现 {len(issues)} 个文件有无用import:")
    print()

    total_unused = 0
    for issue in issues[:20]:  # 只显示前20个
        file = issue["file"]
        unused = issue["unused_imports"]
        total_unused += len(unused)

        print(f"📁 {file}:")
        print(f'   无用import: {", ".join(unused)}')
        print(f'   无用/总计: {issue["unused_count"]}/{issue["total_imports"]}')
        print()

    if len(issues) > 20:
        remaining = len(issues) - 20
        remaining_unused = sum(len(issue["unused_imports"])
                               for issue in issues[20:])
        total_unused += remaining_unused
        print(f"... 还有 {remaining} 个文件有问题")

    print("📈 统计:")
    print(f"   有问题的文件: {len(issues)}个")
    print(f"   总无用import: {total_unused}个")
    print(f"   平均每文件: {total_unused/len(issues):.1f}个")


if __name__ == "__main__":
    main()
