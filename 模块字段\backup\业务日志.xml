<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>e179ea7bd91047d2a6c77b95a3d2817d</id>
<name>业务日志</name>
<apiClassifyId>7e62fb89dd574771a2257a4b09d26ac2</apiClassifyId>
<apiClassifyName>日志管理</apiClassifyName>
<apiClassifyCode>A13_AuditlogBusiness</apiClassifyCode>
<parentApiClassifies/>
<functionId/>
<openMode>0</openMode>
<description>可以根据此接口获取本租户日志数据，做数据分析使用。</description>
<auth>true</auth>
<bodyPassthrough>false</bodyPassthrough>
<healthExam>false</healthExam>
<healthStatus>true</healthStatus>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/digitalModel/log-pub/business/rest/query</completeProxyUrl>
<connectUrl>/log-pub/business/rest/query</connectUrl>
<sort>20</sort>
<handler>openapi</handler>
<httpRequestType>GET</httpRequestType>
<openApi>true</openApi>
<preset>false</preset>
<productId>4d12434dd5de42c2b6fffd093e31e074</productId>
<productCode/>
<proxyUrl>/yonbip/digitalModel/log-pub/business/rest/query</proxyUrl>
<requestParamsDemo>Url: /yonsuite/digitalModel//log-pub/business/rest/query&content=null&busiObjType=null&busiObjCode=null&busiObjName=null&operator=null&startDate=null&endDate=null&page=1&size=10</requestParamsDemo>
<requestProtocol>CUSTOM</requestProtocol>
<serviceHttpMethod>GET</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2020-03-19 16:16:24.000</gmtCreate>
<gmtUpdate>2025-06-30 17:30:10.293</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/digitalModel/log-pub/business/rest/query</address>
<productName/>
<productClassifyId/>
<productClassifyCode/>
<productClassifyName/>
<paramDTOS>
<paramDTOS>
<id>2302753673694412809</id>
<name>content</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412810</defParamId>
<array>false</array>
<paramDesc>日志内容</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2302753673694412811</id>
<name>busiObjType</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412812</defParamId>
<array>false</array>
<paramDesc>业务对象类型</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2302753673694412813</id>
<name>busiObjCode</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412814</defParamId>
<array>false</array>
<paramDesc>业务对象编码</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2302753673694412815</id>
<name>busiObjName</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412816</defParamId>
<array>false</array>
<paramDesc>业务对象名称</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2302753673694412817</id>
<name>operator</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412818</defParamId>
<array>false</array>
<paramDesc>操作人(id)，多个用逗号分隔</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2302753673694412819</id>
<name>startDate</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412820</defParamId>
<array>false</array>
<paramDesc>开始时间（时间戳）</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2302753673694412821</id>
<name>endDate</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412822</defParamId>
<array>false</array>
<paramDesc>结束时间（时间戳）</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2302753673694412823</id>
<name>page</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412824</defParamId>
<array>false</array>
<paramDesc>页码</paramDesc>
<paramType>int</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2302753673694412825</id>
<name>size</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412826</defParamId>
<array>false</array>
<paramDesc>每页数量</paramDesc>
<paramType>int</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2302753673694412827</id>
<name>tenantId</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412828</defParamId>
<array>false</array>
<paramDesc>租户id</paramDesc>
<paramType>string</paramType>
<requestParamType>HeaderParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>false</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2302753673694412829</id>
<name>operNameResid</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412830</defParamId>
<array>false</array>
<paramDesc>操作类型</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>2302753673694412881</id>
<name>content</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>日志内容</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>content</mapName>
<mapRequestParamType>QueryParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2302753673694412883</id>
<name>busiObjType</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>业务对象类型</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>busiObjType</mapName>
<mapRequestParamType>QueryParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2302753673694412885</id>
<name>busiObjCode</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>业务对象编码</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>busiObjCode</mapName>
<mapRequestParamType>QueryParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2302753673694412887</id>
<name>busiObjName</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>业务对象名称</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>busiObjName</mapName>
<mapRequestParamType>QueryParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2302753673694412889</id>
<name>operator</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>操作人(id)，多个用逗号分隔</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>operator</mapName>
<mapRequestParamType>QueryParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2302753673694412891</id>
<name>startDate</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>开始时间（时间戳）</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>startDate</mapName>
<mapRequestParamType>QueryParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2302753673694412893</id>
<name>endDate</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>结束时间（时间戳）</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>endDate</mapName>
<mapRequestParamType>QueryParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2302753673694412895</id>
<name>page</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>页码</paramDesc>
<paramType>int</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>page</mapName>
<mapRequestParamType>QueryParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2302753673694412897</id>
<name>size</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>每页数量</paramDesc>
<paramType>int</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>size</mapName>
<mapRequestParamType>QueryParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2302753673694412899</id>
<name>tenantId</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>租户id</paramDesc>
<paramType>string</paramType>
<requestParamType>HeaderParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>tenantId</mapName>
<mapRequestParamType>HeaderParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2302753673694412901</id>
<name>operNameResid</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>操作类型</paramDesc>
<paramType>string</paramType>
<requestParamType>QueryParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>operNameResid</mapName>
<mapRequestParamType>QueryParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>2302753673694412831</id>
<name>status</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412832</defParamId>
<array>false</array>
<paramDesc>状态码</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>2</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2302753673694412833</id>
<name>data</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<children>
<children>
<id>2302753673694412835</id>
<name>number</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412833</parentId>
<defParamId>2302753673694412836</defParamId>
<array>false</array>
<paramDesc>页码</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>2</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412837</id>
<name>totalPages</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412833</parentId>
<defParamId>2302753673694412838</defParamId>
<array>false</array>
<paramDesc>总页数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>10000</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>2</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412839</id>
<name>content</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412833</parentId>
<children>
<children>
<id>2302753673694412841</id>
<name>busiObjCode</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412842</defParamId>
<array>false</array>
<paramDesc>业务对象编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>YHT-2262631-22630211596791945093</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412843</id>
<name>busiObjName</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412844</defParamId>
<array>false</array>
<paramDesc>业务对象名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>黄家成</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412845</id>
<name>operationDate</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412846</defParamId>
<array>false</array>
<paramDesc>操作时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2020-08-29T08:24:59.989+0000</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412847</id>
<name>operResult</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412848</defParamId>
<array>false</array>
<paramDesc>操作结果</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>success</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412849</id>
<name>operCode</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412850</defParamId>
<array>false</array>
<paramDesc>操作编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412851</id>
<name>detail</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412852</defParamId>
<array>false</array>
<paramDesc>操作详情</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>13910934966在2020-08-29 16:24:59对黄家成设置管理员</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412853</id>
<name>busiObjTypeName</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412854</defParamId>
<array>false</array>
<paramDesc>业务对象所属类型名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>租户管理员</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412855</id>
<name>busiObjId</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412856</defParamId>
<array>false</array>
<paramDesc>业务对象ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2f2c6672-87c7-44e7-a4d3-ef4b27b3597e</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412857</id>
<name>operationName</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412858</defParamId>
<array>false</array>
<paramDesc>操作名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>设置管理员</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412859</id>
<name>busiObjTypeCode</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412860</defParamId>
<array>false</array>
<paramDesc>业务对象所属类型编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>bd_user_manager</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412861</id>
<name>tenantId</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412862</defParamId>
<array>false</array>
<paramDesc>租户ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>s4adr3x4</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412863</id>
<name>newBusiObj</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412864</defParamId>
<array>false</array>
<paramDesc>新业务对象</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>{"begindate":1598689499633}</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412865</id>
<name>sysId</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412866</defParamId>
<array>false</array>
<paramDesc>系统标识</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>aps</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412867</id>
<name>operator</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412868</defParamId>
<array>false</array>
<paramDesc>操作人id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>other</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412869</id>
<name>businessId</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412870</defParamId>
<array>false</array>
<paramDesc>主键（在es中作为索引）</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>ca2ea9e9-5835-44f2-8fdd-35a2b72bb4f1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412871</id>
<name>operatorName</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412872</defParamId>
<array>false</array>
<paramDesc>操作人名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412873</id>
<name>ip</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412839</parentId>
<defParamId>2302753673694412874</defParamId>
<array>false</array>
<paramDesc>ip地址</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2302753673694412840</defParamId>
<array>true</array>
<paramDesc>内容</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-06-30 17:30:10.699</gmtCreate>
<gmtUpdate>2025-06-30 17:30:10.699</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2302753673694412875</id>
<name>totalElements</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId>2302753673694412833</parentId>
<defParamId>2302753673694412876</defParamId>
<array>false</array>
<paramDesc>总条数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>10000</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>2</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2302753673694412834</defParamId>
<array>false</array>
<paramDesc>数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-06-30 17:30:10.690</gmtCreate>
<gmtUpdate>2025-06-30 17:30:10.690</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2302753673694412877</id>
<name>displayCode</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412878</defParamId>
<array>false</array>
<paramDesc>异常码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2302753673694412879</id>
<name>level</name>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<parentId/>
<defParamId>2302753673694412880</defParamId>
<array>false</array>
<paramDesc>异常等级</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>2</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2302753673694412801</id>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<content>{ "status": 1, "data": { "totalElements": 447, "totalPages": 45, "number": 0, "first": false, "last": false, "content": [ { "businessId": "7d9b66a0-f8e0-4175-9ba5-e8c29b9e2ba2", "mdId": null, "mdUri": null, "busiObjTypeCode": "areaFormatRecordCard", "busiObjTypeName": "区域格式信息", "busiObjId": "****************", "busiObjCode": "test_5", "busiObjName": "test_5", "newBusiObj": "{\"area_name\":{\"en_US\":\"test_5\",\"zh_CN\":\"test_5\"},\"areaFormatNumberRecords!number_format_code\":\"0\",\"areaFormatTimeRecords!area_base_info_id\":****************,\"areaFormatTimeRecords!id\":********19629570,\"format_time\":\"15:10:10\",\"format_number\":\"-1,234,567.89\",\"area_code\":\"test_5\",\"areaFormatAddressRecords\":[{\"addr_segment_code\":\"country\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":1,\"id\":********19629572,\"is_show\":1},{\"addr_segment_code\":\"province\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":2,\"id\":********19629573,\"is_show\":1},{\"addr_segment_code\":\"city\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":3,\"id\":********19629574,\"is_show\":1},{\"addr_segment_code\":\"district\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":4,\"id\":********19629575,\"is_show\":1},{\"addr_segment_code\":\"addressone\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":5,\"id\":********19629576,\"is_show\":1},{\"addr_segment_code\":\"addresstwo\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":6,\"id\":********19629577,\"is_show\":1},{\"addr_segment_code\":\"addressthree\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":7,\"id\":********19645952,\"is_show\":1},{\"addr_segment_code\":\"addressfour\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":8,\"id\":********19645953,\"is_show\":1},{\"addr_segment_code\":\"postcode\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":9,\"id\":********19645954,\"is_show\":1}],\"areaFormatDateRecords!area_base_info_id\":****************,\"areaFormatNumberRecords!id\":********19629571,\"format_address\":\"[{\\\"addr_segment_code\\\":\\\"country\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":1,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"province\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":2,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"city\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":3,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"district\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":4,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressone\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":5,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addresstwo\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":6,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressthree\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":7,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressfour\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":8,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"postcode\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":9,\\\"is_show\\\":1}]\",\"areaFormatNumberRecords!area_base_info_id\":****************,\"areaFormatNumberRecords!minus_format_code\":\"0\",\"is_default\":0,\"areaFormatDateRecords!format_enum_code\":\"0\",\"areaFormatTimeRecords!format_enum_code\":\"0\",\"areaFormatDateRecords!id\":********19629569,\"id\":****************,\"pubts\":\"2021-06-29 10:24:27\",\"format_date\":\"2019-11-26\"}", "operCode": "save", "operationName": "Save", "detail": "u8c_vip@163.comon2021-06-29 10:40:33for区域格式信息:test_5(test_5)performedSave", "operator": "YHT-870-57415517002", "operatorName": "<EMAIL>", "operationDate": "2021-06-29T02:40:33.350+00:00", "tenantId": "czqne4bp", "sysId": null, "caepOrg": null, "caepRole": null, "ip": "***********", "operResult": "success", "ts": null, "operDateForExport": null, "struct": 1, "domain": "eventcenter" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2302753673694412802</id>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<content>{ "status": 0, "msg": "查询日志失败", "displayCode":"XXX-XXX-XXXXXX", "level":0, "errorCode": "000000" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2302753673694412801</id>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<content>{ "status": 1, "data": { "totalElements": 447, "totalPages": 45, "number": 0, "first": false, "last": false, "content": [ { "businessId": "7d9b66a0-f8e0-4175-9ba5-e8c29b9e2ba2", "mdId": null, "mdUri": null, "busiObjTypeCode": "areaFormatRecordCard", "busiObjTypeName": "区域格式信息", "busiObjId": "****************", "busiObjCode": "test_5", "busiObjName": "test_5", "newBusiObj": "{\"area_name\":{\"en_US\":\"test_5\",\"zh_CN\":\"test_5\"},\"areaFormatNumberRecords!number_format_code\":\"0\",\"areaFormatTimeRecords!area_base_info_id\":****************,\"areaFormatTimeRecords!id\":********19629570,\"format_time\":\"15:10:10\",\"format_number\":\"-1,234,567.89\",\"area_code\":\"test_5\",\"areaFormatAddressRecords\":[{\"addr_segment_code\":\"country\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":1,\"id\":********19629572,\"is_show\":1},{\"addr_segment_code\":\"province\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":2,\"id\":********19629573,\"is_show\":1},{\"addr_segment_code\":\"city\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":3,\"id\":********19629574,\"is_show\":1},{\"addr_segment_code\":\"district\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":4,\"id\":********19629575,\"is_show\":1},{\"addr_segment_code\":\"addressone\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":5,\"id\":********19629576,\"is_show\":1},{\"addr_segment_code\":\"addresstwo\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":6,\"id\":********19629577,\"is_show\":1},{\"addr_segment_code\":\"addressthree\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":7,\"id\":********19645952,\"is_show\":1},{\"addr_segment_code\":\"addressfour\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":8,\"id\":********19645953,\"is_show\":1},{\"addr_segment_code\":\"postcode\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":9,\"id\":********19645954,\"is_show\":1}],\"areaFormatDateRecords!area_base_info_id\":****************,\"areaFormatNumberRecords!id\":********19629571,\"format_address\":\"[{\\\"addr_segment_code\\\":\\\"country\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":1,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"province\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":2,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"city\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":3,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"district\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":4,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressone\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":5,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addresstwo\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":6,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressthree\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":7,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressfour\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":8,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"postcode\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":9,\\\"is_show\\\":1}]\",\"areaFormatNumberRecords!area_base_info_id\":****************,\"areaFormatNumberRecords!minus_format_code\":\"0\",\"is_default\":0,\"areaFormatDateRecords!format_enum_code\":\"0\",\"areaFormatTimeRecords!format_enum_code\":\"0\",\"areaFormatDateRecords!id\":********19629569,\"id\":****************,\"pubts\":\"2021-06-29 10:24:27\",\"format_date\":\"2019-11-26\"}", "operCode": "save", "operationName": "Save", "detail": "u8c_vip@163.comon2021-06-29 10:40:33for区域格式信息:test_5(test_5)performedSave", "operator": "YHT-870-57415517002", "operatorName": "<EMAIL>", "operationDate": "2021-06-29T02:40:33.350+00:00", "tenantId": "czqne4bp", "sysId": null, "caepOrg": null, "caepRole": null, "ip": "***********", "operResult": "success", "ts": null, "operDateForExport": null, "struct": 1, "domain": "eventcenter" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2302753673694412802</id>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<content>{ "status": 0, "msg": "查询日志失败", "displayCode":"XXX-XXX-XXXXXX", "level":0, "errorCode": "000000" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>2302753673694412801</id>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<content>{ "status": 1, "data": { "totalElements": 447, "totalPages": 45, "number": 0, "first": false, "last": false, "content": [ { "businessId": "7d9b66a0-f8e0-4175-9ba5-e8c29b9e2ba2", "mdId": null, "mdUri": null, "busiObjTypeCode": "areaFormatRecordCard", "busiObjTypeName": "区域格式信息", "busiObjId": "****************", "busiObjCode": "test_5", "busiObjName": "test_5", "newBusiObj": "{\"area_name\":{\"en_US\":\"test_5\",\"zh_CN\":\"test_5\"},\"areaFormatNumberRecords!number_format_code\":\"0\",\"areaFormatTimeRecords!area_base_info_id\":****************,\"areaFormatTimeRecords!id\":********19629570,\"format_time\":\"15:10:10\",\"format_number\":\"-1,234,567.89\",\"area_code\":\"test_5\",\"areaFormatAddressRecords\":[{\"addr_segment_code\":\"country\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":1,\"id\":********19629572,\"is_show\":1},{\"addr_segment_code\":\"province\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":2,\"id\":********19629573,\"is_show\":1},{\"addr_segment_code\":\"city\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":3,\"id\":********19629574,\"is_show\":1},{\"addr_segment_code\":\"district\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":4,\"id\":********19629575,\"is_show\":1},{\"addr_segment_code\":\"addressone\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":5,\"id\":********19629576,\"is_show\":1},{\"addr_segment_code\":\"addresstwo\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":6,\"id\":********19629577,\"is_show\":1},{\"addr_segment_code\":\"addressthree\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":7,\"id\":********19645952,\"is_show\":1},{\"addr_segment_code\":\"addressfour\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":8,\"id\":********19645953,\"is_show\":1},{\"addr_segment_code\":\"postcode\",\"is_required\":1,\"area_base_info_id\":****************,\"addr_separator\":\" \",\"addr_order\":9,\"id\":********19645954,\"is_show\":1}],\"areaFormatDateRecords!area_base_info_id\":****************,\"areaFormatNumberRecords!id\":********19629571,\"format_address\":\"[{\\\"addr_segment_code\\\":\\\"country\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":1,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"province\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":2,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"city\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":3,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"district\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":4,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressone\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":5,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addresstwo\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":6,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressthree\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":7,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressfour\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":8,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"postcode\\\",\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":9,\\\"is_show\\\":1}]\",\"areaFormatNumberRecords!area_base_info_id\":****************,\"areaFormatNumberRecords!minus_format_code\":\"0\",\"is_default\":0,\"areaFormatDateRecords!format_enum_code\":\"0\",\"areaFormatTimeRecords!format_enum_code\":\"0\",\"areaFormatDateRecords!id\":********19629569,\"id\":****************,\"pubts\":\"2021-06-29 10:24:27\",\"format_date\":\"2019-11-26\"}", "operCode": "save", "operationName": "Save", "detail": "u8c_vip@163.comon2021-06-29 10:40:33for区域格式信息:test_5(test_5)performedSave", "operator": "YHT-870-57415517002", "operatorName": "<EMAIL>", "operationDate": "2021-06-29T02:40:33.350+00:00", "tenantId": "czqne4bp", "sysId": null, "caepOrg": null, "caepRole": null, "ip": "***********", "operResult": "success", "ts": null, "operDateForExport": null, "struct": 1, "domain": "eventcenter" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>2302753673694412802</id>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<content>{ "status": 0, "msg": "查询日志失败", "displayCode":"XXX-XXX-XXXXXX", "level":0, "errorCode": "000000" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS>
<errorCodeDTOS>
<id>2302753673694412803</id>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<errorCode>status</errorCode>
<errorMessage>0</errorMessage>
<errorType>API</errorType>
<errorcodeDesc>状态值</errorcodeDesc>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<defErrorId>2302753673694412804</defErrorId>
<ytenantId>0</ytenantId>
<displayCodeId/>
</errorCodeDTOS>
<errorCodeDTOS>
<id>2302753673694412805</id>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<errorCode>msg</errorCode>
<errorMessage>查询日志失败</errorMessage>
<errorType>API</errorType>
<errorcodeDesc/>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<defErrorId>2302753673694412806</defErrorId>
<ytenantId>0</ytenantId>
<displayCodeId/>
</errorCodeDTOS>
<errorCodeDTOS>
<id>2302753673694412807</id>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<errorCode>errorCode</errorCode>
<errorMessage>000000</errorMessage>
<errorType>API</errorType>
<errorcodeDesc/>
<gmtCreate>2024-07-01 11:44:59.000</gmtCreate>
<gmtUpdate>2024-07-01 11:44:59.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<defErrorId>2302753673694412808</defErrorId>
<ytenantId>0</ytenantId>
<displayCodeId/>
</errorCodeDTOS>
</errorCodeDTOS>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88c</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>开发者中心加签业务扩展插件</name>
<configurable>false</configurable>
<description>YonBIPBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonbip.YonBIPBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>e179ea7bd91047d2a6c77b95a3d2817d</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin>
<id>w181ed01-1e9b-4350-b994-71a66f062555</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code>resultParse</code>
<name>返回参数映射插件</name>
<configurable>false</configurable>
<description>符合映射配置的会自动解析，不符合的会自动略过</description>
<pluginType>resultParse</pluginType>
<pluginTypeName>返回值解析插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.result.ResultMapParsePlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>true</visible>
<gmtCreate>2020-05-08 00:00:00</gmtCreate>
<gmtUpdate/>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>e179ea7bd91047d2a6c77b95a3d2817d</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</resultParsePlugin>
<mapReturnPluginConfig>
<id>2302753682284609537</id>
<relateId>e179ea7bd91047d2a6c77b95a3d2817d</relateId>
<level>api</level>
<code>status</code>
<successType>number</successType>
<successCode>1</successCode>
<message>msg</message>
<data>data</data>
<filter>false</filter>
</mapReturnPluginConfig>
<billNo/>
<domain/>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser/>
<createUserName/>
<approvalStatus>1</approvalStatus>
<publishTime>2025-06-30 17:45:10</publishTime>
<pathJoin>true</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout/>
<customUrl>/log-pub/business/rest/query</customUrl>
<fixedUrl>/yonbip/digitalModel</fixedUrl>
<apiCode>e179ea7bd91047d2a6c77b95a3d2817d</apiCode>
<tokenCheckType>0</tokenCheckType>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL/>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>00001951-7ca3-47ac-a462-d5a66e3e6724</updateUserId>
<updateUserName>***********</updateUserName>
<paramIsForce/>
<userIDPassthrough>false</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.iuap-apcom-auditlog</microServiceCode>
<applicationCode>iuap-apcom-auditlog-yms</applicationCode>
<privacyCategory>0</privacyCategory>
<privacyLevel>0</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType>0</serviceType>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType/>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>false</arrayParam>
<fileSize/>
<cc>false</cc>
<paramTransferMode>2</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId>1938847820720111622</apiDefId>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>1</paramExtRequest>
<paramExtResponse>1</paramExtResponse>
<paramExtInExtendKey>1</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene/>
<apiType/>
<paramMark/>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>false</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>false</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>1</chargeStatus>
<beforeSpeed>60</beforeSpeed>
<afterSpeed>120</afterSpeed>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath/>
<pubHistory>
<pubHistory>
<id>2302761404614574081</id>
<apiId>e179ea7bd91047d2a6c77b95a3d2817d</apiId>
<apiName>业务日志</apiName>
<applyReason/>
<publishUserName/>
<version>20250630174510</version>
<operationTime>2025-06-30</operationTime>
<gmtCreate/>
<gmtUpdate/>
<changes>
<changes>
<changePosition>baseInfo</changePosition>
<newList/>
<updateList>
<updateList>
<changeProperty>enableMulti</changeProperty>
<oldValue/>
<newValue>false</newValue>
</updateList>
</updateList>
<deleteList/>
</changes>
<changes>
<changePosition>paramDTOS</changePosition>
<newList/>
<updateList>
<updateList>
<changeProperty>content</changeProperty>
<oldValue>{"id":"2046138827645911405","name":"content","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2046138827645911331","array":false,"paramDesc":"日志内容","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_content","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2302753673694412809","name":"content","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2302753673694412810","array":false,"paramDesc":"日志内容","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_content","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>busiObjType</changeProperty>
<oldValue>{"id":"2046138827645911406","name":"busiObjType","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2046138827645911332","array":false,"paramDesc":"业务对象类型","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_busiObjType","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2302753673694412811","name":"busiObjType","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2302753673694412812","array":false,"paramDesc":"业务对象类型","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_busiObjType","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>busiObjCode</changeProperty>
<oldValue>{"id":"2046138827645911407","name":"busiObjCode","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2046138827645911333","array":false,"paramDesc":"业务对象编码","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_busiObjCode","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2302753673694412813","name":"busiObjCode","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2302753673694412814","array":false,"paramDesc":"业务对象编码","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_busiObjCode","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>busiObjName</changeProperty>
<oldValue>{"id":"2046138827645911408","name":"busiObjName","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2046138827645911334","array":false,"paramDesc":"业务对象名称","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_busiObjName","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2302753673694412815","name":"busiObjName","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2302753673694412816","array":false,"paramDesc":"业务对象名称","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_busiObjName","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>operator</changeProperty>
<oldValue>{"id":"2046138827645911409","name":"operator","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2046138827645911335","array":false,"paramDesc":"操作人(id)，多个用逗号分隔","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_operator","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2302753673694412817","name":"operator","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2302753673694412818","array":false,"paramDesc":"操作人(id)，多个用逗号分隔","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_operator","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>startDate</changeProperty>
<oldValue>{"id":"2046138827645911410","name":"startDate","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2046138827645911336","array":false,"paramDesc":"开始时间（时间戳）","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_startDate","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2302753673694412819","name":"startDate","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2302753673694412820","array":false,"paramDesc":"开始时间（时间戳）","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_startDate","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>endDate</changeProperty>
<oldValue>{"id":"2046138827645911411","name":"endDate","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2046138827645911337","array":false,"paramDesc":"结束时间（时间戳）","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_endDate","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2302753673694412821","name":"endDate","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2302753673694412822","array":false,"paramDesc":"结束时间（时间戳）","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_endDate","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>page</changeProperty>
<oldValue>{"id":"2046138827645911412","name":"page","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2046138827645911338","array":false,"paramDesc":"页码","paramType":"int","requestParamType":"QueryParam","path":"QueryParam_page","example":"1","required":true,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2302753673694412823","name":"page","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2302753673694412824","array":false,"paramDesc":"页码","paramType":"int","requestParamType":"QueryParam","path":"QueryParam_page","example":"1","ytenantId":"0","required":true,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>size</changeProperty>
<oldValue>{"id":"2046138827645911413","name":"size","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2046138827645911339","array":false,"paramDesc":"每页数量","paramType":"int","requestParamType":"QueryParam","path":"QueryParam_size","example":"10","required":true,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2302753673694412825","name":"size","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2302753673694412826","array":false,"paramDesc":"每页数量","paramType":"int","requestParamType":"QueryParam","path":"QueryParam_size","example":"10","ytenantId":"0","required":true,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>operNameResid</changeProperty>
<oldValue>{"id":"2046138827645911415","name":"operNameResid","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2046138827645911341","array":false,"paramDesc":"操作类型","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_operNameResid","example":"","required":false,"defaultValue":"","visible":true}</oldValue>
<newValue>{"id":"2302753673694412829","name":"operNameResid","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2302753673694412830","array":false,"paramDesc":"操作类型","paramType":"string","requestParamType":"QueryParam","path":"QueryParam_operNameResid","example":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false}</newValue>
</updateList>
</updateList>
<deleteList/>
</changes>
<changes>
<changePosition>paramReturnDTOS</changePosition>
<newList>
<newList>
<changeProperty>displayCode</changeProperty>
<oldValue/>
<newValue>{"id":"2302753673694412877","name":"displayCode","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2302753673694412878","array":false,"paramDesc":"异常码","paramType":"string","path":"null_displayCode","example":"","ytenantId":"0"}</newValue>
</newList>
<newList>
<changeProperty>level</changeProperty>
<oldValue/>
<newValue>{"id":"2302753673694412879","name":"level","apiId":"e179ea7bd91047d2a6c77b95a3d2817d","defParamId":"2302753673694412880","array":false,"paramDesc":"异常等级","paramType":"number","path":"null_level","example":"","ytenantId":"0"}</newValue>
</newList>
</newList>
<updateList/>
<deleteList/>
</changes>
</changes>
</pubHistory>
</pubHistory>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode>A13_AuditlogBusiness</domainAppCode>
<multiVersion>0</multiVersion>
<apiTag/>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>2108770660671029249</id>
<name>用友YonBIP</name>
<type>integrateSys</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>PFC</id>
<name>数字化建模</name>
<type>1</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>PF</id>
<name>数字化建模</name>
<type>2</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>GZTSYS</id>
<name>系统管理</name>
<type>3</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>A13_AuditlogBusiness</id>
<name>审计日志业务日志</name>
<type>4</type>
<sort>0</sort>
<enable>0</enable>
<children/>
<parentId/>
<productId/>
<code>A13_AuditlogBusiness</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>GZTSYS</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>PF</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>PFC</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>current_yonbip_default_sys</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<isOrigin>0</isOrigin>
<hasChildren>0</hasChildren>
<order>0</order>
</data>
</ResultVO>
