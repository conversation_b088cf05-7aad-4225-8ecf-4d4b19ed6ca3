<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>YS-API 字段配置中心 - 新架构版本</title>
  
  <!-- 迁移标识 -->
  <meta name="migration-status" content="migrated-to-v3-architecture">
  <meta name="migration-date" content="2025-08-02">
  <meta name="migration-from" content="field-config-manual.html">
  
  <!-- 新架构核心文件 -->
  <script src="../js/core/component-manager.js"></script>
  <script src="../js/core/app-bootstrap.js"></script>
  <script src="../js/api-config-fix.js"></script>
  
  <!-- 所需组件 -->
  <script src="../js/common/api-client.js"></script>
  <script src="../js/common/field-utils.js"></script>
  <script src="../js/common/validation-utils.js"></script>
  <script src="../js/common/error-handler.js"></script>
  <script src="../js/notification-system.js"></script>
  <script src="../field-deduplication-enhancer.js"></script>
  
  <style>
    /* 基础样式重置 */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    html, body {
      height: 100%;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
    }

    /* 主容器 */
    .container {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      max-width: 1400px;
      margin: 0 auto;
      padding: 20px;
      gap: 20px;
    }

    /* 迁移标识样式 */
    .migration-badge {
      position: fixed;
      top: 10px;
      right: 10px;
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 12px;
      font-weight: 600;
      z-index: 9999;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      cursor: help;
      transition: all 0.2s ease;
    }

    .migration-badge:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    }

    /* 页面标题 */
    .page-header {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      padding: 24px 32px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      text-align: center;
    }

    .page-title {
      font-size: 28px;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 8px;
    }

    .page-subtitle {
      font-size: 16px;
      color: #7f8c8d;
      font-weight: 400;
    }

    /* 模块选择区域 */
    .module-selection {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      padding: 24px 32px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .section-icon {
      font-size: 24px;
    }

    .module-controls {
      display: flex;
      gap: 16px;
      align-items: center;
      flex-wrap: wrap;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      min-width: 200px;
    }

    .form-label {
      font-size: 14px;
      font-weight: 500;
      color: #555;
    }

    .form-select {
      padding: 12px 16px;
      border: 2px solid #e1e8ed;
      border-radius: 12px;
      font-size: 16px;
      background: white;
      transition: all 0.3s ease;
      outline: none;
    }

    .form-select:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-select.has-selection {
      border-color: #27ae60;
      background-color: #f8fff9;
    }

    .btn {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    /* 字段配置区域 */
    .field-config-area {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      padding: 24px 32px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      flex: 1;
    }

    .field-list {
      max-height: 400px;
      overflow-y: auto;
      border: 1px solid #e1e8ed;
      border-radius: 8px;
      background: white;
    }

    .field-item {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .field-item:last-child {
      border-bottom: none;
    }

    .field-name {
      font-weight: 500;
      color: #2c3e50;
    }

    .field-chinese {
      color: #7f8c8d;
      font-size: 14px;
    }

    /* 操作按钮区域 */
    .action-buttons {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
      margin-top: 20px;
    }

    /* 进度指示器 */
    .progress-container {
      margin: 20px 0;
    }

    .progress-bar {
      width: 100%;
      height: 8px;
      background: #e1e8ed;
      border-radius: 4px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #667eea, #764ba2);
      width: 0%;
      transition: width 0.3s ease;
    }

    /* 状态消息 */
    .status-message {
      padding: 12px 16px;
      border-radius: 8px;
      margin: 10px 0;
      font-weight: 500;
    }

    .status-success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status-error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .status-info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    /* 隐藏元素 */
    .hidden {
      display: none;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .container {
        padding: 10px;
        gap: 15px;
      }
      
      .module-controls {
        flex-direction: column;
        align-items: stretch;
      }
      
      .action-buttons {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
  <!-- 迁移标识 -->
  <div class="migration-badge" title="此页面已迁移到YS-API V3.0新架构">
    ✅ 新架构版本
  </div>

  <div class="container">
    <!-- 页面标题 -->
    <header class="page-header">
      <h1 class="page-title">YS-API 字段配置中心</h1>
      <p class="page-subtitle">统一管理各模块字段配置，支持基线保存和用户定制</p>
    </header>

    <!-- 模块选择区域 -->
    <section class="module-selection">
      <h2 class="section-title">
        <span class="section-icon">🎯</span>
        模块选择
      </h2>
      <div class="module-controls">
        <div class="form-group">
          <label for="moduleSelect" class="form-label">选择业务模块：</label>
          <select id="moduleSelect" class="form-select">
            <option value="">请选择模块...</option>
          </select>
        </div>
        <button id="loadFieldsBtn" class="btn" disabled>加载字段配置</button>
      </div>
      
      <!-- 进度指示器 -->
      <div id="progressContainer" class="progress-container hidden">
        <div class="progress-bar">
          <div id="progressFill" class="progress-fill"></div>
        </div>
        <div id="progressText" class="status-message status-info">准备加载...</div>
      </div>
    </section>

    <!-- 字段配置区域 -->
    <section class="field-config-area">
      <h2 class="section-title">
        <span class="section-icon">⚙️</span>
        字段配置
      </h2>
      
      <!-- 状态消息 -->
      <div id="statusMessage" class="status-message status-info hidden">
        请先选择模块并加载字段配置
      </div>
      
      <!-- 字段列表 -->
      <div id="fieldList" class="field-list hidden">
        <!-- 字段项目将在这里动态生成 -->
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-buttons">
        <button id="saveBaselineBtn" class="btn" disabled>保存基线配置</button>
        <button id="saveUserConfigBtn" class="btn" disabled>保存用户配置</button>
        <button id="exportConfigBtn" class="btn" disabled>导出配置</button>
        <button id="resetFieldsBtn" class="btn" disabled>重置字段</button>
      </div>
    </section>
  </div>

  <!-- 新架构初始化脚本 -->
  <script>
    // 全局变量
    let currentModule = '';
    let rawFieldData = [];
    let enhancedFieldData = [];
    let isLoading = false;

    // 组件实例（通过新架构获取）
    let apiClient;
    let fieldUtils;
    let validator;
    let errorHandler;
    let notifier;
    let enhancer;

    document.addEventListener('DOMContentLoaded', async function() {
      console.log('🚀 初始化新架构字段配置页面');
      
      try {
        // 启动应用
        await window.startApp({
          environment: 'production',
          features: {
            validation: true,
            errorHandling: true,
            notifications: true,
            fieldDeduplication: true
          }
        });
        
        // 获取所需组件
        apiClient = window.ComponentManager.get('apiClient');
        fieldUtils = window.ComponentManager.get('fieldUtils');
        validator = window.ComponentManager.get('validationUtils');
        errorHandler = window.ComponentManager.get('errorHandler');
        notifier = window.ComponentManager.get('notificationSystem');
        enhancer = window.ComponentManager.get('fieldDeduplicationEnhancer');
        
        console.log('✅ 所有组件加载完成');
        
        // 调用原始初始化逻辑
        await initializePage();
        
        console.log('✅ 页面初始化完成');
        
      } catch (error) {
        console.error('❌ 新架构初始化失败:', error);
        showError('页面初始化失败: ' + error.message);
      }
    });

    // 原始初始化逻辑（已适配新架构）
    async function initializePage() {
      try {
        // 初始化各个组件
        await initializeModuleSelector();
        initializeEventListeners();
        
        showInfo('页面加载完成，请选择模块开始配置');
        
      } catch (error) {
        console.error('页面初始化失败:', error);
        await errorHandler.handleError(error, {
          source: 'page_initialization',
          operation: 'initialize'
        });
        showError('页面初始化失败，请刷新页面重试');
      }
    }

    // 初始化模块选择器
    async function initializeModuleSelector() {
      try {
        console.log('🔄 初始化模块选择器');
        
        // 使用API客户端获取模块列表
        const modules = await apiClient.get('/api/modules');
        
        const moduleSelect = document.getElementById('moduleSelect');
        moduleSelect.innerHTML = '<option value="">请选择模块...</option>';
        
        modules.forEach(module => {
          const option = document.createElement('option');
          option.value = module.value;
          option.textContent = module.label;
          option.title = module.description || '业务模块字段配置';
          moduleSelect.appendChild(option);
        });
        
        console.log(`✅ 模块选择器初始化完成，加载了 ${modules.length} 个模块`);
        
      } catch (error) {
        console.error('❌ 模块选择器初始化失败:', error);
        showError('加载模块列表失败: ' + error.message);
      }
    }

    // 初始化事件监听器
    function initializeEventListeners() {
      const moduleSelect = document.getElementById('moduleSelect');
      const loadFieldsBtn = document.getElementById('loadFieldsBtn');
      const saveBaselineBtn = document.getElementById('saveBaselineBtn');
      const saveUserConfigBtn = document.getElementById('saveUserConfigBtn');
      const exportConfigBtn = document.getElementById('exportConfigBtn');
      const resetFieldsBtn = document.getElementById('resetFieldsBtn');

      // 模块选择变化
      moduleSelect.addEventListener('change', handleModuleChange);
      
      // 按钮事件
      loadFieldsBtn.addEventListener('click', handleLoadFields);
      saveBaselineBtn.addEventListener('click', handleSaveBaseline);
      saveUserConfigBtn.addEventListener('click', handleSaveUserConfig);
      exportConfigBtn.addEventListener('click', handleExportConfig);
      resetFieldsBtn.addEventListener('click', handleResetFields);
    }

    // 处理模块选择变化
    function handleModuleChange(event) {
      const selectedModule = event.target.value;
      const loadBtn = document.getElementById('loadFieldsBtn');
      
      if (selectedModule) {
        currentModule = selectedModule;
        loadBtn.disabled = false;
        event.target.classList.add('has-selection');
        showInfo(`已选择模块: ${selectedModule}`);
      } else {
        currentModule = '';
        loadBtn.disabled = true;
        event.target.classList.remove('has-selection');
        resetUI();
      }
    }

    // 处理加载字段
    async function handleLoadFields() {
      if (!currentModule) {
        showError('请先选择模块');
        return;
      }

      if (isLoading) {
        showError('加载已在进行中');
        return;
      }

      try {
        isLoading = true;
        showProgress(true);
        updateProgress(10, '准备加载字段配置...');
        
        // 禁用控件
        setControlsDisabled(true);
        
        updateProgress(30, '正在获取字段配置...');
        
        // 使用API客户端获取字段数据
        const response = await apiClient.get(`/api/fields/${currentModule}`);
        
        updateProgress(60, '正在处理字段数据...');
        
        // 使用字段工具处理数据
        rawFieldData = response.fields || [];
        
        // 使用字段去重增强器处理
        if (enhancer) {
          enhancedFieldData = await enhancer.enhanceFields(rawFieldData);
        } else {
          enhancedFieldData = rawFieldData;
        }
        
        updateProgress(90, '正在渲染字段列表...');
        
        // 渲染字段列表
        renderFieldList(enhancedFieldData);
        
        updateProgress(100, '字段加载完成');
        
        setTimeout(() => {
          showProgress(false);
          showSuccess(`成功加载 ${enhancedFieldData.length} 个字段`);
          setActionButtonsEnabled(true);
        }, 500);
        
      } catch (error) {
        console.error('❌ 加载字段失败:', error);
        await errorHandler.handleError(error, {
          source: 'field_loading',
          operation: 'load_fields',
          module: currentModule
        });
        showError('加载字段失败: ' + error.message);
        showProgress(false);
      } finally {
        isLoading = false;
        setControlsDisabled(false);
      }
    }

    // 渲染字段列表
    function renderFieldList(fields) {
      const fieldList = document.getElementById('fieldList');
      
      if (!fields || fields.length === 0) {
        fieldList.innerHTML = '<div class="field-item">暂无字段数据</div>';
        fieldList.classList.remove('hidden');
        return;
      }
      
      fieldList.innerHTML = fields.map(field => `
        <div class="field-item" data-field-name="${field.name}">
          <div>
            <div class="field-name">${field.name}</div>
            <div class="field-chinese">${field.chinese || '未设置中文名'}</div>
          </div>
          <div class="field-actions">
            <input type="checkbox" ${field.visible !== false ? 'checked' : ''} 
                   onchange="toggleFieldVisibility('${field.name}', this.checked)">
          </div>
        </div>
      `).join('');
      
      fieldList.classList.remove('hidden');
      hideStatusMessage();
    }

    // 切换字段可见性
    function toggleFieldVisibility(fieldName, visible) {
      const field = enhancedFieldData.find(f => f.name === fieldName);
      if (field) {
        field.visible = visible;
        console.log(`字段 ${fieldName} 可见性设置为: ${visible}`);
      }
    }

    // 处理保存基线配置
    async function handleSaveBaseline() {
      try {
        showInfo('正在保存基线配置...');
        
        const baselineData = {
          module: currentModule,
          fields: enhancedFieldData,
          timestamp: new Date().toISOString(),
          type: 'baseline'
        };
        
        await apiClient.post('/api/config/baseline', baselineData);
        
        showSuccess('基线配置保存成功');
        
      } catch (error) {
        console.error('❌ 保存基线配置失败:', error);
        await errorHandler.handleError(error, {
          source: 'baseline_save',
          operation: 'save_baseline',
          module: currentModule
        });
        showError('保存基线配置失败: ' + error.message);
      }
    }

    // 处理保存用户配置
    async function handleSaveUserConfig() {
      try {
        showInfo('正在保存用户配置...');
        
        const userConfigData = {
          module: currentModule,
          fields: enhancedFieldData,
          timestamp: new Date().toISOString(),
          type: 'user_config',
          userId: 'current_user'
        };
        
        await apiClient.post('/api/config/user', userConfigData);
        
        showSuccess('用户配置保存成功');
        
      } catch (error) {
        console.error('❌ 保存用户配置失败:', error);
        await errorHandler.handleError(error, {
          source: 'user_config_save',
          operation: 'save_user_config',
          module: currentModule
        });
        showError('保存用户配置失败: ' + error.message);
      }
    }

    // 处理导出配置
    function handleExportConfig() {
      try {
        const configData = {
          module: currentModule,
          fields: enhancedFieldData,
          exportTime: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(configData, null, 2)], {
          type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${currentModule}_field_config.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        showSuccess('配置导出成功');
        
      } catch (error) {
        console.error('❌ 导出配置失败:', error);
        showError('导出配置失败: ' + error.message);
      }
    }

    // 处理重置字段
    function handleResetFields() {
      if (confirm('确定要重置所有字段配置吗？')) {
        enhancedFieldData = [...rawFieldData];
        renderFieldList(enhancedFieldData);
        showInfo('字段配置已重置');
      }
    }

    // 工具函数
    function showProgress(show) {
      const container = document.getElementById('progressContainer');
      if (show) {
        container.classList.remove('hidden');
      } else {
        container.classList.add('hidden');
      }
    }

    function updateProgress(percent, text) {
      const fill = document.getElementById('progressFill');
      const textEl = document.getElementById('progressText');
      
      fill.style.width = percent + '%';
      textEl.textContent = text;
    }

    function setControlsDisabled(disabled) {
      document.getElementById('moduleSelect').disabled = disabled;
      document.getElementById('loadFieldsBtn').disabled = disabled;
    }

    function setActionButtonsEnabled(enabled) {
      document.getElementById('saveBaselineBtn').disabled = !enabled;
      document.getElementById('saveUserConfigBtn').disabled = !enabled;
      document.getElementById('exportConfigBtn').disabled = !enabled;
      document.getElementById('resetFieldsBtn').disabled = !enabled;
    }

    function resetUI() {
      document.getElementById('fieldList').classList.add('hidden');
      showStatusMessage('请先选择模块并加载字段配置');
      setActionButtonsEnabled(false);
      rawFieldData = [];
      enhancedFieldData = [];
    }

    function showStatusMessage(message) {
      const statusEl = document.getElementById('statusMessage');
      statusEl.textContent = message;
      statusEl.className = 'status-message status-info';
      statusEl.classList.remove('hidden');
    }

    function hideStatusMessage() {
      document.getElementById('statusMessage').classList.add('hidden');
    }

    function showSuccess(message) {
      if (notifier) {
        notifier.success(message);
      } else {
        const statusEl = document.getElementById('statusMessage');
        statusEl.textContent = message;
        statusEl.className = 'status-message status-success';
        statusEl.classList.remove('hidden');
      }
    }

    function showError(message) {
      if (notifier) {
        notifier.error(message);
      } else {
        const statusEl = document.getElementById('statusMessage');
        statusEl.textContent = message;
        statusEl.className = 'status-message status-error';
        statusEl.classList.remove('hidden');
      }
    }

    function showInfo(message) {
      if (notifier) {
        notifier.info(message);
      } else {
        const statusEl = document.getElementById('statusMessage');
        statusEl.textContent = message;
        statusEl.className = 'status-message status-info';
        statusEl.classList.remove('hidden');
      }
    }
  </script>
</body>
</html>
