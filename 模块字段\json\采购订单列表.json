[{"success": true, "code": 200, "message": "", "data": {"fieldVersion": 20230210, "appCode": "", "tokenSet": false, "tokenDoc": "", "tenantId": 0, "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "id": "b20cfa042e5848309e96c689158c17d1", "name": "采购订单列表查询", "apiClassifyId": 1936116925877714946, "apiClassifyName": "采购订单", "apiClassifyCode": "", "parentApiClassifies": "", "functionId": "", "openMode": 0, "description": "根据表头模式还是表头明细模式、分页条件和自定义条件查询采购订单列表数据信息", "auth": true, "bodyPassthrough": false, "healthExam": false, "healthStatus": true, "responseResultPassthrough": false, "contentType": "application/json", "returnPassthrough": "", "completeProxyUrl": "/yonbip/scm/purchaseorder/list", "connectUrl": "/bill/list", "sort": 20, "handler": "openapi", "httpRequestType": "POST", "openApi": true, "preset": false, "productId": "710a0be3edff4f9092e35f63fd3b9bae", "productCode": "scm", "proxyUrl": "/yonbip/scm/purchaseorder/list", "requestParamsDemo": "Url: /yonbip/scm/purchaseorder/list?access_token=访问令牌 Body: { \"pageIndex\": 1, \"pageSize\": 10, \"isSum\": false, \"simpleVOs\": [ { \"field\": \"code\", \"op\": \"eq\", \"value1\": \"CGA20005000456\" } ], \"queryOrders\": [ { \"field\": \"id\", \"order\": \"asc\" } ] }", "requestProtocol": "HTTP", "serviceHttpMethod": "POST", "publishStatus": true, "approvalMsg": "", "rpcAppName": "", "rpcServiceName": "", "rpcMethodName": "", "rpcServiceUrl": "", "ma": false, "gmtCreate": "2020-01-16 16:52:41", "gmtUpdate": "2024-09-23 13:57:10.000", "address": "https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/purchaseorder/list", "productName": "采购供应", "productClassifyId": "yonsuite", "productClassifyCode": "yonbip", "productClassifyName": "用友 YonBIP", "paramDTOS": {"paramDTOS": [{"id": 2094836196761927695, "name": "pageIndex", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": "", "defParamId": 1998541234691375110, "array": false, "paramDesc": "页码", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": 1, "required": true, "visible": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2094836196761927696, "name": "pageSize", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": "", "defParamId": 1998541234691375111, "array": false, "paramDesc": "每页数", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": 10, "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": 10, "required": true, "visible": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2094836196761927697, "name": "isSum", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": "", "defParamId": 1998541234691375112, "array": false, "paramDesc": "查询表头", "paramType": "boolean", "requestParamType": "BodyParam", "path": "", "example": false, "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": false, "required": false, "visible": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2094836196761927688, "name": "simpleVOs", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": "", "children": {"children": [{"id": 2094836196761927689, "name": "field", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927688, "defParamId": 1998541234691375114, "array": false, "paramDesc": "查询字段（条件）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "code", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2094836196761927690, "name": "op", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927688, "defParamId": 1998541234691375115, "array": false, "paramDesc": "比较符：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "eq", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2094836196761927691, "name": "value1", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927688, "defParamId": 1998541234691375116, "array": false, "paramDesc": "参数值1", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "CGA20005000456", "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "defParamId": 1998541234691375113, "array": true, "paramDesc": "查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2094836196761927692, "name": "queryOrders", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": "", "children": {"children": [{"id": 2094836196761927693, "name": "field", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927692, "defParamId": 1998541234691375118, "array": false, "paramDesc": "排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：purchaseOrders.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "id", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2094836196761927694, "name": "order", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927692, "defParamId": 1998541234691375119, "array": false, "paramDesc": "顺序：正序(asc)、倒序(desc)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "asc", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "defParamId": 1998541234691375117, "array": true, "paramDesc": "排序字段", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "queryParamDTOS": "", "ysApi": false, "presetTokenApi": false, "applyFlag": false, "cover": false, "paramMapDTOS": {"paramMapDTOS": [{"id": 2094836196761927705, "name": "pageIndex", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": "", "defParamId": "", "array": false, "paramDesc": "页码", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "pageIndex", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2094836196761927706, "name": "pageSize", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": "", "defParamId": "", "array": false, "paramDesc": "每页数", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "pageSize", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2094836196761927707, "name": "isSum", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": "", "defParamId": "", "array": false, "paramDesc": "查询表头", "paramType": "boolean", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "isSum", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "boolean", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2094836196761927698, "name": "simpleVOs", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": "", "children": {"children": [{"id": 2094836196761927699, "name": "field", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927698, "defParamId": "", "array": false, "paramDesc": "查询字段（条件）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "field", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2094836196761927700, "name": "op", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927698, "defParamId": "", "array": false, "paramDesc": "比较符：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "op", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2094836196761927701, "name": "value1", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927698, "defParamId": "", "array": false, "paramDesc": "参数值1", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "value1", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "defParamId": "", "array": false, "paramDesc": "查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "simpleVOs", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2094836196761927702, "name": "queryOrders", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": "", "children": {"children": [{"id": 2094836196761927703, "name": "field", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927702, "defParamId": "", "array": false, "paramDesc": "排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：purchaseOrders.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "field", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2094836196761927704, "name": "order", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927702, "defParamId": "", "array": false, "paramDesc": "顺序：正序(asc)、倒序(desc)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "order", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "defParamId": "", "array": false, "paramDesc": "排序字段", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "aggregatedValueObject": false, "mapName": "queryOrders", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "paramReturnDTOS": {"paramReturnDTOS": [{"id": 2094836196761927820, "name": "code", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": "", "defParamId": 1998541234691375130, "array": false, "paramDesc": "编码", "paramType": "string", "requestParamType": "", "path": "", "example": 200, "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927821, "name": "message", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": "", "defParamId": 1998541234691375131, "array": false, "paramDesc": "返回信息", "paramType": "string", "requestParamType": "", "path": "", "example": "操作成功", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927708, "name": "data", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": "", "children": {"children": [{"id": 2094836196761927813, "name": "pageIndex", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927708, "defParamId": 1998541234691375133, "array": false, "paramDesc": "页码", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927814, "name": "pageSize", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927708, "defParamId": 1998541234691375134, "array": false, "paramDesc": "每页数", "paramType": "long", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927815, "name": "recordCount", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927708, "defParamId": 1998541234691375135, "array": false, "paramDesc": "数量", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927709, "name": "recordList", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927708, "children": {"children": [{"id": 2094836196761927713, "name": "product_cCode", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375137, "array": false, "paramDesc": "物料编码", "paramType": "string", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927714, "name": "invoiceVendor", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375138, "array": false, "paramDesc": "开票供应商id", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927715, "name": "priceUOM_Precision", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375139, "array": false, "paramDesc": "计价单位精度", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927716, "name": "modifyStatus", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375140, "array": false, "paramDesc": "变更状态：0：未变更、1：变更中、2：变更完成", "paramType": "long", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927717, "name": "receiveStatus", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375141, "array": false, "paramDesc": "收货状态：0：开立、1：已审核、10：已入库、11：待入库、12：待下单14：待结算15：已结算2：已关闭、3：审核中、4：锁定、5：未发货、6：已发货、7：已完成、8：待收、9：已收齐", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927718, "name": "listOriSum", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375142, "array": false, "paramDesc": "含税金额", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927719, "name": "priceUOM_Code", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375143, "array": false, "paramDesc": "计价单位编码", "paramType": "string", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927720, "name": "totalInTaxMoney", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375144, "array": false, "paramDesc": "累计入库含税金额", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927721, "name": "totalQuantity", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375145, "array": false, "paramDesc": "整单数量", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927722, "name": "natCurrency", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375146, "array": false, "paramDesc": "本币", "paramType": "string", "requestParamType": "", "path": "", "example": "G001ZM0000DEFAULTCURRENCT00000000001", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927723, "name": "listTotalPayOriMoney", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375147, "array": false, "paramDesc": "累计付款核销金额", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927724, "name": "unit_code", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375148, "array": false, "paramDesc": "主计量编码", "paramType": "string", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927728, "name": "id", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375152, "array": false, "paramDesc": "主表id", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927729, "name": "isWfControlled", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375153, "array": false, "paramDesc": "是否审批流控制：true or false", "paramType": "boolean", "requestParamType": "", "path": "", "example": false, "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927730, "name": "totalArrivedTaxMoney", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375154, "array": false, "paramDesc": "累计到货含税金额", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927731, "name": "purchaseOrders_arrivedStatus", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375155, "array": false, "paramDesc": "到货状态：1：到货完成、2：未到货、3：部分到货、4：到货完成", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927732, "name": "bmake_st_purinvoice", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375156, "array": false, "paramDesc": "流程订货订单开蓝票", "paramType": "boolean", "requestParamType": "", "path": "", "example": true, "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927733, "name": "realProductAttribute", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375157, "array": false, "paramDesc": "实物商品属性", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 20, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927734, "name": "purchaseOrders_inWHStatus", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375158, "array": false, "paramDesc": "入库状态：1：入库完成、2：未入库、3：部分入库、4：入库结束", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 21, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927735, "name": "totalSendQty", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375159, "array": false, "paramDesc": "发货数量", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 22, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927736, "name": "natCurrency_priceDigit", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375160, "array": false, "paramDesc": "本币", "paramType": "long", "requestParamType": "", "path": "", "example": 6, "fullName": "", "ytenantId": "", "paramOrder": 23, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927737, "name": "bmake_st_purinrecord_red", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375161, "array": false, "paramDesc": "流程退库", "paramType": "boolean", "requestParamType": "", "path": "", "example": true, "fullName": "", "ytenantId": "", "paramOrder": 24, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927738, "name": "status", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375162, "array": false, "paramDesc": "状态：0：开立、1：已审核、2：已关闭、3：审核中", "paramType": "long", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 25, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927739, "name": "currency_moneyDigit", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375163, "array": false, "paramDesc": "本币金额精度", "paramType": "long", "requestParamType": "", "path": "", "example": 6, "fullName": "", "ytenantId": "", "paramOrder": 26, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927740, "name": "listTotalPayApplyAmount", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375164, "array": false, "paramDesc": "累计付款申请金额", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 27, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927741, "name": "currency_code", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375165, "array": false, "paramDesc": "币种编码", "paramType": "string", "requestParamType": "", "path": "", "example": "CNY", "fullName": "", "ytenantId": "", "paramOrder": 28, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927742, "name": "vouchdate", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375166, "array": false, "paramDesc": "单据日期，格式为:yyyy-MM-dd HH:mm:ss", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-04-23 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 29, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927743, "name": "invoiceVendor_name", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375167, "array": false, "paramDesc": "开票供应商", "paramType": "string", "requestParamType": "", "path": "", "example": "达利园供货目录转移专用供应商", "fullName": "", "ytenantId": "", "paramOrder": 30, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927744, "name": "vendor", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375168, "array": false, "paramDesc": "供货供应商id", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 31, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927745, "name": "purchaseOrders_payStatus", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375169, "array": false, "paramDesc": "核销状态：1：核销完成、2：未核销、3：部分核销", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 32, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927746, "name": "purchaseOrders_warehouse_code", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375170, "array": false, "paramDesc": "仓库编码", "paramType": "string", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 33, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927747, "name": "listOriMoney", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375171, "array": false, "paramDesc": "无税金额", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 34, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927748, "name": "currency", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375172, "array": false, "paramDesc": "币种", "paramType": "string", "requestParamType": "", "path": "", "example": "G001ZM0000DEFAULTCURRENCT00000000001", "fullName": "", "ytenantId": "", "paramOrder": 35, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927749, "name": "pubts", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375173, "array": false, "paramDesc": "时间戳，格式为:yyyy-MM-dd HH:mm:ss", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-04-23 11:34:02", "fullName": "", "ytenantId": "", "paramOrder": 36, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927750, "name": "org_name", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375174, "array": false, "paramDesc": "采购组织", "paramType": "string", "requestParamType": "", "path": "", "example": "eflong", "fullName": "", "ytenantId": "", "paramOrder": 37, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927751, "name": "generalPurchaseOrderType", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375175, "array": false, "paramDesc": "交易类型扩展参数", "paramType": "string", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 38, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927752, "name": "isFlowCoreBill", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375176, "array": false, "paramDesc": "是否流程核心单据", "paramType": "boolean", "requestParamType": "", "path": "", "example": true, "fullName": "", "ytenantId": "", "paramOrder": 39, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927753, "name": "creator", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375177, "array": false, "paramDesc": "创建者", "paramType": "string", "requestParamType": "", "path": "", "example": 17600880447, "fullName": "", "ytenantId": "", "paramOrder": 40, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927754, "name": "product", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375178, "array": false, "paramDesc": "物料id", "paramType": "long", "requestParamType": "", "path": "", "example": 1730491724599552, "fullName": "", "ytenantId": "", "paramOrder": 41, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927755, "name": "oriSum", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375179, "array": false, "paramDesc": "含税金额", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 42, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927756, "name": "inInvoiceOrg_name", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375180, "array": false, "paramDesc": "收票组织", "paramType": "string", "requestParamType": "", "path": "", "example": "eflong", "fullName": "", "ytenantId": "", "paramOrder": 43, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927757, "name": "product_defaultAlbumId", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375181, "array": false, "paramDesc": "物料首图片", "paramType": "string", "requestParamType": "", "path": "", "example": "http://ys-yxy-testres.yonyoucloud.com/fa813c9d-a182-457c-ab2a-a0c40ab113ea.jpg", "fullName": "", "ytenantId": "", "paramOrder": 44, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927758, "name": "purchaseOrders_id", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375182, "array": false, "paramDesc": "订单行id", "paramType": "long", "requestParamType": "", "path": "", "example": 2227385816011009, "fullName": "", "ytenantId": "", "paramOrder": 45, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927759, "name": "totalRecieveQty", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375183, "array": false, "paramDesc": "累计到货数量", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 46, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927760, "name": "demandOrg_name", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375184, "array": false, "paramDesc": "需求组织", "paramType": "string", "requestParamType": "", "path": "", "example": "eflong", "fullName": "", "ytenantId": "", "paramOrder": 47, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927761, "name": "createTime", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375185, "array": false, "paramDesc": "创建时间，格式为:yyyy-MM-dd HH:mm:ss", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-04-23 11:34:01", "fullName": "", "ytenantId": "", "paramOrder": 48, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927762, "name": "purUOM_Precision", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375186, "array": false, "paramDesc": "采购单位精度", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 49, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927763, "name": "currency_priceDigit", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375187, "array": false, "paramDesc": "币种单价精度", "paramType": "long", "requestParamType": "", "path": "", "example": 6, "fullName": "", "ytenantId": "", "paramOrder": 50, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927764, "name": "bEffectStock", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375188, "array": false, "paramDesc": "影响可用量", "paramType": "boolean", "requestParamType": "", "path": "", "example": true, "fullName": "", "ytenantId": "", "paramOrder": 51, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927765, "name": "inOrg", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375189, "array": false, "paramDesc": "收货组织id", "paramType": "string", "requestParamType": "", "path": "", "example": 1730475987734784, "fullName": "", "ytenantId": "", "paramOrder": 52, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927766, "name": "bustype_name", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375190, "array": false, "paramDesc": "交易类型", "paramType": "string", "requestParamType": "", "path": "", "example": "普通订货-订单开票", "fullName": "", "ytenantId": "", "paramOrder": 53, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927767, "name": "purchaseOrders_invPriceExchRate", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375191, "array": false, "paramDesc": "计价单位换算率", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 54, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 19, "enableMulti": false}, {"id": 2094836196761927768, "name": "subQty", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375192, "array": false, "paramDesc": "采购数量", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 55, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927769, "name": "inInvoiceOrg", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375193, "array": false, "paramDesc": "收票组织id", "paramType": "string", "requestParamType": "", "path": "", "example": 1730475987734784, "fullName": "", "ytenantId": "", "paramOrder": 56, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927770, "name": "product_cName", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375194, "array": false, "paramDesc": "物料名称", "paramType": "string", "requestParamType": "", "path": "", "example": "eflong-规格1", "fullName": "", "ytenantId": "", "paramOrder": 57, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927771, "name": "listTaxRate", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375195, "array": false, "paramDesc": "税率", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 58, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 19, "enableMulti": false}, {"id": 2094836196761927772, "name": "bmake_st_purinvoice_red", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375196, "array": false, "paramDesc": "流程订货订单开红票", "paramType": "boolean", "requestParamType": "", "path": "", "example": true, "fullName": "", "ytenantId": "", "paramOrder": 59, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927773, "name": "product_model", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375197, "array": false, "paramDesc": "型号", "paramType": "string", "requestParamType": "", "path": "", "example": "型号", "fullName": "", "ytenantId": "", "paramOrder": 60, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927774, "name": "storagenum", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375198, "array": false, "paramDesc": "已入库数量", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 61, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927775, "name": "purchaseOrders_invExchRate", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375199, "array": false, "paramDesc": "采购换算率", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 62, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 19, "enableMulti": false}, {"id": 2094836196761927776, "name": "vendor_name", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375200, "array": false, "paramDesc": "供应商", "paramType": "string", "requestParamType": "", "path": "", "example": "达利园供货目录转移专用供应商", "fullName": "", "ytenantId": "", "paramOrder": 63, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927777, "name": "vendor_code", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375201, "array": false, "paramDesc": "供应商编码", "paramType": "string", "requestParamType": "", "path": "", "example": 1000101, "fullName": "", "ytenantId": "", "paramOrder": 64, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927778, "name": "oriUnitPrice", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375202, "array": false, "paramDesc": "无税单价", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 65, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927779, "name": "barCode", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375203, "array": false, "paramDesc": "单据条码", "paramType": "string", "requestParamType": "", "path": "", "example": "st_purchaseorder|****************", "fullName": "", "ytenantId": "", "paramOrder": 66, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927780, "name": "isContract", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375204, "array": false, "paramDesc": "是否需要与供应商协同：true：是、false：否", "paramType": "boolean", "requestParamType": "", "path": "", "example": false, "fullName": "", "ytenantId": "", "paramOrder": 67, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927781, "name": "unit_name", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375205, "array": false, "paramDesc": "主计量", "paramType": "string", "requestParamType": "", "path": "", "example": "个", "fullName": "", "ytenantId": "", "paramOrder": 68, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927782, "name": "unit", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375206, "array": false, "paramDesc": "主计量id", "paramType": "long", "requestParamType": "", "path": "", "example": 1730486466924800, "fullName": "", "ytenantId": "", "paramOrder": 69, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927783, "name": "purchaseOrders_invoiceStatus", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375207, "array": false, "paramDesc": "发票状态：1：开票完成、2：未开票、3：部分开票、4：开票结束", "paramType": "long", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 70, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927784, "name": "natCurrency_moneyDigit", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375208, "array": false, "paramDesc": "本币", "paramType": "long", "requestParamType": "", "path": "", "example": 6, "fullName": "", "ytenantId": "", "paramOrder": 71, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927785, "name": "qty", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375209, "array": false, "paramDesc": "数量", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 72, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927786, "name": "unit_Precision", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375210, "array": false, "paramDesc": "主计量精度", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 73, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927787, "name": "oriTaxUnitPrice", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375211, "array": false, "paramDesc": "含税单价", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 74, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927788, "name": "moneysum", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375212, "array": false, "paramDesc": "金额", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 75, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927789, "name": "natCurrency_code", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375213, "array": false, "paramDesc": "本币", "paramType": "string", "requestParamType": "", "path": "", "example": "CNY", "fullName": "", "ytenantId": "", "paramOrder": 76, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927790, "name": "product_modelDescription", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375214, "array": false, "paramDesc": "规格说明", "paramType": "string", "requestParamType": "", "path": "", "example": "说明2", "fullName": "", "ytenantId": "", "paramOrder": 77, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927791, "name": "code", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375215, "array": false, "paramDesc": "单据编码", "paramType": "string", "requestParamType": "", "path": "", "example": "CGA20005000456", "fullName": "", "ytenantId": "", "paramOrder": 78, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927792, "name": "demandOrg", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375216, "array": false, "paramDesc": "需求组织id", "paramType": "string", "requestParamType": "", "path": "", "example": 1730475987734784, "fullName": "", "ytenantId": "", "paramOrder": 79, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927793, "name": "bizFlow", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375217, "array": false, "paramDesc": "流程ID", "paramType": "string", "requestParamType": "", "path": "", "example": "f596bd30-aee8-11ea-8d5f-0624d60000dc", "fullName": "", "ytenantId": "", "paramOrder": 80, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927794, "name": "realProductAttributeType", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375218, "array": false, "paramDesc": "实物商品属性", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 81, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927795, "name": "priceUOM", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375219, "array": false, "paramDesc": "计价单位id", "paramType": "long", "requestParamType": "", "path": "", "example": 1730486466924800, "fullName": "", "ytenantId": "", "paramOrder": 82, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927796, "name": "bi<PERSON><PERSON><PERSON>", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375220, "array": false, "paramDesc": "状态：0：初始开立、1：审批中、2：审批完成、3：不通过流程终止、4：驳回到制单", "paramType": "long", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 83, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927797, "name": "totalInQty", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375221, "array": false, "paramDesc": "累计入库数量", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 84, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927798, "name": "bizFlow_version", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375222, "array": false, "paramDesc": "版本信息", "paramType": "string", "requestParamType": "", "path": "", "example": "V1.0", "fullName": "", "ytenantId": "", "paramOrder": 85, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927799, "name": "currency_name", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375223, "array": false, "paramDesc": "币种", "paramType": "string", "requestParamType": "", "path": "", "example": "人民币", "fullName": "", "ytenantId": "", "paramOrder": 86, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927800, "name": "org", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375224, "array": false, "paramDesc": "采购组织", "paramType": "string", "requestParamType": "", "path": "", "example": 1730475987734784, "fullName": "", "ytenantId": "", "paramOrder": 87, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927801, "name": "bmake_st_purinrecord", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375225, "array": false, "paramDesc": "流程入库", "paramType": "boolean", "requestParamType": "", "path": "", "example": true, "fullName": "", "ytenantId": "", "paramOrder": 88, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927802, "name": "purchaseOrders_purUOM", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375226, "array": false, "paramDesc": "采购单位编码", "paramType": "long", "requestParamType": "", "path": "", "example": 1730486466924800, "fullName": "", "ytenantId": "", "paramOrder": 89, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927803, "name": "bustype", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375227, "array": false, "paramDesc": "交易类型id", "paramType": "string", "requestParamType": "", "path": "", "example": 1785637352591616, "fullName": "", "ytenantId": "", "paramOrder": 90, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927804, "name": "listOriTax", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375228, "array": false, "paramDesc": "税额", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 91, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927805, "name": "retailInvestors", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375229, "array": false, "paramDesc": "是否散户：true or false", "paramType": "boolean", "requestParamType": "", "path": "", "example": false, "fullName": "", "ytenantId": "", "paramOrder": 92, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927806, "name": "inOrg_name", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375230, "array": false, "paramDesc": "收货组织", "paramType": "string", "requestParamType": "", "path": "", "example": "eflong", "fullName": "", "ytenantId": "", "paramOrder": 93, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927807, "name": "listTotalPayAmount", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375231, "array": false, "paramDesc": "累计付款金额", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 94, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927808, "name": "priceUOM_Name", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375232, "array": false, "paramDesc": "计价单位名称", "paramType": "string", "requestParamType": "", "path": "", "example": "个", "fullName": "", "ytenantId": "", "paramOrder": 95, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927809, "name": "listTotalPayNATMoney", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375233, "array": false, "paramDesc": "本币累计付款核销金额", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 96, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927810, "name": "approvenum", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375234, "array": false, "paramDesc": "已审批数量", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 97, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2094836196761927811, "name": "listdiscountTaxType", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375235, "array": false, "paramDesc": "扣税类别：0：应税外加、1：应税内含", "paramType": "string", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 98, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927812, "name": "bizFlow_name", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "defParamId": 1998541234691375236, "array": false, "paramDesc": "流程名称", "paramType": "string", "requestParamType": "", "path": "", "example": "普通订货（订单开票）", "fullName": "", "ytenantId": "", "paramOrder": 99, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927710, "name": "headItem", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927709, "children": {"children": [{"id": 2094836196761927711, "name": "id", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927710, "defParamId": 1998541234691375238, "array": false, "paramDesc": "id", "paramType": "long", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927712, "name": "define46", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927710, "defParamId": 1998541234691375239, "array": false, "paramDesc": "自定义项", "paramType": "string", "requestParamType": "", "path": "", "example": "红色", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1998541234691375237, "array": false, "paramDesc": "表头自定义项", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 100, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1998541234691375136, "array": true, "paramDesc": "返回信息", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927816, "name": "pageCount", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927708, "defParamId": 1998541234691375240, "array": false, "paramDesc": "页数", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927817, "name": "beginPageIndex", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927708, "defParamId": 1998541234691375241, "array": false, "paramDesc": "起始页", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927818, "name": "endPageIndex", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927708, "defParamId": 1998541234691375242, "array": false, "paramDesc": "结束页", "paramType": "long", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2094836196761927819, "name": "pubts", "apiId": "b20cfa042e5848309e96c689158c17d1", "parentId": 2094836196761927708, "defParamId": 1998541234691375243, "array": false, "paramDesc": "时间戳，格式为:yyyy-MM-dd HH:mm:ss", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-04-23 12:40:06", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1998541234691375132, "array": false, "paramDesc": "数据项", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "returnFormatType": "JSON", "paramConstDTOS": "", "paramConstMapDTOS": "", "apiDemoReturnDTOS": {"apiDemoReturnDTOS": [{"id": 2094836196761927827, "apiId": "b20cfa042e5848309e96c689158c17d1", "content": "{ \"code\": \"200\", \"message\": \"操作成功\", \"data\": { \"pageIndex\": 1, \"pageSize\": 10, \"recordCount\": 1, \"recordList\": [ { \"product_cCode\": \"********\", \"invoiceVendor\": ****************, \"priceUOM_Precision\": 1, \"modifyStatus\": 0, \"receiveStatus\": 1, \"listOriSum\": 1, \"priceUOM_Code\": \"001\", \"totalInTaxMoney\": 0, \"totalQuantity\": 1, \"natCurrency\": \"G001ZM0000DEFAULTCURRENCT00000000001\", \"listTotalPayOriMoney\": 0, \"unit_code\": \"001\", \"purchaseOrdersCharacteristics\": 0, \"purchaseOrdersDefineCharacter\": 0, \"purchaseOrderDefineCharacter\": 0, \"id\": ****************, \"isWfControlled\": false, \"totalArrivedTaxMoney\": 0, \"purchaseOrders_arrivedStatus\": 2, \"bmake_st_purinvoice\": true, \"realProductAttribute\": 1, \"purchaseOrders_inWHStatus\": 2, \"totalSendQty\": 0, \"natCurrency_priceDigit\": 6, \"bmake_st_purinrecord_red\": true, \"status\": null, \"currency_moneyDigit\": 6, \"listTotalPayApplyAmount\": 0, \"currency_code\": \"CNY\", \"vouchdate\": \"2021-04-23 00:00:00\", \"invoiceVendor_name\": \"达利园供货目录转移专用供应商\", \"vendor\": ****************, \"purchaseOrders_payStatus\": 2, \"purchaseOrders_warehouse_code\": \"000001\", \"listOriMoney\": 1, \"currency\": \"G001ZM0000DEFAULTCURRENCT00000000001\", \"pubts\": \"2021-04-23 11:34:02\", \"org_name\": \"eflong\", \"generalPurchaseOrderType\": \"1\", \"isFlowCoreBill\": true, \"creator\": \"17600880447\", \"product\": 1730491724599552, \"oriSum\": 1, \"inInvoiceOrg_name\": \"eflong\", \"product_defaultAlbumId\": \"http://ys-yxy-testres.yonyoucloud.com/fa813c9d-a182-457c-ab2a-a0c40ab113ea.jpg\", \"purchaseOrders_id\": 2227385816011009, \"totalRecieveQty\": 0, \"demandOrg_name\": \"eflong\", \"createTime\": \"2021-04-23 11:34:01\", \"purUOM_Precision\": 1, \"currency_priceDigit\": 6, \"bEffectStock\": true, \"inOrg\": \"1730475987734784\", \"bustype_name\": \"普通订货-订单开票\", \"purchaseOrders_invPriceExchRate\": 1, \"subQty\": 1, \"inInvoiceOrg\": \"1730475987734784\", \"product_cName\": \"eflong-规格1\", \"listTaxRate\": 0, \"bmake_st_purinvoice_red\": true, \"product_model\": \"型号\", \"storagenum\": 0, \"purchaseOrders_invExchRate\": 1, \"vendor_name\": \"达利园供货目录转移专用供应商\", \"vendor_code\": \"0001000101\", \"oriUnitPrice\": 1, \"barCode\": \"st_purchaseorder|****************\", \"isContract\": false, \"unit_name\": \"个\", \"unit\": 1730486466924800, \"purchaseOrders_invoiceStatus\": 2, \"natCurrency_moneyDigit\": 6, \"qty\": 1, \"unit_Precision\": 1, \"oriTaxUnitPrice\": 1, \"moneysum\": 1, \"natCurrency_code\": \"CNY\", \"product_modelDescription\": \"说明2\", \"code\": \"CGA20005000456\", \"demandOrg\": \"1730475987734784\", \"bizFlow\": \"f596bd30-aee8-11ea-8d5f-0624d60000dc\", \"realProductAttributeType\": 1, \"priceUOM\": 1730486466924800, \"bizstatus\": 0, \"totalInQty\": 0, \"bizFlow_version\": \"V1.0\", \"currency_name\": \"人民币\", \"org\": \"1730475987734784\", \"bmake_st_purinrecord\": true, \"purchaseOrders_purUOM\": 1730486466924800, \"bustype\": \"1785637352591616\", \"listOriTax\": 0, \"retailInvestors\": false, \"inOrg_name\": \"eflong\", \"listTotalPayAmount\": 0, \"priceUOM_Name\": \"个\", \"listTotalPayNATMoney\": 0, \"approvenum\": 0, \"listdiscountTaxType\": \"0\", \"bizFlow_name\": \"普通订货（订单开票）\", \"headItem\": { \"id\": ****************, \"define46\": \"红色\" } } ], \"pageCount\": 1, \"beginPageIndex\": 1, \"endPageIndex\": 1, \"pubts\": \"2021-04-23 12:40:06\" } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2094836196761927828, "apiId": "b20cfa042e5848309e96c689158c17d1", "content": "{ \"code\": 999, \"message\": \"服务端逻辑异常\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "apiDemoReturnDTOList": {"apiDemoReturnDTOList": [{"id": 2094836196761927827, "apiId": "b20cfa042e5848309e96c689158c17d1", "content": "{ \"code\": \"200\", \"message\": \"操作成功\", \"data\": { \"pageIndex\": 1, \"pageSize\": 10, \"recordCount\": 1, \"recordList\": [ { \"product_cCode\": \"********\", \"invoiceVendor\": ****************, \"priceUOM_Precision\": 1, \"modifyStatus\": 0, \"receiveStatus\": 1, \"listOriSum\": 1, \"priceUOM_Code\": \"001\", \"totalInTaxMoney\": 0, \"totalQuantity\": 1, \"natCurrency\": \"G001ZM0000DEFAULTCURRENCT00000000001\", \"listTotalPayOriMoney\": 0, \"unit_code\": \"001\", \"purchaseOrdersCharacteristics\": 0, \"purchaseOrdersDefineCharacter\": 0, \"purchaseOrderDefineCharacter\": 0, \"id\": ****************, \"isWfControlled\": false, \"totalArrivedTaxMoney\": 0, \"purchaseOrders_arrivedStatus\": 2, \"bmake_st_purinvoice\": true, \"realProductAttribute\": 1, \"purchaseOrders_inWHStatus\": 2, \"totalSendQty\": 0, \"natCurrency_priceDigit\": 6, \"bmake_st_purinrecord_red\": true, \"status\": null, \"currency_moneyDigit\": 6, \"listTotalPayApplyAmount\": 0, \"currency_code\": \"CNY\", \"vouchdate\": \"2021-04-23 00:00:00\", \"invoiceVendor_name\": \"达利园供货目录转移专用供应商\", \"vendor\": ****************, \"purchaseOrders_payStatus\": 2, \"purchaseOrders_warehouse_code\": \"000001\", \"listOriMoney\": 1, \"currency\": \"G001ZM0000DEFAULTCURRENCT00000000001\", \"pubts\": \"2021-04-23 11:34:02\", \"org_name\": \"eflong\", \"generalPurchaseOrderType\": \"1\", \"isFlowCoreBill\": true, \"creator\": \"17600880447\", \"product\": 1730491724599552, \"oriSum\": 1, \"inInvoiceOrg_name\": \"eflong\", \"product_defaultAlbumId\": \"http://ys-yxy-testres.yonyoucloud.com/fa813c9d-a182-457c-ab2a-a0c40ab113ea.jpg\", \"purchaseOrders_id\": 2227385816011009, \"totalRecieveQty\": 0, \"demandOrg_name\": \"eflong\", \"createTime\": \"2021-04-23 11:34:01\", \"purUOM_Precision\": 1, \"currency_priceDigit\": 6, \"bEffectStock\": true, \"inOrg\": \"1730475987734784\", \"bustype_name\": \"普通订货-订单开票\", \"purchaseOrders_invPriceExchRate\": 1, \"subQty\": 1, \"inInvoiceOrg\": \"1730475987734784\", \"product_cName\": \"eflong-规格1\", \"listTaxRate\": 0, \"bmake_st_purinvoice_red\": true, \"product_model\": \"型号\", \"storagenum\": 0, \"purchaseOrders_invExchRate\": 1, \"vendor_name\": \"达利园供货目录转移专用供应商\", \"vendor_code\": \"0001000101\", \"oriUnitPrice\": 1, \"barCode\": \"st_purchaseorder|****************\", \"isContract\": false, \"unit_name\": \"个\", \"unit\": 1730486466924800, \"purchaseOrders_invoiceStatus\": 2, \"natCurrency_moneyDigit\": 6, \"qty\": 1, \"unit_Precision\": 1, \"oriTaxUnitPrice\": 1, \"moneysum\": 1, \"natCurrency_code\": \"CNY\", \"product_modelDescription\": \"说明2\", \"code\": \"CGA20005000456\", \"demandOrg\": \"1730475987734784\", \"bizFlow\": \"f596bd30-aee8-11ea-8d5f-0624d60000dc\", \"realProductAttributeType\": 1, \"priceUOM\": 1730486466924800, \"bizstatus\": 0, \"totalInQty\": 0, \"bizFlow_version\": \"V1.0\", \"currency_name\": \"人民币\", \"org\": \"1730475987734784\", \"bmake_st_purinrecord\": true, \"purchaseOrders_purUOM\": 1730486466924800, \"bustype\": \"1785637352591616\", \"listOriTax\": 0, \"retailInvestors\": false, \"inOrg_name\": \"eflong\", \"listTotalPayAmount\": 0, \"priceUOM_Name\": \"个\", \"listTotalPayNATMoney\": 0, \"approvenum\": 0, \"listdiscountTaxType\": \"0\", \"bizFlow_name\": \"普通订货（订单开票）\", \"headItem\": { \"id\": ****************, \"define46\": \"红色\" } } ], \"pageCount\": 1, \"beginPageIndex\": 1, \"endPageIndex\": 1, \"pubts\": \"2021-04-23 12:40:06\" } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2094836196761927828, "apiId": "b20cfa042e5848309e96c689158c17d1", "content": "{ \"code\": 999, \"message\": \"服务端逻辑异常\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "routingStgy": 0, "routingStgyList": "", "apiDemoReturnDTO": {"id": 2094836196761927827, "apiId": "b20cfa042e5848309e96c689158c17d1", "content": "{ \"code\": \"200\", \"message\": \"操作成功\", \"data\": { \"pageIndex\": 1, \"pageSize\": 10, \"recordCount\": 1, \"recordList\": [ { \"product_cCode\": \"********\", \"invoiceVendor\": ****************, \"priceUOM_Precision\": 1, \"modifyStatus\": 0, \"receiveStatus\": 1, \"listOriSum\": 1, \"priceUOM_Code\": \"001\", \"totalInTaxMoney\": 0, \"totalQuantity\": 1, \"natCurrency\": \"G001ZM0000DEFAULTCURRENCT00000000001\", \"listTotalPayOriMoney\": 0, \"unit_code\": \"001\", \"purchaseOrdersCharacteristics\": 0, \"purchaseOrdersDefineCharacter\": 0, \"purchaseOrderDefineCharacter\": 0, \"id\": ****************, \"isWfControlled\": false, \"totalArrivedTaxMoney\": 0, \"purchaseOrders_arrivedStatus\": 2, \"bmake_st_purinvoice\": true, \"realProductAttribute\": 1, \"purchaseOrders_inWHStatus\": 2, \"totalSendQty\": 0, \"natCurrency_priceDigit\": 6, \"bmake_st_purinrecord_red\": true, \"status\": null, \"currency_moneyDigit\": 6, \"listTotalPayApplyAmount\": 0, \"currency_code\": \"CNY\", \"vouchdate\": \"2021-04-23 00:00:00\", \"invoiceVendor_name\": \"达利园供货目录转移专用供应商\", \"vendor\": ****************, \"purchaseOrders_payStatus\": 2, \"purchaseOrders_warehouse_code\": \"000001\", \"listOriMoney\": 1, \"currency\": \"G001ZM0000DEFAULTCURRENCT00000000001\", \"pubts\": \"2021-04-23 11:34:02\", \"org_name\": \"eflong\", \"generalPurchaseOrderType\": \"1\", \"isFlowCoreBill\": true, \"creator\": \"17600880447\", \"product\": 1730491724599552, \"oriSum\": 1, \"inInvoiceOrg_name\": \"eflong\", \"product_defaultAlbumId\": \"http://ys-yxy-testres.yonyoucloud.com/fa813c9d-a182-457c-ab2a-a0c40ab113ea.jpg\", \"purchaseOrders_id\": 2227385816011009, \"totalRecieveQty\": 0, \"demandOrg_name\": \"eflong\", \"createTime\": \"2021-04-23 11:34:01\", \"purUOM_Precision\": 1, \"currency_priceDigit\": 6, \"bEffectStock\": true, \"inOrg\": \"1730475987734784\", \"bustype_name\": \"普通订货-订单开票\", \"purchaseOrders_invPriceExchRate\": 1, \"subQty\": 1, \"inInvoiceOrg\": \"1730475987734784\", \"product_cName\": \"eflong-规格1\", \"listTaxRate\": 0, \"bmake_st_purinvoice_red\": true, \"product_model\": \"型号\", \"storagenum\": 0, \"purchaseOrders_invExchRate\": 1, \"vendor_name\": \"达利园供货目录转移专用供应商\", \"vendor_code\": \"0001000101\", \"oriUnitPrice\": 1, \"barCode\": \"st_purchaseorder|****************\", \"isContract\": false, \"unit_name\": \"个\", \"unit\": 1730486466924800, \"purchaseOrders_invoiceStatus\": 2, \"natCurrency_moneyDigit\": 6, \"qty\": 1, \"unit_Precision\": 1, \"oriTaxUnitPrice\": 1, \"moneysum\": 1, \"natCurrency_code\": \"CNY\", \"product_modelDescription\": \"说明2\", \"code\": \"CGA20005000456\", \"demandOrg\": \"1730475987734784\", \"bizFlow\": \"f596bd30-aee8-11ea-8d5f-0624d60000dc\", \"realProductAttributeType\": 1, \"priceUOM\": 1730486466924800, \"bizstatus\": 0, \"totalInQty\": 0, \"bizFlow_version\": \"V1.0\", \"currency_name\": \"人民币\", \"org\": \"1730475987734784\", \"bmake_st_purinrecord\": true, \"purchaseOrders_purUOM\": 1730486466924800, \"bustype\": \"1785637352591616\", \"listOriTax\": 0, \"retailInvestors\": false, \"inOrg_name\": \"eflong\", \"listTotalPayAmount\": 0, \"priceUOM_Name\": \"个\", \"listTotalPayNATMoney\": 0, \"approvenum\": 0, \"listdiscountTaxType\": \"0\", \"bizFlow_name\": \"普通订货（订单开票）\", \"headItem\": { \"id\": ****************, \"define46\": \"红色\" } } ], \"pageCount\": 1, \"beginPageIndex\": 1, \"endPageIndex\": 1, \"pubts\": \"2021-04-23 12:40:06\" } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, "apiDemoReturnDTOError": {"id": 2094836196761927828, "apiId": "b20cfa042e5848309e96c689158c17d1", "content": "{ \"code\": 999, \"message\": \"服务端逻辑异常\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}, "errorCodeDTOS": {"errorCodeDTOS": {"id": 2094836196761927824, "apiId": "b20cfa042e5848309e96c689158c17d1", "errorCode": 999, "errorMessage": "服务端逻辑异常", "errorType": "API", "errorcodeDesc": "", "gmtCreate": "2024-09-23 13:57:10.000", "gmtUpdate": "2024-09-23 13:57:10.000", "apiName": "", "edit": false, "defErrorId": 1998541234691375358, "ytenantId": "", "displayCodeId": ""}}, "displayCodeApiConfigDTOS": "", "tokenPlugin": "", "paramParsePlugin": "", "authPlugin": {"id": "09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "", "name": "友户通token认证业务扩展插件", "configurable": false, "description": "YonsuitBusinessExtendPlugin", "pluginType": "auth", "pluginTypeName": "业务扩展插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": false, "gmtCreate": "2020-05-22 00:00:00", "gmtUpdate": "2020-05-22 00:00:00", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "b20cfa042e5848309e96c689158c17d1", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "resultParsePlugin": {"id": "w181ed01-1e9b-4350-b994-71a66f017555", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "resultParse", "name": "返回参数转换插件", "configurable": false, "description": "解决返回值中带！的，转换为json", "pluginType": "resultParse", "pluginTypeName": "返回值解析插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.result.ResultMapTransferParsePlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": true, "gmtCreate": "2020-07-29 00:00:00", "gmtUpdate": "", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "b20cfa042e5848309e96c689158c17d1", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "mapReturnPluginConfig": "", "billNo": "st_purchaseorderlist", "domain": "upu", "apiCategory": "", "docUrl": "", "pathMatch": 0, "createUser": "", "createUserName": "", "approvalStatus": 1, "publishTime": "2024-09-23 13:57:57", "pathJoin": true, "timeOut": 30, "tokenPluginName": "", "authPluginName": "", "resultPluginName": "", "apiDemoReturnRightDemo": "", "apiDemoReturnErrorDemo": "", "mock": false, "mockTimeout": "", "customUrl": "/purchaseorder/list", "fixedUrl": "/yonbip/scm", "apiCode": "b20cfa042e5848309e96c689158c17d1", "tokenCheckType": 0, "enableMulti": false, "multiField": "", "idempotent": "non", "bidirectionalSSL": "", "ucgSchema": "HTTPS", "updateUserId": "53434e39-298d-4db4-8896-cdd073972fff", "updateUserName": "昵称-17600880447", "paramIsForce": "", "userIDPassthrough": false, "applyUser": "", "applyMsg": "", "dr": 0, "microServiceCode": "domain.yonbip-scm-pu", "applicationCode": "yonbip-scm-pubiz", "privacyCategory": 0, "privacyLevel": 3, "apiDesigned": 0, "serviceType": 0, "integrateSchemeCode": "", "integrateSchemeName": "", "integrateObjectCode": "", "integrateObjectName": "", "integrateObjectCreatedType": "", "returnIntegObjId": "", "returnIntegObjName": "", "apiIntegrateDTOList": "", "apiRouteInfoDTOList": "", "arrayParam": false, "fileSize": "", "cc": true, "paramTransferMode": 2, "ytenantId": 0, "statusConf": "", "scene": 1, "version": "", "bizObjUri": "", "bizObjOperationType": "", "apiDefId": 1998541234691375108, "paramExtBizObjCode": "", "paramExtBizObjName": "", "paramExtRequest": 1, "paramExtResponse": 1, "paramExtInExtendKey": 1, "openScene": 1, "integrationScene": "", "apiType": "", "paramMark": "", "integrateSysId": "", "integrateSysName": "", "integrateSysCode": "", "dataZoneSetting": false, "reqDataZoneSetting": false, "respDataZoneSetting": false, "reqDataAllQuery": false, "reqDataAllBody": false, "respDataAllBody": false, "chargeStatus": 1, "beforeSpeed": 60, "afterSpeed": 120, "speedStatus": false, "reqDataRefPath": "", "respDataRefPath": "", "pubHistory": "", "deprecated": 0, "recommendedApiId": "", "recommendedApiName": "", "domainAppCode": "", "multiVersion": 0, "apiTag": ""}}, {"success": true, "code": 200, "message": "", "data": {"id": 2108770660671029249, "name": "用友YonBIP", "type": "integrateSys", "sort": 0, "enable": 0, "children": {"children": {"id": "SCC", "name": "供应链云", "type": 1, "sort": 0, "enable": 0, "children": {"children": {"id": "MM", "name": "采购供应", "type": 2, "sort": 0, "enable": 0, "children": {"children": {"id": "PU", "name": "采购管理", "type": 3, "sort": 0, "enable": 0, "children": {"children": {"id": "upu.st_purchaseorder", "name": "采购订单", "type": 4, "sort": 0, "enable": 0, "children": "", "parentId": "", "productId": "", "code": "upu.st_purchaseorder", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "PU", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "MM", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "SCC", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "current_yonbip_default_sys", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "isOrigin": 0, "hasChildren": 0, "order": 0}}, {"success": true, "code": 200, "message": "", "data": [{"id": "60182ff8-30c4-46a4-96c6-3ce0bebe4fee", "name": "U9002", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "U9采购订单号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:09:56", "gmtUpdate": "2025-07-26 17:09:56", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 100, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "8c558603-24fe-4b30-86ba-bcf97d4f8599", "name": "XS31", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "更改次数", "paramType": "Decimal", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "number", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:09:56", "gmtUpdate": "2025-07-26 17:09:56", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 24, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:09:56", "gmtUpdate": "2025-07-26 17:09:56", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": [{"id": "31be15d2-ceb1-4d59-a557-3351a8d14ffe", "name": "AA", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次号（扩展）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:14:41", "gmtUpdate": "2025-07-26 17:14:41", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "7421f1ce-8268-44b1-a8eb-49ffdcff5afb", "name": "CG00025", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "未收数量2", "paramType": "Decimal", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "number", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:14:41", "gmtUpdate": "2025-07-26 17:14:41", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 24, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "176f1ed5-5d35-4fa7-9e52-a513b8e5b169", "name": "CG01", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "供应商备注（扩展）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:14:41", "gmtUpdate": "2025-07-26 17:14:41", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "15ffdb7a-ab8b-498d-b7c5-55b5089a4dfe", "name": "WW", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "委外交货日期", "paramType": "Date", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "date", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:14:41", "gmtUpdate": "2025-07-26 17:14:41", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "ae2457c2-816f-49ff-a4d5-bf06196d59a6", "name": "XS11", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求分类号test", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:14:41", "gmtUpdate": "2025-07-26 17:14:41", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "a3d1773f-3d33-4a0e-bc02-95f55ec955c9", "name": "XS15", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "顾客订单号（订单表体）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:14:41", "gmtUpdate": "2025-07-26 17:14:41", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:14:41", "gmtUpdate": "2025-07-26 17:14:41", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": [{"id": "60182ff8-30c4-46a4-96c6-3ce0bebe4fee", "name": "U9002", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "U9采购订单号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:14:58", "gmtUpdate": "2025-07-26 17:14:58", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 100, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "8c558603-24fe-4b30-86ba-bcf97d4f8599", "name": "XS31", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "更改次数", "paramType": "Decimal", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "number", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:14:58", "gmtUpdate": "2025-07-26 17:14:58", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 24, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:14:58", "gmtUpdate": "2025-07-26 17:14:58", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": [{"id": "f458f625-a28f-44ee-82dd-07d4e8c6315e", "name": "XS11", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求分类号test", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:15:52", "gmtUpdate": "2025-07-26 17:15:52", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "62cb3b8d-2fe7-4fab-8456-14260b6434cc", "name": "XS15", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "顾客订单号（订单表体）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:15:52", "gmtUpdate": "2025-07-26 17:15:52", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:15:52", "gmtUpdate": "2025-07-26 17:15:52", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": [{"id": "72113971-ae4c-4188-bc55-44b6173f4e0b", "name": "XS15", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "顾客订单号（订单表体）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:16:03", "gmtUpdate": "2025-07-26 17:16:03", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "b946709d-f4d9-4a43-a551-f55beee7f3d5", "name": "XXX0111", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求分类项", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:16:03", "gmtUpdate": "2025-07-26 17:16:03", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:16:03", "gmtUpdate": "2025-07-26 17:16:03", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}]