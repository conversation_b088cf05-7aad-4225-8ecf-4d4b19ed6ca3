# YS-API V3.0 代码所有权配置
# 防止未经审查的重复文件创建

# 全局规则：所有Python文件需要核心团队审查
*.py @ys-api/core-team

# 关键配置文件
config.ini @ys-api/config-team
*.env @ys-api/config-team
docker-compose.yml @ys-api/devops-team
Dockerfile @ys-api/devops-team

# 工具脚本目录 - 防止重复工具创建
*fixer*.py @ys-api/tools-team
*checker*.py @ys-api/tools-team
*builder*.py @ys-api/tools-team
*generator*.py @ys-api/tools-team
*cleaner*.py @ys-api/tools-team

# 测试文件
test_*.py @ys-api/qa-team
*_test.py @ys-api/qa-team

# 部署脚本
deploy*.* @ys-api/devops-team
start_*.* @ys-api/devops-team

# 数据库相关
*.db @ys-api/data-team
migrations/ @ys-api/data-team

# 前端资源
frontend/ @ys-api/frontend-team
*.html @ys-api/frontend-team
*.css @ys-api/frontend-team
*.js @ys-api/frontend-team

# 文档
docs/ @ys-api/docs-team
*.md @ys-api/docs-team

# 防止在这些目录创建新文件（除非经过审查）
backend/services/ @ys-api/backend-team
backend/models/ @ys-api/backend-team
backend/controllers/ @ys-api/backend-team

# 敏感配置
.env* @ys-api/security-team
secrets.* @ys-api/security-team
