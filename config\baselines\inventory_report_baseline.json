{"module_name": "inventory_report", "display_name": "现存量报表", "version": "2.0.0", "source": "json_parser", "total_fields": 125, "created_at": "2025-07-28T20:12:24.845970", "last_updated": "2025-07-28T20:12:24.845970", "fields": {"code": {"api_field_name": "code", "chinese_name": "返回码，调用成功时返回200", "data_type": "NVARCHAR(500)", "param_desc": "返回码，调用成功时返回200", "path": "code", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "message": {"api_field_name": "message", "chinese_name": "调用失败时的错误信息", "data_type": "NVARCHAR(500)", "param_desc": "调用失败时的错误信息", "path": "message", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "调用成功时的返回数据", "data_type": "NVARCHAR(MAX)", "param_desc": "调用成功时的返回数据", "path": "data", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageIndex": {"api_field_name": "pageIndex", "chinese_name": "当前页数", "data_type": "BIGINT", "param_desc": "当前页数", "path": "pageIndex", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageSize": {"api_field_name": "pageSize", "chinese_name": "每页行数", "data_type": "BIGINT", "param_desc": "每页行数", "path": "pageSize", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageCount": {"api_field_name": "pageCount", "chinese_name": "总页数", "data_type": "BIGINT", "param_desc": "总页数", "path": "data.pageCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "beginPageIndex": {"api_field_name": "beginPageIndex", "chinese_name": "开始页码（第一页）", "data_type": "BIGINT", "param_desc": "开始页码（第一页）", "path": "data.beginPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "endPageIndex": {"api_field_name": "endPageIndex", "chinese_name": "结束页码（有多少页）", "data_type": "BIGINT", "param_desc": "结束页码（有多少页）", "path": "data.endPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordCount": {"api_field_name": "recordCount", "chinese_name": "总记录数", "data_type": "BIGINT", "param_desc": "总记录数", "path": "data.recordCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pubts": {"api_field_name": "pubts", "chinese_name": "时间戳,格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "时间戳,格式为:yyyy-MM-dd HH:mm:ss", "path": "data.pubts", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "recordList": {"api_field_name": "recordList", "chinese_name": "数据", "data_type": "NVARCHAR(MAX)", "param_desc": "数据", "path": "data.recordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "id": {"api_field_name": "id", "chinese_name": "主键", "data_type": "NVARCHAR(500)", "param_desc": "主键", "path": "data.recordList.id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "org": {"api_field_name": "org", "chinese_name": "组织主键", "data_type": "NVARCHAR(500)", "param_desc": "组织主键", "path": "org", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "org_name": {"api_field_name": "org_name", "chinese_name": "组织名称", "data_type": "NVARCHAR(500)", "param_desc": "组织名称", "path": "data.recordList.org_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "areaClass": {"api_field_name": "areaClass", "chinese_name": "地区", "data_type": "NVARCHAR(500)", "param_desc": "地区", "path": "data.recordList.areaClass", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "areaClass_name": {"api_field_name": "areaClass_name", "chinese_name": "地区名称", "data_type": "NVARCHAR(500)", "param_desc": "地区名称", "path": "data.recordList.areaClass_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "store": {"api_field_name": "store", "chinese_name": "门店主键", "data_type": "BIGINT", "param_desc": "门店主键", "path": "store", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "store_codebianma": {"api_field_name": "store_codebianma", "chinese_name": "门店编码", "data_type": "NVARCHAR(500)", "param_desc": "门店编码", "path": "data.recordList.store_codebianma", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "store_name": {"api_field_name": "store_name", "chinese_name": "门店名称名称", "data_type": "NVARCHAR(500)", "param_desc": "门店名称名称", "path": "data.recordList.store_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "store_type": {"api_field_name": "store_type", "chinese_name": "门店类型, 1:直营店、2:直营专柜、3:加盟店、", "data_type": "NVARCHAR(500)", "param_desc": "门店类型, 1:直营店、2:直营专柜、3:加盟店、", "path": "data.recordList.store_type", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehouse": {"api_field_name": "warehouse", "chinese_name": "仓库", "data_type": "NVARCHAR(500)", "param_desc": "仓库", "path": "data.recordList.warehouse", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehouse_name": {"api_field_name": "warehouse_name", "chinese_name": "仓库主键", "data_type": "BIGINT", "param_desc": "仓库主键", "path": "warehouse_name", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productClass": {"api_field_name": "productClass", "chinese_name": "物料分类", "data_type": "NVARCHAR(500)", "param_desc": "物料分类", "path": "data.recordList.productClass", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productClass_code": {"api_field_name": "productClass_code", "chinese_name": "物料分类编码", "data_type": "NVARCHAR(500)", "param_desc": "物料分类编码", "path": "data.recordList.productClass_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productClass_name": {"api_field_name": "productClass_name", "chinese_name": "物料分类名称名称", "data_type": "NVARCHAR(500)", "param_desc": "物料分类名称名称", "path": "data.recordList.productClass_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product": {"api_field_name": "product", "chinese_name": "物料", "data_type": "NVARCHAR(500)", "param_desc": "物料", "path": "data.recordList.product", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_cCode": {"api_field_name": "product_cCode", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "product_cCode", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_defaultAlbumId": {"api_field_name": "product_defaultAlbumId", "chinese_name": "物料首图片", "data_type": "NVARCHAR(500)", "param_desc": "物料首图片", "path": "data.recordList.product_defaultAlbumId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_cName": {"api_field_name": "product_cName", "chinese_name": "物料名称", "data_type": "NVARCHAR(500)", "param_desc": "物料名称", "path": "product_cName", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productsku": {"api_field_name": "productsku", "chinese_name": "物料SKU", "data_type": "NVARCHAR(500)", "param_desc": "物料SKU", "path": "data.recordList.productsku", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_modelDescription": {"api_field_name": "product_modelDescription", "chinese_name": "规格型号", "data_type": "NVARCHAR(500)", "param_desc": "规格型号", "path": "data.recordList.product_modelDescription", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productsku_cCode": {"api_field_name": "productsku_cCode", "chinese_name": "物料SKU编码", "data_type": "NVARCHAR(500)", "param_desc": "物料SKU编码", "path": "data.recordList.productsku_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productsku_skuName": {"api_field_name": "productsku_skuName", "chinese_name": "sku名称", "data_type": "NVARCHAR(500)", "param_desc": "sku名称", "path": "productsku_skuName", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "free1": {"api_field_name": "free1", "chinese_name": "物料规格1", "data_type": "NVARCHAR(500)", "param_desc": "物料规格1", "path": "data.recordList.free1", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "free2": {"api_field_name": "free2", "chinese_name": "物料规格2", "data_type": "NVARCHAR(500)", "param_desc": "物料规格2", "path": "data.recordList.free2", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "free3": {"api_field_name": "free3", "chinese_name": "物料规格3", "data_type": "NVARCHAR(500)", "param_desc": "物料规格3", "path": "data.recordList.free3", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "free4": {"api_field_name": "free4", "chinese_name": "物料规格4", "data_type": "NVARCHAR(500)", "param_desc": "物料规格4", "path": "data.recordList.free4", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "free5": {"api_field_name": "free5", "chinese_name": "物料规格5", "data_type": "NVARCHAR(500)", "param_desc": "物料规格5", "path": "data.recordList.free5", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "free6": {"api_field_name": "free6", "chinese_name": "物料规格6", "data_type": "NVARCHAR(500)", "param_desc": "物料规格6", "path": "data.recordList.free6", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "free7": {"api_field_name": "free7", "chinese_name": "物料规格7", "data_type": "NVARCHAR(500)", "param_desc": "物料规格7", "path": "data.recordList.free7", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "free8": {"api_field_name": "free8", "chinese_name": "物料规格8", "data_type": "NVARCHAR(500)", "param_desc": "物料规格8", "path": "data.recordList.free8", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "free9": {"api_field_name": "free9", "chinese_name": "物料规格9", "data_type": "NVARCHAR(500)", "param_desc": "物料规格9", "path": "data.recordList.free9", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "free10": {"api_field_name": "free10", "chinese_name": "物料规格10", "data_type": "NVARCHAR(500)", "param_desc": "物料规格10", "path": "data.recordList.free10", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_productProps": {"api_field_name": "product_productProps", "chinese_name": "物料自定义项", "data_type": "NVARCHAR(MAX)", "param_desc": "物料自定义项", "path": "data.recordList.product_productProps", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define1": {"api_field_name": "define1", "chinese_name": "SKU自定义项1-SKU自定义项60", "data_type": "NVARCHAR(500)", "param_desc": "SKU自定义项1-SKU自定义项60", "path": "data.recordList.product_productSkuProps.define1", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define30": {"api_field_name": "define30", "chinese_name": "物料自定义项30", "data_type": "NVARCHAR(500)", "param_desc": "物料自定义项30", "path": "data.recordList.product_productProps.define30", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_productSkuProps": {"api_field_name": "product_productSkuProps", "chinese_name": "SKU自定义项", "data_type": "NVARCHAR(MAX)", "param_desc": "SKU自定义项", "path": "data.recordList.product_productSkuProps", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define60": {"api_field_name": "define60", "chinese_name": "SKU自定义项1-SKU自定义项60", "data_type": "NVARCHAR(500)", "param_desc": "SKU自定义项1-SKU自定义项60", "path": "data.recordList.product_productSkuProps.define60", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno": {"api_field_name": "batchno", "chinese_name": "批次号", "data_type": "NVARCHAR(500)", "param_desc": "批次号", "path": "batchno", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define1": {"api_field_name": "batchno_define1", "chinese_name": "批次自定义项1", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项1", "path": "batchno_define1", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "producedate": {"api_field_name": "producedate", "chinese_name": "生产日期", "data_type": "NVARCHAR(500)", "param_desc": "生产日期", "path": "data.recordList.producedate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invaliddate": {"api_field_name": "invaliddate", "chinese_name": "有效期至", "data_type": "NVARCHAR(500)", "param_desc": "有效期至", "path": "data.recordList.invaliddate", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unitName": {"api_field_name": "unitName", "chinese_name": "主计量", "data_type": "NVARCHAR(500)", "param_desc": "主计量", "path": "data.recordList.unitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "currentqty": {"api_field_name": "currentqty", "chinese_name": "现存量", "data_type": "BIGINT", "param_desc": "现存量", "path": "data.recordList.currentqty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockmoney": {"api_field_name": "stockmoney", "chinese_name": "库存金额", "data_type": "BIGINT", "param_desc": "库存金额", "path": "data.recordList.stockmoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "availableqty": {"api_field_name": "availableqty", "chinese_name": "可用量", "data_type": "BIGINT", "param_desc": "可用量", "path": "data.recordList.availableqty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "costprice": {"api_field_name": "costprice", "chinese_name": "成本价", "data_type": "BIGINT", "param_desc": "成本价", "path": "data.recordList.costprice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "innoticeqty": {"api_field_name": "innoticeqty", "chinese_name": "收货预计入库量", "data_type": "BIGINT", "param_desc": "收货预计入库量", "path": "data.recordList.innoticeqty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "costmoney": {"api_field_name": "costmoney", "chinese_name": "成本金额", "data_type": "BIGINT", "param_desc": "成本金额", "path": "data.recordList.costmoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "outnoticeqty": {"api_field_name": "outnoticeqty", "chinese_name": "发货预计出库量", "data_type": "BIGINT", "param_desc": "发货预计出库量", "path": "data.recordList.outnoticeqty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "preretailqty": {"api_field_name": "preretailqty", "chinese_name": "订单预计出库量", "data_type": "BIGINT", "param_desc": "订单预计出库量", "path": "data.recordList.preretailqty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "acolytesUnit": {"api_field_name": "acolytesUnit", "chinese_name": "辅计量单位", "data_type": "NVARCHAR(500)", "param_desc": "辅计量单位", "path": "acolytesUnit", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "acolytesUnit_name": {"api_field_name": "acolytesUnit_name", "chinese_name": "辅计量单位名称", "data_type": "NVARCHAR(500)", "param_desc": "辅计量单位名称", "path": "data.recordList.acolytesUnit_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "currentSubQty": {"api_field_name": "currentSubQty", "chinese_name": "现存件数", "data_type": "BIGINT", "param_desc": "现存件数", "path": "data.recordList.currentSubQty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "acolytesUnit_precision": {"api_field_name": "acolytesUnit_precision", "chinese_name": "辅计量单位精度", "data_type": "NVARCHAR(500)", "param_desc": "辅计量单位精度", "path": "data.recordList.acolytesUnit_precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unit_precision": {"api_field_name": "unit_precision", "chinese_name": "主计量单位精度", "data_type": "NVARCHAR(500)", "param_desc": "主计量单位精度", "path": "data.recordList.unit_precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invExchRate": {"api_field_name": "invExchRate", "chinese_name": "换算率", "data_type": "BIGINT", "param_desc": "换算率", "path": "data.recordList.invExchRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "availableSubQty": {"api_field_name": "availableSubQty", "chinese_name": "可用件数", "data_type": "BIGINT", "param_desc": "可用件数", "path": "data.recordList.availableSubQty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "innoticeSubQty": {"api_field_name": "innoticeSubQty", "chinese_name": "入库通知件数", "data_type": "BIGINT", "param_desc": "入库通知件数", "path": "data.recordList.innoticeSubQty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "outnoticeSubQty": {"api_field_name": "outnoticeSubQty", "chinese_name": "出库通知件数", "data_type": "BIGINT", "param_desc": "出库通知件数", "path": "data.recordList.outnoticeSubQty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "preretailSubQty": {"api_field_name": "preretailSubQty", "chinese_name": "预订零售件数", "data_type": "BIGINT", "param_desc": "预订零售件数", "path": "data.recordList.preretailSubQty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product.productClass.name": {"api_field_name": "product.productClass.name", "chinese_name": "物料分类主键", "data_type": "BIGINT", "param_desc": "物料分类主键", "path": "product.productClass.name", "depth": 2, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product.cName": {"api_field_name": "product.cName", "chinese_name": "商品主键", "data_type": "BIGINT", "param_desc": "商品主键", "path": "product.cName", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productsku.skuName": {"api_field_name": "productsku.skuName", "chinese_name": "sku主键", "data_type": "BIGINT", "param_desc": "sku主键", "path": "productsku.skuName", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "open_currentqty_begin": {"api_field_name": "open_currentqty_begin", "chinese_name": "现存量下限", "data_type": "BIGINT", "param_desc": "现存量下限", "path": "open_currentqty_begin", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "open_currentqty_end": {"api_field_name": "open_currentqty_end", "chinese_name": "现存量上限", "data_type": "BIGINT", "param_desc": "现存量上限", "path": "open_currentqty_end", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehouse_names": {"api_field_name": "warehouse_names", "chinese_name": "仓库名称", "data_type": "NVARCHAR(500)", "param_desc": "仓库名称", "path": "warehouse_names", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehouse_code": {"api_field_name": "warehouse_code", "chinese_name": "仓库编码", "data_type": "NVARCHAR(500)", "param_desc": "仓库编码", "path": "warehouse_code", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_productClass_names": {"api_field_name": "product_productClass_names", "chinese_name": "物料分类名称", "data_type": "NVARCHAR(500)", "param_desc": "物料分类名称", "path": "product_productClass_names", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockStatusDoc_status_name": {"api_field_name": "stockStatusDoc_status_name", "chinese_name": "库存状态名称", "data_type": "NVARCHAR(500)", "param_desc": "库存状态名称", "path": "stockStatusDoc_status_name", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_brand": {"api_field_name": "product_brand", "chinese_name": "物料品牌", "data_type": "NVARCHAR(500)", "param_desc": "物料品牌", "path": "product_brand", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_manufacturer": {"api_field_name": "product_manufacturer", "chinese_name": "生产厂商", "data_type": "NVARCHAR(500)", "param_desc": "生产厂商", "path": "product_manufacturer", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_cModel": {"api_field_name": "product_cModel", "chinese_name": "型号", "data_type": "NVARCHAR(500)", "param_desc": "型号", "path": "product_cModel", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "open_validityDistance_begin": {"api_field_name": "open_validityDistance_begin", "chinese_name": "效期天数下限", "data_type": "BIGINT", "param_desc": "效期天数下限", "path": "open_validityDistance_begin", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_validityDistance_end": {"api_field_name": "open_validityDistance_end", "chinese_name": "效期天数上限", "data_type": "BIGINT", "param_desc": "效期天数上限", "path": "open_validityDistance_end", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "batchno_define2": {"api_field_name": "batchno_define2", "chinese_name": "批次自定义项2", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项2", "path": "batchno_define2", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define3": {"api_field_name": "batchno_define3", "chinese_name": "批次自定义项3", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项3", "path": "batchno_define3", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define4": {"api_field_name": "batchno_define4", "chinese_name": "批次自定义项4", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项4", "path": "batchno_define4", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define5": {"api_field_name": "batchno_define5", "chinese_name": "批次自定义项5", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项5", "path": "batchno_define5", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define6": {"api_field_name": "batchno_define6", "chinese_name": "批次自定义项6", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项6", "path": "batchno_define6", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define7": {"api_field_name": "batchno_define7", "chinese_name": "批次自定义项7", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项7", "path": "batchno_define7", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define8": {"api_field_name": "batchno_define8", "chinese_name": "批次自定义项8", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项8", "path": "batchno_define8", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define9": {"api_field_name": "batchno_define9", "chinese_name": "批次自定义项9", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项9", "path": "batchno_define9", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define10": {"api_field_name": "batchno_define10", "chinese_name": "批次自定义项10", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项10", "path": "batchno_define10", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define11": {"api_field_name": "batchno_define11", "chinese_name": "批次自定义项11", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项11", "path": "batchno_define11", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define12": {"api_field_name": "batchno_define12", "chinese_name": "批次自定义项12", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项12", "path": "batchno_define12", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define13": {"api_field_name": "batchno_define13", "chinese_name": "批次自定义项13", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项13", "path": "batchno_define13", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define14": {"api_field_name": "batchno_define14", "chinese_name": "批次自定义项14", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项14", "path": "batchno_define14", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define15": {"api_field_name": "batchno_define15", "chinese_name": "批次自定义项15", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项15", "path": "batchno_define15", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define16": {"api_field_name": "batchno_define16", "chinese_name": "批次自定义项16", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项16", "path": "batchno_define16", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define17": {"api_field_name": "batchno_define17", "chinese_name": "批次自定义项17", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项17", "path": "batchno_define17", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define18": {"api_field_name": "batchno_define18", "chinese_name": "批次自定义项18", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项18", "path": "batchno_define18", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define19": {"api_field_name": "batchno_define19", "chinese_name": "批次自定义项19", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项19", "path": "batchno_define19", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define20": {"api_field_name": "batchno_define20", "chinese_name": "批次自定义项20", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项20", "path": "batchno_define20", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define21": {"api_field_name": "batchno_define21", "chinese_name": "批次自定义项21", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项21", "path": "batchno_define21", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define22": {"api_field_name": "batchno_define22", "chinese_name": "批次自定义项22", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项22", "path": "batchno_define22", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define23": {"api_field_name": "batchno_define23", "chinese_name": "批次自定义项23", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项23", "path": "batchno_define23", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define24": {"api_field_name": "batchno_define24", "chinese_name": "批次自定义项24", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项24", "path": "batchno_define24", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define25": {"api_field_name": "batchno_define25", "chinese_name": "批次自定义项25", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项25", "path": "batchno_define25", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define26": {"api_field_name": "batchno_define26", "chinese_name": "批次自定义项26", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项26", "path": "batchno_define26", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define27": {"api_field_name": "batchno_define27", "chinese_name": "批次自定义项27", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项27", "path": "batchno_define27", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define28": {"api_field_name": "batchno_define28", "chinese_name": "批次自定义项28", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项28", "path": "batchno_define28", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define29": {"api_field_name": "batchno_define29", "chinese_name": "批次自定义项29", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项29", "path": "batchno_define29", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno_define30": {"api_field_name": "batchno_define30", "chinese_name": "批次自定义项30", "data_type": "NVARCHAR(500)", "param_desc": "批次自定义项30", "path": "batchno_define30", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}}}