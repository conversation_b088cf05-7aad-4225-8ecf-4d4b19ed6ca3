# Mock工具使用指南

## 概述
本工具提供统一的Mock机制，用于开发环境下模拟API响应，避免测试代码中重复的Mock逻辑。

## 目录结构
```
dev-tools/mock/
├── mock_utils.py       # Mock工具核心模块
├── mock_data/          # Mock数据存储目录（自动创建）
└── README.md          # 本文件
```

## 快速开始

### 1. 导入Mock工具
```python
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "dev-tools" / "mock"))
from mock_utils import is_mock_mode, get_mock_response
```

### 2. 检查运行模式
```python
if is_mock_mode():
    print("当前运行在Mock模式")
else:
    print("当前运行在生产模式")
```

### 3. 获取Mock响应
```python
# 模拟基准文件保存成功
response = get_mock_response("baseline_save", success=True)
print(f"状态码: {response.status_code}")
print(f"响应数据: {response.json()}")

# 模拟字段配置API
response = get_mock_response("field_config", module_name="sales_order", field_count=50)
```

## 环境模式切换

### 自动检测模式
系统会自动检测当前应该使用哪种模式：

1. **环境变量检测**: 设置 `YS_API_MOCK_MODE=true`
2. **开发标识文件**: 检查项目根目录是否存在 `DEV_MODE` 文件
3. **默认模式**: 默认使用Mock模式（安全优先）

### 手动切换模式
```python
from mock_utils import enable_dev_mode, disable_dev_mode

# 启用开发模式（Mock模式）
enable_dev_mode()

# 禁用开发模式（生产模式）
disable_dev_mode()
```

### 环境变量方式
```bash
# Windows
set YS_API_MOCK_MODE=true

# Linux/Mac
export YS_API_MOCK_MODE=true
```

## 可用的Mock端点

| 端点名称 | 功能 | 参数 |
|---------|------|------|
| `baseline_save` | 基准文件保存 | `success: bool` |
| `user_config_save` | 用户配置保存 | `success: bool` |
| `field_config` | 字段配置获取 | `module_name: str, field_count: int` |
| `network_error` | 网络错误模拟 | 无 |
| `auth_error` | 认证错误模拟 | 无 |

## 使用示例

### 在测试文件中使用
```python
def test_api_functionality():
    if is_mock_mode():
        # Mock模式测试
        response = get_mock_response("baseline_save", success=True)
        assert response.status_code == 200
        print("Mock测试通过")
    else:
        # 真实API测试
        response = requests.post("http://api.example.com/test", json={})
        assert response.status_code == 200
        print("真实API测试通过")
```

### 错误处理测试
```python
def test_error_handling():
    if is_mock_mode():
        # 测试网络错误
        response = get_mock_response("network_error")
        assert response.status_code == 503
        
        # 测试认证错误
        response = get_mock_response("auth_error")
        assert response.status_code == 401
```

## 自定义Mock响应

### 保存自定义Mock数据
```python
from mock_utils import MockAPIClient

mock_client = MockAPIClient()
custom_response = {
    "success": True,
    "message": "自定义响应",
    "data": {"custom_field": "custom_value"}
}

mock_client.save_mock_response("custom_endpoint", custom_response)
```

### 加载自定义Mock数据
```python
custom_data = mock_client.load_mock_response("custom_endpoint")
if custom_data:
    response = MockResponse(200, custom_data)
```

## MockResponse 类

Mock响应对象提供与真实HTTP响应相似的接口：

```python
response = get_mock_response("baseline_save")

# 获取状态码
print(response.status_code)  # 200

# 获取JSON数据
data = response.json()  # 返回字典

# 获取文本内容
print(response.text)  # 字符串

# 获取二进制内容
print(response.content)  # bytes
```

## 最佳实践

### 1. 统一的测试结构
```python
def test_feature():
    """推荐的测试函数结构"""
    print("测试功能...")
    
    if is_mock_mode():
        # Mock模式逻辑
        response = get_mock_response("endpoint_name", **params)
        # 验证Mock响应
    else:
        # 生产模式逻辑
        response = make_real_api_call()
        # 验证真实响应
    
    # 通用验证逻辑
    assert response.status_code == 200
    return True
```

### 2. 环境隔离
- 开发环境：自动使用Mock模式，快速反馈
- 测试环境：可选择性使用Mock或真实API
- 生产环境：强制使用真实API

### 3. Mock数据管理
- 将常用的Mock数据保存到文件
- 定期更新Mock数据以反映API变化
- 为不同测试场景准备不同的Mock数据集

## 故障排除

### 导入错误
如果遇到 `无法解析导入"mock_utils"` 错误：
```python
# 确保路径正确
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "dev-tools" / "mock"))
```

### 模式检测问题
如果模式检测不正确：
1. 检查环境变量 `YS_API_MOCK_MODE`
2. 检查项目根目录的 `DEV_MODE` 文件
3. 手动调用 `enable_dev_mode()` 或 `disable_dev_mode()`

### Mock数据问题
如果Mock响应不符合预期：
1. 检查端点名称是否正确
2. 查看 `mock_data/` 目录下的数据文件
3. 使用 `MockAPIClient` 重新生成Mock数据

## 更新日志

### v1.0.0
- 初始版本
- 支持基本Mock功能
- 自动环境检测
- 统一的Mock响应接口
