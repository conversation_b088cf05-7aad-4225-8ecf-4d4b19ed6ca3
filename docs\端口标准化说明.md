# 端口标准化说明文档

## 📌 端口配置标准

### ✅ 当前标准化端口配置
- **后端服务**: 8050 (固定，禁止改动)
- **前端服务**: 8060 (固定，禁止改动)

### 🚫 已废弃的端口配置
- ~~8000~~ (旧后端端口)
- ~~8080~~ (临时前端端口)
- ~~8001~~ (测试端口)
- ~~9000~~ (代理端口)
- ~~5000~~ (简化端口)

## 🚀 启动方式

### 后端启动
```bash
python backend/start_server_fixed.py
```
- 固定端口: 8050
- 包含完整错误处理
- 生产就绪配置

### 前端启动
```bash
python frontend/start_frontend_fixed.py
```
- 固定端口: 8060
- 自动代理API请求
- 静态文件服务

## 📁 配置文件

### .env
```
PORT=8060
```

### 已更新的文件
- `backend/start_server_fixed.py` - 后端服务启动脚本
- `frontend/start_frontend_fixed.py` - 前端服务启动脚本
- `scripts/port_manager.py` - 端口管理工具
- `backend/app/core/config.py` - 后端配置
- `.env` - 环境配置

### 已废弃的文件
- `backend/start_simple.py` - 简化启动脚本
- `proxy_strangler.py` - 代理层脚本
- 所有 `.bat` 启动脚本

## ⚠️ 重要提醒

1. **禁止修改端口配置** - 所有端口已固定为8050/8060
2. **使用标准启动脚本** - 仅使用`*_fixed.py`脚本
3. **环境配置已锁定** - 不允许添加其他端口配置
4. **文档已同步更新** - 所有文档已更新为新端口

## 🔍 端口检查命令

```bash
# 检查端口占用
netstat -an | findstr "8050\|8060"

# 检查服务状态
curl http://localhost:8050/health
curl http://localhost:8060
```

---
**更新时间**: 2025-08-06  
**状态**: ✅ 端口标准化完成
