import hashlib
import json
import os
import shutil
import subprocess

#!/usr/bin/env python3
"""
项目机器大扫除脚本 - 按用户建议的9步清理方案
"""


class ProjectMachineCleanup:
    def __init___(self):
    """TODO: Add function description."""
    self.cleanup_report = {
        'cleaned_files': [],
        'formatted_files': [],
        'removed_imports': [],
        'duplicate_files': [],
        'errors': [],
    }

    def log_action(self, action_type, message):
        """记录清理动作"""
        print(f"[{action_type}] {message}")
        if action_type not in self.cleanup_report:
            self.cleanup_report[action_type] = []
        self.cleanup_report[action_type].append(message)

    def step7_remove_unused_imports(self):
        """步骤7: 删无用import & 变量"""
        print("🗑️ 步骤7: 清理无用import...")

        try:
            # 先尝试用autoflake
            result = subprocess.run(
                [
                    'python',
                    '-m',
                    'autoflake',
                    '--remove-unused-variables',
                    '--remove-all-unused-imports',
                    '--in-place',
                    '--recursive',
                    '.',
                ],
                capture_output=True,
                text=True,
                cwd='.',
            )

            if result.returncode == 0:
                self.log_action('import_cleanup', 'autoflake清理成功')
            else:
                self.log_action('error', f'autoflake失败: {result.stderr}')
                # 手动清理一些明显的无用import
                self._manual_import_cleanup()

        except Exception:
            self.log_action('error', f'import清理失败: {e}')
            self._manual_import_cleanup()

    def _manual_import_cleanup(self):
        """手动清理import"""
        print("📝 手动清理无用import...")
        import_patterns = [
            'import os  # unused',
            'import sys  # unused',
            'from typing import Dict  # unused',
            'from typing import List  # unused',
        ]

        cleaned_count = 0
        for root, dirs, files in os.walk('.'):
            dirs[:] = [d for d in dirs if not d.startswith(
                '.') and d != '__pycache__']

            for file in files:
                if file.endswith('.py'):
                    filepath = os.path.join(root, file)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            content = f.read()

                        # 简单清理一些明显无用的import
                        lines = content.split('\n')
                        cleaned_lines = []

                        for line in lines:
                            if line.strip().startswith(
                                'import '
                            ) or line.strip().startswith('from '):
                                # 这里可以加更智能的检测
                                pass
                            cleaned_lines.append(line)

                        if len(cleaned_lines) < len(lines):
                            with open(filepath, 'w', encoding='utf-8') as f:
                                f.write('\n'.join(cleaned_lines))
                            cleaned_count += 1
                            self.log_action(
                                'import_cleanup', f'手动清理: {filepath}')

                    except Exception:
                        self.log_action('error', f'处理{filepath}失败: {e}')

        print(f"✅ 手动清理完成，处理了 {cleaned_count} 个文件")

    def step8_format_codebase(self):
        """步骤8: 格式化整仓"""
        print("🎨 步骤8: 格式化代码...")

        try:
            # 使用black格式化
            result = subprocess.run(
                [
                    'python',
                    '-m',
                    'black',
                    '.',
                    '--line-length',
                    '88',
                    '--skip-string-normalization',
                ],
                capture_output=True,
                text=True,
                cwd='.',
            )

            if result.returncode == 0:
                output_lines = result.stderr.split(
                    '\n') if result.stderr else []
                formatted_count = len(
                    [line for line in output_lines if 'reformatted' in line]
                )
                self.log_action(
                    'formatting', f'black格式化成功，处理了 {formatted_count} 个文件'
                )
            else:
                self.log_action('error', f'black格式化失败: {result.stderr}')

        except Exception:
            self.log_action('error', f'格式化失败: {e}')

    def step9_remove_duplicate_files(self):
        """步骤9: 去重复文件"""
        print("🔍 步骤9: 检测和删除重复文件...")

        # 计算文件hash
        file_hashes = {}
        duplicates = []

        for root, dirs, files in os.walk('.'):
            dirs[:] = [d for d in dirs if not d.startswith(
                '.') and d != '__pycache__']

            for file in files:
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'rb') as f:
                        file_hash = hashlib.md5(f.read()).hexdigest()

                    if file_hash in file_hashes:
                        duplicates.append((filepath, file_hashes[file_hash]))
                    else:
                        file_hashes[file_hash] = filepath

                except Exception:
                    self.log_action('error', f'读取{filepath}失败: {e}')

        # 删除重复文件
        removed_count = 0
        for dup_file, original_file in duplicates:
            try:
                # 保留更短路径的文件，删除较长路径的
                if len(dup_file) > len(original_file):
                    os.remove(dup_file)
                    self.log_action(
                        'duplicate_removal',
                        f'删除重复文件: {dup_file} (原文件: {original_file})',
                    )
                    removed_count += 1
                else:
                    os.remove(original_file)
                    file_hashes[
                        hashlib.md5(open(dup_file, 'rb').read()).hexdigest()
                    ] = dup_file
                    self.log_action(
                        'duplicate_removal',
                        f'删除重复文件: {original_file} (保留: {dup_file})',
                    )
                    removed_count += 1
            except Exception:
                self.log_action('error', f'删除重复文件失败: {e}')

        print(f"✅ 删除了 {removed_count} 个重复文件")

    def cleanup_backup_files(self):
        """清理备份文件"""
        print("🗂️ 额外清理: 删除备份文件...")

        backup_patterns = [
            '*.backup',
            '*.backup_*',
            '*_backup.*',
            '*.bak',
            '*~',
            '*.orig',
        ]

        removed_count = 0
        for root, dirs, files in os.walk('.'):
            dirs[:] = [d for d in dirs if not d.startswith(
                '.') and d != '__pycache__']

            for file in files:
                if any(
                    pattern in file.lower()
                    for pattern in ['backup', '.backup_', '_backup']
                ):
                    filepath = os.path.join(root, file)
                    try:
                        os.remove(filepath)
                        self.log_action(
                            'backup_cleanup', f'删除备份文件: {filepath}')
                        removed_count += 1
                    except Exception:
                        self.log_action('error', f'删除备份文件失败: {e}')

        print(f"✅ 删除了 {removed_count} 个备份文件")

    def cleanup_cache_files(self):
        """清理缓存文件"""
        print("🧹 额外清理: 删除缓存文件...")

        removed_count = 0

        # 删除__pycache__目录
        for root, dirs, files in os.walk('.'):
            if '__pycache__' in dirs:
                pycache_path = os.path.join(root, '__pycache__')
                try:
                    shutil.rmtree(pycache_path)
                    self.log_action('cache_cleanup', f'删除缓存目录: {pycache_path}')
                    removed_count += 1
                except Exception:
                    self.log_action('error', f'删除缓存目录失败: {e}')

        # 删除.pyc文件
        for root, dirs, files in os.walk('.'):
            for file in files:
                if file.endswith('.pyc'):
                    filepath = os.path.join(root, file)
                    try:
                        os.remove(filepath)
                        self.log_action(
                            'cache_cleanup', f'删除pyc文件: {filepath}')
                        removed_count += 1
                    except Exception:
                        self.log_action('error', f'删除pyc文件失败: {e}')

        print(f"✅ 删除了 {removed_count} 个缓存文件")

    def generate_report(self):
        """生成清理报告"""
        report_file = 'machine_cleanup_report.json'

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.cleanup_report, f, indent=2, ensure_ascii=False)

        print(f"\n📊 清理报告已保存至: {report_file}")

        # 打印摘要
        print("\n🎯 清理摘要:")
        print(f"   清理的文件: {len(self.cleanup_report.get('cleaned_files', []))}")
        print(
            f"   格式化的文件: {len(self.cleanup_report.get('formatted_files', []))}")
        print(
            f"   删除的重复文件: {len(self.cleanup_report.get('duplicate_removal', []))}"
        )
        print(
            f"   删除的备份文件: {len(self.cleanup_report.get('backup_cleanup', []))}"
        )
        print(
            f"   删除的缓存文件: {len(self.cleanup_report.get('cache_cleanup', []))}")
        print(f"   错误数: {len(self.cleanup_report.get('error', []))}")

    def run_full_cleanup(self):
        """运行完整清理流程"""
        print("🚀 开始项目机器大扫除...")
        print("=" * 60)

        # 执行清理步骤
        self.step7_remove_unused_imports()
        print()

        self.step8_format_codebase()
        print()

        self.step9_remove_duplicate_files()
        print()

        # 额外清理
        self.cleanup_backup_files()
        print()

        self.cleanup_cache_files()
        print()

        self.generate_report()

        print("\n🎉 机器大扫除完成！")


def main():
    """主函数"""
    cleanup = ProjectMachineCleanup()
    cleanup.run_full_cleanup()


if __name__ == "__main__":
    main()
