# Prometheus 监控配置
# 针对 api_error_rate 和 db_retry_count 的实时监控

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules_enhanced.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # YS-API 主服务监控
  - job_name: 'ys-api-main'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s
    scrape_timeout: 3s
    honor_labels: true
    params:
      format: ['prometheus']

  # 数据库重试监控 (专项)
  - job_name: 'ys-api-db-retry'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics/db-retry'
    scrape_interval: 10s
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'db_retry_.*'
        target_label: 'monitor_type'
        replacement: 'database_retry'

  # 错误率专项监控
  - job_name: 'ys-api-error-rate'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics/errors'
    scrape_interval: 5s
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'api_error_.*'
        target_label: 'monitor_type'
        replacement: 'api_errors'

  # 系统资源监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
    scrape_interval: 15s

  # 应用性能监控
  - job_name: 'ys-api-performance'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics/performance'
    scrape_interval: 10s
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'http_request_duration_.*'
        target_label: 'monitor_type'
        replacement: 'performance'

  # ELK系统监控
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['localhost:9200']
    metrics_path: '/_prometheus/metrics'
    scrape_interval: 30s

  # 日志系统监控
  - job_name: 'logstash'
    static_configs:
      - targets: ['localhost:9600']
    metrics_path: '/_node/stats'
    scrape_interval: 30s

# 远程写入配置 (可选)
remote_write:
  - url: "http://localhost:8428/api/v1/write"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500
