# 新系统 Dockerfile (重构后的干净代码)
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# 创建非root用户
RUN groupadd -g 1000 appuser && \
    useradd -r -u 1000 -g appuser appuser

# 安装Python依赖
COPY new-system/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制新系统代码 (重构后的干净代码)
COPY new-system/ .

# 更改所有权
RUN chown -R appuser:appuser /app

# 切换到非root用户
USER appuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

EXPOSE 5000

# 启动新系统
CMD ["python", "app.py"]
