import asyncio
import json
import os
import sys
import time
import traceback
from pathlib import Path

import psutil
import structlog

from ..core.config import settings
from .auto_recovery_manager_enhanced import EnhancedAutoRecoveryManager
from .data_write_manager import DataWriteManager
from .retry_helper import get_retry_statistics

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 优化版自动同步调度器
解决并发风险，实现完成触发同步和任务锁定机制

主要改进：
1. 完成触发同步：基于上次同步完成时间触发
2. 任务锁定机制：防止重复执行
3. 动态间隔调整：根据同步耗时动态调整
4. 资源监控：CPU/内存使用率监控
5. 熔断器模式：防止级联失败
"""


logger = structlog.get_logger()


class DistributedLock:
    """分布式锁管理器"""

    def __init___(self, lock_key: str, timeout_minutes: int = 30):
    """TODO: Add function description."""
        self.lock_key = lock_key
        self.timeout_minutes = timeout_minutes
        self.lock_file = f"/tmp/{lock_key}.lock"
        if sys.platform == "win32":
            self.lock_file = f"C:/temp/{lock_key}.lock"
        self.lock_handle = None

    async def acquire(self) -> bool:
        """获取分布式锁"""
        try:
            # 检查锁文件是否存在且未过期
            if os.path.exists(self.lock_file):
                lock_time = os.path.getmtime(self.lock_file)
                if time.time() - lock_time < self.timeout_minutes * 60:
                    logger.warning(f"锁已存在且未过期: {self.lock_key}")
                    return False

            # 创建锁文件
            with open(self.lock_file, 'w') as f:
                f.write(str(time.time()))

            logger.info(f"成功获取锁: {self.lock_key}")
            return True

        except Exception:
            logger.error(f"获取锁失败: {self.lock_key}", error=str(e))
            return False

    async def release(self):
        """释放分布式锁"""
        try:
            if os.path.exists(self.lock_file):
                os.remove(self.lock_file)
                logger.info(f"成功释放锁: {self.lock_key}")
        except Exception:
            logger.error(f"释放锁失败: {self.lock_key}", error=str(e))


class CircuitBreaker:
    """熔断器模式"""

    def __init___(self, max_failures: int = 5, timeout_minutes: int = 30):
    """TODO: Add function description."""
        self.max_failures = max_failures
        self.timeout_minutes = timeout_minutes
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    def allow_execution(self) -> bool:
        """检查是否允许执行"""
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.timeout_minutes * 60:
                self.state = "HALF_OPEN"
                logger.info("熔断器状态: OPEN -> HALF_OPEN")
            else:
                return False

        return True

    def on_success(self):
        """成功时重置熔断器"""
        if self.state == "HALF_OPEN":
            self.state = "CLOSED"
            logger.info("熔断器状态: HALF_OPEN -> CLOSED")
        self.failure_count = 0

    def on_failure(self):
        """失败时更新熔断器状态"""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.max_failures:
            self.state = "OPEN"
            logger.warning(
                f"熔断器触发: CLOSED -> OPEN (失败次数: {self.failure_count})"
            )


class ResourceMonitor:
    """资源监控器"""

    def __init___(
        self,
        cpu_threshold: int = 80,
        memory_threshold: int = 85,
        disk_threshold: int = 90,
    ):
    """TODO: Add function description."""
        self.cpu_threshold = cpu_threshold
        self.memory_threshold = memory_threshold
        self.disk_threshold = disk_threshold

    def check_resources(self) -> Dict[str, Any]:
        """检查系统资源使用情况"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)

            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent

            # 判断是否超过阈值
            cpu_ok = cpu_percent < self.cpu_threshold
            memory_ok = memory_percent < self.memory_threshold
            disk_ok = disk_percent < self.disk_threshold

            result = {
                "cpu_percent": cpu_percent,
                "memory_percent": memory_percent,
                "disk_percent": disk_percent,
                "cpu_ok": cpu_ok,
                "memory_ok": memory_ok,
                "disk_ok": disk_ok,
                "all_ok": cpu_ok and memory_ok and disk_ok,
            }

            if not result["all_ok"]:
                logger.warning(
                    "系统资源使用率过高",
                    cpu_percent=cpu_percent,
                    memory_percent=memory_percent,
                    disk_percent=disk_percent,
                )

            return result

        except Exception:
            logger.error("资源监控失败", error=str(e))
            return {"all_ok": True}  # 监控失败时允许执行


class AutoSyncScheduler:
    """优化版自动同步调度器"""

    def __init___(self):
    """TODO: Add function description."""
        self.settings = settings
        self.data_write_manager = DataWriteManager()
        self.recovery_manager = EnhancedAutoRecoveryManager()

        # 加载优化配置
        self.config = self._load_optimized_config()

        # 初始化组件
        self.distributed_lock = DistributedLock(
            self.config["concurrency_control"]["lock_key_prefix"],
            self.config["concurrency_control"]["task_lock_timeout_minutes"],
        )
        self.circuit_breaker = CircuitBreaker(
            self.config["error_handling"]["max_failures_before_break"],
            self.config["error_handling"]["circuit_breaker_timeout_minutes"],
        )
        self.resource_monitor = ResourceMonitor(
            self.config["resource_monitoring"]["cpu_threshold_percent"],
            self.config["resource_monitoring"]["memory_threshold_percent"],
            self.config["resource_monitoring"]["disk_threshold_percent"],
        )

        # 同步状态跟踪
        self.last_completion_time = None
        self.last_sync_duration = None
        self.success_count = 0
        self.failure_count = 0

        # 运行状态
        self.is_running = False
        self.current_task = None

    def _load_optimized_config(self) -> Dict:
        """加载优化配置"""
        try:
            config_path = (
                Path(__file__).parent.parent.parent.parent
                / "config"
                / "auto_sync_config_optimized.json"
            )
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning("优化配置文件不存在，使用默认配置")
                return self._get_default_config()
        except Exception:
            logger.error("加载优化配置失败", error=str(e))
            return self._get_default_config()

    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "sync_mode": "completion_triggered",
            "base_interval_minutes": 5,
            "concurrency_control": {
                "enabled": True,
                "max_concurrent_modules": 3,
                "task_lock_timeout_minutes": 30,
                "distributed_lock_enabled": True,
                "lock_key_prefix": "ys_api_sync",
            },
            "dynamic_scheduling": {
                "enabled": True,
                "min_interval_minutes": 2,
                "max_interval_minutes": 15,
                "adaptive_factor": 1.5,
                "performance_threshold_ms": 30000,
            },
            "resource_monitoring": {
                "enabled": True,
                "cpu_threshold_percent": 80,
                "memory_threshold_percent": 85,
                "disk_threshold_percent": 90,
            },
            "error_handling": {
                "circuit_breaker_enabled": True,
                "max_failures_before_break": 5,
                "circuit_breaker_timeout_minutes": 30,
            },
        }

    async def start_scheduler(self):
        """启动优化版调度器"""
        if self.is_running:
            logger.warning("调度器已在运行")
            return

        self.is_running = True
        logger.info(" 优化版自动同步调度器启动")

        # 使用 asyncio.create_task 来避免阻塞
        self.current_task = asyncio.create_task(self._scheduler_loop())

        # 确保任务不会因为未被等待而被垃圾回收
        # 添加回调函数来处理任务完成或异常

        def done_callbackk(task):
    """TODO: Add function description."""
            try:
                # 获取任务结果（如果有异常会抛出）
                task.result()
            except asyncio.CancelledError:
                logger.info("调度器任务被取消")
            except Exception:
                logger.error("调度器任务异常", error=str(e))

        # 添加回调
        self.current_task.add_done_callback(done_callback)

        logger.info(" 调度器任务已创建，非阻塞启动完成")

    async def _scheduler_loop(self):
        """调度器主循环"""
        logger.info(" _scheduler_loop 开始")

        try:
            while self.is_running:
                logger.info(" _scheduler_loop 循环一次")
                await self._run_sync_cycle()
                await self._wait_for_next_cycle()
        except Exception:
            logger.error("调度器运行异常", error=str(e))
        finally:
            self.is_running = False
            logger.info(" _scheduler_loop 结束")


    async def stop_scheduler(self):
        """停止调度器"""
        try:
            self.is_running = False
            if self.current_task:
                self.current_task.cancel()
            logger.info("优化版自动同步调度器已停止")
            return {
                "success": True,
                "message": "调度器停止成功",
                "stopped_at": datetime.now().isoformat(),
            }
        except Exception:
            logger.error("停止调度器失败", error=str(e))
            return {"success": False, "message": f"停止调度器失败: {str(e)}"}


    async def trigger_manual_sync(self):
        """手动触发同步"""
        try:
            logger.info("手动触发同步开始")

            # 检查是否已经在运行同步
            if hasattr(self, '_sync_running') and self._sync_running:
                return {"success": False, "message": "同步正在进行中，请稍后再试"}

            # 设置同步运行标志
            self._sync_running = True

            try:
                # 执行同步
                result = await self._execute_zero_downtime_sync()

                if result["success"]:
                    logger.info("手动同步执行成功")
                    return {
                        "success": True,
                        "message": "手动同步执行成功",
                        "data": result,
                    }
                else:
                    logger.error("手动同步执行失败", error=result.get("message"))
                    return {
                        "success": False,
                        "message": f"手动同步执行失败: {result.get('message', '未知错误')}",
                    }
            finally:
                # 清除同步运行标志
                self._sync_running = False

        except Exception:
            logger.error("手动触发同步异常", error=str(e))
            self._sync_running = False
            return {"success": False, "message": f"手动触发同步失败: {str(e)}"}


    async def _run_sync_cycle(self):
        """执行同步周期"""
        start_time = datetime.now()

        try:
            # 1. 检查熔断器状态
            if not self.circuit_breaker.allow_execution():
                logger.warning("熔断器开启，跳过本次同步")
                return

            # 2. 检查资源使用情况
            if self.config["resource_monitoring"]["enabled"]:
                resource_status = self.resource_monitor.check_resources()
                if not resource_status["all_ok"]:
                    logger.warning("系统资源不足，延迟执行同步")
                    await asyncio.sleep(60)  # 延迟1分钟
                    return

            # 3. 获取分布式锁
            if self.config["concurrency_control"]["enabled"]:
                if not await self.distributed_lock.acquire():
                    logger.warning("无法获取分布式锁，跳过本次同步")
                    return

            try:
                # 4. 执行同步 - 使用零停机模式，避免清空主表
                logger.info("开始执行零停机同步")
                result = await self._execute_zero_downtime_sync()

                # 5. 更新状态
                if result["success"]:
                    self.circuit_breaker.on_success()
                    self.success_count += 1
                    self.last_completion_time = datetime.now()
                    self.last_sync_duration = (
                        datetime.now() - start_time
                    ).total_seconds()
                    logger.info(
                        "零停机同步执行成功",
                        duration_seconds=self.last_sync_duration,
                        success_count=self.success_count,
                    )
                else:
                    self.circuit_breaker.on_failure()
                    self.failure_count += 1
                    logger.error(
                        "零停机同步执行失败",
                        error=result.get("message"),
                        failure_count=self.failure_count,
                    )

            finally:
                # 6. 释放分布式锁
                if self.config["concurrency_control"]["enabled"]:
                    await self.distributed_lock.release()

        except Exception:
            self.circuit_breaker.on_failure()
            self.failure_count += 1
            logger.error("同步周期执行异常", error=str(e))


    async def _execute_zero_downtime_sync(self) -> Dict:
        """执行零停机同步操作"""
        try:
            # 使用零停机模式执行同步，避免清空主表
            modules = self.config.get("modules", [])
            logger.info("开始零停机批量同步", modules=modules)

            # 检查是否有共享表，避免重复清空
            shared_tables = await self._detect_shared_tables(modules)
            if shared_tables:
                logger.info(
                    "检测到共享表，将使用影子表排队机制", shared_tables=shared_tables
                )

            result = await self.data_write_manager.write_all_modules_with_zero_downtime(
                modules=modules,
                record_limit=self.config.get("record_limit"),
                force_recreate_tables=False,  # 零停机模式下不强制重建表
                clear_existing_data=False,  # 零停机模式下不清空主表
            )

            return result

        except Exception:
            logger.error("零停机同步执行异常", error=str(e))
            return {"success": False, "message": str(e)}


    async def _detect_shared_tables(self, modules: List[str]) -> Dict[str, List[str]]:
        """检测共享表"""
        table_modules = {}
        shared_tables = {}

        for module in modules:
            table_name = module  # 简化处理，实际应该通过配置获取表名
            if table_name not in table_modules:
                table_modules[table_name] = []
            table_modules[table_name].append(module)

        # 找出被多个模块共享的表
        for table_name, module_list in table_modules.items():
            if len(module_list) > 1:
                shared_tables[table_name] = module_list

        return shared_tables

    async def _wait_for_next_cycle(self):
        """等待下一个同步周期"""
        if self.config["sync_mode"] == "completion_triggered":
            # 完成触发模式：基于上次完成时间和动态间隔
            interval = self._calculate_dynamic_interval()
        else:
            # 固定间隔模式
            interval = self.config["base_interval_minutes"] * 60

        logger.info(f"等待下一个同步周期: {interval}秒")
        await asyncio.sleep(interval)


    def _calculate_dynamic_interval(self) -> int:
        """计算动态间隔时间"""
        base_interval = self.config["base_interval_minutes"] * 60
        min_interval = self.config["dynamic_scheduling"]["min_interval_minutes"] * 60
        max_interval = self.config["dynamic_scheduling"]["max_interval_minutes"] * 60
        adaptive_factor = self.config["dynamic_scheduling"]["adaptive_factor"]
        performance_threshold = self.config["dynamic_scheduling"][
            "performance_threshold_ms"
        ]

        if self.last_sync_duration:
            # 根据上次同步耗时调整间隔
            if self.last_sync_duration * 1000 > performance_threshold:
                # 同步耗时较长，增加间隔
                interval = min(base_interval * adaptive_factor, max_interval)
            else:
                # 同步耗时较短，减少间隔
                interval = max(base_interval / adaptive_factor, min_interval)
        else:
            interval = base_interval

        return int(interval)

    async def get_scheduler_status(self) -> Dict:
        """获取调度器状态"""
        # 计算下次运行时间
        next_run_time = None
        next_interval = self._calculate_dynamic_interval()

        if self.is_running:
            if self.last_completion_time:
                next_run_time = (
                    self.last_completion_time + timedelta(seconds=next_interval)
                ).isoformat()
            else:
                # 如果没有上次完成时间，使用当前时间加间隔
                next_run_time = (
                    datetime.now() + timedelta(seconds=next_interval)
                ).isoformat()
        else:
            # 即使没有运行，也显示如果启动后的预期运行时间
            next_run_time = (
                datetime.now() + timedelta(seconds=next_interval)
            ).isoformat()

        return {
            "is_running": self.is_running,
            "sync_mode": self.config["sync_mode"],
            "last_completion_time": (
                self.last_completion_time.isoformat()
                if self.last_completion_time
                else None
            ),
            "last_sync_duration": self.last_sync_duration,
            "success_count": self.success_count,
            "failure_count": self.failure_count,
            "success_rate": (
                self.success_count / (self.success_count + self.failure_count)
                if (self.success_count + self.failure_count) > 0
                else 0
            ),
            "circuit_breaker_state": self.circuit_breaker.state,
            "next_interval_seconds": self._calculate_dynamic_interval(),
            "next_run_time": next_run_time,
        }


# 全局调度器实例
_optimized_scheduler = None


async def get_scheduler() -> AutoSyncScheduler:
    """获取优化版调度器实例"""
    global _optimized_scheduler
    if _optimized_scheduler is None:
        _optimized_scheduler = AutoSyncScheduler()
    return _optimized_scheduler


async def start_auto_sync():
    """启动优化版自动同步"""
    scheduler = await get_scheduler()
    await scheduler.start_scheduler()


async def stop_auto_sync():
    """停止优化版自动同步"""
    global _optimized_scheduler
    if _optimized_scheduler:
        await _optimized_scheduler.stop_scheduler()
        _optimized_scheduler = None


async def get_sync_status() -> Dict:
    """获取优化版同步状态"""
    scheduler = await get_scheduler()
    return await scheduler.get_scheduler_status()


# ==================== 增强版调度器 ====================



class RobustAutoSyncScheduler(AutoSyncScheduler):
    """增强版调度器：断线重连、失败重试、数据校验"""


    def __init___(self):

    """TODO: Add function description."""
        super().__init__()
        self.consecutive_failures = 0
        self.max_consecutive_failures = 5


    async def _run_sync_cycle(self):
        """执行同步周期 - 增强版"""
        start_time = datetime.now()

        try:
            # 1. 检查熔断器状态
            if not self.circuit_breaker.allow_execution():
                logger.warning("熔断器开启，跳过本次同步")
                return

            # 2. 检查资源使用情况
            if self.config["resource_monitoring"]["enabled"]:
                resource_status = self.resource_monitor.check_resources()
                if not resource_status["all_ok"]:
                    logger.warning("系统资源不足，延迟执行同步")
                    await asyncio.sleep(60)  # 延迟1分钟
                    return

            # 3. 获取分布式锁
            if self.config["concurrency_control"]["enabled"]:
                if not await self.distributed_lock.acquire():
                    logger.warning("无法获取分布式锁，跳过本次同步")
                    return

            try:
                # 4. 执行同步 - 使用重试包装器
                logger.info("开始执行增强版零停机同步")

                # 使用重试装饰器包装同步方法
                @sync_retry_wrapper(SYNC_RETRY_CONFIG, module_retry_key)
                async def robust_syncc():

    """TODO: Add function description."""
                    return await self._execute_zero_downtime_sync()

                result = await robust_sync()

                # 5. 更新状态
                if result["success"]:
                    self.circuit_breaker.on_success()
                    self.success_count += 1
                    self.consecutive_failures = 0  # 重置连续失败计数
                    self.last_completion_time = datetime.now()
                    self.last_sync_duration = (
                        datetime.now() - start_time
                    ).total_seconds()
                    logger.info(
                        "增强版同步执行成功",
                        duration_seconds=self.last_sync_duration,
                        success_count=self.success_count,
                    )
                else:
                    self.circuit_breaker.on_failure()
                    self.failure_count += 1
                    self.consecutive_failures += 1
                    logger.error(
                        "增强版同步执行失败",
                        error=result.get("message"),
                        failure_count=self.failure_count,
                        consecutive_failures=self.consecutive_failures,
                    )

            finally:
                # 6. 释放分布式锁
                if self.config["concurrency_control"]["enabled"]:
                    await self.distributed_lock.release()

        except Exception:
            self.circuit_breaker.on_failure()
            self.failure_count += 1
            self.consecutive_failures += 1

            logger.error(
                "同步周期执行异常",
                error=str(e),
                traceback=traceback.format_exc(),
                consecutive_failures=self.consecutive_failures,
            )

            # 如果连续失败次数过多，进入长时间等待
            if self.consecutive_failures >= self.max_consecutive_failures:
                logger.error(
                    f"连续失败 {self.consecutive_failures} 次，进入5分钟冷却期"
                )
                await asyncio.sleep(300)  # 5分钟冷却期


    async def get_enhanced_status(self) -> Dict:
        """获取增强版状态信息"""
        base_status = await self.get_scheduler_status()

        # 添加增强信息

        retry_stats = get_retry_statistics()

        enhanced_status = {
            **base_status,
            "consecutive_failures": self.consecutive_failures,
            "max_consecutive_failures": self.max_consecutive_failures,
            "retry_statistics": retry_stats,
            "health_status": (
                "healthy"
                if self.consecutive_failures < 3
                else "degraded" if self.consecutive_failures < 5 else "critical"
            ),
        }

        return enhanced_status


# 全局增强版调度器实例
_robust_scheduler = None


async def get_robust_scheduler() -> RobustAutoSyncScheduler:
    """获取增强版调度器实例"""
    global _robust_scheduler
    if _robust_scheduler is None:
        _robust_scheduler = RobustAutoSyncScheduler()
    return _robust_scheduler


async def start_robust_auto_sync():
    """启动增强版自动同步"""
    scheduler = await get_robust_scheduler()
    await scheduler.start_scheduler()


async def stop_robust_auto_sync():
    """停止增强版自动同步"""
    global _robust_scheduler
    if _robust_scheduler:
        await _robust_scheduler.stop_scheduler()
        _robust_scheduler = None


async def get_robust_sync_status() -> Dict:
    """获取增强版同步状态"""
    scheduler = await get_robust_scheduler()
    return await scheduler.get_enhanced_status()
