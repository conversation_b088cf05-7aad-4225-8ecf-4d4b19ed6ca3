import json
from pathlib import Path

#!/usr/bin/env python3
"""
屎山代码清除策略分析工具
根据TASK.md中的安全策略，分析是否可以安全清除
"""


def analyze_graveyard_safety():
    """分析graveyard目录的清除安全性"""
    graveyard_dir = Path("graveyard")

    if not graveyard_dir.exists():
        return {"status": "no_graveyard", "message": "graveyard目录不存在"}

    # 检查graveyard中的模块
    modules = list(graveyard_dir.iterdir())

    safety_analysis = {
        "total_modules_in_graveyard": len(modules),
        "creation_date": "2025-08-06",
        "current_date": datetime.now().strftime("%Y-%m-%d"),
        "days_since_creation": 0,  # 刚创建
        "recommendation": "",
        "safety_level": "",
        "details": [],
    }

    # 检查每个模块的备份报告
    for module_dir in modules:
        if module_dir.is_dir():
            report_file = module_dir / "migration_report.json"
            if report_file.exists():
                try:
                    with open(report_file, "r", encoding="utf-8") as f:
                        report = json.load(f)

                    safety_analysis["details"].append(
                        {
                            "module": module_dir.name,
                            "has_report": True,
                            "status": report.get("status", "unknown"),
                            "timestamp": report.get("timestamp", "unknown"),
                        }
                    )
                except Exception:
                    safety_analysis["details"].append(
                        {
                            "module": module_dir.name,
                            "has_report": False,
                            "error": str(e),
                        }
                    )
            else:
                safety_analysis["details"].append(
                    {
                        "module": module_dir.name,
                        "has_report": False,
                        "status": "no_report",
                    }
                )

    # 安全性评估
    if safety_analysis["days_since_creation"] < 7:
        safety_analysis["safety_level"] = "❌ 不安全"
        safety_analysis[
            "recommendation"
        ] = """
🛡️ 根据TASK.md中的安全策略，建议等待7天后再清除：

📋 必须人工确认的3个节点:
- **删除屎山代码前**: AI可能误删关键配置文件
- **检查graveyard/目录7天后再删除**

🕒 建议操作时间表:
- 当前时间: 2025-08-06
- 安全清除时间: 2025-08-13 (7天后)
- 清除前验证: 确认新系统稳定运行无问题

⚠️ 清除前必须确认:
1. 所有新模块运行正常
2. 数据库双写一致性验证通过
3. 监控系统无异常告警
4. 业务方确认功能正常
"""
    else:
        safety_analysis["safety_level"] = "✅ 可以安全清除"
        safety_analysis["recommendation"] = "已满足7天安全期，可以安全清除graveyard目录"

    return safety_analysis


def mainn():
    """TODO: Add function description."""
    print("🔍 屎山代码清除策略分析")
    print("=" * 50)

    analysis = analyze_graveyard_safety()

    print(f"📊 分析结果:")
    print(f"  graveyard中的模块数: {analysis['total_modules_in_graveyard']}")
    print(f"  创建日期: {analysis['creation_date']}")
    print(f"  当前日期: {analysis['current_date']}")
    print(f"  距创建天数: {analysis['days_since_creation']} 天")
    print(f"  安全等级: {analysis['safety_level']}")

    print(f"\n📝 建议:")
    print(analysis["recommendation"])

    # 保存分析报告
    report_path = Path("reports/graveyard_safety_analysis.json")
    report_path.parent.mkdir(exist_ok=True)

    with open(report_path, "w", encoding="utf-8") as f:
        json.dump(analysis, f, ensure_ascii=False, indent=2)

    print(f"\n📄 详细分析报告已保存: {report_path}")


if __name__ == "__main__":
    main()
