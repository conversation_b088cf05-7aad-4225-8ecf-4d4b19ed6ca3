pipeline {
    agent any
    
    environment {
        PYTHON_VERSION = '3.9'
        DOCKER_IMAGE = 'ys-api-v3'
        DOCKER_TAG = "${BUILD_NUMBER}"
    }
    
    stages {
        stage('代码检出') {
            steps {
                checkout scm
                script {
                    env.GIT_COMMIT_SHORT = sh(
                        script: "git rev-parse --short HEAD",
                        returnStdout: true
                    ).trim()
                }
            }
        }
        
        stage('环境准备') {
            steps {
                sh '''
                    python3 -m venv venv
                    . venv/bin/activate
                    pip install -r backend/requirements.txt
                '''
            }
        }
        
        stage('代码质量检查') {
            parallel {
                stage('代码风格检查') {
                    steps {
                        sh '''
                            . venv/bin/activate
                            cd backend
                            flake8 app/
                        '''
                    }
                }
                
                stage('安全扫描') {
                    steps {
                        sh '''
                            . venv/bin/activate
                            bandit -r backend/app/
                        '''
                    }
                }
            }
        }
        
        stage('测试') {
            steps {
                sh '''
                    . venv/bin/activate
                    cd backend
                    python -m pytest tests/ --junitxml=test-results.xml
                '''
            }
            post {
                always {
                    junit 'backend/test-results.xml'
                }
            }
        }
        
        stage('构建镜像') {
            when {
                branch 'main'
            }
            steps {
                script {
                    def image = docker.build("${DOCKER_IMAGE}:${DOCKER_TAG}")
                    docker.withRegistry('https://registry-1.docker.io/v2/', 'docker-hub-credentials') {
                        image.push()
                        image.push('latest')
                    }
                }
            }
        }
        
        stage('部署') {
            when {
                branch 'main'
            }
            steps {
                sh '''
                    docker-compose down
                    docker-compose up -d
                    sleep 30
                    curl -f http://localhost:5000/health || exit 1
                '''
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
        success {
            echo '🎉 流水线执行成功！'
        }
        failure {
            echo '❌ 流水线执行失败！'
        }
    }
}
