import json
import os
import py_compile
import sys

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速健康检查脚本 - 验证关键文件和配置
"""


def check_main_files():
    """检查主要文件"""
    print("=== 检查主要文件 ===")

    main_files = [
        "backend/app/main.py",
        "backend/start_server.py",
        "config/modules.json",
        "backend/requirements.txt"
    ]

    for file_path in main_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} - 存在")

            # 检查Python文件语法
            if file_path.endswith('.py'):
                try:
                    py_compile.compile(file_path, doraise=True)
                    print(f"✅ {file_path} - 语法正确")
                except py_compile.PyCompileError as e:
                    print(f"❌ {file_path} - 语法错误: {e}")

            # 检查JSON文件格式
            elif file_path.endswith('.json'):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        json.load(f)
                    print(f"✅ {file_path} - JSON格式正确")
                except Exception:
                    print(f"❌ {file_path} - JSON格式错误: {e}")

        else:
            print(f"❌ {file_path} - 文件缺失")


def check_imports():
    """检查关键模块导入"""
    print("\n=== 检查模块导入 ===")

    try:
        sys.path.insert(0, 'backend')
        print("✅ backend.app.main - 导入成功")
    except Exception:
        print(f"❌ backend.app.main - 导入失败: {e}")


def main():
    """主函数"""
    print("🔍 YS-API V3.0 快速健康检查")
    print("=" * 50)

    check_main_files()
    check_imports()

    print("\n" + "=" * 50)
    print("✅ 健康检查完成")


if __name__ == "__main__":
    main()
