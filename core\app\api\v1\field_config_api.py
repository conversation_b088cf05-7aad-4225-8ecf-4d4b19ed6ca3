import json
from datetime import datetime
from pathlib import Path

import structlog
from pydantic import BaseModel

from ...services.unified_field_service import unified_field_service

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段配置 API 路由
支持前端字段配置界面的所有功能
"""


logger = structlog.get_logger()

router = APIRouter(prefix="/api/v1/field-config", tags=["字段配置"])


# 请求模型
class FieldConfigRequest(BaseModel):
    user_id: str
    user_config: Dict[str, Any]


class FieldSelectionRequest(BaseModel):
    user_id: str
    field_name: str
    is_selected: bool


class ChineseNameRequest(BaseModel):
    user_id: str
    field_name: str
    chinese_name: str


class BaselineSaveRequest(BaseModel):
    user_id: str
    raw_data: bool = True
    api_data: List[Dict[str, Any]]


class UserConfigSaveRequest(BaseModel):
    user_id: str
    enhanced_data: bool = True
    user_config: Dict[str, Any]


class FieldConfigResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


# 使用统一字段服务
field_service = unified_field_service


@router.get("/modules/{module_name}/fields")
async def get_module_fields(
    module_name: str, user_id: str = Query("Alice", description="用户ID")
):
    """
    获取模块字段配置

    Args:
        module_name: 模块名称 (如: sales_order, purchase_order)
        user_id: 用户ID

    Returns:
        字段配置数据
    """
    try:
        logger.info("获取模块字段配置", module_name=module_name, user_id=user_id)

        # 使用新的统一字段服务
        config = await field_service.get_user_field_config(module_name, user_id)

        logger.info(
            "成功获取字段配置",
            module_name=module_name,
            total_fields=config.get("total_fields", 0),
            selected_fields=config.get("selected_fields", 0),
        )

        return FieldConfigResponse(
            success=True, message="成功获取字段配置", data=config
        )

    except Exception:
        logger.error(
            "获取字段配置失败", module_name=module_name, user_id=user_id, error=str(e)
        )
        raise HTTPException(status_code=500, detail=f"获取字段配置失败: {str(e)}")


@router.post("/modules/{module_name}/fields/selection")
async def update_field_selection(
        module_name: str,
        request: FieldSelectionRequest):
    """
    更新字段选择状态

    Args:
        module_name: 模块名称
        request: 字段选择请求

    Returns:
        更新结果
    """
    try:
        logger.info(
            "更新字段选择",
            module_name=module_name,
            user_id=request.user_id,
            field_name=request.field_name,
            is_selected=request.is_selected,
        )

        success = await field_service.update_field_selection(
            module_name=module_name,
            user_id=request.user_id,
            field_name=request.field_name,
            is_selected=request.is_selected,
        )

        if success:
            return FieldConfigResponse(success=True, message="字段选择更新成功")
        else:
            raise HTTPException(status_code=500, detail="字段选择更新失败")

    except Exception:
        logger.error(
            "更新字段选择失败",
            module_name=module_name,
            user_id=request.user_id,
            field_name=request.field_name,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail=f"更新字段选择失败: {str(e)}")


@router.post("/modules/{module_name}/fields/chinese-name")
async def update_chinese_name(module_name: str, request: ChineseNameRequest):
    """
    更新字段中文名称

    Args:
        module_name: 模块名称
        request: 中文名称请求

    Returns:
        更新结果
    """
    try:
        logger.info(
            "更新字段中文名称",
            module_name=module_name,
            user_id=request.user_id,
            field_name=request.field_name,
            chinese_name=request.chinese_name,
        )

        success = await field_service.update_chinese_name(
            module_name=module_name,
            user_id=request.user_id,
            field_name=request.field_name,
            chinese_name=request.chinese_name,
        )

        if success:
            return FieldConfigResponse(success=True, message="中文名称更新成功")
        else:
            raise HTTPException(status_code=500, detail="中文名称更新失败")

    except Exception:
        logger.error(
            "更新中文名称失败",
            module_name=module_name,
            user_id=request.user_id,
            field_name=request.field_name,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail=f"更新中文名称失败: {str(e)}")


@router.get("/modules/{module_name}/selected-fields")
async def get_selected_fields(
    module_name: str, user_id: str = Query("Alice", description="用户ID")
):
    """
    获取用户选中的字段列表

    Args:
        module_name: 模块名称
        user_id: 用户ID

    Returns:
        选中的字段列表
    """
    try:
        logger.info("获取选中字段", module_name=module_name, user_id=user_id)

        selected_fields = await field_service.get_selected_fields(module_name, user_id)

        return FieldConfigResponse(
            success=True,
            message="成功获取选中字段",
            data={"selected_fields": selected_fields},
        )

    except Exception:
        logger.error(
            "获取选中字段失败", module_name=module_name, user_id=user_id, error=str(e)
        )
        raise HTTPException(status_code=500, detail=f"获取选中字段失败: {str(e)}")

        if not config:
            # 如果生成失败，返回基本结构
            config = {
                "module_name": module_name,
                "display_name": _get_module_display_name(module_name),
                "fields": {},
            }

        logger.info(
            "字段配置生成完成",
            module_name=module_name,
            fields_count=len(config.get("fields", {})),
        )

        return FieldConfigResponse(
            success=True, message="成功生成字段配置", data=config
        )

    except Exception:
        logger.error("获取字段配置失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"获取字段配置失败: {str(e)}")


@router.put("/modules/{module_name}/fields")
async def save_module_fields(module_name: str, request: FieldConfigRequest):
    """
    保存模块字段配置

    Args:
        module_name: 模块名称
        request: 包含用户ID和配置数据的请求

    Returns:
        保存结果
    """
    try:
        logger.info(
            "保存模块字段配置", module_name=module_name, user_id=request.user_id
        )

        # 验证配置数据
        if not request.user_config:
            raise HTTPException(status_code=400, detail="配置数据不能为空")

        # 验证字段配置
        validation_result = field_service.validate_field_config(
            request.user_config)
        if not validation_result.is_valid:
            logger.warning("配置验证失败", errors=validation_result.errors)
            # 不阻止保存，只记录警告

        # 保存配置
        success = await field_service.save_field_config(
            module_name, request.user_config
        )

        if success:
            logger.info("字段配置保存成功", module_name=module_name)
            return FieldConfigResponse(success=True, message="字段配置保存成功")
        else:
            raise HTTPException(status_code=500, detail="保存配置失败")

    except HTTPException:
        raise
    except Exception:
        logger.error("保存字段配置失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"保存字段配置失败: {str(e)}")


@router.get("/modules")
async def get_all_modules():
    """
    获取所有可用模块列表

    Returns:
        模块列表
    """
    try:
        modules = {
            "sales_order": "销售订单",
            "purchase_order": "采购订单",
            "production_order": "生产订单",
            "subcontract_order": "委外订单",
            "applyorder": "请购单",
            "subcontract_requisition": "委外请购",
            "product_receipt": "产品入库单",
            "purchase_receipt": "采购入库",
            "subcontract_receipt": "委外入库",
            "materialout": "材料出库单",
            "sales_out": "销售出库",
            "inventory": "现存量",
            "inventory_report": "现存量报表",
            "requirements_planning": "需求计划",
            "material_master": "物料档案",
        }

        return FieldConfigResponse(
            success=True, message="成功获取模块列表", data={"modules": modules}
        )

    except Exception:
        logger.error("获取模块列表失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取模块列表失败: {str(e)}")


@router.delete("/modules/{module_name}/fields")
async def delete_module_fields(
        module_name: str,
        user_id: str = Query("Alice")):
    """
    删除模块字段配置

    Args:
        module_name: 模块名称
        user_id: 用户ID

    Returns:
        删除结果
    """
    try:
        logger.info("删除模块字段配置", module_name=module_name, user_id=user_id)

        config_file = field_service.config_dir / \
            f"field_config_{module_name}.json"

        if config_file.exists():
            config_file.unlink()
            logger.info("字段配置删除成功", module_name=module_name)
            return FieldConfigResponse(success=True, message="字段配置删除成功")
        else:
            return FieldConfigResponse(success=True, message="配置文件不存在，无需删除")

    except Exception:
        logger.error("删除字段配置失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"删除字段配置失败: {str(e)}")


def _get_module_display_name(module_name: str) -> str:
    """获取模块显示名称"""
    display_names = {
        "sales_order": "销售订单",
        "purchase_order": "采购订单",
        "production_order": "生产订单",
        "subcontract_order": "委外订单",
        "applyorder": "请购单",
        "subcontract_requisition": "委外请购",
        "product_receipt": "产品入库单",
        "purchase_receipt": "采购入库",
        "subcontract_receipt": "委外入库",
        "materialout": "材料出库单",
        "sales_out": "销售出库",
        "inventory": "现存量",
        "inventory_report": "现存量报表",
        "requirements_planning": "需求计划",
        "material_master": "物料档案",
    }
    return display_names.get(module_name, module_name)


@router.get("/modules/{module_name}/fields")
async def get_module_fields_from_baseline(
    module_name: str,
    max_depth: int = Query(100, description="最大深度，真正无限制"),
    user_id: str = Query("default", description="用户ID"),
):
    """从基准文件获取模块字段 - 真正无限制深度"""
    try:
        # 基准文件路径
        baseline_file = Path("v3/config/baselines") / \
            f"{module_name}_baseline.json"

        if not baseline_file.exists():
            raise HTTPException(
                status_code=404, detail=f"模块 {module_name} 的基准文件不存在"
            )

        # 读取基准文件
        with open(baseline_file, "r", encoding="utf-8") as f:
            baseline_data = json.load(f)

        # 转换为API格式
        fields = {}
        for field_name, field_info in baseline_data.get("fields", {}).items():
            fields[field_name] = {
                "api_field_name": field_info["api_field_name"],
                "sample_value": field_info["sample_value"],
            }

        return {
            "success": True,
            "data": {
                "module_name": module_name,
                "field_count": len(fields),
                "fields": fields,
            },
            "message": f"成功获取 {len(fields)} 个字段",
        }

    except Exception:
        logger.error(f"获取模块字段失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取字段失败: {str(e)}")


@router.post("/baselines/{module_name}")
async def save_baseline_config(module_name: str, request: BaselineSaveRequest):
    """
    保存基准文件配置

    Args:
        module_name: 模块名称
        request: 基准文件保存请求

    Returns:
        保存结果
    """
    try:
        logger.info("保存基准文件", module_name=module_name, user_id=request.user_id)

        # 验证数据
        if not request.api_data:
            raise HTTPException(status_code=400, detail="API数据不能为空")

        # 创建基准文件目录
        baseline_dir = Path("v3/config/baselines")
        baseline_dir.mkdir(parents=True, exist_ok=True)

        # 清理和转换数据
        cleaned_fields = {}
        for field_data in request.api_data:
            # 获取字段名称
            field_name = (
                field_data.get("api_field_name")
                or field_data.get("api_name")
                or field_data.get("name")
            )
            if not field_name:
                continue

            # 清理技术字段和无效数据
            cleaned_field = {
                "api_field_name": field_name,
                "sample_value": field_data.get("sample_value", ""),
                "path": field_data.get("path", field_name),
                "depth": field_data.get("depth", 0),
            }

            # 过滤掉技术字段
            if not _is_technical_field(field_name):
                cleaned_fields[field_name] = cleaned_field

        # 创建基准配置
        baseline_config = {
            "module_name": module_name,
            "api_source": "YS-API",
            "extraction_time": datetime.now().isoformat(),
            "total_fields": len(cleaned_fields),
            "fields": cleaned_fields,
        }

        # 保存到文件
        baseline_file = baseline_dir / f"{module_name}_baseline.json"
        with open(baseline_file, "w", encoding="utf-8") as f:
            json.dump(baseline_config, f, ensure_ascii=False, indent=2)

        # 创建备份
        backup_dir = baseline_dir / "backups"
        backup_dir.mkdir(exist_ok=True)
        backup_file = (
            backup_dir /
            f"{module_name}_baseline_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(backup_file, "w", encoding="utf-8") as f:
            json.dump(baseline_config, f, ensure_ascii=False, indent=2)

        logger.info(
            "基准文件保存成功",
            module_name=module_name,
            fields_count=len(cleaned_fields),
            file_path=str(baseline_file),
        )

        return FieldConfigResponse(
            success=True,
            message="基准文件保存成功",
            data={
                "file_path": f"v3/config/baselines/{module_name}_baseline.json",
                "fields_count": len(cleaned_fields),
                "backup_path": str(backup_file),
            },
        )

    except Exception:
        logger.error(
            "保存基准文件失败",
            module_name=module_name,
            user_id=request.user_id,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail=f"保存基准文件失败: {str(e)}")


@router.post("/users/{user_id}/{module_name}")
async def save_user_config(
    user_id: str, module_name: str, request: UserConfigSaveRequest
):
    """
    保存用户配置

    Args:
        user_id: 用户ID
        module_name: 模块名称
        request: 用户配置保存请求

    Returns:
        保存结果
    """
    try:
        logger.info("保存用户配置", module_name=module_name, user_id=user_id)

        # 验证数据
        if not request.user_config:
            raise HTTPException(status_code=400, detail="用户配置数据不能为空")

        # 创建用户配置目录
        user_config_dir = Path("v3/config/data/user_field_config") / user_id
        user_config_dir.mkdir(parents=True, exist_ok=True)

        # 处理用户配置数据
        user_config = request.user_config

        # 确保配置包含必要字段
        if "module_name" not in user_config:
            user_config["module_name"] = module_name
        if "user_id" not in user_config:
            user_config["user_id"] = user_id
        if "last_updated" not in user_config:
            user_config["last_updated"] = datetime.now().isoformat()

        # 计算统计信息
        fields = user_config.get("fields", {})
        total_fields = len(fields)
        selected_fields = sum(
            1 for field in fields.values() if field.get("is_selected", False)
        )

        user_config["total_fields"] = total_fields
        user_config["selected_fields"] = selected_fields

        # 保存到文件
        config_file = user_config_dir / f"{module_name}.json"
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump(user_config, f, ensure_ascii=False, indent=2)

        # 创建备份
        backup_dir = user_config_dir / "backups"
        backup_dir.mkdir(exist_ok=True)
        backup_file = (
            backup_dir
            / f"{module_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        with open(backup_file, "w", encoding="utf-8") as f:
            json.dump(user_config, f, ensure_ascii=False, indent=2)

        logger.info(
            "用户配置保存成功",
            module_name=module_name,
            user_id=user_id,
            total_fields=total_fields,
            selected_fields=selected_fields,
            file_path=str(config_file),
        )

        return FieldConfigResponse(
            success=True,
            message="用户配置保存成功",
            data={
                "file_path": f"v3/config/data/user_field_config/{user_id}/{module_name}.json",
                "total_fields": total_fields,
                "selected_fields": selected_fields,
                "backup_path": str(backup_file),
            },
        )

    except Exception:
        logger.error(
            "保存用户配置失败", module_name=module_name, user_id=user_id, error=str(e)
        )
        raise HTTPException(status_code=500, detail=f"保存用户配置失败: {str(e)}")


def _is_technical_field(field_name: str) -> bool:
    """判断是否为技术字段"""
    technical_patterns = [
        "__",
        "_id",
        "_key",
        "_hash",
        "_token",
        "_session",
        "metadata",
        "internal",
        "system",
        "debug",
        "temp",
    ]

    field_lower = field_name.lower()
    return any(pattern in field_lower for pattern in technical_patterns)
