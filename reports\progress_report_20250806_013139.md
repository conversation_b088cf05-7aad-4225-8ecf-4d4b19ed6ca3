# 模块迁移进度报告

生成时间: 2025-08-06T01:31:39.635357

## 📊 总体进度

- **总模块数**: 15
- **已完成**: 1 (6.67%)
- **进行中**: 1
- **未开始**: 13

## 📋 模块状态详情

### ✅ 已完成模块
- 材料出库单列表查询

### 🔄 进行中模块
- 采购订单列表

### 📋 未开始模块
- 采购入库单列表
- 产品入库单列表查询
- 请购单列表查询
- 生产订单列表查询
- 委外订单列表
- 委外入库列表查询
- 委外申请列表查询
- 物料档案批量详情查询
- 现存量报表查询
- 销售出库列表查询
- 销售订单
- 需求计划
- 业务日志

## 🔍 检查点说明

每个模块需要完成以下4个检查点：

1. **测试通过** (test_passed) - 模块功能测试通过
2. **删除测试文件** (test_files_deleted) - 清理临时测试文件
3. **删除模拟数据** (mock_data_deleted) - 清理模拟数据文件
4. **真实数据跑通** (real_data_verified) - 使用真实数据验证功能

## 📈 使用方法

```bash
# 更新模块状态
python scripts/module_tracker.py --update "模块名" "检查点" true

# 生成进度报告
python scripts/module_tracker.py --report

# 生成Markdown报告
python scripts/module_tracker.py --markdown
```
