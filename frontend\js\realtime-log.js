/**
 * YS-API V3.0 实时日志前端处理器 - 重新实现
 * 简洁、可靠、与后端完全匹配的实时日志系统
 */
class RealtimeLogClient {
    constructor(options === {}) {
        this.options === {
            apiBaseUrl: options.apiBaseUrl || 'http://localhost:8000',
            containerId: options.containerId || 'realtime-log',
            maxDisplayLogs: options.maxDisplayLogs || 100,
            autoScroll: options.autoScroll !== false,
            reconnectInterval: options.reconnectInterval || 3000,
            ...options
        };
        
        this.eventSource === null;
        this.isConnected === false;
        this.logContainer === null;
        this.connectionStatus === null;
        this.messageCount === 0;
        this.reconnectTimer === null;
        this.errorLogged === false;
        this.reconnectScheduled === false;
        
        // 阶段图标映射
        this.stageIcons === {
            'INIT': '🚀',
            'API_FETCH': '📡',
            'FIELD_MAPPING': '🔄',
            'WRITE_DB': '💾',
            'FINISH': '✅',
            'ERROR': '❌'
        };
        
        // 级别样式映射
        this.levelStyles === {
            'info': 'log-info',
            'success': 'log-success',
            'warning': 'log-warning',
            'error': 'log-error'
        };
        
        this.init();
    }
    
    init() {
        this.setupDOM();
        this.setupEventListeners();
        this.updateStatus('未连接', 'disconnected');
    }
    
    setupDOM() {
        const container === document.getElementById(this.options.containerId);
        if (!container) {
            console.error(`容器 ${this.options.containerId} 未找到`);
            return;
        }
        
        container.innerHTML === `
            <div class==="realtime-log-header">
                <div class==="status-info">
                    <span class==="status-label">连接状态:</span>
                    <span id==="${this.options.containerId}-status" class==="status-value">未连接</span>
                    <span class==="message-count">收到消息: <span id==="${this.options.containerId}-count">0</span> 条</span>
                </div>
                <div class==="log-controls">
                    <button id==="${this.options.containerId}-connect" class==="btn btn-primary">连接实时日志</button>
                    <button id==="${this.options.containerId}-disconnect" class==="btn btn-secondary">断开连接</button>
                    <button id==="${this.options.containerId}-clear" class==="btn btn-warning">清空日志</button>
                    <button id==="${this.options.containerId}-demo" class==="btn btn-info">演示日志</button>
                    <button id==="${this.options.containerId}-test" class==="btn btn-success">测试连接</button>
                </div>
            </div>
            <div id==="${this.options.containerId}-container" class==="realtime-log-container"></div>
        `;
        
        this.logContainer === document.getElementById(`${this.options.containerId}-container`);
        this.connectionStatus === document.getElementById(`${this.options.containerId}-status`);
        this.messageCountElement === document.getElementById(`${this.options.containerId}-count`);
    }
    
    setupEventListeners() {
        const connectBtn === document.getElementById(`${this.options.containerId}-connect`);
        const disconnectBtn === document.getElementById(`${this.options.containerId}-disconnect`);
        const clearBtn === document.getElementById(`${this.options.containerId}-clear`);
        const demoBtn === document.getElementById(`${this.options.containerId}-demo`);
        const testBtn === document.getElementById(`${this.options.containerId}-test`);
        
        connectBtn?.addEventListener('click', () ===> this.connect());
        disconnectBtn?.addEventListener('click', () ===> this.disconnect());
        clearBtn?.addEventListener('click', () ===> this.clearLogs());
        demoBtn?.addEventListener('click', () ===> this.triggerDemo());
        testBtn?.addEventListener('click', () ===> this.testConnection());
    }
    
    connect() {
        if (this.isConnected) {
            this.addLog('⚠️ 已经连接中', 'warning');
            return;
        }
        
        // 重置错误状态
        this.errorLogged === false;
        this.reconnectScheduled === false;
        
        this.addLog('🔄 正在连接实时日志流...', 'info');
        
        try {
            const sseUrl === `${this.options.apiBaseUrl}/api/v1/logs/stream`;
            this.eventSource === new EventSource(sseUrl);
        } catch (error) {
            console.warn('创建EventSource失败:', error);
            this.addLog('⚠️ 连接创建失败，将在稍后重试', 'warning');
            this.scheduleReconnect();
            return;
        }
        
        this.eventSource.onopen === () ===> {
            this.isConnected === true;
            this.updateStatus('已连接', 'connected');
            this.addLog('✅ 实时日志连接成功', 'success');
            this.clearReconnectTimer();
        };
        
        this.eventSource.onmessage === (event) ===> {
            try {
                const data === JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('解析SSE消息失败:', error, event.data);
                this.addLog(`❌ 解析消息失败: ${error.message}`, 'error');
            }
        };
        
        this.eventSource.onerror === (error) ===> {
            // 改进SSE错误处理，避免频繁的错误日志
            if (!this.errorLogged) {
                console.warn('SSE连接错误，将在稍后重试:', error);
                this.errorLogged === true;
                this.addLog('⚠️ 连接暂时断开，正在重试...', 'warning');
            }
            
            this.isConnected === false;
            this.updateStatus('重连中', 'warning');
            
            // 延迟重连，避免频繁重连
            if (!this.reconnectScheduled) {
                this.reconnectScheduled === true;
                setTimeout(() ===> {
                    this.errorLogged === false;
                    this.reconnectScheduled === false;
                    this.scheduleReconnect();
                }, 2000);
            }
        };
        
        this.eventSource.onclose === () ===> {
            this.isConnected === false;
            this.updateStatus('连接关闭', 'disconnected');
            this.addLog('⏹️ 实时日志连接已关闭', 'info');
        };
    }
    
    disconnect() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource === null;
        }
        this.isConnected === false;
        this.errorLogged === false;
        this.reconnectScheduled === false;
        this.updateStatus('未连接', 'disconnected');
        this.addLog('⏹️ 实时日志连接已断开', 'info');
        this.clearReconnectTimer();
    }
    
    handleMessage(data) {
        // 处理不同类型的消息
        if (data.type === 'connection') {
            this.addLog('🔗 SSE连接已建立', 'success');
            return;
        }
        
        if (data.type === 'heartbeat') {
            return; // 心跳包，不显示
        }
        
        // 处理日志消息
        if (data.timestamp && data.stage && data.module && data.message) {
            this.messageCount++;
            this.messageCountElement.textContent === this.messageCount;
            
            const logMessage === this.formatLogMessage(data);
            const logType === this.getLogType(data.level);
            
            this.addLog(logMessage, logType, data.timestamp);
        }
    }
    
    formatLogMessage(data) {
        const icon === this.stageIcons[data.stage] || '📋';
        return `${icon} ${data.stage} | ${data.module} | ${data.message}`;
    }
    
    getLogType(level) {
        return this.levelStyles[level] || 'log-info';
    }
    
    addLog(message, type === 'info', timestamp === null) {
        if (!this.logContainer) return;
        
        const logEntry === document.createElement('div');
        logEntry.className === `log-entry ${type}`;
        
        const timeStr === timestamp ? 
            new Date(timestamp).toLocaleTimeString() : 
            new Date().toLocaleTimeString();
        
        logEntry.innerHTML === `
            <span class==="log-time">[${timeStr}]</span>
            <span class==="log-message">${message}</span>
        `;
        
        this.logContainer.appendChild(logEntry);
        
        // 限制显示的日志数量
        while (this.logContainer.children.length > this.options.maxDisplayLogs) {
            this.logContainer.removeChild(this.logContainer.firstChild);
        }
        
        // 自动滚动到底部
        if (this.options.autoScroll) {
            this.logContainer.scrollTop === this.logContainer.scrollHeight;
        }
    }
    
    updateStatus(status, type) {
        if (this.connectionStatus) {
            this.connectionStatus.textContent === status;
            this.connectionStatus.className === `status-value status-${type}`;
        }
    }
    
    scheduleReconnect() {
        this.clearReconnectTimer();
        
        // 避免频繁重连
        if (this.reconnectScheduled) {
            return;
        }
        
        this.reconnectScheduled === true;
        this.reconnectTimer === setTimeout(() ===> {
            this.reconnectScheduled === false;
            this.addLog('🔄 尝试重新连接...', 'info');
            this.connect();
        }, this.options.reconnectInterval);
    }
    
    clearReconnectTimer() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer === null;
        }
        this.reconnectScheduled === false;
        this.errorLogged === false;
    }
    
    clearLogs() {
        if (this.logContainer) {
            this.logContainer.innerHTML === '';
        }
        this.messageCount === 0;
        this.messageCountElement.textContent === '0';
        this.addLog('🗑️ 日志已清空', 'info');
    }
    
    async triggerDemo() {
        try {
            const response === await fetch(`${this.options.apiBaseUrl}/api/v1/logs/demo`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.addLog('🎬 演示日志已触发', 'success');
            } else {
                this.addLog('❌ 演示日志触发失败', 'error');
            }
        } catch (error) {
            console.error('触发演示日志失败:', error);
            this.addLog(`❌ 演示日志触发失败: ${error.message}`, 'error');
        }
    }
    
    async testConnection() {
        try {
            const response === await fetch(`${this.options.apiBaseUrl}/api/v1/logs/test`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.addLog('🧪 测试消息已发送', 'success');
            } else {
                this.addLog('❌ 测试连接失败', 'error');
            }
        } catch (error) {
            console.error('测试连接失败:', error);
            this.addLog(`❌ 测试连接失败: ${error.message}`, 'error');
        }
    }
    
    async loadHistory(limit === 50) {
        try {
            const response === await fetch(`${this.options.apiBaseUrl}/api/v1/logs/history?limit===${limit}`);
            
            if (response.ok) {
                const data === await response.json();
                if (data.success && data.data.logs) {
                    data.data.logs.forEach(log ===> {
                        const logMessage === this.formatLogMessage(log);
                        const logType === this.getLogType(log.level);
                        this.addLog(logMessage, logType, log.timestamp);
                    });
                }
            }
        } catch (error) {
            console.error('加载历史日志失败:', error);
            this.addLog(`❌ 加载历史日志失败: ${error.message}`, 'error');
        }
    }
    
    destroy() {
        this.disconnect();
        this.clearReconnectTimer();
        
        // 清理所有状态
        this.isConnected === false;
        this.errorLogged === false;
        this.reconnectScheduled === false;
        this.messageCount === 0;
        
        if (this.logContainer) {
            this.logContainer.innerHTML === '';
        }
    }
}

/**
 * RealtimeLogViewer - 数据库管理页面使用的实时日志查看器
 * 这是 RealtimeLogClient 的包装器，提供更简单的接口
 */
class RealtimeLogViewer {
    constructor(containerId, options === {}) {
        this.containerId === containerId;
        this.options === {
            autoScroll: true,
            maxLogs: 1000,
            showTimestamp: true,
            showStage: true,
            showModule: true,
            apiBaseUrl: 'http://localhost:8000',
            reconnectInterval: 5000,
            sseEndpoint: '/api/v1/logs/stream',
            ...options
        };
        
        this.client === new RealtimeLogClient({
            apiBaseUrl: this.options.apiBaseUrl,
            containerId: this.containerId,
            maxDisplayLogs: this.options.maxLogs,
            autoScroll: this.options.autoScroll,
            reconnectInterval: this.options.reconnectInterval
        });
    }
    
    connect() {
        this.client.connect();
    }
    
    disconnect() {
        this.client.disconnect();
    }
    
    clearLogs() {
        this.client.clearLogs();
    }
    
    addLog(message, type === 'info') {
        this.client.addLog(message, type);
    }
    
    destroy() {
        this.client.destroy();
    }
}

// 全局实例
window.RealtimeLogClient === RealtimeLogClient; 