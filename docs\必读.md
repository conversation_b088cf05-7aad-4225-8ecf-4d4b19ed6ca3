## 🚨 **代码优化重要提醒**

**在进行任何代码优化前，必须先查阅 `10-代码优化进度文档.md`**

### **核心原则**
- ✅ **数据逻辑不变**: 所有字段映射、计算规则、分析逻辑必须保持完全一致
- ✅ **API调用不变**: 保持与用友云API的调用逻辑完全一致  
- ✅ **数据库结构不变**: 保持表结构和字段类型完全一致
- ✅ **业务规则不变**: 保持所有业务规则和验证逻辑完全一致

### **优化约束**
- 绝不能改变任何分析逻辑或计算逻辑，否则将获取异常数据
- 所有数据处理、字段映射、计算规则必须保持完全一致
- 优化必须遵循文档中的风险控制措施和验证标准

---

每次必须先查阅 10-代码优化进度文档.md，重构实施计划.md，重构总结.md，API接口规范.md，数据处理规范.md，字段映射规范.md，系统架构设计.md，前端设计规范.md 等文档
不允许直接复制V2项目的代码。
每次完成更新 更新所有相关文档，特别是是API接口规范.md，数据处理规范.md，字段映射规范.md，系统架构设计.md，前端设计规范.md 等文档。
除致命性错误需要重新构建新的版本外，其他错误只允许在原有版本上修改，不允许重新构建新的版本。
不允许改变页面布局
测试文件在测试结束后，必须删除测试文件，不允许提交测试文件。
进入测试部份，必须全部用真实数据测试，不允许使用测试数据。
YS-API V3.0 确实是统一服务器架构。
修改代码后，必须检查旧代码或旧架构是否还存在，如存在，必须删除。
修改代码后，检查调用端是否需要修改。如有旧端口，不需被调用用，必须删除。
注意事项:
1. 字段名使用中文名称（基于field_configs中的chinese_name）
2. 备注类字段统一使用NVARCHAR(255)长度
3. 数值类型统一使用DECIMAL(18,4)精度
4. 重复字段名已自动去重
5. 只创建is_selected=true的字段
6. 数据库使用中文排序规则(Chinese_PRC_CI_AS)                                                       
端口8888 为当前系统端口，不允许更改，否则引起后端报错
注意写入数据库的字段的逻辑必须与创建的一致，否则报错
注意数据库的创建和数据库的写入所用的字段必须一致，否则报错。JOSN文件位置 config\filed_configs\目录下
遇到问题可以参考V2项目,但不能复制代码。
字段配置的后端代码和前端已确认，不允许修改。
不允许经常创建什么简化版，一堆文件。
前端需要动态日志，及运行日志记录查询
不允许生成md文档报告

## 🎯 **智能字段选择规则（最新优化版）**

### **第一优先级：特征自由项（必选）**
以下特征字段**强制选中**，不可过滤：
- XS31, CG02, CG03, CG04, CG05, SF04, SF05, SF06
- XXX0111, AA, CG00025, CG01, U9002, XS11, XS15

### **第二优先级：重要业务字段**
- **核心字段**：ORGID, 订单号, 编码, 名称, 数量, 状态, 日期
- **业务字段**：业务员, 仓库, 供应商, 客户, 来源单号

### **字段过滤规则**
- **ID字段过滤**：凡是后缀带"ID"的字段一律过滤（例外：ORGID, ORG_ID, ID本身）
- **Excel匹配**：如存在Excel需求文件，优先按Excel定义选择字段（已扩展中英文翻译字典至150+词汇）

### **⚠️ 重要：必须完全过滤的字段（绝对不允许选中）⚠️**
以下字段包含复杂嵌套数据，会导致数据库写入错误，**必须在所有阶段彻底过滤**：

🚫 **orderDefineCharacter** - 订单定义特征（包含大量嵌套JSON）
🚫 **orderDetailPrices** - 订单明细价格（包含复杂价格结构）  
🚫 **orderPrices** - 订单价格（包含多层价格信息）

**过滤策略**：在字段扁平化、智能选择规则、数据写入三个阶段彻底过滤
**技术原因**：数据长度超出NVARCHAR限制，结构过于复杂，非业务必需字段

### **⚠️ 重要：数据库表创建问题修复⚠️**

**常见错误及解决方案：**

1. **unknown_table错误**：
   - 错误信息：`数据库中已存在名为 'unknown_table' 的对象`
   - 原因：配置文件缺少表名信息
   - ✅ 已修复：自动使用模块名作为表名

2. **重复列名错误**：
   - 错误信息：`各表中的列名必须唯一。在表中多次指定了列名`
   - 原因：字段配置中存在重复的中文字段名
   - ✅ 已修复：自动添加序号处理重复列名

**解决建议：**
- 🔧 **重新创建JSON配置文件**：删除现有配置，重新从API生成
- 🔧 **使用强制重建表选项**：在同步时勾选"强制重建表"
- 🔧 **清理已存在的问题表**：手动删除数据库中的unknown_table表

- **过滤名单管理**：支持前端添加/删除自定义过滤项，使用localStorage持久化保存

### **匹配模式选择**
- **精确匹配**：字段名必须完全相等才匹配
- **包含匹配**：字段名包含关键词即可匹配（默认模式）

### **去重处理机制**
1. **第一次去重**：API返回的原字段名相同时，保留一个（因为相同名称来源于同一字段）
2. **第二次去重**：中文名称相同时，按优先级排序，只保留最高优先级的字段（避免数据库写入错误）

### **JSON配置文件优先**
- 如检测到模块已有JSON配置文件，直接加载现有配置，不进行重新生成
- 保持is_selected状态，用户可在前端调整选择状态
- 避免覆盖已确认的字段配置

### **数据类型规则**
- **特征字段**：xs31, cg02, cg03, cg04, cg05, sf04, sf05, sf06, xxx0111, aa, cg00025, cg01, u9002, xs11, xs15 → NVARCHAR(600)
- **其他NVARCHAR类型**：统一使用NVARCHAR(200)
- **数值类型**：BIGINT, DECIMAL(18,4)
- **日期类型**：DATETIME2
- **布尔类型**：BIT


## ⚠️ **API分页逻辑规范（必读，不能删除，必须按要求）**

### **分页逻辑强制要求：**

1. **每页500条**
   - 将pageSize从200改为500条
   - API客户端和快速同步服务都已更新

2. **智能分页逻辑**
   ```
   第1步: 获取第1页数据（500条）
   第2步: 判断是否需要更多页面
   第3步: 循环获取第2页、第3页...直到：
      - 某页数据 < 500条（最后一页）
      - 或者某页无数据（结束）
   ```

3. **详细进度显示（必须实现）**
   每次获取都会显示：
   - 📄 第X页获取N条记录
   - 📊 累计已获取XXX条 
   - 📄 第X页是最后一页，总共X页，XXX条

4. **安全限制**
   - 最大1000页（避免无限循环）
   - 支持50万条记录上限

5. **全量获取原则**
   - **禁止数据截断**：必须获取API返回的全部数据
   - **禁止页数限制**：不能设置人为的页数上限（除安全限制）
   - **禁止记录数限制**：除非用户明确指定record_limit参数

   全量分页获取，每页500条，自动循环获取所有页面

### **实施文件：**
- `v3/backend/app/services/fast_sync_service.py`
- `v3/backend/app/services/ys_api_client.py`

**任何修改API获取逻辑的开发都必须遵循以上规范，违反将导致数据不完整！**

## ⚠️ **自动同步功能修复记录（必读）**

### **已修复的问题（2025-07-16）**

1. **时间设置422错误**
   - 问题：设置时间为0小时时出现HTTP 422错误
   - 原因：前端parseInt返回NaN导致后端验证失败
   - 修复：添加`|| 0`默认值处理和时间范围验证

2. **禁用功能undefined错误**
   - 问题：禁用自动同步时返回undefined错误
   - 原因：异步函数调用和错误处理不当
   - 修复：统一错误处理和API调用逻辑

### **技术要点**
- 所有时间输入必须进行parseInt验证和范围检查
- 使用`parseInt(value) || 0`防止NaN
- 统一使用`API_CONFIG.BASE_URL`和`API_CONFIG.HEADERS`
- 实现完整的HTTP状态码检查和错误处理

### **修复的函数**
- `toggleAutoSync()` (任务计划页面)
- `toggleAutoSync()` (数据库管理页面)  
- `saveAutoSyncSettings()` (数据库管理页面)

### **测试验证**
- 支持0:00到23:59的所有时间设置
- 正确处理启用/禁用操作
- 完善的用户友好错误提示

### **前端与后端连接检查**
- 前端API配置：`API_CONFIG.BASE_URL = 'http://127.0.0.1:8888/api/v1'`
- 后端服务端口：8888
- 连接验证：所有API调用使用统一配置和错误处理

**⚠️ 重要提醒：修改自动同步相关功能时，必须遵循以上修复规范，确保输入验证和错误处理完整性！**

## ⚠️ **启动脚本端口配置（最新更新 - 端口标准化）**

### **标准化端口配置**
- **后端服务**: `http://localhost:8050` (固定，禁止改动)
- **前端服务**: `http://localhost:8060` (固定，禁止改动)
- **API接口**: `http://localhost:8050/api/v1`

### **启动方式**
1. **后端启动**: `python backend/start_server_fixed.py`
   - 固定在8050端口启动后端服务
   - 包含完整的错误处理和日志

2. **前端启动**: `python frontend/start_frontend_fixed.py`
   - 固定在8060端口启动前端服务
   - 自动代理API请求到后端

### **端口标准化说明**
- ✅ 已完成端口标准化：后端8050，前端8060
- ✅ 已废弃所有旧启动脚本和bat文件
- ✅ 已移除所有非标准端口配置
- ✅ 环境配置已标准化，禁止修改

### **常见问题**
- 如果遇到连接错误，请检查：
  1. 后端服务是否在8050端口正常运行
  2. 前端服务是否在8060端口正常运行
  3. 防火墙是否允许8050和8060端口