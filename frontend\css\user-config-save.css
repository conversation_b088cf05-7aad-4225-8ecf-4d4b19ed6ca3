/* 用户配置保存组件样式 */

/* 用户配置保存进度容器 */
#userConfigSaveProgress {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  margin-top: 16px;
  border: 1px solid rgba(39, 174, 96, 0.2);
  box-shadow: 0 4px 20px rgba(39, 174, 96, 0.1);
  transition: all 0.3s ease;
}

#userConfigSaveProgress.show {
  animation: userConfigSlideIn 0.4s ease-out;
}

#userConfigSaveProgress.hide {
  animation: userConfigSlideOut 0.3s ease-in;
}

/* 用户配置保存进度条样式 */
#userConfigProgressBar {
  height: 100%;
  background: linear-gradient(90deg, #27ae60 0%, #2ecc71 50%, #27ae60 100%);
  background-size: 200% 100%;
  width: 0%;
  border-radius: 6px;
  position: relative;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  animation: userConfigProgressShimmer 2s ease-in-out infinite;
}

#userConfigProgressBar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: userConfigProgressGlow 1.5s ease-in-out infinite;
}

/* 用户配置保存进度文本样式 */
#userConfigProgressText {
  color: #27ae60;
  font-weight: 600;
}

#userConfigProgressPercentage {
  color: #27ae60;
  background: rgba(39, 174, 96, 0.1);
  border: 1px solid rgba(39, 174, 96, 0.2);
}

#userConfigProgressDetails {
  background: rgba(39, 174, 96, 0.05);
  border-left: 3px solid #27ae60;
  color: #2c3e50;
}

/* 用户配置保存图标 */
.save-progress-icon {
  color: #27ae60;
}

/* 用户配置保存按钮状态 */
.save-user-config:hover:not(:disabled) {
  background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.save-user-config:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 用户配置保存完成状态 */
#userConfigSaveProgress.completed {
  border-color: #27ae60;
  box-shadow: 0 4px 20px rgba(39, 174, 96, 0.2);
}

#userConfigSaveProgress.completed #userConfigProgressBar {
  background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
}

#userConfigSaveProgress.completed #userConfigProgressPercentage {
  color: #27ae60;
  background: rgba(39, 174, 96, 0.15);
}

/* 用户配置保存错误状态 */
#userConfigSaveProgress.error {
  border-color: #e74c3c;
  box-shadow: 0 4px 20px rgba(231, 76, 60, 0.2);
}

#userConfigSaveProgress.error #userConfigProgressBar {
  background: linear-gradient(90deg, #e74c3c 0%, #c0392b 100%);
}

#userConfigSaveProgress.error #userConfigProgressPercentage {
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
}

#userConfigSaveProgress.error #userConfigProgressText {
  color: #e74c3c;
}

/* 用户配置保存动画 */
@keyframes userConfigSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes userConfigSlideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
}

@keyframes userConfigProgressShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes userConfigProgressGlow {
  0%, 100% {
    opacity: 0;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
    transform: translateX(100%);
  }
}

/* 用户配置保存阶段指示器 */
.user-config-save-stages {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  padding: 0 4px;
}

.user-config-save-stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.user-config-save-stage:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 12px;
  right: -50%;
  width: 100%;
  height: 2px;
  background: rgba(39, 174, 96, 0.2);
  z-index: 1;
}

.user-config-save-stage.completed::after {
  background: #27ae60;
}

.user-config-save-stage-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(39, 174, 96, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #7f8c8d;
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

.user-config-save-stage.completed .user-config-save-stage-dot {
  background: #27ae60;
  color: white;
  transform: scale(1.1);
}

.user-config-save-stage.active .user-config-save-stage-dot {
  background: #27ae60;
  color: white;
  animation: userConfigStagePulse 1.5s ease-in-out infinite;
  transform: scale(1.2);
}

.user-config-save-stage-label {
  font-size: 11px;
  color: #7f8c8d;
  margin-top: 6px;
  text-align: center;
  transition: color 0.3s ease;
}

.user-config-save-stage.completed .user-config-save-stage-label,
.user-config-save-stage.active .user-config-save-stage-label {
  color: #27ae60;
  font-weight: 500;
}

@keyframes userConfigStagePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1.2);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.3);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  #userConfigSaveProgress {
    padding: 16px;
  }
  
  .user-config-save-stages {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .user-config-save-stage {
    flex: none;
    min-width: 80px;
  }
  
  .user-config-save-stage:not(:last-child)::after {
    display: none;
  }
}

/* 用户配置保存通知样式 */
.notification.user-config-success {
  background: rgba(39, 174, 96, 0.9);
  border-left: 4px solid #27ae60;
}

.notification.user-config-error {
  background: rgba(231, 76, 60, 0.9);
  border-left: 4px solid #e74c3c;
}

/* 用户配置保存按钮加载状态 */
.save-user-config.loading {
  position: relative;
  color: transparent;
}

.save-user-config.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: userConfigButtonSpin 1s linear infinite;
}

@keyframes userConfigButtonSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 用户配置保存成功提示 */
.user-config-save-success {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(39, 174, 96, 0.1);
  border: 1px solid rgba(39, 174, 96, 0.2);
  border-radius: 8px;
  color: #27ae60;
  font-size: 14px;
  margin-top: 12px;
  animation: userConfigSuccessFadeIn 0.5s ease-out;
}

.user-config-save-success .success-icon {
  font-size: 16px;
}

@keyframes userConfigSuccessFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 用户配置保存统计信息 */
.user-config-save-stats {
  display: flex;
  gap: 16px;
  margin-top: 12px;
  font-size: 12px;
  color: #7f8c8d;
}

.user-config-save-stat {
  display: flex;
  align-items: center;
  gap: 4px;
}

.user-config-save-stat .stat-value {
  font-weight: 600;
  color: #27ae60;
}