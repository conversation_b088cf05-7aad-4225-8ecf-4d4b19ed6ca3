import sys
from datetime import date
from pathlib import Path

import pyodbc
import pytest

from scripts.rollback_batch_writes import BatchWriteRollback

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回滚脚本测试用例
覆盖死锁处理、异常处理和重试机制
"""


# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))


class TestBatchWriteRollback:
    """批量写入回滚测试"""

    def setup_method(self):
        """测试前置设置"""
        self.rollback = BatchWriteRollback()

    @patch('scripts.rollback_batch_writes.pyodbc.connect')
    def test_successful_rollback(self, mock_connect):
        """测试成功回滚"""
        # 模拟连接和游标
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_conn

        # 模拟表存在
        mock_cursor.fetchone.side_effect = [(1,), (10,)]  # 表存在，删除10条记录

        result = self.rollback.rollback_table_data(
            'test_table', date(2025, 1, 20))

        assert result['success']
        assert result['deleted_count'] == 10
        mock_conn.commit.assert_called_once()

    @patch('scripts.rollback_batch_writes.pyodbc.connect')
    def test_table_not_exists(self, mock_connect):
        """测试表不存在的情况"""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_conn

        # 模拟表不存在
        mock_cursor.fetchone.return_value = (0,)

        result = self.rollback.rollback_table_data(
            'nonexistent_table', date(2025, 1, 20)
        )

        assert result['success'] == False
        assert '不存在' in result['error']

    @patch('scripts.rollback_batch_writes.pyodbc.connect')
    def test_invalid_table_name(self, mock_connect):
        """测试无效表名（SQL注入防护）"""
        result = self.rollback.rollback_table_data(
            'test_table; DROP TABLE users;', date(2025, 1, 20)
        )

        assert result['success'] == False
        assert '无效的表名' in result['error']
        # 确保不会尝试连接数据库
        mock_connect.assert_not_called()

    @patch('scripts.rollback_batch_writes.pyodbc.connect')
    def test_deadlock_handling(self, mock_connect):
        """测试死锁处理"""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_conn

        # 模拟表存在
        mock_cursor.fetchone.return_value = (1,)

        # 模拟死锁错误
        deadlock_error = pyodbc.OperationalError(
            "Transaction (Process ID 52) was deadlocked on lock resources with another process and has been chosen as the deadlock victim. Error code: 1205"
        )
        mock_cursor.execute.side_effect = [
            None,
            deadlock_error,
        ]  # 第一次检查表存在，第二次触发死锁

        result = self.rollback.rollback_table_data(
            'test_table', date(2025, 1, 20))

        assert result['success'] == False
        assert result['retryable']
        assert '死锁错误' in result['error']
        mock_conn.rollback.assert_called_once()

    @patch('scripts.rollback_batch_writes.pyodbc.connect')
    def test_integrity_error_handling(self, mock_connect):
        """测试完整性约束错误"""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_conn

        # 模拟表存在
        mock_cursor.fetchone.return_value = (1,)

        # 模拟完整性约束错误
        integrity_error = pyodbc.IntegrityError(
            "FOREIGN KEY constraint failed")
        mock_cursor.execute.side_effect = [None, integrity_error]

        result = self.rollback.rollback_table_data(
            'test_table', date(2025, 1, 20))

        assert result['success'] == False
        assert result['retryable'] == False
        assert '数据完整性错误' in result['error']
        mock_conn.rollback.assert_called_once()

    @patch('scripts.rollback_batch_writes.pyodbc.connect')
    def test_general_database_error(self, mock_connect):
        """测试一般数据库错误"""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_conn

        # 模拟表存在
        mock_cursor.fetchone.return_value = (1,)

        # 模拟一般数据库错误
        db_error = pyodbc.Error("Connection timeout")
        mock_cursor.execute.side_effect = [None, db_error]

        result = self.rollback.rollback_table_data(
            'test_table', date(2025, 1, 20))

        assert result['success'] == False
        assert result['retryable'] == False
        assert '数据库错误' in result['error']
        mock_conn.rollback.assert_called_once()

    @patch('scripts.rollback_batch_writes.pyodbc.connect')
    def test_connection_failure(self, mock_connect):
        """测试连接失败"""
        mock_connect.side_effect = pyodbc.Error("Cannot connect to database")

        result = self.rollback.rollback_table_data(
            'test_table', date(2025, 1, 20))

        assert result['success'] == False
        assert '数据库错误' in result['error']

    def test_table_name_validation(self):
        """测试表名验证逻辑"""
        valid_names =
        ['users',
         'user_data',
         'UserTable',
         'table123',
         '_internal'
         ]
        invalid_names = [
            'table; DROP TABLE users;',  # SQL注入
            'table--comment',  # SQL注释
            'table/*comment*/',  # SQL注释
            'table with space',  # 空格
            '123table',  # 数字开头
            'table-name',  # 连字符
            '',  # 空字符串
            'table\'',  # 单引号
            'table"',  # 双引号
        ]

        for name in valid_names:
            result = self.rollback.rollback_table_data(name, date(2025, 1, 20))
            # 即使表名有效，由于没有数据库连接，也会失败，但不应该是"无效表名"错误
            assert '无效的表名' not in result.get(
                'error', ''
            ), f"表名 {name} 应该是有效的"

        for name in invalid_names:
            result = self.rollback.rollback_table_data(name, date(2025, 1, 20))
            assert result['success'] == False
            assert '无效的表名' in result['error'], f"表名 {name} 应该是无效的"


class TestRetryLogic:
    """重试逻辑测试"""

    def setup_method(self):
        """测试前置设置"""
        self.rollback = BatchWriteRollback()

    @patch('scripts.rollback_batch_writes.pyodbc.connect')
    def test_retry_on_deadlock(self, mock_connect):
        """测试死锁时的重试逻辑"""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_conn

        # 模拟表存在
        mock_cursor.fetchone.side_effect = [
            (1,),  # 第一次：表存在检查
            (1,),  # 第二次：表存在检查（重试）
            (5,),  # 第三次：成功删除5条记录
        ]

        # 第一次执行失败（死锁），第二次成功
        deadlock_error = pyodbc.OperationalError(
            "deadlock detected. Error code: 1205")
        mock_cursor.execute.side_effect = [
            None,  # 第一次表检查
            deadlock_error,  # 第一次DELETE失败
            None,  # 第二次表检查
            None,  # 第二次DELETE成功
        ]

        # 模拟重试逻辑
        max_retries = 3
        for attempt in range(max_retries):
            result = self.rollback.rollback_table_data(
                'test_table', date(2025, 1, 20))

            if result['success']:
                break
            elif result.get('retryable') and attempt < max_retries - 1:
                continue
            else:
                break

        # 最终应该成功（模拟第二次重试成功的情况）
        # 在实际重试逻辑中，这需要在调用方实现

    def test_no_retry_on_integrity_error(self):
        """测试完整性错误不重试"""
        # 这个测试验证完整性错误返回 retryable: False
        with patch('scripts.rollback_batch_writes.pyodbc.connect') as mock_connect:
            mock_conn = Mock()
            mock_cursor = Mock()
            mock_conn.cursor.return_value = mock_cursor
            mock_connect.return_value = mock_conn

            mock_cursor.fetchone.return_value = (1,)
            integrity_error = pyodbc.IntegrityError(
                "FOREIGN KEY constraint failed")
            mock_cursor.execute.side_effect = [None, integrity_error]

            result = self.rollback.rollback_table_data(
                'test_table', date(2025, 1, 20))

            assert result['success'] == False
            assert result['retryable'] == False


class TestEdgeCases:
    """边界情况测试"""

    def setup_method(self):
        """测试前置设置"""
        self.rollback = BatchWriteRollback()

    @patch('scripts.rollback_batch_writes.pyodbc.connect')
    def test_empty_table_rollback(self, mock_connect):
        """测试空表回滚"""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_conn

        # 模拟表存在但没有数据
        mock_cursor.fetchone.side_effect = [(1,), (0,)]  # 表存在，删除0条记录

        result = self.rollback.rollback_table_data(
            'empty_table', date(2025, 1, 20))

        assert result['success']
        assert result['deleted_count'] == 0

    @patch('scripts.rollback_batch_writes.pyodbc.connect')
    def test_large_data_rollback(self, mock_connect):
        """测试大数据量回滚"""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_conn

        # 模拟表存在且有大量数据
        mock_cursor.fetchone.side_effect = [(1,), (100000,)]  # 表存在，删除10万条记录

        result = self.rollback.rollback_table_data(
            'large_table', date(2025, 1, 20))

        assert result['success']
        assert result['deleted_count'] == 100000

    def test_future_date_rollback(self):
        """测试未来日期回滚"""
        future_date = date(2030, 12, 31)

        with patch('scripts.rollback_batch_writes.pyodbc.connect') as mock_connect:
            mock_conn = Mock()
            mock_cursor = Mock()
            mock_conn.cursor.return_value = mock_cursor
            mock_connect.return_value = mock_conn

            mock_cursor.fetchone.side_effect = [(1,), (0,)]  # 表存在，但未来日期无数据

            result = self.rollback.rollback_table_data(
                'test_table', future_date)

            assert result['success']
            assert result['deleted_count'] == 0


if __name__ == '__main__':
    pytest.main([__file__,
                 '-v',
                 '--cov=scripts.rollback_batch_writes',
                 '--cov-report=html'])
