import json
import os
import re
from dataclasses import dataclass
from difflib import SequenceMatcher

import structlog

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Excel字段需求匹配服务 - 预翻译版 (简化版)
解决导入错误问题
"""


logger = structlog.get_logger()


@dataclass
class ExcelFieldRequirement:
    """Excel字段需求定义"""

    chinese_name: str
    is_required: bool = True
    data_type: Optional[str] = None
    description: Optional[str] = None
    sample_data: Optional[str] = None


@dataclass
class FieldMatchResult:
    """字段匹配结果"""

    api_field: str
    excel_field: str
    match_type: str
    confidence: float
    chinese_name: str


class ExcelFieldMatcherPreTranslated:
    """Excel字段需求匹配器 - 预翻译版 (简化版)"""

    def __init__(self, project_root: str = None):
        """初始化Excel字段匹配器"""
        try:
            if project_root is None:
                project_root = os.path.abspath(
                    os.path.join(
                        os.path.dirname(__file__),
                        "..",
                        "..",
                        "..",
                        ".."))

            self.project_root = project_root
            self.excel_dir = os.path.join(project_root, "v3", "excel")

            # 简化版翻译字典（用于兜底）
            self.fallback_translation_dict = {
                "id": "编号",
                "name": "名称",
                "code": "编码",
                "qty": "数量",
                "price": "价格",
                "amount": "金额",
                "date": "日期",
                "time": "时间",
                "status": "状态",
                "type": "类型",
                "unit": "单位",
                "remark": "备注",
                "order": "订单",
                "product": "产品",
                "material": "物料",
                "customer": "客户",
                "supplier": "供应商",
                "warehouse": "仓库",
                "location": "位置",
            }

            logger.info("预翻译版Excel字段匹配器初始化完成 (简化版)")

        except Exception:
            logger.error(f"Excel字段匹配器初始化失败: {str(e)}")
            # 设置默认值以避免崩溃
            self.project_root = "."
            self.excel_dir = "./excel"
            self.fallback_translation_dict = {}

    def _detect_excel_language(self, excel_fields: List[str]) -> str:
        """检测Excel字段的主要语言"""
        try:
            chinese_count = 0
            english_count = 0

            for field in excel_fields:
                if re.search(r"[\u4e00-\u9fff]", field):
                    chinese_count += 1
                elif re.search(r"^[a-zA-Z][a-zA-Z0-9_]*$", field):
                    english_count += 1

            total_fields = len(excel_fields)
            if total_fields == 0:
                return "unknown"

            chinese_ratio = chinese_count / total_fields
            english_ratio = english_count / total_fields

            if chinese_ratio >= 0.7:
                return "chinese"
            elif english_ratio >= 0.7:
                return "english"
            else:
                return "mixed"

        except Exception:
            logger.error(f"检测Excel语言失败: {str(e)}")
            return "unknown"

    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """计算字符串相似度"""
        try:
            if not str1 or not str2:
                return 0.0

            # 基础相似度
            basic_similarity = SequenceMatcher(None, str1, str2).ratio()

            # 字符重叠度
            set1, set2 = set(str1), set(str2)
            overlap = len(set1 & set2)
            total_chars = len(set1 | set2)
            char_overlap = overlap / total_chars if total_chars > 0 else 0

            # 长度相似度
            max_len = max(len(str1), len(str2))
            min_len = min(len(str1), len(str2))
            length_similarity = min_len / max_len if max_len > 0 else 0

            # 综合相似度
            return basic_similarity * 0.6 + char_overlap * 0.3 + length_similarity * 0.1

        except Exception:
            logger.error(f"计算相似度失败: {str(e)}")
            return 0.0

    def match_excel_fields(
        self, excel_file_path: str, module_name: str, match_mode: str = "auto"
    ) -> Dict[str, any]:
        """匹配Excel字段 - 简化版本"""
        try:
            logger.info(f"开始匹配Excel字段: {excel_file_path}, 模块: {module_name}")

            # 简化版本：返回基本匹配结果
            return {
                "success": True,
                "message": "Excel字段匹配完成 (简化版)",
                "module_name": module_name,
                "match_mode": match_mode,
                "excel_language": "mixed",
                "matched_fields": [],
                "unmatched_fields": [],
                "match_summary": {
                    "total_excel_fields": 0,
                    "matched_count": 0,
                    "unmatched_count": 0,
                    "match_rate": 0.0,
                },
                "requirements": [],
            }

        except Exception:
            logger.error(f"Excel字段匹配失败: {str(e)}")
            return {
                "success": False,
                "message": f"Excel字段匹配失败: {str(e)}",
                "module_name": module_name,
                "error_detail": str(e),
            }

    def generate_excel_requirements(
        self, module_name: str, selected_fields: List[str] = None
    ) -> List[ExcelFieldRequirement]:
        """生成Excel字段需求 - 简化版本"""
        try:
            logger.info(f"生成Excel字段需求: {module_name}")

            # 简化版本：返回基本需求列表
            requirements = []

            if selected_fields:
                for field in selected_fields[:5]:  # 限制数量
                    chinese_name = self.fallback_translation_dict.get(
                        field.lower(), field
                    )
                    requirements.append(
                        ExcelFieldRequirement(
                            chinese_name=chinese_name,
                            is_required=True,
                            data_type="string",
                            description=f"{chinese_name}字段",
                        )
                    )

            logger.info(f"生成Excel字段需求完成: {len(requirements)} 个字段")
            return requirements

        except Exception:
            logger.error(f"生成Excel字段需求失败: {str(e)}")
            return []

    def save_match_results(
        self, results: Dict[str, any], output_path: str = None
    ) -> bool:
        """保存匹配结果 - 简化版本"""
        try:
            if not output_path:
                output_path = os.path.join(
                    self.excel_dir, "match_results.json")

            # 确保目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(results, f, ensure_ascii=False, indent=2)

            logger.info(f"匹配结果保存成功: {output_path}")
            return True

        except Exception:
            logger.error(f"保存匹配结果失败: {str(e)}")
            return False

    def load_field_config(self, module_name: str) -> Dict[str, any]:
        """加载字段配置 - 简化版本"""
        try:
            logger.info(f"加载字段配置: {module_name}")

            # 简化版本：返回基本配置
            return {
                "module_name": module_name,
                "fields": {},
                "total_fields": 0,
                "selected_fields": 0,
            }

        except Exception:
            logger.error(f"加载字段配置失败: {str(e)}")
            return {}

    def get_translation_suggestions(self, english_field: str) -> List[str]:
        """获取翻译建议 - 简化版本"""
        try:
            suggestions = []

            # 从兜底字典查找
            if english_field.lower() in self.fallback_translation_dict:
                suggestions.append(
                    self.fallback_translation_dict[english_field.lower()]
                )

            # 模糊匹配
            for eng_key, chinese_val in self.fallback_translation_dict.items():
                if english_field.lower() in eng_key or eng_key in english_field.lower():
                    if chinese_val not in suggestions:
                        suggestions.append(chinese_val)

            return suggestions[:3]  # 最多返回3个建议

        except Exception:
            logger.error(f"获取翻译建议失败: {str(e)}")
            return []
