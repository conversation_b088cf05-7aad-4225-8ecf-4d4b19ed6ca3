#!/bin/bash
# Day 5: API差分测试脚本
# 自动对比新旧系统的API响应差异

PROXY_BASE="http://127.0.0.1:9000"
LOG_FILE="legacy_snapshots/diff_test_$(date +%Y%m%d_%H%M%S).log"

echo "🔍 开始API差分测试 - $(date)" | tee "$LOG_FILE"
echo "代理地址: $PROXY_BASE" | tee -a "$LOG_FILE"
echo "=" | tee -a "$LOG_FILE"

# 测试端点列表
ENDPOINTS=(
    "/health"
    "/api/v1/health"
    "/api/v1/config/modules"
)

DIFF_COUNT=0
TOTAL_TESTS=0

for endpoint in "${ENDPOINTS[@]}"; do
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    echo "📡 测试端点: $endpoint" | tee -a "$LOG_FILE"
    
    # 获取旧系统响应
    OLD_RESULT=$(curl -s "$PROXY_BASE$endpoint" 2>/dev/null || echo "ERROR")
    
    # 获取新系统响应  
    NEW_RESULT=$(curl -s "$PROXY_BASE$endpoint?version=new" 2>/dev/null || echo "ERROR")
    
    # 比较响应
    if [ "$OLD_RESULT" = "$NEW_RESULT" ]; then
        echo "✅ 响应一致" | tee -a "$LOG_FILE"
    else
        echo "⚠️ 响应差异检测到" | tee -a "$LOG_FILE"
        echo "旧系统: $OLD_RESULT" | tee -a "$LOG_FILE"
        echo "新系统: $NEW_RESULT" | tee -a "$LOG_FILE"
        DIFF_COUNT=$((DIFF_COUNT + 1))
    fi
    
    echo "" | tee -a "$LOG_FILE"
done

echo "📊 差分测试结果:" | tee -a "$LOG_FILE"
echo "总测试数: $TOTAL_TESTS" | tee -a "$LOG_FILE"
echo "发现差异: $DIFF_COUNT" | tee -a "$LOG_FILE"
echo "一致率: $(( (TOTAL_TESTS - DIFF_COUNT) * 100 / TOTAL_TESTS ))%" | tee -a "$LOG_FILE"

if [ $DIFF_COUNT -gt 0 ]; then
    echo "⚠️ 检测到API差异，建议人工确认" | tee -a "$LOG_FILE"
    exit 1
else
    echo "✅ 所有API响应一致" | tee -a "$LOG_FILE"
    exit 0
fi
