import argparse
import json
import re
import shutil
from datetime import datetime
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 自动化页面迁移脚本
自动化处理页面迁移任务，减少手工操作
"""


class AutoMigrationTool:
    def __init___(self, frontend_dir: str):
    """TODO: Add function description."""
        self.frontend_dir = Path(frontend_dir)
        self.backup_dir = self.frontend_dir / "backup"
        self.migrated_dir = self.frontend_dir / "migrated"

        # 确保目录存在
        self.backup_dir.mkdir(exist_ok=True)
        self.migrated_dir.mkdir(exist_ok=True)

        # 组件模板
        self.component_templates = {
            'field-config': {
                'required_components': [
                    'apiClient',
                    'fieldUtils',
                    'validationUtils',
                    'fieldDeduplicationEnhancer',
                ],
                'template_file': 'field-config-template.html',
            },
            'data-management': {
                'required_components': [
                    'apiClient',
                    'validationUtils',
                    'errorHandler',
                    'notificationSystem',
                ],
                'template_file': 'data-management-template.html',
            },
            'report': {
                'required_components': ['apiClient', 'fieldUtils', 'errorHandler'],
                'template_file': 'report-template.html',
            },
            'general': {
                'required_components': [
                    'apiClient',
                    'errorHandler',
                    'notificationSystem',
                ],
                'template_file': 'general-template.html',
            },
        }

        self.logger.info("🔧 自动化迁移工具已初始化")

    def analyze_page(self, file_path: Path) -> Dict:
        """分析页面内容和复杂度"""
        self.logger.info(f"🔍 分析页面: {file_path.name}")

        if not file_path.exists():
            raise FileNotFoundError(f"页面文件不存在: {file_path}")

        content = file_path.read_text(encoding='utf-8')

        analysis = {
            'file_path': str(file_path),
            'file_name': file_path.name,
            'page_type': self._detect_page_type(content),
            'complexity': self._calculate_complexity(content),
            'has_old_components': self._detect_old_components(content),
            'dependencies': self._extract_dependencies(content),
            'custom_functions': self._extract_custom_functions(content),
            'size_kb': len(content.encode('utf-8')) / 1024,
            'line_count': content.count('\n') + 1,
        }

        self.logger.info(
            f"📊 分析完成: 类型={analysis['page_type']}, 复杂度={analysis['complexity']}"
        )
        return analysis

    def _detect_page_type(self, content: str) -> str:
        """检测页面类型"""
        patterns = {
            'field-config': [
                r'field-config',
                r'字段配置',
                r'fieldUtils',
                r'validation',
            ],
            'data-management': [
                r'database',
                r'数据管理',
                r'增删改查',
                r'data.*management',
            ],
            'report': [r'report', r'报表', r'chart', r'统计'],
            'excel': [r'excel', r'表格', r'导入导出'],
            'test': [r'test', r'测试', r'demo'],
        }

        content_lower = content.lower()

        for page_type, keywords in patterns.items():
            if any(re.search(keyword, content_lower) for keyword in keywords):
                return page_type

        return 'general'

    def _calculate_complexity(self, content: str) -> str:
        """计算页面复杂度"""
        score = 0

        # 脚本数量
        script_count = len(re.findall(r'<script', content))
        score += script_count * 2

        # 函数数量
        function_count = len(re.findall(r'function\s+\w+', content))
        score += function_count * 3

        # 全局变量使用
        global_vars = len(re.findall(r'window\.\w+', content))
        score += global_vars

        # 事件监听器数量
        event_listeners = len(re.findall(r'addEventListener', content))
        score += event_listeners * 2

        # AJAX请求数量
        ajax_calls = len(
    re.findall(
        r'fetch\(|XMLHttpRequest|\.ajax\(', content))
        score += ajax_calls * 3

        if score < 20:
            return 'simple'
        elif score < 50:
            return 'medium'
        else:
            return 'complex'

    def _detect_old_components(self, content: str) -> bool:
        """检测是否使用旧组件架构"""
        old_patterns = [
            r'window\.apiClient\s*=',
            r'window\.fieldUtils\s*=',
            r'window\.validationUtils\s*=',
            r'new\s+UnifiedAPIClient',
            r'new\s+FieldUtils',
            r'new\s+ValidationUtils',
        ]

        return any(re.search(pattern, content) for pattern in old_patterns)

    def _extract_dependencies(self, content: str) -> List[str]:
        """提取页面依赖"""
        dependencies = []

        # 脚本引用
        script_refs = re.findall(
    r'<script[^>]*src=["\']([^"\']*)["\']', content)
        dependencies.extend(script_refs)

        # 组件使用
        component_uses = re.findall(r'window\.(\w+)', content)
        dependencies.extend(set(component_uses))

        return list(set(dependencies))

    def _extract_custom_functions(self, content: str) -> List[str]:
        """提取自定义函数"""
        functions = re.findall(r'function\s+(\w+)', content)
        return list(set(functions))

    def backup_page(self, file_path: Path) -> Path:
        """备份原始页面"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
        backup_path = self.backup_dir / backup_name

        shutil.copy2(file_path, backup_path)
        self.logger.info(f"💾 已备份: {backup_path}")

        return backup_path

    def generate_migrated_page(self, analysis: Dict) -> str:
        """生成迁移后的页面代码"""
        page_type = analysis['page_type']
        if page_type not in self.component_templates:
            page_type = 'general'

        template = self.component_templates[page_type]

        # 读取原始内容
        original_path = Path(analysis['file_path'])
        original_content = original_path.read_text(encoding='utf-8')

        # 生成新的页面结构
        migrated_content = self._build_new_page_structure(
            original_content, template, analysis
        )

        return migrated_content

    def _build_new_page_structure(
        self, original_content: str, template: Dict, analysis: Dict
    ) -> str:
        """构建新的页面结构"""
        # 提取原始页面的主要内容
        body_content = self._extract_body_content(original_content)
        custom_scripts = self._extract_custom_scripts(original_content)
        custom_styles = self._extract_custom_styles(original_content)

        # 构建新的HTML结构
        new_html = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{analysis['file_name']} - 已迁移到新架构</title>

    <!-- 新架构核心文件 -->
    <script src="js/core/component-manager.js"></script>
    <script src="js/core/app-bootstrap.js"></script>

    <!-- 所需组件 -->
{self._generate_component_imports(template['required_components'])}

    <!-- 自定义样式 -->
{custom_styles}
</head>
<body>
    <!-- 迁移标识 -->
    <div style="position: fixed; top: 10px; right: 10px; background: #4CAF50; color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px; z-index: 9999;">
        ✅ 已迁移到新架构
    </div>

    <!-- 原始页面内容 -->
{body_content}

    <!-- 新架构初始化脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', async function() {{
            console.log('🚀 初始化新架构页面: {analysis['file_name']}');

            // 启动应用
            await window.startApp({{
                environment: 'production',
                features: {
    self._generate_features_config(
        template['required_components'])}
            }});

            // 获取所需组件
{self._generate_component_getters(template['required_components'])}

            // 调用原始初始化逻辑
            if (typeof initializePage === 'function') {{
                initializePage();
            }}

            console.log('✅ 页面初始化完成: {analysis['file_name']}');
        }});

        // 原始自定义脚本（已适配新架构）
{self._adapt_custom_scripts(custom_scripts)}
    </script>
</body>
</html>"""

        return new_html

    def _extract_body_content(self, content: str) -> str:
        """提取body内容"""
        body_match = re.search(
            r'<body[^>]*>(.*?)</body>', content, re.DOTALL | re.IGNORECASE
        )
        if body_match:
            body_content = body_match.group(1)
            # 移除内联脚本
            body_content = re.sub(
                r'<script[^>]*>.*?</script>', '', body_content, flags=re.DOTALL
            )
            return body_content.strip()
        return '<!-- 未找到body内容 -->'

    def _extract_custom_scripts(self, content: str) -> str:
        """提取自定义脚本"""
        scripts = []
        script_matches = re.findall(
    r'<script[^>]*>(.*?)</script>', content, re.DOTALL)

        for script in script_matches:
            # 跳过外部脚本引用
            if not script.strip() or 'src=' in script:
                continue
            scripts.append(script.strip())

        return '\n\n'.join(scripts)

    def _extract_custom_styles(self, content: str) -> str:
        """提取自定义样式"""
        styles = []

        # 提取<style>标签内容
        style_matches = re.findall(
    r'<style[^>]*>(.*?)</style>', content, re.DOTALL)
        for style in style_matches:
            styles.append(f"    <style>\n{style}\n    </style>")

        # 提取外部CSS引用
        css_matches = re.findall(
    r'<link[^>]*rel=["\']stylesheet["\'][^>]*>', content)
        for css_link in css_matches:
            styles.append(f"    {css_link}")

        return '\n'.join(styles) if styles else '    <!-- 无自定义样式 -->'

    def _generate_component_imports(self, components: List[str]) -> str:
        """生成组件导入脚本"""
        import_map = {
            'apiClient': '    <script src="js/common/api-client.js"></script>',
            'fieldUtils': '    <script src="js/common/field-utils.js"></script>',
            'validationUtils': '    <script src="js/common/validation-utils.js"></script>',
            'errorHandler': '    <script src="js/common/error-handler.js"></script>',
            'notificationSystem': '    <script src="js/notification-system.js"></script>',
            'fieldDeduplicationEnhancer': '    <script src="field-deduplication-enhancer.js"></script>',
        }

        imports = []
        for component in components:
            if component in import_map:
                imports.append(import_map[component])

        return '\n'.join(imports)

    def _generate_features_config(self, components: List[str]) -> str:
        """生成功能配置"""
        features = {}

        if 'validationUtils' in components:
            features['validation'] = True
        if 'errorHandler' in components:
            features['errorHandling'] = True
        if 'notificationSystem' in components:
            features['notifications'] = True
        if 'fieldDeduplicationEnhancer' in components:
            features['fieldDeduplication'] = True

        return json.dumps(features, indent=16)

    def _generate_component_getters(self, components: List[str]) -> str:
        """生成组件获取代码"""
        getters = []

        for component in components:
            var_name = (
                component.replace('Utils', '')
                .replace('System', '')
                .replace('Handler', 'Handler')
                .replace('Client', 'Client')
            )
            if component == 'fieldDeduplicationEnhancer':
                var_name = 'enhancer'
            elif component == 'notificationSystem':
                var_name = 'notifier'
            elif component == 'errorHandler':
                var_name = 'errorHandler'
            elif component == 'validationUtils':
                var_name = 'validator'
            elif component == 'fieldUtils':
                var_name = 'fieldUtils'
            elif component == 'apiClient':
                var_name = 'apiClient'

            getters.append(
                f"            const {var_name} = window.ComponentManager.get('{component}');"
            )

        return '\n'.join(getters)

    def _adapt_custom_scripts(self, scripts: str) -> str:
        """适配自定义脚本到新架构"""
        if not scripts:
            return '        // 无自定义脚本'

        # 替换旧的组件初始化模式
        adapted = scripts

        # 替换组件创建为组件获取
        patterns = [
            (
                r'window\.apiClient\s*=\s*new\s+UnifiedAPIClient\([^)]*\)',
                'window.apiClient = window.ComponentManager.get("apiClient")',
            ),
            (
                r'window\.fieldUtils\s*=\s*new\s+FieldUtils\([^)]*\)',
                'window.fieldUtils = window.ComponentManager.get("fieldUtils")',
            ),
            (
                r'window\.validationUtils\s*=\s*new\s+ValidationUtils\([^)]*\)',
                'window.validationUtils = window.ComponentManager.get("validationUtils")',
            ),
            (
                r'new\s+UnifiedAPIClient\([^)]*\)',
                'window.ComponentManager.get("apiClient")',
            ),
            (r'new\s+FieldUtils\([^)]*\)',
     'window.ComponentManager.get("fieldUtils")'),
            (
                r'new\s+ValidationUtils\([^)]*\)',
                'window.ComponentManager.get("validationUtils")',
            ),
        ]

        for old_pattern, new_code in patterns:
            adapted = re.sub(old_pattern, new_code, adapted)

        # 添加缩进
        adapted = '\n'.join(
            [f"        {line}" for line in adapted.split('\n')])

        return adapted

    def migrate_page(self, file_path: Path) -> Dict:
        """执行单个页面迁移"""
        self.logger.info(f"🚀 开始迁移页面: {file_path.name}")

        try:
            # 1. 分析页面
            analysis = self.analyze_page(file_path)

            # 2. 备份原始文件
            backup_path = self.backup_page(file_path)

            # 3. 生成迁移后的代码
            migrated_content = self.generate_migrated_page(analysis)

            # 4. 保存迁移后的文件
            migrated_path = self.migrated_dir / file_path.name
            migrated_path.write_text(migrated_content, encoding='utf-8')

            # 5. 验证迁移结果
            validation = self._validate_migration(migrated_content)

            result = {
                'status': 'success',
                'original_file': str(file_path),
                'backup_file': str(backup_path),
                'migrated_file': str(migrated_path),
                'analysis': analysis,
                'validation': validation,
                'timestamp': datetime.now().isoformat(),
            }

            logger.info(f"✅ 迁移完成: {file_path.name}")
            return result

        except Exception:
            error_result = {
                'status': 'error',
                'original_file': str(file_path),
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
            }
            logger.info(f"❌ 迁移失败: {file_path.name} - {e}")
            return error_result

    def _validate_migration(self, content: str) -> Dict:
        """验证迁移结果"""
        validation = {
            'has_new_architecture': False,
            'has_component_manager': False,
            'has_app_bootstrap': False,
            'has_component_getters': False,
            'syntax_issues': [],
        }

        # 检查新架构标识
        if 'component-manager.js' in content and 'app-bootstrap.js' in content:
            validation['has_new_architecture'] = True

        # 检查组件管理器使用
        if 'ComponentManager.get' in content:
            validation['has_component_manager'] = True

        # 检查应用启动
        if 'window.startApp' in content:
            validation['has_app_bootstrap'] = True

        # 检查组件获取
        if 'ComponentManager.get' in content:
            validation['has_component_getters'] = True

        # 基本语法检查
        if '<html' not in content or '</html>' not in content:
            validation['syntax_issues'].append('HTML结构不完整')

        if '<script' in content and '</script>' not in content:
            validation['syntax_issues'].append('脚本标签不匹配')

        return validation

    def batch_migrate(
    self,
    pages: List[str],
     priority_order: bool = True) -> Dict:
        """批量迁移页面"""
        self.logger.info(f"📦 开始批量迁移 {len(pages)} 个页面")

        results =
            {'total': len(pages),
            'success': 0,
            'failed': 0,
            'details': []
        }

        # 按优先级排序
        if priority_order:
            pages = self._sort_by_priority(pages)

        for page_name in pages:
            page_path = self.frontend_dir / page_name

            if not page_path.exists():
                logger.info(f"⚠️ 页面不存在: {page_name}")
                results['details'].append(
                    {'page': page_name, 'status': 'not_found'})
                continue

            result = self.migrate_page(page_path)
            results['details'].append(result)

            if result['status'] == 'success':
                results['success'] += 1
            else:
                results['failed'] += 1

        self.logger.info(
            f"📊 批量迁移完成: 成功 {results['success']}, 失败 {results['failed']}"
        )
        return results

    def _sort_by_priority(self, pages: List[str]) -> List[str]:
        """按优先级排序页面"""
        priority_map = {
            'field-config.html': 1,
            'database-v2.html': 2,
            'excel-translation.html': 3,
            'field-config-improved.html': 4,
            'unified-field-config.html': 5,
            'sync-test.html': 6,
        }

        def get_priorityy(page):
    """TODO: Add function description."""
            return priority_map.get(page, 999)

        return sorted(pages, key=get_priority)

    def generate_migration_report(self, results: Dict) -> str:
        """生成迁移报告"""
        report = f"""# 页面迁移报告

## 总体概览
- **总页面数**: {results['total']}
- **成功迁移**: {results['success']}
- **迁移失败**: {results['failed']}
- **成功率**: {results['success']/results['total']*100:.1f}%
- **生成时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 详细结果

"""

        for detail in results['details']:
            if detail['status'] == 'success':
                analysis = detail['analysis']
                validation = detail['validation']

                report += f"""### ✅ {Path(detail['original_file']).name}
- **状态**: 迁移成功
- **页面类型**: {analysis['page_type']}
- **复杂度**: {analysis['complexity']}
- **文件大小**: {analysis['size_kb']:.1f} KB
- **备份位置**: {detail['backup_file']}
- **迁移位置**: {detail['migrated_file']}
- **验证结果**: {'通过' if validation.get('has_new_architecture') else '需要检查'}

"""
            else:
                report += f"""### ❌ {detail.get('page', '未知页面')}
- **状态**: 迁移失败
- **错误**: {detail.get('error', '未知错误')}

"""

        return report


def mainn():

    """TODO: Add function description."""
    parser = argparse.ArgumentParser(description='YS-API V3.0 自动化页面迁移工具')
    parser.add_argument('--frontend-dir', default='frontend', help='前端目录路径')
    parser.add_argument('--pages', nargs='+', help='要迁移的页面文件名')
    parser.add_argument(
        '--all-high-priority', action='store_true', help='迁移所有高优先级页面'
    )
    parser.add_argument('--report', action='store_true', help='生成迁移报告')

    args = parser.parse_args()

    # 初始化工具
    tool = AutoMigrationTool(args.frontend_dir)

    # 确定要迁移的页面
    if args.all_high_priority:
        pages = [
            'field-config.html',
            'database-v2.html',
            'excel-translation.html',
            'field-config-improved.html',
            'unified-field-config.html',
        ]
    elif args.pages:
        pages = args.pages
    else:
        self.logger.info("请指定要迁移的页面或使用 --all-high-priority 选项")
        return

    # 执行迁移
    results = tool.batch_migrate(pages)

    # 生成报告
    if args.report:
        report = tool.generate_migration_report(results)
        report_path = Path(args.frontend_dir) / "migration_report.md"
        report_path.write_text(report, encoding='utf-8')
        self.logger.info(f"📄 迁移报告已生成: {report_path}")


if __name__ == '__main__':
    main()
