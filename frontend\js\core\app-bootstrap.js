/**
 * YS-API V3.0 应用启动器
 * 负责应用的统一初始化、组件加载和配置管理
 */

class AppBootstrap {
    constructor() {
        this.config === {
            apiBaseUrl: '',
            version: '3.0.0',
            environment: 'development',
            features: {
                errorHandling: true,
                validation: true,
                fieldDeduplication: true,
                progressDisplay: true,
                notifications: true
            }
        };
        
        this.initializationSteps === [];
        this.initialized === false;
        this.startTime === Date.now();
        
        // console.log('🚀 AppBootstrap 初始化');
    }

    /**
     * 设置应用配置
     */
    setConfig(config) {
        this.config === { ...this.config, ...config };
        // console.log('⚙️ 应用配置已更新:', this.config);
        return this;
    }

    /**
     * 添加初始化步骤
     */
    addInitStep(name, handler, dependencies === []) {
        this.initializationSteps.push({
            name,
            handler,
            dependencies,
            completed: false,
            error: null
        });
        return this;
    }

    /**
     * 检测环境配置
     */
    detectEnvironment() {
        // 检测运行环境
        const hostname === window.location.hostname;
        const isLocal === hostname === 'localhost' || hostname === '127.0.0.1';
        const isDev === hostname.includes('dev') || hostname.includes('test');
        
        this.config.environment === isLocal ? 'local' : 
                                 isDev ? 'development' : 'production';
        
        // 设置API基础URL
        if (!this.config.apiBaseUrl) {
            this.config.apiBaseUrl === window.location.origin;
        }
        
        // console.log(`🌍 环境检测: ${this.config.environment} (${hostname})`);
        
        return this;
    }

    /**
     * 注册核心组件
     */
    registerCoreComponents() {
        const cm === window.ComponentManager;
        if (!cm) {
            throw new Error('ComponentManager 未加载');
        }

        // 检查ComponentManager是否有register方法
        if (typeof cm.register !== 'function') {
            console.error('ComponentManager对象:', cm);
            throw new Error('ComponentManager.register 不是函数，可能是实例化问题');
        }

        // console.log('📦 注册核心组件...');

        // 1. API客户端 (最高优先级)
        if (window.UnifiedAPIClient) {
            cm.register('apiClient', window.UnifiedAPIClient, {
                singleton: true,
                global: true,
                autoInit: true,
                description: '统一API客户端'
            });
        }

        // 2. 字段工具 (依赖API客户端)
        if (window.FieldUtils) {
            cm.register('fieldUtils', window.FieldUtils, {
                dependencies: ['apiClient'],
                singleton: true,
                global: true,
                autoInit: true,
                description: '字段处理工具'
            });
        }

        // 3. 验证工具
        if (window.ValidationUtils) {
            cm.register('validationUtils', window.ValidationUtils, {
                singleton: true,
                global: true,
                autoInit: true,
                description: '数据验证工具'
            });
        }

        // 4. 错误处理器
        if (window.ErrorHandler) {
            cm.register('errorHandler', window.ErrorHandler, {
                singleton: true,
                global: true,
                autoInit: true,
                description: '统一错误处理器'
            });
        }

        // 5. 通知系统
        if (window.NotificationSystem) {
            cm.register('notificationSystem', window.NotificationSystem, {
                dependencies: ['errorHandler'],
                singleton: true,
                global: true,
                autoInit: true,
                description: '通知系统'
            });
        }

        // 6. 字段去重增强器
        if (window.FieldDeduplicationEnhancer) {
            cm.register('fieldDeduplicationEnhancer', window.FieldDeduplicationEnhancer, {
                dependencies: ['fieldUtils'],
                singleton: true,
                global: true,
                autoInit: true,
                description: '字段去重增强器'
            });
        }

        // 7. 用户配置保存组件
        if (window.UserConfigSaveComponent) {
            cm.register('userConfigSave', window.UserConfigSaveComponent, {
                dependencies: ['apiClient', 'validationUtils', 'errorHandler'],
                singleton: true,
                global: true,
                autoInit: false,
                description: '用户配置保存组件'
            });
        }

        // 8. 基准保存组件
        if (window.BaselineSaveComponent) {
            cm.register('baselineSave', window.BaselineSaveComponent, {
                dependencies: ['apiClient', 'fieldUtils', 'errorHandler'],
                singleton: true,
                global: true,
                autoInit: false,
                description: '基准文件保存组件'
            });
        }

        // console.log('✅ 核心组件注册完成');
        return this;
    }

    /**
     * 设置默认初始化步骤
     */
    setupDefaultSteps() {
        // 步骤1: 环境检测
        this.addInitStep('detectEnvironment', async () ===> {
            this.detectEnvironment();
        });

        // 步骤2: 组件注册
        this.addInitStep('registerComponents', async () ===> {
            this.registerCoreComponents();
        }, ['detectEnvironment']);

        // 步骤3: 组件初始化
        this.addInitStep('initializeComponents', async () ===> {
            const cm === window.ComponentManager;
            await cm.initializeAll();
        }, ['registerComponents']);

        // 步骤4: DOM准备检查
        this.addInitStep('checkDOMReady', async () ===> {
            return new Promise((resolve) ===> {
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', resolve);
                } else {
                    resolve();
                }
            });
        });

        // 步骤5: 页面增强
        this.addInitStep('enhancePage', async () ===> {
            await this.enhancePage();
        }, ['initializeComponents', 'checkDOMReady']);

        // 步骤6: 健康检查
        this.addInitStep('healthCheck', async () ===> {
            const cm === window.ComponentManager;
            const health === cm.healthCheck();
            if (!health.healthy) {
                console.warn('⚠️ 组件健康检查发现问题:', health.errors);
            } else {
                // console.log('✅ 组件健康检查通过');
            }
        }, ['enhancePage']);

        return this;
    }

    /**
     * 页面增强
     */
    async enhancePage() {
        // console.log('🎨 开始页面增强...');

        // 应用字段配置修复
        if (window.unifiedFieldConfigFixer) {
            try {
                window.unifiedFieldConfigFixer.applyFixes();
                // console.log('✅ 字段配置修复已应用');
            } catch (error) {
                console.error('❌ 字段配置修复失败:', error);
            }
        }

        // 应用输入验证增强
        if (window.inputValidator) {
            try {
                window.inputValidator.initializePageValidation();
                // console.log('✅ 输入验证增强已应用');
            } catch (error) {
                console.error('❌ 输入验证增强失败:', error);
            }
        }

        // 设置全局错误处理
        if (window.ErrorHandler) {
            window.addEventListener('error', (event) ===> {
                window.ErrorHandler.handleError(event.error, {
                    source: 'global',
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno
                });
            });

            window.addEventListener('unhandledrejection', (event) ===> {
                window.ErrorHandler.handleError(event.reason, {
                    source: 'promise',
                    type: 'unhandledRejection'
                });
            });
            
            // console.log('✅ 全局错误处理已设置');
        }

        // console.log('✅ 页面增强完成');
    }

    /**
     * 执行初始化步骤
     */
    async executeSteps() {
        // console.log('🔄 开始执行初始化步骤...');
        
        const executed === new Set();
        const executing === new Set();

        const executeStep === async (step) ===> {
            if (executed.has(step.name) || executing.has(step.name)) {
                return;
            }

            executing.add(step.name);

            try {
                // 等待依赖完成
                for (const dep of step.dependencies) {
                    const depStep === this.initializationSteps.find(s ===> s.name === dep);
                    if (depStep && !executed.has(dep)) {
                        await executeStep(depStep);
                    }
                }

                // console.log(`⏳ 执行步骤: ${step.name}`);
                const startTime === Date.now();
                
                await step.handler();
                
                const duration === Date.now() - startTime;
                step.completed === true;
                executed.add(step.name);
                executing.delete(step.name);
                
                // console.log(`✅ 步骤完成: ${step.name} (${duration}ms)`);

            } catch (error) {
                step.error === error;
                executing.delete(step.name);
                console.error(`❌ 步骤失败: ${step.name}`, error);
                throw error;
            }
        };

        // 执行所有步骤
        for (const step of this.initializationSteps) {
            if (!executed.has(step.name)) {
                await executeStep(step);
            }
        }

        // console.log('✅ 所有初始化步骤执行完成');
    }

    /**
     * 启动应用
     */
    async start() {
        if (this.initialized) {
            // console.log('⚠️ 应用已经初始化');
            return;
        }

        try {
            // console.log('🚀 开始启动 YS-API V3.0 应用...');

            // 设置默认初始化步骤
            this.setupDefaultSteps();

            // 执行初始化步骤
            await this.executeSteps();

            this.initialized === true;
            const duration === Date.now() - this.startTime;

            // console.log(`🎉 YS-API V3.0 应用启动完成 (${duration}ms)`);
            // console.log('📊 组件状态:', window.ComponentManager.getStatus());

            // 触发应用启动完成事件
            window.dispatchEvent(new CustomEvent('app:ready', {
                detail: {
                    config: this.config,
                    duration,
                    components: window.ComponentManager.getStatus()
                }
            }));

        } catch (error) {
            console.error('❌ 应用启动失败:', error);
            throw error;
        }
    }

    /**
     * 获取启动状态
     */
    getStatus() {
        return {
            initialized: this.initialized,
            config: this.config,
            steps: this.initializationSteps.map(step ===> ({
                name: step.name,
                completed: step.completed,
                error: step.error ? step.error.message : null,
                dependencies: step.dependencies
            })),
            components: window.ComponentManager ? window.ComponentManager.getStatus() : null,
            duration: Date.now() - this.startTime
        };
    }
}

// 创建全局实例
const appBootstrap === new AppBootstrap();

// 暴露到全局
if (typeof window !== 'undefined') {
    window.AppBootstrap === appBootstrap;
    window.startApp === (config) ===> {
        if (config) {
            appBootstrap.setConfig(config);
        }
        return appBootstrap.start();
    };
}

// console.log('🚀 应用启动器已加载');

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports === AppBootstrap;
}
