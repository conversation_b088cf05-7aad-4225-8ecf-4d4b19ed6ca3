import sys
from datetime import datetime
from pathlib import Path

from file_sentinel import FileSentinel

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能AI文件创建包装器
集成所有防重复机制
"""


class SmartFileCreator:
    """智能文件创建器 - AI调用的统一接口"""

    def __init___(self):
    """TODO: Add function description."""
    self.sentinel = FileSentinel()

    # 预定义的文件类型和用途映射
    self.purpose_templates = {
        'health_checker': {
            'description': '健康检查工具',
            'suggested_name': 'health_check.py',
            'type': 'python',
        },
        'fixer': {
            'description': '代码修复工具',
            'suggested_name': 'code_fixer.py',
            'type': 'python',
        },
        'generator': {
            'description': '代码生成工具',
            'suggested_name': 'code_generator.py',
            'type': 'python',
        },
        'test_runner': {
            'description': '测试运行器',
            'suggested_name': 'test_runner.py',
            'type': 'python',
        },
        'config_manager': {
            'description': '配置管理器',
            'suggested_name': 'config_manager.py',
            'type': 'python',
        },
        'api_client': {
            'description': 'API客户端',
            'suggested_name': 'api_client.py',
            'type': 'python',
        },
    }

    def suggest_alternative(self, purpose: str) -> str:
        """建议替代方案"""
        similar_files = self.sentinel.list_similar_files(purpose)

        if similar_files:
            print(f"\n💡 发现相似用途的文件:")
            for i, info in enumerate(similar_files, 1):
                print(f"  {i}. {info['file_path']} - {info['purpose']}")

            print(f"\n建议:")
            print(f"  1. 扩展现有文件功能")
            print(f"  2. 重构合并相似文件")
            print(f"  3. 如果确实需要新文件，请明确差异化用途")

        return similar_files[0]['file_path'] if similar_files else None

    def create_file_smart(
        self,
        file_path: str,
        content: str,
        purpose: str,
        file_type: str = "python",
        ai_context: str = "",
    ) -> bool:
        """智能创建文件"""

        print(f"🤖 AI请求创建文件: {file_path}")
        print(f"📝 用途: {purpose}")
        print(f"🏷️ 类型: {file_type}")

        if ai_context:
            print(f"🧠 AI上下文: {ai_context}")

        # 1. 检查是否有相同用途的文件
        existing = self.sentinel.check_purpose_exists(purpose, file_type)
        if existing:
            print(f"⚠️ 已存在相同用途的文件: {existing}")

            # 建议替代方案
            self.suggest_alternative(purpose)

            choice = input(
                f"\n选择操作:\n"
                f"  1. 扩展现有文件 {existing}\n"
                f"  2. 仍然创建新文件\n"
                f"  3. 取消创建\n"
                f"请选择 (1/2/3): "
            ).strip()

            if choice == '1':
                print(f"✅ 建议扩展现有文件: {existing}")
                return False
            elif choice == '3':
                print("✅ 取消创建")
                return False
            # choice == '2' 继续创建

        # 2. 检查文件名是否有可疑模式
        suspicious_patterns = [
            '_v1',
            '_v2',
            '_copy',
            '_new',
            '_temp',
            '_test',
            '_backup',
        ]
        filename = Path(file_path).name

        if any(pattern in filename for pattern in suspicious_patterns):
            print(f"⚠️ 文件名包含可疑模式: {filename}")
            choice = input("确定要使用这个文件名吗? (y/N): ").lower()
            if choice != 'y':
                print("✅ 取消创建")
                return False

        # 3. 使用哨兵系统创建
        success = self.sentinel.write_if_absent(
            file_path, content, purpose, file_type)

        if success:
            print(f"✅ 成功创建: {file_path}")
            print(f"🛡️ 哨兵保护已启用")

            # 记录AI创建日志
            self._log_ai_creation(file_path, purpose, ai_context)

        return success

    def _log_ai_creation(self, file_path: str, purpose: str, ai_context: str):
        """记录AI创建日志"""
        log_file = Path(".file_sentinels/ai_creation_log.txt")
        with open(log_file, 'a', encoding='utf-8') as f:

            f.write(f"{datetime.now().isoformat()} - AI创建: {file_path}\n")
            f.write(f"  用途: {purpose}\n")
            f.write(f"  上下文: {ai_context}\n\n")

    def check_before_create(
            self,
            purpose: str,
            file_type: str = "python") -> dict:
        """创建前检查 - 供AI调用"""
        result = {
            'can_create': True,
            'existing_files': [],
            'suggestions': [],
            'warnings': [],
        }

        # 检查现有文件
        existing = self.sentinel.check_purpose_exists(purpose, file_type)
        if existing:
            result['can_create'] = False
            result['existing_files'].append(existing)
            result['suggestions'].append(f"考虑扩展现有文件: {existing}")

        # 检查相似文件
        similar = self.sentinel.list_similar_files(purpose)
        result['existing_files'].extend([f['file_path'] for f in similar])

        if similar:
            result['suggestions'].append("考虑合并相似功能的文件")

        return result


def ai_create_file(
    file_path: str,
    content: str,
    purpose: str,
    file_type: str = "python",
    ai_context: str = "",
) -> bool:
    """供AI调用的统一文件创建接口"""
    creator = SmartFileCreator()
    return creator.create_file_smart(
        file_path, content, purpose, file_type, ai_context)


def ai_check_before_create(purpose: str, file_type: str = "python") -> dict:
    """供AI调用的创建前检查接口"""
    creator = SmartFileCreator()
    return creator.check_before_create(purpose, file_type)


def main():
    """测试接口"""
    if len(sys.argv) < 4:
        print("用法: python smart_file_creator.py <file_path> <purpose> <content>")
        return

    file_path = sys.argv[1]
    purpose = sys.argv[2]
    content = sys.argv[3] if len(sys.argv) > 3 else "# AI生成的文件\npass"

    success = ai_create_file(file_path, content, purpose, ai_context="CLI测试")
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
