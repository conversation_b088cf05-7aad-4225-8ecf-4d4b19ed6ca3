# YS-API 项目概览

## 📋 项目简介

YS-API 是一个基于 FastAPI 的企业级数据集成平台，专门用于从用友 ERP 系统获取数据并写入数据库。项目采用现代化的前后端分离架构，提供智能字段映射、自动同步、实时监控等核心功能。

## 🎯 核心功能

### 1. **API 字段获取与配置**
- **智能字段获取**：从用友 API 自动获取所有业务模块的字段信息
- **字段配置中心**：可视化配置字段映射关系，支持批量操作
- **统一字段管理**：集中管理所有模块的字段配置，支持多用户
- **一键获取功能**：支持一键获取所有15个模块的字段配置

### 2. **智能翻译引擎**
- **4层翻译策略**：业务规则 → 增强翻译 → 智能分词 → 基础翻译
- **JSON 同步机制**：支持翻译结果的 JSON 格式同步
- **置信度评估**：自动评估翻译质量，提供置信度评分
- **Excel 智能匹配**：支持 Excel 文件上传，智能匹配字段翻译

### 3. **数据同步与处理**
- **自动同步调度**：支持定时自动同步数据
- **实时数据处理**：高效的数据写入和管理
- **数据库表管理**：自动创建和管理数据库表结构
- **批量操作支持**：支持大批量数据的处理

### 4. **监控与日志**
- **实时日志系统**：提供实时的操作日志和错误监控
- **性能监控**：监控系统性能和数据处理效率
- **状态检查**：自动同步状态检查和报告
- **错误处理**：完善的错误处理和恢复机制

### 5. **前端界面**
- **16:9 自适应布局**：现代化的响应式界面设计
- **统一字段配置**：直观的字段配置和管理界面
- **实时数据展示**：实时显示数据状态和处理进度
- **用户友好操作**：简洁直观的操作界面

## 🏗️ 项目结构

```
YS-API程序/
├── v3/                          # 主项目目录
│   ├── backend/                 # 后端服务
│   │   ├── app/                # 应用核心
│   │   │   ├── api/v1/         # API 路由
│   │   │   ├── core/           # 核心配置
│   │   │   ├── middleware/     # 中间件
│   │   │   ├── schemas/        # 数据模型
│   │   │   └── services/       # 业务服务
│   │   ├── config.ini          # 配置文件
│   │   └── requirements.txt    # 依赖包
│   ├── frontend/               # 前端界面
│   │   ├── css/               # 样式文件
│   │   ├── js/                # JavaScript 文件
│   │   └── *.html             # 页面文件
│   ├── config/                # 配置文件
│   │   ├── field_configs/     # 字段配置
│   │   ├── user_configs/      # 用户配置
│   │   └── auto_sync_config.json
│   ├── docs/                  # 项目文档
│   ├── excel/                 # Excel 模板文件
│   ├── logs/                  # 日志文件
│   └── scripts/               # 脚本文件
```

## 🔧 技术栈

### 后端技术
- **FastAPI**：现代化的 Python Web 框架
- **SQLAlchemy**：ORM 数据库操作
- **Pydantic**：数据验证和序列化
- **Uvicorn**：ASGI 服务器
- **Celery**：异步任务处理

### 前端技术
- **Vue.js 3**：渐进式 JavaScript 框架
- **Element Plus**：Vue 3 组件库
- **Axios**：HTTP 客户端
- **CSS Grid/Flexbox**：现代布局技术

### 数据库
- **SQLite**：开发环境数据库
- **MySQL/PostgreSQL**：生产环境数据库

## 📊 业务模块

### 核心业务模块（15个）
1. **销售订单** (sales_order)
2. **采购订单** (purchase_order)
3. **生产订单** (production_order)
4. **委外订单** (subcontract_order)
5. **请购单** (applyorder)
6. **委外请购** (subcontract_requisition)
7. **产品入库** (product_receipt)
8. **采购入库** (purchase_receipt)
9. **委外入库** (subcontract_receipt)
10. **材料出库** (materialout)
11. **销售出库** (sales_out)
12. **现存量** (inventory)
13. **现存量报表** (inventory_report)
14. **需求计划** (requirements_planning)
15. **物料档案** (material_master)

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Node.js 14+
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装步骤
1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd YS-API程序/v3
   ```

2. **安装后端依赖**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

3. **启动后端服务**
   ```bash
   python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
   ```

4. **访问前端界面**
   - 打开浏览器访问 `http://localhost:8000`
   - 或直接打开 `frontend/` 目录下的 HTML 文件

## 📈 项目特色

### 1. **智能化程度高**
- 自动字段识别和映射
- 智能翻译引擎
- 自动数据同步

### 2. **用户体验优秀**
- 直观的可视化界面
- 实时反馈和进度显示
- 响应式设计适配各种设备

### 3. **扩展性强**
- 模块化架构设计
- 支持新业务模块快速接入
- 灵活的配置管理

### 4. **稳定性好**
- 完善的错误处理机制
- 实时监控和日志记录
- 自动恢复和重试机制

## 📞 技术支持

- **项目文档**：详细的使用说明和技术文档
- **问题记录**：常见问题和解决方案
- **性能优化**：系统性能优化指南
- **部署指南**：生产环境部署说明

---

*最后更新时间：2024年12月*
*版本：V3.2* 