<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>database-v2.html - 已迁移到新架构</title>
    
    <!-- 新架构核心文件 -->
    <script src="../js/core/component-manager.js"></script>
    <script src="../js/core/app-bootstrap.js"></script>
    <script src="../js/api-config-fix.js"></script>
    
    <!-- 所需组件 -->
    <script src="../js/common/api-client.js"></script>
    <script src="../js/common/validation-utils.js"></script>
    <script src="../js/common/error-handler.js"></script>
    <script src="../js/notification-system.js"></script>
    
    <!-- 自定义样式 -->
    <style>

        :root {
            --primary-color: #60a5fa;
            --primary-hover: #3b82f6;
            --success-color: #4ade80;
            --success-hover: #22c55e;
            --warning-color: #fbbf24;
            --warning-hover: #f59e0b;
            --danger-color: #f87171;
            --danger-hover: #ef4444;
            --secondary-color: #6b7280;
            --secondary-hover: #4b5563;
            --background-dark: #1a1a1a;
            --card-background: #2d2d2d;
            --text-primary: #e0e0e0;
            --text-secondary: #9ca3af;
            --border-color: #444;
            --border-radius: 8px;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--background-dark);
            color: var(--text-primary);
            line-height: 1.6;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px 20px;
            background: var(--card-background);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: var(--primary-color);
            font-size: 32px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 16px;
            max-width: 600px;
            margin: 0 auto;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }

        .status-badge.online {
            background: var(--success-color);
            color: white;
        }

        .status-badge.offline {
            background: var(--danger-color);
            color: white;
        }

        .card {
            background: var(--card-background);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-title::before {
            content: "";
            width: 4px;
            height: 20px;
            background: var(--primary-color);
            border-radius: 2px;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 25px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .status-item {
            background: #333;
            padding: 20px;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .status-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .status-item.success {
            border-left-color: var(--success-color);
        }

        .status-item.error {
            border-left-color: var(--danger-color);
        }

        .status-item.warning {
            border-left-color: var(--warning-color);
        }

        .status-label {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
            font-size: 14px;
        }

        .status-value {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 16px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: var(--border-radius);
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 500;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transition: width 0.3s, height 0.3s;
            transform: translate(-50%, -50%);
        }

        .btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .btn:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(96, 165, 250, 0.4);
        }

        .btn.success {
            background: var(--success-color);
        }

        .btn.success:hover {
            background: var(--success-hover);
            box-shadow: 0 4px 12px rgba(74, 222, 128, 0.4);
        }

        .btn.warning {
            background: var(--warning-color);
        }

        .btn.warning:hover {
            background: var(--warning-hover);
            box-shadow: 0 4px 12px rgba(251, 191, 36, 0.4);
        }

        .btn.danger {
            background: var(--danger-color);
        }

        .btn.danger:hover {
            background: var(--danger-hover);
            box-shadow: 0 4px 12px rgba(248, 113, 113, 0.4);
        }

        .btn.secondary {
            background: var(--secondary-color);
        }

        .btn.secondary:hover {
            background: var(--secondary-hover);
            box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
        }

        .btn:disabled,
        .btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .btn:disabled:hover,
        .btn.disabled:hover {
            background: var(--primary-color);
            transform: none;
            box-shadow: none;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-group select,
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            background: #333;
            color: var(--text-primary);
            font-size: 14px;
            transition: var(--transition);
        }

        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
        }

        .log-container {
            background: #1a1a1a;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            height: 400px;
            overflow-y: auto;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.4;
            scroll-behavior: smooth;
        }

        .log-container::-webkit-scrollbar {
            width: 8px;
        }

        .log-container::-webkit-scrollbar-track {
            background: #333;
        }

        .log-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .log-entry {
            margin: 4px 0;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background 0.2s;
            position: relative;
        }

        .log-entry:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .log-entry.error {
            color: var(--danger-color);
            background: rgba(248, 113, 113, 0.1);
        }

        .log-entry.success {
            color: var(--success-color);
            background: rgba(74, 222, 128, 0.1);
        }

        .log-entry.warning {
            color: var(--warning-color);
            background: rgba(251, 191, 36, 0.1);
        }

        .log-entry.info {
            color: var(--primary-color);
            background: rgba(96, 165, 250, 0.1);
        }

        .log-entry::before {
            content: attr(data-timestamp);
            font-size: 11px;
            color: var(--text-secondary);
            margin-right: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 24px;
            background: #333;
            border-radius: 12px;
            overflow: hidden;
            margin: 15px 0;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }

            100% {
                transform: translateX(100%);
            }
        }

        .progress-text {
            text-align: center;
            margin-top: 8px;
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .spinner {
            border: 3px solid #333;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
            vertical-align: middle;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .hidden {
            display: none !important;
        }

        .log-tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 2px solid var(--border-color);
            background: #333;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 15px 25px;
            color: var(--text-secondary);
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: var(--transition);
            font-weight: 500;
            position: relative;
        }

        .tab-btn.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background: rgba(96, 165, 250, 0.1);
        }

        .tab-btn:hover {
            color: var(--primary-color);
            background: rgba(96, 165, 250, 0.05);
        }

        .connection-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            z-index: 1000;
            transition: var(--transition);
        }

        .connection-indicator.connected {
            background: var(--success-color);
            color: white;
        }

        .connection-indicator.disconnected {
            background: var(--danger-color);
            color: white;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: var(--card-background);
            margin: 10% auto;
            padding: 30px;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 600px;
            border: 1px solid var(--border-color);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .close {
            color: var(--text-secondary);
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: var(--transition);
        }

        .close:hover {
            color: var(--danger-color);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: var(--border-radius);
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.error {
            background: var(--danger-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }

        .notification.info {
            background: var(--primary-color);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .grid {
                grid-template-columns: 1fr;
            }

            .status-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 24px;
            }

            .card {
                padding: 15px;
            }

            .btn {
                width: 100%;
                margin-right: 0;
                margin-bottom: 10px;
            }
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: var(--card-background);
            padding: 2rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            min-width: 400px;
            max-width: 90vw;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        .modal-content h3 {
            margin-top: 0;
            margin-bottom: 1.5rem;
            color: var(--text-primary);
            font-size: 1.25rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--background-dark);
            color: var(--text-primary);
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
        }
    
    </style>
    <link rel="stylesheet" href="../css/realtime-log.css">
    <link rel="stylesheet" href="../css/smart-logger.css">
</head>
<body>
    <!-- 迁移标识 -->
    <div style="position: fixed; top: 10px; right: 10px; background: #4CAF50; color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px; z-index: 9999;">
        ✅ 已迁移到新架构
    </div>
    
    <!-- 原始页面内容 -->
<!-- 连接状态指示器 -->
    <div class="connection-indicator" id="connection-indicator">
        <span class="spinner"></span> 连接中...
    </div>

    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>YS-API V3.0 数据库管理</h1>
            <p>统一的API管理平台，确保前后端调用的一致性和稳定性</p>
            <div id="server-info">
                <span class="status-badge" id="server-status">检测中...</span>
            </div>
        </div>

        <!-- 系统状态概览 -->
        <div class="card">
            <div class="card-title">🔍 系统状态概览</div>
            <div class="status-grid" id="system-status-grid">
                <div class="status-item">
                    <div class="status-label">系统状态</div>
                    <div class="status-value" id="system-status">检查中...</div>
                </div>
                <div class="status-item">
                    <div class="status-label">后端连接</div>
                    <div class="status-value" id="backend-status">连接中...</div>
                </div>
                <div class="status-item">
                    <div class="status-label">数据库连接</div>
                    <div class="status-value" id="db-status">检查中...</div>
                </div>
                <div class="status-item">
                    <div class="status-label">最后更新</div>
                    <div class="status-value" id="last-update">-</div>
                </div>
            </div>
        </div>

        <div class="grid">
            <!-- 自动同步管理 -->
            <div class="card">
                <div class="card-title">🔄 自动同步管理</div>
                <div class="status-grid">
                    <div class="status-item" id="auto-sync-status">
                        <div class="status-label">调度器状态</div>
                        <div class="status-value">检查中...</div>
                    </div>
                    <div class="status-item" id="auto-sync-next">
                        <div class="status-label">下次运行</div>
                        <div class="status-value" id="auto-sync-next-value">-</div>
                        <div class="status-subtitle" id="auto-sync-countdown"
                            style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">-</div>
                    </div>

                </div>
                <div style="margin-top: 20px;">
                    <button class="btn" onclick="startAutoSync()">
                        <span id="auto-sync-start-spinner" class="spinner hidden"></span>
                        启动调度器
                    </button>
                    <button class="btn warning" onclick="stopAutoSync()">
                        <span id="auto-sync-stop-spinner" class="spinner hidden"></span>
                        停止调度器
                    </button>
                    <button class="btn success" onclick="triggerAutoSync()">
                        <span id="auto-sync-trigger-spinner" class="spinner hidden"></span>
                        立即同步
                    </button>
                    <button class="btn secondary" onclick="showAutoSyncConfig()">配置</button>
                </div>
            </div>

            <!-- 物料档案管理 -->
            <div class="card">
                <div class="card-title">📦 物料档案管理</div>
                <div class="status-grid">
                    <div class="status-item" id="material-master-status">
                        <div class="status-label">调度器状态</div>
                        <div class="status-value">检查中...</div>
                    </div>
                    <div class="status-item" id="material-master-next">
                        <div class="status-label">下次运行</div>
                        <div class="status-value" id="material-master-next-value">-</div>
                        <div class="status-subtitle" id="material-master-countdown"
                            style="font-size: 12px; color: var(--text-secondary); margin-top: 4px;">-</div>
                    </div>

                </div>
                <div style="margin-top: 20px;">
                    <button class="btn" onclick="startMaterialMaster()">
                        <span id="material-start-spinner" class="spinner hidden"></span>
                        启动调度器
                    </button>
                    <button class="btn warning" onclick="stopMaterialMaster()">
                        <span id="material-stop-spinner" class="spinner hidden"></span>
                        停止调度器
                    </button>
                    <button class="btn success" onclick="triggerMaterialMaster()">
                        <span id="material-trigger-spinner" class="spinner hidden"></span>
                        立即同步
                    </button>
                    <button class="btn secondary" onclick="showMaterialMasterHistory()">历史记录</button>
                </div>
            </div>
        </div>

        <!-- 数据同步操作 -->
        <div class="card">
            <div class="card-title">📊 数据同步操作</div>
            <div class="grid">
                <div>
                    <div class="form-group">
                        <label for="sync-module">选择模块:</label>
                        <select id="sync-module">
                            <option value="">-- 请选择模块 --</option>
                            <option value="purchase_order">采购订单</option>
                            <option value="purchase_receipt">采购入库</option>
                            <option value="sales_order">销售订单</option>
                            <option value="sales_out">销售出库</option>
                            <option value="production_order">生产订单</option>
                            <option value="product_receipt">产品入库</option>
                            <option value="subcontract_order">委外订单</option>
                            <option value="subcontract_receipt">委外入库</option>
                            <option value="subcontract_requisition">委外申请</option>
                            <option value="materialout">材料出库</option>
                            <option value="material_master">物料档案</option>
                            <option value="inventory_report">现存量报表</option>
                            <option value="requirements_planning">需求计划</option>
                            <option value="applyorder">请购单</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>同步模式:</label>
                        <div
                            style="color: var(--success-color); font-weight: 500; padding: 8px 12px; background: rgba(74, 222, 128, 0.1); border-radius: 4px;">
                            ✅ 全量同步模式 - 每页500条，获取全部数据
                        </div>
                    </div>
                    <div style="margin-top: 20px;">
                        <button class="btn" onclick="syncSingleModule()" title="同步单个模块：选择特定模块进行数据同步，增量更新现有数据">
                            <span id="sync-single-spinner" class="spinner hidden"></span>
                            同步单模块
                        </button>
                        <button class="btn success" onclick="syncAllModules()"
                            title="同步所有15个模块：并行处理多个模块，从YS-API获取数据并写入数据库">
                            <span id="sync-all-spinner" class="spinner hidden"></span>
                            同步所有模块
                        </button>
                        <button class="btn info" onclick="compareData()" title="一键对比API与数据库的数据差异">
                            <span id="compare-spinner" class="spinner hidden"></span>
                            🔍 一键对比差异
                        </button>
                        <button class="btn secondary" onclick="checkSyncStatus()"
                            title="检查当前同步任务的状态：是否正在运行、处理了多少条记录、当前处理的模块等">检查状态</button>
                        <button class="btn warning" onclick="syncSingleModuleWithRealtime()"
                            title="实时同步单个模块：清空现有数据后全量同步，并显示详细的实时日志">
                            <span id="sync-realtime-spinner" class="spinner hidden"></span>
                            实时同步
                        </button>
                    </div>
                </div>
                <div>
                    <div class="progress-bar hidden" id="sync-progress-bar">
                        <div class="progress-fill" id="sync-progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text hidden" id="sync-progress-text">同步进度: 0%</div>
                    <div class="status-grid" id="sync-status-grid">
                        <div class="status-item">
                            <div class="status-label">当前状态</div>
                            <div class="status-value" id="sync-current-status">就绪</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">处理记录</div>
                            <div class="status-value" id="sync-records">0</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">当前模块</div>
                            <div class="status-value" id="sync-current-module">-</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">耗时</div>
                            <div class="status-value" id="sync-duration">-</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据库管理 -->
        <div class="card">
            <div class="card-title">💾 数据库管理</div>
            <div class="grid">
                <div>
                    <p style="margin-bottom: 20px; color: var(--text-secondary);">
                        数据库操作需要谨慎执行，请确保已备份重要数据。
                    </p>
                    <button class="btn danger" onclick="resetDatabase()">
                        <span id="reset-db-spinner" class="spinner hidden"></span>
                        重置数据库
                    </button>
                    <button class="btn success" onclick="createDatabase()">
                        <span id="create-db-spinner" class="spinner hidden"></span>
                        创建数据库
                    </button>
                    <button class="btn primary" onclick="createAllTables()">
                        <span id="create-all-tables-spinner" class="spinner hidden"></span>
                        创建所有表
                    </button>
                    <button class="btn secondary" onclick="showDatabaseTables()">
                        <span id="show-tables-spinner" class="spinner" style="display: none;"></span>
                        查看表结构
                    </button>
                    <button class="btn secondary" onclick="checkDatabaseStatus()">检查数据库状态</button>
                    <button class="btn secondary" onclick="optimizeDatabase()">
                        <span id="optimize-db-spinner" class="spinner" style="display: none;"></span>
                        优化数据库
                    </button>
                    <button class="btn secondary" onclick="showCreateSingleTableModal()">创建单个表</button>
                </div>
                <div>
                    <div class="status-grid" id="database-status-grid">
                        <div class="status-item">
                            <div class="status-label">数据库状态</div>
                            <div class="status-value" id="db-status">检查中...</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">表总数</div>
                            <div class="status-value" id="db-table-count">-</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">数据库大小</div>
                            <div class="status-value" id="db-size">-</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">连接数</div>
                            <div class="status-value" id="db-connections">-</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志和监控 -->
        <div class="card">
            <div class="card-title">📋 系统日志</div>
            <div style="margin-bottom: 20px;">
                <button class="btn secondary" onclick="clearLogs()">清空日志</button>
                <button class="btn secondary" onclick="exportLogs()">导出日志</button>
                <button class="btn" onclick="toggleRealtimeLog()" id="realtime-toggle">
                    <span id="realtime-spinner" class="spinner hidden"></span>
                    启用实时日志
                </button>
                <label style="margin-left: 20px; color: var(--text-secondary);">
                    <input type="checkbox" id="auto-scroll" checked onchange="toggleAutoScroll()"> 自动滚动
                </label>
            </div>

            <!-- 日志选项卡 -->
            <div class="log-tabs">
                <button class="tab-btn active" onclick="switchLogTab('basic')">基础日志</button>
                <button class="tab-btn" onclick="switchLogTab('realtime')">实时日志</button>
                <button class="tab-btn" onclick="switchLogTab('error')">错误日志</button>
            </div>

            <!-- 基础日志容器 -->
            <div class="log-container" id="log-container">
                <!-- 基础日志将在这里显示 -->
            </div>

            <!-- 实时日志容器 -->
            <div class="log-container hidden" id="realtime-log-container">
                <!-- 实时日志将在这里显示 -->
            </div>

            <!-- 错误日志容器 -->
            <div class="log-container hidden" id="error-log-container">
                <!-- 错误日志将在这里显示 -->
            </div>
        </div>
    </div>

    <!-- 配置对话框 -->
    <div id="config-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 style="color: var(--primary-color); margin-bottom: 20px;">配置设置</h2>
            <div id="config-content">
                <!-- 配置内容将在这里动态加载 -->
            </div>
        </div>
    </div>

    <!-- 修复工具代码 -->
    

    

    <!-- 引入实时日志组件 -->

    <!-- 新架构初始化脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 初始化新架构页面: database-v2.html');
            
            // 启动应用
            await window.startApp({
                environment: 'production',
                features: {
                "validation": true,
                "errorHandling": true,
                "notifications": true
}
            });
            
            // 获取所需组件
            const apiClient = window.ComponentManager.get('apiClient');
            const validator = window.ComponentManager.get('validationUtils');
            const errorHandler = window.ComponentManager.get('errorHandler');
            const notifier = window.ComponentManager.get('notificationSystem');
            
            // 调用原始初始化逻辑
            if (typeof initializePage === 'function') {
                initializePage();
            }
            
            console.log('✅ 页面初始化完成: database-v2.html');
        });
        
        // 原始自定义脚本（已适配新架构）
        // 统一鉴权 - 从 localStorage 获取 token
                const TOKEN = localStorage.getItem('ys_api_token') || 'dev_token';
        
                // 清除可能的缓存问题
                if (window.performance && window.performance.navigation.type === window.performance.navigation.TYPE_BACK_FORWARD) {
                    window.location.reload();
                }
                
                // ==================== 中文映射表 ====================
                window.MODULE_NAME_CN = {
                    purchase_order: '采购订单',
                    sales_order: '销售订单',
                    production_order: '生产订单',
                    inventory: '现存量',
                    material_master: '物料档案',
                    requirements_planning: '需求计划',
                    applyorder: '请购单',
                    materialout: '材料出库单',
                    product_receipt: '产品入库单',
                    purchase_receipt: '采购入库单',
                    sales_out: '销售出库单',
                    subcontract_order: '委外订单',
                    subcontract_receipt: '委外入库单',
                    subcontract_requisition: '委外请购单',
                    inventory_report: '现存量报表'
                };
                
                // 统一替换函数
                function mapModuleName(key) {
                    return MODULE_NAME_CN[key] || key;
                }
                
                // 断点续传缓存
                function getResumeModules(allModules) {
                    const lastSuccess = localStorage.getItem('last_success_module');
                    if (lastSuccess && allModules.includes(lastSuccess)) {
                        const startIdx = allModules.indexOf(lastSuccess) + 1;
                        return allModules.slice(startIdx);
                    }
                    return allModules;
                }
                
                // 保存成功的模块
                function saveSuccessModule(moduleName) {
                    localStorage.setItem('last_success_module', moduleName);
                }
        
        // 调试API URL构造
                console.log('API_BASE_URL:', API_BASE_URL);
                console.log('window.location:', window.location);
                
                // 重写fetch函数以添加调试信息
                const originalFetch = window.fetch;
                window.fetch = function(url, options) {
                    console.log('Fetch URL:', url);
                    if (typeof url === 'string' && url.includes('database-v2.html')) {
                        console.error('错误的URL构造:', url);
                        // 修复URL
                        url = url.replace(/.*database-v2\.html/, '');
                        console.log('修复后的URL:', url);
                    }
                    return originalFetch.call(this, url, options);
                };
        
        /**
                 * YS-API V3.0 Database-v2.html 修复工具
                 * 实施高优先级的安全和功能修复
                 */
        
                // ==================== 1. Token 安全管理 ====================
                class TokenManager {
                    static tokenRefreshInterval = null;
                    static tokenExpiryTime = null;
                    static refreshThreshold = 5 * 60 * 1000; // 5分钟前开始刷新
        
                    static getToken() {
                        const token = localStorage.getItem('ys_api_token');
                        if (!token || token === 'dev_token') {
                            // 在生产环境中，无效token会导致连接问题
                            console.warn('Token无效或已过期，正在自动重新登录...');
                            this.handleTokenExpired();
                            return null;
                        }
        
                        // 检查Token是否需要提前刷新
                        this.checkTokenRefresh();
        
                        return token;
                    }
        
                    static checkTokenRefresh() {
                        const expiryTime = localStorage.getItem('token_expiry_time');
        
                        // 如果没有过期时间，尝试重新验证Token
                        if (!expiryTime) {
                            const token = localStorage.getItem('ys_api_token');
                            if (token) {
                                console.log('检测到Token但无过期时间，重新验证...');
                                this.validateAndUpdateTokenExpiry(token);
                            }
                            return;
                        }
        
                        const now = Date.now();
                        const expiry = parseInt(expiryTime);
                        const timeUntilExpiry = expiry - now;
        
                        // 如果Token即将过期（5分钟内），开始刷新
                        if (timeUntilExpiry <= this.refreshThreshold && timeUntilExpiry > 0) {
                            console.log(`Token将在${Math.round(timeUntilExpiry / 1000)}秒后过期，开始提前刷新...`);
                            this.startTokenRefresh();
                        }
                        // 如果Token已过期，立即处理
                        else if (timeUntilExpiry <= 0) {
                            console.warn('Token已过期，立即重新登录...');
                            this.handleTokenExpired();
                        }
                    }
        
                    static startTokenRefresh() {
                        // 避免重复刷新
                        if (this.tokenRefreshInterval) {
                            clearInterval(this.tokenRefreshInterval);
                        }
        
                        console.log('开始Token提前刷新...');
                        if (window.NotificationSystem) {
                            window.NotificationSystem.show('正在刷新Token...', 'info');
                        }
        
                        // 立即尝试刷新一次
                        this.refreshToken();
        
                        // 设置定时刷新（每30秒检查一次）
                        this.tokenRefreshInterval = setInterval(() => {
                            this.checkTokenRefresh();
                        }, 30000);
                    }
        
                    static async refreshToken() {
                        try {
                            console.log('正在刷新Token...');
        
                            // 尝试从配置获取刷新Token
                            const refreshToken = this.getRefreshToken();
                            if (refreshToken) {
                                const newToken = await this.refreshWithToken(refreshToken);
                                if (newToken) {
                                    this.updateToken(newToken);
                                    return;
                                }
                            }
        
                            // 如果刷新Token不可用，尝试自动重新登录
                            await this.autoRelogin();
        
                        } catch (error) {
                            console.error('Token刷新失败:', error);
                            // 刷新失败时，如果Token还未过期，继续使用
                            const expiryTime = localStorage.getItem('token_expiry_time');
                            if (!expiryTime || (expiryTime && parseInt(expiryTime) > Date.now())) {
                                console.log('Token刷新失败，但Token仍有效，继续使用');
                                return;
                            }
                            // 如果Token已过期，强制重新登录
                            this.handleTokenExpired();
                        }
                    }
        
                    static getRefreshToken() {
                        // 从localStorage获取刷新Token
                        return localStorage.getItem('refresh_token');
                    }
        
                    static async refreshWithToken(refreshToken) {
                        try {
                            const response = await fetch(`${ConfigManager.getApiBaseUrl()}/api/v1/auth/refresh`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    refresh_token: refreshToken
                                })
                            });
        
                            if (response.ok) {
                                const data = await response.json();
                                if (data.success && data.token) {
                                    return data.token;
                                }
                            }
        
                            return null;
                        } catch (error) {
                            console.error('使用刷新Token失败:', error);
                            return null;
                        }
                    }
        
                    static updateToken(newToken, expiryTime = null) {
                        localStorage.setItem('ys_api_token', newToken);
        
                        // 如果没有提供过期时间，使用默认的2小时有效期
                        if (expiryTime) {
                            localStorage.setItem('token_expiry_time', expiryTime.toString());
                            console.log('Token已更新，过期时间:', new Date(expiryTime).toLocaleString());
                        } else {
                            // 设置默认2小时过期时间
                            const defaultExpiry = Date.now() + (2 * 60 * 60 * 1000);
                            localStorage.setItem('token_expiry_time', defaultExpiry.toString());
                            console.log('Token已更新，默认过期时间:', new Date(defaultExpiry).toLocaleString());
                        }
        
                        if (window.NotificationSystem) {
                            window.NotificationSystem.show('Token已更新', 'success');
                        }
        
                        // 停止刷新定时器
                        this.stopTokenRefresh();
                    }
        
                    static stopTokenRefresh() {
                        if (this.tokenRefreshInterval) {
                            clearInterval(this.tokenRefreshInterval);
                            this.tokenRefreshInterval = null;
                        }
                    }
        
                    // 清理所有资源
                    static cleanup() {
                        this.stopTokenRefresh();
                        console.log('TokenManager资源已清理');
                    }
        
                    static redirectToLogin() {
                        // 自动重新登录
                        console.log('正在自动重新登录...');
                        if (window.NotificationSystem) {
                            window.NotificationSystem.show('正在自动重新登录...', 'info');
                        }
                        // 先尝试自动登录，失败则显示对话框
                        this.autoRelogin().catch(() => {
                            this.showLoginPrompt();
                        });
                    }
        
                    static handleTokenExpired() {
                        const eventId = `expiry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
                        console.log(`⏰ [${eventId}] Token过期处理开始`);
        
                        // 记录过期前的Token信息
                        const oldToken = localStorage.getItem('ys_api_token');
                        const expiryTime = localStorage.getItem('token_expiry_time');
        
                        console.log(`📊 [${eventId}] Token过期信息:`, {
                            hasToken: !!oldToken,
                            tokenLength: oldToken ? oldToken.length : 0,
                            expiryTime: expiryTime,
                            currentTime: new Date().toISOString()
                        });
        
                        // 清除Token相关数据
                        localStorage.removeItem('ys_api_token');
                        localStorage.removeItem('token_expiry_time');
                        localStorage.removeItem('refresh_token');
        
                        console.log(`🗑️ [${eventId}] Token数据已清除`);
        
                        // 停止Token刷新
                        this.stopTokenRefresh();
                        console.log(`⏹️ [${eventId}] Token刷新已停止`);
        
                        // 显示用户友好的提示
                        if (window.NotificationSystem) {
                            window.NotificationSystem.show('登录已过期，正在重新获取...', 'warning');
                        }
        
                        // 记录到日志系统
                        if (window.Logger) {
                            window.Logger.addLog(`Token过期 [${eventId}]: 开始重新获取`, 'warning');
                        }
        
                        // 延迟重试，避免立即重试
                        setTimeout(() => {
                            console.log(`🔄 [${eventId}] 开始重新获取Token...`);
                            this.autoRelogin();
                        }, 1000);
                    }
        
                    static validateToken(token) {
                        // 生产环境严格验证token
                        return token && token.length > 10 && token !== 'dev_token';
                    }
        
                    static async autoRelogin() {
                        const startTime = Date.now();
                        const requestId = `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
                        console.log(`🔄 [${requestId}] 开始自动获取Token...`);
        
                        try {
                            // 优先请求后端自动获取Token接口
                            const resp = await fetch(`${API_BASE_URL}${APP_CONFIG.API_ENDPOINTS.TOKEN}`);
                            const responseTime = Date.now() - startTime;
        
                            console.log(`📡 [${requestId}] Token请求响应:`, {
                                status: resp.status,
                                statusText: resp.statusText,
                                responseTime: `${responseTime}ms`,
                                url: `${API_BASE_URL}${APP_CONFIG.API_ENDPOINTS.TOKEN}`
                            });
        
                            if (resp.ok) {
                                const data = await resp.json();
                                console.log(`📋 [${requestId}] Token响应数据:`, {
                                    success: data.success,
                                    hasToken: !!data.token,
                                    tokenLength: data.token ? data.token.length : 0,
                                    message: data.message || '无消息'
                                });
        
                                if (data.success && data.token) {
                                    console.log(`✅ [${requestId}] Token获取成功，长度: ${data.token.length}`);
                                    this.updateToken(data.token);
                                    return;
                                } else {
                                    console.error(`❌ [${requestId}] Token获取失败:`, {
                                        success: data.success,
                                        message: data.message,
                                        error: data.error
                                    });
                                }
                            } else {
                                console.error(`❌ [${requestId}] HTTP错误:`, {
                                    status: resp.status,
                                    statusText: resp.statusText,
                                    url: `${API_BASE_URL}${APP_CONFIG.API_ENDPOINTS.TOKEN}`
                                });
                            }
        
                            // 降级方案：显示友好提示
                            console.warn(`⚠️ [${requestId}] 使用降级方案`);
                            if (window.NotificationSystem) {
                                window.NotificationSystem.show('认证失败，请刷新页面重试', 'error');
                            }
        
                            // 记录到日志系统
                            if (window.Logger) {
                                window.Logger.addLog(`Token获取失败 [${requestId}]: HTTP ${resp.status}`, 'error');
                            }
        
                        } catch (e) {
                            const totalTime = Date.now() - startTime;
                            console.error(`💥 [${requestId}] 自动登录异常:`, {
                                error: e.message,
                                stack: e.stack,
                                totalTime: `${totalTime}ms`,
                                url: APP_CONFIG.API_ENDPOINTS.TOKEN
                            });
        
                            // 显示用户友好的错误提示
                            if (window.NotificationSystem) {
                                window.NotificationSystem.show('网络连接失败，请检查服务器', 'error');
                            }
        
                            // 记录到日志系统
                            if (window.Logger) {
                                window.Logger.addLog(`Token获取异常 [${requestId}]: ${e.message}`, 'error');
                            }
                        }
                    }
        
                    static getDefaultToken() {
                        // 尝试从多个来源获取默认token
                        const sources = [
                            localStorage.getItem('default_token'),
                            localStorage.getItem('backup_token'),
                            'your_default_token_here', // 可以在这里设置默认token
                            null
                        ];
        
                        for (const token of sources) {
                            if (token && this.validateToken(token)) {
                                return token;
                            }
                        }
        
                        return null;
                    }
        
                    static fallbackLogin() {
                        // 备用登录方法：直接显示手动输入对话框
                        console.log('使用备用登录方法...');
                        this.showLoginPrompt();
                    }
        
                    static showLoginPrompt() {
                        // 彻底删除手动输入Token窗口，改为自动重试
                        console.log('自动获取Token失败，尝试重新获取...');
                        if (window.NotificationSystem) {
                            window.NotificationSystem.show('Token获取失败，正在重试...', 'warning');
                        }
        
                        // 延迟3秒后重试
                        setTimeout(() => {
                            this.autoRelogin();
                        }, 3000);
                    }
        
                    static saveToken() {
                        // 已删除手动输入Token功能，改为自动重试
                        console.log('手动输入Token功能已禁用，自动重试中...');
                        this.autoRelogin();
                    }
        
                    static closeLoginPrompt() {
                        // 已删除手动输入Token功能，无需关闭弹窗
                        console.log('手动输入Token功能已禁用');
                    }
        
                    static initTokenMonitoring() {
                        const initId = `init_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
                        console.log(`🚀 [${initId}] 初始化Token监控...`);
        
                        // 检查现有Token的过期时间
                        const token = localStorage.getItem('ys_api_token');
                        const expiryTime = localStorage.getItem('token_expiry_time');
        
                        console.log(`📋 [${initId}] 当前Token状态:`, {
                            hasToken: !!token,
                            tokenLength: token ? token.length : 0,
                            hasExpiryTime: !!expiryTime,
                            expiryTime: expiryTime,
                            currentTime: new Date().toISOString()
                        });
        
                        if (token && !expiryTime) {
                            // 如果有Token但没有过期时间，可能是断开连接后重新连接
                            // 需要重新验证Token的有效性
                            console.log(`🔍 [${initId}] 检测到Token但无过期时间，正在验证Token有效性...`);
                            this.validateAndUpdateTokenExpiry(token);
                        }
        
                        // 立即检查一次Token状态
                        this.checkTokenRefresh();
        
                        // 设置定期检查（每分钟检查一次）
                        this.tokenRefreshInterval = setInterval(() => {
                            this.checkTokenRefresh();
                        }, APP_CONFIG.TIMEOUTS.TOKEN_REFRESH);
        
                        console.log(`✅ [${initId}] Token监控已启动，检查间隔: ${APP_CONFIG.TIMEOUTS.TOKEN_REFRESH}ms`);
        
                        // 记录到日志系统
                        if (window.Logger) {
                            window.Logger.addLog(`Token监控初始化 [${initId}]: 完成`, 'info');
                        }
                    }
        
                    static async validateAndUpdateTokenExpiry(token) {
                        try {
                            console.log('正在验证Token有效性...');
        
                            // 尝试调用一个简单的API来验证Token
                            const response = await fetch(`${ConfigManager.getApiBaseUrl()}/api/v1/monitor/health`, {
                                method: 'GET',
                                headers: {
                                    'Authorization': `Bearer ${token}`,
                                    'Content-Type': 'application/json'
                                }
                            });
        
                            if (response.ok) {
                                // Token有效，设置新的过期时间（从当前时间开始计算2小时）
                                const newExpiry = Date.now() + (2 * 60 * 60 * 1000);
                                localStorage.setItem('token_expiry_time', newExpiry.toString());
                                console.log('Token验证成功，设置新过期时间:', new Date(newExpiry).toLocaleString());
        
                                if (window.NotificationSystem) {
                                    window.NotificationSystem.show('Token验证成功', 'success');
                                }
                            } else if (response.status === 401) {
                                // Token无效，需要重新获取
                                console.warn('Token验证失败，需要重新获取');
                                // 不直接重定向，而是显示登录对话框
                                this.showLoginPrompt();
                            } else {
                                // 其他错误，可能是网络问题，暂时保持Token
                                console.warn('Token验证遇到网络问题，暂时保持现有Token');
                                // 设置一个较短的过期时间，以便稍后重新验证
                                const shortExpiry = Date.now() + (30 * 60 * 1000); // 30分钟
                                localStorage.setItem('token_expiry_time', shortExpiry.toString());
                            }
        
                        } catch (error) {
                            console.error('Token验证失败:', error);
                            // 网络错误时，设置较短的过期时间
                            const shortExpiry = Date.now() + (30 * 60 * 1000); // 30分钟
                            localStorage.setItem('token_expiry_time', shortExpiry.toString());
                            console.log('设置临时过期时间:', new Date(shortExpiry).toLocaleString());
                        }
                    }
        
                    static getTokenStatus() {
                        const token = localStorage.getItem('ys_api_token');
                        const expiryTime = localStorage.getItem('token_expiry_time');
        
                        if (!token) {
                            return { valid: false, message: 'Token不存在' };
                        }
        
                        if (!expiryTime) {
                            return { valid: true, message: 'Token有效（无过期时间）' };
                        }
        
                        const now = Date.now();
                        const expiry = parseInt(expiryTime);
                        const timeUntilExpiry = expiry - now;
        
                        if (timeUntilExpiry <= 0) {
                            return { valid: false, message: 'Token已过期' };
                        }
        
                        const minutesLeft = Math.round(timeUntilExpiry / 60000);
                        return {
                            valid: true,
                            message: `Token有效，剩余${minutesLeft}分钟`,
                            timeUntilExpiry: timeUntilExpiry
                        };
                    }
                }
        
                // ==================== 2. 配置管理 ====================
                class ConfigManager {
                    static getApiBaseUrl() {
                        // 优先从配置获取
                        const configUrl = localStorage.getItem('api_base_url');
                        if (configUrl) return configUrl;
        
                        // 直接返回origin，不处理路径
                        return window.location.origin;
                    }
        
                    static setApiBaseUrl(url) {
                        localStorage.setItem('api_base_url', url);
                    }
        
                    static getConfig() {
                        return {
                            API_BASE_URL: this.getApiBaseUrl(),
                            TIMEOUT: 60000,
                            RETRY_ATTEMPTS: 3,
                            RETRY_DELAY: 2000,
                            MAX_LOG_ENTRIES: 1000
                        };
                    }
                }
        
                // ==================== 3. 安全工具 ====================
                class SecurityUtils {
                    static escapeHtml(text) {
                        const div = document.createElement('div');
                        div.textContent = text;
                        return div.innerHTML;
                    }
        
                    static validateInput(input) {
                        // 限制输入长度和字符
                        if (!input || input.length > 1000) return false;
                        if (/[<>]/.test(input)) return false;
                        return true;
                    }
        
                    static sanitizeLogContent(content) {
                        if (!content) return '';
        
                        // 过滤敏感信息
                        const sensitivePatterns = [
                            /token["\s]*[:=]["\s]*[^"\s,}]+/gi,
                            /password["\s]*[:=]["\s]*[^"\s,}]+/gi,
                            /api_key["\s]*[:=]["\s]*[^"\s,}]+/gi,
                            /secret["\s]*[:=]["\s]*[^"\s,}]+/gi
                        ];
        
                        let sanitized = content;
                        sensitivePatterns.forEach(pattern => {
                            sanitized = sanitized.replace(pattern, (match) => {
                                const parts = match.split(/[:=]/);
                                return `${parts[0]}: ***HIDDEN***`;
                            });
                        });
        
                        return this.escapeHtml(sanitized);
                    }
        
                    static getCsrfToken() {
                        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                            localStorage.getItem('csrf_token');
                    }
        
                    static sanitizeData(data) {
                        if (typeof data === 'string') {
                            return this.escapeHtml(data);
                        }
                        if (typeof data === 'object' && data !== null) {
                            const sanitized = {};
                            for (const [key, value] of Object.entries(data)) {
                                sanitized[key] = this.sanitizeData(value);
                            }
                            return sanitized;
                        }
                        return data;
                    }
                }
        
                // ==================== 4. 增强的API客户端 ====================
                class EnhancedAPIClient {
                    static async request(endpoint, options = {}) {
                        const config = ConfigManager.getConfig();
                        const token = TokenManager.getToken();
        
                        if (!token) {
                            throw new Error('Token无效，请重新登录');
                        }
        
                        const url = `${config.API_BASE_URL}${endpoint}`;
                        const defaultOptions = {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'X-CSRF-Token': SecurityUtils.getCsrfToken(),
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        };
        
                        // 合并选项
                        const finalOptions = {
                            ...defaultOptions,
                            ...options,
                            headers: {
                                ...defaultOptions.headers,
                                ...(options.headers || {})
                            }
                        };
        
                        try {
                            // 使用AbortController实现超时
                            const controller = new AbortController();
                            const timeoutId = setTimeout(() => controller.abort(), config.TIMEOUT);
        
                            const response = await fetch(url, {
                                ...finalOptions,
                                signal: controller.signal
                            });
        
                            clearTimeout(timeoutId);
        
                            // 处理401未授权
                            if (response.status === 401) {
                                console.error('API返回401未授权，Token已失效');
                                TokenManager.handleTokenExpired();
                                throw new Error('Token已失效，请重新登录');
                            }
        
                            if (!response.ok) {
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }
        
                            const data = await response.json();
                            return data;
                        } catch (error) {
                            if (error.name === 'AbortError') {
                                throw new Error('请求超时');
                            }
                            throw error;
                        }
                    }
        
                    static async get(endpoint) {
                        return this.request(endpoint);
                    }
        
                    static async post(endpoint, data = null) {
                        const options = {
                            method: 'POST'
                        };
        
                        if (data !== null && Object.keys(data).length > 0) {
                            options.body = JSON.stringify(data);
                            options.headers = { 'Content-Type': 'application/json' };
                        }
        
                        return this.request(endpoint, options);
                    }
                }
        
                // ==================== 5. 任务管理器 ====================
                class TaskManager {
                    constructor() {
                        this.activeTasks = new Map();
                        this.statusPolling = null;
                    }
        
                    async startSyncTask(moduleName, options = {}) {
                        const taskId = `sync_${Date.now()}`;
        
                        try {
                            // 启动任务
                            const response = await EnhancedAPIClient.post('/api/v1/sync/single-improved', {
                                module_name: moduleName,
                                ...options
                            });
        
                            if (response.success) {
                                this.activeTasks.set(taskId, {
                                    type: 'sync',
                                    module: moduleName,
                                    startTime: Date.now(),
                                    status: 'running'
                                });
        
                                // 开始轮询状态
                                this.startStatusPolling(taskId);
        
                                // 创建任务UI
                                this.createTaskUI(taskId);
        
                                return { taskId, success: true };
                            }
                        } catch (error) {
                            console.error(`同步任务启动失败: ${error.message}`);
                            return { success: false, error: error.message };
                        }
                    }
        
                    startStatusPolling(taskId) {
                        this.statusPolling = setInterval(async () => {
                            const task = this.activeTasks.get(taskId);
                            if (!task) return;
        
                            try {
                                const status = await EnhancedAPIClient.get('/api/v1/sync/status-improved');
                                if (status.success) {
                                    task.status = status.data.is_running ? 'running' : 'completed';
                                    this.updateTaskUI(taskId, task);
        
                                    if (task.status === 'completed') {
                                        this.activeTasks.delete(taskId);
                                        clearInterval(this.statusPolling);
                                    }
                                }
                            } catch (error) {
                                console.error('状态轮询失败:', error);
                            }
                        }, 2000);
                    }
        
                    createTaskUI(taskId) {
                        const taskContainer = document.getElementById('task-container') || this.createTaskContainer();
                        const taskElement = document.createElement('div');
                        taskElement.id = `task-${taskId}`;
                        taskElement.className = 'task-item';
                        taskContainer.appendChild(taskElement);
                    }
        
                    createTaskContainer() {
                        const container = document.createElement('div');
                        container.id = 'task-container';
                        container.className = 'task-container';
                        document.body.appendChild(container);
                        return container;
                    }
        
                    updateTaskUI(taskId, task) {
                        const taskElement = document.getElementById(`task-${taskId}`);
                        if (taskElement) {
                            taskElement.innerHTML = `
                                <div class="task-status ${task.status}">
                                    <span class="task-module">${task.module}</span>
                                    <span class="task-status-text">${task.status === 'running' ? '运行中...' : '已完成'}</span>
                                    ${task.status === 'running' ? '<div class="spinner"></div>' : ''}
                                </div>
                            `;
                        }
                    }
                }
        
                // ==================== 6. 增强的日志系统 ====================
                class EnhancedLogger {
                    static addLog(message, type = 'info', container = 'log-container') {
                        const logContainer = document.getElementById(container);
                        if (!logContainer) return;
        
                        const sanitizedMessage = SecurityUtils.sanitizeLogContent(message);
        
                        const logEntry = document.createElement('div');
                        logEntry.className = `log-entry log-${type}`;
                        logEntry.innerHTML = `
                            <span class="log-timestamp">${new Date().toLocaleTimeString()}</span>
                            <span class="log-type">[${type.toUpperCase()}]</span>
                            <span class="log-message">${sanitizedMessage}</span>
                        `;
        
                        logContainer.appendChild(logEntry);
        
                        // 限制日志条目数量
                        this.limitLogEntries(logContainer);
        
                        // 自动滚动到底部
                        if (logContainer.scrollTop + logContainer.clientHeight >= logContainer.scrollHeight - 10) {
                            logContainer.scrollTop = logContainer.scrollHeight;
                        }
                    }
        
                    static limitLogEntries(container, maxEntries = 1000) {
                        const entries = container.children;
                        if (entries.length > maxEntries) {
                            const toRemove = entries.length - maxEntries;
                            for (let i = 0; i < toRemove; i++) {
                                container.removeChild(entries[0]);
                            }
                        }
                    }
        
                    static clearLogs(container = 'log-container') {
                        const logContainer = document.getElementById(container);
                        if (logContainer) {
                            logContainer.innerHTML = '';
                            this.addLog('日志已清空', 'info', container);
                        }
                    }
        
                    // exportLogs 方法已移至全局函数，避免重复定义
                }
        
                // ==================== 7. 错误处理工具 ====================
                class ErrorHandler {
                    static showError(message, details = null) {
                        const errorModal = document.createElement('div');
                        errorModal.className = 'error-modal';
                        errorModal.innerHTML = `
                            <div class="error-content">
                                <h3>操作失败</h3>
                                <p>${SecurityUtils.escapeHtml(message)}</p>
                                ${details ? `<details><summary>详细信息</summary><pre>${SecurityUtils.escapeHtml(details)}</pre></details>` : ''}
                                <button onclick="this.parentElement.parentElement.remove()">确定</button>
                            </div>
                        `;
        
                        document.body.appendChild(errorModal);
                    }
        
                    static getErrorMessage(error) {
                        if (error.message.includes('Failed to fetch')) {
                            return '网络连接失败，请检查服务器是否启动';
                        }
                        if (error.message.includes('timeout')) {
                            return '请求超时，请稍后重试';
                        }
                        if (error.message.includes('401')) {
                            return '登录已过期，请重新登录';
                        }
                        if (error.message.includes('未授权访问')) {
                            return '请先登录系统';
                        }
                        return error.message || '未知错误';
                    }
        
                    static handleError(error, context = '') {
                        const message = this.getErrorMessage(error);
                        console.error(`[${context}] ${error.message}`, error);
                        this.showError(message, context ? `${context}: ${error.message}` : error.message);
                    }
                }
        
                // ==================== 8. 修复应用器 ====================
                class DatabaseV2Fixer {
                    constructor() {
                        this.taskManager = new TaskManager();
                        this.isInitialized = false;
                    }
        
                    // 应用所有修复
                    applyFixes() {
                        try {
                            console.log('🔧 开始应用 Database-v2.html 修复...');
        
                            // 1. 替换全局配置
                            this.replaceGlobalConfig();
        
                            // 2. 替换API客户端
                            this.replaceAPIClient();
        
                            // 3. 替换日志系统
                            this.replaceLogger();
        
                            // 4. 添加错误处理
                            this.addErrorHandling();
        
                            // 5. 添加任务管理
                            this.addTaskManagement();
        
                            // 6. 添加安全检查
                            this.addSecurityChecks();
        
                            console.log('✅ Database-v2.html 修复应用完成');
                            this.isInitialized = true;
        
                        } catch (error) {
                            console.error('❌ 修复应用失败:', error);
                            ErrorHandler.handleError(error, '修复应用');
                        }
                    }
        
                    replaceGlobalConfig() {
                        // 替换全局CONFIG对象
                        if (typeof CONFIG !== 'undefined') {
                            Object.assign(CONFIG, ConfigManager.getConfig());
                        } else {
                            window.CONFIG = ConfigManager.getConfig();
                        }
        
                        // 替换API_BASE_URL
                        if (typeof API_BASE_URL !== 'undefined') {
                            window.API_BASE_URL = ConfigManager.getApiBaseUrl();
                        }
                    }
        
                    replaceAPIClient() {
                        // 替换全局APIClient
                        if (typeof APIClient !== 'undefined') {
                            Object.assign(APIClient, EnhancedAPIClient);
                        } else {
                            window.APIClient = EnhancedAPIClient;
                        }
                    }
        
                    replaceLogger() {
                        // 替换全局Logger
                        if (typeof Logger !== 'undefined') {
                            Object.assign(Logger, EnhancedLogger);
                        } else {
                            window.Logger = EnhancedLogger;
                        }
                    }
        
                    addErrorHandling() {
                        // 添加全局错误处理
                        window.addEventListener('error', (event) => {
                            ErrorHandler.handleError(event.error, '全局错误');
                        });
        
                        window.addEventListener('unhandledrejection', (event) => {
                            ErrorHandler.handleError(event.reason, '未处理的Promise错误');
                        });
                    }
        
                    addTaskManagement() {
                        // 添加任务管理到全局
                        window.taskManager = this.taskManager;
                    }
        
                    addSecurityChecks() {
                        // 检查Token有效性
                        const token = TokenManager.getToken();
                        if (!token) {
                            console.warn('未检测到有效Token，请先登录');
                        }
                    }
        
                    // 获取修复状态
                    getStatus() {
                        return {
                            isInitialized: this.isInitialized,
                            tokenValid: !!TokenManager.getToken(),
                            apiBaseUrl: ConfigManager.getApiBaseUrl(),
                            activeTasks: this.taskManager.activeTasks.size
                        };
                    }
                }
        
                // ==================== 9. 自动应用修复 ====================
                (function () {
                    'use strict';
        
                    // 等待DOM加载完成
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', initFixer);
                    } else {
                        initFixer();
                    }
        
                    function initFixer() {
                        try {
                            const fixer = new DatabaseV2Fixer();
                            fixer.applyFixes();
        
                            // 将修复器暴露到全局
                            window.databaseV2Fixer = fixer;
        
                            console.log('🎉 Database-v2.html 修复工具已加载');
                            console.log('📊 修复状态:', fixer.getStatus());
        
                            // 初始化Token监控
                            TokenManager.initTokenMonitoring();
        
                        } catch (error) {
                            console.error('❌ 修复工具初始化失败:', error);
                        }
                    }
                })();
        
                // ==================== 10. 导出工具类 ====================
                window.DatabaseV2Fixer = DatabaseV2Fixer;
                window.TokenManager = TokenManager;
                window.ConfigManager = ConfigManager;
                window.SecurityUtils = SecurityUtils;
                window.EnhancedAPIClient = EnhancedAPIClient;
                window.TaskManager = TaskManager;
                window.EnhancedLogger = EnhancedLogger;
                window.ErrorHandler = ErrorHandler;
        
                // 将TokenManager的方法暴露到全局
                window.getTokenStatus = TokenManager.getTokenStatus;
        
        // 全局配置（使用动态获取的API基础URL）
        
                // 防抖函数
                function debounce(fn, delay) {
                    let timer = null;
                    return function (...args) {
                        if (timer) clearTimeout(timer);
                        timer = setTimeout(() => fn.apply(this, args), delay);
                    };
                }
        
                // 全局配置（使用动态获取的API基础URL）
                const APP_CONFIG = {
                    API_ENDPOINTS: {
                        HEALTH: '/api/v1/monitor/health',
                        TOKEN: '/api/v1/auth/auto-token',
                        REFRESH: '/api/v1/auth/refresh',
                        MODULES: '/api/v1/modules',
                        SYNC_STATUS: '/api/v1/monitor/sync-status'
                    },
                    TIMEOUTS: {
                        DEFAULT: 30000,
                        UPLOAD: 300000,
                        TOKEN_REFRESH: 60000
                    },
                    RETRY: {
                        MAX_ATTEMPTS: 3,
                        DELAY: 1000
                    }
                };
        
                // 全局错误处理
                window.addEventListener('error', function (event) {
                    console.error('全局错误:', event.error);
                });
        
                window.addEventListener('unhandledrejection', function (event) {
                    console.error('未处理的Promise拒绝:', event.reason);
                });
        
                // 添加错误处理，忽略浏览器扩展相关的错误
                window.addEventListener('error', function (e) {
                    // 处理浏览器扩展相关错误
                    if (e.message && (
                        e.message.includes('runtime.lastError') ||
                        e.message.includes('message port closed') ||
                        e.message.includes('Extension context invalidated')
                    )) {
                        console.log('浏览器扩展通信错误，已忽略:', e.message);
                        return false; // 阻止错误显示
                    }
        
                    // 处理SSE连接错误 - 改进处理
                    if (e.message && (
                        e.message.includes('EventSource') ||
                        e.message.includes('Failed to load resource') ||
                        e.message.includes('ERR_CONNECTION_REFUSED')
                    )) {
                        console.log('SSE连接错误，将在稍后重试:', e.message);
                        return false; // 阻止错误显示
                    }
        
                    // 处理其他常见错误
                    if (e.message && e.message.includes('ResizeObserver loop limit exceeded')) {
                        console.log('ResizeObserver循环限制错误，已忽略');
                        return false;
                    }
        
                    // 处理网络相关错误
                    if (e.message && (
                        e.message.includes('net::ERR_CONNECTION_REFUSED') ||
                        e.message.includes('net::ERR_NETWORK') ||
                        e.message.includes('net::ERR_INTERNET_DISCONNECTED')
                    )) {
                        console.log('网络连接错误，将在稍后重试:', e.message);
                        return false;
                    }
                });
        
                // 处理未捕获的Promise错误
                window.addEventListener('unhandledrejection', function (e) {
                    // 处理浏览器扩展相关Promise错误
                    if (e.reason && e.reason.message && (
                        e.reason.message.includes('runtime.lastError') ||
                        e.reason.message.includes('message port closed') ||
                        e.reason.message.includes('Extension context invalidated')
                    )) {
                        console.log('浏览器扩展Promise错误，已忽略:', e.reason.message);
                        e.preventDefault(); // 阻止错误显示
                        return;
                    }
        
                    // 处理SSE连接Promise错误 - 改进处理
                    if (e.reason && e.reason.message && (
                        e.reason.message.includes('EventSource') ||
                        e.reason.message.includes('Failed to load resource') ||
                        e.reason.message.includes('ERR_CONNECTION_REFUSED')
                    )) {
                        console.log('SSE连接Promise错误，已忽略:', e.reason.message);
                        e.preventDefault();
                        return;
                    }
        
                    // 处理其他Promise错误
                    if (e.reason && e.reason.message && e.reason.message.includes('ResizeObserver')) {
                        console.log('ResizeObserver Promise错误，已忽略');
                        e.preventDefault();
                        return;
                    }
        
                    // 处理网络相关Promise错误
                    if (e.reason && e.reason.message && (
                        e.reason.message.includes('net::ERR_CONNECTION_REFUSED') ||
                        e.reason.message.includes('net::ERR_NETWORK') ||
                        e.reason.message.includes('net::ERR_INTERNET_DISCONNECTED')
                    )) {
                        console.log('网络连接Promise错误，已忽略:', e.reason.message);
                        e.preventDefault();
                        return;
                    }
                });
        
                // 处理Chrome扩展特定的错误
                if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.lastError) {
                    // 静默处理chrome.runtime.lastError
                    console.log('Chrome扩展错误处理已启用');
                }
        
                // 动态获取API基础URL - 确保使用正确的端口
                const API_BASE_URL = window.location.origin;
        
                // 全局连接状态更新函数
                function updateGlobalConnectionStatus(isConnected) {
                    // 确保DOM已加载完成
                    if (!document.body) {
                        setTimeout(() => updateGlobalConnectionStatus(isConnected), 100);
                        return;
                    }
        
                    const indicator = document.getElementById('connection-indicator');
                    const serverStatus = document.getElementById('server-status');
        
                    console.log('更新全局连接状态:', isConnected);
                    console.log('connection-indicator元素:', indicator);
                    console.log('server-status元素:', serverStatus);
        
                    if (indicator) {
                        if (isConnected) {
                            indicator.className = 'connection-indicator connected';
                            indicator.innerHTML = '✅ 已连接';
                            console.log('已更新connection-indicator为已连接');
                        } else {
                            indicator.className = 'connection-indicator disconnected';
                            indicator.innerHTML = '❌ 连接断开';
                            console.log('已更新connection-indicator为连接断开');
                        }
                    } else {
                        console.warn('connection-indicator元素未找到');
                    }
        
                    if (serverStatus) {
                        if (isConnected) {
                            serverStatus.className = 'status-badge online';
                            serverStatus.textContent = '在线';
                            console.log('已更新server-status为在线');
                        } else {
                            serverStatus.className = 'status-badge offline';
                            serverStatus.textContent = '离线';
                            console.log('已更新server-status为离线');
                        }
                    } else {
                        console.warn('server-status元素未找到');
                    }
                }
        
                // 系统配置
                const CONFIG = {
                    API_BASE_URL: API_BASE_URL, // 使用动态获取的API基础URL
                    TIMEOUT: 60000,  // 增加到60秒，数据库信息检查需要更长时间
                    RETRY_ATTEMPTS: 3,
                    RETRY_DELAY: 2000,
                    MAX_LOG_ENTRIES: 1000
                };
        
                // 系统状态管理
                const systemState = {
                    isConnected: false,
                    retryCount: 0,
                    statusUpdateInterval: null,
                    syncProgressInterval: null,
                    countdownInterval: null,
                    logEntries: [],
                    autoScroll: true,
                    lastConnectionCheck: 0,
                    connectionFailCount: 0,
                    maxFailCount: 3, // 连续失败3次才认为断开
                    autoSyncNextRunTime: null,
                    materialMasterNextRunTime: null,
                    realtimeLogEnabled: false, // 实时日志启用状态
                    currentTab: 'basic', // 当前日志选项卡
                    autoSyncRunning: false, // 自动同步运行状态
                    materialMasterRunning: false, // 物料档案运行状态
                    lastStatusUpdate: 0, // 最后状态更新时间
        
                    init() {
                        console.log('系统状态已初始化');
                        this.updateConnectionStatus(false);
                        // 页面加载时立即获取后端状态
                        this.refreshBackendStatus();
                    },
        
                    updateConnectionStatus(isConnected) {
                        // 避免频繁状态切换
                        const now = Date.now();
                        if (now - this.lastConnectionCheck < 2000) { // 2秒内不重复更新
                            return;
                        }
        
                        // 状态稳定机制
                        if (isConnected) {
                            this.connectionFailCount = 0; // 重置失败计数
                            if (!this.isConnected) {
                                this.isConnected = true;
                                this.lastConnectionCheck = now;
                                updateGlobalConnectionStatus(true);
                                console.log('✅ 连接状态已更新为: 已连接');
                                // 连接恢复时立即刷新后端状态
                                this.refreshBackendStatus();
                            }
                        } else {
                            this.connectionFailCount++;
                            if (this.connectionFailCount >= this.maxFailCount && this.isConnected) {
                                this.isConnected = false;
                                this.lastConnectionCheck = now;
                                updateGlobalConnectionStatus(false);
                                console.log('❌ 连接状态已更新为: 连接断开');
                                // 连接断开时重置状态
                                this.resetStatus();
                            }
                        }
        
                        // 更新状态显示
                        updateStatusItem('backend-status', this.isConnected ? '已连接' : '连接中');
                        updateStatusItem('db-status', this.isConnected ? '已连接' : '检查中');
                    },
        
                    // 刷新后端状态
                    async refreshBackendStatus() {
                        try {
                            console.log('刷新后端状态...');
                            const [autoSyncStatus, materialMasterStatus] = await Promise.allSettled([
                                AutoSyncAPI.getStatus(),
                                MaterialMasterAPI.getStatus()
                            ]);
        
                            // 更新自动同步状态
                            if (autoSyncStatus.status === 'fulfilled' && autoSyncStatus.value.success) {
                                const data = autoSyncStatus.value.data.data || autoSyncStatus.value.data;
                                this.autoSyncRunning = data.is_running || false;
                                console.log('自动同步状态:', this.autoSyncRunning);
                            }
        
                            // 更新物料档案状态
                            if (materialMasterStatus.status === 'fulfilled' && materialMasterStatus.value.success) {
                                const data = materialMasterStatus.value.data.data || materialMasterStatus.value.data;
                                this.materialMasterRunning = data.is_running || false;
                                console.log('物料档案状态:', this.materialMasterRunning);
                            }
        
                            // 更新按钮状态
                            updateButtonStates(this.autoSyncRunning, this.materialMasterRunning);
        
                            this.lastStatusUpdate = Date.now();
                            console.log('后端状态刷新完成');
                        } catch (error) {
                            console.error('刷新后端状态失败:', error);
                        }
                    },
        
                    // 重置状态
                    resetStatus() {
                        this.autoSyncRunning = false;
                        this.materialMasterRunning = false;
                        updateButtonStates(false, false);
                        console.log('状态已重置');
                    }
                };
        
        
        
                // 测试API连通性
                async function testAPIConnectivity() {
                    try {
                        // 使用AbortController实现超时
                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => controller.abort(), CONFIG.TIMEOUT);
        
                        const response = await fetch(`${CONFIG.API_BASE_URL}/api/v1/monitor/health`, {
                            method: 'GET',
                            signal: controller.signal
                        });
        
                        clearTimeout(timeoutId);
        
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                systemState.updateConnectionStatus(true);
                                return true;
                            }
                        }
        
                        systemState.updateConnectionStatus(false);
                        return false;
                    } catch (error) {
                        console.debug('API连通性测试失败:', error);
                        systemState.updateConnectionStatus(false);
                        return false;
                    }
                }
        
                // 初始化系统
                async function initializeSystem() {
                    console.log('系统初始化开始');
        
                    try {
                        // 立即测试连接
                        await testAPIConnectivity();
        
                        // 更新状态显示
                        updateStatusItem('backend-status', systemState.isConnected ? '已连接' : '连接中');
                        updateStatusItem('db-status', systemState.isConnected ? '已连接' : '检查中');
        
                        // 更新系统状态
                        updateStatusItem('system-status', systemState.isConnected ? '正常' : '检查中');
        
                        // 更新最后更新时间
                        updateStatusItem('last-update', new Date().toLocaleTimeString());
        
                        console.log('系统初始化完成');
        
                        // 如果连接成功，启动系统状态更新和自动启动调度器
                        if (systemState.isConnected) {
                            setTimeout(async () => {
                                try {
                                    // 更新系统状态
                                    await updateSystemStatus();
        
                                    // 自动启动调度器（如果未运行）
                                    await autoStartSchedulers();
        
                                    // 启动状态刷新定时器
                                    startStatusRefreshTimer();
                                } catch (error) {
                                    console.debug('系统状态更新失败:', error);
                                }
                            }, 1000);
                        }
                    } catch (error) {
                        console.error('系统初始化失败:', error);
                        updateStatusItem('system-status', '异常');
                        updateStatusItem('backend-status', '连接失败');
                        updateStatusItem('db-status', '检查失败');
                    }
                }
        
                // 自动启动调度器
                async function autoStartSchedulers() {
                    try {
                        console.log('检查并自动启动调度器...');
        
                        // 获取自动同步状态
                        const autoSyncStatus = await AutoSyncAPI.getStatus();
                        if (autoSyncStatus.success && autoSyncStatus.data) {
                            const data = autoSyncStatus.data.data || autoSyncStatus.data;
        
                            // 如果调度器未运行但配置已启用，则自动启动
                            if (!data.is_running && data.enabled) {
                                console.log('自动启动自动同步调度器...');
                                const startResult = await AutoSyncAPI.start();
                                if (startResult.success) {
                                    console.log('✅ 自动同步调度器已自动启动');
                                } else {
                                    console.warn('⚠️ 自动启动自动同步调度器失败:', startResult.error);
                                }
                            }
                        }
        
                        // 获取物料档案状态
                        const materialStatus = await MaterialMasterAPI.getStatus();
                        if (materialStatus.success && materialStatus.data) {
                            const data = materialStatus.data.data || materialStatus.data;
        
                            // 如果调度器未运行但配置已启用，则自动启动
                            if (!data.is_running && data.enabled) {
                                console.log('自动启动物料档案调度器...');
                                const startResult = await MaterialMasterAPI.start();
                                if (startResult.success) {
                                    console.log('✅ 物料档案调度器已自动启动');
                                } else {
                                    console.warn('⚠️ 自动启动物料档案调度器失败:', startResult.error);
                                }
                            }
                        }
        
                        // 重新更新系统状态以显示最新信息
                        await updateSystemStatus();
        
                        // 启动倒计时定时器
                        startCountdownTimer();
        
                    } catch (error) {
                        console.error('自动启动调度器失败:', error);
                    }
                }
        
                // 启动倒计时定时器
                function startCountdownTimer() {
                    // 清除现有定时器
                    if (systemState.countdownInterval) {
                        clearInterval(systemState.countdownInterval);
                    }
        
                    // 启动新的倒计时定时器（每秒更新）
                    systemState.countdownInterval = setInterval(() => {
                        updateCountdowns();
                    }, 1000);
        
                    console.log('倒计时定时器已启动');
                }
        
                // 启动状态刷新定时器
                function startStatusRefreshTimer() {
                    // 清除现有定时器
                    if (systemState.statusUpdateInterval) {
                        clearInterval(systemState.statusUpdateInterval);
                    }
        
                    // 启动新的状态刷新定时器（每30秒更新一次）
                    systemState.statusUpdateInterval = setInterval(async () => {
                        if (systemState.isConnected) {
                            try {
                                await systemState.refreshBackendStatus();
                                console.log('定期状态刷新完成');
                            } catch (error) {
                                console.error('定期状态刷新失败:', error);
                            }
                        }
                    }, 30000); // 30秒
        
                    console.log('状态刷新定时器已启动');
                }
        
                // 更新倒计时显示
                function updateCountdowns() {
                    try {
                        // 更新自动同步倒计时
                        if (systemState.autoSyncNextRunTime) {
                            const countdown = calculateCountdown(systemState.autoSyncNextRunTime);
                            const countdownElement = document.getElementById('auto-sync-countdown');
                            if (countdownElement) {
                                countdownElement.textContent = countdown;
                            }
                        }
        
                        // 更新物料档案倒计时
                        if (systemState.materialMasterNextRunTime) {
                            const countdown = calculateCountdown(systemState.materialMasterNextRunTime);
                            const countdownElement = document.getElementById('material-master-countdown');
                            if (countdownElement) {
                                countdownElement.textContent = countdown;
                            }
                        }
                    } catch (error) {
                        console.error('更新倒计时失败:', error);
                    }
                }
        
                // 更新状态项
                function updateStatusItem(id, value) {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                    }
                }
        
                // 通知系统
                class NotificationSystem {
                    static show(message, type = 'info', duration = 3000) {
                        const notification = document.createElement('div');
                        notification.className = `notification ${type}`;
                        notification.textContent = message;
        
                        // 样式
                        notification.style.cssText = `
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            padding: 12px 20px;
                            border-radius: 4px;
                            color: white;
                            font-size: 14px;
                            z-index: 10000;
                            opacity: 0;
                            transform: translateX(100%);
                            transition: all 0.3s ease;
                        `;
        
                        // 根据类型设置背景色
                        switch (type) {
                            case 'success':
                                notification.style.backgroundColor = '#4caf50';
                                break;
                            case 'error':
                                notification.style.backgroundColor = '#f44336';
                                break;
                            case 'warning':
                                notification.style.backgroundColor = '#ff9800';
                                break;
                            default:
                                notification.style.backgroundColor = '#2196f3';
                        }
        
                        document.body.appendChild(notification);
        
                        // 显示动画
                        setTimeout(() => {
                            notification.style.opacity = '1';
                            notification.style.transform = 'translateX(0)';
                        }, 100);
        
                        // 自动隐藏
                        setTimeout(() => {
                            notification.style.opacity = '0';
                            notification.style.transform = 'translateX(100%)';
                            setTimeout(() => {
                                if (document.body.contains(notification)) {
                                    document.body.removeChild(notification);
                                }
                            }, 300);
                        }, duration);
                    }
        
                    static init() {
                        // 初始化通知系统
                        console.log('通知系统已初始化');
                    }
                }
        
                // 日志管理器
                class Logger {
                    static addLog(message, type = 'info', container = 'log-container') {
                        const logContainer = document.getElementById(container);
                        const logEntry = document.createElement('div');
                        logEntry.className = `log-entry ${type}`;
                        logEntry.setAttribute('data-timestamp', new Date().toLocaleTimeString());
                        logEntry.textContent = message;
        
                        logContainer.appendChild(logEntry);
        
                        // 限制日志条目数量
                        if (logContainer.children.length > CONFIG.MAX_LOG_ENTRIES) {
                            logContainer.removeChild(logContainer.firstChild);
                        }
        
                        // 自动滚动
                        if (systemState.autoScroll) {
                            logContainer.scrollTop = logContainer.scrollHeight;
                        }
        
                        // 添加到全局日志数组
                        systemState.logEntries.push({
                            timestamp: new Date().toISOString(),
                            message: message,
                            type: type
                        });
                    }
        
                    static clearLogs(container = 'log-container') {
                        const logContainer = document.getElementById(container);
                        logContainer.innerHTML = '';
                        this.addLog('日志已清空', 'info', container);
                    }
        
                    // exportLogs 方法已移至全局函数，避免重复定义
                }
        
                // API客户端
                class APIClient {
                    static async request(endpoint, options = {}) {
                        const url = `${CONFIG.API_BASE_URL}${endpoint}`;
                        const token = localStorage.getItem('ys_api_token') || 'dev_token';
        
                        const defaultOptions = {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        };
        
                        // 合并选项，headers需要特殊处理
                        const finalOptions = {
                            ...defaultOptions,
                            ...options,
                            headers: {
                                ...defaultOptions.headers,
                                ...(options.headers || {})
                            }
                        };
        
                        try {
                            // 使用AbortController实现超时
                            const controller = new AbortController();
                            const timeoutId = setTimeout(() => controller.abort(), CONFIG.TIMEOUT);
        
                            const response = await fetch(url, {
                                ...finalOptions,
                                signal: controller.signal
                            });
        
                            clearTimeout(timeoutId);
        
                            if (!response.ok) {
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }
        
                            const data = await response.json();
                            systemState.updateConnectionStatus(true);
                            systemState.retryCount = 0;
        
                            return data;
                        } catch (error) {
                            systemState.updateConnectionStatus(false);
        
                            // 重试逻辑
                            if (systemState.retryCount < CONFIG.RETRY_ATTEMPTS) {
                                systemState.retryCount++;
                                Logger.addLog(`重试请求 ${endpoint} (${systemState.retryCount}/${CONFIG.RETRY_ATTEMPTS})`, 'warning');
        
                                await new Promise(resolve => setTimeout(resolve, CONFIG.RETRY_DELAY));
                                return this.request(endpoint, options);
                            }
        
                            throw error;
                        }
                    }
        
                    static async get(endpoint) {
                        return this.request(endpoint);
                    }
        
                    static async post(endpoint, data = null) {
                        const options = {
                            method: 'POST'
                        };
        
                        // 只有当data不为null且不为空对象时才发送请求体
                        if (data !== null && Object.keys(data).length > 0) {
                            options.body = JSON.stringify(data);
                            options.headers = { 'Content-Type': 'application/json' };
                        }
        
                        return this.request(endpoint, options);
                    }
                }
        
                // API接口对象
                const MonitorAPI = {
                    async healthCheck() {
                        try {
                            return await APIClient.get('/api/v1/monitor/health');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async getSyncStatus() {
                        try {
                            return await APIClient.get('/api/v1/monitor/sync-status');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async getDatabaseStatus() {
                        try {
                            return await APIClient.get('/api/v1/monitor/database/info');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    }
                };
        
                const AutoSyncAPI = {
                    async getStatus() {
                        try {
                            return await APIClient.get('/api/v1/tasks/auto-sync/status');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async start() {
                        try {
                            return await APIClient.post('/api/v1/tasks/auto-sync/start');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async stop() {
                        try {
                            return await APIClient.post('/api/v1/tasks/auto-sync/stop');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async trigger() {
                        try {
                            return await APIClient.post('/api/v1/tasks/auto-sync/trigger');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    }
                };
        
                const MaterialMasterAPI = {
                    async getStatus() {
                        try {
                            return await APIClient.get('/api/v1/tasks/material-master/status');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async start() {
                        try {
                            return await APIClient.post('/api/v1/tasks/material-master/start');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async stop() {
                        try {
                            return await APIClient.post('/api/v1/tasks/material-master/stop');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async trigger() {
                        try {
                            return await APIClient.post('/api/v1/tasks/material-master/trigger');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    }
                };
        
                const SyncAPI = {
                    async syncModule(module) {
                        try {
                            // 为同步操作使用更长的超时时间
                            const controller = new AbortController();
                            const timeoutId = setTimeout(() => controller.abort(), 300000); // 5分钟超时
        
                            const response = await fetch(`${CONFIG.API_BASE_URL}/api/v1/sync/write/single`, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                    module_name: module,
                                    record_limit: null,
                                    force_recreate_table: false,
                                    clear_existing_data: true
                                }),
                                signal: controller.signal
                            });
        
                            clearTimeout(timeoutId);
        
                            if (!response.ok) {
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }
        
                            return await response.json();
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async syncAllModules() {
                        try {
                            // 为全量同步使用更长的超时时间
                            const controller = new AbortController();
                            const timeoutId = setTimeout(() => controller.abort(), 600000); // 10分钟超时
        
                            const params = new URLSearchParams({
                                parallel_tasks: '3',
                                force_recreate_tables: 'false'
                            });
        
                            const response = await fetch(`${CONFIG.API_BASE_URL}/api/v1/sync/all?${params.toString()}`, {
                                method: 'POST',
                                signal: controller.signal
                            });
        
                            clearTimeout(timeoutId);
        
                            if (!response.ok) {
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }
        
                            return await response.json();
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async syncModuleRealtime(module) {
                        try {
                            // 为实时同步使用更长的超时时间
                            const controller = new AbortController();
                            const timeoutId = setTimeout(() => controller.abort(), 300000); // 5分钟超时
        
                            const response = await fetch(`${CONFIG.API_BASE_URL}/api/v1/sync/single-improved`, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                    module_name: module,
                                    record_limit: null,
                                    force_recreate_table: false,
                                    clear_existing_data: true
                                }),
                                signal: controller.signal
                            });
        
                            clearTimeout(timeoutId);
        
                            if (!response.ok) {
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }
        
                            return await response.json();
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    }
                };
        
                const DatabaseAPI = {
                    async reset() {
                        try {
                            return await APIClient.post('/api/v1/monitor/database/reset');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async create() {
                        try {
                            return await APIClient.post('/api/v1/monitor/database/create');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async getTables() {
                        try {
                            return await APIClient.get('/api/v1/monitor/database/info');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async getStatus() {
                        try {
                            console.log('🔍 开始获取数据库状态...');
                            const startTime = Date.now();
        
                            // 使用合理的超时时间
                            const controller = new AbortController();
                            const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时
        
                            const response = await fetch(`${CONFIG.API_BASE_URL}/api/v1/monitor/database/info`, {
                                method: 'GET',
                                signal: controller.signal
                            });
        
                            clearTimeout(timeoutId);
                            const endTime = Date.now();
        
                            console.log(`✅ 数据库状态API响应时间: ${endTime - startTime}ms`);
        
                            if (!response.ok) {
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }
        
                            const data = await response.json();
                            console.log('✅ 数据库状态API响应成功:', data);
        
                            return data;
                        } catch (error) {
                            console.error('❌ 数据库状态API失败:', error);
                            return { success: false, error: error.message };
                        }
                    },
        
                    async optimize() {
                        try {
                            return await APIClient.post('/api/v1/monitor/database/optimize');
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async createAllTables() {
                        try {
                            return await APIClient.post('/api/v1/database/tables/create-all', {
                                drop_if_exists: false
                            });
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    },
        
                    async createSingleTable(moduleName) {
                        try {
                            return await APIClient.post('/api/v1/database/tables/create', {
                                module_name: moduleName,
                                drop_if_exists: false
                            });
                        } catch (error) {
                            return { success: false, error: error.message };
                        }
                    }
                };
        
        
        
                // 测试API连通性 - 简化版本
                async function testAPIConnectivity() {
                    try {
                        // 使用AbortController实现超时
                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => controller.abort(), CONFIG.TIMEOUT);
        
                        const response = await fetch(`${CONFIG.API_BASE_URL}/api/v1/monitor/health`, {
                            method: 'GET',
                            signal: controller.signal
                        });
        
                        clearTimeout(timeoutId);
        
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                systemState.updateConnectionStatus(true);
                                return true;
                            }
                        }
        
                        systemState.updateConnectionStatus(false);
                        return false;
                    } catch (error) {
                        console.debug('API连通性测试失败:', error);
                        systemState.updateConnectionStatus(false);
                        return false;
                    }
                }
        
                // 计算倒计时
                function calculateCountdown(nextRunTime) {
                    try {
                        if (!nextRunTime) return '-';
        
                        const now = new Date();
                        const nextRun = new Date(nextRunTime);
                        const diff = nextRun.getTime() - now.getTime();
        
                        if (diff <= 0) return '即将运行';
        
                        const hours = Math.floor(diff / (1000 * 60 * 60));
                        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        
                        if (hours > 0) {
                            return `${hours}小时${minutes}分钟`;
                        } else if (minutes > 0) {
                            return `${minutes}分钟${seconds}秒`;
                        } else {
                            return `${seconds}秒`;
                        }
                    } catch (error) {
                        console.error('计算倒计时失败:', error);
                        return '-';
                    }
                }
        
                // 更新按钮状态
                function updateButtonStates(autoSyncRunning, materialMasterRunning) {
                    try {
                        // 通过 onclick 属性查找按钮元素
                        const autoSyncStartBtn = document.querySelector('button[onclick="startAutoSync()"]');
                        const autoSyncStopBtn = document.querySelector('button[onclick="stopAutoSync()"]');
                        const materialStartBtn = document.querySelector('button[onclick="startMaterialMaster()"]');
                        const materialStopBtn = document.querySelector('button[onclick="stopMaterialMaster()"]');
        
                        // 更新自动同步按钮状态
                        if (autoSyncStartBtn) {
                            autoSyncStartBtn.disabled = autoSyncRunning;
                            // 更新按钮文本（保持spinner元素）
                            const spinner = autoSyncStartBtn.querySelector('#auto-sync-start-spinner');
                            autoSyncStartBtn.innerHTML = '';
                            if (spinner) autoSyncStartBtn.appendChild(spinner);
                            autoSyncStartBtn.appendChild(document.createTextNode(autoSyncRunning ? '运行中' : '启动调度器'));
        
                            // 更新按钮样式
                            if (autoSyncRunning) {
                                autoSyncStartBtn.classList.add('disabled');
                                autoSyncStartBtn.classList.remove('success');
                                autoSyncStartBtn.title = '调度器正在运行中';
                            } else {
                                autoSyncStartBtn.classList.remove('disabled');
                                autoSyncStartBtn.classList.add('success');
                                autoSyncStartBtn.title = '启动自动同步调度器';
                            }
                        }
        
                        if (autoSyncStopBtn) {
                            autoSyncStopBtn.disabled = !autoSyncRunning;
                            // 更新按钮文本（保持spinner元素）
                            const spinner = autoSyncStopBtn.querySelector('#auto-sync-stop-spinner');
                            autoSyncStopBtn.innerHTML = '';
                            if (spinner) autoSyncStopBtn.appendChild(spinner);
                            autoSyncStopBtn.appendChild(document.createTextNode(autoSyncRunning ? '停止调度器' : '已停止'));
        
                            // 更新按钮样式
                            if (!autoSyncRunning) {
                                autoSyncStopBtn.classList.add('disabled');
                                autoSyncStopBtn.classList.remove('warning');
                                autoSyncStopBtn.title = '调度器未运行';
                            } else {
                                autoSyncStopBtn.classList.remove('disabled');
                                autoSyncStopBtn.classList.add('warning');
                                autoSyncStopBtn.title = '停止自动同步调度器';
                            }
                        }
        
                        // 更新物料档案按钮状态
                        if (materialStartBtn) {
                            materialStartBtn.disabled = materialMasterRunning;
                            // 更新按钮文本（保持spinner元素）
                            const spinner = materialStartBtn.querySelector('#material-start-spinner');
                            materialStartBtn.innerHTML = '';
                            if (spinner) materialStartBtn.appendChild(spinner);
                            materialStartBtn.appendChild(document.createTextNode(materialMasterRunning ? '运行中' : '启动调度器'));
        
                            // 更新按钮样式
                            if (materialMasterRunning) {
                                materialStartBtn.classList.add('disabled');
                                materialStartBtn.classList.remove('success');
                                materialStartBtn.title = '调度器正在运行中';
                            } else {
                                materialStartBtn.classList.remove('disabled');
                                materialStartBtn.classList.add('success');
                                materialStartBtn.title = '启动物料档案调度器';
                            }
                        }
        
                        if (materialStopBtn) {
                            materialStopBtn.disabled = !materialMasterRunning;
                            // 更新按钮文本（保持spinner元素）
                            const spinner = materialStopBtn.querySelector('#material-stop-spinner');
                            materialStopBtn.innerHTML = '';
                            if (spinner) materialStopBtn.appendChild(spinner);
                            materialStopBtn.appendChild(document.createTextNode(materialMasterRunning ? '停止调度器' : '已停止'));
        
                            // 更新按钮样式
                            if (!materialMasterRunning) {
                                materialStopBtn.classList.add('disabled');
                                materialStopBtn.classList.remove('warning');
                                materialStopBtn.title = '调度器未运行';
                            } else {
                                materialStopBtn.classList.remove('disabled');
                                materialStopBtn.classList.add('warning');
                                materialStopBtn.title = '停止物料档案调度器';
                            }
                        }
        
                        console.log(`按钮状态已更新: 自动同步=${autoSyncRunning}, 物料档案=${materialMasterRunning}`);
                    } catch (error) {
                        console.error('更新按钮状态失败:', error);
                    }
                }
        
                // 更新系统状态
                async function updateSystemStatus() {
                    try {
                        const [autoSyncStatus, materialMasterStatus, syncStatus, dbStatus] = await Promise.allSettled([
                            AutoSyncAPI.getStatus(),
                            MaterialMasterAPI.getStatus(),
                            MonitorAPI.getSyncStatus(),
                            DatabaseAPI.getStatus()
                        ]);
        
                        // 更新自动同步状态
                        if (autoSyncStatus.status === 'fulfilled' && autoSyncStatus.value.success) {
                            const response = autoSyncStatus.value;
                            const data = response.data.data || response.data;  // 处理嵌套的data结构
                            const statusText = data.is_running ? '运行中' : (data.enabled ? '已停止' : '已禁用');
                            updateStatusItem('auto-sync-status', `✅ ${statusText}`);
        
                            // 更新本地状态
                            systemState.autoSyncRunning = data.is_running || false;
        
                            // 更新下次运行时间和倒计时
                            const nextRunTime = data.next_run_time;
                            if (nextRunTime) {
                                // 保存下次运行时间用于倒计时更新
                                systemState.autoSyncNextRunTime = nextRunTime;
        
                                // 格式化显示时间
                                const nextRunDate = new Date(nextRunTime);
                                const formattedTime = nextRunDate.toLocaleString('zh-CN', {
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: false
                                });
                                updateStatusItem('auto-sync-next-value', formattedTime);
        
                                // 计算并显示倒计时
                                const countdown = calculateCountdown(nextRunTime);
                                const countdownElement = document.getElementById('auto-sync-countdown');
                                if (countdownElement) {
                                    countdownElement.textContent = countdown;
                                }
                            } else {
                                systemState.autoSyncNextRunTime = null;
                                updateStatusItem('auto-sync-next-value', '-');
                                const countdownElement = document.getElementById('auto-sync-countdown');
                                if (countdownElement) {
                                    countdownElement.textContent = '-';
                                }
                            }
        
        
        
                            // 更新按钮状态 - 只更新自动同步状态，物料档案状态稍后更新
                            systemState.autoSyncRunning = data.is_running || false;
                        } else {
                            updateStatusItem('auto-sync-status', '❌ 获取失败');
                            updateStatusItem('auto-sync-next-value', '-');
                            const countdownElement = document.getElementById('auto-sync-countdown');
                            if (countdownElement) {
                                countdownElement.textContent = '-';
                            }
                            updateStatusItem('auto-sync-last', '-');
        
                            // 获取失败时，保持按钮当前状态
                        }
        
                        // 更新物料档案状态
                        if (materialMasterStatus.status === 'fulfilled' && materialMasterStatus.value.success) {
                            const response = materialMasterStatus.value;
                            const data = response.data.data || response.data;  // 处理嵌套的data结构
                            const statusText = data.is_running ? '运行中' : (data.enabled ? '已停止' : '已禁用');
                            updateStatusItem('material-master-status', `✅ ${statusText}`);
        
                            // 更新本地状态
                            systemState.materialMasterRunning = data.is_running || false;
        
                            // 更新下次运行时间和倒计时
                            const nextRunTime = data.next_run_time;
                            if (nextRunTime) {
                                // 保存下次运行时间用于倒计时更新
                                systemState.materialMasterNextRunTime = nextRunTime;
        
                                // 格式化显示时间
                                const nextRunDate = new Date(nextRunTime);
                                const formattedTime = nextRunDate.toLocaleString('zh-CN', {
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: false
                                });
                                updateStatusItem('material-master-next-value', formattedTime);
        
                                // 计算并显示倒计时
                                const countdown = calculateCountdown(nextRunTime);
                                const countdownElement = document.getElementById('material-master-countdown');
                                if (countdownElement) {
                                    countdownElement.textContent = countdown;
                                }
                            } else {
                                systemState.materialMasterNextRunTime = null;
                                updateStatusItem('material-master-next-value', '-');
                                const countdownElement = document.getElementById('material-master-countdown');
                                if (countdownElement) {
                                    countdownElement.textContent = '-';
                                }
                            }
        
                            // 更新本地状态
                            systemState.materialMasterRunning = data.is_running || false;
                        } else {
                            updateStatusItem('material-master-status', '❌ 获取失败');
                            updateStatusItem('material-master-next-value', '-');
                            const countdownElement = document.getElementById('material-master-countdown');
                            if (countdownElement) {
                                countdownElement.textContent = '-';
                            }
                            updateStatusItem('material-master-last', '-');
        
                            // 获取失败时，保持按钮当前状态
                        }
        
                        // 更新同步状态
                        if (syncStatus.status === 'fulfilled' && syncStatus.value.success) {
                            const response = syncStatus.value;
                            const data = response.data || response;  // 处理可能的嵌套结构
                            const statusText = data.is_running ? '同步中' : '就绪';
                            updateStatusItem('sync-current-status', statusText);
                            updateStatusItem('sync-records', data.records_processed || '0');
                            updateStatusItem('sync-current-module', data.current_module || '-');
                            // 计算耗时
                            const duration = data.start_time ?
                                Math.round((Date.now() - new Date(data.start_time).getTime()) / 1000) + 's' :
                                '-';
                            updateStatusItem('sync-duration', duration);
                        } else {
                            updateStatusItem('sync-current-status', '❌ 获取失败');
                            updateStatusItem('sync-records', '0');
                            updateStatusItem('sync-current-module', '-');
                            updateStatusItem('sync-duration', '-');
                        }
        
                        // 更新数据库状态
                        if (dbStatus.status === 'fulfilled' && dbStatus.value.success) {
                            const response = dbStatus.value;
                            const data = response.data || response;  // 处理可能的嵌套结构
                            const summary = data.summary || {};
                            updateStatusItem('db-status', '✅ 正常');
                            updateStatusItem('db-table-count', summary.existing_tables || '0');
                            // 显示总记录数而不是数据库大小
                            const totalRecords = summary.total_records || 0;
                            updateStatusItem('db-size', totalRecords > 0 ? `${totalRecords} 条记录` : '0 条记录');
                            updateStatusItem('db-connections', '1');
                            console.log('✅ 数据库状态更新成功:', summary);
                        } else {
                            console.error('❌ 数据库状态获取失败:', dbStatus);
                            if (dbStatus.status === 'rejected') {
                                console.error('数据库状态API被拒绝:', dbStatus.reason);
                                updateStatusItem('db-status', '❌ 连接超时');
                            } else if (dbStatus.value && !dbStatus.value.success) {
                                console.error('数据库状态API返回失败:', dbStatus.value);
                                updateStatusItem('db-status', '❌ 获取失败');
                            } else {
                                updateStatusItem('db-status', '❌ 获取失败');
                            }
                            updateStatusItem('db-table-count', '0');
                            updateStatusItem('db-size', '-');
                            updateStatusItem('db-connections', '0');
                        }
        
                        // 统一更新按钮状态
                        updateButtonStates(systemState.autoSyncRunning, systemState.materialMasterRunning);
        
                        // 更新最后更新时间
                        const now = new Date();
                        const timeString = now.toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            hour12: false
                        });
                        updateStatusItem('last-update', timeString);
        
                    } catch (error) {
                        ErrorHandler.handle(error, '更新系统状态');
                    }
                }
        
                // 更新状态项
                function updateStatusItem(id, value) {
                    const element = document.getElementById(id);
                    if (element) {
                        // 确保value是字符串类型
                        const stringValue = String(value);
                        element.textContent = stringValue;
        
                        // 根据状态添加样式
                        const statusItem = element.closest('.status-item');
                        if (statusItem) {
                            statusItem.classList.remove('success', 'error', 'warning');
                            if (stringValue.includes('✅')) {
                                statusItem.classList.add('success');
                            } else if (stringValue.includes('❌')) {
                                statusItem.classList.add('error');
                            } else if (stringValue.includes('⚠️')) {
                                statusItem.classList.add('warning');
                            }
                        }
                    }
                }
        
                // 更新全局连接状态显示
                function updateGlobalConnectionStatus(isConnected) {
                    const indicator = document.getElementById('connection-indicator');
                    const serverStatus = document.getElementById('server-status');
        
                    console.log('更新全局连接状态:', isConnected);
                    console.log('connection-indicator元素:', indicator);
                    console.log('server-status元素:', serverStatus);
        
                    if (indicator) {
                        if (isConnected) {
                            indicator.className = 'connection-indicator connected';
                            indicator.innerHTML = '✅ 已连接';
                            console.log('已更新connection-indicator为已连接');
                        } else {
                            indicator.className = 'connection-indicator disconnected';
                            indicator.innerHTML = '❌ 连接断开';
                            console.log('已更新connection-indicator为连接断开');
                        }
                    } else {
                        console.warn('connection-indicator元素未找到');
                    }
        
                    if (serverStatus) {
                        if (isConnected) {
                            serverStatus.className = 'status-badge online';
                            serverStatus.textContent = '在线';
                            console.log('已更新server-status为在线');
                        } else {
                            serverStatus.className = 'status-badge offline';
                            serverStatus.textContent = '离线';
                            console.log('已更新server-status为离线');
                        }
                    } else {
                        console.warn('server-status元素未找到');
                    }
                }
        
                // 显示加载状态
                function showSpinner(spinnerId) {
                    const spinner = document.getElementById(spinnerId);
                    if (spinner) {
                        spinner.classList.remove('hidden');
                    }
                }
        
                // 隐藏加载状态
                function hideSpinner(spinnerId) {
                    const spinner = document.getElementById(spinnerId);
                    if (spinner) {
                        spinner.classList.add('hidden');
                    }
                }
        
                // 自动同步操作
                async function startAutoSync() {
                    try {
                        showSpinner('auto-sync-start-spinner');
                        const result = await AutoSyncAPI.start();
        
                        if (result.success) {
                            Logger.addLog('✅ 自动同步调度器已启动', 'success');
                            NotificationSystem.show('自动同步调度器已启动', 'success');
                            // 立即更新本地状态
                            systemState.autoSyncRunning = true;
                            updateButtonStates(systemState.autoSyncRunning, systemState.materialMasterRunning);
                            // 然后更新系统状态
                            await updateSystemStatus();
                        } else {
                            Logger.addLog(`❌ 启动自动同步调度器失败: ${result.error}`, 'error');
                            NotificationSystem.show('启动自动同步调度器失败', 'error');
                        }
                    } catch (error) {
                        ErrorHandler.handle(error, '启动自动同步调度器');
                    } finally {
                        hideSpinner('auto-sync-start-spinner');
                    }
                }
        
                async function stopAutoSync() {
                    try {
                        showSpinner('auto-sync-stop-spinner');
                        const result = await AutoSyncAPI.stop();
        
                        if (result.success) {
                            Logger.addLog('✅ 自动同步调度器已停止', 'success');
                            NotificationSystem.show('自动同步调度器已停止', 'success');
                            // 立即更新本地状态
                            systemState.autoSyncRunning = false;
                            updateButtonStates(systemState.autoSyncRunning, systemState.materialMasterRunning);
                            // 然后更新系统状态
                            await updateSystemStatus();
                        } else {
                            Logger.addLog(`❌ 停止自动同步调度器失败: ${result.error}`, 'error');
                            NotificationSystem.show('停止自动同步调度器失败', 'error');
                        }
                    } catch (error) {
                        ErrorHandler.handle(error, '停止自动同步调度器');
                    } finally {
                        hideSpinner('auto-sync-stop-spinner');
                    }
                }
        
                async function triggerAutoSync() {
                    try {
                        showSpinner('auto-sync-trigger-spinner');
                        const result = await AutoSyncAPI.trigger();
        
                        if (result.success) {
                            Logger.addLog('✅ 已触发自动同步', 'success');
                            NotificationSystem.show('已触发自动同步', 'success');
                            await updateSystemStatus();
                        } else {
                            Logger.addLog(`❌ 触发自动同步失败: ${result.error}`, 'error');
                            NotificationSystem.show('触发自动同步失败', 'error');
                        }
                    } catch (error) {
                        ErrorHandler.handle(error, '触发自动同步');
                    } finally {
                        hideSpinner('auto-sync-trigger-spinner');
                    }
                }
        
                // 物料档案操作
                async function startMaterialMaster() {
                    try {
                        showSpinner('material-start-spinner');
                        const result = await MaterialMasterAPI.start();
        
                        if (result.success) {
                            Logger.addLog('✅ 物料档案调度器已启动', 'success');
                            NotificationSystem.show('物料档案调度器已启动', 'success');
                            // 立即更新本地状态
                            systemState.materialMasterRunning = true;
                            updateButtonStates(systemState.autoSyncRunning, systemState.materialMasterRunning);
                            // 然后更新系统状态
                            await updateSystemStatus();
                        } else {
                            Logger.addLog(`❌ 启动物料档案调度器失败: ${result.error}`, 'error');
                            NotificationSystem.show('启动物料档案调度器失败', 'error');
                        }
                    } catch (error) {
                        ErrorHandler.handle(error, '启动物料档案调度器');
                    } finally {
                        hideSpinner('material-start-spinner');
                    }
                }
        
                async function stopMaterialMaster() {
                    try {
                        showSpinner('material-stop-spinner');
                        const result = await MaterialMasterAPI.stop();
        
                        if (result.success) {
                            Logger.addLog('✅ 物料档案调度器已停止', 'success');
                            NotificationSystem.show('物料档案调度器已停止', 'success');
                            // 立即更新本地状态
                            systemState.materialMasterRunning = false;
                            updateButtonStates(systemState.autoSyncRunning, systemState.materialMasterRunning);
                            // 然后更新系统状态
                            await updateSystemStatus();
                        } else {
                            Logger.addLog(`❌ 停止物料档案调度器失败: ${result.error}`, 'error');
                            NotificationSystem.show('停止物料档案调度器失败', 'error');
                        }
                    } catch (error) {
                        ErrorHandler.handle(error, '停止物料档案调度器');
                    } finally {
                        hideSpinner('material-stop-spinner');
                    }
                }
        
                async function triggerMaterialMaster() {
                    try {
                        showSpinner('material-trigger-spinner');
                        const result = await MaterialMasterAPI.trigger();
        
                        if (result.success) {
                            Logger.addLog('✅ 已触发物料档案同步', 'success');
                            NotificationSystem.show('已触发物料档案同步', 'success');
                            await updateSystemStatus();
                        } else {
                            Logger.addLog(`❌ 触发物料档案同步失败: ${result.error}`, 'error');
                            NotificationSystem.show('触发物料档案同步失败', 'error');
                        }
                    } catch (error) {
                        ErrorHandler.handle(error, '触发物料档案同步');
                    } finally {
                        hideSpinner('material-trigger-spinner');
                    }
                }
        
                // 数据同步操作
                // 使用防抖包装同步函数
                const debouncedSyncSingleModule = debounce(async function () {
                    await syncSingleModuleImpl();
                }, 300);
        
                async function syncSingleModule() {
                    debouncedSyncSingleModule();
                }
        
                async function syncSingleModuleImpl() {
                    const module = document.getElementById('sync-module').value;
        
                    if (!module) {
                        Logger.addLog('❌ 请选择要同步的模块', 'error');
                        NotificationSystem.show('请选择要同步的模块', 'warning');
                        return;
                    }
        
                    try {
                        showSpinner('sync-single-spinner');
                        const result = await SyncAPI.syncModule(module);  // 移除limit参数，始终全量同步
        
                        if (result.success) {
                            const moduleCnName = mapModuleName(module);
                            Logger.addLog(`✅ 已开始全量同步 ${moduleCnName}`, 'success');
                            NotificationSystem.show(`已开始全量同步 ${moduleCnName}`, 'success');
                            await updateSystemStatus();
                        } else {
                            const moduleCnName = mapModuleName(module);
                            Logger.addLog(`❌ 同步 ${moduleCnName} 失败: ${result.error}`, 'error');
                            NotificationSystem.show(`同步 ${moduleCnName} 失败`, 'error');
                        }
                    } catch (error) {
                        ErrorHandler.handle(error, `同步 ${module} 模块`);
                    } finally {
                        hideSpinner('sync-single-spinner');
                    }
                }
        
                async function syncAllModules() {
                    if (!confirm('确定要同步所有15个模块吗？这将从YS-API获取所有模块数据并写入数据库。')) {
                        return;
                    }
        
                    try {
                        showSpinner('sync-all-spinner');
                        Logger.addLog('🔄 正在启动所有模块同步...', 'info');
        
                        const result = await SyncAPI.syncAllModules();
        
                        if (result.success) {
                            Logger.addLog('✅ 所有模块同步启动成功', 'success');
                            Logger.addLog(`📋 任务ID: ${result.data?.task_id || 'N/A'}`, 'info');
                            Logger.addLog(`📊 模块数量: ${result.data?.module_count || 15}`, 'info');
                            Logger.addLog(`🔄 并行任务: ${result.data?.parallel_tasks || 3}`, 'info');
        
                            NotificationSystem.show('所有模块同步已启动', 'success');
        
                            // 同步结果详情
                            if (result.sync_result) {
                                const syncResult = result.sync_result;
                                const successCount = syncResult.success_count || 0;
                                const failedCount = syncResult.failed_count || 0;
        
                                Logger.addLog(`📈 同步结果: 成功 ${successCount} 个，失败 ${failedCount} 个`,
                                    failedCount > 0 ? 'warning' : 'success');
        
                                if (syncResult.results) {
                                    Object.entries(syncResult.results).forEach(([module, moduleResult]) => {
                                        const status = moduleResult.success ? '✅' : '❌';
                                        Logger.addLog(`${status} ${module}: ${moduleResult.message || ''}`,
                                            moduleResult.success ? 'success' : 'error');
                                    });
                                }
                            }
        
                            await updateSystemStatus();
                        } else {
                            // 处理错误响应
                            let errorMessage = '未知错误';
                            if (result.error) {
                                if (typeof result.error === 'string') {
                                    errorMessage = result.error;
                                } else if (result.error.message) {
                                    errorMessage = result.error.message;
                                } else {
                                    errorMessage = JSON.stringify(result.error);
                                }
                            } else if (result.message) {
                                errorMessage = result.message;
                            }
        
                            Logger.addLog(`❌ 同步所有模块失败: ${errorMessage}`, 'error');
        
                            // 检查是否是422错误，提供更详细的错误信息
                            if (typeof errorMessage === 'string' && errorMessage.includes('422')) {
                                Logger.addLog('💡 提示: 参数格式错误已修复，请重试', 'warning');
                                Logger.addLog('🔧 现在使用正确的查询参数格式', 'info');
                            }
        
                            NotificationSystem.show('同步所有模块失败', 'error');
                        }
                    } catch (error) {
                        // 处理异常
                        let errorMessage = '未知异常';
                        if (error.message) {
                            errorMessage = error.message;
                        } else if (typeof error === 'string') {
                            errorMessage = error;
                        } else {
                            errorMessage = JSON.stringify(error);
                        }
        
                        Logger.addLog(`❌ 同步所有模块异常: ${errorMessage}`, 'error');
        
                        // 检查是否是422错误
                        if (typeof errorMessage === 'string' && errorMessage.includes('422')) {
                            Logger.addLog('💡 参数格式已修复，请刷新页面后重试', 'warning');
                        }
        
                        ErrorHandler.handle(error, '同步所有模块');
                    } finally {
                        hideSpinner('sync-all-spinner');
                    }
                }
        
                // 使用防抖包装实时同步函数
                const debouncedSyncSingleModuleWithRealtime = debounce(async function () {
                    await syncSingleModuleWithRealtimeImpl();
                }, 300);
        
                async function syncSingleModuleWithRealtime() {
                    debouncedSyncSingleModuleWithRealtime();
                }
        
                async function syncSingleModuleWithRealtimeImpl() {
                    const module = document.getElementById('sync-module').value;
        
                    if (!module) {
                        Logger.addLog('❌ 请选择要同步的模块', 'error');
                        NotificationSystem.show('请选择要同步的模块', 'warning');
                        return;
                    }
        
                    try {
                        showSpinner('sync-realtime-spinner');
                        const result = await SyncAPI.syncModuleRealtime(module);  // 移除limit参数，始终全量同步
        
                        if (result.success) {
                            const moduleCnName = mapModuleName(module);
                            Logger.addLog(`✅ 已开始实时全量同步 ${moduleCnName}`, 'success');
                            NotificationSystem.show(`已开始实时全量同步 ${moduleCnName}`, 'success');
                            await updateSystemStatus();
                        } else {
                            const moduleCnName = mapModuleName(module);
                            Logger.addLog(`❌ 实时同步 ${moduleCnName} 失败: ${result.error}`, 'error');
                            NotificationSystem.show(`实时同步 ${moduleCnName} 失败`, 'error');
                        }
                    } catch (error) {
                        const moduleCnName = mapModuleName(module);
                        ErrorHandler.handle(error, `实时同步 ${moduleCnName}`);
                    } finally {
                        hideSpinner('sync-realtime-spinner');
                    }
                }
        
                async function checkSyncStatus() {
                    try {
                        Logger.addLog('🔄 正在检查同步状态...', 'info');
                        const syncStatus = await MonitorAPI.getSyncStatus();
        
                        if (syncStatus.success) {
                            const data = syncStatus.data || syncStatus;
                            const statusText = data.is_running ? '同步中' : '就绪';
        
                            updateStatusItem('sync-current-status', statusText);
                            updateStatusItem('sync-records', data.records_processed || '0');
                            updateStatusItem('sync-current-module', data.current_module || '-');
        
                            // 计算耗时
                            const duration = data.start_time ?
                                Math.round((Date.now() - new Date(data.start_time).getTime()) / 1000) + 's' :
                                '-';
                            updateStatusItem('sync-duration', duration);
        
                            Logger.addLog('✅ 同步状态检查完成', 'success');
                            Logger.addLog(`📊 当前状态: ${statusText}`, 'info');
                            Logger.addLog(`📈 处理记录: ${data.records_processed || 0} 条`, 'info');
                            Logger.addLog(`🎯 当前模块: ${data.current_module || '无'}`, 'info');
                            Logger.addLog(`⏱️ 耗时: ${duration}`, 'info');
        
                            NotificationSystem.show('同步状态检查完成', 'success');
                        } else {
                            Logger.addLog(`❌ 检查同步状态失败: ${syncStatus.error}`, 'error');
                            NotificationSystem.show('检查同步状态失败', 'error');
                        }
                    } catch (error) {
                        ErrorHandler.handle(error, '检查同步状态');
                    }
                }
        
                // 数据库操作
                async function resetDatabase() {
                    if (confirm('确定要重置数据库吗？此操作将删除所有数据，请确保已备份！')) {
                        try {
                            showSpinner('reset-db-spinner');
                            const result = await DatabaseAPI.reset();
        
                            if (result.success) {
                                Logger.addLog('✅ 数据库已重置', 'success');
                                NotificationSystem.show('数据库已重置', 'success');
                                await updateSystemStatus();
                            } else {
                                Logger.addLog(`❌ 重置数据库失败: ${result.error}`, 'error');
                                NotificationSystem.show('重置数据库失败', 'error');
                            }
                        } catch (error) {
                            ErrorHandler.handle(error, '重置数据库');
                        } finally {
                            hideSpinner('reset-db-spinner');
                        }
                    }
                }
        
                async function showDatabaseTables() {
                    try {
                        showSpinner('show-tables-spinner');
                        Logger.addLog('🔄 正在获取数据库表结构信息...', 'info');
        
                        const tables = await DatabaseAPI.getTables();
        
                        if (tables.success && tables.data && tables.data.tables) {
                            const tableList = tables.data.tables;
                            const tableCount = tables.data.table_count || tableList.length;
        
                            Logger.addLog(`📋 数据库表结构信息 (共 ${tableCount} 个表)：`, 'info');
        
                            if (tableList.length > 0) {
                                tableList.forEach(table => {
                                    Logger.addLog(`- ${table.name} (${table.columns || 0} 列)`, 'info');
                                });
                                NotificationSystem.show(`已获取数据库表结构 (${tableCount} 个表)`, 'success');
                            } else {
                                Logger.addLog('数据库中没有找到任何表', 'warning');
                                NotificationSystem.show('数据库中没有找到任何表', 'warning');
                            }
                        } else {
                            Logger.addLog(`❌ 获取数据库表结构失败: ${tables.error || '未知错误'}`, 'error');
                            NotificationSystem.show('获取数据库表结构失败', 'error');
                        }
                    } catch (error) {
                        ErrorHandler.handle(error, '查看数据库表结构');
                    } finally {
                        hideSpinner('show-tables-spinner');
                    }
                }
        
                async function checkDatabaseStatus() {
                    try {
                        const status = await DatabaseAPI.getStatus();
        
                        if (status.success) {
                            updateStatusItem('db-status', `✅ ${status.status || '正常'}`);
                            updateStatusItem('db-table-count', status.table_count || '0');
                            updateStatusItem('db-size', status.size || '-');
                            updateStatusItem('db-connections', status.connections || '0');
        
                            Logger.addLog('✅ 已检查数据库状态', 'success');
                            NotificationSystem.show('已检查数据库状态', 'success');
                        } else {
                            Logger.addLog(`❌ 检查数据库状态失败: ${status.error}`, 'error');
                            NotificationSystem.show('检查数据库状态失败', 'error');
                        }
                    } catch (error) {
                        ErrorHandler.handle(error, '检查数据库状态');
                    }
                }
        
                async function optimizeDatabase() {
                    if (!confirm('数据库优化将清理数据库结构、重建索引和优化性能。\n\n⚠️ 注意：此操作不会删除现有数据，但会花费一些时间。\n\n确定要继续吗？')) {
                        return;
                    }
        
                    try {
                        showSpinner('optimize-db-spinner');
                        Logger.addLog('🔄 正在优化数据库...', 'info');
                        Logger.addLog('📋 优化内容：清理结构、重建索引、优化性能', 'info');
        
                        const result = await DatabaseAPI.optimize();
        
                        if (result.success) {
                            Logger.addLog('✅ 数据库优化完成', 'success');
                            Logger.addLog('🔧 已完成：结构清理、索引重建、性能优化', 'info');
                            NotificationSystem.show('数据库优化完成', 'success');
                            await updateSystemStatus();
                        } else {
                            Logger.addLog(`❌ 数据库优化失败: ${result.error}`, 'error');
                            NotificationSystem.show('数据库优化失败', 'error');
                        }
                    } catch (error) {
                        ErrorHandler.handle(error, '数据库优化');
                    } finally {
                        hideSpinner('optimize-db-spinner');
                    }
                }
        
                async function createDatabase() {
                    try {
                        showSpinner('create-db-spinner');
                        Logger.addLog('🔄 正在创建数据库...', 'info');
        
                        const result = await DatabaseAPI.create();
        
                        if (result.success) {
                            const status = result.data?.status;
                            if (status === 'already_exists') {
                                Logger.addLog('ℹ️ 数据库已存在', 'info');
                                NotificationSystem.show('数据库已存在', 'info');
                            } else {
                                Logger.addLog('✅ 数据库创建成功', 'success');
                                NotificationSystem.show('数据库创建成功', 'success');
                            }
                            await updateSystemStatus();
                        } else {
                            Logger.addLog(`❌ 数据库创建失败: ${result.error}`, 'error');
                            NotificationSystem.show('数据库创建失败', 'error');
                        }
                    } catch (error) {
                        ErrorHandler.handle(error, '创建数据库');
                    } finally {
                        hideSpinner('create-db-spinner');
                    }
                }
        
                async function createAllTables() {
                    if (!confirm('确定要创建所有数据库表吗？这将为所有模块创建表结构。')) {
                        return;
                    }
        
                    try {
                        showSpinner('create-all-tables-spinner');
                        Logger.addLog('🔄 正在创建所有数据库表...', 'info');
        
                        const result = await DatabaseAPI.createAllTables();
        
                        if (result.success) {
                            const summary = result.summary || {};
                            const successCount = summary.success_count || 0;
                            const failedCount = summary.failed_count || 0;
        
                            // 改进提示逻辑
                            if (failedCount === 0) {
                                Logger.addLog(`✅ 批量创建完成: 成功创建 ${successCount} 个表`, 'success');
                                NotificationSystem.show(`批量创建完成: 成功创建 ${successCount} 个表`, 'success');
                            } else {
                                Logger.addLog(`⚠️ 批量创建完成: 成功 ${successCount} 个，${failedCount} 个表已存在`, 'warning');
                                NotificationSystem.show(`批量创建完成: 成功 ${successCount} 个，${failedCount} 个表已存在`, 'warning');
                            }
        
                            // 显示详细结果
                            if (result.data && result.data.results) {
                                const results = result.data.results;
                                for (const [moduleName, moduleResult] of Object.entries(results)) {
                                    let status, logType;
                                    if (moduleResult.success) {
                                        status = '✅';
                                        logType = 'success';
                                    } else if (moduleResult.table_exists) {
                                        status = '⚠️';
                                        logType = 'warning';
                                    } else {
                                        status = '❌';
                                        logType = 'error';
                                    }
                                    Logger.addLog(`${status} ${moduleName}: ${moduleResult.message}`, logType);
                                }
                            }
        
                            await updateSystemStatus();
                        } else {
                            Logger.addLog(`❌ 创建所有表失败: ${result.error}`, 'error');
                            NotificationSystem.show('创建所有表失败', 'error');
                        }
                    } catch (error) {
                        ErrorHandler.handle(error, '创建所有表');
                    } finally {
                        hideSpinner('create-all-tables-spinner');
                    }
                }
        
                async function createSingleTable(moduleName) {
                    try {
                        Logger.addLog(`🔄 正在创建 ${moduleName} 表...`, 'info');
        
                        const result = await DatabaseAPI.createSingleTable(moduleName);
        
                        if (result.success) {
                            const tableName = result.data?.table_name || moduleName;
                            const fieldsCount = result.data?.total_fields || 0;
        
                            Logger.addLog(`✅ 创建表成功: ${tableName} (${fieldsCount} 个字段)`, 'success');
                            NotificationSystem.show(`创建表成功: ${tableName}`, 'success');
        
                            await updateSystemStatus();
                        } else {
                            // 区分不同类型的"失败"
                            if (result.data && result.data.table_exists) {
                                Logger.addLog(`⚠️ 表已存在: ${moduleName}`, 'warning');
                                NotificationSystem.show('表已存在，如需重建请选择删除重建选项', 'warning');
                            } else {
                                Logger.addLog(`❌ 创建表失败: ${result.error || result.message}`, 'error');
                                NotificationSystem.show('创建表失败', 'error');
                            }
                        }
                    } catch (error) {
                        Logger.addLog(`❌ 创建表异常: ${error.message}`, 'error');
                        ErrorHandler.handle(error, '创建表');
                    }
                }
        
                function showCreateSingleTableModal() {
                    const modules = [
                        { name: 'applyorder', display: '请购单' },
                        { name: 'business_log', display: '业务日志' },
                        { name: 'inventory', display: '库存管理' },
                        { name: 'inventory_report', display: '现存量报表' },
                        { name: 'material_master', display: '物料档案' },
                        { name: 'materialout', display: '材料出库' },
                        { name: 'product_receipt', display: '产品入库' },
                        { name: 'production_order', display: '生产订单' },
                        { name: 'purchase_order', display: '采购订单' },
                        { name: 'purchase_receipt', display: '采购入库' },
                        { name: 'requirements_planning', display: '需求计划' },
                        { name: 'sales_order', display: '销售订单' },
                        { name: 'sales_out', display: '销售出库' },
                        { name: 'subcontract_order', display: '委外订单' },
                        { name: 'subcontract_receipt', display: '委外入库' },
                        { name: 'subcontract_requisition', display: '委外申请' }
                    ];
        
                    const modalHTML = `
                        <div id="create-table-modal" class="modal-overlay">
                            <div class="modal-content">
                                <h3>创建单个数据库表</h3>
                                <div class="form-group">
                                    <label>选择模块:</label>
                                    <select id="module-select">
                                        ${modules.map(m => `<option value="${m.name}">${m.display}</option>`).join('')}
                                    </select>
                                </div>
                                <div class="modal-actions">
                                    <button class="btn primary" onclick="executeCreateSingleTable()">创建表</button>
                                    <button class="btn secondary" onclick="closeCreateTableModal()">取消</button>
                                </div>
                            </div>
                        </div>
                    `;
        
                    document.body.insertAdjacentHTML('beforeend', modalHTML);
                }
        
                function closeCreateTableModal() {
                    const modal = document.getElementById('create-table-modal');
                    if (modal) {
                        modal.remove();
                    }
                }
        
                async function executeCreateSingleTable() {
                    const moduleSelect = document.getElementById('module-select');
                    const moduleName = moduleSelect.value;
        
                    if (moduleName) {
                        closeCreateTableModal();
                        await createSingleTable(moduleName);
                    }
                }
        
                // 日志操作
                function clearLogs() {
                    Logger.clearLogs();
                    Logger.clearLogs('realtime-log-container');
                    Logger.clearLogs('error-log-container');
                    NotificationSystem.show('日志已清空', 'success');
                }
        
                function exportLogs() {
                    try {
                        // 获取所有日志容器的内容
                        const containers = ['log-container', 'realtime-log-container', 'error-log-container'];
                        let allLogs = [];
        
                        containers.forEach(containerId => {
                            const container = document.getElementById(containerId);
                            if (container && !container.classList.contains('hidden')) {
                                const logs = Array.from(container.querySelectorAll('.log-entry')).map(entry =>
                                    entry.textContent.trim()
                                ).filter(text => text.length > 0);
                                allLogs = allLogs.concat(logs);
                            }
                        });
        
                        // 如果没有找到日志，使用系统状态中的日志
                        if (allLogs.length === 0 && systemState.logEntries) {
                            allLogs = systemState.logEntries.map(entry =>
                                `[${entry.timestamp}] [${entry.type.toUpperCase()}] ${entry.message}`
                            );
                        }
        
                        if (allLogs.length === 0) {
                            NotificationSystem.show('没有可导出的日志', 'warning');
                            return;
                        }
        
                        const logsText = allLogs.join('\n');
                        const blob = new Blob([logsText], { type: 'text/plain;charset=utf-8' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `ys-api-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
                        a.style.display = 'none';
        
                        // 使用更可靠的下载方法
                        document.body.appendChild(a);
        
                        // 添加事件监听器确保下载完成
                        a.addEventListener('click', function () {
                            setTimeout(() => {
                                document.body.removeChild(a);
                                URL.revokeObjectURL(url);
                            }, 100);
                        });
        
                        // 触发下载
                        a.click();
        
                        Logger.addLog('📤 日志已导出', 'success');
                        NotificationSystem.show('日志已导出，请检查下载文件夹', 'success');
        
                        // 备用清理方法
                        setTimeout(() => {
                            if (document.body.contains(a)) {
                                document.body.removeChild(a);
                            }
                            URL.revokeObjectURL(url);
                        }, 1000);
        
                    } catch (error) {
                        console.error('导出日志失败:', error);
                        NotificationSystem.show('导出日志失败: ' + error.message, 'error');
                    }
                }
        
        
        
                // 实时日志查看器实例
                let realtimeLogViewer = null;
        
                function toggleRealtimeLog() {
                    const button = document.getElementById('realtime-toggle');
                    const spinner = document.getElementById('realtime-spinner');
        
                    console.log('切换实时日志状态，当前状态:', systemState.realtimeLogEnabled);
        
                    if (systemState.realtimeLogEnabled) {
                        // 停用实时日志
                        systemState.realtimeLogEnabled = false;
                        button.innerHTML = '<span id="realtime-spinner" class="spinner hidden"></span>启用实时日志';
        
                        if (realtimeLogViewer) {
                            console.log('断开实时日志连接');
                            try {
                                realtimeLogViewer.disconnect();
                            } catch (error) {
                                console.warn('断开实时日志连接时出现错误:', error);
                            }
                        }
        
                        Logger.addLog('✅ 已停用实时日志', 'success');
                        NotificationSystem.show('已停用实时日志', 'success');
        
                        // 切换回基础日志
                        switchLogTab('basic');
                    } else {
                        // 启用实时日志
                        systemState.realtimeLogEnabled = true;
                        button.innerHTML = '<span id="realtime-spinner" class="spinner hidden"></span>停用实时日志';
        
                        console.log('开始创建实时日志查看器...');
        
                        try {
                            // 创建实时日志查看器
                            if (!realtimeLogViewer) {
                                console.log('创建新的RealtimeLogViewer实例');
                                realtimeLogViewer = new RealtimeLogViewer('realtime-log-container', {
                                    autoScroll: true,
                                    maxLogs: 1000,
                                    showTimestamp: true,
                                    showStage: true,
                                    showModule: true,
                                    apiBaseUrl: ConfigManager.getApiBaseUrl(),
                                    reconnectInterval: 5000,
                                    sseEndpoint: '/api/v1/logs/stream'
                                });
                            }
        
                            console.log('连接实时日志流...');
                            realtimeLogViewer.connect();
        
                            Logger.addLog('✅ 已启用实时日志', 'success');
                            NotificationSystem.show('已启用实时日志', 'success');
        
                            // 自动切换到实时日志选项卡
                            switchLogTab('realtime');
        
                            console.log('实时日志启用完成');
                        } catch (error) {
                            console.error('启用实时日志失败:', error);
                            systemState.realtimeLogEnabled = false;
                            button.innerHTML = '<span id="realtime-spinner" class="spinner hidden"></span>启用实时日志';
        
                            Logger.addLog('❌ 启用实时日志失败，请检查服务器连接', 'error');
                            NotificationSystem.show('启用实时日志失败', 'error');
                        }
                    }
                }
        
                function toggleAutoScroll() {
                    systemState.autoScroll = document.getElementById('auto-scroll').checked;
                    Logger.addLog(`✅ 自动滚动已${systemState.autoScroll ? '启用' : '禁用'}`, 'info');
                }
        
                function switchLogTab(tab) {
                    const containers = ['log-container', 'realtime-log-container', 'error-log-container'];
                    const tabs = ['basic', 'realtime', 'error'];
        
                    // 隐藏所有容器
                    containers.forEach(id => {
                        document.getElementById(id).classList.add('hidden');
                    });
        
                    // 移除所有活动状态
                    document.querySelectorAll('.tab-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });
        
                    // 显示选中的容器
                    if (tab === 'basic') {
                        document.getElementById('log-container').classList.remove('hidden');
                    } else if (tab === 'realtime') {
                        document.getElementById('realtime-log-container').classList.remove('hidden');
        
                        // 如果实时日志未启用，提示用户
                        if (!systemState.realtimeLogEnabled) {
                            Logger.addLog('💡 提示: 实时日志未启用，点击"启用实时日志"按钮开始实时监控', 'info');
                        }
                    } else if (tab === 'error') {
                        document.getElementById('error-log-container').classList.remove('hidden');
                    }
        
                    // 设置活动状态
                    document.querySelector(`button[onclick="switchLogTab('${tab}')"]`).classList.add('active');
        
                    systemState.currentTab = tab;
        
                    Logger.addLog(`📋 切换到${tab === 'basic' ? '基础' : tab === 'realtime' ? '实时' : '错误'}日志`, 'info');
                }
        
                // 配置对话框
                async function showAutoSyncConfig() {
                    const modal = document.getElementById('config-modal');
                    const configContent = document.getElementById('config-content');
        
                    try {
                        // 获取当前配置
                        const response = await APIClient.get('/api/v1/tasks/auto-sync/config');
                        const config = response.success ? response.data : {};
        
                        // 使用当前配置值或默认值
                        const syncInterval = config.sync_interval_minutes || 30;
                        const enableNotifications = config.enable_notifications !== false;
        
                        configContent.innerHTML = `
                            <div class="form-group">
                                <label>同步间隔 (分钟):</label>
                                <input type="number" id="sync-interval" value="${syncInterval}" min="1" max="1440">
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="enable-notifications" ${enableNotifications ? 'checked' : ''}>
                                    启用通知
                                </label>
                            </div>
                            <div style="margin-top: 20px;">
                                <button class="btn success" onclick="saveConfig()">保存配置</button>
                                <button class="btn secondary" onclick="closeModal()">取消</button>
                            </div>
                        `;
        
                        modal.style.display = 'block';
        
                    } catch (error) {
                        Logger.addLog(`❌ 加载配置失败: ${error.message}`, 'error');
                        NotificationSystem.show('加载配置失败', 'error');
                    }
                }
        
                // 页面加载完成后初始化
                document.addEventListener('DOMContentLoaded', function () {
                    console.log('YS-API V3.1 数据库管理中心已加载');
        
                    // 简单的Token初始化
                    if (!localStorage.getItem('ys_api_token')) {
                        localStorage.setItem('ys_api_token', 'dev_token');
                        console.log('已设置默认Token');
                    }
        
                    // 初始化系统状态
                    systemState.init();
        
                    // 初始化通知系统
                    NotificationSystem.init();
        
                    // 立即初始化基础Logger
                    window.Logger = {
                        addLog: (message, type) => console.log(`[${type.toUpperCase()}] ${message}`),
                        logError: (error, context) => console.error(`[ERROR] ${context}: ${error}`),
                        logWarning: (warning, context) => console.warn(`[WARNING] ${context}: ${warning}`),
                        logSuccess: (message, context) => console.log(`[SUCCESS] ${context}: ${message}`),
                        logInfo: (message, context) => console.log(`[INFO] ${context}: ${message}`),
                        updateConnectionStatus: (isConnected, details) => {
                            console.log(`[CONNECTION] ${isConnected ? '已连接' : '连接失败'}: ${details}`);
                            if (typeof updateGlobalConnectionStatus === 'function') {
                                updateGlobalConnectionStatus(isConnected);
                            }
                        },
                        updateSystemStatus: (status, details) => console.log(`[SYSTEM] ${status}: ${details}`),
                        updateSyncStatus: (status, module, records) => console.log(`[SYNC] ${status} - ${module} (${records} 条记录)`),
                        logDatabaseOperation: (operation, table, records) => console.log(`[DB] ${operation} ${table} (${records} 条记录)`)
                    };
        
                    // 延迟初始化系统
                    setTimeout(() => {
                        initializeSystem();
                    }, 500);
                });
        
                // 一键对比数据差异
                async function compareData() {
                    const module = document.getElementById('sync-module').value;
                    if (!module) {
                        NotificationSystem.show('请选择模块', 'warning');
                        return;
                    }
                    
                    try {
                        showSpinner('compare-spinner');
                        Logger.addLog(`🔍 开始对比 ${mapModuleName(module)} 数据差异...`, 'info');
                        
                        // 获取API数据样本
                        const apiResponse = await APIClient.get(`/api/v1/sync/verify/${module}?record_limit=100`);
                        const apiCount = apiResponse.success ? (apiResponse.records_written || 0) : 0;
                        
                        // 获取数据库记录数
                        const dbResponse = await APIClient.get(`/api/v1/monitor/database/info`);
                        let dbCount = 0;
                        if (dbResponse.success && dbResponse.data && dbResponse.data.tables) {
                            const tableInfo = dbResponse.data.tables.find(t => t.name.includes(module));
                            dbCount = tableInfo ? (tableInfo.record_count || 0) : 0;
                        }
                        
                        const difference = Math.abs(apiCount - dbCount);
                        const moduleCnName = mapModuleName(module);
                        
                        // 显示对比结果
                        const resultMessage = `${moduleCnName} 数据对比结果：\n\nAPI 样本: ${apiCount} 条\n数据库: ${dbCount} 条\n差异: ${difference} 条\n\n${difference > 0 ? '⚠️ 存在数据差异，建议重新同步' : '✅ 数据一致'}`;
                        
                        alert(resultMessage);
                        
                        Logger.addLog(`📊 ${moduleCnName} 对比完成: API ${apiCount} 条, DB ${dbCount} 条, 差异 ${difference} 条`, 
                                    difference > 0 ? 'warning' : 'success');
                        
                        if (difference > 0) {
                            NotificationSystem.show(`${moduleCnName} 存在数据差异`, 'warning');
                        } else {
                            NotificationSystem.show(`${moduleCnName} 数据一致`, 'success');
                        }
                        
                    } catch (error) {
                        Logger.addLog(`❌ 对比数据失败: ${error.message}`, 'error');
                        NotificationSystem.show('对比数据失败', 'error');
                    } finally {
                        hideSpinner('compare-spinner');
                    }
                }
        
                // 页面离开时清理
                window.addEventListener('beforeunload', function () {
                    // 清理TokenManager资源
                    if (window.TokenManager) {
                        TokenManager.cleanup();
                    }
        
                    // 清理系统状态定时器
                    if (systemState.statusUpdateInterval) {
                        clearInterval(systemState.statusUpdateInterval);
                    }
                    if (systemState.syncProgressInterval) {
                        clearInterval(systemState.syncProgressInterval);
                    }
                    if (systemState.countdownInterval) {
                        clearInterval(systemState.countdownInterval);
                    }
        
                    // 清理实时日志连接
                    if (window.realtimeLogViewer) {
                        window.realtimeLogViewer.disconnect();
                    }
        
                    console.log('页面资源已清理');
                });
        
                // 关闭模态框
                function closeModal() {
                    const modal = document.getElementById('config-modal');
                    if (modal) {
                        modal.style.display = 'none';
                    }
                }
        
                // 保存配置
                async function saveConfig() {
                    try {
                        const syncInterval = document.getElementById('sync-interval').value;
                        const enableNotifications = document.getElementById('enable-notifications').checked;
        
                        const config = {
                            sync_interval_minutes: parseInt(syncInterval),
                            enable_notifications: enableNotifications
                        };
        
                        // 这里可以添加保存配置的API调用
                        Logger.addLog(`✅ 配置已保存: 同步间隔=${syncInterval}分钟, 通知=${enableNotifications ? '启用' : '禁用'}`, 'success');
                        NotificationSystem.show('配置保存成功', 'success');
        
                        closeModal();
                    } catch (error) {
                        Logger.addLog(`❌ 保存配置失败: ${error.message}`, 'error');
                        NotificationSystem.show('保存配置失败', 'error');
                    }
                }
        
    </script>
</body>
</html>