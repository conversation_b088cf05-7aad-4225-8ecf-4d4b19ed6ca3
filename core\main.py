import logging
import sys
from pathlib import Path

import uvicorn

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 服务器启动脚本 - core目录MVP版本
最小可运行单元
"""


# 设置日志
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

if __name__ == "__main__":
    logger.info("🚀 启动 YS-API V3.0 最小可运行单元 (MVP)")
    logger.info("📡 服务器地址: http://localhost:8001")
    logger.info("🔧 健康检查: http://localhost:8001/health")
    logger.info("=" * 50)

    try:
        uvicorn.run(
            "app.main:app",
            host="127.0.0.1",
            port=8050,  # 固定端口 8050，禁止改动
            log_level="info",
            access_log=True,
            reload=False,
        )
    except KeyboardInterrupt:
        logger.info("\n🛑 服务器已停止")
    except Exception:
        logger.error(f"❌ 启动失败: {e}")
        sys.exit(1)
