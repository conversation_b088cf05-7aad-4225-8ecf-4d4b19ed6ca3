import difflib
import hashlib
import json
import time
from pathlib import Path

#!/usr/bin/env python3
"""
配置回滚机制实现
处理配置冲突和自动回滚
"""


class ConfigRollbackManager:
    """配置回滚管理器"""

    def __init___(self, backup_dir="config_backups"):
    """TODO: Add function description."""
    self.backup_dir = Path(backup_dir)
    self.backup_dir.mkdir(exist_ok=True)

    # 版本历史存储
    self.version_history = {}
    self._load_version_history()

    def _load_version_history(self):
        """加载版本历史"""
        history_file = self.backup_dir / "version_history.json"
        if history_file.exists():
            try:
                with open(history_file, 'r', encoding='utf-8') as f:
                    self.version_history = json.load(f)
            except Exception:
                print(f"加载版本历史失败: {e}")

    def _save_version_history(self):
        """保存版本历史"""
        history_file = self.backup_dir / "version_history.json"
        try:
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(
                    self.version_history,
                    f,
                    indent=2,
                    ensure_ascii=False)
        except Exception:
            print(f"保存版本历史失败: {e}")

    def create_backup(self, module_name: str, config_data: Dict[str, Any],
                      user_id: str = "default", description: str = "") -> str:
        """创建配置备份"""
        # 生成版本ID
        timestamp = int(time.time())
        data_hash = hashlib.md5(
            json.dumps(
                config_data,
                sort_keys=True).encode()).hexdigest()[
            :8]
        version_id = f"v{timestamp}_{data_hash}"

        # 备份数据结构
        backup_data = {
            "version_id": version_id,
            "module_name": module_name,
            "user_id": user_id,
            "config_data": config_data,
            "timestamp": timestamp,
            "description": description,
            "backup_type": "manual"
        }

        # 保存到文件
        backup_file = self.backup_dir / f"{module_name}_{version_id}.json"
        try:
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)

            # 更新版本历史
            if module_name not in self.version_history:
                self.version_history[module_name] = []

            self.version_history[module_name].append({
                "version_id": version_id,
                "timestamp": timestamp,
                "description": description,
                "backup_file": str(backup_file),
                "user_id": user_id
            })

            # 保持最近20个版本
            if len(self.version_history[module_name]) > 20:
                old_versions = self.version_history[module_name][:-20]
                for old_version in old_versions:
                    old_file = Path(old_version["backup_file"])
                    if old_file.exists():
                        old_file.unlink()
                self.version_history[module_name] = self.version_history[module_name][-20:]

            self._save_version_history()
            print(f"✅ 创建备份成功: {module_name} -> {version_id}")
            return version_id

        except Exception:
            print(f"❌ 创建备份失败: {e}")
            return ""

    def detect_conflict(self, module_name: str,
                        new_config: Dict[str, Any]) -> Dict[str, Any]:
        """检测配置冲突"""
        if module_name not in self.version_history or not self.version_history[module_name]:
            return {"has_conflict": False, "message": "没有历史版本"}

        # 获取最新版本
        latest_version = self.version_history[module_name][-1]
        latest_config = self._load_config_by_version(
            module_name, latest_version["version_id"])

        if not latest_config:
            return {"has_conflict": False, "message": "无法加载最新版本"}

        # 比较配置
        conflicts = self._compare_configs(
            latest_config["config_data"], new_config)

        if conflicts:
            return {
                "has_conflict": True,
                "conflicts": conflicts,
                "latest_version": latest_version["version_id"],
                "conflict_count": len(conflicts)
            }
        else:
            return {"has_conflict": False, "message": "没有冲突"}

    def _compare_configs(
            self,
            old_config: Dict,
            new_config: Dict,
            path: str = "") -> List[Dict]:
        """比较两个配置，返回冲突列表"""
        conflicts = []

        # 检查新增字段
        for key in new_config:
            current_path = f"{path}.{key}" if path else key
            if key not in old_config:
                conflicts.append({
                    "type": "added",
                    "path": current_path,
                    "new_value": new_config[key]
                })
            elif isinstance(new_config[key], dict) and isinstance(old_config[key], dict):
                # 递归比较嵌套字典
                sub_conflicts = self._compare_configs(
                    old_config[key], new_config[key], current_path)
                conflicts.extend(sub_conflicts)
            elif new_config[key] != old_config[key]:
                conflicts.append({
                    "type": "modified",
                    "path": current_path,
                    "old_value": old_config[key],
                    "new_value": new_config[key]
                })

        # 检查删除的字段
        for key in old_config:
            current_path = f"{path}.{key}" if path else key
            if key not in new_config:
                conflicts.append({
                    "type": "removed",
                    "path": current_path,
                    "old_value": old_config[key]
                })

        return conflicts

    def auto_resolve(self,
                     module_name: str,
                     conflict_data: Dict[str,
                                         Any]) -> Dict[str,
                                                       Any]:
        """自动解决冲突"""
        if not conflict_data.get("has_conflict"):
            return {"resolved": True, "message": "没有冲突需要解决"}

        conflicts = conflict_data["conflicts"]
        resolution_strategy = {
            "added": "accept",      # 接受新增字段
            "modified": "merge",    # 尝试合并修改
            "removed": "keep"       # 保留被删除的字段
        }

        resolved_conflicts = []
        for conflict in conflicts:
            conflict_type = conflict["type"]
            strategy = resolution_strategy.get(conflict_type, "manual")

            if strategy == "accept":
                resolved_conflicts.append({
                    "conflict": conflict,
                    "resolution": "接受新值",
                    "action": "add_field"
                })
            elif strategy == "merge" and conflict_type == "modified":
                # 简单的值合并策略
                resolved_conflicts.append({
                    "conflict": conflict,
                    "resolution": "使用新值",
                    "action": "update_field"
                })
            elif strategy == "keep":
                resolved_conflicts.append({
                    "conflict": conflict,
                    "resolution": "保留原值",
                    "action": "restore_field"
                })
            else:
                resolved_conflicts.append({
                    "conflict": conflict,
                    "resolution": "需要手动处理",
                    "action": "manual"
                })

        auto_resolved_count = len(
            [c for c in resolved_conflicts if c["action"] != "manual"])
        manual_count = len(
            [c for c in resolved_conflicts if c["action"] == "manual"])

        return {
            "resolved": manual_count == 0,
            "auto_resolved_count": auto_resolved_count,
            "manual_count": manual_count,
            "resolutions": resolved_conflicts
        }

    def manual_resolve(self, module_name: str, conflict_data: Dict[str, Any],
                       user_choices: Dict[str, str]) -> Dict[str, Any]:
        """手动解决冲突"""
        if not conflict_data.get("has_conflict"):
            return {"resolved": True, "message": "没有冲突需要解决"}

        conflicts = conflict_data["conflicts"]
        resolved_conflicts = []

        for i, conflict in enumerate(conflicts):
            conflict_id = f"conflict_{i}"
            user_choice = user_choices.get(conflict_id, "new")  # 默认选择新值

            if user_choice == "old":
                action = "keep_old"
            elif user_choice == "new":
                action = "use_new"
            elif user_choice == "merge":
                action = "merge_values"
            else:
                action = "skip"

            resolved_conflicts.append({
                "conflict": conflict,
                "user_choice": user_choice,
                "action": action
            })

        return {
            "resolved": True,
            "resolution_type": "manual",
            "resolutions": resolved_conflicts
        }

    def rollback_to_version(self, module_name: str, version_id: str) -> bool:
        """回滚到指定版本"""
        backup_data = self._load_config_by_version(module_name, version_id)
        if not backup_data:
            print(f"❌ 版本 {version_id} 不存在")
            return False

        try:
            # 创建当前状态的备份
            current_backup_id = self.create_backup(
                module_name,
                backup_data["config_data"],
                backup_data["user_id"],
                f"回滚前自动备份 (from {version_id})"
            )

            print(f"✅ 回滚成功: {module_name} -> {version_id}")
            print(f"💾 当前状态已备份为: {current_backup_id}")
            return True

        except Exception:
            print(f"❌ 回滚失败: {e}")
            return False

    def _load_config_by_version(
            self,
            module_name: str,
            version_id: str) -> Optional[Dict]:
        """根据版本ID加载配置"""
        backup_file = self.backup_dir / f"{module_name}_{version_id}.json"
        if backup_file.exists():
            try:
                with open(backup_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                print(f"加载版本配置失败: {e}")
        return None

    def list_versions(self, module_name: str, limit: int = 10) -> List[Dict]:
        """列出模块的版本历史"""
        if module_name not in self.version_history:
            return []

        versions = self.version_history[module_name][-limit:]
        return [{"version_id": v["version_id"],
                 "timestamp": v["timestamp"],
                 "time_str": time.strftime("%Y-%m-%d %H:%M:%S",
                                           time.localtime(v["timestamp"])),
                 "description": v["description"],
                 "user_id": v["user_id"]} for v in reversed(versions)]

    def get_version_diff(
            self,
            module_name: str,
            version1: str,
            version2: str) -> List[str]:
        """获取两个版本之间的差异"""
        config1 = self._load_config_by_version(module_name, version1)
        config2 = self._load_config_by_version(module_name, version2)

        if not config1 or not config2:
            return ["无法加载版本配置"]

        # 转换为字符串进行比较
        text1 = json.dumps(
            config1["config_data"],
            indent=2,
            sort_keys=True,
            ensure_ascii=False)
        text2 = json.dumps(
            config2["config_data"],
            indent=2,
            sort_keys=True,
            ensure_ascii=False)

        diff = list(difflib.unified_diff(
            text1.splitlines(keepends=True),
            text2.splitlines(keepends=True),
            fromfile=f"Version {version1}",
            tofile=f"Version {version2}"
        ))

        return diff


def demo_config_rollback():
    """演示配置回滚机制"""
    manager = ConfigRollbackManager()

    print("🚀 配置回滚机制演示")
    print("=" * 50)

    module_name = "材料出库单列表查询"

    # 创建初始配置
    initial_config = {
        "api_endpoint": "/api/v1/material/outbound",
        "timeout": 30,
        "fields": ["material_code", "quantity"]
    }

    # 修改后的配置（有冲突）
    modified_config = {
        "api_endpoint": "/api/v2/material/outbound",  # 修改
        "timeout": 60,                                # 修改
        "fields": ["material_code", "quantity", "warehouse"],  # 新增
        "cache_enabled": True                         # 新增
        # 注意：没有包含某些原有字段，会被检测为删除
    }

    # 步骤1: 创建初始备份
    print("\n📝 步骤1: 创建初始配置备份")
    version1 = manager.create_backup(
        module_name, initial_config, description="初始配置")

    # 步骤2: 检测冲突
    print("\n🔍 步骤2: 检测配置冲突")
    conflict_result = manager.detect_conflict(module_name, modified_config)
    print(f"是否有冲突: {conflict_result.get('has_conflict', False)}")
    if conflict_result.get('has_conflict'):
        print(f"冲突数量: {conflict_result['conflict_count']}")
        for conflict in conflict_result['conflicts'][:3]:  # 只显示前3个
            print(f"  {conflict['type']}: {conflict['path']}")

    # 步骤3: 自动解决冲突
    print("\n🤖 步骤3: 自动解决冲突")
    auto_result = manager.auto_resolve(module_name, conflict_result)
    print(f"自动解决: {auto_result['auto_resolved_count']} 个")
    print(f"需手动处理: {auto_result['manual_count']} 个")

    # 步骤4: 创建修改后的备份
    print("\n📝 步骤4: 创建修改后配置备份")
    version2 = manager.create_backup(
        module_name, modified_config, description="修改版本")

    # 步骤5: 查看版本历史
    print("\n📜 步骤5: 版本历史")
    versions = manager.list_versions(module_name)
    for version in versions:
        print(
            f"  {version['version_id']}: {version['time_str']} - {version['description']}")

    # 步骤6: 模拟回滚
    print("\n↩️ 步骤6: 回滚到初始版本")
    if len(versions) >= 2:
        rollback_success = manager.rollback_to_version(
            module_name, versions[1]['version_id'])
        print(f"回滚结果: {'成功' if rollback_success else '失败'}")


if __name__ == "__main__":
    demo_config_rollback()
