import asyncio
import hashlib
import random
import statistics
import threading
import time
from enum import Enum

import structlog

"""
YS-API V3.0 负载均衡器
Month 3 Week 4: 智能负载均衡和请求调度
"""


logger = structlog.get_logger()


class BalancingStrategy(Enum):
    """负载均衡策略"""
    ROUND_ROBIN = "round_robin"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    LEAST_CONNECTIONS = "least_connections"
    LEAST_RESPONSE_TIME = "least_response_time"
    IP_HASH = "ip_hash"
    RANDOM = "random"
    WEIGHTED_RANDOM = "weighted_random"


class ServerStatus(Enum):
    """服务器状态"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    MAINTENANCE = "maintenance"
    OVERLOADED = "overloaded"


@dataclass
class ServerNode:
    """服务器节点"""
    server_id: str
    host: str
    port: int
    weight: int = 1
    max_connections: int = 100
    current_connections: int = 0
    status: ServerStatus = ServerStatus.HEALTHY

    # 性能指标
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_response_time: float = 0.0
    average_response_time: float = 0.0

    # 健康检查
    last_health_check: datetime = field(default_factory=datetime.now)
    consecutive_failures: int = 0
    consecutive_successes: int = 0

    # 流量统计
    bytes_sent: int = 0
    bytes_received: int = 0

    def __post_init___(self):
    """TODO: Add function description."""
    self.url = f"http://{self.host}:{self.port}"

    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 1.0
        return self.successful_requests / self.total_requests

    @property
    def load_factor(self) -> float:
        """负载因子"""
        if self.max_connections == 0:
            return 0.0
        return self.current_connections / self.max_connections

    def update_response_time(self, response_time: float):
        """更新响应时间"""
        self.total_response_time += response_time
        self.total_requests += 1
        self.average_response_time = self.total_response_time / self.total_requests

    def record_success(self, response_time: float):
        """记录成功请求"""
        self.successful_requests += 1
        self.consecutive_failures = 0
        self.consecutive_successes += 1
        self.update_response_time(response_time)

    def record_failure(self):
        """记录失败请求"""
        self.failed_requests += 1
        self.total_requests += 1
        self.consecutive_successes = 0
        self.consecutive_failures += 1


class RequestContext(NamedTuple):
    """请求上下文"""
    request_id: str
    client_ip: str
    method: str
    path: str
    headers: Dict[str, str]
    body: Any
    timestamp: datetime


@dataclass
class BalancingMetrics:
    """负载均衡指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    requests_per_second: float = 0.0
    errors_per_second: float = 0.0

    # 服务器分布
    server_request_counts: Dict[str, int] = field(default_factory=dict)
    server_error_counts: Dict[str, int] = field(default_factory=dict)

    # 时间窗口统计
    request_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    error_times: deque = field(default_factory=lambda: deque(maxlen=1000))


class LoadBalancer:
    """负载均衡器"""

    def __init___(
        self,
        strategy: BalancingStrategy = BalancingStrategy.ROUND_ROBIN,
        health_check_interval: float = 30.0,
        health_check_timeout: float = 5.0,
        failure_threshold: int = 3,
        success_threshold: int = 2,
        max_retries: int = 2
    ):
    """TODO: Add function description."""
    self.strategy = strategy
    self.health_check_interval = health_check_interval
    self.health_check_timeout = health_check_timeout
    self.failure_threshold = failure_threshold
    self.success_threshold = success_threshold
    self.max_retries = max_retries

    # 服务器管理
    self.servers: Dict[str, ServerNode] = {}
    self.healthy_servers: List[str] = []

    # 负载均衡状态
    self.current_index = 0
    self.weighted_servers: List[str] = []
    self.server_weights: Dict[str, int] = {}

    # 连接追踪
    self.active_connections: Dict[str, set] = defaultdict(set)

    # 统计指标
    self.metrics = BalancingMetrics()

    # 并发控制
    self.lock = threading.Lock()

    # 健康检查
    self.is_running = False
    self.health_check_task: Optional[asyncio.Task] = None

    # 事件回调
    self.event_callbacks = {
        'server_added': [],
        'server_removed': [],
        'server_health_changed': [],
        'request_routed': [],
        'request_failed': []
    }

    logger.info(
        "负载均衡器初始化完成",
        strategy=strategy.value,
        health_check_interval=health_check_interval
    )

    async def start(self):
        """启动负载均衡器"""
        if self.is_running:
            logger.warning("负载均衡器已在运行")
            return

        self.is_running = True

        # 启动健康检查任务
        self.health_check_task = asyncio.create_task(self._health_check_loop())

        logger.info("负载均衡器已启动")

    async def stop(self):
        """停止负载均衡器"""
        if not self.is_running:
            return

        logger.info("正在停止负载均衡器...")

        self.is_running = False

        # 停止健康检查任务
        if self.health_check_task:
            self.health_check_task.cancel()

        logger.info("负载均衡器已停止")

    def add_server(
        self,
        server_id: str,
        host: str,
        port: int,
        weight: int = 1,
        max_connections: int = 100
    ):
        """添加服务器节点"""
        if server_id in self.servers:
            logger.warning("服务器已存在", server_id=server_id)
            return

        server = ServerNode(
            server_id=server_id,
            host=host,
            port=port,
            weight=weight,
            max_connections=max_connections
        )

        with self.lock:
            self.servers[server_id] = server
            self.healthy_servers.append(server_id)
            self.server_weights[server_id] = weight
            self._update_weighted_servers()

            # 初始化连接追踪
            self.active_connections[server_id] = set()

            # 初始化统计
            self.metrics.server_request_counts[server_id] = 0
            self.metrics.server_error_counts[server_id] = 0

        logger.info(
            "服务器已添加",
            server_id=server_id,
            host=host,
            port=port,
            weight=weight
        )

        # 触发服务器添加事件
        self._trigger_event('server_added', {
            'server_id': server_id,
            'host': host,
            'port': port,
            'weight': weight
        })

    def remove_server(self, server_id: str):
        """移除服务器节点"""
        if server_id not in self.servers:
            logger.warning("服务器不存在", server_id=server_id)
            return

        with self.lock:
            # 移除服务器
            del self.servers[server_id]

            # 从健康服务器列表移除
            if server_id in self.healthy_servers:
                self.healthy_servers.remove(server_id)

            # 清理权重信息
            if server_id in self.server_weights:
                del self.server_weights[server_id]

            self._update_weighted_servers()

            # 清理连接追踪
            if server_id in self.active_connections:
                del self.active_connections[server_id]

        logger.info("服务器已移除", server_id=server_id)

        # 触发服务器移除事件
        self._trigger_event('server_removed', {'server_id': server_id})

    def set_server_weight(self, server_id: str, weight: int):
        """设置服务器权重"""
        if server_id not in self.servers:
            logger.warning("服务器不存在", server_id=server_id)
            return

        with self.lock:
            self.servers[server_id].weight = weight
            self.server_weights[server_id] = weight
            self._update_weighted_servers()

        logger.info("服务器权重已更新", server_id=server_id, weight=weight)

    def set_server_status(self, server_id: str, status: ServerStatus):
        """设置服务器状态"""
        if server_id not in self.servers:
            logger.warning("服务器不存在", server_id=server_id)
            return

        old_status = self.servers[server_id].status

        with self.lock:
            self.servers[server_id].status = status

            # 更新健康服务器列表
            if status == ServerStatus.HEALTHY:
                if server_id not in self.healthy_servers:
                    self.healthy_servers.append(server_id)
            else:
                if server_id in self.healthy_servers:
                    self.healthy_servers.remove(server_id)

            self._update_weighted_servers()

        if old_status != status:
            logger.info(
                "服务器状态已更改",
                server_id=server_id,
                old_status=old_status.value,
                new_status=status.value
            )

            # 触发状态变化事件
            self._trigger_event('server_health_changed', {
                'server_id': server_id,
                'old_status': old_status.value,
                'new_status': status.value
            })

    async def route_request(
            self,
            request_ctx: RequestContext) -> Optional[ServerNode]:
        """路由请求到服务器"""
        start_time = time.time()

        try:
            # 选择服务器
            server = await self._select_server(request_ctx)

            if not server:
                self.metrics.failed_requests += 1
                self.metrics.error_times.append(time.time())
                logger.warning("没有可用的服务器", request_id=request_ctx.request_id)
                return None

            # 记录连接
            connection_id = f"{request_ctx.request_id}_{time.time()}"
            with self.lock:
                server.current_connections += 1
                self.active_connections[server.server_id].add(connection_id)

            # 更新统计
            self.metrics.total_requests += 1
            self.metrics.server_request_counts[server.server_id] += 1
            self.metrics.request_times.append(time.time())

            # 触发路由事件
            self._trigger_event('request_routed', {
                'request_id': request_ctx.request_id,
                'server_id': server.server_id,
                'client_ip': request_ctx.client_ip,
                'method': request_ctx.method,
                'path': request_ctx.path
            })

            logger.debug(
                "请求已路由",
                request_id=request_ctx.request_id,
                server_id=server.server_id,
                server_url=server.url
            )

            return server

        except Exception:
            self.metrics.failed_requests += 1
            self.metrics.error_times.append(time.time())

            # 触发失败事件
            self._trigger_event('request_failed', {
                'request_id': request_ctx.request_id,
                'error': str(e)
            })

            logger.error(
                "请求路由失败",
                request_id=request_ctx.request_id,
                error=str(e)
            )
            return None

    async def _select_server(
            self,
            request_ctx: RequestContext) -> Optional[ServerNode]:
        """选择服务器"""
        if not self.healthy_servers:
            return None

        if self.strategy == BalancingStrategy.ROUND_ROBIN:
            return self._select_round_robin()
        elif self.strategy == BalancingStrategy.WEIGHTED_ROUND_ROBIN:
            return self._select_weighted_round_robin()
        elif self.strategy == BalancingStrategy.LEAST_CONNECTIONS:
            return self._select_least_connections()
        elif self.strategy == BalancingStrategy.LEAST_RESPONSE_TIME:
            return self._select_least_response_time()
        elif self.strategy == BalancingStrategy.IP_HASH:
            return self._select_ip_hash(request_ctx.client_ip)
        elif self.strategy == BalancingStrategy.RANDOM:
            return self._select_random()
        elif self.strategy == BalancingStrategy.WEIGHTED_RANDOM:
            return self._select_weighted_random()
        else:
            return self._select_round_robin()

    def _select_round_robin(self) -> Optional[ServerNode]:
        """轮询策略"""
        with self.lock:
            if not self.healthy_servers:
                return None

            server_id = self.healthy_servers[self.current_index]
            self.current_index = (self.current_index +
                                  1) % len(self.healthy_servers)

            return self.servers[server_id]

    def _select_weighted_round_robin(self) -> Optional[ServerNode]:
        """加权轮询策略"""
        with self.lock:
            if not self.weighted_servers:
                return None

            server_id = self.weighted_servers[self.current_index]
            self.current_index = (self.current_index +
                                  1) % len(self.weighted_servers)

            return self.servers[server_id]

    def _select_least_connections(self) -> Optional[ServerNode]:
        """最少连接策略"""
        with self.lock:
            if not self.healthy_servers:
                return None

            min_connections = float('inf')
            selected_server = None

            for server_id in self.healthy_servers:
                server = self.servers[server_id]
                if server.current_connections < min_connections:
                    min_connections = server.current_connections
                    selected_server = server

            return selected_server

    def _select_least_response_time(self) -> Optional[ServerNode]:
        """最短响应时间策略"""
        with self.lock:
            if not self.healthy_servers:
                return None

            min_response_time = float('inf')
            selected_server = None

            for server_id in self.healthy_servers:
                server = self.servers[server_id]
                # 结合响应时间和连接数
                weighted_time = server.average_response_time * \
                    (1 + server.load_factor)

                if weighted_time < min_response_time:
                    min_response_time = weighted_time
                    selected_server = server

            return selected_server

    def _select_ip_hash(self, client_ip: str) -> Optional[ServerNode]:
        """IP哈希策略"""
        if not self.healthy_servers:
            return None

        # 使用IP地址计算哈希
        hash_value = int(hashlib.md5(client_ip.encode()).hexdigest(), 16)
        server_index = hash_value % len(self.healthy_servers)
        server_id = self.healthy_servers[server_index]

        return self.servers[server_id]

    def _select_random(self) -> Optional[ServerNode]:
        """随机策略"""
        if not self.healthy_servers:
            return None

        server_id = random.choice(self.healthy_servers)
        return self.servers[server_id]

    def _select_weighted_random(self) -> Optional[ServerNode]:
        """加权随机策略"""
        if not self.weighted_servers:
            return None

        server_id = random.choice(self.weighted_servers)
        return self.servers[server_id]

    def _update_weighted_servers(self):
        """更新加权服务器列表"""
        self.weighted_servers = []

        for server_id in self.healthy_servers:
            weight = self.server_weights.get(server_id, 1)
            # 根据权重重复添加服务器ID
            self.weighted_servers.extend([server_id] * weight)

    def release_connection(
            self,
            server_id: str,
            connection_id: str,
            response_time: float,
            success: bool = True):
        """释放连接"""
        if server_id not in self.servers:
            return

        server = self.servers[server_id]

        with self.lock:
            # 减少连接数
            if server.current_connections > 0:
                server.current_connections -= 1

            # 移除连接追踪
            self.active_connections[server_id].discard(connection_id)

            # 记录性能指标
            if success:
                server.record_success(response_time)
                self.metrics.successful_requests += 1
            else:
                server.record_failure()
                self.metrics.server_error_counts[server_id] += 1
                self.metrics.error_times.append(time.time())

            # 更新全局响应时间统计
            self._update_response_time_stats(response_time)

        logger.debug(
            "连接已释放",
            server_id=server_id,
            connection_id=connection_id,
            response_time=response_time,
            success=success
        )

    def _update_response_time_stats(self, response_time: float):
        """更新响应时间统计"""
        if self.metrics.total_requests == 1:
            self.metrics.average_response_time = response_time
            self.metrics.min_response_time = response_time
            self.metrics.max_response_time = response_time
        else:
            # 移动平均
            self.metrics.average_response_time = ((self.metrics.average_response_time * (
                self.metrics.total_requests - 1) + response_time) / self.metrics.total_requests)

            self.metrics.min_response_time = min(
                self.metrics.min_response_time, response_time)
            self.metrics.max_response_time = max(
                self.metrics.max_response_time, response_time)

    async def _health_check_loop(self):
        """健康检查循环"""
        logger.info("负载均衡器健康检查任务已启动")

        while self.is_running:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_checks()

            except Exception:
                logger.error("健康检查异常", error=str(e))

        logger.info("负载均衡器健康检查任务已停止")

    async def _perform_health_checks(self):
        """执行健康检查"""
        for server_id in list(self.servers.keys()):
            await self._check_server_health(server_id)

    async def _check_server_health(self, server_id: str):
        """检查单个服务器健康状态"""
        server = self.servers[server_id]

        try:
            # 简单的HTTP健康检查（这里使用模拟）
            # 在实际应用中，应该使用HTTP客户端检查服务器状态
            start_time = time.time()

            # 模拟健康检查
            is_healthy = await self._ping_server(server)

            response_time = time.time() - start_time
            server.last_health_check = datetime.now()

            if is_healthy:
                server.consecutive_failures = 0
                server.consecutive_successes += 1

                # 如果服务器之前不健康，检查是否需要恢复
                if (server.status != ServerStatus.HEALTHY and
                        server.consecutive_successes >= self.success_threshold):

                    self.set_server_status(server_id, ServerStatus.HEALTHY)
                    logger.info("服务器健康状态已恢复", server_id=server_id)

            else:
                server.consecutive_successes = 0
                server.consecutive_failures += 1

                # 如果连续失败次数超过阈值，标记为不健康
                if server.consecutive_failures >= self.failure_threshold:
                    self.set_server_status(server_id, ServerStatus.UNHEALTHY)
                    logger.warning(
                        "服务器健康检查失败",
                        server_id=server_id,
                        consecutive_failures=server.consecutive_failures
                    )

        except Exception:
            server.consecutive_failures += 1
            logger.error(
                "健康检查异常",
                server_id=server_id,
                error=str(e)
            )

    async def _ping_server(self, server: ServerNode) -> bool:
        """ping服务器（模拟实现）"""
        # 在实际应用中，这里应该实现真正的HTTP健康检查
        # 例如：GET /health 端点

        # 模拟健康检查逻辑
        # 基于服务器的历史表现和当前负载
        if server.load_factor > 0.9:  # 负载过高
            return random.random() > 0.3  # 30%概率失败
        elif server.success_rate < 0.8:  # 成功率过低
            return random.random() > 0.2  # 20%概率失败
        else:
            return random.random() > 0.05  # 5%概率失败

    def get_server_statistics(
            self, server_id: str) -> Optional[Dict[str, Any]]:
        """获取服务器统计信息"""
        if server_id not in self.servers:
            return None

        server = self.servers[server_id]

        return {
            'server_id': server_id,
            'host': server.host,
            'port': server.port,
            'url': server.url,
            'weight': server.weight,
            'status': server.status.value,
            'current_connections': server.current_connections,
            'max_connections': server.max_connections,
            'load_factor': server.load_factor,
            'total_requests': server.total_requests,
            'successful_requests': server.successful_requests,
            'failed_requests': server.failed_requests,
            'success_rate': server.success_rate,
            'average_response_time': server.average_response_time,
            'consecutive_failures': server.consecutive_failures,
            'consecutive_successes': server.consecutive_successes,
            'last_health_check': server.last_health_check.isoformat(),
            'bytes_sent': server.bytes_sent,
            'bytes_received': server.bytes_received
        }

    def get_balancer_statistics(self) -> Dict[str, Any]:
        """获取负载均衡器统计信息"""
        # 计算每秒请求数和错误率
        current_time = time.time()
        recent_requests = sum(
            1 for t in self.metrics.request_times if current_time - t <= 60)
        recent_errors = sum(
            1 for t in self.metrics.error_times if current_time -
            t <= 60)

        self.metrics.requests_per_second = recent_requests / 60
        self.metrics.errors_per_second = recent_errors / 60

        # 服务器统计
        server_stats = {}
        for server_id in self.servers:
            server_stats[server_id] = self.get_server_statistics(server_id)

        # 总体统计
        total_servers = len(self.servers)
        healthy_servers = len(self.healthy_servers)
        unhealthy_servers = total_servers - healthy_servers

        return {
            'strategy': self.strategy.value,
            'total_servers': total_servers,
            'healthy_servers': healthy_servers,
            'unhealthy_servers': unhealthy_servers,
            'total_requests': self.metrics.total_requests,
            'successful_requests': self.metrics.successful_requests,
            'failed_requests': self.metrics.failed_requests,
            'success_rate': (
                self.metrics.successful_requests /
                max(1, self.metrics.total_requests)
            ),
            'average_response_time': self.metrics.average_response_time,
            'min_response_time': self.metrics.min_response_time if self.metrics.min_response_time != float('inf') else 0,
            'max_response_time': self.metrics.max_response_time,
            'requests_per_second': self.metrics.requests_per_second,
            'errors_per_second': self.metrics.errors_per_second,
            'server_distribution': self.metrics.server_request_counts,
            'server_errors': self.metrics.server_error_counts,
            'servers': server_stats
        }

    def add_event_callback(self, event_type: str, callback: Callable):
        """添加事件回调"""
        if event_type in self.event_callbacks:
            self.event_callbacks[event_type].append(callback)

    def _trigger_event(self, event_type: str, data: Any):
        """触发事件回调"""
        callbacks = self.event_callbacks.get(event_type, [])
        for callback in callbacks:
            try:
                callback(data)
            except Exception:
                logger.error(
                    "事件回调异常",
                    event_type=event_type,
                    error=str(e)
                )


class AdvancedLoadBalancer(LoadBalancer):
    """高级负载均衡器"""

    def __init___(self, *args, **kwargs):
    """TODO: Add function description."""
    super().__init__(*args, **kwargs)

    # 自适应权重调整
    self.enable_adaptive_weights = True
    self.weight_adjustment_interval = 300.0  # 5分钟
    self.weight_adjustment_task: Optional[asyncio.Task] = None

    # 流量预测
    self.traffic_history: deque = deque(maxlen=288)  # 24小时，每5分钟一个点
    self.prediction_model = None

    # 断路器
    self.circuit_breakers: Dict[str, dict] = {}
    self.circuit_breaker_threshold = 0.5  # 50%错误率
    self.circuit_breaker_timeout = 60.0  # 1分钟

    async def start(self):
        """启动高级负载均衡器"""
        await super().start()

        if self.enable_adaptive_weights:
            self.weight_adjustment_task = asyncio.create_task(
                self._adaptive_weight_loop())

    async def stop(self):
        """停止高级负载均衡器"""
        if self.weight_adjustment_task:
            self.weight_adjustment_task.cancel()

        await super().stop()

    async def _adaptive_weight_loop(self):
        """自适应权重调整循环"""
        logger.info("自适应权重调整任务已启动")

        while self.is_running:
            try:
                await asyncio.sleep(self.weight_adjustment_interval)
                self._adjust_server_weights()

            except Exception:
                logger.error("权重调整异常", error=str(e))

        logger.info("自适应权重调整任务已停止")

    def _adjust_server_weights(self):
        """调整服务器权重"""
        if len(self.healthy_servers) < 2:
            return

        # 计算每个服务器的性能分数
        server_scores = {}

        for server_id in self.healthy_servers:
            server = self.servers[server_id]

            # 性能因子：响应时间、成功率、负载
            response_time_factor = 1.0 / \
                max(0.001, server.average_response_time)
            success_rate_factor = server.success_rate
            load_factor = 1.0 - server.load_factor

            # 综合分数
            score = (response_time_factor * 0.4 +
                     success_rate_factor * 0.4 +
                     load_factor * 0.2)

            server_scores[server_id] = score

        # 根据分数调整权重
        if server_scores:
            max_score = max(server_scores.values())
            min_score = min(server_scores.values())

            if max_score > min_score:
                for server_id, score in server_scores.items():
                    # 将分数标准化到1-10的权重范围
                    normalized_score = (score - min_score) / \
                        (max_score - min_score)
                    new_weight = max(1, int(normalized_score * 9 + 1))

                    old_weight = self.server_weights.get(server_id, 1)
                    if new_weight != old_weight:
                        self.set_server_weight(server_id, new_weight)
                        logger.info(
                            "服务器权重已自动调整",
                            server_id=server_id,
                            old_weight=old_weight,
                            new_weight=new_weight,
                            score=score
                        )


def create_load_balancer(
    strategy: BalancingStrategy = BalancingStrategy.ROUND_ROBIN,
    health_check_interval: float = 30.0,
    advanced: bool = False
) -> LoadBalancer:
    """创建负载均衡器实例"""
    if advanced:
        return AdvancedLoadBalancer(
            strategy=strategy,
            health_check_interval=health_check_interval
        )
    else:
        return LoadBalancer(
            strategy=strategy,
            health_check_interval=health_check_interval
        )
