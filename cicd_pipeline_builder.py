import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

import yaml

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 CI/CD 流水线构建器 (优化版)
创建完整的持续集成和持续部署流水线
"""


class CICDPipelineBuilder:
    def __init___(self, project_root: str):
    """TODO: Add function description."""
    self.project_root = Path(project_root)
    self.pipeline_files = []
    self.created_files = []

    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(
                'cicd_pipeline_build.log',
                encoding='utf-8'),
            logging.StreamHandler(),
        ],
    )
    self.logger = logging.getLogger(__name__)

    def _read_template(self, template_name: str) -> str:
        """读取模板文件内容"""
        template_path = self.project_root / "templates" / template_name
        with open(template_path, 'r', encoding='utf-8') as f:
            return f.read()

    def create_github_actions_workflow(self):
        """创建 GitHub Actions 工作流"""
        self.logger.info("🔧 创建 GitHub Actions 工作流...")

        # 创建 .github/workflows 目录
        workflows_dir = self.project_root / ".github" / "workflows"
        workflows_dir.mkdir(parents=True, exist_ok=True)

        # 主要的 CI/CD 工作流
        main_workflow = {'name': 'YS-API V3.0 CI/CD Pipeline',
                         'on': {'push': {'branches': ['main',
                                                      'develop']},
                                'pull_request': {'branches': ['main']},
                                },
                         'env': {'PYTHON_VERSION': '3.9',
                                 'NODE_VERSION': '18'},
                         'jobs': {'test': {'runs-on': 'ubuntu-latest',
                                           'steps': [{'name': 'Checkout code',
                                                      'uses': 'actions/checkout@v3'},
                                                     {'name': 'Setup Python',
                                                      'uses': 'actions/setup-python@v4',
                                                      'with': {'python-version': '${{ env.PYTHON_VERSION }}'},
                                                      },
                                                     {'name': 'Install dependencies',
                                                      'run': 'pip install -r backend/requirements.txt',
                                                      },
                                                     {'name': 'Run tests',
                                                      'run': 'cd backend && python -m pytest tests/',
                                                      },
                                                     {'name': 'Code quality check',
                                                      'run': 'cd backend && flake8 app/',
                                                      },
                                                     ],
                                           },
                                  'security-scan': {'runs-on': 'ubuntu-latest',
                                                    'steps': [{'name': 'Checkout code',
                                                               'uses': 'actions/checkout@v3'},
                                                              {'name': 'Run security scan',
                                                               'uses': 'securecodewarrior/github-action-add-sarif@v1',
                                                               'with': {'sarif-file': 'security-results.sarif'},
                                                               },
                                                              ],
                                                    },
                                  'build-and-deploy': {'needs': ['test',
                                                                 'security-scan'],
                                                       'runs-on': 'ubuntu-latest',
                                                       'if': 'github.ref == "refs/heads/main"',
                                                       'steps': [{'name': 'Checkout code',
                                                                  'uses': 'actions/checkout@v3'},
                                                                 {'name': 'Build Docker image',
                                                                  'run': 'docker build -t ys-api-v3:${{ github.sha }} .',
                                                                  },
                                                                 {'name': 'Deploy to staging',
                                                                  'run': 'echo "部署到暂存环境"'},
                                                                 ],
                                                       },
                                  },
                         }

        # 保存工作流文件
        workflow_path = workflows_dir / "main.yml"
        with open(workflow_path, 'w', encoding='utf-8') as f:
            yaml.dump(
                main_workflow,
                f,
                default_flow_style=False,
                allow_unicode=True)

        self.created_files.append(str(workflow_path))
        self.logger.info(f"✅ GitHub Actions 工作流已创建: {workflow_path}")

    def create_docker_files(self):
        """创建 Docker 相关文件"""
        self.logger.info("🐳 创建 Docker 配置文件...")

        # 读取 Dockerfile 模板
        dockerfile_content = self._read_template("dockerfile.template")
        dockerfile_path = self.project_root / "Dockerfile"
        with open(dockerfile_path, 'w', encoding='utf-8') as f:
            f.write(dockerfile_content)
        self.created_files.append(str(dockerfile_path))

        # 读取 .dockerignore 模板
        dockerignore_content = self._read_template("dockerignore.template")
        dockerignore_path = self.project_root / ".dockerignore"
        with open(dockerignore_path, 'w', encoding='utf-8') as f:
            f.write(dockerignore_content)
        self.created_files.append(str(dockerignore_path))

        # Docker Compose
        compose_content = {
            'version': '3.8',
            'services': {
                'ys-api': {
                    'build': '.',
                    'ports': ['5000:5000'],
                    'environment': {
                        'FLASK_ENV': 'production',
                        'DATABASE_URL': 'sqlite:///app/ysapi.db',
                    },
                    'volumes': [
                        './backend/ysapi.db:/app/ysapi.db',
                        './config:/app/config',
                        './logs:/app/logs',
                    ],
                    'restart': 'unless-stopped',
                    'healthcheck': {
                        'test': ['CMD', 'curl', '-f', 'http://localhost:5000/health'],
                        'interval': '30s',
                        'timeout': '10s',
                        'retries': 3,
                        'start_period': '40s',
                    },
                },
                'nginx': {
                    'image': 'nginx:alpine',
                    'ports': ['80:80', '443:443'],
                    'volumes': [
                        './nginx.conf:/etc/nginx/nginx.conf',
                        './ssl:/etc/nginx/ssl',
                    ],
                    'depends_on': ['ys-api'],
                    'restart': 'unless-stopped',
                },
            },
            'networks': {'default': {'driver': 'bridge'}},
        }

        compose_path = self.project_root / "docker-compose.yml"
        with open(compose_path, 'w', encoding='utf-8') as f:
            yaml.dump(compose_content, f, default_flow_style=False)
        self.created_files.append(str(compose_path))

        self.logger.info("✅ Docker 配置文件已创建")

    def create_jenkins_pipeline(self):
        """创建 Jenkins 流水线"""
        self.logger.info("🔨 创建 Jenkins 流水线...")

        # 读取 Jenkinsfile 模板
        jenkinsfile_content = self._read_template("jenkinsfile.template")
        jenkinsfile_path = self.project_root / "Jenkinsfile"
        with open(jenkinsfile_path, 'w', encoding='utf-8') as f:
            f.write(jenkinsfile_content)

        self.created_files.append(str(jenkinsfile_path))
        self.logger.info("✅ Jenkins 流水线已创建")

    def create_deployment_scripts(self):
        """创建部署脚本"""
        self.logger.info("📜 创建部署脚本...")

        # 创建 scripts 目录
        scripts_dir = self.project_root / "scripts"
        scripts_dir.mkdir(exist_ok=True)

        # Linux/Mac 部署脚本
        deploy_content = self._read_template("deploy.template")
        deploy_sh_path = scripts_dir / "deploy.sh"
        with open(deploy_sh_path, 'w', encoding='utf-8') as f:
            f.write(deploy_content)

        # 设置执行权限
        try:
            deploy_sh_path.chmod(0o755)
        except Exception:
            pass

        self.created_files.append(str(deploy_sh_path))

        # Windows 部署脚本
        deploy_windows_content = self._read_template("deploy_windows.template")
        deploy_bat_path = scripts_dir / "deploy.bat"
        with open(deploy_bat_path, 'w', encoding='utf-8') as f:
            f.write(deploy_windows_content)
        self.created_files.append(str(deploy_bat_path))

        # 回滚脚本
        rollback_content = self._read_template("rollback.template")
        rollback_sh_path = scripts_dir / "rollback.sh"
        with open(rollback_sh_path, 'w', encoding='utf-8') as f:
            f.write(rollback_content)

        try:
            rollback_sh_path.chmod(0o755)
        except Exception:
            pass

        self.created_files.append(str(rollback_sh_path))

        self.logger.info("✅ 部署脚本已创建")

    def create_monitoring_config(self):
        """创建监控配置"""
        self.logger.info("📊 创建监控配置...")

        # 创建 monitoring 目录
        monitoring_dir = self.project_root / "monitoring"
        monitoring_dir.mkdir(exist_ok=True)

        # Prometheus 配置
        prometheus_config = {
            'global': {'scrape_interval': '15s', 'evaluation_interval': '15s'},
            'rule_files': [],
            'scrape_configs': [
                {
                    'job_name': 'ys-api',
                    'static_configs': [{'targets': ['localhost:5000']}],
                    'metrics_path': '/metrics',
                    'scrape_interval': '5s',
                }
            ],
        }

        prometheus_path = monitoring_dir / "prometheus.yml"
        with open(prometheus_path, 'w', encoding='utf-8') as f:
            yaml.dump(prometheus_config, f, default_flow_style=False)
        self.created_files.append(str(prometheus_path))

        # Grafana 仪表板配置
        dashboard_config = {'dashboard': {'id': None,
                                          'title': 'YS-API V3.0 监控仪表板',
                                          'tags': ['ys-api',
                                                   'python',
                                                   'flask'],
                                          'timezone': 'browser',
                                          'refresh': '5s',
                                          'panels': [{'id': 1,
                                                      'title': 'API 请求速率',
                                                      'type': 'graph',
                                                      'targets': [{'expr': 'rate(flask_http_request_total[1m])',
                                                                   'legendFormat': '请求/秒',
                                                                   }],
                                                      },
                                                     {'id': 2,
                                                      'title': '响应时间',
                                                      'type': 'graph',
                                                      'targets': [{'expr': 'flask_http_request_duration_seconds',
                                                                   'legendFormat': '响应时间',
                                                                   }],
                                                      },
                                                     {'id': 3,
                                                      'title': '错误率',
                                                      'type': 'stat',
                                                      'targets': [{'expr': 'rate(flask_http_request_exceptions_total[1m])',
                                                                   'legendFormat': '错误率',
                                                                   }],
                                                      },
                                                     ],
                                          'time': {'from': 'now-1h',
                                                   'to': 'now'},
                                          }}

        dashboard_path = monitoring_dir / "dashboard.json"
        with open(dashboard_path, 'w', encoding='utf-8') as f:
            json.dump(dashboard_config, f, indent=2, ensure_ascii=False)
        self.created_files.append(str(dashboard_path))

        # Docker Compose 监控服务
        monitoring_compose = {
            'version': '3.8',
            'services': {
                'prometheus': {
                    'image': 'prom/prometheus:latest',
                    'ports': ['9090:9090'],
                    'volumes': [
                        './monitoring/prometheus.yml:/etc/prometheus/prometheus.yml'
                    ],
                    'command': [
                        '--config.file=/etc/prometheus/prometheus.yml',
                        '--storage.tsdb.path=/prometheus',
                        '--web.console.libraries=/etc/prometheus/console_libraries',
                        '--web.console.templates=/etc/prometheus/consoles',
                        '--web.enable-lifecycle',
                    ],
                },
                'grafana': {
                    'image': 'grafana/grafana:latest',
                    'ports': ['3000:3000'],
                    'environment': {'GF_SECURITY_ADMIN_PASSWORD': 'admin123'},
                    'volumes': ['grafana-storage:/var/lib/grafana'],
                },
            },
            'volumes': {'grafana-storage': {}},
        }

        monitoring_compose_path = monitoring_dir / "docker-compose.monitoring.yml"
        with open(monitoring_compose_path, 'w', encoding='utf-8') as f:
            yaml.dump(monitoring_compose, f, default_flow_style=False)
        self.created_files.append(str(monitoring_compose_path))

        self.logger.info("✅ 监控配置已创建")

    def create_documentation(self):
        """创建 CI/CD 文档"""
        self.logger.info("📚 创建 CI/CD 文档...")

        # 创建docs目录
        docs_dir = self.project_root / "docs"
        docs_dir.mkdir(exist_ok=True)

        cicd_doc_content = f'''# YS-API V3.0 CI/CD 流水线文档

## 概述

本文档描述了 YS-API V3.0 项目的完整 CI/CD 流水线配置和使用方法。

## 流水线组件

### 1. GitHub Actions

- **文件位置**: `.github/workflows/main.yml`
- **触发条件**:
  - 推送到 `main` 或 `develop` 分支
  - 向 `main` 分支创建 Pull Request
- **流水线步骤**:
  1. 代码检出
  2. Python 环境设置
  3. 依赖安装
  4. 单元测试
  5. 代码质量检查
  6. 安全扫描
  7. Docker 镜像构建
  8. 部署到暂存环境

### 2. Jenkins 流水线

- **文件位置**: `Jenkinsfile`
- **功能**: 企业级 CI/CD 流水线
- **阶段**:
  - 代码检出
  - 环境准备
  - 代码质量检查（并行执行）
  - 测试
  - Docker 镜像构建
  - 生产环境部署

### 3. Docker 容器化

- **Dockerfile**: 生产环境镜像配置
- **docker-compose.yml**: 多服务编排
- **特性**:
  - 多阶段构建
  - 安全用户权限
  - 健康检查
  - 日志管理

## 部署脚本

### Linux/Mac 部署

```bash
# 执行部署
./scripts/deploy.sh

# 回滚到上一版本
./scripts/rollback.sh
```

### Windows 部署

```cmd
REM 执行部署
scripts\\deploy.bat
```

## 监控配置

### Prometheus 监控

- **配置文件**: `monitoring/prometheus.yml`
- **访问地址**: http://localhost:9090
- **监控指标**:
  - API 请求速率
  - 响应时间
  - 错误率
  - 系统资源使用情况

### Grafana 仪表板

- **访问地址**: http://localhost:3000
- **默认账号**: admin / admin123
- **仪表板**: YS-API V3.0 监控仪表板

### 启动监控服务

```bash
# 启动 Prometheus 和 Grafana
docker-compose -f monitoring/docker-compose.monitoring.yml up -d
```

## 环境配置

### 开发环境

1. 安装依赖
```bash
cd backend
pip install -r requirements.txt
```

2. 启动开发服务器
```bash
python start_server.py
```

### 生产环境

1. 使用 Docker Compose
```bash
docker-compose up -d
```

2. 验证部署
```bash
curl http://localhost:5000/health
```

## 质量门禁

### 代码质量检查

- **工具**: flake8
- **配置**: `.flake8`
- **标准**: PEP 8

### 安全扫描

- **工具**: bandit, safety
- **范围**: Python 代码安全漏洞

### 测试覆盖率

- **工具**: pytest + coverage
- **要求**: 覆盖率 > 80%

## 故障排除

### 常见问题

1. **Docker 镜像构建失败**
   - 检查 Dockerfile 语法
   - 确认依赖文件存在
   - 查看构建日志

2. **服务启动失败**
   - 检查端口占用
   - 查看容器日志
   - 验证配置文件

3. **健康检查失败**
   - 确认服务已完全启动
   - 检查网络连接
   - 验证健康检查端点

### 日志查看

```bash
# 查看应用日志
docker-compose logs ys-api

# 查看所有服务日志
docker-compose logs

# 实时跟踪日志
docker-compose logs -f
```

## 维护指南

### 定期任务

1. **清理旧镜像**
```bash
docker image prune -a
```

2. **备份数据库**
```bash
cp backend/ysapi.db backup/ysapi_$(date +%Y%m%d).db
```

3. **更新依赖**
```bash
pip list --outdated
pip install --upgrade package_name
```

## 安全最佳实践

1. **容器安全**
   - 使用非 root 用户
   - 最小化镜像大小
   - 定期更新基础镜像

2. **网络安全**
   - 使用 HTTPS
   - 配置防火墙
   - 限制端口暴露

3. **数据安全**
   - 加密敏感数据
   - 定期备份
   - 访问控制

## 更新记录

- **{datetime.now().strftime("%Y-%m-%d")}**: 初始版本创建
- **最后更新**: {datetime.now().strftime("%Y-%m-%d")}
'''

        cicd_doc_path = docs_dir / "07-CICD流水线文档.md"
        with open(cicd_doc_path, 'w', encoding='utf-8') as f:
            f.write(cicd_doc_content)

        self.created_files.append(str(cicd_doc_path))
        self.logger.info("✅ CI/CD 文档已创建")

    def build_pipeline(self):
        """构建完整的 CI/CD 流水线"""
        self.logger.info("🚀 开始构建 YS-API V3.0 CI/CD 流水线...")

        try:
            # 创建各个组件
            self.create_github_actions_workflow()
            self.create_docker_files()
            self.create_jenkins_pipeline()
            self.create_deployment_scripts()
            self.create_monitoring_config()
            self.create_documentation()

            # 生成总结报告
            self.generate_summary_report()

            self.logger.info("✅ CI/CD 流水线构建完成！")
            return True

        except Exception:
            self.logger.error(f"❌ CI/CD 流水线构建失败: {str(e)}")
            return False

    def generate_summary_report(self):
        """生成构建总结报告"""
        report_content = f'''# YS-API V3.0 CI/CD 流水线构建报告

**构建时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 📁 创建的文件

'''

        for i, file_path in enumerate(self.created_files, 1):
            relative_path = Path(file_path).relative_to(self.project_root)
            report_content += f"{i}. `{relative_path}`\\n"

        report_content += f'''

## 🔧 流水线组件

### ✅ GitHub Actions
- 工作流文件: `.github/workflows/main.yml`
- 触发条件: 推送到 main/develop 分支，PR 到 main 分支
- 包含: 测试、质量检查、安全扫描、构建、部署

### ✅ Jenkins 流水线
- 流水线文件: `Jenkinsfile`
- 企业级 CI/CD 支持
- 并行执行质量检查和安全扫描

### ✅ Docker 容器化
- 生产级 Dockerfile
- Docker Compose 编排
- 健康检查和日志管理

### ✅ 部署脚本
- Linux/Mac: `scripts/deploy.sh`
- Windows: `scripts/deploy.bat`
- 回滚脚本: `scripts/rollback.sh`

### ✅ 监控配置
- Prometheus 指标收集
- Grafana 可视化仪表板
- 自定义监控指标

### ✅ 文档
- 完整的 CI/CD 使用文档
- 故障排除指南
- 最佳实践说明

## 🚀 快速开始

### 1. 本地开发
```bash
cd backend
pip install -r requirements.txt
python start_server.py
```

### 2. Docker 部署
```bash
# Linux/Mac
./scripts/deploy.sh

# Windows
scripts\\deploy.bat
```

### 3. 启动监控
```bash
docker-compose -f monitoring/docker-compose.monitoring.yml up -d
```

## 📊 访问地址

- **应用服务**: http://localhost:5000
- **前端界面**: http://localhost:5000/static/index.html
- **健康检查**: http://localhost:5000/health
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin123)

## ✅ 完成状态

- [x] GitHub Actions 工作流
- [x] Jenkins 流水线
- [x] Docker 容器化
- [x] 部署脚本
- [x] 监控配置
- [x] 文档说明

**总计创建文件数**: {len(self.created_files)}

---

🎉 **恭喜！YS-API V3.0 CI/CD 流水线已成功构建完成！**

现在您可以享受自动化的代码集成、测试、构建和部署流程。
'''

        report_path = self.project_root / "CICD_构建报告.md"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        self.created_files.append(str(report_path))
        self.logger.info(f"📋 构建报告已生成: {report_path}")


# 主程序入口
if __name__ == "__main__":

    # 获取项目根目录
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.getcwd()

    # 创建 CI/CD 流水线构建器
    builder = CICDPipelineBuilder(project_root)

    # 构建流水线
    success = builder.build_pipeline()

    if success:
        builder.logger.info("🎉 CI/CD 流水线构建成功！")
        builder.logger.info(f"📁 总共创建了 {len(builder.created_files)} 个文件")
        builder.logger.info("📋 创建的文件列表:")
        for i, file_path in enumerate(builder.created_files, 1):
            relative_path = Path(file_path).relative_to(Path(project_root))
            builder.logger.info(f"  {i}. {relative_path}")

        builder.logger.info("🚀 快速开始:")
        builder.logger.info("  1. 本地开发: cd backend && python start_server.py")
        builder.logger.info(
            "  2. Docker 部署: ./scripts/deploy.sh (Linux/Mac) 或 scripts\\deploy.bat (Windows)"
        )
        builder.logger.info(
            "  3. 启动监控: docker-compose -f monitoring/docker-compose.monitoring.yml up -d"
        )
        builder.logger.info("📖 详细文档请查看: docs/07-CICD流水线文档.md")
    else:
        builder.logger.error("❌ CI/CD 流水线构建失败！")
        builder.logger.error("请查看日志文件: cicd_pipeline_build.log")
        sys.exit(1)
