生产订单列表查询
发布时间:2025-04-17 16:27:36
可以根据输入的订单ID信息，查询生产订单的详细信息，包括订单、产品、材料和工艺等内容。

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	开放API: 动态域名，获取方式详见 获取租户所在数据中心域名
集成API: 详细域名信息，请见 连接配置
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/mfg/productionorder/list
请求方式	POST
ContentType	application/json
应用场景	开放API/集成API
API类别	列表查询
事务和幂等性	无
限流次数	60次/分钟

用户身份	支持传递普通用户身份，详细说明见开放平台用户认证接入规范
多语	不支持
2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
pageIndex	int	否	否	页号 默认值:1    默认值: 1
pageSize	int	否	否	每页行数 默认值:10    默认值: 10
id	long	是	否	生产订单
code	string	否	否	生产订单号
status	string	否	否	订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工
transTypeId	string	是	否	交易类型
orgId	string	是	否	工厂
productionDepartmentId	string	是	否	部门
OrderProduct!productId	long	是	否	物料id,当物料id和物料编码同时填写时,取交集
OrderProduct!startDate	string	否	否	开工日期（区间，格式2021-03-02|2021-03-02 23:59:59）    示例: 2021-03-02|2021-03-02 23:59:59
OrderProduct!finishDate	string	否	否	完工日期（区间，格式2021-03-02|2021-03-02 23:59:59）    示例: 2021-03-02|2021-03-02 23:59:59
createTime	string	否	否	创建时间（区间，格式2021-03-02|2021-03-02 23:59:59）    示例: 2021-03-02|2021-03-02 23:59:59
vouchdate	string	否	否	单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）    示例: 2021-03-02|2021-03-02 23:59:59
isShowProcess	int	否	否	是否展示工序，0-不展示；1-全部工序，2-全部工序（含执行信息）；3-未执行完工序    示例: 0    默认值: 0
isShowMaterial	boolean	否	否	是否展示材料:true-是,false-否    默认值: false
isShowByProduct	boolean	否	否	是否展示联副产品:true-是,false-否    默认值: false
isShowActivity	boolean	否	否	是否展示作业:true-是,false-否    默认值: false
OrderProduct!completedFlag	string	否	否	启用完工报告：false-否，true-是
simple	object	否	否	simple
orderProduct.productId.code	string	是	否	物料编码,当物料id和物料编码同时填写时,取交集
open_pubts_begin	string	否	否	时间戳，开始时间    示例: 2022-04-01 00:00:00
open_pubts_end	string	否	否	时间戳，结束时间    示例: 2022-04-20 00:00:00
orderProduct.materialApplyStatus	string	否	否	领料申请状态：0-未申领，1-部分申领，2-全部申领    示例: 0
orderProduct.materialStatus	string	否	否	领料状态：0-未领料，1-部分领用，2-全部领用    示例: 1
orderProduct.finishedWorkApplyStatus	string	否	否	完工申报状态：0-未申报，1-部分申报，2-全部申报    示例: 0
orderProduct.stockStatus	string	否	否	入库状态：0-未入库，1-部分入库，2-全部入库    示例: 2
open_auditTime_begin	DateTime	否	否	审核时间,开始时间    示例: 2023-02-21 11:22:51
open_auditTime_end	DateTime	否	否	审核时间,结束时间    示例: 2023-02-21 11:22:55
open_auditDate_begin	Date	否	否	审核日期,开始日期    示例: 2023-02-21
open_auditDate_end	Date	否	否	审核日期,结束日期    示例: 2023-02-22
orderProduct.retMaterialApplyFlag	short	否	否	退料申请标识，0-否，1-是    示例: 0
simpleVOs	object	是	否	扩展查询条件
field	string	否	否	属性名(条件)
op	string	否	否	比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )
value1	string	否	否	查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)
value2	string	否	否	查询条件值2
logicOp	string	否	否	逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or    示例: and
conditions	object	是	否	下级查询条件
field	string	否	否	属性名(条件)
op	string	否	否	逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or
value1	string	否	否	查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)
value2	string	否	否	查询条件值2
3. 请求示例
Url: /yonbip/mfg/productionorder/list?access_token=访问令牌
Body: {
	"pageIndex": 0,
	"pageSize": 0,
	"id": [
		0
	],
	"code": "",
	"status": "",
	"transTypeId": [
		""
	],
	"orgId": [
		""
	],
	"productionDepartmentId": [
		""
	],
	"OrderProduct!productId": [
		0
	],
	"OrderProduct!startDate": "2021-03-02|2021-03-02 23:59:59",
	"OrderProduct!finishDate": "2021-03-02|2021-03-02 23:59:59",
	"createTime": "2021-03-02|2021-03-02 23:59:59",
	"vouchdate": "2021-03-02|2021-03-02 23:59:59",
	"isShowProcess": 0,
	"isShowMaterial": true,
	"isShowByProduct": true,
	"isShowActivity": true,
	"OrderProduct!completedFlag": "",
	"simple": {
		"orderProduct.productId.code": [
			""
		],
		"open_pubts_begin": "2022-04-01 00:00:00",
		"open_pubts_end": "2022-04-20 00:00:00",
		"orderProduct.materialApplyStatus": "0",
		"orderProduct.materialStatus": "1",
		"orderProduct.finishedWorkApplyStatus": "0",
		"orderProduct.stockStatus": "2",
		"open_auditTime_begin": "2023-02-21 11:22:51",
		"open_auditTime_end": "2023-02-21 11:22:55",
		"open_auditDate_begin": "2023-02-21",
		"open_auditDate_end": "2023-02-22",
		"orderProduct.retMaterialApplyFlag": 0
	},
	"simpleVOs": [
		{
			"field": "",
			"op": "",
			"value1": "",
			"value2": "",
			"logicOp": "and",
			"conditions": [
				{
					"field": "",
					"op": "",
					"value1": "",
					"value2": ""
				}
			]
		}
	]
}
4. 返回值参数
名称	类型	数组	描述
code	string	否	返回码，成功时返回200
message	string	否	调用失败时的错误信息
data	object	否	调用成功时的返回数据
pageIndex	long	否	当前页
pageSize	long	否	页大小
recordCount	long	否	记录总数
recordList	object	是	返回数据对象
OrderProduct_materialName	string	否	物料名称
OrderProduct_startDate	string	否	开工日期
productDefineDts	特征组
po.order.OrderProduct	否	表体自定义特征组
WW	Date	否	委外交货日期
XS11	string	否	需求分类号test
XS15	string	否	顾客订单号（订单表体）
id	string	否	特征id,主键,新增时无需填写,修改时必填
freeCharacteristics	特征组
po.order.OrderProduct	否	自由项特征组
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
defineDts	特征组
po.order.Order	否	表头自定义特征组
U9003	string	否	U9生产订单号
id	string	否	特征id,主键,新增时无需填写,修改时必填
OrderProduct_lineNo	long	否	行号
productUnitPrecision	long	否	生产单位精度
OrderProduct_scrap	long	否	废品率%
OrderProduct_orgId	string	否	库存组织id
OrderProduct_skuCode	string	否	物料SKU编码
transTypeId	string	否	交易类型ID
mainUnitPrecision	long	否	主计量精度
id	long	否	生产订单Id
OrderProduct_sourceType	string	否	来源单据类型：1-无来源，2-计划订单，3-销售订单，4-生产订单，5-完工报告，18-项目物资清单
OrderProduct_productId	long	否	物料Id
OrderProduct_mrpQuantity	long	否	净算量
OrderProduct_changeType	long	否	换算方式：0-固定换算，1-浮动换算
departmentName	string	否	生产部门
orgName	string	否	工厂
auditTime	DateTime	否	审核时间
auditDate	Date	否	审核日期
isWfControlled	boolean	否	是否审批流控制：false-否，true-是
OrderProduct_quantity	long	否	生产数量
OrderProduct_completedQuantity	double	否	已完工数量
OrderProduct_incomingQuantity	double	否	累计入库数量
isHold	boolean	否	挂起状态：false-否，true-是
OrderProduct_skuName	string	否	物料SKU名称
routingVersion	string	否	工艺路线版本
routingCode	string	否	工艺路线编码
routingId	long	否	工艺路线Id
OrderProduct_completedFlag	boolean	否	启用完工报告：false-否，true-是
OrderProduct_materialCode	string	否	物料编码
OrderProduct_productionUnitId	long	否	生产单位ID
status	long	否	订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工
returncount	long	否	退回次数
routingName	string	否	工艺路线名称
verifystate	long	否	审批状态：0-开立，1-已提交，2-已审批，-1-驳回
code	string	否	生产订单号
creatorId	long	否	创建人Id
orderProduct_id	long	否	订单产品行Id
orgId	string	否	工厂Id
vouchdate	string	否	单据日期
OrderProduct_auxiliaryQuantity	long	否	生产件数
OrderProduct_materialApplyFlag	boolean	否	启用领料申请：false-否，true-是
OrderProduct_mainUnit	long	否	主计量Id
OrderProduct_mainUnitTruncationType	long	否	主计量舍位方式: 0-入位，1-舍位，4-四舍五入
transTypeName	string	否	交易类型名称
pubts	string	否	时间戳
OrderProduct_skuId	long	否	物料SKUId
OrderProduct_productUnitTruncationType	long	否	生产单位舍位方式: 0-入位，1-舍位，4-四舍五入
entrustProcessType	short	否	受托加工方式:2-全程受托加工方式;3-工序受托加工方式
OrderProduct_retMaterialApplyFlag	short	否	启用退料申请：0-否,1-是
OrderProduct_orgName	string	否	库存组织
creator	string	否	创建人
OrderProduct_finishDate	string	否	完工日期
OrderProduct_changeRate	long	否	换算率
OrderProduct_isHold	boolean	否	挂起状态：false-否，true-是
entrustCustomer	long	否	受托客户id
OrderProduct_versionCode	string	否	BOM版本
OrderProduct_bomId	long	否	物料清单Id
OrderProduct_productUnitName	string	否	生产单位
OrderProduct_mainUnitName	string	否	主计量
OrderProduct_materialApplyStatus	short	否	领料申请状态：0-未申领，1-部分申领，2-全部申领
OrderProduct_materialStatus	short	否	领料状态：0-未领料，1-部分领用，2-全部领用
entrustCustomerName	string	否	受托客户
OrderProduct_finishedWorkApplyStatus	short	否	完工申报状态：0-未申报，1-部分申报，2-全部申报
OrderProduct_stockStatus	short	否	入库状态：0-未入库，1-部分入库，2-全部入库
createTime	string	否	创建时间
productionDepartmentId	string	否	生产部门Id
offChartReceiptIsAllowed	boolean	否	允许表外产出:false-否，true-是
apsLock	int	否	排程状态:0-未锁定,1-已锁定
dailyschQuantity	BigDecimal	否	排产数量
dailyschStatus	short	否	排产状态：0-未排产，1-部分排产，2-已排产
dailyschConquantity	BigDecimal	否	排产确认数量
transTypeCode	string	否	交易类型编码
orderMaterial	object	是	材料信息
materialDefineDts	特征组
po.order.OrderMaterial	否	自定义特征组
XS11	string	否	需求分类号test
id	string	否	特征id,主键,新增时无需填写,修改时必填
freeCharacteristics	特征组
po.order.OrderMaterial	否	自由项特征组
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
isWholeSet	boolean	否	齐套标识：false-否，true-是
receivedQuantity	BigDecimal	否	已领数量
recipientQuantity	BigDecimal	否	应领数量
numeratorQuantity	BigDecimal	否	分子用量
stockUnitPrecision	int	否	库存单位精度
mainUnitTruncationType	int	否	主计量舍位方式：0-入位，1-舍位，4-四舍五入
stockUnitName	string	否	库存单位
unitUseQuantity	BigDecimal	否	单位使用数量
auxiliaryReceivedQuantity	BigDecimal	否	已领件数
auxiliaryRecipientQuantity	BigDecimal	否	应领件数
orgId	string	否	库存组织id
stockUnitTruncationType	int	否	库存单位舍位方式：0-入位，1-舍位，4-四舍五入
scrap	BigDecimal	否	废品率%
lineNo	BigDecimal	否	行号
supplyType	string	否	发料方式：0-领用，1-入库倒冲，2-不发料
mainUnitPrecision	BigDecimal	否	主计量精度
truncUp	short	否	向上取整：0-否，1-是
substituteFlag	int	否	BOM替代标识
id	long	否	订单材料Id
changeRate	BigDecimal	否	换算率
pubts	string	否	时间戳
denominatorQuantity	BigDecimal	否	分母用量
bomId	long	否	物料清单Id
mainUnit	long	否	主计量Id
fixedQuantity	BigDecimal	否	固定用量：0-否，1-是
orgName	string	否	库存组织
productName	string	否	物料名称
productCode	string	否	物料编码
productId	long	否	物料id
changeType	int	否	换算方式：0-固定换算，1-浮动换算
orderProductId	long	否	订单成产品Id
bomMaterialId	long	否	物料清单子件Id
mainUnitName	string	否	主计量
materialName	string	否	物料名称
requirementDate	string	否	需求日期
skuCode	string	否	物料SKU编码
stockUnitId	long	否	库存单位Id
mustLossQuantity	BigDecimal	否	固定损耗
calcCostFlag	short	否	计算成本；0-否，1-是
orderMaterialExpinfo!id	long	否	材料信息扩展信息id
excessAppliedQty	BigDecimal	否	超额申请数量
auxiliaryExcessAppliedQty	BigDecimal	否	超额申请件数
excessRecipientQty	BigDecimal	否	已超领数量
auxiliaryExcessRecipientQty	BigDecimal	否	已超领件数
orderMaterialExpinfo!excessQty	BigDecimal	否	可超额数量
orderMaterialExpinfo!auxiliaryExcessQty	BigDecimal	否	可超额件数
orderMaterialExpinfo!isExcess	boolean	否	超额标识
orderMaterialExpinfo!excessType	short	否	超额类型：1 比例，2 数量，3 不控制
orderMaterialExpinfo!excessRate	BigDecimal	否	超额比例(%)
orderMaterialExpinfo!fixedExcessQty	BigDecimal	否	固定超额量
orderMaterialExpinfo!designator	string	否	位置号
orderMaterialExpinfo!wholePoint	string	否	齐套检查点：1 订单完工，2订单入库，3订单开工，4工序完工
appliedRetQuantity	BigDecimal	否	退料申请数量
auxiliaryAppliedRetQuantity	BigDecimal	否	退料申请件数
appliedRetRestQuantity	BigDecimal	否	退料申请未退库数量
auxiliaryAppliedRetRestQuantity	BigDecimal	否	退料申请未退库件数
excessAppliedRetQty	BigDecimal	否	超额退料申请数量
auxiliaryExcessAppliedRetQty	BigDecimal	否	超额退料申请件数
excessAppliedRetRestQty	BigDecimal	否	超额退料申请未退库数量
auxiliaryExcessAppliedRetRestQty	BigDecimal	否	超额退料申请未退库件数
appliedRestQuantity	BigDecimal	否	领料申请未出库数量
auxiliaryAppliedRestQuantity	BigDecimal	否	领料申请未出库件数
excessAppliedRestQty	BigDecimal	否	超额领料申请未出库数量
auxiliaryExcessAppliedRestQty	BigDecimal	否	超额领料申请未出库件数
projectId	string	否	项目Id
projectCode	string	否	项目编码
projectName	string	否	项目名称
wbs	string	否	wbs
wbsCode	string	否	WBS任务编码
wbsName	string	否	WBS任务名称
activity	long	否	活动
activityCode	string	否	活动编码
activityName	string	否	活动名称
cfmReceivedQty	number
小数位数:8,最大长度:28	否	确认已领数量
cfmAuxReceivedQty	number
小数位数:8,最大长度:28	否	确认已领件数
cfmExcessRecipientQty	number
小数位数:8,最大长度:28	否	确认已超领数量
cfmExcessAuxQty	number
小数位数:8,最大长度:28	否	确认已超领件数
cfmReceivedKit	number
小数位数:8,最大长度:28	否	确认领料套数
orderActivity	object	是	作业信息
id	long	否	作业ID
lineNum	double	否	行号
orderId	long	否	生产订单ID
orderProductId	long	否	产品行ID
activityId	long	否	作业标准ID
activityType	long	否	作业类型ID
activityTypeCode	string	否	作业类别编码
activityTypeName	string	否	作业类型
orderProcessId	long	否	工序ID
opSn	double	否	工序顺序号
activityClass	int	否	作业类别；0-人工，1-设备，2-委外，3-空闲，4-其他
workCenterId	long	否	工作中心ID
workCenterCode	string	否	工作中心编码
workCenterName	string	否	工作中心名称
operationId	long	否	标准工序ID
operationCode	string	否	工序编码
operationName	string	否	工序名称
activityUnit	long	否	数量单位ID
activityUnitName	string	否	数量单位
activityUnitPrecision	int	否	数量单位精度
activityUnitTruncationType	int	否	数量单位舍入方式；同BigDecimal舍入方式
usageUnit	long	否	计量单位ID
usageUnitName	string	否	计量单位
usageUnitPrecision	int	否	计量单位精度
usageUnitTruncationType	int	否	计量单位舍入方式；同BigDecimal舍入方式
stdUsageQty	double	否	额定总用量
planUsageQty	double	否	计划总用量
denominatorQuantity	double	否	母件底数
usageQty	double	否	标准作业量
isCalcCost	boolean	否	计算成本
activityQty	double	否	数量
usageBasis	int	否	计量基础;0-物料，1-批次
isAutoCreate	int	否	自动创建；0-否，1-是
pubts	string	否	时间戳
orderByProduct	object	是	联副产品信息
id	long	否	联副产品ID
orderProductLineNo	long	否	行号
manufacturingSpecification	string	否	物料规格
numeratorQuantity	double	否	分子用量
productionType	string	否	产出类型；1-联产品，2-副产品
productUnitPrecision	int	否	生产单位精度
mainUnitTruncationType	int	否	主计量舍位方式；同BigDecimal的舍入方式
warehouseName	string	否	预入仓库
unitUseQuantity	double	否	单位产出数量
orgId	string	否	库存组织id
orgName	string	否	库存组织
productName	string	否	物料名称
productCode	string	否	物料编码
productId	long	否	物料id
freeCharacteristics	特征组
po.order.OrderByProduct	否	自由项特征组
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
byProductDefineDts	特征组
po.order.OrderByProduct	否	自定义项特征组
id	string	否	特征id,主键,新增时无需填写,修改时必填
lineNo	double	否	行号
productionDate	string	否	产出日期
isBatchManage	boolean	否	是否批次管理
isExpiryDateManage	boolean	否	是否效期管理
mainUnitPrecision	int	否	主计量精度
changeRate	double	否	换算率
pubts	string	否	时间戳
denominatorQuantity	double	否	分母用量
mainUnit	long	否	主计量ID
quantity	double	否	产出数量
changeType	int	否	换算方式；0-固定换算，1-浮动换算
productUnitTruncationType	int	否	库存单位舍位方式；同BigDecimal的舍入方式
orderProductId	long	否	订单成产品id
productionUnitId	long	否	生产单位ID
mainUnitName	string	否	主计量
materialName	string	否	物料名称
warehouseId	long	否	预入仓库Id
offChartReceipt	boolean	否	表外产出:false-否，true-是
productUnitName	string	否	生产单位
cfmIncomingQty	number
小数位数:8,最大长度:28	否	确认累计入库数量
cfmIncomingAuxQty	number
小数位数:8,最大长度:28	否	确认累计入库件数
cfmScrapStockQty	number
小数位数:8,最大长度:28	否	确认报废入库数量
cfmScrapStockAuxQty	number
小数位数:8,最大长度:28	否	确认报废入库件数
orderProcess	object	是	工序信息
id	long	否	工序ID
processDefineDts	特征组
po.order.OrderProcess	否	自定义项特征组
id	string	否	特征id,主键,新增时无需填写,修改时必填
orderId	long	否	订单ID
orderProductId	long	否	生产订单行ID
operationControlId	long	否	工序控制码ID
operationCode	string	否	工序编码
productUnitPrecision	int	否	生产单位精度
nextId	long	否	后序ID
doScheduling	int	否	参与调度；0-否，1-是
transferProcplanProdQty	double	否	转工序作业计划件数
finishGoodsId	long	否	完工库位id
finishWarehouseId	long	否	完工仓库id
preSn	double	否	前序顺序号
transferProcplanQty	double	否	转工序作业计划数量
workCenterId	long	否	工作中心ID
mainUnitPrecision	int	否	主计量单位精度
executeOrgName	string	否	执行组织
outUnitId	long	否	产出单位ID
outUnitTruncationType	int	否	产出单位舍位方式；同BigDecimal的舍入方式
mainUnitId	long	否	主计量单位ID
checkType	int	否	质检方式；0-自检，1-车间检验
orgName	string	否	组织
routingOperationId	long	否	工艺路线行ID
mainUnitName	string	否	主计量单位
prepareTime	double	否	计划准备时间
routingOperationProcessTime	int	否	单批加工时间
qty	double	否	计划生产数量
occupyProduction	int	否	占用产能；0-否，1-是
computingCosts	int	否	计算成本；0-否，1-是
mainChangeRate	double	否	生产-主计量换算率
immediateHandover	int	否	即时交接；0-否，1-是
operationIdRouteDesc	string	否	工艺描述
outUnitName	string	否	产出单位
operationControlName	string	否	工序控制码名称
outChangeRate	double	否	产出-主计量换算率
mainUnitTruncationType	int	否	主计量舍位方式；同BigDecimal的舍入方式
orgId	string	否	组织ID
processTime	double	否	计划加工时间
procPlanCreate	long	否	工序作业计划创建；0-否，1-是
nextSn	double	否	后序顺序号
prodQty	double	否	计划生产件数
operationId	long	否	工序ID
processType	int	否	加工类型；0-正常加工，1-返工生产，2-报废补投
sn	double	否	顺序号
pubts	string	否	时间戳
planStartDate	string	否	计划开工时间
planEndDate	string	否	计划完工时间
workCenterName	string	否	工作中心名称
timeUnit	int	否	时间单位；0-天，1-小时，2-分，3-秒
operationControlCode	string	否	工序控制码编码
firstCheck	int	否	首检；0-否，1-是
changeType	int	否	换算方式；0-固定换算，1-浮动换算
productUnitTruncationType	int	否	生产单位舍位方式；同BigDecimal的舍入方式
isOutsource	int	否	委外；0-否，1-是
operationName	string	否	工序名称
productionUnitId	long	否	生产单位ID
outUnitPrecision	int	否	产出单位精度
workCenterCode	string	否	工作中心编码
executeOrgId	string	否	执行组织ID
productUnitName	string	否	生产单位
reportWork	int	否	报工；0-否，1-是
scheduleProdNum2	BigDecimal	否	计划加工数量（产出单位）
totalCompleteNum	BigDecimal	否	累计完成数量
totalCompleteNum1	BigDecimal	否	累计完成件数
totalCompleteNum2	BigDecimal	否	累计完成数量（产出单位）
totalQualifiedNum	BigDecimal	否	累计合格数量
totalQualifiedNum1	BigDecimal	否	累计合格件数
totalQualifiedNum2	BigDecimal	否	累计合格数量 （产出单位）
totalScrapNum	BigDecimal	否	累计报废数量
totalScrapNum1	BigDecimal	否	累计报废件数
totalScrapNum2	BigDecimal	否	累计报废数量 （产出单位）
totalReworkNum	BigDecimal	否	累计待返工数量
totalReworkNum1	BigDecimal	否	累计待返工件数
totalReworkNum2	BigDecimal	否	累计待返工数量 （产出单位）
totalReworkProcessNum	BigDecimal	否	累计返工处理数量
totalReworkProcessNum1	BigDecimal	否	累计返工处理件数
totalReworkProcessNum2	BigDecimal	否	累计返工处理数量 （产出单位）
totalTurnNum	BigDecimal	否	累计转出数量
totalTurnNum1	BigDecimal	否	累计转出件数
totalTurnNum2	BigDecimal	否	累计转出数量 （产出单位）
totalQualifiedTurnNum	BigDecimal	否	累计合格转出数量
totalQualifiedTurnNum1	BigDecimal	否	累计合格转出件数
totalQualifiedTurnNum2	BigDecimal	否	累计合格转出数量 （产出单位）
totalReworkTurnNum	BigDecimal	否	累计返工转出数量
totalReworkTurnNum1	BigDecimal	否	累计返工转出件数
totalReworkTurnNum2	BigDecimal	否	累计返工转出数量 （产出单位）
scrapInNum	BigDecimal	否	累计报废转出数量
scrapInNum1	BigDecimal	否	累计报废转出件数
scrapInNum2	BigDecimal	否	累计报废转出数量 （产出数量）
out_sys_id	string	否	外部来源Id
out_sys_code	string	否	外部来源编码
out_sys_version	string	否	外部系统版本
out_sys_type	string	否	外部来源类型
OrderProduct_projectId	string	否	项目Id
out_sys_rowno	string	否	外部来源行号
out_sys_lineid	string	否	外部来源行
OrderProduct_projectCode	string	否	项目编码
OrderProduct_projectName	string	否	项目名称
OrderProduct_wbs	string	否	wbs
OrderProduct_wbsCode	string	否	WBS任务编码
OrderProduct_wbsName	string	否	WBS任务名称
OrderProduct_activity	long	否	活动
OrderProduct_activityCode	string	否	活动编码
OrderProduct_activityName	string	否	活动名称
cfmIncomingQty	number
小数位数:8,最大长度:28	否	确认累计入库数量
cfmIncomingAuxQty	number
小数位数:8,最大长度:28	否	确认累计入库件数
cfmScrapStockQty	number
小数位数:8,最大长度:28	否	确认报废入库数量
cfmScrapStockAuxQty	number
小数位数:8,最大长度:28	否	确认报废入库件数
cfmReceivedKit	number
小数位数:8,最大长度:28	否	确认已领套数
firstCheck	string	否	首检；0-否，1-是
firstCheckType	string	否	首检控制方式；0-不控制，1-严格
firstCheckStatus	string	否	首检状态；0-无需首检，1-待首检，2-首检中，3-首检合格，4-首检不合格
sumRecordList	object	是	合计字段集合
OrderProduct_auxiliaryQuantity	long	否	生产件数
OrderProduct_quantity	long	否	生产数量
mainUnitPrecision	long	否	主计量精度
productUnitPrecision	long	否	生产单位精度
OrderProduct_mrpQuantity	long	否	净算量
pageCount	long	否	总页数
beginPageIndex	long	否	开始页码
endPageIndex	long	否	结束页码
pubts	string	否	时间戳
5. 正确返回示例
{
	"code": "200",
	"message": "操作成功",
	"data": {
		"pageIndex": 1,
		"pageSize": 20,
		"recordCount": 0,
		"recordList": [
			{
				"OrderProduct_materialName": "自行车",
				"OrderProduct_startDate": "2021-03-24 00:00:00",
				"productDefineDts": {
					"WW": "",
					"XS11": "",
					"XS15": "",
					"id": ""
				},
				"freeCharacteristics": {
					"XS15": "",
					"XXX0111": "",
					"id": ""
				},
				"defineDts": {
					"U9003": "",
					"id": ""
				},
				"OrderProduct_lineNo": 10,
				"productUnitPrecision": 3,
				"OrderProduct_scrap": 0,
				"OrderProduct_orgId": "****************",
				"OrderProduct_skuCode": "jq01000001",
				"transTypeId": "1248018423173517",
				"mainUnitPrecision": 3,
				"id": 2184924571914496,
				"OrderProduct_sourceType": "1",
				"OrderProduct_productId": 2061736079708416,
				"OrderProduct_mrpQuantity": 120,
				"OrderProduct_changeType": 0,
				"departmentName": "生产部",
				"orgName": "L工厂1",
				"auditTime": "2023-02-21 11:22:55",
				"auditDate": "2023-02-22",
				"isWfControlled": false,
				"OrderProduct_quantity": 120,
				"OrderProduct_completedQuantity": 1231,
				"OrderProduct_incomingQuantity": 120,
				"isHold": false,
				"OrderProduct_skuName": "自行车",
				"routingVersion": "1.0",
				"routingCode": "dfasdaf",
				"routingId": ****************,
				"OrderProduct_completedFlag": true,
				"OrderProduct_materialCode": "jq01000001",
				"OrderProduct_productionUnitId": ****************,
				"status": 0,
				"returncount": 0,
				"routingName": "工艺",
				"verifystate": 0,
				"code": "SCDD20210324000003",
				"creatorId": ****************,
				"orderProduct_id": ****************,
				"orgId": "****************",
				"vouchdate": "2021-03-24 00:00:00",
				"OrderProduct_auxiliaryQuantity": 120,
				"OrderProduct_materialApplyFlag": false,
				"OrderProduct_mainUnit": ****************,
				"OrderProduct_mainUnitTruncationType": 4,
				"transTypeName": "标准生产",
				"pubts": "2021-03-24 11:40:13",
				"OrderProduct_skuId": ****************,
				"OrderProduct_productUnitTruncationType": 4,
				"entrustProcessType": 2,
				"OrderProduct_retMaterialApplyFlag": 0,
				"OrderProduct_orgName": "L工厂1",
				"creator": "***********",
				"OrderProduct_finishDate": "2021-03-26 00:00:00",
				"OrderProduct_changeRate": 1,
				"OrderProduct_isHold": false,
				"entrustCustomer": 11232131,
				"OrderProduct_versionCode": "A1",
				"OrderProduct_bomId": 2173985857769728,
				"OrderProduct_productUnitName": "件",
				"OrderProduct_mainUnitName": "件",
				"OrderProduct_materialApplyStatus": 1,
				"OrderProduct_materialStatus": 0,
				"entrustCustomerName": "受托客户",
				"OrderProduct_finishedWorkApplyStatus": 2,
				"OrderProduct_stockStatus": 1,
				"createTime": "2021-03-24 11:40:12",
				"productionDepartmentId": "1870534089855232",
				"offChartReceiptIsAllowed": true,
				"apsLock": 0,
				"dailyschQuantity": 10,
				"dailyschStatus": 0,
				"dailyschConquantity": 10,
				"transTypeCode": "PO-001",
				"orderMaterial": [
					{
						"materialDefineDts": {
							"XS11": "",
							"id": ""
						},
						"freeCharacteristics": {
							"XS15": "",
							"XXX0111": "",
							"id": ""
						},
						"isWholeSet": false,
						"receivedQuantity": 40,
						"recipientQuantity": 40,
						"numeratorQuantity": 1,
						"stockUnitPrecision": 7,
						"mainUnitTruncationType": 1,
						"stockUnitName": "个 ",
						"unitUseQuantity": 0.33333333,
						"auxiliaryReceivedQuantity": 40,
						"auxiliaryRecipientQuantity": 40,
						"orgId": "1870887948554496",
						"stockUnitTruncationType": 1,
						"scrap": 0,
						"lineNo": 10,
						"supplyType": "0",
						"mainUnitPrecision": 7,
						"truncUp": 0,
						"substituteFlag": 1,
						"id": 2184924571914498,
						"changeRate": 1,
						"pubts": "2021-03-24 11:40:13",
						"denominatorQuantity": 3,
						"bomId": 2173985857769728,
						"mainUnit": 1986620623900928,
						"fixedQuantity": 0,
						"orgName": "qing-gc001",
						"productName": "",
						"productCode": "",
						"productId": 2062992410120448,
						"changeType": 0,
						"orderProductId": ****************,
						"bomMaterialId": 2173985857769729,
						"mainUnitName": "个 ",
						"materialName": "车轮",
						"requirementDate": "2021-03-24 00:00:00",
						"skuCode": "jq01000003",
						"stockUnitId": 1986620623900928,
						"mustLossQuantity": 0,
						"calcCostFlag": 1,
						"orderMaterialExpinfo!id": 1471560962252734505,
						"excessAppliedQty": 25,
						"auxiliaryExcessAppliedQty": 25,
						"excessRecipientQty": 25,
						"auxiliaryExcessRecipientQty": 25,
						"orderMaterialExpinfo!excessQty": 25,
						"orderMaterialExpinfo!auxiliaryExcessQty": 25,
						"orderMaterialExpinfo!isExcess": false,
						"orderMaterialExpinfo!excessType": 1,
						"orderMaterialExpinfo!excessRate": 50,
						"orderMaterialExpinfo!fixedExcessQty": 25,
						"orderMaterialExpinfo!designator": "EW32421FDS3232S",
						"orderMaterialExpinfo!wholePoint": "1",
						"appliedRetQuantity": 20,
						"auxiliaryAppliedRetQuantity": 20,
						"appliedRetRestQuantity": 20,
						"auxiliaryAppliedRetRestQuantity": 20,
						"excessAppliedRetQty": 20,
						"auxiliaryExcessAppliedRetQty": 20,
						"excessAppliedRetRestQty": 20,
						"auxiliaryExcessAppliedRetRestQty": 20,
						"appliedRestQuantity": 20,
						"auxiliaryAppliedRestQuantity": 20,
						"excessAppliedRestQty": 20,
						"auxiliaryExcessAppliedRestQty": 20,
						"projectId": "",
						"projectCode": "",
						"projectName": "",
						"wbs": "",
						"wbsCode": "",
						"wbsName": "",
						"activity": 0,
						"activityCode": "",
						"activityName": "",
						"cfmReceivedQty": 20,
						"cfmAuxReceivedQty": 20,
						"cfmExcessRecipientQty": 20,
						"cfmExcessAuxQty": 20,
						"cfmReceivedKit": 120
					}
				],
				"orderActivity": [
					{
						"id": 0,
						"lineNum": 10,
						"orderId": 1471560962252734500,
						"orderProductId": 1471560962252734500,
						"activityId": 2499381355729664,
						"activityType": 2499380178506496,
						"activityTypeCode": "001",
						"activityTypeName": "001",
						"orderProcessId": 1471560962252734500,
						"opSn": 10,
						"activityClass": 4,
						"workCenterId": ****************,
						"workCenterCode": "C0001",
						"workCenterName": "车间管理-机加工",
						"operationId": ****************,
						"operationCode": "210618001",
						"operationName": "工序1234",
						"activityUnit": 1998025388839168,
						"activityUnitName": "箱",
						"activityUnitPrecision": 5,
						"activityUnitTruncationType": 4,
						"usageUnit": 1998025388839168,
						"usageUnitName": "箱",
						"usageUnitPrecision": 5,
						"usageUnitTruncationType": 4,
						"stdUsageQty": 144,
						"planUsageQty": 156,
						"denominatorQuantity": 1,
						"usageQty": 1,
						"isCalcCost": true,
						"activityQty": 13,
						"usageBasis": 0,
						"isAutoCreate": 1,
						"pubts": "2022-06-06 18:45:06"
					}
				],
				"orderByProduct": [
					{
						"id": 1471560962252734500,
						"orderProductLineNo": 10,
						"manufacturingSpecification": "Amy测试:A",
						"numeratorQuantity": 1,
						"productionType": "1",
						"productUnitPrecision": 8,
						"mainUnitTruncationType": 4,
						"warehouseName": "倒冲仓",
						"unitUseQuantity": 1,
						"orgId": "****************",
						"orgName": "Amy测试",
						"productName": "",
						"productCode": "",
						"productId": 2037661108424960,
						"freeCharacteristics": {
							"XS15": "",
							"XXX0111": "",
							"id": ""
						},
						"byProductDefineDts": {
							"id": ""
						},
						"lineNo": 10,
						"productionDate": "2022-06-06 00:00:00",
						"isBatchManage": false,
						"isExpiryDateManage": false,
						"mainUnitPrecision": 8,
						"changeRate": 1,
						"pubts": "2022-06-06 18:44:04",
						"denominatorQuantity": 1,
						"mainUnit": ****************,
						"quantity": 12,
						"changeType": 0,
						"productUnitTruncationType": 4,
						"orderProductId": 1471560962252734500,
						"productionUnitId": ****************,
						"mainUnitName": "台",
						"materialName": "台式机",
						"warehouseId": 2533365513196032,
						"offChartReceipt": true,
						"productUnitName": "台",
						"cfmIncomingQty": 120,
						"cfmIncomingAuxQty": 120,
						"cfmScrapStockQty": 120,
						"cfmScrapStockAuxQty": 120
					}
				],
				"orderProcess": [
					{
						"id": 1471560962252734500,
						"processDefineDts": {
							"id": ""
						},
						"orderId": 1471560962252734500,
						"orderProductId": 1471560962252734500,
						"operationControlId": 2550467569832448,
						"operationCode": "210618001",
						"productUnitPrecision": 8,
						"nextId": 1471560962252734500,
						"doScheduling": 1,
						"transferProcplanProdQty": 1,
						"finishGoodsId": 1471560962252734500,
						"finishWarehouseId": 1471560962252734500,
						"preSn": 1,
						"transferProcplanQty": 1,
						"workCenterId": ****************,
						"mainUnitPrecision": 8,
						"executeOrgName": "Amy测试",
						"outUnitId": ****************,
						"outUnitTruncationType": 4,
						"mainUnitId": ****************,
						"checkType": 0,
						"orgName": "Amy测试",
						"routingOperationId": ****************,
						"mainUnitName": "台",
						"prepareTime": 0.3,
						"routingOperationProcessTime": 2,
						"qty": 12,
						"occupyProduction": 0,
						"computingCosts": 0,
						"mainChangeRate": 1,
						"immediateHandover": 1,
						"operationIdRouteDesc": "官方警告",
						"outUnitName": "台",
						"operationControlName": "车间检验",
						"outChangeRate": 1,
						"mainUnitTruncationType": 4,
						"orgId": "****************",
						"processTime": 24,
						"procPlanCreate": 0,
						"nextSn": 20,
						"prodQty": 12,
						"operationId": ****************,
						"processType": 0,
						"sn": 10,
						"pubts": "2022-06-06 18:44:04",
						"planStartDate": "2022-06-06 00:00:00",
						"planEndDate": "2022-06-06 23:59:00",
						"workCenterName": "车间管理-机加工",
						"timeUnit": 1,
						"operationControlCode": "002",
						"firstCheck": 0,
						"changeType": 0,
						"productUnitTruncationType": 4,
						"isOutsource": 0,
						"operationName": "工序1234",
						"productionUnitId": ****************,
						"outUnitPrecision": 8,
						"workCenterCode": "C0001",
						"executeOrgId": "****************",
						"productUnitName": "台",
						"reportWork": 0,
						"scheduleProdNum2": 1,
						"totalCompleteNum": 1,
						"totalCompleteNum1": 1,
						"totalCompleteNum2": 1,
						"totalQualifiedNum": 1,
						"totalQualifiedNum1": 1,
						"totalQualifiedNum2": 1,
						"totalScrapNum": 1,
						"totalScrapNum1": 1,
						"totalScrapNum2": 1,
						"totalReworkNum": 1,
						"totalReworkNum1": 1,
						"totalReworkNum2": 1,
						"totalReworkProcessNum": 1,
						"totalReworkProcessNum1": 1,
						"totalReworkProcessNum2": 1,
						"totalTurnNum": 1,
						"totalTurnNum1": 1,
						"totalTurnNum2": 1,
						"totalQualifiedTurnNum": 1,
						"totalQualifiedTurnNum1": 1,
						"totalQualifiedTurnNum2": 1,
						"totalReworkTurnNum": 1,
						"totalReworkTurnNum1": 1,
						"totalReworkTurnNum2": 1,
						"scrapInNum": 1,
						"scrapInNum1": 1,
						"scrapInNum2": 1
					}
				],
				"out_sys_id": "2297527422652672",
				"out_sys_code": "SCDD20230101",
				"out_sys_version": "01",
				"out_sys_type": "u8c",
				"OrderProduct_projectId": "",
				"out_sys_rowno": "",
				"out_sys_lineid": "",
				"OrderProduct_projectCode": "",
				"OrderProduct_projectName": "",
				"OrderProduct_wbs": "",
				"OrderProduct_wbsCode": "",
				"OrderProduct_wbsName": "",
				"OrderProduct_activity": 0,
				"OrderProduct_activityCode": "",
				"OrderProduct_activityName": "",
				"cfmIncomingQty": 120,
				"cfmIncomingAuxQty": 120,
				"cfmScrapStockQty": 120,
				"cfmScrapStockAuxQty": 120,
				"cfmReceivedKit": 120,
				"firstCheck": "0",
				"firstCheckType": "0",
				"firstCheckStatus": "0"
			}
		],
		"sumRecordList": [
			{
				"OrderProduct_auxiliaryQuantity": 120,
				"OrderProduct_quantity": 120,
				"mainUnitPrecision": 3,
				"productUnitPrecision": 3,
				"OrderProduct_mrpQuantity": 120
			}
		],
		"pageCount": 0,
		"beginPageIndex": 1,
		"endPageIndex": 0,
		"pubts": "2021-03-24 15:11:10"
	}
}
6. 错误返回码
错误码	错误信息	描述
999	取决于错误类型，不同错误信息不同	
7. 错误返回示例
{
 "code": "999",
 "message": "非法的时间： 11111"
}