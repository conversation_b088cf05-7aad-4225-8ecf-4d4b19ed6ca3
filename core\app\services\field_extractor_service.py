import json
import os
import re
import time
from pathlib import Path

import structlog

from ..core.config import MODULES_CONFIG

"""
YS-API V3.0 字段提取服务
负责从API数据和MD文档中提取字段信息
从FieldConfigService拆分出来的专门服务
"""


logger = structlog.get_logger()


class FieldExtractorService:
    """字段提取服务 - 负责从API数据和MD文档中提取字段"""

    def __init___(self):
    """TODO: Add function description."""
    # 配置目录路径
    self.base_dir = Path(__file__).parent.parent.parent.parent  # 到v3目录
    self.config_dir = self.base_dir / "config" / "field_configs"
    self.md_docs_dir = self.base_dir.parent / "md文档"  # 指向根目录下的md文档
    self.md_mappings_path = self.base_dir / "logic" / "md_mappings.json"

    # 确保目录存在
    self.config_dir.mkdir(parents=True, exist_ok=True)

    # 加载MD映射
    self.md_mappings = self._load_md_mappings()

    # 加载模块配置
    self.modules_config = MODULES_CONFIG

    # 当前模块名（用于上下文）
    self.current_module = None

    # 缓存机制
    self.field_cache = {}  # {cache_key: (config, timestamp, ttl)}
    self.cache_ttl = 300  # 5分钟缓存
    self.cache_hits = 0
    self.cache_misses = 0

    # 特征字段模式
    self.characteristic_patterns = [
        r'.*Characteristics$',
        r'.*DefineCharacter$',
        r'.*defineCharacteristics$',
        r'.*freeCharacteristics$',
        r'.*Characteristics',
        r'.*DefineCharacter',
        r'.*Character$',
    ]

    # 重要的嵌套字段前缀
    self.important_prefixes = [
        'orderMaterial',
        'orderProduct',
        'OrderProduct',
        'orderByProduct',
        'orderProcess',
        'orderActivity',
        'productDefineDts',
        'freeCharacteristics',
        'materialDefineDts',
        'purInRecords',
        'osmInRecords',
        'materOuts',
        'storeProRecords',
        'orderDetails',
        'applyOrders',
        'subcontractRequisitionProduct',
        'planOrderItem',
        'salesOutsDefineCharacter',
        'salesOutsCharacteristics',
        'purchaseOrdersDefineCharacter',
        'purchaseOrdersCharacteristics',
    ]

    # 关键特征字段（需要特别提取）
    self.key_characteristic_fields = [
        'XS15',
        'XS11',
        'XXX0111',
        'WW',
        'CG01',
        'CG02',
        'CG03',
        'CG04',
        'CG05',
        'AA',
        'CG00025',
        'U9001',
        'U9002',
        'U9003',
        'U9004',
        'SF04',
        'SF05',
        'SF06',
    ]

    logger.info("字段提取服务初始化完成", cache_ttl=self.cache_ttl)

    def set_current_module(self, module_name: str):
        """设置当前处理的模块名称"""
        self.current_module = module_name

    def _load_md_mappings(self) -> Dict:
        """加载预生成的md_mappings.json文件"""
        if not os.path.exists(self.md_mappings_path):
            logger.warning(
                "MD映射文件不存在，将生成新的映射文件", path=self.md_mappings_path
            )
            return {}

        try:
            with open(self.md_mappings_path, 'r', encoding='utf-8') as f:
                mappings = json.load(f)
                logger.info("成功加载MD映射文件", modules_count=len(mappings))
                return mappings
        except Exception:
            logger.error("加载MD映射文件失败", error=str(e))
            return {}

    async def extract_fields_from_api(
        self,
        module_name: str,
        api_data: Optional[List[Dict]] = None,
        record_id: Optional[str] = None,
        force_refresh: bool = False,
        max_depth: int = 3,
    ) -> Dict:
        """
        从API数据中提取字段信息

        Args:
            module_name: 模块名称
            api_data: API返回的数据
            record_id: 记录ID（用于缓存）
            force_refresh: 是否强制刷新缓存
            max_depth: 最大提取深度

        Returns:
            Dict: 提取的字段信息
        """
        try:
            # 生成缓存键
            cache_key = f"api_fields_{module_name}_{record_id}_{max_depth}"

            # 检查缓存
            if not force_refresh and cache_key in self.field_cache:
                config, timestamp, ttl = self.field_cache[cache_key]
                if time.time() - timestamp < ttl:
                    self.cache_hits += 1
                    logger.info(
                        "从缓存获取API字段",
                        module_name=module_name,
                        cache_hits=self.cache_hits,
                    )
                    return config

            self.cache_misses += 1

            # 如果没有提供API数据，返回空结果
            if not api_data:
                logger.warning("未提供API数据，无法提取字段", module_name=module_name)
                return {}

            # 提取字段
            extracted_fields = {}

            for record in api_data:
                record_fields = self._extract_fields_from_record(
                    record, max_depth)
                extracted_fields.update(record_fields)

            # 确保字段名是英文
            extracted_fields = self._ensure_english_field_names(
                extracted_fields)

            # 缓存结果
            self.field_cache[cache_key] = (
                extracted_fields,
                time.time(),
                self.cache_ttl,
            )

            logger.info(
                "API字段提取完成",
                module_name=module_name,
                fields_count=len(extracted_fields),
                cache_misses=self.cache_misses,
            )

            return extracted_fields

        except Exception:
            logger.error("API字段提取失败", module_name=module_name, error=str(e))
            return {}

    async def extract_fields_from_md(
            self, module_name: str) -> Dict[str, Dict]:
        """
        从MD文档中提取字段信息

        Args:
            module_name: 模块名称

        Returns:
            Dict: 提取的字段信息
        """
        try:
            # 检查缓存
            cache_key = f"md_fields_{module_name}"
            if cache_key in self.field_cache:
                config, timestamp, ttl = self.field_cache[cache_key]
                if time.time() - timestamp < ttl:
                    self.cache_hits += 1
                    logger.info("从缓存获取MD字段", module_name=module_name)
                    return config

            self.cache_misses += 1

            # 查找对应的MD文件
            md_file = None
            for file in self.md_docs_dir.glob("*.md"):
                if module_name.lower() in file.name.lower():
                    md_file = file
                    break

            if not md_file:
                logger.warning(
                    "未找到对应的MD文档",
                    module_name=module_name,
                    md_docs_dir=str(self.md_docs_dir),
                )
                return {}

            # 读取MD文件内容
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析字段
            fields = self.parse_md_fields(content)

            # 缓存结果
            self.field_cache[cache_key] = (fields, time.time(), self.cache_ttl)

            logger.info(
                "MD字段提取完成",
                module_name=module_name,
                fields_count=len(fields),
                md_file=md_file.name,
            )

            return fields

        except Exception:
            logger.error("MD字段提取失败", module_name=module_name, error=str(e))
            return {}

    def parse_md_fields(self, content: str) -> Dict[str, Dict]:
        """
        解析MD文档内容，提取字段信息

        Args:
            content: MD文档内容

        Returns:
            Dict: 解析的字段信息
        """
        try:
            fields = {}

            # 使用正则表达式匹配字段定义
            # 匹配模式：字段名 类型 描述
            pattern = r'(\w+)\s+([A-Z]+(?:\(\d+(?:,\d+)?\))?)\s+(.+)'
            matches = re.findall(pattern, content)

            for match in matches:
                field_name, data_type, description = match

                # 清理描述
                description = self._extract_clean_chinese_name(description)

                fields[field_name] = {
                    "description": description,
                    "data_type": data_type,
                    "source": "md_doc",
                    "chinese_name": description,
                }

            logger.info("MD字段解析完成", fields_count=len(fields))
            return fields

        except Exception:
            logger.error("MD字段解析失败", error=str(e))
            return {}

    def _extract_clean_chinese_name(self, raw_description: str) -> str:
        """从原始描述中提取干净的中文名称"""
        try:
            # 移除特殊字符和多余空格
            cleaned = re.sub(
                r'[^\u4e00-\u9fa5a-zA-Z0-9\s]',
                '',
                raw_description)
            cleaned = re.sub(r'\s+', ' ', cleaned).strip()

            # 如果包含中文，提取中文部分
            chinese_chars = re.findall(r'[\u4e00-\u9fa5]+', cleaned)
            if chinese_chars:
                return chinese_chars[0]

            return cleaned

        except Exception:
            logger.error(
                "清理中文名称失败", raw_description=raw_description, error=str(e)
            )
            return raw_description

    def _extract_fields_from_record(
        self,
        record: Dict,
        max_depth: int,
        current_path: str = "",
        current_depth: int = 0,
    ) -> Dict[str, Dict]:
        """
        从单条记录中提取字段信息

        Args:
            record: 记录数据
            max_depth: 最大深度
            current_path: 当前路径
            current_depth: 当前深度

        Returns:
            Dict: 提取的字段信息
        """
        try:
            fields = {}

            if current_depth >= max_depth:
                return fields

            for key, value in record.items():
                # 构建字段路径
                field_path = f"{current_path}.{key}" if current_path else key

                # 猜测数据类型
                data_type = self._guess_data_type_from_value(value)

                # 创建字段信息
                fields[field_path] = {
                    "description": key,
                    "data_type": data_type,
                    "source": "api_data",
                    "chinese_name": key,
                    "sample_value": str(value)[
                        :100] if value is not None else "",
                }

                # 递归处理嵌套对象
                if isinstance(value, dict) and current_depth < max_depth - 1:
                    nested_fields = self._extract_fields_from_record(
                        value, max_depth, field_path, current_depth + 1
                    )
                    fields.update(nested_fields)

                # 处理列表
                elif isinstance(value, list) and value and isinstance(value[0], dict):
                    if current_depth < max_depth - 1:
                        nested_fields = self._extract_fields_from_record(
                            value[0], max_depth, field_path, current_depth + 1
                        )
                        fields.update(nested_fields)

            return fields

        except Exception:
            logger.error("记录字段提取失败", error=str(e))
            return {}

    def _guess_data_type_from_value(self, value: Any) -> str:
        """根据值猜测数据类型"""
        try:
            if value is None:
                return "NVARCHAR(255)"
            elif isinstance(value, bool):
                return "BIT"
            elif isinstance(value, int):
                if value > 2147483647:
                    return "BIGINT"
                else:
                    return "INT"
            elif isinstance(value, float):
                return "DECIMAL(18,4)"
            elif isinstance(value, str):
                # 检查是否是日期时间
                if self._is_datetime_string(value):
                    return "DATETIME2"
                elif len(value) > 255:
                    return "NTEXT"
                else:
                    return "NVARCHAR(255)"
            else:
                return "NVARCHAR(255)"

        except Exception:
            logger.error("数据类型猜测失败", value=value, error=str(e))
            return "NVARCHAR(255)"

    def _is_datetime_string(self, value: str) -> bool:
        """检查字符串是否是日期时间格式"""
        try:
            # 常见的日期时间格式
            datetime_patterns = [
                r'\d{4}-\d{2}-\d{2}',
                r'\d{4}/\d{2}/\d{2}',
                r'\d{2}-\d{2}-\d{4}',
                r'\d{2}/\d{2}/\d{4}',
            ]

            for pattern in datetime_patterns:
                if re.match(pattern, value):
                    return True

            return False

        except Exception:
            return False

    def _ensure_english_field_names(
        self, extracted_fields: Dict[str, Dict]
    ) -> Dict[str, Dict]:
        """确保字段名是英文"""
        try:
            english_fields = {}

            for field_name, field_info in extracted_fields.items():
                # 检查字段名是否包含中文
                if self._contains_chinese(field_name):
                    # 生成英文字段名
                    english_name = self._generate_english_field_name(
                        field_name)
                    field_info["original_name"] = field_name
                    field_info["api_field_name"] = english_name
                    english_fields[english_name] = field_info
                else:
                    # 字段名已经是英文
                    if self._is_valid_english_field_name(field_name):
                        english_fields[field_name] = field_info
                    else:
                        # 生成有效的英文字段名
                        english_name = self._generate_english_field_name(
                            field_name)
                        field_info["original_name"] = field_name
                        field_info["api_field_name"] = english_name
                        english_fields[english_name] = field_info

            return english_fields

        except Exception:
            logger.error("确保英文字段名失败", error=str(e))
            return extracted_fields

    def _contains_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符"""
        return bool(re.search(r'[\u4e00-\u9fa5]', text))

    def _is_valid_english_field_name(self, field_name: str) -> bool:
        """检查是否是有效的英文字段名"""
        return bool(re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', field_name))

    def _generate_english_field_name(self, field_name: str) -> str:
        """生成英文字段名"""
        try:
            # 移除特殊字符，保留字母数字和下划线
            english_name = re.sub(r'[^a-zA-Z0-9_]', '_', field_name)

            # 确保不以数字开头
            if english_name and english_name[0].isdigit():
                english_name = f"field_{english_name}"

            # 确保不为空
            if not english_name:
                english_name = "unknown_field"

            return english_name

        except Exception:
            logger.error("生成英文字段名失败", field_name=field_name, error=str(e))
            return "unknown_field"

    def get_cache_stats(self) -> Dict:
        """获取缓存统计信息"""
        return {
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "cache_size": len(self.field_cache),
            "hit_rate": (
                self.cache_hits / (self.cache_hits + self.cache_misses)
                if (self.cache_hits + self.cache_misses) > 0
                else 0
            ),
        }

    def clear_cache(self):
        """清空缓存"""
        self.field_cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0
        logger.info("字段提取服务缓存已清空")

    def extract_all_fields_deep(
        self, api_data: List[Dict], module_name: str, max_depth: Optional[int] = None
    ) -> Dict[str, Dict]:
        """
        全深度提取所有字段（无深度限制）

        Args:
            api_data: API返回的数据
            module_name: 模块名称
            max_depth: 最大深度（None表示无限制）

        Returns:
            Dict: 提取的所有字段信息
        """
        try:
            logger.info(
                "开始全深度字段提取", module_name=module_name, max_depth=max_depth
            )

            extracted_fields = {}

            for record in api_data:
                record_fields = self._extract_fields_from_record_deep(
                    record, max_depth)
                extracted_fields.update(record_fields)

            # 确保字段名是英文
            extracted_fields = self._ensure_english_field_names(
                extracted_fields)

            logger.info(
                "全深度字段提取完成",
                module_name=module_name,
                fields_count=len(extracted_fields),
            )

            return extracted_fields

        except Exception:
            logger.error("全深度字段提取失败", module_name=module_name, error=str(e))
            return {}

    def _extract_fields_from_record_deep(
        self,
        record: Dict,
        max_depth: Optional[int] = None,
        current_path: str = "",
        current_depth: int = 0,
    ) -> Dict[str, Dict]:
        """
        全深度提取记录中的字段（无深度限制）

        Args:
            record: 记录数据
            max_depth: 最大深度（None表示无限制）
            current_path: 当前字段路径
            current_depth: 当前深度

        Returns:
            Dict: 提取的字段信息
        """
        fields = {}

        # 检查深度限制
        if max_depth is not None and current_depth >= max_depth:
            return fields

        for key, value in record.items():
            # 构建字段路径
            field_path = f"{current_path}.{key}" if current_path else key

            # 处理数组
            if isinstance(value, list) and value:
                # 为数组添加[*]标记
                array_path = f"{field_path}[*]"

                # 处理数组中的第一个元素
                if value:
                    first_item = value[0]
                    if isinstance(first_item, dict):
                        # 递归处理数组元素
                        array_fields = self._extract_fields_from_record_deep(
                            first_item, max_depth, array_path, current_depth + 1
                        )
                        fields.update(array_fields)
                    else:
                        # 数组元素是简单类型
                        fields[array_path] = {
                            "api_field_name": array_path,
                            "sample_value": str(first_item),
                            "data_type": self._guess_data_type_from_value(first_item),
                            "depth": current_depth + 1,
                            "chinese_name": key,
                            "doc_chinese_name": key,
                            "match_type": "自动翻译",
                            "config_name": key,
                            "config_match_source": "自动翻译",
                            "is_selected": True,
                            "match_reason": "全深度展开",
                            "etl_score": 0.5,
                            "etl_recommended": True,
                            "etl_reasons": ["全深度展开", "数据完整性良好"],
                        }

            # 处理嵌套对象
            elif isinstance(value, dict):
                # 递归处理嵌套对象
                nested_fields = self._extract_fields_from_record_deep(
                    value, max_depth, field_path, current_depth + 1
                )
                fields.update(nested_fields)

            # 处理简单类型
            else:
                fields[field_path] = {
                    "api_field_name": field_path,
                    "sample_value": str(value) if value is not None else "",
                    "data_type": self._guess_data_type_from_value(value),
                    "depth": current_depth,
                    "chinese_name": key,
                    "doc_chinese_name": key,
                    "match_type": "自动翻译",
                    "config_name": key,
                    "config_match_source": "自动翻译",
                    "is_selected": True,
                    "match_reason": "全深度展开",
                    "etl_score": 0.5,
                    "etl_recommended": True,
                    "etl_reasons": ["全深度展开", "数据完整性良好"],
                }

        return fields
