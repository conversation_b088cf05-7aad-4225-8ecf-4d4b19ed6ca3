import argparse
import json
import sqlite3
import sys
from datetime import datetime
from pathlib import Path

#!/usr/bin/env python3
"""
简化的数据库双写检查工具 - 不依赖PostgreSQL
模拟数据一致性检查，用于演示目的
"""


class SimpleDualWriteChecker:
    """简化的双写检查器"""

    def __init___(self):
    """TODO: Add function description."""
    self.project_root = Path(".")
    self.legacy_db = self.project_root / "backend" / "ysapi.db"
    self.results = {
        "timestamp": datetime.now().isoformat(),
        "checks": [],
        "overall_status": "unknown",
    }

    def check_legacy_database(self):
        """检查Legacy数据库"""
        print("🔍 检查Legacy数据库...")

        try:
            if not self.legacy_db.exists():
                self.results["checks"].append(
                    {
                        "check": "legacy_database",
                        "status": "warning",
                        "message": f"Legacy数据库不存在: {self.legacy_db}",
                    }
                )
                print(f"⚠️ Legacy数据库不存在: {self.legacy_db}")
                return False

            # 检查数据库连接
            conn = sqlite3.connect(self.legacy_db)
            cursor = conn.cursor()

            # 获取表列表
            cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            table_count = len(tables)

            self.results["checks"].append(
                {
                    "check": "legacy_database",
                    "status": "success",
                    "message": f"Legacy数据库正常，包含{table_count}个表",
                    "table_count": table_count,
                    "tables": [t[0] for t in tables],
                }
            )

            print(f"✅ Legacy数据库正常，包含{table_count}个表")

            conn.close()
            return True

        except Exception:
            self.results["checks"].append(
                {
                    "check": "legacy_database",
                    "status": "error",
                    "message": f"Legacy数据库检查失败: {e}",
                }
            )
            print(f"❌ Legacy数据库检查失败: {e}")
            return False

    def simulate_new_database_check(self):
        """模拟新数据库检查"""
        print("🔍 模拟新数据库检查...")

        # 检查新系统模块目录
        new_modules_dir = self.project_root / "new-system" / "modules"

        if not new_modules_dir.exists():
            self.results["checks"].append(
                {
                    "check": "new_database",
                    "status": "warning",
                    "message": "新系统模块目录不存在",
                }
            )
            print("⚠️ 新系统模块目录不存在")
            return False

        # 统计新模块数量
        modules = list(new_modules_dir.iterdir())
        module_count = len([m for m in modules if m.is_dir()])

        self.results["checks"].append(
            {
                "check": "new_database",
                "status": "success",
                "message": f"新系统包含{module_count}个模块",
                "module_count": module_count,
                "modules": [m.name for m in modules if m.is_dir()],
            }
        )

        print(f"✅ 新系统包含{module_count}个模块")
        return True

    def check_data_consistency(self):
        """检查数据一致性（模拟）"""
        print("🔍 模拟数据一致性检查...")

        # 获取模块状态
        status_file = self.project_root / "tasks" / "module_status.json"

        if not status_file.exists():
            self.results["checks"].append(
                {
                    "check": "data_consistency",
                    "status": "warning",
                    "message": "模块状态文件不存在",
                }
            )
            print("⚠️ 模块状态文件不存在")
            return False

        try:
            with open(status_file, "r", encoding="utf-8") as f:
                status = json.load(f)

            modules = status.get("modules", {})
            completed_modules = 0

            for module_name, module_data in modules.items():
                if module_data.get("completion_rate", 0) == 100:
                    completed_modules += 1

            consistency_rate = completed_modules / \
                len(modules) * 100 if modules else 0

            self.results["checks"].append(
                {
                    "check": "data_consistency",
                    "status": "success" if consistency_rate == 100 else "warning",
                    "message": f"数据一致性: {consistency_rate:.1f}%",
                    "completed_modules": completed_modules,
                    "total_modules": len(modules),
                    "consistency_rate": consistency_rate,
                })

            print(
                f"✅ 数据一致性: {consistency_rate:.1f%} ({completed_modules}/{len(modules)})"
            )
            return consistency_rate == 100

        except Exception:
            self.results["checks"].append(
                {
                    "check": "data_consistency",
                    "status": "error",
                    "message": f"一致性检查失败: {e}",
                }
            )
            print(f"❌ 一致性检查失败: {e}")
            return False

    def check_graveyard_backups(self):
        """检查graveyard备份"""
        print("🔍 检查graveyard备份...")

        graveyard_dir = self.project_root / "graveyard"

        if not graveyard_dir.exists():
            self.results["checks"].append(
                {
                    "check": "graveyard_backups",
                    "status": "warning",
                    "message": "graveyard备份目录不存在",
                }
            )
            print("⚠️ graveyard备份目录不存在")
            return False

        # 统计备份模块
        backup_modules = list(graveyard_dir.iterdir())
        backup_count = len([m for m in backup_modules if m.is_dir()])

        self.results["checks"].append(
            {
                "check": "graveyard_backups",
                "status": "success",
                "message": f"发现{backup_count}个模块备份",
                "backup_count": backup_count,
                "backup_modules": [
                    m.name for m in backup_modules if m.is_dir()],
            })

        print(f"✅ 发现{backup_count}个模块备份")
        return True

    def run_all_checks(self):
        """运行所有检查"""
        print("🚀 开始数据库双写一致性检查...")
        print("=" * 50)

        checks = [
            self.check_legacy_database,
            self.simulate_new_database_check,
            self.check_data_consistency,
            self.check_graveyard_backups,
        ]

        success_count = 0

        for check in checks:
            if check():
                success_count += 1
            print()

        # 计算总体状态
        if success_count == len(checks):
            self.results["overall_status"] = "success"
            print("🎉 所有检查通过！")
            status_code = 0
        elif success_count > len(checks) // 2:
            self.results["overall_status"] = "warning"
            print("⚠️ 部分检查通过，需要注意")
            status_code = 1
        else:
            self.results["overall_status"] = "error"
            print("❌ 检查失败，需要修复")
            status_code = 2

        # 保存检查结果
        report_path = self.project_root / "reports" / "database_check_report.json"
        report_path.parent.mkdir(exist_ok=True)

        with open(report_path, "w", encoding="utf-8") as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

        print(f"📄 检查报告已保存: {report_path}")
        return status_code


def main():
    """主函数"""

    parser = argparse.ArgumentParser(description="数据库双写一致性检查工具")
    parser.add_argument("--check", action="store_true", help="运行完整检查")
    parser.add_argument(
        "--legacy-only",
        action="store_true",
        help="仅检查Legacy数据库")

    args = parser.parse_args()

    checker = SimpleDualWriteChecker()

    if args.legacy_only:
        success = checker.check_legacy_database()
        sys.exit(0 if success else 1)
    else:
        status_code = checker.run_all_checks()
        sys.exit(status_code)


if __name__ == "__main__":
    main()
