{"module_name": "applyorder", "display_name": "请购单", "version": "2.0.0", "source": "json_parser", "total_fields": 218, "created_at": "2025-07-28T20:12:24.852981", "last_updated": "2025-07-28T20:12:24.852981", "fields": {"code": {"api_field_name": "code", "chinese_name": "请购单编号，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "请购单编号，汇总场景和明细场景均返回", "path": "data.recordList.code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "message": {"api_field_name": "message", "chinese_name": "调用失败时的错误信息", "data_type": "NVARCHAR(500)", "param_desc": "调用失败时的错误信息", "path": "message", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "调用成功时的返回数据", "data_type": "NVARCHAR(MAX)", "param_desc": "调用成功时的返回数据", "path": "data", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageIndex": {"api_field_name": "pageIndex", "chinese_name": "页码", "data_type": "BIGINT", "param_desc": "页码", "path": "pageIndex", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageSize": {"api_field_name": "pageSize", "chinese_name": "每页条数", "data_type": "BIGINT", "param_desc": "每页条数", "path": "pageSize", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageCount": {"api_field_name": "pageCount", "chinese_name": "页数", "data_type": "BIGINT", "param_desc": "页数", "path": "data.pageCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "beginPageIndex": {"api_field_name": "beginPageIndex", "chinese_name": "起始页", "data_type": "BIGINT", "param_desc": "起始页", "path": "data.beginPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "endPageIndex": {"api_field_name": "endPageIndex", "chinese_name": "结束页", "data_type": "BIGINT", "param_desc": "结束页", "path": "data.endPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordCount": {"api_field_name": "recordCount", "chinese_name": "记录数", "data_type": "BIGINT", "param_desc": "记录数", "path": "data.recordCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pubts": {"api_field_name": "pubts", "chinese_name": "时间戳，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "时间戳，汇总场景和明细场景均返回", "path": "data.recordList.pubts", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "recordList": {"api_field_name": "recordList", "chinese_name": "返回列表信息", "data_type": "NVARCHAR(MAX)", "param_desc": "返回列表信息", "path": "data.recordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vouchdate": {"api_field_name": "vouchdate", "chinese_name": "单据日期，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "单据日期，汇总场景和明细场景均返回", "path": "data.recordList.vouchdate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "returncount": {"api_field_name": "returncount", "chinese_name": "退回次数，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "退回次数，汇总场景和明细场景均返回", "path": "data.recordList.returncount", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isWfControlled": {"api_field_name": "isWfControlled", "chinese_name": "是否审批流控制，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "是否审批流控制，汇总场景和明细场景均返回", "path": "data.recordList.isWfControlled", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "verifystate": {"api_field_name": "verifystate", "chinese_name": "审批状态，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "审批状态，汇总场景和明细场景均返回", "path": "data.recordList.verifystate", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bustype": {"api_field_name": "bustype", "chinese_name": "交易类型id", "data_type": "NVARCHAR(500)", "param_desc": "交易类型id", "path": "bustype", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bustype_name": {"api_field_name": "bustype_name", "chinese_name": "交易类型名称，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "交易类型名称，汇总场景和明细场景均返回", "path": "data.recordList.bustype_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyDept": {"api_field_name": "applyDept", "chinese_name": "请购部门id，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "请购部门id，汇总场景和明细场景均返回", "path": "data.recordList.applyDept", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyDept_name": {"api_field_name": "applyDept_name", "chinese_name": "请购部门名称，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "请购部门名称，汇总场景和明细场景均返回", "path": "data.recordList.applyDept_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bizstatus": {"api_field_name": "bi<PERSON><PERSON><PERSON>", "chinese_name": "单据状态, 0:开立、3:审核中、1:已审核、2:已关闭，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "单据状态, 0:开立、3:审核中、1:已审核、2:已关闭，汇总场景和明细场景均返回", "path": "data.recordList.bizstatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "status": {"api_field_name": "status", "chinese_name": "单据状态, 0:开立、3:审核中、1:已审核、2:已关闭，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "单据状态, 0:开立、3:审核中、1:已审核、2:已关闭，汇总场景和明细场景均返回", "path": "data.recordList.status", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currency": {"api_field_name": "currency", "chinese_name": "币种id，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "币种id，汇总场景和明细场景均返回", "path": "data.recordList.currency", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currency_name": {"api_field_name": "currency_name", "chinese_name": "币种名称，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "币种名称，汇总场景和明细场景均返回", "path": "data.recordList.currency_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehouseId": {"api_field_name": "warehouseId", "chinese_name": "要货仓库id，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "要货仓库id，汇总场景和明细场景均返回", "path": "data.recordList.warehouseId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "warehouseId_name": {"api_field_name": "warehouseId_name", "chinese_name": "要货仓库名称，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "要货仓库名称，汇总场景和明细场景均返回", "path": "data.recordList.warehouseId_name", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "source": {"api_field_name": "source", "chinese_name": "来源单据类型，20:计划独立需求、280:计划订单、MR.mr_lrp_plan_order_batch:计划订单、po_production_order:生产订单、ucf-amc-aum.aum_assignapply_card:资产领用申请、yonbip-pm-planme.rscm_project_materiallist_card:项目物资单、SCMSA.voucher_order:销售订单，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "来源单据类型，20:计划独立需求、280:计划订单、MR.mr_lrp_plan_order_batch:计划订单、po_production_order:生产订单、ucf-amc-aum.aum_assignapply_card:资产领用申请、yonbip-pm-planme.rscm_project_materiallist_card:项目物资单、SCMSA.voucher_order:销售订单，汇总场景和明细场景均返回", "path": "data.recordList.source", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "store": {"api_field_name": "store", "chinese_name": "所属门店id，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "所属门店id，汇总场景和明细场景均返回", "path": "data.recordList.store", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isUretailVoucher": {"api_field_name": "isUretailVoucher", "chinese_name": "是否是零售, true:是、false:否，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "是否是零售, true:是、false:否，汇总场景和明细场景均返回", "path": "data.recordList.isUretailVoucher", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "store_name": {"api_field_name": "store_name", "chinese_name": "所属门店名称，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "所属门店名称，汇总场景和明细场景均返回", "path": "data.recordList.store_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "org": {"api_field_name": "org", "chinese_name": "需求组织id，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "需求组织id，汇总场景和明细场景均返回", "path": "data.recordList.org", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "org_name": {"api_field_name": "org_name", "chinese_name": "需求组织名称，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "需求组织名称，汇总场景和明细场景均返回", "path": "data.recordList.org_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "custom": {"api_field_name": "custom", "chinese_name": "客户id，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "客户id，汇总场景和明细场景均返回", "path": "data.recordList.custom", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "creator": {"api_field_name": "creator", "chinese_name": "制单人，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "制单人，汇总场景和明细场景均返回", "path": "data.recordList.creator", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "createTime": {"api_field_name": "createTime", "chinese_name": "制单时间，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "制单时间，汇总场景和明细场景均返回", "path": "data.recordList.createTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "modifier": {"api_field_name": "modifier", "chinese_name": "修改人，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "修改人，汇总场景和明细场景均返回", "path": "data.recordList.modifier", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "modifyTime": {"api_field_name": "modifyTime", "chinese_name": "修改时间，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "修改时间，汇总场景和明细场景均返回", "path": "data.recordList.modifyTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "closer": {"api_field_name": "closer", "chinese_name": "关闭人，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "关闭人，汇总场景和明细场景均返回", "path": "data.recordList.closer", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "closeTime": {"api_field_name": "closeTime", "chinese_name": "关闭时间，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "关闭时间，汇总场景和明细场景均返回", "path": "data.recordList.closeTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "locker": {"api_field_name": "locker", "chinese_name": "锁定人，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "锁定人，汇总场景和明细场景均返回", "path": "data.recordList.locker", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "lockTime": {"api_field_name": "lockTime", "chinese_name": "锁定时间，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "锁定时间，汇总场景和明细场景均返回", "path": "data.recordList.lockTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operator": {"api_field_name": "operator", "chinese_name": "请购员id，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "请购员id，汇总场景和明细场景均返回", "path": "data.recordList.operator", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operator_name": {"api_field_name": "operator_name", "chinese_name": "请购员名称，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "请购员名称，汇总场景和明细场景均返回", "path": "data.recordList.operator_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "auditor": {"api_field_name": "auditor", "chinese_name": "审核人，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "审核人，汇总场景和明细场景均返回", "path": "data.recordList.auditor", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "auditTime": {"api_field_name": "auditTime", "chinese_name": "审核时间，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "审核时间，汇总场景和明细场景均返回", "path": "data.recordList.auditTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auditDate": {"api_field_name": "auditDate", "chinese_name": "审核日期，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "审核日期，汇总场景和明细场景均返回", "path": "data.recordList.auditDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "submitor": {"api_field_name": "submitor", "chinese_name": "提交人，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "提交人，汇总场景和明细场景均返回", "path": "data.recordList.submitor", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "submitTime": {"api_field_name": "submitTime", "chinese_name": "提交时间，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "提交时间，汇总场景和明细场景均返回", "path": "data.recordList.submitTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalQuantity": {"api_field_name": "totalQuantity", "chinese_name": "整单数量，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "整单数量，汇总场景和明细场景均返回", "path": "data.recordList.totalQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "memo": {"api_field_name": "memo", "chinese_name": "备注，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "备注，汇总场景和明细场景均返回", "path": "data.recordList.memo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "id": {"api_field_name": "id", "chinese_name": "表体自定义项id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项id,只有明细场景返回", "path": "data.recordList.bodyItem.id", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "tplid": {"api_field_name": "tplid", "chinese_name": "模板id，汇总场景和明细场景均返回", "data_type": "NVARCHAR(500)", "param_desc": "模板id，汇总场景和明细场景均返回", "path": "data.recordList.tplid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "headItem": {"api_field_name": "headItem", "chinese_name": "表头自定义项", "data_type": "NVARCHAR(MAX)", "param_desc": "表头自定义项", "path": "data.recordList.headItem", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define1": {"api_field_name": "define1", "chinese_name": "表体自定义项1,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项1,只有明细场景返回", "path": "data.recordList.bodyItem.define1", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define2": {"api_field_name": "define2", "chinese_name": "表体自定义项2,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项2,只有明细场景返回", "path": "data.recordList.bodyItem.define2", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define3": {"api_field_name": "define3", "chinese_name": "表体自定义项3,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项3,只有明细场景返回", "path": "data.recordList.bodyItem.define3", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_execStatus": {"api_field_name": "applyorders_execStatus", "chinese_name": "执行状态, 0:未下订单、1:部分下单、2:全部下单、", "data_type": "NVARCHAR(500)", "param_desc": "执行状态, 0:未下订单、1:部分下单、2:全部下单、", "path": "data.recordList.applyorders_execStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_receiveOrg": {"api_field_name": "applyorders_receiveOrg", "chinese_name": "收货组织id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "收货组织id,只有明细场景返回", "path": "data.recordList.applyorders_receiveOrg", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_receiveOrg_name": {"api_field_name": "applyorders_receiveOrg_name", "chinese_name": "收货组织名称,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "收货组织名称,只有明细场景返回", "path": "data.recordList.applyorders_receiveOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_purchaseOrg": {"api_field_name": "applyorders_purchaseOrg", "chinese_name": "采购组织id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "采购组织id,只有明细场景返回", "path": "data.recordList.applyorders_purchaseOrg", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_purchaseOrg_name": {"api_field_name": "applyorders_purchaseOrg_name", "chinese_name": "采购组织名称,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "采购组织名称,只有明细场景返回", "path": "data.recordList.applyorders_purchaseOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_purDept": {"api_field_name": "applyorders_purDept", "chinese_name": "采购部门id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "采购部门id,只有明细场景返回", "path": "data.recordList.applyorders_purDept", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_purDept_name": {"api_field_name": "applyorders_purDept_name", "chinese_name": "采购部门名称,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "采购部门名称,只有明细场景返回", "path": "data.recordList.applyorders_purDept_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_purPerson": {"api_field_name": "applyorders_purPerson", "chinese_name": "采购业务员id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "采购业务员id,只有明细场景返回", "path": "data.recordList.applyorders_purPerson", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_purPerson_name": {"api_field_name": "applyorders_purPerson_name", "chinese_name": "采购业务员名称,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "采购业务员名称,只有明细场景返回", "path": "data.recordList.applyorders_purPerson_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_supplyMoney": {"api_field_name": "applyOrders_supplyMoney", "chinese_name": "累计订货金额,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "累计订货金额,只有明细场景返回", "path": "data.recordList.applyOrders_supplyMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyOrder_orderMoneyRatio": {"api_field_name": "applyOrder_orderMoneyRatio", "chinese_name": "订单金额超量比例,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "订单金额超量比例,只有明细场景返回", "path": "data.recordList.applyOrder_orderMoneyRatio", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_supplyCount": {"api_field_name": "applyorders_supplyCount", "chinese_name": "累计订货数量,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "累计订货数量,只有明细场景返回", "path": "data.recordList.applyorders_supplyCount", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "apporders_id": {"api_field_name": "apporders_id", "chinese_name": "订单行id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "订单行id,只有明细场景返回", "path": "data.recordList.apporders_id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_product": {"api_field_name": "applyorders_product", "chinese_name": "物料id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "物料id,只有明细场景返回", "path": "data.recordList.applyorders_product", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_defaultAlbumId": {"api_field_name": "product_defaultAlbumId", "chinese_name": "物料首图片,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "物料首图片,只有明细场景返回", "path": "data.recordList.product_defaultAlbumId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_product_cCode": {"api_field_name": "applyorders_product_cCode", "chinese_name": "物料编码,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "物料编码,只有明细场景返回", "path": "data.recordList.applyorders_product_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_product_cName": {"api_field_name": "applyorders_product_cName", "chinese_name": "物料名称,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "物料名称,只有明细场景返回", "path": "data.recordList.applyorders_product_cName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_productsku": {"api_field_name": "applyorders_productsku", "chinese_name": "物料SKUid,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "物料SKUid,只有明细场景返回", "path": "data.recordList.applyorders_productsku", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_productsku_cCode": {"api_field_name": "applyorders_productsku_cCode", "chinese_name": "物料sku编码,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "物料sku编码,只有明细场景返回", "path": "data.recordList.applyorders_productsku_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_productsku_cName": {"api_field_name": "applyorders_productsku_cName", "chinese_name": "物料sku名称,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "物料sku名称,只有明细场景返回", "path": "data.recordList.applyorders_productsku_cName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_currency": {"api_field_name": "applyorders_currency", "chinese_name": "币种id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "币种id,只有明细场景返回", "path": "data.recordList.applyorders_currency", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_currency_name": {"api_field_name": "applyorders_currency_name", "chinese_name": "币种名称,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "币种名称,只有明细场景返回", "path": "data.recordList.applyorders_currency_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_currency_priceDigit": {"api_field_name": "applyorders_currency_priceDigit", "chinese_name": "币种单价精度,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "币种单价精度,只有明细场景返回", "path": "data.recordList.applyorders_currency_priceDigit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_currency_moneyDigit": {"api_field_name": "applyorders_currency_moneyDigit", "chinese_name": "币种金额精度,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "币种金额精度,只有明细场景返回", "path": "data.recordList.applyorders_currency_moneyDigit", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_qty": {"api_field_name": "applyorders_qty", "chinese_name": "数量,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "数量,只有明细场景返回", "path": "data.recordList.applyorders_qty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_subQty": {"api_field_name": "applyorders_subQty", "chinese_name": "计价数量,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "计价数量,只有明细场景返回", "path": "data.recordList.applyorders_subQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_rowno": {"api_field_name": "applyorders_rowno", "chinese_name": "行号,只有明细场景返回", "data_type": "BIGINT", "param_desc": "行号,只有明细场景返回", "path": "data.recordList.applyorders_rowno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unit_Precision": {"api_field_name": "unit_Precision", "chinese_name": "主计量精度,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "主计量精度,只有明细场景返回", "path": "data.recordList.unit_Precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_unit": {"api_field_name": "applyorders_unit", "chinese_name": "单位id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "单位id,只有明细场景返回", "path": "data.recordList.applyorders_unit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_unit_name": {"api_field_name": "applyorders_unit_name", "chinese_name": "主计量名称,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "主计量名称,只有明细场景返回", "path": "data.recordList.applyorders_unit_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_product_oUnitId": {"api_field_name": "applyorders_product_oUnitId", "chinese_name": "零售单位id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "零售单位id,只有明细场景返回", "path": "data.recordList.applyorders_product_oUnitId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_product_productOfflineRetail_purchaseUnit": {"api_field_name": "applyorders_product_productOfflineRetail_purchaseUnit", "chinese_name": "采购单位id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "采购单位id,只有明细场景返回", "path": "data.recordList.applyorders_product_productOfflineRetail_purchaseUnit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_invExchRate": {"api_field_name": "applyorders_invExchRate", "chinese_name": "换算率,只有明细场景返回", "data_type": "BIGINT", "param_desc": "换算率,只有明细场景返回", "path": "data.recordList.applyorders_invExchRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_productOfflineRetail_purchaseRate": {"api_field_name": "applyorders_productOfflineRetail_purchaseRate", "chinese_name": "采购单位换算率,只有明细场景返回", "data_type": "BIGINT", "param_desc": "采购单位换算率,只有明细场景返回", "path": "data.recordList.applyorders_productOfflineRetail_purchaseRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "priceUOM": {"api_field_name": "priceUOM", "chinese_name": "计价单位id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "计价单位id,只有明细场景返回", "path": "data.recordList.priceUOM", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "priceUOM_Name": {"api_field_name": "priceUOM_Name", "chinese_name": "计价单位,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "计价单位,只有明细场景返回", "path": "data.recordList.priceUOM_Name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invPriceExchRate": {"api_field_name": "invPriceExchRate", "chinese_name": "计价换算率,只有明细场景返回", "data_type": "BIGINT", "param_desc": "计价换算率,只有明细场景返回", "path": "data.recordList.invPriceExchRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unitExchangeTypePrice": {"api_field_name": "unitExchangeTypePrice", "chinese_name": "计价单位转换率的换算方式,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "计价单位转换率的换算方式,只有明细场景返回", "path": "data.recordList.unitExchangeTypePrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "priceUOM_Precision": {"api_field_name": "priceUOM_Precision", "chinese_name": "计价单位精度,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "计价单位精度,只有明细场景返回", "path": "data.recordList.priceUOM_Precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "taxRate": {"api_field_name": "taxRate", "chinese_name": "税率,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "税率,只有明细场景返回", "path": "data.recordList.taxRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriTax": {"api_field_name": "oriTax", "chinese_name": "税额,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "税额,只有明细场景返回", "path": "data.recordList.oriTax", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriTaxUnitPrice": {"api_field_name": "oriTaxUnitPrice", "chinese_name": "含税单价,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "含税单价,只有明细场景返回", "path": "data.recordList.oriTaxUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriUnitPrice": {"api_field_name": "oriUnitPrice", "chinese_name": "无税单价,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "无税单价,只有明细场景返回", "path": "data.recordList.oriUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriMoney": {"api_field_name": "oriMoney", "chinese_name": "无税金额,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "无税金额,只有明细场景返回", "path": "data.recordList.oriMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "oriSum": {"api_field_name": "oriSum", "chinese_name": "含税金额,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "含税金额,只有明细场景返回", "path": "data.recordList.oriSum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_product_primeCosts": {"api_field_name": "applyorders_product_primeCosts", "chinese_name": "进货价格,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "进货价格,只有明细场景返回", "path": "data.recordList.applyorders_product_primeCosts", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_productsku_primeCosts": {"api_field_name": "applyorders_productsku_primeCosts", "chinese_name": "sku进货价格,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "sku进货价格,只有明细场景返回", "path": "data.recordList.applyorders_productsku_primeCosts", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_requirementDate": {"api_field_name": "applyorders_requirementDate", "chinese_name": "需求日期,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "需求日期,只有明细场景返回", "path": "data.recordList.applyorders_requirementDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_adviseOrderDate": {"api_field_name": "applyorders_adviseOrderDate", "chinese_name": "建议订货日期,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "建议订货日期,只有明细场景返回", "path": "data.recordList.applyorders_adviseOrderDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_adviseSupplier": {"api_field_name": "applyorders_adviseSupplier", "chinese_name": "建议供应商id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "建议供应商id,只有明细场景返回", "path": "data.recordList.applyorders_adviseSupplier", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_adviseSupplier_name": {"api_field_name": "applyorders_adviseSupplier_name", "chinese_name": "建议供应商名称,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "建议供应商名称,只有明细场景返回", "path": "data.recordList.applyorders_adviseSupplier_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_vendor": {"api_field_name": "applyorders_vendor", "chinese_name": "建议供应商id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "建议供应商id,只有明细场景返回", "path": "data.recordList.applyorders_vendor", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyorders_vendor_name": {"api_field_name": "applyorders_vendor_name", "chinese_name": "建议供应商名称,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "建议供应商名称,只有明细场景返回", "path": "data.recordList.applyorders_vendor_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_memo": {"api_field_name": "applyorders_memo", "chinese_name": "备注,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "备注,只有明细场景返回", "path": "data.recordList.applyorders_memo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bodyItem": {"api_field_name": "bodyItem", "chinese_name": "表体自定义项", "data_type": "NVARCHAR(MAX)", "param_desc": "表体自定义项", "path": "data.recordList.bodyItem", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define56": {"api_field_name": "define56", "chinese_name": "表体自定义项56,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项56,只有明细场景返回", "path": "data.recordList.bodyItem.define56", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define57": {"api_field_name": "define57", "chinese_name": "表体自定义项57,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项57,只有明细场景返回", "path": "data.recordList.bodyItem.define57", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define58": {"api_field_name": "define58", "chinese_name": "表体自定义项58,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项58,只有明细场景返回", "path": "data.recordList.bodyItem.define58", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define59": {"api_field_name": "define59", "chinese_name": "表体自定义项59,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项59,只有明细场景返回", "path": "data.recordList.bodyItem.define59", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define60": {"api_field_name": "define60", "chinese_name": "表体自定义项60,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项60,只有明细场景返回", "path": "data.recordList.bodyItem.define60", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define4": {"api_field_name": "define4", "chinese_name": "表体自定义项4,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项4,只有明细场景返回", "path": "data.recordList.bodyItem.define4", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define31": {"api_field_name": "define31", "chinese_name": "表体自定义项31,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项31,只有明细场景返回", "path": "data.recordList.bodyItem.define31", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define32": {"api_field_name": "define32", "chinese_name": "表体自定义项32,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项32,只有明细场景返回", "path": "data.recordList.bodyItem.define32", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define33": {"api_field_name": "define33", "chinese_name": "表体自定义项33,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项33,只有明细场景返回", "path": "data.recordList.bodyItem.define33", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define34": {"api_field_name": "define34", "chinese_name": "表体自定义项34,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项34,只有明细场景返回", "path": "data.recordList.bodyItem.define34", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define35": {"api_field_name": "define35", "chinese_name": "表体自定义项35,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项35,只有明细场景返回", "path": "data.recordList.bodyItem.define35", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define36": {"api_field_name": "define36", "chinese_name": "表体自定义项36,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项36,只有明细场景返回", "path": "data.recordList.bodyItem.define36", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define37": {"api_field_name": "define37", "chinese_name": "表体自定义项37,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项37,只有明细场景返回", "path": "data.recordList.bodyItem.define37", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define38": {"api_field_name": "define38", "chinese_name": "表体自定义项38,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项38,只有明细场景返回", "path": "data.recordList.bodyItem.define38", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define39": {"api_field_name": "define39", "chinese_name": "表体自定义项39,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项39,只有明细场景返回", "path": "data.recordList.bodyItem.define39", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define40": {"api_field_name": "define40", "chinese_name": "表体自定义项40,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项40,只有明细场景返回", "path": "data.recordList.bodyItem.define40", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define41": {"api_field_name": "define41", "chinese_name": "表体自定义项41,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项41,只有明细场景返回", "path": "data.recordList.bodyItem.define41", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define42": {"api_field_name": "define42", "chinese_name": "表体自定义项42,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项42,只有明细场景返回", "path": "data.recordList.bodyItem.define42", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define43": {"api_field_name": "define43", "chinese_name": "表体自定义项43,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项43,只有明细场景返回", "path": "data.recordList.bodyItem.define43", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define44": {"api_field_name": "define44", "chinese_name": "表体自定义项44,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项44,只有明细场景返回", "path": "data.recordList.bodyItem.define44", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define45": {"api_field_name": "define45", "chinese_name": "表体自定义项45,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项45,只有明细场景返回", "path": "data.recordList.bodyItem.define45", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define46": {"api_field_name": "define46", "chinese_name": "表体自定义项46,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项46,只有明细场景返回", "path": "data.recordList.bodyItem.define46", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define47": {"api_field_name": "define47", "chinese_name": "表体自定义项47,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项47,只有明细场景返回", "path": "data.recordList.bodyItem.define47", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define48": {"api_field_name": "define48", "chinese_name": "表体自定义项48,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项48,只有明细场景返回", "path": "data.recordList.bodyItem.define48", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define49": {"api_field_name": "define49", "chinese_name": "表体自定义项49,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项49,只有明细场景返回", "path": "data.recordList.bodyItem.define49", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define50": {"api_field_name": "define50", "chinese_name": "表体自定义项50,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项50,只有明细场景返回", "path": "data.recordList.bodyItem.define50", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define51": {"api_field_name": "define51", "chinese_name": "表体自定义项51,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项51,只有明细场景返回", "path": "data.recordList.bodyItem.define51", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define52": {"api_field_name": "define52", "chinese_name": "表体自定义项52,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项52,只有明细场景返回", "path": "data.recordList.bodyItem.define52", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define53": {"api_field_name": "define53", "chinese_name": "表体自定义项53,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项53,只有明细场景返回", "path": "data.recordList.bodyItem.define53", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define54": {"api_field_name": "define54", "chinese_name": "表体自定义项54,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项54,只有明细场景返回", "path": "data.recordList.bodyItem.define54", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define55": {"api_field_name": "define55", "chinese_name": "表体自定义项55,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项55,只有明细场景返回", "path": "data.recordList.bodyItem.define55", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define5": {"api_field_name": "define5", "chinese_name": "表体自定义项5,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项5,只有明细场景返回", "path": "data.recordList.bodyItem.define5", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define6": {"api_field_name": "define6", "chinese_name": "表体自定义项6,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项6,只有明细场景返回", "path": "data.recordList.bodyItem.define6", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define7": {"api_field_name": "define7", "chinese_name": "表体自定义项7,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项7,只有明细场景返回", "path": "data.recordList.bodyItem.define7", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define8": {"api_field_name": "define8", "chinese_name": "表体自定义项8,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项8,只有明细场景返回", "path": "data.recordList.bodyItem.define8", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define9": {"api_field_name": "define9", "chinese_name": "表体自定义项9,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项9,只有明细场景返回", "path": "data.recordList.bodyItem.define9", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define10": {"api_field_name": "define10", "chinese_name": "表体自定义项10,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项10,只有明细场景返回", "path": "data.recordList.bodyItem.define10", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define11": {"api_field_name": "define11", "chinese_name": "表体自定义项11,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项11,只有明细场景返回", "path": "data.recordList.bodyItem.define11", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define12": {"api_field_name": "define12", "chinese_name": "表体自定义项12,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项12,只有明细场景返回", "path": "data.recordList.bodyItem.define12", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define13": {"api_field_name": "define13", "chinese_name": "表体自定义项13,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项13,只有明细场景返回", "path": "data.recordList.bodyItem.define13", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define14": {"api_field_name": "define14", "chinese_name": "表体自定义项14,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项14,只有明细场景返回", "path": "data.recordList.bodyItem.define14", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define15": {"api_field_name": "define15", "chinese_name": "表体自定义项15,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项15,只有明细场景返回", "path": "data.recordList.bodyItem.define15", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define16": {"api_field_name": "define16", "chinese_name": "表体自定义项16,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项16,只有明细场景返回", "path": "data.recordList.bodyItem.define16", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define17": {"api_field_name": "define17", "chinese_name": "表体自定义项17,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项17,只有明细场景返回", "path": "data.recordList.bodyItem.define17", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define18": {"api_field_name": "define18", "chinese_name": "表体自定义项18,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项18,只有明细场景返回", "path": "data.recordList.bodyItem.define18", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define19": {"api_field_name": "define19", "chinese_name": "表体自定义项19,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项19,只有明细场景返回", "path": "data.recordList.bodyItem.define19", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define20": {"api_field_name": "define20", "chinese_name": "表体自定义项20,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项20,只有明细场景返回", "path": "data.recordList.bodyItem.define20", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define21": {"api_field_name": "define21", "chinese_name": "表体自定义项21,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项21,只有明细场景返回", "path": "data.recordList.bodyItem.define21", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define22": {"api_field_name": "define22", "chinese_name": "表体自定义项22,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项22,只有明细场景返回", "path": "data.recordList.bodyItem.define22", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define23": {"api_field_name": "define23", "chinese_name": "表体自定义项23,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项23,只有明细场景返回", "path": "data.recordList.bodyItem.define23", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define24": {"api_field_name": "define24", "chinese_name": "表体自定义项24,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项24,只有明细场景返回", "path": "data.recordList.bodyItem.define24", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define25": {"api_field_name": "define25", "chinese_name": "表体自定义项25,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项25,只有明细场景返回", "path": "data.recordList.bodyItem.define25", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define26": {"api_field_name": "define26", "chinese_name": "表体自定义项26,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项26,只有明细场景返回", "path": "data.recordList.bodyItem.define26", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define27": {"api_field_name": "define27", "chinese_name": "表体自定义项27,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项27,只有明细场景返回", "path": "data.recordList.bodyItem.define27", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define28": {"api_field_name": "define28", "chinese_name": "表体自定义项28,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项28,只有明细场景返回", "path": "data.recordList.bodyItem.define28", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define29": {"api_field_name": "define29", "chinese_name": "表体自定义项29,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项29,只有明细场景返回", "path": "data.recordList.bodyItem.define29", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define30": {"api_field_name": "define30", "chinese_name": "表体自定义项30,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项30,只有明细场景返回", "path": "data.recordList.bodyItem.define30", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_productsku_modelDescription": {"api_field_name": "applyorders_productsku_modelDescription", "chinese_name": "sku规格型号,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "sku规格型号,只有明细场景返回", "path": "data.recordList.applyorders_productsku_modelDescription", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_product_model": {"api_field_name": "applyorders_product_model", "chinese_name": "型号,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "型号,只有明细场景返回", "path": "data.recordList.applyorders_product_model", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_product_modelDescription": {"api_field_name": "applyorders_product_modelDescription", "chinese_name": "规格说明,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "规格说明,只有明细场景返回", "path": "data.recordList.applyorders_product_modelDescription", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_propertiesValue": {"api_field_name": "applyorders_propertiesValue", "chinese_name": "规格,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "规格,只有明细场景返回", "path": "data.recordList.applyorders_propertiesValue", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "project": {"api_field_name": "project", "chinese_name": "项目id,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "项目id,只有明细场景返回", "path": "data.recordList.project", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "project_code": {"api_field_name": "project_code", "chinese_name": "项目编码,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "项目编码,只有明细场景返回", "path": "data.recordList.project_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define1": {"api_field_name": "applyOrders_vendor_define1", "chinese_name": "供应商自定义项1,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项1,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define1", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "project_name": {"api_field_name": "project_name", "chinese_name": "项目名称,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "项目名称,只有明细场景返回", "path": "data.recordList.project_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define2": {"api_field_name": "applyOrders_vendor_define2", "chinese_name": "供应商自定义项2,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项2,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define2", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyorders_trackNo": {"api_field_name": "applyorders_trackNo", "chinese_name": "跟踪号,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "跟踪号,只有明细场景返回", "path": "data.recordList.applyorders_trackNo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define3": {"api_field_name": "applyOrders_vendor_define3", "chinese_name": "供应商自定义项3,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项3,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define3", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define4": {"api_field_name": "applyOrders_vendor_define4", "chinese_name": "供应商自定义项4,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项4,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define4", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define5": {"api_field_name": "applyOrders_vendor_define5", "chinese_name": "供应商自定义项5,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项5,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define5", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define6": {"api_field_name": "applyOrders_vendor_define6", "chinese_name": "供应商自定义项6,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项6,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define6", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define7": {"api_field_name": "applyOrders_vendor_define7", "chinese_name": "供应商自定义项7,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项7,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define7", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define8": {"api_field_name": "applyOrders_vendor_define8", "chinese_name": "供应商自定义项8,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项8,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define8", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define9": {"api_field_name": "applyOrders_vendor_define9", "chinese_name": "供应商自定义项9,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项9,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define9", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define10": {"api_field_name": "applyOrders_vendor_define10", "chinese_name": "供应商自定义项10,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项10,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define10", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define11": {"api_field_name": "applyOrders_vendor_define11", "chinese_name": "供应商自定义项11,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项11,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define11", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define12": {"api_field_name": "applyOrders_vendor_define12", "chinese_name": "供应商自定义项12,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项12,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define12", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define13": {"api_field_name": "applyOrders_vendor_define13", "chinese_name": "供应商自定义项13,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项13,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define13", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define14": {"api_field_name": "applyOrders_vendor_define14", "chinese_name": "供应商自定义项14,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项14,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define14", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define15": {"api_field_name": "applyOrders_vendor_define15", "chinese_name": "供应商自定义项15,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项15,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define15", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define16": {"api_field_name": "applyOrders_vendor_define16", "chinese_name": "供应商自定义项16,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项16,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define16", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define17": {"api_field_name": "applyOrders_vendor_define17", "chinese_name": "供应商自定义项17,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项17,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define17", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define18": {"api_field_name": "applyOrders_vendor_define18", "chinese_name": "供应商自定义项18,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项18,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define18", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define19": {"api_field_name": "applyOrders_vendor_define19", "chinese_name": "供应商自定义项19,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项19,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define19", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define20": {"api_field_name": "applyOrders_vendor_define20", "chinese_name": "供应商自定义项20,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项20,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define20", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define21": {"api_field_name": "applyOrders_vendor_define21", "chinese_name": "供应商自定义项21,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项21,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define21", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define22": {"api_field_name": "applyOrders_vendor_define22", "chinese_name": "供应商自定义项22,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项22,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define22", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define23": {"api_field_name": "applyOrders_vendor_define23", "chinese_name": "供应商自定义项23,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项23,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define23", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define24": {"api_field_name": "applyOrders_vendor_define24", "chinese_name": "供应商自定义项24,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项24,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define24", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define25": {"api_field_name": "applyOrders_vendor_define25", "chinese_name": "供应商自定义项25,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项25,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define25", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define26": {"api_field_name": "applyOrders_vendor_define26", "chinese_name": "供应商自定义项26,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项26,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define26", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define27": {"api_field_name": "applyOrders_vendor_define27", "chinese_name": "供应商自定义项27,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项27,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define27", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define28": {"api_field_name": "applyOrders_vendor_define28", "chinese_name": "供应商自定义项28,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项28,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define28", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define29": {"api_field_name": "applyOrders_vendor_define29", "chinese_name": "供应商自定义项29,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项29,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define29", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "applyOrders_vendor_define30": {"api_field_name": "applyOrders_vendor_define30", "chinese_name": "供应商自定义项30,只有明细场景返回", "data_type": "NVARCHAR(500)", "param_desc": "供应商自定义项30,只有明细场景返回", "path": "data.recordList.applyOrders_vendor_define30", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product": {"api_field_name": "product", "chinese_name": "物料id", "data_type": "BIGINT", "param_desc": "物料id", "path": "product", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isSum": {"api_field_name": "isSum", "chinese_name": "是否按照表头查询：true:表头、false:表头+明细", "data_type": "BIT", "param_desc": "是否按照表头查询：true:表头、false:表头+明细", "path": "isSum", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "queryOrders": {"api_field_name": "queryOrders", "chinese_name": "排序字段", "data_type": "NVARCHAR(MAX)", "param_desc": "排序字段", "path": "queryOrders", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "field": {"api_field_name": "field", "chinese_name": "属性名(条件) ，1：status(单据状态：0:未审核、1:已审核、2:已关闭、3:审核中)、2：code(单据编号)、3：vouchdate(请购日期)、4：operator(请购员id)、5：org(需求组织id)、6；applyOrders.purchaseOrg(采购组织id)、7：applyOrders.vendor(建议供应商id)、 8：applyDept(请购部门id)、9：headItem.define1(单据头自定义项1)、10：applyOrders.adviseOrderDate(建议订货日期)、11：applyOrders.project(项目id)", "data_type": "NVARCHAR(500)", "param_desc": "属性名(条件) ，1：status(单据状态：0:未审核、1:已审核、2:已关闭、3:审核中)、2：code(单据编号)、3：vouchdate(请购日期)、4：operator(请购员id)、5：org(需求组织id)、6；applyOrders.purchaseOrg(采购组织id)、7：applyOrders.vendor(建议供应商id)、 8：applyDept(请购部门id)、9：headItem.define1(单据头自定义项1)、10：applyOrders.adviseOrderDate(建议订货日期)、11：applyOrders.project(项目id)", "path": "simpleVOs.field", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "order": {"api_field_name": "order", "chinese_name": "顺序：asc：正序、desc：倒序", "data_type": "NVARCHAR(500)", "param_desc": "顺序：asc：正序、desc：倒序", "path": "queryOrders.order", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "simpleVOs": {"api_field_name": "simpleVOs", "chinese_name": "扩展条件查询", "data_type": "NVARCHAR(MAX)", "param_desc": "扩展条件查询", "path": "simpleVOs", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "op": {"api_field_name": "op", "chinese_name": "比较符（条件）：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者", "data_type": "NVARCHAR(500)", "param_desc": "比较符（条件）：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者", "path": "simpleVOs.op", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value1": {"api_field_name": "value1", "chinese_name": "值1（条件）", "data_type": "NVARCHAR(500)", "param_desc": "值1（条件）", "path": "simpleVOs.value1", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}}}