计划订单列表查询
发布时间:2025-01-06 15:08:40
提供按条件分页查询计划订单列表信息的服务。

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	动态域名，获取方式详见 获取租户所在数据中心域名
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/mfg/requirementsplanning/getPlanOrderList
请求方式	POST
ContentType	application/json
应用场景	开放API
API类别	
事务和幂等性	无
限流次数	
当前API限流次数暂未透出

2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
data	object	否	是	参数
pageIndex	number
小数位数:-1,最大长度:8	否	是	页号    示例: 1    默认值: 1
pageSize	number
小数位数:-1,最大长度:4	否	是	每页条数    示例: 10    默认值: 10
orgId	string	否	否	组织id，组织 Id 与组织编码不能同时为空，优先级：orgId>orgCode    示例: 1608788551787872266
orgCode	string	否	否	组织编码，组织 Id 与组织编码不能同时为空，优先级：orgId>orgCode    示例: 122701
planParamId	string	否	否	计划名称id，优先级：planParamId > planParamCode    示例: 3115402872983808
planParamCode	string	否	否	计划名称编码，优先级：planParamId > planParamCode    示例: MRP2019110200002
startDate	date
格式:yyyy-MM-dd	否	是	开始时间    示例: 2022-01-10
endDate	date
格式:yyyy-MM-dd	否	是	结束时间    示例: 2022-01-10
supplyOrgId	string	否	否	供应组织id，优先级：supplyOrgId > supplyOrgCode    示例: 1608788551787872266
supplyOrgCode	string	否	否	供应组织编码，优先级：supplyOrgId > supplyOrgCode    示例: 122701
departmentId	string	否	否	部门id，优先级：departmentId > departmentCode    示例: 1608788551787872266
departmentCode	string	否	否	部门编码，优先级：departmentId > departmentCode    示例: bumen
warehouseId	string	否	否	仓库Id，优先级：warehouseId > warehouseCode    示例: 1608788551787872266
warehouseCode	string	否	否	仓库编码，优先级:warehouseId > warehouseCode    示例: 000012
productIds	string	是	否	物料id，优先级:productIds > productCodes    示例: ["1550141821342973955","1550139381815705608"]
productCodes	string	是	否	物料编码，优先级:productIds > productCodes    示例: ["000123","000124"]
planProperty	string	是	否	计划属性 1,采购 ,2,委外 ,3,自制 ,4,调拨 ,5,组织间需求    示例: ["1","2"]
3. 请求示例
Url: /yonbip/mfg/requirementsplanning/getPlanOrderList?access_token=访问令牌
Body: {
	"data": {
		"pageIndex": 1,
		"pageSize": 10,
		"orgId": "1608788551787872266",
		"orgCode": "122701",
		"planParamId": "3115402872983808",
		"planParamCode": "MRP2019110200002",
		"startDate": "2022-01-10",
		"endDate": "2022-01-10",
		"supplyOrgId": "1608788551787872266",
		"supplyOrgCode": "122701",
		"departmentId": "1608788551787872266",
		"departmentCode": "bumen",
		"warehouseId": "1608788551787872266",
		"warehouseCode": "000012",
		"productIds": [
			"1550141821342973955",
			"1550139381815705608"
		],
		"productCodes": [
			"000123",
			"000124"
		],
		"planProperty": [
			"1",
			"2"
		]
	}
}
4. 返回值参数
名称	类型	数组	描述
code	string	否	返回状态码，200 成功 999 失败
message	string	否	操作通知信息
data	object	是	数据
recordList	object	否	数据信息
id	string	否	计划订单id
orgId	string	否	计划组织id
orgCode	string	否	计划组织编码
transTypeId	string	否	交易类型id
transTypeCode	string	否	交易类型编码
planParamId	string	否	计划名称id
planParamCode	string	否	计划名称编码
planParamName	string	否	计划名称
createType	string	否	创建类型
code	string	否	计划订单号
productCode	string	否	物料编码
productId	string	否	物料id
planProperty	string	否	计划属性
bomId	string	否	BOM唯一标识
bomCode	string	否	BOM编码
uom	string	否	单位
uomName	string	否	单位名称
uomCode	string	否	单位编码
assistUnit	string	否	主计量单位
assistUnitCode	string	否	主计量单位编码
assistUnitName	string	否	主计量单位名称
originQuantity	number
小数位数:8,最大长度:28	否	原始数量
assistUnitCount	number
小数位数:8,最大长度:28	否	主计量计划量
suggestPlanQuantity	number
小数位数:8,最大长度:28	否	建议计划量
inputQty	number
小数位数:8,最大长度:28	否	投入计划量
issuedQuantity	number
小数位数:8,最大长度:28	否	已下达量
startDate	string	否	开工日期
finishDate	string	否	完工日期
status	string	否	状态
demandOrgId	string	否	需求组织
demandOrgCode	string	否	需求组织编码
supplyOrgId	string	否	供应组织
supplyOrgCode	string	否	供应组织编码
invOrgId	string	否	入库组织
invOrgCode	string	否	入库组织编码
source	string	否	来源单据类型
upcode	string	否	来源单据号
srcSourceProductId	string	否	来源物料id
srcSourceProductCode	string	否	来源物料编码
firstsource	string	否	源头单据类型
firstupcode	string	否	源头单据号
firstsourceautoid	string	否	源头单据子表id
sourceMaterialId	string	否	源头物料
sourceMaterialCode	string	否	源头物料编码
departmentId	string	否	部门id
departmentCode	string	否	部门编码
departmentName	string	否	部门名称
warehouseId	string	否	仓库id
warehouseCode	string	否	仓库编码
warehouseName	string	否	仓库名称
isClosed	boolean	否	关闭标识
remark	string	否	备注
projectId	string	否	项目id
projectIdCode	string	否	项目编码
projectIdName	string	否	项目名称
wbs	string	否	wbs任务id
wbsCode	string	否	wbs任务编码
wbsName	string	否	wbs任务名称
activity	string	否	活动id
activityCode	string	否	活动编码
activityName	string	否	活动名称
planOrderItem	object	是	计划订单备料
itemProductId	string	否	物料id
itemProductCode	string	否	物料编码
itemProductName	string	否	物料名称
mainUnitId	string	否	主计量id
mainUnitCode	string	否	主计量编码
mainUnitName	string	否	主计量名称
stockUnitId	string	否	BOM单位id
stockUnitCode	string	否	BOM单位编码
stockUnitName	string	否	BOM单位名称
changeRate	number
小数位数:8,最大长度:28	否	换算率
requirementQuantity	number
小数位数:8,最大长度:28	否	需求数量
auxiliaryRequirementQuantity	number
小数位数:8,最大长度:28	否	需求件数
stockOrgId	string	否	库存单位id
stockOrgCode	string	否	库存单位编码
stockOrgName	string	否	库存单位名称
warehouseId	string	否	仓库id
warehouseCode	string	否	仓库编码
warehouseName	string	否	仓库名称
reqDate	date
格式:yyyy-MM-dd HH:mm:ss	否	需求日期
remark	string	否	备注
substituteFlag	string	否	替代标识
itemUserDefineCharacter	特征组
mr.planworkbench.PlanOrderItem	否	自定义项特征
XS11	string	否	需求分类号test
XS15	string	否	顾客订单号（订单表体）
id	string	否	特征id,主键,新增时无需填写,修改时必填
itemFreeCharacteristics	特征组
mr.planworkbench.PlanOrderItem	否	自由项特征
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
projectId	string	否	项目id
projectIdCode	string	否	项目编码
projectIdName	string	否	项目名称
wbs	string	否	wbs任务id
wbsCode	string	否	wbs任务编码
wbsName	string	否	wbs任务名称
activity	string	否	活动id
activityCode	string	否	活动编码
activityName	string	否	活动名称
reserveid	string	否	跟踪线索id
reserveTypeName	string	否	需求跟踪方式
reserveName	string	否	跟踪线索
pageIndex	number
小数位数:0,最大长度:10	否	当前页码
pageSize	number
小数位数:0,最大长度:10	否	每页条数
recordCount	number
小数位数:0,最大长度:10	否	总条数
pageCount	number
小数位数:0,最大长度:10	否	页码数
beginPageIndex	number
小数位数:0,最大长度:10	否	开始页码
endPageIndex	number
小数位数:0,最大长度:10	否	结束页码
5. 正确返回示例
{
	"code": "200",
	"message": "操作成功",
	"data": [
		{
			"recordList": {
				"id": "1630874665323855906",
				"orgId": "1608788551787872266",
				"orgCode": "02301070001",
				"transTypeId": "1601383117764427779",
				"transTypeCode": "GEN-11",
				"planParamId": "3035900830109952",
				"planParamCode": "GEN0",
				"planParamName": "LRP202301070001",
				"createType": "2",
				"code": "GEN00011",
				"productCode": "000045",
				"productId": "159192034273814118",
				"planProperty": "1",
				"bomId": "1591920342738141189",
				"bomCode": "likun-M.Code-002（固）",
				"uom": "1570768779829837833",
				"uomName": "千克",
				"uomCode": "kg",
				"assistUnit": "1570768797009707017",
				"assistUnitCode": "kg",
				"assistUnitName": "千克",
				"originQuantity": 10,
				"assistUnitCount": 10,
				"suggestPlanQuantity": 10,
				"inputQty": 10,
				"issuedQuantity": 0,
				"startDate": "2023-01-06 00:00:00",
				"finishDate": "2023-01-06 00:00:00",
				"status": "0",
				"demandOrgId": "1570766056850456576",
				"demandOrgCode": "00310",
				"supplyOrgId": "1570766056850456576",
				"supplyOrgCode": "00310",
				"invOrgId": "1570766056850456576",
				"invOrgCode": "00310",
				"source": "10",
				"upcode": "YCD20221228000006",
				"srcSourceProductId": "1570766056850456576",
				"srcSourceProductCode": "1035000045",
				"firstsource": "280",
				"firstupcode": "YCD20221228000006",
				"firstsourceautoid": "1623614151955316744",
				"sourceMaterialId": "1590995696384737289",
				"sourceMaterialCode": "1035000045",
				"departmentId": "1570766056850456576",
				"departmentCode": "001111",
				"departmentName": "部门1",
				"warehouseId": "1570766056850456576",
				"warehouseCode": "001111",
				"warehouseName": "仓库1",
				"isClosed": false,
				"remark": "remark",
				"projectId": "1570766056850456576",
				"projectIdCode": "001111",
				"projectIdName": "项目一号",
				"wbs": "1570766056850456576",
				"wbsCode": "001111",
				"wbsName": "wbs任务一号",
				"activity": "1570766056850456576",
				"activityCode": "001111",
				"activityName": "活动一号",
				"planOrderItem": [
					{
						"itemProductId": "1681453239576297481",
						"itemProductCode": "wlfl014",
						"itemProductName": "WC1",
						"mainUnitId": "1674787939942400002",
						"mainUnitCode": "MKT",
						"mainUnitName": "立方米",
						"stockUnitId": "1674787939942400004",
						"stockUnitCode": "MTQ",
						"stockUnitName": "平方千米",
						"changeRate": 1,
						"requirementQuantity": 5.01,
						"auxiliaryRequirementQuantity": 5.01,
						"stockOrgId": "1681369238604349442",
						"stockOrgCode": "zzw",
						"stockOrgName": "库存w",
						"warehouseId": "1681372373925232646",
						"warehouseCode": "w2",
						"warehouseName": "仓库w2",
						"reqDate": "2024-04-08 23:59:59",
						"remark": "备注",
						"substituteFlag": "0",
						"itemUserDefineCharacter": {
							"ytenant": "0000LDTXR6979CPCME0000",
							"id": "",
							"dadw": false,
							"wsz": 1,
							"XS11": "",
							"XS15": ""
						},
						"itemFreeCharacteristics": {
							"0": "{",
							"1": " ",
							"2": " ",
							"3": " ",
							"4": " ",
							"5": " ",
							"6": " ",
							"7": " ",
							"8": " ",
							"9": " ",
							"10": " ",
							"11": " ",
							"12": " ",
							"13": " ",
							"14": " ",
							"15": " ",
							"16": " ",
							"17": " ",
							"18": " ",
							"19": " ",
							"20": " ",
							"21": " ",
							"22": " ",
							"23": " ",
							"24": " ",
							"25": " ",
							"26": "\"",
							"27": "w",
							"28": "j",
							"29": "b",
							"30": "d",
							"31": "a",
							"32": "\"",
							"33": ":",
							"34": " ",
							"35": "\"",
							"36": "1",
							"37": "6",
							"38": "5",
							"39": "4",
							"40": "0",
							"41": "4",
							"42": "7",
							"43": "7",
							"44": "7",
							"45": "4",
							"46": "8",
							"47": "1",
							"48": "7",
							"49": "1",
							"50": "2",
							"51": "4",
							"52": "3",
							"53": "6",
							"54": "7",
							"55": "\"",
							"56": ",",
							"57": " ",
							"58": " ",
							"59": " ",
							"60": " ",
							"61": " ",
							"62": " ",
							"63": " ",
							"64": " ",
							"65": " ",
							"66": " ",
							"67": " ",
							"68": " ",
							"69": " ",
							"70": " ",
							"71": " ",
							"72": " ",
							"73": " ",
							"74": " ",
							"75": " ",
							"76": " ",
							"77": " ",
							"78": " ",
							"79": " ",
							"80": " ",
							"81": " ",
							"82": "\"",
							"83": "y",
							"84": "t",
							"85": "e",
							"86": "n",
							"87": "a",
							"88": "n",
							"89": "t",
							"90": "\"",
							"91": ":",
							"92": " ",
							"93": "\"",
							"94": "0",
							"95": "0",
							"96": "0",
							"97": "0",
							"98": "L",
							"99": "D",
							"100": "T",
							"101": "X",
							"102": "R",
							"103": "6",
							"104": "9",
							"105": "7",
							"106": "9",
							"107": "C",
							"108": "P",
							"109": "C",
							"110": "M",
							"111": "E",
							"112": "0",
							"113": "0",
							"114": "0",
							"115": "0",
							"116": "\"",
							"117": ",",
							"118": " ",
							"119": " ",
							"120": " ",
							"121": " ",
							"122": " ",
							"123": " ",
							"124": " ",
							"125": " ",
							"126": " ",
							"127": " ",
							"128": " ",
							"129": " ",
							"130": " ",
							"131": " ",
							"132": " ",
							"133": " ",
							"134": " ",
							"135": " ",
							"136": " ",
							"137": " ",
							"138": " ",
							"139": " ",
							"140": " ",
							"141": " ",
							"142": " ",
							"143": "\"",
							"144": "i",
							"145": "d",
							"146": "\"",
							"147": ":",
							"148": " ",
							"149": "\"",
							"150": "1",
							"151": "9",
							"152": "7",
							"153": "0",
							"154": "2",
							"155": "4",
							"156": "9",
							"157": "4",
							"158": "3",
							"159": "4",
							"160": "7",
							"161": "1",
							"162": "7",
							"163": "4",
							"164": "8",
							"165": "7",
							"166": "2",
							"167": "1",
							"168": "4",
							"169": "\"",
							"170": ",",
							"171": " ",
							"172": " ",
							"173": " ",
							"174": " ",
							"175": " ",
							"176": " ",
							"177": " ",
							"178": " ",
							"179": " ",
							"180": " ",
							"181": " ",
							"182": " ",
							"183": " ",
							"184": " ",
							"185": " ",
							"186": " ",
							"187": " ",
							"188": " ",
							"189": " ",
							"190": " ",
							"191": " ",
							"192": " ",
							"193": " ",
							"194": " ",
							"195": " ",
							"196": "\"",
							"197": "w",
							"198": "b",
							"199": "e",
							"200": "\"",
							"201": ":",
							"202": " ",
							"203": "f",
							"204": "a",
							"205": "l",
							"206": "s",
							"207": "e",
							"208": ",",
							"209": " ",
							"210": " ",
							"211": " ",
							"212": " ",
							"213": " ",
							"214": " ",
							"215": " ",
							"216": " ",
							"217": " ",
							"218": " ",
							"219": " ",
							"220": " ",
							"221": " ",
							"222": " ",
							"223": " ",
							"224": " ",
							"225": " ",
							"226": " ",
							"227": " ",
							"228": " ",
							"229": " ",
							"230": " ",
							"231": " ",
							"232": " ",
							"233": " ",
							"234": "\"",
							"235": "p",
							"236": "u",
							"237": "b",
							"238": "t",
							"239": "s",
							"240": "\"",
							"241": ":",
							"242": " ",
							"243": "\"",
							"244": "2",
							"245": "0",
							"246": "2",
							"247": "4",
							"248": "-",
							"249": "0",
							"250": "4",
							"251": "-",
							"252": "0",
							"253": "8",
							"XS15": "",
							"XXX0111": "",
							"id": ""
						},
						"projectId": "1654046039626743861",
						"projectIdCode": "pj1",
						"projectIdName": "项目名称",
						"wbs": "1570766056850456576",
						"wbsCode": "00001111",
						"wbsName": "wbs任务一号",
						"activity": "1570766056850456575",
						"activityCode": "001111",
						"activityName": "活动一号",
						"reserveid": "1570766056850456574",
						"reserveTypeName": "自定义",
						"reserveName": "跟踪线索"
					}
				]
			},
			"pageIndex": 1,
			"pageSize": 10,
			"recordCount": 13,
			"pageCount": 2,
			"beginPageIndex": 1,
			"endPageIndex": 1
		}
	]
}
6. 错误返回码
错误码	错误信息	描述
310008	取决于错误类型，不同错误信息不同	
7. 错误返回示例
{
    "code": "310008",
    "message": "参数校验失败，参数[data]是必填的。"
}