import asyncio
import time
from dataclasses import dataclass
from enum import Enum

import structlog

"""
YS-API V3.0 增强错误处理系统
Month 3 Week 1: 错误处理增强 - 目标覆盖率≥90%
"""


logger = structlog.get_logger()


class ErrorCategory(Enum):
    """错误分类枚举"""
    NETWORK = "network"          # 网络连接错误
    AUTH = "auth"               # 认证授权错误
    SERVER = "server"           # 服务器错误
    CLIENT = "client"           # 客户端错误
    RATE_LIMIT = "rate_limit"   # 限流错误
    DATA = "data"               # 数据解析错误
    TIMEOUT = "timeout"         # 超时错误
    UNKNOWN = "unknown"         # 未知错误


class RecoveryStrategy(Enum):
    """恢复策略枚举"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    TOKEN_REFRESH = "token_refresh"
    DELAYED_RETRY = "delayed_retry"
    RATE_LIMIT_WAIT = "rate_limit_wait"
    RESPONSE_REPAIR = "response_repair"
    CIRCUIT_BREAK = "circuit_break"
    NO_RETRY = "no_retry"


@dataclass
class ErrorInfo:
    """错误信息结构"""
    category: ErrorCategory
    message: str
    original_exception: Exception
    recovery_strategy: RecoveryStrategy
    retry_count: int = 0
    max_retries: int = 3
    retry_delay: float = 1.0
    context: Dict[str, Any] = None


class EnhancedErrorHandler:
    """增强错误处理器 - 目标覆盖率≥90%"""

    # 错误模式匹配规则 - 优化覆盖率，调整优先级
    ERROR_PATTERNS = {
        # 超时错误 - 最高优先级，避免被network覆盖
        ErrorCategory.TIMEOUT: [
            'timeout', 'timed out', 'deadline exceeded',
            'read timeout', 'connect timeout', 'request timeout',
            'response timeout', 'operation timeout', 'query timeout',
            'execution timeout', 'wait timeout', 'slow response'
        ],

        # 限流错误 - 高优先级
        ErrorCategory.RATE_LIMIT: [
            'rate limit', 'too many requests', 'quota exceeded',
            'throttled', '429', 'rate exceeded', 'limit exceeded',
            'quota', 'throttle', 'frequency limit', 'usage limit',
            'request limit', 'api limit', 'bandwidth limit'
        ],

        # 认证错误 - 高优先级
        ErrorCategory.AUTH: [
            'unauthorized', 'forbidden', 'authentication', 'token',
            'access denied', 'invalid credentials', 'expired token',
            'insufficient privileges', '401', '403', 'auth',
            'login failed', 'password', 'credential', 'permission denied',
            'not authorized', 'authorization failed', 'access forbidden'
        ],

        # 网络错误 - 中等优先级
        ErrorCategory.NETWORK: [
            'connection', 'network', 'unreachable',
            'dns', 'socket', 'connection refused', 'connection reset',
            'no route to host', 'network is unreachable', 'host unreachable',
            'connection error', 'network error', 'connection lost',
            'connection aborted', 'ssl error', 'certificate error',
            'handshake', 'proxy error'
        ],

        # 服务器错误 - 中等优先级
        ErrorCategory.SERVER: [
            'internal server error', 'bad gateway', 'service unavailable',
            'gateway timeout', '500', '502', '503', '504',
            'server error', 'upstream', 'backend error',
            'database error', 'service error', 'system error',
            'maintenance', 'overloaded', 'server overload'
        ],

        # 客户端错误 - 中等优先级
        ErrorCategory.CLIENT: [
            'bad request', 'not found', 'method not allowed',
            'not acceptable', 'conflict', '400', '404', '405', '409',
            'validation error', 'invalid parameter', 'missing parameter',
            'invalid format', 'schema error', 'field error',
            'request error', 'payload error', 'content error'
        ],

        # 数据错误 - 低优先级
        ErrorCategory.DATA: [
            'parse error', 'json', 'xml', 'decode', 'format',
            'invalid data', 'malformed', 'encoding error',
            'syntax error', 'structure error', 'data format',
            'serialization', 'deserialization', 'corrupt data',
            'invalid json', 'invalid xml', 'parsing failed'
        ]
    }

    # 恢复策略映射
    RECOVERY_MAPPING = {
        ErrorCategory.NETWORK: RecoveryStrategy.EXPONENTIAL_BACKOFF,
        ErrorCategory.AUTH: RecoveryStrategy.TOKEN_REFRESH,
        ErrorCategory.SERVER: RecoveryStrategy.DELAYED_RETRY,
        ErrorCategory.CLIENT: RecoveryStrategy.NO_RETRY,
        ErrorCategory.RATE_LIMIT: RecoveryStrategy.RATE_LIMIT_WAIT,
        ErrorCategory.DATA: RecoveryStrategy.RESPONSE_REPAIR,
        ErrorCategory.TIMEOUT: RecoveryStrategy.EXPONENTIAL_BACKOFF,
        ErrorCategory.UNKNOWN: RecoveryStrategy.DELAYED_RETRY
    }

    def __init___(self):
    """TODO: Add function description."""
        self.error_stats = {category: 0 for category in ErrorCategory}
        self.recovery_stats = {
            strategy: {'success': 0, 'failure': 0}
            for strategy in RecoveryStrategy
        }
        self.circuit_breakers = {}  # 熔断器状态

    def classify_error(self, exception: Exception,
                       context: Dict[str, Any] = None) -> ErrorInfo:
        """
        增强错误分类器 - 多维度智能识别错误类型

        Args:
            exception: 原始异常
            context: 错误上下文信息

        Returns:
            ErrorInfo: 分类后的错误信息
        """
        error_message = str(exception).lower()
        error_type = type(exception).__name__.lower()

        # 扩展搜索文本，包含更多信息
        search_texts = [
            error_message,
            error_type,
            f"{error_message} {error_type}",
            # 添加上下文信息
            str(context.get('url', '')) if context else '',
            str(context.get('status_code', '')) if context else '',
            str(context.get('response_text', '')) if context else ''
        ]

        # 合并所有搜索文本
        combined_search_text = ' '.join(search_texts).lower()

        logger.debug(
            "错误分类开始",
            error_message=error_message,
            error_type=error_type,
            search_text=combined_search_text[:200]
        )

        # 按优先级匹配错误类型
        for category, patterns in self.ERROR_PATTERNS.items():
            for pattern in patterns:
                # 多维度匹配
                if (pattern.lower() in error_message or
                    pattern.lower() in error_type or
                    pattern.lower() in combined_search_text):

                    recovery_strategy = self.RECOVERY_MAPPING[category]

                    # 更新统计
                    self.error_stats[category] += 1

                    logger.info(
                        "错误分类完成",
                        category=category.value,
                        pattern_matched=pattern,
                        recovery_strategy=recovery_strategy.value,
                        original_error=str(exception)[:100]
                    )

                    return ErrorInfo(
                        category=category,
                        message=str(exception),
                        original_exception=exception,
                        recovery_strategy=recovery_strategy,
                        context=context or {}
                    )

        # HTTP状态码特殊处理
        if context and 'status_code' in context:
            status_code = context['status_code']
            if status_code in [401, 403]:
                category = ErrorCategory.AUTH
            elif status_code in [400, 404, 405, 409]:
                category = ErrorCategory.CLIENT
            elif status_code in [429]:
                category = ErrorCategory.RATE_LIMIT
            elif status_code in [500, 502, 503, 504]:
                category = ErrorCategory.SERVER
            else:
                category = ErrorCategory.UNKNOWN

            if category != ErrorCategory.UNKNOWN:
                recovery_strategy = self.RECOVERY_MAPPING[category]
                self.error_stats[category] += 1

                logger.info(
                    "HTTP状态码分类",
                    category=category.value,
                    status_code=status_code,
                    recovery_strategy=recovery_strategy.value
                )

                return ErrorInfo(
                    category=category,
                    message=str(exception),
                    original_exception=exception,
                    recovery_strategy=recovery_strategy,
                    context=context or {}
                )

        # 异常类型直接匹配
        exception_type_mapping = {
            'connectionerror': ErrorCategory.NETWORK,
            'timeout': ErrorCategory.TIMEOUT,
            'httperror': ErrorCategory.SERVER,
            'urlerror': ErrorCategory.NETWORK,
            'sslerror': ErrorCategory.NETWORK,
            'jsondecodeerror': ErrorCategory.DATA,
            'unicodedecodeerror': ErrorCategory.DATA,
            'valueerror': ErrorCategory.DATA,
            'keyerror': ErrorCategory.DATA,
            'attributeerror': ErrorCategory.CLIENT,
            'typeerror': ErrorCategory.CLIENT
        }

        for error_pattern, category in exception_type_mapping.items():
            if error_pattern in error_type:
                recovery_strategy = self.RECOVERY_MAPPING[category]
                self.error_stats[category] += 1

                logger.info(
                    "异常类型分类",
                    category=category.value,
                    exception_type=error_type,
                    recovery_strategy=recovery_strategy.value
                )

                return ErrorInfo(
                    category=category,
                    message=str(exception),
                    original_exception=exception,
                    recovery_strategy=recovery_strategy,
                    context=context or {}
                )

        # 未匹配到的归为未知错误
        self.error_stats[ErrorCategory.UNKNOWN] += 1

        logger.warning(
            "未知错误类型",
            error_message=error_message[:100],
            error_type=error_type,
            search_text=combined_search_text[:100]
        )

        return ErrorInfo(
            category=ErrorCategory.UNKNOWN,
            message=str(exception),
            original_exception=exception,
            recovery_strategy=RecoveryStrategy.DELAYED_RETRY,
            context=context or {}
        )

    async def handle_error(self,
                           error_info: ErrorInfo,
                           retry_function: Callable,
                           *args, **kwargs) -> Any:
        """
        错误处理主入口 - 执行恢复策略

        Args:
            error_info: 错误信息
            retry_function: 重试函数
            *args, **kwargs: 重试函数参数

        Returns:
            重试结果或抛出最终异常
        """
        strategy = error_info.recovery_strategy

        logger.info(
            "开始错误恢复处理",
            category=error_info.category.value,
            strategy=strategy.value,
            retry_count=error_info.retry_count,
            max_retries=error_info.max_retries
        )

        try:
            if strategy == RecoveryStrategy.EXPONENTIAL_BACKOFF:
                return await self._exponential_backoff_retry(
                    error_info, retry_function, *args, **kwargs)

            elif strategy == RecoveryStrategy.TOKEN_REFRESH:
                return await self._token_refresh_retry(
                    error_info, retry_function, *args, **kwargs)

            elif strategy == RecoveryStrategy.DELAYED_RETRY:
                return await self._delayed_retry(
                    error_info, retry_function, *args, **kwargs)

            elif strategy == RecoveryStrategy.RATE_LIMIT_WAIT:
                return await self._rate_limit_wait_retry(
                    error_info, retry_function, *args, **kwargs)

            elif strategy == RecoveryStrategy.RESPONSE_REPAIR:
                return await self._response_repair_retry(
                    error_info, retry_function, *args, **kwargs)

            elif strategy == RecoveryStrategy.CIRCUIT_BREAK:
                return await self._circuit_break_retry(
                    error_info, retry_function, *args, **kwargs)

            elif strategy == RecoveryStrategy.NO_RETRY:
                # 不重试，直接抛出
                self.recovery_stats[strategy]['failure'] += 1
                raise error_info.original_exception

            else:
                # 未知策略，使用默认延迟重试
                return await self._delayed_retry(error_info, retry_function, *args, **kwargs)

        except Exception:
            self.recovery_stats[strategy]['failure'] += 1
            logger.error(
                "错误恢复失败",
                strategy=strategy.value,
                final_error=str(retry_exception),
                original_error=error_info.message
            )
            raise retry_exception

    async def _exponential_backoff_retry(
    self,
    error_info: ErrorInfo,
    retry_function: Callable,
    *args,
     **kwargs):
        """指数退避重试策略"""
        for attempt in range(error_info.max_retries):
            if attempt > 0:
                # 指数退避: 1s, 2s, 4s, 8s...
                delay = error_info.retry_delay * (2 ** (attempt - 1))
                delay = min(delay, 30)  # 最大30秒

                logger.info(f"指数退避等待 {delay} 秒 (第{attempt+1}次重试)")
                await asyncio.sleep(delay)

            try:
                result = await retry_function(*args, **kwargs)
                if attempt > 0:
                    logger.info(f"指数退避重试成功 (第{attempt+1}次)")
                    self.recovery_stats[RecoveryStrategy.EXPONENTIAL_BACKOFF]['success'] += 1
                return result

            except Exception:
                if attempt == error_info.max_retries - 1:
                    # 最后一次重试失败
                    raise e
                logger.warning(f"指数退避重试失败 (第{attempt+1}次): {str(e)}")

    async def _token_refresh_retry(
    self,
    error_info: ErrorInfo,
    retry_function: Callable,
    *args,
     **kwargs):
        """Token刷新重试策略"""
        # 如果是认证错误，先尝试刷新Token
        if hasattr(
    retry_function,
    '__self__') and hasattr(
        retry_function.__self__,
         '_initialize'):
            try:
                logger.info("尝试刷新API Token")
                await retry_function.__self__._initialize()
                logger.info("Token刷新成功，重试原请求")

                result = await retry_function(*args, **kwargs)
                self.recovery_stats[RecoveryStrategy.TOKEN_REFRESH]['success'] += 1
                return result

            except Exception:
                logger.error(f"Token刷新失败: {str(refresh_error)}")
                raise refresh_error
        else:
            # 无法刷新Token，使用普通重试
            return await self._delayed_retry(error_info, retry_function, *args, **kwargs)

    async def _delayed_retry(
    self,
    error_info: ErrorInfo,
    retry_function: Callable,
    *args,
     **kwargs):
        """延迟重试策略"""
        for attempt in range(error_info.max_retries):
            if attempt > 0:
                delay = error_info.retry_delay * attempt  # 线性增长
                logger.info(f"延迟等待 {delay} 秒 (第{attempt+1}次重试)")
                await asyncio.sleep(delay)

            try:
                result = await retry_function(*args, **kwargs)
                if attempt > 0:
                    logger.info(f"延迟重试成功 (第{attempt+1}次)")
                    self.recovery_stats[RecoveryStrategy.DELAYED_RETRY]['success'] += 1
                return result

            except Exception:
                if attempt == error_info.max_retries - 1:
                    raise e
                logger.warning(f"延迟重试失败 (第{attempt+1}次): {str(e)}")

    async def _rate_limit_wait_retry(
    self,
    error_info: ErrorInfo,
    retry_function: Callable,
    *args,
     **kwargs):
        """限流等待重试策略"""
        # 限流错误通常需要等待更长时间
        base_delay = 60  # 限流等待基础时间60秒

        for attempt in range(min(error_info.max_retries, 3)):  # 限流最多重试3次
            if attempt > 0:
                delay = base_delay * attempt  # 60s, 120s, 180s
                logger.info(f"限流等待 {delay} 秒 (第{attempt+1}次重试)")
                await asyncio.sleep(delay)

            try:
                result = await retry_function(*args, **kwargs)
                if attempt > 0:
                    logger.info(f"限流重试成功 (第{attempt+1}次)")
                    self.recovery_stats[RecoveryStrategy.RATE_LIMIT_WAIT]['success'] += 1
                return result

            except Exception:
                if attempt == 2:  # 限流最多重试3次
                    raise e
                logger.warning(f"限流重试失败 (第{attempt+1}次): {str(e)}")

    async def _response_repair_retry(
    self,
    error_info: ErrorInfo,
    retry_function: Callable,
    *args,
     **kwargs):
        """响应修复重试策略"""
        # 对于数据解析错误，尝试用不同的解析方式
        for attempt in range(error_info.max_retries):
            try:
                result = await retry_function(*args, **kwargs)
                if attempt > 0:
                    logger.info(f"响应修复成功 (第{attempt+1}次)")
                    self.recovery_stats[RecoveryStrategy.RESPONSE_REPAIR]['success'] += 1
                return result

            except Exception:
                if attempt == error_info.max_retries - 1:
                    raise e

                logger.warning(f"响应解析失败 (第{attempt+1}次): {str(e)}")
                await asyncio.sleep(1)  # 短暂等待

    async def _circuit_break_retry(
    self,
    error_info: ErrorInfo,
    retry_function: Callable,
    *args,
     **kwargs):
        """熔断重试策略"""
        function_name = f"{retry_function.__name__}"
        current_time = time.time()

        # 检查熔断状态
        if function_name in self.circuit_breakers:
            breaker = self.circuit_breakers[function_name]
            if current_time < breaker['reset_time']:
                logger.warning(f"熔断器开启，拒绝请求: {function_name}")
                raise Exception(f"Circuit breaker is open for {function_name}")

        try:
            result = await retry_function(*args, **kwargs)
            # 成功后重置熔断器
            if function_name in self.circuit_breakers:
                del self.circuit_breakers[function_name]
            self.recovery_stats[RecoveryStrategy.CIRCUIT_BREAK]['success'] += 1
            return result

        except Exception:
            # 失败后设置熔断器
            self.circuit_breakers[function_name] = {
                'reset_time': current_time + 300,  # 5分钟后重置
                'error': str(e)
            }
            logger.error(f"熔断器开启: {function_name}, 5分钟后重置")
            raise e

    def get_error_coverage_report(self) -> Dict[str, Any]:
        """获取错误覆盖率报告"""
        total_errors = sum(self.error_stats.values())
        covered_categories = sum(
    1 for count in self.error_stats.values() if count > 0)
        total_categories = len(ErrorCategory)

        coverage_rate = covered_categories / \
            total_categories if total_categories > 0 else 0

        # 计算恢复成功率
        recovery_success_rates = {}
        for strategy, stats in self.recovery_stats.items():
            total_attempts = stats['success'] + stats['failure']
            success_rate = stats['success'] / \
                total_attempts if total_attempts > 0 else 0
            recovery_success_rates[strategy.value] = {
                'success_rate': success_rate,
                'total_attempts': total_attempts,
                'successes': stats['success'],
                'failures': stats['failure']
            }

        return {
            'error_coverage_rate': coverage_rate,
            'total_errors_handled': total_errors,
            'errors_by_category': {cat.value: count for cat, count in self.error_stats.items()},
            'recovery_success_rates': recovery_success_rates,
            'circuit_breakers_active': len(self.circuit_breakers),
            'meets_target': coverage_rate >= 0.90  # TASK.md目标：≥90%
        }


# 全局错误处理器实例
enhanced_error_handler = EnhancedErrorHandler()


def create_enhanced_error_handler() -> EnhancedErrorHandler:
    """创建增强错误处理器实例"""
    return EnhancedErrorHandler()


def handle_api_error(context: Dict[str, Any] = None):
    """装饰器：自动处理API调用错误"""

    def decoratorr(func):
    """TODO: Add function description."""

        async def wrapperr(*args, **kwargs):
    """TODO: Add function description."""
            try:
                return await func(*args, **kwargs)
            except Exception:
                error_info = enhanced_error_handler.classify_error(e, context)
                return await enhanced_error_handler.handle_error(error_info, func, *args, **kwargs)
        return wrapper
    return decorator
