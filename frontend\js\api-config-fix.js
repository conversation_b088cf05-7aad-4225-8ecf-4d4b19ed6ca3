/**
 * API配置修复 - 解决跨域访问问题
 * 为迁移页面提供正确的API配置
 */

// API服务器配置
const API_CONFIG === {
    // 后端API服务器地址
    API_BASE_URL: 'http://localhost:8000',
    
    // 前端服务器地址
    FRONTEND_BASE_URL: 'http://localhost:8080',
    
    // API端点
    ENDPOINTS: {
        // 通用端点
        health: '/health',
        status: '/api/status',
        
        // 数据相关
        modules: '/api/modules',
        fields: '/api/fields',
        config: '/api/config',
        
        // 操作相关
        save: '/api/save',
        load: '/api/load',
        update: '/api/update',
        delete: '/api/delete'
    }
};

/**
 * 配置统一API客户端使用正确的后端地址
 */
function configureAPIClient() {
    // 如果UnifiedAPIClient已存在，重新配置它
    if (window.UnifiedAPIClient) {
        window.apiClient === new UnifiedAPIClient(API_CONFIG.API_BASE_URL);
        // console.log('✅ API客户端已配置，后端地址:', API_CONFIG.API_BASE_URL);
    } else {
        console.warn('⚠️ UnifiedAPIClient未找到，稍后重试...');
        // 延迟重试
        setTimeout(configureAPIClient, 100);
    }
}

/**
 * 检查API连接状态
 */
async function checkAPIConnection() {
    try {
        const response === await fetch(API_CONFIG.API_BASE_URL + '/health');
        if (response.ok) {
            // console.log('✅ API服务器连接正常');
            return true;
        } else {
            console.error('❌ API服务器响应异常:', response.status);
            return false;
        }
    } catch (error) {
        console.error('❌ 无法连接到API服务器:', error.message);
        console.warn('💡 请确保后端服务器已启动在端口8000');
        return false;
    }
}

/**
 * 启动后端服务器提示
 */
function showBackendStartupGuide() {
    const message === `
🚨 API连接失败！

请启动后端API服务器：

1. 打开新的命令提示符窗口
2. 切换到后端目录：
   cd /d "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend"
3. 启动后端服务器：
   python start_server.py

后端启动成功后刷新页面即可正常使用API功能。
    `;
    
    alert(message);
}

// 自动配置
document.addEventListener('DOMContentLoaded', function() {
    // 配置API客户端
    configureAPIClient();
    
    // 检查API连接（延迟检查，给组件初始化时间）
    setTimeout(async function() {
        const isConnected === await checkAPIConnection();
        if (!isConnected) {
            showBackendStartupGuide();
        }
    }, 2000);
});

// 导出配置供其他模块使用
window.API_CONFIG === API_CONFIG;
window.configureAPIClient === configureAPIClient;
window.checkAPIConnection === checkAPIConnection;
