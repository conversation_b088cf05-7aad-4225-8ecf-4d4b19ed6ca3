#!/bin/bash

# YS-API V3.0 迁移后安全清理脚本
# 只清理确认安全的临时和测试文件

echo "🧹 YS-API V3.0 迁移后安全清理工具"
echo "=================================================="
echo ""

# 检查是否在正确的目录
if [ ! -d "frontend" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    echo "当前目录应包含 frontend 文件夹"
    exit 1
fi

echo "📂 当前工作目录: $(pwd)"
echo "✅ 检测到 frontend 目录"
echo ""

# 显示将要清理的文件
echo "🔍 将要清理的文件："
echo "-------------------"

# 临时文件列表
temp_files=(
    "frontend/\$null"
    "frontend/component-test.html"
    "frontend/direct-component-test.html"
    "frontend/js-loading-test.html"
    "frontend/performance-optimization-demo.html"
    "frontend/sync-test.html"
    "frontend/test_data_type_frontend.html"
    "frontend/method-fix-test.html"
    "frontend/component-diagnostic.html"
)

# 检查文件是否存在并显示
for file in "${temp_files[@]}"; do
    if [ -f "$file" ]; then
        echo "  📄 $file (存在)"
    else
        echo "  ❌ $file (不存在)"
    fi
done

echo ""
echo "⚠️  注意：此脚本只清理确认安全的临时和测试文件"
echo "❗ 不会删除任何核心功能文件或迁移后的页面"
echo ""

# 询问用户确认
read -p "是否继续清理？(y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 清理已取消"
    exit 0
fi

echo ""
echo "🚀 开始清理..."
echo "=================="

# 清理计数器
removed_count=0
total_count=${#temp_files[@]}

# 执行清理
for file in "${temp_files[@]}"; do
    if [ -f "$file" ]; then
        rm "$file"
        if [ $? -eq 0 ]; then
            echo "✅ 已删除: $file"
            ((removed_count++))
        else
            echo "❌ 删除失败: $file"
        fi
    else
        echo "⏭️  跳过不存在的文件: $file"
    fi
done

echo ""
echo "📊 清理完成统计"
echo "=================="
echo "总文件数: $total_count"
echo "成功删除: $removed_count"
echo "跳过文件: $((total_count - removed_count))"

if [ $removed_count -gt 0 ]; then
    echo ""
    echo "✅ 清理成功完成！"
    echo "💡 建议：清理后请测试迁移页面功能是否正常"
else
    echo ""
    echo "ℹ️  没有找到需要清理的文件"
fi

echo ""
echo "🔗 更多清理选项请参考："
echo "   docs/16-迁移后清理指南.md"
echo ""

echo "⚠️  重要提醒："
echo "   • 已保留所有核心功能文件"
echo "   • 已保留所有迁移后的页面"
echo "   • 已保留新架构组件文件"
echo "   • 备份文件需要手动评估清理"
echo ""

echo "🎯 下一步建议："
echo "   1. 测试迁移后的页面功能"
echo "   2. 验证新架构组件工作正常"
echo "   3. 确认功能无误后再考虑清理备份文件"
