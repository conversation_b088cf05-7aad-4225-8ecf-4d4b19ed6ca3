2025-08-03 14:07:42,933 - INFO - 开始清理调试代码...
2025-08-03 14:07:42,933 - INFO - 步骤1: 清理print语句
2025-08-03 14:07:42,937 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\cicd_builder_simple.py
2025-08-03 14:07:42,939 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\cicd_pipeline_builder.py
2025-08-03 14:07:42,941 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\cicd_pipeline_builder_old.py
2025-08-03 14:07:42,943 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\comprehensive_production_test.py
2025-08-03 14:07:42,945 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\config_environmentizer.py
2025-08-03 14:07:42,945 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\env_config_loader.py
2025-08-03 14:07:42,950 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\production_readiness_report.py
2025-08-03 14:07:42,951 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\production_test_runner.py
2025-08-03 14:07:42,954 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\quick_health_check.py
2025-08-03 14:07:42,955 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\remaining_issues_fixer.py
2025-08-03 14:07:42,956 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\run_comprehensive_check.py
2025-08-03 14:07:42,957 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\setup_test_env.py
2025-08-03 14:07:42,957 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\start_quick_test.py
2025-08-03 14:07:42,958 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\universal_code_quality_fixer.py
2025-08-03 14:07:42,962 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\scripts\rollback_batch_writes.py
2025-08-03 14:07:42,964 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\scripts\test_elk_connection.py
2025-08-03 14:07:42,965 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\scripts\validate_deployment.py
2025-08-03 14:07:42,966 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\tests\api_test_server.py
2025-08-03 14:07:42,967 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\tests\test_baseline_api.py
2025-08-03 14:07:42,970 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\tools\error_handling_load_test.py
2025-08-03 14:07:43,022 - ERROR - 处理文件失败 d:\OneDrive\Desktop\YS-API程序\v3\backend\app\services\zero_downtime_implementation.py: [Errno 13] Permission denied: 'd:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\zero_downtime_implementation.py'
2025-08-03 14:07:43,025 - INFO - 清理了print语句: d:\OneDrive\Desktop\YS-API程序\v3\backend\app\api\v1\config.py
2025-08-03 14:07:43,034 - INFO - 步骤2: 清理TODO注释
2025-08-03 14:07:43,054 - INFO - 清理了TODO注释: d:\OneDrive\Desktop\YS-API程序\v3\remaining_issues_fixer.py
2025-08-03 14:07:43,143 - INFO - 调试代码清理完成!
