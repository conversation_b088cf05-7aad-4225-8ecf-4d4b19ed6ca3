2025-08-02 20:23:10,244 - INFO - 🚀 开始统一代码质量修复
2025-08-02 20:23:10,244 - INFO - ============================================================
2025-08-02 20:23:10,245 - INFO - 🔍 扫描包含 print 语句的 Python 文件...
2025-08-02 20:23:10,263 - INFO - 发现 36 个文件包含 print 语句
2025-08-02 20:23:10,263 - INFO -   📁 final_project_fixer.py: 20 个 print 语句
2025-08-02 20:23:10,264 - INFO -   📁 fix_build_script.py: 6 个 print 语句
2025-08-02 20:23:10,264 - INFO -   📁 fix_execute_task_script.py: 2 个 print 语句
2025-08-02 20:23:10,264 - INFO -   📁 fix_issues.py: 9 个 print 语句
2025-08-02 20:23:10,264 - INFO -   📁 fix_task_issues.py: 12 个 print 语句
2025-08-02 20:23:10,264 - INFO -   📁 install_windows_service.py: 8 个 print 语句
2025-08-02 20:23:10,264 - INFO -   📁 project_health_check.py: 31 个 print 语句
2025-08-02 20:23:10,265 - INFO -   📁 project_health_checker.py: 21 个 print 语句
2025-08-02 20:23:10,265 - INFO -   📁 run_comprehensive_check.py: 17 个 print 语句
2025-08-02 20:23:10,265 - INFO -   📁 start_quick_test.py: 25 个 print 语句
2025-08-02 20:23:10,265 - INFO -   📁 test_baseline_api.py: 50 个 print 语句
2025-08-02 20:23:10,265 - INFO -   📁 universal_code_quality_fixer.py: 4 个 print 语句
2025-08-02 20:23:10,265 - INFO -   📁 start_server.py: 10 个 print 语句
2025-08-02 20:23:10,265 - INFO -   📁 start_simple.py: 3 个 print 语句
2025-08-02 20:23:10,266 - INFO -   📁 add_api_config.py: 12 个 print 语句
2025-08-02 20:23:10,266 - INFO -   📁 auto_migration.py: 12 个 print 语句
2025-08-02 20:23:10,266 - INFO -   📁 diagnose_migration.py: 28 个 print 语句
2025-08-02 20:23:10,266 - INFO -   📁 fix_css_paths.py: 17 个 print 语句
2025-08-02 20:23:10,266 - INFO -   📁 fix_migrated_paths.py: 18 个 print 语句
2025-08-02 20:23:10,266 - INFO -   📁 reliable_server.py: 19 个 print 语句
2025-08-02 20:23:10,266 - INFO -   📁 rollback_batch_writes.py: 33 个 print 语句
2025-08-02 20:23:10,266 - INFO -   📁 test_elk_connection.py: 30 个 print 语句
2025-08-02 20:23:10,266 - INFO -   📁 test_server.py: 15 个 print 语句
2025-08-02 20:23:10,267 - INFO -   📁 validate_deployment.py: 42 个 print 语句
2025-08-02 20:23:10,267 - INFO -   📁 verify_fixes.py: 18 个 print 语句
2025-08-02 20:23:10,267 - INFO -   📁 locust_stress_test.py: 1 个 print 语句
2025-08-02 20:23:10,267 - INFO -   📁 test_md_to_json_converter.py: 1 个 print 语句
2025-08-02 20:23:10,267 - INFO -   📁 error_handling_load_test.py: 13 个 print 语句
2025-08-02 20:23:10,268 - INFO -   📁 code_cleaner.py: 11 个 print 语句
2025-08-02 20:23:10,268 - INFO -   📁 mock_utils.py: 8 个 print 语句
2025-08-02 20:23:10,268 - INFO -   📁 main.py: 7 个 print 语句
2025-08-02 20:23:10,268 - INFO -   📁 config.py: 5 个 print 语句
2025-08-02 20:23:10,268 - INFO -   📁 robust_json_parser.py: 7 个 print 语句
2025-08-02 20:23:10,268 - INFO -   📁 unified_field_service.py: 3 个 print 语句
2025-08-02 20:23:10,268 - INFO -   📁 zero_downtime_implementation.py: 1 个 print 语句
2025-08-02 20:23:10,269 - INFO -   📁 config.py: 1 个 print 语句
2025-08-02 20:23:10,269 - INFO - 
🔧 开始修复 36 个文件...
2025-08-02 20:23:10,271 - INFO - ✅ 修复 final_project_fixer.py: 应用了 3 个替换
2025-08-02 20:23:10,273 - INFO - ✅ 修复 fix_build_script.py: 应用了 3 个替换
2025-08-02 20:23:10,275 - INFO - ✅ 修复 fix_execute_task_script.py: 应用了 2 个替换
2025-08-02 20:23:10,276 - INFO - ✅ 修复 fix_issues.py: 应用了 3 个替换
2025-08-02 20:23:10,278 - INFO - ✅ 修复 fix_task_issues.py: 应用了 4 个替换
2025-08-02 20:23:10,279 - INFO - ✅ 修复 install_windows_service.py: 应用了 2 个替换
2025-08-02 20:23:10,280 - INFO - ✅ 修复 project_health_check.py: 应用了 3 个替换
2025-08-02 20:23:10,282 - INFO - ✅ 修复 project_health_checker.py: 应用了 3 个替换
2025-08-02 20:23:10,284 - INFO - ✅ 修复 run_comprehensive_check.py: 应用了 4 个替换
2025-08-02 20:23:10,285 - INFO - ✅ 修复 start_quick_test.py: 应用了 3 个替换
2025-08-02 20:23:10,286 - INFO - ✅ 修复 test_baseline_api.py: 应用了 4 个替换
2025-08-02 20:23:10,287 - INFO - ✅ 修复 universal_code_quality_fixer.py: 应用了 2 个替换
2025-08-02 20:23:10,288 - INFO - ✅ 修复 start_server.py: 应用了 3 个替换
2025-08-02 20:23:10,289 - INFO - ✅ 修复 start_simple.py: 应用了 2 个替换
2025-08-02 20:23:10,290 - INFO - ✅ 修复 add_api_config.py: 应用了 2 个替换
2025-08-02 20:23:10,292 - INFO - ✅ 修复 auto_migration.py: 应用了 3 个替换
2025-08-02 20:23:10,293 - INFO - ✅ 修复 diagnose_migration.py: 应用了 2 个替换
2025-08-02 20:23:10,294 - INFO - ✅ 修复 fix_css_paths.py: 应用了 2 个替换
2025-08-02 20:23:10,295 - INFO - ✅ 修复 fix_migrated_paths.py: 应用了 2 个替换
2025-08-02 20:23:10,296 - INFO - ✅ 修复 reliable_server.py: 应用了 3 个替换
2025-08-02 20:23:10,297 - INFO - ✅ 修复 rollback_batch_writes.py: 应用了 4 个替换
2025-08-02 20:23:10,298 - INFO - ✅ 修复 test_elk_connection.py: 应用了 3 个替换
2025-08-02 20:23:10,299 - INFO - ✅ 修复 test_server.py: 应用了 1 个替换
2025-08-02 20:23:10,301 - INFO - ✅ 修复 validate_deployment.py: 应用了 3 个替换
2025-08-02 20:23:10,302 - INFO - ✅ 修复 verify_fixes.py: 应用了 2 个替换
2025-08-02 20:23:10,302 - INFO - ℹ️ 跳过 locust_stress_test.py: 无需修改
2025-08-02 20:23:10,304 - INFO - ✅ 修复 test_md_to_json_converter.py: 应用了 1 个替换
2025-08-02 20:23:10,305 - INFO - ✅ 修复 error_handling_load_test.py: 应用了 4 个替换
2025-08-02 20:23:10,306 - INFO - ✅ 修复 code_cleaner.py: 应用了 2 个替换
2025-08-02 20:23:10,307 - INFO - ✅ 修复 mock_utils.py: 应用了 3 个替换
2025-08-02 20:23:10,309 - INFO - ✅ 修复 main.py: 应用了 1 个替换
2025-08-02 20:23:10,309 - INFO - ✅ 修复 config.py: 应用了 2 个替换
2025-08-02 20:23:10,312 - INFO - ✅ 修复 robust_json_parser.py: 应用了 3 个替换
2025-08-02 20:23:10,314 - INFO - ✅ 修复 unified_field_service.py: 应用了 2 个替换
2025-08-02 20:23:10,317 - ERROR - ❌ 修复 zero_downtime_implementation.py 失败: [Errno 13] Permission denied: 'd:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\zero_downtime_implementation.py'
2025-08-02 20:23:10,319 - INFO - ✅ 修复 config.py: 应用了 0 个替换
2025-08-02 20:23:10,319 - INFO - 🔍 验证修复结果...
2025-08-02 20:23:10,323 - WARNING - ⚠️ 10 个文件仍有 print 语句:
2025-08-02 20:23:10,323 - WARNING -   📁 project_health_check.py: 3 个 print
2025-08-02 20:23:10,323 - WARNING -   📁 run_comprehensive_check.py: 2 个 print
2025-08-02 20:23:10,323 - WARNING -   📁 start_quick_test.py: 5 个 print
2025-08-02 20:23:10,323 - WARNING -   📁 test_baseline_api.py: 13 个 print
2025-08-02 20:23:10,323 - WARNING -   📁 universal_code_quality_fixer.py: 2 个 print
2025-08-02 20:23:10,323 - WARNING -   📁 rollback_batch_writes.py: 3 个 print
2025-08-02 20:23:10,323 - WARNING -   📁 test_elk_connection.py: 2 个 print
2025-08-02 20:23:10,324 - WARNING -   📁 validate_deployment.py: 3 个 print
2025-08-02 20:23:10,324 - WARNING -   📁 error_handling_load_test.py: 1 个 print
2025-08-02 20:23:10,324 - WARNING -   📁 config.py: 1 个 print
2025-08-02 20:23:10,324 - INFO - ============================================================
2025-08-02 20:23:10,325 - INFO - 🎉 统一代码质量修复完成！
2025-08-02 20:23:10,325 - INFO - 📊 修复统计: 34 成功, 1 跳过, 1 错误
2025-08-02 20:23:10,325 - INFO - 📄 详细报告: d:\OneDrive\Desktop\YS-API程序\v3\universal_code_fix_report.md
