@echo off
echo ========================================
echo SonarQube 项目分析脚本
echo ========================================

echo.
echo 1. 检查SonarQube配置...
if exist sonar-project.properties (
    echo ✅ SonarQube配置文件存在
) else (
    echo ❌ 缺少sonar-project.properties配置文件
    echo 正在创建默认配置...
    
    echo # SonarQube Project Configuration > sonar-project.properties
    echo sonar.projectKey=ys-api-v3 >> sonar-project.properties
    echo sonar.projectName=YS-API V3.0 >> sonar-project.properties
    echo sonar.projectVersion=3.0 >> sonar-project.properties
    echo sonar.sources=. >> sonar-project.properties
    echo sonar.exclusions=**/temp_cleanup/**,**/__pycache__/**,**/node_modules/**,**/test_*.py,**/*_test.py >> sonar-project.properties
    echo ✅ 已创建配置文件
)

echo.
echo 2. 检查项目结构...
echo 📁 后端文件: backend/
dir /B backend\*.py 2>nul | findstr . >nul && echo ✅ 发现Python文件 || echo ❌ 未发现Python文件

echo 📁 前端文件: frontend/
dir /B frontend\*.js 2>nul | findstr . >nul && echo ✅ 发现JavaScript文件 || echo ❌ 未发现JavaScript文件

echo.
echo 3. 运行代码质量检查...
echo 🔍 检查Python语法...
python -m py_compile backend\start_server_clean.py 2>nul && echo ✅ Python语法正确 || echo ❌ Python语法错误

echo 🔍 检查核心文件...
if exist frontend\js\common\error-handler.js (
    node -c frontend\js\common\error-handler.js 2>nul && echo ✅ JavaScript语法正确 || echo ❌ JavaScript语法错误
) else (
    echo ⚠️ error-handler.js 文件不存在
)

echo.
echo 4. SonarQube分析建议...
echo 💡 在VS Code中查看PROBLEMS面板，查看SonarQube检测到的问题
echo 💡 已修复的问题类型：
echo    - JavaScript语法错误 (=== 赋值问题)
echo    - Python代码质量问题
echo    - 冗余文件清理
echo    - 配置文件优化

echo.
echo 5. 下一步操作...
echo 🔧 如果使用SonarQube服务器，运行: sonar-scanner
echo 🔧 如果使用VS Code扩展，检查PROBLEMS面板
echo 🔧 查看清理日志: temp_cleanup目录

echo.
echo ========================================
echo 分析完成！
echo ========================================
pause
