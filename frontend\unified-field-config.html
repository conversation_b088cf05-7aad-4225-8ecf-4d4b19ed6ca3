<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>unified-field-config.html - 已迁移到新架构</title>
    
    <!-- 新架构核心文件 -->
    <script src="../js/core/component-manager.js"></script>
    <script src="../js/core/app-bootstrap.js"></script>
    <script src="../js/api-config-fix.js"></script>
    
    <!-- 所需组件 -->
    <script src="../js/common/api-client.js"></script>
    <script src="../js/common/field-utils.js"></script>
    <script src="../js/common/validation-utils.js"></script>
    <script src="../js/common/error-handler.js"></script>
    <script src="../js/field-deduplication-enhancer.js"></script>
    
    <!-- 自定义样式 -->
    <style>

    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto;
      font-size: 14px;
      background: #f5f5f5;
      color: #333;
      height: 100vh;
      overflow: hidden
    }

    .container {
      height: 100vh;
      display: flex;
      flex-direction: column;
      background: #fff
    }

    .header {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: #fff;
      padding: 15px 20px;
      text-align: center;
      flex-shrink: 0
    }

    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden
    }

    .control-panel {
      display: flex;
      gap: 15px;
      padding: 15px 20px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      flex-shrink: 0
    }

    .stats-panel {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 10px;
      padding: 10px 20px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      flex-shrink: 0
    }

    .stat-card {
      background: #fff;
      padding: 10px;
      border-radius: 4px;
      text-align: center;
      border-left: 4px solid #409EFF
    }

    .stat-value {
      font-size: 20px;
      font-weight: bold;
      color: #409EFF
    }

    .field-container {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      display: flex;
      flex-direction: column
    }

    .field-content {
      flex: 1;
      overflow-y: auto
    }

    .field-group {
      margin-bottom: 40px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      overflow: hidden
    }

    .group-header {
      background: #f8f9fa;
      padding: 15px 20px;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      font-size: 16px
    }

    .field-list {
      padding: 20px;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: 15px
    }

    .field-item {
      display: flex;
      align-items: flex-start;
      padding: 15px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      background: #fff;
      transition: all .3s;
      min-height: 120px
    }

    .field-item:hover {
      border-color: #409EFF;
      box-shadow: 0 4px 12px rgba(64, 158, 255, .15)
    }

    .field-item.selected {
      background: #f0f9ff;
      border-color: #409EFF
    }

    .field-checkbox-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      margin-right: 15px;
      flex-shrink: 0
    }

    .field-checkbox {
      margin: 0
    }

    .field-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 6px
    }

    .field-name {
      font-weight: bold;
      margin-bottom: 4px;
      font-size: 14px;
      line-height: 1.4;
      color: #303133;
      margin-top: 2px
    }

    .field-chinese {
      font-size: 13px;
      color: #606266;
      margin-bottom: 4px;
      line-height: 1.4
    }

    .field-sample {
      font-size: 12px;
      color: #909399;
      line-height: 1.3
    }

    .field-meta {
      display: flex;
      gap: 8px;
      font-size: 11px;
      align-items: center;
      flex-wrap: wrap
    }

    .data-type-container {
      display: flex;
      flex-direction: column;
      gap: 2px
    }

    .data-type-input {
      width: 100%;
      border: 1px dashed #67c23a;
      border-radius: 4px;
      font-size: 11px;
      padding: 3px 6px;
      margin: 2px 0;
      background: #fafafa;
      transition: all .3s;
      color: #67c23a;
      font-weight: 500;
      resize: none;
      min-height: 24px;
      max-height: 200px;
      overflow-y: auto
    }

    .data-type-input:focus {
      outline: none;
      border-color: #67c23a;
      background: #fff;
      box-shadow: 0 0 0 2px rgba(103, 194, 58, .2)
    }

    .data-type-input:disabled {
      background: #f5f5f5;
      color: #c0c4cc;
      border-color: #e4e7ed
    }

    .data-type-input[disabled] {
      pointer-events: auto !important;
      opacity: 1 !important;
      background: #fff !important;
    }

    .data-type-help {
      font-size: 10px;
      color: #909399;
      cursor: help;
      margin-left: 4px
    }

    .lock-btn {
      background: none;
      border: none;
      cursor: pointer;
      font-size: 16px;
      transition: all .3s;
      padding: 2px
    }

    .lock-btn:hover {
      transform: scale(1.1)
    }

    .lock-btn.locked {
      color: #f56c6c
    }

    .error,
    .success,
    .loading {
      padding: 20px;
      text-align: center
    }

    .error {
      color: #f56c6c
    }

    .success {
      color: #67c23a
    }

    .loading {
      color: #909399
    }

    .chinese-input {
      width: 100%;
      border: 1px dashed #409EFF;
      border-radius: 4px;
      font-size: 13px;
      padding: 4px 6px;
      margin: 2px 0;
      background: #fafafa;
      transition: all .3s;
      resize: none;
      min-height: 24px;
      max-height: 200px;
      overflow-y: auto
    }

    .chinese-input:focus {
      outline: none;
      border-color: #409EFF;
      background: #fff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, .2)
    }

    .chinese-input:disabled {
      background: #f5f5f5;
      color: #c0c4cc;
      border-color: #e4e7ed
    }

    .chinese-input[disabled] {
      pointer-events: auto !important;
      opacity: 1 !important;
      background: #fff !important;
    }

    .data-type-guide {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, .15);
      z-index: 1000;
      max-width: 600px;
      max-height: 80vh;
      overflow-y: auto;
      display: none
    }

    .data-type-guide.show {
      display: block
    }

    .guide-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      border-bottom: 1px solid #e4e7ed;
      padding-bottom: 10px
    }

    .guide-title {
      font-size: 18px;
      font-weight: bold;
      color: #303133
    }

    .guide-close {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #909399
    }

    .guide-close:hover {
      color: #303133
    }

    .guide-content {
      line-height: 1.6
    }

    .guide-section {
      margin-bottom: 15px
    }

    .guide-section h4 {
      color: #409EFF;
      margin-bottom: 8px
    }

    .guide-section ul {
      margin: 0;
      padding-left: 20px
    }

    .guide-section li {
      margin-bottom: 4px
    }

    .guide-example {
      background: #f8f9fa;
      padding: 8px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      margin-top: 5px
    }

    .overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, .5);
      z-index: 999;
      display: none
    }

    .overlay.show {
      display: block
    }

    /* 数据类型参考区域样式 */
    .data-type-reference {
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      margin: 0 20px 20px;
      padding: 15px
    }

    .reference-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      border-bottom: 1px solid #e9ecef;
      padding-bottom: 10px
    }

    .reference-header h3 {
      margin: 0;
      color: #303133;
      font-size: 16px
    }

    .reference-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px
    }

    .type-category h4 {
      margin: 0 0 8px 0;
      color: #409EFF;
      font-size: 14px
    }

    .type-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px
    }

    .type-item {
      background: #f8f9fa;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 12px;
      color: #606266;
      cursor: pointer;
      transition: all .3s
    }

    .type-item:hover {
      background: #e3f2fd;
      border-color: #409EFF;
      color: #409EFF
    }

    /* 恢复按钮样式 */
    .restore-btn {
      background: none;
      border: 1px solid #67c23a;
      color: #67c23a;
      border-radius: 4px;
      padding: 2px 6px;
      font-size: 10px;
      cursor: pointer;
      transition: all .3s;
      margin-top: 4px
    }

    .restore-btn:hover {
      background: #67c23a;
      color: #fff
    }

    /* 错误提示样式 */
    .error-message {
      background: #fef0f0;
      border: 1px solid #fbc4c4;
      color: #f56c6c;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
      font-size: 13px
    }

    .warning-message {
      background: #fdf6ec;
      border: 1px solid #faecd8;
      color: #e6a23c;
      padding: 10px;
      border-radius: 4px;
      margin: 10px 0;
      font-size: 13px
    }
  
    </style>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
</head>
<body>
    <!-- 迁移标识 -->
    <div style="position: fixed; top: 10px; right: 10px; background: #4CAF50; color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px; z-index: 9999;">
        ✅ 已迁移到新架构
    </div>
    
    <!-- 原始页面内容 -->
<div id="app">
    <div class="container">
      <div class="header">
        <h1>统一字段配置</h1>
        <p>可改中文名、可改数据类型、可加锁的稳定版本</p>
      </div>

      <div class="main-content">
        <div class="control-panel">
          <el-select v-model="selectedModule" placeholder="模块" @change="loadConfig">
            <el-option v-for="m in modules" :key="m.module_name" :label="m.display_name" :value="m.module_name" />
          </el-select>
          <el-input v-model="userId" placeholder="用户ID" style="width:120px" />
          <el-button type="primary" @click="loadConfig" :loading="loading">加载配置</el-button>
          <el-button type="success" @click="saveAllChanges" :loading="saving">保存全部</el-button>
          <el-button type="warning" @click="debugFields">调试字段</el-button>
          <el-button type="info" @click="showDataTypeGuide">数据类型说明</el-button>
        </div>

        <div class="stats-panel" v-if="config">
          <div class="stat-card">
            <div class="stat-value">{{ config.total_fields || 0 }}</div>
            <div>总字段</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ config.selected_fields || 0 }}</div>
            <div>已选</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ selectionRate }}%</div>
            <div>选择率</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ userModifiedCount }}</div>
            <div>用户修改</div>
          </div>
        </div>

        <!-- 数据类型说明区域 -->
        <div class="data-type-reference" v-if="config">
          <div class="reference-header">
            <h3>📋 数据类型参考</h3>
            <el-button size="small" type="info" @click="showDataTypeGuide">详细说明</el-button>
          </div>
          <div class="reference-content">
            <div class="type-category">
              <h4>📝 字符串</h4>
              <div class="type-list">
                <span class="type-item" @click="insertDataType('VARCHAR(255)')">VARCHAR(255)</span>
                <span class="type-item" @click="insertDataType('NVARCHAR(200)')">NVARCHAR(200)</span>
                <span class="type-item" @click="insertDataType('TEXT')">TEXT</span>
              </div>
            </div>
            <div class="type-category">
              <h4>🔢 数字</h4>
              <div class="type-list">
                <span class="type-item" @click="insertDataType('INT')">INT</span>
                <span class="type-item" @click="insertDataType('BIGINT')">BIGINT</span>
                <span class="type-item" @click="insertDataType('DECIMAL(10,2)')">DECIMAL(10,2)</span>
              </div>
            </div>
            <div class="type-category">
              <h4>📅 日期时间</h4>
              <div class="type-list">
                <span class="type-item" @click="insertDataType('DATE')">DATE</span>
                <span class="type-item" @click="insertDataType('DATETIME')">DATETIME</span>
                <span class="type-item" @click="insertDataType('TIMESTAMP')">TIMESTAMP</span>
              </div>
            </div>
            <div class="type-category">
              <h4>✅ 布尔</h4>
              <div class="type-list">
                <span class="type-item" @click="insertDataType('BOOLEAN')">BOOLEAN</span>
                <span class="type-item" @click="insertDataType('BIT')">BIT</span>
              </div>
            </div>
          </div>
        </div>

        <div class="field-container" v-if="config && config.fields">
          <div class="field-content">
            <div v-for="(list,group) in groupedFields" :key="group">
              <div class="field-group">
                <div class="group-header">
                  {{ group }} ({{ list.length }})
                  <el-button size="small" @click="toggleGroup(group)">{{ list.every(f=>f.is_selected)?'取消全选':'全选'
                    }}</el-button>
                </div>
                <div class="field-list">
                  <div v-for="f in list" :key="f.name" class="field-item" :class="{selected:f.is_selected}">
                    <div class="field-checkbox-container">
                      <el-checkbox v-model="f.is_selected" @change="updateSelection(f.name,$event)"
                        :disabled="f.locked" />
                      <button class="lock-btn" @click="toggleLock(f.name)" :class="{locked:f.locked}">{{
                        f.locked?'🔒':'🔓' }}</button>
                    </div>
                    <div class="field-info">
                      <div class="field-name">{{ f.name }}</div>
                      <textarea :value="f.chinese_name" @input="handleChineseInput(f.name, $event)"
                        @blur="saveChinese(f.name, $event)" placeholder="中文名" class="chinese-input" :disabled="f.locked"
                        rows="1"></textarea>
                      <div v-if="f.sample_value" class="field-sample">示例: {{ f.sample_value }}</div>
                      <div class="field-meta">
                        <div class="data-type-container">
                          <textarea :value="f.data_type" @input="handleDataTypeInput(f.name, $event)"
                            @blur="saveDataType(f.name, $event)" placeholder="数据类型" class="data-type-input"
                            :disabled="f.locked" rows="1"></textarea>
                        </div>
                        <span v-if="f.user_modified" style="color:#409EFF;font-size:10px">用户修改</span>
                      </div>
                      <button v-if="f.user_modified" class="restore-btn" @click="restoreField(f.name)"
                        title="恢复到基准配置">🔄 恢复</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="loading" class="loading"><el-icon class="is-loading" /> 加载中...</div>
      <div v-if="error" class="error-message">{{ error }}</div>
      <div v-if="success" class="success">{{ success }}</div>
    </div>
  </div>

  <!-- 数据类型说明弹窗 -->
  <div class="overlay" :class="{show: showGuide}" @click="hideDataTypeGuide"></div>
  <div class="data-type-guide" :class="{show: showGuide}">
    <div class="guide-header">
      <div class="guide-title">数据类型参考指南</div>
      <button class="guide-close" @click="hideDataTypeGuide">×</button>
    </div>
    <div class="guide-content">
      <div class="guide-section">
        <h4>📝 字符串类型</h4>
        <ul>
          <li><strong>VARCHAR(n)</strong> - 可变长度字符串，最大n个字符
            <div class="guide-example">推荐: VARCHAR(255) - 一般文本, VARCHAR(1000) - 长文本</div>
          </li>
          <li><strong>NVARCHAR(n)</strong> - Unicode字符串，支持中文（推荐）
            <div class="guide-example">推荐: NVARCHAR(200) - 中文名称, NVARCHAR(500) - 中文描述</div>
          </li>
          <li><strong>CHAR(n)</strong> - 固定长度字符串
            <div class="guide-example">推荐: CHAR(10) - 固定编码, CHAR(20) - 固定ID</div>
          </li>
          <li><strong>TEXT</strong> - 长文本，无长度限制
            <div class="guide-example">推荐: 备注、描述、详细说明</div>
          </li>
        </ul>
      </div>

      <div class="guide-section">
        <h4>🔢 数字类型</h4>
        <ul>
          <li><strong>INT</strong> - 整数，范围 -2,147,483,648 到 2,147,483,647
            <div class="guide-example">推荐: 数量、状态码、ID</div>
          </li>
          <li><strong>BIGINT</strong> - 大整数，范围更大
            <div class="guide-example">推荐: 大数量、长ID、时间戳</div>
          </li>
          <li><strong>DECIMAL(p,s)</strong> - 精确小数，p位数字，s位小数
            <div class="guide-example">推荐: DECIMAL(10,2) - 金额, DECIMAL(8,4) - 精确计算</div>
          </li>
          <li><strong>FLOAT</strong> - 浮点数，适合科学计算
            <div class="guide-example">推荐: 科学数据、统计计算</div>
          </li>
        </ul>
      </div>

      <div class="guide-section">
        <h4>📅 日期时间类型</h4>
        <ul>
          <li><strong>DATE</strong> - 日期，格式 YYYY-MM-DD
            <div class="guide-example">推荐: 出生日期、创建日期</div>
          </li>
          <li><strong>DATETIME</strong> - 日期时间，格式 YYYY-MM-DD HH:MM:SS
            <div class="guide-example">推荐: 创建时间、修改时间、操作时间</div>
          </li>
          <li><strong>TIMESTAMP</strong> - 时间戳，自动更新
            <div class="guide-example">推荐: 系统时间、审计时间</div>
          </li>
        </ul>
      </div>

      <div class="guide-section">
        <h4>✅ 布尔类型</h4>
        <ul>
          <li><strong>BOOLEAN</strong> - 布尔值，true/false
            <div class="guide-example">推荐: 是否有效、是否删除、是否启用</div>
          </li>
          <li><strong>BIT</strong> - 位值，0/1
            <div class="guide-example">推荐: 开关状态、标志位</div>
          </li>
        </ul>
      </div>

      <div class="guide-section">
        <h4>🔧 特殊类型</h4>
        <ul>
          <li><strong>JSON</strong> - JSON格式数据
            <div class="guide-example">推荐: 配置信息、扩展属性</div>
          </li>
          <li><strong>UUID</strong> - 唯一标识符
            <div class="guide-example">推荐: 全局唯一ID、分布式系统</div>
          </li>
          <li><strong>BLOB</strong> - 二进制数据
            <div class="guide-example">推荐: 文件内容、图片数据</div>
          </li>
        </ul>
      </div>

      <div class="guide-section">
        <h4>💡 业务场景推荐</h4>
        <ul>
          <li><strong>组织名称</strong>: NVARCHAR(200) - 支持中文，长度适中</li>
          <li><strong>产品编码</strong>: VARCHAR(50) - 固定格式，长度较短</li>
          <li><strong>数量</strong>: INT - 整数，适合计数</li>
          <li><strong>金额</strong>: DECIMAL(10,2) - 精确小数，避免浮点误差</li>
          <li><strong>创建时间</strong>: DATETIME - 包含日期和时间</li>
          <li><strong>状态</strong>: TINYINT 或 VARCHAR(20) - 状态码或状态名称</li>
          <li><strong>备注</strong>: TEXT - 长文本，无长度限制</li>
        </ul>
      </div>

      <div class="guide-section">
        <h4>⚠️ 注意事项</h4>
        <ul>
          <li>字符串类型优先使用NVARCHAR，支持中文显示</li>
          <li>金额计算使用DECIMAL，避免浮点精度问题</li>
          <li>日期时间根据精度需求选择DATE或DATETIME</li>
          <li>长度设置要考虑实际业务需求，不要过度设置</li>
          <li>支持自定义格式，如：VARCHAR(255) NOT NULL DEFAULT ''</li>
        </ul>
      </div>

      <div class="guide-section">
        <h4>🎯 快速选择</h4>
        <p>根据字段用途快速选择数据类型：</p>
        <div class="guide-example">
          <strong>名称类</strong>: NVARCHAR(200)<br>
          <strong>编码类</strong>: VARCHAR(50)<br>
          <strong>数量类</strong>: INT<br>
          <strong>金额类</strong>: DECIMAL(10,2)<br>
          <strong>日期类</strong>: DATE<br>
          <strong>时间类</strong>: DATETIME<br>
          <strong>状态类</strong>: TINYINT<br>
          <strong>备注类</strong>: TEXT
        </div>
      </div>
    </div>
  </div>

  <!-- 工具函数定义 -->
  

  <!-- 统一脚本 -->
  
  
  
  <!-- 智能日志工具 -->

    <!-- 新架构初始化脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 初始化新架构页面: unified-field-config.html');
            
            // 启动应用
            await window.startApp({
                environment: 'production',
                features: {
                "validation": true,
                "fieldDeduplication": true
}
            });
            
            // 获取所需组件
            const apiClient = window.ComponentManager.get('apiClient');
            const fieldUtils = window.ComponentManager.get('fieldUtils');
            const validator = window.ComponentManager.get('validationUtils');
            const enhancer = window.ComponentManager.get('fieldDeduplicationEnhancer');
            
            // 调用原始初始化逻辑
            if (typeof initializePage === 'function') {
                initializePage();
            }
            
            console.log('✅ 页面初始化完成: unified-field-config.html');
        });
        
        // 原始自定义脚本（已适配新架构）
        // ✅ 工具函数全局可用
            window.throttle = function (fn, delay = 200) {
              let timer = null;
              return function (...args) {
                if (timer) return;
                timer = setTimeout(() => {
                  fn.apply(this, args);
                  timer = null;
                }, delay);
              };
            };
        
            window.debounce = function (fn, delay = 300) {
              let timer = null;
              return function (...args) {
                clearTimeout(timer);
                timer = setTimeout(() => fn.apply(this, args), delay);
              };
            };
        
            window.autoResizeInput = function (input) {
              requestAnimationFrame(() => {
                input.style.height = 'auto';
                const newHeight = Math.min(input.scrollHeight, 200);
                if (input.style.height !== `${newHeight}px`) {
                  input.style.height = newHeight + 'px';
                }
              });
            };
        
            // 输入框自动调整高度事件监听
            document.addEventListener('input', (event) => {
              const target = event.target;
              if (target.classList.contains('chinese-input') || target.classList.contains('data-type-input')) {
                window.autoResizeInput(target);
              }
            });
        
        const API_BASE = window.location.origin;
            const TOKEN = localStorage.getItem('ys_api_token') || 'dev_token';
            
            // 初始化日志工具
            const logger = new SmartLogger('UnifiedFieldConfig', true);
        
            // ==================== 中文映射表 ====================
            window.MODULE_NAME_CN = {
              purchase_order: '采购订单',
              sales_order: '销售订单',
              production_order: '生产订单',
              inventory: '现存量',
              material_master: '物料档案',
              requirements_planning: '需求计划',
              applyorder: '请购单',
              materialout: '材料出库单',
              product_receipt: '产品入库单',
              purchase_receipt: '采购入库单',
              sales_out: '销售出库单',
              subcontract_order: '委外订单',
              subcontract_receipt: '委外入库单',
              subcontract_requisition: '委外请购单',
              inventory_report: '现存量报表'
            };
        
            // 统一替换函数
            function mapModuleName(key) {
              return MODULE_NAME_CN[key] || key;
            }
        
            // 获取 Element Plus 组件
            const { ElMessage } = ElementPlus;
        
            // 统一 fetch 加鉴权
            const _fetch = window.fetch;
            window.fetch = (...args) => {
              const [url, opts = {}] = args;
              // 避免重复添加Authorization头
              if (!opts.headers) opts.headers = {};
              if (!opts.headers.Authorization) {
                opts.headers.Authorization = `Bearer ${TOKEN}`;
              }
              return _fetch(url, opts);
            };
        
            const { createApp, ref, computed } = Vue;
            createApp({
              setup() {
                const modules = ref([]);
                const selectedModule = ref('');
                const userId = ref('Alice');
                const config = ref(null);
                const loading = ref(false);
                const saving = ref(false);
                const error = ref('');
                const success = ref('');
                const showGuide = ref(false);
                const currentEditingField = ref(null);
        
                const groupedFields = computed(() => {
                  if (!config.value?.fields) return {};
                  const map = { '基础字段': [], '业务字段': [], '物料字段': [], '其他字段': [] };
                  Object.entries(config.value.fields).forEach(([n, f]) => {
                    const g = n.toLowerCase().includes('material') ? '物料字段' :
                      /code|name|id|status/.test(n) ? '基础字段' :
                        /qty|amount|price|date/.test(n) ? '业务字段' : '其他字段';
                    map[g].push({ ...f, name: n });
                  });
                  return map;
                });
        
                const selectionRate = computed(() => {
                  if (!config.value?.total_fields) return 0;
                  return Math.round((config.value.selected_fields / config.value.total_fields) * 100);
                });
        
                const userModifiedCount = computed(() => {
                  if (!config.value?.fields) return 0;
                  return Object.values(config.value.fields).filter(f => f.user_modified).length;
                });
        
                function showDataTypeGuide() {
                  showGuide.value = true;
                }
        
                function hideDataTypeGuide() {
                  showGuide.value = false;
                }
        
                // 插入数据类型到当前编辑的输入框
                function insertDataType(dataType) {
                  if (currentEditingField.value) {
                    const input = document.querySelector(`[data-field="${currentEditingField.value}"] .data-type-input`);
                    if (input) {
                      input.value = dataType;
                      input.dispatchEvent(new Event('input', { bubbles: true }));
                      input.dispatchEvent(new Event('blur', { bubbles: true }));
                    }
                  }
                  ElMessage.success(`已插入数据类型: ${dataType}`);
                }
        
                // 处理中文名输入
                function handleChineseInput(fieldName, event) {
                  currentEditingField.value = fieldName;
                  config.value.fields[fieldName].chinese_name = event.target.value;
                  window.autoResizeInput(event.target);
                }
        
                // 处理数据类型输入
                function handleDataTypeInput(fieldName, event) {
                  currentEditingField.value = fieldName;
                  config.value.fields[fieldName].data_type = event.target.value;
                  window.autoResizeInput(event.target);
                }
        
                async function loadModules() {
                  logger.debug('开始加载模块列表...');
                  try {
                    logger.debug('请求URL:', `${API_BASE}/api/v1/unified/modules`);
                    const res = await fetch(`${API_BASE}/api/v1/unified/modules`);
                    logger.debug('响应状态:', res.status, res.statusText);
                    if (!res.ok) {
                      const errorText = await res.text();
                      logger.error('API错误:', errorText);
                      throw new Error(errorText);
                    }
                    const data = await res.json();
                    logger.debug('获取到模块数据:', data);
                    modules.value = data;
                  } catch (e) {
                    logger.error('加载模块失败:', e);
                    logger.debug('使用默认模块列表作为备选');
                    // 使用默认模块列表作为备选
                    modules.value = [
                      { module_name: 'purchase_order', display_name: '采购订单' },
                      { module_name: 'sales_order', display_name: '销售订单' },
                      { module_name: 'production_order', display_name: '生产订单' },
                      { module_name: 'product_receipt', display_name: '产品入库单' }
                    ];
                  }
                  console.log('【loadModules】模块列表加载完成:', modules.value);
                }
        
                async function loadConfig() {
                  if (!selectedModule.value) { error.value = '请先选择模块'; return; }
                  loading.value = true; error.value = ''; success.value = '';
                  try {
                    const startTime = performance.now();
                    const res = await fetch(`${API_BASE}/api/v1/unified/modules/${selectedModule.value}/fields?user_id=${userId.value}`);
                    if (!res.ok) throw new Error(await res.text());
                    const result = await res.json();
                    config.value = result.success ? result.data : result;
                    const endTime = performance.now();
                    console.log(`API响应时间: ${(endTime - startTime).toFixed(2)}ms`);
        
                    ElMessage.success('配置加载成功');
                  } catch (e) {
                    error.value = e.message;
                    ElMessage.error('加载失败: ' + e.message);
                  } finally { loading.value = false; }
                }
        
                async function updateSelection(field, isSelected) {
                  try {
                    const res = await fetch(`${API_BASE}/api/v1/unified/modules/${selectedModule.value}/fields/${field}?user_id=${userId.value}&is_selected=${isSelected}`, { method: 'PUT' });
                    if (!res.ok) throw new Error(await res.text());
                    const result = await res.json();
                    if (result.success) {
                      config.value.fields[field].is_selected = isSelected;
                      config.value.fields[field].user_modified = true;
                      config.value.selected_fields = Object.values(config.value.fields).filter(f => f.is_selected).length;
                    }
                    console.log('【updateSelection】操作后字段状态', JSON.parse(JSON.stringify(config.value.fields)));
                  } catch (e) {
                    error.value = e.message;
                    ElMessage.error('更新失败: ' + e.message);
                  }
                }
        
                async function saveChinese(field, event) {
                  try {
                    // 直接从DOM获取输入框的值，确保获取到用户实际输入的内容
                    const chineseName = event.target.value;
        
                    // 添加调试信息
                    console.log('【saveChinese】字段名:', field);
                    console.log('【saveChinese】中文名值:', chineseName);
                    console.log('【saveChinese】输入框元素:', event.target);
        
                    // 验证字段是否存在
                    if (!config.value.fields[field]) {
                      throw new Error(`字段 ${field} 不存在`);
                    }
        
                    // 验证中文名是否为空
                    if (!chineseName || chineseName.trim() === '') {
                      ElMessage.warning('中文名不能为空');
                      return;
                    }
        
                    const res = await fetch(`${API_BASE}/api/v1/unified/modules/${selectedModule.value}/fields/${field}/chinese_name?user_id=${userId.value}&chinese_name=${encodeURIComponent(chineseName)}`, {
                      method: 'PUT'
                    });
                    if (!res.ok) throw new Error(await res.text());
                    const result = await res.json();
                    if (result.success) {
                      // 更新本地数据
                      config.value.fields[field].chinese_name = chineseName;
                      config.value.fields[field].user_modified = true;
                      ElMessage.success('中文名保存成功');
                    }
                  } catch (e) {
                    error.value = e.message;
                    ElMessage.error('中文名保存失败: ' + e.message);
                  }
                }
        
                async function saveDataType(field, event) {
                  try {
                    const dataType = event.target.value;
                    console.log('【saveDataType】字段名:', field);
                    console.log('【saveDataType】数据类型值:', dataType);
                    console.log('【saveDataType】输入框元素:', event.target);
        
                    if (!config.value.fields[field]) {
                      throw new Error(`字段 ${field} 不存在`);
                    }
        
                    if (!dataType || dataType.trim() === '') {
                      ElMessage.warning('数据类型不能为空');
                      return;
                    }
        
                    const res = await fetch(`${API_BASE}/api/v1/unified/modules/${selectedModule.value}/fields/${field}/data_type?user_id=${userId.value}&data_type=${encodeURIComponent(dataType)}`, {
                      method: 'PUT'
                    });
                    if (!res.ok) throw new Error(await res.text());
                    const result = await res.json();
                    if (result.success) {
                      config.value.fields[field].data_type = dataType;
                      config.value.fields[field].user_modified = true;
                      ElMessage.success('数据类型保存成功');
                    }
                  } catch (e) {
                    error.value = e.message;
                    ElMessage.error('数据类型保存失败: ' + e.message);
                  }
                }
        
                async function toggleLock(field) {
                  try {
                    const newLocked = !config.value.fields[field].locked;
        
                    // 临时绕过：先本地切换，再尝试API
                    config.value.fields[field].locked = newLocked;
                    config.value.fields[field].user_modified = true;
                    ElMessage.success(newLocked ? '字段已锁定' : '字段已解锁');
        
                    // 尝试API调用（如果失败不影响本地状态）
                    try {
                      const res = await fetch(`${API_BASE}/api/v1/unified/modules/${selectedModule.value}/fields/${field}/lock?user_id=${userId.value}&locked=${newLocked}`, {
                        method: 'PUT'
                      });
                      if (res.ok) {
                        const result = await res.json();
                        console.log('Lock API调用成功:', result);
                      } else {
                        console.warn('Lock API调用失败，但本地状态已更新');
                      }
                    } catch (apiError) {
                      console.warn('Lock API调用异常，但本地状态已更新:', apiError.message);
                    }
                    // 移除性能消耗大的调试代码
                  } catch (e) {
                    error.value = e.message;
                    ElMessage.error('锁定操作失败: ' + e.message);
                  }
                }
        
                async function toggleGroup(group) {
                  const list = groupedFields.value[group];
                  const all = list.every(f => f.is_selected);
                  for (const f of list) {
                    if (!f.locked) { // 跳过锁定的字段
                      await updateSelection(f.name, !all);
                    }
                  }
                }
        
                async function saveAllChanges() {
                  saving.value = true;
                  try {
                    const res = await fetch(`${API_BASE}/api/v1/unified/modules/${selectedModule.value}/fields?user_id=${userId.value}`, {
                      method: 'PUT',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({ fields: config.value.fields })
                    });
                    if (!res.ok) throw new Error(await res.text());
                    const result = await res.json();
                    if (result.success) {
                      success.value = '保存成功';
                      ElMessage.success('所有更改已保存');
                    }
                  } catch (e) {
                    error.value = e.message;
                    ElMessage.error('保存失败: ' + e.message);
                  } finally { saving.value = false; }
                }
        
                async function debugFields() {
                  if (!config.value?.fields) {
                    ElMessage.warning('请先加载配置');
                    return;
                  }
        
                  console.log('=== 调试字段状态 ===');
                  console.log('当前模块:', selectedModule.value);
                  console.log('用户ID:', userId.value);
                  console.log('字段总数:', Object.keys(config.value.fields).length);
        
                  // 检查特定字段
                  const targetFields = ['accountOrg_name', 'accountorg_name', 'account_org_name'];
                  targetFields.forEach(field => {
                    if (config.value.fields[field]) {
                      console.log(`字段 ${field}:`, JSON.parse(JSON.stringify(config.value.fields[field])));
                    } else {
                      console.log(`字段 ${field}: 不存在`);
                    }
                  });
        
                  // 显示所有字段名
                  console.log('所有字段名:', Object.keys(config.value.fields));
        
                  ElMessage.success('调试信息已输出到控制台');
                }
        
                async function restoreField(fieldName) {
                  try {
                    if (!config.value?.fields?.[fieldName]) {
                      ElMessage.warning('字段不存在');
                      return;
                    }
        
                    const confirmed = confirm(`确定要恢复字段"${fieldName}"到基准配置吗？\n\n这将清除您对该字段的所有修改。`);
                    if (!confirmed) {
                      return;
                    }
        
                    // 调用恢复API
                    const res = await fetch(`${API_BASE}/api/v1/unified/modules/${selectedModule.value}/fields/${fieldName}/restore?user_id=${userId.value}`, {
                      method: 'PUT'
                    });
        
                    if (!res.ok) throw new Error(await res.text());
                    const result = await res.json();
        
                    if (result.success) {
                      // 更新本地数据
                      config.value.fields[fieldName] = result.data;
                      ElMessage.success('字段已恢复到基准配置');
                    }
                  } catch (e) {
                    error.value = e.message;
                    ElMessage.error('恢复失败: ' + e.message);
                  }
                }
        
                // 初始化
                console.log('【初始化】开始加载页面...');
                // 添加超时处理
                const initTimeout = setTimeout(() => {
                  console.error('【初始化】页面加载超时');
                  error.value = '页面加载超时，请刷新重试';
                }, 30000); // 30秒超时
        
                loadModules().then(() => {
                  clearTimeout(initTimeout);
                  console.log('【初始化】页面加载完成');
                }).catch(e => {
                  clearTimeout(initTimeout);
                  console.error('【初始化】页面加载失败:', e);
                  error.value = '页面加载失败: ' + e.message;
                });
        
                return {
                  modules, selectedModule, userId, config, loading, saving, error, success, showGuide,
                  groupedFields, selectionRate, userModifiedCount,
                  loadConfig, updateSelection, saveChinese, saveDataType, toggleLock, toggleGroup, saveAllChanges, debugFields,
                  showDataTypeGuide, hideDataTypeGuide, restoreField,
                  handleChineseInput, handleDataTypeInput, insertDataType
                };
              }
            }).use(ElementPlus).mount('#app');
            
            
    </script>
</body>
</html>