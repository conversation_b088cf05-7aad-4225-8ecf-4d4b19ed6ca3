{"generation_time": "2025-08-05T11:14:44.062619", "summary": {"total_files": 589, "total_lines": 667844, "total_code_lines": 640930}, "by_extension": {"": {"files": 6, "total_lines": 267, "code_lines": 189, "comment_lines": 38, "blank_lines": 40}, ".example": {"files": 1, "total_lines": 20, "code_lines": 6, "comment_lines": 11, "blank_lines": 3}, ".template": {"files": 14, "total_lines": 484, "code_lines": 348, "comment_lines": 59, "blank_lines": 77}, ".json": {"files": 66, "total_lines": 156276, "code_lines": 156276, "comment_lines": 0, "blank_lines": 0}, ".yaml": {"files": 1, "total_lines": 56, "code_lines": 45, "comment_lines": 6, "blank_lines": 5}, ".py": {"files": 196, "total_lines": 48844, "code_lines": 37296, "comment_lines": 3186, "blank_lines": 8362}, ".md": {"files": 95, "total_lines": 22377, "code_lines": 16971, "comment_lines": 2075, "blank_lines": 3331}, ".txt": {"files": 12, "total_lines": 123, "code_lines": 81, "comment_lines": 22, "blank_lines": 20}, ".bat": {"files": 40, "total_lines": 795, "code_lines": 677, "comment_lines": 8, "blank_lines": 110}, ".yml": {"files": 8, "total_lines": 690, "code_lines": 577, "comment_lines": 62, "blank_lines": 51}, ".ps1": {"files": 2, "total_lines": 56, "code_lines": 40, "comment_lines": 8, "blank_lines": 8}, ".html": {"files": 35, "total_lines": 13674, "code_lines": 11757, "comment_lines": 0, "blank_lines": 1917}, ".ini": {"files": 3, "total_lines": 48, "code_lines": 40, "comment_lines": 0, "blank_lines": 8}, ".backup_env": {"files": 1, "total_lines": 23, "code_lines": 20, "comment_lines": 0, "blank_lines": 3}, ".js": {"files": 49, "total_lines": 182181, "code_lines": 175636, "comment_lines": 10, "blank_lines": 6535}, ".css": {"files": 11, "total_lines": 3869, "code_lines": 3262, "comment_lines": 16, "blank_lines": 591}, ".backup_20250802_202310": {"files": 1, "total_lines": 893, "code_lines": 684, "comment_lines": 56, "blank_lines": 153}, ".xml": {"files": 30, "total_lines": 235021, "code_lines": 235019, "comment_lines": 0, "blank_lines": 2}, ".sh": {"files": 4, "total_lines": 292, "code_lines": 199, "comment_lines": 40, "blank_lines": 53}, ".xlsx": {"files": 14, "total_lines": 1855, "code_lines": 1807, "comment_lines": 25, "blank_lines": 23}}, "files": [{"path": ".dockerignore", "extension": "", "lines": 37, "code_lines": 22}, {"path": ".env.example", "extension": ".example", "lines": 20, "code_lines": 6}, {"path": ".env.template", "extension": ".template", "lines": 20, "code_lines": 6}, {"path": ".giti<PERSON>re", "extension": "", "lines": 11, "code_lines": 6}, {"path": ".ports.json", "extension": ".json", "lines": 96, "code_lines": 96}, {"path": ".pre-commit-config.yaml", "extension": ".yaml", "lines": 56, "code_lines": 45}, {"path": "ai_code_review_system.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "AI_DEVSECOPS_SECURITY_PIPELINE.md", "extension": ".md", "lines": 0, "code_lines": 0}, {"path": "analyze_project_stats.py", "extension": ".py", "lines": 71, "code_lines": 59}, {"path": "ANTI_DUPLICATE_SYSTEM.md", "extension": ".md", "lines": 198, "code_lines": 110}, {"path": "API访问解决方案.md", "extension": ".md", "lines": 76, "code_lines": 45}, {"path": "arch.md", "extension": ".md", "lines": 47, "code_lines": 32}, {"path": "auto_fix_comprehensive_issues.py", "extension": ".py", "lines": 477, "code_lines": 345}, {"path": "auto_project_cleanup.py", "extension": ".py", "lines": 276, "code_lines": 202}, {"path": "build_production_package.py", "extension": ".py", "lines": 544, "code_lines": 430}, {"path": "check_naming_conflicts.py", "extension": ".py", "lines": 133, "code_lines": 99}, {"path": "check_output.txt", "extension": ".txt", "lines": 0, "code_lines": 0}, {"path": "CICD-README.md", "extension": ".md", "lines": 138, "code_lines": 73}, {"path": "cicd_builder_simple.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "cicd_build_report.md", "extension": ".md", "lines": 82, "code_lines": 49}, {"path": "cicd_pipeline_builder.py", "extension": ".py", "lines": 738, "code_lines": 525}, {"path": "cicd_pipeline_builder_optimized.py", "extension": ".py", "lines": 738, "code_lines": 525}, {"path": "CICD_构建报告.md", "extension": ".md", "lines": 87, "code_lines": 47}, {"path": "cleanup_report.json", "extension": ".json", "lines": 128, "code_lines": 128}, {"path": "code-cleanup-implementation-report.md", "extension": ".md", "lines": 201, "code_lines": 123}, {"path": "CODEOWNERS", "extension": "", "lines": 49, "code_lines": 27}, {"path": "CODE_CLEANUP_REPORT.md", "extension": ".md", "lines": 122, "code_lines": 77}, {"path": "comprehensive_check_report.json", "extension": ".json", "lines": 152, "code_lines": 152}, {"path": "comprehensive_check_report.md", "extension": ".md", "lines": 96, "code_lines": 44}, {"path": "comprehensive_code_fixer.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "comprehensive_fix_report.json", "extension": ".json", "lines": 21, "code_lines": 21}, {"path": "comprehensive_fix_report.md", "extension": ".md", "lines": 53, "code_lines": 28}, {"path": "comprehensive_production_test.py", "extension": ".py", "lines": 816, "code_lines": 638}, {"path": "config_environmentizer.py", "extension": ".py", "lines": 112, "code_lines": 82}, {"path": "config_environmentizer_clean.py", "extension": ".py", "lines": 106, "code_lines": 78}, {"path": "core_mvp_extractor.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "day3_final_test.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "day3_test.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "day4_proxy_builder.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "day5_final_validator.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "deploy_production.bat", "extension": ".bat", "lines": 87, "code_lines": 75}, {"path": "docker-compose.yml", "extension": ".yml", "lines": 38, "code_lines": 38}, {"path": "Dockerfile", "extension": "", "lines": 34, "code_lines": 15}, {"path": "duplicate_cleanup_plan.json", "extension": ".json", "lines": 1044, "code_lines": 1044}, {"path": "execute_task_checklist.py", "extension": ".py", "lines": 642, "code_lines": 494}, {"path": "file_sentinel.py", "extension": ".py", "lines": 188, "code_lines": 145}, {"path": "final_project_fixer.py", "extension": ".py", "lines": 237, "code_lines": 172}, {"path": "fix_build_script.py", "extension": ".py", "lines": 251, "code_lines": 185}, {"path": "fix_execute_task_script.py", "extension": ".py", "lines": 177, "code_lines": 131}, {"path": "fix_execution_report.md", "extension": ".md", "lines": 40, "code_lines": 23}, {"path": "fix_issues.py", "extension": ".py", "lines": 79, "code_lines": 66}, {"path": "fix_python_environment.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "fix_python_path.ps1", "extension": ".ps1", "lines": 0, "code_lines": 0}, {"path": "fix_task_issues.py", "extension": ".py", "lines": 460, "code_lines": 320}, {"path": "function_duplicate_checker.py", "extension": ".py", "lines": 97, "code_lines": 69}, {"path": "generate_stats.py", "extension": ".py", "lines": 175, "code_lines": 137}, {"path": "health_check_python310.bat", "extension": ".bat", "lines": 20, "code_lines": 16}, {"path": "hosts_config.txt", "extension": ".txt", "lines": 15, "code_lines": 8}, {"path": "improvement_plan.md", "extension": ".md", "lines": 85, "code_lines": 43}, {"path": "install_ai_devsecops_pipeline.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "install_windows_service.py", "extension": ".py", "lines": 180, "code_lines": 129}, {"path": "Jenkins<PERSON><PERSON>", "extension": "", "lines": 112, "code_lines": 103}, {"path": "machine_cleanup.py", "extension": ".py", "lines": 313, "code_lines": 245}, {"path": "machine_cleanup_report.json", "extension": ".json", "lines": 70, "code_lines": 70}, {"path": "MACHINE_CLEANUP_SUCCESS_REPORT.md", "extension": ".md", "lines": 108, "code_lines": 63}, {"path": "migration_dashboard_fixed.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": "mvp_launcher.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "naming_suggestions.txt", "extension": ".txt", "lines": 30, "code_lines": 28}, {"path": "PORT_ALLOCATION_CORRECTION_REPORT.md", "extension": ".md", "lines": 138, "code_lines": 92}, {"path": "port_locker.py", "extension": ".py", "lines": 196, "code_lines": 165}, {"path": "PORT_LOCKING_IMPLEMENTATION_REPORT.md", "extension": ".md", "lines": 153, "code_lines": 91}, {"path": "production_readiness_report.py", "extension": ".py", "lines": 724, "code_lines": 516}, {"path": "production_readiness_report_20250802_212920.json", "extension": ".json", "lines": 186, "code_lines": 186}, {"path": "production_readiness_report_20250802_212920.md", "extension": ".md", "lines": 117, "code_lines": 63}, {"path": "production_readiness_report_20250804_013940.json", "extension": ".json", "lines": 186, "code_lines": 186}, {"path": "production_readiness_report_20250804_013940.md", "extension": ".md", "lines": 117, "code_lines": 63}, {"path": "production_readiness_report_20250804_014728.json", "extension": ".json", "lines": 186, "code_lines": 186}, {"path": "production_readiness_report_20250804_014728.md", "extension": ".md", "lines": 117, "code_lines": 63}, {"path": "production_test_runner.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "PROJECT_COMPLETION_REPORT.md", "extension": ".md", "lines": 221, "code_lines": 127}, {"path": "project_health_check.py", "extension": ".py", "lines": 570, "code_lines": 445}, {"path": "project_health_checker.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "project_health_report.json", "extension": ".json", "lines": 1417, "code_lines": 1417}, {"path": "project_health_report.txt", "extension": ".txt", "lines": 8, "code_lines": 7}, {"path": "PYTHON_VERSION_COMPATIBILITY_GUIDE.md", "extension": ".md", "lines": 0, "code_lines": 0}, {"path": "python_version_diagnostic.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "PYTHON_VERSION_GUIDE.md", "extension": ".md", "lines": 31, "code_lines": 15}, {"path": "QuickStart.bat", "extension": ".bat", "lines": 56, "code_lines": 49}, {"path": "quick_health_check.py", "extension": ".py", "lines": 285, "code_lines": 219}, {"path": "quick_health_check_fixed.py", "extension": ".py", "lines": 71, "code_lines": 54}, {"path": "quick_health_report_20250802_210530.json", "extension": ".json", "lines": 33, "code_lines": 33}, {"path": "quick_health_report_20250802_210539.json", "extension": ".json", "lines": 33, "code_lines": 33}, {"path": "quick_health_report_20250804_000515.json", "extension": ".json", "lines": 33, "code_lines": 33}, {"path": "quick_health_report_20250804_000530.json", "extension": ".json", "lines": 33, "code_lines": 33}, {"path": "quick_health_report_20250804_001017.json", "extension": ".json", "lines": 33, "code_lines": 33}, {"path": "quick_health_report_20250804_001940.json", "extension": ".json", "lines": 33, "code_lines": 33}, {"path": "quick_health_report_20250804_014659.json", "extension": ".json", "lines": 33, "code_lines": 33}, {"path": "quick_test.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "README.md", "extension": ".md", "lines": 76, "code_lines": 45}, {"path": "real_data_full_test.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "refactor-tasks-completed.md", "extension": ".md", "lines": 0, "code_lines": 0}, {"path": "refactoring_suggestions.md", "extension": ".md", "lines": 139, "code_lines": 47}, {"path": "refactor_data_write_manager.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "refactor_fast_sync_service.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "refactor_ys_api_client.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "remaining_issues_fixer.py", "extension": ".py", "lines": 384, "code_lines": 266}, {"path": "remaining_issues_fix_report.md", "extension": ".md", "lines": 59, "code_lines": 31}, {"path": "run_comprehensive_check.py", "extension": ".py", "lines": 436, "code_lines": 338}, {"path": "run_test.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "security_environmentization_report.md", "extension": ".md", "lines": 70, "code_lines": 39}, {"path": "security_recommendations.md", "extension": ".md", "lines": 15, "code_lines": 7}, {"path": "service_ports_dashboard.html", "extension": ".html", "lines": 441, "code_lines": 388}, {"path": "setup_ide_security_plugins.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "setup_test_env.py", "extension": ".py", "lines": 75, "code_lines": 57}, {"path": "shit_mountain_mapper.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "SimpleStart.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "smart_duplicate_cleaner.py", "extension": ".py", "lines": 193, "code_lines": 140}, {"path": "smart_file_creator.py", "extension": ".py", "lines": 210, "code_lines": 160}, {"path": "start_architecture_test.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "start_backend.bat", "extension": ".bat", "lines": 59, "code_lines": 50}, {"path": "start_backend_locked.bat", "extension": ".bat", "lines": 6, "code_lines": 1}, {"path": "start_backend_simple.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "start_frontend.bat", "extension": ".bat", "lines": 37, "code_lines": 34}, {"path": "start_frontend_locked.bat", "extension": ".bat", "lines": 6, "code_lines": 1}, {"path": "start_frontend_new.bat", "extension": ".bat", "lines": 54, "code_lines": 46}, {"path": "start_quick_test.py", "extension": ".py", "lines": 81, "code_lines": 65}, {"path": "start_reliable_server.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "start_server.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "start_server_fixed.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "start_test_server.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "start_unified.bat", "extension": ".bat", "lines": 23, "code_lines": 19}, {"path": "start_with_python310.bat", "extension": ".bat", "lines": 34, "code_lines": 28}, {"path": "start_ysapi.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "status_check.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "systematic_duplicate_detector.py", "extension": ".py", "lines": 331, "code_lines": 245}, {"path": "systematic_migration_starter.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "system_integration_final.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "SYSTEM_READINESS_REPORT.md", "extension": ".md", "lines": 83, "code_lines": 46}, {"path": "TASK.md", "extension": ".md", "lines": 272, "code_lines": 182}, {"path": "task_execution_report.md", "extension": ".md", "lines": 44, "code_lines": 28}, {"path": "test_anti_duplicate_system.py", "extension": ".py", "lines": 119, "code_lines": 81}, {"path": "test_baseline_api.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "test_health_checker.py", "extension": ".py", "lines": 3, "code_lines": 2}, {"path": "test_mvp.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "test_proxy.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "test_sentinel.py", "extension": ".py", "lines": 1, "code_lines": 0}, {"path": "test_server.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "universal_code_fix_report.md", "extension": ".md", "lines": 68, "code_lines": 47}, {"path": "universal_code_quality_fixer.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "unused_imports.txt", "extension": ".txt", "lines": 2, "code_lines": 2}, {"path": "unused_import_checker.py", "extension": ".py", "lines": 107, "code_lines": 79}, {"path": "verify_field_fetching.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "week1_completion_report.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "week1_completion_week2_planning.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "week1_quick_start.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "week1_week2_integration_demo.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "week2_completion_report.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "week3_integration_final.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "YS-API_SERVICE_PORTS_REFERENCE.md", "extension": ".md", "lines": 175, "code_lines": 96}, {"path": "一键启动.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "启动后端指南.md", "extension": ".md", "lines": 68, "code_lines": 36}, {"path": "启动器.bat", "extension": ".bat", "lines": 183, "code_lines": 157}, {"path": "启动指南.md", "extension": ".md", "lines": 249, "code_lines": 121}, {"path": "启动服务器.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "完整启动指南.md", "extension": ".md", "lines": 137, "code_lines": 84}, {"path": "工程分析报告.md", "extension": ".md", "lines": 133, "code_lines": 95}, {"path": "快速访问.html", "extension": ".html", "lines": 419, "code_lines": 371}, {"path": "手动启动指南.md", "extension": ".md", "lines": 75, "code_lines": 44}, {"path": "测试指南.md", "extension": ".md", "lines": 57, "code_lines": 35}, {"path": "系统导航.html", "extension": ".html", "lines": 459, "code_lines": 398}, {"path": "路径修复指南.bat", "extension": ".bat", "lines": 29, "code_lines": 29}, {"path": "项目分析与任务规划.md", "extension": ".md", "lines": 196, "code_lines": 105}, {"path": ".cleanup_trash\\config.ini", "extension": ".ini", "lines": 24, "code_lines": 20}, {"path": ".cleanup_trash\\config.ini.backup_env", "extension": ".backup_env", "lines": 23, "code_lines": 20}, {"path": ".cleanup_trash\\deploy.template", "extension": ".template", "lines": 69, "code_lines": 44}, {"path": ".cleanup_trash\\deploy_windows.template", "extension": ".template", "lines": 81, "code_lines": 70}, {"path": ".cleanup_trash\\dockerfile.template", "extension": ".template", "lines": 34, "code_lines": 15}, {"path": ".cleanup_trash\\dup_check_output2.txt", "extension": ".txt", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_check_with_api.txt", "extension": ".txt", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_cicd_builder_simple.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_database-v2.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_excel-translation.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_field-config-manual-migrated.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_field-config-manual.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_final_check.txt", "extension": ".txt", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_final_check2.txt", "extension": ".txt", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_final_check_complete.txt", "extension": ".txt", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_maintenance.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_method-fix-test.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_migration-test.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_new-architecture-test.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_path-test.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_production_test_runner.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_project_health_checker.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_refactor-tasks-completed.md", "extension": ".md", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_run_test.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_SimpleStart.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_start_architecture_test.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_start_backend_simple.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_start_reliable_server.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_start_server.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_start_server_fixed.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_start_test_server.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_test_baseline_api.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_test_server.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_unified-field-config-fix.js", "extension": ".js", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_unified-field-config.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_universal_code_quality_fixer.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_一键启动.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\dup_启动服务器.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\element-plus-icons.iife.min.js", "extension": ".js", "lines": 3, "code_lines": 2}, {"path": ".cleanup_trash\\element-plus.css", "extension": ".css", "lines": 1, "code_lines": 1}, {"path": ".cleanup_trash\\element-plus.js", "extension": ".js", "lines": 62904, "code_lines": 61319}, {"path": ".cleanup_trash\\jenkinsfile.template", "extension": ".template", "lines": 112, "code_lines": 103}, {"path": ".cleanup_trash\\realtime-log.css", "extension": ".css", "lines": 494, "code_lines": 415}, {"path": ".cleanup_trash\\rollback.template", "extension": ".template", "lines": 56, "code_lines": 33}, {"path": ".cleanup_trash\\SECURITY_RECOMMENDATIONS_config.ini.md", "extension": ".md", "lines": 29, "code_lines": 16}, {"path": ".cleanup_trash\\smart-logger.css", "extension": ".css", "lines": 321, "code_lines": 266}, {"path": ".cleanup_trash\\test_server.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": ".cleanup_trash\\vue.global.js", "extension": ".js", "lines": 18193, "code_lines": 18063}, {"path": ".cleanup_trash\\zero_downtime_implementation.py.backup_20250802_202310", "extension": ".backup_20250802_202310", "lines": 893, "code_lines": 684}, {"path": ".cleanup_trash\\__init__.py", "extension": ".py", "lines": 1, "code_lines": 0}, {"path": ".cleanup_trash\\采购订单列表.xml", "extension": ".xml", "lines": 5909, "code_lines": 5909}, {"path": ".file_sentinels\\ai_creation_log.txt", "extension": ".txt", "lines": 4, "code_lines": 3}, {"path": ".file_sentinels\\creation_registry.json", "extension": ".json", "lines": 31, "code_lines": 31}, {"path": ".github\\workflows\\main.yml", "extension": ".yml", "lines": 50, "code_lines": 50}, {"path": "backend\\.env", "extension": "", "lines": 24, "code_lines": 16}, {"path": "backend\\BACKEND_TESTING_DEPLOYMENT_REPORT.md", "extension": ".md", "lines": 0, "code_lines": 0}, {"path": "backend\\config.ini", "extension": ".ini", "lines": 24, "code_lines": 20}, {"path": "backend\\DATA_COMPLIANCE_CORRECTION_REPORT.md", "extension": ".md", "lines": 0, "code_lines": 0}, {"path": "backend\\quick_test.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "backend\\README_TESTS.md", "extension": ".md", "lines": 0, "code_lines": 0}, {"path": "backend\\requirements.txt", "extension": ".txt", "lines": 64, "code_lines": 33}, {"path": "backend\\run_backend_tests.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "backend\\run_tests.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "backend\\SECURITY_RECOMMENDATIONS_config.ini.md", "extension": ".md", "lines": 29, "code_lines": 16}, {"path": "backend\\start_server.py", "extension": ".py", "lines": 48, "code_lines": 40}, {"path": "backend\\start_server_clean.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "backend\\start_server_fixed.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "backend\\start_simple.py", "extension": ".py", "lines": 124, "code_lines": 89}, {"path": "backend\\test_api_endpoints.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "backend\\test_backend_automation.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "backend\\test_config.ini", "extension": ".ini", "lines": 0, "code_lines": 0}, {"path": "backend\\test_database_operations.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "backend\\__init__.py", "extension": ".py", "lines": 1, "code_lines": 0}, {"path": "backend\\app\\main.py", "extension": ".py", "lines": 503, "code_lines": 412}, {"path": "backend\\app\\main_original.py", "extension": ".py", "lines": 532, "code_lines": 421}, {"path": "backend\\app\\api\\__init__.py", "extension": ".py", "lines": 4, "code_lines": 4}, {"path": "backend\\app\\api\\v1\\auth.py", "extension": ".py", "lines": 13, "code_lines": 10}, {"path": "backend\\app\\api\\v1\\config.py", "extension": ".py", "lines": 758, "code_lines": 594}, {"path": "backend\\app\\api\\v1\\database.py", "extension": ".py", "lines": 315, "code_lines": 252}, {"path": "backend\\app\\api\\v1\\database_health.py", "extension": ".py", "lines": 82, "code_lines": 60}, {"path": "backend\\app\\api\\v1\\enhanced_sync.py", "extension": ".py", "lines": 275, "code_lines": 220}, {"path": "backend\\app\\api\\v1\\excel_translation.py", "extension": ".py", "lines": 361, "code_lines": 272}, {"path": "backend\\app\\api\\v1\\field_config_api.py", "extension": ".py", "lines": 621, "code_lines": 479}, {"path": "backend\\app\\api\\v1\\maintenance.py", "extension": ".py", "lines": 102, "code_lines": 71}, {"path": "backend\\app\\api\\v1\\monitor.py", "extension": ".py", "lines": 1030, "code_lines": 775}, {"path": "backend\\app\\api\\v1\\realtime_logs.py", "extension": ".py", "lines": 117, "code_lines": 90}, {"path": "backend\\app\\api\\v1\\sync.py", "extension": ".py", "lines": 1452, "code_lines": 1010}, {"path": "backend\\app\\api\\v1\\sync_status.py", "extension": ".py", "lines": 100, "code_lines": 77}, {"path": "backend\\app\\api\\v1\\tasks.py", "extension": ".py", "lines": 497, "code_lines": 376}, {"path": "backend\\app\\api\\v1\\unified_field_config.py", "extension": ".py", "lines": 641, "code_lines": 535}, {"path": "backend\\app\\api\\v1\\__init__.py", "extension": ".py", "lines": 32, "code_lines": 30}, {"path": "backend\\app\\config\\modules.json", "extension": ".json", "lines": 130, "code_lines": 130}, {"path": "backend\\app\\core\\code_quality.py", "extension": ".py", "lines": 696, "code_lines": 555}, {"path": "backend\\app\\core\\config.py", "extension": ".py", "lines": 316, "code_lines": 263}, {"path": "backend\\app\\core\\database.py", "extension": ".py", "lines": 187, "code_lines": 133}, {"path": "backend\\app\\core\\database_connection_pool.py", "extension": ".py", "lines": 527, "code_lines": 384}, {"path": "backend\\app\\core\\database_manager.py", "extension": ".py", "lines": 162, "code_lines": 125}, {"path": "backend\\app\\core\\exceptions.py", "extension": ".py", "lines": 807, "code_lines": 611}, {"path": "backend\\app\\core\\optimized_retry.py", "extension": ".py", "lines": 336, "code_lines": 240}, {"path": "backend\\app\\core\\__init__.py", "extension": ".py", "lines": 1, "code_lines": 0}, {"path": "backend\\app\\middleware\\access_log.py", "extension": ".py", "lines": 61, "code_lines": 40}, {"path": "backend\\app\\schemas\\base.py", "extension": ".py", "lines": 124, "code_lines": 93}, {"path": "backend\\app\\schemas\\config.py", "extension": ".py", "lines": 274, "code_lines": 231}, {"path": "backend\\app\\schemas\\database.py", "extension": ".py", "lines": 81, "code_lines": 56}, {"path": "backend\\app\\schemas\\monitor.py", "extension": ".py", "lines": 103, "code_lines": 76}, {"path": "backend\\app\\schemas\\realtime_log.py", "extension": ".py", "lines": 83, "code_lines": 59}, {"path": "backend\\app\\schemas\\sync.py", "extension": ".py", "lines": 194, "code_lines": 150}, {"path": "backend\\app\\schemas\\__init__.py", "extension": ".py", "lines": 4, "code_lines": 4}, {"path": "backend\\app\\services\\auto_recovery_manager_enhanced.py", "extension": ".py", "lines": 879, "code_lines": 674}, {"path": "backend\\app\\services\\auto_sync_scheduler.py", "extension": ".py", "lines": 723, "code_lines": 550}, {"path": "backend\\app\\services\\business_translation_rules.py", "extension": ".py", "lines": 384, "code_lines": 298}, {"path": "backend\\app\\services\\config_persistence_service.py", "extension": ".py", "lines": 361, "code_lines": 263}, {"path": "backend\\app\\services\\database_manager.py", "extension": ".py", "lines": 899, "code_lines": 673}, {"path": "backend\\app\\services\\database_table_manager.py", "extension": ".py", "lines": 1400, "code_lines": 1070}, {"path": "backend\\app\\services\\data_processor.py", "extension": ".py", "lines": 535, "code_lines": 408}, {"path": "backend\\app\\services\\data_write_manager.py", "extension": ".py", "lines": 2250, "code_lines": 1694}, {"path": "backend\\app\\services\\enhanced_json_field_matcher.py", "extension": ".py", "lines": 223, "code_lines": 173}, {"path": "backend\\app\\services\\excel_field_matcher.py", "extension": ".py", "lines": 571, "code_lines": 457}, {"path": "backend\\app\\services\\excel_field_matcher_pretranslated.py", "extension": ".py", "lines": 269, "code_lines": 206}, {"path": "backend\\app\\services\\fast_sync_service.py", "extension": ".py", "lines": 2046, "code_lines": 1606}, {"path": "backend\\app\\services\\field_analysis_service.py", "extension": ".py", "lines": 510, "code_lines": 375}, {"path": "backend\\app\\services\\field_config_service.py", "extension": ".py", "lines": 383, "code_lines": 311}, {"path": "backend\\app\\services\\field_extractor_service.py", "extension": ".py", "lines": 641, "code_lines": 486}, {"path": "backend\\app\\services\\field_validation_service.py", "extension": ".py", "lines": 262, "code_lines": 185}, {"path": "backend\\app\\services\\field_value_mapping_service.py", "extension": ".py", "lines": 792, "code_lines": 729}, {"path": "backend\\app\\services\\intelligent_field_mapper.py", "extension": ".py", "lines": 641, "code_lines": 468}, {"path": "backend\\app\\services\\log_service.py", "extension": ".py", "lines": 61, "code_lines": 46}, {"path": "backend\\app\\services\\maintenance_manager.py", "extension": ".py", "lines": 349, "code_lines": 265}, {"path": "backend\\app\\services\\material_master_scheduler.py", "extension": ".py", "lines": 468, "code_lines": 382}, {"path": "backend\\app\\services\\md_parser.py", "extension": ".py", "lines": 765, "code_lines": 576}, {"path": "backend\\app\\services\\monitor_service.py", "extension": ".py", "lines": 55, "code_lines": 44}, {"path": "backend\\app\\services\\realtime_log_service.py", "extension": ".py", "lines": 198, "code_lines": 158}, {"path": "backend\\app\\services\\retry_helper.py", "extension": ".py", "lines": 231, "code_lines": 172}, {"path": "backend\\app\\services\\robust_json_parser.py", "extension": ".py", "lines": 618, "code_lines": 451}, {"path": "backend\\app\\services\\status_mapping_service.py", "extension": ".py", "lines": 342, "code_lines": 294}, {"path": "backend\\app\\services\\sync_status_manager.py", "extension": ".py", "lines": 231, "code_lines": 189}, {"path": "backend\\app\\services\\task_service.py", "extension": ".py", "lines": 92, "code_lines": 73}, {"path": "backend\\app\\services\\token_service.py", "extension": ".py", "lines": 48, "code_lines": 39}, {"path": "backend\\app\\services\\unified_field_manager.py", "extension": ".py", "lines": 952, "code_lines": 758}, {"path": "backend\\app\\services\\unified_field_service.py", "extension": ".py", "lines": 532, "code_lines": 414}, {"path": "backend\\app\\services\\ys_api_client.py", "extension": ".py", "lines": 1544, "code_lines": 1193}, {"path": "backend\\app\\services\\zero_downtime_implementation.py", "extension": ".py", "lines": 893, "code_lines": 684}, {"path": "backend\\app\\services\\__init__.py", "extension": ".py", "lines": 8, "code_lines": 4}, {"path": "config\\auto_sync_config_optimized.json", "extension": ".json", "lines": 76, "code_lines": 76}, {"path": "config\\logging_config.json", "extension": ".json", "lines": 258, "code_lines": 258}, {"path": "config\\material_master_schedule.json", "extension": ".json", "lines": 10, "code_lines": 10}, {"path": "config\\modules.json", "extension": ".json", "lines": 107, "code_lines": 107}, {"path": "config\\monitoring_config.json", "extension": ".json", "lines": 19, "code_lines": 19}, {"path": "config\\baselines\\applyorder_baseline.json", "extension": ".json", "lines": 2780, "code_lines": 2780}, {"path": "config\\baselines\\business_log_baseline.json", "extension": ".json", "lines": 414, "code_lines": 414}, {"path": "config\\baselines\\inventory_report_baseline.json", "extension": ".json", "lines": 1493, "code_lines": 1493}, {"path": "config\\baselines\\materialout_baseline.json", "extension": ".json", "lines": 2065, "code_lines": 2065}, {"path": "config\\baselines\\material_master_baseline.json", "extension": ".json", "lines": 3508, "code_lines": 3508}, {"path": "config\\baselines\\production_order_baseline.json", "extension": ".json", "lines": 4236, "code_lines": 4236}, {"path": "config\\baselines\\product_receipt_baseline.json", "extension": ".json", "lines": 1051, "code_lines": 1051}, {"path": "config\\baselines\\purchase_order_baseline.json", "extension": ".json", "lines": 1506, "code_lines": 1506}, {"path": "config\\baselines\\purchase_receipt_baseline.json", "extension": ".json", "lines": 1922, "code_lines": 1922}, {"path": "config\\baselines\\requirements_planning_baseline.json", "extension": ".json", "lines": 1194, "code_lines": 1194}, {"path": "config\\baselines\\sales_order_baseline.json", "extension": ".json", "lines": 3976, "code_lines": 3976}, {"path": "config\\baselines\\sales_out_baseline.json", "extension": ".json", "lines": 2078, "code_lines": 2078}, {"path": "config\\baselines\\subcontract_order_baseline.json", "extension": ".json", "lines": 611, "code_lines": 611}, {"path": "config\\baselines\\subcontract_receipt_baseline.json", "extension": ".json", "lines": 1844, "code_lines": 1844}, {"path": "config\\baselines\\subcontract_requisition_baseline.json", "extension": ".json", "lines": 752, "code_lines": 752}, {"path": "config\\data\\user_field_config\\Alice\\example.json", "extension": ".json", "lines": 8, "code_lines": 8}, {"path": "config\\data\\user_field_config\\Alice\\sales_order.json", "extension": ".json", "lines": 20, "code_lines": 20}, {"path": "config\\data\\user_field_config\\TestUser\\material_master.json", "extension": ".json", "lines": 44, "code_lines": 44}, {"path": "config\\data\\user_field_config\\TestUser\\purchase_order.json", "extension": ".json", "lines": 44, "code_lines": 44}, {"path": "config\\data\\user_field_config\\TestUser\\sales_order.json", "extension": ".json", "lines": 50, "code_lines": 50}, {"path": "config\\monitoring\\alert_rules.yml", "extension": ".yml", "lines": 196, "code_lines": 161}, {"path": "config\\monitoring\\alert_rules_enhanced.yml", "extension": ".yml", "lines": 203, "code_lines": 173}, {"path": "config\\monitoring\\prometheus.yml", "extension": ".yml", "lines": 77, "code_lines": 50}, {"path": "config\\monitoring\\prometheus_enhanced.yml", "extension": ".yml", "lines": 91, "code_lines": 70}, {"path": "core\\start_mvp_simple.bat", "extension": ".bat", "lines": 0, "code_lines": 0}, {"path": "core\\backend\\start_server.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "core\\backend\\app\\__init__.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "core\\backend\\app\\api\\__init__.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "core\\backend\\app\\api\\v1\\__init__.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "core\\backend\\app\\core\\__init__.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "core\\backend\\app\\services\\__init__.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "deploy\\deploy.ps1", "extension": ".ps1", "lines": 56, "code_lines": 40}, {"path": "deploy\\deploy.sh", "extension": ".sh", "lines": 52, "code_lines": 35}, {"path": "dev-tools\\cleanup\\code_cleaner.py", "extension": ".py", "lines": 294, "code_lines": 216}, {"path": "dev-tools\\mock\\mock_utils.py", "extension": ".py", "lines": 262, "code_lines": 204}, {"path": "dev-tools\\mock\\README.md", "extension": ".md", "lines": 216, "code_lines": 122}, {"path": "docs\\01-项目概览.md", "extension": ".md", "lines": 166, "code_lines": 111}, {"path": "docs\\02-系统架构设计.md", "extension": ".md", "lines": 267, "code_lines": 188}, {"path": "docs\\05-API接口规范.md", "extension": ".md", "lines": 654, "code_lines": 487}, {"path": "docs\\06-字段映射规范.md", "extension": ".md", "lines": 702, "code_lines": 451}, {"path": "docs\\07-CICD流水线文档.md", "extension": ".md", "lines": 208, "code_lines": 119}, {"path": "docs\\09-问题解决记录.md", "extension": ".md", "lines": 693, "code_lines": 495}, {"path": "docs\\10-代码优化进度文档.md", "extension": ".md", "lines": 281, "code_lines": 177}, {"path": "docs\\11-第二阶段整合部署指南.md", "extension": ".md", "lines": 299, "code_lines": 212}, {"path": "docs\\12-生产部署指南.md", "extension": ".md", "lines": 141, "code_lines": 106}, {"path": "docs\\13-页面迁移清单.md", "extension": ".md", "lines": 203, "code_lines": 133}, {"path": "docs\\14-迁移验证报告-field-config-manual.md", "extension": ".md", "lines": 167, "code_lines": 107}, {"path": "docs\\15-迁移验证报告-database-v2.md", "extension": ".md", "lines": 258, "code_lines": 157}, {"path": "docs\\16-迁移后清理指南.md", "extension": ".md", "lines": 265, "code_lines": 169}, {"path": "docs\\17-服务器启动指南.md", "extension": ".md", "lines": 143, "code_lines": 84}, {"path": "docs\\18-迁移页面修复完成报告.md", "extension": ".md", "lines": 124, "code_lines": 78}, {"path": "docs\\code-quality-fixes.md", "extension": ".md", "lines": 231, "code_lines": 148}, {"path": "docs\\logging-guidelines.md", "extension": ".md", "lines": 988, "code_lines": 749}, {"path": "docs\\代码质量修复报告.md", "extension": ".md", "lines": 368, "code_lines": 231}, {"path": "docs\\字段配置保存位置说明.md", "extension": ".md", "lines": 253, "code_lines": 169}, {"path": "docs\\必读.md", "extension": ".md", "lines": 215, "code_lines": 140}, {"path": "docs\\最终整合验证报告.md", "extension": ".md", "lines": 344, "code_lines": 197}, {"path": "docs\\错误处理规范.md", "extension": ".md", "lines": 623, "code_lines": 429}, {"path": "docs\\项目架构文档.md", "extension": ".md", "lines": 981, "code_lines": 708}, {"path": "excel\\产品入库.xlsx", "extension": ".xlsx", "lines": 93, "code_lines": 93}, {"path": "excel\\委外入库.xlsx", "extension": ".xlsx", "lines": 98, "code_lines": 97}, {"path": "excel\\委外申请单.xlsx", "extension": ".xlsx", "lines": 81, "code_lines": 80}, {"path": "excel\\委外订单.xlsx", "extension": ".xlsx", "lines": 89, "code_lines": 88}, {"path": "excel\\材料出库.xlsx", "extension": ".xlsx", "lines": 94, "code_lines": 93}, {"path": "excel\\物料创建.xlsx", "extension": ".xlsx", "lines": 99, "code_lines": 98}, {"path": "excel\\现存量报表查询.xlsx", "extension": ".xlsx", "lines": 99, "code_lines": 96}, {"path": "excel\\生产订单.xlsx", "extension": ".xlsx", "lines": 620, "code_lines": 593}, {"path": "excel\\请购单.xlsx", "extension": ".xlsx", "lines": 87, "code_lines": 86}, {"path": "excel\\采购入库.xlsx", "extension": ".xlsx", "lines": 100, "code_lines": 99}, {"path": "excel\\采购订单.xlsx", "extension": ".xlsx", "lines": 87, "code_lines": 84}, {"path": "excel\\销售出库.xlsx", "extension": ".xlsx", "lines": 92, "code_lines": 92}, {"path": "excel\\销售订单列表.xlsx", "extension": ".xlsx", "lines": 100, "code_lines": 94}, {"path": "excel\\需求计划.xlsx", "extension": ".xlsx", "lines": 116, "code_lines": 114}, {"path": "frontend\\BASELINE_SAVE_IMPLEMENTATION.md", "extension": ".md", "lines": 252, "code_lines": 179}, {"path": "frontend\\component-diagnostic.html", "extension": ".html", "lines": 137, "code_lines": 118}, {"path": "frontend\\database-v2.html", "extension": ".html", "lines": 4425, "code_lines": 3796}, {"path": "frontend\\ERROR_HANDLER_IMPLEMENTATION.md", "extension": ".md", "lines": 243, "code_lines": 155}, {"path": "frontend\\excel-translation.html", "extension": ".html", "lines": 836, "code_lines": 725}, {"path": "frontend\\field-config-manual.html", "extension": ".html", "lines": 3079, "code_lines": 2595}, {"path": "frontend\\field-deduplication-enhancer.js", "extension": ".js", "lines": 548, "code_lines": 444}, {"path": "frontend\\FIELD_LIST_DISPLAY_IMPLEMENTATION.md", "extension": ".md", "lines": 451, "code_lines": 309}, {"path": "frontend\\input-validation-enhancer.js", "extension": ".js", "lines": 258, "code_lines": 224}, {"path": "frontend\\maintenance.html", "extension": ".html", "lines": 492, "code_lines": 426}, {"path": "frontend\\method-fix-test.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": "frontend\\migration-console.html", "extension": ".html", "lines": 789, "code_lines": 683}, {"path": "frontend\\migration-test-fixed.html", "extension": ".html", "lines": 43, "code_lines": 39}, {"path": "frontend\\migration-test.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": "frontend\\migration_report.md", "extension": ".md", "lines": 20, "code_lines": 12}, {"path": "frontend\\MODULE_SELECTOR_IMPLEMENTATION.md", "extension": ".md", "lines": 148, "code_lines": 91}, {"path": "frontend\\new-architecture-test.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": "frontend\\NOTIFICATION_SYSTEM_IMPLEMENTATION.md", "extension": ".md", "lines": 257, "code_lines": 177}, {"path": "frontend\\package-lock.json", "extension": ".json", "lines": 5171, "code_lines": 5171}, {"path": "frontend\\package.json", "extension": ".json", "lines": 31, "code_lines": 31}, {"path": "frontend\\PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md", "extension": ".md", "lines": 302, "code_lines": 197}, {"path": "frontend\\PROGRESS_DISPLAY_IMPLEMENTATION.md", "extension": ".md", "lines": 201, "code_lines": 133}, {"path": "frontend\\start_frontend.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "frontend\\unified-field-config-fix.js", "extension": ".js", "lines": 0, "code_lines": 0}, {"path": "frontend\\unified-field-config.html", "extension": ".html", "lines": 1246, "code_lines": 1099}, {"path": "frontend\\unified-page-loader.html", "extension": ".html", "lines": 473, "code_lines": 407}, {"path": "frontend\\USER_CONFIG_SAVE_IMPLEMENTATION.md", "extension": ".md", "lines": 369, "code_lines": 255}, {"path": "frontend\\css\\baseline-save.css", "extension": ".css", "lines": 260, "code_lines": 225}, {"path": "frontend\\css\\element-plus.css", "extension": ".css", "lines": 1, "code_lines": 1}, {"path": "frontend\\css\\error-handler.css", "extension": ".css", "lines": 791, "code_lines": 672}, {"path": "frontend\\css\\field-renderer.css", "extension": ".css", "lines": 314, "code_lines": 268}, {"path": "frontend\\css\\notification-system.css", "extension": ".css", "lines": 524, "code_lines": 448}, {"path": "frontend\\css\\realtime-log.css", "extension": ".css", "lines": 494, "code_lines": 415}, {"path": "frontend\\css\\smart-logger.css", "extension": ".css", "lines": 321, "code_lines": 266}, {"path": "frontend\\css\\user-config-save.css", "extension": ".css", "lines": 348, "code_lines": 285}, {"path": "frontend\\js\\api-config-fix.js", "extension": ".js", "lines": 106, "code_lines": 91}, {"path": "frontend\\js\\api-config.js", "extension": ".js", "lines": 341, "code_lines": 291}, {"path": "frontend\\js\\api-unified.js", "extension": ".js", "lines": 222, "code_lines": 189}, {"path": "frontend\\js\\baseline-save.js", "extension": ".js", "lines": 319, "code_lines": 265}, {"path": "frontend\\js\\element-plus-icons.iife.min.js", "extension": ".js", "lines": 3, "code_lines": 2}, {"path": "frontend\\js\\element-plus.js", "extension": ".js", "lines": 62904, "code_lines": 61319}, {"path": "frontend\\js\\field-list-display.js", "extension": ".js", "lines": 1393, "code_lines": 1200}, {"path": "frontend\\js\\field-statistics.js", "extension": ".js", "lines": 696, "code_lines": 595}, {"path": "frontend\\js\\notification-system.js", "extension": ".js", "lines": 734, "code_lines": 635}, {"path": "frontend\\js\\performance-optimizer.js", "extension": ".js", "lines": 841, "code_lines": 693}, {"path": "frontend\\js\\realtime-log.js", "extension": ".js", "lines": 398, "code_lines": 340}, {"path": "frontend\\js\\unified-components.js", "extension": ".js", "lines": 466, "code_lines": 406}, {"path": "frontend\\js\\user-config-save.js", "extension": ".js", "lines": 585, "code_lines": 483}, {"path": "frontend\\js\\vue.global.js", "extension": ".js", "lines": 18193, "code_lines": 18063}, {"path": "frontend\\js\\common\\api-client.js", "extension": ".js", "lines": 218, "code_lines": 189}, {"path": "frontend\\js\\common\\elk-integration-config.js", "extension": ".js", "lines": 732, "code_lines": 641}, {"path": "frontend\\js\\common\\error-handler.js", "extension": ".js", "lines": 414, "code_lines": 361}, {"path": "frontend\\js\\common\\field-renderer.js", "extension": ".js", "lines": 320, "code_lines": 285}, {"path": "frontend\\js\\common\\field-utils.js", "extension": ".js", "lines": 432, "code_lines": 371}, {"path": "frontend\\js\\common\\smart-logger.js", "extension": ".js", "lines": 30, "code_lines": 24}, {"path": "frontend\\js\\common\\smart-retry-optimizer.js", "extension": ".js", "lines": 527, "code_lines": 460}, {"path": "frontend\\js\\common\\standard-error-logger.js", "extension": ".js", "lines": 615, "code_lines": 540}, {"path": "frontend\\js\\common\\test-data.js", "extension": ".js", "lines": 296, "code_lines": 275}, {"path": "frontend\\js\\common\\user-error-notifier.js", "extension": ".js", "lines": 603, "code_lines": 518}, {"path": "frontend\\js\\common\\validation-utils.js", "extension": ".js", "lines": 514, "code_lines": 433}, {"path": "frontend\\js\\core\\app-bootstrap.js", "extension": ".js", "lines": 411, "code_lines": 350}, {"path": "frontend\\js\\core\\component-manager.js", "extension": ".js", "lines": 384, "code_lines": 324}, {"path": "frontend\\js\\core\\component-migration-tool.js", "extension": ".js", "lines": 403, "code_lines": 356}, {"path": "frontend\\js\\core\\initialization-manager.js", "extension": ".js", "lines": 124, "code_lines": 104}, {"path": "frontend\\js\\core\\page-migration-assistant.js", "extension": ".js", "lines": 603, "code_lines": 516}, {"path": "frontend\\migrated\\database-v2.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": "frontend\\migrated\\excel-translation.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": "frontend\\migrated\\field-config-manual-migrated.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": "frontend\\migrated\\field-config-manual.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": "frontend\\migrated\\maintenance.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": "frontend\\migrated\\path-test.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": "frontend\\migrated\\unified-field-config.html", "extension": ".html", "lines": 0, "code_lines": 0}, {"path": "frontend\\tests\\api-client.test.js", "extension": ".js", "lines": 716, "code_lines": 591}, {"path": "frontend\\tests\\baseline-save.test.js", "extension": ".js", "lines": 379, "code_lines": 323}, {"path": "frontend\\tests\\error-handler.test.js", "extension": ".js", "lines": 587, "code_lines": 477}, {"path": "frontend\\tests\\field-list-display.test.js", "extension": ".js", "lines": 706, "code_lines": 577}, {"path": "frontend\\tests\\load-controller.test.js", "extension": ".js", "lines": 766, "code_lines": 625}, {"path": "frontend\\tests\\module-selector.test.js", "extension": ".js", "lines": 491, "code_lines": 405}, {"path": "frontend\\tests\\notification-system.test.js", "extension": ".js", "lines": 506, "code_lines": 397}, {"path": "frontend\\tests\\performance-optimization.test.js", "extension": ".js", "lines": 742, "code_lines": 594}, {"path": "frontend\\tests\\progress-display.test.js", "extension": ".js", "lines": 643, "code_lines": 515}, {"path": "frontend\\tests\\run-tests.js", "extension": ".js", "lines": 372, "code_lines": 316}, {"path": "frontend\\tests\\setup.js", "extension": ".js", "lines": 35, "code_lines": 29}, {"path": "frontend\\tests\\user-config-save.test.js", "extension": ".js", "lines": 505, "code_lines": 416}, {"path": "frontend\\tests\\reports\\test-report-1753834790480.json", "extension": ".json", "lines": 29, "code_lines": 29}, {"path": "logic\\md_mappings.json", "extension": ".json", "lines": 3028, "code_lines": 3028}, {"path": "md文档\\业务日志.md", "extension": ".md", "lines": 118, "code_lines": 116}, {"path": "md文档\\产品入库单列表.md", "extension": ".md", "lines": 277, "code_lines": 275}, {"path": "md文档\\委外入库列表查询.md", "extension": ".md", "lines": 372, "code_lines": 370}, {"path": "md文档\\委外申请列表查询.md", "extension": ".md", "lines": 247, "code_lines": 245}, {"path": "md文档\\委外订单列表.md", "extension": ".md", "lines": 385, "code_lines": 383}, {"path": "md文档\\材料出库单列表查询.md", "extension": ".md", "lines": 303, "code_lines": 302}, {"path": "md文档\\物料档案批量详情查询V3.md", "extension": ".md", "lines": 686, "code_lines": 684}, {"path": "md文档\\现存量报表查询.md", "extension": ".md", "lines": 325, "code_lines": 323}, {"path": "md文档\\生产订单列表查询.md", "extension": ".md", "lines": 955, "code_lines": 953}, {"path": "md文档\\请购单列表查询.md", "extension": ".md", "lines": 629, "code_lines": 627}, {"path": "md文档\\采购入库列表查询.md", "extension": ".md", "lines": 473, "code_lines": 471}, {"path": "md文档\\采购订单列表查询.md", "extension": ".md", "lines": 349, "code_lines": 347}, {"path": "md文档\\销售出库列表查询.md", "extension": ".md", "lines": 443, "code_lines": 441}, {"path": "md文档\\销售订单列表查询.md", "extension": ".md", "lines": 962, "code_lines": 960}, {"path": "md文档\\需求计划.md", "extension": ".md", "lines": 600, "code_lines": 598}, {"path": "migration\\week1_analysis\\tests\\test_batch_processor.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "migration\\week1_analysis\\tests\\test_data_writer_core.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "migration\\week2_analysis\\refactored\\conflict_resolver.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "migration\\week2_analysis\\refactored\\performance_monitor.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "migration\\week2_analysis\\refactored\\refactored_fast_sync_service.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "migration\\week2_analysis\\refactored\\sync_scheduler.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "migration\\week3_analysis\\refactored\\data_transformer.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "migration\\week3_analysis\\refactored\\endpoint_manager.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "migration\\week3_analysis\\refactored\\request_builder.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "migration\\week3_analysis\\refactored\\response_parser.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "monitoring\\dashboard.json", "extension": ".json", "lines": 52, "code_lines": 52}, {"path": "monitoring\\docker-compose.monitoring.yml", "extension": ".yml", "lines": 24, "code_lines": 24}, {"path": "monitoring\\grafana-dashboard.json", "extension": ".json", "lines": 44, "code_lines": 44}, {"path": "monitoring\\prometheus.yml", "extension": ".yml", "lines": 11, "code_lines": 11}, {"path": "scripts\\add_api_config.py", "extension": ".py", "lines": 81, "code_lines": 53}, {"path": "scripts\\auto_migration.py", "extension": ".py", "lines": 630, "code_lines": 471}, {"path": "scripts\\clean_debug_code.py", "extension": ".py", "lines": 148, "code_lines": 107}, {"path": "scripts\\deploy.bat", "extension": ".bat", "lines": 81, "code_lines": 70}, {"path": "scripts\\deploy.sh", "extension": ".sh", "lines": 69, "code_lines": 44}, {"path": "scripts\\diagnose_migration.py", "extension": ".py", "lines": 129, "code_lines": 93}, {"path": "scripts\\fix_css_paths.py", "extension": ".py", "lines": 141, "code_lines": 101}, {"path": "scripts\\fix_migrated_paths.py", "extension": ".py", "lines": 129, "code_lines": 84}, {"path": "scripts\\frontend_test_server.py", "extension": ".py", "lines": 71, "code_lines": 52}, {"path": "scripts\\reliable_server.py", "extension": ".py", "lines": 78, "code_lines": 57}, {"path": "scripts\\rollback.sh", "extension": ".sh", "lines": 56, "code_lines": 33}, {"path": "scripts\\rollback_batch_writes.py", "extension": ".py", "lines": 309, "code_lines": 242}, {"path": "scripts\\safe_cleanup.bat", "extension": ".bat", "lines": 120, "code_lines": 102}, {"path": "scripts\\safe_cleanup.sh", "extension": ".sh", "lines": 115, "code_lines": 87}, {"path": "scripts\\test_elk_connection.py", "extension": ".py", "lines": 430, "code_lines": 339}, {"path": "scripts\\test_server.py", "extension": ".py", "lines": 0, "code_lines": 0}, {"path": "scripts\\validate_deployment.py", "extension": ".py", "lines": 463, "code_lines": 370}, {"path": "scripts\\verify_fixes.py", "extension": ".py", "lines": 101, "code_lines": 73}, {"path": "templates\\deploy.template", "extension": ".template", "lines": 0, "code_lines": 0}, {"path": "templates\\deploy_production.template", "extension": ".template", "lines": 61, "code_lines": 43}, {"path": "templates\\deploy_staging.template", "extension": ".template", "lines": 51, "code_lines": 34}, {"path": "templates\\deploy_windows.template", "extension": ".template", "lines": 0, "code_lines": 0}, {"path": "templates\\dockerfile.template", "extension": ".template", "lines": 0, "code_lines": 0}, {"path": "templates\\dockerignore.template", "extension": ".template", "lines": 0, "code_lines": 0}, {"path": "templates\\jenkinsfile.template", "extension": ".template", "lines": 0, "code_lines": 0}, {"path": "templates\\rollback.template", "extension": ".template", "lines": 0, "code_lines": 0}, {"path": "tests\\api_test_server.py", "extension": ".py", "lines": 89, "code_lines": 73}, {"path": "tests\\field-config-manual-migrated.html", "extension": ".html", "lines": 780, "code_lines": 663}, {"path": "tests\\locust_stress_test.py", "extension": ".py", "lines": 409, "code_lines": 323}, {"path": "tests\\path-test.html", "extension": ".html", "lines": 55, "code_lines": 49}, {"path": "tests\\test_baseline_api.py", "extension": ".py", "lines": 232, "code_lines": 185}, {"path": "tests\\test_md_to_json_converter.py", "extension": ".py", "lines": 440, "code_lines": 305}, {"path": "tests\\test_rollback_scripts.py", "extension": ".py", "lines": 310, "code_lines": 220}, {"path": "tests\\黑名单功能移除测试.md", "extension": ".md", "lines": 19, "code_lines": 10}, {"path": "tools\\error_handling_load_test.py", "extension": ".py", "lines": 622, "code_lines": 505}, {"path": "tools\\md_to_json_converter.py", "extension": ".py", "lines": 159, "code_lines": 110}, {"path": "模块字段\\业务日志.xml", "extension": ".xml", "lines": 2280, "code_lines": 2280}, {"path": "模块字段\\产品入库单列表查询.xml", "extension": ".xml", "lines": 4837, "code_lines": 4837}, {"path": "模块字段\\委外入库列表查询.xml", "extension": ".xml", "lines": 6586, "code_lines": 6586}, {"path": "模块字段\\委外申请列表查询.xml", "extension": ".xml", "lines": 4767, "code_lines": 4767}, {"path": "模块字段\\委外订单列表.xml", "extension": ".xml", "lines": 573, "code_lines": 573}, {"path": "模块字段\\材料出库单列表查询.xml", "extension": ".xml", "lines": 7516, "code_lines": 7515}, {"path": "模块字段\\物料档案批量详情查询.xml", "extension": ".xml", "lines": 11413, "code_lines": 11413}, {"path": "模块字段\\现存量报表查询.xml", "extension": ".xml", "lines": 6914, "code_lines": 6914}, {"path": "模块字段\\生产订单列表查询.xml", "extension": ".xml", "lines": 16798, "code_lines": 16798}, {"path": "模块字段\\请购单列表查询.xml", "extension": ".xml", "lines": 8845, "code_lines": 8845}, {"path": "模块字段\\采购入库单列表.xml", "extension": ".xml", "lines": 7839, "code_lines": 7839}, {"path": "模块字段\\采购订单列表.xml", "extension": ".xml", "lines": 5909, "code_lines": 5909}, {"path": "模块字段\\销售出库列表查询.xml", "extension": ".xml", "lines": 8462, "code_lines": 8462}, {"path": "模块字段\\销售订单.xml", "extension": ".xml", "lines": 15150, "code_lines": 15150}, {"path": "模块字段\\需求计划.xml", "extension": ".xml", "lines": 5566, "code_lines": 5566}, {"path": "模块字段\\backup\\业务日志.xml", "extension": ".xml", "lines": 2280, "code_lines": 2280}, {"path": "模块字段\\backup\\产品入库单列表查询.xml", "extension": ".xml", "lines": 4837, "code_lines": 4837}, {"path": "模块字段\\backup\\委外入库列表查询.xml", "extension": ".xml", "lines": 6586, "code_lines": 6586}, {"path": "模块字段\\backup\\委外申请列表查询.xml", "extension": ".xml", "lines": 4767, "code_lines": 4767}, {"path": "模块字段\\backup\\委外订单列表.xml", "extension": ".xml", "lines": 7945, "code_lines": 7945}, {"path": "模块字段\\backup\\材料出库单列表查询.xml", "extension": ".xml", "lines": 7516, "code_lines": 7515}, {"path": "模块字段\\backup\\物料档案批量详情查询.xml", "extension": ".xml", "lines": 11413, "code_lines": 11413}, {"path": "模块字段\\backup\\现存量报表查询.xml", "extension": ".xml", "lines": 6916, "code_lines": 6916}, {"path": "模块字段\\backup\\生产订单列表查询.xml", "extension": ".xml", "lines": 16803, "code_lines": 16803}, {"path": "模块字段\\backup\\请购单列表查询.xml", "extension": ".xml", "lines": 8846, "code_lines": 8846}, {"path": "模块字段\\backup\\采购入库单列表.xml", "extension": ".xml", "lines": 8570, "code_lines": 8570}, {"path": "模块字段\\backup\\销售出库列表查询.xml", "extension": ".xml", "lines": 8462, "code_lines": 8462}, {"path": "模块字段\\backup\\销售订单.xml", "extension": ".xml", "lines": 15150, "code_lines": 15150}, {"path": "模块字段\\backup\\需求计划.xml", "extension": ".xml", "lines": 5566, "code_lines": 5566}, {"path": "模块字段\\json\\业务日志.json", "extension": ".json", "lines": 2308, "code_lines": 2308}, {"path": "模块字段\\json\\产品入库单列表查询.json", "extension": ".json", "lines": 4869, "code_lines": 4869}, {"path": "模块字段\\json\\委外入库列表查询.json", "extension": ".json", "lines": 6618, "code_lines": 6618}, {"path": "模块字段\\json\\委外申请列表查询.json", "extension": ".json", "lines": 4813, "code_lines": 4813}, {"path": "模块字段\\json\\委外订单列表.json", "extension": ".json", "lines": 583, "code_lines": 583}, {"path": "模块字段\\json\\材料出库单列表查询.json", "extension": ".json", "lines": 7553, "code_lines": 7553}, {"path": "模块字段\\json\\物料档案批量详情查询.json", "extension": ".json", "lines": 11455, "code_lines": 11455}, {"path": "模块字段\\json\\现存量报表查询.json", "extension": ".json", "lines": 6936, "code_lines": 6936}, {"path": "模块字段\\json\\生产订单列表查询.json", "extension": ".json", "lines": 16848, "code_lines": 16848}, {"path": "模块字段\\json\\请购单列表查询.json", "extension": ".json", "lines": 8787, "code_lines": 8787}, {"path": "模块字段\\json\\转换报告.json", "extension": ".json", "lines": 115, "code_lines": 115}, {"path": "模块字段\\json\\采购入库单列表.json", "extension": ".json", "lines": 7865, "code_lines": 7865}, {"path": "模块字段\\json\\采购订单列表.json", "extension": ".json", "lines": 5949, "code_lines": 5949}, {"path": "模块字段\\json\\销售出库列表查询.json", "extension": ".json", "lines": 8496, "code_lines": 8496}, {"path": "模块字段\\json\\销售订单.json", "extension": ".json", "lines": 15194, "code_lines": 15194}, {"path": "模块字段\\json\\需求计划.json", "extension": ".json", "lines": 5588, "code_lines": 5588}]}