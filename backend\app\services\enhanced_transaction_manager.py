#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 SQL Server 事务管理器
针对现有database_manager.py的事务处理优化
"""

import threading
import time
from contextlib import contextmanager
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
import structlog
import pymssql

logger = structlog.get_logger()


class TransactionLevel(Enum):
    """事务隔离级别"""
    READ_UNCOMMITTED = "READ UNCOMMITTED"
    READ_COMMITTED = "READ COMMITTED"
    REPEATABLE_READ = "REPEATABLE READ"
    SERIALIZABLE = "SERIALIZABLE"


@dataclass
class TransactionStats:
    """事务统计"""
    total_transactions: int = 0
    successful_transactions: int = 0
    failed_transactions: int = 0
    rollback_count: int = 0
    avg_transaction_time: float = 0.0
    batch_operations: int = 0
    records_processed: int = 0


class EnhancedTransactionManager:
    """增强的事务管理器 - 集成到现有database_manager.py"""
    
    def __init__(self, connection_pool_manager):
        self.connection_pool_manager = connection_pool_manager
        self.stats = TransactionStats()
        self._lock = threading.RLock()
        
        logger.info("增强事务管理器初始化完成")
    
    @contextmanager
    def transaction(self, isolation_level: TransactionLevel = TransactionLevel.READ_COMMITTED,
                   timeout: int = 30):
        """事务上下文管理器"""
        start_time = time.time()
        transaction_id = f"txn_{int(start_time)}_{threading.current_thread().ident}"
        
        try:
            with self.connection_pool_manager.get_connection() as conn:
                # 设置隔离级别
                self._set_isolation_level(conn, isolation_level)
                
                # 开始事务
                conn.autocommit = False
                
                try:
                    yield conn
                    
                    # 提交事务
                    conn.commit()
                    self.stats.successful_transactions += 1
                    
                    logger.debug("事务提交成功", 
                               transaction_id=transaction_id,
                               duration=time.time() - start_time)
                    
                except Exception as e:
                    # 回滚事务
                    conn.rollback()
                    self.stats.rollback_count += 1
                    
                    logger.error("事务回滚", 
                               transaction_id=transaction_id,
                               error=str(e))
                    raise
                
                finally:
                    # 恢复自动提交
                    try:
                        conn.autocommit = True
                    except:
                        pass
        
        except Exception as e:
            self.stats.failed_transactions += 1
            raise
        
        finally:
            # 更新统计信息
            self.stats.total_transactions += 1
            duration = time.time() - start_time
            self._update_avg_transaction_time(duration)
    
    def _set_isolation_level(self, conn: pymssql.Connection, level: TransactionLevel):
        """设置事务隔离级别"""
        try:
            level_map = {
                TransactionLevel.READ_UNCOMMITTED: "READ UNCOMMITTED",
                TransactionLevel.READ_COMMITTED: "READ COMMITTED",
                TransactionLevel.REPEATABLE_READ: "REPEATABLE READ",
                TransactionLevel.SERIALIZABLE: "SERIALIZABLE"
            }
            cursor = conn.cursor()
            cursor.execute(f"SET TRANSACTION ISOLATION LEVEL {level_map[level]}")
            cursor.close()
        except Exception as e:
            logger.warning("设置隔离级别失败", level=level.value, error=str(e))
    
    def _update_avg_transaction_time(self, duration: float):
        """更新平均事务时间"""
        with self._lock:
            total = self.stats.total_transactions
            if total == 1:
                self.stats.avg_transaction_time = duration
            else:
                current_avg = self.stats.avg_transaction_time
                self.stats.avg_transaction_time = (current_avg * (total - 1) + duration) / total
    
    def execute_batch_insert(self, table_name: str, columns: List[str], 
                           data: List[tuple], batch_size: int = 1000) -> Dict[str, Any]:
        """优化的批量插入"""
        start_time = time.time()
        total_inserted = 0
        
        try:
            with self.transaction() as conn:
                cursor = conn.cursor()
                
                # 构建SQL
                placeholders = ','.join(['%s' for _ in columns])
                column_str = ','.join(columns)
                sql = f"INSERT INTO {table_name} ({column_str}) VALUES ({placeholders})"
                
                # 分批插入
                for i in range(0, len(data), batch_size):
                    batch_data = data[i:i + batch_size]
                    cursor.executemany(sql, batch_data)
                    total_inserted += len(batch_data)
                
                cursor.close()
                
                self.stats.batch_operations += 1
                self.stats.records_processed += total_inserted
                
                return {
                    'success': True,
                    'inserted': total_inserted,
                    'duration': time.time() - start_time,
                    'batches': (len(data) + batch_size - 1) // batch_size
                }
                
        except Exception as e:
            logger.error("批量插入失败", table=table_name, error=str(e))
            return {
                'success': False,
                'inserted': 0,
                'error': str(e),
                'duration': time.time() - start_time
            }
    
    def execute_batch_update(self, table_name: str, updates: List[Dict[str, Any]], 
                           key_column: str = 'id', batch_size: int = 1000) -> Dict[str, Any]:
        """批量更新操作"""
        start_time = time.time()
        total_updated = 0
        
        try:
            with self.transaction() as conn:
                cursor = conn.cursor()
                
                # 分批更新
                for i in range(0, len(updates), batch_size):
                    batch_updates = updates[i:i + batch_size]
                    
                    for update_data in batch_updates:
                        # 构建UPDATE SQL
                        key_value = update_data.pop(key_column)
                        set_clauses = [f"{col} = %s" for col in update_data.keys()]
                        values = list(update_data.values()) + [key_value]
                        
                        sql = f"UPDATE {table_name} SET {', '.join(set_clauses)} WHERE {key_column} = %s"
                        cursor.execute(sql, values)
                        total_updated += cursor.rowcount
                
                cursor.close()
                
                self.stats.batch_operations += 1
                self.stats.records_processed += total_updated
                
                return {
                    'success': True,
                    'updated': total_updated,
                    'duration': time.time() - start_time,
                    'batches': (len(updates) + batch_size - 1) // batch_size
                }
                
        except Exception as e:
            logger.error("批量更新失败", table=table_name, error=str(e))
            return {
                'success': False,
                'updated': 0,
                'error': str(e),
                'duration': time.time() - start_time
            }
    
    def get_transaction_stats(self) -> Dict[str, Any]:
        """获取事务统计信息"""
        with self._lock:
            return {
                'total_transactions': self.stats.total_transactions,
                'successful_transactions': self.stats.successful_transactions,
                'failed_transactions': self.stats.failed_transactions,
                'success_rate': (self.stats.successful_transactions / max(1, self.stats.total_transactions)) * 100,
                'rollback_count': self.stats.rollback_count,
                'avg_transaction_time': self.stats.avg_transaction_time,
                'batch_operations': self.stats.batch_operations,
                'records_processed': self.stats.records_processed
            }


# 全局事务管理器
_transaction_manager: Optional[EnhancedTransactionManager] = None
_manager_lock = threading.Lock()


def get_transaction_manager() -> EnhancedTransactionManager:
    """获取全局事务管理器实例"""
    global _transaction_manager
    
    if _transaction_manager is None:
        with _manager_lock:
            if _transaction_manager is None:
                from .enhanced_connection_pool import get_connection_pool_manager
                _transaction_manager = EnhancedTransactionManager(get_connection_pool_manager())
    
    return _transaction_manager


def transaction(isolation_level: TransactionLevel = TransactionLevel.READ_COMMITTED,
               timeout: int = 30):
    """便捷的事务装饰器"""
    manager = get_transaction_manager()
    return manager.transaction(isolation_level, timeout)


def batch_insert(table_name: str, columns: List[str], data: List[tuple], 
                batch_size: int = 1000) -> Dict[str, Any]:
    """便捷的批量插入函数"""
    manager = get_transaction_manager()
    return manager.execute_batch_insert(table_name, columns, data, batch_size)


def batch_update(table_name: str, updates: List[Dict[str, Any]], 
                key_column: str = 'id', batch_size: int = 1000) -> Dict[str, Any]:
    """便捷的批量更新函数"""
    manager = get_transaction_manager()
    return manager.execute_batch_update(table_name, updates, key_column, batch_size)


def get_transaction_stats() -> Dict[str, Any]:
    """便捷的统计信息获取函数"""
    manager = get_transaction_manager()
    return manager.get_transaction_stats()


# 集成到现有database_manager.py的函数
def integrate_transaction_manager():
    """集成事务管理器到现有database_manager.py"""
    logger.info("开始集成事务管理器到现有数据库管理器")
    
    # 这个函数将被现有的database_manager.py调用
    # 用于增强事务处理能力
    
    return get_transaction_manager()
