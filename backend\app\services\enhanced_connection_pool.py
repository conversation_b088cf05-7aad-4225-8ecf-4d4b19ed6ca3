#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 SQL Server 连接池管理器
专门针对SQL Server数据库的连接池管理，集成到现有database_manager.py
"""

import threading
import time
import queue
from contextlib import contextmanager
from typing import Dict, Any, Optional
from dataclasses import dataclass
import structlog
import pymssql
import configparser
from pathlib import Path

logger = structlog.get_logger()


@dataclass
class ConnectionPoolConfig:
    """连接池配置"""
    min_connections: int = 5
    max_connections: int = 20
    timeout: int = 30
    health_check_interval: int = 60
    max_idle_time: int = 300  # 5分钟


@dataclass
class SQLServerConfig:
    """SQL Server配置"""
    server: str
    port: int
    database: str
    username: str
    password: str
    charset: str = 'utf8'
    timeout: int = 30


@dataclass
class ConnectionInfo:
    """连接信息"""
    connection: Any
    created_at: float
    last_used: float
    in_use: bool = False
    health_status: bool = True


class SQLServerConnectionPool:
    """SQL Server连接池管理器"""
    
    def __init__(self, config: ConnectionPoolConfig, db_config: SQLServerConfig):
        self.pool_config = config
        self.db_config = db_config
        
        # 连接池存储
        self._available_connections: queue.Queue = queue.Queue(
            maxsize=config.max_connections
        )
        self._all_connections: Dict[int, ConnectionInfo] = {}
        self._connection_counter = 0
        
        # 线程锁
        self._lock = threading.RLock()
        self._health_check_lock = threading.Lock()
        
        # 健康检查线程
        self._health_check_thread = None
        self._stop_health_check = threading.Event()
        
        # 统计信息
        self.stats = {
            'total_connections': 0,
            'active_connections': 0,
            'failed_connections': 0,
            'total_requests': 0,
            'failed_requests': 0,
            'health_check_failures': 0,
            'last_health_check': None
        }
        
        # 初始化连接池
        self._initialize_pool()
        self._start_health_check()
        
        logger.info(
            "SQL Server连接池初始化完成",
            min_connections=config.min_connections,
            max_connections=config.max_connections,
            server=db_config.server,
            database=db_config.database
        )
    
    def _initialize_pool(self):
        """初始化连接池，创建最小连接数"""
        try:
            for _ in range(self.pool_config.min_connections):
                conn = self._create_connection()
                if conn:
                    self._add_connection_to_pool(conn)
                    
            logger.info(
                "连接池初始化成功",
                initialized_connections=len(self._all_connections)
            )
        except Exception as e:
            logger.error("连接池初始化失败", error=str(e))
            raise
    
    def _create_connection(self) -> Optional[pymssql.Connection]:
        """创建新的SQL Server连接"""
        try:
            config = self.db_config
            
            return pymssql.connect(
                server=config.server,
                port=config.port,
                database=config.database,
                user=config.username,
                password=config.password,
                charset=config.charset,
                timeout=config.timeout,
                autocommit=False
            )
            
        except Exception as e:
            logger.error("创建SQL Server连接失败", error=str(e))
            self.stats['failed_connections'] += 1
            return None
    
    def _add_connection_to_pool(self, connection: pymssql.Connection):
        """将连接添加到连接池"""
        with self._lock:
            conn_id = self._connection_counter
            self._connection_counter += 1
            
            conn_info = ConnectionInfo(
                connection=connection,
                created_at=time.time(),
                last_used=time.time(),
                in_use=False,
                health_status=True
            )
            
            self._all_connections[conn_id] = conn_info
            self._available_connections.put((conn_id, connection))
            self.stats['total_connections'] += 1
            
            logger.debug("连接已添加到池", connection_id=conn_id)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn_id = None
        connection = None
        
        try:
            conn_id, connection = self._acquire_connection()
            self.stats['total_requests'] += 1
            yield connection
            
        except Exception as e:
            logger.error("连接使用过程中出错", error=str(e))
            self.stats['failed_requests'] += 1
            raise
            
        finally:
            if conn_id is not None:
                self._release_connection(conn_id)
    
    def _acquire_connection(self) -> tuple[int, pymssql.Connection]:
        """获取可用连接"""
        try:
            # 尝试从池中获取连接
            conn_id, connection = self._available_connections.get(
                timeout=self.pool_config.timeout
            )
            
            with self._lock:
                if conn_id in self._all_connections:
                    conn_info = self._all_connections[conn_id]
                    
                    # 检查连接健康状态
                    if not self._check_connection_health(connection):
                        logger.warning("连接健康检查失败，重新创建", connection_id=conn_id)
                        self._remove_connection(conn_id)
                        return self._acquire_connection()  # 递归获取新连接
                    
                    conn_info.in_use = True
                    conn_info.last_used = time.time()
                    self.stats['active_connections'] += 1
                    
                    return conn_id, connection
                else:
                    # 连接已被移除，重新获取
                    return self._acquire_connection()
                    
        except queue.Empty:
            # 池中没有可用连接，尝试创建新连接
            if len(self._all_connections) < self.pool_config.max_connections:
                connection = self._create_connection()
                if connection:
                    self._add_connection_to_pool(connection)
                    return self._acquire_connection()
            
            raise Exception(f"连接池已满且无法获取连接，超时时间: {self.pool_config.timeout}秒")
    
    def _release_connection(self, conn_id: int):
        """释放连接回池"""
        with self._lock:
            if conn_id in self._all_connections:
                conn_info = self._all_connections[conn_id]
                conn_info.in_use = False
                conn_info.last_used = time.time()
                self.stats['active_connections'] -= 1
                
                # 检查连接是否超过最大空闲时间
                if (time.time() - conn_info.last_used) > self.pool_config.max_idle_time:
                    self._remove_connection(conn_id)
                else:
                    # 将连接放回池中
                    self._available_connections.put((conn_id, conn_info.connection))
                
                logger.debug("连接已释放", connection_id=conn_id)
    
    def _remove_connection(self, conn_id: int):
        """从池中移除连接"""
        with self._lock:
            if conn_id in self._all_connections:
                conn_info = self._all_connections[conn_id]
                
                try:
                    conn_info.connection.close()
                except:
                    pass  # 忽略关闭连接时的错误
                
                del self._all_connections[conn_id]
                self.stats['total_connections'] -= 1
                
                logger.debug("连接已从池中移除", connection_id=conn_id)
    
    def _check_connection_health(self, connection: pymssql.Connection) -> bool:
        """检查连接健康状态"""
        try:
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            return True
                
        except Exception as e:
            logger.warning("连接健康检查失败", error=str(e))
            return False
    
    def _start_health_check(self):
        """启动健康检查线程"""
        self._health_check_thread = threading.Thread(
            target=self._health_check_worker,
            daemon=True,
            name="SQLServerConnectionPoolHealthCheck"
        )
        self._health_check_thread.start()
        logger.info("连接池健康检查线程已启动")
    
    def _health_check_worker(self):
        """健康检查工作线程"""
        while not self._stop_health_check.is_set():
            try:
                self._perform_health_check()
                self.stats['last_health_check'] = time.time()
                
            except Exception as e:
                logger.error("健康检查执行失败", error=str(e))
                self.stats['health_check_failures'] += 1
            
            # 等待下次检查
            self._stop_health_check.wait(self.pool_config.health_check_interval)
    
    def _perform_health_check(self):
        """执行健康检查"""
        with self._health_check_lock:
            unhealthy_connections = []
            
            # 检查所有非活跃连接
            for conn_id, conn_info in self._all_connections.items():
                if not conn_info.in_use:
                    if not self._check_connection_health(conn_info.connection):
                        unhealthy_connections.append(conn_id)
                        conn_info.health_status = False
            
            # 移除不健康的连接
            for conn_id in unhealthy_connections:
                logger.warning("移除不健康连接", connection_id=conn_id)
                self._remove_connection(conn_id)
            
            # 确保最小连接数
            current_count = len(self._all_connections)
            if current_count < self.pool_config.min_connections:
                needed = self.pool_config.min_connections - current_count
                for _ in range(needed):
                    conn = self._create_connection()
                    if conn:
                        self._add_connection_to_pool(conn)
            
            logger.debug(
                "健康检查完成",
                total_connections=len(self._all_connections),
                removed_unhealthy=len(unhealthy_connections)
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self._lock:
            stats = self.stats.copy()
            stats.update({
                'pool_size': len(self._all_connections),
                'available_connections': self._available_connections.qsize(),
                'in_use_connections': sum(1 for c in self._all_connections.values() if c.in_use)
            })
            return stats
    
    def close(self):
        """关闭连接池"""
        logger.info("正在关闭SQL Server连接池...")
        
        # 停止健康检查线程
        self._stop_health_check.set()
        if self._health_check_thread and self._health_check_thread.is_alive():
            self._health_check_thread.join(timeout=5)
        
        # 关闭所有连接
        with self._lock:
            for conn_id in list(self._all_connections.keys()):
                self._remove_connection(conn_id)
        
        logger.info("SQL Server连接池已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class ConnectionPoolManager:
    """连接池管理器 - 集成到现有database_manager.py"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "backend/config.ini"
        self.connection_pool: Optional[SQLServerConnectionPool] = None
        self._lock = threading.Lock()
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化SQL Server连接池"""
        try:
            sqlserver_config = self._load_sqlserver_config()
            if sqlserver_config:
                pool_config = ConnectionPoolConfig(
                    min_connections=5,
                    max_connections=20,
                    timeout=30,
                    health_check_interval=60
                )
                
                self.connection_pool = SQLServerConnectionPool(
                    pool_config, sqlserver_config
                )
                logger.info("SQL Server连接池初始化成功")
            else:
                raise Exception("无法加载SQL Server配置")
        except Exception as e:
            logger.error("SQL Server连接池初始化失败", error=str(e))
            raise
    
    def _load_sqlserver_config(self) -> Optional[SQLServerConfig]:
        """加载SQL Server配置"""
        try:
            config = configparser.ConfigParser()
            config_path = Path(self.config_file)
            
            if not config_path.exists():
                logger.error("配置文件不存在", path=str(config_path))
                return None
            
            config.read(str(config_path), encoding='utf-8')
            
            if 'database' not in config:
                logger.error("配置文件中缺少database段")
                return None
            
            db_section = config['database']
            
            # 处理环境变量
            password = db_section.get('password', '')
            if password.startswith('${ENV:') and password.endswith('}'):
                env_var = password[6:-1]  # 移除 ${ENV: 和 }
                password = os.environ.get(env_var, '')
                if not password:
                    logger.warning(f"环境变量 {env_var} 未设置，使用空密码")
            
            return SQLServerConfig(
                server=db_section.get('server', 'localhost'),
                port=int(db_section.get('port', 1433)),
                database=db_section.get('database', 'YSAPI'),
                username=db_section.get('username', ''),
                password=password,
                charset='utf8',
                timeout=30
            )
        except Exception as e:
            logger.error("加载SQL Server配置失败", error=str(e))
            return None
    
    def get_connection(self):
        """获取数据库连接"""
        if not self.connection_pool:
            raise Exception("连接池未初始化")
        
        return self.connection_pool.get_connection()
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        if not self.connection_pool:
            return {}
        
        return self.connection_pool.get_stats()
    
    def close(self):
        """关闭连接池"""
        if self.connection_pool:
            self.connection_pool.close()
            self.connection_pool = None
        logger.info("连接池管理器已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


# 全局连接池管理器实例
_pool_manager: Optional[ConnectionPoolManager] = None
_manager_lock = threading.Lock()


def get_connection_pool_manager() -> ConnectionPoolManager:
    """获取全局连接池管理器实例（单例模式）"""
    global _pool_manager
    
    if _pool_manager is None:
        with _manager_lock:
            if _pool_manager is None:
                _pool_manager = ConnectionPoolManager()
    
    return _pool_manager


def get_database_connection():
    """便捷函数：获取数据库连接"""
    manager = get_connection_pool_manager()
    return manager.get_connection()


def get_connection_stats() -> Dict[str, Any]:
    """便捷函数：获取连接池统计"""
    manager = get_connection_pool_manager()
    return manager.get_pool_stats()


# 在现有database_manager.py中集成连接池
def integrate_connection_pool():
    """集成连接池到现有database_manager.py"""
    logger.info("开始集成连接池到现有数据库管理器")
    
    # 这个函数将被现有的database_manager.py调用
    # 用于替换原有的连接管理方式
    
    return get_connection_pool_manager()
