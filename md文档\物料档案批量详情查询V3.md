物料档案批量详情查询V3
发布时间:2025-07-17 18:34:11
特征物料档案批量详情查询，批量详情查询最多支持10条数据

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	动态域名，获取方式详见 获取租户所在数据中心域名
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/digitalModel/product/batchdetailnew
请求方式	POST
ContentType	application/json
应用场景	开放API
API类别	
事务和幂等性	无
限流次数	
150次/分钟
购买API加速包，升级至300次/分钟
立即购买
2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
id	long	否	否	物料档案id    示例: 1530976715290968000
productCode	string	否	否	物料编码(物料id和编码二选一，同时填入以id为准)    示例: code1
orgId	string	否	否	组织id(组织id和编码二选一必填，同时填入时以id为准) 示例：666666"    示例: 2538984869630976
orgCode	string	否	否	组织编码(组织id和编码二选一必填，同时填入时以id为准) 示例：666666    示例: XMGS
3. 请求示例
Url: /yonbip/digitalModel/product/batchdetailnew?access_token=访问令牌
Body: [{
	"id": 1530976715290968000,
	"productCode": "code1",
	"orgId": "2538984869630976",
	"orgCode": "XMGS"
}]
4. 返回值参数
名称	类型	数组	描述
code	string	否	返回码，调用成功时返回200
message	string	否	调用失败时的错误信息
data	object	是	调用成功时的返回数据
productCharacterDef	特征组
pc.product.Product	否	物料自定义项
WL01	string	否	参考料号1
WL02	string	否	参考料号2
WL03	string	否	U9料品编码
WL04	string	否	客户型号
WL05	string	否	销售产品名称
WL06	Decimal	否	料品每箱台数
WL07	string	否	目录编号
id	string	否	特征id,主键,新增时无需填写,修改时必填
productPropCharacterDefine	特征组
pc.product.Product	否	物料自定义属性
C_001	string	否	颜色
id	string	否	特征id,主键,新增时无需填写,修改时必填
detail	object	否	物料详情
id	long	否	物料详情id
productId	long	否	物料id
productExtendCharacterDef	特征组
pc.product.ProductExtend	否	物料详情自定义项
WL08	string	否	销售员
WL09	string	否	跟单员
id	string	否	特征id,主键,新增时无需填写,修改时必填
productApplyRangeId	long	否	适用范围id
purchaseUnit	long	否	采购单位id
purchaseUnitCode	string	否	采购单位编码
purchaseUnitName	string	否	采购单位名称
purchasePriceUnit	long	否	采购计价单位id
purchasePriceUnitCode	string	否	采购计价单位编码
purchasePriceUnitName	string	否	采购计价单位名称
stockUnit	long	否	库存单位id
stockUnitCode	string	否	库存单位编码
stockUnitName	string	否	库存单位名称
batchUnit	long	否	批发销售单位id
batchUnitCode	string	否	批发销售单位编码
batchUnitName	string	否	批发销售单位名称
onlineUnit	long	否	线上零售单位id
onlineUnitCode	string	否	线上零售单位编码
onlineUnitName	string	否	线上零售单位名称
offlineUnit	long	否	线下零售单位id
offlineUnitCode	string	否	线下零售单位编码
offlineUnitName	string	否	线下零售单位名称
requireUnit	long	否	要货单位id
requireUnitCode	string	否	要货单位编码
requireUnitName	string	否	要货单位名称
batchPriceUnit	long	否	批发计价单位id
batchPriceUnitCode	string	否	批发计价单位编码
batchPriceUnitName	string	否	批发计价单位名称
inspectionUnit	long	否	检验单位
inspectionUnitCode	string	否	检验单位编码
inspectionUnitName	string	否	检验单位名称
batchPrice	Decimal	否	批发价
markPrice	Decimal	否	建议零售价
lowestMarkPrice	Decimal	否	最低零售价
salePrice	Decimal	否	线上零售价
marketPrice	Decimal	否	市场价
primeCosts	Decimal	否	采购参考价
settleAccountsRate	long	否	结算费率
displayPrice	boolean	否	线上显示价格, true:是、false:否
batchManage	boolean	否	批次管理, true:是、false:否
expiryDateManage	boolean	否	有效期管理, true:是、false:否
serialNoManage	boolean	否	序列号管理, true:是、false:否
safetyStock	Decimal	否	安全库存
highestStock	Decimal	否	最高库存
lowestStock	long	否	最低库存
ropStock	long	否	再订货点
canSale	boolean	否	B2B是否可售, true:是，false:否
minOrderQuantity	long	否	起订量
deliveryDays	long	否	交货周期
enableCyclePurchase	boolean	否	启用周期购，true：是、false：否。
enableDeposit	boolean	否	启用定金业务, true:是、false:否。
depositDealPayType	long	否	定金设置方式, 0:固定金额、1:成交金额百分比
enableModifyDeposit	boolean	否	订单改价时可修改定金, true:是、false:否
depositPayType	long	否	支付尾款方式, 0:线上支付尾款、1:线下支付尾款
metaDescription	object	否	搜索简介
baseSaleCount	long	否	初始销量
enableContractManagement	boolean	否	ECN管控，true：是，false：否。
allowStorePurchase	boolean	否	允许门店自采, true:是、false:否
priceChangeAllowed	boolean	否	允许开单改价, true:是、false:否
saleInOfflineStore	boolean	否	允许门店销售, true:是、false:否
offlineStoreOrder	boolean	否	允许门店要货, true:是、false:否
offlineStoreReturn	boolean	否	允许门店退货, true:是、false:否
weighingOrNot	boolean	否	是否称重, true:是、false:否
process	boolean	否	加工, true:是、false:否
material	boolean	否	材料, true:是、false:否
retailPriceDimension	long	否	零售价取价维度, 1:物料、2:物料SKU
deliverQuantityChange	long	否	交货数量改变时, 1:单价不变重算金额、2:金额不变重算单价
noTaxCostPrice	Decimal	否	参考成本
checkByBatch	boolean	否	按批次核算, true:是、false:否
accountingByItem	boolean	否	按单品核算
storeOffAndOffState	boolean	否	商城上架, true:是、false:否
orderLoadAndUnloadStatus	boolean	否	订货上架, true:是、false:否
mallUpCount	long	否	商城上架数量
mallDownCount	long	否	商城下架数量
orderUpCount	long	否	U订货上架数量
orderDownCount	long	否	U订货下架数量
tenant	long	否	租户ID
saleChannel	string	否	销售渠道, 1:销售批发、2:线上零售、3:线下零售、4:微分销
barCode	string	否	条形码
stopStatus	boolean	否	启用状态，true代表停用，false代表启用
checkFree	long	否	按规格核算, 0:不按规格核算、1:指定规格核算、2:按SKU核算、
canOrder	boolean	否	可预约, true:是、false:否、
onlyOrder	boolean	否	仅预约, true:是、false:否、
orderAdvanceTime	long	否	预约提前期, 0:无、1:一天、2:两天、3:三天、4:一周、5:两周、6:一月、
valueManageType	long	否	价值管理模式, 99:费用、0:存货核算、1:固定资产
costValuation	long	否	成本计价方法, 0:先进先出法、1:移动平均法、2:全月平均法
checkByCost	boolean	否	按费用核算, true:是、false:否、yCost
materialCost	boolean	否	ma材料费用化, true:是、false:否、terialCost
planDefaultAttribute	long	否	计划默认属性, 1:采购，3:自制，5：委外。
planMethod	long	否	计划方法, 0：MRP/LRP、1:：N-不计划、10：库存计划、5：MPS。
keySubPart	boolean	否	关键子件, true:是、false:否
supplyDemandPolicy	long	否	供需策略, 0:PE、1:LP、
fixedLeadTime	long	否	固定提前期
supplyType	long	否	供应类型, 0：领用、1：入库倒冲、2：不发料、5：开工倒冲、6：工序完工倒冲 、7：完工倒冲。
produceDepartment	string	否	生产部门ID
produceDepartmentCode	string	否	生产部门编码
produceDepartmentName	string	否	生产部门名称
manufacturePlanner	string	否	计划员id
manufacturePlannerCode	string	否	计划员编码
manufacturePlannerName	string	否	计划员名称
engineeringDrawingNo	string	否	工程图号
planProduceLimit	long	否	计划下达超量上限
utility	boolean	否	公用工程, true:是、false:否、
weigh	boolean	否	是否过磅, true:是、false:否、
productVendor	long	否	供应商id
productVendorCode	string	否	供应商编码
productVendorName	string	否	供应商名称
productBuyer	string	否	采购员id
productBuyerCode	string	否	采购员编码
productBuyerName	string	否	采购员名称
maxPrimeCosts	long	否	最高进价
requestOrderLimit	long	否	请购订货超量上限
enableSubscribe	boolean	否	启用预订业务, true:是、false:否
recommend	boolean	否	推荐物料, true:是、false:否
erpOuterCode	string	否	商家商品外部编码
saleStyle	string	否	销售方式, 1:现金购买、2:积分兑换
shortName	string	否	物料简称
mnemonicCode	string	否	助记码
receiptName	object	否	开票名称
incomeTaxRates	string	否	进项税率id
outputTaxRate	string	否	销项税率id
produceUnit	long	否	生产单位id
produceUnitCode	string	否	生产单位编码
produceUnitName	string	否	生产单位名称
warehouseManager	string	否	库管员id
warehouseManagerCode	string	否	库管员编码
warehouseManagerName	string	否	库管员名称
deliveryWarehouse	long	否	发货仓库id
deliveryWarehouseCode	string	否	发货仓库编码
deliveryWarehouseName	string	否	发货仓库名称
returnWarehouse	long	否	退货仓库id
returnWarehouseCode	string	否	退货仓库编码
returnWarehouseName	string	否	退货仓库名称
inStoreExcessLimit	long	否	入库超量上限
outStoreExcessLimit	long	否	出库超量上限
storageLossRate	long	否	保管损耗率
allowNegativeInventory	boolean	否	允许负库存, true:是、false:否
scanCountUnit	long	否	扫码计数单位，0：主计量单位，1：库存单位。
exemption	boolean	否	免检, true:是、false:否、
warehousingByResult	boolean	否	根据检验结果入库，true代表是，false代表否
salesReturnsExemption	boolean	否	销售退货免检, true:是、false:否、
returnsWarehousingByResult	boolean	否	退货根据检验结果入库, true:是、false:否、
periodicalInspection	boolean	否	定期检验, true:是、false:否、
displayName	object	否	显示名称
titleMemo	object	否	卖点
barcodeManage	boolean	否	条码管理, true:是、false:否
receiptWarehouse	long	否	收货仓库id
receiptWarehouseCode	string	否	收货仓库编码
receiptWarehouseName	string	否	收货仓库名称
BOMType	long	否	物料BOM类型，0：标准件，5：计划件。
batchRule	long	否	批量规则,0：直接批量、5：经济批量、10：固定批量
fixedQuantity	long	否	固定批量
prepareFeed	boolean	否	是否长周期备料，true：是，false：否。
specialMaterials	boolean	否	是否专用料，true：是，false：否。
virtualPart	boolean	否	是否虚拟件，true：是，false：否。
demandConsolidation	long	否	物料需求合并，0：空，5：是，10：否。
demandConsolidationType	long	否	需求合并类型，0：固定，10：动态。
demandConsolidationUnit	long	否	需求合并时格，0：日，10：月，15：月。
demandConsolidationNumber	long	否	需求合并时格数
demandConsolidationDateType	long	否	需求合并日，0：需求首日，1：期间首日。
reservation	boolean	否	可预留，true：是，false：否。
lossType	long	否	损耗类型，0：无损耗，5：固定损耗，10：变动损耗。
ECNControl	boolean	否	ECN管控，true：是，false：否。
ytenantId	string	否	友互通id
inspectionType	long	否	检验，1代表是，0代表否
logisticsRelated	boolean	否	物流相关，true：是，false：否。
weighingMode	long	否	称重方式，1：是，0：否。
reviewGrossWeight	boolean	否	复核毛重，true：是，false：否。
specialCarTransport	boolean	否	专车运输，true：是，false：否。
orgId	string	否	创建组织ID
businessAttribute	string	否	业务属性, 1:采购、7:销售、3:自制、2:委外
businessAttributePurchase	long	否	业务属性-采购，1: 是，0：否。
businessAttributeSale	long	否	业务属性-销售，1: 是，0：否。
businessAttributeSelfCreate	long	否	业务属性-自制，1: 是，0：否。
businessAttributeOutSourcing	long	否	业务属性-委外，1: 是，0：否。
testRule	long	否	检验规则，0代表按物料检验，1代表按检验项目检验
enableStockPeriodRecheck	long	否	启用库存周期复检，1:启用，0: 停用。
enableStockExpireCheck	long	否	启用库存临期检验，1：是，0：否。
enableSparePartsManagement	long	否	启用备件管理，1：是，0：否。
fullSetInspection	long	否	齐套检查，1：是，0：否。
directProduction	long	否	是否直接生产，1：是，0：否。
costItems	string	否	费用项目id
costItemsCode	string	否	费用项目编码
costItemsName	string	否	费用项目名称
manageByInventory	int	否	按项目管理库存（0表示不开启，1表示开启）
checkByProject	int	否	按项目核算（0表示不开启，1表示开启）
checkBySalesOrders	int	否	按销售订单核算（0表示不开启，1表示开启）
checkByClient	int	否	按客户核算（0表示不开启，1表示开启）
checkByOutsourcing	int	否	按委外商核算（0表示不开启，1表示开启）
atpInspection	number
小数位数:0,最大长度:10	否	ATP检查（0表示否，1表示是）
doublePick	number
小数位数:8,最大长度:19	否	领料倍量
productOrges	object	是	物料分配的组织。
realProductAttribute	long	否	物料性质，1：实物物料，2：虚拟物料
code	string	否	物料编码
name	object	否	物料名称
model	object	否	型号
keywords	object	否	关键字
productClass	long	否	商品分类id
productClassCode	string	否	商品分类编码
productClassName	string	否	商品分类名称
manageClass	long	否	物料分类id
manageClassCode	string	否	物料分类编码
manageClassName	string	否	物料分类名称
purchaseClass	long	否	采购分类ID
purchaseClassCode	string	否	采购分类编码
purchaseClassName	string	否	采购分类名称
productTemplate	long	否	物料模板id
brand	long	否	品牌id
brandCode	string	否	品牌编码
brandName	string	否	品牌名称
placeOfOrigin	string	否	产地
manufacturer	string	否	生产厂商
productLine	long	否	产品线ID
shareDescription	object	否	分享说明
unit	long	否	主计量单位ID
unitCode	string	否	主计量单位编码
unitName	string	否	主计量单位名称
taxClass	string	否	税收分类码id
orgId	string	否	组织ID
defaultSKUId	long	否	默认SKUID
id	long	否	物料id
tenant	long	否	租户id
erpCode	string	否	外部编码
deleted	boolean	否	是否删除，1：是，0：否。
createTime	string	否	创建时间
createDate	string	否	创建日期
creator	string	否	创建人
realProductAttributeType	long	否	实物物料属性，1：普通物料，2：实体卡券，3：实体储值卡，20：描述性物料，4：设备
weight	double	否	毛重
weightUnit	long	否	毛重单位id
weightUnitCode	string	否	毛重单位编码
weightUnitName	string	否	毛重单位名称
volume	double	否	体积
volumeUnit	long	否	体积单位id
unitUseType	long	否	设置规则, 1:使用物料模板的计量单位、2:使用物料自己的计量单位
enableAssistUnit	boolean	否	启用辅计量, true:启用
creatorId	long	否	创建人
registrationManager	boolean	否	注册证管理, true:是、false:否
authorizationManager	boolean	否	授权书管理, true:是、false:否
planClass	long	否	计划分类id
planClassCode	string	否	计划分类编码
planClassName	string	否	计划分类名称
ytenantId	string	否	友互通id
transType	string	否	物料类型ID
transTypeCode	string	否	物料类型编码
transTypeName	string	否	物料类型名称
productFamily	long	否	产品族，1：是，0：否。
salesAndOperations	long	否	参与SOP，1：是，0：否。
productBarCodes	object	是	物料多条码
id	long	否	多条码id
barCode	string	否	条码值
productAssistUnitExchanges	object	是	物料辅计量换算对照
id	long	否	辅计量换算对照id
productId	long	否	物料id
unitExchangeType	int	否	换算方式，0固定换算，1浮动换算
assistUnitCount	BigDecimal	否	辅计量数量
assistUnit	long	否	辅计量单位id
mainUnitCount	BigDecimal	否	主计量数量
productAttachments	object	是	物料附件
productId	long	否	物料id
folder	string	否	附件地址
fileName	string	否	附件名称
productAlbums	object	是	物料图片
imgBusinessId	string	否	物料图片业务id
videoBusinessId	string	否	物料视频业务id
homepageBusinessId	string	否	物料首页图片业务id
productTags	object	是	物料标签
id	number
小数位数:0,最大长度:19	否	标签分配关系id
tagId	number
小数位数:0,最大长度:19	否	标签id
optionalType	number
小数位数:0,最大长度:10	否	选配方式（0表示特征选配，1表示组件选配）
length	number
小数位数:8,最大长度:10	否	长
width	number
小数位数:8,最大长度:10	否	宽
height	number
小数位数:8,最大长度:10	否	高
5. 正确返回示例
{
	"code": "200",
	"message": "操作成功",
	"data": [
		{
			"productCharacterDef": {
				"WL01": "",
				"WL02": "",
				"WL03": "",
				"WL04": "",
				"WL05": "",
				"WL06": 0,
				"WL07": "",
				"id": ""
			},
			"productPropCharacterDefine": {
				"C_001": "",
				"id": ""
			},
			"detail": {
				"id": 1530976723880902700,
				"productId": 1530976715290968000,
				"productExtendCharacterDef": {
					"WL08": "",
					"WL09": "",
					"id": ""
				},
				"productApplyRangeId": 1530976723880902700,
				"purchaseUnit": ****************,
				"purchaseUnitCode": "KGM",
				"purchaseUnitName": "千克",
				"purchasePriceUnit": ****************,
				"purchasePriceUnitCode": "KGM",
				"purchasePriceUnitName": "千克",
				"stockUnit": ****************,
				"stockUnitCode": "KGM",
				"stockUnitName": "千克",
				"batchUnit": ****************,
				"batchUnitCode": "KGM",
				"batchUnitName": "千克",
				"onlineUnit": ****************,
				"onlineUnitCode": "KGM",
				"onlineUnitName": "千克",
				"offlineUnit": ****************,
				"offlineUnitCode": "KGM",
				"offlineUnitName": "千克",
				"requireUnit": ****************,
				"requireUnitCode": "KGM",
				"requireUnitName": "千克",
				"batchPriceUnit": ****************,
				"batchPriceUnitCode": "KGM",
				"batchPriceUnitName": "千克",
				"inspectionUnit": ****************,
				"inspectionUnitCode": "KGM",
				"inspectionUnitName": "千克",
				"batchPrice": 0,
				"markPrice": 100,
				"lowestMarkPrice": 0,
				"salePrice": 100,
				"marketPrice": 100,
				"primeCosts": 100,
				"settleAccountsRate": 10,
				"displayPrice": true,
				"batchManage": true,
				"expiryDateManage": false,
				"serialNoManage": false,
				"safetyStock": 100,
				"highestStock": 100,
				"lowestStock": 100,
				"ropStock": 100,
				"canSale": true,
				"minOrderQuantity": 100,
				"deliveryDays": 1,
				"enableCyclePurchase": false,
				"enableDeposit": false,
				"depositDealPayType": 0,
				"enableModifyDeposit": false,
				"depositPayType": 0,
				"metaDescription": {
					"simplifiedName": "搜索简介"
				},
				"baseSaleCount": 100,
				"enableContractManagement": false,
				"allowStorePurchase": true,
				"priceChangeAllowed": false,
				"saleInOfflineStore": true,
				"offlineStoreOrder": true,
				"offlineStoreReturn": true,
				"weighingOrNot": false,
				"process": false,
				"material": false,
				"retailPriceDimension": 1,
				"deliverQuantityChange": 1,
				"noTaxCostPrice": 100,
				"checkByBatch": false,
				"accountingByItem": false,
				"storeOffAndOffState": false,
				"orderLoadAndUnloadStatus": false,
				"mallUpCount": 0,
				"mallDownCount": 1,
				"orderUpCount": 0,
				"orderDownCount": 1,
				"tenant": ****************,
				"saleChannel": "1,2",
				"barCode": "条形码",
				"stopStatus": false,
				"checkFree": 0,
				"canOrder": false,
				"onlyOrder": false,
				"orderAdvanceTime": 0,
				"valueManageType": 0,
				"costValuation": 0,
				"checkByCost": false,
				"materialCost": false,
				"planDefaultAttribute": 1,
				"planMethod": 0,
				"keySubPart": false,
				"supplyDemandPolicy": 0,
				"fixedLeadTime": 10,
				"supplyType": 0,
				"produceDepartment": "****************",
				"produceDepartmentCode": "0002",
				"produceDepartmentName": "销售部门",
				"manufacturePlanner": "*****************",
				"manufacturePlannerCode": "A000001",
				"manufacturePlannerName": "甘甘",
				"engineeringDrawingNo": "123",
				"planProduceLimit": 1,
				"utility": false,
				"weigh": false,
				"productVendor": 2584060529692928,
				"productVendorCode": "1847000023",
				"productVendorName": "供应商测试",
				"productBuyer": "*****************",
				"productBuyerCode": "A000001",
				"productBuyerName": "甘甘",
				"maxPrimeCosts": 100,
				"requestOrderLimit": 100,
				"enableSubscribe": false,
				"recommend": false,
				"erpOuterCode": "商家商品外部编码",
				"saleStyle": "1",
				"shortName": "物料简称",
				"mnemonicCode": "助记码",
				"receiptName": {
					"simplifiedName": "开票名称"
				},
				"incomeTaxRates": "2538775356445954",
				"outputTaxRate": "2538775356445954",
				"produceUnit": ****************,
				"produceUnitCode": "KGM",
				"produceUnitName": "千克",
				"warehouseManager": "*****************",
				"warehouseManagerCode": "A000001",
				"warehouseManagerName": "甘甘",
				"deliveryWarehouse": 1519760519882866700,
				"deliveryWarehouseCode": "000133",
				"deliveryWarehouseName": "rewrw",
				"returnWarehouse": 1519760519882866700,
				"returnWarehouseCode": "000133",
				"returnWarehouseName": "rewrw",
				"inStoreExcessLimit": 10,
				"outStoreExcessLimit": 10,
				"storageLossRate": 10,
				"allowNegativeInventory": true,
				"scanCountUnit": 0,
				"exemption": false,
				"warehousingByResult": false,
				"salesReturnsExemption": false,
				"returnsWarehousingByResult": false,
				"periodicalInspection": false,
				"displayName": {},
				"titleMemo": {},
				"barcodeManage": true,
				"receiptWarehouse": 1519760519882866700,
				"receiptWarehouseCode": "000133",
				"receiptWarehouseName": "rewrw",
				"BOMType": 0,
				"batchRule": 0,
				"fixedQuantity": 100,
				"prepareFeed": false,
				"specialMaterials": false,
				"virtualPart": false,
				"demandConsolidation": 0,
				"demandConsolidationType": 0,
				"demandConsolidationUnit": 0,
				"demandConsolidationNumber": 1,
				"demandConsolidationDateType": 0,
				"reservation": false,
				"lossType": 0,
				"ECNControl": false,
				"ytenantId": "0000KWJZLURO8TQU4Y0000",
				"inspectionType": 0,
				"logisticsRelated": false,
				"weighingMode": 0,
				"reviewGrossWeight": false,
				"specialCarTransport": false,
				"orgId": "2538984869630976",
				"businessAttribute": "1,7",
				"businessAttributePurchase": 1,
				"businessAttributeSale": 1,
				"businessAttributeSelfCreate": 0,
				"businessAttributeOutSourcing": 0,
				"testRule": 1,
				"enableStockPeriodRecheck": 0,
				"enableStockExpireCheck": 0,
				"enableSparePartsManagement": 0,
				"fullSetInspection": 0,
				"directProduction": 0,
				"costItems": "1978437901431078916",
				"costItemsCode": "ZDH_FYXM",
				"costItemsName": "自动化_费用项目",
				"manageByInventory": 0,
				"checkByProject": 0,
				"checkBySalesOrders": 0,
				"checkByClient": 0,
				"checkByOutsourcing": 0,
				"atpInspection": 0,
				"doublePick": 10
			},
			"productOrges": [
				{
					"orgId": "2538984869630976",
					"orgType": 1
				}
			],
			"realProductAttribute": 1,
			"code": "code1",
			"name": {
				"simplifiedName": "simplifiedName1"
			},
			"model": {},
			"keywords": {},
			"productClass": 2541495026652160,
			"productClassCode": "000002",
			"productClassName": "黄金",
			"manageClass": 2541494503052544,
			"manageClassCode": "000002",
			"manageClassName": "黄金",
			"purchaseClass": 1546426425542180900,
			"purchaseClassCode": "PTO001",
			"purchaseClassName": "PTO商品",
			"productTemplate": 2541493655655424,
			"brand": 2570445127045635,
			"brandCode": "品牌2",
			"brandName": "品牌2",
			"placeOfOrigin": "产地",
			"manufacturer": "生产厂商",
			"productLine": 2570288159150336,
			"shareDescription": {},
			"unit": ****************,
			"unitCode": "KGM",
			"unitName": "千克",
			"taxClass": "1010105000000000000",
			"orgId": "2538984869630976",
			"defaultSKUId": 1530976723880902700,
			"id": 1530976715290968000,
			"tenant": ****************,
			"erpCode": "外部编码",
			"deleted": false,
			"createTime": "2022-08-25 20:05:48",
			"createDate": "2022-08-25 00:00:00",
			"creator": "YonSuite默认用户",
			"realProductAttributeType": 1,
			"weight": 1,
			"weightUnit": ****************,
			"weightUnitCode": "KGM",
			"weightUnitName": "千克",
			"volume": 2,
			"volumeUnit": 2571526900585472,
			"unitUseType": 2,
			"enableAssistUnit": false,
			"creatorId": 2538773096593408,
			"registrationManager": false,
			"authorizationManager": false,
			"planClass": 2570383093256707,
			"planClassCode": "wxt0009",
			"planClassName": "计划分类9",
			"ytenantId": "0000KWJZLURO8TQU4Y0000",
			"transType": "2538775354987782",
			"transTypeCode": "SYCSR002",
			"transTypeName": "通用物料",
			"productFamily": 0,
			"salesAndOperations": 0,
			"productBarCodes": [
				{
					"id": 1680013386205102085,
					"barCode": "\"123\""
				}
			],
			"productAssistUnitExchanges": [
				{
					"id": 1680014013270327299,
					"productId": 1679287639833313282,
					"unitExchangeType": 0,
					"assistUnitCount": 1,
					"assistUnit": 1476511169941864456,
					"mainUnitCount": 23
				}
			],
			"productAttachments": [
				{
					"productId": 1530976715290968000,
					"folder": "https://apcom-file-pub-npro.obs.cn-north-4.myhuaweicloud.com/iuap-apcom-file-public/iuap-apcom-file/0000L7PIX0Z2ARJ3MD0000/2024052219/78b79e63-39b2-4a7c-a15c-0a25ec2cd109.jpg",
					"fileName": "鲜花10.jpg"
				}
			],
			"productAlbums": [
				{
					"productId": 1530976715290968000,
					"folder": "https://apcom-file-pub-npro.obs.cn-north-4.myhuaweicloud.com/iuap-apcom-file-public/iuap-apcom-file/0000L7PIX0Z2ARJ3MD0000/2024052219/dbb52e2b-8afb-48fb-8b8e-ef062a536b27.jpeg",
					"imgName": "鲜花1.jpeg"
				}
			],
			"imgBusinessId": "df6a101d-d11f-4971-95d4-1a9db4df9e1e",
			"videoBusinessId": "e78c2cd4-7685-65a5-c396-bc1354de7c25",
			"homepageBusinessId": "c16b0bf4-2941-43a4-b382-ab1167de5b17",
			"productTags": [
				{
					"id": 1913058646531506185,
					"tagId": 1837964897149255683
				}
			],
			"optionalType": 0,
			"length": 10.5,
			"width": 10.5,
			"height": 10.5
		}
	]
}
6. 业务异常码
查看业务异常码
异常码	异常码信息	描述

暂时没有数据哦~
7. 错误返回码
错误码	错误信息	描述
999	服务端逻辑异常	检查入参是否填写正确，参数值是否真实存在，仍提示该信息请联系开发人员。
8. 错误返回示例
{"code":"999","message":"服务端逻辑异常"}
9. 接口变更日志
序号	修改时间	变更内容概要
1	2025-07-17