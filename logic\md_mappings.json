{"purchase_order": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "pageIndex": "页码", "pageSize": "每页数", "isSum": "查询表头", "simpleVOs": "查询条件", "field": "排序条件", "op": "比较符", "value1": "参数值1", "queryOrders": "排序", "order": "顺序", "Url": "访问令牌", "code": "单据编码", "message": "返回信息", "data": "数据项", "recordCount": "数量", "recordList": "返回信息", "product_cCode": "物料编码", "invoiceVendor": "开票供应商id", "priceUOM_Precision": "计价单位精度", "modifyStatus": "变更状态", "receiveStatus": "收货状态", "priceUOM_Code": "计价单位编码", "natCurrency": "本币", "unit_code": "主计量编码", "purchaseOrdersCharacteristics": "特征组", "pu.purchaseorder.PurchaseOrders": "表体自定义项特征组", "XS15": "顾客订单号", "XXX0111": "需求分类项", "id": "主表id", "purchaseOrdersDefineCharacter": "特征组", "AA": "批次号", "CG00025": "未收数量2", "CG01": "供应商备注", "WW": "委外交货日期", "XS11": "需求分类号", "purchaseOrderDefineCharacter": "特征组", "pu.purchaseorder.PurchaseOrder": "表头自定义项特征组", "U9002": "U9采购订单号", "XS31": "更改次数", "isWfControlled": "审批流控制", "purchaseOrders_arrivedStatus": "到货状态", "bmake_st_purinvoice": "流程订货订单开蓝票", "realProductAttribute": "实物商品属性", "purchaseOrders_inWHStatus": "入库状态", "natCurrency_priceDigit": "本币", "bmake_st_purinrecord_red": "流程退库", "status": "状态", "currency_moneyDigit": "本币金额精度", "currency_code": "币种编码", "vouchdate": "单据日期", "invoiceVendor_name": "开票供应商", "vendor": "供货供应商id", "purchaseOrders_payStatus": "核销状态", "purchaseOrders_warehouse_code": "仓库编码", "currency": "币种", "pubts": "时间戳", "org_name": "采购组织", "generalPurchaseOrderType": "交易扩展参数", "isFlowCoreBill": "流程核心单据", "creator": "创建者", "product": "物料id", "inInvoiceOrg_name": "收票组织", "product_defaultAlbumId": "物料首图片", "purchaseOrders_id": "订单行id", "demandOrg_name": "需求组织", "createTime": "创建时间", "purUOM_Precision": "采购单位精度", "currency_priceDigit": "币种单价精度", "bEffectStock": "影响可用量", "inOrg": "收货组织id", "bustype_name": "交易", "inInvoiceOrg": "收票组织id", "product_cName": "物料名称", "bmake_st_purinvoice_red": "流程订货订单开红票", "product_model": "型号", "vendor_name": "供应商", "vendor_code": "供应商编码", "barCode": "单据条码", "isContract": "需要与供应商协同", "unit_name": "主计量", "unit": "主计量id", "purchaseOrders_invoiceStatus": "发票状态", "natCurrency_moneyDigit": "本币", "unit_Precision": "主计量精度", "natCurrency_code": "本币", "product_modelDescription": "规格说明", "demandOrg": "需求组织id", "bizFlow": "流程ID", "realProductAttributeType": "实物商品属性", "priceUOM": "计价单位id", "bizstatus": "状态", "bizFlow_version": "版本信息", "currency_name": "币种", "org": "采购组织", "bmake_st_purinrecord": "流程入库", "purchaseOrders_purUOM": "采购单位编码", "bustype": "交易id", "retailInvestors": "散户", "inOrg_name": "收货组织", "priceUOM_Name": "计价单位名称", "listdiscountTaxType": "扣税类别", "bizFlow_name": "流程名称", "pageCount": "页数", "beginPageIndex": "起始页", "endPageIndex": "结束页"}, "sales_order": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "pageIndex": "页号", "pageSize": "每页记录数", "code": "单据编号", "nextStatusName": "订单状态", "open_orderDate_begin": "制单日期开始时间", "open_orderDate_end": "制单结束时间", "open_hopeReceiveDate_begin": "期望收货开始时间", "open_hopeReceiveDate_end": "期望收货截止", "open_vouchdate_begin": "单据开始时间", "open_vouchdate_end": "单据截止时间", "isSum": "查询表头", "simpleVOs": "查询条件", "op": "比较符", "value1": "查询条件值1", "field": "排序条件", "logicOp": "分级逻辑符", "value2": "查询条件值2", "queryOrders": "排序", "order": "顺序", "Url": "访问令牌", "message": "调用失败时的错误信息", "data": "调用成功时的返回数据", "recordCount": "总共记录数", "recordList": "记录列表", "vouchdate": "单据日期", "id": "特征id", "parentOrderNo": "父级订单号", "salesOrgId": "销售组织id", "salesOrgId_name": "销售组织名称", "saleDepartmentId": "销售部门id", "transactionTypeId": "交易id", "transactionTypeId_name": "交易名称", "agentId": "客户id", "agentId_name": "客户名称", "receiveContacter": "客户联系人", "receiveContacterPhone": "客户联系人电话", "receievInvoiceMobile": "收票手机号", "receievInvoiceEmail": "收票邮箱", "purchaseNo": "客户采购订单号", "corpContact": "销售业务员id", "corpContactUserName": "销售业务员", "settlementOrgId_name": "开票组织名称", "corpContactUserErpCode": "业务员erp编码", "orderPrices": "订单金额", "currency": "币种id", "currency_priceDigit": "原币单价精度", "currency_moneyDigit": "原币金额精度", "originalName": "币种", "natCurrency": "本币pk", "natCurrency_priceDigit": "本币单价精度", "natCurrency_moneyDigit": "本币金额精度", "domesticCode": "本币简称", "domesticName": "本币", "exchRate": "汇率", "exchangeRateType_name": "汇率名称", "exchangeRateType": "汇率Idid", "ctTplId": "合同模板id", "ctTplCode": "合同模板编码", "ctTplName": "合同模板", "signFileId": "待签署合同文件", "signStatus": "电子签署状态", "statusCode": "订单当前状态码", "nextStatus": "订单状态", "currentStatus": "当前状态位置", "payStatusCode": "付款状态", "settlementOrgId": "财务组织id", "lockIn": "标记锁", "confirmDate": "订单确认时间", "payDate": "订单付款时间", "orderPayType": "支付方式", "settlement": "结算方式id", "shippingChoiceId": "发运方式id", "sendDate": "预计发货日期", "hopeReceiveDate": "期望收货日期", "opposeMemo": "驳回批注", "haveDelivery": "存在发货单", "occupyInventory": "库存占用时机标识", "separatePromotionType": "拆单规则标识", "synSourceOrg": "协同来源组织id", "synSourceTenant": "协同来源租户", "synSourceOrg_name": "协同来源组织名称", "tagName": "采购组织弹框", "bizId": "商家id", "bizName": "商家名称", "agentRelationId": "客户交易关系id", "points": "积分", "pubts": "时间戳", "pubuts": "时间戳", "orderInvoice": "发票信息", "orderShippingAddress": "收货地址信息", "orderErp": "订单erp", "deliveryDate": "交货日期", "isWfControlled": "审批流控制", "verifystate": "审批状态", "status": "状态", "orderDefineCharacter": "特征组", "voucher.order.Order": "表头自定义项特征组", "A086": "账款到期日", "AE86": "销售订单收款协议", "U9004": "U9销售订单号", "XS01": "成交方式", "XS02": "装运港", "XS03": "目的港", "XS04": "溢短装", "XS05": "投保比例", "XS06": "基本险别", "XS07": "允许转运", "XS08": "允许分批", "XS09": "最迟装运期", "XS10": "贸易方式", "XS12": "合同号", "XS13": "商标", "XS14": "装柜", "XS17": "付款方式", "XS18": "首次", "XS19": "见附件", "XS20": "首次未确定", "XS21": "首次未确定", "XS22": "返单", "XS23": "确定无变更", "XS24": "返单未确定", "XS25": "返单未确定", "XS26": "确定变更", "XS27": "单头备注1", "XS28": "单头备注2", "XS29": "单头备注3", "XS30": "单头备注4", "XS31": "更改次数", "XS32": "车牌号码", "isFinishDelivery": "订单发完货", "productId_pbatchName": "商品包装单位", "idKey": "行标识", "productId": "商品id", "priceMark": "价格标识", "isBatchManage": "批次管理", "isExpiryDateManage": "有效期管理", "expireDateNo": "保质期", "expireDateUnit": "保质期单位", "skuId": "商品SKUid", "erpCode": "skuERP编码", "orderProductType": "商品售卖", "productCode": "商品编码", "productName": "商品名称", "skuCode": "SKU编码", "specDescription": "规格描述", "projectId": "项目id", "unitExchangeType": "浮动", "unitExchangeTypePrice": "浮动", "productAuxUnitName": "销售单位", "productUnitName": "计价单位", "qtyName": "主计量", "orderDetailPrices": "订单详情金额", "orderDetailId": "主体ID", "prepayInvRvnRecogBkgMeth": "预收款开票应收入账方式", "checkByRevenueManagement": "收入管理核算", "revPerformObligation": "已生成收入履约义务", "serviceStartDate": "服务起始日期", "serviceEndDate": "服务结束日期", "optionalQuotationId_code": "报价配置清单编码", "variantconfigctsCode": "配置号", "variantconfigctsVersion": "配置清单版本", "calBase": "计算基准", "stockName": "发货仓库", "stockOrgId_name": "库存组织", "consignTime": "计划发货日期", "projectId_name": "项目名称", "projectId_code": "项目编码", "taxId": "数目税率id", "costCurrencyName": "成本币种", "costAmt": "成本金额", "costPrice": "成本价", "stockId": "仓库ID", "lineno": "行号", "orderDetails_stockOrgId": "库存组织id", "taxItems": "税目", "taxCode": "税目税率编码", "shoppingCartId": "购物车id", "groupId": "分组Id", "rebateReturnProductId": "返货单商品id", "mutualActivities": "活动的对象", "activities": "包含的", "bizProductId": "商家商品id", "bizSkuId": "商家skuid", "iDeleted": "删除", "iOrgId": "组织ID", "memo": "备注", "createDate": "创建日期", "creatorId": "创建人", "auditorId": "审核人ID", "auditDate": "审批日期", "closerId": "关闭人ID", "closeDate": "关闭日期", "modifierId": "修改人id", "modifyDate": "修改日期", "cCreator": "创建人", "iProductAuxUnitId": "销售单位id", "iProductUnitId": "计价单位id", "masterUnitId": "主计量单位id", "purUOM_Precision": "销售单位精度", "priceUOM_Precision": "计价单位精度", "unit_Precision": "主计量单位精度", "cBizName": "供应商名称", "orderId": "订单ID", "orderDetailCharacteristics": "特征组", "voucher.order.OrderDetail": "表体自定义项特征组", "XS15": "顾客订单号", "XXX0111": "需求分类项", "orderDetailDefineCharacter": "特征组", "WL06": "料品每箱台数", "XS11": "需求分类号", "creator": "创建人", "createTime": "创建时间", "auditor": "审批人", "auditTime": "审批时间", "closeTime": "关闭时间", "closer": "关闭人", "modifier": "修改人", "modifyTime": "修改时间", "bmake_st_salesout": "流程入库", "bmake_voucher_delivery": "流程发货", "bizFlow": "流程ID", "bmake_voucher_saleinvoice": "流程开票", "isFlowCoreBill": "流程核心单据", "bizFlow_version": "版本信息", "batchNo": "批次号", "productDate": "生产日期", "invalidDate": "有效期至", "isAdvRecInv": "预收款开票", "tradeRouteID": "贸易路径ID", "tradeRouteID_code": "贸易路径编码", "tradeRouteID_name": "贸易路径", "isEndTrade": "末级站点", "tradeRouteLineno": "站点", "collaborationPocode": "协同来源单据号", "collaborationPodetailid": "协同来源单据子表id", "collaborationPoid": "协同来源单据主表id", "collaborationPorowno": "协同来源单据行号", "collaborationSource": "协同来源单据", "sumRecordList": "合计", "pageCount": "总共记录数", "beginPageIndex": "页码列表的开始索引", "endPageIndex": "页码列表的结束索引"}, "production_order": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "pageIndex": "当前页", "pageSize": "页大小", "id": "特征id", "code": "生产订单号", "status": "订单状态", "transTypeId": "交易ID", "orgId": "组织ID", "productionDepartmentId": "生产部门Id", "OrderProduct": "启用完工报告", "createTime": "创建时间", "vouchdate": "单据日期", "isShowProcess": "展示工序", "isShowMaterial": "展示材料", "isShowByProduct": "展示联副产品", "isShowActivity": "展示作业", "orderProduct.productId.code": "物料编码", "open_pubts_begin": "时间戳", "open_pubts_end": "时间戳", "orderProduct.materialApplyStatus": "领料申请状态", "orderProduct.materialStatus": "领料状态", "orderProduct.finishedWorkApplyStatus": "完工申报状态", "orderProduct.stockStatus": "入库状态", "open_auditTime_begin": "审核时间", "open_auditTime_end": "审核时间", "open_auditDate_begin": "审核日期", "open_auditDate_end": "审核日期", "orderProduct.retMaterialApplyFlag": "退料申请标识", "simpleVOs": "扩展查询条件", "field": "属性名", "op": "逻辑连接符", "value1": "查询条件值1", "value2": "查询条件值2", "logicOp": "逻辑连接符", "conditions": "下级查询条件", "Url": "访问令牌", "message": "调用失败时的错误信息", "data": "调用成功时的返回数据", "recordCount": "记录总数", "recordList": "返回数据对象", "OrderProduct_materialName": "物料名称", "OrderProduct_startDate": "开工日期", "productDefineDts": "特征组", "po.order.OrderProduct": "自由项特征组", "WW": "委外交货日期", "XS11": "需求分类号", "XS15": "顾客订单号", "freeCharacteristics": "特征组", "XXX0111": "需求分类项", "defineDts": "特征组", "po.order.Order": "表头自定义特征组", "U9003": "U9生产订单号", "OrderProduct_lineNo": "行号", "productUnitPrecision": "生产单位精度", "OrderProduct_scrap": "废品率", "OrderProduct_orgId": "库存组织id", "OrderProduct_skuCode": "物料SKU编码", "mainUnitPrecision": "主计量精度", "OrderProduct_sourceType": "来源单据", "OrderProduct_productId": "物料Id", "OrderProduct_mrpQuantity": "净算量", "OrderProduct_changeType": "换算方式", "departmentName": "生产部门", "orgName": "组织", "auditTime": "审核时间", "auditDate": "审核日期", "isWfControlled": "审批流控制", "OrderProduct_quantity": "生产数量", "OrderProduct_completedQuantity": "已完工数量", "OrderProduct_incomingQuantity": "累计入库数量", "isHold": "挂起状态", "OrderProduct_skuName": "物料SKU名称", "routingVersion": "工艺路线版本", "routingCode": "工艺路线编码", "routingId": "工艺路线Id", "OrderProduct_completedFlag": "启用完工报告", "OrderProduct_materialCode": "物料编码", "OrderProduct_productionUnitId": "生产单位ID", "returncount": "退回次数", "routingName": "工艺路线名称", "verifystate": "审批状态", "creatorId": "创建人Id", "orderProduct_id": "订单产品行Id", "OrderProduct_auxiliaryQuantity": "生产件数", "OrderProduct_materialApplyFlag": "启用领料申请", "OrderProduct_mainUnit": "主计量Id", "OrderProduct_mainUnitTruncationType": "主计量舍位方式", "transTypeName": "交易名称", "pubts": "时间戳", "OrderProduct_skuId": "物料SKUId", "OrderProduct_productUnitTruncationType": "生产单位舍位方式", "entrustProcessType": "受托加工方式", "OrderProduct_retMaterialApplyFlag": "启用退料申请", "OrderProduct_orgName": "库存组织", "creator": "创建人", "OrderProduct_finishDate": "完工日期", "OrderProduct_changeRate": "换算率", "OrderProduct_isHold": "挂起状态", "entrustCustomer": "受托客户id", "OrderProduct_versionCode": "BOM版本", "OrderProduct_bomId": "物料清单Id", "OrderProduct_productUnitName": "生产单位", "OrderProduct_mainUnitName": "主计量", "OrderProduct_materialApplyStatus": "领料申请状态", "OrderProduct_materialStatus": "领料状态", "entrustCustomerName": "受托客户", "OrderProduct_finishedWorkApplyStatus": "完工申报状态", "OrderProduct_stockStatus": "入库状态", "offChartReceiptIsAllowed": "允许表外产出", "apsLock": "排程状态", "dailyschQuantity": "排产数量", "dailyschStatus": "排产状态", "dailyschConquantity": "排产确认数量", "transTypeCode": "交易编码", "orderMaterial": "材料信息", "materialDefineDts": "特征组", "po.order.OrderMaterial": "自由项特征组", "isWholeSet": "齐套标识", "receivedQuantity": "已领数量", "recipientQuantity": "应领数量", "numeratorQuantity": "分子用量", "stockUnitPrecision": "库存单位精度", "mainUnitTruncationType": "主计量舍位方式", "stockUnitName": "库存单位", "unitUseQuantity": "单位产出数量", "auxiliaryReceivedQuantity": "已领件数", "auxiliaryRecipientQuantity": "应领件数", "stockUnitTruncationType": "库存单位舍位方式", "scrap": "废品率", "lineNo": "行号", "supplyType": "发料方式", "truncUp": "向上取整", "substituteFlag": "BOM替代标识", "changeRate": "换算率", "denominatorQuantity": "分母用量", "bomId": "物料清单Id", "mainUnit": "主计量ID", "fixedQuantity": "固定用量", "productName": "物料名称", "productCode": "物料编码", "productId": "物料id", "changeType": "换算方式", "orderProductId": "生产订单行ID", "bomMaterialId": "物料清单子件Id", "mainUnitName": "主计量单位", "materialName": "物料名称", "requirementDate": "需求日期", "skuCode": "物料SKU编码", "stockUnitId": "库存单位Id", "mustLossQuantity": "固定损耗", "calcCostFlag": "计算成本", "orderMaterialExpinfo": "齐套检查点", "excessAppliedQty": "超额申请数量", "auxiliaryExcessAppliedQty": "超额申请件数", "excessRecipientQty": "已超领数量", "auxiliaryExcessRecipientQty": "已超领件数", "appliedRetQuantity": "退料申请数量", "auxiliaryAppliedRetQuantity": "退料申请件数", "appliedRetRestQuantity": "退料申请未退库数量", "auxiliaryAppliedRetRestQuantity": "退料申请未退库件数", "excessAppliedRetQty": "超额退料申请数量", "auxiliaryExcessAppliedRetQty": "超额退料申请件数", "excessAppliedRetRestQty": "超额退料申请未退库数量", "auxiliaryExcessAppliedRetRestQty": "超额退料申请未退库件数", "appliedRestQuantity": "领料申请未出库数量", "auxiliaryAppliedRestQuantity": "领料申请未出库件数", "excessAppliedRestQty": "超额领料申请未出库数量", "auxiliaryExcessAppliedRestQty": "超额领料申请未出库件数", "projectId": "项目Id", "projectCode": "项目编码", "projectName": "项目名称", "wbsCode": "WBS任务编码", "wbsName": "WBS任务名称", "activity": "活动", "activityCode": "活动编码", "activityName": "活动名称", "orderActivity": "作业信息", "lineNum": "行号", "orderId": "订单ID", "activityId": "作业标准ID", "activityType": "作业ID", "activityTypeCode": "作业类别编码", "activityTypeName": "作业", "orderProcessId": "工序ID", "opSn": "工序顺序号", "activityClass": "作业类别", "workCenterId": "工作中心ID", "workCenterCode": "工作中心编码", "workCenterName": "工作中心名称", "operationId": "工序ID", "operationCode": "工序编码", "operationName": "工序名称", "activityUnit": "数量单位ID", "activityUnitName": "数量单位", "activityUnitPrecision": "数量单位精度", "activityUnitTruncationType": "数量单位舍入方式", "usageUnit": "计量单位ID", "usageUnitName": "计量单位", "usageUnitPrecision": "计量单位精度", "usageUnitTruncationType": "计量单位舍入方式", "stdUsageQty": "额定总用量", "planUsageQty": "计划总用量", "usageQty": "标准作业量", "isCalcCost": "计算成本", "activityQty": "数量", "usageBasis": "计量基础", "isAutoCreate": "自动创建", "orderByProduct": "联副产品信息", "orderProductLineNo": "行号", "manufacturingSpecification": "物料规格", "productionType": "产出", "warehouseName": "预入仓库", "po.order.OrderByProduct": "自定义项特征组", "byProductDefineDts": "特征组", "productionDate": "产出日期", "isBatchManage": "批次管理", "isExpiryDateManage": "效期管理", "quantity": "产出数量", "productUnitTruncationType": "生产单位舍位方式", "productionUnitId": "生产单位ID", "warehouseId": "预入仓库Id", "offChartReceipt": "表外产出", "productUnitName": "生产单位", "orderProcess": "工序信息", "processDefineDts": "特征组", "po.order.OrderProcess": "自定义项特征组", "operationControlId": "工序控制码ID", "nextId": "后序ID", "doScheduling": "参与调度", "transferProcplanProdQty": "转工序作业计划件数", "finishGoodsId": "完工库位id", "finishWarehouseId": "完工仓库id", "preSn": "前序顺序号", "transferProcplanQty": "转工序作业计划数量", "executeOrgName": "执行组织", "outUnitId": "产出单位ID", "outUnitTruncationType": "产出单位舍位方式", "mainUnitId": "主计量单位ID", "checkType": "质检方式", "routingOperationId": "工艺路线行ID", "prepareTime": "计划准备时间", "routingOperationProcessTime": "单批加工时间", "qty": "计划生产数量", "occupyProduction": "占用产能", "computingCosts": "计算成本", "mainChangeRate": "生产", "immediateHandover": "即时交接", "operationIdRouteDesc": "工艺描述", "outUnitName": "产出单位", "operationControlName": "工序控制码名称", "outChangeRate": "产出", "processTime": "计划加工时间", "procPlanCreate": "工序作业计划创建", "nextSn": "后序顺序号", "prodQty": "计划生产件数", "processType": "加工", "sn": "顺序号", "planStartDate": "计划开工时间", "planEndDate": "计划完工时间", "timeUnit": "时间单位", "operationControlCode": "工序控制码编码", "firstCheck": "首检", "isOutsource": "委外", "outUnitPrecision": "产出单位精度", "executeOrgId": "执行组织ID", "reportWork": "报工", "scheduleProdNum2": "计划加工数量", "totalCompleteNum": "累计完成数量", "totalCompleteNum1": "累计完成件数", "totalCompleteNum2": "累计完成数量", "totalQualifiedNum": "累计合格数量", "totalQualifiedNum1": "累计合格件数", "totalQualifiedNum2": "累计合格数量", "totalScrapNum": "累计报废数量", "totalScrapNum1": "累计报废件数", "totalScrapNum2": "累计报废数量", "totalReworkNum": "累计待返工数量", "totalReworkNum1": "累计待返工件数", "totalReworkNum2": "累计待返工数量", "totalReworkProcessNum": "累计返工处理数量", "totalReworkProcessNum1": "累计返工处理件数", "totalReworkProcessNum2": "累计返工处理数量", "totalTurnNum": "累计转出数量", "totalTurnNum1": "累计转出件数", "totalTurnNum2": "累计转出数量", "totalQualifiedTurnNum": "累计合格转出数量", "totalQualifiedTurnNum1": "累计合格转出件数", "totalQualifiedTurnNum2": "累计合格转出数量", "totalReworkTurnNum": "累计返工转出数量", "totalReworkTurnNum1": "累计返工转出件数", "totalReworkTurnNum2": "累计返工转出数量", "scrapInNum": "累计报废转出数量", "scrapInNum1": "累计报废转出件数", "scrapInNum2": "累计报废转出数量", "out_sys_id": "外部来源Id", "out_sys_code": "外部来源编码", "out_sys_version": "外部系统版本", "out_sys_type": "外部来源", "OrderProduct_projectId": "项目Id", "out_sys_rowno": "外部来源行号", "out_sys_lineid": "外部来源行", "OrderProduct_projectCode": "项目编码", "OrderProduct_projectName": "项目名称", "OrderProduct_wbs": "wbs", "OrderProduct_wbsCode": "WBS任务编码", "OrderProduct_wbsName": "WBS任务名称", "OrderProduct_activity": "活动", "OrderProduct_activityCode": "活动编码", "OrderProduct_activityName": "活动名称", "firstCheckType": "首检控制方式", "firstCheckStatus": "首检状态", "sumRecordList": "合计集合", "pageCount": "总页数", "beginPageIndex": "开始页码", "endPageIndex": "结束页码"}, "subcontract_order": {"orderSubcontractProduct_isPresent": {"chinese_name": "Subcontract产品Present", "data_type": "boolean", "description": "", "max_length": null, "source": "config_file_conversion"}, "requireConsult": {"chinese_name": "Consult", "data_type": "boolean", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderProduct_id": {"chinese_name": "订单产品行Id", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "productDefineDts": {"chinese_name": "特征组", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "productDefineDts.WW": {"chinese_name": "DefineDtsWW", "data_type": "datetime", "description": "", "max_length": null, "source": "config_file_conversion"}, "productDefineDts.XS11": {"chinese_name": "DefineDtsXS", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "productDefineDts.ytenant": {"chinese_name": "DefineDts", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "productDefineDts.XS15": {"chinese_name": "DefineDtsXS", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "productDefineDts.id": {"chinese_name": "DefineDts", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "productDefineDts.pubts": {"chinese_name": "DefineDts", "data_type": "datetime", "description": "", "max_length": null, "source": "config_file_conversion"}, "subcontractProductExt_arrivePlanNoQty": {"chinese_name": "产品ExtPlanNo数量", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_natSum": {"chinese_name": "Subcontract产品Sum", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "arriveStatus": {"chinese_name": "状态", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "transTypeId": {"chinese_name": "交易ID", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "mainUnitPrecision": {"chinese_name": "主计量精度", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderProduct_retMaterialApplyFlag": {"chinese_name": "产品物料ApplyFlag", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_changeRate": {"chinese_name": "换算率", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderProduct_rcvOrgIdName": {"chinese_name": "名称", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "id": {"chinese_name": "特征id", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_sourceid": {"chinese_name": "订单产品", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "fcIdPriceDigit": {"chinese_name": "价格", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_sourceautoid": {"chinese_name": "订单产品", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "isWfControlled": {"chinese_name": "审批流控制", "data_type": "boolean", "description": "", "max_length": null, "source": "config_file_conversion"}, "verificationStatus": {"chinese_name": "状态", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "requireConfirm": {"chinese_name": "Confirm", "data_type": "boolean", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderProduct_rcvOrgId": {"chinese_name": "产品OrgId", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "productId_isBatchManage": {"chinese_name": "IdBatchManage", "data_type": "boolean", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_requestedPaymentFC": {"chinese_name": "Subcontract产品PaymentFC", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderProduct_lineNo": {"chinese_name": "行号", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProductExt_autoClose": {"chinese_name": "Subcontract产品ExtClose", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_subcontractQuantityPU": {"chinese_name": "计价数量", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontract_tcId": {"chinese_name": "SubcontractId", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "sourceType": {"chinese_name": "", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "warehouseId": {"chinese_name": "供应仓库id", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_rcvAddrType": {"chinese_name": "", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "taxFC": {"chinese_name": "FC", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProductExt_inClose": {"chinese_name": "Subcontract产品ExtClose", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "tcIdMoneyDigit": {"chinese_name": "IdMoneyDigit", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_materialCode": {"chinese_name": "制造物料编码", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "productId_isExpiryDateManage": {"chinese_name": "日期", "data_type": "boolean", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_subcontractUnitId": {"chinese_name": "单位", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "productDefineDts__id": {"chinese_name": "特征id", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "consultStatus": {"chinese_name": "状态", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "status": {"chinese_name": "订单状态", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "verifystate": {"chinese_name": "审批状态", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_natMoney": {"chinese_name": "Subcontract产品Money", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "tradeThrowVersion": {"chinese_name": "ThrowVersion", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "materialVerifStatus": {"chinese_name": "状态", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_firstsource": {"chinese_name": "订单产品", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_priceUnitId": {"chinese_name": "单位", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_deliveryDate": {"chinese_name": "交货日期", "data_type": "datetime", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_subcontractUnitName": {"chinese_name": "委外单位", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderProduct_arriveSubQuantity": {"chinese_name": "数量", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "warehouseName": {"chinese_name": "供应仓库", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orgId": {"chinese_name": "库存组织id", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "vouchdate": {"chinese_name": "单据日期", "data_type": "datetime", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_subcontractQuantityMU": {"chinese_name": "委外数量", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_oriMoney": {"chinese_name": "Subcontract产品Money", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "transTypeName": {"chinese_name": "交易名称", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontract_tcOrgId_name": {"chinese_name": "名称", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "pubts": {"chinese_name": "时间戳", "data_type": "datetime", "description": "", "max_length": null, "source": "config_file_conversion"}, "isFlowCoreBill": {"chinese_name": "FlowCoreBill", "data_type": "boolean", "description": "", "max_length": null, "source": "config_file_conversion"}, "totalMoneyFC": {"chinese_name": "MoneyFC", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "netMoneyFC": {"chinese_name": "MoneyFC", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_bomId": {"chinese_name": "物料清单Id", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_discountTaxType": {"chinese_name": "", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "creator": {"chinese_name": "创建人", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_mainUnitName": {"chinese_name": "主计量", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontract_costAccountingMethod": {"chinese_name": "SubcontractAccountingMethod", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderProductPubts": {"chinese_name": "产品Pubts", "data_type": "datetime", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_isHold": {"chinese_name": "挂起状态", "data_type": "boolean", "description": "", "max_length": null, "source": "config_file_conversion"}, "priceUnitPrecision": {"chinese_name": "计价单位精度", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_priceUnitName": {"chinese_name": "计价单位", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "productDefineDts__WW": {"chinese_name": "DefineDtsWW", "data_type": "datetime", "description": "", "max_length": null, "source": "config_file_conversion"}, "subcontractVendorId": {"chinese_name": "委外商Id", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "productDefineDts__XS15": {"chinese_name": "顾客订单号", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontract_osmBusiType": {"chinese_name": "", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "createTime": {"chinese_name": "创建时间", "data_type": "datetime", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_subcontractUnitIdCode": {"chinese_name": "单位", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_materialModelDescription": {"chinese_name": "描述", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_oriTaxUnitPrice": {"chinese_name": "单位", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "productDefineDts__XS11": {"chinese_name": "需求分类号", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontract_closeApply": {"chinese_name": "SubcontractApply", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "materialApplyFlag": {"chinese_name": "ApplyFlag", "data_type": "boolean", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_natUnitPrice": {"chinese_name": "单位", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "isBeginning": {"chinese_name": "Beginning", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_continuousOsm": {"chinese_name": "Subcontract产品Osm", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontract_tcIdName": {"chinese_name": "名称", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_tcOrgIdSon": {"chinese_name": "Subcontract产品OrgIdSon", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "subcontractProductExt_arrivePlanNoSubQty": {"chinese_name": "产品ExtPlanNoSub数量", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_subcontractToPrice": {"chinese_name": "价格", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_upcode": {"chinese_name": "编码", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "subcontractProductExt_arriveQuantity": {"chinese_name": "数量", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "totalMoneyTC": {"chinese_name": "MoneyTC", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_taxRate": {"chinese_name": "Subcontract产品Rate", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProductConsult_requireSign": {"chinese_name": "Subcontract产品ConsultSign", "data_type": "boolean", "description": "", "max_length": null, "source": "config_file_conversion"}, "subcontractProductExt_arrivePlanSubQty": {"chinese_name": "产品ExtPlanSub数量", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_subcontractQuantitySU": {"chinese_name": "委外件数", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_oriSum": {"chinese_name": "Subcontract产品Sum", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_requestedPaymentTC": {"chinese_name": "Subcontract产品PaymentTC", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "stockStatus": {"chinese_name": "状态", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "materialApplyStatus": {"chinese_name": "状态", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_priceChangeType": {"chinese_name": "价格", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_materialName": {"chinese_name": "物料名称", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_osTaxRateIdName": {"chinese_name": "名称", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "mainUnit": {"chinese_name": "主单位id", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_versionMemo": {"chinese_name": "备注", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orgName": {"chinese_name": "库存组织", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_oriUnitPrice": {"chinese_name": "单位", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderProduct_arriveQuantity": {"chinese_name": "数量", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "isHold": {"chinese_name": "挂起状态", "data_type": "boolean", "description": "", "max_length": null, "source": "config_file_conversion"}, "netMoneyTC": {"chinese_name": "MoneyTC", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "tcIdPriceDigit": {"chinese_name": "价格", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_tcOrgIdSon_name": {"chinese_name": "产品行收票组织名称", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_natTax": {"chinese_name": "Subcontract产品Tax", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProductExt_arriveClose": {"chinese_name": "Subcontract产品ExtClose", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_sourceType": {"chinese_name": "来源单据", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_version": {"chinese_name": "BOM版本", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_natTaxUnitPrice": {"chinese_name": "单位", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "materialStatus": {"chinese_name": "状态", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "code": {"chinese_name": "委外订单号", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_firstupcode": {"chinese_name": "编码", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "bizFlow": {"chinese_name": "Flow", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "mainUnitTruncationType": {"chinese_name": "单位", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "taxTC": {"chinese_name": "TC", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_productId": {"chinese_name": "物料Id", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderProduct_orderSubcontractProduct_taxRateId": {"chinese_name": "产品Subcontract产品RateId", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "bizFlow_version": {"chinese_name": "Flow", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "changeFlag": {"chinese_name": "Flag", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "paymentStatus": {"chinese_name": "状态", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderProduct_firstsourceid": {"chinese_name": "产品", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontract_paymentBy": {"chinese_name": "SubcontractBy", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_lineAttach": {"chinese_name": "Subcontract产品Attach", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_tcOrgAccountSon": {"chinese_name": "Subcontract产品OrgAccountSon", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "subcontractUnitPrecision": {"chinese_name": "委外单位精度", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "subcontractVendorName": {"chinese_name": "委外商", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "changeType": {"chinese_name": "换算方式", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "fcIdMoneyDigit": {"chinese_name": "IdMoneyDigit", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "subcontractProduct_isContinuousOsmEnd": {"chinese_name": "产品ContinuousOsmEnd", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontract_tcOrgAccount": {"chinese_name": "产品行跨组织委外结算", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "freeCharacteristics": {"chinese_name": "特征组", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "freeCharacteristics.ytenant": {"chinese_name": "Characteristics", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "freeCharacteristics.id": {"chinese_name": "Characteristics", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "freeCharacteristics.pubts": {"chinese_name": "Characteristics", "data_type": "datetime", "description": "", "max_length": null, "source": "config_file_conversion"}, "OrderProduct_lineClose": {"chinese_name": "订单产品Close", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontract_tcOrgId": {"chinese_name": "产品行收票组织id", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontractProduct_oriTax": {"chinese_name": "Subcontract产品Tax", "data_type": "decimal", "description": "", "max_length": null, "source": "config_file_conversion"}, "invoiceStatus": {"chinese_name": "状态", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "subcontractProductExt_hasArrivePlan": {"chinese_name": "产品ExtArrivePlan", "data_type": "integer", "description": "", "max_length": null, "source": "config_file_conversion"}, "orderSubcontract_fcId": {"chinese_name": "SubcontractId", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "bizFlow_name": {"chinese_name": "名称", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "memo": {"chinese_name": "备注", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "XXX0111": {"chinese_name": "需求分类项", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "AA": {"chinese_name": "批次号", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "CG00025": {"chinese_name": "未收数量2", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "CG01": {"chinese_name": "供应商备注", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "U9002": {"chinese_name": "U9采购订单号", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "XS31": {"chinese_name": "更改次数", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "CG02": {"chinese_name": "供应商备注2", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "CG03": {"chinese_name": "供应商备注3", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "CG04": {"chinese_name": "供应商备注4", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "CG05": {"chinese_name": "供应商备注5", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "SF04": {"chinese_name": "自由项4", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "SF05": {"chinese_name": "自由项5", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}, "SF06": {"chinese_name": "自由项6", "data_type": "string", "description": "", "max_length": null, "source": "config_file_conversion"}}, "applyorder": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "bustype": "交易id", "product": "物料id", "pageIndex": "分页", "pageSize": "每页条数", "isSum": "按照表头查询", "queryOrders": "排序", "field": "属性名", "order": "顺序", "simpleVOs": "扩展条件查询", "op": "比较符", "value1": "值1", "Url": "访问令牌", "code": "请购单编号", "message": "调用失败时的错误信息", "data": "调用成功时的返回数据", "salesOrgId_name": "销售组织名称", "pageCount": "页数", "beginPageIndex": "起始页", "endPageIndex": "结束页", "recordCount": "记录数", "pubts": "时间戳", "synSourceOrg": "协同来源组织id", "recordList": "返回列表信息", "vouchdate": "单据日期", "returncount": "退回次数", "isWfControlled": "审批流控制", "verifystate": "审批状态", "bustype_name": "交易名称", "applyDept": "请购部门id", "applyDept_name": "请购部门名称", "bizstatus": "单据状态", "status": "单据状态", "currency": "币种id", "currency_name": "币种名称", "warehouseId": "要货仓库id", "warehouseId_name": "要货仓库名称", "source": "来源单据", "store": "所属门店id", "isUretailVoucher": "零售", "store_name": "所属门店名称", "org": "需求组织id", "org_name": "需求组织名称", "custom": "客户id", "creator": "制单人", "createTime": "制单时间", "modifier": "修改人", "modifyTime": "修改时间", "closer": "关闭人", "closeTime": "关闭时间", "locker": "锁定人", "lockTime": "锁定时间", "operator": "请购员id", "operator_name": "请购员名称", "auditor": "审核人", "auditTime": "审核时间", "auditDate": "审核日期", "submitor": "提交人", "submitTime": "提交时间", "memo": "备注", "id": "特征id", "tplid": "模板id", "applyorders_execStatus": "执行状态", "applyorders_receiveOrg": "收货组织id", "applyorders_receiveOrg_name": "收货组织名称", "applyorders_purchaseOrg": "采购组织id", "applyorders_purchaseOrg_name": "采购组织名称", "applyorders_purDept": "采购部门id", "applyorders_purDept_name": "采购部门名称", "applyorders_purPerson": "采购业务员id", "applyorders_purPerson_name": "采购业务员名称", "applyOrder_orderMoneyRatio": "订单金额超量比例", "apporders_id": "订单行id", "applyorders_product": "物料id", "product_defaultAlbumId": "物料首图片", "applyorders_product_cCode": "物料编码", "applyorders_product_cName": "物料名称", "applyorders_productsku": "物料SKUid", "applyorders_productsku_cCode": "物料sku编码", "applyorders_productsku_cName": "物料sku名称", "applyorders_currency": "币种id", "applyorders_currency_name": "币种名称", "applyorders_currency_priceDigit": "币种单价精度", "applyorders_currency_moneyDigit": "币种金额精度", "applyorders_rowno": "行号", "unit_Precision": "主计量精度", "applyorders_unit": "单位id", "applyorders_unit_name": "主计量名称", "applyorders_product_oUnitId": "零售单位id", "applyorders_product_productOfflineRetail_purchaseUnit": "采购单位id", "applyorders_invExchRate": "换算率", "applyorders_productOfflineRetail_purchaseRate": "采购单位换算率", "priceUOM": "计价单位id", "priceUOM_Name": "计价单位", "invPriceExchRate": "计价换算率", "unitExchangeTypePrice": "计价单位转换率的换算方式", "priceUOM_Precision": "计价单位精度", "taxRate": "税率", "applyorders_requirementDate": "需求日期", "applyorders_adviseOrderDate": "建议订货日期", "applyorders_adviseSupplier": "建议供应商id", "applyorders_adviseSupplier_name": "建议供应商名称", "applyorders_vendor": "建议供应商id", "applyorders_vendor_name": "建议供应商名称", "applyorders_memo": "备注", "applyorders_productsku_modelDescription": "sku规格型号", "applyorders_product_model": "型号", "applyorders_product_modelDescription": "规格说明", "applyorders_propertiesValue": "规格", "project": "项目id", "project_code": "项目编码", "project_name": "项目名称", "applyorders_trackNo": "跟踪号", "applyOrderDefineCharacter": "特征组", "pu.applyorder.ApplyOrder": "表头自定义项特征组", "U9001": "U9请购单号", "applyOrdersDefineCharacter": "特征组", "pu.applyorder.ApplyOrders": "子表自由项特征组", "XS11": "需求分类号", "XS15": "顾客订单号", "applyOrdersCharacteristics": "特征组", "XXX0111": "需求分类项"}, "subcontract_requisition": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "pageIndex": "当前页", "pageSize": "页大小", "orgId": "需求组织ID", "code": "委外申请单号", "vouchdate": "单据日期", "transTypeId": "交易ID", "status": "申请单状态", "productId": "物料ID", "requisitionDate": "需求日期", "departmentId": "需求部门ID", "operatorId": "需求人ID", "simple": "扩展参数", "simpleVOs": "扩展查询条件", "field": "属性名", "op": "逻辑连接符", "value1": "查询条件值1", "value2": "查询条件值2", "logicOp": "逻辑连接符", "conditions": "下级查询条件", "Url": "访问令牌", "message": "接口返回信息", "data": "接口返回数据", "recordCount": "记录总数", "recordList": "返回数据对象", "id": "特征id", "orgName": "需求组织", "transTypeName": "交易", "transTypeExtendAttrsJson": "交易扩展属性", "departmentName": "需求部门", "operatorName": "需求人名称", "sourceType": "来源类别", "defineCharacteristics": "特征组", "po.subcontractrequisition.SubcontractRequisition": "自定义项特征组", "isWfControlled": "审批流控制", "verifystate": "审批状态", "pubts": "时间戳", "subcontractRequisitionProduct": "产品行数据", "outsourceOrgId": "委外组织ID", "rcvOrgId": "收货组织ID", "productCode": "物料编码", "productName": "物料名称", "demandQuantityDU": "需求件数", "demandUnitId": "需求单位ID", "demandUnitName": "需求单位", "demandUnitTruncationType": "需求单位舍位", "demandUnitPrecision": "需求单位精度", "demandQuantityMU": "需求数量", "mainUnitId": "需求主单位ID", "mainUnitName": "需求主计量单位", "mainUnitTruncationType": "需求主计量单位舍位", "mainUnitPrecision": "需求主计量单位精度", "rcvOrgName": "收货组织", "outsourceOrgName": "委外组织", "defineCharacteristicsPro": "特征组", "po.subcontractrequisition.SubcontractRequisitionProduct": "自由项特征组", "WW0555": "委外申请单参考需求日期", "XS11": "需求分类号", "XS15": "顾客订单号", "freeCharacteristics": "特征组", "XXX0111": "需求分类项", "sumRecordList": "合计集合", "pageCount": "总页数", "beginPageIndex": "开始页码", "endPageIndex": "结束页码"}, "product_receipt": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "pageIndex": "当前页", "code": "单据编号", "pageSize": "分页大小", "warehouse_name": "仓库名称", "bustype_name": "交易名称", "stockMgr_name": "库管员名称", "operator": "经办人id", "department": "部门id", "org": "库存组织id", "product_cName": "物料名称", "open_hopeReceiveDate_begin": "查询开始时间", "open_hopeReceiveDate_end": "查询结束时间", "simpleVOs": "扩展查询条件", "field": "属性名", "op": "条件比较符", "value1": "值1", "value2": "值2", "Url": "访问令牌", "message": "调用失败时的错误信息", "data": "调用成功时的返回数据", "salesOrgId_name": "销售组织名称", "sumRecordList": "sum合计信息", "pageCount": "总页数", "beginPageIndex": "开始页码", "endPageIndex": "结束页码", "recordCount": "总记录数", "pubts": "时间戳", "synSourceOrg": "协同来源组织id", "recordList": "返回结果对象", "factoryFiOrg": "完工组织id", "storeProRecords_product": "物料id", "currency": "币种id", "storeProRecords_unit": "主计量", "storeProRecords_productsku": "物料sku", "storeProRecords_stockUnitId": "库存单位id", "vouchdate": "单据日期", "department_name": "部门名称", "accountOrg": "会计主体id", "org_name": "库存组织名称", "totalPieces": "整单件数", "stockMgr": "库管员IDid", "store": "门店id", "store_name": "门店名称", "warehouse": "仓库id", "bustype": "业务id", "accountOrg_name": "会计主体名称", "status": "单据状态", "totalQuantity": "整单数量", "totalMaterial": "已材料出", "creator": "创建人", "createTime": "创建时间", "modifier": "最后修改人", "modifyTime": "最后修改时间", "auditor": "提交人", "auditTime": "提交时间", "memo": "备注", "auditorId": "审批人", "creatorId": "创建人", "id": "特征id", "modifierId": "修改人", "tplid": "模板id", "storeProRecords_id": "子表id", "product_cCode": "物料编码", "storeProRecordsCharacteristics": "特征组", "st.storeprorecord.StoreProRecords": "子表自定义项特征组", "XS15": "顾客订单号", "XXX0111": "需求分类项", "productsku_cCode": "sku编码", "productsku_cName": "sku名称", "product_modelDescription": "规格型号", "qty": "数量", "product_unitName": "计量单位", "subQty": "件数", "stockUnit_name": "库存单位", "project_name": "项目名称", "natUnitPrice": "单价", "natMoney": "金额", "natCurrency_priceDigit": "币种单价精度", "natCurrency_moneyDigit": "币种金额精度", "unit_Precision": "主计量精度", "stockUnitId_Precision": "库存单位精度", "storeProRecordsDefineCharacter": "特征组", "XS11": "需求分类号", "storeProRecordDefineCharacter": "特征组", "st.storeprorecord.StoreProRecord": "主表自定义项特征组", "out_sys_id": "外部来源线索", "out_sys_code": "外部来源编码", "out_sys_version": "外部来源版本", "out_sys_type": "外部来源", "out_sys_rowno": "外部来源行号", "out_sys_lineid": "外部来源行"}, "purchase_receipt": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "pageIndex": "当前页数", "code": "单据编号", "pageSize": "当前页数", "bustype_name": "交易名称", "warehouse_name": "仓库名称", "vendor_name": "供应商名称", "org_id": "库存组织id", "org_name": "库存组织名称", "org_code": "库存组织编码", "purchaseOrg_name": "采购组织名称", "inInvoiceOrg_name": "收票组织名称", "stockMgr_name": "库管员名称", "operator_name": "经办人名称", "department_name": "部门名称", "project_name": "项目名称", "product.productClass.name": "物料分类", "pocode": "源头单据编码", "product_cName": "物料名称", "open_vouchdate_begin": "开始时间", "open_vouchdate_end": "结束时间", "isSum": "查询表头", "simpleVOs": "扩展查询条件", "field": "属性名", "op": "比较符", "value1": "值1", "value2": "值2", "Url": "访问令牌", "message": "调用失败时的错误信息", "data": "调用成功时的返回数据", "pageCount": "页面数", "beginPageIndex": "开始页码", "endPageIndex": "结束页码", "recordCount": "总数", "pubts": "时间戳", "recordList": "返回结果对象", "vouchdate": "单据日期", "vendor_code": "供应商编码", "warehouse_code": "仓库编码", "status": "单据状态", "department_code": "部门编码", "totalQuantity": "整单数量", "totalPieces": "整单件数", "inInvoiceOrg": "收票组织id", "accountOrg": "会计主体", "isBeginning": "期初", "bustype": "业务id", "vendor": "供应商id", "contact": "联系人", "warehouse": "仓库id", "operator": "经办人id", "purchaseOrg": "采购组织IDid", "org": "库存组织id", "department": "部门id", "stockMgr": "库管员IDid", "moneysum": "金额", "paymentsum": "付款金额", "unpaymentsum": "未付款金额", "store": "门店id", "store_name": "门店名称", "custom": "客户id", "payor": "付款人id", "payor_name": "付款人名称", "paytime": "付款时间", "paymentstatus": "付款状态", "creator": "创建人", "createTime": "创建时间", "modifier": "最后修改人", "modifyTime": "最后修改时间", "auditor": "提交人", "auditTime": "提交时间", "memo": "备注", "id": "特征id", "srcBill": "来源单据id", "tplid": "模板id", "exchangestatus": "交换状态", "purInRecords_id": "订单行id", "product": "物料id", "product_cCode": "物料编码", "productsku": "物料SKUid", "tradeRoute_name": "贸易路径", "productsku_cCode": "物料sku编码", "productsku_cName": "物料sku名称", "productClass_code": "物料分类编码", "propertiesValue": "规格", "batchno": "批次号", "invaliddate": "有效期至", "producedate": "生产日期", "unit": "单位id", "qty": "数量", "unit_code": "计量单位编码", "unit_name": "计量单位名称", "subQty": "件数", "stockUnit_name": "库存单位", "project_code": "项目编码", "oriUnitPrice": "无税单价", "oriTaxUnitPrice": "含税单价", "oriMoney": "无税金额", "oriSum": "含税金额", "oriTax": "税额", "taxRate": "税率", "billqty": "累计开票数量", "billSubQty": "累计开票件数", "sqty": "累计结算数量", "smoney": "累计结算金额", "sfee": "累计结算费用", "totalBillOriSum": "累计开票含税金额", "priceUOM": "计价单位id", "priceUOM_Code": "计价单位编码", "priceUOM_Name": "计价单位名称", "natCurrency_priceDigit": "本币单价精度", "natCurrency_moneyDigit": "本币金额精度", "currency_priceDigit": "币种单价精度", "currency_moneyDigit": "币种金额精度", "unit_Precision": "主计量精度", "priceUOM_Precision": "计价单位精度", "stockUnitId_Precision": "库存单位精度", "isGiftProduct": "赠品", "bmake_st_purinvoice_red": "流程红票", "bmake_st_purinvoice": "流程蓝票", "bizFlow": "流程ID", "bizFlow_version": "版本信息", "isFlowCoreBill": "流程核心单据", "purInRecordDefineCharacter": "特征组", "st.purinrecord.PurInRecord": "表头自定义项特征组", "CG04": "送货单号", "U9002": "U9采购订单号", "purInRecordsDefineCharacter": "特征组", "st.purinrecord.PurInRecords": "自由项特征组", "CG00025": "未收数量2", "CG01": "供应商备注", "CG02": "检验数", "CG03": "合格数", "CG05": "送货单号", "WW": "委外交货日期", "XS11": "需求分类号", "XS15": "顾客订单号", "purInRecordsCharacteristics": "特征组", "XXX0111": "需求分类项", "out_sys_id": "外部来源线索", "out_sys_code": "外部来源编码", "out_sys_version": "外部来源版本", "out_sys_type": "外部来源", "out_sys_rowno": "外部来源行号", "out_sys_lineid": "外部来源行", "tradeRouteID": "贸易路径id", "isEndTrade": "末级", "tradeRouteLineno": "站点", "collaborationPolineno": "协同来源单据行号", "coSourceType": "协同源头单据", "coUpcode": "协同源头单据号", "coSourceLineNo": "协同源头单据行号", "coSourceid": "协同来源单据id", "coSourceautoid": "协同来源单据子表id", "collaborationPodetailid": "协同来源单据行", "collaborationPocode": "协同来源单据号", "collaborationPoid": "协同来源单据主表id", "collaborationSource": "协同来源单据", "totalOutStockQuantity": "累计销售出库数量", "sumRecordList": "合计对象", "currency": "币种id", "purInRecords_unit": "计量单位", "natCurrency": "本币币种id", "purInRecords_stockUnitId": "库存单位", "purInRecords_priceUOM": "计价单位"}, "subcontract_receipt": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "isSum": "按照表头查询", "code": "单据编号", "pageSize": "每页显示数据数", "pageIndex": "当前页", "open_vouchdate_begin": "单据开始日期", "open_vouchdate_end": "单据结束日期", "status": "单据状态", "simpleVOs": "扩展查询条件", "field": "属性名", "op": "比较符", "value1": "值1", "value2": "值2", "Url": "访问令牌", "message": "调用失败时的错误信息", "data": "调用成功时的返回数据", "pageCount": "总页数", "beginPageIndex": "开始页", "endPageIndex": "结束页", "recordCount": "总记录数", "recordList": "返回结果对象", "osmInRecords_productionType": "产出", "vendor_code": "委外供应商编码", "oriTax": "税额", "pocode": "委外订单编码", "product_cCode": "物料编码", "invoiceVendor": "开票供应商ID", "sfee": "累计结算费用", "priceUOM_Precision": "计价单位精度", "memo": "备注", "stockStatusDoc_name": "库存状态", "priceUOM_Code": "计价单位编码", "totalQuantity": "整单数量", "natCurrency": "本币ID", "taxitems_name": "税目名称", "stockUnitId_Precision": "库存单位精度", "costMoney": "成本金额", "id": "特征id", "tplid": "模板id", "isWfControlled": "审批流控制", "natSum": "本币含税金额", "warehouse": "仓库id", "isAutomaticVerify": "自动核销", "warehouse_name": "仓库", "auditTime": "审核时间", "natCurrency_priceDigit": "本币单价精度", "exchRateType": "汇率ID", "billqty": "累计开票数量", "invExchRate": "换算率", "isGiftProduct": "赠品", "returncount": "退回次数", "verifystate": "审批状态", "invoicingDocEntryAndMgmt": "立账开票依据", "isVerification": "核销标识", "currency_moneyDigit": "币种金额精度", "warehouse_code": "仓库编码", "stockStatusDoc": "库存状态id", "productsku_cName": "物料sku名称", "osmOrg_name": "委外组织", "vouchdate": "单据日期", "receiptDocEntryAndMgmt": "入库立账方式", "natCurrency_name": "本币名称", "invoiceVendor_name": "开票供应商", "invPriceExchRate": "计价换算率", "vendor": "委外供应商id", "sqty": "累计结算数量", "currency": "币种ID", "pubts": "时间戳", "smoney": "累计结算金额", "org_name": "收货组织", "isFlowCoreBill": "流程核心单据", "auditDate": "审核日期", "creator": "创建人", "product": "物料id", "oriSum": "含税金额", "inInvoiceOrg_name": "收票组织", "exchRateType_name": "汇率", "department_name": "委外部门", "auditor": "审核人", "accountOrg": "会计主体", "priceQty": "计价数量", "createTime": "创建时间", "natMoney": "本币无税金额", "taxitems_code": "税目编码", "department_code": "委外部门编码", "osmInRecords_osmType": "委外", "currency_priceDigit": "币种单价精度", "stockUnit_name": "库存单位", "isBeginning": "期初", "bustype_name": "交易", "modifier": "修改人", "natTax": "本币税额", "source": "上游单据", "srcBill": "来源单据id", "subQty": "件数", "modifyTime": "修改时间", "inInvoiceOrg": "收票组织", "product_cName": "物料名称", "vendor_name": "委外供应商", "oriUnitPrice": "无税单价", "barCode": "单据条码", "unit_name": "计量单位", "taxRate": "税率", "unit": "单位id", "productsku": "物料SKUid", "productsku_cCode": "物料sku编码", "natCurrency_moneyDigit": "本币金额精度", "accountOrg_name": "会计主体", "qty": "数量", "unit_Precision": "主计量精度", "oriTaxUnitPrice": "含税单价", "oriMoney": "无税金额", "contactsPieces": "应收件数", "contactsQuantity": "应收数量", "natUnitPrice": "本币无税单价", "exchRate": "汇率", "osmInRecords_id": "订单行id", "priceUOM": "计价单位id", "department": "委外部门ID", "currency_name": "币种名称", "org": "收货组织", "custom": "客户id", "osmOrg": "委外组织ID", "bustype": "交易id", "costUnitPrice": "成本单价", "upcode": "上游单据号", "priceUOM_Name": "计价单位名称", "taxitems": "税目id", "natTaxUnitPrice": "本币含税单价", "unDeductTaxRate": "不可抵扣税率", "unDeductTax": "不可抵扣税额", "oriUnDeductTax": "原币不可抵扣税额", "osmInRecordsCharacteristics": "特征组", "st.osminrecord.OsmInRecords": "子表自定义项特征组", "XS15": "顾客订单号", "XXX0111": "需求分类项", "osmInRecordsDefineCharacter": "特征组", "CG02": "检验数", "CG03": "合格数", "CG05": "送货单号", "WW": "委外交货日期", "XS11": "需求分类号", "osmInRecordDefineCharacter": "特征组", "st.osminrecord.OsmInRecord": "主表自定义项特征组", "CG04": "送货单号", "osmInRecords": "委外入库单子表", "opSn": "工序顺序号", "operationId": "工序", "endOp": "末序", "sourcePoOrderCode": "生产订单号", "sourcePoOrderProductRowno": "生产订单行号", "sourcePoOrderId": "生产订单ID", "sourcePoOrderProductId": "生产订单行ID", "costAccountingMethod": "委外成本核算方式"}, "materialout": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "pageIndex": "页码", "pageSize": "每页条数", "open_vouchdate_begin": "单据开始时间", "open_vouchdate_end": "单据结束时间", "bustype_name": "交易名称", "stockOrg": "库存组织id", "stockOrg_code": "库存组织编码", "stockOrg_name": "库存组织名称", "product_cName": "物料名称", "product.productClass.name": "物料分类ID", "simpleVOs": "扩展查询条件", "field": "属性名", "op": "条件比较符", "value1": "值1", "value2": "值2", "Url": "访问令牌", "code": "单据编号", "message": "调用失败时的错误信息", "data": "调用成功时的返回数据", "salesOrgId": "销售组织id", "saleDepartmentId": "销售部门id", "transactionTypeId": "交易id", "settlementOrgId": "开票组织id", "sumRecordList": "sum合计信息", "totalPieces": "整单件数", "totalQuantity": "整单数量", "subQty": "件数", "qty": "数量", "pageCount": "总页数", "beginPageIndex": "开始页码", "endPageIndex": "结束页码", "recordCount": "总条数", "pubts": "时间戳", "recordList": "返回结果对象", "currency": "币种id", "materOuts_product": "物料id", "materOuts_unit": "主计量", "materOuts_productsku": "物料sku", "vouchdate": "单据日期", "org": "库存组织IDid", "org_code": "库存组织编码", "org_name": "库存组织名称", "store": "门店id", "bustype": "业务id", "store_name": "门店名称", "department_name": "部门名称", "department": "部门id", "warehouse": "仓库id", "warehouse_name": "仓库名称", "stockMgr_name": "库管员名称", "stockMgr": "库管员IDid", "memo": "备注", "bustype_extend_attrs_json": "出库", "accountOrg_name": "会计主体名称", "accountOrg": "会计主体id", "exchangestatus": "交换状态", "status": "单据状态", "srcbill": "来源单据id", "creator": "创建人", "srcbillno": "来源单据", "srcBillType": "来源上级单据", "createTime": "创建时间", "modifier": "修改人", "modifyTime": "修改时间", "auditor": "提交人", "auditTime": "提交时间", "id": "特征id", "tplid": "模板id", "materOuts": "以下名需要拼接materOuts", "product_cCode": "物料编码", "productsku_cCode": "sku编码", "productsku_cName": "sku名称", "productClass_code": "物料分类编码", "propertiesValue": "规格", "batchno": "批次号", "invaliddate": "有效期至", "product_unitName": "计量单位", "stockUnitId": "库存单位id", "stockUnit_name": "库存单位", "project_code": "项目编码", "project_name": "项目名称", "natUnitPrice": "单价", "natMoney": "金额", "natCurrency_priceDigit": "币种单价精度", "natCurrency_moneyDigit": "币种金额精度", "unit_code": "主计量编码", "unit_Precision": "主计量精度", "stockUnitId_Precision": "库存单位精度", "materialOutsCharacteristics": "特征组", "st.materialout.MaterialOuts": "子表自定义项特征组", "XS15": "顾客订单号", "XXX0111": "需求分类项", "materialOutsDefineCharacter": "特征组", "XS11": "需求分类号", "materialOutDefineCharacter": "特征组", "st.materialout.MaterialOut": "主表自定义项特征组", "TL001": "退料理由", "isWip": "在制品", "costAccountingMethod": "委外成本核算方式", "bodyParallel": "材料出库子表平行表", "wipOpSn": "在制品工序顺序号", "wipOperationId": "在制品工序ID", "odyParallel_wipOperationCode": "在制品工序编码", "bodyParallel_wipOperationName": "在制品工序名称", "out_sys_id": "外部来源线索", "out_sys_code": "外部来源编码", "out_sys_version": "外部来源版本", "out_sys_type": "外部来源", "out_sys_rowno": "外部来源行号", "out_sys_lineid": "外部来源行"}, "sales_out": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "isdefault": "该参数可忽略不管", "pageIndex": "当前页", "code": "单据编码", "pageSize": "分页大小", "vouchdate": "单据日期", "stockOrg": "库存组织id", "salesOrg": "销售组织主键", "invoiceOrg": "开票客户主键", "invoiceCust": "开票客户主键", "upcode": "来源单据号", "department": "部门主键", "operator": "业务员主键", "warehouse": "仓库主键", "stockMgr": "库管员id", "cust": "客户主键", "product_cName": "物料名称", "bustype.name": "交易名称", "product_cName_ManageClass": "物料分类id", "isSum": "查询表头", "simpleVOs": "扩展查询条件", "op": "比较符", "value2": "值2如", "value1": "值1如", "field": "属性名", "Url": "访问令牌", "message": "调用失败时的错误信息", "data": "调用成功时的返回数据", "recordCount": "总记录数", "recordList": "返回数据列表", "cReceiveAddress": "收货地址", "oriTax": "税率", "details_stockUnitId": "库存单位主键", "product_cCode": "物料编码", "details_taxId": "税目主键", "natCurrency": "本币主键", "sourcesys": "来源单据领域", "tradeRouteID": "贸易路径id", "stockUnitId_Precision": "库存单位精度", "id": "特征id", "status_mobile_row": "单据状态", "invoiceTitle": "发票抬头", "details_priceUOM": "计价单位主键", "natSum": "合计本币金额", "isEndTrade": "末级", "srcBillType": "来源单据", "diliverStatus": "发货状态", "warehouse_name": "仓库名称", "natCurrency_priceDigit": "本币精度", "exchRateType": "汇率枚举值", "tradeRouteLineno": "站点", "invExchRate": "单位转换率", "product_defaultAlbumId": "物料图片", "status": "单据状态", "currency_moneyDigit": "币种精度", "invoiceCust_name": "开票客户名称", "details_productsku": "物料KSU主键", "invoiceOrg_name": "开票组织名称", "tradeRoute_name": "贸易路径", "productsku_cName": "物料SKU名称", "invPriceExchRate": "计价单位转换率", "currency": "原币主键", "pubts": "时间戳", "org_name": "发货组织名称", "cReceiveMobile": "收货电话", "createDate": "创建日期", "creator": "创建人", "oriSum": "合计金额", "exchRateType_name": "汇率名称", "accountOrg": "会计主体主键", "stsalesOutExchangeInfo_d_key": "逻辑冗余", "cReceiver": "收货人", "details_id": "子表主键", "priceQty": "合计计价数量", "createTime": "创建时间", "taxUnitPriceTag": "价格含税标志", "details_product": "物料主键", "taxNum": "纳税识别号", "department_name": "部门名称", "operator_name": "业务员名称", "invoiceAddress": "营业地址", "bankAccount": "银行账号", "subBankName": "开户支行", "bankName": "开户银行", "invoiceTelephone": "营业电话", "invoiceUpcType": "发票", "natMoney": "合计本币无税金额", "currency_priceDigit": "币种精度", "stockUnit_name": "库存单位名称", "collaborationPolineno": "协同来源单据行号", "bustype_name": "交易名称", "modifier": "修改人", "firstupcode": "源头单据编码", "source": "来源单据", "natTax": "本币税额", "subQty": "合计副计量数量", "taxItems": "税率显示值", "modifyTime": "修改时间", "invoiceTitleType": "发票抬头", "receiveContacterPhone": "收货人联系电话", "modifyInvoiceType": "发票可改标志", "natCurrencyName": "本币名称", "salesOrg_name": "销售组织名称", "modifyDate": "修改日期", "unitName": "主计量名称", "contactName": "联系人名称", "srcBillNO": "来源单据号", "oriUnitPrice": "原币无税单价", "taxCode": "税目编码", "barCode": "单据码", "unit_name": "主计量名称冗余", "taxRate": "税率", "unit": "库存单位", "productsku_cCode": "物料SKU编码", "natCurrency_moneyDigit": "本币精度", "accountOrg_name": "会计主体名称", "taxId": "税目编码", "qty": "合计数量", "unit_Precision": "主计量精度", "oriTaxUnitPrice": "原币含税单价", "oriMoney": "合计原币无税金额", "contactsPieces": "合计应发件量", "contactsQuantity": "合计应发数量", "natUnitPrice": "本币无税单价", "receiveAccountingBasis": "立账开票依据", "logistics": "物料单号", "exchRate": "汇率", "currencyName": "币种名称", "cust_name": "客户名称", "org": "库存组织主键", "priceUOM_name": "计价单位名称", "bustype": "交易主键", "receiveId": "收货地址主键", "saleStyle": "商品售卖", "iLogisticId": "物流公司", "status_mobile": "单据状态", "natTaxUnitPrice": "本币含税单价", "salesOutDefineCharacter": "特征组", "st.salesout.SalesOut": "表头自定义项特征组", "salesOutsDefineCharacter": "特征组", "st.salesout.SalesOuts": "自由特征组", "SF04": "发票号", "SF05": "发货单日期", "SF06": "发货单号", "XS15": "顾客订单号", "salesOutsCharacteristics": "特征组", "XXX0111": "需求分类项", "out_sys_id": "外部来源线索", "out_sys_code": "外部来源编码", "out_sys_version": "外部来源版本", "out_sys_type": "外部来源", "out_sys_rowno": "外部来源行号", "out_sys_lineid": "外部来源行", "collaborationPocode": "协同来源单据号", "collaborationPoid": "协同来源单据id", "collaborationPodetailid": "协同来源单据子表id", "collaborationSource": "协同来源", "salesOutsExtend": "协同源头单据", "sumRecordList": "合计信息", "totalPieces": "合计件数", "invoiceOriSum": "合计开票金额", "saleReturnQty": "合计退货数量", "totalQuantity": "合计数量", "invoiceQty": "合计开票数量", "pageCount": "总页数", "beginPageIndex": "开始页页号", "endPageIndex": "最终页页号"}, "inventory": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "org": "组织id", "warehouse.code": "仓库编码", "warehouse": "仓库id", "productsku": "物料skuid", "batchno": "批次号", "productn.code": "物料编码", "product": "物料id", "product.oUnitId": "主计量", "stockStatusDoc": "库存状态", "store": "门店id", "bStockStatusDocNotNull": "库存状态非空", "billnum": "可用量规则分配中的单据", "Url": "访问令牌", "code": "状态码", "message": "提示信息", "data": "返回数据参数", "product_name": "物料name", "product_code": "物料编码", "productsku_name": "物料sku名字", "productsku_code": "物料sku编码", "manageClass": "物料分类id", "manageClass_code": "物料分类编码", "manageClass_name": "物料分类名称", "unit": "主计量", "currentqty": "现存量", "innoticeqty": "收货预计入库数量", "planavailableqty": "计划可用量", "availableqty": "可用量", "preretailqty": "订单预计出库数量"}, "inventory_report": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "pageSize": "每页行数", "pageIndex": "当前页数", "warehouse_name": "仓库名称", "product.productClass.name": "物料分类主键", "product.cName": "商品主键", "productsku.skuName": "sku主键", "batchno": "批号", "acolytesUnit": "辅单位", "org": "组织", "store": "门店", "open_currentqty_begin": "现存量下限", "open_currentqty_end": "现存量上限", "product_cName": "物料名称", "product_cCode": "物料编码", "warehouse_names": "仓库名称", "warehouse_code": "仓库编码", "productsku_skuName": "物料SKU名称", "product_productClass_names": "物料分类名称", "stockStatusDoc_status_name": "库存状态名称", "product_brand": "物料品牌", "product_manufacturer": "生产厂商", "product_cModel": "型号", "open_validityDistance_begin": "效期天数下限", "open_validityDistance_end": "效期天数上限", "batchno_define1": "批次属性1", "batchno_define2": "批次自定义项2", "batchno_define3": "批次自定义项3", "batchno_define4": "批次自定义项4", "batchno_define5": "批次自定义项5", "batchno_define6": "批次自定义项6", "batchno_define7": "批次自定义项7", "batchno_define8": "批次自定义项8", "batchno_define9": "批次自定义项9", "batchno_define10": "批次自定义项10", "batchno_define11": "批次自定义项11", "batchno_define12": "批次自定义项12", "batchno_define13": "批次自定义项13", "batchno_define14": "批次自定义项14", "batchno_define15": "批次自定义项15", "batchno_define16": "批次自定义项16", "batchno_define17": "批次自定义项17", "batchno_define18": "批次自定义项18", "batchno_define19": "批次自定义项19", "batchno_define20": "批次自定义项20", "batchno_define21": "批次自定义项21", "batchno_define22": "批次自定义项22", "batchno_define23": "批次自定义项23", "batchno_define24": "批次自定义项24", "batchno_define25": "批次自定义项25", "batchno_define26": "批次自定义项26", "batchno_define27": "批次自定义项27", "batchno_define28": "批次自定义项28", "batchno_define29": "批次自定义项29", "batchno_define30": "批次自定义项30", "Url": "访问令牌", "code": "返回码", "message": "调用失败时的错误信息", "data": "调用成功时的返回数据", "pageCount": "总页数", "beginPageIndex": "开始页码", "endPageIndex": "结束页码", "recordCount": "总记录数", "pubts": "时间戳", "recordList": "数据", "id": "主键", "org_name": "组织名称", "areaClass": "地区", "areaClass_name": "地区名称", "store_codebianma": "门店编码", "store_name": "门店名称名称", "store_type": "门店", "warehouse": "仓库", "productClass": "物料分类", "productClass_code": "物料分类编码", "productClass_name": "物料分类名称名称", "product": "物料", "product_defaultAlbumId": "物料首图片", "productsku": "物料SKU", "product_modelDescription": "规格型号", "productsku_cCode": "物料SKU编码", "free1": "物料规格1", "free2": "物料规格2", "free3": "物料规格3", "free4": "物料规格4", "free5": "物料规格5", "free6": "物料规格6", "free7": "物料规格7", "free8": "物料规格8", "free9": "物料规格9", "free10": "物料规格10", "product_productProps": "物料自定义项", "define1": "SKU自定义项1", "define30": "物料自定义项30", "product_productSkuProps": "SKU自定义项", "define60": "SKU自定义项1", "producedate": "生产日期", "invaliddate": "有效期至", "unitName": "主计量", "currentqty": "现存量", "stockmoney": "库存金额", "availableqty": "可用量", "costprice": "成本价", "innoticeqty": "收货预计入库量", "costmoney": "成本金额", "outnoticeqty": "发货预计出库量", "preretailqty": "订单预计出库量", "acolytesUnit_name": "辅计量单位名称", "currentSubQty": "现存件数", "acolytesUnit_precision": "辅计量单位精度", "unit_precision": "主计量单位精度", "invExchRate": "换算率", "availableSubQty": "可用件数", "innoticeSubQty": "入库通知件数", "outnoticeSubQty": "出库通知件数", "preretailSubQty": "预订零售件数"}, "requirements_planning": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "data": "数据", "orgId": "计划组织id", "orgCode": "计划组织编码", "planParamId": "计划名称id", "planParamCode": "计划名称编码", "supplyOrgId": "供应组织", "supplyOrgCode": "供应组织编码", "departmentId": "部门id", "departmentCode": "部门编码", "warehouseId": "仓库id", "warehouseCode": "仓库编码", "productIds": "物料id", "productCodes": "物料编码", "planProperty": "计划属性", "Url": "访问令牌", "code": "计划订单号", "message": "操作通知信息", "recordList": "数据信息", "id": "特征id", "transTypeId": "交易id", "transTypeCode": "交易编码", "planParamName": "计划名称", "createType": "创建", "productCode": "物料编码", "productId": "物料id", "bomId": "BOM唯一标识", "bomCode": "BOM编码", "uom": "单位", "uomName": "单位名称", "uomCode": "单位编码", "assistUnit": "主计量单位", "assistUnitCode": "主计量单位编码", "assistUnitName": "主计量单位名称", "startDate": "开工日期", "finishDate": "完工日期", "status": "状态", "demandOrgId": "需求组织", "demandOrgCode": "需求组织编码", "invOrgId": "入库组织", "invOrgCode": "入库组织编码", "source": "来源单据", "upcode": "来源单据号", "srcSourceProductId": "来源物料id", "srcSourceProductCode": "来源物料编码", "firstsource": "源头单据", "firstupcode": "源头单据号", "firstsourceautoid": "源头单据子表id", "sourceMaterialId": "源头物料", "sourceMaterialCode": "源头物料编码", "departmentName": "部门名称", "warehouseName": "仓库名称", "isClosed": "关闭标识", "remark": "备注", "projectId": "项目id", "projectIdCode": "项目编码", "projectIdName": "项目名称", "wbs": "wbs任务id", "wbsCode": "wbs任务编码", "wbsName": "wbs任务名称", "activity": "活动id", "activityCode": "活动编码", "activityName": "活动名称", "planOrderItem": "计划订单备料", "itemProductId": "物料id", "itemProductCode": "物料编码", "itemProductName": "物料名称", "mainUnitId": "主计量id", "mainUnitCode": "主计量编码", "mainUnitName": "主计量名称", "stockUnitId": "BOM单位id", "stockUnitCode": "BOM单位编码", "stockUnitName": "BOM单位名称", "stockOrgId": "库存单位id", "stockOrgCode": "库存单位编码", "stockOrgName": "库存单位名称", "substituteFlag": "替代标识", "itemUserDefineCharacter": "特征组", "mr.planworkbench.PlanOrderItem": "自由项特征", "XS11": "需求分类号", "XS15": "顾客订单号", "itemFreeCharacteristics": "特征组", "XXX0111": "需求分类项", "reserveid": "跟踪线索id", "reserveTypeName": "需求跟踪方式", "reserveName": "跟踪线索"}, "material_master": {"API": "类别", "access_token": "调用方应用token", "Body": "参数", "pageIndex": "当前页索引", "pageSize": "分页大小", "productCode": "物料编码", "productName": "物料名称", "managerClassIdList": "物料分类Id集合", "managerClassCodeList": "物料分类编码集合", "productClassIdList": "商品分类Id集合", "productClassCodeList": "商品分类编码集合", "purchaseClassIdList": "采购分类Id集合", "purchaseClassCodeList": "采购分类编码集合", "productTemplate": "物料模板ID", "modelDescription": "规格说明", "model": "型号", "beganTime": "开始时间", "endTime": "结束时间", "productCharacterDef": "特征组", "pc.product.Product": "物料特征自定义项", "WL01": "参考料号1", "WL02": "参考料号2", "WL03": "U9料品编码", "WL04": "客户型号", "WL05": "销售产品名称", "WL06": "料品每箱台数", "WL07": "目录编号", "id": "特征id", "orgId": "使用组织id", "productTagList": "标签id集合", "mktStatus": "商城上下架状态", "mktUOrderStatus": "订货上下架状态", "erpCodeList": "商家编码集合", "shortName": "物料简称", "mnemonicCode": "助记码", "Url": "访问令牌", "code": "调用完成返回码", "message": "调用完成返回信息", "data": "调用完成返回数据", "pageCount": "总页数", "beginPageIndex": "起始页索引", "endPageIndex": "截止页索引", "recordCount": "总条数", "recordList": "结果集"}}