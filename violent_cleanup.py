#!/usr/bin/env python3
"""
YS-API V3.0 暴力清理法 - 自动化污染代码清除器
集成特征识别、架构修复、依赖重建和防护机制
"""

import os
import re
import json
import shutil
import subprocess
from pathlib import Path
from typing import List, Dict, Set


class PollutionDetector:
    """污染代码检测器"""
    
    def __init__(self):
        # AI生成代码特征签名
        self.AI_SIGNATURES = [
            "Auto-generated by",
            "AI_generated", 
            "ShadowClass",
            "AI_Refactor",
            "prototype.__override__",
            "TODO: Add function description",
            "__init___",  # 错误的init方法
            "extends AI_Base",
            "class.*Shadow.*:",
            "# Generated by AI",
            "/* AI Generated */",
            "newException",  # 错误的异常类
            "require is not defined"
        ]
        
        # 垃圾文件模式
        self.GARBAGE_PATTERNS = [
            "*_shadow.py",
            "*_shadow.js", 
            "*_ai_generated.*",
            "*_auto.*",
            "temp_*.py",
            "backup_*.py",
            "*_duplicate.py",
            "*_copy.py"
        ]
        
        self.pollution_files = []
        self.garbage_files = []
        
    def scan_pollution(self, root_path: str) -> Dict:
        """扫描污染代码"""
        print("🔍 开始扫描污染代码...")
        
        pollution_report = {
            "signature_matches": [],
            "garbage_files": [],
            "corrupted_files": [],
            "shadow_implementations": []
        }
        
        for root, dirs, files in os.walk(root_path):
            # 跳过系统目录
            dirs[:] = [d for d in dirs if d not in {
                '.git', '__pycache__', 'node_modules', '.vscode', 'logs'
            }]
            
            for file in files:
                if file.endswith(('.py', '.js', '.ts', '.jsx', '.tsx')):
                    file_path = Path(root) / file
                    self._scan_file_pollution(file_path, pollution_report)
                    
                # 检查垃圾文件模式
                self._check_garbage_patterns(Path(root) / file, pollution_report)
        
        return pollution_report
    
    def _scan_file_pollution(self, file_path: Path, report: Dict):
        """扫描单个文件的污染"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            # 检查AI签名
            for signature in self.AI_SIGNATURES:
                if re.search(signature, content, re.IGNORECASE):
                    report["signature_matches"].append({
                        "file": str(file_path),
                        "signature": signature,
                        "line_count": content.count('\n') + 1
                    })
                    
            # 检查影子实现
            if '_shadow' in str(file_path).lower() or 'Shadow' in content:
                report["shadow_implementations"].append(str(file_path))
                
            # 检查架构腐蚀
            if re.search(r'prototype\.__override__|extends AI_Base', content):
                report["corrupted_files"].append(str(file_path))
                
        except Exception as e:
            print(f"⚠️ 扫描文件失败: {file_path} - {e}")
    
    def _check_garbage_patterns(self, file_path: Path, report: Dict):
        """检查垃圾文件模式"""
        for pattern in self.GARBAGE_PATTERNS:
            if file_path.match(pattern):
                report["garbage_files"].append(str(file_path))
                break


class ArchitectureRepairer:
    """架构修复器"""
    
    def __init__(self):
        self.repair_rules = [
            # Python修复规则
            (r'class\s+\w+\s*\(\s*AI_Base\s*\):', r'class \1:'),
            (r'def __init___\(', r'def __init__('),
            (r'except Exception as e:\s*pass', r'except Exception:\n        pass'),
            (r'# TODO: Add function description\.', ''),
            
            # JavaScript修复规则  
            (r'prototype\.__override__\s*=.*?;', ''),
            (r'class\s+(\w+)\s+extends\s+AI_Base', r'class \1'),
            (r'/\*\s*AI Generated\s*\*/', ''),
            (r'//\s*Generated by AI.*', ''),
        ]
    
    def repair_file(self, file_path: Path) -> bool:
        """修复单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 应用修复规则
            for pattern, replacement in self.repair_rules:
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            
            # 如果有修改，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
                
        except Exception as e:
            print(f"❌ 修复文件失败: {file_path} - {e}")
        
        return False
    
    def batch_repair(self, file_paths: List[str]) -> int:
        """批量修复文件"""
        repaired_count = 0
        
        for file_path in file_paths:
            if self.repair_file(Path(file_path)):
                repaired_count += 1
                print(f"✅ 修复: {file_path}")
        
        return repaired_count


class DependencyRebuilder:
    """依赖重建器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        
    def nuclear_cleanup(self):
        """核爆式清理依赖"""
        print("💥 执行核爆式依赖清理...")
        
        # 清理Python缓存
        self._cleanup_python_cache()
        
        # 清理临时文件
        self._cleanup_temp_files()
        
        # 重建Python环境（如果有requirements.txt）
        self._rebuild_python_deps()
    
    def _cleanup_python_cache(self):
        """清理Python缓存"""
        print("🧹 清理Python缓存...")
        
        # 删除__pycache__
        for pycache in self.project_root.rglob('__pycache__'):
            if pycache.is_dir():
                shutil.rmtree(pycache)
                print(f"删除: {pycache}")
        
        # 删除.pyc文件
        for pyc in self.project_root.rglob('*.pyc'):
            pyc.unlink()
    
    def _cleanup_temp_files(self):
        """清理临时文件"""
        print("🧹 清理临时文件...")
        
        temp_dirs = ['logs', 'temp_cleanup', 'recovery_backup']
        for temp_dir in temp_dirs:
            temp_path = self.project_root / temp_dir
            if temp_path.exists():
                shutil.rmtree(temp_path)
                print(f"删除临时目录: {temp_path}")
    
    def _rebuild_python_deps(self):
        """重建Python依赖"""
        requirements_file = self.project_root / 'requirements.txt'
        if requirements_file.exists():
            print("📦 重建Python依赖...")
            try:
                subprocess.run([
                    'pip', 'install', '-r', str(requirements_file), '--force-reinstall'
                ], check=True)
            except subprocess.CalledProcessError as e:
                print(f"⚠️ 依赖安装失败: {e}")


class EmergencyShutdown:
    """紧急熔断机制"""
    
    def __init__(self):
        self.critical_signatures = [
            "AI_generated",
            "ShadowClass", 
            "prototype.__override__",
            "process.exit(1)"  # 防止递归检测
        ]
    
    def create_pollution_detector_guard(self, target_dir: str):
        """创建污染检测守护程序"""
        guard_code = '''#!/usr/bin/env python3
"""
污染检测守护程序 - 实时监控和熔断
"""

import os
import sys
import time
import signal
from pathlib import Path

class PollutionGuard:
    def __init__(self):
        self.critical_signatures = [
            "AI_generated",
            "ShadowClass", 
            "prototype.__override__"
        ]
        self.shutdown_triggered = False
    
    def scan_and_shutdown(self):
        """扫描污染并触发熔断"""
        for root, dirs, files in os.walk('.'):
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                if file.endswith(('.py', '.js')):
                    file_path = Path(root) / file
                    if self._check_pollution(file_path):
                        self._emergency_shutdown(file_path)
    
    def _check_pollution(self, file_path: Path) -> bool:
        """检查文件污染"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            for signature in self.critical_signatures:
                if signature in content:
                    return True
        except:
            pass
        return False
    
    def _emergency_shutdown(self, polluted_file: Path):
        """紧急熔断"""
        if self.shutdown_triggered:
            return
            
        self.shutdown_triggered = True
        
        print(f"🚨 检测到污染代码: {polluted_file}")
        print("🔥 触发紧急熔断...")
        
        # 创建熔断标记文件
        with open('.pollution_detected', 'w') as f:
            f.write(f"Pollution detected in: {polluted_file}\\n")
            f.write(f"Timestamp: {time.time()}\\n")
        
        # 发送信号停止其他进程
        os.kill(os.getpid(), signal.SIGTERM)

if __name__ == "__main__":
    guard = PollutionGuard()
    guard.scan_and_shutdown()
'''
        
        guard_file = Path(target_dir) / 'pollution_guard.py'
        with open(guard_file, 'w', encoding='utf-8') as f:
            f.write(guard_code)
        
        print(f"✅ 污染检测守护程序已创建: {guard_file}")


def main():
    """主函数 - 暴力清理流程"""
    print("💣 YS-API V3.0 暴力清理法启动")
    print("=" * 60)
    
    project_root = "."
    
    # 步骤1: 污染检测
    detector = PollutionDetector()
    pollution_report = detector.scan_pollution(project_root)
    
    print(f"\n📊 污染扫描结果:")
    print(f"- 特征匹配: {len(pollution_report['signature_matches'])} 处")
    print(f"- 垃圾文件: {len(pollution_report['garbage_files'])} 个")
    print(f"- 腐蚀文件: {len(pollution_report['corrupted_files'])} 个")
    print(f"- 影子实现: {len(pollution_report['shadow_implementations'])} 个")
    
    # 步骤2: 暴力删除垃圾文件
    print(f"\n💥 开始暴力清理...")
    deleted_count = 0
    
    for garbage_file in pollution_report['garbage_files']:
        try:
            Path(garbage_file).unlink()
            print(f"🗑️ 删除垃圾文件: {garbage_file}")
            deleted_count += 1
        except Exception as e:
            print(f"⚠️ 删除失败: {garbage_file} - {e}")
    
    # 步骤3: 架构修复
    print(f"\n🔧 开始架构修复...")
    repairer = ArchitectureRepairer()
    
    # 收集需要修复的文件
    repair_files = set()
    for match in pollution_report['signature_matches']:
        repair_files.add(match['file'])
    repair_files.update(pollution_report['corrupted_files'])
    
    repaired_count = repairer.batch_repair(list(repair_files))
    
    # 步骤4: 依赖核爆重建
    print(f"\n💥 开始依赖核爆重建...")
    rebuilder = DependencyRebuilder(project_root)
    rebuilder.nuclear_cleanup()
    
    # 步骤5: 部署防护机制
    print(f"\n🛡️ 部署防护机制...")
    shutdown = EmergencyShutdown()
    shutdown.create_pollution_detector_guard(project_root)
    
    # 生成清理报告
    report = {
        "timestamp": __import__('datetime').datetime.now().isoformat(),
        "deleted_files": deleted_count,
        "repaired_files": repaired_count,
        "pollution_signatures": len(pollution_report['signature_matches']),
        "summary": f"删除{deleted_count}个垃圾文件，修复{repaired_count}个污染文件"
    }
    
    with open('cleanup_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n🎉 暴力清理完成!")
    print("=" * 60)
    print(f"📊 清理统计:")
    print(f"- 删除垃圾文件: {deleted_count} 个")
    print(f"- 修复污染文件: {repaired_count} 个") 
    print(f"- 清理污染特征: {len(pollution_report['signature_matches'])} 处")
    print(f"- 防护机制: 已部署")
    print(f"\n📄 详细报告: cleanup_report.json")
    print(f"🛡️ 守护程序: pollution_guard.py")


if __name__ == "__main__":
    main()
