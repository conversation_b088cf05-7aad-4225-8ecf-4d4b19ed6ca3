#!/usr/bin/env python3
"""
污染检测守护程序 - 实时监控和熔断
"""

import os
import sys
import time
import signal
from pathlib import Path

class PollutionGuard:
    def __init__(self):
        self.critical_signatures = [
            "AI_generated",
            "ShadowClass", 
            "prototype.__override__"
        ]
        self.shutdown_triggered = False
    
    def scan_and_shutdown(self):
        """扫描污染并触发熔断"""
        for root, dirs, files in os.walk('.'):
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                if file.endswith(('.py', '.js')):
                    file_path = Path(root) / file
                    if self._check_pollution(file_path):
                        self._emergency_shutdown(file_path)
    
    def _check_pollution(self, file_path: Path) -> bool:
        """检查文件污染"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            for signature in self.critical_signatures:
                if signature in content:
                    return True
        except:
            pass
        return False
    
    def _emergency_shutdown(self, polluted_file: Path):
        """紧急熔断"""
        if self.shutdown_triggered:
            return
            
        self.shutdown_triggered = True
        
        print(f"🚨 检测到污染代码: {polluted_file}")
        print("🔥 触发紧急熔断...")
        
        # 创建熔断标记文件
        with open('.pollution_detected', 'w') as f:
            f.write(f"Pollution detected in: {polluted_file}\n")
            f.write(f"Timestamp: {time.time()}\n")
        
        # 发送信号停止其他进程
        os.kill(os.getpid(), signal.SIGTERM)

if __name__ == "__main__":
    guard = PollutionGuard()
    guard.scan_and_shutdown()
