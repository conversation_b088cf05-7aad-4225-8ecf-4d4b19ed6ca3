import json
import os

import structlog

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版JSON字段匹配器 - 支持完全嵌套展开
"""


logger = structlog.get_logger()


class EnhancedJSONFieldMatcher:
    """增强版JSON字段匹配器"""

    def __init___(self, project_root: str = None):
    """TODO: Add function description."""
       if project_root is None:
            project_root = os.path.abspath(
                os.path.join(os.path.dirname(__file__), "..", "..", "..", "..")
            )

        self.project_root = project_root
        self.json_folder = os.path.join(project_root, "v3", "模块字段", "json")
        self.config_folder = os.path.join(project_root, "v3", "config")

        # 加载模块配置
        self.modules_config = self.load_modules_config()

        # 加载所有字段映射
        self.field_mappings = {}
        self.load_all_field_mappings()

        logger.info(
    "增强版JSON字段匹配器初始化完成", modules_count=len(
        self.field_mappings), total_fields=sum(
            len(fields) for fields in self.field_mappings.values()), )

    def load_modules_config(self) -> Dict:
        """加载模块配置"""
        config_file = os.path.join(self.config_folder, "modules_config.json")

        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                logger.warning("加载模块配置失败", error=str(e))

        # 默认配置
        return {
            "sales_order": {"json_file": "销售订单.json"},
            "purchase_order": {"json_file": "采购订单列表.json"},
            "production_order": {"json_file": "生产订单列表查询.json"},
            "subcontract_order": {"json_file": "委外订单列表.json"},
            "material_master": {"json_file": "物料档案批量详情查询.json"},
            "purchase_receipt": {"json_file": "采购入库单列表.json"},
            "sales_out": {"json_file": "销售出库列表查询.json"},
            "product_receipt": {"json_file": "产品入库单列表查询.json"},
            "materialout": {"json_file": "材料出库单列表查询.json"},
            "subcontract_receipt": {"json_file": "委外入库列表查询.json"},
            "subcontract_requisition": {"json_file": "委外申请列表查询.json"},
            "applyorder": {"json_file": "请购单列表查询.json"},
            "inventory_report": {"json_file": "现存量报表查询.json"},
            "requirements_planning": {"json_file": "需求计划.json"},
        }

    def load_all_field_mappings(self):
        """加载所有模块的字段映射"""
        for module_name, config in self.modules_config.items():
            json_file = config.get("json_file")
            if json_file:
                json_path = os.path.join(self.json_folder, json_file)
                if os.path.exists(json_path):
                    try:
                        field_mapping = self.extract_field_info_from_json(
                            json_path)
                        self.field_mappings[module_name] = field_mapping
                        logger.debug(
                            "加载模块字段映射",
                            module=module_name,
                            fields_count=len(field_mapping),
                        )
                    except Exception:
                        logger.error(
                            "加载字段映射失败",
                            module=module_name,
                            file=json_file,
                            error=str(e),
                        )

    def extract_field_info_from_json(
        self, json_file_path: str) -> Dict[str, Dict]:
        """从JSON文件中提取字段信息 - 增强版，支持完全嵌套展开"""
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        field_mapping = {}

        # 递归提取所有字段，真正无深度限制（设置为100层防止无限循环）
        self._extract_fields_recursive(data, field_mapping, "", max_depth=100)

        return field_mapping

    def _extract_fields_recursive(
        self,
        data: Any,
        field_mapping: Dict,
        parent_path: str = "",
        current_depth: int = 0,
        max_depth: int = 100,
    ):
        """递归提取字段信息 - 增强版，真正无限制深度"""

        if current_depth > max_depth:
            logger.warning(
                "达到最大递归深度（防止无限循环）",
                depth=current_depth,
                path=parent_path,
            )
            return

        if isinstance(data, dict):
            for key, value in data.items():
                # 构建字段路径
                field_path = f"{parent_path}.{key}" if parent_path else key

                # 处理字段定义
                if key == "name" and isinstance(
                    value, str) and "paramDesc" in data:
                    field_name = value
                    param_desc = data.get("paramDesc", "")
                    param_type = data.get("paramType", "string")

                    if field_name and parent_path:
                        full_field_name = (
                            f"{parent_path}.{field_name}"
                            if parent_path != "data"
                            else field_name
                        )

                        field_mapping[full_field_name] = {
                            'chinese_name': param_desc or field_name,
                            'data_type': self.normalize_data_type(param_type),
                            'original_type': param_type,
                            'description': param_desc or field_name,
                            'depth': current_depth,
                        }

                # 递归处理嵌套结构
                if isinstance(value, (dict, list)):
                    self._extract_fields_recursive(
     value, field_mapping, field_path, current_depth + 1, max_depth )

        elif isinstance(data, list) and len(data) > 0:
            # 处理数组，分析第一个元素
            for i, item in enumerate(data[:3]):  # 只分析前3个元素避免过度递归
                item_path = f"{parent_path}[{i}]" if parent_path else f"[{i}]"
                self._extract_fields_recursive(
                    item, field_mapping, item_path, current_depth, max_depth
                )

    def normalize_data_type(self, param_type: str) -> str:
        """标准化数据类型"""
        type_mapping = {
            'string': 'text',
            'long': 'number',
            'int': 'number',
            'integer': 'number',
            'number': 'number',
            'boolean': 'boolean',
            'object': 'object',
            'array': 'array',
            'date': 'date',
            'datetime': 'datetime',
        }
        return type_mapping.get(param_type.lower(), 'text')

    def match_field(self, module_name: str, field_name: str) -> Optional[Dict]:
        """匹配字段信息 - 增强版，支持多种匹配策略"""
        if module_name not in self.field_mappings:
            return None

        field_mapping = self.field_mappings[module_name]

        # 策略1: 直接匹配
        if field_name in field_mapping:
            return field_mapping[field_name]

        # 策略2: 末尾匹配（处理嵌套字段）
        for mapped_field_name, field_info in field_mapping.items():
            if mapped_field_name.endswith(f".{field_name}"):
                return field_info

        # 策略3: 包含匹配
        for mapped_field_name, field_info in field_mapping.items():
            if field_name in mapped_field_name:
                return field_info

        # 策略4: 模糊匹配（去掉路径前缀）
        for mapped_field_name, field_info in field_mapping.items():
            mapped_base_name = mapped_field_name.split('.')[-1]
            if mapped_base_name == field_name:
                return field_info

        return None

    def get_field_chinese_name(
        self, module_name: str, field_name: str
    ) -> Optional[str]:
        """获取字段中文名称"""
        matched_info = self.match_field(module_name, field_name)
        return matched_info['chinese_name'] if matched_info else None

    def get_field_data_type(
    self,
    module_name: str,
     field_name: str) -> Optional[str]:
        """获取字段数据类型"""
        matched_info = self.match_field(module_name, field_name)
        return matched_info['data_type'] if matched_info else None

    def get_field_max_length(
    self,
    module_name: str,
     field_name: str) -> Optional[int]:
        """获取字段最大长度"""
        matched_info = self.match_field(module_name, field_name)
        return matched_info.get('max_length') if matched_info else None
