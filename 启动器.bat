@echo off
chcp 65001 >nul
title YS-API V3.0 系统启动器
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                  🚀 YS-API V3.0 系统启动器                   ║
echo ║                     System Launcher                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 请选择启动方式:
echo.
echo 1. 🚀 启动后端服务 (http://localhost:8000)
echo 2. 🌐 启动前端服务 (http://localhost:3000)  
echo 3. 🔄 同时启动前后端 (推荐)
echo 4. 🐳 Docker容器启动
echo 5. 🧪 运行系统测试
echo 6. 📊 查看系统状态
echo 7. ❌ 退出
echo.

set /p choice=请输入选择 (1-7): 

if "%choice%"=="1" goto backend
if "%choice%"=="2" goto frontend  
if "%choice%"=="3" goto both
if "%choice%"=="4" goto docker
if "%choice%"=="5" goto test
if "%choice%"=="6" goto status
if "%choice%"=="7" goto exit

echo ❌ 无效选择，请重新输入
pause
goto start

:backend
echo.
echo 🚀 启动后端服务...
call start_backend.bat
goto end

:frontend
echo.
echo 🌐 启动前端服务...
call start_frontend_new.bat
goto end

:both
echo.
echo 🔄 同时启动前后端服务...
echo.
echo 📝 正在启动后端服务...
start "YS-API Backend" /MIN start_backend.bat

echo ⏳ 等待后端服务启动...
timeout /t 5 /nobreak >nul

echo 📝 正在启动前端服务...
start "YS-API Frontend" /MIN start_frontend_new.bat

echo.
echo ✅ 服务启动完成!
echo.
echo 🌐 访问地址:
echo    前端主页: http://localhost:3000
echo    后端API:  http://localhost:8000
echo    健康检查: http://localhost:8000/health
echo.
echo 💡 提示: 两个服务窗口已最小化运行
echo 💡 关闭服务: 在各自窗口按 Ctrl+C
echo.

echo 🌐 是否立即打开前端页面? (y/n)
set /p open=请选择: 
if /i "%open%"=="y" (
    start http://localhost:3000
)
goto end

:docker
echo.
echo 🐳 Docker容器启动...
if not exist "docker-compose.yml" (
    echo ❌ 错误: docker-compose.yml 文件不存在
    pause
    goto start
)

echo 🔍 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Docker 未安装或未启动
    echo 💡 请安装并启动 Docker Desktop
    pause
    goto start
)

echo 🏗️ 构建并启动容器...
docker-compose up -d

echo.
echo ✅ Docker容器启动完成!
echo 🌐 访问地址: http://localhost:8000
echo 📊 查看状态: docker-compose ps
echo 🔄 停止服务: docker-compose down
echo.
pause
goto end

:test
echo.
echo 🧪 运行系统测试...
echo.
echo 🔍 选择测试类型:
echo 1. 快速健康检查
echo 2. 完整生产环境测试
echo 3. 返回主菜单
echo.
set /p test_choice=请选择 (1-3): 

if "%test_choice%"=="1" (
    echo 🏥 运行快速健康检查...
    python quick_health_check.py
) else if "%test_choice%"=="2" (
    echo 🔬 运行完整生产环境测试...
    python production_readiness_report.py
) else if "%test_choice%"=="3" (
    goto start
) else (
    echo ❌ 无效选择
)
pause
goto start

:status
echo.
echo 📊 系统状态检查...
echo.

echo 🔍 检查后端服务状态...
curl -s http://localhost:8000/health >nul 2>&1
if errorlevel 1 (
    echo ❌ 后端服务未运行 (http://localhost:8000)
) else (
    echo ✅ 后端服务正常 (http://localhost:8000)
)

echo 🔍 检查前端服务状态...
curl -s http://localhost:3000 >nul 2>&1
if errorlevel 1 (
    echo ❌ 前端服务未运行 (http://localhost:3000)
) else (
    echo ✅ 前端服务正常 (http://localhost:3000)
)

echo.
echo 🖥️ 系统进程状态:
tasklist | findstr python.exe

echo.
echo 🌐 网络端口占用:
netstat -ano | findstr :8000
netstat -ano | findstr :3000

echo.
pause
goto start

:exit
echo.
echo 👋 感谢使用 YS-API V3.0!
echo.
exit

:end
echo.
echo 🔄 返回主菜单? (y/n)
set /p return=请选择: 
if /i "%return%"=="y" goto start
exit

:start
goto :eof
