"""
代理层路由配置 - 采购订单模块
"""

STRANGLER_ROUTES = {
    "purchase_order": {
        "legacy_path": "/api/modules/purchase_order",
        "new_path": "/api/v2/modules/purchase_order",
        "migration_status": "in_progress",  # in_progress, completed, legacy_only
        "traffic_split": 10,  # 10%流量到新系统
        "health_check": {
            "legacy": "/api/modules/purchase_order/health",
            "new": "/api/v2/modules/purchase_order/health",
        },
    }
}

# 其他模块配置...
STRANGLER_ROUTES.update(
    {
        "material_outbound": {
            "legacy_path": "/api/modules/material_outbound",
            "new_path": "/api/v2/modules/material_outbound",
            "migration_status": "completed",  # 第1个模块已完成
            "traffic_split": 100,  # 100%流量到新系统
        }
    }
)
