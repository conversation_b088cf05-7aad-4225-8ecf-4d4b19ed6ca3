/**
 * 统一错误处理器 - 解决项目中错误处理机制不统一的问题
 * 基于代码缺陷分析报告创建
 * 版本: 1.0.0
 */

class ErrorHandler {
    constructor(options = {}) {
        this.options = {
            enableRetry: options.enableRetry !== false,
            maxRetries: options.maxRetries || 3,
            retryDelay: options.retryDelay || 1000,
            enableLogging: options.enableLogging !== false,
            enableUserNotification: options.enableUserNotification !== false,
            ...options
        };

        // 错误类型定义
        this.ErrorTypes = {
            NETWORK: 'network',
            API: 'api',
            VALIDATION: 'validation',
            AUTH: 'authentication',
            FILE_IO: 'file_io',
            DATABASE: 'database',
            UNKNOWN: 'unknown'
        };

        // 错误级别定义
        this.ErrorLevels = {
            CRITICAL: 'critical',
            HIGH: 'high',
            MEDIUM: 'medium',
            LOW: 'low'
        };

        this.errorStats = {
            total: 0,
            byType: {},
            byLevel: {}
        };
    }

    /**
     * 处理异步操作的错误（支持重试）
     * @param {Function} operation - 要执行的异步操作
     * @param {Object} options - 错误处理选项
     * @returns {Promise} 操作结果
     */
    async handleAsyncOperation(operation, options = {}) {
        const config = { ...this.options, ...options };
        let lastError = null;

        for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
            try {
                const result = await operation();

                // 如果之前有失败，记录恢复成功
                if (attempt > 0) {
                    this.logError({
                        type: this.ErrorTypes.UNKNOWN,
                        level: this.ErrorLevels.MEDIUM,
                        message: `操作在第${attempt + 1}次尝试后成功`,
                        context: { attempts: attempt + 1 }
                    });
                }

                return { success: true, data: result, attempts: attempt + 1 };

            } catch (error) {
                lastError = error;

                // 如果还有重试机会
                if (attempt < config.maxRetries && this.shouldRetry(error, config)) {
                    this.logError({
                        type: this.classifyError(error),
                        level: this.ErrorLevels.MEDIUM,
                        message: `操作失败，准备重试 (${attempt + 1}/${config.maxRetries})`,
                        error: error,
                        context: { attempt: attempt + 1, willRetry: true }
                    });

                    // 等待后重试
                    await this.delay(config.retryDelay * Math.pow(2, attempt));
                    continue;
                }

                // 所有重试都失败
                break;
            }
        }

        // 处理最终失败
        const errorInfo = this.processError(lastError, config);
        return {
            success: false,
            error: errorInfo,
            attempts: config.maxRetries + 1
        };
    }

    /**
     * 处理同步操作的错误
     * @param {Function} operation - 要执行的同步操作
     * @param {Object} options - 错误处理选项
     * @returns {Object} 操作结果
     */
    handleSyncOperation(operation, options = {}) {
        try {
            const result = operation();
            return { success: true, data: result };
        } catch (error) {
            const errorInfo = this.processError(error, options);
            return { success: false, error: errorInfo };
        }
    }

    /**
     * 处理API调用错误
     * @param {Response} response - fetch响应对象
     * @param {Object} options - 处理选项
     * @returns {Object} 错误信息
     */
    async handleApiError(response, options = {}) {
        let errorData = null;

        try {
            // 尝试解析错误响应
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                errorData = await response.json();
            } else {
                errorData = { message: await response.text() };
            }
        } catch (parseError) {
            errorData = { message: `HTTP ${response.status}: ${response.statusText}` };
        }

        const errorInfo = {
            type: this.ErrorTypes.API,
            level: this.getApiErrorLevel(response.status),
            message: errorData.message || `API请求失败 (${response.status})`,
            status: response.status,
            url: response.url,
            data: errorData,
            timestamp: new Date().toISOString()
        };

        this.logError(errorInfo);

        if (options.showToUser !== false) {
            this.showUserError(errorInfo);
        }

        return errorInfo;
    }

    /**
     * 分类错误类型
     * @param {Error} error - 错误对象
     * @returns {string} 错误类型
     */
    classifyError(error) {
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            return this.ErrorTypes.NETWORK;
        }
        if (error.name === 'ValidationError') {
            return this.ErrorTypes.VALIDATION;
        }
        if (error.message && error.message.includes('auth')) {
            return this.ErrorTypes.AUTH;
        }
        if (error.message && error.message.includes('file')) {
            return this.ErrorTypes.FILE_IO;
        }
        if (error.message && error.message.includes('database')) {
            return this.ErrorTypes.DATABASE;
        }
        return this.ErrorTypes.UNKNOWN;
    }

    /**
     * 判断是否应该重试
     * @param {Error} error - 错误对象
     * @param {Object} config - 配置
     * @returns {boolean} 是否应该重试
     */
    shouldRetry(error, config) {
        if (!config.enableRetry) return false;

        const errorType = this.classifyError(error);

        // 网络错误通常可以重试
        if (errorType === this.ErrorTypes.NETWORK) return true;

        // 验证错误通常不应该重试
        if (errorType === this.ErrorTypes.VALIDATION) return false;
        if (errorType === this.ErrorTypes.AUTH) return false;

        // API错误根据状态码判断
        if (error.status) {
            // 5xx服务器错误可以重试，4xx客户端错误不重试
            return error.status >= 500;
        }

        // 其他情况可以重试
        return true;
    }

    /**
     * 获取API错误级别
     * @param {number} status - HTTP状态码
     * @returns {string} 错误级别
     */
    getApiErrorLevel(status) {
        if (status >= 500) return this.ErrorLevels.HIGH;
        if (status === 401 || status === 403) return this.ErrorLevels.MEDIUM;
        if (status >= 400) return this.ErrorLevels.LOW;
        return this.ErrorLevels.MEDIUM;
    }

    /**
     * 处理错误（统一入口）
     * @param {Error} error - 错误对象
     * @param {Object} options - 处理选项
     * @returns {Object} 处理后的错误信息
     */
    processError(error, options = {}) {
        const errorInfo = {
            type: this.classifyError(error),
            level: options.level || this.ErrorLevels.MEDIUM,
            message: error.message || '未知错误',
            stack: error.stack,
            timestamp: new Date().toISOString(),
            context: options.context || {}
        };

        // 记录错误
        this.logError(errorInfo);

        // 显示用户通知
        if (options.showToUser !== false) {
            this.showUserError(errorInfo);
        }

        // 更新统计
        this.updateErrorStats(errorInfo);

        return errorInfo;
    }

    /**
     * 记录错误日志
     * @param {Object} errorInfo - 错误信息
     */
    logError(errorInfo) {
        if (!this.options.enableLogging) return;

        const logLevel = this.getLogLevel(errorInfo.level);
        const logMessage = `[${errorInfo.type.toUpperCase()}] ${errorInfo.message}`;

        // 使用智能日志工具（如果可用）
        if (window.SmartLogger) {
            const logger = new SmartLogger('ErrorHandler', true);
            logger[logLevel](logMessage, {
                type: errorInfo.type,
                level: errorInfo.level,
                timestamp: errorInfo.timestamp,
                context: errorInfo.context
            });
        } else {
            // 回退到console
            console[logLevel](`🚨 ${logMessage}`, errorInfo);
        }
    }

    /**
     * 显示用户错误提示
     * @param {Object} errorInfo - 错误信息
     */
    showUserError(errorInfo) {
        if (!this.options.enableUserNotification) return;

        // 触发自定义事件，让UI组件处理
        const event = new CustomEvent('errorNotification', {
            detail: {
                type: errorInfo.type,
                level: errorInfo.level,
                message: this.getUserFriendlyMessage(errorInfo),
                timestamp: errorInfo.timestamp
            }
        });

        window.dispatchEvent(event);
    }

    /**
     * 获取用户友好的错误消息
     * @param {Object} errorInfo - 错误信息
     * @returns {string} 用户友好的消息
     */
    getUserFriendlyMessage(errorInfo) {
        const friendlyMessages = {
            [this.ErrorTypes.NETWORK]: '网络连接异常，请检查网络后重试',
            [this.ErrorTypes.API]: '服务暂时不可用，请稍后重试',
            [this.ErrorTypes.VALIDATION]: '输入数据有误，请检查后重新提交',
            [this.ErrorTypes.AUTH]: '身份验证失败，请重新登录',
            [this.ErrorTypes.FILE_IO]: '文件操作失败，请检查文件权限',
            [this.ErrorTypes.DATABASE]: '数据操作失败，请联系管理员'
        };

        return friendlyMessages[errorInfo.type] || '操作失败，请稍后重试';
    }

    /**
     * 获取日志级别
     * @param {string} errorLevel - 错误级别
     * @returns {string} 日志级别
     */
    getLogLevel(errorLevel) {
        const mapping = {
            [this.ErrorLevels.CRITICAL]: 'error',
            [this.ErrorLevels.HIGH]: 'error',
            [this.ErrorLevels.MEDIUM]: 'warn',
            [this.ErrorLevels.LOW]: 'info'
        };
        return mapping[errorLevel] || 'warn';
    }

    /**
     * 更新错误统计
     * @param {Object} errorInfo - 错误信息
     */
    updateErrorStats(errorInfo) {
        this.errorStats.total++;
        this.errorStats.byType[errorInfo.type] = (this.errorStats.byType[errorInfo.type] || 0) + 1;
        this.errorStats.byLevel[errorInfo.level] = (this.errorStats.byLevel[errorInfo.level] || 0) + 1;
    }

    /**
     * 获取错误统计
     * @returns {Object} 错误统计信息
     */
    getErrorStats() {
        return { ...this.errorStats };
    }

    /**
     * 延迟函数
     * @param {number} ms - 延迟毫秒数
     * @returns {Promise} Promise对象
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 重置统计信息
     */
    resetStats() {
        this.errorStats = {
            total: 0,
            byType: {},
            byLevel: {}
        };
    }
}

// 全局错误处理器实例
window.GlobalErrorHandler = new ErrorHandler({
    enableRetry: true,
    maxRetries: 3,
    retryDelay: 1000,
    enableLogging: true,
    enableUserNotification: true
});

// 便捷函数
window.handleAsync = (operation, options) =>;
    window.GlobalErrorHandler.handleAsyncOperation(operation, options);

window.handleSync = (operation, options) =>;
    window.GlobalErrorHandler.handleSyncOperation(operation, options);

window.handleApiError = (response, options) =>;
    window.GlobalErrorHandler.handleApiError(response, options);

// 全局暴露 - 兼容新的组件管理架构
window.ErrorHandler = ErrorHandler;

// 如果ComponentManager存在，使用它来管理实例
if (window.ComponentManager) {
    // 延迟注册，等待ComponentManager完全初始化
    setTimeout(() => {
        if (window.ComponentManager && !window.ComponentManager.isRegistered('errorHandler')) {
            window.ComponentManager.register('errorHandler', ErrorHandler, {
                singleton: true,
                global: true,
                autoInit: true,
                description: '统一错误处理器'
            });
        }
    }, 0);
} else {
    // 传统方式创建实例（向后兼容）
    if (!window.GlobalErrorHandler) {
        window.GlobalErrorHandler = new ErrorHandler();
    }
    // 为兼容性同时创建ErrorHandler引用
    if (!window.ErrorHandler) {
        window.ErrorHandler = window.GlobalErrorHandler;
    }
}

// 导出给模块化使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorHandler;
}
