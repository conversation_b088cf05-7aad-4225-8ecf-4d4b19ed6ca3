import re
import sys
from collections import defaultdict
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件命名冲突检查器
"""


class NamingConflictChecker:
    def __init___(self):
    """TODO: Add function description."""
    self.naming_patterns = {
        'test_files': re.compile(r'test_.*\.py$'),
        'config_files': re.compile(r'.*config.*\.py$'),
        'utils_files': re.compile(r'.*util.*\.py$'),
        'helper_files': re.compile(r'.*helper.*\.py$'),
        'fixer_files': re.compile(r'.*fixer.*\.py$'),
        'checker_files': re.compile(r'.*checker.*\.py$'),
        'builder_files': re.compile(r'.*builder.*\.py$'),
        'generator_files': re.compile(r'.*generator.*\.py$'),
    }

    self.suspicious_suffixes = [
        '_v1',
        '_v2',
        '_new',
        '_old',
        '_backup',
        '_copy',
        '_fixed',
        '_final',
        '_temp',
        '_test',
        '2',
        '_alt',
    ]

    def check_naming_conflicts(self, files: list) -> bool:
        """检查命名冲突"""
        has_conflicts = False
        file_groups = defaultdict(list)

        # 按模式分组
        for file_path in files:
            path = Path(file_path)
            if not path.exists():
                continue

            filename = path.name

            # 检查可疑后缀
            for suffix in self.suspicious_suffixes:
                if suffix in filename:
                    print(f"⚠️ 可疑文件名: {file_path} (包含 '{suffix}')")
                    has_conflicts = True

            # 按功能分组
            for pattern_name, pattern in self.naming_patterns.items():
                if pattern.match(filename):
                    file_groups[pattern_name].append(file_path)

        # 检查每组中是否有重复功能
        for group_name, group_files in file_groups.items():
            if len(group_files) > 1:
                # 进一步检查是否真的重复
                if self._check_functional_similarity(group_files):
                    print(f"❌ 发现可能重复的 {group_name}:")
                    for file in group_files:
                        print(f"   - {file}")
                    has_conflicts = True

        return has_conflicts

    def _check_functional_similarity(self, files: list) -> bool:
        """检查文件功能相似性"""
        # 简单的启发式检查
        if len(files) < 2:
            return False

        # 检查文件大小相似性
        sizes = []
        for file_path in files:
            try:
                size = Path(file_path).stat().st_size
                sizes.append(size)
            except Exception:
                continue

        if len(sizes) >= 2:
            # 如果文件大小差异很小，可能是重复的
            max_size = max(sizes)
            min_size = min(sizes)
            if max_size > 0 and (max_size - min_size) / max_size < 0.1:
                return True

        return False


def check_naming_conflicts(files):
    """公共接口：检查命名冲突"""
    checker = NamingConflictChecker()
    return checker.check_naming_conflicts(files)


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python check_naming_conflicts.py file1.py file2.py ...")
        return 0

    files = sys.argv[1:]
    checker = NamingConflictChecker()

    print("🔍 检查文件命名冲突...")
    has_conflicts = checker.check_naming_conflicts(files)

    if has_conflicts:
        print("\n❌ 发现命名冲突，建议检查是否有重复功能")
        print("💡 建议:")
        print("   1. 删除不必要的重复文件")
        print("   2. 重命名具有明确不同用途的文件")
        print("   3. 合并功能相同的文件")
        return 1
    else:
        print("✅ 命名检查通过")
        return 0


if __name__ == "__main__":
    sys.exit(main())
