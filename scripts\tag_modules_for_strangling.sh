#!/bin/bash
# 标记待绞杀模块脚本

echo "🏷️ 开始标记待绞杀模块..."

# 创建标记目录
mkdir -p analysis/strangling_targets

# 读取屎山代码识别结果
SHIT_FUNCTIONS_FILE="analysis/shit_mountain/shit_functions.txt"
SHIT_CONDITIONS_FILE="analysis/shit_mountain/shit_conditions.txt"
GIANT_CLASSES_FILE="analysis/shit_mountain/giant_classes.txt"

# 提取模块列表
echo "📋 提取含有屎山代码的模块..."

# 从文件路径中提取模块名
get_module_from_path() {
    echo "$1" | sed 's|backend/app/||' | sed 's|/.*||' | sort -u
}

# 标记含有超长函数的模块
if [ -f "$SHIT_FUNCTIONS_FILE" ]; then
    echo "标记超长函数模块..."
    cat "$SHIT_FUNCTIONS_FILE" | cut -d: -f1 | while read file; do
        get_module_from_path "$file"
    done > analysis/strangling_targets/shit_function_modules.txt
fi

# 标记含有复杂条件的模块
if [ -f "$SHIT_CONDITIONS_FILE" ]; then
    echo "标记复杂条件模块..."
    cat "$SHIT_CONDITIONS_FILE" | cut -d: -f1 | while read file; do
        get_module_from_path "$file"
    done > analysis/strangling_targets/shit_condition_modules.txt
fi

# 标记巨型类模块
if [ -f "$GIANT_CLASSES_FILE" ]; then
    echo "标记巨型类模块..."
    cat "$GIANT_CLASSES_FILE" | cut -d: -f1 | while read file; do
        get_module_from_path "$file"
    done > analysis/strangling_targets/giant_class_modules.txt
fi

# 合并所有待绞杀模块
echo "📊 合并待绞杀模块列表..."
cat analysis/strangling_targets/*_modules.txt | sort -u > analysis/strangling_targets/all_targets.txt

# 按优先级排序（基于问题严重程度）
echo "🎯 按优先级排序绞杀目标..."
cat > analysis/strangling_targets/priority_targets.txt << EOF
# 高优先级绞杀目标（多种屎山问题）
$(comm -12 <(sort analysis/strangling_targets/shit_function_modules.txt) <(sort analysis/strangling_targets/giant_class_modules.txt))

# 中优先级绞杀目标（单一严重问题）
$(cat analysis/strangling_targets/giant_class_modules.txt | grep -v -f <(comm -12 <(sort analysis/strangling_targets/shit_function_modules.txt) <(sort analysis/strangling_targets/giant_class_modules.txt)))

# 低优先级绞杀目标（复杂条件问题）
$(cat analysis/strangling_targets/shit_condition_modules.txt | grep -v -f analysis/strangling_targets/giant_class_modules.txt | grep -v -f analysis/strangling_targets/shit_function_modules.txt)
EOF

# 生成绞杀计划
echo "📅 生成绞杀计划..."
cat > analysis/strangling_targets/strangling_plan.md << EOF
# 模块绞杀计划

生成时间: $(date)

## 绞杀优先级

### 🔥 高优先级（Week 1-2）
$(sed -n '/# 高优先级绞杀目标/,/# 中优先级绞杀目标/p' analysis/strangling_targets/priority_targets.txt | grep -v '^#' | head -10)

### 🟡 中优先级（Week 3-4）
$(sed -n '/# 中优先级绞杀目标/,/# 低优先级绞杀目标/p' analysis/strangling_targets/priority_targets.txt | grep -v '^#' | head -10)

### 🟢 低优先级（Month 2）
$(sed -n '/# 低优先级绞杀目标/,$p' analysis/strangling_targets/priority_targets.txt | grep -v '^#' | head -10)

## 绞杀策略

每个模块的绞杀步骤：
1. 创建新模块接口
2. 实现新模块逻辑
3. 添加代理层路由
4. 逐步切换流量（10% → 50% → 100%）
5. 监控验证
6. 删除旧模块

## 执行命令

\`\`\`bash
# 开始绞杀指定模块
./strangler_fig_executor.sh MODULE_NAME

# 检查绞杀进度
./check_strangling_progress.sh
\`\`\`
EOF

echo "✅ 模块标记完成！"
echo "📄 查看绞杀计划: cat analysis/strangling_targets/strangling_plan.md"
echo "📊 待绞杀模块总数: $(wc -l < analysis/strangling_targets/all_targets.txt)"
