networks:
  default:
    driver: bridge
services:
  nginx:
    depends_on:
    - ys-api
    image: nginx:alpine
    ports:
    - 80:80
    - 443:443
    restart: unless-stopped
    volumes:
    - ./nginx.conf:/etc/nginx/nginx.conf
    - ./ssl:/etc/nginx/ssl
  ys-api:
    build: .
    environment:
      DATABASE_URL: sqlite:///app/ysapi.db
      FLASK_ENV: production
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:5000/health
      timeout: 10s
    ports:
    - 5000:5000
    restart: unless-stopped
    volumes:
    - ./backend/ysapi.db:/app/ysapi.db
    - ./config:/app/config
    - ./logs:/app/logs
version: '3.8'
