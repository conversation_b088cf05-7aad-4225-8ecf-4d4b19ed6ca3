import json
from pathlib import Path

import structlog

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 字段值域映射服务
负责管理字段值的业务含义映射，支持枚举、布尔、多语言等类型
"""


logger = structlog.get_logger()


class FieldValueMappingService:
    """字段值域映射服务"""

    def __init___(self):
    """TODO: Add function description."""
    self.mappings = self._load_mappings()
    logger.info("字段值域映射服务初始化完成")

    def _load_mappings(self) -> Dict[str, Dict]:
        """加载值域映射配置"""
        # 默认映射配置
        default_mappings = {
            "purchase_order": {
                "status": {
                    "0": {
                        "business_meaning": "开立",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#1890ff",
                        "labels": {"zh": "开立", "en": "Draft"},
                    },
                    "1": {
                        "business_meaning": "已审核",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#52c41a",
                        "labels": {"zh": "已审核", "en": "Approved"},
                    },
                    "2": {
                        "business_meaning": "已关闭",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#ff4d4f",
                        "labels": {"zh": "已关闭", "en": "Closed"},
                    },
                    "3": {
                        "business_meaning": "审核中",
                        "mapping_type": "enum",
                        "sort_order": 4,
                        "color": "#faad14",
                        "labels": {"zh": "审核中", "en": "Reviewing"},
                    },
                },
                "modifyStatus": {
                    "0": {
                        "business_meaning": "未变更",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#52c41a",
                        "labels": {"zh": "未变更", "en": "Unchanged"},
                    },
                    "1": {
                        "business_meaning": "变更中",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#faad14",
                        "labels": {"zh": "变更中", "en": "Changing"},
                    },
                    "2": {
                        "business_meaning": "变更完成",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#1890ff",
                        "labels": {"zh": "变更完成", "en": "Changed"},
                    },
                },
                "purchaseOrders_arrivedStatus": {
                    "1": {
                        "business_meaning": "到货完成",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#52c41a",
                        "labels": {"zh": "到货完成", "en": "Arrived"},
                    },
                    "2": {
                        "business_meaning": "未到货",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#ff4d4f",
                        "labels": {"zh": "未到货", "en": "Not Arrived"},
                    },
                    "3": {
                        "business_meaning": "部分到货",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#faad14",
                        "labels": {"zh": "部分到货", "en": "Partially Arrived"},
                    },
                    "4": {
                        "business_meaning": "到货完成",
                        "mapping_type": "enum",
                        "sort_order": 4,
                        "color": "#52c41a",
                        "labels": {"zh": "到货完成", "en": "Arrived"},
                    },
                },
                "purchaseOrders_inWHStatus": {
                    "1": {
                        "business_meaning": "入库完成",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#52c41a",
                        "labels": {"zh": "入库完成", "en": "In Stock"},
                    },
                    "2": {
                        "business_meaning": "未入库",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#ff4d4f",
                        "labels": {"zh": "未入库", "en": "Not In Stock"},
                    },
                    "3": {
                        "business_meaning": "部分入库",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#faad14",
                        "labels": {"zh": "部分入库", "en": "Partially In Stock"},
                    },
                    "4": {
                        "business_meaning": "入库结束",
                        "mapping_type": "enum",
                        "sort_order": 4,
                        "color": "#1890ff",
                        "labels": {"zh": "入库结束", "en": "Stock Complete"},
                    },
                },
                "purchaseOrders_payStatus": {
                    "1": {
                        "business_meaning": "核销完成",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#52c41a",
                        "labels": {"zh": "核销完成", "en": "Paid"},
                    },
                    "2": {
                        "business_meaning": "未核销",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#ff4d4f",
                        "labels": {"zh": "未核销", "en": "Unpaid"},
                    },
                    "3": {
                        "business_meaning": "部分核销",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#faad14",
                        "labels": {"zh": "部分核销", "en": "Partially Paid"},
                    },
                },
                "purchaseOrders_invoiceStatus": {
                    "1": {
                        "business_meaning": "开票完成",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#52c41a",
                        "labels": {"zh": "开票完成", "en": "Invoiced"},
                    },
                    "2": {
                        "business_meaning": "未开票",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#ff4d4f",
                        "labels": {"zh": "未开票", "en": "Not Invoiced"},
                    },
                    "3": {
                        "business_meaning": "部分开票",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#faad14",
                        "labels": {"zh": "部分开票", "en": "Partially Invoiced"},
                    },
                    "4": {
                        "business_meaning": "开票结束",
                        "mapping_type": "enum",
                        "sort_order": 4,
                        "color": "#1890ff",
                        "labels": {"zh": "开票结束", "en": "Invoice Complete"},
                    },
                },
                "isWfControlled": {
                    "true": {
                        "business_meaning": "是",
                        "mapping_type": "boolean",
                        "sort_order": 1,
                        "color": "#52c41a",
                        "labels": {"zh": "是", "en": "Yes"},
                    },
                    "false": {
                        "business_meaning": "否",
                        "mapping_type": "boolean",
                        "sort_order": 2,
                        "color": "#ff4d4f",
                        "labels": {"zh": "否", "en": "No"},
                    },
                    "1": {
                        "business_meaning": "是",
                        "mapping_type": "boolean",
                        "sort_order": 1,
                        "color": "#52c41a",
                        "labels": {"zh": "是", "en": "Yes"},
                    },
                    "0": {
                        "business_meaning": "否",
                        "mapping_type": "boolean",
                        "sort_order": 2,
                        "color": "#ff4d4f",
                        "labels": {"zh": "否", "en": "No"},
                    },
                    "T": {
                        "business_meaning": "是",
                        "mapping_type": "boolean",
                        "sort_order": 1,
                        "color": "#52c41a",
                        "labels": {"zh": "是", "en": "Yes"},
                    },
                    "F": {
                        "business_meaning": "否",
                        "mapping_type": "boolean",
                        "sort_order": 2,
                        "color": "#ff4d4f",
                        "labels": {"zh": "否", "en": "No"},
                    },
                },
                "purchaseOrders_isGiftProduct": {
                    "true": {
                        "business_meaning": "是",
                        "mapping_type": "boolean",
                        "sort_order": 1,
                        "color": "#52c41a",
                        "labels": {"zh": "是", "en": "Yes"},
                    },
                    "false": {
                        "business_meaning": "否",
                        "mapping_type": "boolean",
                        "sort_order": 2,
                        "color": "#ff4d4f",
                        "labels": {"zh": "否", "en": "No"},
                    },
                    "1": {
                        "business_meaning": "是",
                        "mapping_type": "boolean",
                        "sort_order": 1,
                        "color": "#52c41a",
                        "labels": {"zh": "是", "en": "Yes"},
                    },
                    "0": {
                        "business_meaning": "否",
                        "mapping_type": "boolean",
                        "sort_order": 2,
                        "color": "#ff4d4f",
                        "labels": {"zh": "否", "en": "No"},
                    },
                },
                "listdiscountTaxType": {
                    "0": {
                        "business_meaning": "应税外加",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#1890ff",
                        "labels": {"zh": "应税外加", "en": "Tax Excluded"},
                    },
                    "1": {
                        "business_meaning": "应税内含",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#52c41a",
                        "labels": {"zh": "应税内含", "en": "Tax Included"},
                    },
                },
            },
            "production_order": {
                "status": {
                    "0": {
                        "business_meaning": "开立",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#1890ff",
                        "labels": {"zh": "开立", "en": "Draft"},
                    },
                    "1": {
                        "business_meaning": "已审核",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#52c41a",
                        "labels": {"zh": "已审核", "en": "Approved"},
                    },
                    "2": {
                        "business_meaning": "已关闭",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#ff4d4f",
                        "labels": {"zh": "已关闭", "en": "Closed"},
                    },
                    "3": {
                        "business_meaning": "审核中",
                        "mapping_type": "enum",
                        "sort_order": 4,
                        "color": "#faad14",
                        "labels": {"zh": "审核中", "en": "Reviewing"},
                    },
                    "4": {
                        "business_meaning": "已锁定",
                        "mapping_type": "enum",
                        "sort_order": 5,
                        "color": "#722ed1",
                        "labels": {"zh": "已锁定", "en": "Locked"},
                    },
                    "5": {
                        "business_meaning": "已开工",
                        "mapping_type": "enum",
                        "sort_order": 6,
                        "color": "#13c2c2",
                        "labels": {"zh": "已开工", "en": "Started"},
                    },
                },
                "OrderProduct_materialStatus": {
                    "0": {
                        "business_meaning": "未领料",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#ff4d4f",
                        "labels": {"zh": "未领料", "en": "Not Picked"},
                    },
                    "1": {
                        "business_meaning": "已领料",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#52c41a",
                        "labels": {"zh": "已领料", "en": "Picked"},
                    },
                    "2": {
                        "business_meaning": "部分领料",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#faad14",
                        "labels": {"zh": "部分领料", "en": "Partially Picked"},
                    },
                },
                "OrderProduct_materialApplyStatus": {
                    "0": {
                        "business_meaning": "未申请",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#ff4d4f",
                        "labels": {"zh": "未申请", "en": "Not Applied"},
                    },
                    "1": {
                        "business_meaning": "已申请",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#faad14",
                        "labels": {"zh": "已申请", "en": "Applied"},
                    },
                    "2": {
                        "business_meaning": "已审核",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#52c41a",
                        "labels": {"zh": "已审核", "en": "Approved"},
                    },
                },
                "OrderProduct_finishedWorkApplyStatus": {
                    "0": {
                        "business_meaning": "未申报",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#ff4d4f",
                        "labels": {"zh": "未申报", "en": "Not Declared"},
                    },
                    "1": {
                        "business_meaning": "已申报",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#faad14",
                        "labels": {"zh": "已申报", "en": "Declared"},
                    },
                    "2": {
                        "business_meaning": "已审核",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#52c41a",
                        "labels": {"zh": "已审核", "en": "Approved"},
                    },
                },
                "OrderProduct_stockStatus": {
                    "0": {
                        "business_meaning": "未入库",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#ff4d4f",
                        "labels": {"zh": "未入库", "en": "Not In Stock"},
                    },
                    "1": {
                        "business_meaning": "已入库",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#52c41a",
                        "labels": {"zh": "已入库", "en": "In Stock"},
                    },
                    "2": {
                        "business_meaning": "部分入库",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#faad14",
                        "labels": {"zh": "部分入库", "en": "Partially In Stock"},
                    },
                },
                "dailyschStatus": {
                    "0": {
                        "business_meaning": "未排产",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#ff4d4f",
                        "labels": {"zh": "未排产", "en": "Not Scheduled"},
                    },
                    "1": {
                        "business_meaning": "已排产",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#52c41a",
                        "labels": {"zh": "已排产", "en": "Scheduled"},
                    },
                    "2": {
                        "business_meaning": "排产中",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#faad14",
                        "labels": {"zh": "排产中", "en": "Scheduling"},
                    },
                },
                "OrderProduct_financeStatus": {
                    "0": {
                        "business_meaning": "未结算",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#ff4d4f",
                        "labels": {"zh": "未结算", "en": "Not Settled"},
                    },
                    "1": {
                        "business_meaning": "已结算",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#52c41a",
                        "labels": {"zh": "已结算", "en": "Settled"},
                    },
                    "2": {
                        "business_meaning": "部分结算",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#faad14",
                        "labels": {"zh": "部分结算", "en": "Partially Settled"},
                    },
                },
                "firstCheckStatus": {
                    "0": {
                        "business_meaning": "未首检",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#ff4d4f",
                        "labels": {"zh": "未首检", "en": "Not First Check"},
                    },
                    "1": {
                        "business_meaning": "已首检",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#52c41a",
                        "labels": {"zh": "已首检", "en": "First Checked"},
                    },
                    "2": {
                        "business_meaning": "首检中",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#faad14",
                        "labels": {"zh": "首检中", "en": "First Checking"},
                    },
                },
                "OrderProduct_realFinishStatus": {
                    "0": {
                        "business_meaning": "未完工",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#ff4d4f",
                        "labels": {"zh": "未完工", "en": "Not Finished"},
                    },
                    "1": {
                        "business_meaning": "已完工",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#52c41a",
                        "labels": {"zh": "已完工", "en": "Finished"},
                    },
                    "2": {
                        "business_meaning": "部分完工",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#faad14",
                        "labels": {"zh": "部分完工", "en": "Partially Finished"},
                    },
                },
                "OrderProduct_syncStatus": {
                    "0": {
                        "business_meaning": "未同步",
                        "mapping_type": "enum",
                        "sort_order": 1,
                        "color": "#ff4d4f",
                        "labels": {"zh": "未同步", "en": "Not Synced"},
                    },
                    "1": {
                        "business_meaning": "已同步",
                        "mapping_type": "enum",
                        "sort_order": 2,
                        "color": "#52c41a",
                        "labels": {"zh": "已同步", "en": "Synced"},
                    },
                    "2": {
                        "business_meaning": "同步中",
                        "mapping_type": "enum",
                        "sort_order": 3,
                        "color": "#faad14",
                        "labels": {"zh": "同步中", "en": "Syncing"},
                    },
                },
            },
        }

        # 尝试从配置文件加载自定义映射
        config_path = Path("config/field_value_mappings.json")
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    custom_mappings = json.load(f)
                    # 合并自定义映射
                    for module, mappings in custom_mappings.items():
                        if module in default_mappings:
                            default_mappings[module].update(mappings)
                        else:
                            default_mappings[module] = mappings
                logger.info("已加载自定义值域映射配置")
            except Exception:
                logger.error("加载自定义值域映射配置失败", error=str(e))

        return default_mappings

    def get_field_mapping(
            self,
            module_name: str,
            field_name: str,
            value: Any,
            language: str = "zh") -> Optional[Dict]:
        """
        获取字段值映射

        Args:
            module_name: 模块名称
            field_name: 字段名称
            value: 原始值
            language: 语言代码（zh/en）

        Returns:
            Optional[Dict]: 映射信息
        """
        try:
            # 获取模块的字段映射
            module_mappings = self.mappings.get(module_name, {})
            field_mappings = module_mappings.get(field_name, {})

            # 如果没有找到映射，返回None
            if not field_mappings:
                return None

            # 转换为字符串进行查找
            str_value = str(value).lower()

            # 查找映射
            for original_value, mapping_info in field_mappings.items():
                if str(original_value).lower() == str_value:
                    # 返回映射信息，包含指定语言的标签
                    result = mapping_info.copy()
                    result['original_value'] = original_value
                    result['display_value'] = mapping_info['labels'].get(
                        language, mapping_info['business_meaning']
                    )
                    return result

            return None

        except Exception:
            logger.error(
                "获取字段值映射失败",
                module_name=module_name,
                field_name=field_name,
                value=value,
                error=str(e),
            )
            return None

    def map_field_value(
            self,
            module_name: str,
            field_name: str,
            value: Any,
            language: str = "zh") -> str:
        """
        映射字段值

        Args:
            module_name: 模块名称
            field_name: 字段名称
            value: 原始值
            language: 语言代码（zh/en）

        Returns:
            str: 映射后的显示值
        """
        mapping_info = self.get_field_mapping(
            module_name, field_name, value, language)
        if mapping_info:
            return mapping_info['display_value']
        else:
            return str(value)

    def map_record_fields(
        self, module_name: str, record: Dict[str, Any], language: str = "zh"
    ) -> Dict[str, Any]:
        """
        映射记录中的所有字段

        Args:
            module_name: 模块名称
            record: 数据记录
            language: 语言代码（zh/en）

        Returns:
            Dict[str, Any]: 映射后的记录
        """
        try:
            mapped_record = record.copy()
            module_mappings = self.mappings.get(module_name, {})

            for field_name, value in record.items():
                if field_name in module_mappings:
                    mapped_value = self.map_field_value(
                        module_name, field_name, value, language
                    )
                    mapped_record[field_name] = mapped_value

            return mapped_record

        except Exception:
            logger.error("记录字段映射失败", module_name=module_name, error=str(e))
            return record

    def get_mappable_fields(self, module_name: str) -> list:
        """
        获取模块中可映射的字段列表

        Args:
            module_name: 模块名称

        Returns:
            list: 可映射字段列表
        """
        return list(self.mappings.get(module_name, {}).keys())

    def get_field_mapping_options(
        self, module_name: str, field_name: str, language: str = "zh"
    ) -> List[Dict]:
        """
        获取字段的映射选项（用于下拉框等）

        Args:
            module_name: 模块名称
            field_name: 字段名称
            language: 语言代码（zh/en）

        Returns:
            List[Dict]: 映射选项列表
        """
        try:
            module_mappings = self.mappings.get(module_name, {})
            field_mappings = module_mappings.get(field_name, {})

            options = []
            for original_value, mapping_info in field_mappings.items():
                option = {
                    'value': original_value,
                    'label': mapping_info['labels'].get(
                        language, mapping_info['business_meaning']
                    ),
                    'business_meaning': mapping_info['business_meaning'],
                    'mapping_type': mapping_info['mapping_type'],
                    'sort_order': mapping_info['sort_order'],
                    'color': mapping_info['color'],
                }
                options.append(option)

            # 按排序字段排序
            options.sort(key=lambda x: x['sort_order'])

            return options

        except Exception:
            logger.error(
                "获取字段映射选项失败",
                module_name=module_name,
                field_name=field_name,
                error=str(e),
            )
            return []

    def add_field_mapping(
            self,
            module_name: str,
            field_name: str,
            original_value: str,
            mapping_info: Dict) -> bool:
        """
        添加字段值映射

        Args:
            module_name: 模块名称
            field_name: 字段名称
            original_value: 原始值
            mapping_info: 映射信息

        Returns:
            bool: 是否成功
        """
        try:
            if module_name not in self.mappings:
                self.mappings[module_name] = {}

            if field_name not in self.mappings[module_name]:
                self.mappings[module_name][field_name] = {}

            self.mappings[module_name][field_name][original_value] = mapping_info

            logger.info(
                "添加字段值映射成功",
                module_name=module_name,
                field_name=field_name,
                original_value=original_value,
                mapping_info=mapping_info,
            )
            return True

        except Exception:
            logger.error(
                "添加字段值映射失败",
                module_name=module_name,
                field_name=field_name,
                original_value=original_value,
                error=str(e),
            )
            return False

    def save_mappings(self) -> bool:
        """
        保存映射配置到文件

        Returns:
            bool: 是否成功
        """
        try:
            config_path = Path("config/field_value_mappings.json")
            config_path.parent.mkdir(exist_ok=True)

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.mappings, f, ensure_ascii=False, indent=2)

            logger.info("字段值域映射配置已保存", path=str(config_path))
            return True

        except Exception:
            logger.error("保存字段值域映射配置失败", error=str(e))
            return False


# 全局实例
_field_value_mapping_service: Optional[FieldValueMappingService] = None


def get_field_value_mapping_service() -> FieldValueMappingService:
    """获取字段值域映射服务实例"""
    global _field_value_mapping_service
    if _field_value_mapping_service is None:
        _field_value_mapping_service = FieldValueMappingService()
    return _field_value_mapping_service
