@echo off
chcp 65001 > nul
echo ============================================================
echo 🚀 第三阶段：深入系统测试和功能验证
echo ============================================================
echo.

echo 🔍 Step 1: 基础启动验证
echo ------------------------------------------------------------
python scripts/phase3_verification.py
echo.

echo 📋 启动指南
echo ------------------------------------------------------------
echo 后端启动命令：
echo   方法1: cd backend ^&^& python start_server.py
echo   方法2: cd backend ^&^& python start_simple.py
echo.
echo 前端启动命令：
echo   cd frontend ^&^& python -m http.server 8080
echo.
echo 访问地址：
echo   前端页面: http://localhost:8080
echo   后端API: http://localhost:8000
echo.

echo 📝 下一步操作建议：
echo ------------------------------------------------------------
echo 1. 根据验证报告修复发现的问题
echo 2. 手动启动后端服务: cd backend ^&^& python start_server.py
echo 3. 手动启动前端服务: cd frontend ^&^& python -m http.server 8080
echo 4. 运行模块功能测试: python scripts/module_functional_test.py --all
echo 5. 检查测试和模拟代码清理需求
echo.

echo ⚠️ 重要提醒：
echo ------------------------------------------------------------
echo - 现在处于功能验证阶段，需要确认所有业务逻辑正确性
echo - 需要使用真实数据进行测试，而非模拟数据
echo - 测试通过后才能删除测试代码和模拟代码
echo - graveyard目录将在7天后（2025-08-13）进行清理
echo.

pause
