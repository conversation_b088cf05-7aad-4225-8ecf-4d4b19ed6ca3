<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>edc735d69b654129b0da4c38f2d8877f</id>
<name>产品入库列表查询</name>
<apiClassifyId>cd241c49f6fb427ca4d40e87ab224373</apiClassifyId>
<apiClassifyName>产品入库单</apiClassifyName>
<apiClassifyCode/>
<parentApiClassifies/>
<functionId/>
<openMode>0</openMode>
<description>产品入库列表查询</description>
<auth>true</auth>
<bodyPassthrough>false</bodyPassthrough>
<healthExam>false</healthExam>
<healthStatus>true</healthStatus>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/scm/storeprorecord/list</completeProxyUrl>
<connectUrl>/bill/list</connectUrl>
<sort>20</sort>
<handler>openapi</handler>
<httpRequestType>POST</httpRequestType>
<openApi>true</openApi>
<preset>false</preset>
<productId>710a0be3edff4f9092e35f63fd3b9bae</productId>
<productCode>scm</productCode>
<proxyUrl>/yonbip/scm/storeprorecord/list</proxyUrl>
<requestParamsDemo>Url : /yonsuite/scm/storeprorecord/list?access_token=访问令牌 Body: { "pageIndex": 0, "code": "", "pageSize": 0, "warehouse_name": [ "" ], "bustype_name": "", "stockMgr_name": [ "" ], "operator": [ "" ], "department": [ "" ], "org": {}, "product_cName": [ "" ], "open_hopeReceiveDate_begin": "", "open_hopeReceiveDate_end": "", "simpleVOs": [ { "field": "pubts", "op": "between", "value1": "2020-09-19 00:00:00", "value2": "2020-09-19 23:59:59" }, { "field": "headItem.define1", //表头自定义项 "op": "eq", "value1": "test1" }, { "field": "storeProRecords.bodyItem.define1", //表体自定义项 "op": "eq", "value1": "test2" } ] }</requestParamsDemo>
<requestProtocol>HTTP</requestProtocol>
<serviceHttpMethod>POST</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2020-01-16 18:20:20</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/storeprorecord/list</address>
<productName>采购供应</productName>
<productClassifyId>yonsuite</productClassifyId>
<productClassifyCode>yonbip</productClassifyCode>
<productClassifyName>用友 YonBIP</productClassifyName>
<paramDTOS>
<paramDTOS>
<id>2162396650814308363</id>
<name>pageIndex</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735046</defParamId>
<array>false</array>
<paramDesc>页号</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>1</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag>[pageIndex]</paramTag>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2162396650814308364</id>
<name>code</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735047</defParamId>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2162396650814308365</id>
<name>pageSize</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735048</defParamId>
<array>false</array>
<paramDesc>每页行数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>10</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag>[pageSize]</paramTag>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2162396650814308366</id>
<name>warehouse_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735049</defParamId>
<array>true</array>
<paramDesc>仓库名字</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2162396650814308367</id>
<name>bustype_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735050</defParamId>
<array>false</array>
<paramDesc>交易类型</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2162396650814308368</id>
<name>stockMgr_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735051</defParamId>
<array>true</array>
<paramDesc>库管人名字列表</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2162396650814308369</id>
<name>operator</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735052</defParamId>
<array>true</array>
<paramDesc>操作员id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2162396650814308370</id>
<name>department</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735053</defParamId>
<array>true</array>
<paramDesc>部门id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2162396650814308371</id>
<name>org</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735054</defParamId>
<array>false</array>
<paramDesc>组织id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2162396650814308372</id>
<name>product_cName</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735055</defParamId>
<array>true</array>
<paramDesc>物料ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2162396650814308373</id>
<name>open_hopeReceiveDate_begin</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735056</defParamId>
<array>false</array>
<paramDesc>区间查询开始时间 : "2020-03-02"</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2162396650814308374</id>
<name>open_hopeReceiveDate_end</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735057</defParamId>
<array>false</array>
<paramDesc>区间查询结束时间 :"2020-03-02 23:59:59"</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2162396650814308358</id>
<name>simpleVOs</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<children>
<children>
<id>2162396650814308359</id>
<name>field</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308358</parentId>
<defParamId>1856797255115735059</defParamId>
<array>false</array>
<paramDesc>属性名(条件),子表加前缀storeProRecords.</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2162396650814308360</id>
<name>op</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308358</parentId>
<defParamId>1856797255115735060</defParamId>
<array>false</array>
<paramDesc>条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2162396650814308361</id>
<name>value1</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308358</parentId>
<defParamId>1856797255115735061</defParamId>
<array>false</array>
<paramDesc>值1(条件),单条件时仅使用这个配置</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2162396650814308362</id>
<name>value2</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308358</parentId>
<defParamId>1856797255115735062</defParamId>
<array>false</array>
<paramDesc>值2(条件),单条件时此配置无效</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>1856797255115735058</defParamId>
<array>true</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>2162396650814308380</id>
<name>pageIndex</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>页号</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageIndex</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2162396650814308381</id>
<name>code</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>code</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2162396650814308382</id>
<name>pageSize</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>每页行数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageSize</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2162396650814308383</id>
<name>warehouse_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>仓库名字</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>warehouse_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2162396650814308384</id>
<name>bustype_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>交易类型</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>bustype_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2162396650814308385</id>
<name>stockMgr_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>库管人名字列表</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>stockMgr_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2162396650814308386</id>
<name>operator</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>操作员id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>operator</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2162396650814308387</id>
<name>department</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>部门id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>department</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2162396650814308388</id>
<name>org</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>组织id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>org</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2162396650814308389</id>
<name>product_cName</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product_cName</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2162396650814308390</id>
<name>open_hopeReceiveDate_begin</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>区间查询开始时间 : "2020-03-02"</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_hopeReceiveDate_begin</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2162396650814308391</id>
<name>open_hopeReceiveDate_end</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>区间查询结束时间 :"2020-03-02 23:59:59"</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_hopeReceiveDate_end</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2162396650814308375</id>
<name>simpleVOs</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<children>
<children>
<id>2162396650814308376</id>
<name>field</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308375</parentId>
<defParamId/>
<array>false</array>
<paramDesc>属性名(条件),子表加前缀storeProRecords.</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308377</id>
<name>op</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308375</parentId>
<defParamId/>
<array>false</array>
<paramDesc>条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>op</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308378</id>
<name>value1</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308375</parentId>
<defParamId/>
<array>false</array>
<paramDesc>值1(条件),单条件时仅使用这个配置</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value1</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308379</id>
<name>value2</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308375</parentId>
<defParamId/>
<array>false</array>
<paramDesc>值2(条件),单条件时此配置无效</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value2</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>simpleVOs</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>2162396650814308472</id>
<name>code</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735080</defParamId>
<array>false</array>
<paramDesc>返回码，调用成功时返回200</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2162396650814308473</id>
<name>message</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<defParamId>1856797255115735081</defParamId>
<array>false</array>
<paramDesc>调用失败时的错误信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2162396650814308392</id>
<name>data</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId/>
<children>
<children>
<id>2162396650814308393</id>
<name>sumRecordList</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308392</parentId>
<children>
<children>
<id>2162396650814308394</id>
<name>totalQuantity</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308393</parentId>
<defParamId>1856797255115735084</defParamId>
<array>false</array>
<paramDesc>总数量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308395</id>
<name>qty</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308393</parentId>
<defParamId>1856797255115735085</defParamId>
<array>false</array>
<paramDesc>数量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308396</id>
<name>totalPieces</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308393</parentId>
<defParamId>1856797255115735086</defParamId>
<array>false</array>
<paramDesc>总件</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308397</id>
<name>subQty</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308393</parentId>
<defParamId>1856797255115735087</defParamId>
<array>false</array>
<paramDesc>子表数量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1856797255115735083</defParamId>
<array>true</array>
<paramDesc>sum合计信息</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308465</id>
<name>pageIndex</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308392</parentId>
<defParamId>1856797255115735088</defParamId>
<array>false</array>
<paramDesc>当前页</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308466</id>
<name>pageSize</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308392</parentId>
<defParamId>1856797255115735089</defParamId>
<array>false</array>
<paramDesc>分页大小</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308467</id>
<name>pageCount</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308392</parentId>
<defParamId>1856797255115735090</defParamId>
<array>false</array>
<paramDesc>总页数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308468</id>
<name>beginPageIndex</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308392</parentId>
<defParamId>1856797255115735091</defParamId>
<array>false</array>
<paramDesc>开始页码</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308469</id>
<name>endPageIndex</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308392</parentId>
<defParamId>1856797255115735092</defParamId>
<array>false</array>
<paramDesc>结束页码</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308470</id>
<name>recordCount</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308392</parentId>
<defParamId>1856797255115735093</defParamId>
<array>false</array>
<paramDesc>总记录数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308471</id>
<name>pubts</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308392</parentId>
<defParamId>1856797255115735094</defParamId>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>格式：yyyy-MM-dd HH:mm:ss</example>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308398</id>
<name>recordList</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308392</parentId>
<children>
<children>
<id>2162396650814308399</id>
<name>factoryFiOrg</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735096</defParamId>
<array>false</array>
<paramDesc>完工组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308400</id>
<name>storeProRecords_product</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735097</defParamId>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308401</id>
<name>currency</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735098</defParamId>
<array>false</array>
<paramDesc>币种id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308402</id>
<name>storeProRecords_unit</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735099</defParamId>
<array>false</array>
<paramDesc>主计量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308403</id>
<name>storeProRecords_productsku</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735100</defParamId>
<array>false</array>
<paramDesc>物料sku</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308404</id>
<name>storeProRecords_stockUnitId</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735101</defParamId>
<array>false</array>
<paramDesc>库存单位id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308405</id>
<name>vouchdate</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735102</defParamId>
<array>false</array>
<paramDesc>单据日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308406</id>
<name>code</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735103</defParamId>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308407</id>
<name>department_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735104</defParamId>
<array>false</array>
<paramDesc>部门名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;department.name&quot;,&quot;cItemName&quot;:&quot;department_name&quot;,&quot;cCaption&quot;:&quot;部门&quot;,&quot;cShowCaption&quot;:&quot;部门&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_department&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;department&quot;:&quot;id&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecord&quot;,&quot;cControlType&quot;:&quot;TreeRefer&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308408</id>
<name>accountOrg</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735105</defParamId>
<array>false</array>
<paramDesc>会计主体id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;accountOrg&quot;,&quot;cItemName&quot;:&quot;accountOrg&quot;,&quot;cCaption&quot;:&quot;会计主体id&quot;,&quot;cShowCaption&quot;:&quot;会计主体id&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:true,&quot;cRefType&quot;:&quot;aa_orgtree&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;accountOrg_name&quot;:&quot;name&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecord&quot;,&quot;cControlType&quot;:&quot;Refer&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:{&quot;id&quot;:&quot;4f1ba370-624b-4de6-90d3-d916f2b3df9c&quot;,&quot;metaType&quot;:&quot;Class&quot;,&quot;name&quot;:&quot;FinanceOrg&quot;,&quot;uri&quot;:&quot;aa.org.FinanceOrg&quot;},&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308409</id>
<name>org_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735106</defParamId>
<array>false</array>
<paramDesc>库存组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;org.name&quot;,&quot;cItemName&quot;:&quot;org_name&quot;,&quot;cCaption&quot;:&quot;库存组织&quot;,&quot;cShowCaption&quot;:&quot;库存组织&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_orgstock&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;org&quot;:&quot;id&quot;,&quot;accountOrg&quot;:&quot;finorgid&quot;,&quot;accountOrg_name&quot;:&quot;finorg_name&quot;},&quot;cDataRule&quot;:&quot;\&quot;&lt;%u8c-config.option.singleOrg%&gt;\&quot;==\&quot;false\&quot; &amp;&amp; \&quot;&lt;%loginUser.storeId%&gt;\&quot;==\&quot;null\&quot;&quot;,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecord&quot;,&quot;cControlType&quot;:&quot;Refer&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308410</id>
<name>stockMgr_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735107</defParamId>
<array>false</array>
<paramDesc>库管员名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;stockMgr.name&quot;,&quot;cItemName&quot;:&quot;stockMgr_name&quot;,&quot;cCaption&quot;:&quot;库管员&quot;,&quot;cShowCaption&quot;:&quot;库管员&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_operator&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;stockMgr&quot;:&quot;id&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecord&quot;,&quot;cControlType&quot;:&quot;Refer&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308411</id>
<name>department</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735108</defParamId>
<array>false</array>
<paramDesc>部门id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;department&quot;,&quot;cItemName&quot;:&quot;department&quot;,&quot;cCaption&quot;:&quot;部门id&quot;,&quot;cShowCaption&quot;:&quot;部门id&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:true,&quot;cRefType&quot;:&quot;aa_department&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;department_name&quot;:&quot;name&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecord&quot;,&quot;cControlType&quot;:&quot;TreeRefer&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:{&quot;id&quot;:&quot;4d9e3282-6100-48ff-826c-3e50a36cc72e&quot;,&quot;metaType&quot;:&quot;Class&quot;,&quot;name&quot;:&quot;Department&quot;,&quot;uri&quot;:&quot;aa.dept.Department&quot;},&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308412</id>
<name>totalPieces</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735109</defParamId>
<array>false</array>
<paramDesc>整单件数(废弃)</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308413</id>
<name>org</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735110</defParamId>
<array>false</array>
<paramDesc>库存组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;org&quot;,&quot;cItemName&quot;:&quot;org&quot;,&quot;cCaption&quot;:&quot;库存组织id&quot;,&quot;cShowCaption&quot;:&quot;库存组织id&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:true,&quot;cRefType&quot;:&quot;aa_orgstock&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;org&quot;:&quot;id&quot;,&quot;accountOrg&quot;:&quot;finorgid&quot;,&quot;accountOrg_name&quot;:&quot;finorg_name&quot;},&quot;cDataRule&quot;:&quot;\&quot;&lt;%productcenter.option.singleOrg%&gt;\&quot;==\&quot;false\&quot;&quot;,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecord&quot;,&quot;cControlType&quot;:&quot;Column&quot;,&quot;refReturn&quot;:&quot;id&quot;,&quot;dataType&quot;:{&quot;id&quot;:&quot;c1135e08-c4bf-4499-90bf-67030d1f2653&quot;,&quot;metaType&quot;:&quot;Class&quot;,&quot;name&quot;:&quot;InventoryOrg&quot;,&quot;uri&quot;:&quot;aa.org.InventoryOrg&quot;},&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308414</id>
<name>stockMgr</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735111</defParamId>
<array>false</array>
<paramDesc>库管员IDid</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308415</id>
<name>store</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735112</defParamId>
<array>false</array>
<paramDesc>门店id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308416</id>
<name>store_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735113</defParamId>
<array>false</array>
<paramDesc>门店名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;store.name&quot;,&quot;cItemName&quot;:&quot;store_name&quot;,&quot;cCaption&quot;:&quot;门店&quot;,&quot;cShowCaption&quot;:&quot;门店&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_department&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:null,&quot;cDataRule&quot;:&quot;\&quot;&lt;%productcenter.option.isOpenURetail%&gt;\&quot;==\&quot;true\&quot;&quot;,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecord&quot;,&quot;cControlType&quot;:&quot;Column&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308417</id>
<name>warehouse</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735114</defParamId>
<array>false</array>
<paramDesc>仓库id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308418</id>
<name>warehouse_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735115</defParamId>
<array>false</array>
<paramDesc>仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;warehouse.name&quot;,&quot;cItemName&quot;:&quot;warehouse_name&quot;,&quot;cCaption&quot;:&quot;仓库&quot;,&quot;cShowCaption&quot;:&quot;仓库&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_warehouse&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:null,&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecord&quot;,&quot;cControlType&quot;:&quot;Column&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308419</id>
<name>bustype</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735116</defParamId>
<array>false</array>
<paramDesc>业务类型id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308420</id>
<name>accountOrg_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735117</defParamId>
<array>false</array>
<paramDesc>会计主体名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;accountOrg.name&quot;,&quot;cItemName&quot;:&quot;accountOrg_name&quot;,&quot;cCaption&quot;:&quot;会计主体&quot;,&quot;cShowCaption&quot;:&quot;会计主体&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_orgtree&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;accountOrg&quot;:&quot;id&quot;},&quot;cDataRule&quot;:&quot;\&quot;&lt;%u8c-config.option.singleOrg%&gt;\&quot;==\&quot;false\&quot;&quot;,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecord&quot;,&quot;cControlType&quot;:&quot;Refer&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308421</id>
<name>bustype_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735118</defParamId>
<array>false</array>
<paramDesc>交易类型名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;bustype.name&quot;,&quot;cItemName&quot;:&quot;bustype_name&quot;,&quot;cCaption&quot;:&quot;交易类型&quot;,&quot;cShowCaption&quot;:&quot;交易类型&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_user&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:null,&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecord&quot;,&quot;cControlType&quot;:&quot;Column&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308422</id>
<name>status</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735119</defParamId>
<array>false</array>
<paramDesc>单据状态, 0:未提交、1:已提交、</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308423</id>
<name>operator</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735120</defParamId>
<array>false</array>
<paramDesc>经办人id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308424</id>
<name>totalQuantity</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735121</defParamId>
<array>false</array>
<paramDesc>整单数量(废弃)</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308425</id>
<name>totalMaterial</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735122</defParamId>
<array>false</array>
<paramDesc>已材料出, true:是、false:否、(废弃)</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308426</id>
<name>creator</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735123</defParamId>
<array>false</array>
<paramDesc>创建人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308427</id>
<name>createTime</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735124</defParamId>
<array>false</array>
<paramDesc>创建时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308428</id>
<name>modifier</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735125</defParamId>
<array>false</array>
<paramDesc>最后修改人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308429</id>
<name>modifyTime</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735126</defParamId>
<array>false</array>
<paramDesc>最后修改时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308430</id>
<name>auditor</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735127</defParamId>
<array>false</array>
<paramDesc>提交人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308431</id>
<name>auditTime</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735128</defParamId>
<array>false</array>
<paramDesc>提交时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308432</id>
<name>memo</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735129</defParamId>
<array>false</array>
<paramDesc>备注</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308433</id>
<name>auditorId</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735130</defParamId>
<array>false</array>
<paramDesc>审批人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308434</id>
<name>creatorId</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735131</defParamId>
<array>false</array>
<paramDesc>创建人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308435</id>
<name>id</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735132</defParamId>
<array>false</array>
<paramDesc>主表id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308436</id>
<name>modifierId</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735133</defParamId>
<array>false</array>
<paramDesc>修改人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308437</id>
<name>pubts</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735134</defParamId>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308438</id>
<name>tplid</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735135</defParamId>
<array>false</array>
<paramDesc>模板id(废弃)</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308439</id>
<name>storeProRecords_id</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735136</defParamId>
<array>false</array>
<paramDesc>子表id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308440</id>
<name>product_cCode</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735137</defParamId>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;storeProRecords.product.cCode&quot;,&quot;cItemName&quot;:&quot;product_cCode&quot;,&quot;cCaption&quot;:&quot;物料编码&quot;,&quot;cShowCaption&quot;:&quot;物料编码&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_productsku&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;product&quot;:&quot;id&quot;,&quot;product_cName&quot;:&quot;product_cName&quot;,&quot;productsku&quot;:&quot;productskus_id&quot;,&quot;productsku_cCode&quot;:&quot;productskus_cCode&quot;,&quot;unit&quot;:&quot;oUnitId&quot;,&quot;unit_name&quot;:&quot;unitName&quot;,&quot;product_unitName&quot;:&quot;unitName&quot;,&quot;isBatchManage&quot;:&quot;productOfflineRetail_isBatchManage&quot;,&quot;isExpiryDateManage&quot;:&quot;productOfflineRetail_isExpiryDateManage&quot;,&quot;free@1@@10&quot;:&quot;retailskus!free@1@@10&quot;,&quot;skudefine@1@@60&quot;:&quot;productSkuProps!define@1@@60&quot;,&quot;prodefine@1@@30&quot;:&quot;productProps!define@1@@30&quot;,&quot;propertiesValue&quot;:&quot;propertiesValue&quot;,&quot;stockUnitId&quot;:&quot;stockUnit&quot;,&quot;stockUnit_name&quot;:&quot;stockUnit_name&quot;,&quot;product_cCode&quot;:&quot;cCode&quot;,&quot;productsku_cName&quot;:&quot;cName&quot;,&quot;invExchRate&quot;:&quot;stockRate&quot;,&quot;expireDateNo&quot;:&quot;productOfflineRetail_expireDateNo&quot;,&quot;expireDateUnit&quot;:&quot;productOfflineRetail_expireDateUnit&quot;,&quot;unit_Precision&quot;:&quot;unitPrecision&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecords&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:&quot;cCode&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308441</id>
<name>product_cName</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735138</defParamId>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;storeProRecords.product.cName&quot;,&quot;cItemName&quot;:&quot;product_cName&quot;,&quot;cCaption&quot;:&quot;物料名称&quot;,&quot;cShowCaption&quot;:&quot;物料名称&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_productsku&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;product&quot;:&quot;id&quot;,&quot;product_cName&quot;:&quot;product_cName&quot;,&quot;productsku&quot;:&quot;productskus_id&quot;,&quot;productsku_cCode&quot;:&quot;productskus_cCode&quot;,&quot;unit&quot;:&quot;oUnitId&quot;,&quot;unit_name&quot;:&quot;unitName&quot;,&quot;product_unitName&quot;:&quot;unitName&quot;,&quot;isBatchManage&quot;:&quot;productOfflineRetail_isBatchManage&quot;,&quot;isExpiryDateManage&quot;:&quot;productOfflineRetail_isExpiryDateManage&quot;,&quot;free@1@@10&quot;:&quot;retailskus!free@1@@10&quot;,&quot;skudefine@1@@60&quot;:&quot;productSkuProps!define@1@@60&quot;,&quot;prodefine@1@@30&quot;:&quot;productProps!define@1@@30&quot;,&quot;propertiesValue&quot;:&quot;propertiesValue&quot;,&quot;stockUnitId&quot;:&quot;stockUnit&quot;,&quot;stockUnit_name&quot;:&quot;stockUnit_name&quot;,&quot;product_cCode&quot;:&quot;cCode&quot;,&quot;productsku_cName&quot;:&quot;cName&quot;,&quot;invExchRate&quot;:&quot;stockRate&quot;,&quot;expireDateNo&quot;:&quot;productOfflineRetail_expireDateNo&quot;,&quot;expireDateUnit&quot;:&quot;productOfflineRetail_expireDateUnit&quot;,&quot;unit_Precision&quot;:&quot;unitPrecision&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecords&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:&quot;cName&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308443</id>
<name>productsku_cCode</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735140</defParamId>
<array>false</array>
<paramDesc>sku编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;storeProRecords.productsku.cCode&quot;,&quot;cItemName&quot;:&quot;productsku_cCode&quot;,&quot;cCaption&quot;:&quot;sku编码&quot;,&quot;cShowCaption&quot;:&quot;sku编码&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_productsku&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;product&quot;:&quot;id&quot;,&quot;product_cName&quot;:&quot;product_cName&quot;,&quot;productsku&quot;:&quot;productskus_id&quot;,&quot;productsku_cCode&quot;:&quot;productskus_cCode&quot;,&quot;unit&quot;:&quot;oUnitId&quot;,&quot;unit_name&quot;:&quot;unitName&quot;,&quot;product_unitName&quot;:&quot;unitName&quot;,&quot;isBatchManage&quot;:&quot;productOfflineRetail_isBatchManage&quot;,&quot;isExpiryDateManage&quot;:&quot;productOfflineRetail_isExpiryDateManage&quot;,&quot;free@1@@10&quot;:&quot;retailskus!free@1@@10&quot;,&quot;skudefine@1@@60&quot;:&quot;productSkuProps!define@1@@60&quot;,&quot;prodefine@1@@30&quot;:&quot;productProps!define@1@@30&quot;,&quot;propertiesValue&quot;:&quot;propertiesValue&quot;,&quot;stockUnitId&quot;:&quot;stockUnit&quot;,&quot;stockUnit_name&quot;:&quot;stockUnit_name&quot;,&quot;product_cCode&quot;:&quot;cCode&quot;,&quot;productsku_cName&quot;:&quot;cName&quot;,&quot;invExchRate&quot;:&quot;stockRate&quot;,&quot;expireDateNo&quot;:&quot;productOfflineRetail_expireDateNo&quot;,&quot;expireDateUnit&quot;:&quot;productOfflineRetail_expireDateUnit&quot;,&quot;unit_Precision&quot;:&quot;unitPrecision&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecords&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:&quot;productskus_cCode&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308444</id>
<name>productsku_cName</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735141</defParamId>
<array>false</array>
<paramDesc>sku名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;storeProRecords.productsku.skuName&quot;,&quot;cItemName&quot;:&quot;productsku_cName&quot;,&quot;cCaption&quot;:&quot;sku名称&quot;,&quot;cShowCaption&quot;:&quot;sku名称&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_productsku&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;product&quot;:&quot;id&quot;,&quot;product_cName&quot;:&quot;product_cName&quot;,&quot;productsku&quot;:&quot;productskus_id&quot;,&quot;productsku_cCode&quot;:&quot;productskus_cCode&quot;,&quot;unit&quot;:&quot;oUnitId&quot;,&quot;unit_name&quot;:&quot;unitName&quot;,&quot;product_unitName&quot;:&quot;unitName&quot;,&quot;isBatchManage&quot;:&quot;productOfflineRetail_isBatchManage&quot;,&quot;isExpiryDateManage&quot;:&quot;productOfflineRetail_isExpiryDateManage&quot;,&quot;free@1@@10&quot;:&quot;retailskus!free@1@@10&quot;,&quot;skudefine@1@@60&quot;:&quot;productSkuProps!define@1@@60&quot;,&quot;prodefine@1@@30&quot;:&quot;productProps!define@1@@30&quot;,&quot;propertiesValue&quot;:&quot;propertiesValue&quot;,&quot;stockUnitId&quot;:&quot;stockUnit&quot;,&quot;stockUnit_name&quot;:&quot;stockUnit_name&quot;,&quot;product_cCode&quot;:&quot;cCode&quot;,&quot;productsku_cName&quot;:&quot;cName&quot;,&quot;invExchRate&quot;:&quot;stockRate&quot;,&quot;expireDateNo&quot;:&quot;productOfflineRetail_expireDateNo&quot;,&quot;expireDateUnit&quot;:&quot;productOfflineRetail_expireDateUnit&quot;,&quot;unit_Precision&quot;:&quot;unitPrecision&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecords&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:&quot;cName&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308445</id>
<name>product_modelDescription</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735142</defParamId>
<array>false</array>
<paramDesc>规格型号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308446</id>
<name>qty</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735143</defParamId>
<array>false</array>
<paramDesc>数量</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308447</id>
<name>product_unitName</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735144</defParamId>
<array>false</array>
<paramDesc>计量单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308448</id>
<name>subQty</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735145</defParamId>
<array>false</array>
<paramDesc>件数</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308449</id>
<name>stockUnit_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735146</defParamId>
<array>false</array>
<paramDesc>库存单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;storeProRecords.stockUnitId.name&quot;,&quot;cItemName&quot;:&quot;stockUnit_name&quot;,&quot;cCaption&quot;:&quot;库存单位&quot;,&quot;cShowCaption&quot;:&quot;库存单位&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;productcenter.pc_productassitunitsref&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;stockUnitId&quot;:&quot;assistUnit&quot;,&quot;stockUnit_name&quot;:&quot;assistUnit_Name&quot;,&quot;invExchRate&quot;:&quot;mainUnitCount&quot;,&quot;unitExchangeType&quot;:&quot;unitExchangeType&quot;,&quot;stockUnitId_Precision&quot;:&quot;assistUnit_Precision&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecords&quot;,&quot;cControlType&quot;:&quot;Refer&quot;,&quot;refReturn&quot;:&quot;assistUnit_Name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308450</id>
<name>project_name</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735147</defParamId>
<array>false</array>
<paramDesc>项目名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;storeProRecords.project.name&quot;,&quot;cItemName&quot;:&quot;project_name&quot;,&quot;cCaption&quot;:&quot;项目名称&quot;,&quot;cShowCaption&quot;:&quot;项目名称&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;ucfbasedoc.bd_outer_projectcardMCref&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;project&quot;:&quot;id&quot;,&quot;project_name&quot;:&quot;name&quot;,&quot;project_code&quot;:&quot;code&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.storeprorecord.StoreProRecords&quot;,&quot;cControlType&quot;:&quot;Refer&quot;,&quot;refReturn&quot;:null,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308451</id>
<name>natUnitPrice</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735148</defParamId>
<array>false</array>
<paramDesc>单价</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308452</id>
<name>natMoney</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735149</defParamId>
<array>false</array>
<paramDesc>金额</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308453</id>
<name>natCurrency_priceDigit</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735150</defParamId>
<array>false</array>
<paramDesc>币种单价精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308454</id>
<name>natCurrency_moneyDigit</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735151</defParamId>
<array>false</array>
<paramDesc>币种金额精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308455</id>
<name>unit_Precision</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735152</defParamId>
<array>false</array>
<paramDesc>主计量精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308456</id>
<name>stockUnitId_Precision</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735153</defParamId>
<array>false</array>
<paramDesc>库存单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308459</id>
<name>out_sys_id</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735156</defParamId>
<array>false</array>
<paramDesc>外部来源线索</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308460</id>
<name>out_sys_code</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735157</defParamId>
<array>false</array>
<paramDesc>外部来源编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308461</id>
<name>out_sys_version</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735158</defParamId>
<array>false</array>
<paramDesc>外部来源版本</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>62</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308462</id>
<name>out_sys_type</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735159</defParamId>
<array>false</array>
<paramDesc>外部来源类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>63</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308463</id>
<name>out_sys_rowno</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735160</defParamId>
<array>false</array>
<paramDesc>外部来源行号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>64</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2162396650814308464</id>
<name>out_sys_lineid</name>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<parentId>2162396650814308398</parentId>
<defParamId>1856797255115735161</defParamId>
<array>false</array>
<paramDesc>外部来源行</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>65</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1856797255115735095</defParamId>
<array>true</array>
<paramDesc>返回结果对象</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag>respData</paramTag>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1856797255115735082</defParamId>
<array>false</array>
<paramDesc>调用成功时的返回数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2162396659404242948</id>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<content>{ "code": "", "message": "", "data": { "sumRecordList": [ { "totalQuantity": "", "qty": "", "totalPieces": "", "subQty": "" } ], "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "pubts": "格式：yyyy-MM-dd HH:mm:ss", "recordList": [ { "factoryFiOrg": "", "storeProRecords_product": "", "currency": "", "storeProRecords_unit": "", "storeProRecords_productsku": "", "storeProRecords_stockUnitId": "", "vouchdate": "", "code": "", "department_name": "", "accountOrg": "", "org_name": "", "stockMgr_name": "", "department": "", "totalPieces": "", "org": "", "stockMgr": "", "store": "", "store_name": "", "warehouse": "", "warehouse_name": "", "bustype": "", "accountOrg_name": "", "bustype_name": "", "status": "", "operator": "", "totalQuantity": 0, "totalMaterial": "", "creator": "", "createTime": "", "modifier": "", "modifyTime": "", "auditor": "", "auditTime": "", "memo": "", "auditorId": "", "creatorId": "", "id": "", "modifierId": "", "pubts": "", "tplid": "", "storeProRecords_id": "", "product_cCode": "", "product_cName": "", "storeProRecordsCharacteristics": 0, "productsku_cCode": "", "productsku_cName": "", "product_modelDescription": "", "qty": 0, "product_unitName": "", "subQty": 0, "stockUnit_name": "", "project_name": "", "natUnitPrice": 0, "natMoney": 0, "natCurrency_priceDigit": 0, "natCurrency_moneyDigit": 0, "unit_Precision": 0, "stockUnitId_Precision": 0, "storeProRecordsDefineCharacter": 0, "storeProRecordDefineCharacter": 0, "out_sys_id": "", "out_sys_code": "", "out_sys_version": "", "out_sys_type": "", "out_sys_rowno": "", "out_sys_lineid": "" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-12-23 14:41:43.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:43.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2162396659404242949</id>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<content>{ "code": "999", "message": "No enum constant org.imeta.core.base.ConditionOperator.2" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-12-23 14:41:43.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:43.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2162396659404242948</id>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<content>{ "code": "", "message": "", "data": { "sumRecordList": [ { "totalQuantity": "", "qty": "", "totalPieces": "", "subQty": "" } ], "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "pubts": "格式：yyyy-MM-dd HH:mm:ss", "recordList": [ { "factoryFiOrg": "", "storeProRecords_product": "", "currency": "", "storeProRecords_unit": "", "storeProRecords_productsku": "", "storeProRecords_stockUnitId": "", "vouchdate": "", "code": "", "department_name": "", "accountOrg": "", "org_name": "", "stockMgr_name": "", "department": "", "totalPieces": "", "org": "", "stockMgr": "", "store": "", "store_name": "", "warehouse": "", "warehouse_name": "", "bustype": "", "accountOrg_name": "", "bustype_name": "", "status": "", "operator": "", "totalQuantity": 0, "totalMaterial": "", "creator": "", "createTime": "", "modifier": "", "modifyTime": "", "auditor": "", "auditTime": "", "memo": "", "auditorId": "", "creatorId": "", "id": "", "modifierId": "", "pubts": "", "tplid": "", "storeProRecords_id": "", "product_cCode": "", "product_cName": "", "storeProRecordsCharacteristics": 0, "productsku_cCode": "", "productsku_cName": "", "product_modelDescription": "", "qty": 0, "product_unitName": "", "subQty": 0, "stockUnit_name": "", "project_name": "", "natUnitPrice": 0, "natMoney": 0, "natCurrency_priceDigit": 0, "natCurrency_moneyDigit": 0, "unit_Precision": 0, "stockUnitId_Precision": 0, "storeProRecordsDefineCharacter": 0, "storeProRecordDefineCharacter": 0, "out_sys_id": "", "out_sys_code": "", "out_sys_version": "", "out_sys_type": "", "out_sys_rowno": "", "out_sys_lineid": "" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-12-23 14:41:43.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:43.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2162396659404242949</id>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<content>{ "code": "999", "message": "No enum constant org.imeta.core.base.ConditionOperator.2" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-12-23 14:41:43.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:43.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>2162396659404242948</id>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<content>{ "code": "", "message": "", "data": { "sumRecordList": [ { "totalQuantity": "", "qty": "", "totalPieces": "", "subQty": "" } ], "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "pubts": "格式：yyyy-MM-dd HH:mm:ss", "recordList": [ { "factoryFiOrg": "", "storeProRecords_product": "", "currency": "", "storeProRecords_unit": "", "storeProRecords_productsku": "", "storeProRecords_stockUnitId": "", "vouchdate": "", "code": "", "department_name": "", "accountOrg": "", "org_name": "", "stockMgr_name": "", "department": "", "totalPieces": "", "org": "", "stockMgr": "", "store": "", "store_name": "", "warehouse": "", "warehouse_name": "", "bustype": "", "accountOrg_name": "", "bustype_name": "", "status": "", "operator": "", "totalQuantity": 0, "totalMaterial": "", "creator": "", "createTime": "", "modifier": "", "modifyTime": "", "auditor": "", "auditTime": "", "memo": "", "auditorId": "", "creatorId": "", "id": "", "modifierId": "", "pubts": "", "tplid": "", "storeProRecords_id": "", "product_cCode": "", "product_cName": "", "storeProRecordsCharacteristics": 0, "productsku_cCode": "", "productsku_cName": "", "product_modelDescription": "", "qty": 0, "product_unitName": "", "subQty": 0, "stockUnit_name": "", "project_name": "", "natUnitPrice": 0, "natMoney": 0, "natCurrency_priceDigit": 0, "natCurrency_moneyDigit": 0, "unit_Precision": 0, "stockUnitId_Precision": 0, "storeProRecordsDefineCharacter": 0, "storeProRecordDefineCharacter": 0, "out_sys_id": "", "out_sys_code": "", "out_sys_version": "", "out_sys_type": "", "out_sys_rowno": "", "out_sys_lineid": "" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-12-23 14:41:43.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:43.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>2162396659404242949</id>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<content>{ "code": "999", "message": "No enum constant org.imeta.core.base.ConditionOperator.2" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-12-23 14:41:43.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:43.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS>
<errorCodeDTOS>
<id>2162396650814308476</id>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<errorCode>999</errorCode>
<errorMessage>入参错误等异常</errorMessage>
<errorType>API</errorType>
<errorcodeDesc>根据返回错误信息做出相应调整</errorcodeDesc>
<gmtCreate>2024-12-23 14:41:42.000</gmtCreate>
<gmtUpdate>2024-12-23 14:41:42.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<defErrorId>1856797255115735244</defErrorId>
<ytenantId/>
<displayCodeId/>
</errorCodeDTOS>
</errorCodeDTOS>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>友户通token认证业务扩展插件</name>
<configurable>false</configurable>
<description>YonsuitBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>edc735d69b654129b0da4c38f2d8877f</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin>
<id>w181ed01-1e9b-4350-b994-71a66f062522</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code>resultParse</code>
<name>UCG标准返回值解析插件</name>
<configurable>false</configurable>
<description>符合UCG标准的返回值会自动解析，不符合的会自动略过</description>
<pluginType>resultParse</pluginType>
<pluginTypeName>返回值解析插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.result.UCGResultParsePlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>true</visible>
<gmtCreate>2019-08-19 00:00:00</gmtCreate>
<gmtUpdate/>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>edc735d69b654129b0da4c38f2d8877f</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</resultParsePlugin>
<mapReturnPluginConfig/>
<billNo>st_storeprorecordlist</billNo>
<domain>ustock</domain>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser/>
<createUserName/>
<approvalStatus>1</approvalStatus>
<publishTime>2024-12-23 14:42:17</publishTime>
<pathJoin>true</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout/>
<customUrl>/storeprorecord/list</customUrl>
<fixedUrl>/yonbip/scm</fixedUrl>
<apiCode>edc735d69b654129b0da4c38f2d8877f</apiCode>
<tokenCheckType>0</tokenCheckType>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL/>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>36a8b72b-d965-404d-a02d-66ff4a7afeb3</updateUserId>
<updateUserName>昵称-王章宇</updateUserName>
<paramIsForce/>
<userIDPassthrough>false</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.yonbip-scm-stock</microServiceCode>
<applicationCode>yonbip-scm-stock</applicationCode>
<privacyCategory>1</privacyCategory>
<privacyLevel>4</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType>0</serviceType>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType/>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>false</arrayParam>
<fileSize/>
<cc>true</cc>
<paramTransferMode>2</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId>1856797255115735044</apiDefId>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>1</paramExtRequest>
<paramExtResponse>1</paramExtResponse>
<paramExtInExtendKey>1</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene>1</integrationScene>
<apiType>3</apiType>
<paramMark>
<request>
<request>
<id>2162396659404242950</id>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<paramCode>pageIndex</paramCode>
<refCode>pageIndex</refCode>
<paramPosition>request</paramPosition>
<ytenantId>0</ytenantId>
<creator>36a8b72b-d965-404d-a02d-66ff4a7afeb3</creator>
<createTime>2024-12-23T06:41:43.000+00:00</createTime>
<modifier>00001951-7ca3-47ac-a462-d5a66e3e6724</modifier>
<pubts>2024-12-23T06:41:43.000+00:00</pubts>
<modifyTime>2024-12-23T06:41:43.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</request>
<request>
<id>2162396659404242951</id>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<paramCode>pageSize</paramCode>
<refCode>pageSize</refCode>
<paramPosition>request</paramPosition>
<ytenantId>0</ytenantId>
<creator>36a8b72b-d965-404d-a02d-66ff4a7afeb3</creator>
<createTime>2024-12-23T06:41:43.000+00:00</createTime>
<modifier>00001951-7ca3-47ac-a462-d5a66e3e6724</modifier>
<pubts>2024-12-23T06:41:43.000+00:00</pubts>
<modifyTime>2024-12-23T06:41:43.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</request>
<request>
<id>2162396659404242952</id>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<paramCode>lastUpdateTime</paramCode>
<refCode/>
<paramPosition>request</paramPosition>
<ytenantId>0</ytenantId>
<creator>36a8b72b-d965-404d-a02d-66ff4a7afeb3</creator>
<createTime>2024-12-23T06:41:43.000+00:00</createTime>
<modifier>00001951-7ca3-47ac-a462-d5a66e3e6724</modifier>
<pubts>2024-12-23T06:41:43.000+00:00</pubts>
<modifyTime>2024-12-23T06:41:43.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</request>
<request>
<id>2162396659404242953</id>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<paramCode>thisSyncTime</paramCode>
<refCode/>
<paramPosition>request</paramPosition>
<ytenantId>0</ytenantId>
<creator>36a8b72b-d965-404d-a02d-66ff4a7afeb3</creator>
<createTime>2024-12-23T06:41:43.000+00:00</createTime>
<modifier>00001951-7ca3-47ac-a462-d5a66e3e6724</modifier>
<pubts>2024-12-23T06:41:43.000+00:00</pubts>
<modifyTime>2024-12-23T06:41:43.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</request>
</request>
<response>
<response>
<id>2162396659404242954</id>
<apiId>edc735d69b654129b0da4c38f2d8877f</apiId>
<paramCode>respData</paramCode>
<refCode>data.recordList</refCode>
<paramPosition>response</paramPosition>
<ytenantId>0</ytenantId>
<creator>36a8b72b-d965-404d-a02d-66ff4a7afeb3</creator>
<createTime>2024-12-23T06:41:43.000+00:00</createTime>
<modifier>00001951-7ca3-47ac-a462-d5a66e3e6724</modifier>
<pubts>2024-12-23T06:41:43.000+00:00</pubts>
<modifyTime>2024-12-23T06:41:43.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</response>
</response>
</paramMark>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>true</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>true</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>1</chargeStatus>
<beforeSpeed>40</beforeSpeed>
<afterSpeed>80</afterSpeed>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath>data.recordList</respDataRefPath>
<pubHistory/>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode/>
<multiVersion>0</multiVersion>
<apiTag/>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>2108770660671029249</id>
<name>用友YonBIP</name>
<type>integrateSys</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>SCC</id>
<name>供应链云</name>
<type>1</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MM</id>
<name>采购供应</name>
<type>2</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>ST</id>
<name>库存管理</name>
<type>3</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>ustock.st_storeprorecord</id>
<name>产品入库</name>
<type>4</type>
<sort>0</sort>
<enable>0</enable>
<children/>
<parentId/>
<productId/>
<code>ustock.st_storeprorecord</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>ST</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MM</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>SCC</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>current_yonbip_default_sys</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<isOrigin>0</isOrigin>
<hasChildren>0</hasChildren>
<order>0</order>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>72113971-ae4c-4188-bc55-44b6173f4e0b</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:50:17</gmtCreate>
<gmtUpdate>2025-07-26 17:50:17</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>b946709d-f4d9-4a43-a551-f55beee7f3d5</id>
<name>XXX0111</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类项</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:50:17</gmtCreate>
<gmtUpdate>2025-07-26 17:50:17</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:50:17</gmtCreate>
<gmtUpdate>2025-07-26 17:50:17</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>913a0595-0a10-4344-a3ce-dbd456d2d199</id>
<name>XS11</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类号test</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:50:29</gmtCreate>
<gmtUpdate>2025-07-26 17:50:29</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:50:29</gmtCreate>
<gmtUpdate>2025-07-26 17:50:29</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:50:39</gmtCreate>
<gmtUpdate>2025-07-26 17:50:39</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>