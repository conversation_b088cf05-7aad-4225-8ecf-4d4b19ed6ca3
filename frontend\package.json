{"name": "ys-api-frontend-tests", "version": "1.0.0", "description": "YS-API前端组件单元测试", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "devDependencies": {"jest": "^29.0.0", "jest-environment-jsdom": "^30.0.5", "jsdom": "^22.0.0"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "collectCoverageFrom": ["**/*.js", "!**/node_modules/**", "!**/tests/**"], "coverageReporters": ["text", "lcov", "html"]}}