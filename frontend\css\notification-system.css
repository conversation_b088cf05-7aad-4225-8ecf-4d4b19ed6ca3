/**
 * 通知和消息系统样式
 * 支持成功、错误、警告等不同类型的消息显示
 * 实现消息的视觉效果和动画，提升用户体验
 * 支持响应式设计和无障碍访问
 */

/* 通知容器 */
.notification-container {
  position: fixed;
  z-index: 10000;
  pointer-events: none;
  max-width: 420px;
  width: 100%;
}

/* 位置变体 */
.notification-top-right {
  top: 20px;
  right: 20px;
}

.notification-top-left {
  top: 20px;
  left: 20px;
}

.notification-bottom-right {
  bottom: 20px;
  right: 20px;
}

.notification-bottom-left {
  bottom: 20px;
  left: 20px;
}

.notification-top-center {
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.notification-bottom-center {
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

/* 通知项 */
.notification {
  position: relative;
  margin-bottom: 12px;
  padding: 16px 20px;
  border-radius: 12px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  pointer-events: auto;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateX(100%) scale(0.9);
  max-width: 100%;
  word-wrap: break-word;
  overflow: hidden;
}

/* 显示状态 */
.notification.show {
  opacity: 1;
  transform: translateX(0) scale(1);
}

/* 隐藏状态 */
.notification.hide {
  opacity: 0;
  transform: translateX(100%) scale(0.9);
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
  max-height: 0;
}

/* 悬停效果 */
.notification:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

/* 焦点样式 */
.notification:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* 通知类型样式 */
.notification-success {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.95) 0%, rgba(34, 139, 34, 0.95) 100%);
  border-color: rgba(40, 167, 69, 0.3);
}

.notification-error {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.95) 0%, rgba(178, 34, 34, 0.95) 100%);
  border-color: rgba(220, 53, 69, 0.3);
}

.notification-warning {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.95) 0%, rgba(255, 140, 0, 0.95) 100%);
  border-color: rgba(255, 193, 7, 0.3);
  color: #212529;
}

.notification-info {
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.95) 0%, rgba(102, 126, 234, 0.95) 100%);
  border-color: rgba(0, 123, 255, 0.3);
}

/* 通知内容 */
.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 8px;
}

.notification-icon {
  font-size: 18px;
  line-height: 1;
  flex-shrink: 0;
  margin-top: 1px;
}

.notification-message {
  flex: 1;
  line-height: 1.4;
  font-weight: 500;
}

.notification-timestamp {
  font-size: 11px;
  opacity: 0.8;
  font-weight: 400;
  margin-top: 4px;
  font-family: 'Courier New', monospace;
}

/* 操作按钮区域 */
.notification-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.notification-action-button {
  padding: 6px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: inherit;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(5px);
}

.notification-action-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.notification-action-button:active {
  transform: translateY(0);
}

/* 关闭按钮 */
.notification-close {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: inherit;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.notification-close:hover {
  background: rgba(255, 255, 255, 0.2);
  opacity: 1;
  transform: scale(1.1);
}

.notification-close:active {
  transform: scale(0.95);
}

/* 进度条 */
.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0 0 12px 12px;
  overflow: hidden;
}

.notification-progress-bar {
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  width: 0%;
  transition: width 0.1s linear;
  border-radius: 0 0 12px 12px;
  position: relative;
}

.notification-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: progressShimmer 2s ease-in-out infinite;
}

/* 进度条动画 */
@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 通知入场动画变体 */
.notification-container.notification-top-right .notification,
.notification-container.notification-bottom-right .notification {
  transform: translateX(100%) scale(0.9);
}

.notification-container.notification-top-left .notification,
.notification-container.notification-bottom-left .notification {
  transform: translateX(-100%) scale(0.9);
}

.notification-container.notification-top-center .notification,
.notification-container.notification-bottom-center .notification {
  transform: translateY(-100%) scale(0.9);
}

.notification-container.notification-top-center .notification.show,
.notification-container.notification-bottom-center .notification.show {
  transform: translateY(0) scale(1);
}

.notification-container.notification-top-center .notification.hide,
.notification-container.notification-bottom-center .notification.hide {
  transform: translateY(-100%) scale(0.9);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-container {
    max-width: calc(100vw - 32px);
    left: 16px !important;
    right: 16px !important;
    transform: none !important;
  }
  
  .notification {
    padding: 14px 16px;
    font-size: 13px;
  }
  
  .notification-content {
    gap: 10px;
  }
  
  .notification-icon {
    font-size: 16px;
  }
  
  .notification-actions {
    margin-top: 10px;
  }
  
  .notification-action-button {
    padding: 5px 10px;
    font-size: 11px;
  }
  
  .notification-close {
    width: 20px;
    height: 20px;
    font-size: 14px;
    top: 6px;
    right: 6px;
  }
}

@media (max-width: 480px) {
  .notification-container {
    max-width: calc(100vw - 16px);
    left: 8px !important;
    right: 8px !important;
  }
  
  .notification {
    padding: 12px 14px;
    font-size: 12px;
  }
  
  .notification-actions {
    flex-direction: column;
  }
  
  .notification-action-button {
    width: 100%;
    justify-content: center;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .notification {
    border-width: 2px;
    border-style: solid;
  }
  
  .notification-success {
    border-color: #28a745;
  }
  
  .notification-error {
    border-color: #dc3545;
  }
  
  .notification-warning {
    border-color: #ffc107;
  }
  
  .notification-info {
    border-color: #007bff;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .notification {
    transition: opacity 0.2s ease;
  }
  
  .notification:hover {
    transform: none;
  }
  
  .notification-progress-bar::after {
    animation: none;
  }
  
  @keyframes progressShimmer {
    0%, 100% {
      transform: translateX(0);
    }
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .notification {
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .notification-warning {
    color: #000;
  }
  
  .notification-close {
    background: rgba(255, 255, 255, 0.05);
  }
  
  .notification-close:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .notification-action-button {
    border-color: rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
  }
  
  .notification-action-button:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

/* 打印样式 */
@media print {
  .notification-container {
    display: none;
  }
}

/* 特殊效果 */
.notification.notification-pulse {
  animation: notificationPulse 2s ease-in-out infinite;
}

@keyframes notificationPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

.notification.notification-shake {
  animation: notificationShake 0.5s ease-in-out;
}

@keyframes notificationShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* 通知组合效果 */
.notification-container:has(.notification-error) .notification-success {
  opacity: 0.7;
}

.notification-container:has(.notification-error:hover) .notification:not(.notification-error) {
  opacity: 0.5;
}

/* 无障碍访问增强 */
.notification[aria-live="assertive"] {
  /* 重要通知的特殊样式 */
  border-width: 2px;
}

.notification:focus-visible {
  outline: 3px solid rgba(255, 255, 255, 0.7);
  outline-offset: 2px;
}

/* 通知计数器 */
.notification-container::before {
  content: attr(data-count);
  position: absolute;
  top: -10px;
  right: -10px;
  background: rgba(220, 53, 69, 0.9);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 11px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s ease;
}

.notification-container[data-count]:not([data-count="0"])::before {
  opacity: 1;
  transform: scale(1);
}

/* 加载状态 */
.notification.notification-loading {
  position: relative;
  overflow: hidden;
}

.notification.notification-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
  animation: notificationLoading 1.5s ease-in-out infinite;
}

@keyframes notificationLoading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}