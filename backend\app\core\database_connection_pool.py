import asyncio
import time
from contextlib import asynccontextmanager
from dataclasses import dataclass

import pyodbc
import structlog

from .config import settings

"""
YS-API V3.0 优化的数据库连接池服务
实现连接复用、健康检查、智能连接管理
从原有数据库模块优化而来
"""


logger = structlog.get_logger()


@dataclass
class ConnectionInfo:
    """连接信息"""

    connection: pyodbc.Connection
    created_at: float
    last_used: float
    use_count: int
    is_healthy: bool
    last_health_check: float


class DatabaseConnectionPool:
    """优化的数据库连接池服务"""

    def __init__(
        self,
        min_connections: int = 5,
        max_connections: int = 20,
        connection_timeout: int = 30,
        health_check_interval: int = 300,
        max_idle_time: int = 600,
    ):
        """
        初始化连接池

        Args:
            min_connections: 最小连接数
            max_connections: 最大连接数
            connection_timeout: 连接超时时间（秒）
            health_check_interval: 健康检查间隔（秒）
            max_idle_time: 最大空闲时间（秒）
        """
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        self.health_check_interval = health_check_interval
        self.max_idle_time = max_idle_time

        # 连接池状态
        self.connections: List[ConnectionInfo] = []
        self.available_connections: List[ConnectionInfo] = []
        self.in_use_connections: List[ConnectionInfo] = []

        # 统计信息
        self.total_connections_created = 0
        self.total_connections_closed = 0
        self.total_requests = 0
        self.total_wait_time = 0.0

        # 控制锁
        self._lock = asyncio.Lock()
        self._shutdown = False

        # 健康检查任务
        self._health_check_task = None

        # 连接字符串
        self.connection_string = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={settings.DATABASE_SERVER};"
            f"DATABASE={settings.DATABASE_NAME};"
            f"UID={settings.DATABASE_USER};"
            f"PWD={settings.DATABASE_PASSWORD};"
            f"TrustServerCertificate=yes;"
        )

        logger.info(
            "数据库连接池初始化完成",
            min_connections=min_connections,
            max_connections=max_connections,
            database=settings.DATABASE_NAME,
        )

    async def start(self):
        """启动连接池"""
        try:
            # 创建初始连接
            await self._create_initial_connections()

            # 启动健康检查任务
            self._health_check_task = asyncio.create_task(
                self._health_check_loop())

            logger.info(
                "数据库连接池启动成功", initial_connections=len(self.connections)
            )

        except Exception:
            logger.error("数据库连接池启动失败", error=str(e))
            raise

    async def stop(self):
        """停止连接池"""
        try:
            self._shutdown = True

            # 停止健康检查任务
            if self._health_check_task:
                self._health_check_task.cancel()
                try:
                    await self._health_check_task
                except asyncio.CancelledError:
                    pass

            # 关闭所有连接
            await self._close_all_connections()

            logger.info(
                "数据库连接池已停止",
                total_created=self.total_connections_created,
                total_closed=self.total_connections_closed,
            )

        except Exception:
            logger.error("停止数据库连接池失败", error=str(e))

    @asynccontextmanager
    async def get_connection(self):
        """获取数据库连接（异步上下文管理器）"""
        connection_info = None
        start_time = time.time()

        try:
            self.total_requests += 1

            # 获取连接
            connection_info = await self._get_connection()

            # 更新统计信息
            wait_time = time.time() - start_time
            self.total_wait_time += wait_time

            yield connection_info.connection

        except Exception:
            logger.error("获取数据库连接失败", error=str(e))
            raise
        finally:
            if connection_info:
                await self._return_connection(connection_info)

    async def execute_query(
        self, sql: str, params: Optional[Dict] = None
    ) -> List[Dict]:
        """
        执行查询

        Args:
            sql: SQL语句
            params: 参数

        Returns:
            List[Dict]: 查询结果
        """
        async with self.get_connection() as conn:
            try:
                cursor = conn.cursor()

                if params:
                    cursor.execute(sql, params)
                else:
                    cursor.execute(sql)

                # 获取列名
                columns = [column[0] for column in cursor.description]

                # 获取结果
                results = []
                for row in cursor.fetchall():
                    results.append(dict(zip(columns, row)))

                cursor.close()
                return results

            except Exception:
                logger.error("执行查询失败", sql=sql, error=str(e))
                raise

    async def execute_batch(self, sql: str, params_list: List[Dict]) -> int:
        """
        批量执行SQL

        Args:
            sql: SQL语句
            params_list: 参数列表

        Returns:
            int: 影响的行数
        """
        async with self.get_connection() as conn:
            try:
                cursor = conn.cursor()
                total_affected = 0

                for params in params_list:
                    cursor.execute(sql, params)
                    total_affected += cursor.rowcount

                conn.commit()
                cursor.close()

                return total_affected

            except Exception:
                logger.error("批量执行SQL失败", sql=sql, error=str(e))
                conn.rollback()
                raise

    async def _create_initial_connections(self):
        """创建初始连接"""
        try:
            for _ in range(self.min_connections):
                await self._create_connection()

            logger.info("初始连接创建完成", count=self.min_connections)

        except Exception:
            logger.error("创建初始连接失败", error=str(e))
            raise

    async def _create_connection(self) -> ConnectionInfo:
        """创建新连接"""
        try:
            # 在线程池中创建连接（避免阻塞事件循环）
            loop = asyncio.get_event_loop()
            connection = await loop.run_in_executor(
                None,
                pyodbc.connect,
                self.connection_string,
                timeout=self.connection_timeout,
            )

            # 设置连接属性
            connection.autocommit = False

            # 创建连接信息
            now = time.time()
            connection_info = ConnectionInfo(
                connection=connection,
                created_at=now,
                last_used=now,
                use_count=0,
                is_healthy=True,
                last_health_check=now,
            )

            self.connections.append(connection_info)
            self.available_connections.append(connection_info)
            self.total_connections_created += 1

            logger.debug("新连接创建成功", total_connections=len(self.connections))

            return connection_info

        except Exception:
            logger.error("创建连接失败", error=str(e))
            raise

    async def _get_connection(self) -> ConnectionInfo:
        """获取可用连接"""
        async with self._lock:
            # 检查可用连接
            if self.available_connections:
                connection_info = self.available_connections.pop(0)
                self.in_use_connections.append(connection_info)

                # 更新使用信息
                connection_info.last_used = time.time()
                connection_info.use_count += 1

                return connection_info

            # 检查是否可以创建新连接
            if len(self.connections) < self.max_connections:
                connection_info = await self._create_connection()
                self.in_use_connections.append(connection_info)
                return connection_info

            # 等待可用连接
            logger.warning(
                "连接池已满，等待可用连接",
                total_connections=len(self.connections),
                in_use=len(self.in_use_connections),
            )

            # 等待直到有连接可用
            while (
                not self.available_connections
                and len(self.connections) >= self.max_connections
            ):
                await asyncio.sleep(0.1)

            # 重新尝试获取连接
            return await self._get_connection()

    async def _return_connection(self, connection_info: ConnectionInfo):
        """归还连接"""
        async with self._lock:
            try:
                # 从使用中移除
                if connection_info in self.in_use_connections:
                    self.in_use_connections.remove(connection_info)

                # 检查连接是否健康
                if connection_info.is_healthy:
                    # 放回可用连接池
                    self.available_connections.append(connection_info)
                else:
                    # 关闭不健康的连接
                    await self._close_connection(connection_info)

                    # 如果需要，创建新连接
                    if len(self.connections) < self.min_connections:
                        await self._create_connection()

            except Exception:
                logger.error("归还连接失败", error=str(e))

    async def _close_connection(self, connection_info: ConnectionInfo):
        """关闭连接"""
        try:
            if connection_info.connection:
                connection_info.connection.close()

            if connection_info in self.connections:
                self.connections.remove(connection_info)

            if connection_info in self.available_connections:
                self.available_connections.remove(connection_info)

            if connection_info in self.in_use_connections:
                self.in_use_connections.remove(connection_info)

            self.total_connections_closed += 1

            logger.debug("连接已关闭", total_connections=len(self.connections))

        except Exception:
            logger.error("关闭连接失败", error=str(e))

    async def _close_all_connections(self):
        """关闭所有连接"""
        try:
            for connection_info in self.connections[:]:
                await self._close_connection(connection_info)

            logger.info("所有连接已关闭")

        except Exception:
            logger.error("关闭所有连接失败", error=str(e))

    async def _health_check_loop(self):
        """健康检查循环"""
        while not self._shutdown:
            try:
                await self._perform_health_check()
                await asyncio.sleep(self.health_check_interval)

            except asyncio.CancelledError:
                break
            except Exception:
                logger.error("健康检查失败", error=str(e))
                await asyncio.sleep(60)  # 出错后等待1分钟再重试

    async def _perform_health_check(self):
        """执行健康检查"""
        try:
            now = time.time()
            unhealthy_connections = []

            # 检查所有连接
            for connection_info in self.connections:
                # 检查连接是否超时
                if now - connection_info.last_used > self.max_idle_time:
                    logger.debug(
                        "连接空闲超时，标记为不健康",
                        idle_time=now - connection_info.last_used,
                    )
                    connection_info.is_healthy = False

                # 检查连接是否真的健康
                if now - connection_info.last_health_check > self.health_check_interval:
                    if await self._test_connection(connection_info):
                        connection_info.is_healthy = True
                        connection_info.last_health_check = now
                    else:
                        connection_info.is_healthy = False
                        unhealthy_connections.append(connection_info)

            # 关闭不健康的连接
            for connection_info in unhealthy_connections:
                await self._close_connection(connection_info)

            # 确保最小连接数
            while len(self.connections) < self.min_connections:
                await self._create_connection()

            # 清理过多空闲连接
            await self._cleanup_idle_connections()

            logger.debug(
                "健康检查完成",
                total_connections=len(self.connections),
                available=len(self.available_connections),
                in_use=len(self.in_use_connections),
                unhealthy_closed=len(unhealthy_connections),
            )

        except Exception:
            logger.error("执行健康检查失败", error=str(e))

    async def _test_connection(self, connection_info: ConnectionInfo) -> bool:
        """测试连接是否健康"""
        try:
            loop = asyncio.get_event_loop()
            cursor = await loop.run_in_executor(None, connection_info.connection.cursor)
            await loop.run_in_executor(None, cursor.execute, "SELECT 1")
            await loop.run_in_executor(None, cursor.close)
            return True

        except Exception:
            logger.debug("连接健康检查失败", error=str(e))
            return False

    async def _cleanup_idle_connections(self):
        """清理空闲连接"""
        try:
            now = time.time()
            idle_connections = []

            # 找出空闲连接
            for connection_info in self.available_connections:
                if now - connection_info.last_used > self.max_idle_time:
                    idle_connections.append(connection_info)

            # 保留最小连接数，关闭多余的空闲连接
            excess_count = len(idle_connections) - max(
                0, self.min_connections - len(self.in_use_connections)
            )

            if excess_count > 0:
                for connection_info in idle_connections[:excess_count]:
                    await self._close_connection(connection_info)

                logger.debug("清理空闲连接", closed_count=excess_count)

        except Exception:
            logger.error("清理空闲连接失败", error=str(e))

    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        try:
            total_connections = len(self.connections)
            available_connections = len(self.available_connections)
            in_use_connections = len(self.in_use_connections)

            avg_wait_time = (
                self.total_wait_time / self.total_requests
                if self.total_requests > 0
                else 0.0
            )

            return {
                "total_connections": total_connections,
                "available_connections": available_connections,
                "in_use_connections": in_use_connections,
                "utilization_rate": (
                    in_use_connections / total_connections
                    if total_connections > 0
                    else 0.0
                ),
                "total_requests": self.total_requests,
                "avg_wait_time": avg_wait_time,
                "total_created": self.total_connections_created,
                "total_closed": self.total_connections_closed,
                "pool_config": {
                    "min_connections": self.min_connections,
                    "max_connections": self.max_connections,
                    "connection_timeout": self.connection_timeout,
                    "health_check_interval": self.health_check_interval,
                    "max_idle_time": self.max_idle_time,
                },
            }

        except Exception:
            logger.error("获取连接池统计信息失败", error=str(e))
            return {}

    async def reset_pool(self):
        """重置连接池"""
        try:
            logger.info("开始重置连接池")

            # 关闭所有现有连接
            await self._close_all_connections()

            # 重新创建初始连接
            await self._create_initial_connections()

            # 重置统计信息
            self.total_requests = 0
            self.total_wait_time = 0.0

            logger.info("连接池重置完成")

        except Exception:
            logger.error("重置连接池失败", error=str(e))
            raise
