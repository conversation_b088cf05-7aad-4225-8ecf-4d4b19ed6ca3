{"module_name": "material_master", "display_name": "物料档案", "version": "2.0.0", "source": "json_parser", "total_fields": 272, "created_at": "2025-07-28T20:12:24.841975", "last_updated": "2025-07-28T20:12:24.841975", "fields": {"code": {"api_field_name": "code", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "data.code", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "message": {"api_field_name": "message", "chinese_name": "调用失败时的错误信息", "data_type": "NVARCHAR(500)", "param_desc": "调用失败时的错误信息", "path": "message", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "调用成功时的返回数据", "data_type": "NVARCHAR(MAX)", "param_desc": "调用成功时的返回数据", "path": "data", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "detail": {"api_field_name": "detail", "chinese_name": "物料详情", "data_type": "NVARCHAR(MAX)", "param_desc": "物料详情", "path": "data.detail", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "id": {"api_field_name": "id", "chinese_name": "物料档案id", "data_type": "BIGINT", "param_desc": "物料档案id", "path": "id", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productId": {"api_field_name": "productId", "chinese_name": "物料id", "data_type": "BIGINT", "param_desc": "物料id", "path": "data.productAlbums.productId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productApplyRangeId": {"api_field_name": "productApplyRangeId", "chinese_name": "适用范围id", "data_type": "BIGINT", "param_desc": "适用范围id", "path": "data.detail.productApplyRangeId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchaseUnit": {"api_field_name": "purchaseUnit", "chinese_name": "采购单位id", "data_type": "BIGINT", "param_desc": "采购单位id", "path": "data.detail.purchaseUnit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchaseUnitCode": {"api_field_name": "purchaseUnitCode", "chinese_name": "采购单位编码", "data_type": "NVARCHAR(500)", "param_desc": "采购单位编码", "path": "data.detail.purchaseUnitCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchaseUnitName": {"api_field_name": "purchaseUnitName", "chinese_name": "采购单位名称", "data_type": "NVARCHAR(500)", "param_desc": "采购单位名称", "path": "data.detail.purchaseUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "purchasePriceUnit": {"api_field_name": "purchasePriceUnit", "chinese_name": "采购计价单位id", "data_type": "BIGINT", "param_desc": "采购计价单位id", "path": "data.detail.purchasePriceUnit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchasePriceUnitCode": {"api_field_name": "purchasePriceUnitCode", "chinese_name": "采购计价单位编码", "data_type": "NVARCHAR(500)", "param_desc": "采购计价单位编码", "path": "data.detail.purchasePriceUnitCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchasePriceUnitName": {"api_field_name": "purchasePriceUnitName", "chinese_name": "采购计价单位名称", "data_type": "NVARCHAR(500)", "param_desc": "采购计价单位名称", "path": "data.detail.purchasePriceUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockUnit": {"api_field_name": "stockUnit", "chinese_name": "库存单位id", "data_type": "BIGINT", "param_desc": "库存单位id", "path": "data.detail.stockUnit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockUnitCode": {"api_field_name": "stockUnitCode", "chinese_name": "库存单位编码", "data_type": "NVARCHAR(500)", "param_desc": "库存单位编码", "path": "data.detail.stockUnitCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockUnitName": {"api_field_name": "stockUnitName", "chinese_name": "库存单位名称", "data_type": "NVARCHAR(500)", "param_desc": "库存单位名称", "path": "data.detail.stockUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchUnit": {"api_field_name": "batchUnit", "chinese_name": "批发销售单位id", "data_type": "BIGINT", "param_desc": "批发销售单位id", "path": "data.detail.batchUnit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "batchUnitCode": {"api_field_name": "batchUnitCode", "chinese_name": "批发销售单位编码", "data_type": "NVARCHAR(500)", "param_desc": "批发销售单位编码", "path": "data.detail.batchUnitCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "batchUnitName": {"api_field_name": "batchUnitName", "chinese_name": "批发销售单位名称", "data_type": "NVARCHAR(500)", "param_desc": "批发销售单位名称", "path": "data.detail.batchUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "onlineUnit": {"api_field_name": "onlineUnit", "chinese_name": "线上零售单位id", "data_type": "BIGINT", "param_desc": "线上零售单位id", "path": "data.detail.onlineUnit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "onlineUnitCode": {"api_field_name": "onlineUnitCode", "chinese_name": "线上零售单位编码", "data_type": "NVARCHAR(500)", "param_desc": "线上零售单位编码", "path": "data.detail.onlineUnitCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "onlineUnitName": {"api_field_name": "onlineUnitName", "chinese_name": "线上零售单位名称", "data_type": "NVARCHAR(500)", "param_desc": "线上零售单位名称", "path": "data.detail.onlineUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "offlineUnit": {"api_field_name": "offlineUnit", "chinese_name": "线下零售单位id", "data_type": "BIGINT", "param_desc": "线下零售单位id", "path": "data.detail.offlineUnit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "offlineUnitCode": {"api_field_name": "offlineUnitCode", "chinese_name": "线下零售单位编码", "data_type": "NVARCHAR(500)", "param_desc": "线下零售单位编码", "path": "data.detail.offlineUnitCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "offlineUnitName": {"api_field_name": "offlineUnitName", "chinese_name": "线下零售单位名称", "data_type": "NVARCHAR(500)", "param_desc": "线下零售单位名称", "path": "data.detail.offlineUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "requireUnit": {"api_field_name": "requireUnit", "chinese_name": "要货单位id", "data_type": "BIGINT", "param_desc": "要货单位id", "path": "data.detail.requireUnit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "requireUnitCode": {"api_field_name": "requireUnitCode", "chinese_name": "要货单位编码", "data_type": "NVARCHAR(500)", "param_desc": "要货单位编码", "path": "data.detail.requireUnitCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "requireUnitName": {"api_field_name": "requireUnitName", "chinese_name": "要货单位名称", "data_type": "NVARCHAR(500)", "param_desc": "要货单位名称", "path": "data.detail.requireUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchPriceUnit": {"api_field_name": "batchPriceUnit", "chinese_name": "批发计价单位id", "data_type": "BIGINT", "param_desc": "批发计价单位id", "path": "data.detail.batchPriceUnit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "batchPriceUnitCode": {"api_field_name": "batchPriceUnitCode", "chinese_name": "批发计价单位编码", "data_type": "NVARCHAR(500)", "param_desc": "批发计价单位编码", "path": "data.detail.batchPriceUnitCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "batchPriceUnitName": {"api_field_name": "batchPriceUnitName", "chinese_name": "批发计价单位名称", "data_type": "NVARCHAR(500)", "param_desc": "批发计价单位名称", "path": "data.detail.batchPriceUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "inspectionUnit": {"api_field_name": "inspectionUnit", "chinese_name": "检验单位", "data_type": "BIGINT", "param_desc": "检验单位", "path": "data.detail.inspectionUnit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "inspectionUnitCode": {"api_field_name": "inspectionUnitCode", "chinese_name": "检验单位编码", "data_type": "NVARCHAR(500)", "param_desc": "检验单位编码", "path": "data.detail.inspectionUnitCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "inspectionUnitName": {"api_field_name": "inspectionUnitName", "chinese_name": "检验单位名称", "data_type": "NVARCHAR(500)", "param_desc": "检验单位名称", "path": "data.detail.inspectionUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchPrice": {"api_field_name": "batchPrice", "chinese_name": "批发价", "data_type": "DECIMAL(18,4)", "param_desc": "批发价", "path": "data.detail.batchPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "markPrice": {"api_field_name": "mark<PERSON><PERSON>", "chinese_name": "建议零售价", "data_type": "DECIMAL(18,4)", "param_desc": "建议零售价", "path": "data.detail.markPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "lowestMarkPrice": {"api_field_name": "lowestMarkPrice", "chinese_name": "最低零售价", "data_type": "DECIMAL(18,4)", "param_desc": "最低零售价", "path": "data.detail.lowestMarkPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "salePrice": {"api_field_name": "salePrice", "chinese_name": "线上零售价", "data_type": "DECIMAL(18,4)", "param_desc": "线上零售价", "path": "data.detail.salePrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "marketPrice": {"api_field_name": "marketPrice", "chinese_name": "市场价", "data_type": "DECIMAL(18,4)", "param_desc": "市场价", "path": "data.detail.marketPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "primeCosts": {"api_field_name": "primeCosts", "chinese_name": "采购参考价", "data_type": "DECIMAL(18,4)", "param_desc": "采购参考价", "path": "data.detail.primeCosts", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "settleAccountsRate": {"api_field_name": "settleAccountsRate", "chinese_name": "结算费率", "data_type": "BIGINT", "param_desc": "结算费率", "path": "data.detail.settleAccountsRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "displayPrice": {"api_field_name": "displayPrice", "chinese_name": "线上显示价格, true:是、false:否", "data_type": "BIT", "param_desc": "线上显示价格, true:是、false:否", "path": "data.detail.displayPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchManage": {"api_field_name": "batchManage", "chinese_name": "批次管理, true:是、false:否", "data_type": "BIT", "param_desc": "批次管理, true:是、false:否", "path": "data.detail.batchManage", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "expiryDateManage": {"api_field_name": "expiryDateManage", "chinese_name": "有效期管理, true:是、false:否", "data_type": "BIT", "param_desc": "有效期管理, true:是、false:否", "path": "data.detail.expiryDateManage", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "serialNoManage": {"api_field_name": "serialNoManage", "chinese_name": "序列号管理, true:是、false:否", "data_type": "BIT", "param_desc": "序列号管理, true:是、false:否", "path": "data.detail.serialNoManage", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "safetyStock": {"api_field_name": "safetyStock", "chinese_name": "安全库存", "data_type": "DECIMAL(18,4)", "param_desc": "安全库存", "path": "data.detail.safetyStock", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "highestStock": {"api_field_name": "highestStock", "chinese_name": "最高库存", "data_type": "DECIMAL(18,4)", "param_desc": "最高库存", "path": "data.detail.highestStock", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "lowestStock": {"api_field_name": "lowestStock", "chinese_name": "最低库存", "data_type": "BIGINT", "param_desc": "最低库存", "path": "data.detail.lowestStock", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "ropStock": {"api_field_name": "ropStock", "chinese_name": "再订货点", "data_type": "BIGINT", "param_desc": "再订货点", "path": "data.detail.ropStock", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "canSale": {"api_field_name": "canSale", "chinese_name": "B2B是否可售, true:是，false:否", "data_type": "BIT", "param_desc": "B2B是否可售, true:是，false:否", "path": "data.detail.canSale", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "minOrderQuantity": {"api_field_name": "minOrderQuantity", "chinese_name": "起订量", "data_type": "BIGINT", "param_desc": "起订量", "path": "data.detail.minOrderQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "deliveryDays": {"api_field_name": "deliveryDays", "chinese_name": "交货周期", "data_type": "BIGINT", "param_desc": "交货周期", "path": "data.detail.deliveryDays", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "enableCyclePurchase": {"api_field_name": "enableCyclePurchase", "chinese_name": "启用周期购，true：是、false：否。", "data_type": "BIT", "param_desc": "启用周期购，true：是、false：否。", "path": "data.detail.enableCyclePurchase", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "enableDeposit": {"api_field_name": "enableDeposit", "chinese_name": "启用定金业务, true:是、false:否。", "data_type": "BIT", "param_desc": "启用定金业务, true:是、false:否。", "path": "data.detail.enableDeposit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "depositDealPayType": {"api_field_name": "depositDealPayType", "chinese_name": "定金设置方式, 0:固定金额、1:成交金额百分比", "data_type": "BIGINT", "param_desc": "定金设置方式, 0:固定金额、1:成交金额百分比", "path": "data.detail.depositDealPayType", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "enableModifyDeposit": {"api_field_name": "enableModifyDeposit", "chinese_name": "订单改价时可修改定金, true:是、false:否", "data_type": "BIT", "param_desc": "订单改价时可修改定金, true:是、false:否", "path": "data.detail.enableModifyDeposit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "depositPayType": {"api_field_name": "depositPayType", "chinese_name": "支付尾款方式, 0:线上支付尾款、1:线下支付尾款", "data_type": "BIGINT", "param_desc": "支付尾款方式, 0:线上支付尾款、1:线下支付尾款", "path": "data.detail.depositPayType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "metaDescription": {"api_field_name": "metaDescription", "chinese_name": "搜索简介", "data_type": "NVARCHAR(MAX)", "param_desc": "搜索简介", "path": "data.detail.metaDescription", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "baseSaleCount": {"api_field_name": "baseSaleCount", "chinese_name": "初始销量", "data_type": "BIGINT", "param_desc": "初始销量", "path": "data.detail.baseSaleCount", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "enableContractManagement": {"api_field_name": "enableContractManagement", "chinese_name": "ECN管控，true：是，false：否。", "data_type": "BIT", "param_desc": "ECN管控，true：是，false：否。", "path": "data.detail.enableContractManagement", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "allowStorePurchase": {"api_field_name": "allowStorePurchase", "chinese_name": "允许门店自采, true:是、false:否", "data_type": "BIT", "param_desc": "允许门店自采, true:是、false:否", "path": "data.detail.allowStorePurchase", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "priceChangeAllowed": {"api_field_name": "priceChangeAllowed", "chinese_name": "允许开单改价, true:是、false:否", "data_type": "BIT", "param_desc": "允许开单改价, true:是、false:否", "path": "data.detail.priceChangeAllowed", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "saleInOfflineStore": {"api_field_name": "saleInOfflineStore", "chinese_name": "允许门店销售, true:是、false:否", "data_type": "BIT", "param_desc": "允许门店销售, true:是、false:否", "path": "data.detail.saleInOfflineStore", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "offlineStoreOrder": {"api_field_name": "offlineStoreOrder", "chinese_name": "允许门店要货, true:是、false:否", "data_type": "BIT", "param_desc": "允许门店要货, true:是、false:否", "path": "data.detail.offlineStoreOrder", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "offlineStoreReturn": {"api_field_name": "offlineStoreReturn", "chinese_name": "允许门店退货, true:是、false:否", "data_type": "BIT", "param_desc": "允许门店退货, true:是、false:否", "path": "data.detail.offlineStoreReturn", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "weighingOrNot": {"api_field_name": "weighingOrNot", "chinese_name": "是否称重, true:是、false:否", "data_type": "BIT", "param_desc": "是否称重, true:是、false:否", "path": "data.detail.weighingOrNot", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "process": {"api_field_name": "process", "chinese_name": "加工, true:是、false:否", "data_type": "BIT", "param_desc": "加工, true:是、false:否", "path": "data.detail.process", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "material": {"api_field_name": "material", "chinese_name": "材料, true:是、false:否", "data_type": "BIT", "param_desc": "材料, true:是、false:否", "path": "data.detail.material", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "retailPriceDimension": {"api_field_name": "retailPriceDimension", "chinese_name": "零售价取价维度, 1:物料、2:物料SKU", "data_type": "BIGINT", "param_desc": "零售价取价维度, 1:物料、2:物料SKU", "path": "data.detail.retailPriceDimension", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "deliverQuantityChange": {"api_field_name": "deliverQuantityChange", "chinese_name": "交货数量改变时, 1:单价不变重算金额、2:金额不变重算单价", "data_type": "BIGINT", "param_desc": "交货数量改变时, 1:单价不变重算金额、2:金额不变重算单价", "path": "data.detail.deliverQuantityChange", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "noTaxCostPrice": {"api_field_name": "noTaxCostPrice", "chinese_name": "参考成本", "data_type": "DECIMAL(18,4)", "param_desc": "参考成本", "path": "data.detail.noTaxCostPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "checkByBatch": {"api_field_name": "checkByBatch", "chinese_name": "按批次核算, true:是、false:否", "data_type": "BIT", "param_desc": "按批次核算, true:是、false:否", "path": "data.detail.checkByBatch", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "accountingByItem": {"api_field_name": "accountingByItem", "chinese_name": "按单品核算", "data_type": "BIT", "param_desc": "按单品核算", "path": "data.detail.accountingByItem", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "storeOffAndOffState": {"api_field_name": "storeOffAndOffState", "chinese_name": "商城上架, true:是、false:否", "data_type": "BIT", "param_desc": "商城上架, true:是、false:否", "path": "data.detail.storeOffAndOffState", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderLoadAndUnloadStatus": {"api_field_name": "orderLoadAndUnloadStatus", "chinese_name": "订货上架, true:是、false:否", "data_type": "BIT", "param_desc": "订货上架, true:是、false:否", "path": "data.detail.orderLoadAndUnloadStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "mallUpCount": {"api_field_name": "mallUpCount", "chinese_name": "商城上架数量", "data_type": "BIGINT", "param_desc": "商城上架数量", "path": "data.detail.mallUpCount", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "mallDownCount": {"api_field_name": "mallDownCount", "chinese_name": "商城下架数量", "data_type": "BIGINT", "param_desc": "商城下架数量", "path": "data.detail.mallDownCount", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderUpCount": {"api_field_name": "orderUpCount", "chinese_name": "U订货上架数量", "data_type": "BIGINT", "param_desc": "U订货上架数量", "path": "data.detail.orderUpCount", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderDownCount": {"api_field_name": "orderDownCount", "chinese_name": "U订货下架数量", "data_type": "BIGINT", "param_desc": "U订货下架数量", "path": "data.detail.orderDownCount", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "tenant": {"api_field_name": "tenant", "chinese_name": "租户id", "data_type": "BIGINT", "param_desc": "租户id", "path": "data.tenant", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "saleChannel": {"api_field_name": "saleChannel", "chinese_name": "销售渠道, 1:销售批发、2:线上零售、3:线下零售、4:微分销", "data_type": "NVARCHAR(500)", "param_desc": "销售渠道, 1:销售批发、2:线上零售、3:线下零售、4:微分销", "path": "data.detail.saleChannel", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "barCode": {"api_field_name": "barCode", "chinese_name": "条码值", "data_type": "NVARCHAR(500)", "param_desc": "条码值", "path": "data.productBarCodes.barCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stopStatus": {"api_field_name": "stopStatus", "chinese_name": "启用状态，true代表停用，false代表启用", "data_type": "BIT", "param_desc": "启用状态，true代表停用，false代表启用", "path": "data.detail.stopStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "checkFree": {"api_field_name": "checkFree", "chinese_name": "按规格核算, 0:不按规格核算、1:指定规格核算、2:按SKU核算、", "data_type": "BIGINT", "param_desc": "按规格核算, 0:不按规格核算、1:指定规格核算、2:按SKU核算、", "path": "data.detail.checkFree", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "canOrder": {"api_field_name": "canOrder", "chinese_name": "可预约, true:是、false:否、", "data_type": "BIT", "param_desc": "可预约, true:是、false:否、", "path": "data.detail.canOrder", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "onlyOrder": {"api_field_name": "only<PERSON><PERSON>r", "chinese_name": "仅预约, true:是、false:否、", "data_type": "BIT", "param_desc": "仅预约, true:是、false:否、", "path": "data.detail.onlyOrder", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderAdvanceTime": {"api_field_name": "orderAdvanceTime", "chinese_name": "预约提前期, 0:无、1:一天、2:两天、3:三天、4:一周、5:两周、6:一月、", "data_type": "BIGINT", "param_desc": "预约提前期, 0:无、1:一天、2:两天、3:三天、4:一周、5:两周、6:一月、", "path": "data.detail.orderAdvanceTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "valueManageType": {"api_field_name": "valueManageType", "chinese_name": "价值管理模式, 99:费用、0:存货核算、1:固定资产", "data_type": "BIGINT", "param_desc": "价值管理模式, 99:费用、0:存货核算、1:固定资产", "path": "data.detail.valueManageType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "costValuation": {"api_field_name": "costValuation", "chinese_name": "成本计价方法, 0:先进先出法、1:移动平均法、2:全月平均法", "data_type": "BIGINT", "param_desc": "成本计价方法, 0:先进先出法、1:移动平均法、2:全月平均法", "path": "data.detail.costValuation", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "checkByCost": {"api_field_name": "checkByCost", "chinese_name": "按费用核算, true:是、false:否、yCost", "data_type": "BIT", "param_desc": "按费用核算, true:是、false:否、yCost", "path": "data.detail.checkByCost", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "materialCost": {"api_field_name": "materialCost", "chinese_name": "ma材料费用化, true:是、false:否、terialCost", "data_type": "BIT", "param_desc": "ma材料费用化, true:是、false:否、terialCost", "path": "data.detail.materialCost", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "planDefaultAttribute": {"api_field_name": "planDefaultAttribute", "chinese_name": "计划默认属性, 1:采购，3:自制，5：委外。", "data_type": "BIGINT", "param_desc": "计划默认属性, 1:采购，3:自制，5：委外。", "path": "data.detail.planDefaultAttribute", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "planMethod": {"api_field_name": "planMethod", "chinese_name": "计划方法, 0：MRP/LRP、1:：N-不计划、10：库存计划、5：MPS。", "data_type": "BIGINT", "param_desc": "计划方法, 0：MRP/LRP、1:：N-不计划、10：库存计划、5：MPS。", "path": "data.detail.planMethod", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "keySubPart": {"api_field_name": "keySubPart", "chinese_name": "关键子件, true:是、false:否", "data_type": "BIT", "param_desc": "关键子件, true:是、false:否", "path": "data.detail.keySubPart", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "supplyDemandPolicy": {"api_field_name": "supplyDemandPolicy", "chinese_name": "供需策略, 0:PE、1:LP、", "data_type": "BIGINT", "param_desc": "供需策略, 0:PE、1:LP、", "path": "data.detail.supplyDemandPolicy", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "fixedLeadTime": {"api_field_name": "fixedLeadTime", "chinese_name": "固定提前期", "data_type": "BIGINT", "param_desc": "固定提前期", "path": "data.detail.fixedLeadTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "supplyType": {"api_field_name": "supplyType", "chinese_name": "供应类型, 0：领用、1：入库倒冲、2：不发料、5：开工倒冲、6：工序完工倒冲 、7：完工倒冲。", "data_type": "BIGINT", "param_desc": "供应类型, 0：领用、1：入库倒冲、2：不发料、5：开工倒冲、6：工序完工倒冲 、7：完工倒冲。", "path": "data.detail.supplyType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "produceDepartment": {"api_field_name": "produceDepartment", "chinese_name": "生产部门ID", "data_type": "NVARCHAR(500)", "param_desc": "生产部门ID", "path": "data.detail.produceDepartment", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "produceDepartmentCode": {"api_field_name": "produceDepartmentCode", "chinese_name": "生产部门编码", "data_type": "NVARCHAR(500)", "param_desc": "生产部门编码", "path": "data.detail.produceDepartmentCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "produceDepartmentName": {"api_field_name": "produceDepartmentName", "chinese_name": "生产部门名称", "data_type": "NVARCHAR(500)", "param_desc": "生产部门名称", "path": "data.detail.produceDepartmentName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "manufacturePlanner": {"api_field_name": "manufacturePlanner", "chinese_name": "计划员id", "data_type": "NVARCHAR(500)", "param_desc": "计划员id", "path": "data.detail.manufacturePlanner", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "manufacturePlannerCode": {"api_field_name": "manufacturePlannerCode", "chinese_name": "计划员编码", "data_type": "NVARCHAR(500)", "param_desc": "计划员编码", "path": "data.detail.manufacturePlannerCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "manufacturePlannerName": {"api_field_name": "manufacturePlannerName", "chinese_name": "计划员名称", "data_type": "NVARCHAR(500)", "param_desc": "计划员名称", "path": "data.detail.manufacturePlannerName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "engineeringDrawingNo": {"api_field_name": "engineeringDrawingNo", "chinese_name": "工程图号", "data_type": "NVARCHAR(500)", "param_desc": "工程图号", "path": "data.detail.engineeringDrawingNo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "planProduceLimit": {"api_field_name": "planProduceLimit", "chinese_name": "计划下达超量上限", "data_type": "BIGINT", "param_desc": "计划下达超量上限", "path": "data.detail.planProduceLimit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "utility": {"api_field_name": "utility", "chinese_name": "公用工程, true:是、false:否、", "data_type": "BIT", "param_desc": "公用工程, true:是、false:否、", "path": "data.detail.utility", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "weigh": {"api_field_name": "weigh", "chinese_name": "是否过磅, true:是、false:否、", "data_type": "BIT", "param_desc": "是否过磅, true:是、false:否、", "path": "data.detail.weigh", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productVendor": {"api_field_name": "productVendor", "chinese_name": "供应商id", "data_type": "BIGINT", "param_desc": "供应商id", "path": "data.detail.productVendor", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productVendorCode": {"api_field_name": "productVendorCode", "chinese_name": "供应商编码", "data_type": "NVARCHAR(500)", "param_desc": "供应商编码", "path": "data.detail.productVendorCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productVendorName": {"api_field_name": "productVendorName", "chinese_name": "供应商名称", "data_type": "NVARCHAR(500)", "param_desc": "供应商名称", "path": "data.detail.productVendorName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productBuyer": {"api_field_name": "productBuyer", "chinese_name": "采购员id", "data_type": "NVARCHAR(500)", "param_desc": "采购员id", "path": "data.detail.productBuyer", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productBuyerCode": {"api_field_name": "productBuyerCode", "chinese_name": "采购员编码", "data_type": "NVARCHAR(500)", "param_desc": "采购员编码", "path": "data.detail.productBuyerCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productBuyerName": {"api_field_name": "productBuyerName", "chinese_name": "采购员名称", "data_type": "NVARCHAR(500)", "param_desc": "采购员名称", "path": "data.detail.productBuyerName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "maxPrimeCosts": {"api_field_name": "maxPrimeCosts", "chinese_name": "最高进价", "data_type": "BIGINT", "param_desc": "最高进价", "path": "data.detail.maxPrimeCosts", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "requestOrderLimit": {"api_field_name": "requestOrderLimit", "chinese_name": "请购订货超量上限", "data_type": "BIGINT", "param_desc": "请购订货超量上限", "path": "data.detail.requestOrderLimit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "enableSubscribe": {"api_field_name": "enableSubscribe", "chinese_name": "启用预订业务, true:是、false:否", "data_type": "BIT", "param_desc": "启用预订业务, true:是、false:否", "path": "data.detail.enableSubscribe", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recommend": {"api_field_name": "recommend", "chinese_name": "推荐物料, true:是、false:否", "data_type": "BIT", "param_desc": "推荐物料, true:是、false:否", "path": "data.detail.recommend", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "erpOuterCode": {"api_field_name": "erpOuterCode", "chinese_name": "商家商品外部编码", "data_type": "NVARCHAR(500)", "param_desc": "商家商品外部编码", "path": "data.detail.erpOuterCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "saleStyle": {"api_field_name": "saleStyle", "chinese_name": "销售方式, 1:现金购买、2:积分兑换", "data_type": "NVARCHAR(500)", "param_desc": "销售方式, 1:现金购买、2:积分兑换", "path": "data.detail.saleStyle", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "shortName": {"api_field_name": "shortName", "chinese_name": "物料简称", "data_type": "NVARCHAR(500)", "param_desc": "物料简称", "path": "data.detail.shortName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "mnemonicCode": {"api_field_name": "mnemonicCode", "chinese_name": "助记码", "data_type": "NVARCHAR(500)", "param_desc": "助记码", "path": "data.detail.mnemonicCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "receiptName": {"api_field_name": "receiptName", "chinese_name": "开票名称", "data_type": "NVARCHAR(MAX)", "param_desc": "开票名称", "path": "data.detail.receiptName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "incomeTaxRates": {"api_field_name": "incomeTaxRates", "chinese_name": "进项税率id", "data_type": "NVARCHAR(500)", "param_desc": "进项税率id", "path": "data.detail.incomeTaxRates", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "outputTaxRate": {"api_field_name": "outputTaxRate", "chinese_name": "销项税率id", "data_type": "NVARCHAR(500)", "param_desc": "销项税率id", "path": "data.detail.outputTaxRate", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "produceUnit": {"api_field_name": "produceUnit", "chinese_name": "生产单位id", "data_type": "BIGINT", "param_desc": "生产单位id", "path": "data.detail.produceUnit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "produceUnitCode": {"api_field_name": "produceUnitCode", "chinese_name": "生产单位编码", "data_type": "NVARCHAR(500)", "param_desc": "生产单位编码", "path": "data.detail.produceUnitCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "produceUnitName": {"api_field_name": "produceUnitName", "chinese_name": "生产单位名称", "data_type": "NVARCHAR(500)", "param_desc": "生产单位名称", "path": "data.detail.produceUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehouseManager": {"api_field_name": "warehouseManager", "chinese_name": "库管员id", "data_type": "NVARCHAR(500)", "param_desc": "库管员id", "path": "data.detail.warehouseManager", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "warehouseManagerCode": {"api_field_name": "warehouseManagerCode", "chinese_name": "库管员编码", "data_type": "NVARCHAR(500)", "param_desc": "库管员编码", "path": "data.detail.warehouseManagerCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "warehouseManagerName": {"api_field_name": "warehouseManagerName", "chinese_name": "库管员名称", "data_type": "NVARCHAR(500)", "param_desc": "库管员名称", "path": "data.detail.warehouseManagerName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "deliveryWarehouse": {"api_field_name": "deliveryWarehouse", "chinese_name": "发货仓库id", "data_type": "BIGINT", "param_desc": "发货仓库id", "path": "data.detail.deliveryWarehouse", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "deliveryWarehouseCode": {"api_field_name": "deliveryWarehouseCode", "chinese_name": "发货仓库编码", "data_type": "NVARCHAR(500)", "param_desc": "发货仓库编码", "path": "data.detail.deliveryWarehouseCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "deliveryWarehouseName": {"api_field_name": "deliveryWarehouseName", "chinese_name": "发货仓库名称", "data_type": "NVARCHAR(500)", "param_desc": "发货仓库名称", "path": "data.detail.deliveryWarehouseName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "returnWarehouse": {"api_field_name": "returnWarehouse", "chinese_name": "退货仓库id", "data_type": "BIGINT", "param_desc": "退货仓库id", "path": "data.detail.returnWarehouse", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "returnWarehouseCode": {"api_field_name": "returnWarehouseCode", "chinese_name": "退货仓库编码", "data_type": "NVARCHAR(500)", "param_desc": "退货仓库编码", "path": "data.detail.returnWarehouseCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "returnWarehouseName": {"api_field_name": "returnWarehouseName", "chinese_name": "退货仓库名称", "data_type": "NVARCHAR(500)", "param_desc": "退货仓库名称", "path": "data.detail.returnWarehouseName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "inStoreExcessLimit": {"api_field_name": "inStoreExcessLimit", "chinese_name": "入库超量上限", "data_type": "BIGINT", "param_desc": "入库超量上限", "path": "data.detail.inStoreExcessLimit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "outStoreExcessLimit": {"api_field_name": "outStoreExcessLimit", "chinese_name": "出库超量上限", "data_type": "BIGINT", "param_desc": "出库超量上限", "path": "data.detail.outStoreExcessLimit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "storageLossRate": {"api_field_name": "storageLossRate", "chinese_name": "保管损耗率", "data_type": "BIGINT", "param_desc": "保管损耗率", "path": "data.detail.storageLossRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "allowNegativeInventory": {"api_field_name": "allowNegativeInventory", "chinese_name": "允许负库存, true:是、false:否", "data_type": "BIT", "param_desc": "允许负库存, true:是、false:否", "path": "data.detail.allowNegativeInventory", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "scanCountUnit": {"api_field_name": "scanCountUnit", "chinese_name": "扫码计数单位，0：主计量单位，1：库存单位。", "data_type": "BIGINT", "param_desc": "扫码计数单位，0：主计量单位，1：库存单位。", "path": "data.detail.scanCountUnit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "exemption": {"api_field_name": "exemption", "chinese_name": "免检, true:是、false:否、", "data_type": "BIT", "param_desc": "免检, true:是、false:否、", "path": "data.detail.exemption", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehousingByResult": {"api_field_name": "warehousingByResult", "chinese_name": "根据检验结果入库，true代表是，false代表否", "data_type": "BIT", "param_desc": "根据检验结果入库，true代表是，false代表否", "path": "data.detail.warehousingByResult", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "salesReturnsExemption": {"api_field_name": "salesReturnsExemption", "chinese_name": "销售退货免检, true:是、false:否、", "data_type": "BIT", "param_desc": "销售退货免检, true:是、false:否、", "path": "data.detail.salesReturnsExemption", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "returnsWarehousingByResult": {"api_field_name": "returnsWarehousingByResult", "chinese_name": "退货根据检验结果入库, true:是、false:否、", "data_type": "BIT", "param_desc": "退货根据检验结果入库, true:是、false:否、", "path": "data.detail.returnsWarehousingByResult", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "periodicalInspection": {"api_field_name": "periodicalInspection", "chinese_name": "定期检验, true:是、false:否、", "data_type": "BIT", "param_desc": "定期检验, true:是、false:否、", "path": "data.detail.periodicalInspection", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "displayName": {"api_field_name": "displayName", "chinese_name": "显示名称", "data_type": "NVARCHAR(MAX)", "param_desc": "显示名称", "path": "data.detail.displayName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "titleMemo": {"api_field_name": "titleMemo", "chinese_name": "卖点", "data_type": "NVARCHAR(MAX)", "param_desc": "卖点", "path": "data.detail.titleMemo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "barcodeManage": {"api_field_name": "barcodeManage", "chinese_name": "条码管理, true:是、false:否", "data_type": "BIT", "param_desc": "条码管理, true:是、false:否", "path": "data.detail.barcodeManage", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "receiptWarehouse": {"api_field_name": "receiptWarehouse", "chinese_name": "收货仓库id", "data_type": "BIGINT", "param_desc": "收货仓库id", "path": "data.detail.receiptWarehouse", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "receiptWarehouseCode": {"api_field_name": "receiptWarehouseCode", "chinese_name": "收货仓库编码", "data_type": "NVARCHAR(500)", "param_desc": "收货仓库编码", "path": "data.detail.receiptWarehouseCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "receiptWarehouseName": {"api_field_name": "receiptWarehouseName", "chinese_name": "收货仓库名称", "data_type": "NVARCHAR(500)", "param_desc": "收货仓库名称", "path": "data.detail.receiptWarehouseName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "BOMType": {"api_field_name": "BOMType", "chinese_name": "物料BOM类型，0：标准件，5：计划件。", "data_type": "BIGINT", "param_desc": "物料BOM类型，0：标准件，5：计划件。", "path": "data.detail.BOMType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchRule": {"api_field_name": "batchRule", "chinese_name": "批量规则,0：直接批量、5：经济批量、10：固定批量", "data_type": "BIGINT", "param_desc": "批量规则,0：直接批量、5：经济批量、10：固定批量", "path": "data.detail.batchRule", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "fixedQuantity": {"api_field_name": "fixedQuantity", "chinese_name": "固定批量", "data_type": "BIGINT", "param_desc": "固定批量", "path": "data.detail.fixedQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "prepareFeed": {"api_field_name": "prepareFeed", "chinese_name": "是否长周期备料，true：是，false：否。", "data_type": "BIT", "param_desc": "是否长周期备料，true：是，false：否。", "path": "data.detail.prepareFeed", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "specialMaterials": {"api_field_name": "specialMaterials", "chinese_name": "是否专用料，true：是，false：否。", "data_type": "BIT", "param_desc": "是否专用料，true：是，false：否。", "path": "data.detail.specialMaterials", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "virtualPart": {"api_field_name": "virtualPart", "chinese_name": "是否虚拟件，true：是，false：否。", "data_type": "BIT", "param_desc": "是否虚拟件，true：是，false：否。", "path": "data.detail.virtualPart", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "demandConsolidation": {"api_field_name": "demandConsolidation", "chinese_name": "物料需求合并，0：空，5：是，10：否。", "data_type": "BIGINT", "param_desc": "物料需求合并，0：空，5：是，10：否。", "path": "data.detail.demandConsolidation", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "demandConsolidationType": {"api_field_name": "demandConsolidationType", "chinese_name": "需求合并类型，0：固定，10：动态。", "data_type": "BIGINT", "param_desc": "需求合并类型，0：固定，10：动态。", "path": "data.detail.demandConsolidationType", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "demandConsolidationUnit": {"api_field_name": "demandConsolidationUnit", "chinese_name": "需求合并时格，0：日，10：月，15：月。", "data_type": "BIGINT", "param_desc": "需求合并时格，0：日，10：月，15：月。", "path": "data.detail.demandConsolidationUnit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "demandConsolidationNumber": {"api_field_name": "demandConsolidationNumber", "chinese_name": "需求合并时格数", "data_type": "BIGINT", "param_desc": "需求合并时格数", "path": "data.detail.demandConsolidationNumber", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "demandConsolidationDateType": {"api_field_name": "demandConsolidationDateType", "chinese_name": "需求合并日，0：需求首日，1：期间首日。", "data_type": "BIGINT", "param_desc": "需求合并日，0：需求首日，1：期间首日。", "path": "data.detail.demandConsolidationDateType", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "reservation": {"api_field_name": "reservation", "chinese_name": "可预留，true：是，false：否。", "data_type": "BIT", "param_desc": "可预留，true：是，false：否。", "path": "data.detail.reservation", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "lossType": {"api_field_name": "lossType", "chinese_name": "损耗类型，0：无损耗，5：固定损耗，10：变动损耗。", "data_type": "BIGINT", "param_desc": "损耗类型，0：无损耗，5：固定损耗，10：变动损耗。", "path": "data.detail.lossType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "ECNControl": {"api_field_name": "ECNControl", "chinese_name": "ECN管控，true：是，false：否。", "data_type": "BIT", "param_desc": "ECN管控，true：是，false：否。", "path": "data.detail.ECNControl", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "ytenantId": {"api_field_name": "ytenantId", "chinese_name": "友互通id", "data_type": "NVARCHAR(500)", "param_desc": "友互通id", "path": "data.ytenantId", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "inspectionType": {"api_field_name": "inspectionType", "chinese_name": "检验，1代表是，0代表否", "data_type": "BIGINT", "param_desc": "检验，1代表是，0代表否", "path": "data.detail.inspectionType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "logisticsRelated": {"api_field_name": "logisticsRelated", "chinese_name": "物流相关，true：是，false：否。", "data_type": "BIT", "param_desc": "物流相关，true：是，false：否。", "path": "data.detail.logisticsRelated", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "weighingMode": {"api_field_name": "weighingMode", "chinese_name": "称重方式，1：是，0：否。", "data_type": "BIGINT", "param_desc": "称重方式，1：是，0：否。", "path": "data.detail.weighingMode", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "reviewGrossWeight": {"api_field_name": "reviewGrossWeight", "chinese_name": "复核毛重，true：是，false：否。", "data_type": "BIT", "param_desc": "复核毛重，true：是，false：否。", "path": "data.detail.reviewGrossWeight", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "specialCarTransport": {"api_field_name": "specialCarTransport", "chinese_name": "专车运输，true：是，false：否。", "data_type": "BIT", "param_desc": "专车运输，true：是，false：否。", "path": "data.detail.specialCarTransport", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orgId": {"api_field_name": "orgId", "chinese_name": "组织id(组织id和编码二选一必填，同时填入时以id为准) 示例：666666\"", "data_type": "NVARCHAR(500)", "param_desc": "组织id(组织id和编码二选一必填，同时填入时以id为准) 示例：666666\"", "path": "orgId", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "businessAttribute": {"api_field_name": "businessAttribute", "chinese_name": "业务属性, 1:采购、7:销售、3:自制、2:委外", "data_type": "NVARCHAR(500)", "param_desc": "业务属性, 1:采购、7:销售、3:自制、2:委外", "path": "data.detail.businessAttribute", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "businessAttributePurchase": {"api_field_name": "businessAttributePurchase", "chinese_name": "业务属性-采购，1: 是，0：否。", "data_type": "BIGINT", "param_desc": "业务属性-采购，1: 是，0：否。", "path": "data.detail.businessAttributePurchase", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "businessAttributeSale": {"api_field_name": "businessAttributeSale", "chinese_name": "业务属性-销售，1: 是，0：否。", "data_type": "BIGINT", "param_desc": "业务属性-销售，1: 是，0：否。", "path": "data.detail.businessAttributeSale", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "businessAttributeSelfCreate": {"api_field_name": "businessAttributeSelfCreate", "chinese_name": "业务属性-自制，1: 是，0：否。", "data_type": "BIGINT", "param_desc": "业务属性-自制，1: 是，0：否。", "path": "data.detail.businessAttributeSelfCreate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "businessAttributeOutSourcing": {"api_field_name": "businessAttributeOutSourcing", "chinese_name": "业务属性-委外，1: 是，0：否。", "data_type": "BIGINT", "param_desc": "业务属性-委外，1: 是，0：否。", "path": "data.detail.businessAttributeOutSourcing", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "testRule": {"api_field_name": "testRule", "chinese_name": "检验规则，0代表按物料检验，1代表按检验项目检验", "data_type": "BIGINT", "param_desc": "检验规则，0代表按物料检验，1代表按检验项目检验", "path": "data.detail.testRule", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "enableStockPeriodRecheck": {"api_field_name": "enableStockPeriodRecheck", "chinese_name": "启用库存周期复检，1:启用，0: 停用。", "data_type": "BIGINT", "param_desc": "启用库存周期复检，1:启用，0: 停用。", "path": "data.detail.enableStockPeriodRecheck", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "enableStockExpireCheck": {"api_field_name": "enableStockExpireCheck", "chinese_name": "启用库存临期检验，1：是，0：否。", "data_type": "BIGINT", "param_desc": "启用库存临期检验，1：是，0：否。", "path": "data.detail.enableStockExpireCheck", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "enableSparePartsManagement": {"api_field_name": "enableSparePartsManagement", "chinese_name": "启用备件管理，1：是，0：否。", "data_type": "BIGINT", "param_desc": "启用备件管理，1：是，0：否。", "path": "data.detail.enableSparePartsManagement", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "fullSetInspection": {"api_field_name": "fullSetInspection", "chinese_name": "齐套检查，1：是，0：否。", "data_type": "BIGINT", "param_desc": "齐套检查，1：是，0：否。", "path": "data.detail.fullSetInspection", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "directProduction": {"api_field_name": "directProduction", "chinese_name": "是否直接生产，1：是，0：否。", "data_type": "BIGINT", "param_desc": "是否直接生产，1：是，0：否。", "path": "data.detail.directProduction", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "costItems": {"api_field_name": "costItems", "chinese_name": "费用项目id", "data_type": "NVARCHAR(500)", "param_desc": "费用项目id", "path": "data.detail.costItems", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "costItemsCode": {"api_field_name": "costItemsCode", "chinese_name": "费用项目编码", "data_type": "NVARCHAR(500)", "param_desc": "费用项目编码", "path": "data.detail.costItemsCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "costItemsName": {"api_field_name": "costItemsName", "chinese_name": "费用项目名称", "data_type": "NVARCHAR(500)", "param_desc": "费用项目名称", "path": "data.detail.costItemsName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "manageByInventory": {"api_field_name": "manageByInventory", "chinese_name": "按项目管理库存（0表示不开启，1表示开启）", "data_type": "BIGINT", "param_desc": "按项目管理库存（0表示不开启，1表示开启）", "path": "data.detail.manageByInventory", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "checkByProject": {"api_field_name": "checkByProject", "chinese_name": "按项目核算（0表示不开启，1表示开启）", "data_type": "BIGINT", "param_desc": "按项目核算（0表示不开启，1表示开启）", "path": "data.detail.checkByProject", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "checkBySalesOrders": {"api_field_name": "checkBySalesOrders", "chinese_name": "按销售订单核算（0表示不开启，1表示开启）", "data_type": "BIGINT", "param_desc": "按销售订单核算（0表示不开启，1表示开启）", "path": "data.detail.checkBySalesOrders", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "checkByClient": {"api_field_name": "checkByClient", "chinese_name": "按客户核算（0表示不开启，1表示开启）", "data_type": "BIGINT", "param_desc": "按客户核算（0表示不开启，1表示开启）", "path": "data.detail.checkByClient", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "checkByOutsourcing": {"api_field_name": "checkByOutsourcing", "chinese_name": "按委外商核算（0表示不开启，1表示开启）", "data_type": "BIGINT", "param_desc": "按委外商核算（0表示不开启，1表示开启）", "path": "data.detail.checkByOutsourcing", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "atpInspection": {"api_field_name": "atpInspection", "chinese_name": "ATP检查（0表示否，1表示是）", "data_type": "NVARCHAR(500)", "param_desc": "ATP检查（0表示否，1表示是）", "path": "data.detail.atpInspection", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "doublePick": {"api_field_name": "doublePick", "chinese_name": "领料倍量", "data_type": "NVARCHAR(500)", "param_desc": "领料倍量", "path": "data.detail.doublePick", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productOrges": {"api_field_name": "productOrges", "chinese_name": "物料分配的组织。", "data_type": "NVARCHAR(MAX)", "param_desc": "物料分配的组织。", "path": "data.productOrges", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orgType": {"api_field_name": "orgType", "chinese_name": "组织类型，1：普通组织，2：商家组织，3：客户组织。", "data_type": "BIGINT", "param_desc": "组织类型，1：普通组织，2：商家组织，3：客户组织。", "path": "data.productOrges.orgType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "realProductAttribute": {"api_field_name": "realProductAttribute", "chinese_name": "物料性质，1：实物物料，2：虚拟物料", "data_type": "BIGINT", "param_desc": "物料性质，1：实物物料，2：虚拟物料", "path": "data.realProductAttribute", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "name": {"api_field_name": "name", "chinese_name": "物料名称", "data_type": "NVARCHAR(MAX)", "param_desc": "物料名称", "path": "data.name", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "model": {"api_field_name": "model", "chinese_name": "型号", "data_type": "NVARCHAR(MAX)", "param_desc": "型号", "path": "data.model", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "keywords": {"api_field_name": "keywords", "chinese_name": "关键字", "data_type": "NVARCHAR(MAX)", "param_desc": "关键字", "path": "data.keywords", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productClass": {"api_field_name": "productClass", "chinese_name": "商品分类id", "data_type": "BIGINT", "param_desc": "商品分类id", "path": "data.productClass", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productClassCode": {"api_field_name": "productClassCode", "chinese_name": "商品分类编码", "data_type": "NVARCHAR(500)", "param_desc": "商品分类编码", "path": "data.productClassCode", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productClassName": {"api_field_name": "productClassName", "chinese_name": "商品分类名称", "data_type": "NVARCHAR(500)", "param_desc": "商品分类名称", "path": "data.productClassName", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "manageClass": {"api_field_name": "manageClass", "chinese_name": "物料分类id", "data_type": "BIGINT", "param_desc": "物料分类id", "path": "data.manageClass", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "manageClassCode": {"api_field_name": "manageClassCode", "chinese_name": "物料分类编码", "data_type": "NVARCHAR(500)", "param_desc": "物料分类编码", "path": "data.manageClassCode", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "manageClassName": {"api_field_name": "manageClassName", "chinese_name": "物料分类名称", "data_type": "NVARCHAR(500)", "param_desc": "物料分类名称", "path": "data.manageClassName", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "purchaseClass": {"api_field_name": "purchaseClass", "chinese_name": "采购分类ID", "data_type": "BIGINT", "param_desc": "采购分类ID", "path": "data.purchaseClass", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchaseClassCode": {"api_field_name": "purchaseClassCode", "chinese_name": "采购分类编码", "data_type": "NVARCHAR(500)", "param_desc": "采购分类编码", "path": "data.purchaseClassCode", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchaseClassName": {"api_field_name": "purchaseClassName", "chinese_name": "采购分类名称", "data_type": "NVARCHAR(500)", "param_desc": "采购分类名称", "path": "data.purchaseClassName", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productTemplate": {"api_field_name": "productTemplate", "chinese_name": "物料模板id", "data_type": "BIGINT", "param_desc": "物料模板id", "path": "data.productTemplate", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "brand": {"api_field_name": "brand", "chinese_name": "品牌id", "data_type": "BIGINT", "param_desc": "品牌id", "path": "data.brand", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "brandCode": {"api_field_name": "brandCode", "chinese_name": "品牌编码", "data_type": "NVARCHAR(500)", "param_desc": "品牌编码", "path": "data.brandCode", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "brandName": {"api_field_name": "brandName", "chinese_name": "品牌名称", "data_type": "NVARCHAR(500)", "param_desc": "品牌名称", "path": "data.brandName", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "placeOfOrigin": {"api_field_name": "placeOfOrigin", "chinese_name": "产地", "data_type": "NVARCHAR(500)", "param_desc": "产地", "path": "data.placeOfOrigin", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "manufacturer": {"api_field_name": "manufacturer", "chinese_name": "生产厂商", "data_type": "NVARCHAR(500)", "param_desc": "生产厂商", "path": "data.manufacturer", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productLine": {"api_field_name": "productLine", "chinese_name": "产品线ID", "data_type": "BIGINT", "param_desc": "产品线ID", "path": "data.productLine", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "shareDescription": {"api_field_name": "shareDescription", "chinese_name": "分享说明", "data_type": "NVARCHAR(MAX)", "param_desc": "分享说明", "path": "data.shareDescription", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unit": {"api_field_name": "unit", "chinese_name": "主计量单位ID", "data_type": "BIGINT", "param_desc": "主计量单位ID", "path": "data.unit", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unitCode": {"api_field_name": "unitCode", "chinese_name": "主计量单位编码", "data_type": "NVARCHAR(500)", "param_desc": "主计量单位编码", "path": "data.unitCode", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unitName": {"api_field_name": "unitName", "chinese_name": "主计量单位名称", "data_type": "NVARCHAR(500)", "param_desc": "主计量单位名称", "path": "data.unitName", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "taxClass": {"api_field_name": "taxClass", "chinese_name": "税收分类码id", "data_type": "NVARCHAR(500)", "param_desc": "税收分类码id", "path": "data.taxClass", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "defaultSKUId": {"api_field_name": "defaultSKUId", "chinese_name": "默认SKUID", "data_type": "BIGINT", "param_desc": "默认SKUID", "path": "data.defaultSKUId", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "erpCode": {"api_field_name": "erpCode", "chinese_name": "外部编码", "data_type": "NVARCHAR(500)", "param_desc": "外部编码", "path": "data.erpCode", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "deleted": {"api_field_name": "deleted", "chinese_name": "是否删除，1：是，0：否。", "data_type": "BIT", "param_desc": "是否删除，1：是，0：否。", "path": "data.deleted", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "createTime": {"api_field_name": "createTime", "chinese_name": "创建时间", "data_type": "NVARCHAR(500)", "param_desc": "创建时间", "path": "data.createTime", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "createDate": {"api_field_name": "createDate", "chinese_name": "创建日期", "data_type": "NVARCHAR(500)", "param_desc": "创建日期", "path": "data.createDate", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "creator": {"api_field_name": "creator", "chinese_name": "创建人", "data_type": "NVARCHAR(500)", "param_desc": "创建人", "path": "data.creator", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "realProductAttributeType": {"api_field_name": "realProductAttributeType", "chinese_name": "实物物料属性，1：普通物料，2：实体卡券，3：实体储值卡，20：描述性物料，4：设备", "data_type": "BIGINT", "param_desc": "实物物料属性，1：普通物料，2：实体卡券，3：实体储值卡，20：描述性物料，4：设备", "path": "data.realProductAttributeType", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "weight": {"api_field_name": "weight", "chinese_name": "毛重", "data_type": "DECIMAL(18,4)", "param_desc": "毛重", "path": "data.weight", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "weightUnit": {"api_field_name": "weightUnit", "chinese_name": "毛重单位id", "data_type": "BIGINT", "param_desc": "毛重单位id", "path": "data.weightUnit", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "weightUnitCode": {"api_field_name": "weightUnitCode", "chinese_name": "毛重单位编码", "data_type": "NVARCHAR(500)", "param_desc": "毛重单位编码", "path": "data.weightUnitCode", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "weightUnitName": {"api_field_name": "weightUnitName", "chinese_name": "毛重单位名称", "data_type": "NVARCHAR(500)", "param_desc": "毛重单位名称", "path": "data.weightUnitName", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "volume": {"api_field_name": "volume", "chinese_name": "体积", "data_type": "DECIMAL(18,4)", "param_desc": "体积", "path": "data.volume", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "volumeUnit": {"api_field_name": "volumeUnit", "chinese_name": "体积单位id", "data_type": "BIGINT", "param_desc": "体积单位id", "path": "data.volumeUnit", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unitUseType": {"api_field_name": "unitUseType", "chinese_name": "设置规则, 1:使用物料模板的计量单位、2:使用物料自己的计量单位", "data_type": "BIGINT", "param_desc": "设置规则, 1:使用物料模板的计量单位、2:使用物料自己的计量单位", "path": "data.unitUseType", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "enableAssistUnit": {"api_field_name": "enableAssistUnit", "chinese_name": "启用辅计量, true:启用", "data_type": "BIT", "param_desc": "启用辅计量, true:启用", "path": "data.enableAssistUnit", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "creatorId": {"api_field_name": "creatorId", "chinese_name": "创建人", "data_type": "BIGINT", "param_desc": "创建人", "path": "data.creatorId", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "registrationManager": {"api_field_name": "registrationManager", "chinese_name": "注册证管理, true:是、false:否", "data_type": "BIT", "param_desc": "注册证管理, true:是、false:否", "path": "data.registrationManager", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "authorizationManager": {"api_field_name": "authorizationManager", "chinese_name": "授权书管理, true:是、false:否", "data_type": "BIT", "param_desc": "授权书管理, true:是、false:否", "path": "data.authorizationManager", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "planClass": {"api_field_name": "planClass", "chinese_name": "计划分类id", "data_type": "BIGINT", "param_desc": "计划分类id", "path": "data.planClass", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "planClassCode": {"api_field_name": "planClassCode", "chinese_name": "计划分类编码", "data_type": "NVARCHAR(500)", "param_desc": "计划分类编码", "path": "data.planClassCode", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "planClassName": {"api_field_name": "planClassName", "chinese_name": "计划分类名称", "data_type": "NVARCHAR(500)", "param_desc": "计划分类名称", "path": "data.planClassName", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "transType": {"api_field_name": "transType", "chinese_name": "物料类型ID", "data_type": "NVARCHAR(500)", "param_desc": "物料类型ID", "path": "data.transType", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "transTypeCode": {"api_field_name": "transTypeCode", "chinese_name": "物料类型编码", "data_type": "NVARCHAR(500)", "param_desc": "物料类型编码", "path": "data.transTypeCode", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "transTypeName": {"api_field_name": "transTypeName", "chinese_name": "物料类型名称", "data_type": "NVARCHAR(500)", "param_desc": "物料类型名称", "path": "data.transTypeName", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productFamily": {"api_field_name": "productFamily", "chinese_name": "产品族，1：是，0：否。", "data_type": "BIGINT", "param_desc": "产品族，1：是，0：否。", "path": "data.productFamily", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "salesAndOperations": {"api_field_name": "salesAndOperations", "chinese_name": "参与SOP，1：是，0：否。", "data_type": "BIGINT", "param_desc": "参与SOP，1：是，0：否。", "path": "data.salesAndOperations", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productBarCodes": {"api_field_name": "productBarCodes", "chinese_name": "物料多条码", "data_type": "NVARCHAR(MAX)", "param_desc": "物料多条码", "path": "data.productBarCodes", "depth": 1, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productAssistUnitExchanges": {"api_field_name": "productAssistUnitExchanges", "chinese_name": "物料辅计量换算对照", "data_type": "NVARCHAR(MAX)", "param_desc": "物料辅计量换算对照", "path": "data.productAssistUnitExchanges", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unitExchangeType": {"api_field_name": "unitExchangeType", "chinese_name": "换算方式，0固定换算，1浮动换算", "data_type": "BIGINT", "param_desc": "换算方式，0固定换算，1浮动换算", "path": "data.productAssistUnitExchanges.unitExchangeType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "assistUnitCount": {"api_field_name": "assistUnitCount", "chinese_name": "辅计量数量", "data_type": "NVARCHAR(500)", "param_desc": "辅计量数量", "path": "data.productAssistUnitExchanges.assistUnitCount", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "assistUnit": {"api_field_name": "assistUnit", "chinese_name": "辅计量单位id", "data_type": "BIGINT", "param_desc": "辅计量单位id", "path": "data.productAssistUnitExchanges.assistUnit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "mainUnitCount": {"api_field_name": "mainUnitCount", "chinese_name": "主计量数量", "data_type": "NVARCHAR(500)", "param_desc": "主计量数量", "path": "data.productAssistUnitExchanges.mainUnitCount", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productAttachments": {"api_field_name": "productAttachments", "chinese_name": "物料附件", "data_type": "NVARCHAR(MAX)", "param_desc": "物料附件", "path": "data.productAttachments", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "folder": {"api_field_name": "folder", "chinese_name": "图片地址", "data_type": "NVARCHAR(500)", "param_desc": "图片地址", "path": "data.productAlbums.folder", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "fileName": {"api_field_name": "fileName", "chinese_name": "附件名称", "data_type": "NVARCHAR(500)", "param_desc": "附件名称", "path": "data.productAttachments.fileName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productAlbums": {"api_field_name": "productAlbums", "chinese_name": "物料图片", "data_type": "NVARCHAR(MAX)", "param_desc": "物料图片", "path": "data.productAlbums", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "imgName": {"api_field_name": "imgName", "chinese_name": "图片名称", "data_type": "NVARCHAR(500)", "param_desc": "图片名称", "path": "data.productAlbums.imgName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "imgBusinessId": {"api_field_name": "imgBusinessId", "chinese_name": "物料图片业务id", "data_type": "NVARCHAR(500)", "param_desc": "物料图片业务id", "path": "data.imgBusinessId", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "videoBusinessId": {"api_field_name": "videoBusinessId", "chinese_name": "物料视频业务id", "data_type": "NVARCHAR(500)", "param_desc": "物料视频业务id", "path": "data.videoBusinessId", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "homepageBusinessId": {"api_field_name": "homepageBusinessId", "chinese_name": "物料首页图片业务id", "data_type": "NVARCHAR(500)", "param_desc": "物料首页图片业务id", "path": "data.homepageBusinessId", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productTags": {"api_field_name": "productTags", "chinese_name": "物料标签", "data_type": "NVARCHAR(MAX)", "param_desc": "物料标签", "path": "data.productTags", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "tagId": {"api_field_name": "tagId", "chinese_name": "标签id", "data_type": "NVARCHAR(500)", "param_desc": "标签id", "path": "data.productTags.tagId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "optionalType": {"api_field_name": "optionalType", "chinese_name": "选配方式（0表示特征选配，1表示组件选配）", "data_type": "NVARCHAR(500)", "param_desc": "选配方式（0表示特征选配，1表示组件选配）", "path": "data.optionalType", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "length": {"api_field_name": "length", "chinese_name": "长", "data_type": "NVARCHAR(500)", "param_desc": "长", "path": "data.length", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "width": {"api_field_name": "width", "chinese_name": "宽", "data_type": "NVARCHAR(500)", "param_desc": "宽", "path": "data.width", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "height": {"api_field_name": "height", "chinese_name": "高", "data_type": "NVARCHAR(500)", "param_desc": "高", "path": "data.height", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productCode": {"api_field_name": "productCode", "chinese_name": "物料编码(物料id和编码二选一，同时填入以id为准)", "data_type": "NVARCHAR(500)", "param_desc": "物料编码(物料id和编码二选一，同时填入以id为准)", "path": "productCode", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orgCode": {"api_field_name": "orgCode", "chinese_name": "组织编码(组织id和编码二选一必填，同时填入时以id为准) 示例：666666", "data_type": "NVARCHAR(500)", "param_desc": "组织编码(组织id和编码二选一必填，同时填入时以id为准) 示例：666666", "path": "orgCode", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}}}