import re
import sys
import xml.etree.ElementTree as ET
from pathlib import Path

#!/usr/bin/env python3
"""
XML文件修复工具
用于修复包含非法字符和格式问题的XML文件
"""


def clean_xml_content(content):
    """清理XML内容中的非法字符和格式"""
    # 1. 移除XML声明外的BOM和其他字符
    content = content.strip()

    # 2. 处理多个根元素问题
    if content.count('<ResultVO>') > 1:
        first_end = content.find('</ResultVO>')
        if first_end != -1:
            content = content[:first_end + len('</ResultVO>')]

    # 3. 清理JSON字符串中的特殊字符

    def clean_json_in_cdataa(match):
    """TODO: Add function description."""
    json_str = match.group(1)
    # 替换JSON中的特殊字符
    json_str = json_str.replace(
        '"<%u8c-config.option.singleOrg%>"', '"false"')
    json_str = json_str.replace('""false""', '"false"')
    return f'"{json_str}"'

    # 修复refTypeContext中的JSON
    content = re.sub(
        r'<refTypeContext>(.*?)</refTypeContext>',
        lambda m: f'<refTypeContext>{clean_json_in_cdata(m)}</refTypeContext>',
        content,
        flags=re.DOTALL)

    # 4. 移除或转义无效的XML字符
    # 移除控制字符但保留换行和制表符
    content = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', content)

    # 5. 修复未转义的特殊字符
    content = content.replace('&', '&amp;')
    content = content.replace('<', '&lt;').replace('>', '&gt;')
    # 恢复XML标签
    content = re.sub(r'&lt;(/?\w+[^&]*?)&gt;', r'<\1>', content)

    return content


def validate_and_fix_xml(xml_file_path):
    """验证并修复XML文件"""
    print(f"🔧 修复XML文件: {xml_file_path}")

    if not Path(xml_file_path).exists():
        print(f"❌ 文件不存在: {xml_file_path}")
        return False

    try:
        # 读取原始内容
        with open(xml_file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()

        print(f"📊 原始文件大小: {len(original_content)} 字符")

        # 尝试直接解析
        try:
            ET.fromstring(original_content)
            print("✅ XML文件格式正确，无需修复")
            return True
        except ET.ParseError as e:
            print(f"⚠️ XML解析错误: {e}")
            print("🔧 开始修复...")

        # 清理内容
        cleaned_content = clean_xml_content(original_content)
        print(f"📊 清理后文件大小: {len(cleaned_content)} 字符")

        # 再次尝试解析
        try:
            root = ET.fromstring(cleaned_content)
            print("✅ 修复成功，XML可以正常解析")

            # 备份原文件
            backup_path = f"{xml_file_path}.backup"
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            print(f"📁 原文件已备份到: {backup_path}")

            # 保存修复后的文件
            with open(xml_file_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            print(f"💾 修复后的文件已保存")

            return True

        except ET.ParseError as e:
            print(f"❌ 修复失败，仍有解析错误: {e}")

            # 尝试更激进的修复：只保留第一个完整的元素
            lines = cleaned_content.split('\n')
            in_root = False
            root_depth = 0
            cleaned_lines = []

            for line in lines:
                if '<ResultVO>' in line and not in_root:
                    in_root = True
                    cleaned_lines.append(line)
                elif in_root:
                    cleaned_lines.append(line)
                    if '<ResultVO>' in line:
                        root_depth += 1
                    elif '</ResultVO>' in line:
                        root_depth -= 1
                        if root_depth < 0:
                            break

            final_content = '\n'.join(cleaned_lines)

            try:
                ET.fromstring(final_content)
                with open(xml_file_path, 'w', encoding='utf-8') as f:
                    f.write(final_content)
                print("✅ 激进修复成功")
                return True
            except Exception:
                print("❌ 所有修复尝试都失败了")
                return False

    except Exception:
        print(f"❌ 修复过程中出现异常: {e}")
        return False


def batch_fix_xml_files():
    """批量修复所有XML文件"""
    xml_dir = Path("模块字段/backup")
    if not xml_dir.exists():
        print(f"❌ XML目录不存在: {xml_dir}")
        return

    xml_files = list(xml_dir.glob("*.xml"))
    print(f"🔍 找到 {len(xml_files)} 个XML文件")

    success_count = 0
    for xml_file in xml_files:
        print(f"\n处理: {xml_file.name}")
        if validate_and_fix_xml(xml_file):
            success_count += 1

    print(f"\n📊 批量修复结果: {success_count}/{len(xml_files)} 成功")


if __name__ == "__main__":

    if len(sys.argv) > 1:
        # 修复指定文件
        xml_file = sys.argv[1]
        validate_and_fix_xml(xml_file)
    else:
        # 批量修复
        batch_fix_xml_files()
