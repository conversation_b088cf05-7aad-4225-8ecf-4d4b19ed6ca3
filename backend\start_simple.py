import json
import logging
import xml.etree.ElementTree as ET
from pathlib import Path

import uvicorn
from fastapi import FastAPI
from fastapi.responses import FileResponse, RedirectResponse, Response
from fastapi.staticfiles import StaticFiles

"""
简化的启动脚本 - 跳过数据库初始化
"""


# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="YS-API V3.0 Simple",
    description="用友云数据同步和字段配置管理API - 简化版",
    version="3.0.0",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务
project_root = Path(__file__).parent.parent
frontend_dir = project_root / "frontend"
css_dir = frontend_dir / "css"
js_dir = frontend_dir / "js"
migrated_dir = frontend_dir / "migrated"

app.mount(
    "/frontend",
    StaticFiles(
        directory=str(frontend_dir)),
    name="frontend")
app.mount("/css", StaticFiles(directory=str(css_dir)), name="css")
app.mount("/js", StaticFiles(directory=str(js_dir)), name="js")

# 添加迁移页面直接访问路径
if migrated_dir.exists():
    app.mount(
        "/migrated",
        StaticFiles(
            directory=str(migrated_dir)),
        name="migrated")


# 页面路由
@app.get("/")
async def roott():
    """TODO: Add function description."""
    return RedirectResponse(url="/excel-translation.html", status_code=302)


@app.get("/health")
async def health_checkk():
    """TODO: Add function description."""
    return {
        "status": "healthy",
        "service": "YS-API V3.0 Simple",
        "version": "3.0.0"}


@app.get("/database-v2.html")
async def database_v2_pagee():
    """TODO: Add function description."""
    page_path = frontend_dir / "database-v2.html"
    if page_path.exists():
        return FileResponse(str(page_path))
    return {"error": f"File not found: {page_path}"}


@app.get("/unified-field-config.html")
async def unified_field_config_pagee():
    """TODO: Add function description."""
    page_path = frontend_dir / "unified-field-config.html"
    if page_path.exists():
        return FileResponse(str(page_path))
    return {"error": f"File not found: {page_path}"}


@app.get("/field-config.html")
async def field_config_pagee():
    """TODO: Add function description."""
    page_path = frontend_dir / "field-config.html"
    if page_path.exists():
        return FileResponse(str(page_path))
    return {"error": f"File not found: {page_path}"}


@app.get("/excel-translation.html")
async def excel_translation_pagee():
    """TODO: Add function description."""
    page_path = frontend_dir / "excel-translation.html"
    if page_path.exists():
        return FileResponse(str(page_path))
    return {"error": f"File not found: {page_path}"}


@app.get("/test-loading.html")
async def test_loading_pagee():
    """TODO: Add function description."""
    page_path = frontend_dir / "test-loading.html"
    if page_path.exists():
        return FileResponse(str(page_path))
    return {"error": f"File not found: {page_path}"}


@app.get("/favicon.ico")
async def faviconn():
    """TODO: Add function description."""
    return Response(status_code=204)


# 添加基础API端点用于字段配置
@app.get("/api/v1/field-config/modules")
async def get_modules():
    """获取所有模块列表"""
    try:
        # 从配置文件读取模块列表
        config_path = Path(__file__).parent.parent / "config" / "modules.json"
        if config_path.exists():
            with open(config_path, "r", encoding="utf-8") as f:
                modules_config = json.load(f)
                if isinstance(modules_config, list):
                    return {"modules": modules_config}
                return modules_config

        # 默认模块列表
        return {
            "modules": [
                {"name": "material_outbound", "display_name": "材料出库单列表查询"},
                {"name": "purchase_order", "display_name": "采购订单列表"},
                {"name": "purchase_inbound", "display_name": "采购入库单列表"},
                {"name": "product_inbound", "display_name": "产品入库单列表查询"},
                {"name": "purchase_request", "display_name": "请购单列表查询"},
                {"name": "production_order", "display_name": "生产订单列表查询"},
                {"name": "outsourcing_order", "display_name": "委外订单列表"},
                {"name": "outsourcing_inbound", "display_name": "委外入库列表查询"},
                {"name": "outsourcing_request", "display_name": "委外申请列表查询"},
                {"name": "sales_outbound", "display_name": "销售出库列表查询"},
                {"name": "sales_order", "display_name": "销售订单"},
                {"name": "demand_plan", "display_name": "需求计划"},
                {"name": "business_log", "display_name": "业务日志"},
            ]
        }
    except Exception:
        logger.error(f"获取模块列表失败: {e}")
        return {"error": "Failed to get modules", "message": str(e)}


@app.get("/api/v1/field-config/modules/{module_name}/fields")
async def get_module_fields(
    module_name: str, max_depth: int = 10, user_id: str = "default"
):
    """获取指定模块的字段配置"""
    try:
        # 从XML文件读取字段配置
        xml_path = Path(__file__).parent.parent / "模块字段" / f"{module_name}.xml"
        if not xml_path.exists():
            # 尝试用显示名称查找
            module_mapping = {
                "材料出库单列表查询": "材料出库单列表查询.xml",
                "采购订单列表": "采购订单列表.xml",
                "采购入库单列表": "采购入库列表.xml",
                "产品入库单列表查询": "产品入库列表查询.xml",
                "请购单列表查询": "请购单列表查询.xml",
                "生产订单列表查询": "生产订单列表查询.xml",
                "委外订单列表": "委外订单列表.xml",
                "委外入库列表查询": "委外入库列表查询.xml",
                "委外申请列表查询": "委外申请列表查询.xml",
                "销售出库列表查询": "销售出库列表查询.xml",
                "销售订单": "销售订单.xml",
                "需求计划": "需求计划.xml",
                "业务日志": "业务日志.xml",
            }

            xml_filename = module_mapping.get(module_name)
            if xml_filename:
                xml_path = Path(__file__).parent.parent / "模块字段" / xml_filename

        if xml_path.exists():
            # 简化的字段解析
            tree = ET.parse(xml_path)
            root = tree.getroot()

            fields = []
            for field in root.findall(".//field"):
                field_info = {
                    "name": field.get("name", ""),
                    "display_name": field.get("display_name", field.get("name", "")),
                    "type": field.get("type", "string"),
                    "required": field.get("required", "false").lower() == "true",
                    "description": field.get("description", ""),
                    "source": "xml",
                }
                fields.append(field_info)

            return {
                "module_name": module_name,
                "fields": fields,
                "total_count": len(fields),
                "source": "xml_config",
            }
        else:
            # 返回示例字段
            return {
                "module_name": module_name,
                "fields": [
                    {
                        "name": "id",
                        "display_name": "ID",
                        "type": "string",
                        "required": True,
                    },
                    {
                        "name": "name",
                        "display_name": "名称",
                        "type": "string",
                        "required": True,
                    },
                    {
                        "name": "date",
                        "display_name": "日期",
                        "type": "date",
                        "required": False,
                    },
                    {
                        "name": "amount",
                        "display_name": "金额",
                        "type": "number",
                        "required": False,
                    },
                ],
                "total_count": 4,
                "source": "default",
            }
    except Exception:
        logger.error(f"获取模块 {module_name} 字段失败: {e}")
        return {
            "error": "Failed to get fields",
            "message": str(e),
            "module_name": module_name,
        }


if __name__ == "__main__":
    logger.info("启动简化服务器...")
    logger.info(f"前端目录: {frontend_dir}")
    logger.info(f"前端目录存在: {frontend_dir.exists()}")

    uvicorn.run(
        app,
        host="127.0.0.1",
        port=5000,
        log_level="info")  # 修改为端口5000
# 设置日志
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
