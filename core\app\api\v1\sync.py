import asyncio
import traceback
import uuid
from datetime import datetime
from typing import Optional

import structlog
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.config import MODULES_CONFIG
from ...core.database import async_engine
from ...services.auto_sync_scheduler import get_auto_sync_scheduler
from ...services.data_write_manager import DataWriteManager
from ...services.fast_sync_service import run_fast_sync
from ...services.task_service import TaskService
from ...services.zero_downtime_implementation import ZeroDowntimeManager

"""
YS-API V3.0 数据同步和写入API
严格遵循@必读.md要求：数据写入功能API接口

提供功能：
1. 单模块数据写入
2. 批量模块数据写入
3. 写入状态查询
4. 写入监控
"""


router = APIRouter(tags=["数据同步写入"])
logger = structlog.get_logger()


# 请求模型
class SingleModuleWriteRequest(BaseModel):
    """单模块写入请求"""

    module_name: str = Field(..., description="模块名称")
    record_limit: Optional[int] = Field(None, description="记录数限制", ge=1)
    force_recreate_table: bool = Field(False, description="是否强制重建表")
    clear_existing_data: bool = Field(False, description="是否在同步前清空现有数据")


class BatchWriteRequest(BaseModel):
    """批量写入请求"""

    module_names: Optional[List[str]] = Field(
        None, description="模块名称列表（为空则写入所有模块）"
    )
    record_limit: Optional[int] = Field(None, description="每个模块记录数限制", ge=1)
    force_recreate_tables: bool = Field(False, description="是否强制重建所有表")
    clear_existing_data: bool = Field(False, description="是否在同步前清空现有数据")


class EnhancedSyncRequest(BaseModel):
    """增强版同步请求"""

    module_name: str = Field(..., description="模块名称")
    record_limit: Optional[int] = Field(10, description="记录数限制", ge=1)
    use_zero_downtime: bool = Field(True, description="是否使用零停机模式")
    timeout_seconds: Optional[int] = Field(60, description="超时时间（秒）", ge=5)
    clear_existing_data: bool = Field(False, description="是否在同步前清空现有数据")


# 全局写入管理器
write_manager = DataWriteManager()


@router.post(
    "/write/single",
    summary="单模块数据写入",
    description="从API获取数据并写入指定模块的数据库表",
)
async def write_single_module(request: SingleModuleWriteRequest):
    """单模块数据写入"""
    try:
        # 验证模块名称
        valid_modules = [m["name"] for m in MODULES_CONFIG]
        if request.module_name not in valid_modules:
            raise HTTPException(
                status_code=400,
                detail=f"无效的模块名称 '{request.module_name}'，可用模块: {valid_modules}",
            )

        logger.info(
            "开始单模块数据写入",
            module_name=request.module_name,
            record_limit=request.record_limit,
            force_recreate_table=request.force_recreate_table,
        )

        # 执行数据写入
        result = await write_manager.write_single_module(
            module_name=request.module_name,
            record_limit=request.record_limit,
            force_recreate_table=request.force_recreate_table,
            clear_existing_data=request.clear_existing_data,
        )

        if result["success"]:
            logger.info(
                "单模块数据写入成功",
                **{k: v for k, v in result.items() if k != "message"},
            )
        else:
            logger.warning("单模块数据写入失败", result=result)

        return result

    except HTTPException:
        raise
    except Exception:
        logger.error(
            "单模块数据写入API异常", module_name=request.module_name, error=str(e)
        )
        raise HTTPException(status_code=500, detail=f"数据写入失败: {str(e)}")


@router.post(
    "/write/batch",
    summary="批量模块数据写入",
    description="批量从API获取数据并写入多个模块的数据库表",
)
async def write_batch_modules(request: BatchWriteRequest):
    """批量模块数据写入"""
    try:
        # 验证模块名称
        valid_modules = [m["name"] for m in MODULES_CONFIG]

        if request.module_names:
            # 验证指定的模块名称
            invalid_modules = [
                m for m in request.module_names if m not in valid_modules
            ]
            if invalid_modules:
                raise HTTPException(
                    status_code=400,
                    detail=f"无效的模块名称: {invalid_modules}，可用模块: {valid_modules}",
                )

            # 只处理指定模块
            # 注意：当前write_all_modules方法处理所有模块，这里需要修改逻辑
            logger.info(
                "开始指定模块批量数据写入",
                modules=request.module_names,
                record_limit=request.record_limit,
            )

            # 对指定模块逐个执行写入，增加超时保护
            results = {}
            success_count = 0
            failed_count = 0
            total_records_written = 0

            for module_name in request.module_names:
                try:
                    # 每个模块设置10分钟超时
                    result = await asyncio.wait_for(
                        write_manager.write_single_module(
                            module_name=module_name,
                            record_limit=request.record_limit,
                            force_recreate_table=request.force_recreate_tables,
                            clear_existing_data=request.clear_existing_data,
                        ),
                        timeout=600.0,  # 10分钟超时
                    )
                    results[module_name] = result

                    if result["success"]:
                        success_count += 1
                        total_records_written += result.get(
                            "records_written", 0)
                    else:
                        failed_count += 1

                except asyncio.TimeoutError:
                    logger.error(f"模块 {module_name} 同步超时")
                    results[module_name] = {
                        "success": False,
                        "message": f"模块 {module_name} 同步超时（10分钟）",
                        "error": "timeout",
                    }
                    failed_count += 1
                except Exception:
                    logger.error(f"模块 {module_name} 同步异常", error=str(e))
                    results[module_name] = {
                        "success": False,
                        "message": f"模块 {module_name} 同步异常: {str(e)}",
                        "error": str(e),
                    }
                    failed_count += 1

            summary = {
                "success": failed_count == 0,
                "message": f"指定模块批量写入完成: 成功 {success_count} 个，失败 {failed_count} 个",
                "total_modules": len(request.module_names),
                "success_count": success_count,
                "failed_count": failed_count,
                "total_records_written": total_records_written,
                "results": results,
                "completed_at": datetime.now().isoformat(),
            }

        else:
            # 写入所有模块
            logger.info("开始全部模块批量数据写入", record_limit=request.record_limit)

            try:
                # 设置20分钟超时
                summary = await asyncio.wait_for(
                    write_manager.write_all_modules(
                        record_limit=request.record_limit,
                        force_recreate_tables=request.force_recreate_tables,
                        clear_existing_data=request.clear_existing_data,
                    ),
                    timeout=1200.0,  # 20分钟超时
                )
            except asyncio.TimeoutError:
                logger.error("批量写入所有模块超时")
                summary = {
                    "success": False,
                    "message": "批量写入所有模块超时（20分钟）",
                    "error": "timeout",
                    "suggestion": "建议减少并发数或检查网络连接",
                }
            except Exception:
                logger.error("批量写入所有模块异常", error=str(e))
                summary = {
                    "success": False,
                    "message": f"批量写入所有模块异常: {str(e)}",
                    "error": str(e),
                }

        if summary["success"]:
            logger.info(
                "批量数据写入成功",
                **{k: v for k, v in summary.items() if k not in ["results", "message"]},
            )
        else:
            logger.warning(
                "批量数据写入失败",
                summary={k: v for k, v in summary.items() if k != "results"},
            )

        return summary

    except HTTPException:
        raise
    except Exception:
        logger.error("批量数据写入API异常", error=str(e))
        raise HTTPException(status_code=500, detail=f"批量数据写入失败: {str(e)}")


@router.get(
    "/status",
    summary="数据写入状态查询",
    description="查询数据写入状态，包括表创建状态和记录数",
)
async def get_write_status(
    module_name: Optional[str] = Query(
        None, description="模块名称（为空则返回所有模块状态）"
    )
):
    """数据写入状态查询"""
    try:
        logger.info("查询数据写入状态", module_name=module_name)

        # 如果指定了模块名称，验证有效性
        if module_name:
            valid_modules = [m["name"] for m in MODULES_CONFIG]
            if module_name not in valid_modules:
                raise HTTPException(
                    status_code=400,
                    detail=f"无效的模块名称 '{module_name}'，可用模块: {valid_modules}",
                )

        status = await write_manager.get_write_status(module_name)

        if status["success"]:
            logger.info("数据写入状态查询成功", module_name=module_name, status=status)
        else:
            logger.warning("数据写入状态查询失败", status=status)

        return status

    except HTTPException:
        raise
    except Exception:
        logger.error("数据写入状态查询API异常", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"状态查询失败: {str(e)}")


@router.get(
    "/modules",
    summary="获取可用模块列表",
    description="获取所有可用于数据写入的模块列表",
)
async def get_available_modules():
    """获取可用模块列表"""
    try:
        modules = []
        for config in MODULES_CONFIG:
            modules.append(
                {
                    "name": config["name"],
                    "display_name": config.get("display_name", config["name"]),
                    "api_endpoint": config.get("api_endpoint", ""),
                    "description": config.get("description", ""),
                }
            )

        logger.info("获取可用模块列表成功", modules_count=len(modules))

        return {
    "success": True,
    "modules_count": len(modules),
     "modules": modules}

    except Exception:
        logger.error("获取可用模块列表失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取模块列表失败: {str(e)}")


@router.get(
    "/verify/{module_name}",
    summary="数据对比验证",
    description="获取指定模块的API数据样本用于对比验证",
)
async def verify_module_data(
    module_name: str,
    record_limit: int = Query(100, description="样本记录数限制", ge=1, le=1000),
):
    """数据对比验证 - GET方法"""
    try:
        # 验证模块名称
        valid_modules = [m["name"] for m in MODULES_CONFIG]
        if module_name not in valid_modules:
            raise HTTPException(
                status_code=400,
                detail=f"无效的模块名称 '{module_name}'，可用模块: {valid_modules}",
            )

        logger.info(
            "开始数据对比验证", module_name=module_name, record_limit=record_limit
        )

        # 获取API数据样本
        result = await write_manager.get_api_data_sample(
            module_name=module_name, record_limit=record_limit
        )

        if result["success"]:
            logger.info(
                "数据对比验证成功",
                module_name=module_name,
                records_count=result.get("records_count", 0),
            )
        else:
            logger.warning("数据对比验证失败", result=result)

        return result

    except HTTPException:
        raise
    except Exception:
        logger.error("数据对比验证API异常", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"数据对比验证失败: {str(e)}")


@router.post(
    "/verify/{module_name}",
    summary="限量同步验证",
    description="验证指定模块的数据同步功能（限制记录数）",
)
async def verify_module_sync(
    module_name: str,
    record_limit: int = Query(5, description="验证记录数限制", ge=1, le=1000),
):
    """限量同步验证"""
    try:
        # 验证模块名称
        valid_modules = [m["name"] for m in MODULES_CONFIG]
        if module_name not in valid_modules:
            raise HTTPException(
                status_code=400,
                detail=f"无效的模块名称 '{module_name}'，可用模块: {valid_modules}",
            )

        logger.info(
            "开始限量同步验证", module_name=module_name, record_limit=record_limit
        )

        # 执行限量同步（不重建表）
        result = await write_manager.write_single_module(
            module_name=module_name,
            record_limit=record_limit,
            force_recreate_table=False,
        )

        # 添加验证标记
        result["verification_mode"] = True
        result["verification_record_limit"] = record_limit

        if result["success"]:
            logger.info(
                "限量同步验证成功",
                **{k: v for k, v in result.items() if k != "message"},
            )
        else:
            logger.warning("限量同步验证失败", result=result)

        return result

    except HTTPException:
        raise
    except Exception:
        logger.error("限量同步验证API异常", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"限量同步验证失败: {str(e)}")


@router.delete(
    "/tables/{module_name}",
    summary="清空模块数据表",
    description="清空指定模块的数据表内容",
)
async def clear_module_table(module_name: str):
    """清空模块数据表"""
    try:
        # 验证模块名称
        valid_modules = [m["name"] for m in MODULES_CONFIG]
        if module_name not in valid_modules:
            raise HTTPException(
                status_code=400,
                detail=f"无效的模块名称 '{module_name}'，可用模块: {valid_modules}",
            )

        logger.info("开始清空模块数据表", module_name=module_name)

        # 使用中文表名（与数据库表管理器保持一致）
        table_name = write_manager.db_table_manager._get_chinese_table_name(
            module_name)

        # 检查表是否存在
        table_exists = await write_manager.db_table_manager.check_table_exists(
            table_name
        )
        if not table_exists:
            raise HTTPException(
    status_code=404,
     detail=f"表 '{table_name}' 不存在")

        # 清空表数据
        await write_manager._truncate_table(table_name)

        result = {
            "success": True,
            "message": f"成功清空表 '{table_name}' 的数据",
            "module_name": module_name,
            "table_name": table_name,
            "cleared_at": datetime.now().isoformat(),
        }

        logger.info("清空模块数据表成功", **result)

        return result

    except HTTPException:
        raise
    except Exception:
        logger.error("清空模块数据表API异常", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"清空表失败: {str(e)}")


@router.get(
    "/health", summary="数据写入服务健康检查", description="检查数据写入服务的健康状态"
)
async def write_service_health():
    """数据写入服务健康检查"""
    try:
        # 检查YS API连接
        api_test = await write_manager.ys_client.test_connection()

        # 检查数据库连接
        db_test = True
        try:
            conn = await write_manager.db_table_manager.get_connection()
            conn.close()
        except Exception:
            db_test = False

        health_status = {
            "success": True,
            "service": "数据写入服务",
            "status": "healthy" if (api_test and db_test) else "unhealthy",
            "components": {
                "ys_api_client": "connected" if api_test else "disconnected",
                "database": "connected" if db_test else "disconnected",
            },
            "checked_at": datetime.now().isoformat(),
        }

        logger.info("数据写入服务健康检查完成", **health_status)

        return health_status

    except Exception:
        logger.error("数据写入服务健康检查失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@router.post(
    "/all",
    summary="全量数据同步",
    description="执行全部15个模块的数据同步任务（排除物料档案，因为它有独立调度器）",
)
async def sync_all_modules(
    parallel_tasks: int = Query(3, description="并发任务数", ge=1, le=5),
    record_limit: Optional[int] = Query(None, description="每个模块记录数限制", ge=1),
    force_recreate_tables: bool = Query(False, description="是否强制重建所有表"),
    clear_existing_data: bool = Query(True, description="是否在同步前清空现有数据"),
):
    """全量数据同步 - 排除物料档案模块"""
    try:
        logger.info(
            "开始全量数据同步", parallel_tasks=parallel_tasks, record_limit=record_limit
        )

        # 获取所有可用模块，排除物料档案（它有独立的调度器）
        all_modules = [
            m["name"] for m in MODULES_CONFIG if m["name"] != "material_master"
        ]

        logger.info(
            "全量同步模块列表",
            excluded_modules=["material_master"],
            included_modules=all_modules,
            total_modules=len(all_modules),
        )

        # 创建批量写入请求
        batch_request = BatchWriteRequest(
            module_names=all_modules,
            record_limit=record_limit,
            force_recreate_tables=force_recreate_tables,
            clear_existing_data=clear_existing_data,
        )

        # 使用超时保护执行批量写入
        try:
            # 设置超时时间为30分钟（1800秒）
            result = await asyncio.wait_for(
                write_batch_modules(batch_request), timeout=1800.0  # 30分钟超时
            )
        except asyncio.TimeoutError:
            logger.error("全量同步超时（30分钟）")
            return {
                "success": False,
                "message": "全量同步超时，请检查网络连接或减少并发数",
                "error": "timeout",
                "suggestion": "建议减少parallel_tasks参数或检查网络连接",
            }
        except Exception:
            logger.error("全量同步执行异常", error=str(e))
            return {
                "success": False,
                "message": f"全量同步执行异常: {str(e)}",
                "error": str(e),
            }

        # 添加任务ID和额外信息
        if result.get("success"):

            task_id = f"sync_all_{uuid.uuid4().hex[:8]}"

            # 将同步结果存储到全局变量中，供状态查询使用
            global sync_task_results
            if 'sync_task_results' not in globals():
                sync_task_results = {}

            sync_task_results[task_id] = {
                "task_id": task_id,
                "status": "completed",
                "progress": 100,
                "current_operation": "全量同步已完成",
                "total_processed": result.get("total_records_written", 0),
                "started_at": datetime.now().isoformat(),
                "completed_at": datetime.now().isoformat(),
                "module_count": len(all_modules),
                "success_count": result.get("success_count", 0),
                "failed_count": result.get("failed_count", 0),
                "duration_seconds": 0,
                "message": f"全量同步完成：成功 {result.get('success_count', 0)} 个模块，失败 {result.get('failed_count', 0)} 个模块（物料档案独立管理）",
                "sync_result": result,
                "excluded_modules": ["material_master"],
            }

            return {
                "success": True,
                "message": "全量同步任务启动成功（物料档案独立管理）",
                "data": {
                    "task_id": task_id,
                    "module_count": len(all_modules),
                    "parallel_tasks": parallel_tasks,
                    "record_limit": record_limit,
                    "force_recreate_tables": force_recreate_tables,
                    "modules": all_modules,
                    "excluded_modules": ["material_master"],
                    "started_at": datetime.now().isoformat(),
                    "note": "物料档案由独立调度器管理，不包含在全量同步中",
                },
                "sync_result": result,
            }
        else:
            return {
                "success": False,
                "message": f"全量同步任务启动失败: {result.get('message', '未知错误')}",
                "error": result,
            }

    except Exception:
        logger.error("全量数据同步API异常", error=str(e))
        raise HTTPException(status_code=500, detail=f"全量同步失败: {str(e)}")


# 添加同步状态查询API端点
@router.get(
    "/status/{task_id}",
    summary="查询同步任务状态",
    description="根据任务ID查询同步任务的执行状态和进度",
)
async def get_sync_task_status(task_id: str):
    """查询同步任务状态"""
    try:
        logger.info("查询同步任务状态", task_id=task_id)

        # 获取真实任务状态数据

        task_service = TaskService()
        task_status = await task_service.get_task_status(task_id)

        logger.info(
            "同步任务状态查询成功", task_id=task_id, status=task_status["status"]
        )

        return {"success": True, "data": task_status, "message": "获取任务状态成功"}

    except Exception:
        logger.error("查询同步任务状态失败", task_id=task_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"查询任务状态失败: {str(e)}")


# 关闭资源的生命周期事件
@router.post(
    "/fast-sync/all",
    summary="V2风格快速全量同步",
    description="基于V2架构优化的高性能同步，从30分钟优化到3分钟",
)
async def fast_sync_all_modules(
    record_limit: Optional[int] = Query(None, description="每个模块记录数限制", ge=1),
    force_recreate_tables: bool = Query(False, description="是否强制重建所有表"),
    clear_existing_data: bool = Query(True, description="是否在同步前清空现有数据"),
):
    """V2风格快速全量同步 - 性能优化版本"""
    try:
        logger.info(
            "开始V2风格快速全量同步",
            record_limit=record_limit,
            force_recreate_tables=force_recreate_tables,
        )

        # 执行快速同步
        result = run_fast_sync(
            record_limit=record_limit,
            force_recreate_tables=force_recreate_tables,
            clear_existing_data=clear_existing_data,
        )

        # 格式化返回结果
        return {
            "success": True,
            "data": {
                "sync_type": "fast_sync_v2_style",
                "total_duration": result["total_duration"],
                "start_time": result["start_time"],
                "end_time": result["end_time"],
                "summary": result["summary"],
                "modules": result["modules"],
            },
            "message": f"快速同步完成，耗时 {result['total_duration']:.1f} 秒",
        }

    except Exception:
        logger.error("V2风格快速同步失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"快速同步失败: {str(e)}")


@router.on_event("shutdown")
async def shutdown_write_manager():
    """关闭数据写入管理器资源"""
    try:
        await write_manager.close()
        logger.info("数据写入管理器资源已关闭")
    except Exception:
        logger.error("关闭数据写入管理器资源失败", error=str(e))


# ==================== 改进的同步系统API端点 ====================


# 导入改进的同步系统
# import sys
# from pathlib import Path
# 添加项目根目录到路径
# sys.path.append(str(Path(__file__).parent.parent.parent.parent.parent))
# from improved_sync_system import ImprovedSyncSystem

# 全局同步管理器
# class GlobalSyncManager:
#     def __init___(self):

    """TODO: Add function description."""
#         self.sync_system = None
#         self.sync_task = None
#         self.is_running = False
#         self.progress_logs = []
#         self.current_status = "idle"
#
#     def start_syncc(self):

    """TODO: Add function description."""
#         """启动同步"""
#         if self.is_running:
#             return {
#                 "success": False,
#                 "message": "同步正在进行中，请先停止当前同步"
#             }
#
#         # 创建新的同步系统
#         self.sync_system = ImprovedSyncSystem()
#
#         # 设置回调函数
#         self.sync_system.set_callbacks(
#             progress_callback=self._on_progress,
#             error_callback=self._on_error,
#             success_callback=self._on_success
#         )
#
#         # 启动异步任务
#         self.sync_task = asyncio.create_task(self._run_sync())
#         self.is_running = True
#         self.current_status = "running"
#         self.progress_logs = []
#
#         return {
#             "success": True,
#             "message": "同步已启动",
#             "task_id": f"sync_{int(datetime.now().timestamp())}"
#         }

#     def stop_syncc(self):

    """TODO: Add function description."""
#         """停止同步"""
#         if not self.is_running or not self.sync_system:
#             return {
#                 "success": False,
#                 "message": "没有正在运行的同步任务"
#             }
#
#         # 发送停止信号
#         success = self.sync_system.stop_sync()
#
#         if success:
#             self.current_status = "stopping"
#             return {
#                 "success": True,
#                 "message": "停止信号已发送，正在停止同步..."
#             }
#         else:
#             return {
#                 "success": False,
#                 "message": "停止失败，请稍后重试"
#             }

#     def get_statuss(self):

    """TODO: Add function description."""
#         """获取同步状态"""
#         if not self.sync_system:
#             return {
#                 "success": True,
#                 "data": {
#                     "is_running": False,
#                     "current_status": "idle",
#                     "progress_logs": [],
#                     "completed_modules": 0,
#                     "total_modules": 15,
#                     "current_module": None
#                 }
#             }
#
#         return {
#             "success": True,
#             "data": {
#                 "is_running": self.is_running,
#                 "current_status": self.current_status,
#                 "progress_logs": self.progress_logs[-20:],  # 最近20条日志
#                 "completed_modules": self.sync_system.completed_modules,
#                 "total_modules": self.sync_system.total_modules,
#                 "current_module": self.sync_system.current_module,
#                 "success_modules": self.sync_system.success_modules,
#                 "failed_modules": self.sync_system.failed_modules
#             }
#         }
#
#     def _on_progresss(self, message: str, level: str):

    """TODO: Add function description."""
#         """进度回调"""
#         log_entry = {
#             "timestamp": datetime.now().isoformat(),
#             "message": message,
#             "level": level
#         }
#         self.progress_logs.append(log_entry)
#
#         # 限制日志数量
#         if len(self.progress_logs) > 100:
#             self.progress_logs = self.progress_logs[-50:]
#
#     def _on_errorr(self, error: str):

    """TODO: Add function description."""
#         """错误回调"""
#         self._on_progress(f" 错误: {error}", "error")
#
#     def _on_successs(self, result: str):

    """TODO: Add function description."""
#         """成功回调"""
#         self._on_progress(f" 成功: {result}", "success")
#
#     async def _run_syncc(self):

    """TODO: Add function description."""
#         """运行同步任务"""
#         try:
#             await self.sync_system.start_sync()
#             self.current_status = "completed"
#         except Exception:
#             self.current_status = "error"
#             self._on_error(str(e))
#         finally:
#             self.is_running = False

# 全局实例
# global_sync_manager = GlobalSyncManager()

# 这部分路由已在下面重复定义，删除重复部分

# @router.get("/status-improved",
#            summary="获取改进同步系统状态",
#            description="获取详细的同步进度和状态信息")
# async def get_improved_sync_statuss():

    """TODO: Add function description."""
#     """获取改进同步系统状态"""
#     try:
#         result = global_sync_manager.get_status()
#         return result
#     except Exception:
#         logger.error("获取改进同步状态失败", error=str(e))
#         return {
#             "success": False,
#             "message": f"获取状态失败: {str(e)}"
#         }

# ==================== 改进的同步系统API端点结束 ====================

# ==================== 改进的同步系统 ====================

# 全局状态管理
improved_sync_status = {
    "is_running": False,
    "should_stop": False,
    "current_status": "idle",
    "current_module": None,
    "completed_modules": 0,
    "total_modules": 15,
    "progress_logs": [],
    "success_modules": [],
    "failed_modules": [],
    "start_time": None,
    "last_update": None,
}

# 全局写入管理器（用于改进的同步系统）
improved_write_manager = DataWriteManager()


@router.post(
    "/start-improved",
    summary="启动改进的同步系统",
    description="启动改进的同步系统，支持有效停止和中文表名",
)
async def start_improved_sync():
    """启动改进的同步系统"""
    global improved_sync_status

    if improved_sync_status["is_running"]:
        return {"success": False, "message": "同步系统已在运行中，请先停止当前同步"}

    try:
        logger.info("启动改进的同步系统")

        # 重置状态
        improved_sync_status.update(
            {
                "is_running": True,
                "should_stop": False,
                "current_status": "running",
                "current_module": None,
                "completed_modules": 0,
                "progress_logs": [],
                "success_modules": [],
                "failed_modules": [],
                "start_time": datetime.now().isoformat(),
                "last_update": datetime.now().isoformat(),
            }
        )

        # 启动后台任务
        asyncio.create_task(run_improved_sync())

        return {
            "success": True,
            "message": "改进的同步系统已启动",
            "data": {
                "total_modules": improved_sync_status["total_modules"],
                "started_at": improved_sync_status["start_time"],
            },
        }

    except Exception:
        logger.error("启动改进的同步系统失败", error=str(e))
        return {"success": False, "message": f"启动失败: {str(e)}"}


@router.post(
    "/stop-improved", summary="停止改进的同步系统", description="有效停止改进的同步系统"
)
async def stop_improved_sync():
    """停止改进的同步系统"""
    global improved_sync_status

    if not improved_sync_status["is_running"]:
        return {"success": False, "message": "当前没有正在运行的同步操作"}

    try:
        logger.info("停止改进的同步系统")

        # 设置停止标志
        improved_sync_status["should_stop"] = True
        improved_sync_status["current_status"] = "stopping"
        improved_sync_status["last_update"] = datetime.now().isoformat()

        # 添加停止日志
        add_improved_sync_log("🛑 收到停止信号，正在停止同步...", "warning")

        return {"success": True, "message": "停止信号已发送"}

    except Exception:
        logger.error("停止改进的同步系统失败", error=str(e))
        return {"success": False, "message": f"停止失败: {str(e)}"}


@router.post("/stop", summary="停止同步操作", description="停止正在进行的同步操作")
async def stop_sync():
    """停止同步操作"""
    global improved_sync_status

    if not improved_sync_status["is_running"]:
        return {
            "success": True,
            "message": "当前没有正在进行的同步操作",
            "data": {"was_running": False},
        }

    try:
        logger.info("收到停止同步请求")

        # 设置停止标志
        improved_sync_status["should_stop"] = True
        improved_sync_status["current_status"] = "stopping"
        improved_sync_status["last_update"] = datetime.now().isoformat()

        # 添加停止日志
        add_improved_sync_log("🛑 收到停止信号，正在停止同步...", "warning")

        return {
            "success": True,
            "message": "已发送停止信号，同步操作将在当前模块完成后停止",
            "data": {
                "was_running": True,
                "current_operation": improved_sync_status["current_status"],
                "running_since": improved_sync_status["start_time"],
            },
        }

    except Exception:
        logger.error("停止同步操作失败", error=str(e))
        return {"success": False, "message": f"停止失败: {str(e)}"}


@router.get(
    "/status-improved",
    summary="查询改进的同步系统状态",
    description="获取改进的同步系统的详细状态",
)
async def get_improved_sync_status():
    """查询改进的同步系统状态"""
    try:
        return {"success": True, "data": improved_sync_status.copy()}
    except Exception:
        logger.error("查询改进的同步系统状态失败", error=str(e))
        return {"success": False, "message": f"查询状态失败: {str(e)}"}


@router.post(
    "/single-improved",
    summary="改进的单个模块同步",
    description="使用改进的同步系统同步单个模块",
)
async def start_improved_single_sync(request: SingleModuleWriteRequest):
    """改进的单个模块同步"""
    global improved_sync_status

    # 检查是否有其他同步正在运行
    if improved_sync_status["is_running"]:
        return {"success": False, "message": "有其他同步正在运行，请先停止当前同步"}

    try:
        logger.info("启动改进的单个模块同步", module_name=request.module_name)

        # 验证模块名称
        valid_modules = [m["name"] for m in MODULES_CONFIG]
        if request.module_name not in valid_modules:
            return {
                "success": False,
                "message": f"无效的模块名称 '{request.module_name}'，可用模块: {valid_modules}",
            }

        # 设置单个模块同步状态
        improved_sync_status.update(
            {
                "is_running": True,
                "should_stop": False,
                "current_status": "running",
                "current_module": request.module_name,
                "completed_modules": 0,
                "total_modules": 1,
                "progress_logs": [],
                "success_modules": [],
                "failed_modules": [],
                "start_time": datetime.now().isoformat(),
                "last_update": datetime.now().isoformat(),
            }
        )

        # 获取中文表名
        chinese_table_names = {
            'applyorder': '请购单',
            'inventory': '库存管理',
            'inventory_report': '现存量报表',
            'material_master': '物料档案',
            'materialout': '材料出库',
            'product_receipt': '产品入库',
            'production_order': '生产订单',
            'purchase_order': '采购订单',
            'purchase_receipt': '采购入库',
            'requirements_planning': '需求计划',
            'sales_order': '销售订单',
            'sales_out': '销售出库',
            'subcontract_order': '委外订单',
            'subcontract_receipt': '委外入库',
            'subcontract_requisition': '委外申请',
        }

        chinese_name = chinese_table_names.get(
    request.module_name, request.module_name)

        # 添加开始日志
        add_improved_sync_log(f" 开始同步: {chinese_name}", "info")

        # 启动后台任务
        asyncio.create_task(run_improved_single_sync(request))

        return {
            "success": True,
            "message": f"改进的单个模块同步已启动: {chinese_name}",
            "data": {
                "module_name": request.module_name,
                "chinese_name": chinese_name,
                "started_at": improved_sync_status["start_time"],
            },
        }

    except Exception:
        logger.error("启动改进的单个模块同步失败", error=str(e))
        return {"success": False, "message": f"启动失败: {str(e)}"}


async def run_improved_single_sync(request: SingleModuleWriteRequest):
    """运行改进的单个模块同步"""
    global improved_sync_status

    try:
        # 添加调试日志
        logger.info("开始执行单个模块同步", module_name=request.module_name)
        add_improved_sync_log(f" 开始执行: {request.module_name}", "info")

        # 执行同步（添加超时保护）
        add_improved_sync_log(f" 正在获取数据...", "info")

        # 设置超时时间为5分钟
        timeout_seconds = 300

        try:
            result = await asyncio.wait_for(
                improved_write_manager.write_single_module(
                    module_name=request.module_name,
                    record_limit=request.record_limit,
                    force_recreate_table=request.force_recreate_table,
                    clear_existing_data=request.clear_existing_data,
                ),
                timeout=timeout_seconds,
            )
        except asyncio.TimeoutError:
            logger.error(
                "单个模块同步超时",
                module_name=request.module_name,
                timeout=timeout_seconds,
            )
            add_improved_sync_log(f" 同步超时（{timeout_seconds}秒）", "error")

            # 设置超时状态
            improved_sync_status["failed_modules"].append(request.module_name)
            improved_sync_status["is_running"] = False
            improved_sync_status["current_status"] = "timeout"
            improved_sync_status["last_update"] = datetime.now().isoformat()
            add_improved_sync_log(" 单个模块同步超时", "error")
            return

        add_improved_sync_log(f" 同步执行完成，处理结果...", "info")
        logger.info(
            "单个模块同步执行完成", module_name=request.module_name, result=result
        )

        # 获取中文表名
        chinese_table_names = {
            'applyorder': '请购单',
            'inventory': '库存管理',
            'inventory_report': '现存量报表',
            'material_master': '物料档案',
            'materialout': '材料出库',
            'product_receipt': '产品入库',
            'production_order': '生产订单',
            'purchase_order': '采购订单',
            'purchase_receipt': '采购入库',
            'requirements_planning': '需求计划',
            'sales_order': '销售订单',
            'sales_out': '销售出库',
            'subcontract_order': '委外订单',
            'subcontract_receipt': '委外入库',
            'subcontract_requisition': '委外申请',
        }

        chinese_name = chinese_table_names.get(
    request.module_name, request.module_name)

        if result["success"]:
            improved_sync_status["success_modules"].append(request.module_name)
            improved_sync_status["completed_modules"] = 1
            records = result.get("records_written", 0)
            add_improved_sync_log(
                f" {chinese_name} 同步成功: {records} 条记录", "success"
            )
        else:
            improved_sync_status["failed_modules"].append(request.module_name)
            error_msg = result.get("message", "未知错误")
            add_improved_sync_log(
    f" {chinese_name} 同步失败: {error_msg}", "error")

        # 完成同步
        improved_sync_status["is_running"] = False
        improved_sync_status["current_status"] = "completed"
        improved_sync_status["last_update"] = datetime.now().isoformat()

        if result["success"]:
            add_improved_sync_log(" 单个模块同步完成", "success")
        else:
            add_improved_sync_log(" 单个模块同步失败", "warning")

    except Exception:
        logger.error(
            "改进的单个模块同步异常", error=str(e), module_name=request.module_name
        )
        add_improved_sync_log(f"💥 同步过程中发生异常: {str(e)}", "error")

        # 添加详细的错误信息

        error_details = traceback.format_exc()
        logger.error("异常详情", error_details=error_details)

        improved_sync_status["failed_modules"].append(request.module_name)
        improved_sync_status["is_running"] = False
        improved_sync_status["current_status"] = "error"
        improved_sync_status["last_update"] = datetime.now().isoformat()
        add_improved_sync_log(f" 单个模块同步异常: {str(e)}", "error")


async def run_improved_sync():
    """运行改进的同步系统"""
    global improved_sync_status

    modules = [
        'applyorder',
        'inventory',
        'inventory_report',
        'material_master',
        'materialout',
        'product_receipt',
        'production_order',
        'purchase_order',
        'purchase_receipt',
        'requirements_planning',
        'sales_order',
        'sales_out',
        'subcontract_order',
        'subcontract_receipt',
        'subcontract_requisition',
    ]

    chinese_table_names = {
        'applyorder': '请购单',
        'inventory': '库存管理',
        'inventory_report': '现存量报表',
        'material_master': '物料档案',
        'materialout': '材料出库',
        'product_receipt': '产品入库',
        'production_order': '生产订单',
        'purchase_order': '采购订单',
        'purchase_receipt': '采购入库',
        'requirements_planning': '需求计划',
        'sales_order': '销售订单',
        'sales_out': '销售出库',
        'subcontract_order': '委外订单',
        'subcontract_receipt': '委外入库',
        'subcontract_requisition': '委外申请',
    }

    try:
        add_improved_sync_log(" 开始改进的同步系统", "info")

        for i, module in enumerate(modules):
            # 检查停止信号
            if improved_sync_status["should_stop"]:
                add_improved_sync_log(
    f"🛑 同步已停止 ({i}/{len(modules)})", "warning")
                improved_sync_status["current_status"] = "stopped"
                improved_sync_status["is_running"] = False
                improved_sync_status["current_module"] = None
                break

            improved_sync_status["current_module"] = module
            chinese_name = chinese_table_names.get(module, module)

            add_improved_sync_log(
                f" 同步中: {chinese_name} ({i+1}/{len(modules)})", "info"
            )

            try:
                result = await improved_write_manager.write_single_module(
                    module_name=module,
                    force_recreate_table=False,
                    clear_existing_data=True,
                )

                if result["success"]:
                    improved_sync_status["success_modules"].append(module)
                    improved_sync_status["completed_modules"] += 1
                    records = result.get("records_written", 0)
                    add_improved_sync_log(
                        f" {chinese_name} 同步成功: {records} 条记录", "success"
                    )
                else:
                    improved_sync_status["failed_modules"].append(module)
                    error_msg = result.get("message", "未知错误")
                    add_improved_sync_log(
                        f" {chinese_name} 同步失败: {error_msg}", "error"
                    )

            except Exception:
                improved_sync_status["failed_modules"].append(module)
                add_improved_sync_log(
    f" {chinese_name} 同步异常: {str(e)}", "error")

            # 更新状态
            improved_sync_status["last_update"] = datetime.now().isoformat()

            # 再次检查停止信号（在模块完成后）
            if improved_sync_status["should_stop"]:
                add_improved_sync_log(
                    f"🛑 同步已停止 ({i+1}/{len(modules)})", "warning"
                )
                improved_sync_status["current_status"] = "stopped"
                improved_sync_status["is_running"] = False
                improved_sync_status["current_module"] = None
                break

            # 添加短暂延迟，让停止信号有机会被处理
            await asyncio.sleep(0.1)

        # 完成同步
        improved_sync_status["is_running"] = False
        improved_sync_status["current_status"] = "completed"
        improved_sync_status["current_module"] = None
        improved_sync_status["last_update"] = datetime.now().isoformat()

        success_count = len(improved_sync_status["success_modules"])
        failed_count = len(improved_sync_status["failed_modules"])

        if failed_count == 0:
            add_improved_sync_log(
                f" 改进的同步完成！成功同步 {success_count}/{len(modules)} 个模块",
                "success",
            )
        else:
            add_improved_sync_log(
                f" 改进的同步完成！成功: {success_count}，失败: {failed_count}",
                "warning",
            )

    except Exception:
        logger.error("改进的同步系统异常", error=str(e))
        improved_sync_status["is_running"] = False
        improved_sync_status["current_status"] = "error"
        improved_sync_status["last_update"] = datetime.now().isoformat()
        add_improved_sync_log(f" 改进的同步系统异常: {str(e)}", "error")


def add_improved_sync_log(message: str, level: str = "info"):
    """添加改进的同步日志"""
    global improved_sync_status

    log_entry = {
        "message": message,
        "level": level,
        "timestamp": datetime.now().isoformat(),
    }

    improved_sync_status["progress_logs"].append(log_entry)

    # 保持日志数量不超过50条
    if len(improved_sync_status["progress_logs"]) > 50:
        improved_sync_status["progress_logs"] = improved_sync_status["progress_logs"][
            -50:
        ]


# ==================== 改进的同步系统结束 ====================

# ==================== 零停机同步系统 ====================


@router.post(
    "/zero-downtime-sync",
    summary="触发零停机同步",
    description="执行零停机数据同步，使用影子表技术避免数据真空期",
)
async def trigger_zero_downtime_sync(
    modules: Optional[List[str]] = Query(
        None, description="指定模块列表（为空则使用配置的模块）"
    )
):
    """触发零停机同步"""
    try:

        scheduler = get_auto_sync_scheduler()
        result = await scheduler.trigger_zero_downtime_sync(modules)

        return {
            "success": result.get("success", False),
            "message": result.get("message", ""),
            "details": result,
        }

    except Exception:
        logger.error("零停机同步API异常", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get(
    "/zero-downtime-status",
    summary="获取零停机同步状态",
    description="获取零停机同步的切换历史和状态信息",
)
async def get_zero_downtime_status():
    """获取零停机同步状态"""
    try:

        async def session_factoryy():
    """TODO: Add function description."""
            return AsyncSession(async_engine)

        manager = ZeroDowntimeManager(session_factory)

        # 获取切换历史
        history_result = await manager.get_switch_history(limit=10)

        return {"success": True, "history": history_result.get("history", [])}

    except Exception:
        logger.error("获取零停机状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/emergency-rollback/{module_name}",
    summary="应急回滚指定模块",
    description="在零停机同步出现问题时，快速回滚到上一个备份版本",
)


async def emergency_rollback(module_name: str):
    """应急回滚指定模块"""
    try:


        async def session_factoryy():


    """TODO: Add function description."""
            return AsyncSession(async_engine)

        manager = ZeroDowntimeManager(session_factory)
        result = await manager.emergency_rollback(module_name)

        return result

    except Exception:
        logger.error("应急回滚失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/initialize-zero-downtime/{module_name}",
    summary="初始化模块零停机表结构",
    description="为指定模块初始化零停机所需的影子表和模板表",
)


async def initialize_zero_downtime_tables(module_name: str):
    """初始化模块零停机表结构"""
    try:


        async def session_factoryy():


    """TODO: Add function description."""
            return AsyncSession(async_engine)

        manager = ZeroDowntimeManager(session_factory)
        result = await manager.initialize_zero_downtime_tables(module_name)

        return result

    except Exception:
        logger.error("初始化零停机表结构失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


# ==================== 零停机同步系统结束 ====================
