{"log": {"level": "info", "format": "json", "output": {"type": "file", "filename": "logs/ys-api.log", "rotation": {"enabled": true, "maxSize": "100MB", "maxFiles": 30, "maxAge": "30d", "compress": true, "compressionLevel": 6}}, "performance": {"async": true, "bufferSize": 1000, "flushInterval": "5s", "sampling": {"enabled": true, "rate": {"trace": 0.01, "debug": 0.1, "info": 1.0, "warn": 1.0, "error": 1.0, "fatal": 1.0}}}, "categories": {"system": {"level": "info", "output": {"filename": "logs/system.log", "rotation": {"maxSize": "50MB", "maxFiles": 10}}}, "business": {"level": "info", "output": {"filename": "logs/business.log", "rotation": {"maxSize": "200MB", "maxFiles": 50}}}, "performance": {"level": "warn", "output": {"filename": "logs/performance.log", "rotation": {"maxSize": "100MB", "maxFiles": 20}}}, "security": {"level": "info", "output": {"filename": "logs/security.log", "rotation": {"maxSize": "100MB", "maxFiles": 30, "retention": "90d"}}}, "error": {"level": "error", "output": {"filename": "logs/error.log", "rotation": {"maxSize": "500MB", "maxFiles": 100, "retention": "180d"}}}}, "transports": {"elasticsearch": {"enabled": true, "hosts": ["*************:9200", "localhost:9200"], "fallback_mode": "file", "connection_timeout": "10s", "max_retries": 5, "retry_on_timeout": true, "index": "ys-api-logs", "batchSize": 100, "flushInterval": "10s", "timeout": "5s", "health_check": {"enabled": true, "interval": "30s", "endpoint": "/_cluster/health"}, "mapping": {"timestamp": {"type": "date"}, "level": {"type": "keyword"}, "message": {"type": "text"}, "context.type": {"type": "keyword"}, "context.module": {"type": "keyword"}, "error.code": {"type": "keyword"}, "user.id": {"type": "keyword"}, "performance.duration": {"type": "float"}}}, "prometheus": {"enabled": true, "endpoint": "/metrics", "metrics": {"log_entries_total": {"type": "counter", "help": "Total number of log entries", "labels": ["level", "category"]}, "log_errors_total": {"type": "counter", "help": "Total number of error log entries", "labels": ["error_type", "module"]}, "log_performance_duration": {"type": "histogram", "help": "Performance operation duration", "buckets": [0.1, 0.5, 1.0, 2.0, 5.0, 10.0], "labels": ["operation"]}}}}, "filters": {"sensitiveData": {"enabled": true, "fields": ["password", "token", "secret", "key", "credential", "ssn", "creditCard", "phone", "email"], "replacement": "[REDACTED]"}, "noiseReduction": {"enabled": true, "rules": [{"condition": "message contains 'health check'", "action": "sample", "rate": 0.01}, {"condition": "level == 'debug' and context.type == 'validation'", "action": "sample", "rate": 0.1}, {"condition": "context.type == 'performance' and performance.duration < 100", "action": "drop"}]}}, "alerts": {"enabled": true, "rules": [{"name": "high_error_rate", "condition": "error_count > 10 in 5m", "severity": "critical", "channels": ["email", "slack"], "cooldown": "15m"}, {"name": "slow_performance", "condition": "avg(performance.duration) > 2000 in 10m", "severity": "warning", "channels": ["slack"], "cooldown": "30m"}, {"name": "disk_space_low", "condition": "log_disk_usage > 80%", "severity": "warning", "channels": ["email"], "cooldown": "60m"}]}, "cleanup": {"enabled": true, "schedule": "0 2 * * *", "rules": [{"pattern": "logs/*.log.gz", "maxAge": "90d", "action": "delete"}, {"pattern": "logs/debug-*.log", "maxAge": "7d", "action": "compress"}, {"pattern": "logs/temp-*.log", "maxAge": "1d", "action": "delete"}]}, "monitoring": {"health": {"enabled": true, "endpoint": "/health/logging", "checks": [{"name": "log_file_writable", "type": "file_access", "target": "logs/ys-api.log"}, {"name": "elasticsearch_connection", "type": "http", "target": "http://localhost:9200/_cluster/health"}, {"name": "disk_space", "type": "disk_usage", "target": "logs/", "threshold": 85}, {"name": "log_buffer_size", "type": "memory", "threshold": 100}]}, "statistics": {"enabled": true, "interval": "1m", "metrics": ["entries_per_second", "errors_per_minute", "average_entry_size", "buffer_utilization", "disk_usage_percentage"]}}}}