#!/bin/bash
# 清理旧屎山代码脚本

echo "🗑️ 开始清理旧屎山代码..."

# 检查参数
if [ $# -lt 1 ]; then
    echo "使用方法: $0 <模块名称> [--force]"
    echo "示例: $0 '材料出库单列表查询'"
    echo "强制删除: $0 '材料出库单列表查询' --force"
    exit 1
fi

MODULE_NAME="$1"
FORCE_DELETE="$2"

# 检查配置文件
CONFIG_FILE="config/migration_status.json"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 错误: 迁移配置文件不存在: $CONFIG_FILE"
    exit 1
fi

# 检查模块迁移状态
MODULE_STATUS=$(python3 -c "
import json
with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)
module_config = config.get('modules', {}).get('$MODULE_NAME', {})
print(module_config.get('migration_stage', 'not_started'))
")

if [ "$MODULE_STATUS" != "completed" ] && [ "$FORCE_DELETE" != "--force" ]; then
    echo "❌ 错误: 模块 '$MODULE_NAME' 迁移未完成 (状态: $MODULE_STATUS)"
    echo "只有迁移完成的模块才能安全删除旧代码"
    echo "如果确定要强制删除，请使用 --force 参数"
    exit 1
fi

# 创建清理目录
mkdir -p graveyard/$(date +%Y%m%d)
GRAVEYARD_DIR="graveyard/$(date +%Y%m%d)/$MODULE_NAME"
mkdir -p "$GRAVEYARD_DIR"

echo "📋 模块: $MODULE_NAME"
echo "📊 迁移状态: $MODULE_STATUS"
echo "🗂️ 备份目录: $GRAVEYARD_DIR"

# 确认操作
if [ "$FORCE_DELETE" != "--force" ]; then
    echo "⚠️ 即将删除旧代码，已备份到 graveyard 目录"
    read -p "确认继续？[y/N] " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 操作已取消"
        exit 1
    fi
fi

# 查找模块相关文件
echo "🔍 查找模块相关文件..."

# 文件查找函数
find_module_files() {
    local module_name="$1"
    local search_paths=("backend/app" "frontend" "tests" "config")
    
    echo "查找包含模块名称的文件..."
    for path in "${search_paths[@]}"; do
        if [ -d "$path" ]; then
            # 查找文件名包含模块名的文件
            find "$path" -type f \( -name "*.py" -o -name "*.js" -o -name "*.html" -o -name "*.json" -o -name "*.xml" \) \
                -exec grep -l "$module_name" {} \; 2>/dev/null
            
            # 查找目录名包含模块名的目录
            find "$path" -type d -name "*$(echo $module_name | sed 's/[^a-zA-Z0-9]/_/g')*" 2>/dev/null
        fi
    done | sort -u
}

# 查找文件
MODULE_FILES=$(find_module_files "$MODULE_NAME")

if [ -z "$MODULE_FILES" ]; then
    echo "⚠️ 警告: 未找到模块相关文件"
else
    echo "📁 找到以下相关文件:"
    echo "$MODULE_FILES" | while read file; do
        echo "  - $file"
    done
fi

# 备份文件函数
backup_files() {
    echo "$MODULE_FILES" | while read file; do
        if [ -f "$file" ]; then
            # 创建目录结构
            local backup_path="$GRAVEYARD_DIR/$(dirname "$file")"
            mkdir -p "$backup_path"
            
            # 复制文件
            cp "$file" "$GRAVEYARD_DIR/$file"
            echo "📄 已备份: $file"
        elif [ -d "$file" ]; then
            # 复制整个目录
            cp -r "$file" "$GRAVEYARD_DIR/"
            echo "📁 已备份目录: $file"
        fi
    done
}

# 删除文件函数
delete_files() {
    echo "$MODULE_FILES" | while read file; do
        if [ -f "$file" ]; then
            # 检查文件是否只属于这个模块
            local other_modules=$(python3 -c "
import json
with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)
modules = list(config.get('modules', {}).keys())
modules.remove('$MODULE_NAME')

with open('$file', 'r', encoding='utf-8', errors='ignore') as f:
    content = f.read()

found_modules = []
for module in modules:
    if module in content:
        found_modules.append(module)

print(len(found_modules))
")
            
            if [ "$other_modules" -eq 0 ]; then
                rm "$file"
                echo "🗑️ 已删除: $file"
            else
                echo "⚠️ 保留 $file (包含其他模块引用)"
            fi
        elif [ -d "$file" ]; then
            # 检查目录是否为空或只包含当前模块文件
            if [ -z "$(ls -A "$file" 2>/dev/null)" ]; then
                rmdir "$file"
                echo "🗑️ 已删除空目录: $file"
            else
                echo "⚠️ 保留 $file (目录非空)"
            fi
        fi
    done
}

# 执行备份
echo "💾 开始备份文件..."
backup_files

# 执行删除
echo "🗑️ 开始删除文件..."
delete_files

# 更新迁移配置
echo "📝 更新迁移配置..."
python3 -c "
import json
from datetime import datetime

with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)

if '$MODULE_NAME' in config.get('modules', {}):
    config['modules']['$MODULE_NAME']['legacy_code_removed'] = True
    config['modules']['$MODULE_NAME']['removal_date'] = datetime.now().isoformat()
    config['modules']['$MODULE_NAME']['backup_location'] = '$GRAVEYARD_DIR'

with open('$CONFIG_FILE', 'w') as f:
    json.dump(config, f, ensure_ascii=False, indent=2)

print('✅ 配置已更新')
"

# 生成清理报告
echo "📄 生成清理报告..."
cat > "reports/cleanup_$(echo $MODULE_NAME | sed 's/[^a-zA-Z0-9]/_/g')_$(date +%Y%m%d_%H%M%S).md" << EOF
# 屎山代码清理报告

## 清理信息
- **模块名称**: $MODULE_NAME
- **清理时间**: $(date)
- **操作人员**: $(whoami)
- **迁移状态**: $MODULE_STATUS
- **备份位置**: $GRAVEYARD_DIR

## 清理文件列表
$(echo "$MODULE_FILES" | sed 's/^/- /')

## 安全措施
- ✅ 所有文件已备份到 graveyard 目录
- ✅ 多模块共享文件已保留
- ✅ 空目录已清理
- ✅ 迁移配置已更新

## 回滚方案
如需回滚，执行以下命令：
\`\`\`bash
# 恢复备份文件
cp -r "$GRAVEYARD_DIR"/* ./

# 重置迁移状态
python3 -c "
import json
with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)
if '$MODULE_NAME' in config.get('modules', {}):
    config['modules']['$MODULE_NAME']['legacy_code_removed'] = False
    config['modules']['$MODULE_NAME']['migration_stage'] = 'in_progress'
    del config['modules']['$MODULE_NAME']['removal_date']
    del config['modules']['$MODULE_NAME']['backup_location']
with open('$CONFIG_FILE', 'w') as f:
    json.dump(config, f, ensure_ascii=False, indent=2)
"
\`\`\`

## 垃圾回收
30天后可以安全删除备份文件：
\`\`\`bash
rm -rf "$GRAVEYARD_DIR"
\`\`\`
EOF

# 设置定时清理（30天后删除备份）
echo "⏰ 设置30天后自动清理备份..."
cat > "graveyard/cleanup_$(echo $MODULE_NAME | sed 's/[^a-zA-Z0-9]/_/g').sh" << EOF
#!/bin/bash
# 自动清理脚本 - 生成于 $(date)
# 30天后执行

echo "🗑️ 清理30天前的备份: $MODULE_NAME"
if [ -d "$GRAVEYARD_DIR" ]; then
    rm -rf "$GRAVEYARD_DIR"
    echo "✅ 备份已清理: $GRAVEYARD_DIR"
else
    echo "⚠️ 备份目录不存在: $GRAVEYARD_DIR"
fi
EOF

chmod +x "graveyard/cleanup_$(echo $MODULE_NAME | sed 's/[^a-zA-Z0-9]/_/g').sh"

echo "✅ 屎山代码清理完成！"
echo "📄 清理报告: reports/cleanup_$(echo $MODULE_NAME | sed 's/[^a-zA-Z0-9]/_/g')_$(date +%Y%m%d_%H%M%S).md"
echo "💾 备份位置: $GRAVEYARD_DIR"
echo "⏰ 30天后自动清理备份"
echo ""
echo "🔍 验证清理结果:"
echo "  - 检查模块功能是否正常"
echo "  - 运行相关测试用例"
echo "  - 监控系统错误日志"
