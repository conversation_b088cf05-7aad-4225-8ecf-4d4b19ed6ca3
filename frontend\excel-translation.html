<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>excel-translation.html - 已迁移到新架构</title>
    
    <!-- 新架构核心文件 -->
    <script src="../js/core/component-manager.js"></script>
    <script src="../js/core/app-bootstrap.js"></script>
    <script src="../js/api-config-fix.js"></script>
    
    <!-- 所需组件 -->
    <script src="../js/common/api-client.js"></script>
    <script src="../js/common/field-utils.js"></script>
    <script src="../js/common/validation-utils.js"></script>
    <script src="../js/common/error-handler.js"></script>
    
    <!-- 自定义样式 -->
    <style>

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .translation-section {
            margin-bottom: 30px;
        }
        
        .upload-area {
            border: 2px dashed #e4e7ed;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s;
            background: #fafbfc;
        }
        
        .upload-area:hover {
            border-color: #409eff;
            background: #f0f9ff;
        }
        
        .upload-area.active {
            border-color: #67c23a;
            background: #f0f9ff;
        }
        
        .upload-icon {
            font-size: 3em;
            color: #c0c4cc;
            margin-bottom: 15px;
        }
        
        .upload-text {
            color: #606266;
            font-size: 1.1em;
            margin-bottom: 15px;
        }
        
        .upload-hint {
            color: #909399;
            font-size: 0.9em;
        }
        
        .module-selector {
            margin: 20px 0;
        }
        
        .translation-controls {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            align-items: center;
        }
        
        .results-section {
            margin-top: 30px;
        }
        
        .match-result-card {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .match-result-header {
            background: #f5f7fa;
            padding: 15px;
            border-bottom: 1px solid #e4e7ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .match-result-body {
            padding: 20px;
        }
        
        .confidence-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .confidence-high {
            background: #f0f9ff;
            color: #1890ff;
        }
        
        .confidence-medium {
            background: #fff7e6;
            color: #fa8c16;
        }
        
        .confidence-low {
            background: #fff2f0;
            color: #ff4d4f;
        }
        
        .statistics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            opacity: 0.9;
        }
        
        .progress-section {
            margin: 20px 0;
        }
        
        .export-section {
            margin-top: 30px;
            padding: 20px;
            background: #f5f7fa;
            border-radius: 10px;
        }
        
        .log-viewer {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .log-viewer .log-success {
            color: #4fc08d;
        }
        
        .log-viewer .log-warning {
            color: #f0ad4e;
        }
        
        .log-viewer .log-error {
            color: #d9534f;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 10px;
            text-align: center;
        }
    
    </style>
    <link rel="stylesheet" href="../static/css/element-plus.css">
</head>
<body>
    <!-- 迁移标识 -->
    <div style="position: fixed; top: 10px; right: 10px; background: #4CAF50; color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px; z-index: 9999;">
        ✅ 已迁移到新架构
    </div>
    
    <!-- 原始页面内容 -->
<div id="app">
        <div class="container">
            <!-- 头部 -->
            <div class="header">
                <h1>📊 Excel智能翻译匹配</h1>
                <p>基于预翻译技术的高性能Excel字段匹配系统</p>
            </div>
            
            <!-- 主要内容 -->
            <div class="content">
                <!-- Excel上传区域 -->
                <div class="translation-section">
                    <h2>📁 Excel文件上传</h2>
                    <div class="upload-area" 
                         @drop="handleDrop" 
                         @dragover.prevent 
                         @dragenter.prevent
                         :class="{ active: isDragActive }"
                         @dragenter="isDragActive = true"
                         @dragleave="isDragActive = false">
                        <div class="upload-icon">📄</div>
                        <div class="upload-text">
                            <strong>点击选择或拖拽Excel文件到此处</strong>
                        </div>
                        <div class="upload-hint">
                            支持 .xlsx, .xls 格式，文件大小不超过10MB
                        </div>
                        <el-upload
                            ref="upload"
                            :auto-upload="false"
                            :show-file-list="true"
                            :on-change="handleFileChange"
                            accept=".xlsx,.xls"
                            :limit="1"
                            :file-list="fileList">
                            <el-button type="primary" size="large" style="margin-top: 15px;">
                                <i class="el-icon-upload"></i> 选择Excel文件
                            </el-button>
                        </el-upload>
                    </div>
                </div>
                
                <!-- 模块选择和配置 -->
                <div class="translation-section" v-if="selectedFile">
                    <h2>⚙️ 翻译配置</h2>
                    <div class="module-selector">
                        <el-form :inline="true">
                            <el-form-item label="选择业务模块:">
                                <el-select v-model="selectedModule" placeholder="请选择模块" style="width: 300px;">
                                    <el-option
                                        v-for="module in availableModules"
                                        :key="module.value"
                                        :label="module.label"
                                        :value="module.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="匹配模式:">
                                <el-radio-group v-model="matchMode">
                                    <el-radio label="auto">智能检测</el-radio>
                                    <el-radio label="chinese">中文匹配</el-radio>
                                    <el-radio label="english">英文匹配</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-form>
                    </div>
                    
                    <div class="translation-controls">
                        <el-button 
                            type="primary" 
                            size="large"
                            :loading="isTranslating"
                            @click="startTranslation"
                            :disabled="!selectedModule">
                            <i class="el-icon-magic-stick"></i> 
                            {{ isTranslating ? '正在翻译匹配...' : '开始智能翻译' }}
                        </el-button>
                        
                        <el-button 
                            type="success" 
                            size="large"
                            @click="runPreTranslation"
                            :loading="isPreTranslating">
                            <i class="el-icon-refresh"></i>
                            {{ isPreTranslating ? '正在预翻译...' : '执行预翻译' }}
                        </el-button>
                        
                        <el-button 
                            v-if="translationResults.length > 0"
                            type="info" 
                            size="large"
                            @click="exportResults">
                            <i class="el-icon-download"></i> 导出结果
                        </el-button>
                    </div>
                </div>
                
                <!-- 翻译进度 -->
                <div class="progress-section" v-if="isTranslating || translationProgress > 0">
                    <h3>🚀 翻译进度</h3>
                    <el-progress 
                        :percentage="translationProgress" 
                        :status="progressStatus"
                        :show-text="true">
                    </el-progress>
                    <div class="log-viewer" v-if="translationLogs.length > 0">
                        <div v-for="log in translationLogs" :key="log.id" :class="log.type">
                            [{{ log.timestamp }}] {{ log.message }}
                        </div>
                    </div>
                </div>
                
                <!-- 翻译统计 -->
                <div v-if="statistics.total_excel_fields > 0" class="results-section">
                    <h2>📊 翻译统计</h2>
                    <div class="statistics-grid">
                        <div class="stat-card">
                            <div class="stat-number">{{ statistics.matched_fields }}</div>
                            <div class="stat-label">匹配字段数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{{ (statistics.match_rate * 100).toFixed(1) }}%</div>
                            <div class="stat-label">匹配成功率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{{ (statistics.avg_confidence * 100).toFixed(1) }}%</div>
                            <div class="stat-label">平均置信度</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{{ statistics.high_confidence_matches }}</div>
                            <div class="stat-label">高置信度匹配</div>
                        </div>
                    </div>
                </div>
                
                <!-- 匹配结果 -->
                <div v-if="translationResults.length > 0" class="results-section">
                    <h2>✅ 翻译匹配结果</h2>
                    <div class="match-result-card" v-for="result in translationResults" :key="result.excel_field">
                        <div class="match-result-header">
                            <div>
                                <strong>{{ result.excel_field }}</strong> 
                                → 
                                <strong style="color: #409eff;">{{ result.chinese_name }}</strong>
                            </div>
                            <div class="confidence-badge" :class="getConfidenceClass(result.confidence)">
                                {{ (result.confidence * 100).toFixed(1) }}%
                            </div>
                        </div>
                        <div class="match-result-body">
                            <p><strong>匹配字段:</strong> {{ result.api_field }}</p>
                            <p><strong>匹配类型:</strong> {{ result.match_type }}</p>
                            <p><strong>置信度:</strong> {{ (result.confidence * 100).toFixed(1) }}%</p>
                        </div>
                    </div>
                </div>
                
                <!-- 导出功能 -->
                <div v-if="translationResults.length > 0" class="export-section">
                    <h3>📤 导出翻译结果</h3>
                    <p>将翻译结果导出为各种格式，方便后续使用和分析。</p>
                    <el-button-group>
                        <el-button type="primary" @click="exportAsJson">
                            <i class="el-icon-document"></i> JSON格式
                        </el-button>
                        <el-button type="success" @click="exportAsExcel">
                            <i class="el-icon-s-grid"></i> Excel格式
                        </el-button>
                        <el-button type="info" @click="exportAsReport">
                            <i class="el-icon-s-order"></i> 分析报告
                        </el-button>
                    </el-button-group>
                </div>
            </div>
        </div>
        
        <!-- 加载遮罩 -->
        <div v-if="isLoading" class="loading-overlay">
            <div class="loading-content">
                <el-spin size="large"></el-spin>
                <p style="margin-top: 20px;">{{ loadingMessage }}</p>
            </div>
        </div>
    </div>

    <!-- 新架构初始化脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 初始化新架构页面: excel-translation.html');
            
            // 启动应用
            await window.startApp({
                environment: 'production',
                features: {
                "errorHandling": true
}
            });
            
            // 获取所需组件
            const apiClient = window.ComponentManager.get('apiClient');
            const fieldUtils = window.ComponentManager.get('fieldUtils');
            const errorHandler = window.ComponentManager.get('errorHandler');
            
            // 调用原始初始化逻辑
            if (typeof initializePage === 'function') {
                initializePage();
            }
            
            console.log('✅ 页面初始化完成: excel-translation.html');
        });
        
        // 原始自定义脚本（已适配新架构）
        // 添加错误处理，忽略浏览器扩展相关的错误
                window.addEventListener('error', function(e) {
                    if (e.message && e.message.includes('runtime.lastError')) {
                        console.log('浏览器扩展通信错误，已忽略');
                        return false; // 阻止错误显示
                    }
                });
        
                // 处理未捕获的Promise错误
                window.addEventListener('unhandledrejection', function(e) {
                    if (e.reason && e.reason.message && e.reason.message.includes('runtime.lastError')) {
                        console.log('浏览器扩展Promise错误，已忽略');
                        e.preventDefault(); // 阻止错误显示
                    }
                });
        
                // 动态获取API基础URL
                const API_BASE_URL = window.location.origin;
        
                const { createApp, ref, reactive, computed, onMounted } = Vue;
                const { ElMessage } = ElementPlus;
                
                createApp({
                    setup() {
                        // 响应式数据
                        const selectedFile = ref(null);
                        const selectedModule = ref('');
                        const matchMode = ref('auto');
                        const isTranslating = ref(false);
                        const isPreTranslating = ref(false);
                        const isLoading = ref(false);
                        const loadingMessage = ref('');
                        const isDragActive = ref(false);
                        const fileList = ref([]);
                        
                        const translationProgress = ref(0);
                        const progressStatus = ref('');
                        const translationLogs = ref([]);
                        const translationResults = ref([]);
                        
                        const statistics = reactive({
                            total_excel_fields: 0,
                            matched_fields: 0,
                            match_rate: 0.0,
                            avg_confidence: 0.0,
                            high_confidence_matches: 0,
                            low_confidence_matches: 0,
                            match_types: {}
                        });
                        
                        const availableModules = ref([
                            { value: 'sales_order', label: '销售订单' },
                            { value: 'purchase_order', label: '采购订单' },
                            { value: 'production_order', label: '生产订单' },
                            { value: 'inventory', label: '现存量查询' },
                            { value: 'material_master', label: '物料档案' },
                            { value: 'purchase_receipt', label: '采购入库' },
                            { value: 'product_receipt', label: '产品入库' },
                            { value: 'sales_out', label: '销售出库' },
                            { value: 'materialout', label: '材料出库' },
                            { value: 'subcontract_order', label: '委外订单' },
                            { value: 'subcontract_receipt', label: '委外入库' },
                            { value: 'subcontract_requisition', label: '委外申请单' },
                            { value: 'applyorder', label: '请购单' },
                            { value: 'requirements_planning', label: '需求计划' },
                            { value: 'inventory_report', label: '现存量报表' }
                        ]);
                        
                        // 方法
                        const addLog = (message, type = 'log-success') => {
                            translationLogs.value.push({
                                id: Date.now(),
                                timestamp: new Date().toLocaleTimeString(),
                                message,
                                type
                            });
                        };
                        
                        const handleFileChange = (file) => {
                            // 添加文件类型验证
                            if (!file.name.match(/\.(xlsx|xls)$/i)) {
                                ElMessage.error('请选择Excel文件 (.xlsx 或 .xls)');
                                return;
                            }
                            
                            // 添加文件大小限制 (10MB)
                            if (file.raw && file.raw.size > 10 * 1024 * 1024) {
                                ElMessage.error('文件过大，请选择小于10MB的文件');
                                return;
                            }
                            
                            selectedFile.value = file;
                            fileList.value = [file];
                            addLog(`文件已选择: ${file.name}`, 'log-success');
                        };
                        
                        const handleDrop = (event) => {
                            event.preventDefault();
                            isDragActive.value = false;
                            
                            const files = event.dataTransfer.files;
                            if (files.length > 0) {
                                const file = files[0];
                                if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                                    handleFileChange(file);
                                } else {
                                    ElMessage.error('请选择Excel文件 (.xlsx 或 .xls)');
                                }
                            }
                        };
                        
                        const startTranslation = async () => {
                            if (!selectedFile.value || !selectedModule.value) {
                                ElMessage.warning('请选择Excel文件和业务模块');
                                return;
                            }
                            
                            isTranslating.value = true;
                            translationProgress.value = 0;
                            progressStatus.value = 'active';
                            translationResults.value = [];
                            translationLogs.value = [];
                            
                            try {
                                addLog('开始Excel翻译匹配...');
                                
                                // 创建FormData
                                const formData = new FormData();
                                formData.append('excel_file', selectedFile.value.raw);
                                formData.append('module_name', selectedModule.value);
                                formData.append('match_mode', matchMode.value);
                                
                                translationProgress.value = 30;
                                addLog('正在上传文件...');
                                
                                // 调用后端API
                                const response = await fetch('/api/v1/excel/translate-match', {
                                    method: 'POST',
                                    body: formData
                                });
                                
                                if (!response.ok) {
                                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                                }
                                
                                translationProgress.value = 60;
                                addLog('正在处理翻译匹配...');
                                
                                const result = await response.json();
                                
                                translationProgress.value = 90;
                                addLog('正在分析结果...');
                                
                                // 更新结果
                                translationResults.value = result.matches || [];
                                Object.assign(statistics, result.statistics || {});
                                
                                translationProgress.value = 100;
                                progressStatus.value = 'success';
                                addLog(`翻译匹配完成! 成功匹配 ${translationResults.value.length} 个字段`);
                                
                                ElMessage.success('Excel翻译匹配完成!');
                                
                            } catch (error) {
                                console.error('翻译匹配失败:', error);
                                progressStatus.value = 'exception';
                                addLog(`翻译匹配失败: ${error.message}`, 'log-error');
                                ElMessage.error('翻译匹配失败: ' + error.message);
                            } finally {
                                isTranslating.value = false;
                            }
                        };
                        
                        const runPreTranslation = async () => {
                            isPreTranslating.value = true;
                            isLoading.value = true;
                            loadingMessage.value = '正在执行批量预翻译...';
                            
                            try {
                                addLog('开始批量预翻译...');
                                
                                const response = await fetch('/api/v1/config/pretranslate', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    }
                                });
                                
                                if (!response.ok) {
                                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                                }
                                
                                const result = await response.json();
                                
                                addLog(`预翻译完成! 处理了 ${result.processed_files} 个配置文件`);
                                addLog(`总计翻译 ${result.translated_fields}/${result.total_fields} 个字段`);
                                
                                ElMessage.success('批量预翻译完成!');
                                
                            } catch (error) {
                                console.error('预翻译失败:', error);
                                addLog(`预翻译失败: ${error.message}`, 'log-error');
                                ElMessage.error('预翻译失败: ' + error.message);
                            } finally {
                                isPreTranslating.value = false;
                                isLoading.value = false;
                            }
                        };
                        
                        const getConfidenceClass = (confidence) => {
                            if (confidence >= 0.8) return 'confidence-high';
                            if (confidence >= 0.6) return 'confidence-medium';
                            return 'confidence-low';
                        };
                        
                        const exportResults = () => {
                            // 默认导出JSON格式
                            exportAsJson();
                        };
                        
                        const exportAsJson = () => {
                            const data = {
                                timestamp: new Date().toISOString(),
                                module: selectedModule.value,
                                statistics: statistics,
                                matches: translationResults.value
                            };
                            
                            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `excel-translation-${selectedModule.value}-${Date.now()}.json`;
                            a.click();
                            URL.revokeObjectURL(url);
                        };
                        
                        const exportAsExcel = () => {
                            // 实现Excel导出功能
                            try {
                                // 创建工作表数据
                                const worksheetData = [
                                    // 表头
                                    ['Excel字段名', '中文名称', 'API字段名', '匹配类型', '置信度', '匹配状态'],
                                    // 数据行
                                    ...translationResults.value.map(result => [
                                        result.excel_field,
                                        result.chinese_name,
                                        result.api_field,
                                        result.match_type,
                                        `${(result.confidence * 100).toFixed(1)}%`,
                                        result.confidence >= 0.8 ? '高置信度' : result.confidence >= 0.6 ? '中置信度' : '低置信度'
                                    ])
                                ];
                                
                                // 创建CSV内容
                                const csvContent = worksheetData.map(row => 
                                    row.map(cell => `"${cell}"`).join(',')
                                ).join('\n');
                                
                                // 添加BOM以支持中文
                                const BOM = '\uFEFF';
                                const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
                                const url = URL.createObjectURL(blob);
                                const a = document.createElement('a');
                                a.href = url;
                                a.download = `excel-translation-${selectedModule.value}-${Date.now()}.csv`;
                                a.click();
                                URL.revokeObjectURL(url);
                                
                                ElMessage.success('Excel文件导出成功!');
                            } catch (error) {
                                console.error('Excel导出失败:', error);
                                ElMessage.error('Excel导出失败: ' + error.message);
                            }
                        };
                        
                        const exportAsReport = () => {
                            // 实现分析报告导出功能
                            try {
                                const reportData = {
                                    report_info: {
                                        title: 'Excel智能翻译匹配分析报告',
                                        generated_at: new Date().toISOString(),
                                        module: selectedModule.value,
                                        file_name: selectedFile.value?.name || '未知文件'
                                    },
                                    summary: {
                                        total_excel_fields: statistics.total_excel_fields,
                                        matched_fields: statistics.matched_fields,
                                        match_rate: `${(statistics.match_rate * 100).toFixed(1)}%`,
                                        avg_confidence: `${(statistics.avg_confidence * 100).toFixed(1)}%`,
                                        high_confidence_matches: statistics.high_confidence_matches,
                                        low_confidence_matches: statistics.low_confidence_matches
                                    },
                                    detailed_results: translationResults.value.map(result => ({
                                        excel_field: result.excel_field,
                                        chinese_name: result.chinese_name,
                                        api_field: result.api_field,
                                        match_type: result.match_type,
                                        confidence: `${(result.confidence * 100).toFixed(1)}%`,
                                        status: result.confidence >= 0.8 ? '高置信度' : result.confidence >= 0.6 ? '中置信度' : '低置信度'
                                    })),
                                    recommendations: generateRecommendations()
                                };
                                
                                const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
                                const url = URL.createObjectURL(blob);
                                const a = document.createElement('a');
                                a.href = url;
                                a.download = `translation-report-${selectedModule.value}-${Date.now()}.json`;
                                a.click();
                                URL.revokeObjectURL(url);
                                
                                ElMessage.success('分析报告导出成功!');
                            } catch (error) {
                                console.error('报告导出失败:', error);
                                ElMessage.error('报告导出失败: ' + error.message);
                            }
                        };
                        
                        const generateRecommendations = () => {
                            const recommendations = [];
                            
                            if (statistics.match_rate < 0.5) {
                                recommendations.push('匹配率较低，建议检查Excel文件格式和字段命名');
                            }
                            
                            if (statistics.avg_confidence < 0.6) {
                                recommendations.push('平均置信度较低，建议使用"执行预翻译"功能提升翻译质量');
                            }
                            
                            if (statistics.low_confidence_matches > statistics.high_confidence_matches) {
                                recommendations.push('低置信度匹配较多，建议手动检查和调整字段映射');
                            }
                            
                            if (recommendations.length === 0) {
                                recommendations.push('翻译匹配效果良好，可以继续使用当前配置');
                            }
                            
                            return recommendations;
                        };
                        
                        return {
                            selectedFile,
                            selectedModule,
                            matchMode,
                            isTranslating,
                            isPreTranslating,
                            isLoading,
                            loadingMessage,
                            isDragActive,
                            fileList,
                            translationProgress,
                            progressStatus,
                            translationLogs,
                            translationResults,
                            statistics,
                            availableModules,
                            handleFileChange,
                            handleDrop,
                            startTranslation,
                            runPreTranslation,
                            getConfidenceClass,
                            exportResults,
                            exportAsJson,
                            exportAsExcel,
                            exportAsReport
                        };
                    }
                }).use(ElementPlus).mount('#app');
                
                
    </script>
</body>
</html>