import json
import re
from datetime import datetime
from enum import Enum

import structlog

"""
YS-API V3.0 响应格式标准化器
Month 3 Week 3: 统一响应格式和分页信息标准化
"""


logger = structlog.get_logger()


class ResponseStatus(Enum):
    """响应状态枚举"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    PARTIAL = "partial"


class DataFormat(Enum):
    """数据格式枚举"""
    LIST = "list"
    OBJECT = "object"
    PAGINATED = "paginated"
    AGGREGATED = "aggregated"


@dataclass
class PaginationInfo:
    """分页信息"""
    current_page: int
    page_size: int
    total_records: int
    total_pages: int
    has_next: bool
    has_previous: bool
    next_page: Optional[int] = None
    previous_page: Optional[int] = None


@dataclass
class ResponseMetadata:
    """响应元数据"""
    timestamp: str
    version: str
    source: str
    module: str
    request_id: Optional[str] = None
    processing_time: Optional[float] = None
    data_format: Optional[str] = None
    locale: str = "zh-CN"


@dataclass
class StandardErrorInfo:
    """标准错误信息"""
    code: str
    message: str
    details: Optional[str] = None
    field: Optional[str] = None
    suggestion: Optional[str] = None


@dataclass
class StandardResponse:
    """标准响应格式"""
    status: str
    data: Any
    metadata: ResponseMetadata
    pagination: Optional[PaginationInfo] = None
    errors: Optional[List[StandardErrorInfo]] = None
    warnings: Optional[List[str]] = None


class ResponseFormatStandardizer:
    """响应格式标准化器"""

    def __init___(self):
    """TODO: Add function description."""
    self.standardization_stats = {
        'total_responses': 0,
        'successful_standardizations': 0,
        'failed_standardizations': 0,
        'format_distributions': {}
    }

    # 模块特定的响应结构配置
    self.module_configs = self._initialize_module_configs()

    logger.info("响应格式标准化器初始化完成")

    def _initialize_module_configs(self) -> Dict[str, Dict[str, Any]]:
        """初始化模块配置"""
        return {
            'material_outbound': {
                'data_format': DataFormat.PAGINATED,
                'primary_key': 'bill_number',
                'sort_field': 'out_date',
                'summary_fields': ['total_quantity', 'total_amount'],
                'display_name': '材料出库单'
            },
            'purchase_order': {
                'data_format': DataFormat.PAGINATED,
                'primary_key': 'order_number',
                'sort_field': 'order_date',
                'summary_fields': ['total_quantity', 'total_amount'],
                'display_name': '采购订单'
            },
            'inventory_report': {
                'data_format': DataFormat.LIST,
                'primary_key': 'material_code',
                'sort_field': 'last_update',
                'summary_fields': ['total_quantity'],
                'display_name': '现存量报表'
            },
            'production_order': {
                'data_format': DataFormat.PAGINATED,
                'primary_key': 'production_order_number',
                'sort_field': 'start_date',
                'summary_fields': ['total_planned', 'total_completed'],
                'display_name': '生产订单'
            },
            'sales_order': {
                'data_format': DataFormat.PAGINATED,
                'primary_key': 'order_number',
                'sort_field': 'order_date',
                'summary_fields': ['total_quantity', 'total_amount'],
                'display_name': '销售订单'
            }
        }

    def standardize_response(
        self,
        raw_response: Dict[str, Any],
        module_name: str,
        request_id: Optional[str] = None,
        processing_time: Optional[float] = None
    ) -> StandardResponse:
        """
        标准化响应格式

        Args:
            raw_response: 原始响应数据
            module_name: 模块名称
            request_id: 请求ID
            processing_time: 处理时间

        Returns:
            StandardResponse: 标准化后的响应
        """
        self.standardization_stats['total_responses'] += 1

        try:
            # 获取模块配置
            module_config = self.module_configs.get(module_name, {})

            # 解析原始响应
            parsed_data = self._parse_raw_response(raw_response)

            # 标准化数据
            standardized_data = self._standardize_data(
                parsed_data['data'],
                module_config
            )

            # 创建分页信息
            pagination = self._create_pagination_info(
                parsed_data.get('pagination', {}),
                module_config.get('data_format', DataFormat.LIST)
            )

            # 创建元数据
            metadata = self._create_metadata(
                module_name,
                module_config,
                request_id,
                processing_time
            )

            # 处理错误和警告
            errors = self._standardize_errors(parsed_data.get('errors', []))
            warnings = parsed_data.get('warnings', [])

            # 确定响应状态
            status = self._determine_response_status(
                parsed_data.get('success', True),
                errors,
                warnings
            )

            # 创建标准响应
            standard_response = StandardResponse(
                status=status.value,
                data=standardized_data,
                metadata=metadata,
                pagination=pagination,
                errors=errors if errors else None,
                warnings=warnings if warnings else None
            )

            self.standardization_stats['successful_standardizations'] += 1
            self._update_format_distribution(
                module_config.get(
                    'data_format', DataFormat.LIST))

            logger.info(
                "响应格式标准化完成",
                module=module_name,
                status=status.value,
                data_count=self._get_data_count(standardized_data)
            )

            return standard_response

        except Exception:
            self.standardization_stats['failed_standardizations'] += 1
            logger.error(
                "响应格式标准化失败",
                module=module_name,
                error=str(e)
            )

            # 返回错误响应
            return self._create_error_response(
                str(e),
                module_name,
                request_id,
                processing_time
            )

    def _parse_raw_response(
            self, raw_response: Dict[str, Any]) -> Dict[str, Any]:
        """解析原始响应数据"""
        # 兼容多种响应格式
        if 'code' in raw_response:
            # YS-API标准格式
            success = raw_response.get('code') == '00000'
            data = raw_response.get('data', {})

            if isinstance(data, dict):
                # 分页数据格式
                return {
                    'success': success, 'data': data.get(
                        'list', data), 'pagination': {
                        'total': data.get(
                            'total', 0), 'pageNum': data.get(
                            'pageNum', 1), 'pageSize': data.get(
                            'pageSize', 10)}, 'errors': [] if success else [
                            raw_response.get(
                                'message', '未知错误')], 'warnings': []}
            else:
                # 直接数据格式
                return {
                    'success': success,
                    'data': data,
                    'pagination': {},
                    'errors': [] if success else [
                        raw_response.get(
                            'message',
                            '未知错误')],
                    'warnings': []}

        elif 'status' in raw_response:
            # 标准格式
            return {
                'success': raw_response.get('status') == 'success',
                'data': raw_response.get('data', []),
                'pagination': raw_response.get('pagination', {}),
                'errors': raw_response.get('errors', []),
                'warnings': raw_response.get('warnings', [])
            }

        else:
            # 直接数据格式
            return {
                'success': True,
                'data': raw_response,
                'pagination': {},
                'errors': [],
                'warnings': []
            }

    def _standardize_data(
            self, data: Any, module_config: Dict[str, Any]) -> Any:
        """标准化数据内容"""
        if not data:
            return []

        # 确保数据是列表格式（对于列表类型的响应）
        if isinstance(data, list):
            standardized_list = []
            for item in data:
                if isinstance(item, dict):
                    standardized_item = self._standardize_data_item(
                        item, module_config)
                    standardized_list.append(standardized_item)
            return standardized_list

        elif isinstance(data, dict):
            # 单个对象
            return self._standardize_data_item(data, module_config)

        else:
            return data

    def _standardize_data_item(
            self, item: Dict[str, Any], module_config: Dict[str, Any]) -> Dict[str, Any]:
        """标准化单个数据项"""
        standardized_item = item.copy()

        # 添加模块特定的标准化处理
        primary_key = module_config.get('primary_key')
        if primary_key and primary_key in standardized_item:
            # 确保主键字段存在且不为空
            if not standardized_item[primary_key]:
                standardized_item[primary_key] = f"AUTO_{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # 标准化日期字段
        for key, value in standardized_item.items():
            if isinstance(value, str) and self._is_date_like(value):
                try:
                    # 尝试解析并标准化日期格式
                    parsed_date = self._parse_date_string(value)
                    if parsed_date:
                        standardized_item[key] = parsed_date.isoformat()
                except Exception:
                    pass  # 保持原值

        return standardized_item

    def _create_pagination_info(self,
                                pagination_data: Dict[str,
                                                      Any],
                                data_format: DataFormat) -> Optional[PaginationInfo]:
        """创建分页信息"""
        if data_format != DataFormat.PAGINATED:
            return None

        try:
            total_records = int(pagination_data.get('total', 0))
            page_size = int(pagination_data.get('pageSize', 10))
            current_page = int(pagination_data.get('pageNum', 1))

            total_pages = (total_records + page_size -
                           1) // page_size if page_size > 0 else 1

            has_next = current_page < total_pages
            has_previous = current_page > 1

            next_page = current_page + 1 if has_next else None
            previous_page = current_page - 1 if has_previous else None

            return PaginationInfo(
                current_page=current_page,
                page_size=page_size,
                total_records=total_records,
                total_pages=total_pages,
                has_next=has_next,
                has_previous=has_previous,
                next_page=next_page,
                previous_page=previous_page
            )

        except Exception:
            logger.warning(f"创建分页信息失败: {str(e)}")
            return None

    def _create_metadata(
        self,
        module_name: str,
        module_config: Dict[str, Any],
        request_id: Optional[str],
        processing_time: Optional[float]
    ) -> ResponseMetadata:
        """创建响应元数据"""
        return ResponseMetadata(
            timestamp=datetime.now().isoformat(),
            version="3.0",
            source="YS-API",
            module=module_name,
            request_id=request_id,
            processing_time=processing_time,
            data_format=module_config.get(
                'data_format',
                DataFormat.LIST).value,
            locale="zh-CN")

    def _standardize_errors(
            self,
            errors: List[Any]) -> List[StandardErrorInfo]:
        """标准化错误信息"""
        standardized_errors = []

        for error in errors:
            if isinstance(error, str):
                # 简单字符串错误
                standardized_errors.append(StandardErrorInfo(
                    code="GENERAL_ERROR",
                    message=error
                ))
            elif isinstance(error, dict):
                # 结构化错误
                standardized_errors.append(StandardErrorInfo(
                    code=error.get('code', 'UNKNOWN_ERROR'),
                    message=error.get('message', '未知错误'),
                    details=error.get('details'),
                    field=error.get('field'),
                    suggestion=error.get('suggestion')
                ))

        return standardized_errors

    def _determine_response_status(
            self,
            success: bool,
            errors: List[StandardErrorInfo],
            warnings: List[str]) -> ResponseStatus:
        """确定响应状态"""
        if not success or errors:
            return ResponseStatus.ERROR
        elif warnings:
            return ResponseStatus.WARNING
        else:
            return ResponseStatus.SUCCESS

    def _update_format_distribution(self, data_format: DataFormat):
        """更新格式分布统计"""
        format_key = data_format.value
        if format_key not in self.standardization_stats['format_distributions']:
            self.standardization_stats['format_distributions'][format_key] = 0
        self.standardization_stats['format_distributions'][format_key] += 1

    def _get_data_count(self, data: Any) -> int:
        """获取数据数量"""
        if isinstance(data, list):
            return len(data)
        elif isinstance(data, dict):
            return 1
        else:
            return 0

    def _is_date_like(self, value: str) -> bool:
        """判断字符串是否像日期"""
        date_patterns = [
            r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
            r'\d{4}/\d{2}/\d{2}',  # YYYY/MM/DD
            r'\d{4}年\d{1,2}月\d{1,2}日',  # 中文日期
        ]

        for pattern in date_patterns:
            if re.search(pattern, value):
                return True
        return False

    def _parse_date_string(self, date_str: str) -> Optional[datetime]:
        """解析日期字符串"""
        date_formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d',
            '%Y/%m/%d %H:%M:%S',
            '%Y/%m/%d',
            '%Y年%m月%d日',
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(date_str.strip(), fmt)
            except ValueError:
                continue
        return None

    def _create_error_response(
        self,
        error_message: str,
        module_name: str,
        request_id: Optional[str],
        processing_time: Optional[float]
    ) -> StandardResponse:
        """创建错误响应"""
        metadata = ResponseMetadata(
            timestamp=datetime.now().isoformat(),
            version="3.0",
            source="YS-API",
            module=module_name,
            request_id=request_id,
            processing_time=processing_time,
            locale="zh-CN"
        )

        error_info = StandardErrorInfo(
            code="STANDARDIZATION_ERROR",
            message=error_message
        )

        return StandardResponse(
            status=ResponseStatus.ERROR.value,
            data=[],
            metadata=metadata,
            errors=[error_info]
        )

    def to_dict(self, response: StandardResponse) -> Dict[str, Any]:
        """将标准响应转换为字典"""
        response_dict = {
            'status': response.status,
            'data': response.data,
            'metadata': asdict(response.metadata)
        }

        if response.pagination:
            response_dict['pagination'] = asdict(response.pagination)

        if response.errors:
            response_dict['errors'] = [
                asdict(error) for error in response.errors]

        if response.warnings:
            response_dict['warnings'] = response.warnings

        return response_dict

    def to_json(self, response: StandardResponse, **kwargs) -> str:
        """将标准响应转换为JSON字符串"""
        response_dict = self.to_dict(response)
        return json.dumps(response_dict, ensure_ascii=False, **kwargs)

    def get_standardization_statistics(self) -> Dict[str, Any]:
        """获取标准化统计信息"""
        success_rate = (
            self.standardization_stats['successful_standardizations'] /
            max(self.standardization_stats['total_responses'], 1)
        )

        return {
            'total_responses': self.standardization_stats['total_responses'],
            'successful_standardizations': self.standardization_stats['successful_standardizations'],
            'failed_standardizations': self.standardization_stats['failed_standardizations'],
            'success_rate': success_rate,
            'format_distributions': self.standardization_stats['format_distributions'],
            'supported_modules': list(
                self.module_configs.keys())}

    def add_module_config(self, module_name: str, config: Dict[str, Any]):
        """添加模块配置"""
        self.module_configs[module_name] = config

    def update_module_config(self, module_name: str, config: Dict[str, Any]):
        """更新模块配置"""
        if module_name in self.module_configs:
            self.module_configs[module_name].update(config)
        else:
            self.module_configs[module_name] = config


def create_response_format_standardizer() -> ResponseFormatStandardizer:
    """创建响应格式标准化器实例"""
    return ResponseFormatStandardizer()
