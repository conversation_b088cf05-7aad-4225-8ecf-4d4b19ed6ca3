import base64
import hashlib
import hmac
import time
from urllib.parse import quote_plus

import structlog

"""
YS-API V3.0 签名认证增强器
Month 3 Week 2: 优化签名和认证流程的可靠性
"""


logger = structlog.get_logger()


class SignatureValidator:
    """签名验证器"""

    @staticmethod
    def validate_signature_format(signature: str) -> bool:
        """验证签名格式"""
        try:
            # 检查是否为Base64格式
            decoded = base64.b64decode(signature)
            return len(decoded) == 32  # SHA256签名长度
        except Exception:
            return False

    @staticmethod
    def validate_timestamp(
            timestamp: str,
            tolerance_seconds: int = 300) -> bool:
        """验证时间戳有效性（5分钟容差）"""
        try:
            ts = int(timestamp)
            current_time = int(time.time())
            return abs(current_time - ts) <= tolerance_seconds
        except Exception:
            return False


class EnhancedAuthenticator:
    """增强认证器"""

    def __init___(self, app_key: str, app_secret: str, tenant_id: str):
    """TODO: Add function description."""
    self.app_key = app_key
    self.app_secret = app_secret
    self.tenant_id = tenant_id

    # 认证统计
    self.auth_stats = {
        'total_requests': 0,
        'successful_auth': 0,
        'failed_auth': 0,
        'signature_errors': 0,
        'timestamp_errors': 0,
        'token_refresh_count': 0
    }

    # Token缓存
    self.cached_token = None
    self.token_expires_at = None

    logger.info(
        "增强认证器初始化完成",
        app_key=app_key[:8] + "...",
        tenant_id=tenant_id
    )

    def generate_signature(self, timestamp: str) -> str:
        """
        生成增强签名

        Args:
            timestamp: 时间戳字符串

        Returns:
            str: Base64编码的签名
        """
        try:
            # 构建参数字符串
            param_string = f"appKey{self.app_key}timestamp{timestamp}"

            # 生成HMAC-SHA256签名
            signature_bytes = hmac.new(
                self.app_secret.encode('utf-8'),
                param_string.encode('utf-8'),
                hashlib.sha256
            ).digest()

            # Base64编码
            signature = base64.b64encode(signature_bytes).decode('utf-8')

            # 验证生成的签名格式
            if not SignatureValidator.validate_signature_format(signature):
                raise ValueError("生成的签名格式无效")

            logger.debug(
                "签名生成成功",
                timestamp=timestamp,
                param_string_length=len(param_string),
                signature_length=len(signature)
            )

            return signature

        except Exception:
            self.auth_stats['signature_errors'] += 1
            logger.error(
                "签名生成失败",
                timestamp=timestamp,
                error=str(e)
            )
            raise

    def generate_timestamp(self) -> str:
        """生成当前时间戳"""
        return str(int(time.time()))

    def build_token_request_url(self, base_url: str) -> str:
        """
        构建Token请求URL

        Args:
            base_url: API基础URL

        Returns:
            str: 完整的Token请求URL
        """
        self.auth_stats['total_requests'] += 1

        try:
            # 生成时间戳
            timestamp = self.generate_timestamp()

            # 验证时间戳
            if not SignatureValidator.validate_timestamp(timestamp):
                raise ValueError("时间戳验证失败")

            # 生成签名
            signature = self.generate_signature(timestamp)

            # URL编码签名
            encoded_signature = quote_plus(signature)

            # 构建完整URL
            token_url = (
                f"{base_url}/open-auth/selfAppAuth/getAccessToken"
                f"?appKey={self.app_key}"
                f"&timestamp={timestamp}"
                f"&signature={encoded_signature}"
            )

            self.auth_stats['successful_auth'] += 1

            logger.info(
                "Token请求URL构建成功",
                app_key=self.app_key[:8] + "...",
                timestamp=timestamp,
                url_length=len(token_url)
            )

            return token_url

        except Exception:
            self.auth_stats['failed_auth'] += 1
            logger.error(
                "Token请求URL构建失败",
                error=str(e),
                auth_stats=self.auth_stats
            )
            raise

    def validate_token_response(self, response: Dict[str, Any]) -> bool:
        """
        验证Token响应

        Args:
            response: API响应数据

        Returns:
            bool: 验证是否通过
        """
        try:
            # 检查响应码
            if response.get('code') != '00000':
                logger.error(
                    "Token响应错误",
                    code=response.get('code'),
                    message=response.get('message')
                )
                return False

            # 检查数据结构
            data = response.get('data', {})
            if not isinstance(data, dict):
                logger.error("Token响应数据格式错误")
                return False

            # 检查必要字段
            required_fields = ['access_token', 'expire']
            for field in required_fields:
                if field not in data:
                    logger.error(f"Token响应缺少字段: {field}")
                    return False

            # 验证Token格式
            access_token = data['access_token']
            if not isinstance(access_token, str) or len(access_token) < 10:
                logger.error("Token格式无效")
                return False

            # 验证过期时间
            expire_time = data['expire']
            if not isinstance(expire_time, (int, str)):
                logger.error("Token过期时间格式无效")
                return False

            logger.info(
                "Token响应验证通过",
                token_length=len(access_token),
                expire_time=expire_time
            )

            return True

        except Exception:
            logger.error(
                "Token响应验证异常",
                error=str(e)
            )
            return False

    def cache_token(self, token_data: Dict[str, Any]) -> None:
        """
        缓存Token信息

        Args:
            token_data: Token数据
        """
        try:
            self.cached_token = token_data['access_token']

            # 计算过期时间（提前5分钟过期）
            expire_timestamp = int(token_data['expire'])
            self.token_expires_at = datetime.fromtimestamp(
                expire_timestamp - 300)

            logger.info(
                "Token缓存成功",
                token_preview=self.cached_token[:20] + "...",
                expires_at=self.token_expires_at.isoformat()
            )

        except Exception:
            logger.error(
                "Token缓存失败",
                error=str(e)
            )

    def is_token_valid(self) -> bool:
        """检查缓存的Token是否有效"""
        if not self.cached_token or not self.token_expires_at:
            return False

        return datetime.now() < self.token_expires_at

    def get_cached_token(self) -> Optional[str]:
        """获取缓存的Token（如果有效）"""
        if self.is_token_valid():
            return self.cached_token
        return None

    def refresh_token_if_needed(self) -> bool:
        """检查是否需要刷新Token"""
        if not self.is_token_valid():
            self.auth_stats['token_refresh_count'] += 1
            logger.info(
                "Token需要刷新",
                refresh_count=self.auth_stats['token_refresh_count']
            )
            return True
        return False

    def get_auth_statistics(self) -> Dict[str, Any]:
        """获取认证统计信息"""
        success_rate = (
            self.auth_stats['successful_auth'] /
            max(self.auth_stats['total_requests'], 1)
        )

        return {
            'total_requests': self.auth_stats['total_requests'],
            'successful_auth': self.auth_stats['successful_auth'],
            'failed_auth': self.auth_stats['failed_auth'],
            'success_rate': success_rate,
            'signature_errors': self.auth_stats['signature_errors'],
            'timestamp_errors': self.auth_stats['timestamp_errors'],
            'token_refresh_count': self.auth_stats['token_refresh_count'],
            'cached_token_valid': self.is_token_valid(),
            'token_expires_at': (
                self.token_expires_at.isoformat()
                if self.token_expires_at else None
            )
        }

    def validate_auth_flow(self, base_url: str) -> Dict[str, Any]:
        """
        验证完整的认证流程

        Args:
            base_url: API基础URL

        Returns:
            Dict: 验证结果
        """
        validation_results = {
            'timestamp_generation': False,
            'signature_generation': False,
            'url_construction': False,
            'overall_success': False,
            'errors': []
        }

        try:
            # 1. 时间戳生成验证
            timestamp = self.generate_timestamp()
            if SignatureValidator.validate_timestamp(timestamp):
                validation_results['timestamp_generation'] = True
            else:
                validation_results['errors'].append("时间戳生成或验证失败")

            # 2. 签名生成验证
            try:
                signature = self.generate_signature(timestamp)
                if SignatureValidator.validate_signature_format(signature):
                    validation_results['signature_generation'] = True
                else:
                    validation_results['errors'].append("签名格式验证失败")
            except Exception:
                validation_results['errors'].append(f"签名生成失败: {str(e)}")

            # 3. URL构建验证
            try:
                token_url = self.build_token_request_url(base_url)
                if token_url and 'appKey' in token_url and 'signature' in token_url:
                    validation_results['url_construction'] = True
                else:
                    validation_results['errors'].append("URL构建验证失败")
            except Exception:
                validation_results['errors'].append(f"URL构建失败: {str(e)}")

            # 综合评估
            validation_results['overall_success'] = all([
                validation_results['timestamp_generation'],
                validation_results['signature_generation'],
                validation_results['url_construction']
            ])

            logger.info(
                "认证流程验证完成",
                success=validation_results['overall_success'],
                errors_count=len(validation_results['errors'])
            )

        except Exception:
            validation_results['errors'].append(f"认证流程验证异常: {str(e)}")
            logger.error(
                "认证流程验证异常",
                error=str(e)
            )

        return validation_results


def create_enhanced_authenticator(
        app_key: str,
        app_secret: str,
        tenant_id: str) -> EnhancedAuthenticator:
    """创建增强认证器实例"""
    return EnhancedAuthenticator(app_key, app_secret, tenant_id)
