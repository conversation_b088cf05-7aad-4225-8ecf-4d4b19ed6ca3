import shutil
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动清理明显的测试文件和临时文件
Manual Cleanup of Obvious Test Files
"""


def manual_cleanup():
    """手动清理明显可以删除的文件"""
    project_root = Path(__file__).parent.parent

    # 明显可以删除的文件列表
    files_to_delete = [
        "temp_update.py",
        "test_baseline_api.py",
        "quick_test.py",
        "test_sentinel.py",
        "test_anti_duplicate_system.py",
        "day3_final_test.py",
        "test_server.py",
        "comprehensive_production_test.py",
        "test_day3_core.py",
        "test_proxy.py",
        "test_day3_offline.py",
        "start_quick_test.py",
        "simple_week3_test.py",
        "test_real_data_validation.py",
    ]

    # 目录级删除
    dirs_to_clean = [
        ".cleanup_trash",
        "tests/module_migration",
        "frontend/tests",
        "migration/week1_analysis/tests",
    ]

    print("🧹 手动清理测试文件")
    print("=" * 50)

    deleted_count = 0

    # 删除单个文件
    for file_name in files_to_delete:
        file_path = project_root / file_name
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"✅ 已删除: {file_name}")
                deleted_count += 1
            except Exception:
                print(f"❌ 删除失败 {file_name}: {e}")
        else:
            print(f"ℹ️ 文件不存在: {file_name}")

    # 清理目录
    for dir_name in dirs_to_clean:
        dir_path = project_root / dir_name
        if dir_path.exists() and dir_path.is_dir():
            try:
                shutil.rmtree(dir_path)
                print(f"✅ 已删除目录: {dir_name}")
                deleted_count += 1
            except Exception:
                print(f"❌ 删除目录失败 {dir_name}: {e}")
        else:
            print(f"ℹ️ 目录不存在: {dir_name}")

    # 清理__pycache__目录
    print("\n🔍 清理__pycache__目录...")
    pycache_count = 0
    for pycache_dir in project_root.rglob("__pycache__"):
        if pycache_dir.is_dir():
            try:
                shutil.rmtree(pycache_dir)
                print(f"✅ 已删除: {pycache_dir.relative_to(project_root)}")
                pycache_count += 1
            except Exception:
                print(f"❌ 删除失败: {e}")

    # 清理.pyc文件
    print("\n🔍 清理.pyc文件...")
    pyc_count = 0
    for pyc_file in project_root.rglob("*.pyc"):
        try:
            pyc_file.unlink()
            pyc_count += 1
        except Exception:
            print(f"❌ 删除.pyc文件失败: {e}")

    print(f"\n📊 清理结果:")
    print(f"  - 文件/目录: {deleted_count} 个")
    print(f"  - __pycache__: {pycache_count} 个")
    print(f"  - .pyc文件: {pyc_count} 个")
    print(f"  - 总计: {deleted_count + pycache_count + pyc_count} 项")

    return deleted_count + pycache_count + pyc_count


if __name__ == "__main__":
    total_cleaned = manual_cleanup()
    print(f"\n🎉 手动清理完成，共清理 {total_cleaned} 项")
