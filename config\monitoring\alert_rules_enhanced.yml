# 增强版告警规则
# 专门监控 api_error_rate 和 db_retry_count

groups:
  - name: ys-api-critical-alerts
    interval: 10s
    rules:
      # 高优先级：API错误率告警
      - alert: HighAPIErrorRate
        expr: |
          (
            rate(api_error_total[5m]) / 
            rate(api_request_total[5m])
          ) * 100 > 5
        for: 2m
        labels:
          severity: critical
          service: ys-api
          alert_type: error_rate
        annotations:
          summary: "API错误率过高"
          description: "API错误率在过去5分钟内超过5%，当前值: {{ $value }}%"
          runbook_url: "https://docs.ys-api.com/alerts/high-error-rate"
          dashboard_url: "http://grafana:3000/d/api-errors"

      # 高优先级：数据库重试次数告警  
      - alert: HighDatabaseRetryCount
        expr: |
          rate(db_retry_count_total[5m]) > 10
        for: 3m
        labels:
          severity: warning
          service: ys-api
          alert_type: database_retry
        annotations:
          summary: "数据库重试次数过高"
          description: "数据库重试频率在过去5分钟内超过10次/分钟，当前值: {{ $value }}"
          possible_causes: "数据库连接不稳定、死锁频繁、网络延迟"
          action_required: "检查数据库状态和网络连接"

      # 数据库死锁专项告警
      - alert: DatabaseDeadlockDetected
        expr: |
          increase(db_retry_count_total{retry_reason="deadlock"}[10m]) > 5
        for: 1m
        labels:
          severity: critical
          service: ys-api
          alert_type: database_deadlock
        annotations:
          summary: "检测到数据库死锁"
          description: "在过去10分钟内检测到{{ $value }}次死锁"
          immediate_action: "检查并发事务和锁定策略"

      # 响应时间恶化告警
      - alert: ResponseTimeDeterioration
        expr: |
          histogram_quantile(0.95, 
            rate(http_request_duration_seconds_bucket[10m])
          ) > 2
        for: 5m
        labels:
          severity: warning
          service: ys-api
          alert_type: performance
        annotations:
          summary: "API响应时间恶化"
          description: "95%的请求响应时间超过2秒，当前P95: {{ $value }}s"
          
      # 重试成功率低告警
      - alert: LowRetrySuccessRate
        expr: |
          (
            rate(db_retry_success_total[10m]) / 
            rate(db_retry_count_total[10m])
          ) < 0.8
        for: 5m
        labels:
          severity: warning
          service: ys-api
          alert_type: retry_failure
        annotations:
          summary: "重试成功率过低"
          description: "重试成功率低于80%，当前值: {{ $value | humanizePercentage }}"

  - name: ys-api-system-alerts
    interval: 30s
    rules:
      # 系统资源告警
      - alert: HighMemoryUsage
        expr: |
          (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "内存使用率过高"
          description: "系统内存使用率超过85%，当前值: {{ $value }}%"

      - alert: HighCPUUsage
        expr: |
          100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 10m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "CPU使用率过高"
          description: "系统CPU使用率超过80%，当前值: {{ $value }}%"

      # 磁盘空间告警
      - alert: LowDiskSpace
        expr: |
          (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 85
        for: 5m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "磁盘空间不足"
          description: "磁盘使用率超过85%，挂载点: {{ $labels.mountpoint }}，当前值: {{ $value }}%"

  - name: ys-api-business-alerts
    interval: 60s
    rules:
      # 业务指标告警
      - alert: LowConfigSaveSuccessRate
        expr: |
          (
            rate(config_save_success_total[30m]) / 
            rate(config_save_attempts_total[30m])
          ) < 0.95
        for: 10m
        labels:
          severity: warning
          service: ys-api
          alert_type: business_metric
        annotations:
          summary: "配置保存成功率过低"
          description: "配置保存成功率低于95%，当前值: {{ $value | humanizePercentage }}"

      - alert: UnusualErrorPattern
        expr: |
          rate(api_error_total{error_type="validation"}[15m]) > 
          rate(api_error_total{error_type="validation"}[6h] offset 1d) * 3
        for: 5m
        labels:
          severity: warning
          service: ys-api
          alert_type: anomaly
        annotations:
          summary: "异常错误模式"
          description: "验证错误频率异常增加，比昨天同期高3倍"

  - name: ys-api-elk-alerts
    interval: 60s
    rules:
      # ELK系统告警
      - alert: ElasticsearchDown
        expr: |
          up{job="elasticsearch"} == 0
        for: 2m
        labels:
          severity: critical
          service: elk
        annotations:
          summary: "Elasticsearch服务下线"
          description: "Elasticsearch无法访问，日志收集将受影响"

      - alert: LogIngestionLag
        expr: |
          increase(logstash_events_in_total[5m]) == 0
        for: 5m
        labels:
          severity: warning
          service: elk
        annotations:
          summary: "日志摄取延迟"
          description: "Logstash在过去5分钟内没有处理新的日志事件"

# 告警路由配置
alerting_rules:
  # 立即通知的严重告警
  critical_immediate:
    - HighAPIErrorRate
    - DatabaseDeadlockDetected
    - ElasticsearchDown
    - LowDiskSpace
  
  # 5分钟聚合的警告
  warning_aggregated:
    - HighDatabaseRetryCount
    - ResponseTimeDeterioration
    - LowRetrySuccessRate
    - HighMemoryUsage
    - HighCPUUsage
  
  # 30分钟聚合的业务告警  
  business_aggregated:
    - LowConfigSaveSuccessRate
    - UnusualErrorPattern
    - LogIngestionLag
