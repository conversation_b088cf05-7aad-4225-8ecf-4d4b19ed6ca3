import asyncio
import time
from collections import defaultdict

import structlog

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步状态管理器
用于管理表级锁和同步状态，避免并发冲突
"""


logger = structlog.get_logger()


class SyncStatusManager:
    """同步状态管理器"""

    def __init___(self):
    """TODO: Add function description."""
    # 表级锁状态 {table_name: {"module": str, "start_time": datetime, "lock_id":
    # str}}
    self.table_locks = {}
    # 影子表队列 {table_name: asyncio.Queue}
    self.shadow_table_queues = defaultdict(asyncio.Queue)
    # 全局锁
    self.global_lock = asyncio.Lock()
    # 锁超时时间（秒）
    self.lock_timeout = 1800  # 30分钟

    async def is_table_locked(self, table_name: str) -> bool:
        """检查表是否被锁定"""
        async with self.global_lock:
            if table_name not in self.table_locks:
                return False

            lock_info = self.table_locks[table_name]
            start_time = lock_info["start_time"]

            # 检查锁是否超时
            if datetime.now() - start_time > timedelta(seconds=self.lock_timeout):
                logger.warning(
                    "表锁超时，自动释放",
                    table_name=table_name,
                    module=lock_info["module"],
                    duration_seconds=(
                        datetime.now() -
                        start_time).total_seconds(),
                )
                del self.table_locks[table_name]
                return False

            return True

    async def acquire_table_lock(
            self,
            table_name: str,
            module: str) -> Optional[str]:
        """获取表锁"""
        async with self.global_lock:
            if await self.is_table_locked(table_name):
                current_lock = self.table_locks[table_name]
                logger.warning(
                    "表已被锁定",
                    table_name=table_name,
                    current_module=current_lock["module"],
                    requesting_module=module,
                )
                return None

            lock_id = f"{module}_{int(time.time())}"
            self.table_locks[table_name] = {
                "module": module,
                "start_time": datetime.now(),
                "lock_id": lock_id,
            }

            logger.info(
                "获取表锁成功", table_name=table_name, module=module, lock_id=lock_id
            )
            return lock_id

    async def release_table_lock(self, table_name: str, lock_id: str) -> bool:
        """释放表锁"""
        async with self.global_lock:
            if table_name not in self.table_locks:
                logger.warning(
                    "尝试释放不存在的表锁", table_name=table_name, lock_id=lock_id
                )
                return False

            current_lock = self.table_locks[table_name]
            if current_lock["lock_id"] != lock_id:
                logger.warning(
                    "锁ID不匹配，无法释放",
                    table_name=table_name,
                    current_lock_id=current_lock["lock_id"],
                    release_lock_id=lock_id,
                )
                return False

            del self.table_locks[table_name]
            logger.info("释放表锁成功", table_name=table_name, lock_id=lock_id)
            return True

    async def get_table_lock_info(self, table_name: str) -> Optional[Dict]:
        """获取表锁信息"""
        async with self.global_lock:
            if table_name not in self.table_locks:
                return None

            lock_info = self.table_locks[table_name]
            return {
                "table_name": table_name,
                "module": lock_info["module"],
                "start_time": lock_info["start_time"].isoformat(),
                "lock_id": lock_info["lock_id"],
                "duration_seconds": (
                    datetime.now() - lock_info["start_time"]
                ).total_seconds(),
            }

    async def enqueue_shadow_table(
        self, table_name: str, shadow_table_name: str, module: str
    ):
        """将影子表加入队列"""
        queue = self.shadow_table_queues[table_name]
        await queue.put(
            {
                "shadow_table_name": shadow_table_name,
                "module": module,
                "enqueue_time": datetime.now(),
            }
        )

        logger.info(
            "影子表已加入队列",
            table_name=table_name,
            shadow_table_name=shadow_table_name,
            module=module,
            queue_size=queue.qsize(),
        )

    async def process_shadow_table_queue(
            self, table_name: str, switch_callback):
        """处理影子表队列"""
        queue = self.shadow_table_queues[table_name]

        while not queue.empty():
            try:
                shadow_info = await queue.get()
                shadow_table_name = shadow_info["shadow_table_name"]
                module = shadow_info["module"]

                logger.info(
                    "开始处理影子表切换",
                    table_name=table_name,
                    shadow_table_name=shadow_table_name,
                    module=module,
                )

                # 执行影子表切换
                result = await switch_callback(table_name, shadow_table_name)

                if result.get("success"):
                    logger.info(
                        "影子表切换成功",
                        table_name=table_name,
                        shadow_table_name=shadow_table_name,
                        module=module,
                    )
                else:
                    logger.error(
                        "影子表切换失败",
                        table_name=table_name,
                        shadow_table_name=shadow_table_name,
                        module=module,
                        error=result.get("message"),
                    )

                queue.task_done()

            except Exception:
                logger.error("处理影子表队列异常", table_name=table_name, error=str(e))
                queue.task_done()

    async def get_all_locks_status(self) -> List[Dict]:
        """获取所有锁状态"""
        async with self.global_lock:
            locks_status = []
            for table_name, lock_info in self.table_locks.items():
                locks_status.append(
                    {
                        "table_name": table_name,
                        "module": lock_info["module"],
                        "start_time": lock_info["start_time"].isoformat(),
                        "lock_id": lock_info["lock_id"],
                        "duration_seconds": (
                            datetime.now() - lock_info["start_time"]
                        ).total_seconds(),
                    }
                )
            return locks_status

    async def cleanup_expired_locks(self):
        """清理过期锁"""
        async with self.global_lock:
            expired_tables = []
            for table_name, lock_info in self.table_locks.items():
                if datetime.now() - lock_info["start_time"] > timedelta(
                    seconds=self.lock_timeout
                ):
                    expired_tables.append(table_name)

            for table_name in expired_tables:
                lock_info = self.table_locks[table_name]
                logger.warning(
                    "清理过期锁",
                    table_name=table_name,
                    module=lock_info["module"],
                    duration_seconds=(
                        datetime.now() - lock_info["start_time"]
                    ).total_seconds(),
                )
                del self.table_locks[table_name]


# 全局同步状态管理器实例
_sync_status_manager = None


def get_sync_status_manager() -> SyncStatusManager:
    """获取同步状态管理器实例"""
    global _sync_status_manager
    if _sync_status_manager is None:
        _sync_status_manager = SyncStatusManager()
    return _sync_status_manager
