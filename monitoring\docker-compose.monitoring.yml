services:
  grafana:
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
    image: grafana/grafana:latest
    ports:
    - 3000:3000
    volumes:
    - grafana-storage:/var/lib/grafana
  prometheus:
    command:
    - --config.file=/etc/prometheus/prometheus.yml
    - --storage.tsdb.path=/prometheus
    - --web.console.libraries=/etc/prometheus/console_libraries
    - --web.console.templates=/etc/prometheus/consoles
    - --web.enable-lifecycle
    image: prom/prometheus:latest
    ports:
    - 9090:9090
    volumes:
    - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
version: '3.8'
volumes:
  grafana-storage: {}
