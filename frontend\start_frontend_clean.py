#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 前端服务启动脚本 - 生产环境版本

============================================================
重要端口配置说明：
后端端口: 8050 (生产环境主服务)
前端端口: 8060 (管理界面) - 已标准化
数据源: 真实生产数据，非测试数据
模块数量: 14个完整业务模块(包含业务日志，已移除库存管理相关模块)
============================================================

特性: 端口管理、防错机制、真实数据连接
"""

import http.server
import socketserver
import sys
import time
import webbrowser
from pathlib import Path

from port_manager import PortManager

# 添加scripts目录到路径以导入端口管理器
sys.path.append(str(Path(__file__).parent.parent / "scripts"))

# 前端端口配置 - 固定端口，禁止改动
FRONTEND_PORT = 8060  # 固定前端端口 8060，禁止改动
BACKEND_PORT = 8050  # 固定后端端口 8050，禁止改动


class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""

    def __init__(self, *args, **kwargs):
        """初始化处理器"""
        # 设置前端目录为服务根目录
        frontend_dir = Path(__file__).parent
        super().__init__(*args, directory=str(frontend_dir), **kwargs)

    def end_headers(self):
        """添加CORS头"""
        # 添加CORS头
        self.send_header("Access-Control-Allow-Origin", "*")
        self.send_header(
            "Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS"
        )
        self.send_header(
            "Access-Control-Allow-Headers",
            "Content-Type, Authorization")
        super().end_headers()

    def do_OPTIONS(self):
        """处理预检请求"""
        # 处理预检请求
        self.send_response(200)
        self.end_headers()

    def log_message(self, format, *args):
        """自定义日志格式"""
        # 自定义日志格式
        print(f"[{self.address_string()}] {format % args}")


def main():
    """主启动函数"""
    print("🌐 YS-API V3.0 前端服务启动")
    print(f"端口: {FRONTEND_PORT}")
    print(f"后端服务端口: {BACKEND_PORT}")
    print()

    # 端口管理
    port_manager = PortManager()

    # 检查并准备前端端口
    if not port_manager.ensure_port_available(FRONTEND_PORT):
        print("前端端口准备失败，服务无法启动")
        sys.exit(1)

    # 检查前端目录
    frontend_dir = Path(__file__).parent
    if not frontend_dir.exists():
        port_manager.handle_critical_error(f"前端目录不存在: {frontend_dir}", None)

    print(f"前端目录: {frontend_dir}")
    print(f"服务地址: http://localhost:{FRONTEND_PORT}")
    print(f"字段配置: http://localhost:{FRONTEND_PORT}/field-config-fixed.html")
    print(f"API测试: http://localhost:{FRONTEND_PORT}/api-test.html")
    print()

    try:
        # 创建HTTP服务器
        with socketserver.TCPServer(
            ("", FRONTEND_PORT), CustomHTTPRequestHandler
        ) as httpd:
            print(f"✅ 前端服务已启动在端口 {FRONTEND_PORT}")
            print("按 Ctrl+C 停止服务")
            print()

            # 自动打开浏览器
            try:
                time.sleep(1)  # 等待服务启动
                webbrowser.open(
                    f"http://localhost:{FRONTEND_PORT}/field-config-fixed.html"
                )
                print("🌐 已自动打开浏览器")
            except (ImportError, OSError, ValueError) as e:
                print(f"自动打开浏览器失败: {e}")

            # 启动服务
            httpd.serve_forever()

    except KeyboardInterrupt:
        print("\n⏹️ 服务已停止")
    except (ImportError, OSError, ValueError) as e:
        port_manager.handle_critical_error("前端服务启动失败", e)


if __name__ == "__main__":
    main()
