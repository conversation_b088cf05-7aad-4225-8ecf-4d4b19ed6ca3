import configparser
import importlib.util
import os
import sqlite3
import subprocess
import sys
from datetime import datetime
from pathlib import Path

import requests

# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 项目健康检查脚本
全面检查项目代码质量、依赖、配置和运行状态
"""


class ProjectHealthChecker:
    """项目健康检查器"""

    def __init__(self, project_root: str):
        """内部方法: __init__"""
        self.project_root = Path(project_root)
        self.report = {
            "timestamp": None,
            "overall_status": "UNKNOWN",
            "checks": {},
            "issues": [],
            "recommendations": []
        }

    def run_all_checks(self) -> Dict:
        """运行所有检查"""
        self.logger.info("🔍 开始YS-API V3.0项目健康检查...")

        checks = [
            self.check_python_environment,
            self.check_dependencies,
            self.check_database,
            self.check_file_structure,
            self.check_config_files,
            self.check_frontend_files,
            self.check_api_endpoints,
            self.check_logs_and_temp_files,
            self.check_code_quality,
            self.check_security
        ]

        for check in checks:
            try:
                check()
            except Exception:
                self.report["issues"].append(
                    f"检查失败: {check.__name__} - {str(e)}")

        # 计算总体状态
        issues_count = len(self.report["issues"])
        if issues_count == 0:
            self.report["overall_status"] = "HEALTHY"
        elif issues_count <= 3:
            self.report["overall_status"] = "WARNING"
        else:
            self.report["overall_status"] = "CRITICAL"

        return self.report

    def check_python_environment(self):
        """执行 check_python_environment 检查"""
        """检查Python环境"""
         self.logger.info("📦 检查Python环境...")

          checks = {}

           # 检查Python版本
           version = sys.version_info
            checks["python_version"] = f"{version.major}.{version.minor}.{version.micro}"
            checks["python_version_ok"] = version.major >= 3 and version.minor >= 8

            # 检查pip
            try:
                subprocess.run([sys.executable, "-m", "pip", "--version"],
                               capture_output=True, check=True)
                checks["pip_available"] = True
            except subprocess.CalledProcessError:
                checks["pip_available"] = False
                self.report["issues"].append("❌ pip不可用")

            self.report["checks"]["python_environment"] = checks

    def check_dependencies(self):
        """执行 check_dependencies 检查"""
        """检查依赖包"""
         self.logger.info("📋 检查依赖包...")

          requirements_file = self.project_root / "backend" / "requirements.txt"
           if not requirements_file.exists():
                self.report["issues"].append("❌ requirements.txt文件不存在")
                return

            with open(requirements_file, 'r', encoding='utf-8') as f:
                requirements = [
                    line.strip() for line in f if line.strip() and not line.startswith('# ')]

            missing_packages = []
            for req in requirements:
                package = req.split('==')[0].split('>=')[0].split('<=')[0]
                try:
                    importlib.util.find_spec(package.replace('-', '_'))
                except ImportError:
                    missing_packages.append(package)

            self.report["checks"]["dependencies"] = {
                "total_packages": len(requirements),
                "missing_packages": missing_packages,
                "all_installed": len(missing_packages) == 0
            }

            if missing_packages:
                self.report["issues"].append(
                    f"❌ 缺少依赖包: {', '.join(missing_packages)}")

    def check_database(self):
        """执行 check_database 检查"""
        """检查数据库连接"""
         self.logger.info("🗄️ 检查数据库...")

          db_file = self.project_root / "backend" / "ysapi.db"
           checks = {
                "db_file_exists": db_file.exists(),
                "db_file_size": 0,
                "db_connection_ok": False
            }

            if db_file.exists():
                checks["db_file_size"] = db_file.stat().st_size
                try:
                    conn = sqlite3.connect(str(db_file))
                    cursor = conn.cursor()
                    cursor.execute(
                        "SELECT name FROM sqlite_master WHERE type='table';")
                    tables = cursor.fetchall()
                    checks["db_connection_ok"] = True
                    checks["tables_count"] = len(tables)
                    conn.close()
                except Exception:
                    self.report["issues"].append(f"❌ 数据库连接失败: {str(e)}")
            else:
                self.report["issues"].append("⚠️ 数据库文件不存在，将自动创建")

            self.report["checks"]["database"] = checks

    def check_file_structure(self):
        """执行 check_file_structure 检查"""
        """检查文件结构完整性"""
         self.logger.info("📁 检查文件结构...")

          required_dirs = [
               "backend", "frontend", "config", "docs", "logs", "excel"
               ]
           required_files = [
                "backend/main.py",
                "backend/start_simple.py",
                "backend/config.ini",
                "frontend/migration-test-fixed.html"
            ]

            checks = {
                "required_dirs": {},
                "required_files": {},
                "structure_complete": True
            }

            for dir_name in required_dirs:
                dir_path = self.project_root / dir_name
                exists = dir_path.exists() and dir_path.is_dir()
                checks["required_dirs"][dir_name] = exists
                if not exists:
                    checks["structure_complete"] = False
                    self.report["issues"].append(f"❌ 缺少目录: {dir_name}")

            for file_path in required_files:
                full_path = self.project_root / file_path
                exists = full_path.exists() and full_path.is_file()
                checks["required_files"][file_path] = exists
                if not exists:
                    checks["structure_complete"] = False
                    self.report["issues"].append(f"❌ 缺少文件: {file_path}")

            self.report["checks"]["file_structure"] = checks

    def check_config_files(self):
        """执行 check_config_files 检查"""
        """检查配置文件"""
         self.logger.info("⚙️ 检查配置文件...")

          config_file = self.project_root / "backend" / "config.ini"
           checks = {
                "config_file_exists": config_file.exists(),
                "sections": {},
                "values_valid": True
            }

            if config_file.exists():
                config = configparser.ConfigParser()
                try:
                    config.read(config_file, encoding='utf-8')

                    required_sections = ['database', 'api']
                    for section in required_sections:
                        checks["sections"][section] = config.has_section(
                            section)
                        if not checks["sections"][section]:
                            checks["values_valid"] = False
                            self.report["issues"].append(
                                f"❌ 配置缺少section: {section}")

                    # 检查关键配置值
                    if config.has_section('database'):
                        db_checks = {
                            "server": bool(
                                config.get(
                                    'database', 'server', fallback='')), "database": bool(
                config.get(
                    'database', 'database', fallback='')), "username": bool(
                        config.get(
                            'database', 'username', fallback=''))}
                        checks["database_config"] = db_checks

                except Exception:
                    self.report["issues"].append(f"❌ 配置文件读取失败: {str(e)}")
            else:
                self.report["issues"].append("❌ 配置文件不存在")

            self.report["checks"]["config_files"] = checks

    def check_frontend_files(self):
        """执行 check_frontend_files 检查"""
        """检查前端文件"""
         self.logger.info("🌐 检查前端文件...")

          frontend_dir = self.project_root / "frontend"
           checks = {
                "main_files": {},
                "css_files": {},
                "js_files": {},
                "migrated_files": {}
            }

            # 检查主要HTML文件
            main_files = [
                "database-v2.html", "excel-translation.html",
                "field-config-manual.html", "maintenance.html",
                "unified-field-config.html"
            ]

            for file_name in main_files:
                file_path = frontend_dir / file_name
                checks["main_files"][file_name] = file_path.exists()

            # 检查迁移后的文件
            migrated_dir = frontend_dir / "migrated"
            if migrated_dir.exists():
                migrated_files = [
                    "database-v2.html", "excel-translation.html",
                    "field-config-manual.html", "maintenance.html",
                    "unified-field-config.html"
                ]

                for file_name in migrated_files:
                    file_path = migrated_dir / file_name
                    checks["migrated_files"][file_name] = file_path.exists()

            self.report["checks"]["frontend"] = checks

    def check_api_endpoints(self):
        """执行 check_api_endpoints 检查"""
        """检查API端点"""
         self.logger.info("🔗 检查API端点...")

          checks = {
               "backend_running": False,
                "health_endpoint": False,
                "error": None
               }

           try:
                # 尝试检查健康端点
                response = requests.get(
                    "http://localhost:8000/health", timeout=3)
                checks["backend_running"] = True
                checks["health_endpoint"] = response.status_code == 200
            except requests.exceptions.ConnectionError:
                checks["error"] = "无法连接到后端服务"
            except requests.exceptions.Timeout:
                checks["error"] = "连接超时"
            except Exception:
                checks["error"] = str(e)

            self.report["checks"]["api_endpoints"] = checks

            if not checks["backend_running"]:
                self.report["recommendations"].append(
                    "💡 启动后端服务: python backend/start_simple.py")

    def check_logs_and_temp_files(self):
        """执行 check_logs_and_temp_files 检查"""
        """检查日志和临时文件"""
         self.logger.info("📝 检查日志和临时文件...")

          checks = {
               "log_files": [],
                "temp_files": [],
                "cleanup_needed": False
               }

           # 检查日志文件
           logs_dir = self.project_root / "logs"
            if logs_dir.exists():
                log_files = list(logs_dir.glob("*.log"))
                checks["log_files"] = [str(f.name) for f in log_files]

                # 检查日志文件大小
                for log_file in log_files:
                    size_mb = log_file.stat().st_size / (1024 * 1024)
                    if size_mb > 100:  # 大于100MB
                        checks["cleanup_needed"] = True
                        self.report["recommendations"].append(
                            f"💡 日志文件过大: {log_file.name} ({size_mb:.1f}MB)")

            # 检查临时文件
            temp_patterns = ["*.tmp", "*.temp", "*.bak", "*~"]
            for pattern in temp_patterns:
                temp_files = list(self.project_root.rglob(pattern))
                checks["temp_files"].extend(
                    [str(f.relative_to(self.project_root)) for f in temp_files])

            if checks["temp_files"]:
                checks["cleanup_needed"] = True
                self.report["recommendations"].append("💡 发现临时文件，建议清理")

            self.report["checks"]["logs_and_temp"] = checks

    def check_code_quality(self):
        """执行 check_code_quality 检查"""
        """检查代码质量"""
         self.logger.info("🔍 检查代码质量...")

          checks = {
               "python_files": 0,
                "html_files": 0,
                "js_files": 0,
                "quality_issues": []
               }

           # 统计文件数量
           python_files = list(self.project_root.rglob("*.py"))
            html_files = list(self.project_root.rglob("*.html"))
            js_files = list(self.project_root.rglob("*.js"))

            checks["python_files"] = len(python_files)
            checks["html_files"] = len(html_files)
            checks["js_files"] = len(js_files)

            # 检查Python文件中的常见质量问题
            for py_file in python_files[:20]:  # 限制检查前20个文件
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 检查TODO注释
                    if "TODO" in content or "FIXME" in content:  # 注意: 此处需要进一步优化
                        checks["quality_issues"].append(
                            f"{py_file.name} 包含TODO/FIXME")  # 注意: 此处需要进一步优化

                    # 检查print语句
                    if "self.logger.info(" in content and "console.log" not in content:
                        lines = content.split('\n')
                        for i, line in enumerate(lines, 1):
                            if "self.logger.info(" in line and "console.log" not in line:
                                checks["quality_issues"].append(
                                    f"{py_file.name}:{i} 包含print语句")

                except Exception:
                    pass

            self.report["checks"]["code_quality"] = checks

    def check_security(self):
        """执行 check_security 检查"""
        """检查安全相关配置"""
         self.logger.info("🔒 检查安全配置...")

          checks = {
               "config_has_secrets": False,
                "hardcoded_credentials": [],
                "security_issues": []
               }

           # 检查配置文件中的硬编码凭据
           config_file = self.project_root / "backend" / "config.ini"
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 检查可能的敏感信息
                    sensitive_patterns = [
                        "password",
                        "secret",
                        "key",
                        "token"
                    ]

                    for pattern in sensitive_patterns:
                        if pattern.lower() in content.lower():
                            lines = content.split('\n')
                            for line in lines:
                                if pattern.lower() in line.lower() and '=' in line:
                                    checks["hardcoded_credentials"].append(
                                        line.strip())

                except Exception:
                    pass

            # 检查JavaScript文件中的敏感信息
            js_files = list(self.project_root.rglob("*.js"))
            for js_file in js_files[:10]:  # 限制检查前10个文件
                try:
                    with open(js_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    if "apikey" in content.lower() or "password" in content.lower():
                        checks["security_issues"].append(
                            f"{js_file.name} 可能包含敏感信息")

                except Exception:
                    pass

            self.report["checks"]["security"] = checks

    def generate_report(self) -> str:
        """生成检查报告"""
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("YS-API V3.0 项目健康检查报告")
        report_lines.append("=" * 60)
        report_lines.append(f"检查时间: {self.report.get('timestamp', 'N/A')}")
        report_lines.append(f"总体状态: {self.report['overall_status']}")
        report_lines.append("")

        # 问题汇总
        if self.report["issues"]:
            report_lines.append("🔴 发现的问题:")
            for issue in self.report["issues"]:
                report_lines.append(f"  {issue}")
        else:
            report_lines.append("✅ 未发现严重问题")

        report_lines.append("")

        # 建议
        if self.report["recommendations"]:
            report_lines.append("💡 建议操作:")
            for rec in self.report["recommendations"]:
                report_lines.append(f"  {rec}")

        return "\n".join(report_lines)


if __name__ == "__main__":
    project_root = Path(__file__).parent
    checker = ProjectHealthChecker(str(project_root))

    # 设置时间戳
    checker.report["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # 运行检查
    result = checker.run_all_checks()

    # 保存报告
    report_file = project_root / "project_health_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(checker.generate_report())

    # 打印摘要
    self.logger.info("\n" + "=" * 60)
    logger.info("检查完成！")
    logger.info(f"报告已保存至: {report_file}")
    logger.info(f"总体状态: {result['overall_status']}")

    if result['issues']:
        self.logger.info(f"发现问题: {len(result['issues'])} 个")
        self.logger.info("请查看详细报告并处理相关问题")
    else:
        self.logger.info("✅ 项目状态良好！")

    # 生成修复脚本
    fix_script = project_root / "fix_issues.py"
    with open(fix_script, 'w', encoding='utf-8') as f:
        f.write('''# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 自动修复脚本
根据健康检查结果自动修复常见问题
"""


class AutoFixer:
    def __init__(self):
        """内部方法: __init__"""
        self.project_root = Path(__file__).parent
        self.fixed_issues = []


    def fix_missing_dependencies(self):
       """fix_missing_dependencies 方法"""
        """修复缺失的依赖包"""
        self.logger.info("🔧 修复缺失的依赖包...")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r",
                str(self.project_root / "backend" / "requirements.txt")
            ], check=True)
            self.fixed_issues.append("✅ 已安装缺失的依赖包")
        except Exception:
            logger.info(f"❌ 安装依赖包失败: {e}")


    def fix_database(self):
       """fix_database 方法"""
        """修复数据库问题"""
        self.logger.info("🔧 修复数据库...")
        db_file = self.project_root / "backend" / "ysapi.db"
        if not db_file.exists():
            try:
                conn = sqlite3.connect(str(db_file))
                conn.close()
                self.fixed_issues.append("✅ 已创建数据库文件")
            except Exception:
                logger.info(f"❌ 创建数据库失败: {e}")


    def fix_temp_files(self):
       """fix_temp_files 方法"""
        """清理临时文件"""
        self.logger.info("🔧 清理临时文件...")
        temp_patterns = ["*.tmp", "*.temp", "*.bak", "*~", "*.path_backup"]
        cleaned = 0

        for pattern in temp_patterns:
            for file_path in self.project_root.rglob(pattern):
                try:
                    file_path.unlink()
                    cleaned += 1
                except Exception:
                    pass

        if cleaned > 0:
            self.fixed_issues.append(f"✅ 已清理 {cleaned} 个临时文件")


    def run_all_fixes(self):
       """run_all_fixes 方法"""
        """运行所有修复"""
        self.logger.info("🛠️ 开始自动修复...")

        self.fix_missing_dependencies()
        self.fix_database()
        self.fix_temp_files()

        if self.fixed_issues:
            logger.info("\n修复完成:")
            for issue in self.fixed_issues:
                logger.info(f"  {issue}")
        else:
            logger.info("✅ 无需修复")

if __name__ == "__main__":
    fixer = AutoFixer()
    fixer.run_all_fixes()
''')

    logger.info(f"\n修复脚本已生成: {fix_script}")
    logger.info("运行 'python fix_issues.py' 可自动修复常见问题")
