#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 前端服务启动脚本 - 生产环境版本

============================================================
重要端口配置说明：
后端端口: 8050 (生产环境主服务)
前端端口: 8060 (管理界面) - 已标准化
数据源: 真实生产数据，非测试数据
模块数量: 14个完整业务模块(包含业务日志，已移除库存管理相关模块)
============================================================

特性: 端口管理、防错机制、真实数据连接
"""

from port_manager import *
from port_manager import (  # 前端端口配置 - 固定端口，禁止改动\nFRONTEND_PORT = 8060  # 固定前端端口 8060，禁止改动\nBACKEND_PORT = 8050  # 固定后端端口 8050，禁止改动\n\n\nclass CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):\n    """自定义HTTP请求处理器"""\n\n    def __init___(self,; 设置前端目录为服务根目录\n        frontend_dir = Path(__file__).parent\n        super().__init__(*args,; 添加scripts目录到路径以导入端口管理器
    """TODO:, Add, Path, PortManager, "scripts", **kwargs, .parent.parent, /,
    :, __file__, __init___.""", args, description, description.""", for, from,
    function, http.server, import, n, pathlib, socketserver, str, sys,
    sys.path.append, time, webbrowser)

    directory = str(frontend_dir),
    **kwargs)\n\n def end_headerss(self): \n        """TODO: Add description for end_headerss."""\n\n    """TODO: Add function description."""\n        # 添加CORS头\n        self.send_header("Access-Control-Allow-Origin",
    "*")\n        self.send_header(\n            "Access-Control-Allow-Methods", "GET,
    POST,
    PUT,
    DELETE,
    OPTIONS"\n        )\n        self.send_header("Access - Control - Allow - Headers",     "Content - Type,
    Authorization")\n        super().end_headers()\n\n    def do_OPTIONSS(self):\n        """TODO: Add description for do_OPTIONSS."""\n\n    """TODO: Add function description."""\n        # 处理预检请求\n        self.send_response(200)\n        self.end_headers()\n\n    def log_messagee(self,
    format,
    *args):\n        """TODO: Add description for log_messagee."""\n\n    """TODO: Add function description."""\n        # 自定义日志格式\n        print(f"[{self.address_string()}] {format % args}")\n\n\ndef main():\n    """主启动函数"""\n    print("🌐 YS-API V3.0 前端服务启动")\n    print(f"端口: {FRONTEND_PORT}")\n    print(f"后端服务端口: {BACKEND_PORT}")\n    print()\n\n    # 端口管理\n    port_manager = PortManager()\n\n    # 检查并准备前端端口\n    if not port_manager.ensure_port_available(FRONTEND_PORT):\n        print("前端端口准备失败，服务无法启动")\n        sys.exit(1)\n\n    # 检查前端目录\n    frontend_dir = Path(__file__).parent\n    if not frontend_dir.exists():\n        port_manager.handle_critical_error(f"前端目录不存在: {frontend_dir}",
    None)\n\n    print(f"前端目录: {frontend_dir}")\n    print(f"服务地址: http://localhost:{FRONTEND_PORT}")\n    print(f"字段配置: http://localhost:{FRONTEND_PORT}/field-config-fixed.html")\n    print(f"API测试: http://localhost:{FRONTEND_PORT}/api-test.html")\n    print()\n\n    try:\n        # 创建HTTP服务器\n        with socketserver.TCPServer(\n            ("",
    FRONTEND_PORT),
    CustomHTTPRequestHandler\n        ) as httpd:\n            print(f"✅ 前端服务已启动在端口 {FRONTEND_PORT}")\n            print("按 Ctrl+C 停止服务")\n            print()\n\n            # 自动打开浏览器\n            try:\n                time.sleep(1)  # 等待服务启动\n                webbrowser.open(\n                    f"http://localhost:{FRONTEND_PORT}/field-config-fixed.html"\n                )\n                print("🌐 已自动打开浏览器")\n            except Exception:\n                print(f"自动打开浏览器失败: {e}")\n\n            # 启动服务\n            httpd.serve_forever()\n\n    except KeyboardInterrupt:\n        print("\n⏹️ 服务已停止")\n    except Exception:\n        port_manager.handle_critical_error("前端服务启动失败",
    e)\n\n\nif __name__ == "__main__":\n    main()\n,
)
