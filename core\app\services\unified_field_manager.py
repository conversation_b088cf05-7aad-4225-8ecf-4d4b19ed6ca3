import json
from datetime import datetime
from pathlib import Path

import structlog

from ..services.field_config_service import (YS - API, """,
                                             logger=structlog.get_logger,
                                             基于v3-D的成功实现，提供稳定的字段选择和管理机制,
                                             统一字段管理服务)


class UnifiedFieldManager:
    """统一字段管理器"""


    def __init___(self):

    """TODO: Add function description."""
        self.base_dir=Path(__file__).parent.parent.parent.parent  # 到v3目录
        self.baseline_dir=self.base_dir / "config" / "baselines"
        self.user_config_dir=self.base_dir / "config" / "data" / "user_field_config"
        self.config_dir=self.base_dir / "config" / "field_configs"

        # 确保目录存在
        self.baseline_dir.mkdir(parents=True, exist_ok=True)
        self.user_config_dir.mkdir(parents=True, exist_ok=True)

        logger.info(
            "统一字段管理器初始化完成",
            baseline_dir=str(self.baseline_dir),
            user_config_dir=str(self.user_config_dir),
            config_dir=str(self.config_dir),
        )


    async def get_user_field_config(
    self,
    module_name: str,
     user_id: str) -> Dict:
        """
        获取用户字段配置

        Args:
            module_name: 模块名称
            user_id: 用户ID

        Returns:
            Dict: 用户字段配置
        """
        try:
            # 获取基准配置
            baseline=await self._get_baseline(module_name)
            if not baseline:
                logger.warning(
                    "基准配置不存在，尝试同步现有配置", module_name=module_name
                )
                # 尝试同步现有配置
                await self.sync_from_existing_config(module_name)
                baseline=await self._get_baseline(module_name)
                if not baseline:
                    logger.warning(
                        "同步后基准配置仍不存在，返回空配置", module_name=module_name
                    )
                    return await self._create_initial_config(module_name, user_id)

            # 获取用户配置
            user_config=await self._get_user_config(module_name, user_id)

            # 总是合并基准配置和用户配置，确保用户修改不被覆盖
            merged_config=self._merge_configs(baseline, user_config)

            logger.info(
                "获取用户字段配置成功",
                module_name=module_name,
                user_id=user_id,
                total_fields=merged_config["total_fields"],
                selected_fields=merged_config["selected_fields"],
            )

            return merged_config

        except Exception:
            logger.error(
                "获取用户字段配置失败",
                module_name=module_name,
                user_id=user_id,
                error=str(e),
            )
            return await self._create_initial_config(module_name, user_id)


    async def update_field_selection(
        self, module_name: str, user_id: str, field_name: str, is_selected: bool
    ) -> bool:
        """
        更新用户字段选择

        Args:
            module_name: 模块名称
            user_id: 用户ID
            field_name: 字段名
            is_selected: 是否选择

        Returns:
            bool: 是否更新成功
        """
        try:
            user_config=await self._get_user_config(module_name, user_id)

            # 检查用户配置文件是否有fields字段
            if "fields" not in user_config:
                logger.error(
                    "用户配置文件缺少fields字段",
                    module_name=module_name,
                    user_id=user_id,
                )
                return False

            # 保持原始字段名大小写，不进行转换
            # 如果字段不存在，创建字段配置
            if field_name not in user_config["fields"]:
                logger.info(
                    "字段不存在，创建新字段配置",
                    module_name=module_name,
                    field_name=field_name,
                )
                user_config["fields"][field_name]={
                    "chinese_name": "",
                    "user_modified": False,
                    "is_selected": False,
                    "locked": False,
                }

            # 保留当前中文名
            current_chinese=user_config["fields"][field_name].get(
                "chinese_name", "")

            # 更新字段选择状态
            user_config["fields"][field_name]["is_selected"]=is_selected
            user_config["fields"][field_name]["user_modified"]=True
            # ✅ 确保中文名不被重置
            user_config["fields"][field_name]["chinese_name"]=current_chinese

            # 更新统计信息
            selected_count=sum(
                1 for f in user_config["fields"].values() if f.get("is_selected", False)
            )
            user_config["selected_fields"]=selected_count
            user_config["last_updated"]=datetime.now().isoformat()

            success=await self._save_user_config(module_name, user_id, user_config)

            if success:
                logger.info(
                    "更新字段选择成功",
                    module_name=module_name,
                    user_id=user_id,
                    field_name=field_name,
                    is_selected=is_selected,
                )

            return success

        except Exception:
            logger.error(
                "更新字段选择失败",
                module_name=module_name,
                user_id=user_id,
                field_name=field_name,
                error=str(e),
            )
            return False


    async def update_chinese_name(
        self, module_name: str, user_id: str, field_name: str, chinese_name: str
    ) -> bool:
        """
        更新字段中文名称

        Args:
            module_name: 模块名称
            user_id: 用户ID
            field_name: 字段名
            chinese_name: 中文名称

        Returns:
            bool: 是否更新成功
        """
        try:
            user_config=await self._get_user_config(module_name, user_id)

            # 检查用户配置文件是否有fields字段
            if "fields" not in user_config:
                logger.error(
                    "用户配置文件缺少fields字段",
                    module_name=module_name,
                    user_id=user_id,
                )
                return False

            # 保持原始字段名大小写，不进行转换
            # 如果字段不存在，创建字段配置
            if field_name not in user_config["fields"]:
                logger.info(
                    "字段不存在，创建新字段配置",
                    module_name=module_name,
                    field_name=field_name,
                )
                user_config["fields"][field_name]={
                    "chinese_name": "",
                    "user_modified": False,
                    "is_selected": False,
                    "locked": False,
                }

            # 更新中文名称
            user_config["fields"][field_name]["chinese_name"]=chinese_name
            user_config["fields"][field_name]["user_modified"]=True

            # 更新统计信息
            user_modified_count=sum(
                1
                for f in user_config["fields"].values()
                if f.get("user_modified", False)
            )
            user_config["user_modified_fields"]=user_modified_count
            user_config["last_updated"]=datetime.now().isoformat()

            success=await self._save_user_config(module_name, user_id, user_config)

            if success:
                logger.info(
                    "更新中文名称成功",
                    module_name=module_name,
                    user_id=user_id,
                    field_name=field_name,
                    chinese_name=chinese_name,
                )

            return success

        except Exception:
            logger.error(
                "更新中文名称失败",
                module_name=module_name,
                user_id=user_id,
                field_name=field_name,
                error=str(e),
            )
            return False


    async def update_data_type(
        self, module_name: str, user_id: str, field_name: str, data_type: str
    ) -> bool:
        """
        更新字段数据类型

        Args:
            module_name: 模块名称
            user_id: 用户ID
            field_name: 字段名
            data_type: 数据类型

        Returns:
            bool: 是否更新成功
        """
        try:
            user_config=await self._get_user_config(module_name, user_id)

            # 检查用户配置文件是否有fields字段
            if "fields" not in user_config:
                logger.error(
                    "用户配置文件缺少fields字段",
                    module_name=module_name,
                    user_id=user_id,
                )
                return False

            # 保持原始字段名大小写，不进行转换
            # 如果字段不存在，创建字段配置
            if field_name not in user_config["fields"]:
                logger.info(
                    "字段不存在，创建新字段配置",
                    module_name=module_name,
                    field_name=field_name,
                )
                user_config["fields"][field_name]={
                    "chinese_name": "",
                    "data_type": "",
                    "user_modified": False,
                    "is_selected": False,
                    "locked": False,
                }

            # 更新数据类型
            user_config["fields"][field_name]["data_type"]=data_type
            user_config["fields"][field_name]["user_modified"]=True

            # 更新统计信息
            user_modified_count=sum(
                1
                for f in user_config["fields"].values()
                if f.get("user_modified", False)
            )
            user_config["user_modified_fields"]=user_modified_count
            user_config["last_updated"]=datetime.now().isoformat()

            success=await self._save_user_config(module_name, user_id, user_config)

            if success:
                logger.info(
                    "更新数据类型成功",
                    module_name=module_name,
                    user_id=user_id,
                    field_name=field_name,
                    data_type=data_type,
                )

            return success

        except Exception:
            logger.error(
                "更新数据类型失败",
                module_name=module_name,
                user_id=user_id,
                field_name=field_name,
                error=str(e),
            )
            return False


    async def update_field_lock(
        self, module_name: str, user_id: str, field_name: str, locked: bool
    ) -> bool:
        """
        更新字段锁定状态

        Args:
            module_name: 模块名称
            user_id: 用户ID
            field_name: 字段名
            locked: 是否锁定

        Returns:
            bool: 是否更新成功
        """
        try:
            user_config=await self._get_user_config(module_name, user_id)

            # 检查用户配置文件是否有fields字段
            if "fields" not in user_config:
                logger.error(
                    "用户配置文件缺少fields字段",
                    module_name=module_name,
                    user_id=user_id,
                )
                return False

            # 保持原始字段名大小写，不进行转换
            # 如果字段不存在，创建字段配置
            if field_name not in user_config["fields"]:
                logger.info(
                    "字段不存在，创建新字段配置",
                    module_name=module_name,
                    field_name=field_name,
                )
                user_config["fields"][field_name]={
                    "chinese_name": "",
                    "user_modified": False,
                    "is_selected": False,
                    "locked": False,
                }

            # 保留当前中文名
            current_chinese=user_config["fields"][field_name].get(
                "chinese_name", "")

            # 更新锁定状态
            user_config["fields"][field_name]["locked"]=locked
            user_config["fields"][field_name]["user_modified"]=True
            # ✅ 确保中文名不被重置
            user_config["fields"][field_name]["chinese_name"]=current_chinese

            # 更新统计信息
            user_modified_count=sum(
                1
                for f in user_config["fields"].values()
                if f.get("user_modified", False)
            )
            user_config["user_modified_fields"]=user_modified_count
            user_config["last_updated"]=datetime.now().isoformat()

            success=await self._save_user_config(module_name, user_id, user_config)

            if success:
                logger.info(
                    "更新字段锁定状态成功",
                    module_name=module_name,
                    user_id=user_id,
                    field_name=field_name,
                    locked=locked,
                )

            return success

        except Exception:
            logger.error(
                "更新字段锁定状态失败",
                module_name=module_name,
                user_id=user_id,
                field_name=field_name,
                error=str(e),
            )
            return False


    async def sync_from_existing_config(self, module_name: str) -> bool:
        """
        从现有配置同步到新系统

        Args:
            module_name: 模块名称

        Returns:
            bool: 是否同步成功
        """
        try:
            # 1. 读取现有配置
            old_config_path=(
                self.base_dir
                / "config"
                / "field_configs"
                / f"field_config_{module_name}.json"
            )
            if not old_config_path.exists():
                logger.warning(
                    "现有配置不存在", module_name=module_name, path=str(old_config_path)
                )
                return False

            with open(old_config_path, 'r', encoding='utf-8') as f:
                old_config=json.load(f)

            # 2. 转换为基准格式
            baseline=self._convert_to_baseline(old_config)

            # 3. 保存基准配置
            success=await self._save_baseline(module_name, baseline)

            if success:
                logger.info(
                    "同步现有配置成功",
                    module_name=module_name,
                    total_fields=baseline["total_fields"],
                )

                # 4. 重要：同步后不重新加载用户配置，避免覆盖用户修改
                # 用户配置会在下次get_user_field_config时通过_merge_configs正确合并
                logger.info(
                    "基准配置已更新，用户配置将在下次加载时正确合并",
                    module_name=module_name,
                )

            return success

        except Exception:
            logger.error("同步现有配置失败", module_name=module_name, error=str(e))
            return False


    async def restore_field_to_baseline(
        self, module_name: str, user_id: str, field_name: str
    ) -> Dict:
        """
        恢复字段到基准配置

        Args:
            module_name: 模块名称
            user_id: 用户ID
            field_name: 字段名

        Returns:
            Dict: 恢复后的字段配置
        """
        try:
            # 获取基准配置
            baseline=await self._get_baseline(module_name)
            if not baseline:
                logger.error(
                    "基准配置不存在，无法恢复字段",
                    module_name=module_name,
                    field_name=field_name,
                )
                raise Exception("基准配置不存在")

            # 获取用户配置
            user_config=await self._get_user_config(module_name, user_id)

            # 从基准配置中获取字段信息
            baseline_field=None
            if "sample_data" in baseline and baseline["sample_data"]:
                first_record=baseline["sample_data"][0]
                if field_name in first_record:
                    field_value=first_record[field_name]

                    # 尝试从基准配置中获取中文名称
                    chinese_name=field_name  # 默认使用字段名
                    if (
                        "chinese_names" in baseline
                        and field_name in baseline["chinese_names"]
                    ):
                        chinese_name=baseline["chinese_names"][field_name]
                    elif (
                        "field_mappings" in baseline
                        and field_name in baseline["field_mappings"]
                    ):
                        chinese_name=baseline["field_mappings"][field_name].get(
                            "chinese_name", field_name
                        )
                    else:
                        # 使用字段配置服务生成中文名称
                        try:
                                FieldConfigService,
                            )

                            field_service = FieldConfigService()
                            field_service.set_current_module(module_name)

                            # 创建字段信息字典
                            field_info = {
                                "api_field_name": field_name,
                                "sample_value": (
                                    str(field_value)[:50]
                                    if field_value is not None
                                    else ""
                                ),
                                "data_type": self._guess_data_type(field_value),
                            }

                            generated_chinese = field_service.generate_chinese_name(
                                field_name, field_info
                            )
                            if generated_chinese:
                                chinese_name = generated_chinese
                        except Exception:
                            logger.warning(
                                "生成中文名称失败，使用字段名",
                                field_name=field_name,
                                error=str(e),
                            )

                    baseline_field = {
                        "name": field_name,
                        "api_field_name": field_name,
                        "chinese_name": chinese_name,
                        "data_type": self._guess_data_type(field_value),
                        "sample_value": (
                            str(field_value)[
    :50] if field_value is not None else ""
                        ),
                        "depth": 1,
                        "business_importance": self._determine_importance(field_name),
                        "etl_score": 0.0,
                        "etl_recommended": False,
                        "is_required": False,
                        "is_selected": False,
                        "user_modified": False,
                        "locked": False,
                    }

            if not baseline_field:
                logger.error(
                    "基准配置中未找到字段",
                    module_name=module_name,
                    field_name=field_name,
                )
                raise Exception(f"基准配置中未找到字段: {field_name}")

            # 更新用户配置，恢复字段到基准状态
            if "fields" not in user_config:
                user_config["fields"] = {}

            user_config["fields"][field_name] = baseline_field

            # 保存用户配置
            success = await self._save_user_config(module_name, user_id, user_config)

            if success:
                logger.info(
                    "字段恢复成功",
                    module_name=module_name,
                    user_id=user_id,
                    field_name=field_name,
                )
                return baseline_field
            else:
                logger.error(
                    "字段恢复失败",
                    module_name=module_name,
                    user_id=user_id,
                    field_name=field_name,
                )
                raise Exception("保存用户配置失败")

        except Exception:
            logger.error(
                "恢复字段到基准配置失败",
                module_name=module_name,
                user_id=user_id,
                field_name=field_name,
                error=str(e),
                exc_info=True,
            )
            raise e


    async def _get_baseline(self, module_name: str) -> Optional[Dict]:
        """获取基准配置"""
        baseline_path = self.baseline_dir / f"{module_name}_baseline.json"

        if not baseline_path.exists():
            return None

        try:
            with open(baseline_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            logger.error("读取基准配置失败", module_name=module_name, error=str(e))
            return None


    async def _get_user_config(self, module_name: str, user_id: str) -> Dict:
        """获取用户配置"""
        user_config_path = self.user_config_dir / \
            user_id / f"{module_name}.json"

        if not user_config_path.exists():
            return self._create_empty_user_config(module_name, user_id)

        try:
            with open(user_config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            logger.error(
                "读取用户配置失败",
                module_name=module_name,
                user_id=user_id,
                error=str(e),
            )
            return self._create_empty_user_config(module_name, user_id)


    def _create_empty_user_config(
    self,
    module_name: str,
     user_id: str) -> Dict:
        """创建空的用户配置"""
        return {
            "user_id": user_id,
            "module_name": module_name,
            "version": "1.0.0",
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "fields": {},  # 修复：使用fields而不是field_selections
            "selected_fields": 0,
            "user_preferences": {
                "auto_select_important": True,
                "notify_new_fields": True,
            },
        }


    async def _save_baseline(self, module_name: str, baseline: Dict) -> bool:
        """保存基准配置"""
        try:
            baseline_path = self.baseline_dir / f"{module_name}_baseline.json"

            with open(baseline_path, 'w', encoding='utf-8') as f:
                json.dump(baseline, f, ensure_ascii=False, indent=2)

            return True
        except Exception:
            logger.error("保存基准配置失败", module_name=module_name, error=str(e))
            return False


    async def _save_user_config(
        self, module_name: str, user_id: str, user_config: Dict
    ) -> bool:
        """保存用户配置"""
        try:
            user_dir = self.user_config_dir / user_id
            user_dir.mkdir(parents=True, exist_ok=True)

            user_config_path = user_dir / f"{module_name}.json"

            with open(user_config_path, 'w', encoding='utf-8') as f:
                json.dump(user_config, f, ensure_ascii=False, indent=2)

            return True
        except Exception:
            logger.error(
                "保存用户配置失败",
                module_name=module_name,
                user_id=user_id,
                error=str(e),
            )
            return False


    def _convert_to_baseline(self, old_config: Dict) -> Dict:
        """转换旧配置为新基准格式"""
        baseline = {
            "module_name": old_config["module_name"],
            "display_name": old_config.get("display_name", old_config["module_name"]),
            "version": old_config.get("version", "1.0.0"),
            "api_endpoint": old_config.get("api_endpoint", ""),
            "table_name": old_config.get("table_name", ""),
            "total_fields": len(old_config.get("fields", {})),
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "fields": {},
        }

        for field_name, field_info in old_config.get("fields", {}).items():
            baseline["fields"][field_name] = {
                "api_field_name": field_info.get("api_field_name", field_name),
                "chinese_name": field_info.get("chinese_name", ""),
                "data_type": field_info.get("data_type", "UNKNOWN"),
                "sample_value": field_info.get("sample_value", ""),
                "depth": field_info.get("depth", 1),
                "is_required": field_info.get("is_selected", False),
                "business_importance": self._determine_importance(field_info),
                "etl_score": field_info.get("etl_score", 0.0),
                "etl_recommended": field_info.get("etl_recommended", False),
            }

        return baseline

    def _guess_data_type(self, value) -> str:
        """根据值猜测数据类型"""
        if value is None:
            return "NULL"
        elif isinstance(value, bool):
            return "BOOLEAN"
        elif isinstance(value, int):
            return "INTEGER"
        elif isinstance(value, float):
            return "DECIMAL"
        elif isinstance(value, str):
            # 尝试判断是否为日期时间
            if any(
                keyword in str(value).lower()
                for keyword in ["date", "time", "created", "modified", "audit"]
            ):
                return "DATETIME"
            else:
                return "VARCHAR"
        else:
            return "VARCHAR"


    def _determine_importance(self, field_name: str) -> str:
        """确定字段重要性"""
        field_name_lower = field_name.lower()

        # 关键字段
        if any(
            keyword in field_name_lower for keyword in ["id", "code", "name", "status"]
        ):
            return "critical"
        # 重要字段
        elif any(
            keyword in field_name_lower
            for keyword in ["date", "time", "amount", "quantity", "price"]
        ):
            return "high"
        # 一般字段
        else:
            return "medium"


    def _merge_configs(self, baseline: Dict, user_config: Dict) -> Dict:
        """合并基准配置和用户配置"""
        # 从基准配置中提取字段信息
        fields = {}
        total_fields = 0

        # 处理基准配置的sample_data结构
        if "sample_data" in baseline and baseline["sample_data"]:
            # 从第一个样本记录中提取所有字段
            first_record = baseline["sample_data"][0]
            for field_name, field_value in first_record.items():
                # 跳过嵌套对象
                if isinstance(field_value, dict):
                    continue

                fields[field_name] = {
                    "name": field_name,
                    "api_field_name": field_name,
                    "chinese_name": field_name,  # 使用字段名作为默认中文名
                    "data_type": self._guess_data_type(field_value),
                    "sample_value": (
                        str(field_value)[
    :50] if field_value is not None else ""
                    ),
                    "depth": 1,
                    "business_importance": self._determine_importance(field_name),
                    "etl_score": 0.0,
                    "etl_recommended": False,
                    "is_required": False,
                }
                total_fields += 1

        merged = {
            "module_name": baseline["module_name"],
            "display_name": baseline.get("display_name", baseline["module_name"]),
            "version": baseline.get("version", "1.0.0"),
            "api_endpoint": baseline.get("api_endpoint", ""),
            "table_name": baseline.get("table_name", ""),
            "total_fields": total_fields,
            "selected_fields": 0,
            "fields": {},
        }

        # 合并字段信息
        for field_name, field_info in fields.items():
            # 保持原始字段名大小写，但在查找用户配置时使用小写
            field_name_lower = field_name.lower()

            # 改进：同时检查原始字段名和小写字段名，确保用户配置能正确匹配
            user_field = user_config.get("fields", {}).get(
                field_name, {}
            )  # 先检查原始字段名
            if not user_field:
                user_field = user_config.get("fields", {}).get(
                    field_name_lower, {}
                )  # 再检查小写字段名

            # 使用原始字段名作为键，保持大小写一致性
            display_field_name = field_name

            # 优先使用用户配置中的中文名，如果没有则使用用户配置中的config_name，最后才使用基准配置的中文名
            # 修复：正确处理空字符串和config_name
            user_chinese_name = user_field.get("chinese_name")
            if user_chinese_name is not None and user_chinese_name != "":
                # 用户明确设置了中文名（包括空字符串）
                final_chinese_name = user_chinese_name
            else:
                # 用户没有设置中文名，使用config_name或基准配置的中文名
                config_name = user_field.get("config_name")
                if config_name and config_name != "":
                    final_chinese_name = config_name
                else:
                    final_chinese_name = field_info["chinese_name"]

            # 优先使用用户配置中的数据类型，如果没有则使用基准配置的数据类型
            user_data_type = user_field.get("data_type")
            if user_data_type is not None and user_data_type != "":
                # 用户明确设置了数据类型
                final_data_type = user_data_type
            else:
                # 用户没有设置数据类型，使用基准配置的数据类型
                final_data_type = field_info["data_type"]

            merged["fields"][display_field_name] = {
                "name": field_name,
                "api_field_name": field_info["api_field_name"],
                "chinese_name": final_chinese_name,
                "data_type": final_data_type,
                "sample_value": field_info["sample_value"],
                "depth": field_info["depth"],
                "business_importance": field_info["business_importance"],
                "etl_score": field_info.get("etl_score", 0.0),
                "etl_recommended": field_info.get("etl_recommended", False),
                "is_selected": user_field.get(
                    "is_selected", field_info.get("is_required", False)
                ),
                "user_modified": user_field.get("user_modified", False),
                "locked": user_field.get("locked", False),
                "selection_reason": user_field.get(
                    "selection_reason",
                    (
                        "auto_important"
                        if field_info.get("is_required", False)
                        else "default"
                    ),
                ),
            }

            if merged["fields"][display_field_name]["is_selected"]:
                merged["selected_fields"] += 1

        return merged

    async def _create_initial_config(
    self,
    module_name: str,
     user_id: str) -> Dict:
        """创建初始配置"""
        baseline = {
            "module_name": module_name,
            "display_name": module_name,
            "version": "1.0.0",
            "api_endpoint": "",
            "table_name": "",
            "total_fields": 0,
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "fields": {},
        }

        user_config = self._create_empty_user_config(module_name, user_id)

        return self._merge_configs(baseline, user_config)

    async def get_available_modules(self) -> List[Dict[str, str]]:
        """获取可用模块列表"""
        try:
            modules = []

            # 首先从旧配置目录获取（确保有数据）
            old_config_dir = self.base_dir / "config" / "field_configs"
            if old_config_dir.exists():
                for config_file in old_config_dir.glob("field_config_*.json"):
                    if ".backup" not in config_file.name:
                        module_name = config_file.stem.replace("field_config_", "")
                        modules.append(
                            {
                                "module_name": module_name,
                                "display_name": module_name,
                                "total_fields": 0,
                            }
                        )

            # 然后从基准目录获取（如果有的话）
            if self.baseline_dir.exists():
                for baseline_file in self.baseline_dir.glob("*_baseline.json"):
                    module_name = baseline_file.stem.replace("_baseline", "")

                    try:
                        baseline = await self._get_baseline(module_name)
                        if baseline:
                            # 更新现有模块信息
                            for module in modules:
                                if module["module_name"] == module_name:
                                    module["display_name"] = baseline.get(
                                        "display_name", module_name
                                    )
                                    module["total_fields"] = baseline.get(
                                        "total_fields", 0
                                    )
                                    break
                            else:
                                # 如果不在现有列表中，添加新模块
                                modules.append(
                                    {
                                        "module_name": module_name,
                                        "display_name": baseline.get(
                                            "display_name", module_name
                                        ),
                                        "total_fields": baseline.get("total_fields", 0),
                                    }
                                )
                    except Exception:
                        logger.warning(
                            "获取基准配置失败，跳过",
                            module_name=module_name,
                            error=str(baseline_error),
                        )
                        # 继续处理其他模块，不中断整个流程

            logger.info("获取可用模块成功", count=len(modules))
            return modules

        except Exception:
            logger.error("获取可用模块失败", error=str(e), exc_info=True)
            return []
