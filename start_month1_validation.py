import subprocess
import time
from pathlib import Path

#!/usr/bin/env python3
"""
第1个月验证启动脚本
开始3个月迁移计划的第1个月: 14模块深度验证
"""


def print_banner():
    """打印启动横幅"""
    print("=" * 70)
    print("🚀 YS-API V3.0 第1个月验证计划启动")
    print("=" * 70)
    print("📅 计划周期: Day 6-35 (30天)")
    print("🎯 目标: 完成15个业务模块的4步验证流程")
    print("📋 验证步骤: 测试通过 → 删除测试文件 → 删除模拟数据 → 真实数据跑通")
    print("✅ 成功标准: 单模块≥75%, 批量验证≥80%")
    print("=" * 70)


def check_prerequisites():
    """检查前置条件"""
    print("\n🔍 检查前置条件...")

    checks = [
        ("模块验证脚本", "verify_module.py"),
        ("批量验证脚本", "batch_validate_modules.py"),
        ("模块配置目录", "模块字段/backup"),
        ("结果保存目录", "legacy_snapshots"),
        ("5天闭环文档", "TASK.md"),
    ]

    all_good = True
    for name, path in checks:
        if Path(path).exists():
            print(f"✅ {name}: {path}")
        else:
            print(f"❌ {name}: {path} 不存在!")
            all_good = False

    return all_good


def run_initial_validation():
    """运行初始验证"""
    print("\n🏃 运行初始批量验证...")

    try:
        # 确保目录存在
        Path("legacy_snapshots").mkdir(exist_ok=True)

        # 运行批量验证
        result = subprocess.run(
            ["python", "batch_validate_modules.py"],
            capture_output=True,
            text=True,
            encoding="utf-8",
        )

        print("📊 批量验证结果:")
        if result.stdout:
            # 提取关键统计信息
            lines = result.stdout.split("\n")
            for line in lines:
                if any(
                    keyword in line
                    for keyword in [
                        "总计模块数",
                        "验证通过",
                        "验证失败",
                        "成功率",
                        "耗时",
                    ]
                ):
                    print(f"   {line}")

        return result.returncode == 0

    except Exception:
        print(f"❌ 验证执行失败: {e}")
        return False


def generate_daily_plan():
    """生成每日验证计划"""
    modules = [
        "材料出库单列表查询",
        "采购订单列表",
        "采购入库单列表",
        "产品入库列表查询",
        "请购单列表查询",
        "生产订单列表查询",
        "委外订单列表",
        "委外入库列表查询",
        "委外申请列表查询",
        "销售出库列表查询",
        "销售订单",
        "需求计划",
        "业务日志",
    ]

    # 按天分配模块 (30天，每天0.5个模块平均)
    daily_plan = []
    modules_per_day = len(modules) / 30

    for day in range(1, 31):
        start_idx = int((day - 1) * modules_per_day)
        end_idx = int(day * modules_per_day)

        if day == 30:  # 最后一天包含所有剩余模块
            end_idx = len(modules)

        day_modules = modules[start_idx:end_idx]
        if day_modules:
            daily_plan.append(
                {
                    "day": day,
                    "modules": day_modules,
                    "focus": "验证" if day <= 20 else "修复",
                    "priority": "高" if day <= 10 else (
                        "中" if day <= 20 else "低"),
                })

    return daily_plan


def save_monthly_plan(daily_plan):
    """保存月度计划"""
    plan_content = []
    plan_content.append("# 第1个月验证详细计划")
    plan_content.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    plan_content.append("")
    plan_content.append("## 计划概览")
    plan_content.append("- 总计: 30天")
    plan_content.append("- 模块数: 15个")
    plan_content.append("- 验证阶段: Day 1-20 (主要验证)")
    plan_content.append("- 修复阶段: Day 21-30 (问题修复)")
    plan_content.append("")

    # 按周分组
    for week in range(1, 5):  # 4周
        week_start = (week - 1) * 7 + 1
        week_end = min(week * 7, 30)
        week_days = [
            d for d in daily_plan if week_start <= d["day"] <= week_end]

        plan_content.append(f"## 第{week}周 (Day {week_start}-{week_end})")
        for day_info in week_days:
            modules_str = (
                ", ".join(
                    day_info["modules"]) if day_info["modules"] else "休整日")
            plan_content.append(
                f"### Day {day_info['day']} - {day_info['focus']} ({day_info['priority']}优先级)"
            )
            plan_content.append(f"- 模块: {modules_str}")
            plan_content.append("")

    plan_content.append("## 执行命令")
    plan_content.append("```bash")
    plan_content.append("# 单个模块验证")
    plan_content.append('python verify_module.py "模块名称"')
    plan_content.append("")
    plan_content.append("# 批量验证")
    plan_content.append("python batch_validate_modules.py")
    plan_content.append("```")

    # 保存计划文件
    plan_file = Path("legacy_snapshots") / f"month1_plan_{int(time.time())}.md"
    with open(plan_file, "w", encoding="utf-8") as f:
        f.write("\n".join(plan_content))

    return plan_file


def main():
    """主函数"""
    print_banner()

    # 检查前置条件
    if not check_prerequisites():
        print("\n❌ 前置条件检查失败，请先完成5天闭环!")
        return 1

    print("\n✅ 前置条件检查通过")

    # 运行初始验证
    if not run_initial_validation():
        print("\n⚠️ 初始验证有问题，但继续制定计划...")

    # 生成计划
    print("\n📋 生成第1个月详细计划...")
    daily_plan = generate_daily_plan()
    plan_file = save_monthly_plan(daily_plan)

    print(f"✅ 月度计划已生成: {plan_file}")

    # 总结
    print("\n" + "=" * 70)
    print("🎉 第1个月验证计划启动完成!")
    print("=" * 70)
    print("📝 接下来的步骤:")
    print("1. 查看详细计划文件")
    print("2. 开始Day 1的模块验证")
    print("3. 每日跟踪进度和结果")
    print("4. 根据验证结果调整策略")
    print("")
    print("🚀 开始命令:")
    print('python verify_module.py "材料出库单列表查询"')
    print("")

    return 0


if __name__ == "__main__":
    exit(main())
