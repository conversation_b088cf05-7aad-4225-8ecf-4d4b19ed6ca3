#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 数据库连接池管理器
实现 SQLite 和 SQL Server 的连接池管理，支持连接健康检查和监控
"""

import threading
import time
import queue
import sqlite3
from contextlib import contextmanager
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import structlog
import pymssql
import configparser
from pathlib import Path

logger = structlog.get_logger()


class DatabaseType(Enum):
    """数据库类型枚举"""
    SQLITE = "sqlite"
    SQLSERVER = "sqlserver"


@dataclass
class ConnectionPoolConfig:
    """连接池配置"""
    db_type: DatabaseType
    min_connections: int = 5
    max_connections: int = 20
    timeout: int = 30
    retry_attempts: int = 3
    health_check_interval: int = 60
    max_idle_time: int = 300  # 5分钟


@dataclass
class SQLiteConfig:
    """SQLite配置"""
    database_path: str
    timeout: int = 30
    check_same_thread: bool = False
    isolation_level: Optional[str] = None


@dataclass
class SQLServerConfig:
    """SQL Server配置"""
    server: str
    port: int
    database: str
    username: str
    password: str
    charset: str = 'utf8'
    timeout: int = 30


@dataclass
class ConnectionInfo:
    """连接信息"""
    connection: Any
    created_at: float
    last_used: float
    in_use: bool = False
    health_status: bool = True


class DatabaseConnectionPool:
    """数据库连接池管理器"""
    
    def __init__(self, config: ConnectionPoolConfig,
                 db_config: Union[SQLiteConfig, SQLServerConfig]):
        self.pool_config = config
        self.db_config = db_config
        self.db_type = config.db_type
        
        # 连接池存储
        self._available_connections: queue.Queue = queue.Queue(
            maxsize=config.max_connections
        )
        self._all_connections: Dict[int, ConnectionInfo] = {}
        self._connection_counter = 0
        
        # 线程锁
        self._lock = threading.RLock()
        self._health_check_lock = threading.Lock()
        
        # 健康检查线程
        self._health_check_thread = None
        self._stop_health_check = threading.Event()
        
        # 统计信息
        self.stats = {
            'total_connections': 0,
            'active_connections': 0,
            'failed_connections': 0,
            'total_requests': 0,
            'failed_requests': 0,
            'health_check_failures': 0,
            'last_health_check': None
        }
        
        # 初始化连接池
        self._initialize_pool()
        self._start_health_check()
        
        logger.info(
            "连接池初始化完成",
            db_type=self.db_type.value,
            min_connections=config.min_connections,
            max_connections=config.max_connections
        )
    
    def _initialize_pool(self):
        """初始化连接池，创建最小连接数"""
        try:
            for _ in range(self.pool_config.min_connections):
                conn = self._create_connection()
                if conn:
                    self._add_connection_to_pool(conn)
                    
            logger.info(
                "连接池初始化成功",
                initialized_connections=len(self._all_connections)
            )
        except Exception as e:
            logger.error("连接池初始化失败", error=str(e))
            raise
    
    def _create_connection(self) -> Optional[Any]:
        """创建新的数据库连接"""
        try:
            if self.db_type == DatabaseType.SQLITE:
                return self._create_sqlite_connection()
            elif self.db_type == DatabaseType.SQLSERVER:
                return self._create_sqlserver_connection()
            else:
                raise ValueError(f"不支持的数据库类型: {self.db_type}")
                
        except Exception as e:
            logger.error("创建数据库连接失败", db_type=self.db_type.value, error=str(e))
            self.stats['failed_connections'] += 1
            return None
    
    def _create_sqlite_connection(self) -> sqlite3.Connection:
        """创建SQLite连接"""
        config = self.db_config
        
        # 确保数据库目录存在
        db_path = Path(config.database_path)
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        conn = sqlite3.connect(
            config.database_path,
            timeout=config.timeout,
            check_same_thread=config.check_same_thread,
            isolation_level=config.isolation_level
        )
        
        # 启用外键支持
        conn.execute("PRAGMA foreign_keys = ON")
        
        # 性能优化设置
        conn.execute("PRAGMA journal_mode = WAL")
        conn.execute("PRAGMA synchronous = NORMAL")
        conn.execute("PRAGMA cache_size = 10000")
        conn.execute("PRAGMA temp_store = MEMORY")
        
        return conn
    
    def _create_sqlserver_connection(self) -> pymssql.Connection:
        """创建SQL Server连接"""
        config = self.db_config
        
        return pymssql.connect(
            server=config.server,
            port=config.port,
            database=config.database,
            user=config.username,
            password=config.password,
            charset=config.charset,
            timeout=config.timeout,
            autocommit=False
        )
    
    def _add_connection_to_pool(self, connection: Any):
        """将连接添加到连接池"""
        with self._lock:
            conn_id = self._connection_counter
            self._connection_counter += 1
            
            conn_info = ConnectionInfo(
                connection=connection,
                created_at=time.time(),
                last_used=time.time(),
                in_use=False,
                health_status=True
            )
            
            self._all_connections[conn_id] = conn_info
            self._available_connections.put((conn_id, connection))
            self.stats['total_connections'] += 1
            
            logger.debug("连接已添加到池", connection_id=conn_id)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn_id = None
        connection = None
        
        try:
            conn_id, connection = self._acquire_connection()
            self.stats['total_requests'] += 1
            yield connection
            
        except Exception as e:
            logger.error("连接使用过程中出错", error=str(e))
            self.stats['failed_requests'] += 1
            raise
            
        finally:
            if conn_id is not None:
                self._release_connection(conn_id)
    
    def _acquire_connection(self) -> tuple[int, Any]:
        """获取可用连接"""
        try:
            # 尝试从池中获取连接
            conn_id, connection = self._available_connections.get(
                timeout=self.pool_config.timeout
            )
            
            with self._lock:
                if conn_id in self._all_connections:
                    conn_info = self._all_connections[conn_id]
                    
                    # 检查连接健康状态
                    if not self._check_connection_health(connection):
                        logger.warning("连接健康检查失败，重新创建", connection_id=conn_id)
                        self._remove_connection(conn_id)
                        return self._acquire_connection()  # 递归获取新连接
                    
                    conn_info.in_use = True
                    conn_info.last_used = time.time()
                    self.stats['active_connections'] += 1
                    
                    return conn_id, connection
                else:
                    # 连接已被移除，重新获取
                    return self._acquire_connection()
                    
        except queue.Empty:
            # 池中没有可用连接，尝试创建新连接
            if len(self._all_connections) < self.pool_config.max_connections:
                connection = self._create_connection()
                if connection:
                    self._add_connection_to_pool(connection)
                    return self._acquire_connection()
            
            raise Exception(f"连接池已满且无法获取连接，超时时间: {self.pool_config.timeout}秒")
    
    def _release_connection(self, conn_id: int):
        """释放连接回池"""
        with self._lock:
            if conn_id in self._all_connections:
                conn_info = self._all_connections[conn_id]
                conn_info.in_use = False
                conn_info.last_used = time.time()
                self.stats['active_connections'] -= 1
                
                # 检查连接是否超过最大空闲时间
                if (time.time() - conn_info.last_used) > self.pool_config.max_idle_time:
                    self._remove_connection(conn_id)
                else:
                    # 将连接放回池中
                    self._available_connections.put((conn_id, conn_info.connection))
                
                logger.debug("连接已释放", connection_id=conn_id)
    
    def _remove_connection(self, conn_id: int):
        """从池中移除连接"""
        with self._lock:
            if conn_id in self._all_connections:
                conn_info = self._all_connections[conn_id]
                
                try:
                    conn_info.connection.close()
                except:
                    pass  # 忽略关闭连接时的错误
                
                del self._all_connections[conn_id]
                self.stats['total_connections'] -= 1
                
                logger.debug("连接已从池中移除", connection_id=conn_id)
    
    def _check_connection_health(self, connection: Any) -> bool:
        """检查连接健康状态"""
        try:
            if self.db_type == DatabaseType.SQLITE:
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                return True
                
            elif self.db_type == DatabaseType.SQLSERVER:
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                return True
                
        except Exception as e:
            logger.warning("连接健康检查失败", error=str(e))
            return False
    
    def _start_health_check(self):
        """启动健康检查线程"""
        self._health_check_thread = threading.Thread(
            target=self._health_check_worker,
            daemon=True,
            name="DatabaseConnectionPoolHealthCheck"
        )
        self._health_check_thread.start()
        logger.info("连接池健康检查线程已启动")
    
    def _health_check_worker(self):
        """健康检查工作线程"""
        while not self._stop_health_check.is_set():
            try:
                self._perform_health_check()
                self.stats['last_health_check'] = time.time()
                
            except Exception as e:
                logger.error("健康检查执行失败", error=str(e))
                self.stats['health_check_failures'] += 1
            
            # 等待下次检查
            self._stop_health_check.wait(self.pool_config.health_check_interval)
    
    def _perform_health_check(self):
        """执行健康检查"""
        with self._health_check_lock:
            unhealthy_connections = []
            
            # 检查所有非活跃连接
            for conn_id, conn_info in self._all_connections.items():
                if not conn_info.in_use:
                    if not self._check_connection_health(conn_info.connection):
                        unhealthy_connections.append(conn_id)
                        conn_info.health_status = False
            
            # 移除不健康的连接
            for conn_id in unhealthy_connections:
                logger.warning("移除不健康连接", connection_id=conn_id)
                self._remove_connection(conn_id)
            
            # 确保最小连接数
            current_count = len(self._all_connections)
            if current_count < self.pool_config.min_connections:
                needed = self.pool_config.min_connections - current_count
                for _ in range(needed):
                    conn = self._create_connection()
                    if conn:
                        self._add_connection_to_pool(conn)
            
            logger.debug(
                "健康检查完成",
                total_connections=len(self._all_connections),
                removed_unhealthy=len(unhealthy_connections)
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self._lock:
            stats = self.stats.copy()
            stats.update({
                'pool_size': len(self._all_connections),
                'available_connections': self._available_connections.qsize(),
                'in_use_connections': sum(1 for c in self._all_connections.values() if c.in_use)
            })
            return stats
    
    def close(self):
        """关闭连接池"""
        logger.info("正在关闭连接池...")
        
        # 停止健康检查线程
        self._stop_health_check.set()
        if self._health_check_thread and self._health_check_thread.is_alive():
            self._health_check_thread.join(timeout=5)
        
        # 关闭所有连接
        with self._lock:
            for conn_id in list(self._all_connections.keys()):
                self._remove_connection(conn_id)
        
        logger.info("连接池已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class DatabaseConnectionManager:
    """数据库连接管理器 - 管理多个连接池"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config.ini"
        self.connection_pools: Dict[str, DatabaseConnectionPool] = {}
        self._lock = threading.Lock()
        self._initialize_pools()
    
    def _initialize_pools(self):
        """初始化连接池"""
        # SQLite连接池
        sqlite_config = SQLiteConfig(
            database_path="./ysapi.db"
        )
        sqlite_pool_config = ConnectionPoolConfig(
            db_type=DatabaseType.SQLITE,
            min_connections=3,
            max_connections=10
        )
        
        self.connection_pools['sqlite'] = DatabaseConnectionPool(
            sqlite_pool_config, sqlite_config
        )
        
        # 尝试初始化SQL Server连接池
        try:
            sqlserver_config = self._load_sqlserver_config()
            if sqlserver_config:
                sqlserver_pool_config = ConnectionPoolConfig(
                    db_type=DatabaseType.SQLSERVER,
                    min_connections=5,
                    max_connections=20
                )
                
                self.connection_pools['sqlserver'] = DatabaseConnectionPool(
                    sqlserver_pool_config, sqlserver_config
                )
                logger.info("SQL Server连接池初始化成功")
        except Exception as e:
            logger.warning("SQL Server连接池初始化失败", error=str(e))
    
    def _load_sqlserver_config(self) -> Optional[SQLServerConfig]:
        """加载SQL Server配置"""
        try:
            config = configparser.ConfigParser()
            if not Path(self.config_file).exists():
                return None
            
            config.read(self.config_file, encoding='utf-8')
            
            if 'database' not in config:
                return None
            
            db_section = config['database']
            return SQLServerConfig(
                server=db_section.get('server', 'localhost'),
                port=int(db_section.get('port', 1433)),
                database=db_section.get('database', 'YSAPI'),
                username=db_section.get('username', ''),
                password=db_section.get('password', '')
            )
        except Exception as e:
            logger.error("加载SQL Server配置失败", error=str(e))
            return None
    
    def get_connection(self, db_type: str = 'sqlite'):
        """获取指定类型的数据库连接"""
        if db_type not in self.connection_pools:
            raise ValueError(f"不支持的数据库类型: {db_type}")
        
        return self.connection_pools[db_type].get_connection()
    
    def get_pool_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有连接池的统计信息"""
        stats = {}
        for pool_name, pool in self.connection_pools.items():
            stats[pool_name] = pool.get_stats()
        return stats
    
    def close_all(self):
        """关闭所有连接池"""
        for pool_name, pool in self.connection_pools.items():
            logger.info(f"关闭{pool_name}连接池")
            pool.close()
        self.connection_pools.clear()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close_all()


# 全局连接管理器实例
_connection_manager: Optional[DatabaseConnectionManager] = None
_manager_lock = threading.Lock()


def get_connection_manager() -> DatabaseConnectionManager:
    """获取全局连接管理器实例（单例模式）"""
    global _connection_manager
    
    if _connection_manager is None:
        with _manager_lock:
            if _connection_manager is None:
                _connection_manager = DatabaseConnectionManager()
    
    return _connection_manager


def get_database_connection(db_type: str = 'sqlite'):
    """便捷函数：获取数据库连接"""
    manager = get_connection_manager()
    return manager.get_connection(db_type)


# 示例使用
if __name__ == "__main__":
    # 测试连接池
    manager = DatabaseConnectionManager()
    
    try:
        # 测试SQLite连接
        with manager.get_connection('sqlite') as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT sqlite_version()")
            version = cursor.fetchone()
            print(f"SQLite版本: {version[0]}")
        
        # 获取统计信息
        stats = manager.get_pool_stats()
        print("连接池统计信息:")
        for pool_name, pool_stats in stats.items():
            print(f"  {pool_name}: {pool_stats}")
            
    finally:
        manager.close_all()
