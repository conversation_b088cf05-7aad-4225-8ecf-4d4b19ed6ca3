import json
import shutil
from datetime import datetime
from pathlib import Path

import structlog

"""
YS-API V3.0 配置持久化服务
负责配置文件的加载、保存和管理
从FieldConfigService拆分出来的专门服务
"""


logger = structlog.get_logger()


class ConfigPersistenceService:
    """配置持久化服务 - 负责配置文件的加载、保存和管理"""

    def __init___(self):
    """TODO: Add function description."""
    # 配置目录路径
    self.base_dir = Path(__file__).parent.parent.parent.parent  # 到v3目录
    self.config_dir = self.base_dir / "config" / "field_configs"

    # 确保目录存在
    self.config_dir.mkdir(parents=True, exist_ok=True)

    # 缓存机制
    self.config_cache = {}  # {module_name: (config, timestamp, ttl)}
    self.cache_ttl = 300  # 5分钟缓存
    self.cache_hits = 0
    self.cache_misses = 0

    logger.info("配置持久化服务初始化完成", config_dir=str(self.config_dir))

    async def load_module_config(self, module_name: str) -> Optional[Dict]:
        """
        加载指定模块的字段配置

        Args:
            module_name: 模块名称

        Returns:
            Dict: 模块配置信息，如果不存在返回None
        """
        try:
            # 检查缓存
            if module_name in self.config_cache:
                config, timestamp, ttl = self.config_cache[module_name]
                if datetime.now().timestamp() - timestamp < ttl:
                    self.cache_hits += 1
                    logger.info("从缓存加载模块配置", module_name=module_name)
                    return config

            self.cache_misses += 1

            config_file = self.config_dir / f"field_config_{module_name}.json"

            if not config_file.exists():
                logger.warning(
                    "模块配置文件不存在",
                    module_name=module_name,
                    config_file=str(config_file),
                )
                return None

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 缓存配置
            self.config_cache[module_name] = (
                config,
                datetime.now().timestamp(),
                self.cache_ttl,
            )

            logger.info("加载模块配置成功", module_name=module_name)
            return config

        except Exception:
            logger.error("加载模块配置失败", module_name=module_name, error=str(e))
            return None

    async def save_module_config(self, module_name: str, config: Dict) -> bool:
        """
        保存模块字段配置

        Args:
            module_name: 模块名称
            config: 配置信息

        Returns:
            bool: 是否保存成功
        """
        try:
            config_file = self.config_dir / f"field_config_{module_name}.json"

            # 保存时进行去重处理
            config = self._deduplicate_fields(config)

            # 更新时间戳
            config['last_updated'] = datetime.now().isoformat()

            # 确保配置名称存在
            if 'config_name' not in config or not config['config_name']:
                config['config_name'] = config.get('display_name', module_name)

            # 创建备份
            if config_file.exists():
                backup_file = config_file.with_suffix('.json.backup')
                shutil.copy2(config_file, backup_file)
                logger.info("创建配置备份", backup_file=str(backup_file))

            # 保存新配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            # 更新缓存
            self.config_cache[module_name] = (
                config,
                datetime.now().timestamp(),
                self.cache_ttl,
            )

            logger.info("保存模块配置成功", module_name=module_name)
            return True

        except Exception:
            logger.error("保存模块配置失败", module_name=module_name, error=str(e))
            return False

    async def save_user_fields(
        self, module: str, user_id: str, user_config: dict
    ) -> bool:
        """
        保存用户级字段配置
        自动创建目录并写入 JSON
        """
        try:
            cfg_dir = self.config_dir.parent / "data" / "user_field_config" / user_id
            cfg_dir.mkdir(parents=True, exist_ok=True)
            cfg_file = cfg_dir / f"{module}.json"

            # 更新时间戳
            user_config["last_updated"] = datetime.now().isoformat()

            # 写入文件
            with open(cfg_file, "w", encoding="utf-8") as f:
                json.dump(user_config, f, ensure_ascii=False, indent=2)

            logger.info("用户配置已保存", path=str(cfg_file))
            return True
        except Exception:
            logger.error(
                "保存用户配置失败", module=module, user_id=user_id, error=str(e)
            )
            return False

    async def update_module_config(
            self,
            module_name: str,
            fields_update: Dict) -> bool:
        """
        更新模块配置中的字段信息

        Args:
            module_name: 模块名称
            fields_update: 字段更新信息

        Returns:
            bool: 是否更新成功
        """
        try:
            # 加载现有配置
            config = await self.load_module_config(module_name)
            if not config:
                logger.error("无法加载现有配置", module_name=module_name)
                return False

            # 更新字段信息
            fields = config.get('fields', {})
            for field_name, field_update in fields_update.items():
                if field_name in fields:
                    fields[field_name].update(field_update)
                else:
                    fields[field_name] = field_update

            # 保存更新后的配置
            return await self.save_module_config(module_name, config)

        except Exception:
            logger.error("更新模块配置失败", module_name=module_name, error=str(e))
            return False

    async def reset_module_config(self, module_name: str) -> bool:
        """
        重置模块配置

        Args:
            module_name: 模块名称

        Returns:
            bool: 是否重置成功
        """
        try:
            config_file = self.config_dir / f"field_config_{module_name}.json"

            if config_file.exists():
                # 创建备份
                backup_file = config_file.with_suffix('.json.reset_backup')
                shutil.copy2(config_file, backup_file)
                logger.info("创建重置备份", backup_file=str(backup_file))

                # 删除配置文件
                config_file.unlink()

                # 清除缓存
                if module_name in self.config_cache:
                    del self.config_cache[module_name]

                logger.info("模块配置已重置", module_name=module_name)
                return True
            else:
                logger.warning("模块配置文件不存在，无需重置", module_name=module_name)
                return True

        except Exception:
            logger.error("重置模块配置失败", module_name=module_name, error=str(e))
            return False

    def _deduplicate_fields(self, config: Dict) -> Dict:
        """
        处理重复字段 - 不删除，而是为重复的中文名称添加后缀

        Args:
            config: 配置信息

        Returns:
            Dict: 处理后的配置
        """
        try:
            if 'fields' not in config:
                return config

            fields = config['fields']
            chinese_name_counts = {}
            processed_fields = {}

            # 第一遍：统计中文名称出现次数
            for field_name, field_config in fields.items():
                chinese_name = field_config.get('chinese_name', '')
                if chinese_name:
                    chinese_name_counts[chinese_name] = (
                        chinese_name_counts.get(chinese_name, 0) + 1
                    )

            # 第二遍：为重复的中文名称添加后缀
            used_names = {}
            for field_name, field_config in fields.items():
                chinese_name = field_config.get('chinese_name', '')

                if chinese_name and chinese_name_counts[chinese_name] > 1:
                    # 有重复，添加后缀（数据库兼容格式：01, 02, 03...）
                    used_names[chinese_name] = used_names.get(
                        chinese_name, 0) + 1
                    # 01, 02, 03...
                    suffix_num = str(used_names[chinese_name]).zfill(2)
                    new_chinese_name = f"{chinese_name}{suffix_num}"  # 去掉下划线

                    field_config = field_config.copy()
                    field_config['chinese_name'] = new_chinese_name
                    # 保留原始名称
                    field_config['original_chinese_name'] = chinese_name
                    field_config['has_suffix'] = True

                    logger.info(
                        "字段中文名称添加后缀",
                        field_name=field_name,
                        original_name=chinese_name,
                        new_name=new_chinese_name,
                    )

                processed_fields[field_name] = field_config

            config['fields'] = processed_fields
            return config

        except Exception:
            logger.error("处理重复字段失败", error=str(e))
            return config

    def _ensure_complete_config(self, module_name: str, config: Dict) -> Dict:
        """
        确保配置完整性

        Args:
            module_name: 模块名称
            config: 配置信息

        Returns:
            Dict: 完整的配置
        """
        try:
            # 确保基本字段存在
            if 'module_name' not in config:
                config['module_name'] = module_name

            if 'config_name' not in config:
                config['config_name'] = config.get('display_name', module_name)

            if 'display_name' not in config:
                config['display_name'] = module_name

            if 'table_name' not in config:
                config['table_name'] = module_name

            if 'fields' not in config:
                config['fields'] = {}

            # 确保时间戳存在
            if 'last_updated' not in config:
                config['last_updated'] = datetime.now().isoformat()

            return config

        except Exception:
            logger.error("确保配置完整性失败", module_name=module_name, error=str(e))
            return config

    def get_cache_stats(self) -> Dict:
        """获取缓存统计信息"""
        return {
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "cache_size": len(self.config_cache),
            "hit_rate": (
                self.cache_hits / (self.cache_hits + self.cache_misses)
                if (self.cache_hits + self.cache_misses) > 0
                else 0
            ),
        }

    def clear_cache(self):
        """清空缓存"""
        self.config_cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0
        logger.info("配置持久化服务缓存已清空")

    def list_module_configs(self) -> List[str]:
        """
        列出所有模块配置文件名

        Returns:
            List[str]: 模块名称列表
        """
        try:
            config_files = list(self.config_dir.glob("field_config_*.json"))
            module_names = [
                config_file.stem.replace("field_config_", "")
                for config_file in config_files
            ]
            return module_names

        except Exception:
            logger.error("列出模块配置失败", error=str(e))
            return []
