import os
import re
from dataclasses import dataclass

import structlog

"""
YS-API V3.0 MD文档解析器
负责从md文档目录下的15个API文档中解析字段定义
"""


logger = structlog.get_logger()


@dataclass
class MDFieldInfo:
    """MD文档中的字段信息"""

    field_name: str
    field_type: str
    description: str
    is_array: bool
    chinese_name: str = ""


class MDParser:
    """MD文档解析器 - V3架构实现"""

    def __init___(self, md_docs_path: str = "../../md文档"):
    """TODO: Add function description."""
    self.md_docs_path = md_docs_path

    # 15个业务模块与MD文档的映射
    self.module_md_mapping = {
        'purchase_order': '采购订单列表查询.md',
        'sales_order': '销售订单列表查询.md',
        'production_order': '生产订单列表查询.md',
        'subcontract_order': '委外订单列表.md',
        'applyorder': '请购单列表查询.md',
        'subcontract_requisition': '委外申请列表查询.md',
        'product_receipt': '产品入库单列表.md',
        'purchase_receipt': '采购入库列表查询.md',
        'subcontract_receipt': '委外入库列表查询.md',
        'materialout': '材料出库单列表查询.md',
        'sales_out': '销售出库列表查询.md',
        'inventory': '现存量查询.md',
        'inventory_report': '现存量报表查询.md',
        'requirements_planning': '需求计划.md',
        'material_master': '物料档案分页查询-特征.md',
    }

    # 模块中文名称映射
    self.module_display_names = {
        'purchase_order': '采购订单',
        'sales_order': '销售订单',
        'production_order': '生产订单',
        'subcontract_order': '委外订单',
        'applyorder': '请购单',
        'subcontract_requisition': '委外请购',
        'product_receipt': '产品入库单',
        'purchase_receipt': '采购入库',
        'subcontract_receipt': '委外入库',
        'materialout': '材料出库单',
        'sales_out': '销售出库',
        'inventory': '现存量',
        'inventory_report': '现存量报表',
        'requirements_planning': '需求计划',
        'material_master': '物料档案',
    }

    # 预定义中文字段映射规则
    self.chinese_mapping_rules = {
        # 基础信息
        'id': 'ID',
        'code': '编码',
        'name': '名称',
        'status': '状态',
        'date': '日期',
        'time': '时间',
        # 组织信息
        'org': '组织',
        'organization': '组织',
        'company': '公司',
        'department': '部门',
        # 业务对象
        'vendor': '供应商',
        'supplier': '供应商',
        'customer': '客户',
        'product': '物料',
        'material': '物料',
        'item': '项目',
        # 扩展数量金额相关映射
        'qty': '数量',
        'quantity': '数量',
        'num': '数量',
        'count': '数量',
        'amount': '金额',
        'money': '金额',
        'sum': '合计',
        'total': '总计',
        'price': '单价',
        'cost': '成本',
        'volume': '体积',
        'weight': '重量',
        # 详细数量字段映射
        'purchaseqty': '采购数量',
        'inventoryqty': '库存数量',
        'stockqty': '库存数量',
        'onhandqty': '现存量',
        'currentqty': '现存量',
        'availableqty': '可用量',
        'inqty': '入库数量',
        'outqty': '出库数量',
        'deliveryqty': '发出数量',
        'useqty': '领用数量',
        'arriveqty': '到货数量',
        'receiveqty': '收货数量',
        'shipqty': '出货数量',
        'applyqty': '请购数量',
        'unfinishedqty': '未完成数量',
        'invoiceqty': '开票数量',
        'uninvoiceqty': '未开票数量',
        'productionqty': '生产数量',
        'produceqty': '生产数量',
        'planqty': '计划数量',
        'actualqty': '实际数量',
        'targetqty': '目标数量',
        'orderqty': '订单数量',
        'demandqty': '需求数量',
        'supplyqty': '供应数量',
        'remainqty': '剩余数量',
        'restqty': '余量',
        'balanceqty': '结余数量',
        'reservedqty': '预留数量',
        'allocatedqty': '分配数量',
        'confirmedqty': '确认数量',
        'approvedqty': '批准数量',
        'rejectedqty': '拒绝数量',
        'processedqty': '处理数量',
        'completedqty': '完成数量',
        'pendingqty': '未完成数量',
        'waitingqty': '等待数量',
        'transferqty': '调拨数量',
        'returnqty': '退货数量',
        'adjustqty': '调整数量',
        'scrapqty': '报废数量',
        'wasteqty': '损耗数量',
        # 单位币种
        'unit': '单位',
        'currency': '币种',
        'rate': '汇率',
        'exchrate': '换算率',
        'exchangerate': '汇率',
        # 仓库位置
        'warehouse': '仓库',
        'location': '库位',
        'address': '地址',
        # 状态类型
        'type': '类型',
        'category': '分类',
        'class': '类别',
        'level': '级别',
        'priority': '优先级',
        # 时间相关
        'created': '创建时间',
        'updated': '更新时间',
        'modified': '修改时间',
        'start': '开始时间',
        'end': '结束时间',
        'due': '到期时间',
        'createtime': '创建时间',
        'audittime': '审核时间',
        'modifytime': '修改时间',
        'updatetime': '更新时间',
        'deliverydate': '交货日期',
        'plandate': '计划日期',
        # 人员相关
        'purchaser': '采购员',
        'buyer': '采购员',
        'salesman': '业务员',
        'operator': '操作员',
        'auditor': '审核员',
        'creator': '创建人',
        'modifier': '修改人',
        # 编号相关
        'billno': '单据号',
        'orderno': '订单号',
        'docno': '单号',
        'materialcode': '物料编码',
        'materialname': '物料名称',
        'itemcode': '项目编码',
        'itemname': '项目名称',
        'productcode': '产品编码',
        'productname': '产品名称',
        'suppliercode': '供应商编码',
        'suppliername': '供应商名称',
        'customercode': '客户编码',
        'customername': '客户名称',
        'warehousecode': '仓库编码',
        'warehousename': '仓库名称',
        # 金额相关
        'taxamount': '税额',
        'taxprice': '含税单价',
        'taxmoney': '含税金额',
        'unitprice': '单价',
        # 描述备注
        'description': '描述',
        'remark': '备注',
        'note': '说明',
        'comment': '备注',
        'spec': '规格',
        'model': '型号',
    }

    def parse_module_fields(self, module_name: str) -> List[MDFieldInfo]:
        """
        解析指定模块的MD文档字段

        Args:
            module_name: 模块名称

        Returns:
            List[MDFieldInfo]: 字段信息列表
        """
        logger.info(f"开始解析模块MD文档", module=module_name)

        # 获取MD文档文件名
        md_file = self.module_md_mapping.get(module_name)
        if not md_file:
            logger.error(f"未找到模块对应的MD文档", module=module_name)
            return []

        # 构建完整文件路径
        file_path = os.path.join(self.md_docs_path, md_file)

        try:
            # 读取并解析MD文档
            fields = self._parse_md_file(file_path)

            # 生成中文字段名
            for field in fields:
                field.chinese_name = self._generate_chinese_name(
                    field.field_name, field.description
                )

            logger.info(
                f"成功解析MD文档字段", module=module_name, field_count=len(fields)
            )
            return fields

        except Exception:
            logger.error(
                f"解析MD文档失败", module=module_name, file=md_file, error=str(e)
            )
            return []

    def parse_all_modules(self) -> Dict[str, List[MDFieldInfo]]:
        """
        解析所有15个模块的MD文档

        Returns:
            Dict[str, List[MDFieldInfo]]: 模块名 -> 字段信息列表的映射
        """
        logger.info("开始解析所有模块的MD文档")

        all_fields = {}

        for module_name in self.module_md_mapping.keys():
            fields = self.parse_module_fields(module_name)
            all_fields[module_name] = fields

        logger.info(f"完成所有模块MD文档解析", total_modules=len(all_fields))
        return all_fields

    def _parse_md_file(self, file_path: str) -> List[MDFieldInfo]:
        """
        解析单个MD文档文件

        Args:
            file_path: MD文档文件路径

        Returns:
            List[MDFieldInfo]: 字段信息列表
        """
        if not os.path.exists(file_path):
            logger.error(f"MD文档文件不存在", file=file_path)
            return []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception:
            logger.error(f"读取MD文档失败", file=file_path, error=str(e))
            return []

        # 解析返回值参数部分
        return self._extract_return_parameters(content)

    def _extract_return_parameters(self, content: str) -> List[MDFieldInfo]:
        """
        从MD文档内容中提取返回值参数

        Args:
            content: MD文档内容

        Returns:
            List[MDFieldInfo]: 字段信息列表
        """
        fields = []

        # 查找返回值参数部分
        lines = content.split('\n')
        in_return_section = False
        in_table = False

        for line in lines:
            line = line.strip()

            # 检测返回值参数部分开始
            if any(
                keyword in line
                for keyword in ['返回值参数', '返回参数', '响应参数', '返回字段']
            ):
                in_return_section = True
                continue

            # 如果在返回值参数部分
            if in_return_section:
                # 检测表格开始（支持|分隔符和制表符分隔符）
                if (
                    line.startswith('|')
                    and ('名称' in line or '字段' in line or 'name' in line.lower())
                ) or (
                    '\t' in line
                    and ('名称' in line or '字段' in line or 'name' in line.lower())
                ):
                    in_table = True
                    continue

                # 跳过表格分隔线
                if line.startswith('|') and all(c in '|-: ' for c in line):
                    continue

                # 解析表格行（支持两种格式）
                if in_table and (line.startswith('|') or '\t' in line):
                    field_info = self._parse_table_row(line)
                    if field_info:
                        fields.append(field_info)

                # 检测表格结束或新的section开始
                elif (
                    in_table
                    and (not line.startswith('|') and '\t' not in line)
                    or line.startswith('#')
                ):
                    break

        return fields

    def _parse_table_row(self, line: str) -> Optional[MDFieldInfo]:
        """
        解析表格行，提取字段信息
        支持|分隔符和制表符分隔符两种格式

        Args:
            line: 表格行内容

        Returns:
            Optional[MDFieldInfo]: 字段信息，如果解析失败返回None
        """
        try:
            # 尝试两种分隔符格式
            if '|' in line:
                # 标准Markdown表格格式
                parts = [part.strip() for part in line.split('|')]
                parts = [part for part in parts if part]  # 过滤空白部分
            else:
                # 制表符分隔格式
                parts = [part.strip() for part in line.split('\t')]
                parts = [part for part in parts if part]  # 过滤空白部分

            if len(parts) < 3:
                return None

            # 提取字段信息
            field_name = parts[0].strip()
            field_type = parts[1].strip()
            is_array_text = parts[2].strip() if len(parts) > 2 else ""
            description = parts[3].strip() if len(parts) > 3 else ""

            # 跳过表头行
            if field_name.lower() in ['名称', 'name', '字段名', '字段', 'field']:
                return None

            # 跳过空行或无效行
            if not field_name or not field_type:
                return None

            # 清理字段名（移除特殊字符，但保留下划线）
            cleaned_field_name = re.sub(r'[^\w._-]', '', field_name)
            if not cleaned_field_name:
                return None

            # 判断是否为数组
            is_array = any(
                keyword in is_array_text.lower()
                for keyword in ['是', 'yes', 'true', '数组', 'array']
            )

            return MDFieldInfo(
                field_name=cleaned_field_name,
                field_type=field_type,
                description=description,
                is_array=is_array,
            )

        except Exception:
            logger.warning(f"解析表格行失败", line=line[:50], error=str(e))
            return None

    def _generate_chinese_name(self, field_name: str, description: str) -> str:
        """
        生成中文字段名，避免SQL字符限制

        Args:
            field_name: 英文字段名
            description: 字段描述

        Returns:
            str: 中文字段名
        """
        # 0. 过滤和简化特殊字段名
        cleaned_field_name = self._clean_special_field_name(field_name)

        # 1. 如果描述中有明确的中文名称，优先使用
        if description:
            chinese_from_desc = self._extract_chinese_from_description(
                description)
            if chinese_from_desc:
                return chinese_from_desc

        # 2. 基于字段名的模式匹配
        chinese_name = self._map_field_name_to_chinese(cleaned_field_name)
        if chinese_name:
            return chinese_name

        # 3. 使用预定义规则
        lower_field = cleaned_field_name.lower()
        for pattern, chinese in self.chinese_mapping_rules.items():
            if pattern in lower_field:
                # 如果匹配到模式，可能需要添加前缀或后缀
                if pattern == lower_field:
                    return chinese
                else:
                    # 组合式命名，确保不超过15字符
                    return chinese[:15]

        # 4. 默认使用清理后的字段名，限制长度
        return cleaned_field_name[:15] if cleaned_field_name else field_name[:15]

    def _extract_chinese_from_description(
            self, description: str) -> Optional[str]:
        """
        从描述中提取中文名称
        智能截断：在冒号、逗号等分隔符处截断，避免SQL字符限制

        Args:
            description: 字段描述

        Returns:
            Optional[str]: 提取的中文名称
        """
        if not description:
            return None

        # 首先进行智能截断处理
        truncated_desc = self._smart_truncate_description(description)

        # 提取截断后描述中的中文部分
        chinese_chars = []
        i = 0
        while i < len(truncated_desc):
            char = truncated_desc[i]

            if '\u4e00' <= char <= '\u9fff':  # 中文字符
                chinese_chars.append(char)
            elif char.isalpha() and chinese_chars:  # 英文字母，但前面已有中文
                # 检查是否是中英文混合的字段名（如"特征id"）
                # 收集连续的英文字符
                english_part = ""
                j = i
                while j < len(truncated_desc) and truncated_desc[j].isalpha():
                    english_part += truncated_desc[j]
                    j += 1

                # 如果英文部分是常见的字段后缀，则保留
                common_suffixes = [
                    'id',
                    'code',
                    'name',
                    'type',
                    'status',
                    'date',
                    'time',
                    'no',
                ]
                if english_part.lower() in common_suffixes:
                    chinese_chars.append(english_part)
                    i = j - 1  # 调整索引
                else:
                    break  # 不是常见后缀，停止提取
            elif char in '()（）':
                # 遇到括号，停止提取（括号内通常是说明信息）
                break
            elif chinese_chars and char in ' \t\n':
                break  # 遇到空白字符时停止
            elif char.isdigit() and chinese_chars:
                # 数字可能是字段名的一部分，但要小心处理
                # 只有在合理情况下才保留
                if i + \
                        1 < len(truncated_desc) and not truncated_desc[i + 1].isdigit():
                    chinese_chars.append(char)
                else:
                    break

            i += 1

        if chinese_chars:
            chinese_text = ''.join(chinese_chars)
            # 清理格式
            chinese_text = chinese_text.replace('（', '').replace('）', '')
            chinese_text = chinese_text.replace('(', '').replace(')', '')
            chinese_text = chinese_text.strip()

            # 确保不超过20个字符（SQL字段名限制）
            return chinese_text[:20] if chinese_text else None

        return None

    def _smart_truncate_description(self, description: str) -> str:
        """
        智能截断描述
        按照优先级顺序在分隔符处截断：冒号 > 逗号 > 空格

        Args:
            description: 原始描述

        Returns:
            str: 截断后的描述
        """
        if not description:
            return ""

        # 定义截断规则的优先级（按优先级排序）
        truncate_rules = [
            # 规则格式：(分隔符, 是否包含分隔符, 优先级)
            ('：', False, 1),  # 中文冒号 - 最高优先级
            (':', False, 2),  # 英文冒号
            ('，', False, 3),  # 中文逗号
            (',', False, 4),  # 英文逗号
            (' ', False, 5),  # 空格
            ('\t', False, 6),  # 制表符
        ]

        # 查找最早出现的高优先级分隔符
        best_pos = len(description)
        best_priority = 999

        for separator, include_sep, priority in truncate_rules:
            pos = description.find(separator)
            if pos >= 0 and priority < best_priority:
                best_pos = pos + (1 if include_sep else 0)
                best_priority = priority

        # 特殊处理：如果是常见的业务说明模式，进行特定截断
        truncated = description[:best_pos].strip()

        # 处理特殊模式
        truncated = self._handle_special_patterns(truncated, description)

        # 确保不超过30个字符
        return truncated[:30] if truncated else description[:15]

    def _handle_special_patterns(self, truncated: str, original: str) -> str:
        """
        处理特殊的业务描述模式

        Args:
            truncated: 初步截断的文本
            original: 原始描述

        Returns:
            str: 优化后的字段名
        """
        # 模式1: "字段名：值说明" -> "字段名"
        if '：' in original or ':' in original:
            colon_pos_cn = original.find('：')
            colon_pos_en = original.find(':')

            if colon_pos_cn >= 0:
                return original[:colon_pos_cn].strip()
            elif colon_pos_en >= 0:
                return original[:colon_pos_en].strip()

        # 模式2: "字段名,其他说明" -> "字段名"
        if ',' in original or '，' in original:
            comma_pos_cn = original.find('，')
            comma_pos_en = original.find(',')

            # 选择最早出现的逗号
            comma_pos = -1
            if comma_pos_cn >= 0 and comma_pos_en >= 0:
                comma_pos = min(comma_pos_cn, comma_pos_en)
            elif comma_pos_cn >= 0:
                comma_pos = comma_pos_cn
            elif comma_pos_en >= 0:
                comma_pos = comma_pos_en

            if comma_pos >= 0:
                before_comma = original[:comma_pos].strip()
                # 如果逗号前的内容是有意义的字段名（包含中文且长度合理）
                if (
                    before_comma
                    and any('\u4e00' <= char <= '\u9fff' for char in before_comma)
                    and len(before_comma) <= 20
                ):
                    return before_comma

        # 模式3: "字段名 其他说明" -> "字段名"
        if ' ' in original:
            space_pos = original.find(' ')
            if space_pos > 0:
                before_space = original[:space_pos].strip()
                # 如果空格前的内容是有意义的字段名
                if (
                    before_space
                    and any('\u4e00' <= char <= '\u9fff' for char in before_space)
                    and len(before_space) <= 20
                ):
                    return before_space

        return truncated

    def _map_field_name_to_chinese(self, field_name: str) -> Optional[str]:
        """
        基于字段名模式映射到中文

        Args:
            field_name: 英文字段名

        Returns:
            Optional[str]: 中文字段名
        """
        lower_field = field_name.lower()

        # 精确匹配
        exact_mapping = {
            'id': 'ID',
            'code': '编码',
            'name': '名称',
            'status': '状态',
            'qty': '数量',
            'amount': '金额',
            'price': '单价',
            'date': '日期',
            'time': '时间',
        }

        if lower_field in exact_mapping:
            return exact_mapping[lower_field]

        # 组合匹配
        if lower_field.endswith('_id') or lower_field.endswith('id'):
            prefix = lower_field.replace('_id', '').replace('id', '')
            if prefix in self.chinese_mapping_rules:
                return f"{self.chinese_mapping_rules[prefix]}ID"
            else:
                return f"{prefix}ID"

        if lower_field.endswith('_name') or lower_field.endswith('name'):
            prefix = lower_field.replace('_name', '').replace('name', '')
            if prefix in self.chinese_mapping_rules:
                return f"{self.chinese_mapping_rules[prefix]}名称"
            else:
                return f"{prefix}名称"

        if lower_field.endswith('_code') or lower_field.endswith('code'):
            prefix = lower_field.replace('_code', '').replace('code', '')
            if prefix in self.chinese_mapping_rules:
                return f"{self.chinese_mapping_rules[prefix]}编码"
            else:
                return f"{prefix}编码"

        return None

    def _clean_special_field_name(self, field_name: str) -> str:
        """
        清理和简化特殊字段名

        Args:
            field_name: 原字段名

        Returns:
            str: 清理后的字段名
        """
        if not field_name:
            return field_name

        # 处理包路径类字段名，如 "pu.purchaseorder.PurchaseOrders"
        if '.' in field_name:
            parts = field_name.split('.')
            # 取最后一部分作为字段名
            field_name = parts[-1]

        # 移除常见的前缀和后缀
        prefixes_to_remove = ['get', 'set', 'is', 'has', 'can', 'should']
        suffixes_to_remove = ['List', 'Info', 'Data', 'Detail', 'Item']

        lower_field = field_name.lower()

        # 移除前缀
        for prefix in prefixes_to_remove:
            if lower_field.startswith(
                    prefix.lower()) and len(field_name) > len(prefix):
                field_name = field_name[len(prefix):]
                break

        # 移除后缀
        for suffix in suffixes_to_remove:
            if lower_field.endswith(
                    suffix.lower()) and len(field_name) > len(suffix):
                field_name = field_name[: -len(suffix)]
                break

        # 移除特殊字符，保留字母数字和下划线

        cleaned = re.sub(r'[^\w]', '', field_name)

        return cleaned if cleaned else field_name

    def get_module_display_name(self, module_name: str) -> str:
        """
        获取模块的中文显示名称

        Args:
            module_name: 模块名称

        Returns:
            str: 中文显示名称
        """
        return self.module_display_names.get(module_name, module_name)

    def validate_md_files(self) -> Dict[str, bool]:
        """
        验证所有MD文档文件是否存在

        Returns:
            Dict[str, bool]: 模块名 -> 文件是否存在的映射
        """
        validation_result = {}

        for module_name, md_file in self.module_md_mapping.items():
            file_path = os.path.join(self.md_docs_path, md_file)
            exists = os.path.exists(file_path)
            validation_result[module_name] = exists

            if not exists:
                logger.warning(
                    f"MD文档文件不存在",
                    module=module_name,
                    file=file_path)

        return validation_result
