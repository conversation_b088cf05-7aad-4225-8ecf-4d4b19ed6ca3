现存量报表查询
发布时间:2025-02-12 15:05:45
调用该接口需要先在API调用中绑定用户身份 ，才能正常调用该接口。

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	动态域名，获取方式详见 获取租户所在数据中心域名
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/stockanalysis/list
请求方式	POST
ContentType	application/json
应用场景	开放API
API类别	
事务和幂等性	无
限流次数	4cd v3
python start_improved_sync_system.py
0次/分钟

用户身份	支持传递普通用户身份，详细说明见开放平台用户认证接入规范
多语	不支持
2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
pageSize	long	否	是	每页行数    示例: 10
pageIndex	long	否	是	当前页数    示例: 1
warehouse_name	long	是	否	仓库主键    示例: [1620139372761344]
product.productClass.name	long	是	否	物料分类主键    示例: [1613185527927040]
product.cName	long	是	否	商品主键    示例: [1613334295384320]
productsku.skuName	long	是	否	sku主键    示例: [1613347223376128]
batchno	string	否	否	批次号    示例: 批次号
acolytesUnit	string	否	否	辅计量单位    示例: 1
org	string	是	否	组织主键    示例: ["1620041647149568"]
store	long	是	否	门店主键    示例: [1620139372769888]
open_currentqty_begin	long	否	否	现存量下限    示例: 1
open_currentqty_end	long	否	否	现存量上限    示例: 1000
product_cName	string	是	否	物料名称    示例: [称重物料1,称重物料2]
product_cCode	string	是	否	物料编码    示例: [1620139372769888,1620139372769882]
warehouse_names	string	是	否	仓库名称    示例: [仓库1,仓库2]
warehouse_code	string	是	否	仓库编码    示例: [0105108,0105123]
productsku_skuName	string	是	否	sku名称    示例: [物料1,物料2]
product_productClass_names	string	是	否	物料分类名称    示例: [物料分类01,物料分类02]
stockStatusDoc_status_name	string	是	否	库存状态名称    示例: [合格,待检]
product_brand	string	否	否	物料品牌    示例: 品牌01
product_manufacturer	string	否	否	生产厂商    示例: 厂商01
product_cModel	string	否	否	型号    示例: Z0001
open_validityDistance_begin	long	否	否	效期天数下限    示例: 1
open_validityDistance_end	long	否	否	效期天数上限    示例: 1000
batchno_define1	string	否	否	批次自定义项1    示例: ck-001
batchno_define2	string	否	否	批次自定义项2
batchno_define3	string	否	否	批次自定义项3
batchno_define4	string	否	否	批次自定义项4
batchno_define5	string	否	否	批次自定义项5
batchno_define6	string	否	否	批次自定义项6
batchno_define7	string	否	否	批次自定义项7
batchno_define8	string	否	否	批次自定义项8
batchno_define9	string	否	否	批次自定义项9
batchno_define10	string	否	否	批次自定义项10
batchno_define11	string	否	否	批次自定义项11
batchno_define12	string	否	否	批次自定义项12
batchno_define13	string	否	否	批次自定义项13
batchno_define14	string	否	否	批次自定义项14
batchno_define15	string	否	否	批次自定义项15
batchno_define16	string	否	否	批次自定义项16
batchno_define17	string	否	否	批次自定义项17
batchno_define18	string	否	否	批次自定义项18
batchno_define19	string	否	否	批次自定义项19
batchno_define20	string	否	否	批次自定义项20
batchno_define21	string	否	否	批次自定义项21
batchno_define22	string	否	否	批次自定义项22
batchno_define23	string	否	否	批次自定义项23
batchno_define24	string	否	否	批次自定义项24
batchno_define25	string	否	否	批次自定义项25
batchno_define26	string	否	否	批次自定义项26
batchno_define27	string	否	否	批次自定义项27
batchno_define28	string	否	否	批次自定义项28
batchno_define29	string	否	否	批次自定义项29
batchno_define30	string	否	否	批次自定义项30
3. 请求示例
Url: /yonbip/scm/stockanalysis/list?access_token=访问令牌
Body: {
	"pageSize": 10,
	"pageIndex": 1,
	"warehouse_name": [
		1620139372761344
	],
	"product.productClass.name": [
		1613185527927040
	],
	"product.cName": [
		1613334295384320
	],
	"productsku.skuName": [
		1613347223376128
	],
	"batchno": "批次号",
	"acolytesUnit": "1",
	"org": [
		"1620041647149568"
	],
	"store": [
		1620139372769888
	],
	"open_currentqty_begin": 1,
	"open_currentqty_end": 1000,
	"product_cName": [
		"[称重物料1,称重物料2]"
	],
	"product_cCode": [
		1620139372769888,
		1620139372769882
	],
	"warehouse_names": [
		"[仓库1,仓库2]"
	],
	"warehouse_code": [
		105108,
		105123
	],
	"productsku_skuName": [
		"[物料1,物料2]"
	],
	"product_productClass_names": [
		"[物料分类01,物料分类02]"
	],
	"stockStatusDoc_status_name": [
		"[合格,待检]"
	],
	"product_brand": "品牌01",
	"product_manufacturer": "厂商01",
	"product_cModel": "Z0001",
	"open_validityDistance_begin": 1,
	"open_validityDistance_end": 1000,
	"batchno_define1": "ck-001",
	"batchno_define2": "",
	"batchno_define3": "",
	"batchno_define4": "",
	"batchno_define5": "",
	"batchno_define6": "",
	"batchno_define7": "",
	"batchno_define8": "",
	"batchno_define9": "",
	"batchno_define10": "",
	"batchno_define11": "",
	"batchno_define12": "",
	"batchno_define13": "",
	"batchno_define14": "",
	"batchno_define15": "",
	"batchno_define16": "",
	"batchno_define17": "",
	"batchno_define18": "",
	"batchno_define19": "",
	"batchno_define20": "",
	"batchno_define21": "",
	"batchno_define22": "",
	"batchno_define23": "",
	"batchno_define24": "",
	"batchno_define25": "",
	"batchno_define26": "",
	"batchno_define27": "",
	"batchno_define28": "",
	"batchno_define29": "",
	"batchno_define30": ""
}
4. 返回值参数
名称	类型	数组	描述
code	string	否	返回码，调用成功时返回200
message	string	否	调用失败时的错误信息
data	object	否	调用成功时的返回数据
pageIndex	int	否	当前页数
pageSize	int	否	每页行数
pageCount	int	否	总页数
beginPageIndex	int	否	开始页码（第一页）
endPageIndex	int	否	结束页码（有多少页）
recordCount	int	否	总记录数
pubts	string	否	时间戳,格式为:yyyy-MM-dd HH:mm:ss
recordList	object	是	数据
id	string	否	主键
org	string	否	组织
org_name	string	否	组织名称
areaClass	string	否	地区
areaClass_name	string	否	地区名称
store	string	否	门店
store_codebianma	string	否	门店编码
store_name	string	否	门店名称名称
store_type	string	否	门店类型, 1:直营店、2:直营专柜、3:加盟店、
warehouse	string	否	仓库
warehouse_name	string	否	仓库名称
productClass	string	否	物料分类
productClass_code	string	否	物料分类编码
productClass_name	string	否	物料分类名称名称
product	string	否	物料
product_cCode	string	否	物料编码
product_defaultAlbumId	string	否	物料首图片
product_cName	string	否	物料名称
productsku	string	否	物料SKU
product_modelDescription	string	否	规格型号
productsku_cCode	string	否	物料SKU编码
productsku_skuName	string	否	物料SKU名称
free1	string	否	物料规格1
free2	string	否	物料规格2
free3	string	否	物料规格3
free4	string	否	物料规格4
free5	string	否	物料规格5
free6	string	否	物料规格6
free7	string	否	物料规格7
free8	string	否	物料规格8
free9	string	否	物料规格9
free10	string	否	物料规格10
product_productProps	object	否	物料自定义项
define1	string	否	物料自定义项1-物料自定义项30
define30	string	否	物料自定义项30
product_productSkuProps	object	否	SKU自定义项
define1	string	否	SKU自定义项1-SKU自定义项60
define60	string	否	SKU自定义项1-SKU自定义项60
batchno	string	否	批号
batchno_define1	string	否	批次属性1-批次属性30
producedate	string	否	生产日期
invaliddate	string	否	有效期至
unitName	string	否	主计量
currentqty	int	否	现存量
stockmoney	int	否	库存金额
availableqty	int	否	可用量
costprice	int	否	成本价
innoticeqty	int	否	收货预计入库量
costmoney	int	否	成本金额
outnoticeqty	int	否	发货预计出库量
preretailqty	int	否	订单预计出库量
acolytesUnit	string	否	辅单位
acolytesUnit_name	string	否	辅计量单位名称
currentSubQty	int	否	现存件数
acolytesUnit_precision	string	否	辅计量单位精度
unit_precision	string	否	主计量单位精度
invExchRate	int	否	换算率
availableSubQty	int	否	可用件数
innoticeSubQty	int	否	入库通知件数
outnoticeSubQty	int	否	出库通知件数
preretailSubQty	int	否	预订零售件数
5. 正确返回示例
{
	"code": "",
	"message": "",
	"data": {
		"pageIndex": 0,
		"pageSize": 0,
		"pageCount": 0,
		"beginPageIndex": 0,
		"endPageIndex": 0,
		"recordCount": 0,
		"pubts": "",
		"recordList": [
			{
				"id": "",
				"org": "",
				"org_name": "",
				"areaClass": "",
				"areaClass_name": "",
				"store": "",
				"store_codebianma": "",
				"store_name": "",
				"store_type": "",
				"warehouse": "",
				"warehouse_name": "",
				"productClass": "",
				"productClass_code": "",
				"productClass_name": "",
				"product": "",
				"product_cCode": "",
				"product_defaultAlbumId": "",
				"product_cName": "",
				"productsku": "",
				"product_modelDescription": "",
				"productsku_cCode": "",
				"productsku_skuName": "",
				"free1": "",
				"free2": "",
				"free3": "",
				"free4": "",
				"free5": "",
				"free6": "",
				"free7": "",
				"free8": "",
				"free9": "",
				"free10": "",
				"product_productProps": {
					"define1": "",
					"define30": ""
				},
				"product_productSkuProps": {
					"define1": "",
					"define60": ""
				},
				"batchno": "",
				"batchno_define1": "",
				"producedate": "",
				"invaliddate": "",
				"unitName": "",
				"currentqty": 0,
				"stockmoney": 0,
				"availableqty": 0,
				"costprice": 0,
				"innoticeqty": 0,
				"costmoney": 0,
				"outnoticeqty": 0,
				"preretailqty": 0,
				"acolytesUnit": "",
				"acolytesUnit_name": "",
				"currentSubQty": 0,
				"acolytesUnit_precision": "",
				"unit_precision": "",
				"invExchRate": 0,
				"availableSubQty": 0,
				"innoticeSubQty": 0,
				"outnoticeSubQty": 0,
				"preretailSubQty": 0
			}
		]
	}
}
6. 错误返回码
错误码	错误信息	描述
7. 错误返回示例