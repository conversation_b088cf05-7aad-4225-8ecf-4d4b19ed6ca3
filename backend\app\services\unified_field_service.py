import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path

import structlog

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一字段服务 - 基于JSON解析器的新架构
取消field_configs依赖，直接基于用户配置管理字段
"""


logger = structlog.get_logger()

# 导入JSON解析器
try:
except ImportError:
    # 如果在当前目录运行，使用相对导入

    sys.path.append(str(Path(__file__).parent.parent.parent.parent))


class UnifiedFieldService:
    """统一字段服务"""

    def __init___(self):
    """TODO: Add function description."""
    self.base_dir = Path(__file__).parent.parent.parent.parent
    self.baseline_dir = self.base_dir / "config" / "baselines"
    self.user_config_dir = self.base_dir / "config" / "data" / "user_field_config"

    # 初始化JSON解析器
    self.json_parser = RobustJSONParser()

    # 字段映射缓存
    self.field_mappings: Dict[str, Dict[str, FieldInfo]] = {}
    self._initialized = False

    # 确保目录存在
    self.baseline_dir.mkdir(parents=True, exist_ok=True)
    self.user_config_dir.mkdir(parents=True, exist_ok=True)

    self.logger.info("统一字段服务初始化完成")

    async def initialize(self):
        """初始化服务，加载JSON映射"""
        if self._initialized:
            return

        try:
            logger.info("开始初始化JSON字段映射")

            # 解析所有JSON文件
            self.field_mappings = self.json_parser.parse_all_modules()

            logger.info(
                "JSON字段映射初始化完成",
                modules_count=len(self.field_mappings),
                total_fields=sum(
                    len(fields) for fields in self.field_mappings.values()
                ),
            )

            # 生成基准配置
            await self._generate_baselines_from_json()

            self._initialized = True

        except Exception:
            logger.error("初始化失败", error=str(e))
            raise

    async def _generate_baselines_from_json(self):
        """从JSON映射生成基准配置"""
        try:
            for module_name, field_mappings in self.field_mappings.items():
                baseline = await self._create_baseline_from_mappings(
                    module_name, field_mappings
                )
                await self._save_baseline(module_name, baseline)

                logger.info(
                    "基准配置生成完成",
                    module=module_name,
                    fields_count=len(field_mappings),
                )

        except Exception:
            logger.error("生成基准配置失败", error=str(e))

    async def _create_baseline_from_mappings(
        self, module_name: str, field_mappings: Dict[str, FieldInfo]
    ) -> Dict:
        """从字段映射创建基准配置"""
        baseline = {
            "module_name": module_name,
            "display_name": self._get_module_display_name(module_name),
            "version": "2.0.0",
            "source": "json_parser",
            "total_fields": len(field_mappings),
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "fields": {},
        }

        for field_path, field_info in field_mappings.items():
            baseline["fields"][field_info.name] = {
                "api_field_name": field_info.name,
                "chinese_name": field_info.chinese_name,
                "data_type": self._convert_to_sql_server_type(field_info.param_type),
                "param_desc": field_info.param_desc,
                "path": field_info.path,
                "depth": field_info.depth,
                "is_array": field_info.is_array,
                "business_importance": self._determine_importance(field_info),
                "is_required": self._is_required_field(field_info),
                "etl_score": 0.0,
                "etl_recommended": False,
            }

        return baseline

    def _convert_to_sql_server_type(self, param_type: str) -> str:
        """转换为SQL Server类型"""
        if param_type.startswith(
                "NVARCHAR") or param_type.startswith("VARCHAR"):
            return param_type
        elif param_type in ["BIGINT", "DECIMAL(18,4)", "BIT", "DATE", "DATETIME"]:
            return param_type
        else:
            return "NVARCHAR(500)"  # 默认类型

    def _determine_importance(self, field_info: FieldInfo) -> str:
        """确定字段重要性"""
        field_name_lower = field_info.name.lower()
        chinese_name_lower = field_info.chinese_name.lower()

        # 关键字段
        critical_keywords = ["id", "code", "编号", "状态", "status"]
        if any(
            keyword in field_name_lower or keyword in chinese_name_lower
            for keyword in critical_keywords
        ):
            return "critical"

        # 重要字段
        important_keywords = [
            "date",
            "time",
            "日期",
            "时间",
            "amount",
            "金额",
            "quantity",
            "数量",
        ]
        if any(
            keyword in field_name_lower or keyword in chinese_name_lower
            for keyword in important_keywords
        ):
            return "high"

        # 深度较浅的字段通常更重要
        if field_info.depth <= 2:
            return "medium"

        return "low"

    def _is_required_field(self, field_info: FieldInfo) -> bool:
        """判断是否为必需字段"""
        importance = self._determine_importance(field_info)
        return importance in ["critical", "high"]

    def _get_module_display_name(self, module_name: str) -> str:
        """获取模块显示名称"""
        display_names = {
            "sales_order": "销售订单",
            "purchase_order": "采购订单",
            "production_order": "生产订单",
            "subcontract_order": "委外订单",
            "material_master": "物料档案",
            "purchase_receipt": "采购入库",
            "sales_out": "销售出库",
            "product_receipt": "产品入库",
            "materialout": "材料出库",
            "subcontract_receipt": "委外入库",
            "subcontract_requisition": "委外申请",
            "applyorder": "请购单",
            "inventory_report": "现存量报表",
            "requirements_planning": "需求计划",
            "business_log": "业务日志",
        }
        return display_names.get(module_name, module_name)

    async def get_user_field_config(
            self,
            module_name: str,
            user_id: str) -> Dict:
        """获取用户字段配置"""
        try:
            # 确保已初始化
            await self.initialize()

            # 获取基准配置
            baseline = await self._get_baseline(module_name)
            if not baseline:
                logger.warning("基准配置不存在", module_name=module_name)
                return {
                    "fields": {},
                    "module_name": module_name,
                    "total_fields": 0,
                    "selected_fields": 0,
                }

            # 获取用户配置
            user_config = await self._get_user_config(module_name, user_id)

            # 合并配置
            merged_config = self._merge_configs(baseline, user_config)

            logger.info(
                "获取用户字段配置成功",
                module_name=module_name,
                user_id=user_id,
                total_fields=merged_config["total_fields"],
                selected_fields=merged_config["selected_fields"],
            )

            return merged_config

        except Exception:
            logger.error(
                "获取用户字段配置失败",
                module_name=module_name,
                user_id=user_id,
                error=str(e),
            )
            return {
                "fields": {},
                "module_name": module_name,
                "total_fields": 0,
                "selected_fields": 0,
            }

    async def update_field_selection(
            self,
            module_name: str,
            user_id: str,
            field_name: str,
            is_selected: bool) -> bool:
        """更新字段选择状态"""
        try:
            user_config = await self._get_user_config(module_name, user_id)

            if "fields" not in user_config:
                user_config["fields"] = {}

            if field_name not in user_config["fields"]:
                user_config["fields"][field_name] = {
                    "chinese_name": "",
                    "user_modified": False,
                    "is_selected": False,
                    "locked": False,
                }

            # 更新选择状态
            user_config["fields"][field_name]["is_selected"] = is_selected
            user_config["fields"][field_name]["user_modified"] = True

            # 更新统计
            selected_count = sum(
                1 for f in user_config["fields"].values() if f.get(
                    "is_selected", False))
            user_config["selected_fields"] = selected_count
            user_config["last_updated"] = datetime.now().isoformat()

            success = await self._save_user_config(module_name, user_id, user_config)

            if success:
                logger.info(
                    "更新字段选择成功",
                    module_name=module_name,
                    user_id=user_id,
                    field_name=field_name,
                    is_selected=is_selected,
                )

            return success

        except Exception:
            logger.error(
                "更新字段选择失败",
                module_name=module_name,
                user_id=user_id,
                field_name=field_name,
                error=str(e),
            )
            return False

    async def update_chinese_name(
            self,
            module_name: str,
            user_id: str,
            field_name: str,
            chinese_name: str) -> bool:
        """更新字段中文名称"""
        try:
            user_config = await self._get_user_config(module_name, user_id)

            if "fields" not in user_config:
                user_config["fields"] = {}

            if field_name not in user_config["fields"]:
                user_config["fields"][field_name] = {
                    "chinese_name": "",
                    "user_modified": False,
                    "is_selected": False,
                    "locked": False,
                }

            # 更新中文名称
            user_config["fields"][field_name]["chinese_name"] = chinese_name
            user_config["fields"][field_name]["user_modified"] = True
            user_config["last_updated"] = datetime.now().isoformat()

            success = await self._save_user_config(module_name, user_id, user_config)

            if success:
                logger.info(
                    "更新中文名称成功",
                    module_name=module_name,
                    user_id=user_id,
                    field_name=field_name,
                    chinese_name=chinese_name,
                )

            return success

        except Exception:
            logger.error(
                "更新中文名称失败",
                module_name=module_name,
                user_id=user_id,
                field_name=field_name,
                error=str(e),
            )
            return False

    def get_field_info_from_json(
        self, module_name: str, field_name: str
    ) -> Optional[FieldInfo]:
        """从JSON映射获取字段信息"""
        return self.json_parser.get_field_mapping(module_name, field_name)

    async def get_selected_fields(
            self,
            module_name: str,
            user_id: str) -> List[str]:
        """获取用户选中的字段列表"""
        try:
            user_config = await self._get_user_config(module_name, user_id)

            selected_fields = [
                field_name for field_name,
                field_config in user_config.get(
                    "fields",
                    {}).items() if field_config.get(
                    "is_selected",
                    False)]

            logger.info(
                "获取选中字段成功",
                module_name=module_name,
                user_id=user_id,
                selected_count=len(selected_fields),
            )

            return selected_fields

        except Exception:
            logger.error(
                "获取选中字段失败",
                module_name=module_name,
                user_id=user_id,
                error=str(e),
            )
            return []

    async def _get_baseline(self, module_name: str) -> Optional[Dict]:
        """获取基准配置"""
        baseline_path = self.baseline_dir / f"{module_name}_baseline.json"

        if not baseline_path.exists():
            return None

        try:
            with open(baseline_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            logger.error("读取基准配置失败", module_name=module_name, error=str(e))
            return None

    async def _save_baseline(self, module_name: str, baseline: Dict) -> bool:
        """保存基准配置"""
        try:
            baseline_path = self.baseline_dir / f"{module_name}_baseline.json"

            with open(baseline_path, 'w', encoding='utf-8') as f:
                json.dump(baseline, f, ensure_ascii=False, indent=2)

            return True
        except Exception:
            logger.error("保存基准配置失败", module_name=module_name, error=str(e))
            return False

    async def _get_user_config(self, module_name: str, user_id: str) -> Dict:
        """获取用户配置"""
        user_config_path = self.user_config_dir / \
            user_id / f"{module_name}.json"

        if not user_config_path.exists():
            return self._create_empty_user_config(module_name, user_id)

        try:
            with open(user_config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            logger.error(
                "读取用户配置失败",
                module_name=module_name,
                user_id=user_id,
                error=str(e),
            )
            return self._create_empty_user_config(module_name, user_id)

    async def _save_user_config(
        self, module_name: str, user_id: str, user_config: Dict
    ) -> bool:
        """保存用户配置"""
        try:
            user_dir = self.user_config_dir / user_id
            user_dir.mkdir(parents=True, exist_ok=True)

            user_config_path = user_dir / f"{module_name}.json"

            with open(user_config_path, 'w', encoding='utf-8') as f:
                json.dump(user_config, f, ensure_ascii=False, indent=2)

            return True
        except Exception:
            logger.error(
                "保存用户配置失败",
                module_name=module_name,
                user_id=user_id,
                error=str(e),
            )
            return False

    def _create_empty_user_config(
            self,
            module_name: str,
            user_id: str) -> Dict:
        """创建空的用户配置"""
        return {
            "user_id": user_id,
            "module_name": module_name,
            "version": "2.0.0",
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "fields": {},
            "selected_fields": 0,
            "user_preferences": {
                "auto_select_important": True,
                "notify_new_fields": True,
            },
        }

    def _merge_configs(self, baseline: Dict, user_config: Dict) -> Dict:
        """合并基准配置和用户配置"""
        merged = {
            "module_name": baseline["module_name"],
            "display_name": baseline.get(
                "display_name",
                baseline["module_name"]),
            "version": baseline.get(
                "version",
                "2.0.0"),
            "total_fields": baseline["total_fields"],
            "selected_fields": 0,
            "fields": {},
        }

        for field_name, field_info in baseline.get("fields", {}).items():
            user_field = user_config.get("fields", {}).get(field_name, {})

            # 优先使用用户配置的中文名
            chinese_name = user_field.get("chinese_name")
            if not chinese_name:
                chinese_name = field_info.get("chinese_name", field_name)

            merged["fields"][field_name] = {
                "name": field_name,
                "api_field_name": field_info["api_field_name"],
                "chinese_name": chinese_name,
                "data_type": field_info["data_type"],
                "param_desc": field_info.get(
                    "param_desc",
                    ""),
                "path": field_info.get(
                    "path",
                    field_name),
                "depth": field_info.get(
                    "depth",
                    1),
                "business_importance": field_info.get(
                    "business_importance",
                    "medium"),
                "is_selected": user_field.get(
                    "is_selected",
                    field_info.get(
                        "is_required",
                        False)),
                "user_modified": user_field.get(
                    "user_modified",
                    False),
                "locked": user_field.get(
                    "locked",
                    False),
            }

            if merged["fields"][field_name]["is_selected"]:
                merged["selected_fields"] += 1

        return merged


# 全局实例
unified_field_service = UnifiedFieldService()


# 使用示例
async def main():
    """主函数示例"""
    service = unified_field_service

    # 初始化服务
    await service.initialize()

    # 获取用户配置
    user_config = await service.get_user_field_config("sales_order", "Alice")
    logger.info(
        f"销售订单模块 - 总字段数: {user_config['total_fields']}, 已选择: {user_config['selected_fields']}"
    )

    # 更新字段选择
    success = await service.update_field_selection("sales_order", "Alice", "code", True)
    logger.info(f"更新字段选择: {'成功' if success else '失败'}")

    # 获取选中的字段
    selected_fields = await service.get_selected_fields("sales_order", "Alice")
    logger.info(f"选中的字段: {selected_fields}")


if __name__ == "__main__":
    asyncio.run(main())
