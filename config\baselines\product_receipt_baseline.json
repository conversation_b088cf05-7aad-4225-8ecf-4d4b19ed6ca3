{"module_name": "product_receipt", "display_name": "产品入库", "version": "2.0.0", "source": "json_parser", "total_fields": 90, "created_at": "2025-07-28T20:12:24.833977", "last_updated": "2025-07-28T20:12:24.833977", "fields": {"code": {"api_field_name": "code", "chinese_name": "单据编号", "data_type": "NVARCHAR(500)", "param_desc": "单据编号", "path": "data.recordList.code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "message": {"api_field_name": "message", "chinese_name": "调用失败时的错误信息", "data_type": "NVARCHAR(500)", "param_desc": "调用失败时的错误信息", "path": "message", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "调用成功时的返回数据", "data_type": "NVARCHAR(MAX)", "param_desc": "调用成功时的返回数据", "path": "data", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "sumRecordList": {"api_field_name": "sumRecordList", "chinese_name": "sum合计信息", "data_type": "NVARCHAR(MAX)", "param_desc": "sum合计信息", "path": "data.sumRecordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalQuantity": {"api_field_name": "totalQuantity", "chinese_name": "整单数量(废弃)", "data_type": "BIGINT", "param_desc": "整单数量(废弃)", "path": "data.recordList.totalQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "qty": {"api_field_name": "qty", "chinese_name": "数量", "data_type": "BIGINT", "param_desc": "数量", "path": "data.recordList.qty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalPieces": {"api_field_name": "totalPieces", "chinese_name": "整单件数(废弃)", "data_type": "NVARCHAR(500)", "param_desc": "整单件数(废弃)", "path": "data.recordList.totalPieces", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "subQty": {"api_field_name": "subQty", "chinese_name": "件数", "data_type": "DECIMAL(18,4)", "param_desc": "件数", "path": "data.recordList.subQty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageIndex": {"api_field_name": "pageIndex", "chinese_name": "页号", "data_type": "BIGINT", "param_desc": "页号", "path": "pageIndex", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageSize": {"api_field_name": "pageSize", "chinese_name": "每页行数", "data_type": "BIGINT", "param_desc": "每页行数", "path": "pageSize", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageCount": {"api_field_name": "pageCount", "chinese_name": "总页数", "data_type": "BIGINT", "param_desc": "总页数", "path": "data.pageCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "beginPageIndex": {"api_field_name": "beginPageIndex", "chinese_name": "开始页码", "data_type": "BIGINT", "param_desc": "开始页码", "path": "data.beginPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "endPageIndex": {"api_field_name": "endPageIndex", "chinese_name": "结束页码", "data_type": "BIGINT", "param_desc": "结束页码", "path": "data.endPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordCount": {"api_field_name": "recordCount", "chinese_name": "总记录数", "data_type": "BIGINT", "param_desc": "总记录数", "path": "data.recordCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pubts": {"api_field_name": "pubts", "chinese_name": "时间戳", "data_type": "NVARCHAR(500)", "param_desc": "时间戳", "path": "data.recordList.pubts", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "recordList": {"api_field_name": "recordList", "chinese_name": "返回结果对象", "data_type": "NVARCHAR(MAX)", "param_desc": "返回结果对象", "path": "data.recordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "factoryFiOrg": {"api_field_name": "factoryFiOrg", "chinese_name": "完工组织id", "data_type": "NVARCHAR(500)", "param_desc": "完工组织id", "path": "data.recordList.factoryFiOrg", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "storeProRecords_product": {"api_field_name": "storeProRecords_product", "chinese_name": "物料id", "data_type": "NVARCHAR(500)", "param_desc": "物料id", "path": "data.recordList.storeProRecords_product", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currency": {"api_field_name": "currency", "chinese_name": "币种id", "data_type": "NVARCHAR(500)", "param_desc": "币种id", "path": "data.recordList.currency", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "storeProRecords_unit": {"api_field_name": "storeProRecords_unit", "chinese_name": "主计量", "data_type": "NVARCHAR(500)", "param_desc": "主计量", "path": "data.recordList.storeProRecords_unit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "storeProRecords_productsku": {"api_field_name": "storeProRecords_productsku", "chinese_name": "物料sku", "data_type": "NVARCHAR(500)", "param_desc": "物料sku", "path": "data.recordList.storeProRecords_productsku", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "storeProRecords_stockUnitId": {"api_field_name": "storeProRecords_stockUnitId", "chinese_name": "库存单位id", "data_type": "NVARCHAR(500)", "param_desc": "库存单位id", "path": "data.recordList.storeProRecords_stockUnitId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "vouchdate": {"api_field_name": "vouchdate", "chinese_name": "单据日期", "data_type": "NVARCHAR(500)", "param_desc": "单据日期", "path": "data.recordList.vouchdate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "department_name": {"api_field_name": "department_name", "chinese_name": "部门名称", "data_type": "NVARCHAR(500)", "param_desc": "部门名称", "path": "data.recordList.department_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "accountOrg": {"api_field_name": "accountOrg", "chinese_name": "会计主体id", "data_type": "NVARCHAR(500)", "param_desc": "会计主体id", "path": "data.recordList.accountOrg", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "org_name": {"api_field_name": "org_name", "chinese_name": "库存组织名称", "data_type": "NVARCHAR(500)", "param_desc": "库存组织名称", "path": "data.recordList.org_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockMgr_name": {"api_field_name": "stockMgr_name", "chinese_name": "库管人名字列表", "data_type": "NVARCHAR(500)", "param_desc": "库管人名字列表", "path": "stockMgr_name", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "department": {"api_field_name": "department", "chinese_name": "部门id", "data_type": "NVARCHAR(500)", "param_desc": "部门id", "path": "department", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "org": {"api_field_name": "org", "chinese_name": "组织id", "data_type": "NVARCHAR(500)", "param_desc": "组织id", "path": "org", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockMgr": {"api_field_name": "stockMgr", "chinese_name": "库管员IDid", "data_type": "NVARCHAR(500)", "param_desc": "库管员IDid", "path": "data.recordList.stockMgr", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "store": {"api_field_name": "store", "chinese_name": "门店id", "data_type": "NVARCHAR(500)", "param_desc": "门店id", "path": "data.recordList.store", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "store_name": {"api_field_name": "store_name", "chinese_name": "门店名称", "data_type": "NVARCHAR(500)", "param_desc": "门店名称", "path": "data.recordList.store_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehouse": {"api_field_name": "warehouse", "chinese_name": "仓库id", "data_type": "NVARCHAR(500)", "param_desc": "仓库id", "path": "data.recordList.warehouse", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "warehouse_name": {"api_field_name": "warehouse_name", "chinese_name": "仓库名字", "data_type": "NVARCHAR(500)", "param_desc": "仓库名字", "path": "warehouse_name", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bustype": {"api_field_name": "bustype", "chinese_name": "业务类型id", "data_type": "NVARCHAR(500)", "param_desc": "业务类型id", "path": "data.recordList.bustype", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "accountOrg_name": {"api_field_name": "accountOrg_name", "chinese_name": "会计主体名称", "data_type": "NVARCHAR(500)", "param_desc": "会计主体名称", "path": "data.recordList.accountOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bustype_name": {"api_field_name": "bustype_name", "chinese_name": "交易类型", "data_type": "NVARCHAR(500)", "param_desc": "交易类型", "path": "bustype_name", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "status": {"api_field_name": "status", "chinese_name": "单据状态, 0:未提交、1:已提交、", "data_type": "NVARCHAR(500)", "param_desc": "单据状态, 0:未提交、1:已提交、", "path": "data.recordList.status", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operator": {"api_field_name": "operator", "chinese_name": "操作员id", "data_type": "NVARCHAR(500)", "param_desc": "操作员id", "path": "operator", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalMaterial": {"api_field_name": "totalMaterial", "chinese_name": "已材料出, true:是、false:否、(废弃)", "data_type": "NVARCHAR(500)", "param_desc": "已材料出, true:是、false:否、(废弃)", "path": "data.recordList.totalMaterial", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "creator": {"api_field_name": "creator", "chinese_name": "创建人", "data_type": "NVARCHAR(500)", "param_desc": "创建人", "path": "data.recordList.creator", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "createTime": {"api_field_name": "createTime", "chinese_name": "创建时间", "data_type": "NVARCHAR(500)", "param_desc": "创建时间", "path": "data.recordList.createTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "modifier": {"api_field_name": "modifier", "chinese_name": "最后修改人", "data_type": "NVARCHAR(500)", "param_desc": "最后修改人", "path": "data.recordList.modifier", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "modifyTime": {"api_field_name": "modifyTime", "chinese_name": "最后修改时间", "data_type": "NVARCHAR(500)", "param_desc": "最后修改时间", "path": "data.recordList.modifyTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auditor": {"api_field_name": "auditor", "chinese_name": "提交人", "data_type": "NVARCHAR(500)", "param_desc": "提交人", "path": "data.recordList.auditor", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "auditTime": {"api_field_name": "auditTime", "chinese_name": "提交时间", "data_type": "NVARCHAR(500)", "param_desc": "提交时间", "path": "data.recordList.auditTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "memo": {"api_field_name": "memo", "chinese_name": "备注", "data_type": "NVARCHAR(500)", "param_desc": "备注", "path": "data.recordList.memo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "auditorId": {"api_field_name": "auditorId", "chinese_name": "审批人", "data_type": "NVARCHAR(500)", "param_desc": "审批人", "path": "data.recordList.auditorId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "creatorId": {"api_field_name": "creatorId", "chinese_name": "创建人", "data_type": "NVARCHAR(500)", "param_desc": "创建人", "path": "data.recordList.creatorId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "id": {"api_field_name": "id", "chinese_name": "主表id", "data_type": "NVARCHAR(500)", "param_desc": "主表id", "path": "data.recordList.id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "modifierId": {"api_field_name": "modifierId", "chinese_name": "修改人", "data_type": "NVARCHAR(500)", "param_desc": "修改人", "path": "data.recordList.modifierId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "tplid": {"api_field_name": "tplid", "chinese_name": "模板id(废弃)", "data_type": "NVARCHAR(500)", "param_desc": "模板id(废弃)", "path": "data.recordList.tplid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "storeProRecords_id": {"api_field_name": "storeProRecords_id", "chinese_name": "子表id", "data_type": "NVARCHAR(500)", "param_desc": "子表id", "path": "data.recordList.storeProRecords_id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_cCode": {"api_field_name": "product_cCode", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "data.recordList.product_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_cName": {"api_field_name": "product_cName", "chinese_name": "物料ID", "data_type": "NVARCHAR(500)", "param_desc": "物料ID", "path": "product_cName", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productsku_cCode": {"api_field_name": "productsku_cCode", "chinese_name": "sku编码", "data_type": "NVARCHAR(500)", "param_desc": "sku编码", "path": "data.recordList.productsku_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productsku_cName": {"api_field_name": "productsku_cName", "chinese_name": "sku名称", "data_type": "NVARCHAR(500)", "param_desc": "sku名称", "path": "data.recordList.productsku_cName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_modelDescription": {"api_field_name": "product_modelDescription", "chinese_name": "规格型号", "data_type": "NVARCHAR(500)", "param_desc": "规格型号", "path": "data.recordList.product_modelDescription", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_unitName": {"api_field_name": "product_unitName", "chinese_name": "计量单位", "data_type": "NVARCHAR(500)", "param_desc": "计量单位", "path": "data.recordList.product_unitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockUnit_name": {"api_field_name": "stockUnit_name", "chinese_name": "库存单位", "data_type": "NVARCHAR(500)", "param_desc": "库存单位", "path": "data.recordList.stockUnit_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "project_name": {"api_field_name": "project_name", "chinese_name": "项目名称", "data_type": "NVARCHAR(500)", "param_desc": "项目名称", "path": "data.recordList.project_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natUnitPrice": {"api_field_name": "natUnitPrice", "chinese_name": "单价", "data_type": "BIGINT", "param_desc": "单价", "path": "data.recordList.natUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natMoney": {"api_field_name": "natMoney", "chinese_name": "金额", "data_type": "BIGINT", "param_desc": "金额", "path": "data.recordList.natMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_priceDigit": {"api_field_name": "natCurrency_priceDigit", "chinese_name": "币种单价精度", "data_type": "BIGINT", "param_desc": "币种单价精度", "path": "data.recordList.natCurrency_priceDigit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_moneyDigit": {"api_field_name": "natCurrency_moneyDigit", "chinese_name": "币种金额精度", "data_type": "BIGINT", "param_desc": "币种金额精度", "path": "data.recordList.natCurrency_moneyDigit", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unit_Precision": {"api_field_name": "unit_Precision", "chinese_name": "主计量精度", "data_type": "BIGINT", "param_desc": "主计量精度", "path": "data.recordList.unit_Precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockUnitId_Precision": {"api_field_name": "stockUnitId_Precision", "chinese_name": "库存单位精度", "data_type": "BIGINT", "param_desc": "库存单位精度", "path": "data.recordList.stockUnitId_Precision", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "out_sys_id": {"api_field_name": "out_sys_id", "chinese_name": "外部来源线索", "data_type": "NVARCHAR(500)", "param_desc": "外部来源线索", "path": "data.recordList.out_sys_id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "out_sys_code": {"api_field_name": "out_sys_code", "chinese_name": "外部来源编码", "data_type": "NVARCHAR(500)", "param_desc": "外部来源编码", "path": "data.recordList.out_sys_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "out_sys_version": {"api_field_name": "out_sys_version", "chinese_name": "外部来源版本", "data_type": "NVARCHAR(500)", "param_desc": "外部来源版本", "path": "data.recordList.out_sys_version", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_type": {"api_field_name": "out_sys_type", "chinese_name": "外部来源类型", "data_type": "NVARCHAR(500)", "param_desc": "外部来源类型", "path": "data.recordList.out_sys_type", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_rowno": {"api_field_name": "out_sys_rowno", "chinese_name": "外部来源行号", "data_type": "NVARCHAR(500)", "param_desc": "外部来源行号", "path": "data.recordList.out_sys_rowno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_lineid": {"api_field_name": "out_sys_lineid", "chinese_name": "外部来源行", "data_type": "NVARCHAR(500)", "param_desc": "外部来源行", "path": "data.recordList.out_sys_lineid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_hopeReceiveDate_begin": {"api_field_name": "open_hopeReceiveDate_begin", "chinese_name": "区间查询开始时间 : \"2020-03-02\"", "data_type": "NVARCHAR(500)", "param_desc": "区间查询开始时间 : \"2020-03-02\"", "path": "open_hopeReceiveDate_begin", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_hopeReceiveDate_end": {"api_field_name": "open_hopeReceiveDate_end", "chinese_name": "区间查询结束时间 :\"2020-03-02 23:59:59\"", "data_type": "NVARCHAR(500)", "param_desc": "区间查询结束时间 :\"2020-03-02 23:59:59\"", "path": "open_hopeReceiveDate_end", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "simpleVOs": {"api_field_name": "simpleVOs", "chinese_name": "扩展查询条件", "data_type": "NVARCHAR(MAX)", "param_desc": "扩展查询条件", "path": "simpleVOs", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "field": {"api_field_name": "field", "chinese_name": "属性名(条件),子表加前缀storeProRecords.", "data_type": "NVARCHAR(500)", "param_desc": "属性名(条件),子表加前缀storeProRecords.", "path": "simpleVOs.field", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "op": {"api_field_name": "op", "chinese_name": "条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)", "data_type": "NVARCHAR(500)", "param_desc": "条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)", "path": "simpleVOs.op", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value1": {"api_field_name": "value1", "chinese_name": "值1(条件),单条件时仅使用这个配置", "data_type": "NVARCHAR(500)", "param_desc": "值1(条件),单条件时仅使用这个配置", "path": "simpleVOs.value1", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value2": {"api_field_name": "value2", "chinese_name": "值2(条件),单条件时此配置无效", "data_type": "NVARCHAR(500)", "param_desc": "值2(条件),单条件时此配置无效", "path": "simpleVOs.value2", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}}}