<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>f91945f5461546fab9be6f4aae4163f3</id>
<name>现存量报表查询</name>
<apiClassifyId>ba6b5fb8023442e4817b6cb8e665b54a</apiClassifyId>
<apiClassifyName>现存量</apiClassifyName>
<apiClassifyCode/>
<parentApiClassifies/>
<functionId/>
<openMode/>
<description>调用该接口需要先在API调用中绑定用户身份 ，才能正常调用该接口。</description>
<auth>true</auth>
<bodyPassthrough>false</bodyPassthrough>
<healthExam>true</healthExam>
<healthStatus/>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/scm/stockanalysis/list</completeProxyUrl>
<connectUrl>/report/list</connectUrl>
<sort>30</sort>
<handler>openapi</handler>
<httpRequestType>POST</httpRequestType>
<openApi>true</openApi>
<preset>false</preset>
<productId>710a0be3edff4f9092e35f63fd3b9bae</productId>
<productCode>scm</productCode>
<proxyUrl>/yonbip/scm/stockanalysis/list</proxyUrl>
<requestParamsDemo>Url: /yonbip/scm/stockanalysis/list?access_token=访问令牌 Body: { "pageSize": 10, "pageIndex": 1, "warehouse_name": [ 1620139372761344 ], "product.productClass.name": [ 1613185527927040 ], "product.cName": [ 1613334295384320 ], "productsku.skuName": [ 1613347223376128 ], "batchno": "批次号", "acolytesUnit": "1", "org": [ "1620041647149568" ], "store": [ 1620139372769888 ], "open_currentqty_begin": 1, "open_currentqty_end": 1000 }</requestParamsDemo>
<requestProtocol>HTTP</requestProtocol>
<serviceHttpMethod>POST</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2020-11-17 20:51:10.000</gmtCreate>
<gmtUpdate>2025-02-12 13:39:59.000</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/stockanalysis/list</address>
<productName>采购供应</productName>
<productClassifyId>yonsuite</productClassifyId>
<productClassifyCode>yonbip</productClassifyCode>
<productClassifyName>用友 YonBIP</productClassifyName>
<paramDTOS>
<paramDTOS>
<id>2200211570023203077</id>
<name>pageSize</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202825</defParamId>
<array>false</array>
<paramDesc>每页行数</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>10</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203078</id>
<name>pageIndex</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202826</defParamId>
<array>false</array>
<paramDesc>当前页数</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203079</id>
<name>warehouse_name</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202827</defParamId>
<array>true</array>
<paramDesc>仓库主键</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[1620139372761344]</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203080</id>
<name>product.productClass.name</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202828</defParamId>
<array>true</array>
<paramDesc>物料分类主键</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[1613185527927040]</example>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203081</id>
<name>product.cName</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202829</defParamId>
<array>true</array>
<paramDesc>商品主键</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[1613334295384320]</example>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203082</id>
<name>productsku.skuName</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202830</defParamId>
<array>true</array>
<paramDesc>sku主键</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[1613347223376128]</example>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203083</id>
<name>batchno</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202831</defParamId>
<array>false</array>
<paramDesc>批次号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>批次号</example>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203084</id>
<name>acolytesUnit</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202832</defParamId>
<array>false</array>
<paramDesc>辅计量单位</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203085</id>
<name>org</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202833</defParamId>
<array>true</array>
<paramDesc>组织主键</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>["1620041647149568"]</example>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203086</id>
<name>store</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202834</defParamId>
<array>true</array>
<paramDesc>门店主键</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[1620139372769888]</example>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203087</id>
<name>open_currentqty_begin</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202835</defParamId>
<array>false</array>
<paramDesc>现存量下限</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203088</id>
<name>open_currentqty_end</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202836</defParamId>
<array>false</array>
<paramDesc>现存量上限</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1000</example>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203089</id>
<name>product_cName</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202837</defParamId>
<array>true</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[称重物料1,称重物料2]</example>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203090</id>
<name>product_cCode</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202838</defParamId>
<array>true</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[1620139372769888,1620139372769882]</example>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203091</id>
<name>warehouse_names</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202839</defParamId>
<array>true</array>
<paramDesc>仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[仓库1,仓库2]</example>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203092</id>
<name>warehouse_code</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202840</defParamId>
<array>true</array>
<paramDesc>仓库编码</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[0105108,0105123]</example>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203093</id>
<name>productsku_skuName</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202841</defParamId>
<array>true</array>
<paramDesc>sku名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[物料1,物料2]</example>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203094</id>
<name>product_productClass_names</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202842</defParamId>
<array>true</array>
<paramDesc>物料分类名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[物料分类01,物料分类02]</example>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203095</id>
<name>stockStatusDoc_status_name</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202843</defParamId>
<array>true</array>
<paramDesc>库存状态名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[合格,待检]</example>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203096</id>
<name>product_brand</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202844</defParamId>
<array>false</array>
<paramDesc>物料品牌</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>品牌01</example>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203097</id>
<name>product_manufacturer</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202845</defParamId>
<array>false</array>
<paramDesc>生产厂商</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>厂商01</example>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203098</id>
<name>product_cModel</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202846</defParamId>
<array>false</array>
<paramDesc>型号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>Z0001</example>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203099</id>
<name>open_validityDistance_begin</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202847</defParamId>
<array>false</array>
<paramDesc>效期天数下限</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203100</id>
<name>open_validityDistance_end</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202848</defParamId>
<array>false</array>
<paramDesc>效期天数上限</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1000</example>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203101</id>
<name>batchno_define1</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202849</defParamId>
<array>false</array>
<paramDesc>批次自定义项1</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>ck-001</example>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203102</id>
<name>batchno_define2</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202850</defParamId>
<array>false</array>
<paramDesc>批次自定义项2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203103</id>
<name>batchno_define3</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202851</defParamId>
<array>false</array>
<paramDesc>批次自定义项3</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203104</id>
<name>batchno_define4</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202852</defParamId>
<array>false</array>
<paramDesc>批次自定义项4</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203105</id>
<name>batchno_define5</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202853</defParamId>
<array>false</array>
<paramDesc>批次自定义项5</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203106</id>
<name>batchno_define6</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202854</defParamId>
<array>false</array>
<paramDesc>批次自定义项6</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203107</id>
<name>batchno_define7</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202855</defParamId>
<array>false</array>
<paramDesc>批次自定义项7</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203108</id>
<name>batchno_define8</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202856</defParamId>
<array>false</array>
<paramDesc>批次自定义项8</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203109</id>
<name>batchno_define9</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202857</defParamId>
<array>false</array>
<paramDesc>批次自定义项9</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203110</id>
<name>batchno_define10</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202858</defParamId>
<array>false</array>
<paramDesc>批次自定义项10</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203111</id>
<name>batchno_define11</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202859</defParamId>
<array>false</array>
<paramDesc>批次自定义项11</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203112</id>
<name>batchno_define12</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202860</defParamId>
<array>false</array>
<paramDesc>批次自定义项12</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203113</id>
<name>batchno_define13</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202861</defParamId>
<array>false</array>
<paramDesc>批次自定义项13</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203114</id>
<name>batchno_define14</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202862</defParamId>
<array>false</array>
<paramDesc>批次自定义项14</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203115</id>
<name>batchno_define15</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202863</defParamId>
<array>false</array>
<paramDesc>批次自定义项15</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203116</id>
<name>batchno_define16</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202864</defParamId>
<array>false</array>
<paramDesc>批次自定义项16</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203117</id>
<name>batchno_define17</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202865</defParamId>
<array>false</array>
<paramDesc>批次自定义项17</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203118</id>
<name>batchno_define18</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202866</defParamId>
<array>false</array>
<paramDesc>批次自定义项18</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203119</id>
<name>batchno_define19</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202867</defParamId>
<array>false</array>
<paramDesc>批次自定义项19</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203120</id>
<name>batchno_define20</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202868</defParamId>
<array>false</array>
<paramDesc>批次自定义项20</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>43</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203121</id>
<name>batchno_define21</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202869</defParamId>
<array>false</array>
<paramDesc>批次自定义项21</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>44</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203122</id>
<name>batchno_define22</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202870</defParamId>
<array>false</array>
<paramDesc>批次自定义项22</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>45</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203123</id>
<name>batchno_define23</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202871</defParamId>
<array>false</array>
<paramDesc>批次自定义项23</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>46</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203124</id>
<name>batchno_define24</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202872</defParamId>
<array>false</array>
<paramDesc>批次自定义项24</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>47</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203125</id>
<name>batchno_define25</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202873</defParamId>
<array>false</array>
<paramDesc>批次自定义项25</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>48</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203126</id>
<name>batchno_define26</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202874</defParamId>
<array>false</array>
<paramDesc>批次自定义项26</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>49</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203127</id>
<name>batchno_define27</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202875</defParamId>
<array>false</array>
<paramDesc>批次自定义项27</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>50</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203128</id>
<name>batchno_define28</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202876</defParamId>
<array>false</array>
<paramDesc>批次自定义项28</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>51</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203129</id>
<name>batchno_define29</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202877</defParamId>
<array>false</array>
<paramDesc>批次自定义项29</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>52</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200211570023203130</id>
<name>batchno_define30</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202878</defParamId>
<array>false</array>
<paramDesc>批次自定义项30</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>53</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203131</id>
<name>pageSize</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>每页行数</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageSize</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203132</id>
<name>pageIndex</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>当前页数</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageIndex</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203133</id>
<name>warehouse_name</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>仓库主键</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>warehouse_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203134</id>
<name>product.productClass.name</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料分类主键</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product.productClass.name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203135</id>
<name>product.cName</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>商品主键</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product.cName</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203136</id>
<name>productsku.skuName</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>sku主键</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>productsku.skuName</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203137</id>
<name>batchno</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203138</id>
<name>acolytesUnit</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>辅计量单位</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>acolytesUnit</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203139</id>
<name>org</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>组织主键</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>org</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203140</id>
<name>store</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>门店主键</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>store</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203141</id>
<name>open_currentqty_begin</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>现存量下限</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_currentqty_begin</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203142</id>
<name>open_currentqty_end</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>现存量上限</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_currentqty_end</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203143</id>
<name>product_cName</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product_cName</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203144</id>
<name>product_cCode</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product_cCode</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203145</id>
<name>warehouse_names</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>warehouse_names</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203146</id>
<name>warehouse_code</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>仓库编码</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>warehouse_code</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203147</id>
<name>productsku_skuName</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>sku名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>productsku_skuName</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203148</id>
<name>product_productClass_names</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料分类名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product_productClass_names</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203149</id>
<name>stockStatusDoc_status_name</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>库存状态名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>stockStatusDoc_status_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203150</id>
<name>product_brand</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料品牌</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product_brand</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203151</id>
<name>product_manufacturer</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>生产厂商</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product_manufacturer</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203152</id>
<name>product_cModel</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>型号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product_cModel</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203153</id>
<name>open_validityDistance_begin</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>效期天数下限</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_validityDistance_begin</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203154</id>
<name>open_validityDistance_end</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>效期天数上限</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_validityDistance_end</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203155</id>
<name>batchno_define1</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项1</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define1</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203156</id>
<name>batchno_define2</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define2</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203157</id>
<name>batchno_define3</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项3</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define3</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203158</id>
<name>batchno_define4</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项4</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define4</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203159</id>
<name>batchno_define5</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项5</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define5</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203160</id>
<name>batchno_define6</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项6</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define6</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203161</id>
<name>batchno_define7</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项7</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define7</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203162</id>
<name>batchno_define8</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项8</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define8</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203163</id>
<name>batchno_define9</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项9</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define9</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203164</id>
<name>batchno_define10</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项10</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define10</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203165</id>
<name>batchno_define11</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项11</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define11</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203166</id>
<name>batchno_define12</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项12</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define12</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203167</id>
<name>batchno_define13</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项13</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define13</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203168</id>
<name>batchno_define14</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项14</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define14</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203169</id>
<name>batchno_define15</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项15</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define15</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203170</id>
<name>batchno_define16</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项16</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define16</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203171</id>
<name>batchno_define17</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项17</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define17</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203172</id>
<name>batchno_define18</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项18</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define18</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203173</id>
<name>batchno_define19</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项19</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define19</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203174</id>
<name>batchno_define20</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项20</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define20</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203175</id>
<name>batchno_define21</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项21</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define21</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203176</id>
<name>batchno_define22</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项22</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define22</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203177</id>
<name>batchno_define23</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项23</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define23</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203178</id>
<name>batchno_define24</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项24</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define24</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203179</id>
<name>batchno_define25</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项25</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define25</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203180</id>
<name>batchno_define26</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项26</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define26</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203181</id>
<name>batchno_define27</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项27</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define27</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203182</id>
<name>batchno_define28</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项28</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define28</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203183</id>
<name>batchno_define29</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项29</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define29</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200211570023203184</id>
<name>batchno_define30</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次自定义项30</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>batchno_define30</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>2200211570023203255</id>
<name>code</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202933</defParamId>
<array>false</array>
<paramDesc>返回码，调用成功时返回200</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2200211570023203256</id>
<name>message</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<defParamId>2200211570023202934</defParamId>
<array>false</array>
<paramDesc>调用失败时的错误信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2200211570023203185</id>
<name>data</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId/>
<children>
<children>
<id>2200211570023203248</id>
<name>pageIndex</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203185</parentId>
<defParamId>2200211570023202936</defParamId>
<array>false</array>
<paramDesc>当前页数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203249</id>
<name>pageSize</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203185</parentId>
<defParamId>2200211570023202937</defParamId>
<array>false</array>
<paramDesc>每页行数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203250</id>
<name>pageCount</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203185</parentId>
<defParamId>2200211570023202938</defParamId>
<array>false</array>
<paramDesc>总页数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203251</id>
<name>beginPageIndex</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203185</parentId>
<defParamId>2200211570023202939</defParamId>
<array>false</array>
<paramDesc>开始页码（第一页）</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203252</id>
<name>endPageIndex</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203185</parentId>
<defParamId>2200211570023202940</defParamId>
<array>false</array>
<paramDesc>结束页码（有多少页）</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203253</id>
<name>recordCount</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203185</parentId>
<defParamId>2200211570023202941</defParamId>
<array>false</array>
<paramDesc>总记录数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203254</id>
<name>pubts</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203185</parentId>
<defParamId>2200211570023202942</defParamId>
<array>false</array>
<paramDesc>时间戳,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203186</id>
<name>recordList</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203185</parentId>
<children>
<children>
<id>2200211570023203193</id>
<name>id</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202944</defParamId>
<array>false</array>
<paramDesc>主键</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203194</id>
<name>org</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202945</defParamId>
<array>false</array>
<paramDesc>组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203195</id>
<name>org_name</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202946</defParamId>
<array>false</array>
<paramDesc>组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203196</id>
<name>areaClass</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202947</defParamId>
<array>false</array>
<paramDesc>地区</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203197</id>
<name>areaClass_name</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202948</defParamId>
<array>false</array>
<paramDesc>地区名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203198</id>
<name>store</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202949</defParamId>
<array>false</array>
<paramDesc>门店</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203199</id>
<name>store_codebianma</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202950</defParamId>
<array>false</array>
<paramDesc>门店编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203200</id>
<name>store_name</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202951</defParamId>
<array>false</array>
<paramDesc>门店名称名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203201</id>
<name>store_type</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202952</defParamId>
<array>false</array>
<paramDesc>门店类型, 1:直营店、2:直营专柜、3:加盟店、</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203202</id>
<name>warehouse</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202953</defParamId>
<array>false</array>
<paramDesc>仓库</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203203</id>
<name>warehouse_name</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202954</defParamId>
<array>false</array>
<paramDesc>仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203204</id>
<name>productClass</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202955</defParamId>
<array>false</array>
<paramDesc>物料分类</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203205</id>
<name>productClass_code</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202956</defParamId>
<array>false</array>
<paramDesc>物料分类编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203206</id>
<name>productClass_name</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202957</defParamId>
<array>false</array>
<paramDesc>物料分类名称名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203207</id>
<name>product</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202958</defParamId>
<array>false</array>
<paramDesc>物料</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203208</id>
<name>product_cCode</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202959</defParamId>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203209</id>
<name>product_defaultAlbumId</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202960</defParamId>
<array>false</array>
<paramDesc>物料首图片</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203210</id>
<name>product_cName</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202961</defParamId>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203211</id>
<name>productsku</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202962</defParamId>
<array>false</array>
<paramDesc>物料SKU</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203212</id>
<name>product_modelDescription</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202963</defParamId>
<array>false</array>
<paramDesc>规格型号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203213</id>
<name>productsku_cCode</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202964</defParamId>
<array>false</array>
<paramDesc>物料SKU编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203214</id>
<name>productsku_skuName</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202965</defParamId>
<array>false</array>
<paramDesc>物料SKU名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203215</id>
<name>free1</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202966</defParamId>
<array>false</array>
<paramDesc>物料规格1</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203216</id>
<name>free2</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202967</defParamId>
<array>false</array>
<paramDesc>物料规格2</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203217</id>
<name>free3</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202968</defParamId>
<array>false</array>
<paramDesc>物料规格3</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203218</id>
<name>free4</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202969</defParamId>
<array>false</array>
<paramDesc>物料规格4</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203219</id>
<name>free5</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202970</defParamId>
<array>false</array>
<paramDesc>物料规格5</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203220</id>
<name>free6</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202971</defParamId>
<array>false</array>
<paramDesc>物料规格6</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203221</id>
<name>free7</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202972</defParamId>
<array>false</array>
<paramDesc>物料规格7</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203222</id>
<name>free8</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202973</defParamId>
<array>false</array>
<paramDesc>物料规格8</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203223</id>
<name>free9</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202974</defParamId>
<array>false</array>
<paramDesc>物料规格9</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203224</id>
<name>free10</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202975</defParamId>
<array>false</array>
<paramDesc>物料规格10</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203187</id>
<name>product_productProps</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<children>
<children>
<id>2200211570023203188</id>
<name>define1</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203187</parentId>
<defParamId>2200211570023202977</defParamId>
<array>false</array>
<paramDesc>物料自定义项1-物料自定义项30</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203189</id>
<name>define30</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203187</parentId>
<defParamId>2200211570023202978</defParamId>
<array>false</array>
<paramDesc>物料自定义项30</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2200211570023202976</defParamId>
<array>false</array>
<paramDesc>物料自定义项</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible/>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203190</id>
<name>product_productSkuProps</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<children>
<children>
<id>2200211570023203191</id>
<name>define1</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203190</parentId>
<defParamId>2200211570023202980</defParamId>
<array>false</array>
<paramDesc>SKU自定义项1-SKU自定义项60</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203192</id>
<name>define60</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203190</parentId>
<defParamId>2200211570023202981</defParamId>
<array>false</array>
<paramDesc>SKU自定义项1-SKU自定义项60</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2200211570023202979</defParamId>
<array>false</array>
<paramDesc>SKU自定义项</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible/>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203225</id>
<name>batchno</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202982</defParamId>
<array>false</array>
<paramDesc>批号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203226</id>
<name>batchno_define1</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202983</defParamId>
<array>false</array>
<paramDesc>批次属性1-批次属性30</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203227</id>
<name>producedate</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202984</defParamId>
<array>false</array>
<paramDesc>生产日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203228</id>
<name>invaliddate</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202985</defParamId>
<array>false</array>
<paramDesc>有效期至</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203229</id>
<name>unitName</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202986</defParamId>
<array>false</array>
<paramDesc>主计量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203230</id>
<name>currentqty</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202987</defParamId>
<array>false</array>
<paramDesc>现存量</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203231</id>
<name>stockmoney</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202988</defParamId>
<array>false</array>
<paramDesc>库存金额</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203232</id>
<name>availableqty</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202989</defParamId>
<array>false</array>
<paramDesc>可用量</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203233</id>
<name>costprice</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202990</defParamId>
<array>false</array>
<paramDesc>成本价</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203234</id>
<name>innoticeqty</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202991</defParamId>
<array>false</array>
<paramDesc>收货预计入库量</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203235</id>
<name>costmoney</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202992</defParamId>
<array>false</array>
<paramDesc>成本金额</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203236</id>
<name>outnoticeqty</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202993</defParamId>
<array>false</array>
<paramDesc>发货预计出库量</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203237</id>
<name>preretailqty</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202994</defParamId>
<array>false</array>
<paramDesc>订单预计出库量</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203238</id>
<name>acolytesUnit</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202995</defParamId>
<array>false</array>
<paramDesc>辅单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203239</id>
<name>acolytesUnit_name</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202996</defParamId>
<array>false</array>
<paramDesc>辅计量单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"acolytesUnit.name","cItemName":"acolytesUnit_name","cCaption":"辅计量单位","cShowCaption":"辅计量单位","iMaxLength":255,"bHidden":false,"cRefType":"aa_productunit","cRefId":null,"cRefRetId":{"acolytesUnit":"id"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":false,"bIsNull":true,"bSelfDefine":null,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"stock.currentstock.CurrentStock","cControlType":"Refer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"true"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203240</id>
<name>currentSubQty</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202997</defParamId>
<array>false</array>
<paramDesc>现存件数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203241</id>
<name>acolytesUnit_precision</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202998</defParamId>
<array>false</array>
<paramDesc>辅计量单位精度</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203242</id>
<name>unit_precision</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023202999</defParamId>
<array>false</array>
<paramDesc>主计量单位精度</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203243</id>
<name>invExchRate</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023203000</defParamId>
<array>false</array>
<paramDesc>换算率</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203244</id>
<name>availableSubQty</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023203001</defParamId>
<array>false</array>
<paramDesc>可用件数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203245</id>
<name>innoticeSubQty</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023203002</defParamId>
<array>false</array>
<paramDesc>入库通知件数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203246</id>
<name>outnoticeSubQty</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023203003</defParamId>
<array>false</array>
<paramDesc>出库通知件数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200211570023203247</id>
<name>preretailSubQty</name>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<parentId>2200211570023203186</parentId>
<defParamId>2200211570023203004</defParamId>
<array>false</array>
<paramDesc>预订零售件数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2200211570023202943</defParamId>
<array>true</array>
<paramDesc>数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2200211570023202935</defParamId>
<array>false</array>
<paramDesc>调用成功时的返回数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 13:32:18.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:18.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2200211578613137419</id>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "pubts": "", "recordList": [ { "id": "", "org": "", "org_name": "", "areaClass": "", "areaClass_name": "", "store": "", "store_codebianma": "", "store_name": "", "store_type": "", "warehouse": "", "warehouse_name": "", "productClass": "", "productClass_code": "", "productClass_name": "", "product": "", "product_cCode": "", "product_defaultAlbumId": "", "product_cName": "", "productsku": "", "product_modelDescription": "", "productsku_cCode": "", "productsku_skuName": "", "free1": "", "free2": "", "free3": "", "free4": "", "free5": "", "free6": "", "free7": "", "free8": "", "free9": "", "free10": "", "product_productProps": { "define1": "", "define2": "", "define3": "", "define4": "", "define5": "", "define6": "", "define7": "", "define8": "", "define9": "", "define10": "", "define11": "", "define12": "", "define13": "", "define14": "", "define15": "", "define16": "", "define17": "", "define18": "", "define19": "", "define20": "", "define21": "", "define22": "", "define23": "", "define24": "", "define25": "", "define26": "", "define27": "", "define28": "", "define29": "", "define30": "" }, "product_productSkuProps": { "define1": "", "define2": "", "define3": "", "define4": "", "define5": "", "define6": "", "define7": "", "define8": "", "define9": "", "define10": "", "define11": "", "define12": "", "define13": "", "define14": "", "define15": "", "define16": "", "define17": "", "define18": "", "define19": "", "define20": "", "define21": "", "define22": "", "define23": "", "define24": "", "define25": "", "define26": "", "define27": "", "define28": "", "define29": "", "define30": "", "define31": "", "define32": "", "define33": "", "define34": "", "define35": "", "define36": "", "define37": "", "define38": "", "define39": "", "define40": "", "define41": "", "define42": "", "define43": "", "define44": "", "define45": "", "define46": "", "define47": "", "define48": "", "define49": "", "define50": "", "define51": "", "define52": "", "define53": "", "define54": "", "define55": "", "define56": "", "define57": "", "define58": "", "define59": "", "define60": "" }, "batchno": "", "batchno_define1": "", "batchno_define2": "", "batchno_define3": "", "batchno_define4": "", "batchno_define5": "", "batchno_define6": "", "batchno_define7": "", "batchno_define8": "", "batchno_define9": "", "batchno_define10": "", "batchno_define11": "", "batchno_define12": "", "batchno_define13": "", "batchno_define14": "", "batchno_define15": "", "batchno_define16": "", "batchno_define17": "", "batchno_define18": "", "batchno_define19": "", "batchno_define20": "", "batchno_define21": "", "batchno_define22": "", "batchno_define23": "", "batchno_define24": "", "batchno_define25": "", "batchno_define26": "", "batchno_define27": "", "batchno_define28": "", "batchno_define29": "", "batchno_define30": "", "producedate": "", "invaliddate": "", "unitName": "", "currentqty": 0, "stockmoney": 0, "availableqty": 0, "costprice": 0, "innoticeqty": 0, "costmoney": 0, "outnoticeqty": 0, "preretailqty": 0, "acolytesUnit": "", "acolytesUnit_name": "", "currentSubQty": 0, "acolytesUnit_precision": "", "unit_precision": "", "invExchRate": 0, "availableSubQty": 0, "innoticeSubQty": 0, "outnoticeSubQty": 0, "preretailSubQty": 0 } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-02-12 13:32:19.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:19.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2200211578613137420</id>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<content/>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-02-12 13:32:19.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:19.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2200211578613137419</id>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "pubts": "", "recordList": [ { "id": "", "org": "", "org_name": "", "areaClass": "", "areaClass_name": "", "store": "", "store_codebianma": "", "store_name": "", "store_type": "", "warehouse": "", "warehouse_name": "", "productClass": "", "productClass_code": "", "productClass_name": "", "product": "", "product_cCode": "", "product_defaultAlbumId": "", "product_cName": "", "productsku": "", "product_modelDescription": "", "productsku_cCode": "", "productsku_skuName": "", "free1": "", "free2": "", "free3": "", "free4": "", "free5": "", "free6": "", "free7": "", "free8": "", "free9": "", "free10": "", "product_productProps": { "define1": "", "define2": "", "define3": "", "define4": "", "define5": "", "define6": "", "define7": "", "define8": "", "define9": "", "define10": "", "define11": "", "define12": "", "define13": "", "define14": "", "define15": "", "define16": "", "define17": "", "define18": "", "define19": "", "define20": "", "define21": "", "define22": "", "define23": "", "define24": "", "define25": "", "define26": "", "define27": "", "define28": "", "define29": "", "define30": "" }, "product_productSkuProps": { "define1": "", "define2": "", "define3": "", "define4": "", "define5": "", "define6": "", "define7": "", "define8": "", "define9": "", "define10": "", "define11": "", "define12": "", "define13": "", "define14": "", "define15": "", "define16": "", "define17": "", "define18": "", "define19": "", "define20": "", "define21": "", "define22": "", "define23": "", "define24": "", "define25": "", "define26": "", "define27": "", "define28": "", "define29": "", "define30": "", "define31": "", "define32": "", "define33": "", "define34": "", "define35": "", "define36": "", "define37": "", "define38": "", "define39": "", "define40": "", "define41": "", "define42": "", "define43": "", "define44": "", "define45": "", "define46": "", "define47": "", "define48": "", "define49": "", "define50": "", "define51": "", "define52": "", "define53": "", "define54": "", "define55": "", "define56": "", "define57": "", "define58": "", "define59": "", "define60": "" }, "batchno": "", "batchno_define1": "", "batchno_define2": "", "batchno_define3": "", "batchno_define4": "", "batchno_define5": "", "batchno_define6": "", "batchno_define7": "", "batchno_define8": "", "batchno_define9": "", "batchno_define10": "", "batchno_define11": "", "batchno_define12": "", "batchno_define13": "", "batchno_define14": "", "batchno_define15": "", "batchno_define16": "", "batchno_define17": "", "batchno_define18": "", "batchno_define19": "", "batchno_define20": "", "batchno_define21": "", "batchno_define22": "", "batchno_define23": "", "batchno_define24": "", "batchno_define25": "", "batchno_define26": "", "batchno_define27": "", "batchno_define28": "", "batchno_define29": "", "batchno_define30": "", "producedate": "", "invaliddate": "", "unitName": "", "currentqty": 0, "stockmoney": 0, "availableqty": 0, "costprice": 0, "innoticeqty": 0, "costmoney": 0, "outnoticeqty": 0, "preretailqty": 0, "acolytesUnit": "", "acolytesUnit_name": "", "currentSubQty": 0, "acolytesUnit_precision": "", "unit_precision": "", "invExchRate": 0, "availableSubQty": 0, "innoticeSubQty": 0, "outnoticeSubQty": 0, "preretailSubQty": 0 } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-02-12 13:32:19.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:19.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2200211578613137420</id>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<content/>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-02-12 13:32:19.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:19.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>2200211578613137419</id>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "pubts": "", "recordList": [ { "id": "", "org": "", "org_name": "", "areaClass": "", "areaClass_name": "", "store": "", "store_codebianma": "", "store_name": "", "store_type": "", "warehouse": "", "warehouse_name": "", "productClass": "", "productClass_code": "", "productClass_name": "", "product": "", "product_cCode": "", "product_defaultAlbumId": "", "product_cName": "", "productsku": "", "product_modelDescription": "", "productsku_cCode": "", "productsku_skuName": "", "free1": "", "free2": "", "free3": "", "free4": "", "free5": "", "free6": "", "free7": "", "free8": "", "free9": "", "free10": "", "product_productProps": { "define1": "", "define2": "", "define3": "", "define4": "", "define5": "", "define6": "", "define7": "", "define8": "", "define9": "", "define10": "", "define11": "", "define12": "", "define13": "", "define14": "", "define15": "", "define16": "", "define17": "", "define18": "", "define19": "", "define20": "", "define21": "", "define22": "", "define23": "", "define24": "", "define25": "", "define26": "", "define27": "", "define28": "", "define29": "", "define30": "" }, "product_productSkuProps": { "define1": "", "define2": "", "define3": "", "define4": "", "define5": "", "define6": "", "define7": "", "define8": "", "define9": "", "define10": "", "define11": "", "define12": "", "define13": "", "define14": "", "define15": "", "define16": "", "define17": "", "define18": "", "define19": "", "define20": "", "define21": "", "define22": "", "define23": "", "define24": "", "define25": "", "define26": "", "define27": "", "define28": "", "define29": "", "define30": "", "define31": "", "define32": "", "define33": "", "define34": "", "define35": "", "define36": "", "define37": "", "define38": "", "define39": "", "define40": "", "define41": "", "define42": "", "define43": "", "define44": "", "define45": "", "define46": "", "define47": "", "define48": "", "define49": "", "define50": "", "define51": "", "define52": "", "define53": "", "define54": "", "define55": "", "define56": "", "define57": "", "define58": "", "define59": "", "define60": "" }, "batchno": "", "batchno_define1": "", "batchno_define2": "", "batchno_define3": "", "batchno_define4": "", "batchno_define5": "", "batchno_define6": "", "batchno_define7": "", "batchno_define8": "", "batchno_define9": "", "batchno_define10": "", "batchno_define11": "", "batchno_define12": "", "batchno_define13": "", "batchno_define14": "", "batchno_define15": "", "batchno_define16": "", "batchno_define17": "", "batchno_define18": "", "batchno_define19": "", "batchno_define20": "", "batchno_define21": "", "batchno_define22": "", "batchno_define23": "", "batchno_define24": "", "batchno_define25": "", "batchno_define26": "", "batchno_define27": "", "batchno_define28": "", "batchno_define29": "", "batchno_define30": "", "producedate": "", "invaliddate": "", "unitName": "", "currentqty": 0, "stockmoney": 0, "availableqty": 0, "costprice": 0, "innoticeqty": 0, "costmoney": 0, "outnoticeqty": 0, "preretailqty": 0, "acolytesUnit": "", "acolytesUnit_name": "", "currentSubQty": 0, "acolytesUnit_precision": "", "unit_precision": "", "invExchRate": 0, "availableSubQty": 0, "innoticeSubQty": 0, "outnoticeSubQty": 0, "preretailSubQty": 0 } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-02-12 13:32:19.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:19.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>2200211578613137420</id>
<apiId>f91945f5461546fab9be6f4aae4163f3</apiId>
<content/>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-02-12 13:32:19.000</gmtCreate>
<gmtUpdate>2025-02-12 13:32:19.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS/>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>友户通token认证业务扩展插件</name>
<configurable>false</configurable>
<description>YonsuitBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>f91945f5461546fab9be6f4aae4163f3</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin/>
<mapReturnPluginConfig/>
<billNo/>
<domain/>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser>96b6c7b9-2295-4fd8-96eb-924ea9af4075</createUser>
<createUserName/>
<approvalStatus>4</approvalStatus>
<publishTime>2025-02-12 15:05:45</publishTime>
<pathJoin>false</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout/>
<customUrl>/stockanalysis/list</customUrl>
<fixedUrl>/yonbip/scm</fixedUrl>
<apiCode>f91945f5461546fab9be6f4aae4163f3</apiCode>
<tokenCheckType>0</tokenCheckType>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL/>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>99ea7655-00a2-4bda-b23c-19ade37ea574</updateUserId>
<updateUserName>u8c_vip管理员</updateUserName>
<paramIsForce/>
<userIDPassthrough>true</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.yonbip-scm-stock</microServiceCode>
<applicationCode>ST</applicationCode>
<privacyCategory>0</privacyCategory>
<privacyLevel>4</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType/>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType/>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>false</arrayParam>
<fileSize/>
<cc>false</cc>
<paramTransferMode>2</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId>2200211570023202823</apiDefId>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>0</paramExtRequest>
<paramExtResponse>0</paramExtResponse>
<paramExtInExtendKey>0</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene/>
<apiType/>
<paramMark/>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>false</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>false</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>1</chargeStatus>
<beforeSpeed>40</beforeSpeed>
<afterSpeed>80</afterSpeed>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath/>
<pubHistory/>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode/>
<multiVersion>0</multiVersion>
<apiTag/>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>2108770660671029249</id>
<name>用友YonBIP</name>
<type>integrateSys</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>SCC</id>
<name>供应链云</name>
<type>1</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MM</id>
<name>采购供应</name>
<type>2</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>ST</id>
<name>库存管理</name>
<type>3</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>ustock.stock_stockanalysis</id>
<name>现存量表</name>
<type>4</type>
<sort>0</sort>
<enable>0</enable>
<children/>
<parentId/>
<productId/>
<code>ustock.stock_stockanalysis</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>ST</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MM</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>SCC</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>current_yonbip_default_sys</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<isOrigin>0</isOrigin>
<hasChildren>0</hasChildren>
<order>0</order>
</data>
</ResultVO>
