import re
from datetime import datetime

import structlog

from ..core.database import get_async_session
from .field_config_service import FieldConfigService

"""
YS-API V3.0 数据处理器
负责字段映射、数据清洗和类型转换
"""


logger = structlog.get_logger()


class DataProcessor:
    """数据处理器"""

    def __init___(self):
    """TODO: Add function description."""

    def process_data(
            self,
            module_name: str,
            api_data: List[Dict]) -> List[Dict]:
        """
        处理模块数据：批量字段映射和数据清洗

        Args:
            module_name: 模块名称
            api_data: API返回的原始数据列表

        Returns:
            List[Dict]: 处理后的数据列表
        """
        try:
            if not api_data or len(api_data) == 0:
                logger.warning("API数据为空", module_name=module_name)
                return []

            # 直接返回原始数据，不进行字段映射
            processed_records = api_data

            logger.info(
                "数据处理完成",
                module_name=module_name,
                input_count=len(api_data),
                output_count=len(processed_records),
            )

            return processed_records

        except Exception:
            logger.error("数据处理失败", module_name=module_name, error=str(e))
            return []

    def _process_single_record(
        self, record: Dict, field_config: Dict
    ) -> Optional[Dict]:
        """处理单条记录的简化版本"""
        try:
            processed = {}
            fields = field_config.get('fields', {})

            for api_field, config in fields.items():
                if config.get('is_selected', False) and api_field in record:
                    chinese_name = config['chinese_name']
                    processed[chinese_name] = record[api_field]

            return processed if processed else None

        except Exception:
            logger.warning("单条记录处理失败", error=str(e))
            return None

    async def process_record(
            self,
            api_record: Dict,
            field_config: Dict) -> Dict:
        """
        处理单条记录：字段映射和数据清洗

        Args:
            api_record: API返回的原始记录
            field_config: 字段配置信息

        Returns:
            Dict: 处理后的记录
        """
        try:
            processed_record = {}
            fields = field_config.get('fields', {})

            # 遍历所有选中的字段
            for api_field, field_conf in fields.items():
                if not field_conf.get('is_selected', False):
                    continue

                chinese_name = field_conf['chinese_name']
                data_type = field_conf['data_type']

                # 获取原始值
                raw_value = self._extract_field_value(api_record, api_field)

                # 数据清洗和类型转换
                cleaned_value = self._clean_and_convert_value(
                    raw_value, data_type, api_field
                )

                # 存储到中文字段名
                processed_record[chinese_name] = cleaned_value

            logger.debug(
                "记录处理完成",
                input_fields=len(api_record),
                output_fields=len(processed_record),
            )

            return processed_record

        except Exception:
            logger.error(
                "记录处理失败", api_record_sample=str(api_record)[:200], error=str(e)
            )
            raise

    def _extract_field_value(self, record: Dict, field_path: str) -> Any:
        """
        从记录中提取字段值，支持嵌套字段

        Args:
            record: 原始记录
            field_path: 字段路径（支持点号分隔的嵌套路径）

        Returns:
            Any: 字段值
        """
        try:
            # 支持嵌套字段访问，如 "vendor.name"
            if '.' in field_path:
                parts = field_path.split('.')
                value = record
                for part in parts:
                    if isinstance(value, dict) and part in value:
                        value = value[part]
                    else:
                        return None
                return value
            else:
                return record.get(field_path)

        except Exception:
            logger.warning("字段值提取失败", field_path=field_path, error=str(e))
            return None

    def _clean_and_convert_value(
        self, value: Any, data_type: str, field_name: str
    ) -> Any:
        """
        智能数据清洗和类型转换

        Args:
            value: 原始值
            data_type: 目标数据类型
            field_name: 字段名（用于调试）

        Returns:
            Any: 清洗后的值
        """
        if value is None or value == '':
            return None

        try:
            # 字符串类型
            if data_type.startswith('NVARCHAR') or data_type == 'NTEXT':
                str_value = str(value).strip()

                # 提取最大长度限制
                if '(' in data_type and ')' in data_type:
                    length_part = data_type.split('(')[1].split(')')[0]
                    if length_part.isdigit():
                        max_length = int(length_part)
                        if len(str_value) > max_length:
                            str_value = str_value[:max_length]
                            logger.warning(
                                "字符串被截断",
                                field_name=field_name,
                                original_length=len(str(value)),
                                max_length=max_length,
                            )

                return str_value

            # 整数类型
            elif data_type in ['INT', 'BIGINT', 'SMALLINT', 'TINYINT']:
                if isinstance(value, str):
                    # 移除非数字字符，但保留负号
                    numeric_str = re.sub(r'[^\d-]', '', value)
                    return int(
                        numeric_str) if numeric_str and numeric_str != '-' else 0
                elif isinstance(value, (int, float)):
                    return int(value)
                else:
                    return 0

            # 小数类型
            elif data_type.startswith('DECIMAL') or data_type in [
                'FLOAT',
                'REAL',
                'MONEY',
            ]:
                if isinstance(value, str):
                    # 移除非数字字符，但保留小数点和负号
                    numeric_str = re.sub(r'[^\d.-]', '', value)
                    if numeric_str and numeric_str not in ['-', '.', '-.']:
                        return float(numeric_str)
                    else:
                        return 0.0
                elif isinstance(value, (int, float)):
                    return float(value)
                else:
                    return 0.0

            # 布尔类型
            elif data_type == 'BIT':
                if isinstance(value, str):
                    return value.lower() in [
                        'true', '1', 'yes', 'y', '是', 'on']
                elif isinstance(value, (int, float)):
                    return bool(value)
                else:
                    return bool(value)

            # 日期时间类型
            elif data_type == 'DATETIME2':
                if isinstance(value, str):
                    return self._parse_datetime_string(value)
                elif isinstance(value, datetime):
                    return value
                else:
                    return None

            else:
                # 未知类型，转为字符串
                return str(value)

        except (ValueError, TypeError) as e:
            logger.warning(
                "数据类型转换失败",
                field_name=field_name,
                value=str(value)[:100],
                target_type=data_type,
                error=str(e),
            )

            # 转换失败时的默认值
            if data_type.startswith('NVARCHAR') or data_type == 'NTEXT':
                return str(value)[:100] if value else ""
            elif data_type in ['INT', 'BIGINT', 'SMALLINT', 'TINYINT']:
                return 0
            elif data_type.startswith('DECIMAL') or data_type in [
                'FLOAT',
                'REAL',
                'MONEY',
            ]:
                return 0.0
            elif data_type == 'BIT':
                return False
            elif data_type == 'DATETIME2':
                return None
            else:
                return str(value) if value else None

    def _parse_datetime_string(self, date_str: str) -> Optional[datetime]:
        """
        解析日期时间字符串

        Args:
            date_str: 日期时间字符串

        Returns:
            datetime: 解析后的日期时间对象
        """
        if not date_str or not isinstance(date_str, str):
            return None

        # 常见的日期时间格式
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y-%m-%d',
            '%Y/%m/%d',
            '%Y/%m/%d %H:%M:%S',
            '%m/%d/%Y',
            '%d/%m/%Y',
        ]

        # 清理字符串
        date_str = date_str.strip()

        # 尝试各种格式
        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue

        # 如果都不匹配，尝试ISO格式解析
        try:
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except ValueError:
            pass

        logger.warning("日期时间解析失败", date_str=date_str)
        return None

    async def batch_save_to_database(
        self, records: List[Dict], field_config: Dict
    ) -> int:
        """
        批量保存记录到数据库

        Args:
            records: 处理后的记录列表
            field_config: 字段配置

        Returns:
            int: 成功保存的记录数
        """
        if not records:
            return 0

        table_name = field_config.get(
            'table_name', field_config.get('module_name'))

        try:
            # 构建批量插入SQL
            columns = list(records[0].keys())
            columns_str = ', '.join([f'[{col}]' for col in columns])
            placeholders = ', '.join(
                [
                    ':' +
                    col.replace(' ', '_').replace('[', '').replace(']', '')
                    for col in columns
                ]
            )

            sql = f"""
            INSERT INTO [{table_name}] ({columns_str})
            VALUES ({placeholders})
            """

            # 准备参数数据
            batch_params = []
            for record in records:
                param_dict = {}
                for col in columns:
                    # 将列名转换为参数名（移除特殊字符）
                    param_name = col.replace(
                        ' ', '_').replace(
                        '[', '').replace(
                        ']', '')
                    param_dict[param_name] = record[col]
                batch_params.append(param_dict)

            # 执行批量插入
            async with get_async_session() as session:
                await session.execute(sql, batch_params)
                await session.commit()

                success_count = len(batch_params)
                logger.info(
                    "批量数据保存成功",
                    table_name=table_name,
                    records_count=success_count,
                )

                return success_count

        except Exception:
            logger.error(
                "批量数据保存失败",
                table_name=table_name,
                records_count=len(records),
                error=str(e),
            )

            # 尝试逐条插入
            return await self._single_save_fallback(records, field_config)

    async def _single_save_fallback(
        self, records: List[Dict], field_config: Dict
    ) -> int:
        """
        逐条保存的降级方案

        Args:
            records: 记录列表
            field_config: 字段配置

        Returns:
            int: 成功保存的记录数
        """
        table_name = field_config.get(
            'table_name', field_config.get('module_name'))
        success_count = 0

        try:
            async with get_async_session() as session:
                for i, record in enumerate(records):
                    try:
                        columns = list(record.keys())
                        columns_str = ', '.join(
                            [f'[{col}]' for col in columns])
                        values_str = ', '.join(
                            [
                                f":{col.replace(' ', '_').replace('[', '').replace(']', '')}"
                                for col in columns
                            ]
                        )

                        sql = f"INSERT INTO [{table_name}] ({columns_str}) VALUES ({values_str})"

                        # 准备参数
                        params = {}
                        for col in columns:
                            param_name = (
                                col.replace(
                                    ' ', '_').replace(
                                    '[', '').replace(
                                    ']', ''))
                            params[param_name] = record[col]

                        await session.execute(sql, params)
                        success_count += 1

                    except Exception:
                        logger.warning(
                            "单条记录保存失败",
                            table_name=table_name,
                            record_index=i,
                            error=str(e),
                        )

                await session.commit()

            logger.info(
                "降级单条保存完成",
                table_name=table_name,
                success_count=success_count,
                total_count=len(records),
            )

            return success_count

        except Exception:
            logger.error("降级保存失败", table_name=table_name, error=str(e))
            return success_count

    def validate_record(self, record: Dict, field_config: Dict) -> List[str]:
        """
        验证记录数据

        Args:
            record: 记录数据
            field_config: 字段配置

        Returns:
            List[str]: 验证错误列表
        """
        errors = []
        fields = field_config.get('fields', {})

        for api_field, field_conf in fields.items():
            if not field_conf.get('is_selected', False):
                continue

            chinese_name = field_conf['chinese_name']
            is_required = field_conf.get('is_required', False)

            # 检查必填字段
            if is_required and chinese_name not in record:
                errors.append(f"缺少必填字段: {chinese_name}")
                continue

            # 检查字段值
            value = record.get(chinese_name)
            if is_required and (value is None or value == ''):
                errors.append(f"必填字段不能为空: {chinese_name}")

            # 可以添加更多验证规则...

        return errors

    async def write_module_data(
        self, module_name: str, processed_data: List[Dict]
    ) -> Dict:
        """
        将处理后的数据写入数据库

        Args:
            module_name: 模块名称
            processed_data: 处理后的数据列表

        Returns:
            Dict: 写入结果
        """
        try:
            if not processed_data or len(processed_data) == 0:
                return {
                    "success": True,
                    "written_count": 0,
                    "message": "没有数据需要写入",
                }

            # 获取字段配置

            field_config_service = FieldConfigService()
            field_config = await field_config_service.load_field_config(module_name)

            if not field_config:
                logger.error("字段配置加载失败", module_name=module_name)
                return {
                    "success": False,
                    "written_count": 0,
                    "error": "字段配置加载失败",
                }

            # 执行真实的数据库写入
            logger.info(
                "开始真实数据库写入",
                module_name=module_name,
                record_count=len(processed_data),
            )

            written_count = await self.batch_save_to_database(
                processed_data, field_config
            )

            return {
                "success": True,
                "written_count": written_count,
                "message": f"成功写入{written_count}条记录到{module_name}表",
            }

        except Exception:
            logger.error("数据库写入失败", module_name=module_name, error=str(e))
            return {"success": False, "written_count": 0, "error": str(e)}
