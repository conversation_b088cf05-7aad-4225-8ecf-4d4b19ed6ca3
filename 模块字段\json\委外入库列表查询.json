[{"success": true, "code": 200, "message": "", "data": {"fieldVersion": 20230210, "appCode": "", "tokenSet": false, "tokenDoc": "", "tenantId": 0, "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "id": "a321db2a1d7f4e4ba4859f28a57a9da6", "name": "委外入库列表查询", "apiClassifyId": "9a11bbb107934f28814208338724658a", "apiClassifyName": "委外入库单", "apiClassifyCode": "OSMIN", "parentApiClassifies": "", "functionId": "", "openMode": "", "description": "委外入库列表查询", "auth": true, "bodyPassthrough": false, "healthExam": "", "healthStatus": "", "responseResultPassthrough": false, "contentType": "application/json", "returnPassthrough": "", "completeProxyUrl": "/yonbip/scm/osminrecord/list", "connectUrl": "/bill/list", "sort": 20, "handler": "openapi", "httpRequestType": "POST", "openApi": true, "preset": false, "productId": "710a0be3edff4f9092e35f63fd3b9bae", "productCode": "scm", "proxyUrl": "/yonbip/scm/osminrecord/list", "requestParamsDemo": "Url: /yonbip/scm/osminrecord/list?access_token=访问令牌 Body: { \"isSum\": true, \"code\": \"OSMI20220317000001\", \"pageSize\": 20, \"pageIndex\": 1, \"open_vouchdate_begin\": \"2022-03-17 00:00:00\", \"open_vouchdate_end\": \"2022-03-21 00:00:00\", \"status\": \"\", \"simpleVOs\": [ { \"field\": \"\", \"op\": \"\", \"value1\": \"\", \"value2\": \"\" } ] }", "requestProtocol": "HTTP", "serviceHttpMethod": "POST", "publishStatus": true, "approvalMsg": "", "rpcAppName": "", "rpcServiceName": "", "rpcMethodName": "", "rpcServiceUrl": "", "ma": false, "gmtCreate": "2020-12-12 14:50:36.000", "gmtUpdate": "2023-11-29 14:16:00.000", "address": "https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/osminrecord/list", "productName": "采购供应", "productClassifyId": "yonsuite", "productClassifyCode": "yonbip", "productClassifyName": "用友 YonBIP", "paramDTOS": {"paramDTOS": [{"id": 1872936969107931145, "name": "isSum", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": 1861195559319633926, "array": false, "paramDesc": "是否按照表头查询 true:表头 false:表头+明细 默认为false", "paramType": "boolean", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": false, "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:00.000", "gmtUpdate": "2023-11-29 14:16:00.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 1872936969107931146, "name": "code", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": 1861195559319633927, "array": false, "paramDesc": "单据编号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "OSMI20220317000001", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:00.000", "gmtUpdate": "2023-11-29 14:16:00.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 1872936969107931147, "name": "pageSize", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": 1861195559319633928, "array": false, "paramDesc": "每页显示数据数", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": 20, "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": 20, "required": true, "visible": true, "gmtCreate": "2023-11-29 14:16:00.000", "gmtUpdate": "2023-11-29 14:16:00.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 1872936969107931148, "name": "pageIndex", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": 1861195559319633929, "array": false, "paramDesc": "当前页数", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": 1, "required": true, "visible": true, "gmtCreate": "2023-11-29 14:16:00.000", "gmtUpdate": "2023-11-29 14:16:00.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 1872936969107931149, "name": "open_vouchdate_begin", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": 1861195559319633930, "array": false, "paramDesc": "单据开始日期", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "2022-03-17 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 4, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:00.000", "gmtUpdate": "2023-11-29 14:16:00.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 1872936969107931150, "name": "open_vouchdate_end", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": 1861195559319633931, "array": false, "paramDesc": "单据结束日期", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "2022-03-21 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 5, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:00.000", "gmtUpdate": "2023-11-29 14:16:00.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 1872936969107931151, "name": "status", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": 1861195559319633932, "array": false, "paramDesc": "单据状态，0 开立 1已审核 3 审核中", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:00.000", "gmtUpdate": "2023-11-29 14:16:00.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 1872936969107931140, "name": "simpleVOs", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "children": {"children": [{"id": 1872936969107931141, "name": "field", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936969107931140, "defParamId": 1861195559319633934, "array": false, "paramDesc": "属性名(条件传属性的名称，如单据编号code、单据日期vouchdate、收货组织org.code、委外组织osmOrg.code、收票组织inInvoiceOrg.code、委外供应商vendor.code、仓库编码warehouse.code、物料编码osmInRecords.product.cCode、物料分类osmInRecords.product.manageClass.code、物料SKU编码osmInRecords.productsku.cCode等)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:00.000", "gmtUpdate": "2023-11-29 14:16:00.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 1872936969107931142, "name": "op", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936969107931140, "defParamId": 1861195559319633935, "array": false, "paramDesc": "比较符(in:包含;eq:等于;lt:小于;gt:大于;like:模糊匹配;between:介于)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:00.000", "gmtUpdate": "2023-11-29 14:16:00.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 1872936969107931143, "name": "value1", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936969107931140, "defParamId": 1861195559319633936, "array": false, "paramDesc": "值1(条件)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:00.000", "gmtUpdate": "2023-11-29 14:16:00.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 1872936969107931144, "name": "value2", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936969107931140, "defParamId": 1861195559319633937, "array": false, "paramDesc": "值2(条件)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:00.000", "gmtUpdate": "2023-11-29 14:16:00.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "defParamId": 1861195559319633933, "array": true, "paramDesc": "扩展查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:00.000", "gmtUpdate": "2023-11-29 14:16:00.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "queryParamDTOS": "", "ysApi": false, "presetTokenApi": false, "applyFlag": false, "cover": false, "paramMapDTOS": {"paramMapDTOS": [{"id": 1872936977697865733, "name": "isSum", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": "", "array": false, "paramDesc": "是否按照表头查询 true:表头 false:表头+明细 默认为false", "paramType": "boolean", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "isSum", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "boolean", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 1872936977697865734, "name": "code", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": "", "array": false, "paramDesc": "单据编号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "code", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 1872936977697865735, "name": "pageSize", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": "", "array": false, "paramDesc": "每页显示数据数", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "pageSize", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 1872936977697865736, "name": "pageIndex", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": "", "array": false, "paramDesc": "当前页数", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "pageIndex", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 1872936977697865737, "name": "open_vouchdate_begin", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": "", "array": false, "paramDesc": "单据开始日期", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "aggregatedValueObject": false, "mapName": "open_vouchdate_begin", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 1872936977697865738, "name": "open_vouchdate_end", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": "", "array": false, "paramDesc": "单据结束日期", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "aggregatedValueObject": false, "mapName": "open_vouchdate_end", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 1872936977697865739, "name": "status", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": "", "array": false, "paramDesc": "单据状态，0 开立 1已审核 3 审核中", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "aggregatedValueObject": false, "mapName": "status", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 1872936977697865728, "name": "simpleVOs", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "children": {"children": [{"id": 1872936977697865729, "name": "field", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865728, "defParamId": "", "array": false, "paramDesc": "属性名(条件传属性的名称，如单据编号code、单据日期vouchdate、收货组织org.code、委外组织osmOrg.code、收票组织inInvoiceOrg.code、委外供应商vendor.code、仓库编码warehouse.code、物料编码osmInRecords.product.cCode、物料分类osmInRecords.product.manageClass.code、物料SKU编码osmInRecords.productsku.cCode等)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "field", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 1872936977697865730, "name": "op", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865728, "defParamId": "", "array": false, "paramDesc": "比较符(in:包含;eq:等于;lt:小于;gt:大于;like:模糊匹配;between:介于)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "op", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 1872936977697865731, "name": "value1", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865728, "defParamId": "", "array": false, "paramDesc": "值1(条件)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "value1", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 1872936977697865732, "name": "value2", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865728, "defParamId": "", "array": false, "paramDesc": "值2(条件)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "value2", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "defParamId": "", "array": false, "paramDesc": "扩展查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "aggregatedValueObject": false, "mapName": "simpleVOs", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "paramReturnDTOS": {"paramReturnDTOS": [{"id": 1872936977697865877, "name": "code", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": 1861195559319633950, "array": false, "paramDesc": "返回码，调用成功时返回200。", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865878, "name": "message", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "defParamId": 1861195559319633951, "array": false, "paramDesc": "调用失败时的错误信息", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865740, "name": "data", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": "", "children": {"children": [{"id": 1872936977697865871, "name": "pageIndex", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865740, "defParamId": 1861195559319633953, "array": false, "paramDesc": "当前页", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865872, "name": "pageSize", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865740, "defParamId": 1861195559319633954, "array": false, "paramDesc": "每页显示数据数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865873, "name": "pageCount", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865740, "defParamId": 1861195559319633955, "array": false, "paramDesc": "总页数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865874, "name": "beginPageIndex", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865740, "defParamId": 1861195559319633956, "array": false, "paramDesc": "开始页", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865875, "name": "endPageIndex", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865740, "defParamId": 1861195559319633957, "array": false, "paramDesc": "结束页", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865876, "name": "recordCount", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865740, "defParamId": 1861195559319633958, "array": false, "paramDesc": "总记录数", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865741, "name": "recordList", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865740, "children": {"children": [{"id": 1872936977697865754, "name": "osmInRecords_productionType", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633960, "array": false, "paramDesc": "产出类型", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865755, "name": "vendor_code", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633961, "array": false, "paramDesc": "委外供应商编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865756, "name": "oriTax", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633962, "array": false, "paramDesc": "税额", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865757, "name": "pocode", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633963, "array": false, "paramDesc": "委外订单编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865758, "name": "product_cCode", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633964, "array": false, "paramDesc": "物料编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865759, "name": "invoiceVendor", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633965, "array": false, "paramDesc": "开票供应商ID", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865760, "name": "sfee", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633966, "array": false, "paramDesc": "累计结算费用", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865761, "name": "priceUOM_Precision", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633967, "array": false, "paramDesc": "计价单位精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865762, "name": "memo", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633968, "array": false, "paramDesc": "备注", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865763, "name": "stockStatusDoc_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633969, "array": false, "paramDesc": "库存状态", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865764, "name": "priceUOM_Code", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633970, "array": false, "paramDesc": "计价单位编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865765, "name": "totalQuantity", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633971, "array": false, "paramDesc": "整单数量", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865766, "name": "natCurrency", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633972, "array": false, "paramDesc": "本币ID", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865767, "name": "taxitems_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633973, "array": false, "paramDesc": "税目名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865768, "name": "stockUnitId_Precision", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633974, "array": false, "paramDesc": "库存单位精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 14, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865769, "name": "costMoney", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633975, "array": false, "paramDesc": "成本金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865770, "name": "id", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633976, "array": false, "paramDesc": "单据主表id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865771, "name": "tplid", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633977, "array": false, "paramDesc": "模板id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865772, "name": "isWfControlled", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633978, "array": false, "paramDesc": "是否审批流控制（true:审批流控制 false:非审批流控制）", "paramType": "boolean", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865773, "name": "natSum", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633979, "array": false, "paramDesc": "本币含税金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865774, "name": "warehouse", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633980, "array": false, "paramDesc": "仓库id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 20, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865775, "name": "isAutomaticVerify", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633981, "array": false, "paramDesc": "是否自动核销，true:是、false:否", "paramType": "boolean", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 21, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865776, "name": "warehouse_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633982, "array": false, "paramDesc": "仓库", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 22, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865777, "name": "auditTime", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633983, "array": false, "paramDesc": "审核时间", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 23, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865778, "name": "natCurrency_priceDigit", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633984, "array": false, "paramDesc": "本币单价精度", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 24, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865779, "name": "exchRateType", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633985, "array": false, "paramDesc": "汇率类型ID", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 25, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865780, "name": "billqty", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633986, "array": false, "paramDesc": "累计开票数量", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 26, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865781, "name": "invExchRate", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633987, "array": false, "paramDesc": "换算率", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 27, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865782, "name": "status", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633988, "array": false, "paramDesc": "单据状态，0 开立 1已审核 3 审核中", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 28, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865783, "name": "isGiftProduct", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633989, "array": false, "paramDesc": "赠品，true:是、false:否", "paramType": "boolean", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 29, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865784, "name": "returncount", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633990, "array": false, "paramDesc": "退回次数", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 30, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865785, "name": "verifystate", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633991, "array": false, "paramDesc": "审批状态 （0：未提交 1：已提交 2：已审核）", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 31, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865786, "name": "invoicingDocEntryAndMgmt", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633992, "array": false, "paramDesc": "立账开票依据", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 32, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865787, "name": "isVerification", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633993, "array": false, "paramDesc": "核销标识", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 33, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865788, "name": "currency_moneyDigit", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633994, "array": false, "paramDesc": "币种金额精度", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 34, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865789, "name": "warehouse_code", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633995, "array": false, "paramDesc": "仓库编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 35, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865790, "name": "stockStatusDoc", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633996, "array": false, "paramDesc": "库存状态id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 36, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865791, "name": "productsku_cName", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633997, "array": false, "paramDesc": "物料sku名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 37, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865792, "name": "osmOrg_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633998, "array": false, "paramDesc": "委外组织", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 38, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865793, "name": "vouchdate", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319633999, "array": false, "paramDesc": "单据日期", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 39, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865794, "name": "receiptDocEntryAndMgmt", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634000, "array": false, "paramDesc": "入库立账方式", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 40, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865795, "name": "natCurrency_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634001, "array": false, "paramDesc": "本币名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 41, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865796, "name": "invoiceVendor_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634002, "array": false, "paramDesc": "开票供应商", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 42, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865797, "name": "invPriceExchRate", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634003, "array": false, "paramDesc": "计价换算率", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 43, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865798, "name": "vendor", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634004, "array": false, "paramDesc": "委外供应商id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 44, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865799, "name": "sqty", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634005, "array": false, "paramDesc": "累计结算数量", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 45, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865800, "name": "currency", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634006, "array": false, "paramDesc": "币种ID", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 46, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865801, "name": "pubts", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634007, "array": false, "paramDesc": "时间戳", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 47, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865802, "name": "smoney", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634008, "array": false, "paramDesc": "累计结算金额", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 48, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865803, "name": "org_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634009, "array": false, "paramDesc": "收货组织", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 49, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865804, "name": "isFlowCoreBill", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634010, "array": false, "paramDesc": "是否流程核心单据,true:是、false:否", "paramType": "boolean", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 50, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865805, "name": "auditDate", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634011, "array": false, "paramDesc": "审核日期", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 51, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865806, "name": "creator", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634012, "array": false, "paramDesc": "创建人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 52, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865807, "name": "product", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634013, "array": false, "paramDesc": "物料id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 53, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865808, "name": "oriSum", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634014, "array": false, "paramDesc": "含税金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 54, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865809, "name": "inInvoiceOrg_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634015, "array": false, "paramDesc": "收票组织", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 55, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865810, "name": "exchRateType_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634016, "array": false, "paramDesc": "汇率类型", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 56, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865811, "name": "department_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634017, "array": false, "paramDesc": "委外部门", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 57, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865812, "name": "auditor", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634018, "array": false, "paramDesc": "审核人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 58, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865813, "name": "accountOrg", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634019, "array": false, "paramDesc": "会计主体", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 59, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865814, "name": "priceQty", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634020, "array": false, "paramDesc": "计价数量", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 60, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865815, "name": "createTime", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634021, "array": false, "paramDesc": "创建时间", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 61, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865816, "name": "natMoney", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634022, "array": false, "paramDesc": "本币无税金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 62, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865817, "name": "taxitems_code", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634023, "array": false, "paramDesc": "税目编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 63, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865818, "name": "department_code", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634024, "array": false, "paramDesc": "委外部门编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 64, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865819, "name": "osmInRecords_osmType", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634025, "array": false, "paramDesc": "委外类型", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 65, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865820, "name": "currency_priceDigit", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634026, "array": false, "paramDesc": "币种单价精度", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 66, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865821, "name": "stockUnit_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634027, "array": false, "paramDesc": "库存单位", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 67, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865822, "name": "isBeginning", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634028, "array": false, "paramDesc": "是否期初，,true:是、false:否", "paramType": "boolean", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 68, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865823, "name": "bustype_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634029, "array": false, "paramDesc": "交易类型", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 69, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865824, "name": "modifier", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634030, "array": false, "paramDesc": "修改人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 70, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865825, "name": "natTax", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634031, "array": false, "paramDesc": "本币税额", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 71, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865826, "name": "source", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634032, "array": false, "paramDesc": "上游单据类型，po_subcontract_order：委外订单，po_osm_arrive_order：委外到货单", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 72, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865827, "name": "srcBill", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634033, "array": false, "paramDesc": "来源单据id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 73, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865828, "name": "subQty", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634034, "array": false, "paramDesc": "件数", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 74, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865829, "name": "modifyTime", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634035, "array": false, "paramDesc": "修改时间", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 75, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865830, "name": "inInvoiceOrg", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634036, "array": false, "paramDesc": "收票组织", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 76, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865831, "name": "product_cName", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634037, "array": false, "paramDesc": "物料名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 77, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865832, "name": "vendor_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634038, "array": false, "paramDesc": "委外供应商", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 78, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865833, "name": "oriUnitPrice", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634039, "array": false, "paramDesc": "无税单价", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 79, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865834, "name": "barCode", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634040, "array": false, "paramDesc": "单据条码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 80, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865835, "name": "unit_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634041, "array": false, "paramDesc": "计量单位", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 81, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865836, "name": "taxRate", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634042, "array": false, "paramDesc": "税率", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 82, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865837, "name": "unit", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634043, "array": false, "paramDesc": "单位id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 83, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865838, "name": "productsku", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634044, "array": false, "paramDesc": "物料SKUid", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 84, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865839, "name": "productsku_cCode", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634045, "array": false, "paramDesc": "物料sku编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 85, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865840, "name": "natCurrency_moneyDigit", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634046, "array": false, "paramDesc": "本币金额精度", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 86, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865841, "name": "accountOrg_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634047, "array": false, "paramDesc": "会计主体", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 87, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865842, "name": "qty", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634048, "array": false, "paramDesc": "数量", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 88, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865843, "name": "unit_Precision", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634049, "array": false, "paramDesc": "主计量精度", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 89, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865844, "name": "oriTaxUnitPrice", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634050, "array": false, "paramDesc": "含税单价", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 90, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865845, "name": "oriMoney", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634051, "array": false, "paramDesc": "无税金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 91, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865846, "name": "contactsPieces", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634052, "array": false, "paramDesc": "应收件数", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 92, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865847, "name": "contactsQuantity", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634053, "array": false, "paramDesc": "应收数量", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 93, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865848, "name": "natUnitPrice", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634054, "array": false, "paramDesc": "本币无税单价", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 94, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865849, "name": "code", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634055, "array": false, "paramDesc": "单据编号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 95, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865850, "name": "exchRate", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634056, "array": false, "paramDesc": "汇率", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 96, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865851, "name": "osmInRecords_id", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634057, "array": false, "paramDesc": "订单行id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 97, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865852, "name": "priceUOM", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634058, "array": false, "paramDesc": "计价单位id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 98, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865853, "name": "department", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634059, "array": false, "paramDesc": "委外部门ID", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 99, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865854, "name": "currency_name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634060, "array": false, "paramDesc": "币种名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 100, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865855, "name": "org", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634061, "array": false, "paramDesc": "收货组织", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 101, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865856, "name": "custom", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634062, "array": false, "paramDesc": "客户id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 102, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865857, "name": "osmOrg", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634063, "array": false, "paramDesc": "委外组织ID", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 103, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865858, "name": "bustype", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634064, "array": false, "paramDesc": "交易类型id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 104, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865859, "name": "costUnitPrice", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634065, "array": false, "paramDesc": "成本单价", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 105, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865860, "name": "upcode", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634066, "array": false, "paramDesc": "上游单据号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 106, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865861, "name": "priceUOM_Name", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634067, "array": false, "paramDesc": "计价单位名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 107, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865862, "name": "taxitems", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634068, "array": false, "paramDesc": "税目id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 108, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865863, "name": "natTaxUnitPrice", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634069, "array": false, "paramDesc": "本币含税单价", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 109, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865864, "name": "unDeductTaxRate", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634070, "array": false, "paramDesc": "不可抵扣税率", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 110, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865865, "name": "unDeductTax", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634071, "array": false, "paramDesc": "不可抵扣税额", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 111, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865866, "name": "oriUnDeductTax", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634072, "array": false, "paramDesc": "原币不可抵扣税额", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 112, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865742, "name": "bodyItem", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "children": {"children": {"id": 1872936977697865743, "name": "define1", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865742, "defParamId": 1861195559319634074, "array": false, "paramDesc": "单据体自定义(单据体自定义项最多可设置60个)", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}}, "defParamId": 1861195559319634073, "array": false, "paramDesc": "单据体自定义项", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 113, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865744, "name": "headItem", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "children": {"children": {"id": 1872936977697865745, "name": "define1", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865744, "defParamId": 1861195559319634076, "array": false, "paramDesc": "单据头自定义(单据头自定义项最多可设置60个)", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}}, "defParamId": 1861195559319634075, "array": false, "paramDesc": "单据头自定义项", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 114, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865746, "name": "osmInRecords", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "children": {"children": [{"id": 1872936977697865747, "name": "opSn", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865746, "defParamId": 1861195559319634081, "array": false, "paramDesc": "工序顺序号", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865748, "name": "operationId", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865746, "defParamId": 1861195559319634082, "array": false, "paramDesc": "工序", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865749, "name": "endOp", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865746, "defParamId": 1861195559319634083, "array": false, "paramDesc": "末序(false:否,true:是)", "paramType": "boolean", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865750, "name": "sourcePoOrderCode", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865746, "defParamId": 1861195559319634084, "array": false, "paramDesc": "生产订单号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865751, "name": "sourcePoOrderProductRowno", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865746, "defParamId": 1861195559319634085, "array": false, "paramDesc": "生产订单行号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865752, "name": "sourcePoOrderId", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865746, "defParamId": 1861195559319634086, "array": false, "paramDesc": "生产订单ID", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865753, "name": "sourcePoOrderProductId", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865746, "defParamId": 1861195559319634087, "array": false, "paramDesc": "生产订单行ID", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}]}, "defParamId": 1861195559319634080, "array": false, "paramDesc": "委外入库单子表", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 118, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 1872936977697865870, "name": "costAccountingMethod", "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "parentId": 1872936977697865741, "defParamId": 1861195559319634098, "array": false, "paramDesc": "委外成本核算方式(0:按委外入库核算成本,1:按委外订单核算成本)", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 119, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}]}, "defParamId": 1861195559319633959, "array": true, "paramDesc": "返回结果对象", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}]}, "defParamId": 1861195559319633952, "array": false, "paramDesc": "调用成功时的返回数据", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}]}, "returnFormatType": "JSON", "paramConstDTOS": "", "paramConstMapDTOS": "", "apiDemoReturnDTOS": {"apiDemoReturnDTOS": [{"id": 1872936977697865884, "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"recordList\": [ { \"osmInRecords_productionType\": 0, \"vendor_code\": \"\", \"oriTax\": 0, \"pocode\": \"\", \"product_cCode\": \"\", \"invoiceVendor\": 0, \"sfee\": 0, \"priceUOM_Precision\": 0, \"memo\": \"\", \"stockStatusDoc_name\": \"\", \"priceUOM_Code\": \"\", \"totalQuantity\": 0, \"natCurrency\": \"\", \"taxitems_name\": \"\", \"stockUnitId_Precision\": 0, \"costMoney\": 0, \"id\": 0, \"tplid\": 0, \"isWfControlled\": true, \"natSum\": 0, \"warehouse\": 0, \"isAutomaticVerify\": true, \"warehouse_name\": \"\", \"auditTime\": \"\", \"natCurrency_priceDigit\": 0, \"exchRateType\": \"\", \"billqty\": 0, \"invExchRate\": 0, \"status\": 0, \"isGiftProduct\": true, \"returncount\": 0, \"verifystate\": 0, \"invoicingDocEntryAndMgmt\": \"\", \"isVerification\": 0, \"currency_moneyDigit\": 0, \"warehouse_code\": \"\", \"stockStatusDoc\": 0, \"productsku_cName\": \"\", \"osmOrg_name\": \"\", \"vouchdate\": \"\", \"receiptDocEntryAndMgmt\": \"\", \"natCurrency_name\": \"\", \"invoiceVendor_name\": \"\", \"invPriceExchRate\": 0, \"vendor\": 0, \"sqty\": 0, \"currency\": \"\", \"pubts\": \"\", \"smoney\": 0, \"org_name\": \"\", \"isFlowCoreBill\": true, \"auditDate\": \"\", \"creator\": \"\", \"product\": 0, \"oriSum\": 0, \"inInvoiceOrg_name\": \"\", \"exchRateType_name\": \"\", \"department_name\": \"\", \"auditor\": \"\", \"accountOrg\": \"\", \"priceQty\": 0, \"createTime\": \"\", \"natMoney\": 0, \"taxitems_code\": \"\", \"department_code\": \"\", \"osmInRecords_osmType\": 0, \"currency_priceDigit\": 0, \"stockUnit_name\": \"\", \"isBeginning\": true, \"bustype_name\": \"\", \"modifier\": \"\", \"natTax\": 0, \"source\": \"\", \"srcBill\": \"\", \"subQty\": 0, \"modifyTime\": \"\", \"inInvoiceOrg\": \"\", \"product_cName\": \"\", \"vendor_name\": \"\", \"oriUnitPrice\": 0, \"barCode\": \"\", \"unit_name\": \"\", \"taxRate\": 0, \"unit\": 0, \"productsku\": 0, \"productsku_cCode\": \"\", \"natCurrency_moneyDigit\": 0, \"accountOrg_name\": \"\", \"qty\": 0, \"unit_Precision\": 0, \"oriTaxUnitPrice\": 0, \"oriMoney\": 0, \"contactsPieces\": 0, \"contactsQuantity\": 0, \"natUnitPrice\": 0, \"code\": \"\", \"exchRate\": 0, \"osmInRecords_id\": 0, \"priceUOM\": 0, \"department\": \"\", \"currency_name\": \"\", \"org\": \"\", \"custom\": 0, \"osmOrg\": \"\", \"bustype\": \"\", \"costUnitPrice\": 0, \"upcode\": \"\", \"priceUOM_Name\": \"\", \"taxitems\": \"\", \"natTaxUnitPrice\": 0, \"unDeductTaxRate\": 0, \"unDeductTax\": 0, \"oriUnDeductTax\": 0, \"bodyItem\": { \"define1\": \"\" }, \"headItem\": { \"define1\": \"\" }, \"osmInRecordsCharacteristics\": 0, \"osmInRecordsDefineCharacter\": 0, \"osmInRecordDefineCharacter\": 0, \"osmInRecords\": { \"opSn\": 0, \"operationId\": 0, \"endOp\": true, \"sourcePoOrderCode\": \"\", \"sourcePoOrderProductRowno\": \"\", \"sourcePoOrderId\": 0, \"sourcePoOrderProductId\": 0 }, \"costAccountingMethod\": \"\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 1872936977697865885, "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "content": "{ \"code\": \"999\", \"message\": \"No enum constant org.imeta.core.base.ConditionOperator.2\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "apiDemoReturnDTOList": {"apiDemoReturnDTOList": [{"id": 1872936977697865884, "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"recordList\": [ { \"osmInRecords_productionType\": 0, \"vendor_code\": \"\", \"oriTax\": 0, \"pocode\": \"\", \"product_cCode\": \"\", \"invoiceVendor\": 0, \"sfee\": 0, \"priceUOM_Precision\": 0, \"memo\": \"\", \"stockStatusDoc_name\": \"\", \"priceUOM_Code\": \"\", \"totalQuantity\": 0, \"natCurrency\": \"\", \"taxitems_name\": \"\", \"stockUnitId_Precision\": 0, \"costMoney\": 0, \"id\": 0, \"tplid\": 0, \"isWfControlled\": true, \"natSum\": 0, \"warehouse\": 0, \"isAutomaticVerify\": true, \"warehouse_name\": \"\", \"auditTime\": \"\", \"natCurrency_priceDigit\": 0, \"exchRateType\": \"\", \"billqty\": 0, \"invExchRate\": 0, \"status\": 0, \"isGiftProduct\": true, \"returncount\": 0, \"verifystate\": 0, \"invoicingDocEntryAndMgmt\": \"\", \"isVerification\": 0, \"currency_moneyDigit\": 0, \"warehouse_code\": \"\", \"stockStatusDoc\": 0, \"productsku_cName\": \"\", \"osmOrg_name\": \"\", \"vouchdate\": \"\", \"receiptDocEntryAndMgmt\": \"\", \"natCurrency_name\": \"\", \"invoiceVendor_name\": \"\", \"invPriceExchRate\": 0, \"vendor\": 0, \"sqty\": 0, \"currency\": \"\", \"pubts\": \"\", \"smoney\": 0, \"org_name\": \"\", \"isFlowCoreBill\": true, \"auditDate\": \"\", \"creator\": \"\", \"product\": 0, \"oriSum\": 0, \"inInvoiceOrg_name\": \"\", \"exchRateType_name\": \"\", \"department_name\": \"\", \"auditor\": \"\", \"accountOrg\": \"\", \"priceQty\": 0, \"createTime\": \"\", \"natMoney\": 0, \"taxitems_code\": \"\", \"department_code\": \"\", \"osmInRecords_osmType\": 0, \"currency_priceDigit\": 0, \"stockUnit_name\": \"\", \"isBeginning\": true, \"bustype_name\": \"\", \"modifier\": \"\", \"natTax\": 0, \"source\": \"\", \"srcBill\": \"\", \"subQty\": 0, \"modifyTime\": \"\", \"inInvoiceOrg\": \"\", \"product_cName\": \"\", \"vendor_name\": \"\", \"oriUnitPrice\": 0, \"barCode\": \"\", \"unit_name\": \"\", \"taxRate\": 0, \"unit\": 0, \"productsku\": 0, \"productsku_cCode\": \"\", \"natCurrency_moneyDigit\": 0, \"accountOrg_name\": \"\", \"qty\": 0, \"unit_Precision\": 0, \"oriTaxUnitPrice\": 0, \"oriMoney\": 0, \"contactsPieces\": 0, \"contactsQuantity\": 0, \"natUnitPrice\": 0, \"code\": \"\", \"exchRate\": 0, \"osmInRecords_id\": 0, \"priceUOM\": 0, \"department\": \"\", \"currency_name\": \"\", \"org\": \"\", \"custom\": 0, \"osmOrg\": \"\", \"bustype\": \"\", \"costUnitPrice\": 0, \"upcode\": \"\", \"priceUOM_Name\": \"\", \"taxitems\": \"\", \"natTaxUnitPrice\": 0, \"unDeductTaxRate\": 0, \"unDeductTax\": 0, \"oriUnDeductTax\": 0, \"bodyItem\": { \"define1\": \"\" }, \"headItem\": { \"define1\": \"\" }, \"osmInRecordsCharacteristics\": 0, \"osmInRecordsDefineCharacter\": 0, \"osmInRecordDefineCharacter\": 0, \"osmInRecords\": { \"opSn\": 0, \"operationId\": 0, \"endOp\": true, \"sourcePoOrderCode\": \"\", \"sourcePoOrderProductRowno\": \"\", \"sourcePoOrderId\": 0, \"sourcePoOrderProductId\": 0 }, \"costAccountingMethod\": \"\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 1872936977697865885, "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "content": "{ \"code\": \"999\", \"message\": \"No enum constant org.imeta.core.base.ConditionOperator.2\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "routingStgy": 0, "routingStgyList": "", "apiDemoReturnDTO": {"id": 1872936977697865884, "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"recordList\": [ { \"osmInRecords_productionType\": 0, \"vendor_code\": \"\", \"oriTax\": 0, \"pocode\": \"\", \"product_cCode\": \"\", \"invoiceVendor\": 0, \"sfee\": 0, \"priceUOM_Precision\": 0, \"memo\": \"\", \"stockStatusDoc_name\": \"\", \"priceUOM_Code\": \"\", \"totalQuantity\": 0, \"natCurrency\": \"\", \"taxitems_name\": \"\", \"stockUnitId_Precision\": 0, \"costMoney\": 0, \"id\": 0, \"tplid\": 0, \"isWfControlled\": true, \"natSum\": 0, \"warehouse\": 0, \"isAutomaticVerify\": true, \"warehouse_name\": \"\", \"auditTime\": \"\", \"natCurrency_priceDigit\": 0, \"exchRateType\": \"\", \"billqty\": 0, \"invExchRate\": 0, \"status\": 0, \"isGiftProduct\": true, \"returncount\": 0, \"verifystate\": 0, \"invoicingDocEntryAndMgmt\": \"\", \"isVerification\": 0, \"currency_moneyDigit\": 0, \"warehouse_code\": \"\", \"stockStatusDoc\": 0, \"productsku_cName\": \"\", \"osmOrg_name\": \"\", \"vouchdate\": \"\", \"receiptDocEntryAndMgmt\": \"\", \"natCurrency_name\": \"\", \"invoiceVendor_name\": \"\", \"invPriceExchRate\": 0, \"vendor\": 0, \"sqty\": 0, \"currency\": \"\", \"pubts\": \"\", \"smoney\": 0, \"org_name\": \"\", \"isFlowCoreBill\": true, \"auditDate\": \"\", \"creator\": \"\", \"product\": 0, \"oriSum\": 0, \"inInvoiceOrg_name\": \"\", \"exchRateType_name\": \"\", \"department_name\": \"\", \"auditor\": \"\", \"accountOrg\": \"\", \"priceQty\": 0, \"createTime\": \"\", \"natMoney\": 0, \"taxitems_code\": \"\", \"department_code\": \"\", \"osmInRecords_osmType\": 0, \"currency_priceDigit\": 0, \"stockUnit_name\": \"\", \"isBeginning\": true, \"bustype_name\": \"\", \"modifier\": \"\", \"natTax\": 0, \"source\": \"\", \"srcBill\": \"\", \"subQty\": 0, \"modifyTime\": \"\", \"inInvoiceOrg\": \"\", \"product_cName\": \"\", \"vendor_name\": \"\", \"oriUnitPrice\": 0, \"barCode\": \"\", \"unit_name\": \"\", \"taxRate\": 0, \"unit\": 0, \"productsku\": 0, \"productsku_cCode\": \"\", \"natCurrency_moneyDigit\": 0, \"accountOrg_name\": \"\", \"qty\": 0, \"unit_Precision\": 0, \"oriTaxUnitPrice\": 0, \"oriMoney\": 0, \"contactsPieces\": 0, \"contactsQuantity\": 0, \"natUnitPrice\": 0, \"code\": \"\", \"exchRate\": 0, \"osmInRecords_id\": 0, \"priceUOM\": 0, \"department\": \"\", \"currency_name\": \"\", \"org\": \"\", \"custom\": 0, \"osmOrg\": \"\", \"bustype\": \"\", \"costUnitPrice\": 0, \"upcode\": \"\", \"priceUOM_Name\": \"\", \"taxitems\": \"\", \"natTaxUnitPrice\": 0, \"unDeductTaxRate\": 0, \"unDeductTax\": 0, \"oriUnDeductTax\": 0, \"bodyItem\": { \"define1\": \"\" }, \"headItem\": { \"define1\": \"\" }, \"osmInRecordsCharacteristics\": 0, \"osmInRecordsDefineCharacter\": 0, \"osmInRecordDefineCharacter\": 0, \"osmInRecords\": { \"opSn\": 0, \"operationId\": 0, \"endOp\": true, \"sourcePoOrderCode\": \"\", \"sourcePoOrderProductRowno\": \"\", \"sourcePoOrderId\": 0, \"sourcePoOrderProductId\": 0 }, \"costAccountingMethod\": \"\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, "apiDemoReturnDTOError": {"id": 1872936977697865885, "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "content": "{ \"code\": \"999\", \"message\": \"No enum constant org.imeta.core.base.ConditionOperator.2\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}, "errorCodeDTOS": {"errorCodeDTOS": {"id": 1872936977697865881, "apiId": "a321db2a1d7f4e4ba4859f28a57a9da6", "errorCode": 999, "errorMessage": "服务端逻辑异常", "errorType": "API", "errorcodeDesc": "", "gmtCreate": "2023-11-29 14:16:01.000", "gmtUpdate": "2023-11-29 14:16:01.000", "apiName": "", "edit": false, "defErrorId": 1861195559319634248, "ytenantId": "", "displayCodeId": ""}}, "displayCodeApiConfigDTOS": "", "tokenPlugin": "", "paramParsePlugin": "", "authPlugin": {"id": "09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "", "name": "友户通token认证业务扩展插件", "configurable": false, "description": "YonsuitBusinessExtendPlugin", "pluginType": "auth", "pluginTypeName": "业务扩展插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": false, "gmtCreate": "2020-05-22 00:00:00", "gmtUpdate": "2020-05-22 00:00:00", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "a321db2a1d7f4e4ba4859f28a57a9da6", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "resultParsePlugin": {"id": "w181ed01-1e9b-4350-b994-71a66f017555", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "resultParse", "name": "返回参数转换插件", "configurable": false, "description": "解决返回值中带！的，转换为json", "pluginType": "resultParse", "pluginTypeName": "返回值解析插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.result.ResultMapTransferParsePlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": true, "gmtCreate": "2020-07-29 00:00:00", "gmtUpdate": "", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "a321db2a1d7f4e4ba4859f28a57a9da6", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "mapReturnPluginConfig": "", "billNo": "", "domain": "", "apiCategory": "", "docUrl": "", "pathMatch": 0, "createUser": "36a8b72b-d965-404d-a02d-66ff4a7afeb3", "createUserName": "", "approvalStatus": 1, "publishTime": "2023-11-29 14:16:14", "pathJoin": false, "timeOut": 30, "tokenPluginName": "", "authPluginName": "", "resultPluginName": "", "apiDemoReturnRightDemo": "", "apiDemoReturnErrorDemo": "", "mock": false, "mockTimeout": 0, "customUrl": "osminrecord/list", "fixedUrl": "/yonbip/scm/", "apiCode": "a321db2a1d7f4e4ba4859f28a57a9da6", "tokenCheckType": "", "enableMulti": false, "multiField": "", "idempotent": "non", "bidirectionalSSL": "", "ucgSchema": "HTTPS", "updateUserId": "36a8b72b-d965-404d-a02d-66ff4a7afeb3", "updateUserName": "昵称-王章宇", "paramIsForce": true, "userIDPassthrough": true, "applyUser": "", "applyMsg": "", "dr": 0, "microServiceCode": "domain.yonbip-scm-stock", "applicationCode": "yonbip-scm-stock", "privacyCategory": 1, "privacyLevel": 3, "apiDesigned": 0, "serviceType": 0, "integrateSchemeCode": "", "integrateSchemeName": "", "integrateObjectCode": "", "integrateObjectName": "", "integrateObjectCreatedType": "", "returnIntegObjId": "", "returnIntegObjName": "", "apiIntegrateDTOList": "", "apiRouteInfoDTOList": "", "arrayParam": false, "fileSize": "", "cc": true, "paramTransferMode": 2, "ytenantId": 0, "statusConf": "", "scene": 1, "version": "", "bizObjUri": "", "bizObjOperationType": "", "apiDefId": 1861195559319633924, "paramExtBizObjCode": "", "paramExtBizObjName": "", "paramExtRequest": 1, "paramExtResponse": 1, "paramExtInExtendKey": 1, "openScene": 1, "integrationScene": "", "apiType": "", "paramMark": "", "integrateSysId": "", "integrateSysName": "", "integrateSysCode": "", "dataZoneSetting": false, "reqDataZoneSetting": false, "respDataZoneSetting": false, "reqDataAllQuery": false, "reqDataAllBody": false, "respDataAllBody": false, "chargeStatus": 1, "beforeSpeed": 40, "afterSpeed": 80, "speedStatus": false, "reqDataRefPath": "", "respDataRefPath": "", "pubHistory": "", "deprecated": 0, "recommendedApiId": "", "recommendedApiName": "", "domainAppCode": "", "multiVersion": 0, "apiTag": ""}}, {"success": true, "code": 200, "message": "", "data": {"id": 2108770660671029249, "name": "用友YonBIP", "type": "integrateSys", "sort": 0, "enable": 0, "children": {"children": {"id": "SCC", "name": "供应链云", "type": 1, "sort": 0, "enable": 0, "children": {"children": {"id": "MM", "name": "采购供应", "type": 2, "sort": 0, "enable": 0, "children": {"children": {"id": "ST", "name": "库存管理", "type": 3, "sort": 0, "enable": 0, "children": {"children": {"id": "ustock.st_osminrecord", "name": "委外入库单", "type": 4, "sort": 0, "enable": 0, "children": "", "parentId": "", "productId": "", "code": "ustock.st_osminrecord", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "ST", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "MM", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "SCC", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "current_yonbip_default_sys", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "isOrigin": 0, "hasChildren": 0, "order": 0}}, {"success": true, "code": 200, "message": "", "data": [{"id": "72113971-ae4c-4188-bc55-44b6173f4e0b", "name": "XS15", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "顾客订单号（订单表体）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:29:59", "gmtUpdate": "2025-07-26 17:29:59", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "b946709d-f4d9-4a43-a551-f55beee7f3d5", "name": "XXX0111", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求分类项", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:29:59", "gmtUpdate": "2025-07-26 17:29:59", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:29:59", "gmtUpdate": "2025-07-26 17:29:59", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": [{"id": "97f27951-ce5e-460b-964a-a1af5fbfd786", "name": "CG02", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "检验数", "paramType": "Decimal", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "number", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:30:10", "gmtUpdate": "2025-07-26 17:30:10", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 24, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "16056259-e88e-4cf0-9ccb-62d881fb9426", "name": "CG03", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "合格数", "paramType": "Decimal", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "number", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:30:10", "gmtUpdate": "2025-07-26 17:30:10", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 24, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "4d8e812b-5658-461e-8fe1-988462ebfc9c", "name": "CG05", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "送货单号（单身）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:30:10", "gmtUpdate": "2025-07-26 17:30:10", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "26422e53-b04e-48b1-bbfa-a190bf54f59b", "name": "WW", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "委外交货日期", "paramType": "Date", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "date", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:30:10", "gmtUpdate": "2025-07-26 17:30:10", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "eded1c3e-5903-49a9-8615-4986e9abc719", "name": "XS11", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求分类号test", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:30:10", "gmtUpdate": "2025-07-26 17:30:10", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:30:10", "gmtUpdate": "2025-07-26 17:30:10", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": [{"id": "36470ffe-96dd-4e1c-b3eb-9424e51cf835", "name": "CG04", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "送货单号（单头）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:30:20", "gmtUpdate": "2025-07-26 17:30:20", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:30:20", "gmtUpdate": "2025-07-26 17:30:20", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}]