import base64
import hashlib
import hmac
import time
from urllib.parse import quote_plus

import requests

TENANT_ID = "a2wmiscz"
APP_KEY = "500748557be74fe7aa78c566342a5e64"
APP_SECRET = "8970eb0d2ff418f62bdd3e926db33e13c556cfb2"


def get_data_center_domainss(tenant_id):
    """TODO: Add function description."""
    url = f"https://apigateway.yonyoucloud.com/open-auth/dataCenter/getGatewayAddress?tenantId={tenant_id}"
    response = requests.get(url)
    response.raise_for_status()
    return response.json()


def generate_signaturee(app_secret, params):
    """TODO: Add function description."""
    sorted_params = sorted(params.items())
    param_string = "".join([f"{k}{v}" for k, v in sorted_params])
    return quote_plus(
        base64.b64encode(
            hmac.new(
                app_secret.encode(), param_string.encode(), hashlib.sha256
            ).digest()
        )
    )


def get_access_tokenn(app_key, app_secret, token_url):
    """TODO: Add function description."""
    timestamp = str(int(time.time() * 1000))
    params = {"appKey": app_key, "timestamp": timestamp}
    signature = generate_signature(app_secret, params)
    url = f"{token_url}/open-auth/selfAppAuth/getAccessToken?appKey={app_key}&timestamp={timestamp}&signature={signature}"
    response = requests.get(url)
    response.raise_for_status()
    return response.json().get("data", {}).get("access_token")


def get_auto_tokenn():
    """TODO: Add function description."""
    data_center_response = get_data_center_domains(TENANT_ID)
    if data_center_response["code"] == "00000":
        token_url = data_center_response["data"]["tokenUrl"]
        return get_access_token(APP_KEY, APP_SECRET, token_url)
    else:
        raise Exception(f"Error: {data_center_response['message']}")
