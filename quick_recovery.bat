@echo off
chcp 65001 >nul
echo 🚀 开始YS-API V3.0项目恢复...
echo ====================================================

REM 步骤1：确定恢复点
echo 📍 步骤1：定位恢复点...
set GOOD_COMMIT=11a3e5c
echo 选择恢复点: %GOOD_COMMIT% (v0-shit-mountain)

REM 步骤2：备份当前有价值的文件
echo 💾 步骤2：备份有价值的文件...
if not exist recovery_backup mkdir recovery_backup
if not exist recovery_backup\scripts mkdir recovery_backup\scripts
if not exist recovery_backup\docs mkdir recovery_backup\docs
if not exist recovery_backup\config mkdir recovery_backup\config
if not exist recovery_backup\tools mkdir recovery_backup\tools

echo 备份核心脚本...
if exist analyze_dependencies.py copy /Y analyze_dependencies.py recovery_backup\ >nul 2>&1
if exist three_step_fix.py copy /Y three_step_fix.py recovery_backup\ >nul 2>&1
if exist Flake8修复完成报告.md copy /Y "Flake8修复完成报告.md" recovery_backup\docs\ >nul 2>&1
if exist quick_status_check.py copy /Y quick_status_check.py recovery_backup\ >nul 2>&1

echo 备份scripts目录的重要文件...
if exist scripts\port_manager*.py copy /Y scripts\port_manager*.py recovery_backup\scripts\ >nul 2>&1
if exist scripts\migrate_*.py copy /Y scripts\migrate_*.py recovery_backup\scripts\ >nul 2>&1

echo 备份配置文件...
if exist config xcopy /E /Y config recovery_backup\config\ >nul 2>&1

echo ✅ 备份完成

REM 步骤3：生成差异清单
echo 📋 步骤3：生成差异清单...
git diff %GOOD_COMMIT% HEAD > ai-changes.patch
git diff %GOOD_COMMIT% HEAD --name-only > changed-files.txt

for /f %%i in ('find /c /v "" changed-files.txt') do echo 差异文件数量: %%i
echo 前10个变更文件:
more +1 changed-files.txt | head -10

REM 步骤4：回到干净状态
echo 🔄 步骤4：回到干净代码状态...
echo 正在回滚到提交: %GOOD_COMMIT%

REM 获取当前分支名
for /f "tokens=*" %%i in ('git branch --show-current') do set CURRENT_BRANCH=%%i
echo 当前分支: %CURRENT_BRANCH%

REM 强制回到干净状态
git reset --hard %GOOD_COMMIT%

echo ✅ 已回到干净状态

REM 步骤5：选择性恢复有用文件
echo 📂 步骤5：恢复有价值的文件...

echo 恢复分析工具...
if exist recovery_backup\analyze_dependencies.py (
    copy /Y recovery_backup\analyze_dependencies.py . >nul 2>&1
    echo ✅ 恢复 analyze_dependencies.py
)
if exist recovery_backup\three_step_fix.py (
    copy /Y recovery_backup\three_step_fix.py . >nul 2>&1
    echo ✅ 恢复 three_step_fix.py
)
if exist recovery_backup\quick_status_check.py (
    copy /Y recovery_backup\quick_status_check.py . >nul 2>&1
    echo ✅ 恢复 quick_status_check.py
)

echo 恢复脚本目录...
if exist recovery_backup\scripts xcopy /E /Y recovery_backup\scripts\* scripts\ >nul 2>&1

echo 恢复配置文件...
if exist recovery_backup\config xcopy /E /Y recovery_backup\config\* config\ >nul 2>&1

REM 步骤6：清理和重建环境
echo 🧹 步骤6：清理环境...

REM 清理Python缓存
for /d /r . %%d in (__pycache__) do @if exist "%%d" rd /s /q "%%d" 2>nul
del /s /q *.pyc 2>nul

REM 清理日志
if exist logs rd /s /q logs 2>nul
if exist temp_cleanup rd /s /q temp_cleanup 2>nul

echo ✅ 环境清理完成

REM 步骤7：验证核心功能
echo 🔍 步骤7：验证核心功能...

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python环境正常
) else (
    echo ⚠️ Python未安装或不在PATH中
)

REM 测试核心脚本
if exist analyze_dependencies.py (
    echo 测试依赖分析器...
    python analyze_dependencies.py >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ 依赖分析器正常
    ) else (
        echo ⚠️ 依赖分析器有问题
    )
)

REM 检查启动脚本
if exist backend\start_server.py (
    echo ✅ 后端启动脚本存在
) else (
    echo ⚠️ 后端启动脚本缺失
)

if exist frontend\start_frontend.py (
    echo ✅ 前端启动脚本存在
) else (
    echo ⚠️ 前端启动脚本缺失
)

REM 步骤8：创建新的提交点
echo 💾 步骤8：创建恢复点...
git add .
git commit -m "项目恢复完成 - 回到v0-shit-mountain + 保留有价值更新"

echo.
echo 🎉 项目恢复完成！
echo ====================================================
echo 📋 恢复总结:
echo - 基础代码: 回到 v0-shit-mountain 状态
echo - 保留文件: 分析工具、脚本、配置等有价值文件
echo - 清理环境: 删除缓存和临时文件
echo - 验证功能: 核心功能测试完成
echo.
echo 🚀 下一步:
echo 1. 运行 python backend\start_server.py 启动后端
echo 2. 运行 python frontend\start_frontend.py 启动前端
echo 3. 测试主要功能是否正常
echo.
echo 备份位置: recovery_backup\ 目录
echo 差异文件: ai-changes.patch, changed-files.txt

pause
