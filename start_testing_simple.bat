@echo off
chcp 65001 > nul
echo ============================================================
echo YS-API V3.0 模块功能验证 - 自动启动
echo ============================================================
echo.

echo 正在启动后端服务（简化版本）...
start "YS-API后端服务" cmd /k "cd backend && python start_simple.py"
echo 后端服务地址: http://localhost:5000
echo.

echo 正在启动前端服务...
start "YS-API前端服务" cmd /k "cd frontend && python -m http.server 8080"
echo 前端服务地址: http://localhost:8080
echo.

echo 等待服务启动完成...
timeout /t 8 > nul
echo.

echo ============================================================
echo 开始15个模块功能验证
echo ============================================================
python scripts/module_functional_test.py --all

echo.
echo ============================================================
echo 验证完成！请检查上方结果
echo ============================================================
pause
