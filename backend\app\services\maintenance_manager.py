import asyncio
import os
import time

import schedule
import structlog

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 维护管理器
包含临时文件清理和缓存清理功能
"""


logger = structlog.get_logger()


class MaintenanceManager:
    """维护管理器"""

    def __init___(self):
    """TODO: Add function description."""
    self.running = False
    self.cleanup_task = None
    self.cache_cleanup_task = None
    self.temp_cleanup_task = None

    # 配置清理路径
    self.temp_dirs = ["logs/temp", "cache", "downloads", "uploads/temp"]

    self.cache_dirs = ["cache", "logs/cache", "frontend/static/cache"]

    # 文件保留时间（天）
    self.temp_file_retention_days = 7  # 临时文件保留7天
    self.cache_file_retention_days = 1  # 缓存文件保留1天
    self.log_file_retention_days = 30  # 日志文件保留30天

    # 清理时间配置
    self.cache_cleanup_time = "02:00"  # 每天凌晨2点清理缓存
    self.temp_cleanup_time = "03:00"  # 每周日凌晨3点清理临时文件
    self.log_cleanup_time = "04:00"  # 每周日凌晨4点清理日志

    logger.info("维护管理器初始化完成")

    async def start(self):
        """启动维护管理器"""
        if self.running:
            logger.warning("维护管理器已在运行")
            return

        logger.info("启动维护管理器")
        self.running = True

        # 启动调度器
        self._start_scheduler()

        # 启动清理任务
        self.cleanup_task = asyncio.create_task(self._run_cleanup_scheduler())

        logger.info("维护管理器启动成功")

    async def stop(self):
        """停止维护管理器"""
        if not self.running:
            return

        logger.info("停止维护管理器")
        self.running = False

        # 取消清理任务
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass

        logger.info("维护管理器已停止")

    def _start_scheduler(self):
        """启动调度器"""
        # 每天凌晨2点清理缓存
        schedule.every().day.at(self.cache_cleanup_time).do(
            self._schedule_cache_cleanup
        )

        # 每周日凌晨3点清理临时文件
        schedule.every().sunday.at(self.temp_cleanup_time).do(
            self._schedule_temp_cleanup
        )

        # 每周日凌晨4点清理日志
        schedule.every().sunday.at(
            self.log_cleanup_time).do(
            self._schedule_log_cleanup)

        logger.info("维护调度器启动成功")

    async def _run_cleanup_scheduler(self):
        """运行清理调度器"""
        while self.running:
            try:
                # 运行待执行的任务
                schedule.run_pending()
                await asyncio.sleep(60)  # 每分钟检查一次

            except asyncio.CancelledError:
                break
            except Exception:
                logger.error("清理调度器运行异常", error=str(e))
                await asyncio.sleep(60)

    def _schedule_cache_cleanup(self):
        """调度缓存清理"""
        asyncio.create_task(self.cleanup_cache())

    def _schedule_temp_cleanup(self):
        """调度临时文件清理"""
        asyncio.create_task(self.cleanup_temp_files())

    def _schedule_log_cleanup(self):
        """调度日志清理"""
        asyncio.create_task(self.cleanup_log_files())

    async def cleanup_cache(self):
        """清理缓存文件"""
        logger.info("开始清理缓存文件")

        try:
            total_cleaned = 0
            total_size_cleaned = 0

            for cache_dir in self.cache_dirs:
                if os.path.exists(cache_dir):
                    cleaned_count, cleaned_size = await self._cleanup_directory(
                        cache_dir, self.cache_file_retention_days, "缓存"
                    )
                    total_cleaned += cleaned_count
                    total_size_cleaned += cleaned_size

            logger.info(
                "缓存清理完成",
                files_cleaned=total_cleaned,
                size_cleaned_mb=round(total_size_cleaned / 1024 / 1024, 2),
            )

        except Exception:
            logger.error("缓存清理失败", error=str(e))

    async def cleanup_temp_files(self):
        """清理临时文件"""
        logger.info("开始清理临时文件")

        try:
            total_cleaned = 0
            total_size_cleaned = 0

            for temp_dir in self.temp_dirs:
                if os.path.exists(temp_dir):
                    cleaned_count, cleaned_size = await self._cleanup_directory(
                        temp_dir, self.temp_file_retention_days, "临时"
                    )
                    total_cleaned += cleaned_count
                    total_size_cleaned += cleaned_size

            logger.info(
                "临时文件清理完成",
                files_cleaned=total_cleaned,
                size_cleaned_mb=round(total_size_cleaned / 1024 / 1024, 2),
            )

        except Exception:
            logger.error("临时文件清理失败", error=str(e))

    async def cleanup_log_files(self):
        """清理日志文件"""
        logger.info("开始清理日志文件")

        try:
            log_dir = "logs"
            if os.path.exists(log_dir):
                cleaned_count, cleaned_size = await self._cleanup_directory(
                    log_dir,
                    self.log_file_retention_days,
                    "日志",
                    exclude_patterns=[".log", ".json"],
                )

                logger.info(
                    "日志文件清理完成",
                    files_cleaned=cleaned_count,
                    size_cleaned_mb=round(cleaned_size / 1024 / 1024, 2),
                )

        except Exception:
            logger.error("日志文件清理失败", error=str(e))

    async def _cleanup_directory(
        self,
        directory: str,
        retention_days: int,
        file_type: str,
        exclude_patterns: List[str] = None,
    ) -> tuple:
        """清理指定目录中的过期文件"""
        cleaned_count = 0
        cleaned_size = 0
        cutoff_time = time.time() - (retention_days * 24 * 3600)

        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)

                    # 检查是否在排除列表中
                    if exclude_patterns:
                        should_exclude = False
                        for pattern in exclude_patterns:
                            if pattern in file:
                                should_exclude = True
                                break
                        if should_exclude:
                            continue

                    try:
                        # 检查文件修改时间
                        file_mtime = os.path.getmtime(file_path)
                        if file_mtime < cutoff_time:
                            # 删除过期文件
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            cleaned_count += 1
                            cleaned_size += file_size

                            logger.debug(f"删除过期{file_type}文件", file=file_path)

                    except Exception:
                        logger.warning(
                            f"删除{file_type}文件失败", file=file_path, error=str(e)
                        )

            if cleaned_count > 0:
                logger.info(
                    f"{file_type}文件清理完成",
                    directory=directory,
                    files_cleaned=cleaned_count,
                    size_cleaned_mb=round(cleaned_size / 1024 / 1024, 2),
                )

        except Exception:
            logger.error(
                f"清理{file_type}目录失败",
                directory=directory,
                error=str(e))

        return cleaned_count, cleaned_size

    async def manual_cleanup_cache(self):
        """手动清理缓存"""
        logger.info("执行手动缓存清理")
        await self.cleanup_cache()

    async def manual_cleanup_temp(self):
        """手动清理临时文件"""
        logger.info("执行手动临时文件清理")
        await self.cleanup_temp_files()

    async def manual_cleanup_logs(self):
        """手动清理日志文件"""
        logger.info("执行手动日志文件清理")
        await self.cleanup_log_files()

    async def get_cleanup_status(self) -> Dict:
        """获取清理状态"""
        try:
            # 计算各目录大小
            cache_size = await self._get_directory_size(self.cache_dirs)
            temp_size = await self._get_directory_size(self.temp_dirs)
            log_size = await self._get_directory_size(["logs"])

            return {
                "success": True,
                "data": {
                    "cache_size_mb": round(cache_size / 1024 / 1024, 2),
                    "temp_size_mb": round(temp_size / 1024 / 1024, 2),
                    "log_size_mb": round(log_size / 1024 / 1024, 2),
                    "total_size_mb": round(
                        (cache_size + temp_size + log_size) / 1024 / 1024, 2
                    ),
                    "next_cache_cleanup": (
                        schedule.next_run().strftime("%Y-%m-%d %H:%M:%S")
                        if schedule.jobs
                        else "未设置"
                    ),
                    "next_temp_cleanup": (
                        schedule.next_run().strftime("%Y-%m-%d %H:%M:%S")
                        if schedule.jobs
                        else "未设置"
                    ),
                    "next_log_cleanup": (
                        schedule.next_run().strftime("%Y-%m-%d %H:%M:%S")
                        if schedule.jobs
                        else "未设置"
                    ),
                },
                "message": "获取清理状态成功",
            }

        except Exception:
            logger.error("获取清理状态失败", error=str(e))
            return {
                "success": False,
                "data": {},
                "message": f"获取清理状态失败: {str(e)}",
            }

    async def _get_directory_size(self, directories: List[str]) -> int:
        """计算目录总大小"""
        total_size = 0

        for directory in directories:
            if os.path.exists(directory):
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        try:
                            file_path = os.path.join(root, file)
                            total_size += os.path.getsize(file_path)
                        except Exception:
                            continue

        return total_size

    async def health_check(self) -> Dict:
        """健康检查"""
        try:
            status = await self.get_cleanup_status()

            return {
                "healthy": True,
                "status": "running" if self.running else "stopped",
                "details": status.get("data", {}),
            }

        except Exception:
            logger.error("维护管理器健康检查失败", error=str(e))
            return {"healthy": False, "status": "error", "error": str(e)}


# 全局维护管理器实例
maintenance_manager = MaintenanceManager()


def get_maintenance_manager() -> MaintenanceManager:
    """获取维护管理器实例"""
    return maintenance_manager
