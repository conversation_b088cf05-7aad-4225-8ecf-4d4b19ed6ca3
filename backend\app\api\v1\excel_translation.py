import os
import re
import sys
import tempfile
from pathlib import Path

import structlog
from batch_pretranslation import BatchPreTranslator
from fastapi.responses import JSONResponse

from ...services.excel_field_matcher_pretranslated import \
    ExcelFieldMatcherPreTranslated
from ...services.field_config_service import FieldConfigService

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Excel翻译匹配API接口
"""


logger = structlog.get_logger()

router = APIRouter(prefix="/excel", tags=["Excel翻译"])


@router.post("/translate-match")
async def translate_match_excel(
    excel_file: UploadFile = File(...),
    module_name: str = Form(...),
    match_mode: str = Form(default="auto"),
) -> JSONResponse:
    """
    Excel文件翻译匹配

    Args:
        excel_file: 上传的Excel文件
        module_name: 业务模块名称
        match_mode: 匹配模式 (auto/chinese/english)

    Returns:
        匹配结果和统计信息
    """
    try:
        logger.info(f"开始Excel翻译匹配: {excel_file.filename}, 模块: {module_name}")

        # 验证文件类型
        if not excel_file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(
                status_code=400, detail="不支持的文件格式，请上传Excel文件"
            )

        # 验证文件大小 (10MB限制)
        if excel_file.size > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=400, detail="文件过大，请上传小于10MB的文件"
            )

        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
            content = await excel_file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # 获取API字段配置
            config_service = FieldConfigService()
            api_fields = config_service.get_module_fields(module_name)

            if not api_fields:
                raise HTTPException(
                    status_code=404, detail=f"未找到模块 {module_name} 的字段配置"
                )

            # 使用预翻译匹配器
            matcher = ExcelFieldMatcherPreTranslated()

            # 执行匹配
            matches = matcher.match_excel_fields_pretranslated(
                temp_file_path, api_fields, module_name
            )

            # 分析匹配质量
            quality_analysis = matcher.analyze_match_quality_pretranslated(
                matches)

            # 格式化结果
            result_matches = []
            for excel_field, match_result in matches.items():
                result_matches.append(
                    {
                        "excel_field": excel_field,
                        "api_field": match_result.api_field,
                        "chinese_name": match_result.chinese_name,
                        "match_type": match_result.match_type,
                        "confidence": match_result.confidence,
                    }
                )

            # 按置信度排序
            result_matches.sort(key=lambda x: x['confidence'], reverse=True)

            logger.info(f"Excel翻译匹配完成: 匹配 {len(result_matches)} 个字段")

            return JSONResponse(
                {
                    "success": True,
                    "message": "Excel翻译匹配完成",
                    "data": {
                        "module_name": module_name,
                        "excel_filename": excel_file.filename,
                        "match_mode": match_mode,
                        "matches": result_matches,
                        "statistics": quality_analysis,
                    },
                }
            )

        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except HTTPException:
        raise
    except Exception:
        logger.error(f"Excel翻译匹配失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")


@router.post("/analyze-fields")
async def analyze_excel_fields(
        excel_file: UploadFile = File(...)) -> JSONResponse:
    """
    分析Excel文件字段

    Args:
        excel_file: 上传的Excel文件

    Returns:
        字段分析结果
    """
    try:
        logger.info(f"开始分析Excel字段: {excel_file.filename}")

        # 验证文件类型
        if not excel_file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(
                status_code=400, detail="不支持的文件格式，请上传Excel文件"
            )

        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
            content = await excel_file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:

            # 读取Excel字段
            df = pd.read_excel(temp_file_path, nrows=0)
            excel_fields = df.columns.tolist()

            # 分析字段特征
            field_analysis = {
                "total_fields": len(excel_fields),
                "fields": excel_fields,
                "language_detection": {},
                "field_types": {
                    "chinese": [],
                    "english": [],
                    "mixed": [],
                    "numeric": [],
                },
            }

            # 检测字段语言类型

            for field in excel_fields:
                if re.search(r'[\u4e00-\u9fff]', field):
                    field_analysis["field_types"]["chinese"].append(field)
                elif re.search(r'^[a-zA-Z][a-zA-Z0-9_]*$', field):
                    field_analysis["field_types"]["english"].append(field)
                elif re.search(r'\d', field):
                    field_analysis["field_types"]["numeric"].append(field)
                else:
                    field_analysis["field_types"]["mixed"].append(field)

            # 语言检测统计
            total = len(excel_fields)
            field_analysis["language_detection"] = {
                "chinese_ratio": (
                    len(field_analysis["field_types"]["chinese"]) / total
                    if total > 0
                    else 0
                ),
                "english_ratio": (
                    len(field_analysis["field_types"]["english"]) / total
                    if total > 0
                    else 0
                ),
                "mixed_ratio": (
                    len(field_analysis["field_types"]["mixed"]) / total
                    if total > 0
                    else 0
                ),
                "numeric_ratio": (
                    len(field_analysis["field_types"]["numeric"]) / total
                    if total > 0
                    else 0
                ),
            }

            logger.info(f"Excel字段分析完成: {total} 个字段")

            return JSONResponse(
                {
                    "success": True,
                    "message": "Excel字段分析完成",
                    "data": field_analysis,
                }
            )

        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except HTTPException:
        raise
    except Exception:
        logger.error(f"Excel字段分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")


@router.get("/modules")
async def get_available_modules() -> JSONResponse:
    """
    获取可用的业务模块列表

    Returns:
        模块列表
    """
    try:
        config_service = FieldConfigService()
        modules = config_service.get_available_modules()

        return JSONResponse(
            {
                "success": True,
                "message": "获取模块列表成功",
                "data": {"modules": modules},
            }
        )

    except Exception:
        logger.error(f"获取模块列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")


@router.post("/config/pretranslate")
async def run_batch_pretranslation() -> JSONResponse:
    """
    执行批量预翻译

    Returns:
        预翻译结果
    """
    try:
        logger.info("开始批量预翻译")

        # 导入批量预翻译脚本

        # 动态导入批量预翻译模块
        project_root = Path(__file__).parent.parent.parent.parent.parent
        sys.path.insert(0, str(project_root))

        # 执行批量翻译
        translator = BatchPreTranslator()
        results = translator.run_batch_translation()

        logger.info(f"批量预翻译完成: 处理 {results['processed_files']} 个文件")

        return JSONResponse(
            {
                "success": True,
                "message": "批量预翻译完成",
                "data": {
                    "processed_files": results['processed_files'],
                    "total_fields": results['total_fields'],
                    "translated_fields": results['translated_fields'],
                    "failed_translations": results['failed_translations'],
                    "translation_rate": (
                        results['translated_fields'] / results['total_fields']
                        if results['total_fields'] > 0
                        else 0
                    ),
                    "updated_configs": results['updated_configs'],
                },
            }
        )

    except Exception:
        logger.error(f"批量预翻译失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"预翻译失败: {str(e)}")


@router.get("/translation-status")
async def get_translation_status() -> JSONResponse:
    """
    获取翻译状态

    Returns:
        翻译状态信息
    """
    try:
        config_service = FieldConfigService()

        # 统计预翻译状态
        status_info = {
            "total_modules": 0,
            "pretranslated_modules": 0,
            "total_fields": 0,
            "translated_fields": 0,
            "modules_status": [],
        }

        modules = config_service.get_available_modules()
        status_info["total_modules"] = len(modules)

        for module in modules:
            module_fields = config_service.get_module_fields(module["value"])
            module_status = {
                "module_name": module["value"],
                "display_name": module["label"],
                "total_fields": len(module_fields),
                "translated_fields": 0,
                "has_pretranslation": False,
            }

            # 检查预翻译状态
            for field_name, field_info in module_fields.items():
                status_info["total_fields"] += 1
                if field_info.get('chinese_name') and field_info.get(
                    'auto_translated', False
                ):
                    module_status["translated_fields"] += 1
                    status_info["translated_fields"] += 1

            module_status["has_pretranslation"] = module_status["translated_fields"] > 0
            if module_status["has_pretranslation"]:
                status_info["pretranslated_modules"] += 1

            status_info["modules_status"].append(module_status)

        return JSONResponse(
            {"success": True, "message": "获取翻译状态成功", "data": status_info}
        )

    except Exception:
        logger.error(f"获取翻译状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")
