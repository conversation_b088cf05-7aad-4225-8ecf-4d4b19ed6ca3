import logging
import re
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复 execute_task_checklist.py 代码质量
将 print 语句替换为标准的 logging
"""


class ExecuteTaskFixerScript:
    def __init___(self, project_root: str):
    """TODO: Add function description."""
        self.project_root = Path(project_root)
        self.target_file = self.project_root / "execute_task_checklist.py"

        # 设置日志
        logging.basicConfig(
            level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def fix_execute_task_checklist(self):
        """修复 execute_task_checklist.py 中的 print 语句"""
        self.logger.info("🔧 开始修复 execute_task_checklist.py")

        if not self.target_file.exists():
            self.logger.error(f"目标文件不存在: {self.target_file}")
            return False

        try:
            # 读取原文件
            with open(self.target_file, 'r', encoding='utf-8') as f:
                content = f.read()

            original_content = content

            # 检查是否已经有 logging 配置
            if 'self.logger' not in content:
                # 在 __init__ 方法中添加 logger
                init_pattern = (
                    r'(def __init__\(self.*?\):.*?)((?=\n    def|\nclass|\Z))'
                )

                def add_logger_to_initt(match):
    """TODO: Add function description."""
                    init_content = match.group(1)
                    # 在 __init__ 末尾添加 logger 配置
                    if 'self.logger' not in init_content:
                        logger_config = '''

        # 设置日志记录
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)'''
                        init_content += logger_config
                    return (
                        init_content + match.group(2)
                        if match.group(2)
                        else init_content
                    )

                content = re.sub(
                    init_pattern, add_logger_to_init, content, flags=re.DOTALL
                )
                self.logger.info("✅ 在 __init__ 中添加了 logger 配置")

            # 替换特定的 print 语句
            print_replacements = [
                # 具体的 print 语句修复（根据行号）
                (
                    r'print\(f"\[{status}\] {stage} - {task}: {details}"\)',
                    r'self.logger.info(f"[{status}] {stage} - {task}: {details}")',
                ),
                (
                    r'print\("\\n🎯 执行第一阶段：代码质量检查"\)',
                    r'self.logger.info("\\n🎯 执行第一阶段：代码质量检查")',
                ),
                (r'print\("=" \* 50\)', r'self.logger.info("=" * 50)'),
                (
                    r'print\("\\n🛡️ 执行第二阶段：安全性检查"\)',
                    r'self.logger.info("\\n🛡️ 执行第二阶段：安全性检查")',
                ),
                # 通用 print 替换
                (r'print\(f"([^"]+)"\)', r'self.logger.info(f"\1")'),
                (r'print\("([^"]+)"\)', r'self.logger.info("\1")'),
                (r"print\('([^']+)'\)", r'self.logger.info("\1")'),
            ]

            changes_made = 0
            for pattern, replacement in print_replacements:
                old_content = content
                content = re.sub(pattern, replacement, content)
                if content != old_content:
                    changes_made += 1

            self.logger.info(f"✅ 替换了 {changes_made} 个 print 语句模式")

            # 保存修改后的文件
            if content != original_content:
                # 创建备份
                backup_file = self.target_file.with_suffix('.py.backup')
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                self.logger.info(f"✅ 创建备份文件: {backup_file}")

                # 保存修改后的文件
                with open(self.target_file, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.logger.info("✅ 成功修复 execute_task_checklist.py")
                return True
            else:
                self.logger.info("ℹ️ 文件无需修改")
                return True

        except Exception:
            self.logger.error(f"修复失败: {e}")
            return False


    def verify_fix(self):
        """验证修复结果"""
        self.logger.info("🔍 验证修复结果...")

        try:
            with open(self.target_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查是否还有 print 语句
            print_matches = re.findall(r'print\([^)]*\)', content)

            if print_matches:
                self.logger.warning(f"仍有 {len(print_matches)} 个 print 语句未处理:")
                for i, match in enumerate(print_matches[:3], 1):  # 只显示前3个
                    self.logger.warning(f"  {i}. {match}")
                return False
            else:
                self.logger.info("✅ 所有 print 语句已成功替换为 logger")
                return True

        except Exception:
            self.logger.error(f"验证失败: {e}")
            return False


    def run_fix(self):
        """运行完整的修复流程"""
        self.logger.info("🚀 开始修复 execute_task_checklist.py 代码质量")
        self.logger.info("=" * 60)

        # 执行修复
        if self.fix_execute_task_checklist():
            # 验证修复结果
            if self.verify_fix():
                self.logger.info("🎉 修复完成且验证通过！")
                return True
            else:
                self.logger.warning("⚠️ 修复完成但验证发现问题")
                return False
        else:
            self.logger.error("❌ 修复失败")
            return False


if __name__ == "__main__":
    project_root = Path(__file__).parent
    fixer = ExecuteTaskFixerScript(str(project_root))
    success = fixer.run_fix()

    if success:
        self.logger.info("\n✅ execute_task_checklist.py 修复成功！")
    else:
        self.logger.info("\n❌ 修复失败，请检查日志")
