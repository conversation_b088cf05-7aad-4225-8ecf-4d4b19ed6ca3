委外订单列表查询
发布时间:2024-07-20 05:17:04
可以根据输入的委外组织，订单状态，单据日期等过滤参数信息，可以查询到委外订单列表的内容，包括审核日期，委外部门，产品行ID等信息。

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	开放API: 动态域名，获取方式详见 获取租户所在数据中心域名
集成API: 详细域名信息，请见 连接配置
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/mfg/subcontractorder/list
请求方式	POST
ContentType	application/json
应用场景	开放API/集成API
API类别	列表查询
事务和幂等性	无
限流次数	60次/分钟

多语	不支持
2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
pageIndex	int	否	是	页号 默认值:1    示例: 1    默认值: 1
pageSize	int	否	是	每页行数 默认值:10    示例: 10    默认值: 10
code	string	否	否	委外订单号    示例: WWDD202105010001
status	string	否	否	订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工    示例: 0
transTypeId	string	是	否	交易类型    示例: ["1866605942578527"]
orgId	string	是	否	组织    示例: ["1866605942198782"]
subcontractVendorId	string	是	否	委外商    示例: ["1866605942197864"]
OrderProduct!productId	long	是	否	物料id    示例: [1866605942115973]
OrderProductSubcontract!deliveryDate	string	否	否	交货日期（区间，格式2021-03-02|2021-03-02 23:59:59）    示例: 2021-03-02|2021-03-02 23:59:59
vouchdate	string	否	否	单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）    示例: 2021-03-02|2021-03-02 23:59:59
isShowMaterial	boolean	否	否	是否展示材料:true-是,false-否    示例: false    默认值: false
simple	object	否	否	simple
open_pubts_begin	string	否	否	时间戳，开始时间    示例: 2022-04-01 00:00:00
open_pubts_end	string	否	否	时间戳，结束时间    示例: 2022-04-20 00:00:00
orderProduct.wbs	string	否	否	wbs    示例: 1866605942198447
orderProduct.projectId	string	否	否	项目Id    示例: 1866605942178545
orderProduct.activity	number
小数位数:0,最大长度:32	否	否	活动id    示例: 1866605427458631
3. 请求示例
Url: /yonbip/mfg/subcontractorder/list?access_token=访问令牌
Body: {
	"pageIndex": 1,
	"pageSize": 10,
	"code": "WWDD202105010001",
	"status": "0",
	"transTypeId": [
		"1866605942578527"
	],
	"orgId": [
		"1866605942198782"
	],
	"subcontractVendorId": [
		"1866605942197864"
	],
	"OrderProduct!productId": [
		1866605942115973
	],
	"OrderProductSubcontract!deliveryDate": "2021-03-02|2021-03-02 23:59:59",
	"vouchdate": "2021-03-02|2021-03-02 23:59:59",
	"isShowMaterial": false,
	"simple": {
		"open_pubts_begin": "2022-04-01 00:00:00",
		"open_pubts_end": "2022-04-20 00:00:00",
		"orderProduct.wbs": "1866605942198447",
		"orderProduct.projectId": "1866605942178545",
		"orderProduct.activity": 1866605427458631
	}
}
4. 返回值参数
名称	类型	数组	描述
code	long	否	返回码，成功时返回200
message	string	否	接口返回信息
data	object	否	接口返回信息
salesOrgId_name	string	否	销售组织名称
pageIndex	long	否	当前页
pageSize	long	否	页大小
recordCount	long	否	记录总数
recordList	object	是	返回数据对象
id	long	否	委外订单Id
transTypeId	string	否	交易类型ID
isWfControlled	boolean	否	是否审批流控制：false-否，true-是
orgName	string	否	组织
status	long	否	订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工
isHold	boolean	否	挂起状态：false-否，true-是
code	string	否	委外订单号
verifystate	long	否	审批状态：0-开立，1-已提交，2-已审批，-1-驳回
vouchdate	string	否	单据日期
creatorId	long	否	创建人Id
creator	string	否	创建人
orgId	string	否	组织Id
transTypeName	string	否	交易类型名称
transTypeCode	string	否	交易类型编码
subcontractVendorId	string	否	委外商Id
subcontractVendorName	string	否	委外商
pubts	string	否	时间戳
createTime	string	否	创建时间
orderProduct_id	long	否	订单产品行Id
OrderProduct_productId	long	否	物料Id
OrderProduct_materialName	string	否	物料名称
OrderProduct_materialCode	string	否	制造物料编码
orderProduct_orderSubcontractProduct_subcontractUnitId	long	否	委外单位Id
orderProduct_orderSubcontractProduct_priceUnitId	long	否	计价单位Id
mainUnitPrecision	long	否	主计量精度
priceUnitPrecision	long	否	计价单位精度
subcontractUnitPrecision	long	否	委外单位精度
OrderProduct_sourceType	string	否	来源单据类型：1-无来源，2-计划订单，3-销售订单，4-生产订单，5-完工报告
OrderProduct_deliveryDate	string	否	交货日期
OrderProduct_subcontractQuantityMU	double	否	委外数量
OrderProduct_subcontractQuantitySU	double	否	委外件数
OrderProduct_subcontractQuantityPU	double	否	计价数量
OrderProduct_changeRate	long	否	换算率
OrderProduct_isHold	boolean	否	挂起状态：false-否，true-是
OrderProduct_mainUnitName	string	否	主计量
OrderProduct_subcontractUnitName	string	否	委外单位
OrderProduct_priceUnitName	string	否	计价单位
OrderProduct_bomId	long	否	物料清单Id
OrderProduct_version	string	否	BOM版本
freeCharacteristics	特征组
po.order.OrderProduct	否	自由项特征组
productDefineDts	特征组
po.order.OrderProduct	否	产品行自定义项特征组
defineDts	特征组
po.order.Order	否	自定义项特征组
out_sys_id	string	否	外部来源线索
out_sys_code	string	否	外部来源编码
out_sys_version	string	否	外部来源版本
out_sys_type	string	否	外部来源类型
OrderProduct_out_sys_lineid	string	否	外部来源行
OrderProduct_out_sys_rowno	string	否	外部来源行号
orderMaterial	object	是	材料信息，入参isShowMaterial为true时显示
isWholeSet	boolean	否	齐套标识：false-否，true-是
id	long	否	订单材料Id
lineNo	Decimal	否	行号
productCode	string	否	物料编码，特征租户返回
productName	string	否	物料名称，特征租户返回
productId	long	否	物料id
mainUnit	long	否	主单位id
mainUnitName	string	否	主单位
mainUnitPrecision	int	否	主单位精度
stockUnitName	string	否	库存单位
stockUnitId	long	否	库存单位id
stockUnitPrecision	int	否	库存单位精度
changeRate	Decimal	否	换算率
changeType	int	否	换算方式: 0-固定换算，1-浮动换算
recipientQuantity	Decimal	否	应领数量
auxiliaryRecipientQuantity	Decimal	否	应领件数
bomUnitName	string	否	物料清单单位
bomUnitId	long	否	物料清单单位id
bomUnitPrecision	int	否	物料清单单位精度
bomUnitChangeRate	Decimal	否	物料清单单位换算率
bomAuxiliaryRecipientQty	Decimal	否	应领件数（bom单位）
bomId	long	否	物料清单Id
bomMaterialId	long	否	物料清单子件行id
numeratorQuantity	Decimal	否	分子用量
denominatorQuantity	Decimal	否	母件底数
scrap	Decimal	否	子件损耗率
mustLossQuantity	Decimal	否	固定损耗
bomUnitUseQuantity	Decimal	否	单位使用量（bom单位）
mainNumeratorQuantity	Decimal	否	分子用量（主单位）
mainDenominatorQuantity	Decimal	否	母件底数（主单位）
unitUseQuantity	Decimal	否	单位使用量
fixedQuantity	int	否	固定用量：0-否，1-是
orderProductId	long	否	产品行id
orgId	string	否	库存组织id
orgName	string	否	库存组织
warehouseId	long	否	供应仓库id
warehouseName	string	否	供应仓库
requirementDate	DateTime	否	需求日期
verificationBy	string	否	核销依据：0-按订单应发核销 1-按不含损耗的BOM用量核销 2-按含损耗的BOM用量核销
subcontractSupplyType	int	否	委外供应方式：0-我方
supplyType	string	否	供应方式: 0-领用 1-入库倒冲 2-不发料
supDirectShip	boolean	否	供应商直运
doubleReplenish	int	否	补货倍量：0-否，1-是
replenishAdjustQuantity	Decimal	否	补货调整数量
auxiliaryReplenishAdjustQuantity	Decimal	否	补货调整件数
excessAppliedQty	Decimal	否	超额申请数量
auxiliaryExcessAppliedQty	Decimal	否	超额申请件数
materialDefineDts	特征组
po.order.OrderMaterial	否	自定义特征组
XS11	string	否	需求分类号test
id	string	否	特征id,主键,新增时无需填写,修改时必填
freeCharacteristics	特征组
po.order.OrderMaterial	否	自由项特征组
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
orderSubcontract_tradePath	long	否	贸易路径id
orderSubcontract_tradePathName	string	否	贸易路径
orderSubcontractProduct_tradePath	long	否	产品行贸易路径id
orderSubcontractProduct_tradePathName	string	否	产品行贸易路径
orderSubcontractProduct_cooperateDocNo	string	否	协同单号
orderSubcontractProduct_cooperateDocId	long	否	协同单id
orderSubcontractProduct_cooperateLineNo	string	否	协同单行号
orderSubcontractProduct_cooperateLineId	long	否	协同单行id
orderSubcontract_tcOrgId	string	否	产品行收票组织id
orderSubcontractProduct_tcOrgIdSon_name	string	否	产品行收票组织名称
orderSubcontract_tcOrgAccount	int	否	产品行跨组织委外结算: 1-是;0-否
wbs	string	否	wbs
wbsCode	string	否	WBS任务编码
wbsName	string	否	WBS任务名称
activity	number
小数位数:0,最大长度:36	否	活动
activityCode	string	否	活动编码
activityTaskName	string	否	活动名称
OrderProduct_projectId	string	否	项目id
OrderProduct_projectCode	string	否	项目编码
OrderProduct_projectName	string	否	项目
sumRecordList	object	是	合计字段集合
OrderProduct_subcontractQuantityMU	double	否	委外数量
OrderProduct_subcontractQuantitySU	double	否	委外件数
OrderProduct_subcontractQuantityPU	double	否	计价数量
mainUnitPrecision	long	否	主计量精度
pageCount	long	否	总页数
beginPageIndex	long	否	开始页码
endPageIndex	long	否	结束页码
pubts	string	否	时间戳
synSourceOrg	string	否	协同来源组织id
synSourceOrg	string	否	协同来源组织id
5. 正确返回示例
{
	"code": 200,
	"message": "操作成功",
	"data": {
		"pageIndex": 1,
		"pageSize": 20,
		"recordCount": 0,
		"recordList": [
			{
				"id": 2184924571914496,
				"transTypeId": "1248018423173517",
				"isWfControlled": false,
				"orgName": "L工厂1",
				"status": 0,
				"isHold": false,
				"code": "SCDD20210324000003",
				"verifystate": 0,
				"vouchdate": "2021-03-24 00:00:00",
				"creatorId": 1979891486839040,
				"creator": "18510959384",
				"orgId": "1866605942198528",
				"transTypeName": "标准生产",
				"transTypeCode": "PO-001",
				"subcontractVendorId": "1870534089855232",
				"subcontractVendorName": "供应商",
				"pubts": "2021-03-24 11:40:13",
				"createTime": "2021-03-24 11:40:12",
				"orderProduct_id": 2184924571914497,
				"OrderProduct_productId": 2061736079708416,
				"OrderProduct_materialName": "自行车",
				"OrderProduct_materialCode": "jq01000001",
				"orderProduct_orderSubcontractProduct_subcontractUnitId": 1869676091724032,
				"orderProduct_orderSubcontractProduct_priceUnitId": 1869676091724032,
				"mainUnitPrecision": 3,
				"priceUnitPrecision": 3,
				"subcontractUnitPrecision": 4,
				"OrderProduct_sourceType": "1",
				"OrderProduct_deliveryDate": "2021-03-24 00:00:00",
				"OrderProduct_subcontractQuantityMU": 120,
				"OrderProduct_subcontractQuantitySU": 120,
				"OrderProduct_subcontractQuantityPU": 120,
				"OrderProduct_changeRate": 1,
				"OrderProduct_isHold": false,
				"OrderProduct_mainUnitName": "件",
				"OrderProduct_subcontractUnitName": "件",
				"OrderProduct_priceUnitName": "件",
				"OrderProduct_bomId": 2173985857769728,
				"OrderProduct_version": "A1",
				"freeCharacteristics": {},
				"productDefineDts": {},
				"defineDts": {},
				"out_sys_id": "100000",
				"out_sys_code": "WBWWDD001",
				"out_sys_version": "V1.0.0",
				"out_sys_type": "Type001",
				"OrderProduct_out_sys_lineid": "10010",
				"OrderProduct_out_sys_rowno": "10",
				"orderMaterial": [
					{
						"isWholeSet": false,
						"id": NaN,
						"lineNo": 10,
						"productCode": "788044",
						"productName": "成套领料-材料001",
						"productId": 1696909495433691137,
						"mainUnit": 2652431423640064,
						"mainUnitName": "千克",
						"mainUnitPrecision": 1,
						"stockUnitName": "千克",
						"stockUnitId": 2652431423640064,
						"stockUnitPrecision": 6,
						"changeRate": 1,
						"changeType": 0,
						"recipientQuantity": 1500,
						"auxiliaryRecipientQuantity": 1500,
						"bomUnitName": "千克",
						"bomUnitId": 2652431423640064,
						"bomUnitPrecision": 6,
						"bomUnitChangeRate": 1,
						"bomAuxiliaryRecipientQty": 1500,
						"bomId": 1699890469018796032,
						"bomMaterialId": 1699890469018796033,
						"numeratorQuantity": 1,
						"denominatorQuantity": 1,
						"scrap": 0,
						"mustLossQuantity": 0,
						"bomUnitUseQuantity": 1,
						"mainNumeratorQuantity": 1,
						"mainDenominatorQuantity": 1,
						"unitUseQuantity": 1,
						"fixedQuantity": 0,
						"orderProductId": 1700060163165650954,
						"orgId": "1478105315702997001",
						"orgName": "测试qss质检组织",
						"warehouseId": 1504817297772511237,
						"warehouseName": "委外仓qss",
						"requirementDate": "2023-04-28 00:00:00",
						"verificationBy": "0",
						"subcontractSupplyType": 0,
						"supplyType": "0",
						"supDirectShip": false,
						"doubleReplenish": 0,
						"replenishAdjustQuantity": 1500,
						"auxiliaryReplenishAdjustQuantity": 1500,
						"excessAppliedQty": 0,
						"auxiliaryExcessAppliedQty": 0,
						"materialDefineDts": {
							"XS11": "",
							"id": ""
						},
						"freeCharacteristics": {
							"XS15": "",
							"XXX0111": "",
							"id": ""
						}
					}
				],
				"orderSubcontract_tradePath": 1852042374122307592,
				"orderSubcontract_tradePathName": "01路径",
				"orderSubcontractProduct_tradePath": 1852042374122307592,
				"orderSubcontractProduct_tradePathName": "1852042374122307592",
				"orderSubcontractProduct_cooperateDocNo": "UO-**************",
				"orderSubcontractProduct_cooperateDocId": 1852955192105369602,
				"orderSubcontractProduct_cooperateLineNo": "10",
				"orderSubcontractProduct_cooperateLineId": 1852955192105369603,
				"orderSubcontract_tcOrgId": "1631969031461273603",
				"orderSubcontractProduct_tcOrgIdSon_name": "收票组织",
				"orderSubcontract_tcOrgAccount": 0,
				"wbs": "",
				"wbsCode": "",
				"wbsName": "",
				"activity": 0,
				"activityCode": "",
				"activityTaskName": "",
				"OrderProduct_projectId": "",
				"OrderProduct_projectCode": "",
				"OrderProduct_projectName": ""
			}
		],
		"sumRecordList": [
			{
				"OrderProduct_subcontractQuantityMU": 186.8874,
				"OrderProduct_subcontractQuantitySU": 120,
				"OrderProduct_subcontractQuantityPU": 120,
				"mainUnitPrecision": 1
			}
		],
		"pageCount": 0,
		"beginPageIndex": 1,
		"endPageIndex": 0,
		"pubts": "2021-03-24 15:11:10"
	}
}
6. 错误返回码
错误码	错误信息	描述
999	取决于错误类型，不同错误信息不同	
7. 错误返回示例
{
 "code": "999",
 "message": "非法的时间： 11111"
}