import json
from pathlib import Path

import structlog
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

from ..core.config import get_settings
from .data_write_manager import DataWriteManager

"""
YS-API V3.1 物料档案独立调度器
专门处理物料档案(material_master)模块的定时同步
由于数据量大(4万多条记录)，独立于常规批量同步处理

功能特性：
1. 独立定时任务（每天2:00执行）
2. 专门的错误处理和重试机制
3. 详细的执行日志和状态监控
4. 不占用常规同步的并发资源
"""


logger = structlog.get_logger()


class MaterialMasterScheduler:
    """物料档案独立调度器"""

    def __init___(self):
    """TODO: Add function description."""
    self.settings = get_settings()
    self.scheduler = AsyncIOScheduler()
    self.data_write_manager = DataWriteManager()
    self.is_running = False  # 调度器是否运行
    self.is_syncing = False  # 是否正在执行同步任务
    self.last_sync_time = None
    self.sync_history = []

    # 配置文件路径
    v3_root = Path(__file__).parent.parent.parent.parent
    self.config_file = v3_root / "config" / "material_master_schedule.json"

    # 日志文件路径
    self.logs_dir = v3_root / "logs"
    self.logs_dir.mkdir(exist_ok=True)
    self.sync_log_file = self.logs_dir / "material_master_sync.log"

    # 默认配置
    self.default_config = {
        "enabled": True,
        "sync_hour": 2,  # 每天2:00执行
        "sync_minute": 0,
        "max_retries": 3,
        "retry_delay_minutes": 30,
        "force_recreate_tables": False,
        "record_limit": None,  # 全量同步
        "clear_existing_data": True,  # 清空现有数据
    }

    # 加载配置
    self.config = self._load_config()

    logger.info(
        "物料档案调度器初始化完成",
        module="material_master",
        sync_time=f"{self.config['sync_hour']:02d}:{self.config['sync_minute']:02d}",
    )

    def _write_log(self, message: str, level: str = "INFO"):
        """写入日志文件"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] [{level}] {message}\n"

            with open(self.sync_log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except Exception:
            logger.error("写入日志文件失败", error=str(e))

    def _load_config(self) -> Dict:
        """加载配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(
                    "物料档案调度配置加载成功", config_file=str(self.config_file)
                )
                self._write_log("物料档案调度配置加载成功")
                return {**self.default_config, **config}
            else:
                logger.info(
                    "配置文件不存在，使用默认配置", config_file=str(self.config_file)
                )
                self._write_log("配置文件不存在，使用默认配置")
                self._save_config(self.default_config)
                return self.default_config.copy()
        except Exception:
            logger.error("加载配置失败，使用默认配置", error=str(e))
            self._write_log(f"加载配置失败: {str(e)}", "ERROR")
            return self.default_config.copy()

    def _save_config(self, config: Dict) -> bool:
        """保存配置"""
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info("物料档案调度配置保存成功", config_file=str(self.config_file))
            self._write_log("物料档案调度配置保存成功")
            return True
        except Exception:
            logger.error("保存配置失败", error=str(e))
            self._write_log(f"保存配置失败: {str(e)}", "ERROR")
            return False

    async def start(self):
        """启动物料档案调度器"""
        if self.is_running:
            logger.warning("物料档案调度器已在运行")
            self._write_log("物料档案调度器已在运行", "WARNING")
            return

        try:
            self._write_log("开始启动物料档案调度器")

            # 配置定时任务
            if self.config["enabled"]:
                trigger = CronTrigger(
                    hour=self.config["sync_hour"],
                    minute=self.config["sync_minute"])

                self.scheduler.add_job(
                    self._execute_material_master_sync,
                    trigger=trigger,
                    id="material_master_sync_job",
                    name="物料档案独立同步",
                    replace_existing=True,
                    max_instances=1,  # 防止重复执行
                )

                logger.info(
                    "物料档案同步任务已配置",
                    hour=self.config["sync_hour"],
                    minute=self.config["sync_minute"],
                )
                self._write_log(
                    f"物料档案同步任务已配置 - 每日 {self.config['sync_hour']:02d}:{self.config['sync_minute']:02d}"
                )

            # 启动调度器
            if not self.scheduler.running:
                self.scheduler.start()
                self._write_log("物料档案调度器已启动")

            self.is_running = True

            next_run = self._get_next_run_time()
            logger.info(
                "物料档案调度器启动成功",
                enabled=self.config["enabled"],
                next_run=next_run,
            )
            self._write_log(f"物料档案调度器启动成功，下次运行: {next_run}")

        except Exception:
            logger.error("启动物料档案调度器失败", error=str(e))
            self._write_log(f"启动物料档案调度器失败: {str(e)}", "ERROR")
            raise

    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            return

        try:
            self.scheduler.shutdown(wait=True)
            self.is_running = False
            logger.info("物料档案调度器已停止")
            self._write_log("物料档案调度器已停止")
        except Exception:
            logger.error("停止物料档案调度器失败", error=str(e))
            self._write_log(f"停止物料档案调度器失败: {str(e)}", "ERROR")

    async def _execute_material_master_sync(self):
        """执行物料档案同步任务"""
        sync_start_time = datetime.now()
        sync_id = f"material_master_sync_{sync_start_time.strftime('%Y%m%d_%H%M%S')}"

        # 检查是否已经在同步中
        if self.is_syncing:
            logger.warning(
                "已有物料档案同步任务在执行中，跳过此次同步", sync_id=sync_id
            )
            self._write_log(
                f"已有物料档案同步任务在执行中，跳过此次同步 {sync_id}", "WARNING"
            )
            return

        # 设置同步状态
        self.is_syncing = True

        logger.info("开始执行物料档案同步任务", sync_id=sync_id)
        self._write_log(f"🚀 开始执行物料档案同步任务 {sync_id}")

        try:
            # 执行物料档案同步
            result = await self.data_write_manager.write_single_module(
                module_name="material_master",
                record_limit=self.config.get("record_limit"),
                force_recreate_table=self.config.get(
                    "force_recreate_tables", False),
                clear_existing_data=self.config.get(
                    "clear_existing_data", True),
            )

            sync_end_time = datetime.now()
            duration = (sync_end_time - sync_start_time).total_seconds()

            # 记录同步历史
            sync_record = {
                "sync_id": sync_id,
                "module_name": "material_master",
                "start_time": sync_start_time.isoformat(),
                "end_time": sync_end_time.isoformat(),
                "duration_seconds": duration,
                "success": result.get("success", False),
                "records_processed": result.get("records_processed", 0),
                "records_written": result.get("records_written", 0),
                "table_name": result.get("table_name", ""),
                "message": result.get("message", ""),
                "details": result,
            }

            self.sync_history.append(sync_record)
            self.last_sync_time = sync_start_time

            # 保留最近30次记录
            if len(self.sync_history) > 30:
                self.sync_history = self.sync_history[-30:]

            if result.get("success"):
                logger.info(
                    "物料档案同步任务执行成功",
                    sync_id=sync_id,
                    duration=duration,
                    records_processed=result.get("records_processed", 0),
                    records_written=result.get("records_written", 0),
                )
                self._write_log(
                    f"✅ 物料档案同步任务执行成功 {sync_id} - 耗时{duration:.1f}秒, 处理{result.get('records_processed', 0)}条, 写入{result.get('records_written', 0)}条"
                )
            else:
                logger.error(
                    "物料档案同步任务执行失败",
                    sync_id=sync_id,
                    message=result.get("message", ""),
                )
                self._write_log(
                    f"❌ 物料档案同步任务执行失败 {sync_id}: {result.get('message', '')}",
                    "ERROR",
                )

                # 如果配置了重试，安排重试任务
                if self.config.get("max_retries", 0) > 0:
                    await self._schedule_retry(sync_id, 1)

        except Exception:
            logger.error("物料档案同步任务执行异常", sync_id=sync_id, error=str(e))
            self._write_log(f"💥 物料档案同步任务执行异常 {sync_id}: {str(e)}", "ERROR")

            # 记录失败的同步历史
            sync_record = {
                "sync_id": sync_id,
                "module_name": "material_master",
                "start_time": sync_start_time.isoformat(),
                "end_time": datetime.now().isoformat(),
                "duration_seconds": (
                    datetime.now() -
                    sync_start_time).total_seconds(),
                "success": False,
                "error": str(e),
                "message": f"同步异常: {str(e)}",
            }
            self.sync_history.append(sync_record)

            # 如果配置了重试，安排重试任务
            if self.config.get("max_retries", 0) > 0:
                await self._schedule_retry(sync_id, 1)

        finally:
            # 无论成功还是失败，都要重置同步状态
            self.is_syncing = False
            logger.info("物料档案同步任务状态已重置", sync_id=sync_id)
            self._write_log(f"物料档案同步任务状态已重置 {sync_id}")

    async def _schedule_retry(self, original_sync_id: str, retry_count: int):
        """安排重试任务"""
        if retry_count > self.config.get("max_retries", 3):
            logger.warning("已达到最大重试次数", original_sync_id=original_sync_id)
            self._write_log(f"已达到最大重试次数 {original_sync_id}", "WARNING")
            return

        retry_delay = self.config.get("retry_delay_minutes", 30)
        retry_time = datetime.now() + timedelta(minutes=retry_delay)

        logger.info(
            "安排物料档案同步重试任务",
            original_sync_id=original_sync_id,
            retry_count=retry_count,
            retry_time=retry_time.isoformat(),
        )
        self._write_log(
            f"安排物料档案同步重试任务 {original_sync_id} 第{retry_count}次重试，时间: {retry_time.strftime('%Y-%m-%d %H:%M:%S')}"
        )

        # 添加重试任务
        self.scheduler.add_job(
            self._execute_material_master_sync,
            trigger="date",
            run_date=retry_time,
            id=f"material_master_retry_{original_sync_id}_{retry_count}",
            name=f"物料档案重试同步任务 #{retry_count}",
            replace_existing=True,
        )

    def _get_next_run_time(self) -> Optional[str]:
        """获取下次运行时间"""
        try:
            if not self.scheduler or not self.scheduler.get_jobs():
                return None

            job = self.scheduler.get_job("material_master_sync_job")
            if job and job.next_run_time:
                return job.next_run_time.isoformat()
            return None
        except Exception:
            logger.error("获取下次运行时间失败", error=str(e))
            return None

    def get_status(self) -> Dict:
        """获取调度器状态"""
        return {
            "success": True,
            "data": {
                "module_name": "material_master",
                "is_running": self.is_running,  # 调度器是否运行
                "is_syncing": self.is_syncing,  # 是否正在执行同步任务
                "enabled": self.config.get("enabled", False),
                "sync_hour": self.config.get("sync_hour", 2),
                "sync_minute": self.config.get("sync_minute", 0),
                "max_retries": self.config.get("max_retries", 3),
                "retry_delay_minutes": self.config.get("retry_delay_minutes", 30),
                "next_run_time": self._get_next_run_time(),
                "last_sync_time": (
                    self.last_sync_time.isoformat() if self.last_sync_time else None
                ),
                "total_history_records": len(self.sync_history),
                "log_file": str(self.sync_log_file),
                "force_recreate_tables": self.config.get(
                    "force_recreate_tables", False
                ),
                "clear_existing_data": self.config.get("clear_existing_data", True),
                "scheduler_status": "运行中" if self.is_running else "已停止",
                "sync_status": "同步中" if self.is_syncing else "空闲",
                "can_start": not self.is_running,  # 是否可以启动调度器
                "can_stop": self.is_running,  # 是否可以停止调度器
                "can_trigger": self.is_running
                and not self.is_syncing,  # 是否可以手动触发同步
            },
        }

    def get_sync_history(self) -> Dict:
        """获取同步历史"""
        return {
            "success": True,
            "data": {
                "module_name": "material_master",
                "history": self.sync_history,
                "total_records": len(self.sync_history),
                "last_updated": datetime.now().isoformat(),
            },
        }

    def update_config(self, new_config: Dict) -> Dict:
        """更新配置"""
        try:
            # 合并配置
            updated_config = {**self.config, **new_config}

            # 保存配置
            if self._save_config(updated_config):
                self.config = updated_config
                self._write_log("物料档案调度配置更新成功")

                # 如果调度器正在运行，需要重新配置任务
                if self.is_running:
                    logger.info("重新配置物料档案同步任务")
                    self._write_log("重新配置物料档案同步任务")
                    # 重新配置任务
                    if self.scheduler.get_job("material_master_sync_job"):
                        self.scheduler.remove_job("material_master_sync_job")

                    if updated_config.get("enabled", True):
                        trigger = CronTrigger(
                            hour=updated_config["sync_hour"],
                            minute=updated_config["sync_minute"],
                        )

                        self.scheduler.add_job(
                            self._execute_material_master_sync,
                            trigger=trigger,
                            id="material_master_sync_job",
                            name="物料档案独立同步",
                            replace_existing=True,
                            max_instances=1,
                        )

                return {
                    "success": True,
                    "message": "物料档案调度配置更新成功",
                    "data": self.config,
                }
            else:
                return {"success": False, "message": "配置保存失败"}
        except Exception:
            logger.error("更新配置失败", error=str(e))
            self._write_log(f"更新配置失败: {str(e)}", "ERROR")
            return {"success": False, "message": f"更新配置失败: {str(e)}"}

    async def trigger_manual_sync(self) -> Dict:
        """手动触发物料档案同步"""
        logger.info("手动触发物料档案同步")
        self._write_log("手动触发物料档案同步")

        try:
            result = await self.data_write_manager.write_single_module(
                module_name="material_master",
                record_limit=self.config.get("record_limit"),
                force_recreate_table=False,  # 手动触发不强制重建表
                clear_existing_data=True,  # 清空现有数据
            )

            logger.info(
                "手动物料档案同步执行完成",
                success=result.get("success", False),
                message=result.get("message", ""),
            )
            self._write_log(f"手动物料档案同步执行完成: {result.get('message', '')}")

            return result

        except Exception:
            logger.error("手动物料档案同步执行失败", error=str(e))
            self._write_log(f"手动物料档案同步执行失败: {str(e)}", "ERROR")
            return {
                "success": False,
                "message": f"手动物料档案同步失败: {str(e)}",
                "error": str(e),
            }


# 全局实例
_material_master_scheduler = None


def get_material_master_scheduler() -> MaterialMasterScheduler:
    """获取物料档案调度器单例"""
    global _material_master_scheduler
    if _material_master_scheduler is None:
        _material_master_scheduler = MaterialMasterScheduler()
    return _material_master_scheduler
