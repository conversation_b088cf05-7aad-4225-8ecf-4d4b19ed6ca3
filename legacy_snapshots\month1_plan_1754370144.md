# 第1个月验证详细计划
生成时间: 2025-08-05 13:02:24

## 计划概览
- 总计: 30天
- 模块数: 15个
- 验证阶段: Day 1-20 (主要验证)
- 修复阶段: Day 21-30 (问题修复)

## 第1周 (Day 1-7)
### Day 2 - 验证 (高优先级)
- 模块: 材料出库单列表查询

### Day 4 - 验证 (高优先级)
- 模块: 采购订单列表

### Day 6 - 验证 (高优先级)
- 模块: 采购入库单列表

## 第2周 (Day 8-14)
### Day 8 - 验证 (高优先级)
- 模块: 产品入库列表查询

### Day 10 - 验证 (高优先级)
- 模块: 请购单列表查询

### Day 12 - 验证 (中优先级)
- 模块: 生产订单列表查询

### Day 14 - 验证 (中优先级)
- 模块: 委外订单列表

## 第3周 (Day 15-21)
### Day 16 - 验证 (中优先级)
- 模块: 委外入库列表查询

### Day 18 - 验证 (中优先级)
- 模块: 委外申请列表查询

### Day 20 - 验证 (中优先级)
- 模块: 物料档案批量详情查询

## 第4周 (Day 22-28)
### Day 22 - 修复 (低优先级)
- 模块: 现存量报表查询

### Day 24 - 修复 (低优先级)
- 模块: 销售出库列表查询

### Day 26 - 修复 (低优先级)
- 模块: 销售订单

### Day 28 - 修复 (低优先级)
- 模块: 需求计划

## 执行命令
```bash
# 单个模块验证
python verify_module.py "模块名称"

# 批量验证
python batch_validate_modules.py
```