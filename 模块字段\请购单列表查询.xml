<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>0f1453d26e6741faa95ace9533a61683</id>
<name>请购单列表查询</name>
<apiClassifyId>1936116693949480962</apiClassifyId>
<apiClassifyName>请购单</apiClassifyName>
<apiClassifyCode/>
<parentApiClassifies/>
<functionId/>
<openMode>0</openMode>
<description>根据交易类型、物料、表头模式还是表头明细模式、分页条件和自定义条件查询请购单列表数据信息</description>
<auth>true</auth>
<bodyPassthrough>false</bodyPassthrough>
<healthExam>false</healthExam>
<healthStatus>true</healthStatus>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/scm/applyorder/list</completeProxyUrl>
<connectUrl>/bill/list</connectUrl>
<sort>20</sort>
<handler>openapi</handler>
<httpRequestType>POST</httpRequestType>
<openApi>true</openApi>
<preset>false</preset>
<productId>710a0be3edff4f9092e35f63fd3b9bae</productId>
<productCode>scm</productCode>
<proxyUrl>/yonbip/scm/applyorder/list</proxyUrl>
<requestParamsDemo>Url: /yonbip/scm/applyorder/list?access_token=访问令牌 Body: { "bustype": "110000000000029", "product": 1730491724599552, "pageIndex": 1, "pageSize": 10, "isSum": false, "queryOrders": [ { "field": "id", "order": "asc" } ], "simpleVOs": [ { "field": "code", "op": "eq", "value1": "CGDD2010140000000003" } ] }</requestParamsDemo>
<requestProtocol>HTTP</requestProtocol>
<serviceHttpMethod>POST</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2020-01-16 16:46:24</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/applyorder/list</address>
<productName>采购供应</productName>
<productClassifyId>yonsuite</productClassifyId>
<productClassifyCode>yonbip</productClassifyCode>
<productClassifyName>用友 YonBIP</productClassifyName>
<paramDTOS>
<paramDTOS>
<id>2106049437256122376</id>
<name>bustype</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<defParamId>1996896485556355078</defParamId>
<array>false</array>
<paramDesc>交易类型id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>110000000000029</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2106049437256122377</id>
<name>product</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<defParamId>1996896485556355079</defParamId>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1730491724599552</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2106049437256122378</id>
<name>pageIndex</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<defParamId>1996896485556355080</defParamId>
<array>false</array>
<paramDesc>页码</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2106049437256122379</id>
<name>pageSize</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<defParamId>1996896485556355081</defParamId>
<array>false</array>
<paramDesc>每页条数</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>10</example>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2106049437256122380</id>
<name>isSum</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<defParamId>1996896485556355082</defParamId>
<array>false</array>
<paramDesc>是否按照表头查询：true:表头、false:表头+明细</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>false</example>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>false</defaultValue>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2106049437256122369</id>
<name>queryOrders</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<children>
<children>
<id>2106049437256122370</id>
<name>field</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122369</parentId>
<defParamId>1996896485556355084</defParamId>
<array>false</array>
<paramDesc>排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：applyOrders.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>id</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2106049437256122371</id>
<name>order</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122369</parentId>
<defParamId>1996896485556355085</defParamId>
<array>false</array>
<paramDesc>顺序：asc：正序、desc：倒序</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>asc</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>1996896485556355083</defParamId>
<array>true</array>
<paramDesc>排序字段</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2106049437256122372</id>
<name>simpleVOs</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<children>
<children>
<id>2106049437256122373</id>
<name>field</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122372</parentId>
<defParamId>1996896485556355087</defParamId>
<array>false</array>
<paramDesc>属性名(条件) ，1：status(单据状态：0:未审核、1:已审核、2:已关闭、3:审核中)、2：code(单据编号)、3：vouchdate(请购日期)、4：operator(请购员id)、5：org(需求组织id)、6；applyOrders.purchaseOrg(采购组织id)、7：applyOrders.vendor(建议供应商id)、 8：applyDept(请购部门id)、9：headItem.define1(单据头自定义项1)、10：applyOrders.adviseOrderDate(建议订货日期)、11：applyOrders.project(项目id)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>code</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2106049437256122374</id>
<name>op</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122372</parentId>
<defParamId>1996896485556355088</defParamId>
<array>false</array>
<paramDesc>比较符（条件）：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>eq</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2106049437256122375</id>
<name>value1</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122372</parentId>
<defParamId>1996896485556355089</defParamId>
<array>false</array>
<paramDesc>值1（条件）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>CGDD2010140000000003</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>1996896485556355086</defParamId>
<array>true</array>
<paramDesc>扩展条件查询</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>2106049437256122388</id>
<name>bustype</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>交易类型id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>bustype</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2106049437256122389</id>
<name>product</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2106049437256122390</id>
<name>pageIndex</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>页码</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageIndex</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2106049437256122391</id>
<name>pageSize</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>每页条数</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageSize</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2106049437256122392</id>
<name>isSum</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>是否按照表头查询：true:表头、false:表头+明细</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>isSum</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>boolean</serviceParamType>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2106049437256122381</id>
<name>queryOrders</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<children>
<children>
<id>2106049437256122382</id>
<name>field</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122381</parentId>
<defParamId/>
<array>false</array>
<paramDesc>排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：applyOrders.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122383</id>
<name>order</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122381</parentId>
<defParamId/>
<array>false</array>
<paramDesc>顺序：asc：正序、desc：倒序</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>order</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>排序字段</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>queryOrders</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2106049437256122384</id>
<name>simpleVOs</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<children>
<children>
<id>2106049437256122385</id>
<name>field</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122384</parentId>
<defParamId/>
<array>false</array>
<paramDesc>属性名(条件) ，1：status(单据状态：0:未审核、1:已审核、2:已关闭、3:审核中)、2：code(单据编号)、3：vouchdate(请购日期)、4：operator(请购员id)、5：org(需求组织id)、6；applyOrders.purchaseOrg(采购组织id)、7：applyOrders.vendor(建议供应商id)、 8：applyDept(请购部门id)、9：headItem.define1(单据头自定义项1)、10：applyOrders.adviseOrderDate(建议订货日期)、11：applyOrders.project(项目id)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122386</id>
<name>op</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122384</parentId>
<defParamId/>
<array>false</array>
<paramDesc>比较符（条件）：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>op</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122387</id>
<name>value1</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122384</parentId>
<defParamId/>
<array>false</array>
<paramDesc>值1（条件）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value1</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>扩展条件查询</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>simpleVOs</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>2106049445846056973</id>
<name>code</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<defParamId>1996896485556355102</defParamId>
<array>false</array>
<paramDesc>返回码，调用成功时返回200</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2106049445846056974</id>
<name>message</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<defParamId>1996896485556355103</defParamId>
<array>false</array>
<paramDesc>调用失败时的错误信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2106049437256122393</id>
<name>data</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId/>
<children>
<children>
<id>2106049445846056966</id>
<name>pageIndex</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122393</parentId>
<defParamId>1996896485556355105</defParamId>
<array>false</array>
<paramDesc>分页</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049445846056967</id>
<name>pageSize</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122393</parentId>
<defParamId>1996896485556355106</defParamId>
<array>false</array>
<paramDesc>每页条数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049445846056968</id>
<name>pageCount</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122393</parentId>
<defParamId>1996896485556355107</defParamId>
<array>false</array>
<paramDesc>页数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049445846056969</id>
<name>beginPageIndex</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122393</parentId>
<defParamId>1996896485556355108</defParamId>
<array>false</array>
<paramDesc>起始页</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049445846056970</id>
<name>endPageIndex</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122393</parentId>
<defParamId>1996896485556355109</defParamId>
<array>false</array>
<paramDesc>结束页</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049445846056971</id>
<name>recordCount</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122393</parentId>
<defParamId>1996896485556355110</defParamId>
<array>false</array>
<paramDesc>记录数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>100</example>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049445846056972</id>
<name>pubts</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122393</parentId>
<defParamId>1996896485556355111</defParamId>
<array>false</array>
<paramDesc>时间戳，格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2024-03-04 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122394</id>
<name>recordList</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122393</parentId>
<children>
<children>
<id>2106049437256122462</id>
<name>vouchdate</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355113</defParamId>
<array>false</array>
<paramDesc>单据日期，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-04 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122463</id>
<name>code</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355114</defParamId>
<array>false</array>
<paramDesc>请购单编号，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>CGQG0000201905100001</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122464</id>
<name>returncount</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355115</defParamId>
<array>false</array>
<paramDesc>退回次数，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122465</id>
<name>isWfControlled</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355116</defParamId>
<array>false</array>
<paramDesc>是否审批流控制，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122466</id>
<name>verifystate</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355117</defParamId>
<array>false</array>
<paramDesc>审批状态，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122467</id>
<name>bustype</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355118</defParamId>
<array>false</array>
<paramDesc>交易类型id，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122468</id>
<name>bustype_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355119</defParamId>
<array>false</array>
<paramDesc>交易类型名称，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>采购要货</example>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"bustype.name","cItemName":"bustype_name","cCaption":"交易类型","cShowCaption":"交易类型","iMaxLength":255,"bHidden":false,"cRefType":"transtype.bd_billtyperef","cRefId":null,"cRefRetId":{"bustype":"id"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrder","cControlType":"Column","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122469</id>
<name>applyDept</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355120</defParamId>
<array>false</array>
<paramDesc>请购部门id，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996900540021735427</example>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122470</id>
<name>applyDept_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355121</defParamId>
<array>false</array>
<paramDesc>请购部门名称，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>数智请购部门</example>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyDept.name","cItemName":"applyDept_name","cCaption":"请购部门","cShowCaption":"请购部门","iMaxLength":255,"bHidden":false,"cRefType":"ucf-org-center.bd_adminorgsharetreeref","cRefId":null,"cRefRetId":null,"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrder","cControlType":"Column","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122471</id>
<name>bizstatus</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355122</defParamId>
<array>false</array>
<paramDesc>单据状态, 0:开立、3:审核中、1:已审核、2:已关闭，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122472</id>
<name>status</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355123</defParamId>
<array>false</array>
<paramDesc>单据状态, 0:开立、3:审核中、1:已审核、2:已关闭，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122473</id>
<name>currency</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355124</defParamId>
<array>false</array>
<paramDesc>币种id，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996901321700016134</example>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122474</id>
<name>currency_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355125</defParamId>
<array>false</array>
<paramDesc>币种名称，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>人名币</example>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"currency.name","cItemName":"currency_name","cCaption":"币种","cShowCaption":"币种","iMaxLength":255,"bHidden":false,"cRefType":"ucfbasedoc.bd_currencytenantref","cRefId":null,"cRefRetId":{"currency":"id","currency_code":"code","currency_name":"name","currency_priceDigit":"priceDigit","currency_moneyDigit":"moneyDigit"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrder","cControlType":"Column","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122475</id>
<name>warehouseId</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355126</defParamId>
<array>false</array>
<paramDesc>要货仓库id，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996901321700016135</example>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122476</id>
<name>warehouseId_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355127</defParamId>
<array>false</array>
<paramDesc>要货仓库名称，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>货品仓库</example>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"warehouseId.name","cItemName":"warehouseId_name","cCaption":"要货仓库","cShowCaption":"要货仓库","iMaxLength":255,"bHidden":true,"cRefType":"productcenter.aa_warehouse","cRefId":null,"cRefRetId":{"warehouseId":"id"},"cDataRule":null,"iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":null,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrder","cControlType":"refer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122477</id>
<name>source</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355128</defParamId>
<array>false</array>
<paramDesc>来源单据类型，20:计划独立需求、280:计划订单、MR.mr_lrp_plan_order_batch:计划订单、po_production_order:生产订单、ucf-amc-aum.aum_assignapply_card:资产领用申请、yonbip-pm-planme.rscm_project_materiallist_card:项目物资单、SCMSA.voucher_order:销售订单，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>MR.mr_lrp_plan_order_batch</example>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122478</id>
<name>store</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355129</defParamId>
<array>false</array>
<paramDesc>所属门店id，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996902524296626181</example>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122479</id>
<name>isUretailVoucher</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355130</defParamId>
<array>false</array>
<paramDesc>是否是零售, true:是、false:否，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122480</id>
<name>store_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355131</defParamId>
<array>false</array>
<paramDesc>所属门店名称，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>零售门店</example>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"store.name","cItemName":"store_name","cCaption":"所属门店","cShowCaption":"所属门店","iMaxLength":255,"bHidden":false,"cRefType":"retail.aa_store","cRefId":null,"cRefRetId":"{\"store\",\"id\"}","cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrder","cControlType":"Column","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122481</id>
<name>org</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355132</defParamId>
<array>false</array>
<paramDesc>需求组织id，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996902524296626182</example>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122482</id>
<name>org_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355133</defParamId>
<array>false</array>
<paramDesc>需求组织名称，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>达利园组织</example>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"org.name","cItemName":"org_name","cCaption":"需求组织","cShowCaption":"需求组织","iMaxLength":255,"bHidden":false,"cRefType":"aa_orgtree","cRefId":null,"cRefRetId":{"org":"id","org_name":"name"},"cDataRule":"\"<%u8c-config.option.singleOrg%>\"==\"false\"","iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrder","cControlType":"refer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122483</id>
<name>custom</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355134</defParamId>
<array>false</array>
<paramDesc>客户id，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996903245851131908</example>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122484</id>
<name>creator</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355135</defParamId>
<array>false</array>
<paramDesc>制单人，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>张三</example>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122485</id>
<name>createTime</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355136</defParamId>
<array>false</array>
<paramDesc>制单时间，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-04 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122486</id>
<name>modifier</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355137</defParamId>
<array>false</array>
<paramDesc>修改人，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>张三</example>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122487</id>
<name>modifyTime</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355138</defParamId>
<array>false</array>
<paramDesc>修改时间，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-04 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122488</id>
<name>closer</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355139</defParamId>
<array>false</array>
<paramDesc>关闭人，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>李响</example>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122489</id>
<name>closeTime</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355140</defParamId>
<array>false</array>
<paramDesc>关闭时间，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-05 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122490</id>
<name>locker</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355141</defParamId>
<array>false</array>
<paramDesc>锁定人，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>张三</example>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122491</id>
<name>lockTime</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355142</defParamId>
<array>false</array>
<paramDesc>锁定时间，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-05 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122492</id>
<name>operator</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355143</defParamId>
<array>false</array>
<paramDesc>请购员id，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996904998192021506</example>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122493</id>
<name>operator_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355144</defParamId>
<array>false</array>
<paramDesc>请购员名称，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>王晨</example>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"operator.name","cItemName":"operator_name","cCaption":"请购员","cShowCaption":"请购员","iMaxLength":255,"bHidden":false,"cRefType":"ucf-staff-center.bd_staff_outer_ref","cRefId":null,"cRefRetId":{"operator":"id","applyDept":"dept_id","applyDept_name":"dept_id_name"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrder","cControlType":"Column","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122494</id>
<name>auditor</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355145</defParamId>
<array>false</array>
<paramDesc>审核人，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>刘策</example>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122495</id>
<name>auditTime</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355146</defParamId>
<array>false</array>
<paramDesc>审核时间，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-05 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122496</id>
<name>auditDate</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355147</defParamId>
<array>false</array>
<paramDesc>审核日期，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-05 12:36:12</example>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122497</id>
<name>submitor</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355148</defParamId>
<array>false</array>
<paramDesc>提交人，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>王晨</example>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122498</id>
<name>submitTime</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355149</defParamId>
<array>false</array>
<paramDesc>提交时间，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-05 12:20:12</example>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122499</id>
<name>totalQuantity</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355150</defParamId>
<array>false</array>
<paramDesc>整单数量，汇总场景和明细场景均返回</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>200</example>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122500</id>
<name>memo</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355151</defParamId>
<array>false</array>
<paramDesc>备注，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>来货请购</example>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122501</id>
<name>id</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355152</defParamId>
<array>false</array>
<paramDesc>请购单id，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996904998192021507</example>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122502</id>
<name>pubts</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355153</defParamId>
<array>false</array>
<paramDesc>时间戳，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-04 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122503</id>
<name>tplid</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355154</defParamId>
<array>false</array>
<paramDesc>模板id，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996906535790313475</example>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122395</id>
<name>headItem</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<children>
<children>
<id>2106049437256122396</id>
<name>id</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122395</parentId>
<defParamId>1996896485556355156</defParamId>
<array>false</array>
<paramDesc>表头自定义项id，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996906535790313476</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122397</id>
<name>define1</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122395</parentId>
<defParamId>1996896485556355157</defParamId>
<array>false</array>
<paramDesc>表头自定义项1，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表头自定义项1</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122398</id>
<name>define2</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122395</parentId>
<defParamId>1996896485556355158</defParamId>
<array>false</array>
<paramDesc>表头自定义项2，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表头自定义项2</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122399</id>
<name>define3</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122395</parentId>
<defParamId>1996896485556355159</defParamId>
<array>false</array>
<paramDesc>表头自定义项3，汇总场景和明细场景均返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表头自定义项3</example>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1996896485556355155</defParamId>
<array>false</array>
<paramDesc>表头自定义项</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible/>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122504</id>
<name>applyorders_execStatus</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355161</defParamId>
<array>false</array>
<paramDesc>执行状态, 0:未下订单、1:部分下单、2:全部下单、</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表头自定义项4</example>
<fullName/>
<ytenantId/>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122505</id>
<name>applyorders_receiveOrg</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355162</defParamId>
<array>false</array>
<paramDesc>收货组织id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996916044853673985</example>
<fullName/>
<ytenantId/>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122506</id>
<name>applyorders_receiveOrg_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355163</defParamId>
<array>false</array>
<paramDesc>收货组织名称,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>达利园组织</example>
<fullName/>
<ytenantId/>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.receiveOrg.name","cItemName":"applyorders_receiveOrg_name","cCaption":"收货组织","cShowCaption":"收货组织","iMaxLength":255,"bHidden":false,"cRefType":"aa_orgtree","cRefId":null,"cRefRetId":{"receiveOrg":"id","receiveOrg_name":"name"},"cDataRule":"\"<%loginUser.storeId%>\"==\"null\" && \"<%u8c-config.option.singleOrg%>\"==\"false\"","iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"Refer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122507</id>
<name>applyorders_purchaseOrg</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355164</defParamId>
<array>false</array>
<paramDesc>采购组织id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996916044853673985</example>
<fullName/>
<ytenantId/>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122508</id>
<name>applyorders_purchaseOrg_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355165</defParamId>
<array>false</array>
<paramDesc>采购组织名称,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>达利园组织</example>
<fullName/>
<ytenantId/>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.purchaseOrg.name","cItemName":"applyorders_purchaseOrg_name","cCaption":"采购组织","cShowCaption":"采购组织","iMaxLength":255,"bHidden":false,"cRefType":"aa_orgtree","cRefId":null,"cRefRetId":{"purchaseOrg":"id","purchaseOrg_name":"name"},"cDataRule":"\"<%loginUser.storeId%>\"==\"null\" && \"<%u8c-config.option.singleOrg%>\"==\"false\"","iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"Refer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122509</id>
<name>applyorders_purDept</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355166</defParamId>
<array>false</array>
<paramDesc>采购部门id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996916044853673986</example>
<fullName/>
<ytenantId/>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122510</id>
<name>applyorders_purDept_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355167</defParamId>
<array>false</array>
<paramDesc>采购部门名称,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>采购部门</example>
<fullName/>
<ytenantId/>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.purDept.name","cItemName":"applyorders_purDept_name","cCaption":"采购部门","cShowCaption":"采购部门","iMaxLength":255,"bHidden":true,"cRefType":"ucf-org-center.bd_adminorgsharetreeref","cRefId":null,"cRefRetId":{"purDept":"id"},"cDataRule":null,"iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":false,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"TreeRefer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122511</id>
<name>applyorders_purPerson</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355168</defParamId>
<array>false</array>
<paramDesc>采购业务员id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996916551654047750</example>
<fullName/>
<ytenantId/>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122512</id>
<name>applyorders_purPerson_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355169</defParamId>
<array>false</array>
<paramDesc>采购业务员名称,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>李晨</example>
<fullName/>
<ytenantId/>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.purPerson.name","cItemName":"applyorders_purPerson_name","cCaption":"采购业务员","cShowCaption":"采购业务员","iMaxLength":255,"bHidden":true,"cRefType":"ucf-staff-center.bd_staff_outer_ref","cRefId":null,"cRefRetId":{"purPerson":"id"},"cDataRule":null,"iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":false,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"Refer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122513</id>
<name>applyOrders_supplyMoney</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355170</defParamId>
<array>false</array>
<paramDesc>累计订货金额,只有明细场景返回</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>200</example>
<fullName/>
<ytenantId/>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122514</id>
<name>applyOrder_orderMoneyRatio</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355171</defParamId>
<array>false</array>
<paramDesc>订单金额超量比例,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId/>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122515</id>
<name>applyorders_supplyCount</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355172</defParamId>
<array>false</array>
<paramDesc>累计订货数量,只有明细场景返回</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId/>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122516</id>
<name>apporders_id</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355173</defParamId>
<array>false</array>
<paramDesc>订单行id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996916551654047751</example>
<fullName/>
<ytenantId/>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122517</id>
<name>applyorders_product</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355174</defParamId>
<array>false</array>
<paramDesc>物料id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996917006926348290</example>
<fullName/>
<ytenantId/>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122518</id>
<name>product_defaultAlbumId</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355175</defParamId>
<array>false</array>
<paramDesc>物料首图片,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996917006926348291</example>
<fullName/>
<ytenantId/>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122519</id>
<name>applyorders_product_cCode</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355176</defParamId>
<array>false</array>
<paramDesc>物料编码,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>00000002</example>
<fullName/>
<ytenantId/>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.product.cCode","cItemName":"applyorders_product_cCode","cCaption":"物料编码","cShowCaption":"物料编码","iMaxLength":255,"bHidden":false,"cRefType":"aa_nomalproduct","cRefId":null,"cRefRetId":{"product":"id","product_cName":"cName","productsku":"productskus_id","productsku_cCode":"productskus_cCode","productsku_cName":"skuName","product_oUnitId":"oUnitId","unit":"productOfflineRetail_storeOrderUnit","product_productOfflineRetail_purchaseUnit":"purchaseUnit","unit_name":"purchaseUnit_name","invExchRate":"purchaseRate","productOfflineRetail_purchaseRate":"purchaseRate","taxRate":"productOfflineRetail_inputTax","product_primeCosts":"primeCosts","productsku_primeCosts":"productskus_primeCosts","isBatchManage":"productOfflineRetail_isBatchManage","isSerialNoManage":"productOfflineRetail_isSerialNoManage","isExpiryDateManage":"productOfflineRetail_isExpiryDateManage","expireDateNo":"productOfflineRetail_expireDateNo","expireDateUnit":"productOfflineRetail_expireDateUnit","free@1@@10":"retailskus!free@1@@10","skudefine@1@@60":"productSkuProps!define@1@@60","prodefine@1@@30":"productProps!define@1@@30","propertiesValue":"propertiesValue","product_modelDescription":"modelDescription","productskus_modelDescription":"productskus_modelDescription"},"cDataRule":null,"iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":true,"bIsNull":false,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"refer","refReturn":"cCode","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122520</id>
<name>applyorders_product_cName</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355177</defParamId>
<array>false</array>
<paramDesc>物料名称,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>苹果</example>
<fullName/>
<ytenantId/>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.product.cName","cItemName":"applyorders_product_cName","cCaption":"物料名称","cShowCaption":"物料名称","iMaxLength":255,"bHidden":false,"cRefType":"aa_nomalproduct","cRefId":null,"cRefRetId":{"product":"id","product_cName":"cName","productsku":"productskus_id","productsku_cCode":"productskus_cCode","productsku_cName":"skuName","product_oUnitId":"oUnitId","unit":"productOfflineRetail_storeOrderUnit","product_productOfflineRetail_purchaseUnit":"purchaseUnit","unit_name":"purchaseUnit_name","invExchRate":"purchaseRate","productOfflineRetail_purchaseRate":"purchaseRate","taxRate":"productOfflineRetail_inputTax","product_primeCosts":"primeCosts","productsku_primeCosts":"productskus_primeCosts","isBatchManage":"productOfflineRetail_isBatchManage","isSerialNoManage":"productOfflineRetail_isSerialNoManage","isExpiryDateManage":"productOfflineRetail_isExpiryDateManage","expireDateNo":"productOfflineRetail_expireDateNo","expireDateUnit":"productOfflineRetail_expireDateUnit","free@1@@10":"retailskus!free@1@@10","skudefine@1@@60":"productSkuProps!define@1@@60","prodefine@1@@30":"productProps!define@1@@30","propertiesValue":"propertiesValue","product_modelDescription":"modelDescription","productskus_modelDescription":"productskus_modelDescription"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":false,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"refer","refReturn":"cName","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122521</id>
<name>applyorders_productsku</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355178</defParamId>
<array>false</array>
<paramDesc>物料SKUid,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996917006926348290</example>
<fullName/>
<ytenantId/>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122522</id>
<name>applyorders_productsku_cCode</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355179</defParamId>
<array>false</array>
<paramDesc>物料sku编码,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>00000002</example>
<fullName/>
<ytenantId/>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.productsku.cCode","cItemName":"applyorders_productsku_cCode","cCaption":"物料sku编码","cShowCaption":"物料sku编码","iMaxLength":255,"bHidden":false,"cRefType":"aa_productsku","cRefId":null,"cRefRetId":{"product":"id","product_cName":"cName","productsku":"productskus_id","productsku_cCode":"productskus_cCode","productsku_cName":"skuName","product_oUnitId":"oUnitId","unit":"productOfflineRetail_storeOrderUnit","product_productOfflineRetail_purchaseUnit":"purchaseUnit","unit_name":"purchaseUnit_name","invExchRate":"purchaseRate","productOfflineRetail_purchaseRate":"purchaseRate","taxRate":"productOfflineRetail_inputTax","product_primeCosts":"primeCosts","productsku_primeCosts":"productskus_primeCosts","isBatchManage":"productOfflineRetail_isBatchManage","isSerialNoManage":"productOfflineRetail_isSerialNoManage","isExpiryDateManage":"productOfflineRetail_isExpiryDateManage","expireDateNo":"productOfflineRetail_expireDateNo","expireDateUnit":"productOfflineRetail_expireDateUnit","free@1@@10":"retailskus!free@1@@10","skudefine@1@@60":"productSkuProps!define@1@@60","prodefine@1@@30":"productProps!define@1@@30","propertiesValue":"propertiesValue","product_modelDescription":"modelDescription","productskus_modelDescription":"productskus_modelDescription"},"cDataRule":null,"iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":true,"bIsNull":false,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"refer","refReturn":"productskus_cCode","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122523</id>
<name>applyorders_productsku_cName</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355180</defParamId>
<array>false</array>
<paramDesc>物料sku名称,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>苹果</example>
<fullName/>
<ytenantId/>
<paramOrder>62</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.productsku.skuName","cItemName":"applyorders_productsku_cName","cCaption":"物料sku名称","cShowCaption":"物料sku名称","iMaxLength":255,"bHidden":false,"cRefType":"aa_productsku","cRefId":null,"cRefRetId":{"product":"id","product_cName":"cName","productsku":"productskus_id","productsku_cCode":"productskus_cCode","productsku_cName":"skuName","product_oUnitId":"oUnitId","unit":"productOfflineRetail_storeOrderUnit","product_productOfflineRetail_purchaseUnit":"purchaseUnit","unit_name":"purchaseUnit_name","invExchRate":"purchaseRate","productOfflineRetail_purchaseRate":"purchaseRate","taxRate":"productOfflineRetail_inputTax","product_primeCosts":"primeCosts","productsku_primeCosts":"productskus_primeCosts","isBatchManage":"productOfflineRetail_isBatchManage","isSerialNoManage":"productOfflineRetail_isSerialNoManage","isExpiryDateManage":"productOfflineRetail_isExpiryDateManage","expireDateNo":"productOfflineRetail_expireDateNo","expireDateUnit":"productOfflineRetail_expireDateUnit","free@1@@10":"retailskus!free@1@@10","skudefine@1@@60":"productSkuProps!define@1@@60","prodefine@1@@30":"productProps!define@1@@30","propertiesValue":"propertiesValue","product_modelDescription":"modelDescription","productskus_modelDescription":"productskus_modelDescription"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":false,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"refer","refReturn":"cName","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122524</id>
<name>applyorders_currency</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355181</defParamId>
<array>false</array>
<paramDesc>币种id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996917006926348290</example>
<fullName/>
<ytenantId/>
<paramOrder>63</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122525</id>
<name>applyorders_currency_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355182</defParamId>
<array>false</array>
<paramDesc>币种名称,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>人民币</example>
<fullName/>
<ytenantId/>
<paramOrder>64</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.currency.name","cItemName":"applyorders_currency_name","cCaption":"币种","cShowCaption":"币种","iMaxLength":255,"bHidden":false,"cRefType":"ucfbasedoc.bd_currencytenantref","cRefId":null,"cRefRetId":{"currency":"id","currency_code":"code","currency_name":"name","currency_priceDigit":"priceDigit","currency_moneyDigit":"moneyDigit"},"cDataRule":null,"iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"refer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122526</id>
<name>applyorders_currency_priceDigit</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355183</defParamId>
<array>false</array>
<paramDesc>币种单价精度,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>65</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122527</id>
<name>applyorders_currency_moneyDigit</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355184</defParamId>
<array>false</array>
<paramDesc>币种金额精度,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>66</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122528</id>
<name>applyorders_qty</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355185</defParamId>
<array>false</array>
<paramDesc>数量,只有明细场景返回</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId/>
<paramOrder>67</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122529</id>
<name>applyorders_subQty</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355186</defParamId>
<array>false</array>
<paramDesc>计价数量,只有明细场景返回</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId/>
<paramOrder>68</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122530</id>
<name>applyorders_rowno</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355187</defParamId>
<array>false</array>
<paramDesc>行号,只有明细场景返回</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId/>
<paramOrder>69</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122531</id>
<name>unit_Precision</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355188</defParamId>
<array>false</array>
<paramDesc>主计量精度,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>70</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122532</id>
<name>applyorders_unit</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355189</defParamId>
<array>false</array>
<paramDesc>单位id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996918364130246657</example>
<fullName/>
<ytenantId/>
<paramOrder>71</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122533</id>
<name>applyorders_unit_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355190</defParamId>
<array>false</array>
<paramDesc>主计量名称,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>吨</example>
<fullName/>
<ytenantId/>
<paramOrder>72</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.unit.name","cItemName":"applyorders_unit_name","cCaption":"主计量","cShowCaption":"主计量","iMaxLength":255,"bHidden":false,"cRefType":"aa_productunit","cRefId":null,"cRefRetId":{"unit":"id"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":false,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"refer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122534</id>
<name>applyorders_product_oUnitId</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355191</defParamId>
<array>false</array>
<paramDesc>零售单位id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996918364130246657</example>
<fullName/>
<ytenantId/>
<paramOrder>73</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122535</id>
<name>applyorders_product_productOfflineRetail_purchaseUnit</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355192</defParamId>
<array>false</array>
<paramDesc>采购单位id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>吨</example>
<fullName/>
<ytenantId/>
<paramOrder>74</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122536</id>
<name>applyorders_invExchRate</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355193</defParamId>
<array>false</array>
<paramDesc>换算率,只有明细场景返回</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>75</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122537</id>
<name>applyorders_productOfflineRetail_purchaseRate</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355194</defParamId>
<array>false</array>
<paramDesc>采购单位换算率,只有明细场景返回</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>76</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122538</id>
<name>priceUOM</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355195</defParamId>
<array>false</array>
<paramDesc>计价单位id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996918364130246658</example>
<fullName/>
<ytenantId/>
<paramOrder>77</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122539</id>
<name>priceUOM_Name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355196</defParamId>
<array>false</array>
<paramDesc>计价单位,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>吨</example>
<fullName/>
<ytenantId/>
<paramOrder>78</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.priceUOM.name","cItemName":"priceUOM_Name","cCaption":"计价单位","cShowCaption":"计价单位","iMaxLength":255,"bHidden":false,"cRefType":"productcenter.pc_productassitunitsref","cRefId":null,"cRefRetId":{"priceUOM":"assistUnit","priceUOM_Name":"assistUnit_Name","priceUOM_Code":"assistUnit_Code","invPriceExchRate":"mainUnitCount","unitExchangeTypePrice":"unitExchangeType","priceUOM_Precision":"assistUnit_Precision"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"refer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122540</id>
<name>invPriceExchRate</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355197</defParamId>
<array>false</array>
<paramDesc>计价换算率,只有明细场景返回</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>79</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122541</id>
<name>unitExchangeTypePrice</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355198</defParamId>
<array>false</array>
<paramDesc>计价单位转换率的换算方式,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>80</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122542</id>
<name>priceUOM_Precision</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355199</defParamId>
<array>false</array>
<paramDesc>计价单位精度,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>81</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122543</id>
<name>taxRate</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355200</defParamId>
<array>false</array>
<paramDesc>税率,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>82</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.taxRate","cItemName":"taxRate","cCaption":"税率","cShowCaption":"税率","iMaxLength":255,"bHidden":false,"cRefType":"ucfbasedoc.bd_taxrate","cRefId":null,"cRefRetId":{"taxitems":"id","taxitems_name":"name"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"refer","refReturn":"ntaxRate","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122544</id>
<name>oriTax</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355201</defParamId>
<array>false</array>
<paramDesc>税额,只有明细场景返回</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId/>
<paramOrder>83</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122545</id>
<name>oriTaxUnitPrice</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355202</defParamId>
<array>false</array>
<paramDesc>含税单价,只有明细场景返回</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>12</example>
<fullName/>
<ytenantId/>
<paramOrder>84</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122546</id>
<name>oriUnitPrice</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355203</defParamId>
<array>false</array>
<paramDesc>无税单价,只有明细场景返回</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>6</example>
<fullName/>
<ytenantId/>
<paramOrder>85</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122547</id>
<name>oriMoney</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355204</defParamId>
<array>false</array>
<paramDesc>无税金额,只有明细场景返回</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>150</example>
<fullName/>
<ytenantId/>
<paramOrder>86</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122548</id>
<name>oriSum</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355205</defParamId>
<array>false</array>
<paramDesc>含税金额,只有明细场景返回</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>200</example>
<fullName/>
<ytenantId/>
<paramOrder>87</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122549</id>
<name>applyorders_product_primeCosts</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355206</defParamId>
<array>false</array>
<paramDesc>进货价格,只有明细场景返回</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>23</example>
<fullName/>
<ytenantId/>
<paramOrder>88</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122550</id>
<name>applyorders_productsku_primeCosts</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355207</defParamId>
<array>false</array>
<paramDesc>sku进货价格,只有明细场景返回</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>24</example>
<fullName/>
<ytenantId/>
<paramOrder>89</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122551</id>
<name>applyorders_requirementDate</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355208</defParamId>
<array>false</array>
<paramDesc>需求日期,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-04 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>90</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122552</id>
<name>applyorders_adviseOrderDate</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355209</defParamId>
<array>false</array>
<paramDesc>建议订货日期,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-04 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>91</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122553</id>
<name>applyorders_adviseSupplier</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355210</defParamId>
<array>false</array>
<paramDesc>建议供应商id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996919652626202626</example>
<fullName/>
<ytenantId/>
<paramOrder>92</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122554</id>
<name>applyorders_adviseSupplier_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355211</defParamId>
<array>false</array>
<paramDesc>建议供应商名称,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>达利园供应商</example>
<fullName/>
<ytenantId/>
<paramOrder>93</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.adviseSupplier.name","cItemName":"applyorders_adviseSupplier_name","cCaption":"建议供应商","cShowCaption":"建议供应商","iMaxLength":255,"bHidden":true,"cRefType":"yssupplier.aa_vendor","cRefId":null,"cRefRetId":{"adviseSupplier":"id"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"Column","refReturn":null,"dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122555</id>
<name>applyorders_vendor</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355212</defParamId>
<array>false</array>
<paramDesc>建议供应商id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996919652626202626</example>
<fullName/>
<ytenantId/>
<paramOrder>94</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122556</id>
<name>applyorders_vendor_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355213</defParamId>
<array>false</array>
<paramDesc>建议供应商名称,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>达利园供应商</example>
<fullName/>
<ytenantId/>
<paramOrder>95</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.vendor.name","cItemName":"applyorders_vendor_name","cCaption":"建议供应商","cShowCaption":"建议供应商","iMaxLength":255,"bHidden":false,"cRefType":"yssupplier.aa_vendor","cRefId":null,"cRefRetId":{"vendor":"id","contact":"contact","applyorders_vendor_taxrate":"taxrate","applyOrders_vendor_define@1@@30":"customItems!define@1@@30"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"Column","refReturn":null,"dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122557</id>
<name>applyorders_memo</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355214</defParamId>
<array>false</array>
<paramDesc>备注,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>要货请购</example>
<fullName/>
<ytenantId/>
<paramOrder>96</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122400</id>
<name>bodyItem</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<children>
<children>
<id>2106049437256122401</id>
<name>id</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355216</defParamId>
<array>false</array>
<paramDesc>表体自定义项id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996919652626202627</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122402</id>
<name>define1</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355217</defParamId>
<array>false</array>
<paramDesc>表体自定义项1,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项1</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122403</id>
<name>define2</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355218</defParamId>
<array>false</array>
<paramDesc>表体自定义项2,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项2</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122404</id>
<name>define3</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355219</defParamId>
<array>false</array>
<paramDesc>表体自定义项3,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项3</example>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122405</id>
<name>define56</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355220</defParamId>
<array>false</array>
<paramDesc>表体自定义项56,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项56</example>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122406</id>
<name>define57</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355221</defParamId>
<array>false</array>
<paramDesc>表体自定义项57,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项57</example>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122407</id>
<name>define58</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355222</defParamId>
<array>false</array>
<paramDesc>表体自定义项58,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项58</example>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122408</id>
<name>define59</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355223</defParamId>
<array>false</array>
<paramDesc>表体自定义项59,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项59</example>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122409</id>
<name>define60</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355224</defParamId>
<array>false</array>
<paramDesc>表体自定义项60,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项60</example>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122410</id>
<name>define4</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355225</defParamId>
<array>false</array>
<paramDesc>表体自定义项4,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项4</example>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122411</id>
<name>define31</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355226</defParamId>
<array>false</array>
<paramDesc>表体自定义项31,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项31</example>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122412</id>
<name>define32</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355227</defParamId>
<array>false</array>
<paramDesc>表体自定义项32,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项32</example>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122413</id>
<name>define33</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355228</defParamId>
<array>false</array>
<paramDesc>表体自定义项33,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项33</example>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122414</id>
<name>define34</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355229</defParamId>
<array>false</array>
<paramDesc>表体自定义项34,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项34</example>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122415</id>
<name>define35</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355230</defParamId>
<array>false</array>
<paramDesc>表体自定义项35,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项35</example>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122416</id>
<name>define36</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355231</defParamId>
<array>false</array>
<paramDesc>表体自定义项36,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项36</example>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122417</id>
<name>define37</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355232</defParamId>
<array>false</array>
<paramDesc>表体自定义项37,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项37</example>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122418</id>
<name>define38</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355233</defParamId>
<array>false</array>
<paramDesc>表体自定义项38,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项38</example>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122419</id>
<name>define39</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355234</defParamId>
<array>false</array>
<paramDesc>表体自定义项39,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项39</example>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122420</id>
<name>define40</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355235</defParamId>
<array>false</array>
<paramDesc>表体自定义项40,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项40</example>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122421</id>
<name>define41</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355236</defParamId>
<array>false</array>
<paramDesc>表体自定义项41,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项41</example>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122422</id>
<name>define42</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355237</defParamId>
<array>false</array>
<paramDesc>表体自定义项42,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项42</example>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122423</id>
<name>define43</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355238</defParamId>
<array>false</array>
<paramDesc>表体自定义项43,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项43</example>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122424</id>
<name>define44</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355239</defParamId>
<array>false</array>
<paramDesc>表体自定义项44,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项44</example>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122425</id>
<name>define45</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355240</defParamId>
<array>false</array>
<paramDesc>表体自定义项45,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项45</example>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122426</id>
<name>define46</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355241</defParamId>
<array>false</array>
<paramDesc>表体自定义项46,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项46</example>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122427</id>
<name>define47</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355242</defParamId>
<array>false</array>
<paramDesc>表体自定义项47,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项47</example>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122428</id>
<name>define48</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355243</defParamId>
<array>false</array>
<paramDesc>表体自定义项48,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项48</example>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122429</id>
<name>define49</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355244</defParamId>
<array>false</array>
<paramDesc>表体自定义项49,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项49</example>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122430</id>
<name>define50</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355245</defParamId>
<array>false</array>
<paramDesc>表体自定义项50,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项50</example>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122431</id>
<name>define51</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355246</defParamId>
<array>false</array>
<paramDesc>表体自定义项51,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项51</example>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122432</id>
<name>define52</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355247</defParamId>
<array>false</array>
<paramDesc>表体自定义项52,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项52</example>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122433</id>
<name>define53</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355248</defParamId>
<array>false</array>
<paramDesc>表体自定义项53,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项53</example>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122434</id>
<name>define54</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355249</defParamId>
<array>false</array>
<paramDesc>表体自定义项54,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项54</example>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122435</id>
<name>define55</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355250</defParamId>
<array>false</array>
<paramDesc>表体自定义项55,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项55</example>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122436</id>
<name>define5</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355251</defParamId>
<array>false</array>
<paramDesc>表体自定义项5,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项5</example>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122437</id>
<name>define6</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355252</defParamId>
<array>false</array>
<paramDesc>表体自定义项6,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项6</example>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122438</id>
<name>define7</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355253</defParamId>
<array>false</array>
<paramDesc>表体自定义项7,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项7</example>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122439</id>
<name>define8</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355254</defParamId>
<array>false</array>
<paramDesc>表体自定义项8,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项8</example>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122440</id>
<name>define9</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355255</defParamId>
<array>false</array>
<paramDesc>表体自定义项9,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项9</example>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122441</id>
<name>define10</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355256</defParamId>
<array>false</array>
<paramDesc>表体自定义项10,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项10</example>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122442</id>
<name>define11</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355257</defParamId>
<array>false</array>
<paramDesc>表体自定义项11,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项11</example>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122443</id>
<name>define12</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355258</defParamId>
<array>false</array>
<paramDesc>表体自定义项12,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项12</example>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122444</id>
<name>define13</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355259</defParamId>
<array>false</array>
<paramDesc>表体自定义项13,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项13</example>
<fullName/>
<ytenantId/>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122445</id>
<name>define14</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355260</defParamId>
<array>false</array>
<paramDesc>表体自定义项14,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项14</example>
<fullName/>
<ytenantId/>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122446</id>
<name>define15</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355261</defParamId>
<array>false</array>
<paramDesc>表体自定义项15,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项15</example>
<fullName/>
<ytenantId/>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122447</id>
<name>define16</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355262</defParamId>
<array>false</array>
<paramDesc>表体自定义项16,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项16</example>
<fullName/>
<ytenantId/>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122448</id>
<name>define17</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355263</defParamId>
<array>false</array>
<paramDesc>表体自定义项17,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项17</example>
<fullName/>
<ytenantId/>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122449</id>
<name>define18</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355264</defParamId>
<array>false</array>
<paramDesc>表体自定义项18,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项18</example>
<fullName/>
<ytenantId/>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122450</id>
<name>define19</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355265</defParamId>
<array>false</array>
<paramDesc>表体自定义项19,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项19</example>
<fullName/>
<ytenantId/>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122451</id>
<name>define20</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355266</defParamId>
<array>false</array>
<paramDesc>表体自定义项20,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项20</example>
<fullName/>
<ytenantId/>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122452</id>
<name>define21</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355267</defParamId>
<array>false</array>
<paramDesc>表体自定义项21,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项21</example>
<fullName/>
<ytenantId/>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122453</id>
<name>define22</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355268</defParamId>
<array>false</array>
<paramDesc>表体自定义项22,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项22</example>
<fullName/>
<ytenantId/>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122454</id>
<name>define23</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355269</defParamId>
<array>false</array>
<paramDesc>表体自定义项23,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项23</example>
<fullName/>
<ytenantId/>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122455</id>
<name>define24</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355270</defParamId>
<array>false</array>
<paramDesc>表体自定义项24,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项24</example>
<fullName/>
<ytenantId/>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122456</id>
<name>define25</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355271</defParamId>
<array>false</array>
<paramDesc>表体自定义项25,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项25</example>
<fullName/>
<ytenantId/>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122457</id>
<name>define26</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355272</defParamId>
<array>false</array>
<paramDesc>表体自定义项26,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项26</example>
<fullName/>
<ytenantId/>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122458</id>
<name>define27</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355273</defParamId>
<array>false</array>
<paramDesc>表体自定义项27,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项27</example>
<fullName/>
<ytenantId/>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122459</id>
<name>define28</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355274</defParamId>
<array>false</array>
<paramDesc>表体自定义项28,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项28</example>
<fullName/>
<ytenantId/>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122460</id>
<name>define29</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355275</defParamId>
<array>false</array>
<paramDesc>表体自定义项29,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项29</example>
<fullName/>
<ytenantId/>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122461</id>
<name>define30</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122400</parentId>
<defParamId>1996896485556355276</defParamId>
<array>false</array>
<paramDesc>表体自定义项30,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>表体自定义项30</example>
<fullName/>
<ytenantId/>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1996896485556355215</defParamId>
<array>false</array>
<paramDesc>表体自定义项</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>97</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible/>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122558</id>
<name>applyorders_productsku_modelDescription</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355277</defParamId>
<array>false</array>
<paramDesc>sku规格型号,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>33mm</example>
<fullName/>
<ytenantId/>
<paramOrder>98</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122559</id>
<name>applyorders_product_model</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355278</defParamId>
<array>false</array>
<paramDesc>型号,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>33mm</example>
<fullName/>
<ytenantId/>
<paramOrder>99</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122560</id>
<name>applyorders_product_modelDescription</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355279</defParamId>
<array>false</array>
<paramDesc>规格说明,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>细口</example>
<fullName/>
<ytenantId/>
<paramOrder>100</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122561</id>
<name>applyorders_propertiesValue</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355280</defParamId>
<array>false</array>
<paramDesc>规格,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>细口</example>
<fullName/>
<ytenantId/>
<paramOrder>101</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122562</id>
<name>project</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355281</defParamId>
<array>false</array>
<paramDesc>项目id,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1996923973363302404</example>
<fullName/>
<ytenantId/>
<paramOrder>102</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122563</id>
<name>project_code</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355282</defParamId>
<array>false</array>
<paramDesc>项目编码,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>00034</example>
<fullName/>
<ytenantId/>
<paramOrder>103</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.project.code","cItemName":"project_code","cCaption":"项目编码","cShowCaption":"项目编码","iMaxLength":255,"bHidden":true,"cRefType":"ucfbasedoc.bd_outer_projectcardMCref","cRefId":null,"cRefRetId":{"project":"id","project_code":"code","project_name":"name"},"cDataRule":"\"<%loginUser.storeId%>\"==\"null\"","iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":false,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"Refer","refReturn":"code","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122564</id>
<name>applyOrders_vendor_define1</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355283</defParamId>
<array>false</array>
<paramDesc>供应商自定义项1,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项1</example>
<fullName/>
<ytenantId/>
<paramOrder>104</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122565</id>
<name>project_name</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355284</defParamId>
<array>false</array>
<paramDesc>项目名称,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>虚拟项目</example>
<fullName/>
<ytenantId/>
<paramOrder>105</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"applyOrders.project.name","cItemName":"project_name","cCaption":"项目名称","cShowCaption":"项目名称","iMaxLength":255,"bHidden":false,"cRefType":"ucfbasedoc.bd_outer_projectcardMCref","cRefId":null,"cRefRetId":{"project":"id","project_code":"code","project_name":"name"},"cDataRule":"\"<%loginUser.storeId%>\"==\"null\"","iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"pu.applyorder.ApplyOrders","cControlType":"Refer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122566</id>
<name>applyOrders_vendor_define2</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355285</defParamId>
<array>false</array>
<paramDesc>供应商自定义项2,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项2</example>
<fullName/>
<ytenantId/>
<paramOrder>106</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122567</id>
<name>applyorders_trackNo</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355286</defParamId>
<array>false</array>
<paramDesc>跟踪号,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0002</example>
<fullName/>
<ytenantId/>
<paramOrder>107</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122568</id>
<name>applyOrders_vendor_define3</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355287</defParamId>
<array>false</array>
<paramDesc>供应商自定义项3,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项3</example>
<fullName/>
<ytenantId/>
<paramOrder>108</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122569</id>
<name>applyOrders_vendor_define4</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355288</defParamId>
<array>false</array>
<paramDesc>供应商自定义项4,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项4</example>
<fullName/>
<ytenantId/>
<paramOrder>109</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122570</id>
<name>applyOrders_vendor_define5</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355289</defParamId>
<array>false</array>
<paramDesc>供应商自定义项5,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项5</example>
<fullName/>
<ytenantId/>
<paramOrder>110</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122571</id>
<name>applyOrders_vendor_define6</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355290</defParamId>
<array>false</array>
<paramDesc>供应商自定义项6,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项6</example>
<fullName/>
<ytenantId/>
<paramOrder>111</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122572</id>
<name>applyOrders_vendor_define7</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355291</defParamId>
<array>false</array>
<paramDesc>供应商自定义项7,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项7</example>
<fullName/>
<ytenantId/>
<paramOrder>112</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122573</id>
<name>applyOrders_vendor_define8</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355292</defParamId>
<array>false</array>
<paramDesc>供应商自定义项8,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项8</example>
<fullName/>
<ytenantId/>
<paramOrder>113</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122574</id>
<name>applyOrders_vendor_define9</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355293</defParamId>
<array>false</array>
<paramDesc>供应商自定义项9,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项9</example>
<fullName/>
<ytenantId/>
<paramOrder>114</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122575</id>
<name>applyOrders_vendor_define10</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355294</defParamId>
<array>false</array>
<paramDesc>供应商自定义项10,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项10</example>
<fullName/>
<ytenantId/>
<paramOrder>115</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122576</id>
<name>applyOrders_vendor_define11</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355295</defParamId>
<array>false</array>
<paramDesc>供应商自定义项11,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项11</example>
<fullName/>
<ytenantId/>
<paramOrder>116</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122577</id>
<name>applyOrders_vendor_define12</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355296</defParamId>
<array>false</array>
<paramDesc>供应商自定义项12,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项12</example>
<fullName/>
<ytenantId/>
<paramOrder>117</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122578</id>
<name>applyOrders_vendor_define13</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355297</defParamId>
<array>false</array>
<paramDesc>供应商自定义项13,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项13</example>
<fullName/>
<ytenantId/>
<paramOrder>118</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122579</id>
<name>applyOrders_vendor_define14</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355298</defParamId>
<array>false</array>
<paramDesc>供应商自定义项14,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项14</example>
<fullName/>
<ytenantId/>
<paramOrder>119</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122580</id>
<name>applyOrders_vendor_define15</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355299</defParamId>
<array>false</array>
<paramDesc>供应商自定义项15,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项15</example>
<fullName/>
<ytenantId/>
<paramOrder>120</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122581</id>
<name>applyOrders_vendor_define16</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355300</defParamId>
<array>false</array>
<paramDesc>供应商自定义项16,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项16</example>
<fullName/>
<ytenantId/>
<paramOrder>121</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122582</id>
<name>applyOrders_vendor_define17</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355301</defParamId>
<array>false</array>
<paramDesc>供应商自定义项17,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项17</example>
<fullName/>
<ytenantId/>
<paramOrder>122</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122583</id>
<name>applyOrders_vendor_define18</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355302</defParamId>
<array>false</array>
<paramDesc>供应商自定义项18,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项18</example>
<fullName/>
<ytenantId/>
<paramOrder>123</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122584</id>
<name>applyOrders_vendor_define19</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355303</defParamId>
<array>false</array>
<paramDesc>供应商自定义项19,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项19</example>
<fullName/>
<ytenantId/>
<paramOrder>124</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122585</id>
<name>applyOrders_vendor_define20</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355304</defParamId>
<array>false</array>
<paramDesc>供应商自定义项20,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项20</example>
<fullName/>
<ytenantId/>
<paramOrder>125</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122586</id>
<name>applyOrders_vendor_define21</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355305</defParamId>
<array>false</array>
<paramDesc>供应商自定义项21,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项21</example>
<fullName/>
<ytenantId/>
<paramOrder>126</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122587</id>
<name>applyOrders_vendor_define22</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355306</defParamId>
<array>false</array>
<paramDesc>供应商自定义项22,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项22</example>
<fullName/>
<ytenantId/>
<paramOrder>127</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122588</id>
<name>applyOrders_vendor_define23</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355307</defParamId>
<array>false</array>
<paramDesc>供应商自定义项23,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项23</example>
<fullName/>
<ytenantId/>
<paramOrder>128</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122589</id>
<name>applyOrders_vendor_define24</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355308</defParamId>
<array>false</array>
<paramDesc>供应商自定义项24,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项24</example>
<fullName/>
<ytenantId/>
<paramOrder>129</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122590</id>
<name>applyOrders_vendor_define25</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355309</defParamId>
<array>false</array>
<paramDesc>供应商自定义项25,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项25</example>
<fullName/>
<ytenantId/>
<paramOrder>130</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122591</id>
<name>applyOrders_vendor_define26</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355310</defParamId>
<array>false</array>
<paramDesc>供应商自定义项26,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项26</example>
<fullName/>
<ytenantId/>
<paramOrder>131</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122592</id>
<name>applyOrders_vendor_define27</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355311</defParamId>
<array>false</array>
<paramDesc>供应商自定义项27,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项27</example>
<fullName/>
<ytenantId/>
<paramOrder>132</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122593</id>
<name>applyOrders_vendor_define28</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355312</defParamId>
<array>false</array>
<paramDesc>供应商自定义项28,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项28</example>
<fullName/>
<ytenantId/>
<paramOrder>133</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122594</id>
<name>applyOrders_vendor_define29</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355313</defParamId>
<array>false</array>
<paramDesc>供应商自定义项29,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项29</example>
<fullName/>
<ytenantId/>
<paramOrder>134</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2106049437256122595</id>
<name>applyOrders_vendor_define30</name>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<parentId>2106049437256122394</parentId>
<defParamId>1996896485556355314</defParamId>
<array>false</array>
<paramDesc>供应商自定义项30,只有明细场景返回</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>供应商自定义项30</example>
<fullName/>
<ytenantId/>
<paramOrder>135</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1996896485556355112</defParamId>
<array>true</array>
<paramDesc>返回列表信息</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1996896485556355104</defParamId>
<array>false</array>
<paramDesc>调用成功时的返回数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-10-08 16:33:43.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:43.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2106049445846056979</id>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 1, "pageSize": 10, "pageCount": 10, "beginPageIndex": 1, "endPageIndex": 10, "recordCount": 100, "pubts": "2024-03-04 00:00:00", "recordList": [ { "vouchdate": "2021-03-04 00:00:00", "code": "CGQG0000201905100001", "returncount": "1", "isWfControlled": "1", "verifystate": "1", "bustype": "****************", "bustype_name": "采购要货", "applyDept": "1996900540021735427", "applyDept_name": "数智请购部门", "bizstatus": "0", "status": "0", "currency": "1996901321700016134", "currency_name": "人名币", "warehouseId": "1996901321700016135", "warehouseId_name": "货品仓库", "source": "MR.mr_lrp_plan_order_batch", "store": "1996902524296626181", "isUretailVoucher": "false", "store_name": "零售门店", "org": "1996902524296626182", "org_name": "达利园组织", "custom": "1996903245851131908", "creator": "张三", "createTime": "2021-03-04 00:00:00", "modifier": "张三", "modifyTime": "2021-03-04 00:00:00", "closer": "李响", "closeTime": "2021-03-05 00:00:00", "locker": "张三", "lockTime": "2021-03-05 00:00:00", "operator": "1996904998192021506", "operator_name": "王晨", "auditor": "刘策", "auditTime": "2021-03-05 00:00:00", "auditDate": "2021-03-05 12:36:12", "submitor": "王晨", "submitTime": "2021-03-05 12:20:12", "totalQuantity": 200, "memo": "来货请购", "id": "1996904998192021507", "pubts": "2021-03-04 00:00:00", "tplid": "1996906535790313475", "headItem": { "id": "1996906535790313476", "define1": "表头自定义项1", "define2": "表头自定义项2", "define3": "表头自定义项3" }, "applyorders_execStatus": "表头自定义项4", "applyorders_receiveOrg": "1996916044853673985", "applyorders_receiveOrg_name": "达利园组织", "applyorders_purchaseOrg": "1996916044853673985", "applyorders_purchaseOrg_name": "达利园组织", "applyorders_purDept": "1996916044853673986", "applyorders_purDept_name": "采购部门", "applyorders_purPerson": "1996916551654047750", "applyorders_purPerson_name": "李晨", "applyOrders_supplyMoney": "200", "applyOrder_orderMoneyRatio": "20", "applyorders_supplyCount": 20, "apporders_id": "1996916551654047751", "applyorders_product": "1996917006926348290", "product_defaultAlbumId": "1996917006926348291", "applyorders_product_cCode": "00000002", "applyorders_product_cName": "苹果", "applyorders_productsku": "1996917006926348290", "applyorders_productsku_cCode": "00000002", "applyorders_productsku_cName": "苹果", "applyorders_currency": "1996917006926348290", "applyorders_currency_name": "人民币", "applyorders_currency_priceDigit": "2", "applyorders_currency_moneyDigit": "2", "applyorders_qty": 20, "applyorders_subQty": 20, "applyorders_rowno": 10, "unit_Precision": "2", "applyorders_unit": "1996918364130246657", "applyorders_unit_name": "吨", "applyorders_product_oUnitId": "1996918364130246657", "applyorders_product_productOfflineRetail_purchaseUnit": "吨", "applyorders_invExchRate": 1, "applyorders_productOfflineRetail_purchaseRate": 1, "priceUOM": "1996918364130246658", "priceUOM_Name": "吨", "invPriceExchRate": 1, "unitExchangeTypePrice": "0", "priceUOM_Precision": "2", "taxRate": "2", "oriTax": 20, "oriTaxUnitPrice": 12, "oriUnitPrice": 6, "oriMoney": 150, "oriSum": 200, "applyorders_product_primeCosts": 23, "applyorders_productsku_primeCosts": 24, "applyorders_requirementDate": "2021-03-04 00:00:00", "applyorders_adviseOrderDate": "2021-03-04 00:00:00", "applyorders_adviseSupplier": "1996919652626202626", "applyorders_adviseSupplier_name": "达利园供应商", "applyorders_vendor": "1996919652626202626", "applyorders_vendor_name": "达利园供应商", "applyorders_memo": "要货请购", "bodyItem": { "id": "1996919652626202627", "define1": "表体自定义项1", "define2": "表体自定义项2", "define3": "表体自定义项3", "define56": "表体自定义项56", "define57": "表体自定义项57", "define58": "表体自定义项58", "define59": "表体自定义项59", "define60": "表体自定义项60", "define4": "表体自定义项4", "define31": "表体自定义项31", "define32": "表体自定义项32", "define33": "表体自定义项33", "define34": "表体自定义项34", "define35": "表体自定义项35", "define36": "表体自定义项36", "define37": "表体自定义项37", "define38": "表体自定义项38", "define39": "表体自定义项39", "define40": "表体自定义项40", "define41": "表体自定义项41", "define42": "表体自定义项42", "define43": "表体自定义项43", "define44": "表体自定义项44", "define45": "表体自定义项45", "define46": "表体自定义项46", "define47": "表体自定义项47", "define48": "表体自定义项48", "define49": "表体自定义项49", "define50": "表体自定义项50", "define51": "表体自定义项51", "define52": "表体自定义项52", "define53": "表体自定义项53", "define54": "表体自定义项54", "define55": "表体自定义项55", "define5": "表体自定义项5", "define6": "表体自定义项6", "define7": "表体自定义项7", "define8": "表体自定义项8", "define9": "表体自定义项9", "define10": "表体自定义项10", "define11": "表体自定义项11", "define12": "表体自定义项12", "define13": "表体自定义项13", "define14": "表体自定义项14", "define15": "表体自定义项15", "define16": "表体自定义项16", "define17": "表体自定义项17", "define18": "表体自定义项18", "define19": "表体自定义项19", "define20": "表体自定义项20", "define21": "表体自定义项21", "define22": "表体自定义项22", "define23": "表体自定义项23", "define24": "表体自定义项24", "define25": "表体自定义项25", "define26": "表体自定义项26", "define27": "表体自定义项27", "define28": "表体自定义项28", "define29": "表体自定义项29", "define30": "表体自定义项30" }, "applyorders_productsku_modelDescription": "33mm", "applyorders_product_model": "33mm", "applyorders_product_modelDescription": "细口", "applyorders_propertiesValue": "细口", "project": "1996923973363302404", "project_code": "00034", "applyOrders_vendor_define1": "供应商自定义项1", "project_name": "虚拟项目", "applyOrders_vendor_define2": "供应商自定义项2", "applyorders_trackNo": "0002", "applyOrders_vendor_define3": "供应商自定义项3", "applyOrders_vendor_define4": "供应商自定义项4", "applyOrders_vendor_define5": "供应商自定义项5", "applyOrders_vendor_define6": "供应商自定义项6", "applyOrders_vendor_define7": "供应商自定义项7", "applyOrders_vendor_define8": "供应商自定义项8", "applyOrders_vendor_define9": "供应商自定义项9", "applyOrders_vendor_define10": "供应商自定义项10", "applyOrders_vendor_define11": "供应商自定义项11", "applyOrders_vendor_define12": "供应商自定义项12", "applyOrders_vendor_define13": "供应商自定义项13", "applyOrders_vendor_define14": "供应商自定义项14", "applyOrders_vendor_define15": "供应商自定义项15", "applyOrders_vendor_define16": "供应商自定义项16", "applyOrders_vendor_define17": "供应商自定义项17", "applyOrders_vendor_define18": "供应商自定义项18", "applyOrders_vendor_define19": "供应商自定义项19", "applyOrders_vendor_define20": "供应商自定义项20", "applyOrders_vendor_define21": "供应商自定义项21", "applyOrders_vendor_define22": "供应商自定义项22", "applyOrders_vendor_define23": "供应商自定义项23", "applyOrders_vendor_define24": "供应商自定义项24", "applyOrders_vendor_define25": "供应商自定义项25", "applyOrders_vendor_define26": "供应商自定义项26", "applyOrders_vendor_define27": "供应商自定义项27", "applyOrders_vendor_define28": "供应商自定义项28", "applyOrders_vendor_define29": "供应商自定义项29", "applyOrders_vendor_define30": "供应商自定义项30", "applyOrderDefineCharacter": "id : \"1995982748459204616\" pubts : \"2024-05-13 09:16:15\" ytenant : \"0000LJ5I3I7H6YAPZ90000\"", "applyOrdersDefineCharacter": "id : \"1995982748459204616\" pubts : \"2024-05-13 09:16:15\" ytenant : \"0000LJ5I3I7H6YAPZ90000\"", "applyOrdersCharacteristics": "id : \"1995982748459204616\" pubts : \"2024-05-13 09:16:15\" ytenant : \"0000LJ5I3I7H6YAPZ90000\"" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2106049445846056980</id>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<content>{ "code": 999, "message": "服务端逻辑异常" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2106049445846056979</id>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 1, "pageSize": 10, "pageCount": 10, "beginPageIndex": 1, "endPageIndex": 10, "recordCount": 100, "pubts": "2024-03-04 00:00:00", "recordList": [ { "vouchdate": "2021-03-04 00:00:00", "code": "CGQG0000201905100001", "returncount": "1", "isWfControlled": "1", "verifystate": "1", "bustype": "****************", "bustype_name": "采购要货", "applyDept": "1996900540021735427", "applyDept_name": "数智请购部门", "bizstatus": "0", "status": "0", "currency": "1996901321700016134", "currency_name": "人名币", "warehouseId": "1996901321700016135", "warehouseId_name": "货品仓库", "source": "MR.mr_lrp_plan_order_batch", "store": "1996902524296626181", "isUretailVoucher": "false", "store_name": "零售门店", "org": "1996902524296626182", "org_name": "达利园组织", "custom": "1996903245851131908", "creator": "张三", "createTime": "2021-03-04 00:00:00", "modifier": "张三", "modifyTime": "2021-03-04 00:00:00", "closer": "李响", "closeTime": "2021-03-05 00:00:00", "locker": "张三", "lockTime": "2021-03-05 00:00:00", "operator": "1996904998192021506", "operator_name": "王晨", "auditor": "刘策", "auditTime": "2021-03-05 00:00:00", "auditDate": "2021-03-05 12:36:12", "submitor": "王晨", "submitTime": "2021-03-05 12:20:12", "totalQuantity": 200, "memo": "来货请购", "id": "1996904998192021507", "pubts": "2021-03-04 00:00:00", "tplid": "1996906535790313475", "headItem": { "id": "1996906535790313476", "define1": "表头自定义项1", "define2": "表头自定义项2", "define3": "表头自定义项3" }, "applyorders_execStatus": "表头自定义项4", "applyorders_receiveOrg": "1996916044853673985", "applyorders_receiveOrg_name": "达利园组织", "applyorders_purchaseOrg": "1996916044853673985", "applyorders_purchaseOrg_name": "达利园组织", "applyorders_purDept": "1996916044853673986", "applyorders_purDept_name": "采购部门", "applyorders_purPerson": "1996916551654047750", "applyorders_purPerson_name": "李晨", "applyOrders_supplyMoney": "200", "applyOrder_orderMoneyRatio": "20", "applyorders_supplyCount": 20, "apporders_id": "1996916551654047751", "applyorders_product": "1996917006926348290", "product_defaultAlbumId": "1996917006926348291", "applyorders_product_cCode": "00000002", "applyorders_product_cName": "苹果", "applyorders_productsku": "1996917006926348290", "applyorders_productsku_cCode": "00000002", "applyorders_productsku_cName": "苹果", "applyorders_currency": "1996917006926348290", "applyorders_currency_name": "人民币", "applyorders_currency_priceDigit": "2", "applyorders_currency_moneyDigit": "2", "applyorders_qty": 20, "applyorders_subQty": 20, "applyorders_rowno": 10, "unit_Precision": "2", "applyorders_unit": "1996918364130246657", "applyorders_unit_name": "吨", "applyorders_product_oUnitId": "1996918364130246657", "applyorders_product_productOfflineRetail_purchaseUnit": "吨", "applyorders_invExchRate": 1, "applyorders_productOfflineRetail_purchaseRate": 1, "priceUOM": "1996918364130246658", "priceUOM_Name": "吨", "invPriceExchRate": 1, "unitExchangeTypePrice": "0", "priceUOM_Precision": "2", "taxRate": "2", "oriTax": 20, "oriTaxUnitPrice": 12, "oriUnitPrice": 6, "oriMoney": 150, "oriSum": 200, "applyorders_product_primeCosts": 23, "applyorders_productsku_primeCosts": 24, "applyorders_requirementDate": "2021-03-04 00:00:00", "applyorders_adviseOrderDate": "2021-03-04 00:00:00", "applyorders_adviseSupplier": "1996919652626202626", "applyorders_adviseSupplier_name": "达利园供应商", "applyorders_vendor": "1996919652626202626", "applyorders_vendor_name": "达利园供应商", "applyorders_memo": "要货请购", "bodyItem": { "id": "1996919652626202627", "define1": "表体自定义项1", "define2": "表体自定义项2", "define3": "表体自定义项3", "define56": "表体自定义项56", "define57": "表体自定义项57", "define58": "表体自定义项58", "define59": "表体自定义项59", "define60": "表体自定义项60", "define4": "表体自定义项4", "define31": "表体自定义项31", "define32": "表体自定义项32", "define33": "表体自定义项33", "define34": "表体自定义项34", "define35": "表体自定义项35", "define36": "表体自定义项36", "define37": "表体自定义项37", "define38": "表体自定义项38", "define39": "表体自定义项39", "define40": "表体自定义项40", "define41": "表体自定义项41", "define42": "表体自定义项42", "define43": "表体自定义项43", "define44": "表体自定义项44", "define45": "表体自定义项45", "define46": "表体自定义项46", "define47": "表体自定义项47", "define48": "表体自定义项48", "define49": "表体自定义项49", "define50": "表体自定义项50", "define51": "表体自定义项51", "define52": "表体自定义项52", "define53": "表体自定义项53", "define54": "表体自定义项54", "define55": "表体自定义项55", "define5": "表体自定义项5", "define6": "表体自定义项6", "define7": "表体自定义项7", "define8": "表体自定义项8", "define9": "表体自定义项9", "define10": "表体自定义项10", "define11": "表体自定义项11", "define12": "表体自定义项12", "define13": "表体自定义项13", "define14": "表体自定义项14", "define15": "表体自定义项15", "define16": "表体自定义项16", "define17": "表体自定义项17", "define18": "表体自定义项18", "define19": "表体自定义项19", "define20": "表体自定义项20", "define21": "表体自定义项21", "define22": "表体自定义项22", "define23": "表体自定义项23", "define24": "表体自定义项24", "define25": "表体自定义项25", "define26": "表体自定义项26", "define27": "表体自定义项27", "define28": "表体自定义项28", "define29": "表体自定义项29", "define30": "表体自定义项30" }, "applyorders_productsku_modelDescription": "33mm", "applyorders_product_model": "33mm", "applyorders_product_modelDescription": "细口", "applyorders_propertiesValue": "细口", "project": "1996923973363302404", "project_code": "00034", "applyOrders_vendor_define1": "供应商自定义项1", "project_name": "虚拟项目", "applyOrders_vendor_define2": "供应商自定义项2", "applyorders_trackNo": "0002", "applyOrders_vendor_define3": "供应商自定义项3", "applyOrders_vendor_define4": "供应商自定义项4", "applyOrders_vendor_define5": "供应商自定义项5", "applyOrders_vendor_define6": "供应商自定义项6", "applyOrders_vendor_define7": "供应商自定义项7", "applyOrders_vendor_define8": "供应商自定义项8", "applyOrders_vendor_define9": "供应商自定义项9", "applyOrders_vendor_define10": "供应商自定义项10", "applyOrders_vendor_define11": "供应商自定义项11", "applyOrders_vendor_define12": "供应商自定义项12", "applyOrders_vendor_define13": "供应商自定义项13", "applyOrders_vendor_define14": "供应商自定义项14", "applyOrders_vendor_define15": "供应商自定义项15", "applyOrders_vendor_define16": "供应商自定义项16", "applyOrders_vendor_define17": "供应商自定义项17", "applyOrders_vendor_define18": "供应商自定义项18", "applyOrders_vendor_define19": "供应商自定义项19", "applyOrders_vendor_define20": "供应商自定义项20", "applyOrders_vendor_define21": "供应商自定义项21", "applyOrders_vendor_define22": "供应商自定义项22", "applyOrders_vendor_define23": "供应商自定义项23", "applyOrders_vendor_define24": "供应商自定义项24", "applyOrders_vendor_define25": "供应商自定义项25", "applyOrders_vendor_define26": "供应商自定义项26", "applyOrders_vendor_define27": "供应商自定义项27", "applyOrders_vendor_define28": "供应商自定义项28", "applyOrders_vendor_define29": "供应商自定义项29", "applyOrders_vendor_define30": "供应商自定义项30", "applyOrderDefineCharacter": "id : \"1995982748459204616\" pubts : \"2024-05-13 09:16:15\" ytenant : \"0000LJ5I3I7H6YAPZ90000\"", "applyOrdersDefineCharacter": "id : \"1995982748459204616\" pubts : \"2024-05-13 09:16:15\" ytenant : \"0000LJ5I3I7H6YAPZ90000\"", "applyOrdersCharacteristics": "id : \"1995982748459204616\" pubts : \"2024-05-13 09:16:15\" ytenant : \"0000LJ5I3I7H6YAPZ90000\"" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2106049445846056980</id>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<content>{ "code": 999, "message": "服务端逻辑异常" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>2106049445846056979</id>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 1, "pageSize": 10, "pageCount": 10, "beginPageIndex": 1, "endPageIndex": 10, "recordCount": 100, "pubts": "2024-03-04 00:00:00", "recordList": [ { "vouchdate": "2021-03-04 00:00:00", "code": "CGQG0000201905100001", "returncount": "1", "isWfControlled": "1", "verifystate": "1", "bustype": "****************", "bustype_name": "采购要货", "applyDept": "1996900540021735427", "applyDept_name": "数智请购部门", "bizstatus": "0", "status": "0", "currency": "1996901321700016134", "currency_name": "人名币", "warehouseId": "1996901321700016135", "warehouseId_name": "货品仓库", "source": "MR.mr_lrp_plan_order_batch", "store": "1996902524296626181", "isUretailVoucher": "false", "store_name": "零售门店", "org": "1996902524296626182", "org_name": "达利园组织", "custom": "1996903245851131908", "creator": "张三", "createTime": "2021-03-04 00:00:00", "modifier": "张三", "modifyTime": "2021-03-04 00:00:00", "closer": "李响", "closeTime": "2021-03-05 00:00:00", "locker": "张三", "lockTime": "2021-03-05 00:00:00", "operator": "1996904998192021506", "operator_name": "王晨", "auditor": "刘策", "auditTime": "2021-03-05 00:00:00", "auditDate": "2021-03-05 12:36:12", "submitor": "王晨", "submitTime": "2021-03-05 12:20:12", "totalQuantity": 200, "memo": "来货请购", "id": "1996904998192021507", "pubts": "2021-03-04 00:00:00", "tplid": "1996906535790313475", "headItem": { "id": "1996906535790313476", "define1": "表头自定义项1", "define2": "表头自定义项2", "define3": "表头自定义项3" }, "applyorders_execStatus": "表头自定义项4", "applyorders_receiveOrg": "1996916044853673985", "applyorders_receiveOrg_name": "达利园组织", "applyorders_purchaseOrg": "1996916044853673985", "applyorders_purchaseOrg_name": "达利园组织", "applyorders_purDept": "1996916044853673986", "applyorders_purDept_name": "采购部门", "applyorders_purPerson": "1996916551654047750", "applyorders_purPerson_name": "李晨", "applyOrders_supplyMoney": "200", "applyOrder_orderMoneyRatio": "20", "applyorders_supplyCount": 20, "apporders_id": "1996916551654047751", "applyorders_product": "1996917006926348290", "product_defaultAlbumId": "1996917006926348291", "applyorders_product_cCode": "00000002", "applyorders_product_cName": "苹果", "applyorders_productsku": "1996917006926348290", "applyorders_productsku_cCode": "00000002", "applyorders_productsku_cName": "苹果", "applyorders_currency": "1996917006926348290", "applyorders_currency_name": "人民币", "applyorders_currency_priceDigit": "2", "applyorders_currency_moneyDigit": "2", "applyorders_qty": 20, "applyorders_subQty": 20, "applyorders_rowno": 10, "unit_Precision": "2", "applyorders_unit": "1996918364130246657", "applyorders_unit_name": "吨", "applyorders_product_oUnitId": "1996918364130246657", "applyorders_product_productOfflineRetail_purchaseUnit": "吨", "applyorders_invExchRate": 1, "applyorders_productOfflineRetail_purchaseRate": 1, "priceUOM": "1996918364130246658", "priceUOM_Name": "吨", "invPriceExchRate": 1, "unitExchangeTypePrice": "0", "priceUOM_Precision": "2", "taxRate": "2", "oriTax": 20, "oriTaxUnitPrice": 12, "oriUnitPrice": 6, "oriMoney": 150, "oriSum": 200, "applyorders_product_primeCosts": 23, "applyorders_productsku_primeCosts": 24, "applyorders_requirementDate": "2021-03-04 00:00:00", "applyorders_adviseOrderDate": "2021-03-04 00:00:00", "applyorders_adviseSupplier": "1996919652626202626", "applyorders_adviseSupplier_name": "达利园供应商", "applyorders_vendor": "1996919652626202626", "applyorders_vendor_name": "达利园供应商", "applyorders_memo": "要货请购", "bodyItem": { "id": "1996919652626202627", "define1": "表体自定义项1", "define2": "表体自定义项2", "define3": "表体自定义项3", "define56": "表体自定义项56", "define57": "表体自定义项57", "define58": "表体自定义项58", "define59": "表体自定义项59", "define60": "表体自定义项60", "define4": "表体自定义项4", "define31": "表体自定义项31", "define32": "表体自定义项32", "define33": "表体自定义项33", "define34": "表体自定义项34", "define35": "表体自定义项35", "define36": "表体自定义项36", "define37": "表体自定义项37", "define38": "表体自定义项38", "define39": "表体自定义项39", "define40": "表体自定义项40", "define41": "表体自定义项41", "define42": "表体自定义项42", "define43": "表体自定义项43", "define44": "表体自定义项44", "define45": "表体自定义项45", "define46": "表体自定义项46", "define47": "表体自定义项47", "define48": "表体自定义项48", "define49": "表体自定义项49", "define50": "表体自定义项50", "define51": "表体自定义项51", "define52": "表体自定义项52", "define53": "表体自定义项53", "define54": "表体自定义项54", "define55": "表体自定义项55", "define5": "表体自定义项5", "define6": "表体自定义项6", "define7": "表体自定义项7", "define8": "表体自定义项8", "define9": "表体自定义项9", "define10": "表体自定义项10", "define11": "表体自定义项11", "define12": "表体自定义项12", "define13": "表体自定义项13", "define14": "表体自定义项14", "define15": "表体自定义项15", "define16": "表体自定义项16", "define17": "表体自定义项17", "define18": "表体自定义项18", "define19": "表体自定义项19", "define20": "表体自定义项20", "define21": "表体自定义项21", "define22": "表体自定义项22", "define23": "表体自定义项23", "define24": "表体自定义项24", "define25": "表体自定义项25", "define26": "表体自定义项26", "define27": "表体自定义项27", "define28": "表体自定义项28", "define29": "表体自定义项29", "define30": "表体自定义项30" }, "applyorders_productsku_modelDescription": "33mm", "applyorders_product_model": "33mm", "applyorders_product_modelDescription": "细口", "applyorders_propertiesValue": "细口", "project": "1996923973363302404", "project_code": "00034", "applyOrders_vendor_define1": "供应商自定义项1", "project_name": "虚拟项目", "applyOrders_vendor_define2": "供应商自定义项2", "applyorders_trackNo": "0002", "applyOrders_vendor_define3": "供应商自定义项3", "applyOrders_vendor_define4": "供应商自定义项4", "applyOrders_vendor_define5": "供应商自定义项5", "applyOrders_vendor_define6": "供应商自定义项6", "applyOrders_vendor_define7": "供应商自定义项7", "applyOrders_vendor_define8": "供应商自定义项8", "applyOrders_vendor_define9": "供应商自定义项9", "applyOrders_vendor_define10": "供应商自定义项10", "applyOrders_vendor_define11": "供应商自定义项11", "applyOrders_vendor_define12": "供应商自定义项12", "applyOrders_vendor_define13": "供应商自定义项13", "applyOrders_vendor_define14": "供应商自定义项14", "applyOrders_vendor_define15": "供应商自定义项15", "applyOrders_vendor_define16": "供应商自定义项16", "applyOrders_vendor_define17": "供应商自定义项17", "applyOrders_vendor_define18": "供应商自定义项18", "applyOrders_vendor_define19": "供应商自定义项19", "applyOrders_vendor_define20": "供应商自定义项20", "applyOrders_vendor_define21": "供应商自定义项21", "applyOrders_vendor_define22": "供应商自定义项22", "applyOrders_vendor_define23": "供应商自定义项23", "applyOrders_vendor_define24": "供应商自定义项24", "applyOrders_vendor_define25": "供应商自定义项25", "applyOrders_vendor_define26": "供应商自定义项26", "applyOrders_vendor_define27": "供应商自定义项27", "applyOrders_vendor_define28": "供应商自定义项28", "applyOrders_vendor_define29": "供应商自定义项29", "applyOrders_vendor_define30": "供应商自定义项30", "applyOrderDefineCharacter": "id : \"1995982748459204616\" pubts : \"2024-05-13 09:16:15\" ytenant : \"0000LJ5I3I7H6YAPZ90000\"", "applyOrdersDefineCharacter": "id : \"1995982748459204616\" pubts : \"2024-05-13 09:16:15\" ytenant : \"0000LJ5I3I7H6YAPZ90000\"", "applyOrdersCharacteristics": "id : \"1995982748459204616\" pubts : \"2024-05-13 09:16:15\" ytenant : \"0000LJ5I3I7H6YAPZ90000\"" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>2106049445846056980</id>
<apiId>0f1453d26e6741faa95ace9533a61683</apiId>
<content>{ "code": 999, "message": "服务端逻辑异常" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-10-08 16:33:44.000</gmtCreate>
<gmtUpdate>2024-10-08 16:33:44.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS/>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>友户通token认证业务扩展插件</name>
<configurable>false</configurable>
<description>YonsuitBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>0f1453d26e6741faa95ace9533a61683</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin>
<id>w181ed01-1e9b-4350-b994-71a66f017555</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code>resultParse</code>
<name>返回参数转换插件</name>
<configurable>false</configurable>
<description>解决返回值中带！的，转换为json</description>
<pluginType>resultParse</pluginType>
<pluginTypeName>返回值解析插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.result.ResultMapTransferParsePlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>true</visible>
<gmtCreate>2020-07-29 00:00:00</gmtCreate>
<gmtUpdate/>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>0f1453d26e6741faa95ace9533a61683</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</resultParsePlugin>
<mapReturnPluginConfig/>
<billNo>pu_applyorderlist</billNo>
<domain>upu</domain>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser/>
<createUserName/>
<approvalStatus>1</approvalStatus>
<publishTime>2024-10-08 17:35:22</publishTime>
<pathJoin>true</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout/>
<customUrl>/applyorder/list</customUrl>
<fixedUrl>/yonbip/scm</fixedUrl>
<apiCode>0f1453d26e6741faa95ace9533a61683</apiCode>
<tokenCheckType>0</tokenCheckType>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL/>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>6c50f52d-6387-4181-b1cd-2e07411e823d</updateUserId>
<updateUserName>昵称-15652370404</updateUserName>
<paramIsForce/>
<userIDPassthrough>false</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.yonbip-scm-pu</microServiceCode>
<applicationCode>yonbip-scm-pubiz</applicationCode>
<privacyCategory>1</privacyCategory>
<privacyLevel>4</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType>0</serviceType>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType/>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>false</arrayParam>
<fileSize/>
<cc>true</cc>
<paramTransferMode>2</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId>1996896485556355076</apiDefId>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>1</paramExtRequest>
<paramExtResponse>1</paramExtResponse>
<paramExtInExtendKey>1</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene/>
<apiType/>
<paramMark/>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>false</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>false</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>1</chargeStatus>
<beforeSpeed>40</beforeSpeed>
<afterSpeed>80</afterSpeed>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath/>
<pubHistory/>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode/>
<multiVersion>0</multiVersion>
<apiTag/>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>2108770660671029249</id>
<name>用友YonBIP</name>
<type>integrateSys</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>SCC</id>
<name>供应链云</name>
<type>1</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MM</id>
<name>采购供应</name>
<type>2</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>PU</id>
<name>采购管理</name>
<type>3</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>upu.pu_applyorder</id>
<name>请购单</name>
<type>4</type>
<sort>0</sort>
<enable>0</enable>
<children/>
<parentId/>
<productId/>
<code>upu.pu_applyorder</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>PU</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MM</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>SCC</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>current_yonbip_default_sys</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<isOrigin>0</isOrigin>
<hasChildren>0</hasChildren>
<order>0</order>
</data>
</ResultVO>
{
  "success" : true,
  "code" : "200",
  "message" : "",
  "data" : [ {
    "id" : "fe630146-6bc8-493b-ab1d-858c17c1d9e0",
    "name" : "U9001",
    "apiId" : null,
    "parentId" : null,
    "children" : null,
    "defParamId" : null,
    "array" : false,
    "paramDesc" : "U9请购单号",
    "paramType" : "string",
    "requestParamType" : "BodyParam",
    "path" : null,
    "example" : null,
    "fullName" : null,
    "ytenantId" : null,
    "paramOrder" : null,
    "bizType" : "text",
    "baseType" : true,
    "defaultValue" : null,
    "required" : false,
    "visible" : true,
    "gmtCreate" : "2025-07-26 16:36:53",
    "gmtUpdate" : "2025-07-26 16:36:53",
    "entityId" : null,
    "entityCode" : null,
    "apiName" : null,
    "maxLength" : "100",
    "childId" : null,
    "edit" : true,
    "regularRule" : null,
    "mapName" : null,
    "mapRequestParamType" : null,
    "refType" : false,
    "refTypeContext" : null,
    "defineHidden" : 0,
    "integrateObjectId" : null,
    "format" : null,
    "decimals" : null,
    "paramTag" : null,
    "rowLimit" : null,
    "enableMulti" : false,
    "extend" : false
  }, {
    "id" : "5a93627b-27f7-40cb-a8fc-d6599545710b",
    "name" : "id",
    "apiId" : null,
    "parentId" : null,
    "children" : null,
    "defParamId" : null,
    "array" : false,
    "paramDesc" : "特征id,主键,新增时无需填写,修改时必填",
    "paramType" : "string",
    "requestParamType" : "BodyParam",
    "path" : null,
    "example" : null,
    "fullName" : null,
    "ytenantId" : null,
    "paramOrder" : null,
    "bizType" : "text",
    "baseType" : true,
    "defaultValue" : null,
    "required" : false,
    "visible" : true,
    "gmtCreate" : "2025-07-26 16:36:53",
    "gmtUpdate" : "2025-07-26 16:36:53",
    "entityId" : null,
    "entityCode" : null,
    "apiName" : null,
    "maxLength" : "36",
    "childId" : null,
    "edit" : true,
    "regularRule" : null,
    "mapName" : null,
    "mapRequestParamType" : null,
    "refType" : false,
    "refTypeContext" : null,
    "defineHidden" : 0,
    "integrateObjectId" : null,
    "format" : null,
    "decimals" : null,
    "paramTag" : null,
    "rowLimit" : null,
    "enableMulti" : false,
    "extend" : false
  } ]
}