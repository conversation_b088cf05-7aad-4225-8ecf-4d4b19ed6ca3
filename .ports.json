{"project_info": {"name": "YS-API", "original_port": 8000, "port_range": "8010-8019", "rationale": "基于原端口8000，分配专属端口段8010-8019，避免与常见端口冲突"}, "project_ports": {"web_frontend": 8010, "api_backend": 8011, "database": 8012, "monitoring": 8013, "docs": 8014, "test": 8015, "admin": 8016, "websocket": 8017, "reserved1": 8018, "reserved2": 8019}, "port_rules": {"locked": true, "strict_mode": true, "fallback_disabled": true, "avoid_common_ports": true}, "service_mapping": {"frontend": {"port": 8010, "description": "YS-API前端界面", "url": "http://ysapi.local:8010"}, "backend": {"port": 8011, "description": "YS-API后端服务", "url": "http://api.ysapi.local:8011"}, "database": {"port": 8012, "description": "SQLite Web管理界面", "url": "http://db.ysapi.local:8012"}, "monitoring": {"port": 8013, "description": "系统监控面板", "url": "http://monitor.ysapi.local:8013"}, "docs": {"port": 8014, "description": "API文档服务", "url": "http://docs.ysapi.local:8014"}, "test": {"port": 8015, "description": "测试环境服务", "url": "http://test.ysapi.local:8015"}, "admin": {"port": 8016, "description": "管理后台", "url": "http://admin.ysapi.local:8016"}, "websocket": {"port": 8017, "description": "WebSocket实时通信", "url": "ws://ws.ysapi.local:8017"}}, "port_conflict_analysis": {"avoided_common_ports": ["3000 (React/Vue常用)", "4000 (Angular常用)", "5000 (Flask默认)", "6000 (Webpack dev server)", "7000 (<PERSON><PERSON><PERSON>默认)", "8080 (Tomcat/Spring Boot常用)", "9000 (PHP-FPM常用)"], "chosen_range_benefits": ["基于项目原端口8000扩展", "8010-8019为YS-API专属端口段", "与常见开发工具端口完全避开", "预留端口供未来扩展"]}, "hosts_config": ["# YS-API 本地开发域名 - 添加到 /etc/hosts 或 C:\\Windows\\System32\\drivers\\etc\\hosts", "127.0.0.1 ysapi.local", "127.0.0.1 api.ysapi.local", "127.0.0.1 db.ysapi.local", "127.0.0.1 monitor.ysapi.local", "127.0.0.1 docs.ysapi.local", "127.0.0.1 test.ysapi.local", "127.0.0.1 admin.ysapi.local", "127.0.0.1 ws.ysapi.local"]}