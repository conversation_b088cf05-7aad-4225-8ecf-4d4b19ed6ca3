import types

import structlog
from sqlalchemy import create_engine

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 数据库管理器增强配置
基于现有SQLAlchemy架构，优化连接池和事务处理
"""


logger = structlog.get_logger()


class DatabaseManagerEnhancement:
    """数据库管理器增强配置"""

    @staticmethod
    def get_optimized_engine_config() -> Dict[str, Any]:
        """获取优化的SQLAlchemy引擎配置"""
        return {
            # 连接池优化配置
            'pool_size': 20,  # 增加核心连接池大小
            'max_overflow': 30,  # 增加溢出连接数
            'pool_pre_ping': True,  # 启用连接预检查
            'pool_recycle': 1800,  # 30分钟回收连接（防止长时间连接断开）
            'pool_timeout': 30,  # 获取连接超时时间

            # 连接参数优化
            'connect_args': {
                'timeout': 30,  # 连接超时
                'command_timeout': 60,  # 命令超时
                'autocommit': False,  # 明确设置非自动提交
                'mars_connection': False,  # 关闭MARS（避免冲突）
            },

            # 事务和错误处理
            'isolation_level': 'READ_COMMITTED',  # 默认隔离级别
            'echo': False,  # 生产环境关闭SQL日志
            'echo_pool': False,  # 关闭连接池日志

            # 性能优化
            'execution_options': {
                'autoflush': False,  # 关闭自动刷新
                'compiled_cache': {},  # 启用SQL编译缓存
            }
        }

    @staticmethod
    def get_master_engine_config() -> Dict[str, Any]:
        """获取Master数据库引擎配置"""
        return {
            'pool_size': 5,  # Master数据库较少操作
            'max_overflow': 10,
            'pool_pre_ping': True,
            'pool_recycle': 3600,  # 1小时回收
            'pool_timeout': 30,
            'connect_args': {
                'timeout': 30,
                'autocommit': True,  # Master操作通常需要自动提交
            }
        }

    @staticmethod
    def get_batch_operation_config() -> Dict[str, Any]:
        """获取批量操作配置"""
        return {
            'batch_size': 1000,  # 默认批量大小
            'max_batch_size': 5000,  # 最大批量大小
            'chunk_size': 500,  # 数据块大小
            'timeout_per_batch': 300,  # 每批次超时时间（5分钟）
            'max_retries': 3,  # 最大重试次数
            'retry_delay': 5,  # 重试延迟（秒）
        }


class TransactionContextManager:
    """事务上下文管理器 - 基于现有SQLAlchemy架构"""

    def __init___(self, engine, isolation_level: Optional[str] = None):
    """TODO: Add function description."""
    self.engine = engine
    self.isolation_level = isolation_level or 'READ_COMMITTED'
    self.connection = None
    self.transaction = None

    def __enter__(self):
        """进入事务上下文"""
        try:
            self.connection = self.engine.connect()

            # 设置隔离级别
            if self.isolation_level:
                self.connection.execute(
                    f"SET TRANSACTION ISOLATION LEVEL {self.isolation_level}"
                )

            # 开始事务
            self.transaction = self.connection.begin()

            logger.debug("事务开始", isolation_level=self.isolation_level)
            return self.connection

        except Exception:
            if self.connection:
                self.connection.close()
            logger.error("事务开始失败", error=str(e))
            raise

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出事务上下文"""
        try:
            if exc_type is None:
                # 没有异常，提交事务
                if self.transaction:
                    self.transaction.commit()
                    logger.debug("事务提交成功")
            else:
                # 有异常，回滚事务
                if self.transaction:
                    self.transaction.rollback()
                    logger.warning("事务回滚", error=str(exc_val))
        except Exception:
            logger.error("事务处理异常", error=str(e))
        finally:
            if self.connection:
                self.connection.close()


class BatchOperationManager:
    """批量操作管理器 - 基于现有架构增强"""

    def __init___(self, database_manager):
    """TODO: Add function description."""
    self.db_manager = database_manager
    self.config = DatabaseManagerEnhancement.get_batch_operation_config()
    self.stats = {
        'total_operations': 0,
        'successful_operations': 0,
        'failed_operations': 0,
        'total_records': 0
    }

    def batch_insert(self, table_name: str, data: list,
                     columns: list = None) -> Dict[str, Any]:
        """批量插入数据"""
        if not data:
            return {'success': True, 'inserted': 0, 'message': '没有数据需要插入'}

        try:
            # 验证表名安全性
            if not self.db_manager._validate_table_name(table_name):
                raise ValueError(f"不安全的表名: {table_name}")

            # 分批处理
            batch_size = self.config['batch_size']
            total_inserted = 0

            with TransactionContextManager(self.db_manager.engine) as conn:
                for i in range(0, len(data), batch_size):
                    batch_data = data[i:i + batch_size]

                    # 构建INSERT语句
                    if columns:
                        placeholders = ', '.join(
                            [f':{col}' for col in columns])
                        sql = f"INSERT INTO [{table_name}] ({', '.join(columns)}) VALUES ({placeholders})"
                    else:
                        # 假设data是字典列表，从第一个元素获取列名
                        if isinstance(batch_data[0], dict):
                            columns = list(batch_data[0].keys())
                            placeholders = ', '.join(
                                [f':{col}' for col in columns])
                            sql = f"INSERT INTO [{table_name}] ({', '.join(columns)}) VALUES ({placeholders})"
                        else:
                            raise ValueError("数据格式不支持，需要提供columns参数")

                    # 执行批量插入
                    result = conn.execute(sql, batch_data)
                    total_inserted += result.rowcount

            self.stats['total_operations'] += 1
            self.stats['successful_operations'] += 1
            self.stats['total_records'] += total_inserted

            logger.info("批量插入成功",
                        table=table_name,
                        inserted=total_inserted,
                        batches=len(range(0, len(data), batch_size)))

            return {
                'success': True,
                'inserted': total_inserted,
                'batches': len(range(0, len(data), batch_size)),
                'message': f'成功插入 {total_inserted} 条记录'
            }

        except Exception:
            self.stats['total_operations'] += 1
            self.stats['failed_operations'] += 1

            logger.error("批量插入失败", table=table_name, error=str(e))
            return {
                'success': False,
                'inserted': 0,
                'error': str(e),
                'message': f'批量插入失败: {str(e)}'
            }

    def batch_update(self, table_name: str, updates: list,
                     key_column: str = 'id') -> Dict[str, Any]:
        """批量更新数据"""
        if not updates:
            return {'success': True, 'updated': 0, 'message': '没有数据需要更新'}

        try:
            # 验证表名安全性
            if not self.db_manager._validate_table_name(table_name):
                raise ValueError(f"不安全的表名: {table_name}")

            batch_size = self.config['batch_size']
            total_updated = 0

            with TransactionContextManager(self.db_manager.engine) as conn:
                for i in range(0, len(updates), batch_size):
                    batch_updates = updates[i:i + batch_size]

                    for update_data in batch_updates:
                        if not isinstance(update_data, dict):
                            continue

                        if key_column not in update_data:
                            continue

                        # 构建UPDATE语句
                        set_clauses = []
                        params = {}

                        for col, value in update_data.items():
                            if col != key_column:
                                set_clauses.append(f"[{col}] = :{col}")
                                params[col] = value

                        params[key_column] = update_data[key_column]

                        sql = f"UPDATE [{table_name}] SET {', '.join(set_clauses)} WHERE [{key_column}] = :{key_column}"

                        result = conn.execute(sql, params)
                        total_updated += result.rowcount

            self.stats['total_operations'] += 1
            self.stats['successful_operations'] += 1
            self.stats['total_records'] += total_updated

            logger.info("批量更新成功",
                        table=table_name,
                        updated=total_updated,
                        batches=len(range(0, len(updates), batch_size)))

            return {
                'success': True,
                'updated': total_updated,
                'batches': len(range(0, len(updates), batch_size)),
                'message': f'成功更新 {total_updated} 条记录'
            }

        except Exception:
            self.stats['total_operations'] += 1
            self.stats['failed_operations'] += 1

            logger.error("批量更新失败", table=table_name, error=str(e))
            return {
                'success': False,
                'updated': 0,
                'error': str(e),
                'message': f'批量更新失败: {str(e)}'
            }

    def get_stats(self) -> Dict[str, Any]:
        """获取批量操作统计信息"""
        success_rate = 0
        if self.stats['total_operations'] > 0:
            success_rate = (
                self.stats['successful_operations'] / self.stats['total_operations']) * 100

        return {
            **self.stats,
            'success_rate': round(success_rate, 2)
        }


# 增强现有DatabaseManager的辅助函数
def enhance_database_manager(database_manager):
    """为现有DatabaseManager添加增强功能"""

    # 添加事务上下文管理器方法

    def get_transaction(self, isolation_level: Optional[str] = None):
        """获取事务上下文管理器"""
        return TransactionContextManager(self.engine, isolation_level)

    # 添加批量操作管理器

    def get_batch_manager(self):
        """获取批量操作管理器"""
        if not hasattr(self, '_batch_manager'):
            self._batch_manager = BatchOperationManager(self)
        return self._batch_manager

    # 添加优化的引擎重建方法

    def rebuild_engine_with_optimization(self):
        """使用优化配置重建引擎"""
        if self.engine:
            self.engine.dispose()

        if self.master_engine:
            self.master_engine.dispose()

        # 获取优化配置
        main_config = DatabaseManagerEnhancement.get_optimized_engine_config()
        master_config = DatabaseManagerEnhancement.get_master_engine_config()

        # 重建引擎

        self.engine = create_engine(
            self.db_config.connection_string,
            **main_config
        )

        self.master_engine = create_engine(
            self.db_config.master_connection_string,
            **master_config
        )

        logger.info("数据库引擎已使用优化配置重建")

    # 绑定方法到实例
    database_manager.get_transaction = types.MethodType(
        get_transaction, database_manager)
    database_manager.get_batch_manager = types.MethodType(
        get_batch_manager, database_manager)
    database_manager.rebuild_engine_with_optimization = types.MethodType(
        rebuild_engine_with_optimization, database_manager)

    logger.info("DatabaseManager增强功能已添加")
    return database_manager
