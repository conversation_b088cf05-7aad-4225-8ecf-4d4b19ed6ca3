[{"success": true, "code": 200, "message": "", "data": {"fieldVersion": 20230210, "appCode": "", "tokenSet": false, "tokenDoc": "", "tenantId": 0, "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "id": "b483475dfd65499ab122b773eb9a8061", "name": "材料出库列表查询", "apiClassifyId": "934dff6b3da94520a34bf8dd2fd29998", "apiClassifyName": "材料出库单", "apiClassifyCode": "", "parentApiClassifies": "", "functionId": "", "openMode": 0, "description": "材料出库列表查询", "auth": true, "bodyPassthrough": false, "healthExam": true, "healthStatus": true, "responseResultPassthrough": false, "contentType": "application/json", "returnPassthrough": "", "completeProxyUrl": "/yonbip/scm/materialout/list", "connectUrl": "/bill/list", "sort": 20, "handler": "openapi", "httpRequestType": "POST", "openApi": true, "preset": false, "productId": "710a0be3edff4f9092e35f63fd3b9bae", "productCode": "scm", "proxyUrl": "/yonbip/scm/materialout/list", "requestParamsDemo": "Url: /yonbip/scm/materialout/list?access_token=访问令牌 Body: { \"pageIndex\": 0, \"pageSize\": 0, \"open_vouchdate_begin\": \"\", \"open_vouchdate_end\": \"\", \"bustype_name\": \"\", \"stockOrg\": [ 0 ], \"stockOrg_code\": [ \"\" ], \"stockOrg_name\": \"\", \"product_cName\": [ \"\" ], \"product.productClass.name\": [ \"\" ], \"simpleVOs\": [ { \"field\": \"\", \"op\": \"\", \"value1\": \"\", \"value2\": \"\" } ] }", "requestProtocol": "HTTP", "serviceHttpMethod": "POST", "publishStatus": true, "approvalMsg": "", "rpcAppName": "", "rpcServiceName": "", "rpcMethodName": "", "rpcServiceUrl": "", "ma": false, "gmtCreate": "2020-01-16 18:45:26", "gmtUpdate": "2024-09-05 10:41:29.000", "address": "https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/materialout/list", "productName": "采购供应", "productClassifyId": "yonsuite", "productClassifyCode": "yonbip", "productClassifyName": "用友 YonBIP", "paramDTOS": {"paramDTOS": [{"id": 2081376276070793230, "name": "pageIndex", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": 1850748025614368792, "array": false, "paramDesc": "页号", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": 1, "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081376276070793231, "name": "pageSize", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": 1850748025614368793, "array": false, "paramDesc": "每页行数", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": 10, "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081376276070793232, "name": "open_vouchdate_begin", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": 1850748025614368794, "array": false, "paramDesc": "单据开始时间", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081376276070793233, "name": "open_vouchdate_end", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": 1850748025614368795, "array": false, "paramDesc": "单据结束时间", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081376276070793234, "name": "bustype_name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": 1850748025614368796, "array": false, "paramDesc": "交易类型", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081376276070793235, "name": "stockOrg", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": 1850748025614368797, "array": true, "paramDesc": "库存组织id", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081376276070793236, "name": "stockOrg_code", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": 1850748025614368798, "array": true, "paramDesc": "库存组织编码", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081376276070793237, "name": "stockOrg_name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": 1850748025614368799, "array": false, "paramDesc": "库存组织名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081376276070793238, "name": "product_cName", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": 1850748025614368800, "array": true, "paramDesc": "物料ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081376276070793239, "name": "product.productClass.name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": 1850748025614368801, "array": true, "paramDesc": "物料分类ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081376276070793225, "name": "simpleVOs", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "children": {"children": [{"id": 2081376276070793226, "name": "field", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793225, "defParamId": 1850748025614368803, "array": false, "paramDesc": "属性名(条件),子表加前缀[materOuts.];materOuts.upcoded为来源单据号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081376276070793227, "name": "op", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793225, "defParamId": 1850748025614368804, "array": false, "paramDesc": "条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081376276070793228, "name": "value1", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793225, "defParamId": 1850748025614368805, "array": false, "paramDesc": "值1(条件),单条件时仅使用这个配置", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081376276070793229, "name": "value2", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793225, "defParamId": 1850748025614368806, "array": false, "paramDesc": "值2(条件),单条件时此配置无效", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "defParamId": 1850748025614368802, "array": true, "paramDesc": "扩展查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "queryParamDTOS": "", "ysApi": false, "presetTokenApi": false, "applyFlag": false, "cover": false, "paramMapDTOS": {"paramMapDTOS": [{"id": 2081376276070793245, "name": "pageIndex", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": "", "array": false, "paramDesc": "页号", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "pageIndex", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081376276070793246, "name": "pageSize", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": "", "array": false, "paramDesc": "每页行数", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "pageSize", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081376276070793247, "name": "open_vouchdate_begin", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": "", "array": false, "paramDesc": "单据开始时间", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "open_vouchdate_begin", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081376276070793248, "name": "open_vouchdate_end", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": "", "array": false, "paramDesc": "单据结束时间", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "open_vouchdate_end", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081376276070793249, "name": "bustype_name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": "", "array": false, "paramDesc": "交易类型", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "aggregatedValueObject": false, "mapName": "bustype_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081376276070793250, "name": "stockOrg", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": "", "array": false, "paramDesc": "库存组织id", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "aggregatedValueObject": false, "mapName": "stockOrg", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081376276070793251, "name": "stockOrg_code", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": "", "array": false, "paramDesc": "库存组织编码", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "aggregatedValueObject": false, "mapName": "stockOrg_code", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081376276070793252, "name": "stockOrg_name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": "", "array": false, "paramDesc": "库存组织名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "aggregatedValueObject": false, "mapName": "stockOrg_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081376276070793253, "name": "product_cName", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": "", "array": false, "paramDesc": "物料ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "aggregatedValueObject": false, "mapName": "product_cName", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081376276070793254, "name": "product.productClass.name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": "", "array": false, "paramDesc": "物料分类ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "aggregatedValueObject": false, "mapName": "product.productClass.name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081376276070793240, "name": "simpleVOs", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "children": {"children": [{"id": 2081376276070793241, "name": "field", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793240, "defParamId": "", "array": false, "paramDesc": "属性名(条件),子表加前缀[materOuts.];materOuts.upcoded为来源单据号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "field", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081376276070793242, "name": "op", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793240, "defParamId": "", "array": false, "paramDesc": "条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "op", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081376276070793243, "name": "value1", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793240, "defParamId": "", "array": false, "paramDesc": "值1(条件),单条件时仅使用这个配置", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "value1", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081376276070793244, "name": "value2", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793240, "defParamId": "", "array": false, "paramDesc": "值2(条件),单条件时此配置无效", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "value2", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "defParamId": "", "array": false, "paramDesc": "扩展查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "aggregatedValueObject": false, "mapName": "simpleVOs", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "paramReturnDTOS": {"paramReturnDTOS": [{"id": 2081376284660727961, "name": "code", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": 1850748025614368822, "array": false, "paramDesc": "返回码，调用成功时返回200", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727962, "name": "message", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "defParamId": 1850748025614368823, "array": false, "paramDesc": "调用失败时的错误信息", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376276070793255, "name": "data", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": "", "children": {"children": [{"id": 2081376276070793256, "name": "sumRecordList", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793255, "children": {"children": [{"id": 2081376276070793257, "name": "totalPieces", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793256, "defParamId": 1850748025614368826, "array": false, "paramDesc": "合计价格", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376276070793258, "name": "totalQuantity", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793256, "defParamId": 1850748025614368827, "array": false, "paramDesc": "合计数量", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376276070793259, "name": "subQty", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793256, "defParamId": 1850748025614368828, "array": false, "paramDesc": "件数", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376276070793260, "name": "qty", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793256, "defParamId": 1850748025614368829, "array": false, "paramDesc": "数量", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1850748025614368825, "array": true, "paramDesc": "sum合计信息", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727954, "name": "pageIndex", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793255, "defParamId": 1850748025614368830, "array": false, "paramDesc": "页码", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727955, "name": "pageSize", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793255, "defParamId": 1850748025614368831, "array": false, "paramDesc": "每页条数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727956, "name": "pageCount", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793255, "defParamId": 1850748025614368832, "array": false, "paramDesc": "总页数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727957, "name": "beginPageIndex", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793255, "defParamId": 1850748025614368833, "array": false, "paramDesc": "开始页码", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727958, "name": "endPageIndex", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793255, "defParamId": 1850748025614368834, "array": false, "paramDesc": "结束页码", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727959, "name": "recordCount", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793255, "defParamId": 1850748025614368835, "array": false, "paramDesc": "总条数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727960, "name": "pubts", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793255, "defParamId": 1850748025614368836, "array": false, "paramDesc": "时间戳,格式为:yyyy-MM-dd HH:mm:ss", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376276070793261, "name": "recordList", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793255, "children": {"children": [{"id": 2081376284660727880, "name": "currency", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368838, "array": false, "paramDesc": "币种id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727881, "name": "materOuts_product", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368839, "array": false, "paramDesc": "物料id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727882, "name": "materOuts_unit", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368840, "array": false, "paramDesc": "主计量", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727883, "name": "materOuts_productsku", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368841, "array": false, "paramDesc": "物料sku", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727884, "name": "vouchdate", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368842, "array": false, "paramDesc": "单据日期", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727885, "name": "code", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368843, "array": false, "paramDesc": "单据编号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727886, "name": "org", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368844, "array": false, "paramDesc": "库存组织IDid", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727887, "name": "org_code", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368845, "array": false, "paramDesc": "库存组织编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727888, "name": "org_name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368846, "array": false, "paramDesc": "库存组织名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"org.name\",\"cItemName\":\"org_name\",\"cCaption\":\"库存组织\",\"cShowCaption\":\"库存组织\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_orgtree\",\"cRefId\":null,\"cRefRetId\":{\"org\":\"id\"},\"cDataRule\":\"\\\"%u8c-config.option.singleOrg%>\\\"==\\\"false\\\"\",\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOut\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727889, "name": "store", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368847, "array": false, "paramDesc": "门店id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727890, "name": "bustype", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368848, "array": false, "paramDesc": "业务类型id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727891, "name": "bustype_name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368849, "array": false, "paramDesc": "交易类型名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"bustype.name\",\"cItemName\":\"bustype_name\",\"cCaption\":\"交易类型\",\"cShowCaption\":\"交易类型\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_user\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOut\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727892, "name": "store_name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368850, "array": false, "paramDesc": "门店名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"store.name\",\"cItemName\":\"store_name\",\"cCaption\":\"门店\",\"cShowCaption\":\"门店\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_department\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":\"\\\"%productcenter.option.isOpenURetail%>\\\"==\\\"true\\\"\",\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOut\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727893, "name": "department_name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368851, "array": false, "paramDesc": "部门名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"department.name\",\"cItemName\":\"department_name\",\"cCaption\":\"部门\",\"cShowCaption\":\"部门\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucf-org-center.bd_adminorgsharetreeref\",\"cRefId\":null,\"cRefRetId\":{\"department\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOut\",\"cControlType\":\"TreeRefer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727894, "name": "department", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368852, "array": false, "paramDesc": "部门id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 14, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727895, "name": "warehouse", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368853, "array": false, "paramDesc": "仓库id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727896, "name": "warehouse_name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368854, "array": false, "paramDesc": "仓库名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"warehouse.name\",\"cItemName\":\"warehouse_name\",\"cCaption\":\"仓库\",\"cShowCaption\":\"仓库\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_warehouse\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOut\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727897, "name": "stockMgr_name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368855, "array": false, "paramDesc": "库管员名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"stockMgr.name\",\"cItemName\":\"stockMgr_name\",\"cCaption\":\"库管员\",\"cShowCaption\":\"库管员\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucf-staff-center.bd_staff_outer_ref\",\"cRefId\":null,\"cRefRetId\":{\"stockMgr\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOut\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727898, "name": "stockMgr", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368856, "array": false, "paramDesc": "库管员IDid", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727899, "name": "memo", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368857, "array": false, "paramDesc": "备注", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727900, "name": "bustype_extend_attrs_json", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368858, "array": false, "paramDesc": "出库类型", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 20, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727901, "name": "accountOrg_name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368859, "array": false, "paramDesc": "会计主体名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 21, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"accountOrg.name\",\"cItemName\":\"accountOrg_name\",\"cCaption\":\"会计主体\",\"cShowCaption\":\"会计主体\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_orgtree\",\"cRefId\":null,\"cRefRetId\":{\"accountOrg\":\"id\"},\"cDataRule\":\"\\\"%u8c-config.option.singleOrg%>\\\"==\\\"false\\\"\",\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOut\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727902, "name": "accountOrg", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368860, "array": false, "paramDesc": "会计主体id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 22, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727903, "name": "totalPieces", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368861, "array": false, "paramDesc": "整单件数", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 23, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727904, "name": "<PERSON><PERSON>us", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368862, "array": false, "paramDesc": "交换状态", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 24, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727905, "name": "status", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368863, "array": false, "paramDesc": "单据状态, 0:未提交、1:已提交、", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 25, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727906, "name": "totalQuantity", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368864, "array": false, "paramDesc": "整单数量", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 26, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727907, "name": "srcbill", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368865, "array": false, "paramDesc": "来源单据id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 27, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727908, "name": "creator", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368866, "array": false, "paramDesc": "创建人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 28, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727909, "name": "srcbillno", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368867, "array": false, "paramDesc": "来源单据", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 29, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727910, "name": "srcBillType", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368868, "array": false, "paramDesc": "来源上级单据类型, productionorder.po_production_order_ustock:生产订单材料、upu.st_purchaseorder:采购订单、upu.pu_arrivalorder:到货订单、productionorder.po_production_order:生产订单产品、st_storeprorecord:产品入库单、st_storecheckplan:盘点倒冲、po_production_order:生产订单、2:计划订单、3:销售订单、", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 30, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727911, "name": "createTime", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368869, "array": false, "paramDesc": "创建时间,格式为:yyyy-MM-dd HH:mm:ss", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 31, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727912, "name": "modifier", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368870, "array": false, "paramDesc": "修改人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 32, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727913, "name": "modifyTime", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368871, "array": false, "paramDesc": "修改时间,格式为:yyyy-MM-dd HH:mm:ss", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 33, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727914, "name": "auditor", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368872, "array": false, "paramDesc": "提交人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 34, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727915, "name": "auditTime", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368873, "array": false, "paramDesc": "提交时间,格式为:yyyy-MM-dd HH:mm:ss", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 35, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727916, "name": "id", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368874, "array": false, "paramDesc": "主表id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 36, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727917, "name": "pubts", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368875, "array": false, "paramDesc": "时间戳,格式为:yyyy-MM-dd HH:mm:ss", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 37, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727918, "name": "tplid", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368876, "array": false, "paramDesc": "模板id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 38, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376276070793262, "name": "headItem", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "children": {"children": [{"id": 2081376276070793263, "name": "id", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793262, "defParamId": 1850748025614368878, "array": false, "paramDesc": "表头自定义项id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376276070793264, "name": "define1", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793262, "defParamId": 1850748025614368879, "array": false, "paramDesc": "表头自定义项1", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376276070793265, "name": "define2", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793262, "defParamId": 1850748025614368880, "array": false, "paramDesc": "表头自定义项2", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376276070793266, "name": "define3", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793262, "defParamId": 1850748025614368881, "array": false, "paramDesc": "表头自定义项3", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376276070793267, "name": "define4", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793262, "defParamId": 1850748025614368882, "array": false, "paramDesc": "表头自定义项4", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1850748025614368877, "array": false, "paramDesc": "以下字段名需要拼接headItem!", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 39, "baseType": true, "defaultValue": "", "required": false, "visible": "", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376276070793268, "name": "materOuts", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "children": {"children": {"id": 2081376276070793269, "name": "id", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793268, "defParamId": 1850748025614368884, "array": false, "paramDesc": "子表id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}}, "defParamId": 1850748025614368883, "array": false, "paramDesc": "以下字段名需要拼接materOuts!", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 40, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727919, "name": "product_cCode", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368885, "array": false, "paramDesc": "物料编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 41, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"materOuts.product.cCode\",\"cItemName\":\"product_cCode\",\"cCaption\":\"物料编码\",\"cShowCaption\":\"物料编码\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cName\":\"product_cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"unit\":\"oUnitId\",\"unit_name\":\"unitName\",\"product_unitName\":\"unitName\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"isSerialNoManage\":\"productOfflineRetail_isSerialNoManage\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"stockUnitId\":\"stockUnit\",\"stockUnit_name\":\"stockUnit_name\",\"product_cCode\":\"cCode\",\"productsku_cName\":\"cName\",\"invExchRate\":\"stockRate\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"unit_Precision\":\"unitPrecision\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOuts\",\"cControlType\":\"refer\",\"refReturn\":\"cCode\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727920, "name": "product_cName", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368886, "array": false, "paramDesc": "物料名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 42, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"materOuts.product.cName\",\"cItemName\":\"product_cName\",\"cCaption\":\"物料名称\",\"cShowCaption\":\"物料名称\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cName\":\"product_cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"unit\":\"oUnitId\",\"unit_name\":\"unitName\",\"product_unitName\":\"unitName\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"isSerialNoManage\":\"productOfflineRetail_isSerialNoManage\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"stockUnitId\":\"stockUnit\",\"stockUnit_name\":\"stockUnit_name\",\"product_cCode\":\"cCode\",\"productsku_cName\":\"cName\",\"invExchRate\":\"stockRate\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"unit_Precision\":\"unitPrecision\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOuts\",\"cControlType\":\"refer\",\"refReturn\":\"cName\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727921, "name": "productsku_cCode", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368887, "array": false, "paramDesc": "sku编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 43, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"materOuts.productsku.cCode\",\"cItemName\":\"productsku_cCode\",\"cCaption\":\"sku编码\",\"cShowCaption\":\"sku编码\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cName\":\"product_cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"unit\":\"oUnitId\",\"unit_name\":\"unitName\",\"product_unitName\":\"unitName\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"isSerialNoManage\":\"productOfflineRetail_isSerialNoManage\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"stockUnitId\":\"stockUnit\",\"stockUnit_name\":\"stockUnit_name\",\"product_cCode\":\"cCode\",\"productsku_cName\":\"cName\",\"invExchRate\":\"stockRate\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"unit_Precision\":\"unitPrecision\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOuts\",\"cControlType\":\"refer\",\"refReturn\":\"productskus_cCode\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727922, "name": "productsku_cName", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368888, "array": false, "paramDesc": "sku名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 44, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"materOuts.productsku.skuName\",\"cItemName\":\"productsku_cName\",\"cCaption\":\"sku名称\",\"cShowCaption\":\"sku名称\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cName\":\"product_cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"unit\":\"oUnitId\",\"unit_name\":\"unitName\",\"product_unitName\":\"unitName\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"isSerialNoManage\":\"productOfflineRetail_isSerialNoManage\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"stockUnitId\":\"stockUnit\",\"stockUnit_name\":\"stockUnit_name\",\"product_cCode\":\"cCode\",\"productsku_cName\":\"cName\",\"invExchRate\":\"stockRate\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"unit_Precision\":\"unitPrecision\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOuts\",\"cControlType\":\"refer\",\"refReturn\":\"cName\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727923, "name": "productClass_code", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368889, "array": false, "paramDesc": "物料分类编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 45, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727924, "name": "propertiesValue", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368890, "array": false, "paramDesc": "规格", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 46, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727925, "name": "batchno", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368891, "array": false, "paramDesc": "批次号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 47, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"materOuts.batchno\",\"cItemName\":\"batchno\",\"cCaption\":\"批次号\",\"cShowCaption\":\"批次号\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"st_batchnoref\",\"cRefId\":null,\"cRefRetId\":{\"batchno\":\"batchno\",\"producedate\":\"producedate\",\"invaliddate\":\"invaliddate\",\"define@1@@30\":\"define@1@@30\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOuts\",\"cControlType\":\"refer\",\"refReturn\":\"batchno\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727926, "name": "invaliddate", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368892, "array": false, "paramDesc": "有效期至", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 48, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727927, "name": "qty", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368893, "array": false, "paramDesc": "数量", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 49, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727928, "name": "product_unitName", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368894, "array": false, "paramDesc": "计量单位", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 50, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727929, "name": "subQty", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368895, "array": false, "paramDesc": "件数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 51, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727930, "name": "stockUnitId", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368896, "array": false, "paramDesc": "库存单位id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 52, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727931, "name": "stockUnit_name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368897, "array": false, "paramDesc": "库存单位", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 53, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"materOuts.stockUnitId.name\",\"cItemName\":\"stockUnit_name\",\"cCaption\":\"库存单位\",\"cShowCaption\":\"库存单位\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"productcenter.pc_productassitunitsref\",\"cRefId\":null,\"cRefRetId\":{\"stockUnitId\":\"assistUnit\",\"stockUnit_name\":\"assistUnit_Name\",\"invExchRate\":\"mainUnitCount\",\"unitExchangeType\":\"unitExchangeType\",\"stockUnitId_Precision\":\"assistUnit_Precision\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOuts\",\"cControlType\":\"refer\",\"refReturn\":\"assistUnit_Name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727932, "name": "project_code", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368898, "array": false, "paramDesc": "项目编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 54, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727933, "name": "project_name", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368899, "array": false, "paramDesc": "项目名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 55, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"materOuts.project.name\",\"cItemName\":\"project_name\",\"cCaption\":\"项目名称\",\"cShowCaption\":\"项目名称\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucfbasedoc.bd_outer_projectcardMCref\",\"cRefId\":null,\"cRefRetId\":{\"project\":\"id\",\"project_name\":\"name\",\"project_code\":\"code\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.materialout.MaterialOuts\",\"cControlType\":\"Refer\",\"refReturn\":null,\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727934, "name": "natUnitPrice", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368900, "array": false, "paramDesc": "单价", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 56, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727935, "name": "natMoney", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368901, "array": false, "paramDesc": "金额", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 57, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727815, "name": "bodyItem", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "children": {"children": [{"id": 2081376284660727816, "name": "id", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368903, "array": false, "paramDesc": "表体自定义项id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727817, "name": "define1", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368904, "array": false, "paramDesc": "表体自定义项1", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727818, "name": "define2", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368905, "array": false, "paramDesc": "表体自定义项2", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727819, "name": "define3", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368906, "array": false, "paramDesc": "表体自定义项3", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727820, "name": "define4", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368907, "array": false, "paramDesc": "表体自定义项4", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727821, "name": "define5", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368908, "array": false, "paramDesc": "表体自定义项5", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727822, "name": "define6", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368909, "array": false, "paramDesc": "表体自定义项6", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727823, "name": "define7", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368910, "array": false, "paramDesc": "表体自定义项7", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727824, "name": "define8", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368911, "array": false, "paramDesc": "表体自定义项8", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727825, "name": "define9", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368912, "array": false, "paramDesc": "表体自定义项9", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727826, "name": "define10", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368913, "array": false, "paramDesc": "表体自定义项10", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727827, "name": "define11", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368914, "array": false, "paramDesc": "表体自定义项11", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727828, "name": "define12", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368915, "array": false, "paramDesc": "表体自定义项12", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727829, "name": "define13", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368916, "array": false, "paramDesc": "表体自定义项13", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727830, "name": "define14", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368917, "array": false, "paramDesc": "表体自定义项14", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 14, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727831, "name": "define15", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368918, "array": false, "paramDesc": "表体自定义项15", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727832, "name": "define16", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368919, "array": false, "paramDesc": "表体自定义项16", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727833, "name": "define17", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368920, "array": false, "paramDesc": "表体自定义项17", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727834, "name": "define18", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368921, "array": false, "paramDesc": "表体自定义项18", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727835, "name": "define19", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368922, "array": false, "paramDesc": "表体自定义项19", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727836, "name": "define20", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368923, "array": false, "paramDesc": "表体自定义项20", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 20, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727837, "name": "define21", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368924, "array": false, "paramDesc": "表体自定义项21", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 21, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727838, "name": "define22", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368925, "array": false, "paramDesc": "表体自定义项22", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 22, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727839, "name": "define23", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368926, "array": false, "paramDesc": "表体自定义项23", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 23, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727840, "name": "define24", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368927, "array": false, "paramDesc": "表体自定义项24", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 24, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727841, "name": "define25", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368928, "array": false, "paramDesc": "表体自定义项25", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 25, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727842, "name": "define26", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368929, "array": false, "paramDesc": "表体自定义项26", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 26, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727843, "name": "define27", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368930, "array": false, "paramDesc": "表体自定义项27", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 27, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727844, "name": "define28", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368931, "array": false, "paramDesc": "表体自定义项28", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 28, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727845, "name": "define29", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368932, "array": false, "paramDesc": "表体自定义项29", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 29, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727846, "name": "define30", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368933, "array": false, "paramDesc": "表体自定义项30", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 30, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727847, "name": "define31", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368934, "array": false, "paramDesc": "表体自定义项31", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 31, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727848, "name": "define32", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368935, "array": false, "paramDesc": "表体自定义项32", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 32, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727849, "name": "define33", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368936, "array": false, "paramDesc": "表体自定义项33", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 33, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727850, "name": "define34", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368937, "array": false, "paramDesc": "表体自定义项34", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 34, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727851, "name": "define35", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368938, "array": false, "paramDesc": "表体自定义项35", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 35, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727852, "name": "define36", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368939, "array": false, "paramDesc": "表体自定义项36", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 36, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727853, "name": "define37", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368940, "array": false, "paramDesc": "表体自定义项37", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 37, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727854, "name": "define38", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368941, "array": false, "paramDesc": "表体自定义项38", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 38, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727855, "name": "define39", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368942, "array": false, "paramDesc": "表体自定义项39", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 39, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727856, "name": "define40", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368943, "array": false, "paramDesc": "表体自定义项40", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 40, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727857, "name": "define41", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368944, "array": false, "paramDesc": "表体自定义项41", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 41, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727858, "name": "define42", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368945, "array": false, "paramDesc": "表体自定义项42", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 42, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727859, "name": "define43", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368946, "array": false, "paramDesc": "表体自定义项43", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 43, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727860, "name": "define44", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368947, "array": false, "paramDesc": "表体自定义项44", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 44, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727861, "name": "define45", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368948, "array": false, "paramDesc": "表体自定义项45", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 45, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727862, "name": "define46", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368949, "array": false, "paramDesc": "表体自定义项46", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 46, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727863, "name": "define47", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368950, "array": false, "paramDesc": "表体自定义项47", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 47, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727864, "name": "define48", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368951, "array": false, "paramDesc": "表体自定义项48", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 48, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727865, "name": "define49", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368952, "array": false, "paramDesc": "表体自定义项49", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 49, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727866, "name": "define50", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368953, "array": false, "paramDesc": "表体自定义项50", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 50, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727867, "name": "define51", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368954, "array": false, "paramDesc": "表体自定义项51", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 51, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727868, "name": "define52", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368955, "array": false, "paramDesc": "表体自定义项52", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 52, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727869, "name": "define53", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368956, "array": false, "paramDesc": "表体自定义项53", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 53, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727870, "name": "define54", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368957, "array": false, "paramDesc": "表体自定义项54", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 54, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727871, "name": "define55", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368958, "array": false, "paramDesc": "表体自定义项55", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 55, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727872, "name": "define56", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368959, "array": false, "paramDesc": "表体自定义项56", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 56, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727873, "name": "define57", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368960, "array": false, "paramDesc": "表体自定义项57", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 57, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727874, "name": "define58", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368961, "array": false, "paramDesc": "表体自定义项58", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 58, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727875, "name": "define59", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368962, "array": false, "paramDesc": "表体自定义项59", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 59, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727876, "name": "define60", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727815, "defParamId": 1850748025614368963, "array": false, "paramDesc": "表体自定义项60", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 60, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1850748025614368902, "array": false, "paramDesc": "以下字段名需要拼接bodyItem!", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 58, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727936, "name": "natCurrency_priceDigit", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368964, "array": false, "paramDesc": "币种单价精度", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 59, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727937, "name": "natCurrency_moneyDigit", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368965, "array": false, "paramDesc": "币种金额精度", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 60, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727938, "name": "unit_code", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368966, "array": false, "paramDesc": "主计量编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 61, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727939, "name": "unit_Precision", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368967, "array": false, "paramDesc": "主计量精度", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 62, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727940, "name": "stockUnitId_Precision", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368968, "array": false, "paramDesc": "库存单位精度", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 63, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727944, "name": "isWip", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368972, "array": false, "paramDesc": "是否在制品", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 67, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727945, "name": "costAccountingMethod", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1850748025614368973, "array": false, "paramDesc": "委外成本核算方式：0 按委外入库核算成本，1 按委外订单核算成本", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 68, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727877, "name": "bodyParallel", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "children": {"children": [{"id": 2081376284660727878, "name": "wipOpSn", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727877, "defParamId": 1850748025614368975, "array": false, "paramDesc": "在制品工序顺序号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727879, "name": "wipOperationId", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376284660727877, "defParamId": 1850748025614368976, "array": false, "paramDesc": "在制品工序ID", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1850748025614368974, "array": false, "paramDesc": "材料出库子表平行表（st.materialout.MaterialOutsParallel）", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 69, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727946, "name": "odyParallel_wipOperationCode", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1855977363037224962, "array": false, "paramDesc": "在制品工序编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 70, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727947, "name": "bodyParallel_wipOperationName", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1855977363037224963, "array": false, "paramDesc": "在制品工序名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 71, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727948, "name": "out_sys_id", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1856807545865240582, "array": false, "paramDesc": "外部来源线索", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 72, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727949, "name": "out_sys_code", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1856807545865240583, "array": false, "paramDesc": "外部来源编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 73, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727950, "name": "out_sys_version", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1856807545865240584, "array": false, "paramDesc": "外部来源版本", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 74, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727951, "name": "out_sys_type", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1856807545865240585, "array": false, "paramDesc": "外部来源类型", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 75, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727952, "name": "out_sys_rowno", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1856807545865240586, "array": false, "paramDesc": "外部来源行号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 76, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081376284660727953, "name": "out_sys_lineid", "apiId": "b483475dfd65499ab122b773eb9a8061", "parentId": 2081376276070793261, "defParamId": 1856807545865240587, "array": false, "paramDesc": "外部来源行", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 77, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1850748025614368837, "array": true, "paramDesc": "返回结果对象", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1850748025614368824, "array": false, "paramDesc": "调用成功时的返回数据", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:41:29.000", "gmtUpdate": "2024-09-05 10:41:29.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "returnFormatType": "JSON", "paramConstDTOS": "", "paramConstMapDTOS": "", "apiDemoReturnDTOS": {"apiDemoReturnDTOS": [{"id": 2081376284660727967, "apiId": "b483475dfd65499ab122b773eb9a8061", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"sumRecordList\": [ { \"totalPieces\": \"\", \"totalQuantity\": \"\", \"subQty\": \"\", \"qty\": \"\" } ], \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"pubts\": \"\", \"recordList\": [ { \"currency\": \"\", \"materOuts_product\": \"\", \"materOuts_unit\": \"\", \"materOuts_productsku\": \"\", \"vouchdate\": \"\", \"code\": \"\", \"org\": \"\", \"org_code\": \"\", \"org_name\": \"\", \"store\": \"\", \"bustype\": \"\", \"bustype_name\": \"\", \"store_name\": \"\", \"department_name\": \"\", \"department\": \"\", \"warehouse\": \"\", \"warehouse_name\": \"\", \"stockMgr_name\": \"\", \"stockMgr\": \"\", \"memo\": \"\", \"bustype_extend_attrs_json\": \"\", \"accountOrg_name\": \"\", \"accountOrg\": \"\", \"totalPieces\": \"\", \"exchangestatus\": \"\", \"status\": \"\", \"totalQuantity\": 0, \"srcbill\": \"\", \"creator\": \"\", \"srcbillno\": \"\", \"srcBillType\": \"\", \"createTime\": \"\", \"modifier\": \"\", \"modifyTime\": \"\", \"auditor\": \"\", \"auditTime\": \"\", \"id\": \"\", \"pubts\": \"\", \"tplid\": \"\", \"headItem\": { \"id\": \"\", \"define1\": \"\", \"define2\": \"\", \"define3\": \"\", \"define4\": \"\" }, \"materOuts\": { \"id\": \"\" }, \"product_cCode\": \"\", \"product_cName\": \"\", \"productsku_cCode\": \"\", \"productsku_cName\": \"\", \"productClass_code\": \"\", \"propertiesValue\": \"\", \"batchno\": \"\", \"invaliddate\": \"\", \"qty\": 0, \"product_unitName\": \"\", \"subQty\": 0, \"stockUnitId\": \"\", \"stockUnit_name\": \"\", \"project_code\": \"\", \"project_name\": \"\", \"natUnitPrice\": 0, \"natMoney\": 0, \"bodyItem\": { \"id\": \"\", \"define1\": \"\", \"define2\": \"\", \"define3\": \"\", \"define4\": \"\", \"define5\": \"\", \"define6\": \"\", \"define7\": \"\", \"define8\": \"\", \"define9\": \"\", \"define10\": \"\", \"define11\": \"\", \"define12\": \"\", \"define13\": \"\", \"define14\": \"\", \"define15\": \"\", \"define16\": \"\", \"define17\": \"\", \"define18\": \"\", \"define19\": \"\", \"define20\": \"\", \"define21\": \"\", \"define22\": \"\", \"define23\": \"\", \"define24\": \"\", \"define25\": \"\", \"define26\": \"\", \"define27\": \"\", \"define28\": \"\", \"define29\": \"\", \"define30\": \"\", \"define31\": \"\", \"define32\": \"\", \"define33\": \"\", \"define34\": \"\", \"define35\": \"\", \"define36\": \"\", \"define37\": \"\", \"define38\": \"\", \"define39\": \"\", \"define40\": \"\", \"define41\": \"\", \"define42\": \"\", \"define43\": \"\", \"define44\": \"\", \"define45\": \"\", \"define46\": \"\", \"define47\": \"\", \"define48\": \"\", \"define49\": \"\", \"define50\": \"\", \"define51\": \"\", \"define52\": \"\", \"define53\": \"\", \"define54\": \"\", \"define55\": \"\", \"define56\": \"\", \"define57\": \"\", \"define58\": \"\", \"define59\": \"\", \"define60\": \"\" }, \"natCurrency_priceDigit\": \"\", \"natCurrency_moneyDigit\": \"\", \"unit_code\": \"\", \"unit_Precision\": \"\", \"stockUnitId_Precision\": \"\", \"materialOutsCharacteristics\": 0, \"materialOutsDefineCharacter\": 0, \"materialOutDefineCharacter\": 0, \"isWip\": \"\", \"costAccountingMethod\": \"\", \"bodyParallel\": { \"wipOpSn\": \"\", \"wipOperationId\": \"\" }, \"odyParallel_wipOperationCode\": \"\", \"bodyParallel_wipOperationName\": \"\", \"out_sys_id\": \"\", \"out_sys_code\": \"\", \"out_sys_version\": \"\", \"out_sys_type\": \"\", \"out_sys_rowno\": \"\", \"out_sys_lineid\": \"\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2081376284660727968, "apiId": "b483475dfd65499ab122b773eb9a8061", "content": "{ \"code\": \"999\", \"message\": \"No enum constant org.imeta.core.base.ConditionOperator.2\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "apiDemoReturnDTOList": {"apiDemoReturnDTOList": [{"id": 2081376284660727967, "apiId": "b483475dfd65499ab122b773eb9a8061", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"sumRecordList\": [ { \"totalPieces\": \"\", \"totalQuantity\": \"\", \"subQty\": \"\", \"qty\": \"\" } ], \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"pubts\": \"\", \"recordList\": [ { \"currency\": \"\", \"materOuts_product\": \"\", \"materOuts_unit\": \"\", \"materOuts_productsku\": \"\", \"vouchdate\": \"\", \"code\": \"\", \"org\": \"\", \"org_code\": \"\", \"org_name\": \"\", \"store\": \"\", \"bustype\": \"\", \"bustype_name\": \"\", \"store_name\": \"\", \"department_name\": \"\", \"department\": \"\", \"warehouse\": \"\", \"warehouse_name\": \"\", \"stockMgr_name\": \"\", \"stockMgr\": \"\", \"memo\": \"\", \"bustype_extend_attrs_json\": \"\", \"accountOrg_name\": \"\", \"accountOrg\": \"\", \"totalPieces\": \"\", \"exchangestatus\": \"\", \"status\": \"\", \"totalQuantity\": 0, \"srcbill\": \"\", \"creator\": \"\", \"srcbillno\": \"\", \"srcBillType\": \"\", \"createTime\": \"\", \"modifier\": \"\", \"modifyTime\": \"\", \"auditor\": \"\", \"auditTime\": \"\", \"id\": \"\", \"pubts\": \"\", \"tplid\": \"\", \"headItem\": { \"id\": \"\", \"define1\": \"\", \"define2\": \"\", \"define3\": \"\", \"define4\": \"\" }, \"materOuts\": { \"id\": \"\" }, \"product_cCode\": \"\", \"product_cName\": \"\", \"productsku_cCode\": \"\", \"productsku_cName\": \"\", \"productClass_code\": \"\", \"propertiesValue\": \"\", \"batchno\": \"\", \"invaliddate\": \"\", \"qty\": 0, \"product_unitName\": \"\", \"subQty\": 0, \"stockUnitId\": \"\", \"stockUnit_name\": \"\", \"project_code\": \"\", \"project_name\": \"\", \"natUnitPrice\": 0, \"natMoney\": 0, \"bodyItem\": { \"id\": \"\", \"define1\": \"\", \"define2\": \"\", \"define3\": \"\", \"define4\": \"\", \"define5\": \"\", \"define6\": \"\", \"define7\": \"\", \"define8\": \"\", \"define9\": \"\", \"define10\": \"\", \"define11\": \"\", \"define12\": \"\", \"define13\": \"\", \"define14\": \"\", \"define15\": \"\", \"define16\": \"\", \"define17\": \"\", \"define18\": \"\", \"define19\": \"\", \"define20\": \"\", \"define21\": \"\", \"define22\": \"\", \"define23\": \"\", \"define24\": \"\", \"define25\": \"\", \"define26\": \"\", \"define27\": \"\", \"define28\": \"\", \"define29\": \"\", \"define30\": \"\", \"define31\": \"\", \"define32\": \"\", \"define33\": \"\", \"define34\": \"\", \"define35\": \"\", \"define36\": \"\", \"define37\": \"\", \"define38\": \"\", \"define39\": \"\", \"define40\": \"\", \"define41\": \"\", \"define42\": \"\", \"define43\": \"\", \"define44\": \"\", \"define45\": \"\", \"define46\": \"\", \"define47\": \"\", \"define48\": \"\", \"define49\": \"\", \"define50\": \"\", \"define51\": \"\", \"define52\": \"\", \"define53\": \"\", \"define54\": \"\", \"define55\": \"\", \"define56\": \"\", \"define57\": \"\", \"define58\": \"\", \"define59\": \"\", \"define60\": \"\" }, \"natCurrency_priceDigit\": \"\", \"natCurrency_moneyDigit\": \"\", \"unit_code\": \"\", \"unit_Precision\": \"\", \"stockUnitId_Precision\": \"\", \"materialOutsCharacteristics\": 0, \"materialOutsDefineCharacter\": 0, \"materialOutDefineCharacter\": 0, \"isWip\": \"\", \"costAccountingMethod\": \"\", \"bodyParallel\": { \"wipOpSn\": \"\", \"wipOperationId\": \"\" }, \"odyParallel_wipOperationCode\": \"\", \"bodyParallel_wipOperationName\": \"\", \"out_sys_id\": \"\", \"out_sys_code\": \"\", \"out_sys_version\": \"\", \"out_sys_type\": \"\", \"out_sys_rowno\": \"\", \"out_sys_lineid\": \"\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2081376284660727968, "apiId": "b483475dfd65499ab122b773eb9a8061", "content": "{ \"code\": \"999\", \"message\": \"No enum constant org.imeta.core.base.ConditionOperator.2\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "routingStgy": 0, "routingStgyList": "", "apiDemoReturnDTO": {"id": 2081376284660727967, "apiId": "b483475dfd65499ab122b773eb9a8061", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"sumRecordList\": [ { \"totalPieces\": \"\", \"totalQuantity\": \"\", \"subQty\": \"\", \"qty\": \"\" } ], \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"pubts\": \"\", \"recordList\": [ { \"currency\": \"\", \"materOuts_product\": \"\", \"materOuts_unit\": \"\", \"materOuts_productsku\": \"\", \"vouchdate\": \"\", \"code\": \"\", \"org\": \"\", \"org_code\": \"\", \"org_name\": \"\", \"store\": \"\", \"bustype\": \"\", \"bustype_name\": \"\", \"store_name\": \"\", \"department_name\": \"\", \"department\": \"\", \"warehouse\": \"\", \"warehouse_name\": \"\", \"stockMgr_name\": \"\", \"stockMgr\": \"\", \"memo\": \"\", \"bustype_extend_attrs_json\": \"\", \"accountOrg_name\": \"\", \"accountOrg\": \"\", \"totalPieces\": \"\", \"exchangestatus\": \"\", \"status\": \"\", \"totalQuantity\": 0, \"srcbill\": \"\", \"creator\": \"\", \"srcbillno\": \"\", \"srcBillType\": \"\", \"createTime\": \"\", \"modifier\": \"\", \"modifyTime\": \"\", \"auditor\": \"\", \"auditTime\": \"\", \"id\": \"\", \"pubts\": \"\", \"tplid\": \"\", \"headItem\": { \"id\": \"\", \"define1\": \"\", \"define2\": \"\", \"define3\": \"\", \"define4\": \"\" }, \"materOuts\": { \"id\": \"\" }, \"product_cCode\": \"\", \"product_cName\": \"\", \"productsku_cCode\": \"\", \"productsku_cName\": \"\", \"productClass_code\": \"\", \"propertiesValue\": \"\", \"batchno\": \"\", \"invaliddate\": \"\", \"qty\": 0, \"product_unitName\": \"\", \"subQty\": 0, \"stockUnitId\": \"\", \"stockUnit_name\": \"\", \"project_code\": \"\", \"project_name\": \"\", \"natUnitPrice\": 0, \"natMoney\": 0, \"bodyItem\": { \"id\": \"\", \"define1\": \"\", \"define2\": \"\", \"define3\": \"\", \"define4\": \"\", \"define5\": \"\", \"define6\": \"\", \"define7\": \"\", \"define8\": \"\", \"define9\": \"\", \"define10\": \"\", \"define11\": \"\", \"define12\": \"\", \"define13\": \"\", \"define14\": \"\", \"define15\": \"\", \"define16\": \"\", \"define17\": \"\", \"define18\": \"\", \"define19\": \"\", \"define20\": \"\", \"define21\": \"\", \"define22\": \"\", \"define23\": \"\", \"define24\": \"\", \"define25\": \"\", \"define26\": \"\", \"define27\": \"\", \"define28\": \"\", \"define29\": \"\", \"define30\": \"\", \"define31\": \"\", \"define32\": \"\", \"define33\": \"\", \"define34\": \"\", \"define35\": \"\", \"define36\": \"\", \"define37\": \"\", \"define38\": \"\", \"define39\": \"\", \"define40\": \"\", \"define41\": \"\", \"define42\": \"\", \"define43\": \"\", \"define44\": \"\", \"define45\": \"\", \"define46\": \"\", \"define47\": \"\", \"define48\": \"\", \"define49\": \"\", \"define50\": \"\", \"define51\": \"\", \"define52\": \"\", \"define53\": \"\", \"define54\": \"\", \"define55\": \"\", \"define56\": \"\", \"define57\": \"\", \"define58\": \"\", \"define59\": \"\", \"define60\": \"\" }, \"natCurrency_priceDigit\": \"\", \"natCurrency_moneyDigit\": \"\", \"unit_code\": \"\", \"unit_Precision\": \"\", \"stockUnitId_Precision\": \"\", \"materialOutsCharacteristics\": 0, \"materialOutsDefineCharacter\": 0, \"materialOutDefineCharacter\": 0, \"isWip\": \"\", \"costAccountingMethod\": \"\", \"bodyParallel\": { \"wipOpSn\": \"\", \"wipOperationId\": \"\" }, \"odyParallel_wipOperationCode\": \"\", \"bodyParallel_wipOperationName\": \"\", \"out_sys_id\": \"\", \"out_sys_code\": \"\", \"out_sys_version\": \"\", \"out_sys_type\": \"\", \"out_sys_rowno\": \"\", \"out_sys_lineid\": \"\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, "apiDemoReturnDTOError": {"id": 2081376284660727968, "apiId": "b483475dfd65499ab122b773eb9a8061", "content": "{ \"code\": \"999\", \"message\": \"No enum constant org.imeta.core.base.ConditionOperator.2\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-09-05 10:41:30.000", "gmtUpdate": "2024-09-05 10:41:30.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}, "errorCodeDTOS": "", "displayCodeApiConfigDTOS": "", "tokenPlugin": "", "paramParsePlugin": "", "authPlugin": {"id": "09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "", "name": "友户通token认证业务扩展插件", "configurable": false, "description": "YonsuitBusinessExtendPlugin", "pluginType": "auth", "pluginTypeName": "业务扩展插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": false, "gmtCreate": "2020-05-22 00:00:00", "gmtUpdate": "2020-05-22 00:00:00", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "b483475dfd65499ab122b773eb9a8061", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "resultParsePlugin": {"id": "w181ed01-1e9b-4350-b994-71a66f017555", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "resultParse", "name": "返回参数转换插件", "configurable": false, "description": "解决返回值中带！的，转换为json", "pluginType": "resultParse", "pluginTypeName": "返回值解析插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.result.ResultMapTransferParsePlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": true, "gmtCreate": "2020-07-29 00:00:00", "gmtUpdate": "", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "b483475dfd65499ab122b773eb9a8061", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "mapReturnPluginConfig": "", "billNo": "st_materialoutlist", "domain": "ustock", "apiCategory": "", "docUrl": "", "pathMatch": 0, "createUser": "", "createUserName": "", "approvalStatus": 1, "publishTime": "2024-09-05 11:09:24", "pathJoin": true, "timeOut": 30, "tokenPluginName": "", "authPluginName": "", "resultPluginName": "", "apiDemoReturnRightDemo": "", "apiDemoReturnErrorDemo": "", "mock": false, "mockTimeout": "", "customUrl": "/materialout/list", "fixedUrl": "/yonbip/scm", "apiCode": "b483475dfd65499ab122b773eb9a8061", "tokenCheckType": 0, "enableMulti": false, "multiField": "", "idempotent": "non", "bidirectionalSSL": "", "ucgSchema": "HTTPS", "updateUserId": "36a8b72b-d965-404d-a02d-66ff4a7afeb3", "updateUserName": "昵称-王章宇", "paramIsForce": "", "userIDPassthrough": false, "applyUser": "", "applyMsg": "", "dr": 0, "microServiceCode": "domain.yonbip-scm-stock", "applicationCode": "yonbip-scm-stock", "privacyCategory": 1, "privacyLevel": 4, "apiDesigned": 0, "serviceType": 0, "integrateSchemeCode": "", "integrateSchemeName": "", "integrateObjectCode": "", "integrateObjectName": "", "integrateObjectCreatedType": "", "returnIntegObjId": "", "returnIntegObjName": "", "apiIntegrateDTOList": "", "apiRouteInfoDTOList": "", "arrayParam": false, "fileSize": "", "cc": true, "paramTransferMode": 1, "ytenantId": 0, "statusConf": "", "scene": 1, "version": "", "bizObjUri": "", "bizObjOperationType": "", "apiDefId": 1850748025614368790, "paramExtBizObjCode": "", "paramExtBizObjName": "", "paramExtRequest": 1, "paramExtResponse": 1, "paramExtInExtendKey": 1, "openScene": 1, "integrationScene": "", "apiType": "", "paramMark": "", "integrateSysId": "", "integrateSysName": "", "integrateSysCode": "", "dataZoneSetting": false, "reqDataZoneSetting": false, "respDataZoneSetting": false, "reqDataAllQuery": false, "reqDataAllBody": false, "respDataAllBody": false, "chargeStatus": 1, "beforeSpeed": 40, "afterSpeed": 80, "speedStatus": false, "reqDataRefPath": "", "respDataRefPath": "", "pubHistory": "", "deprecated": 0, "recommendedApiId": "", "recommendedApiName": "", "domainAppCode": "", "multiVersion": 0, "apiTag": ""}}, {"success": true, "code": 200, "message": "", "data": {"id": 2108770660671029249, "name": "用友YonBIP", "type": "integrateSys", "sort": 0, "enable": 0, "children": {"children": {"id": "SCC", "name": "供应链云", "type": 1, "sort": 0, "enable": 0, "children": {"children": {"id": "MM", "name": "采购供应", "type": 2, "sort": 0, "enable": 0, "children": {"children": {"id": "ST", "name": "库存管理", "type": 3, "sort": 0, "enable": 0, "children": {"children": {"id": "ustock.st_materialout", "name": "材料出库单", "type": 4, "sort": 0, "enable": 0, "children": "", "parentId": "", "productId": "", "code": "ustock.st_materialout", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "ST", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "MM", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "SCC", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "current_yonbip_default_sys", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "isOrigin": 0, "hasChildren": 0, "order": 0}}, {"success": true, "code": 200, "message": "", "data": [{"id": "72113971-ae4c-4188-bc55-44b6173f4e0b", "name": "XS15", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "顾客订单号（订单表体）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:48:03", "gmtUpdate": "2025-07-26 17:48:03", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "b946709d-f4d9-4a43-a551-f55beee7f3d5", "name": "XXX0111", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求分类项", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:48:03", "gmtUpdate": "2025-07-26 17:48:03", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:48:03", "gmtUpdate": "2025-07-26 17:48:03", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": [{"id": "19173169-b78a-4df1-9ff6-7b6b86840c07", "name": "XS11", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求分类号test", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:48:15", "gmtUpdate": "2025-07-26 17:48:15", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:48:15", "gmtUpdate": "2025-07-26 17:48:15", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": [{"id": "9e6cfc56-06f1-4bfb-b0f0-a1345e5ae982", "name": "TL001", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "退料理由", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "bd.customerdoc_D001.D001", "ytenantId": "", "paramOrder": "", "bizType": "quote", "baseType": false, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:48:25", "gmtUpdate": "2025-07-26 17:48:25", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": true, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:48:25", "gmtUpdate": "2025-07-26 17:48:25", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": {"id": "ffeabc0e-ef62-464e-9a2f-df9b4972eb68", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:48:32", "gmtUpdate": "2025-07-26 17:48:32", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}}]