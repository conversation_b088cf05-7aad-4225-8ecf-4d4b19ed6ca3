采购订单列表查询
发布时间:2024-11-23 09:15:23
根据表头模式还是表头明细模式、分页条件和自定义条件查询采购订单列表数据信息

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	动态域名，获取方式详见 获取租户所在数据中心域名
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/purchaseorder/list
请求方式	POST
ContentType	application/json
应用场景	开放API
API类别	
事务和幂等性	无
限流次数	60次/分钟

多语	不支持
2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
pageIndex	long	否	是	页码    示例: 1    默认值: 1
pageSize	long	否	是	每页数    示例: 10    默认值: 10
isSum	boolean	否	否	查询表头    示例: false    默认值: false
simpleVOs	object	是	否	查询条件
field	string	否	否	查询字段（条件）    示例: code
op	string	否	否	比较符：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者    示例: eq
value1	string	否	否	参数值1    示例: CGA20005000456
queryOrders	object	是	否	排序字段
field	string	否	否	排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：purchaseOrders.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)    示例: id
order	string	否	否	顺序：正序(asc)、倒序(desc)    示例: asc
3. 请求示例
Url: /yonbip/scm/purchaseorder/list?access_token=访问令牌
Body: {
	"pageIndex": 1,
	"pageSize": 10,
	"isSum": false,
	"simpleVOs": [
		{
			"field": "code",
			"op": "eq",
			"value1": "CGA20005000456"
		}
	],
	"queryOrders": [
		{
			"field": "id",
			"order": "asc"
		}
	]
}
4. 返回值参数
名称	类型	数组	描述
code	string	否	编码
message	string	否	返回信息
data	object	否	数据项
pageIndex	long	否	页码
pageSize	long	否	每页数
recordCount	long	否	数量
recordList	object	是	返回信息
product_cCode	string	否	物料编码
invoiceVendor	long	否	开票供应商id
priceUOM_Precision	long	否	计价单位精度
modifyStatus	long	否	变更状态：0：未变更、1：变更中、2：变更完成
receiveStatus	long	否	收货状态：0：开立、1：已审核、10：已入库、11：待入库、12：待下单14：待结算15：已结算2：已关闭、3：审核中、4：锁定、5：未发货、6：已发货、7：已完成、8：待收、9：已收齐
listOriSum	number
小数位数:8,最大长度:28	否	含税金额
priceUOM_Code	string	否	计价单位编码
totalInTaxMoney	number
小数位数:8,最大长度:28	否	累计入库含税金额
totalQuantity	number
小数位数:8,最大长度:28	否	整单数量
natCurrency	string	否	本币
listTotalPayOriMoney	number
小数位数:8,最大长度:28	否	累计付款核销金额
unit_code	string	否	主计量编码
purchaseOrdersCharacteristics	特征组
pu.purchaseorder.PurchaseOrders	否	自由项特征组
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
purchaseOrdersDefineCharacter	特征组
pu.purchaseorder.PurchaseOrders	否	表体自定义项特征组
AA	string	否	批次号（扩展）
CG00025	Decimal	否	未收数量2
CG01	string	否	供应商备注（扩展）
WW	Date	否	委外交货日期
XS11	string	否	需求分类号test
XS15	string	否	顾客订单号（订单表体）
id	string	否	特征id,主键,新增时无需填写,修改时必填
purchaseOrderDefineCharacter	特征组
pu.purchaseorder.PurchaseOrder	否	表头自定义项特征组
U9002	string	否	U9采购订单号
XS31	Decimal	否	更改次数
id	string	否	特征id,主键,新增时无需填写,修改时必填
id	long	否	主表id
isWfControlled	boolean	否	是否审批流控制：true or false
totalArrivedTaxMoney	number
小数位数:8,最大长度:28	否	累计到货含税金额
purchaseOrders_arrivedStatus	long	否	到货状态：1：到货完成、2：未到货、3：部分到货、4：到货完成
bmake_st_purinvoice	boolean	否	流程订货订单开蓝票
realProductAttribute	long	否	实物商品属性
purchaseOrders_inWHStatus	long	否	入库状态：1：入库完成、2：未入库、3：部分入库、4：入库结束
totalSendQty	number
小数位数:8,最大长度:28	否	发货数量
natCurrency_priceDigit	long	否	本币
bmake_st_purinrecord_red	boolean	否	流程退库
status	long	否	状态：0：开立、1：已审核、2：已关闭、3：审核中
currency_moneyDigit	long	否	本币金额精度
listTotalPayApplyAmount	number
小数位数:8,最大长度:28	否	累计付款申请金额
currency_code	string	否	币种编码
vouchdate	string	否	单据日期，格式为:yyyy-MM-dd HH:mm:ss
invoiceVendor_name	string	否	开票供应商
vendor	long	否	供货供应商id
purchaseOrders_payStatus	long	否	核销状态：1：核销完成、2：未核销、3：部分核销
purchaseOrders_warehouse_code	string	否	仓库编码
listOriMoney	number
小数位数:8,最大长度:28	否	无税金额
currency	string	否	币种
pubts	string	否	时间戳，格式为:yyyy-MM-dd HH:mm:ss
org_name	string	否	采购组织
generalPurchaseOrderType	string	否	交易类型扩展参数
isFlowCoreBill	boolean	否	是否流程核心单据
creator	string	否	创建者
product	long	否	物料id
oriSum	number
小数位数:8,最大长度:28	否	含税金额
inInvoiceOrg_name	string	否	收票组织
product_defaultAlbumId	string	否	物料首图片
purchaseOrders_id	long	否	订单行id
totalRecieveQty	number
小数位数:8,最大长度:28	否	累计到货数量
demandOrg_name	string	否	需求组织
createTime	string	否	创建时间，格式为:yyyy-MM-dd HH:mm:ss
purUOM_Precision	long	否	采购单位精度
currency_priceDigit	long	否	币种单价精度
bEffectStock	boolean	否	影响可用量
inOrg	string	否	收货组织id
bustype_name	string	否	交易类型
purchaseOrders_invPriceExchRate	number
小数位数:8,最大长度:19	否	计价单位换算率
subQty	number
小数位数:8,最大长度:28	否	采购数量
inInvoiceOrg	string	否	收票组织id
product_cName	string	否	物料名称
listTaxRate	number
小数位数:8,最大长度:19	否	税率
bmake_st_purinvoice_red	boolean	否	流程订货订单开红票
product_model	string	否	型号
storagenum	number
小数位数:8,最大长度:28	否	已入库数量
purchaseOrders_invExchRate	number
小数位数:8,最大长度:19	否	采购换算率
vendor_name	string	否	供应商
vendor_code	string	否	供应商编码
oriUnitPrice	number
小数位数:8,最大长度:28	否	无税单价
barCode	string	否	单据条码
isContract	boolean	否	是否需要与供应商协同：true：是、false：否
unit_name	string	否	主计量
unit	long	否	主计量id
purchaseOrders_invoiceStatus	long	否	发票状态：1：开票完成、2：未开票、3：部分开票、4：开票结束
natCurrency_moneyDigit	long	否	本币
qty	number
小数位数:8,最大长度:28	否	数量
unit_Precision	long	否	主计量精度
oriTaxUnitPrice	number
小数位数:8,最大长度:28	否	含税单价
moneysum	number
小数位数:8,最大长度:28	否	金额
natCurrency_code	string	否	本币
product_modelDescription	string	否	规格说明
code	string	否	单据编码
demandOrg	string	否	需求组织id
bizFlow	string	否	流程ID
realProductAttributeType	long	否	实物商品属性
priceUOM	long	否	计价单位id
bizstatus	long	否	状态：0：初始开立、1：审批中、2：审批完成、3：不通过流程终止、4：驳回到制单
totalInQty	number
小数位数:8,最大长度:28	否	累计入库数量
bizFlow_version	string	否	版本信息
currency_name	string	否	币种
org	string	否	采购组织
bmake_st_purinrecord	boolean	否	流程入库
purchaseOrders_purUOM	long	否	采购单位编码
bustype	string	否	交易类型id
listOriTax	number
小数位数:8,最大长度:28	否	税额
retailInvestors	boolean	否	是否散户：true or false
inOrg_name	string	否	收货组织
listTotalPayAmount	number
小数位数:8,最大长度:28	否	累计付款金额
priceUOM_Name	string	否	计价单位名称
listTotalPayNATMoney	number
小数位数:8,最大长度:28	否	本币累计付款核销金额
approvenum	number
小数位数:8,最大长度:28	否	已审批数量
listdiscountTaxType	string	否	扣税类别：0：应税外加、1：应税内含
bizFlow_name	string	否	流程名称
pageCount	long	否	页数
beginPageIndex	long	否	起始页
endPageIndex	long	否	结束页
pubts	string	否	时间戳，格式为:yyyy-MM-dd HH:mm:ss
5. 正确返回示例
{
	"code": "200",
	"message": "操作成功",
	"data": {
		"pageIndex": 1,
		"pageSize": 10,
		"recordCount": 1,
		"recordList": [
			{
				"product_cCode": "00000002",
				"invoiceVendor": 2057714685366528,
				"priceUOM_Precision": 1,
				"modifyStatus": 0,
				"receiveStatus": 1,
				"listOriSum": 1,
				"priceUOM_Code": "001",
				"totalInTaxMoney": 0,
				"totalQuantity": 1,
				"natCurrency": "G001ZM0000DEFAULTCURRENCT00000000001",
				"listTotalPayOriMoney": 0,
				"unit_code": "001",
				"purchaseOrdersCharacteristics": {
					"id": "",
					"XS15": "",
					"XXX0111": ""
				},
				"purchaseOrdersDefineCharacter": {
					"id": "",
					"AA": "",
					"CG00025": 0,
					"CG01": "",
					"WW": "",
					"XS11": "",
					"XS15": ""
				},
				"purchaseOrderDefineCharacter": {
					"id": "",
					"U9002": "",
					"XS31": 0
				},
				"id": 2227385816011008,
				"isWfControlled": false,
				"totalArrivedTaxMoney": 0,
				"purchaseOrders_arrivedStatus": 2,
				"bmake_st_purinvoice": true,
				"realProductAttribute": 1,
				"purchaseOrders_inWHStatus": 2,
				"totalSendQty": 0,
				"natCurrency_priceDigit": 6,
				"bmake_st_purinrecord_red": true,
				"status": 0,
				"currency_moneyDigit": 6,
				"listTotalPayApplyAmount": 0,
				"currency_code": "CNY",
				"vouchdate": "2021-04-23 00:00:00",
				"invoiceVendor_name": "达利园供货目录转移专用供应商",
				"vendor": 2057714685366528,
				"purchaseOrders_payStatus": 2,
				"purchaseOrders_warehouse_code": "000001",
				"listOriMoney": 1,
				"currency": "G001ZM0000DEFAULTCURRENCT00000000001",
				"pubts": "2021-04-23 11:34:02",
				"org_name": "eflong",
				"generalPurchaseOrderType": "1",
				"isFlowCoreBill": true,
				"creator": "17600880447",
				"product": 1730491724599552,
				"oriSum": 1,
				"inInvoiceOrg_name": "eflong",
				"product_defaultAlbumId": "http://ys-yxy-testres.yonyoucloud.com/fa813c9d-a182-457c-ab2a-a0c40ab113ea.jpg",
				"purchaseOrders_id": 2227385816011009,
				"totalRecieveQty": 0,
				"demandOrg_name": "eflong",
				"createTime": "2021-04-23 11:34:01",
				"purUOM_Precision": 1,
				"currency_priceDigit": 6,
				"bEffectStock": true,
				"inOrg": "1730475987734784",
				"bustype_name": "普通订货-订单开票",
				"purchaseOrders_invPriceExchRate": 1,
				"subQty": 1,
				"inInvoiceOrg": "1730475987734784",
				"product_cName": "eflong-规格1",
				"listTaxRate": 0,
				"bmake_st_purinvoice_red": true,
				"product_model": "型号",
				"storagenum": 0,
				"purchaseOrders_invExchRate": 1,
				"vendor_name": "达利园供货目录转移专用供应商",
				"vendor_code": "0001000101",
				"oriUnitPrice": 1,
				"barCode": "st_purchaseorder|2227385816011008",
				"isContract": false,
				"unit_name": "个",
				"unit": 1730486466924800,
				"purchaseOrders_invoiceStatus": 2,
				"natCurrency_moneyDigit": 6,
				"qty": 1,
				"unit_Precision": 1,
				"oriTaxUnitPrice": 1,
				"moneysum": 1,
				"natCurrency_code": "CNY",
				"product_modelDescription": "说明2",
				"code": "CGA20005000456",
				"demandOrg": "1730475987734784",
				"bizFlow": "f596bd30-aee8-11ea-8d5f-0624d60000dc",
				"realProductAttributeType": 1,
				"priceUOM": 1730486466924800,
				"bizstatus": 0,
				"totalInQty": 0,
				"bizFlow_version": "V1.0",
				"currency_name": "人民币",
				"org": "1730475987734784",
				"bmake_st_purinrecord": true,
				"purchaseOrders_purUOM": 1730486466924800,
				"bustype": "1785637352591616",
				"listOriTax": 0,
				"retailInvestors": false,
				"inOrg_name": "eflong",
				"listTotalPayAmount": 0,
				"priceUOM_Name": "个",
				"listTotalPayNATMoney": 0,
				"approvenum": 0,
				"listdiscountTaxType": "0",
				"bizFlow_name": "普通订货（订单开票）"
			}
		],
		"pageCount": 1,
		"beginPageIndex": 1,
		"endPageIndex": 1,
		"pubts": "2021-04-23 12:40:06"
	}
}
6. 错误返回码
错误码	错误信息	描述
999	服务端逻辑异常	
7. 错误返回示例
{
    "code": 999,
    "message": "服务端逻辑异常"
}