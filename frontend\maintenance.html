<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>maintenance.html - 已迁移到新架构</title>
    
    <!-- 新架构核心文件 -->
    <script src="../js/core/component-manager.js"></script>
    <script src="../js/core/app-bootstrap.js"></script>
    <script src="../js/api-config-fix.js"></script>
    
    <!-- 所需组件 -->
    <script src="../js/common/api-client.js"></script>
    <script src="../js/common/validation-utils.js"></script>
    <script src="../js/common/error-handler.js"></script>
    <script src="../js/notification-system.js"></script>
    
    <!-- 自定义样式 -->
    <style>

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .content {
            padding: 30px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .status-item h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .status-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .schedule-info {
            background: #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .schedule-info h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        
        .schedule-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .schedule-item:last-child {
            border-bottom: none;
        }
        
        .log-container {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-entry {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-entry:last-child {
            border-bottom: none;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .success {
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    
    </style>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
</head>
<body>
    <!-- 迁移标识 -->
    <div style="position: fixed; top: 10px; right: 10px; background: #4CAF50; color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px; z-index: 9999;">
        ✅ 已迁移到新架构
    </div>
    
    <!-- 原始页面内容 -->
<div class="container">
        <div class="header">
            <h1>🧹 YS-API V3.0 维护管理</h1>
        </div>
        
        <div class="content">
            <!-- 状态概览 -->
            <div class="status-card">
                <h2>📊 存储状态概览</h2>
                <div class="status-grid" id="statusGrid">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <!-- 清理操作 -->
            <div class="status-card">
                <h2>🛠️ 维护操作</h2>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="cleanupCache()">
                        🗑️ 清理缓存
                    </button>
                    <button class="btn btn-warning" onclick="cleanupTemp()">
                        📁 清理临时文件
                    </button>
                    <button class="btn btn-danger" onclick="cleanupLogs()">
                        📋 清理日志文件
                    </button>
                    <button class="btn btn-success" onclick="cleanupAll()">
                        🧹 清理所有文件
                    </button>
                </div>
            </div>
            
            <!-- 定时任务信息 -->
            <div class="schedule-info">
                <h3>⏰ 定时清理计划</h3>
                <div id="scheduleInfo">
                    <div class="loading">加载中...</div>
                </div>
            </div>
            
            <!-- 操作日志 -->
            <div class="status-card">
                <h2>📝 操作日志</h2>
                <div class="log-container" id="logContainer">
                    <div class="log-entry">系统启动，等待操作...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新架构初始化脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 初始化新架构页面: maintenance.html');
            
            // 启动应用
            await window.startApp({
                environment: 'production',
                features: {
                "errorHandling": true,
                "notifications": true
}
            });
            
            // 获取所需组件
            const apiClient = window.ComponentManager.get('apiClient');
            const errorHandler = window.ComponentManager.get('errorHandler');
            const notifier = window.ComponentManager.get('notificationSystem');
            
            // 调用原始初始化逻辑
            if (typeof initializePage === 'function') {
                initializePage();
            }
            
            console.log('✅ 页面初始化完成: maintenance.html');
        });
        
        // 原始自定义脚本（已适配新架构）
        // API基础URL
                const API_BASE = 'http://127.0.0.1:8000/api/v1';
                
                // 日志容器
                const logContainer = document.getElementById('logContainer');
                
                // 添加日志
                function addLog(message, type = 'info') {
                    const timestamp = new Date().toLocaleString();
                    const logEntry = document.createElement('div');
                    logEntry.className = 'log-entry';
                    logEntry.innerHTML = `[${timestamp}] ${message}`;
                    
                    if (type === 'error') {
                        logEntry.style.color = '#dc3545';
                    } else if (type === 'success') {
                        logEntry.style.color = '#28a745';
                    }
                    
                    logContainer.appendChild(logEntry);
                    logContainer.scrollTop = logContainer.scrollHeight;
                }
                
                // 显示通知
                function showNotification(message, type = 'info') {
                    const notification = document.createElement('div');
                    notification.className = type === 'error' ? 'error' : 'success';
                    notification.textContent = message;
                    
                    document.querySelector('.content').insertBefore(notification, document.querySelector('.status-card'));
                    
                    setTimeout(() => {
                        notification.remove();
                    }, 5000);
                }
                
                // 加载状态信息
                async function loadStatus() {
                    try {
                        const response = await fetch(`${API_BASE}/maintenance/status`);
                        const data = await response.json();
                        
                        if (data.success) {
                            updateStatusGrid(data.data);
                            updateScheduleInfo(data.data);
                        } else {
                            throw new Error(data.message);
                        }
                    } catch (error) {
                        addLog(`加载状态失败: ${error.message}`, 'error');
                        showNotification(`加载状态失败: ${error.message}`, 'error');
                    }
                }
                
                // 更新状态网格
                function updateStatusGrid(data) {
                    const statusGrid = document.getElementById('statusGrid');
                    statusGrid.innerHTML = `
                        <div class="status-item">
                            <h3>缓存大小</h3>
                            <div class="status-value">${data.cache_size_mb} MB</div>
                        </div>
                        <div class="status-item">
                            <h3>临时文件大小</h3>
                            <div class="status-value">${data.temp_size_mb} MB</div>
                        </div>
                        <div class="status-item">
                            <h3>日志文件大小</h3>
                            <div class="status-value">${data.log_size_mb} MB</div>
                        </div>
                        <div class="status-item">
                            <h3>总大小</h3>
                            <div class="status-value">${data.total_size_mb} MB</div>
                        </div>
                    `;
                }
                
                // 更新定时任务信息
                function updateScheduleInfo(data) {
                    const scheduleInfo = document.getElementById('scheduleInfo');
                    scheduleInfo.innerHTML = `
                        <div class="schedule-item">
                            <span>缓存清理</span>
                            <span>每天 ${data.next_cache_cleanup || '未设置'}</span>
                        </div>
                        <div class="schedule-item">
                            <span>临时文件清理</span>
                            <span>每周日 ${data.next_temp_cleanup || '未设置'}</span>
                        </div>
                        <div class="schedule-item">
                            <span>日志文件清理</span>
                            <span>每周日 ${data.next_log_cleanup || '未设置'}</span>
                        </div>
                    `;
                }
                
                // 清理缓存
                async function cleanupCache() {
                    try {
                        addLog('开始清理缓存...');
                        const response = await fetch(`${API_BASE}/maintenance/cleanup/cache`, {
                            method: 'POST'
                        });
                        const data = await response.json();
                        
                        if (data.success) {
                            addLog('缓存清理完成', 'success');
                            showNotification('缓存清理完成', 'success');
                            loadStatus(); // 重新加载状态
                        } else {
                            throw new Error(data.message);
                        }
                    } catch (error) {
                        addLog(`缓存清理失败: ${error.message}`, 'error');
                        showNotification(`缓存清理失败: ${error.message}`, 'error');
                    }
                }
                
                // 清理临时文件
                async function cleanupTemp() {
                    try {
                        addLog('开始清理临时文件...');
                        const response = await fetch(`${API_BASE}/maintenance/cleanup/temp`, {
                            method: 'POST'
                        });
                        const data = await response.json();
                        
                        if (data.success) {
                            addLog('临时文件清理完成', 'success');
                            showNotification('临时文件清理完成', 'success');
                            loadStatus(); // 重新加载状态
                        } else {
                            throw new Error(data.message);
                        }
                    } catch (error) {
                        addLog(`临时文件清理失败: ${error.message}`, 'error');
                        showNotification(`临时文件清理失败: ${error.message}`, 'error');
                    }
                }
                
                // 清理日志文件
                async function cleanupLogs() {
                    try {
                        addLog('开始清理日志文件...');
                        const response = await fetch(`${API_BASE}/maintenance/cleanup/logs`, {
                            method: 'POST'
                        });
                        const data = await response.json();
                        
                        if (data.success) {
                            addLog('日志文件清理完成', 'success');
                            showNotification('日志文件清理完成', 'success');
                            loadStatus(); // 重新加载状态
                        } else {
                            throw new Error(data.message);
                        }
                    } catch (error) {
                        addLog(`日志文件清理失败: ${error.message}`, 'error');
                        showNotification(`日志文件清理失败: ${error.message}`, 'error');
                    }
                }
                
                // 清理所有文件
                async function cleanupAll() {
                    try {
                        addLog('开始清理所有文件...');
                        const response = await fetch(`${API_BASE}/maintenance/cleanup/all`, {
                            method: 'POST'
                        });
                        const data = await response.json();
                        
                        if (data.success) {
                            addLog('所有文件清理完成', 'success');
                            showNotification('所有文件清理完成', 'success');
                            loadStatus(); // 重新加载状态
                        } else {
                            throw new Error(data.message);
                        }
                    } catch (error) {
                        addLog(`文件清理失败: ${error.message}`, 'error');
                        showNotification(`文件清理失败: ${error.message}`, 'error');
                    }
                }
                
                // 页面加载完成后初始化
                document.addEventListener('DOMContentLoaded', function() {
                    addLog('维护管理页面已加载');
                    loadStatus();
                    
                    // 每30秒刷新一次状态
                    setInterval(loadStatus, 30000);
                });
    </script>
</body>
</html>