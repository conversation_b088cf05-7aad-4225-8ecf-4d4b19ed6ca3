<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>2152020559581937665</id>
<name>计划订单列表查询</name>
<apiClassifyId>1623462659853647878</apiClassifyId>
<apiClassifyName>需求计划</apiClassifyName>
<apiClassifyCode>requirementsplanning.mr_mps_plan_workbench_batch_import</apiClassifyCode>
<parentApiClassifies/>
<functionId/>
<openMode/>
<description>提供按条件分页查询计划订单列表信息的服务。</description>
<auth>true</auth>
<bodyPassthrough/>
<healthExam>false</healthExam>
<healthStatus>false</healthStatus>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/mfg/requirementsplanning/getPlanOrderList</completeProxyUrl>
<connectUrl>/api/v1/getPlanOrderByPage</connectUrl>
<sort>20</sort>
<handler>openapi</handler>
<httpRequestType>POST</httpRequestType>
<openApi>true</openApi>
<preset>false</preset>
<productId>4a176d6a681a4ebdbd053262493b5dff</productId>
<productCode/>
<proxyUrl>/yonbip/mfg/requirementsplanning/getPlanOrderList</proxyUrl>
<requestParamsDemo>Url: /yonbip/mfg/requirementsplanning/getPlanOrderList?access_token=访问令牌 Body: { "data": { "orgId": "1608788551787872266", "orgCode": "122701", "planParamId": "3115402872983808", "planParamCode": "MRP2019110200002", "startDate": "2022-01-10", "endDate": "2022-01-10", "supplyOrgId": "1608788551787872266", "supplyOrgCode": "122701", "departmentId": "1608788551787872266", "departmentCode": "bumen", "warehouseId": "1608788551787872266", "warehouseCode": "000012", "productIds": [ "1550141821342973955", "1550139381815705608" ], "productCodes": [ "000123", "000124" ], "planProperty": [ "1", "2" ] } }</requestParamsDemo>
<requestProtocol>HTTP</requestProtocol>
<serviceHttpMethod>POST</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2024-12-09 15:09:26.000</gmtCreate>
<gmtUpdate>2025-06-24 20:00:20.000</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/mfg/requirementsplanning/getPlanOrderList</address>
<productName/>
<productClassifyId/>
<productClassifyCode/>
<productClassifyName/>
<paramDTOS>
<paramDTOS>
<id>2172771101278994437</id>
<name>data</name>
<apiId>2152020559581937665</apiId>
<parentId/>
<children>
<children>
<id>2172771101278994438</id>
<name>pageIndex</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152045332953300996</defParamId>
<array>false</array>
<paramDesc>页号</paramDesc>
<paramType>number</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>1</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>8</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals>-1</decimals>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994439</id>
<name>pageSize</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152045332953300997</defParamId>
<array>false</array>
<paramDesc>每页条数</paramDesc>
<paramType>number</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>10</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>4</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals>-1</decimals>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994440</id>
<name>orgId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937671</defParamId>
<array>false</array>
<paramDesc>组织id，组织 Id 与组织编码不能同时为空，优先级：orgId>orgCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1608788551787872266</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994441</id>
<name>orgCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937672</defParamId>
<array>false</array>
<paramDesc>组织编码，组织 Id 与组织编码不能同时为空，优先级：orgId>orgCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>122701</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994442</id>
<name>planParamId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937673</defParamId>
<array>false</array>
<paramDesc>计划名称id，优先级：planParamId > planParamCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>3115402872983808</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994443</id>
<name>planParamCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937674</defParamId>
<array>false</array>
<paramDesc>计划名称编码，优先级：planParamId > planParamCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>MRP2019110200002</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994444</id>
<name>startDate</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937675</defParamId>
<array>false</array>
<paramDesc>开始时间</paramDesc>
<paramType>date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-01-10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format>yyyy-MM-dd</format>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994445</id>
<name>endDate</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937676</defParamId>
<array>false</array>
<paramDesc>结束时间</paramDesc>
<paramType>date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-01-10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format>yyyy-MM-dd</format>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994446</id>
<name>supplyOrgId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937677</defParamId>
<array>false</array>
<paramDesc>供应组织id，优先级：supplyOrgId > supplyOrgCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1608788551787872266</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994447</id>
<name>supplyOrgCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937678</defParamId>
<array>false</array>
<paramDesc>供应组织编码，优先级：supplyOrgId > supplyOrgCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>122701</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994448</id>
<name>departmentId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937679</defParamId>
<array>false</array>
<paramDesc>部门id，优先级：departmentId > departmentCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1608788551787872266</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994449</id>
<name>departmentCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937680</defParamId>
<array>false</array>
<paramDesc>部门编码，优先级：departmentId > departmentCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>bumen</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994450</id>
<name>warehouseId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937681</defParamId>
<array>false</array>
<paramDesc>仓库Id，优先级：warehouseId > warehouseCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1608788551787872266</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994451</id>
<name>warehouseCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937682</defParamId>
<array>false</array>
<paramDesc>仓库编码，优先级:warehouseId > warehouseCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>000012</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994452</id>
<name>productIds</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937683</defParamId>
<array>true</array>
<paramDesc>物料id，优先级:productIds > productCodes</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>["1550141821342973955","1550139381815705608"]</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994453</id>
<name>productCodes</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937684</defParamId>
<array>true</array>
<paramDesc>物料编码，优先级:productIds > productCodes</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>["000123","000124"]</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2172771101278994454</id>
<name>planProperty</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994437</parentId>
<defParamId>2152020559581937685</defParamId>
<array>true</array>
<paramDesc>计划属性 1,采购 ,2,委外 ,3,自制 ,4,调拨 ,5,组织间需求</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>["1","2"]</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>2152020559581937670</defParamId>
<array>false</array>
<paramDesc>参数</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>2172771101278994455</id>
<name>data</name>
<apiId>2152020559581937665</apiId>
<parentId/>
<children>
<children>
<id>2172771101278994456</id>
<name>pageIndex</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>页号</paramDesc>
<paramType>number</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageIndex</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>number</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994457</id>
<name>pageSize</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>页数大小</paramDesc>
<paramType>number</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageSize</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>number</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994458</id>
<name>orgId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>组织id，组织 Id 与组织编码不能同时为空，优先级：orgId>orgCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>orgId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994459</id>
<name>orgCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>组织编码，组织 Id 与组织编码不能同时为空，优先级：orgId>orgCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>orgCode</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994460</id>
<name>planParamId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划名称id，优先级：planParamId > planParamCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>planParamId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994461</id>
<name>planParamCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划名称编码，优先级：planParamId > planParamCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>planParamCode</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994462</id>
<name>startDate</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>开始时间</paramDesc>
<paramType>date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>startDate</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>date</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994463</id>
<name>endDate</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>结束时间</paramDesc>
<paramType>date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>endDate</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>date</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994464</id>
<name>supplyOrgId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>供应组织id，优先级：supplyOrgId > supplyOrgCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>supplyOrgId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994465</id>
<name>supplyOrgCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>供应组织编码，优先级：supplyOrgId > supplyOrgCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>supplyOrgCode</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994466</id>
<name>departmentId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>部门id，优先级：departmentId > departmentCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>departmentId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994467</id>
<name>departmentCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>部门编码，优先级：departmentId > departmentCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>departmentCode</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994468</id>
<name>warehouseId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>仓库Id，优先级：warehouseId > warehouseCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>warehouseId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994469</id>
<name>warehouseCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>仓库编码，优先级:warehouseId > warehouseCode</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>warehouseCode</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994470</id>
<name>productIds</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料id，优先级:productIds > productCodes</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>productIds</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994471</id>
<name>productCodes</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料编码，优先级:productIds > productCodes</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>productCodes</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994472</id>
<name>planProperty</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994455</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划属性 1,采购 ,2,委外 ,3,自制 ,4,调拨 ,5,组织间需求</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>planProperty</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>参数</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>data</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>2172771101278994578</id>
<name>code</name>
<apiId>2152020559581937665</apiId>
<parentId/>
<defParamId>2152804708941037571</defParamId>
<array>false</array>
<paramDesc>返回状态码，200 成功 999 失败</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>200</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2172771101278994579</id>
<name>message</name>
<apiId>2152020559581937665</apiId>
<parentId/>
<defParamId>2152804708941037572</defParamId>
<array>false</array>
<paramDesc>操作通知信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>操作成功</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2172771101278994473</id>
<name>data</name>
<apiId>2152020559581937665</apiId>
<parentId/>
<children>
<children>
<id>2172771101278994474</id>
<name>recordList</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994473</parentId>
<children>
<children>
<id>2172771101278994511</id>
<name>id</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037575</defParamId>
<array>false</array>
<paramDesc>计划订单id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1630874665323855906</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994512</id>
<name>orgId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037576</defParamId>
<array>false</array>
<paramDesc>计划组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1608788551787872266</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994513</id>
<name>orgCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037577</defParamId>
<array>false</array>
<paramDesc>计划组织编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>***********</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994514</id>
<name>transTypeId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037578</defParamId>
<array>false</array>
<paramDesc>交易类型id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1601383117764427779</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994515</id>
<name>transTypeCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037579</defParamId>
<array>false</array>
<paramDesc>交易类型编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>GEN-11</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994516</id>
<name>planParamId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037580</defParamId>
<array>false</array>
<paramDesc>计划名称id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994517</id>
<name>planParamCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037581</defParamId>
<array>false</array>
<paramDesc>计划名称编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>GEN0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994518</id>
<name>planParamName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037582</defParamId>
<array>false</array>
<paramDesc>计划名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>LRP2***********</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994519</id>
<name>createType</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037583</defParamId>
<array>false</array>
<paramDesc>创建类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994520</id>
<name>code</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037584</defParamId>
<array>false</array>
<paramDesc>计划订单号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>GEN00011</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994521</id>
<name>productCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037585</defParamId>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>000045</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994522</id>
<name>productId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037586</defParamId>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>159192034273814118</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994523</id>
<name>planProperty</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037587</defParamId>
<array>false</array>
<paramDesc>计划属性</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994524</id>
<name>bomId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037588</defParamId>
<array>false</array>
<paramDesc>BOM唯一标识</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1591920342738141189</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994525</id>
<name>bomCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037589</defParamId>
<array>false</array>
<paramDesc>BOM编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>likun-M.Code-002（固）</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994526</id>
<name>uom</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037590</defParamId>
<array>false</array>
<paramDesc>单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1570768779829837833</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994527</id>
<name>uomName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037591</defParamId>
<array>false</array>
<paramDesc>单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994528</id>
<name>uomCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037592</defParamId>
<array>false</array>
<paramDesc>单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>kg</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994529</id>
<name>assistUnit</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037593</defParamId>
<array>false</array>
<paramDesc>主计量单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1570768797009707017</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994530</id>
<name>assistUnitCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037594</defParamId>
<array>false</array>
<paramDesc>主计量单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>kg</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994531</id>
<name>assistUnitName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037595</defParamId>
<array>false</array>
<paramDesc>主计量单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>千克</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994532</id>
<name>originQuantity</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037596</defParamId>
<array>false</array>
<paramDesc>原始数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994533</id>
<name>assistUnitCount</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037597</defParamId>
<array>false</array>
<paramDesc>主计量计划量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994534</id>
<name>suggestPlanQuantity</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037598</defParamId>
<array>false</array>
<paramDesc>建议计划量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994535</id>
<name>inputQty</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037599</defParamId>
<array>false</array>
<paramDesc>投入计划量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994536</id>
<name>issuedQuantity</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037600</defParamId>
<array>false</array>
<paramDesc>已下达量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994537</id>
<name>startDate</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037601</defParamId>
<array>false</array>
<paramDesc>开工日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2023-01-06 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994538</id>
<name>finishDate</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037602</defParamId>
<array>false</array>
<paramDesc>完工日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2023-01-06 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994539</id>
<name>status</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037603</defParamId>
<array>false</array>
<paramDesc>状态</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994540</id>
<name>demandOrgId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037604</defParamId>
<array>false</array>
<paramDesc>需求组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>********56850456576</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994541</id>
<name>demandOrgCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037605</defParamId>
<array>false</array>
<paramDesc>需求组织编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>00310</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994542</id>
<name>supplyOrgId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037606</defParamId>
<array>false</array>
<paramDesc>供应组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>********56850456576</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994543</id>
<name>supplyOrgCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037607</defParamId>
<array>false</array>
<paramDesc>供应组织编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>00310</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994544</id>
<name>invOrgId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037608</defParamId>
<array>false</array>
<paramDesc>入库组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>********56850456576</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994545</id>
<name>invOrgCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037609</defParamId>
<array>false</array>
<paramDesc>入库组织编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>00310</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994546</id>
<name>source</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037610</defParamId>
<array>false</array>
<paramDesc>来源单据类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994547</id>
<name>upcode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037611</defParamId>
<array>false</array>
<paramDesc>来源单据号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>YCD20221228000006</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994548</id>
<name>srcSourceProductId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037612</defParamId>
<array>false</array>
<paramDesc>来源物料id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>********56850456576</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994549</id>
<name>srcSourceProductCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037613</defParamId>
<array>false</array>
<paramDesc>来源物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1035000045</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994550</id>
<name>firstsource</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037614</defParamId>
<array>false</array>
<paramDesc>源头单据类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>280</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994551</id>
<name>firstupcode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037615</defParamId>
<array>false</array>
<paramDesc>源头单据号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>YCD20221228000006</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994552</id>
<name>firstsourceautoid</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037616</defParamId>
<array>false</array>
<paramDesc>源头单据子表id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1623614151955316744</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994553</id>
<name>sourceMaterialId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037617</defParamId>
<array>false</array>
<paramDesc>源头物料</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1590995696384737289</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994554</id>
<name>sourceMaterialCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037618</defParamId>
<array>false</array>
<paramDesc>源头物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1035000045</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994555</id>
<name>departmentId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037619</defParamId>
<array>false</array>
<paramDesc>部门id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>********56850456576</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994556</id>
<name>departmentCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037620</defParamId>
<array>false</array>
<paramDesc>部门编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>001111</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994557</id>
<name>departmentName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037621</defParamId>
<array>false</array>
<paramDesc>部门名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>部门1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994558</id>
<name>warehouseId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037622</defParamId>
<array>false</array>
<paramDesc>仓库id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>********56850456576</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994559</id>
<name>warehouseCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037623</defParamId>
<array>false</array>
<paramDesc>仓库编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>001111</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994560</id>
<name>warehouseName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037624</defParamId>
<array>false</array>
<paramDesc>仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>仓库1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994561</id>
<name>isClosed</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037625</defParamId>
<array>false</array>
<paramDesc>关闭标识</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994562</id>
<name>remark</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037626</defParamId>
<array>false</array>
<paramDesc>备注</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>remark</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994563</id>
<name>projectId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037627</defParamId>
<array>false</array>
<paramDesc>项目id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>********56850456576</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994564</id>
<name>projectIdCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037628</defParamId>
<array>false</array>
<paramDesc>项目编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>001111</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994565</id>
<name>projectIdName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037629</defParamId>
<array>false</array>
<paramDesc>项目名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>项目一号</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994566</id>
<name>wbs</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037630</defParamId>
<array>false</array>
<paramDesc>wbs任务id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>********56850456576</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994567</id>
<name>wbsCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037631</defParamId>
<array>false</array>
<paramDesc>wbs任务编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>001111</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994568</id>
<name>wbsName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037632</defParamId>
<array>false</array>
<paramDesc>wbs任务名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>wbs任务一号</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994569</id>
<name>activity</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037633</defParamId>
<array>false</array>
<paramDesc>活动id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>********56850456576</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994570</id>
<name>activityCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037634</defParamId>
<array>false</array>
<paramDesc>活动编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>001111</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994571</id>
<name>activityName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<defParamId>2152804708941037635</defParamId>
<array>false</array>
<paramDesc>活动名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>活动一号</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994475</id>
<name>planOrderItem</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994474</parentId>
<children>
<children>
<id>2172771101278994476</id>
<name>itemProductId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037637</defParamId>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1681453239576297481</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994477</id>
<name>itemProductCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037638</defParamId>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>wlfl014</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994478</id>
<name>itemProductName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037639</defParamId>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>WC1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994479</id>
<name>mainUnitId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037640</defParamId>
<array>false</array>
<paramDesc>主计量id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1674787939942400002</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994480</id>
<name>mainUnitCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037641</defParamId>
<array>false</array>
<paramDesc>主计量编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>MKT</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994481</id>
<name>mainUnitName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037642</defParamId>
<array>false</array>
<paramDesc>主计量名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>立方米</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994482</id>
<name>stockUnitId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037643</defParamId>
<array>false</array>
<paramDesc>BOM单位id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1674787939942400004</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994483</id>
<name>stockUnitCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037644</defParamId>
<array>false</array>
<paramDesc>BOM单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>MTQ</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994484</id>
<name>stockUnitName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037645</defParamId>
<array>false</array>
<paramDesc>BOM单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>平方千米</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994485</id>
<name>changeRate</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037646</defParamId>
<array>false</array>
<paramDesc>换算率</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994486</id>
<name>requirementQuantity</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037647</defParamId>
<array>false</array>
<paramDesc>需求数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>5.01</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994487</id>
<name>auxiliaryRequirementQuantity</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037648</defParamId>
<array>false</array>
<paramDesc>需求件数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>5.01</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994488</id>
<name>stockOrgId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037649</defParamId>
<array>false</array>
<paramDesc>库存单位id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1681369238604349442</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994489</id>
<name>stockOrgCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037650</defParamId>
<array>false</array>
<paramDesc>库存单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>zzw</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994490</id>
<name>stockOrgName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037651</defParamId>
<array>false</array>
<paramDesc>库存单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>库存w</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994491</id>
<name>warehouseId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037652</defParamId>
<array>false</array>
<paramDesc>仓库id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1681372373925232646</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994492</id>
<name>warehouseCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037653</defParamId>
<array>false</array>
<paramDesc>仓库编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>w2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994493</id>
<name>warehouseName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037654</defParamId>
<array>false</array>
<paramDesc>仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>仓库w2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994494</id>
<name>reqDate</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037655</defParamId>
<array>false</array>
<paramDesc>需求日期</paramDesc>
<paramType>date</paramType>
<requestParamType/>
<path/>
<example>2024-04-08 23:59:59</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format>yyyy-MM-dd HH:mm:ss</format>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994495</id>
<name>remark</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037656</defParamId>
<array>false</array>
<paramDesc>备注</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>备注</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994496</id>
<name>substituteFlag</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037657</defParamId>
<array>false</array>
<paramDesc>替代标识</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994499</id>
<name>projectId</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037660</defParamId>
<array>false</array>
<paramDesc>项目id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1654046039626743861</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994500</id>
<name>projectIdCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037661</defParamId>
<array>false</array>
<paramDesc>项目编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>pj1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994501</id>
<name>projectIdName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037662</defParamId>
<array>false</array>
<paramDesc>项目名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>项目名称</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994502</id>
<name>wbs</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037663</defParamId>
<array>false</array>
<paramDesc>wbs任务id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>********56850456576</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994503</id>
<name>wbsCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037664</defParamId>
<array>false</array>
<paramDesc>wbs任务编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>00001111</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994504</id>
<name>wbsName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037665</defParamId>
<array>false</array>
<paramDesc>wbs任务名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>wbs任务一号</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994505</id>
<name>activity</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037666</defParamId>
<array>false</array>
<paramDesc>活动id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>********56850456575</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994506</id>
<name>activityCode</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037667</defParamId>
<array>false</array>
<paramDesc>活动编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>001111</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994507</id>
<name>activityName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037668</defParamId>
<array>false</array>
<paramDesc>活动名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>活动一号</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994508</id>
<name>reserveid</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037669</defParamId>
<array>false</array>
<paramDesc>跟踪线索id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>********56850456574</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994509</id>
<name>reserveTypeName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037670</defParamId>
<array>false</array>
<paramDesc>需求跟踪方式</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>自定义</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994510</id>
<name>reserveName</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994475</parentId>
<defParamId>2152804708941037671</defParamId>
<array>false</array>
<paramDesc>跟踪线索</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>跟踪线索</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2152804708941037636</defParamId>
<array>true</array>
<paramDesc>计划订单备料</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2152804708941037574</defParamId>
<array>false</array>
<paramDesc>数据信息</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994572</id>
<name>pageIndex</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994473</parentId>
<defParamId>2152804708941037672</defParamId>
<array>false</array>
<paramDesc>当前页码</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994573</id>
<name>pageSize</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994473</parentId>
<defParamId>2152804708941037673</defParamId>
<array>false</array>
<paramDesc>每页条数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994574</id>
<name>recordCount</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994473</parentId>
<defParamId>2152804708941037674</defParamId>
<array>false</array>
<paramDesc>总条数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>13</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994575</id>
<name>pageCount</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994473</parentId>
<defParamId>2152804708941037675</defParamId>
<array>false</array>
<paramDesc>页码数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994576</id>
<name>beginPageIndex</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994473</parentId>
<defParamId>2162369558160605191</defParamId>
<array>false</array>
<paramDesc>开始页码</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2172771101278994577</id>
<name>endPageIndex</name>
<apiId>2152020559581937665</apiId>
<parentId>2172771101278994473</parentId>
<defParamId>2162369558160605192</defParamId>
<array>false</array>
<paramDesc>结束页码</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>0</decimals>
<maxLength>10</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2152804708941037573</defParamId>
<array>true</array>
<paramDesc>数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-01-06 14:10:47.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:47.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2172771109868929031</id>
<apiId>2152020559581937665</apiId>
<content>{ "code": "200", "message": "操作成功", "data": [ { "recordList": { "id": "1630874665323855906", "orgId": "1608788551787872266", "orgCode": "***********", "transTypeId": "1601383117764427779", "transTypeCode": "GEN-11", "planParamId": "****************", "planParamCode": "GEN0", "planParamName": "LRP2***********", "createType": "2", "code": "GEN00011", "productCode": "000045", "productId": "159192034273814118", "planProperty": "1", "bomId": "1591920342738141189", "bomCode": "likun-M.Code-002（固）", "uom": "1570768779829837833", "uomName": "千克", "uomCode": "kg", "assistUnit": "1570768797009707017", "assistUnitCode": "kg", "assistUnitName": "千克", "originQuantity": 10, "assistUnitCount": 10, "suggestPlanQuantity": 10, "inputQty": 10, "issuedQuantity": 0, "startDate": "2023-01-06 00:00:00", "finishDate": "2023-01-06 00:00:00", "status": "0", "demandOrgId": "********56850456576", "demandOrgCode": "00310", "supplyOrgId": "********56850456576", "supplyOrgCode": "00310", "invOrgId": "********56850456576", "invOrgCode": "00310", "source": "10", "upcode": "YCD20221228000006", "srcSourceProductId": "********56850456576", "srcSourceProductCode": "1035000045", "firstsource": "280", "firstupcode": "YCD20221228000006", "firstsourceautoid": "1623614151955316744", "sourceMaterialId": "1590995696384737289", "sourceMaterialCode": "1035000045", "departmentId": "********56850456576", "departmentCode": "001111", "departmentName": "部门1", "warehouseId": "********56850456576", "warehouseCode": "001111", "warehouseName": "仓库1", "isClosed": false, "remark": "remark", "projectId": "********56850456576", "projectIdCode": "001111", "projectIdName": "项目一号", "wbs": "********56850456576", "wbsCode": "001111", "wbsName": "wbs任务一号", "activity": "********56850456576", "activityCode": "001111", "activityName": "活动一号", "planOrderItem": [ { "itemProductId": "1681453239576297481", "itemProductCode": "wlfl014", "itemProductName": "WC1", "mainUnitId": "1674787939942400002", "mainUnitCode": "MKT", "mainUnitName": "立方米", "stockUnitId": "1674787939942400004", "stockUnitCode": "MTQ", "stockUnitName": "平方千米", "changeRate": 1, "requirementQuantity": 5.01, "auxiliaryRequirementQuantity": 5.01, "stockOrgId": "1681369238604349442", "stockOrgCode": "zzw", "stockOrgName": "库存w", "warehouseId": "1681372373925232646", "warehouseCode": "w2", "warehouseName": "仓库w2", "reqDate": "2024-04-08 23:59:59", "remark": "备注", "substituteFlag": "0", "itemUserDefineCharacter": { "ytenant": "0000LDTXR6979CPCME0000", "id": "1976091036898295816", "dadw": false, "wsz": 1 }, "itemFreeCharacteristics": "{ \"wjbda\": \"1654047774817124367\", \"ytenant\": \"0000LDTXR6979CPCME0000\", \"id\": \"1970249434717487214\", \"wbe\": false, \"pubts\": \"2024-04-08", "projectId": "1654046039626743861", "projectIdCode": "pj1", "projectIdName": "项目名称", "wbs": "********56850456576", "wbsCode": "00001111", "wbsName": "wbs任务一号", "activity": "********56850456575", "activityCode": "001111", "activityName": "活动一号", "reserveid": "********56850456574", "reserveTypeName": "自定义", "reserveName": "跟踪线索" } ] }, "pageIndex": 1, "pageSize": 10, "recordCount": 13, "pageCount": 2, "beginPageIndex": 1, "endPageIndex": 1 } ] }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-01-06 14:10:48.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:48.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2172771109868929032</id>
<apiId>2152020559581937665</apiId>
<content>{ "code": "310008", "message": "参数校验失败，参数[data]是必填的。" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-01-06 14:10:48.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:48.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2172771109868929031</id>
<apiId>2152020559581937665</apiId>
<content>{ "code": "200", "message": "操作成功", "data": [ { "recordList": { "id": "1630874665323855906", "orgId": "1608788551787872266", "orgCode": "***********", "transTypeId": "1601383117764427779", "transTypeCode": "GEN-11", "planParamId": "****************", "planParamCode": "GEN0", "planParamName": "LRP2***********", "createType": "2", "code": "GEN00011", "productCode": "000045", "productId": "159192034273814118", "planProperty": "1", "bomId": "1591920342738141189", "bomCode": "likun-M.Code-002（固）", "uom": "1570768779829837833", "uomName": "千克", "uomCode": "kg", "assistUnit": "1570768797009707017", "assistUnitCode": "kg", "assistUnitName": "千克", "originQuantity": 10, "assistUnitCount": 10, "suggestPlanQuantity": 10, "inputQty": 10, "issuedQuantity": 0, "startDate": "2023-01-06 00:00:00", "finishDate": "2023-01-06 00:00:00", "status": "0", "demandOrgId": "********56850456576", "demandOrgCode": "00310", "supplyOrgId": "********56850456576", "supplyOrgCode": "00310", "invOrgId": "********56850456576", "invOrgCode": "00310", "source": "10", "upcode": "YCD20221228000006", "srcSourceProductId": "********56850456576", "srcSourceProductCode": "1035000045", "firstsource": "280", "firstupcode": "YCD20221228000006", "firstsourceautoid": "1623614151955316744", "sourceMaterialId": "1590995696384737289", "sourceMaterialCode": "1035000045", "departmentId": "********56850456576", "departmentCode": "001111", "departmentName": "部门1", "warehouseId": "********56850456576", "warehouseCode": "001111", "warehouseName": "仓库1", "isClosed": false, "remark": "remark", "projectId": "********56850456576", "projectIdCode": "001111", "projectIdName": "项目一号", "wbs": "********56850456576", "wbsCode": "001111", "wbsName": "wbs任务一号", "activity": "********56850456576", "activityCode": "001111", "activityName": "活动一号", "planOrderItem": [ { "itemProductId": "1681453239576297481", "itemProductCode": "wlfl014", "itemProductName": "WC1", "mainUnitId": "1674787939942400002", "mainUnitCode": "MKT", "mainUnitName": "立方米", "stockUnitId": "1674787939942400004", "stockUnitCode": "MTQ", "stockUnitName": "平方千米", "changeRate": 1, "requirementQuantity": 5.01, "auxiliaryRequirementQuantity": 5.01, "stockOrgId": "1681369238604349442", "stockOrgCode": "zzw", "stockOrgName": "库存w", "warehouseId": "1681372373925232646", "warehouseCode": "w2", "warehouseName": "仓库w2", "reqDate": "2024-04-08 23:59:59", "remark": "备注", "substituteFlag": "0", "itemUserDefineCharacter": { "ytenant": "0000LDTXR6979CPCME0000", "id": "1976091036898295816", "dadw": false, "wsz": 1 }, "itemFreeCharacteristics": "{ \"wjbda\": \"1654047774817124367\", \"ytenant\": \"0000LDTXR6979CPCME0000\", \"id\": \"1970249434717487214\", \"wbe\": false, \"pubts\": \"2024-04-08", "projectId": "1654046039626743861", "projectIdCode": "pj1", "projectIdName": "项目名称", "wbs": "********56850456576", "wbsCode": "00001111", "wbsName": "wbs任务一号", "activity": "********56850456575", "activityCode": "001111", "activityName": "活动一号", "reserveid": "********56850456574", "reserveTypeName": "自定义", "reserveName": "跟踪线索" } ] }, "pageIndex": 1, "pageSize": 10, "recordCount": 13, "pageCount": 2, "beginPageIndex": 1, "endPageIndex": 1 } ] }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-01-06 14:10:48.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:48.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2172771109868929032</id>
<apiId>2152020559581937665</apiId>
<content>{ "code": "310008", "message": "参数校验失败，参数[data]是必填的。" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-01-06 14:10:48.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:48.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>2172771109868929031</id>
<apiId>2152020559581937665</apiId>
<content>{ "code": "200", "message": "操作成功", "data": [ { "recordList": { "id": "1630874665323855906", "orgId": "1608788551787872266", "orgCode": "***********", "transTypeId": "1601383117764427779", "transTypeCode": "GEN-11", "planParamId": "****************", "planParamCode": "GEN0", "planParamName": "LRP2***********", "createType": "2", "code": "GEN00011", "productCode": "000045", "productId": "159192034273814118", "planProperty": "1", "bomId": "1591920342738141189", "bomCode": "likun-M.Code-002（固）", "uom": "1570768779829837833", "uomName": "千克", "uomCode": "kg", "assistUnit": "1570768797009707017", "assistUnitCode": "kg", "assistUnitName": "千克", "originQuantity": 10, "assistUnitCount": 10, "suggestPlanQuantity": 10, "inputQty": 10, "issuedQuantity": 0, "startDate": "2023-01-06 00:00:00", "finishDate": "2023-01-06 00:00:00", "status": "0", "demandOrgId": "********56850456576", "demandOrgCode": "00310", "supplyOrgId": "********56850456576", "supplyOrgCode": "00310", "invOrgId": "********56850456576", "invOrgCode": "00310", "source": "10", "upcode": "YCD20221228000006", "srcSourceProductId": "********56850456576", "srcSourceProductCode": "1035000045", "firstsource": "280", "firstupcode": "YCD20221228000006", "firstsourceautoid": "1623614151955316744", "sourceMaterialId": "1590995696384737289", "sourceMaterialCode": "1035000045", "departmentId": "********56850456576", "departmentCode": "001111", "departmentName": "部门1", "warehouseId": "********56850456576", "warehouseCode": "001111", "warehouseName": "仓库1", "isClosed": false, "remark": "remark", "projectId": "********56850456576", "projectIdCode": "001111", "projectIdName": "项目一号", "wbs": "********56850456576", "wbsCode": "001111", "wbsName": "wbs任务一号", "activity": "********56850456576", "activityCode": "001111", "activityName": "活动一号", "planOrderItem": [ { "itemProductId": "1681453239576297481", "itemProductCode": "wlfl014", "itemProductName": "WC1", "mainUnitId": "1674787939942400002", "mainUnitCode": "MKT", "mainUnitName": "立方米", "stockUnitId": "1674787939942400004", "stockUnitCode": "MTQ", "stockUnitName": "平方千米", "changeRate": 1, "requirementQuantity": 5.01, "auxiliaryRequirementQuantity": 5.01, "stockOrgId": "1681369238604349442", "stockOrgCode": "zzw", "stockOrgName": "库存w", "warehouseId": "1681372373925232646", "warehouseCode": "w2", "warehouseName": "仓库w2", "reqDate": "2024-04-08 23:59:59", "remark": "备注", "substituteFlag": "0", "itemUserDefineCharacter": { "ytenant": "0000LDTXR6979CPCME0000", "id": "1976091036898295816", "dadw": false, "wsz": 1 }, "itemFreeCharacteristics": "{ \"wjbda\": \"1654047774817124367\", \"ytenant\": \"0000LDTXR6979CPCME0000\", \"id\": \"1970249434717487214\", \"wbe\": false, \"pubts\": \"2024-04-08", "projectId": "1654046039626743861", "projectIdCode": "pj1", "projectIdName": "项目名称", "wbs": "********56850456576", "wbsCode": "00001111", "wbsName": "wbs任务一号", "activity": "********56850456575", "activityCode": "001111", "activityName": "活动一号", "reserveid": "********56850456574", "reserveTypeName": "自定义", "reserveName": "跟踪线索" } ] }, "pageIndex": 1, "pageSize": 10, "recordCount": 13, "pageCount": 2, "beginPageIndex": 1, "endPageIndex": 1 } ] }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-01-06 14:10:48.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:48.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>2172771109868929032</id>
<apiId>2152020559581937665</apiId>
<content>{ "code": "310008", "message": "参数校验失败，参数[data]是必填的。" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-01-06 14:10:48.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:48.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS>
<errorCodeDTOS>
<id>2172771109868929029</id>
<apiId>2152020559581937665</apiId>
<errorCode>310008</errorCode>
<errorMessage>取决于错误类型，不同错误信息不同</errorMessage>
<errorType>API</errorType>
<errorcodeDesc/>
<gmtCreate>2025-01-06 14:10:48.000</gmtCreate>
<gmtUpdate>2025-01-06 14:10:48.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<defErrorId>2152020559581937902</defErrorId>
<ytenantId>0</ytenantId>
<displayCodeId/>
</errorCodeDTOS>
</errorCodeDTOS>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>友户通token认证业务扩展插件</name>
<configurable>false</configurable>
<description>YonsuitBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>2152020559581937665</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin/>
<mapReturnPluginConfig/>
<billNo/>
<domain/>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser>3970ff8d-d4d1-4e03-9b63-f59412cf4886</createUser>
<createUserName>昵称-15810437080</createUserName>
<approvalStatus>1</approvalStatus>
<publishTime>2025-06-24 20:00:33</publishTime>
<pathJoin>false</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout>0</mockTimeout>
<customUrl>requirementsplanning/getPlanOrderList</customUrl>
<fixedUrl>/yonbip/mfg/</fixedUrl>
<apiCode>2152020559581937665</apiCode>
<tokenCheckType>0</tokenCheckType>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL>false</bidirectionalSSL>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>ced88565-facc-4067-b773-e9b1337f3f4e</updateUserId>
<updateUserName>PJJ</updateUserName>
<paramIsForce>true</paramIsForce>
<userIDPassthrough>true</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.yonbip-mm-mfmr</microServiceCode>
<applicationCode>MR</applicationCode>
<privacyCategory>1</privacyCategory>
<privacyLevel>3</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType>0</serviceType>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType>-1</integrateObjectCreatedType>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>false</arrayParam>
<fileSize>0</fileSize>
<cc>true</cc>
<paramTransferMode>1</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId>2152020559581937668</apiDefId>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>0</paramExtRequest>
<paramExtResponse>0</paramExtResponse>
<paramExtInExtendKey>0</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene>0</integrationScene>
<apiType/>
<paramMark/>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>false</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>false</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>2</chargeStatus>
<beforeSpeed/>
<afterSpeed/>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath/>
<pubHistory>
<pubHistory>
<id>2298378158575976458</id>
<apiId>2152020559581937665</apiId>
<apiName>计划订单列表查询</apiName>
<applyReason/>
<publishUserName/>
<version>20250624200033</version>
<operationTime>2025-06-24</operationTime>
<gmtCreate/>
<gmtUpdate/>
</pubHistory>
</pubHistory>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode>requirementsplanning.mr_mps_plan_workbench_batch_import</domainAppCode>
<multiVersion>0</multiVersion>
<apiTag/>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>2108770660671029249</id>
<name>用友YonBIP</name>
<type>integrateSys</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MFC</id>
<name>制造云</name>
<type>1</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MF</id>
<name>生产制造</name>
<type>2</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MR</id>
<name>生产计划</name>
<type>3</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>requirementsplanning.mr_mps_plan_workbench_batch_import</id>
<name>计划订单</name>
<type>4</type>
<sort>0</sort>
<enable>0</enable>
<children/>
<parentId/>
<productId/>
<code>requirementsplanning.mr_mps_plan_workbench_batch_import</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MR</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MF</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MFC</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>current_yonbip_default_sys</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<isOrigin>0</isOrigin>
<hasChildren>0</hasChildren>
<order>0</order>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>fe16c539-4839-4f12-98cb-54960ac7806d</id>
<name>XS11</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类号test</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:53:18</gmtCreate>
<gmtUpdate>2025-07-26 17:53:18</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>7d259f5d-7a1d-41e3-a3a5-27570d5c2dc4</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:53:18</gmtCreate>
<gmtUpdate>2025-07-26 17:53:18</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:53:18</gmtCreate>
<gmtUpdate>2025-07-26 17:53:18</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
