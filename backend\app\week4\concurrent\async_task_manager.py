import asyncio
import time
import uuid
from collections import defaultdict
from enum import Enum

import structlog

"""
YS-API V3.0 异步任务管理器
Month 3 Week 4: 高性能异步任务调度和管理
"""


logger = structlog.get_logger()


class AsyncTaskStatus(Enum):
    """异步任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class TaskType(Enum):
    """任务类型"""
    API_REQUEST = "api_request"
    DATA_PROCESSING = "data_processing"
    FILE_OPERATION = "file_operation"
    DATABASE_OPERATION = "database_operation"
    SCHEDULED_TASK = "scheduled_task"
    MAINTENANCE = "maintenance"


@dataclass
class AsyncTask:
    """异步任务定义"""
    task_id: str
    name: str
    task_type: TaskType
    coro: Awaitable[Any]
    priority: int = 5  # 1-10, 10为最高优先级
    timeout: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    retry_delay: float = 1.0
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: AsyncTaskStatus = AsyncTaskStatus.PENDING
    result: Any = None
    error: Optional[Exception] = None
    context: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)
    callback: Optional[Callable] = None


@dataclass
class TaskGroup:
    """任务组"""
    group_id: str
    name: str
    tasks: List[str] = field(default_factory=list)
    parallel: bool = True
    created_at: datetime = field(default_factory=datetime.now)
    completed_tasks: int = 0
    failed_tasks: int = 0


class AsyncTaskManager:
    """异步任务管理器"""

    def __init___(
        self,
        max_concurrent_tasks: int = 100,
        max_queue_size: int = 10000,
        task_timeout: float = 300.0,
        cleanup_interval: float = 3600.0  # 1小时清理一次
    ):
    """TODO: Add function description."""
    self.max_concurrent_tasks = max_concurrent_tasks
    self.max_queue_size = max_queue_size
    self.task_timeout = task_timeout
    self.cleanup_interval = cleanup_interval

    # 任务存储
    self.tasks: Dict[str, AsyncTask] = {}
    self.task_groups: Dict[str, TaskGroup] = {}
    self.pending_tasks: asyncio.PriorityQueue = asyncio.PriorityQueue(
        maxsize=max_queue_size)
    self.running_tasks: Dict[str, asyncio.Task] = {}

    # 任务依赖关系
    self.dependency_graph: Dict[str, List[str]] = defaultdict(list)
    self.reverse_dependencies: Dict[str, List[str]] = defaultdict(list)

    # 调度控制
    self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
    self.is_running = False
    self.scheduler_task: Optional[asyncio.Task] = None
    self.cleanup_task: Optional[asyncio.Task] = None

    # 统计信息
    self.stats = {
        'total_tasks_created': 0,
        'total_tasks_completed': 0,
        'total_tasks_failed': 0,
        'total_tasks_cancelled': 0,
        'total_tasks_timeout': 0,
        'total_processing_time': 0.0,
        'peak_concurrent_tasks': 0,
        'average_queue_time': 0.0,
        'task_type_stats': defaultdict(int)
    }

    # 事件回调
    self.event_callbacks = {
        'task_started': [],
        'task_completed': [],
        'task_failed': [],
        'task_cancelled': [],
        'queue_full': [],
        'high_concurrency': []
    }

    logger.info(
        "异步任务管理器初始化完成",
        max_concurrent=max_concurrent_tasks,
        max_queue_size=max_queue_size
    )

    async def start(self):
        """启动任务管理器"""
        if self.is_running:
            logger.warning("任务管理器已在运行")
            return

        self.is_running = True

        # 启动调度器
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())

        # 启动清理任务
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())

        logger.info("异步任务管理器已启动")

    async def stop(self, timeout: float = 30.0):
        """停止任务管理器"""
        if not self.is_running:
            return

        logger.info("正在停止异步任务管理器...")

        self.is_running = False

        # 取消所有运行中的任务
        for task_id, task in self.running_tasks.items():
            if not task.done():
                task.cancel()
                logger.debug("取消任务", task_id=task_id)

        # 等待所有任务完成或超时
        if self.running_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(
                        *self.running_tasks.values(),
                        return_exceptions=True),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                logger.warning("等待任务完成超时")

        # 停止调度器和清理任务
        if self.scheduler_task:
            self.scheduler_task.cancel()
        if self.cleanup_task:
            self.cleanup_task.cancel()

        logger.info("异步任务管理器已停止")

    async def submit_task(
        self,
        coro: Awaitable[Any],
        name: str = None,
        task_type: TaskType = TaskType.API_REQUEST,
        priority: int = 5,
        timeout: Optional[float] = None,
        max_retries: int = 3,
        context: Dict[str, Any] = None,
        dependencies: List[str] = None,
        callback: Optional[Callable] = None
    ) -> str:
        """提交异步任务"""
        if not self.is_running:
            raise RuntimeError("任务管理器未运行")

        task_id = str(uuid.uuid4())

        task = AsyncTask(
            task_id=task_id,
            name=name or f"Task_{task_id[:8]}",
            task_type=task_type,
            coro=coro,
            priority=priority,
            timeout=timeout or self.task_timeout,
            max_retries=max_retries,
            context=context or {},
            dependencies=dependencies or [],
            callback=callback
        )

        self.tasks[task_id] = task
        self.stats['total_tasks_created'] += 1
        self.stats['task_type_stats'][task_type.value] += 1

        # 处理依赖关系
        if dependencies:
            self._add_dependencies(task_id, dependencies)

        # 检查是否可以立即调度
        if self._can_schedule_task(task_id):
            await self._queue_task(task)

        logger.debug(
            "任务已提交",
            task_id=task_id,
            name=task.name,
            priority=priority,
            dependencies=len(dependencies) if dependencies else 0
        )

        return task_id

    async def submit_task_group(
        self,
        tasks: List[Dict[str, Any]],
        group_name: str = None,
        parallel: bool = True
    ) -> str:
        """提交任务组"""
        group_id = str(uuid.uuid4())
        group = TaskGroup(
            group_id=group_id,
            name=group_name or f"Group_{group_id[:8]}",
            parallel=parallel
        )

        task_ids = []

        for task_config in tasks:
            task_id = await self.submit_task(**task_config)
            task_ids.append(task_id)

            # 如果是串行执行，添加依赖关系
            if not parallel and task_ids:
                prev_task_id = task_ids[-2] if len(task_ids) > 1 else None
                if prev_task_id:
                    self._add_dependencies(task_id, [prev_task_id])

        group.tasks = task_ids
        self.task_groups[group_id] = group

        logger.info(
            "任务组已提交",
            group_id=group_id,
            task_count=len(task_ids),
            parallel=parallel
        )

        return group_id

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        task = self.tasks.get(task_id)
        if not task:
            return False

        # 如果任务在运行中，取消asyncio任务
        if task_id in self.running_tasks:
            self.running_tasks[task_id].cancel()

        task.status = AsyncTaskStatus.CANCELLED
        task.completed_at = datetime.now()
        self.stats['total_tasks_cancelled'] += 1

        # 触发回调
        await self._trigger_event('task_cancelled', task)

        logger.info("任务已取消", task_id=task_id)
        return True

    async def cancel_task_group(self, group_id: str) -> bool:
        """取消任务组"""
        group = self.task_groups.get(group_id)
        if not group:
            return False

        cancelled_count = 0
        for task_id in group.tasks:
            if await self.cancel_task(task_id):
                cancelled_count += 1

        logger.info(
            "任务组已取消",
            group_id=group_id,
            cancelled_count=cancelled_count,
            total_tasks=len(group.tasks)
        )

        return cancelled_count > 0

    def get_task_status(self, task_id: str) -> Optional[AsyncTask]:
        """获取任务状态"""
        return self.tasks.get(task_id)

    def get_task_group_status(self, group_id: str) -> Optional[Dict[str, Any]]:
        """获取任务组状态"""
        group = self.task_groups.get(group_id)
        if not group:
            return None

        task_statuses = []
        for task_id in group.tasks:
            task = self.tasks.get(task_id)
            if task:
                task_statuses.append({
                    'task_id': task_id,
                    'name': task.name,
                    'status': task.status.value,
                    'progress': self._calculate_task_progress(task)
                })

        completed = sum(1 for t in task_statuses if t['status'] == 'completed')
        failed = sum(1 for t in task_statuses if t['status'] == 'failed')

        return {
            'group_id': group_id,
            'name': group.name,
            'total_tasks': len(group.tasks),
            'completed_tasks': completed,
            'failed_tasks': failed,
            'progress': completed / len(group.tasks) if group.tasks else 0,
            'tasks': task_statuses
        }

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        current_time = datetime.now()

        # 计算平均处理时间
        completed_tasks = self.stats['total_tasks_completed']
        avg_processing_time = (
            self.stats['total_processing_time'] / completed_tasks
            if completed_tasks > 0 else 0
        )

        # 计算成功率
        total_finished = (
            self.stats['total_tasks_completed'] +
            self.stats['total_tasks_failed'] +
            self.stats['total_tasks_cancelled']
        )
        success_rate = (
            self.stats['total_tasks_completed'] / total_finished
            if total_finished > 0 else 0
        )

        return {
            'current_time': current_time.isoformat(),
            'total_tasks': len(
                self.tasks),
            'pending_tasks': self.pending_tasks.qsize(),
            'running_tasks': len(
                self.running_tasks),
            'completed_tasks': self.stats['total_tasks_completed'],
            'failed_tasks': self.stats['total_tasks_failed'],
            'cancelled_tasks': self.stats['total_tasks_cancelled'],
            'timeout_tasks': self.stats['total_tasks_timeout'],
            'success_rate': success_rate,
            'average_processing_time': avg_processing_time,
            'peak_concurrent_tasks': self.stats['peak_concurrent_tasks'],
            'task_type_distribution': dict(
                self.stats['task_type_stats']),
            'queue_utilization': self.pending_tasks.qsize() / self.max_queue_size,
            'concurrency_utilization': len(
                self.running_tasks) / self.max_concurrent_tasks}

    def add_event_callback(self, event_type: str, callback: Callable):
        """添加事件回调"""
        if event_type in self.event_callbacks:
            self.event_callbacks[event_type].append(callback)

    async def _scheduler_loop(self):
        """调度器主循环"""
        logger.info("任务调度器已启动")

        while self.is_running:
            try:
                # 检查并发数限制
                if len(self.running_tasks) >= self.max_concurrent_tasks:
                    await asyncio.sleep(0.1)
                    continue

                # 从优先级队列获取任务
                try:
                    priority, task_id, task = await asyncio.wait_for(
                        self.pending_tasks.get(), timeout=1.0
                    )

                    # 检查任务是否仍然有效
                    if task_id not in self.tasks or task.status != AsyncTaskStatus.PENDING:
                        continue

                    # 启动任务
                    await self._start_task(task)

                except asyncio.TimeoutError:
                    # 没有待处理任务，继续循环
                    continue

            except Exception:
                logger.error("调度器异常", error=str(e))
                await asyncio.sleep(1.0)

        logger.info("任务调度器已停止")

    async def _start_task(self, task: AsyncTask):
        """启动任务"""
        task.status = AsyncTaskStatus.RUNNING
        task.started_at = datetime.now()

        # 计算队列等待时间
        queue_time = (task.started_at - task.created_at).total_seconds()
        self._update_average_queue_time(queue_time)

        # 创建asyncio任务
        async_task = asyncio.create_task(self._execute_task_with_timeout(task))
        self.running_tasks[task.task_id] = async_task

        # 更新峰值并发数
        current_concurrent = len(self.running_tasks)
        if current_concurrent > self.stats['peak_concurrent_tasks']:
            self.stats['peak_concurrent_tasks'] = current_concurrent

            # 检查高并发告警
            if current_concurrent > self.max_concurrent_tasks * 0.8:
                await self._trigger_event('high_concurrency', {
                    'current_concurrent': current_concurrent,
                    'max_concurrent': self.max_concurrent_tasks
                })

        # 触发任务开始事件
        await self._trigger_event('task_started', task)

        logger.debug(
            "任务已启动",
            task_id=task.task_id,
            name=task.name,
            queue_time=queue_time,
            concurrent_tasks=current_concurrent
        )

    async def _execute_task_with_timeout(self, task: AsyncTask):
        """执行带超时的任务"""
        try:
            # 使用信号量控制并发
            async with self.semaphore:
                # 执行任务
                if task.timeout:
                    task.result = await asyncio.wait_for(task.coro, timeout=task.timeout)
                else:
                    task.result = await task.coro

                # 任务成功完成
                task.status = AsyncTaskStatus.COMPLETED
                task.completed_at = datetime.now()

                # 更新统计
                processing_time = (
                    task.completed_at -
                    task.started_at).total_seconds()
                self.stats['total_tasks_completed'] += 1
                self.stats['total_processing_time'] += processing_time

                # 执行回调
                if task.callback:
                    try:
                        await task.callback(task)
                    except Exception:
                        logger.error(
                            "任务回调异常", task_id=task.task_id, error=str(e))

                # 触发完成事件
                await self._trigger_event('task_completed', task)

                # 检查依赖任务
                await self._check_dependent_tasks(task.task_id)

                logger.debug(
                    "任务执行成功",
                    task_id=task.task_id,
                    processing_time=processing_time
                )

        except asyncio.CancelledError:
            task.status = AsyncTaskStatus.CANCELLED
            task.completed_at = datetime.now()
            self.stats['total_tasks_cancelled'] += 1

            logger.debug("任务被取消", task_id=task.task_id)

        except asyncio.TimeoutError:
            task.status = AsyncTaskStatus.TIMEOUT
            task.completed_at = datetime.now()
            task.error = asyncio.TimeoutError(f"任务超时: {task.timeout}s")
            self.stats['total_tasks_timeout'] += 1

            # 检查重试
            if task.retry_count < task.max_retries:
                await self._retry_task(task)
            else:
                await self._trigger_event('task_failed', task)

            logger.warning(
                "任务执行超时",
                task_id=task.task_id,
                timeout=task.timeout,
                retry_count=task.retry_count
            )

        except Exception:
            task.status = AsyncTaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error = e
            self.stats['total_tasks_failed'] += 1

            # 检查重试
            if task.retry_count < task.max_retries:
                await self._retry_task(task)
            else:
                await self._trigger_event('task_failed', task)

            logger.error(
                "任务执行失败",
                task_id=task.task_id,
                error=str(e),
                retry_count=task.retry_count
            )

        finally:
            # 清理运行中任务记录
            self.running_tasks.pop(task.task_id, None)

    async def _retry_task(self, task: AsyncTask):
        """重试任务"""
        task.retry_count += 1
        task.status = AsyncTaskStatus.PENDING
        task.started_at = None

        # 等待重试延迟
        await asyncio.sleep(task.retry_delay * task.retry_count)

        # 重新排队
        await self._queue_task(task)

        logger.info(
            "任务重试",
            task_id=task.task_id,
            retry_count=task.retry_count,
            max_retries=task.max_retries
        )

    async def _queue_task(self, task: AsyncTask):
        """将任务放入队列"""
        try:
            # 优先级越高，数值越小（PriorityQueue是最小堆）
            priority = 10 - task.priority
            await self.pending_tasks.put((priority, task.task_id, task))

        except asyncio.QueueFull:
            logger.error("任务队列已满", task_id=task.task_id)
            await self._trigger_event('queue_full', task)
            raise

    def _add_dependencies(self, task_id: str, dependencies: List[str]):
        """添加任务依赖"""
        for dep_id in dependencies:
            self.dependency_graph[dep_id].append(task_id)
            self.reverse_dependencies[task_id].append(dep_id)

    def _can_schedule_task(self, task_id: str) -> bool:
        """检查任务是否可以调度"""
        # 检查所有依赖任务是否已完成
        dependencies = self.reverse_dependencies.get(task_id, [])
        for dep_id in dependencies:
            dep_task = self.tasks.get(dep_id)
            if not dep_task or dep_task.status != AsyncTaskStatus.COMPLETED:
                return False
        return True

    async def _check_dependent_tasks(self, completed_task_id: str):
        """检查依赖于已完成任务的其他任务"""
        dependent_tasks = self.dependency_graph.get(completed_task_id, [])

        for task_id in dependent_tasks:
            if self._can_schedule_task(task_id):
                task = self.tasks.get(task_id)
                if task and task.status == AsyncTaskStatus.PENDING:
                    await self._queue_task(task)

    def _calculate_task_progress(self, task: AsyncTask) -> float:
        """计算任务进度"""
        if task.status == AsyncTaskStatus.COMPLETED:
            return 1.0
        elif task.status == AsyncTaskStatus.RUNNING:
            # 基于运行时间估算进度（简化实现）
            if task.started_at and task.timeout:
                elapsed = (datetime.now() - task.started_at).total_seconds()
                return min(0.9, elapsed / task.timeout)
            return 0.5
        else:
            return 0.0

    def _update_average_queue_time(self, queue_time: float):
        """更新平均队列等待时间"""
        current_avg = self.stats['average_queue_time']
        completed_tasks = self.stats['total_tasks_completed']

        if completed_tasks == 0:
            self.stats['average_queue_time'] = queue_time
        else:
            # 简单的移动平均
            self.stats['average_queue_time'] = (
                current_avg * 0.9) + (queue_time * 0.1)

    async def _trigger_event(self, event_type: str, data: Any):
        """触发事件回调"""
        callbacks = self.event_callbacks.get(event_type, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(data)
                else:
                    callback(data)
            except Exception:
                logger.error(
                    "事件回调异常",
                    event_type=event_type,
                    error=str(e)
                )

    async def _cleanup_loop(self):
        """清理循环"""
        logger.info("任务清理器已启动")

        while self.is_running:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_completed_tasks()

            except Exception:
                logger.error("清理任务异常", error=str(e))

        logger.info("任务清理器已停止")

    async def _cleanup_completed_tasks(self):
        """清理已完成的任务"""
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(hours=24)  # 保留24小时

        tasks_to_remove = []
        for task_id, task in self.tasks.items():
            if (
                task.status in [
                    AsyncTaskStatus.COMPLETED,
                    AsyncTaskStatus.FAILED,
                    AsyncTaskStatus.CANCELLED] and task.completed_at and task.completed_at < cutoff_time):
                tasks_to_remove.append(task_id)

        for task_id in tasks_to_remove:
            del self.tasks[task_id]
            # 清理依赖关系
            self.dependency_graph.pop(task_id, None)
            self.reverse_dependencies.pop(task_id, None)

        if tasks_to_remove:
            logger.info("清理已完成任务", cleaned_count=len(tasks_to_remove))


def create_async_task_manager(
    max_concurrent_tasks: int = 100,
    max_queue_size: int = 10000,
    task_timeout: float = 300.0
) -> AsyncTaskManager:
    """创建异步任务管理器实例"""
    return AsyncTaskManager(
        max_concurrent_tasks=max_concurrent_tasks,
        max_queue_size=max_queue_size,
        task_timeout=task_timeout
    )
