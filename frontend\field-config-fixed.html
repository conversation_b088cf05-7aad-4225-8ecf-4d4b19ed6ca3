<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YS-API V3.0 生产环境 - 字段配置管理</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #4CAF50;
        }

        .section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 15px;
            font-weight: bold;
            border-bottom: 1px solid #e0e0e0;
        }

        .section-content {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        select,
        input,
        button {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        select {
            width: 100%;
            max-width: 300px;
        }

        button {
            background: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 10px;
        }

        button:hover {
            background: #45a049;
        }

        button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }

        .field-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }

        .field-item {
            padding: 10px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .field-item:last-child {
            border-bottom: none;
        }

        .field-info {
            flex-grow: 1;
        }

        .field-name {
            font-weight: bold;
            color: #333;
        }

        .field-details {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
        }

        .status.loading {
            background: #fff3cd;
            color: #856404;
        }

        .message {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }

        .message.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .message.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🏭 YS-API V3.0 生产环境</h1>
            <h2>字段配置管理系统</h2>
            <p>生产环境 - 16个业务模块 - 真实数据连接</p>
            <p>API连接状态: <span id="apiStatus" class="status loading">检查中...</span></p>
        </div>

        <div class="section">
            <div class="section-header">1. 模块选择</div>
            <div class="section-content">
                <div class="form-group">
                    <label for="moduleSelect">选择模块:</label>
                    <select id="moduleSelect" onchange="loadModuleFields()">
                        <option value="">请选择模块...</option>
                    </select>
                </div>
                <button onclick="loadModules()">刷新模块列表</button>
                <div id="moduleMessage"></div>
            </div>
        </div>

        <div class="section">
            <div class="section-header">2. 字段列表</div>
            <div class="section-content">
                <div class="form-group">
                    <label>当前模块字段 (<span id="fieldCount">0</span> 个):</label>
                    <div id="fieldList" class="field-list">
                        <div style="text-align: center; padding: 20px; color: #666;">
                            请先选择模块
                        </div>
                    </div>
                </div>
                <button onclick="refreshFields()" id="refreshBtn" disabled>刷新字段</button>
                <button onclick="exportFields()" id="exportBtn" disabled>导出配置</button>
                <div id="fieldMessage"></div>
            </div>
        </div>

        <div class="section">
            <div class="section-header">3. 连接诊断</div>
            <div class="section-content">
                <button onclick="testConnection()">测试API连接</button>
                <button onclick="checkBackend()">检查后端状态</button>
                <div id="diagnosticResults"></div>
            </div>
        </div>
    </div>

    <script>
        // 生产环境API配置
        const API_BASE_URL = 'http://localhost:8050';  // 后端主服务端口
        let currentModule = null;
        let modules = [];
        let fields = [];

        // 页面加载时初始化
        window.onload = function () {
            showMessage('moduleMessage', '初始化页面...', 'info');
            checkApiConnection();
            loadModules();
        };

        // 显示消息
        function showMessage(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="message ${type}">${message}</div>`;
        }

        // 更新状态
        function updateStatus(elementId, text, type = 'loading') {
            const element = document.getElementById(elementId);
            element.textContent = text;
            element.className = `status ${type}`;
        }

        // 检查API连接
        async function checkApiConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                updateStatus('apiStatus', '连接正常', 'success');
                showMessage('moduleMessage', `✅ 后端服务正常: ${data.service}`, 'success');
            } catch (error) {
                updateStatus('apiStatus', '连接失败', 'error');
                showMessage('moduleMessage', `❌ 无法连接到后端服务: ${error.message}`, 'error');
            }
        }

        // 加载模块列表
        async function loadModules() {
            try {
                showMessage('moduleMessage', '正在加载模块列表...', 'info');

                const response = await fetch(`${API_BASE_URL}/api/v1/field-config/modules`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                modules = data.modules || [];

                // 更新下拉列表
                const select = document.getElementById('moduleSelect');
                select.innerHTML = '<option value="">请选择模块...</option>';

                modules.forEach(module => {
                    const option = document.createElement('option');
                    option.value = module.name || module.display_name;
                    option.textContent = module.display_name || module.name;
                    select.appendChild(option);
                });

                showMessage('moduleMessage', `✅ 成功加载 ${modules.length} 个模块`, 'success');

            } catch (error) {
                console.error('加载模块失败:', error);
                showMessage('moduleMessage', `❌ 加载模块失败: ${error.message}`, 'error');
            }
        }

        // 加载模块字段
        async function loadModuleFields() {
            const select = document.getElementById('moduleSelect');
            const selectedModule = select.value;

            if (!selectedModule) {
                currentModule = null;
                updateFieldList([]);
                document.getElementById('refreshBtn').disabled = true;
                document.getElementById('exportBtn').disabled = true;
                return;
            }

            currentModule = selectedModule;
            document.getElementById('refreshBtn').disabled = false;
            document.getElementById('exportBtn').disabled = false;

            await refreshFields();
        }

        // 刷新字段
        async function refreshFields() {
            if (!currentModule) return;

            try {
                showMessage('fieldMessage', '正在加载字段列表...', 'info');

                const response = await fetch(`${API_BASE_URL}/api/v1/field-config/modules/${encodeURIComponent(currentModule)}/fields`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.error) {
                    throw new Error(data.message || '获取字段失败');
                }

                fields = data.fields || [];
                updateFieldList(fields);

                showMessage('fieldMessage', `✅ 成功加载 ${fields.length} 个字段`, 'success');

            } catch (error) {
                console.error('加载字段失败:', error);
                showMessage('fieldMessage', `❌ 加载字段失败: ${error.message}`, 'error');
                updateFieldList([]);
            }
        }

        // 更新字段列表显示
        function updateFieldList(fieldList) {
            const container = document.getElementById('fieldList');
            const countElement = document.getElementById('fieldCount');

            countElement.textContent = fieldList.length;

            if (fieldList.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">暂无字段数据</div>';
                return;
            }

            const html = fieldList.map((field, index) => `
                <div class="field-item">
                    <div class="field-info">
                        <div class="field-name">${field.display_name || field.name}</div>
                        <div class="field-details">
                            类型: ${field.type || 'unknown'} | 
                            必填: ${field.required ? '是' : '否'} | 
                            名称: ${field.name}
                        </div>
                    </div>
                    <div class="status success">#${index + 1}</div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 导出字段配置
        function exportFields() {
            if (!fields || fields.length === 0) {
                showMessage('fieldMessage', '❌ 没有可导出的字段数据', 'error');
                return;
            }

            const exportData = {
                module: currentModule,
                timestamp: new Date().toISOString(),
                fields: fields
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${currentModule}_fields_${new Date().getTime()}.json`;
            a.click();
            URL.revokeObjectURL(url);

            showMessage('fieldMessage', '✅ 字段配置已导出', 'success');
        }

        // 生产环境连接测试
        async function testConnection() {
            const results = document.getElementById('diagnosticResults');
            results.innerHTML = '<div class="message info">正在连接生产环境...</div>';

            let html = '';

            // 测试生产环境后端健康检查
            try {
                const healthResponse = await fetch(`${API_BASE_URL}/health`);
                const healthData = await healthResponse.json();
                html += `<div class="message success">✅ 生产环境后端连接成功: ${JSON.stringify(healthData)}</div>`;
            } catch (error) {
                html += `<div class="message error">❌ 生产环境后端连接失败: ${error.message}</div>`;
            }

            // 测试生产环境模块API - 16个模块
            try {
                const moduleResponse = await fetch(`${API_BASE_URL}/api/v1/field-config/modules`);
                const moduleData = await moduleResponse.json();
                const moduleCount = moduleData.modules?.length || 0;
                if (moduleCount >= 16) {
                    html += `<div class="message success">✅ 生产环境模块API: 获取到 ${moduleCount} 个模块（包含业务日志）</div>`;
                } else {
                    html += `<div class="message error">❌ 模块数量不足: 期望16个，实际${moduleCount}个</div>`;
                }
            } catch (error) {
                html += `<div class="message error">❌ 生产环境模块API失败: ${error.message}</div>`;
            }

            // 测试业务日志模块
            try {
                const businessLogResponse = await fetch(`${API_BASE_URL}/api/business-logs`);
                const businessLogData = await businessLogResponse.json();
                if (businessLogData.success) {
                    html += `<div class="message success">✅ 业务日志模块: 连接成功，真实数据可用</div>`;
                } else {
                    html += `<div class="message error">❌ 业务日志模块: ${businessLogData.error}</div>`;
                }
            } catch (error) {
                html += `<div class="message error">❌ 业务日志模块连接失败: ${error.message}</div>`;
            }

            results.innerHTML = html;
        }

        // 检查生产环境后端状态
        async function checkBackend() {
            const results = document.getElementById('diagnosticResults');
            results.innerHTML = '<div class="message info">正在检查生产环境状态...</div>';

            try {
                // 检查生产环境端点
                const healthResponse = await fetch(`${API_BASE_URL}/health`);
                const healthData = await healthResponse.json();

                let html = `<div class="message success">✅ 生产环境后端 (端口8050): 运行正常</div>`;
                html += `<div class="message info">服务状态: ${JSON.stringify(healthData)}</div>`;

                results.innerHTML = html;
            } catch (error) {
                results.innerHTML = `<div class="message error">❌ 生产环境后端连接失败: ${error.message}</div>`;
            }
        }
    </script>
</body>

</html>