#!/usr/bin/env python3
"""
SonarQube问题状态检查工具
"""


from pathlib import Path


def check_sonarqube_status():
    """检查SonarQube修复状态"""
    print("🔍 SonarQube问题修复状态检查")
    print("=" * 50)

    project_root = Path(__file__).parent

    # 1. 检查语法错误修复
    print("\n1. 语法错误检查:")

    # 检查error-handler.js
    error_handler_path = project_root / "frontend/js/common/error-handler.js"
    if error_handler_path.exists():
        with open(error_handler_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 检查是否还有 === 赋值错误
        assignment_errors = content.count(" === {") + content.count(" === new")
        if assignment_errors == 0:
            print("  ✅ error-handler.js: 语法错误已修复")
        else:
            print(f"  ❌ error-handler.js: 仍有 {assignment_errors} 个赋值错误")
    else:
        print("  ⚠️ error-handler.js 文件不存在")

    # 检查Python文件
    clean_server_path = project_root / "backend/start_server_clean.py"
    if clean_server_path.exists():
        print("  ✅ 清理版后端启动文件存在")
    else:
        print("  ❌ 清理版后端启动文件不存在")

    # 2. 检查冗余文件清理
    print("\n2. 冗余文件清理:")

    cleanup_dir = project_root / "temp_cleanup"
    if cleanup_dir.exists():
        cleanup_files = list(cleanup_dir.glob("*.py"))
        print(f"  ✅ 已移动 {len(cleanup_files)} 个冗余文件到temp_cleanup/")
    else:
        print("  ⚠️ temp_cleanup目录不存在")

    # 3. 检查配置文件
    print("\n3. 配置文件检查:")

    sonar_config = project_root / "sonar-project.properties"
    if sonar_config.exists():
        print("  ✅ SonarQube配置文件存在")
    else:
        print("  ❌ SonarQube配置文件不存在")

    # 4. 统计剩余文件
    print("\n4. 项目文件统计:")

    python_files = list(project_root.glob("*.py"))
    js_files = list(project_root.glob("frontend/**/*.js"))

    print(f"  📄 根目录Python文件: {len(python_files)}")
    print(f"  📄 前端JavaScript文件: {len(js_files)}")

    # 5. 建议
    print("\n5. SonarQube分析建议:")
    print("  💡 在VS Code中:")
    print("     1. 打开PROBLEMS面板 (Ctrl+Shift+M)")
    print("     2. 查看SonarQube检测到的问题")
    print("     3. 对比修复前后的错误数量")
    print("  💡 如果错误大幅减少，说明修复成功")

    print("\n✅ 检查完成!")


if __name__ == "__main__":
    check_sonarqube_status()
