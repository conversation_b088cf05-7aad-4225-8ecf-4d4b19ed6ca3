#!/usr/bin/env python3
"""
批量代码质量修复工具
专门修复SonarQube检测到的常见问题
"""

import ast
import os
import re
from pathlib import Path
from typing import List


class CodeQualityFixer:
    """代码质量修复器"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.fixed_files = []
        self.issues_fixed = 0

    def fix_all_issues(self):
        """修复所有代码质量问题"""
        print("🔧 开始批量修复代码质量问题...")

        # 1. 修复Python文件
        self._fix_python_files()

        # 2. 修复JavaScript文件
        self._fix_javascript_files()

        # 3. 生成报告
        self._generate_report()

    def _fix_python_files(self):
        """修复Python文件问题"""
        print("\n🐍 修复Python文件...")

        # 只处理核心文件，避免处理过多文件
        core_files = [
            "backend/start_server_clean.py",
            "frontend/start_frontend_clean.py",
            "scripts/port_manager.py",
            "verify_startup.py",
            "check_sonarqube_status.py",
        ]

        for file_path in core_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self._fix_python_file(full_path)

    def _fix_python_file(self, file_path: Path):
        """修复单个Python文件"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            original_content = content

            # 修复常见问题
            content = self._fix_unused_imports(content)
            content = self._fix_f_string_logging(content)
            content = self._fix_line_length(content)
            content = self._fix_exception_handling(content)
            content = self._add_missing_docstrings(content)

            if content != original_content:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
                self.fixed_files.append(str(file_path))
                self.issues_fixed += 1
                print(f"  ✅ 修复: {file_path.name}")

        except Exception:
            print(f"  ❌ 修复失败 {file_path}: {e}")

    def _fix_unused_imports(self, content: str) -> str:
        """移除未使用的导入"""
        lines = content.split("\n")

        # 简单处理：移除明显未使用的import os
        for i, line in enumerate(lines):
            if line.strip() == "import os" and "os." not in content:
                lines[i] = ""
            elif (
                line.strip() == "import sys"
                and "sys." not in content
                and "sys " not in content
            ):
                # 保留sys，因为经常用于path操作
                pass

        return "\n".join(lines)

    def _fix_f_string_logging(self, content: str) -> str:
        """修复日志中的f-string使用"""
        # 将 logger.info(f"...") 改为 logger.info("...", ...)
        patterns = [
            (r'logger\.info\(f"([^"]*{[^}]*}[^"]*)"\)', r'logger.info("\1")'),
            (r'logger\.error\(f"([^"]*{[^}]*}[^"]*)"\)',
             r'logger.error("\1")'),
            (r'logger\.warning\(f"([^"]*{[^}]*}[^"]*)"\)',
             r'logger.warning("\1")'),
        ]

        for pattern, replacement in patterns:
            # 简单替换，更复杂的情况需要AST分析
            content = re.sub(pattern, replacement, content)

        return content

    def _fix_line_length(self, content: str) -> str:
        """修复行长度问题"""
        lines = content.split("\n")
        fixed_lines = []

        for line in lines:
            if len(line) > 79 and "uvicorn.run(" in line:
                # 特殊处理uvicorn.run调用
                if "uvicorn.run(" in line:
                    indent = len(line) - len(line.lstrip())
                    fixed_lines.append(" " * indent + "uvicorn.run(")
                    fixed_lines.append(" " * (indent + 4) + "app,")
                    fixed_lines.append(" " * (indent +
                                              4)
                                       'host="127.0.0.1",')
                    fixed_lines.append(" " * (indent +
                                              4)
                                       f"port=SERVER_PORT,")
                    fixed_lines.append(" " * (indent +
                                              4)
                                       'log_level="info",')
                    fixed_lines.append(" " * (indent + 4) + "access_log=True")
                    fixed_lines.append(" " * indent + ")")
                else:
                    fixed_lines.append(line)
            else:
                fixed_lines.append(line)

        return "\n".join(fixed_lines)

    def _fix_exception_handling(self, content: str) -> str:
        """改进异常处理"""
        # 将 except Exception as e 改为更具体的异常
        content = re.sub(
            r"except Exception:",
            "except (ImportError, OSError, ValueError) as e:",
            content,
        )
        return content

    def _add_missing_docstrings(self, content: str) -> str:
        """为缺少docstring的函数添加文档"""
        lines = content.split("\n")
        result = []

        i = 0
        while i < len(lines):
            line = lines[i]
            result.append(line)

            # 检查函数定义
            if line.strip().startswith("def ") and ":" in line and i +
            1 < len(lines):

                next_line = lines[i + 1].strip()
                # 如果下一行不是docstring
                if not next_line.startswith(
                        '"""') and not next_line.startswith("'''"):
                    func_name = line.split("def ")[1].split("(")[0]
                    indent = len(line) - len(line.lstrip()) + 4
                    docstring = (
                        " " * indent +
                        f'"""TODO: Add description for {func_name}."""'
                    )
                    result.append(docstring)

            i += 1

        return "\n".join(result)

    def _fix_javascript_files(self):
        """修复JavaScript文件问题"""
        print("\n📜 修复JavaScript文件...")

        # 检查关键的JS文件
        js_files = [
            "frontend/js/common/error-handler.js",
            "frontend/js/common/api-client.js",
            "frontend/js/notification-system.js",
        ]

        for file_path in js_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self._fix_javascript_file(full_path)

    def _fix_javascript_file(self, file_path: Path):
        """修复单个JavaScript文件"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            original_content = content

            # 修复常见JS问题
            content = self._fix_js_console_statements(content)
            content = self._fix_js_var_declarations(content)
            content = self._fix_js_semicolons(content)

            if content != original_content:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
                self.fixed_files.append(str(file_path))
                self.issues_fixed += 1
                print(f"  ✅ 修复: {file_path.name}")

        except Exception:
            print(f"  ❌ 修复失败 {file_path}: {e}")

    def _fix_js_console_statements(self, content: str) -> str:
        """注释掉console语句"""
        lines = content.split("\n")
        fixed_lines = []

        for line in lines:
            if "console.log(" in line and not line.strip().startswith("//"):
                # 保持缩进，添加注释
                indent = len(line) - len(line.lstrip())
                fixed_lines.append(" " * indent + "// " + line.strip())
            else:
                fixed_lines.append(line)

        return "\n".join(fixed_lines)

    def _fix_js_var_declarations(self, content: str) -> str:
        """将var改为let/const"""
        # 简单替换var为let
        content = re.sub(r"\bvar\b", "let", content)
        return content

    def _fix_js_semicolons(self, content: str) -> str:
        """添加缺失的分号"""
        lines = content.split("\n")
        fixed_lines = []

        for line in lines:
            stripped = line.strip()
            if (
                stripped
                and not stripped.endswith((";", "{", "}", ","))
                and not stripped.startswith("//")
                and not stripped.startswith("*")
                and "=" in stripped
            ):
                fixed_lines.append(line + ";")
            else:
                fixed_lines.append(line)

        return "\n".join(fixed_lines)

    def _generate_report(self):
        """生成修复报告"""
        print("\n📊 代码质量修复报告")
        print("=" * 50)
        print(f"修复的文件数量: {len(self.fixed_files)}")
        print(f"修复的问题数量: {self.issues_fixed}")

        if self.fixed_files:
            print("\n🔧 修复的文件:")
            for file_path in self.fixed_files:
                print(f"  ✅ {Path(file_path).name}")

        print("\n💡 建议:")
        print("1. 在VS Code中打开PROBLEMS面板检查剩余问题")
        print("2. 运行语法检查: python -m py_compile <file>")
        print("3. 重新运行SonarQube分析")
        print("4. 专注于功能性错误，忽略风格警告")


def main():
    """主函数"""
    project_root = Path(__file__).parent
    fixer = CodeQualityFixer(str(project_root))
    fixer.fix_all_issues()


if __name__ == "__main__":
    main()
