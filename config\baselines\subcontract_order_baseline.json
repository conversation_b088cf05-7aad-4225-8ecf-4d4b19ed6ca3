{"module_name": "subcontract_order", "generated_at": "2025-07-27T21:17:05.278023", "field_count": 151, "fields": {"orderSubcontractProduct_isPresent": {"api_field_name": "orderSubcontractProduct_isPresent", "sample_value": "False"}, "requireConsult": {"api_field_name": "requireConsult", "sample_value": "False"}, "orderProduct_id": {"api_field_name": "orderProduct_id", "sample_value": "2321283366156500999"}, "productDefineDts": {"api_field_name": "productDefineDts", "sample_value": "dict"}, "productDefineDts.WW": {"api_field_name": "productDefineDts.WW", "sample_value": "2025-08-20 00:00:00"}, "productDefineDts.XS11": {"api_field_name": "productDefineDts.XS11", "sample_value": "176616-20"}, "productDefineDts.ytenant": {"api_field_name": "productDefineDts.ytenant", "sample_value": "a2wmiscz"}, "productDefineDts.XS15": {"api_field_name": "productDefineDts.XS15", "sample_value": "176616"}, "productDefineDts.id": {"api_field_name": "productDefineDts.id", "sample_value": "2321283366156501005"}, "productDefineDts.pubts": {"api_field_name": "productDefineDts.pubts", "sample_value": "2025-07-25 11:45:02"}, "subcontractProductExt_arrivePlanNoQty": {"api_field_name": "subcontractProductExt_arrivePlanNoQty", "sample_value": "0.0"}, "orderSubcontractProduct_natSum": {"api_field_name": "orderSubcontractProduct_natSum", "sample_value": "7.6"}, "arriveStatus": {"api_field_name": "arriveStatus", "sample_value": "0"}, "transTypeId": {"api_field_name": "transTypeId", "sample_value": "1967600514994432"}, "mainUnitPrecision": {"api_field_name": "mainUnitPrecision", "sample_value": "0"}, "orderProduct_retMaterialApplyFlag": {"api_field_name": "orderProduct_retMaterialApplyFlag", "sample_value": "0"}, "OrderProduct_changeRate": {"api_field_name": "OrderProduct_changeRate", "sample_value": "1.0"}, "orderProduct_rcvOrgIdName": {"api_field_name": "orderProduct_rcvOrgIdName", "sample_value": "江门市金羚风扇制造有限公司"}, "id": {"api_field_name": "id", "sample_value": "2321283366156500998"}, "OrderProduct_sourceid": {"api_field_name": "OrderProduct_sourceid", "sample_value": "2321136632892227588"}, "fcIdPriceDigit": {"api_field_name": "fcIdPriceDigit", "sample_value": "2"}, "OrderProduct_sourceautoid": {"api_field_name": "OrderProduct_sourceautoid", "sample_value": "2321136632892227686"}, "isWfControlled": {"api_field_name": "isWfControlled", "sample_value": "True"}, "verificationStatus": {"api_field_name": "verificationStatus", "sample_value": "0"}, "requireConfirm": {"api_field_name": "requireConfirm", "sample_value": "False"}, "orderProduct_rcvOrgId": {"api_field_name": "orderProduct_rcvOrgId", "sample_value": "2108054698226352134"}, "productId_isBatchManage": {"api_field_name": "productId_isBatchManage", "sample_value": "False"}, "orderSubcontractProduct_requestedPaymentFC": {"api_field_name": "orderSubcontractProduct_requestedPaymentFC", "sample_value": "0.0"}, "orderProduct_lineNo": {"api_field_name": "orderProduct_lineNo", "sample_value": "10.0"}, "orderSubcontractProductExt_autoClose": {"api_field_name": "orderSubcontractProductExt_autoClose", "sample_value": "0"}, "OrderProduct_subcontractQuantityPU": {"api_field_name": "OrderProduct_subcontractQuantityPU", "sample_value": "38.0"}, "orderSubcontract_tcId": {"api_field_name": "orderSubcontract_tcId", "sample_value": "2123823790927380489"}, "sourceType": {"api_field_name": "sourceType", "sample_value": "6"}, "warehouseId": {"api_field_name": "warehouseId", "sample_value": "2175662499976708101"}, "orderSubcontractProduct_rcvAddrType": {"api_field_name": "orderSubcontractProduct_rcvAddrType", "sample_value": "0"}, "taxFC": {"api_field_name": "taxFC", "sample_value": "5.44"}, "orderSubcontractProductExt_inClose": {"api_field_name": "orderSubcontractProductExt_inClose", "sample_value": "0"}, "tcIdMoneyDigit": {"api_field_name": "tcIdMoneyDigit", "sample_value": "2"}, "OrderProduct_materialCode": {"api_field_name": "OrderProduct_materialCode", "sample_value": "C0209000157"}, "productId_isExpiryDateManage": {"api_field_name": "productId_isExpiryDateManage", "sample_value": "False"}, "orderSubcontractProduct_subcontractUnitId": {"api_field_name": "orderSubcontractProduct_subcontractUnitId", "sample_value": "2113321195728273414"}, "productDefineDts__id": {"api_field_name": "productDefineDts__id", "sample_value": "2321283366156501005"}, "consultStatus": {"api_field_name": "consultStatus", "sample_value": "1"}, "status": {"api_field_name": "status", "sample_value": "0"}, "verifystate": {"api_field_name": "verifystate", "sample_value": "0"}, "orderSubcontractProduct_natMoney": {"api_field_name": "orderSubcontractProduct_natMoney", "sample_value": "6.73"}, "tradeThrowVersion": {"api_field_name": "tradeThrowVersion", "sample_value": "0"}, "materialVerifStatus": {"api_field_name": "materialVerifStatus", "sample_value": "3"}, "OrderProduct_firstsource": {"api_field_name": "OrderProduct_firstsource", "sample_value": "SCMSA.voucher_order"}, "orderSubcontractProduct_priceUnitId": {"api_field_name": "orderSubcontractProduct_priceUnitId", "sample_value": "2113321195728273414"}, "OrderProduct_deliveryDate": {"api_field_name": "OrderProduct_deliveryDate", "sample_value": "2025-08-20 00:00:00"}, "OrderProduct_subcontractUnitName": {"api_field_name": "OrderProduct_subcontractUnitName", "sample_value": "件"}, "orderProduct_arriveSubQuantity": {"api_field_name": "orderProduct_arriveSubQuantity", "sample_value": "0.0"}, "warehouseName": {"api_field_name": "warehouseName", "sample_value": "中间仓"}, "orgId": {"api_field_name": "orgId", "sample_value": "2108054698226352134"}, "vouchdate": {"api_field_name": "vouchdate", "sample_value": "2025-07-25 00:00:00"}, "OrderProduct_subcontractQuantityMU": {"api_field_name": "OrderProduct_subcontractQuantityMU", "sample_value": "38.0"}, "orderSubcontractProduct_oriMoney": {"api_field_name": "orderSubcontractProduct_oriMoney", "sample_value": "6.73"}, "transTypeName": {"api_field_name": "transTypeName", "sample_value": "标准委外"}, "orderSubcontract_tcOrgId_name": {"api_field_name": "orderSubcontract_tcOrgId_name", "sample_value": "江门市金羚风扇制造有限公司"}, "pubts": {"api_field_name": "pubts", "sample_value": "2025-07-25 16:42:31"}, "isFlowCoreBill": {"api_field_name": "isFlowCoreBill", "sample_value": "True"}, "totalMoneyFC": {"api_field_name": "totalMoneyFC", "sample_value": "47.24"}, "netMoneyFC": {"api_field_name": "netMoneyFC", "sample_value": "41.8"}, "OrderProduct_bomId": {"api_field_name": "OrderProduct_bomId", "sample_value": "2302489516970082344"}, "orderSubcontractProduct_discountTaxType": {"api_field_name": "orderSubcontractProduct_discountTaxType", "sample_value": "0"}, "creator": {"api_field_name": "creator", "sample_value": "关旭吕"}, "OrderProduct_mainUnitName": {"api_field_name": "OrderProduct_mainUnitName", "sample_value": "件"}, "orderSubcontract_costAccountingMethod": {"api_field_name": "orderSubcontract_costAccountingMethod", "sample_value": "1"}, "orderProductPubts": {"api_field_name": "orderProductPubts", "sample_value": "2025-07-25 16:42:31"}, "OrderProduct_isHold": {"api_field_name": "OrderProduct_isHold", "sample_value": "False"}, "priceUnitPrecision": {"api_field_name": "priceUnitPrecision", "sample_value": "0"}, "OrderProduct_priceUnitName": {"api_field_name": "OrderProduct_priceUnitName", "sample_value": "件"}, "productDefineDts__WW": {"api_field_name": "productDefineDts__WW", "sample_value": "2025-08-20 00:00:00"}, "subcontractVendorId": {"api_field_name": "subcontractVendorId", "sample_value": "2175802515921043605"}, "productDefineDts__XS15": {"api_field_name": "productDefineDts__XS15", "sample_value": "176616"}, "orderSubcontract_osmBusiType": {"api_field_name": "orderSubcontract_osmBusiType", "sample_value": "1"}, "createTime": {"api_field_name": "createTime", "sample_value": "2025-07-25 16:42:31"}, "orderSubcontractProduct_subcontractUnitIdCode": {"api_field_name": "orderSubcontractProduct_subcontractUnitIdCode", "sample_value": "JIAN"}, "OrderProduct_materialModelDescription": {"api_field_name": "OrderProduct_materialModelDescription", "sample_value": "ZX-Ф90×Ф15×63×2（48）"}, "orderSubcontractProduct_oriTaxUnitPrice": {"api_field_name": "orderSubcontractProduct_oriTaxUnitPrice", "sample_value": "0.2"}, "productDefineDts__XS11": {"api_field_name": "productDefineDts__XS11", "sample_value": "176616-20"}, "orderSubcontract_closeApply": {"api_field_name": "orderSubcontract_closeApply", "sample_value": "0"}, "materialApplyFlag": {"api_field_name": "materialApplyFlag", "sample_value": "False"}, "orderSubcontractProduct_natUnitPrice": {"api_field_name": "orderSubcontractProduct_natUnitPrice", "sample_value": "0.18"}, "isBeginning": {"api_field_name": "isBeginning", "sample_value": "0"}, "orderSubcontractProduct_continuousOsm": {"api_field_name": "orderSubcontractProduct_continuousOsm", "sample_value": "0"}, "orderSubcontract_tcIdName": {"api_field_name": "orderSubcontract_tcIdName", "sample_value": "人民币【4位采购专用】"}, "orderSubcontractProduct_tcOrgIdSon": {"api_field_name": "orderSubcontractProduct_tcOrgIdSon", "sample_value": "2108054698226352134"}, "subcontractProductExt_arrivePlanNoSubQty": {"api_field_name": "subcontractProductExt_arrivePlanNoSubQty", "sample_value": "0.0"}, "orderSubcontractProduct_subcontractToPrice": {"api_field_name": "orderSubcontractProduct_subcontractToPrice", "sample_value": "1.0"}, "OrderProduct_upcode": {"api_field_name": "OrderProduct_upcode", "sample_value": "WWSQ20250725004"}, "subcontractProductExt_arriveQuantity": {"api_field_name": "subcontractProductExt_arriveQuantity", "sample_value": "0.0"}, "totalMoneyTC": {"api_field_name": "totalMoneyTC", "sample_value": "47.24"}, "orderSubcontractProduct_taxRate": {"api_field_name": "orderSubcontractProduct_taxRate", "sample_value": "13.0"}, "orderSubcontractProductConsult_requireSign": {"api_field_name": "orderSubcontractProductConsult_requireSign", "sample_value": "False"}, "subcontractProductExt_arrivePlanSubQty": {"api_field_name": "subcontractProductExt_arrivePlanSubQty", "sample_value": "0.0"}, "OrderProduct_subcontractQuantitySU": {"api_field_name": "OrderProduct_subcontractQuantitySU", "sample_value": "38.0"}, "orderSubcontractProduct_oriSum": {"api_field_name": "orderSubcontractProduct_oriSum", "sample_value": "7.6"}, "orderSubcontractProduct_requestedPaymentTC": {"api_field_name": "orderSubcontractProduct_requestedPaymentTC", "sample_value": "0.0"}, "stockStatus": {"api_field_name": "stockStatus", "sample_value": "0"}, "materialApplyStatus": {"api_field_name": "materialApplyStatus", "sample_value": "3"}, "orderSubcontractProduct_priceChangeType": {"api_field_name": "orderSubcontractProduct_priceChangeType", "sample_value": "0"}, "OrderProduct_materialName": {"api_field_name": "OrderProduct_materialName", "sample_value": "转子铁芯Ф90×Ф15×63×2（48）"}, "orderSubcontractProduct_osTaxRateIdName": {"api_field_name": "orderSubcontractProduct_osTaxRateIdName", "sample_value": "13%增值税税率"}, "mainUnit": {"api_field_name": "mainUnit", "sample_value": "2113321195728273414"}, "OrderProduct_versionMemo": {"api_field_name": "OrderProduct_versionMemo", "sample_value": "-1"}, "orgName": {"api_field_name": "orgName", "sample_value": "江门市金羚风扇制造有限公司"}, "orderSubcontractProduct_oriUnitPrice": {"api_field_name": "orderSubcontractProduct_oriUnitPrice", "sample_value": "0.177"}, "orderProduct_arriveQuantity": {"api_field_name": "orderProduct_arriveQuantity", "sample_value": "0.0"}, "isHold": {"api_field_name": "isHold", "sample_value": "False"}, "netMoneyTC": {"api_field_name": "netMoneyTC", "sample_value": "41.8"}, "tcIdPriceDigit": {"api_field_name": "tcIdPriceDigit", "sample_value": "4"}, "orderSubcontractProduct_tcOrgIdSon_name": {"api_field_name": "orderSubcontractProduct_tcOrgIdSon_name", "sample_value": "江门市金羚风扇制造有限公司"}, "orderSubcontractProduct_natTax": {"api_field_name": "orderSubcontractProduct_natTax", "sample_value": "0.87"}, "orderSubcontractProductExt_arriveClose": {"api_field_name": "orderSubcontractProductExt_arriveClose", "sample_value": "0"}, "OrderProduct_sourceType": {"api_field_name": "OrderProduct_sourceType", "sample_value": "6"}, "OrderProduct_version": {"api_field_name": "OrderProduct_version", "sample_value": "-1"}, "orderSubcontractProduct_natTaxUnitPrice": {"api_field_name": "orderSubcontractProduct_natTaxUnitPrice", "sample_value": "0.2"}, "materialStatus": {"api_field_name": "materialStatus", "sample_value": "0"}, "code": {"api_field_name": "code", "sample_value": "WWDD20250725013"}, "OrderProduct_firstupcode": {"api_field_name": "OrderProduct_firstupcode", "sample_value": "JL01-SO250722018"}, "bizFlow": {"api_field_name": "bizFlow", "sample_value": "2249119918366130183"}, "mainUnitTruncationType": {"api_field_name": "mainUnitTruncationType", "sample_value": "0"}, "taxTC": {"api_field_name": "taxTC", "sample_value": "5.44"}, "OrderProduct_productId": {"api_field_name": "OrderProduct_productId", "sample_value": "2192925116026323032"}, "orderProduct_orderSubcontractProduct_taxRateId": {"api_field_name": "orderProduct_orderSubcontractProduct_taxRateId", "sample_value": "10004"}, "bizFlow_version": {"api_field_name": "bizFlow_version", "sample_value": "V1.0"}, "changeFlag": {"api_field_name": "changeFlag", "sample_value": "0"}, "paymentStatus": {"api_field_name": "paymentStatus", "sample_value": "0"}, "orderProduct_firstsourceid": {"api_field_name": "orderProduct_firstsourceid", "sample_value": "2318868056939429899"}, "orderSubcontract_paymentBy": {"api_field_name": "orderSubcontract_paymentBy", "sample_value": "0"}, "orderSubcontractProduct_lineAttach": {"api_field_name": "orderSubcontractProduct_lineAttach", "sample_value": "6561cd18-cfda-4a36-b67a-0d46474e8a52"}, "orderSubcontractProduct_tcOrgAccountSon": {"api_field_name": "orderSubcontractProduct_tcOrgAccountSon", "sample_value": "0"}, "subcontractUnitPrecision": {"api_field_name": "subcontractUnitPrecision", "sample_value": "0"}, "subcontractVendorName": {"api_field_name": "subcontractVendorName", "sample_value": "佛山市南海区东唐电机厂"}, "changeType": {"api_field_name": "changeType", "sample_value": "0"}, "fcIdMoneyDigit": {"api_field_name": "fcIdMoneyDigit", "sample_value": "2"}, "subcontractProduct_isContinuousOsmEnd": {"api_field_name": "subcontractProduct_isContinuousOsmEnd", "sample_value": "0"}, "orderSubcontract_tcOrgAccount": {"api_field_name": "orderSubcontract_tcOrgAccount", "sample_value": "0"}, "freeCharacteristics": {"api_field_name": "freeCharacteristics", "sample_value": "dict"}, "freeCharacteristics.ytenant": {"api_field_name": "freeCharacteristics.ytenant", "sample_value": "a2wmiscz"}, "freeCharacteristics.id": {"api_field_name": "freeCharacteristics.id", "sample_value": "2321283366156501004"}, "freeCharacteristics.pubts": {"api_field_name": "freeCharacteristics.pubts", "sample_value": "2025-07-25 16:42:31"}, "OrderProduct_lineClose": {"api_field_name": "OrderProduct_lineClose", "sample_value": "0"}, "orderSubcontract_tcOrgId": {"api_field_name": "orderSubcontract_tcOrgId", "sample_value": "2108054698226352134"}, "orderSubcontractProduct_oriTax": {"api_field_name": "orderSubcontractProduct_oriTax", "sample_value": "0.87"}, "invoiceStatus": {"api_field_name": "invoiceStatus", "sample_value": "0"}, "subcontractProductExt_hasArrivePlan": {"api_field_name": "subcontractProductExt_hasArrivePlan", "sample_value": "0"}, "orderSubcontract_fcId": {"api_field_name": "orderSubcontract_fcId", "sample_value": "2107447879489552424"}, "bizFlow_name": {"api_field_name": "bizFlow_name", "sample_value": "委外订单"}}}