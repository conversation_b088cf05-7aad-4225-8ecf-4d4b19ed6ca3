@echo off
title YS-API V3.0 - 前端服务器
echo ========================================
echo      YS-API V3.0 前端服务器
echo ========================================
echo.
echo 正在切换到frontend目录...
cd /d "d:\OneDrive\Desktop\YS-API程序\v3\frontend"

echo 当前工作目录: %cd%
echo.
echo 检查关键文件是否存在...
if exist "migrated\database-v2.html" (
    echo ✅ 迁移页面存在
) else (
    echo ❌ 迁移页面不存在
    pause
    exit
)

if exist "js\core\component-manager.js" (
    echo ✅ 核心组件存在
) else (
    echo ❌ 核心组件不存在
    pause
    exit
)

echo.
echo 启动前端静态服务器（端口8080）...
echo 前端地址: http://localhost:8080
echo 测试页面: http://localhost:8080/migrated/database-v2.html
echo.
echo 注意: 需要同时启动后端API服务器才能使用完整功能
echo 按Ctrl+C停止服务器
echo ========================================
python -m http.server 8080
