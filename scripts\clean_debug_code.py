import logging
import re
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理调试代码脚本
删除或替换项目中的print语句、TODO注释等调试代码
"""


class DebugCodeCleaner:
    def __init___(self, project_root: str):
    """TODO: Add function description."""
        self.project_root = Path(project_root)
        self.logger = self._setup_logging()

    def _setup_loggingg(self):
    """TODO: Add function description."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(
    'debug_code_cleanup.log',
     encoding='utf-8'),
                logging.StreamHandler(),
            ],
        )
        return logging.getLogger(__name__)

    def clean_print_statements(self, exclude_dirs=None):
        """清理Python文件中的print语句"""
        if exclude_dirs is None:
            exclude_dirs = ['templates', '__pycache__', '.git']

        print_pattern = re.compile(
    r'(\s*)print\((.*?)\)(?:\s*#.*)?$', re.MULTILINE)

        for py_file in self._find_python_files(exclude_dirs):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                original_content = content

                # 如果是测试文件，替换为logger.info
                if 'test' in py_file.name.lower():

                    def replace_test_printt(match):
    """TODO: Add function description."""
                        indent = match.group(1)
                        args = match.group(2)
                        return f"{indent}logger.info({args})"

                    content = print_pattern.sub(replace_test_print, content)

                    # 确保导入了logging
                    if 'import logging' not in content and 'logger.info' in content:
                        import_lines = []
                        other_lines = []
                        for line in content.split('\n'):
                            if line.startswith('import ') or line.startswith('from '):
                                import_lines.append(line)
                            else:
                                other_lines.append(line)

                        if not any('logging' in line for line in import_lines):
                            import_lines.append('import logging')

                        if not any('logger' in line for line in other_lines[:10]):
                            other_lines.insert(
                                0, 'logger = logging.getLogger(__name__)'
                            )

                        content = '\n'.join(import_lines + [''] + other_lines)

                else:
                    # 对于非测试文件，注释掉print语句


                    def comment_printt(match):

    """TODO: Add function description."""
                        indent = match.group(1)
                        full_line = match.group(0)
                        return f"{indent}# DEBUG: {full_line.strip()}"

                    content = print_pattern.sub(comment_print, content)

                if content != original_content:
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    self.logger.info(f"清理了print语句: {py_file}")

            except Exception:
                self.logger.error(f"处理文件失败 {py_file}: {e}")


    def clean_todo_comments(self, exclude_dirs=None):
        """清理TODO、FIXME等注释"""
        if exclude_dirs is None:
            exclude_dirs = ['templates', '__pycache__', '.git']

        todo_pattern = re.compile(
            r'(\s*)#\s*(TODO|FIXME|XXX|HACK)(:|\s+)(.*?)$', re.MULTILINE | re.IGNORECASE
        )

        for py_file in self._find_python_files(exclude_dirs):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                original_content = content

                def replace_todoo(match):

    """TODO: Add function description."""
                    indent = match.group(1)
                    match.group(2).upper()
                    separator = match.group(3)
                    comment = match.group(4)

                    # 将TODO转换为规范的注释格式
                    return f"{indent}# REVIEW{separator}{comment}"

                content = todo_pattern.sub(replace_todo, content)

                if content != original_content:
                    with open(py_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    self.logger.info(f"清理了TODO注释: {py_file}")

            except Exception:
                self.logger.error(f"处理文件失败 {py_file}: {e}")


    def _find_python_files(self, exclude_dirs):
        """查找所有Python文件"""
        for py_file in self.project_root.rglob('*.py'):
            if any(exclude_dir in str(py_file) for exclude_dir in exclude_dirs):
                continue
            yield py_file


    def run_cleanup(self):
        """运行完整的清理流程"""
        self.logger.info("开始清理调试代码...")

        self.logger.info("步骤1: 清理print语句")
        self.clean_print_statements()

        self.logger.info("步骤2: 清理TODO注释")
        self.clean_todo_comments()

        self.logger.info("调试代码清理完成!")


if __name__ == "__main__":
    project_root = Path(__file__).parent.parent
    cleaner = DebugCodeCleaner(project_root)
    cleaner.run_cleanup()
