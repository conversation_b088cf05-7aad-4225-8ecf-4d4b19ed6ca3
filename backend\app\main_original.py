import os
from contextlib import asynccontextmanager
from pathlib import Path

import structlog
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

from .api.v1 import (API, V3.0, YS, AccessLogMiddleware,  # 导入路由模块
                     RedirectResponse, Response, """, -, .api.v1,
                     .core.database, .middleware.access_log,
                     .services.auto_sync_scheduler, auth, config, database,
                     database_health, enhanced_sync, ensure_ysapi_database,
                     excel_translation, fastapi.responses, field_config_api,
                     from, get_scheduler, import, init_database, maintenance,
                     monitor, realtime_logs, sync, sync_status, tasks, time,
                     unified_field_config, uvicorn, 主服务,
                     统一FastAPI架构，整合原V2的双服务功能)

# 配置结构化日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.JSONRenderer(),
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("✅ lifespan 开始")

    try:
        logger.info("开始 init_database")
        await init_database()
        logger.info("✅ init_database 完成")

        logger.info("开始 ensure_ysapi_database")
        await ensure_ysapi_database()
        logger.info("✅ ensure_ysapi_database 完成")

        logger.info("开始获取 scheduler")
        scheduler = await get_scheduler()
        logger.info("开始启动 scheduler")
        await scheduler.start_scheduler()
        logger.info("✅ scheduler 启动完成")

    except Exception:
        logger.error("❌ lifespan 启动失败", error=str(e))
        raise

    logger.info("✅ lifespan 启动完成")

    yield

    logger.info("✅ lifespan 关闭开始")

    # 停止自动同步调度器
    try:

        scheduler = await get_scheduler()
        await scheduler.stop_scheduler()
        logger.info("自动同步调度器已停止")
    except Exception:
        logger.error("停止自动同步调度器失败", error=str(e))


# 创建FastAPI应用实例
app = FastAPI(
    title="YS-API V3.0",
    description="用友云数据同步和字段配置管理API",
    version="3.0.0",
    lifespan=lifespan,
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
)


# 添加请求日志中间件
@app.middleware("http")
async def request_logging_middleware(request: Request, call_next):
    """请求日志中间件"""

    start_time = time.time()

    # 记录请求开始
    logger.info(
        "请求开始",
        method=request.method,
        url=str(request.url),
        client_ip=request.client.host if request.client else "unknown",
        user_agent=request.headers.get("user-agent", "unknown"),
    )

    try:
        # 处理请求
        response = await call_next(request)

        # 计算处理时间
        process_time = time.time() - start_time

        # 记录请求完成
        logger.info(
            "请求完成",
            method=request.method,
            url=str(request.url),
            status_code=response.status_code,
            process_time_ms=round(process_time * 1000, 2),
        )

        return response

    except Exception:
        # 计算处理时间
        process_time = time.time() - start_time

        # 记录请求错误
        logger.error(
            "请求处理失败",
            method=request.method,
            url=str(request.url),
            error=str(e),
            process_time_ms=round(process_time * 1000, 2),
            exc_info=True,
        )
        raise


# 添加访问日志中间件
app.add_middleware(AccessLogMiddleware)

# 注册API路由
app.include_router(config.router, prefix="/api/v1/config", tags=["字段配置"])
app.include_router(field_config_api.router, tags=["字段配置API"])  # 新增字段配置API
app.include_router(
    unified_field_config.router, prefix="/api/v1/unified", tags=["统一字段配置"]
)
app.include_router(monitor.router, prefix="/api/v1/monitor", tags=["系统监控"])
app.include_router(database.router, prefix="/api/v1/database", tags=["数据库管理"])
app.include_router(database_health.router, prefix="/api/v1", tags=["数据库健康检查"])
app.include_router(sync.router, prefix="/api/v1/sync", tags=["数据同步"])
app.include_router(
    enhanced_sync.router, prefix="/api/v1/enhanced-sync", tags=["增强版同步"]
)
app.include_router(tasks.router, prefix="/api/v1/tasks", tags=["任务调度"])
app.include_router(realtime_logs.router, prefix="/api/v1/logs", tags=["实时日志"])
app.include_router(maintenance.router, prefix="/api/v1", tags=["维护管理"])
app.include_router(sync_status.router, prefix="/api/v1", tags=["同步状态"])
# 将excel_translation路由移到最后，避免路由冲突
app.include_router(
    excel_translation.router,
    prefix="/api/v1",
    tags=["Excel翻译"])
app.include_router(auth.router, prefix="/api/v1/auth")

# 静态文件服务（前端资源）
# 获取项目根目录
project_root = Path(__file__).parent.parent.parent

# 添加前端根目录静态文件服务
frontend_dir = project_root / "frontend"
if frontend_dir.exists():
    app.mount(
        "/frontend",
        StaticFiles(
            directory=str(frontend_dir)),
        name="frontend")

# 添加CSS文件静态服务
css_dir = frontend_dir / "css"
if css_dir.exists():
    app.mount("/css", StaticFiles(directory=str(css_dir)), name="css")

# 添加JS文件静态服务
js_dir = frontend_dir / "js"
if js_dir.exists():
    app.mount("/js", StaticFiles(directory=str(js_dir)), name="js")

# 14个业务模块配置 - 生产就绪锁定版本
MODULES = [
    {
        "name": "applyorder",
        "display": "请购单",
        "api_endpoint": "/yonbip/scm/applyorder/list",
    },
    {
        "name": "material_master_batch_detail",
        "display": "物料档案批量详情查询",
        "api_endpoint": "/yonbip/digitalModel/product/listproductbycondition",
    },
    {
        "name": "materialout",
        "display": "材料出库单",
        "api_endpoint": "/yonbip/scm/materialout/list",
    },
    {
        "name": "product_receipt",
        "display": "产品入库单",
        "api_endpoint": "/yonbip/scm/productreceipt/list",
    },
    {
        "name": "production_order",
        "display": "生产订单",
        "api_endpoint": "/yonbip/scm/productionorder/list",
    },
    {
        "name": "purchase_order",
        "display": "采购订单",
        "api_endpoint": "/yonbip/scm/purchaseorder/list",
    },
    {
        "name": "purchase_receipt",
        "display": "采购入库",
        "api_endpoint": "/yonbip/scm/purchasereceipt/list",
    },
    {
        "name": "requirements_planning",
        "display": "需求计划",
        "api_endpoint": "/yonbip/mfg/requirementsplanning/getPlanOrderList",
    },
    {
        "name": "sales_order",
        "display": "销售订单",
        "api_endpoint": "/yonbip/scm/salesorder/list",
    },
    {
        "name": "sales_out",
        "display": "销售出库",
        "api_endpoint": "/yonbip/scm/salesout/list",
    },
    {
        "name": "subcontract_order",
        "display": "委外订单",
        "api_endpoint": "/yonbip/scm/subcontractorder/list",
    },
    {
        "name": "subcontract_receipt",
        "display": "委外入库",
        "api_endpoint": "/yonbip/scm/subcontractreceipt/list",
    },
    {
        "name": "subcontract_requisition",
        "display": "委外申请",
        "api_endpoint": "/yonbip/scm/subcontractrequisition/list",
    },
    {
        "name": "inventory_report",
        "display": "现存量报表",
        "api_endpoint": "/yonbip/scm/inventoryreport/list",
    },
]


@app.get("/")
async def root():
    """根路径，重定向到YS - API字段配置中心"""

    return RedirectResponse(url="/unified-field-config.html", status_code=302)


@app.get("/field-config-center.html")
async def field_config_center():
    """YS - API字段配置中心页面"""
    test_html_path = project_root / "test_frontend.html"
    if test_html_path.exists():
        return FileResponse(test_html_path)
    else:
        raise HTTPException(status_code=404, detail="测试页面不存在")


@app.get("/field-config.html")
async def field_config_page():
    """字段配置页面"""
    page_path = os.path.join(
        os.path.dirname(__file__), "..", "..", "frontend", "field-config.html"
    )
    if os.path.exists(page_path):
        return FileResponse(page_path)
    else:
        raise HTTPException(status_code=404, detail="字段配置页面未找到")


@app.get("/field-config-improved.html")
async def field_config_improved_page():
    """改进版字段配置页面"""
    page_path = os.path.join(
        os.path.dirname(__file__),
        "..",
        "..",
        "frontend",
        "field-config-improved.html")
    if os.path.exists(page_path):
        return FileResponse(page_path)
    else:
        raise HTTPException(status_code=404, detail="改进版字段配置页面未找到")


@app.get("/field-config-manual.html")
async def field_config_manual_page():
    """字段配置手动加载页面"""
    page_path = os.path.join(
        os.path.dirname(__file__),
        "..",
        "..",
        "frontend",
        "field-config-manual.html")
    if os.path.exists(page_path):
        return FileResponse(page_path)
    else:
        raise HTTPException(status_code=404, detail="字段配置手动加载页面未找到")


@app.get("/index.html")
async def index_page():
    """主控台首页页面 - 重定向到Excel翻译页面"""

    return RedirectResponse(url="/excel-translation.html", status_code=302)


@app.get("/database.html")
async def database_page():
    """数据库管理页面"""
    database_path = os.path.join(
        os.path.dirname(__file__), "..", "..", "frontend", "database-v2.html"
    )
    if os.path.exists(database_path):
        return FileResponse(database_path)
    else:
        raise HTTPException(status_code=404, detail="数据库管理页面未找到")


@app.get("/database-v2.html")
async def database_v2_page():
    """数据库管理页面 V2"""
    database_path = os.path.join(
        os.path.dirname(__file__), "..", "..", "frontend", "database-v2.html"
    )
    if os.path.exists(database_path):
        return FileResponse(database_path)
    else:
        raise HTTPException(status_code=404, detail="数据库管理页面未找到")


@app.get("/unified-field-config.html")
async def unified_field_config_page():
    """统一字段配置页面"""
    page_path = os.path.join(
        os.path.dirname(__file__),
        "..",
        "..",
        "frontend",
        "unified-field-config.html")
    if os.path.exists(page_path):
        return FileResponse(page_path)
    else:
        raise HTTPException(status_code=404, detail="统一字段配置页面未找到")


@app.get("/simple-field-config.html")
async def simple_field_config_page():
    """简化字段配置页面"""
    page_path = os.path.join(
        os.path.dirname(__file__),
        "..",
        "..",
        "frontend",
        "simple-field-config.html")
    if os.path.exists(page_path):
        return FileResponse(page_path)
    else:
        raise HTTPException(status_code=404, detail="简化字段配置页面未找到")


@app.get("/excel-translation.html")
async def excel_translation_page():
    """Excel智能翻译页面"""
    excel_path = os.path.join(
        os.path.dirname(__file__),
        "..",
        "..",
        "frontend",
        "excel-translation.html")
    if os.path.exists(excel_path):
        return FileResponse(excel_path)
    else:
        raise HTTPException(status_code=404, detail="Excel翻译页面未找到")


@app.get("/快速访问.html")
async def quick_access_page():
    """快速访问页面"""
    quick_access_path = os.path.join(
        os.path.dirname(__file__), "..", "..", "快速访问.html"
    )
    if os.path.exists(quick_access_path):
        return FileResponse(quick_access_path)
    else:
        raise HTTPException(status_code=404, detail="快速访问页面未找到")


@app.get("/test-loading.html")
async def test_loading_page():
    """页面加载测试页面"""
    page_path = os.path.join(
        os.path.dirname(__file__), "..", "..", "frontend", "test-loading.html"
    )
    if os.path.exists(page_path):
        return FileResponse(page_path)
    else:
        raise HTTPException(status_code=404, detail="测试页面未找到")


@app.get("/test-connection.html")
async def test_connection_page():
    """连接测试页面"""
    page_path = os.path.join(
        os.path.dirname(__file__),
        "..",
        "..",
        "frontend",
        "test-connection.html")
    if os.path.exists(page_path):
        return FileResponse(page_path)
    else:
        raise HTTPException(status_code=404, detail="连接测试页面未找到")


@app.get("/frontend-diagnostic.html")
async def frontend_diagnostic_page():
    """前端诊断工具页面"""
    page_path = os.path.join(
        os.path.dirname(__file__),
        "..",
        "..",
        "frontend",
        "frontend-diagnostic.html")
    if os.path.exists(page_path):
        return FileResponse(page_path)
    else:
        raise HTTPException(status_code=404, detail="前端诊断工具页面未找到")


@app.get("/test-simple.html")
async def test_simple_page():
    """简单测试页面"""
    page_path = os.path.join(
        os.path.dirname(__file__), "..", "..", "frontend", "test-simple.html"
    )
    if os.path.exists(page_path):
        return FileResponse(page_path)
    else:
        raise HTTPException(status_code=404, detail="简单测试页面未找到")


@app.get("/maintenance.html")
async def maintenance_page():
    """维护管理页面"""
    page_path = os.path.join(
        os.path.dirname(__file__), "..", "..", "frontend", "maintenance.html"
    )
    if os.path.exists(page_path):
        return FileResponse(page_path)
    else:
        raise HTTPException(status_code=404, detail="维护管理页面未找到")


@app.get("/sync-test.html")
async def sync_test_page():
    """同步测试页面"""
    page_path = os.path.join(
        os.path.dirname(__file__), "..", "..", "frontend", "sync-test.html"
    )
    if os.path.exists(page_path):
        return FileResponse(page_path)
    else:
        raise HTTPException(status_code=404, detail="同步测试页面未找到")


@app.get("/.well-known/appspecific/com.chrome.devtools.json")
async def chrome_devtools_config():
    """Chrome DevTools配置文件 - 快速返回避免延迟"""
    return {"status": "not_configured"}


@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """快速处理404错误"""
    return JSONResponse(
        status_code=404, content={
            "detail": "Not found", "path": str(
                request.url.path)})


@app.get("/favicon.ico")
async def favicon():
    """
    处理浏览器的favicon请求，避免404错误
    """

    return Response(status_code=204)  # 204 No Content


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "YS-API V3.0", "version": "3.0.0"}


@app.get("/api/v1/modules")
async def get_modules():
    """获取15个业务模块列表"""
    return {
        "success": True,
        "data": {"modules": MODULES, "total_count": len(MODULES)},
        "message": "获取模块列表成功",
    }


if __name__ == "__main__":

    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
