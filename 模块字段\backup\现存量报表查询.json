[{"success": true, "code": 200, "message": "", "data": {"fieldVersion": 20230210, "appCode": "", "tokenSet": false, "tokenDoc": "", "tenantId": 0, "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "id": "f91945f5461546fab9be6f4aae4163f3", "name": "现存量报表查询", "apiClassifyId": "ba6b5fb8023442e4817b6cb8e665b54a", "apiClassifyName": "现存量", "apiClassifyCode": "", "parentApiClassifies": "", "functionId": "", "openMode": "", "description": "调用该接口需要先在API调用中绑定用户身份 ，才能正常调用该接口。", "auth": true, "bodyPassthrough": false, "healthExam": true, "healthStatus": "", "responseResultPassthrough": false, "contentType": "application/json", "returnPassthrough": "", "completeProxyUrl": "/yonbip/scm/stockanalysis/list", "connectUrl": "/report/list", "sort": 30, "handler": "openapi", "httpRequestType": "POST", "openApi": true, "preset": false, "productId": "710a0be3edff4f9092e35f63fd3b9bae", "productCode": "scm", "proxyUrl": "/yonbip/scm/stockanalysis/list", "requestParamsDemo": "Url: /yonbip/scm/stockanalysis/list?access_token=访问令牌 Body: { \"pageSize\": 10, \"pageIndex\": 1, \"warehouse_name\": [ 1620139372761344 ], \"product.productClass.name\": [ 1613185527927040 ], \"product.cName\": [ 1613334295384320 ], \"productsku.skuName\": [ 1613347223376128 ], \"batchno\": \"批次号\", \"acolytesUnit\": \"1\", \"org\": [ \"1620041647149568\" ], \"store\": [ 1620139372769888 ], \"open_currentqty_begin\": 1, \"open_currentqty_end\": 1000 }", "requestProtocol": "HTTP", "serviceHttpMethod": "POST", "publishStatus": true, "approvalMsg": "", "rpcAppName": "", "rpcServiceName": "", "rpcMethodName": "", "rpcServiceUrl": "", "ma": false, "gmtCreate": "2020-11-17 20:51:10.000", "gmtUpdate": "2025-02-12 13:39:59.000", "address": "https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/stockanalysis/list", "productName": "采购供应", "productClassifyId": "yonsuite", "productClassifyCode": "yonbip", "productClassifyName": "用友 YonBIP", "paramDTOS": {"paramDTOS": [{"id": 2200211570023203077, "name": "pageSize", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202825, "array": false, "paramDesc": "每页行数", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": 10, "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203078, "name": "pageIndex", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202826, "array": false, "paramDesc": "当前页数", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203079, "name": "warehouse_name", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202827, "array": true, "paramDesc": "仓库主键", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "[1620139372761344]", "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203080, "name": "product.productClass.name", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202828, "array": true, "paramDesc": "物料分类主键", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "[1613185527927040]", "fullName": "", "ytenantId": "", "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203081, "name": "product.cName", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202829, "array": true, "paramDesc": "商品主键", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "[1613334295384320]", "fullName": "", "ytenantId": "", "paramOrder": 4, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203082, "name": "productsku.skuName", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202830, "array": true, "paramDesc": "sku主键", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "[1613347223376128]", "fullName": "", "ytenantId": "", "paramOrder": 5, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203083, "name": "batchno", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202831, "array": false, "paramDesc": "批次号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "批次号", "fullName": "", "ytenantId": "", "paramOrder": 6, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203084, "name": "acolytesUnit", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202832, "array": false, "paramDesc": "辅计量单位", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 7, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203085, "name": "org", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202833, "array": true, "paramDesc": "组织主键", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[\"1620041647149568\"]", "fullName": "", "ytenantId": "", "paramOrder": 8, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203086, "name": "store", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202834, "array": true, "paramDesc": "门店主键", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "[1620139372769888]", "fullName": "", "ytenantId": "", "paramOrder": 9, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203087, "name": "open_currentqty_begin", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202835, "array": false, "paramDesc": "现存量下限", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 10, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203088, "name": "open_currentqty_end", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202836, "array": false, "paramDesc": "现存量上限", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": 1000, "fullName": "", "ytenantId": "", "paramOrder": 11, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203089, "name": "product_cName", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202837, "array": true, "paramDesc": "物料名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[称重物料1,称重物料2]", "fullName": "", "ytenantId": "", "paramOrder": 12, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203090, "name": "product_cCode", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202838, "array": true, "paramDesc": "物料编码", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[1620139372769888,1620139372769882]", "fullName": "", "ytenantId": "", "paramOrder": 13, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203091, "name": "warehouse_names", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202839, "array": true, "paramDesc": "仓库名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[仓库1,仓库2]", "fullName": "", "ytenantId": "", "paramOrder": 14, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203092, "name": "warehouse_code", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202840, "array": true, "paramDesc": "仓库编码", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[0105108,0105123]", "fullName": "", "ytenantId": "", "paramOrder": 15, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203093, "name": "productsku_skuName", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202841, "array": true, "paramDesc": "sku名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[物料1,物料2]", "fullName": "", "ytenantId": "", "paramOrder": 16, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203094, "name": "product_productClass_names", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202842, "array": true, "paramDesc": "物料分类名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[物料分类01,物料分类02]", "fullName": "", "ytenantId": "", "paramOrder": 17, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203095, "name": "stockStatusDoc_status_name", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202843, "array": true, "paramDesc": "库存状态名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[合格,待检]", "fullName": "", "ytenantId": "", "paramOrder": 18, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203096, "name": "product_brand", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202844, "array": false, "paramDesc": "物料品牌", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "品牌01", "fullName": "", "ytenantId": "", "paramOrder": 19, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203097, "name": "product_manufacturer", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202845, "array": false, "paramDesc": "生产厂商", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "厂商01", "fullName": "", "ytenantId": "", "paramOrder": 20, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203098, "name": "product_cModel", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202846, "array": false, "paramDesc": "型号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "Z0001", "fullName": "", "ytenantId": "", "paramOrder": 21, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203099, "name": "open_validityDistance_begin", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202847, "array": false, "paramDesc": "效期天数下限", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 22, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203100, "name": "open_validityDistance_end", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202848, "array": false, "paramDesc": "效期天数上限", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": 1000, "fullName": "", "ytenantId": "", "paramOrder": 23, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203101, "name": "batchno_define1", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202849, "array": false, "paramDesc": "批次自定义项1", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "ck-001", "fullName": "", "ytenantId": "", "paramOrder": 24, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203102, "name": "batchno_define2", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202850, "array": false, "paramDesc": "批次自定义项2", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 25, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203103, "name": "batchno_define3", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202851, "array": false, "paramDesc": "批次自定义项3", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 26, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203104, "name": "batchno_define4", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202852, "array": false, "paramDesc": "批次自定义项4", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 27, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203105, "name": "batchno_define5", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202853, "array": false, "paramDesc": "批次自定义项5", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 28, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203106, "name": "batchno_define6", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202854, "array": false, "paramDesc": "批次自定义项6", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 29, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203107, "name": "batchno_define7", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202855, "array": false, "paramDesc": "批次自定义项7", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 30, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203108, "name": "batchno_define8", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202856, "array": false, "paramDesc": "批次自定义项8", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 31, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203109, "name": "batchno_define9", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202857, "array": false, "paramDesc": "批次自定义项9", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 32, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203110, "name": "batchno_define10", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202858, "array": false, "paramDesc": "批次自定义项10", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 33, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203111, "name": "batchno_define11", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202859, "array": false, "paramDesc": "批次自定义项11", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 34, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203112, "name": "batchno_define12", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202860, "array": false, "paramDesc": "批次自定义项12", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 35, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203113, "name": "batchno_define13", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202861, "array": false, "paramDesc": "批次自定义项13", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 36, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203114, "name": "batchno_define14", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202862, "array": false, "paramDesc": "批次自定义项14", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 37, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203115, "name": "batchno_define15", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202863, "array": false, "paramDesc": "批次自定义项15", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 38, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203116, "name": "batchno_define16", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202864, "array": false, "paramDesc": "批次自定义项16", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 39, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203117, "name": "batchno_define17", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202865, "array": false, "paramDesc": "批次自定义项17", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 40, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203118, "name": "batchno_define18", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202866, "array": false, "paramDesc": "批次自定义项18", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 41, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203119, "name": "batchno_define19", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202867, "array": false, "paramDesc": "批次自定义项19", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 42, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203120, "name": "batchno_define20", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202868, "array": false, "paramDesc": "批次自定义项20", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 43, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203121, "name": "batchno_define21", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202869, "array": false, "paramDesc": "批次自定义项21", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 44, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203122, "name": "batchno_define22", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202870, "array": false, "paramDesc": "批次自定义项22", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 45, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203123, "name": "batchno_define23", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202871, "array": false, "paramDesc": "批次自定义项23", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 46, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203124, "name": "batchno_define24", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202872, "array": false, "paramDesc": "批次自定义项24", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 47, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203125, "name": "batchno_define25", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202873, "array": false, "paramDesc": "批次自定义项25", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 48, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203126, "name": "batchno_define26", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202874, "array": false, "paramDesc": "批次自定义项26", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 49, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203127, "name": "batchno_define27", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202875, "array": false, "paramDesc": "批次自定义项27", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 50, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203128, "name": "batchno_define28", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202876, "array": false, "paramDesc": "批次自定义项28", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 51, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203129, "name": "batchno_define29", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202877, "array": false, "paramDesc": "批次自定义项29", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 52, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2200211570023203130, "name": "batchno_define30", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202878, "array": false, "paramDesc": "批次自定义项30", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 53, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "queryParamDTOS": "", "ysApi": false, "presetTokenApi": false, "applyFlag": false, "cover": false, "paramMapDTOS": {"paramMapDTOS": [{"id": 2200211570023203131, "name": "pageSize", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "每页行数", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "pageSize", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203132, "name": "pageIndex", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "当前页数", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "pageIndex", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203133, "name": "warehouse_name", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "仓库主键", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "warehouse_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203134, "name": "product.productClass.name", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "物料分类主键", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "product.productClass.name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203135, "name": "product.cName", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "商品主键", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "aggregatedValueObject": false, "mapName": "product.cName", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203136, "name": "productsku.skuName", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "sku主键", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "aggregatedValueObject": false, "mapName": "productsku.skuName", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203137, "name": "batchno", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203138, "name": "acolytesUnit", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "辅计量单位", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "aggregatedValueObject": false, "mapName": "acolytesUnit", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203139, "name": "org", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "组织主键", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "aggregatedValueObject": false, "mapName": "org", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203140, "name": "store", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "门店主键", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "aggregatedValueObject": false, "mapName": "store", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203141, "name": "open_currentqty_begin", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "现存量下限", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "aggregatedValueObject": false, "mapName": "open_currentqty_begin", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203142, "name": "open_currentqty_end", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "现存量上限", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "aggregatedValueObject": false, "mapName": "open_currentqty_end", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203143, "name": "product_cName", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "物料名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "aggregatedValueObject": false, "mapName": "product_cName", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203144, "name": "product_cCode", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "物料编码", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "aggregatedValueObject": false, "mapName": "product_cCode", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203145, "name": "warehouse_names", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "仓库名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 14, "baseType": true, "aggregatedValueObject": false, "mapName": "warehouse_names", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203146, "name": "warehouse_code", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "仓库编码", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "aggregatedValueObject": false, "mapName": "warehouse_code", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203147, "name": "productsku_skuName", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "sku名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "aggregatedValueObject": false, "mapName": "productsku_skuName", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203148, "name": "product_productClass_names", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "物料分类名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "aggregatedValueObject": false, "mapName": "product_productClass_names", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203149, "name": "stockStatusDoc_status_name", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "库存状态名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "aggregatedValueObject": false, "mapName": "stockStatusDoc_status_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203150, "name": "product_brand", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "物料品牌", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "aggregatedValueObject": false, "mapName": "product_brand", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203151, "name": "product_manufacturer", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "生产厂商", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 20, "baseType": true, "aggregatedValueObject": false, "mapName": "product_manufacturer", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203152, "name": "product_cModel", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "型号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 21, "baseType": true, "aggregatedValueObject": false, "mapName": "product_cModel", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203153, "name": "open_validityDistance_begin", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "效期天数下限", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 22, "baseType": true, "aggregatedValueObject": false, "mapName": "open_validityDistance_begin", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203154, "name": "open_validityDistance_end", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "效期天数上限", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 23, "baseType": true, "aggregatedValueObject": false, "mapName": "open_validityDistance_end", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203155, "name": "batchno_define1", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项1", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 24, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define1", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203156, "name": "batchno_define2", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项2", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 25, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define2", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203157, "name": "batchno_define3", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项3", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 26, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define3", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203158, "name": "batchno_define4", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项4", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 27, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define4", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203159, "name": "batchno_define5", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项5", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 28, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define5", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203160, "name": "batchno_define6", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项6", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 29, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define6", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203161, "name": "batchno_define7", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项7", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 30, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define7", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203162, "name": "batchno_define8", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项8", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 31, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define8", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203163, "name": "batchno_define9", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项9", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 32, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define9", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203164, "name": "batchno_define10", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项10", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 33, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define10", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203165, "name": "batchno_define11", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项11", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 34, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define11", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203166, "name": "batchno_define12", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项12", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 35, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define12", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203167, "name": "batchno_define13", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项13", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 36, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define13", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203168, "name": "batchno_define14", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项14", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 37, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define14", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203169, "name": "batchno_define15", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项15", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 38, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define15", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203170, "name": "batchno_define16", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项16", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 39, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define16", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203171, "name": "batchno_define17", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项17", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 40, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define17", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203172, "name": "batchno_define18", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项18", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 41, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define18", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203173, "name": "batchno_define19", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项19", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 42, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define19", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203174, "name": "batchno_define20", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项20", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 43, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define20", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203175, "name": "batchno_define21", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项21", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 44, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define21", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203176, "name": "batchno_define22", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项22", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 45, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define22", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203177, "name": "batchno_define23", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项23", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 46, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define23", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203178, "name": "batchno_define24", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项24", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 47, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define24", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203179, "name": "batchno_define25", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项25", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 48, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define25", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203180, "name": "batchno_define26", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项26", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 49, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define26", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203181, "name": "batchno_define27", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项27", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 50, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define27", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203182, "name": "batchno_define28", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项28", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 51, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define28", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203183, "name": "batchno_define29", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项29", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 52, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define29", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2200211570023203184, "name": "batchno_define30", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": "", "array": false, "paramDesc": "批次自定义项30", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 53, "baseType": true, "aggregatedValueObject": false, "mapName": "batchno_define30", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "paramReturnDTOS": {"paramReturnDTOS": [{"id": 2200211570023203255, "name": "code", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202933, "array": false, "paramDesc": "返回码，调用成功时返回200", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203256, "name": "message", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "defParamId": 2200211570023202934, "array": false, "paramDesc": "调用失败时的错误信息", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203185, "name": "data", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": "", "children": {"children": [{"id": 2200211570023203248, "name": "pageIndex", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203185, "defParamId": 2200211570023202936, "array": false, "paramDesc": "当前页数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203249, "name": "pageSize", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203185, "defParamId": 2200211570023202937, "array": false, "paramDesc": "每页行数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203250, "name": "pageCount", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203185, "defParamId": 2200211570023202938, "array": false, "paramDesc": "总页数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203251, "name": "beginPageIndex", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203185, "defParamId": 2200211570023202939, "array": false, "paramDesc": "开始页码（第一页）", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203252, "name": "endPageIndex", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203185, "defParamId": 2200211570023202940, "array": false, "paramDesc": "结束页码（有多少页）", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203253, "name": "recordCount", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203185, "defParamId": 2200211570023202941, "array": false, "paramDesc": "总记录数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203254, "name": "pubts", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203185, "defParamId": 2200211570023202942, "array": false, "paramDesc": "时间戳,格式为:yyyy-MM-dd HH:mm:ss", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203186, "name": "recordList", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203185, "children": {"children": [{"id": 2200211570023203193, "name": "id", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202944, "array": false, "paramDesc": "主键", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203194, "name": "org", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202945, "array": false, "paramDesc": "组织", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203195, "name": "org_name", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202946, "array": false, "paramDesc": "组织名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203196, "name": "areaClass", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202947, "array": false, "paramDesc": "地区", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203197, "name": "areaClass_name", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202948, "array": false, "paramDesc": "地区名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203198, "name": "store", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202949, "array": false, "paramDesc": "门店", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203199, "name": "store_codebianma", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202950, "array": false, "paramDesc": "门店编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203200, "name": "store_name", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202951, "array": false, "paramDesc": "门店名称名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203201, "name": "store_type", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202952, "array": false, "paramDesc": "门店类型, 1:直营店、2:直营专柜、3:加盟店、", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203202, "name": "warehouse", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202953, "array": false, "paramDesc": "仓库", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203203, "name": "warehouse_name", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202954, "array": false, "paramDesc": "仓库名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203204, "name": "productClass", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202955, "array": false, "paramDesc": "物料分类", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203205, "name": "productClass_code", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202956, "array": false, "paramDesc": "物料分类编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203206, "name": "productClass_name", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202957, "array": false, "paramDesc": "物料分类名称名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203207, "name": "product", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202958, "array": false, "paramDesc": "物料", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 14, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203208, "name": "product_cCode", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202959, "array": false, "paramDesc": "物料编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203209, "name": "product_defaultAlbumId", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202960, "array": false, "paramDesc": "物料首图片", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203210, "name": "product_cName", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202961, "array": false, "paramDesc": "物料名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203211, "name": "productsku", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202962, "array": false, "paramDesc": "物料SKU", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203212, "name": "product_modelDescription", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202963, "array": false, "paramDesc": "规格型号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203213, "name": "productsku_cCode", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202964, "array": false, "paramDesc": "物料SKU编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 20, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203214, "name": "productsku_skuName", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202965, "array": false, "paramDesc": "物料SKU名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 21, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203215, "name": "free1", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202966, "array": false, "paramDesc": "物料规格1", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 22, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203216, "name": "free2", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202967, "array": false, "paramDesc": "物料规格2", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 23, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203217, "name": "free3", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202968, "array": false, "paramDesc": "物料规格3", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 24, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203218, "name": "free4", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202969, "array": false, "paramDesc": "物料规格4", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 25, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203219, "name": "free5", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202970, "array": false, "paramDesc": "物料规格5", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 26, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203220, "name": "free6", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202971, "array": false, "paramDesc": "物料规格6", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 27, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203221, "name": "free7", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202972, "array": false, "paramDesc": "物料规格7", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 28, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203222, "name": "free8", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202973, "array": false, "paramDesc": "物料规格8", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 29, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203223, "name": "free9", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202974, "array": false, "paramDesc": "物料规格9", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 30, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203224, "name": "free10", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202975, "array": false, "paramDesc": "物料规格10", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 31, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203187, "name": "product_productProps", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "children": {"children": [{"id": 2200211570023203188, "name": "define1", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203187, "defParamId": 2200211570023202977, "array": false, "paramDesc": "物料自定义项1-物料自定义项30", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203189, "name": "define30", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203187, "defParamId": 2200211570023202978, "array": false, "paramDesc": "物料自定义项30", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 2200211570023202976, "array": false, "paramDesc": "物料自定义项", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 32, "baseType": true, "defaultValue": "", "required": false, "visible": "", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203190, "name": "product_productSkuProps", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "children": {"children": [{"id": 2200211570023203191, "name": "define1", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203190, "defParamId": 2200211570023202980, "array": false, "paramDesc": "SKU自定义项1-SKU自定义项60", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203192, "name": "define60", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203190, "defParamId": 2200211570023202981, "array": false, "paramDesc": "SKU自定义项1-SKU自定义项60", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 2200211570023202979, "array": false, "paramDesc": "SKU自定义项", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 33, "baseType": true, "defaultValue": "", "required": false, "visible": "", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203225, "name": "batchno", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202982, "array": false, "paramDesc": "批号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 34, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203226, "name": "batchno_define1", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202983, "array": false, "paramDesc": "批次属性1-批次属性30", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 35, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203227, "name": "producedate", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202984, "array": false, "paramDesc": "生产日期", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 36, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203228, "name": "invaliddate", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202985, "array": false, "paramDesc": "有效期至", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 37, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203229, "name": "unitName", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202986, "array": false, "paramDesc": "主计量", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 38, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203230, "name": "currentqty", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202987, "array": false, "paramDesc": "现存量", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 39, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203231, "name": "stockmoney", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202988, "array": false, "paramDesc": "库存金额", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 40, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203232, "name": "availableqty", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202989, "array": false, "paramDesc": "可用量", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 41, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203233, "name": "costprice", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202990, "array": false, "paramDesc": "成本价", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 42, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203234, "name": "innoticeqty", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202991, "array": false, "paramDesc": "收货预计入库量", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 43, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203235, "name": "costmoney", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202992, "array": false, "paramDesc": "成本金额", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 44, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203236, "name": "outnoticeqty", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202993, "array": false, "paramDesc": "发货预计出库量", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 45, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203237, "name": "preretailqty", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202994, "array": false, "paramDesc": "订单预计出库量", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 46, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203238, "name": "acolytesUnit", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202995, "array": false, "paramDesc": "辅单位", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 47, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203239, "name": "acolytesUnit_name", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202996, "array": false, "paramDesc": "辅计量单位名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 48, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"acolytesUnit.name\",\"cItemName\":\"acolytesUnit_name\",\"cCaption\":\"辅计量单位\",\"cShowCaption\":\"辅计量单位\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productunit\",\"cRefId\":null,\"cRefRetId\":{\"acolytesUnit\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":null,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"stock.currentstock.CurrentStock\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203240, "name": "currentSubQty", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202997, "array": false, "paramDesc": "现存件数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 49, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203241, "name": "acolytesUnit_precision", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202998, "array": false, "paramDesc": "辅计量单位精度", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 50, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203242, "name": "unit_precision", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023202999, "array": false, "paramDesc": "主计量单位精度", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 51, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203243, "name": "invExchRate", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023203000, "array": false, "paramDesc": "换算率", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 52, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203244, "name": "availableSubQty", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023203001, "array": false, "paramDesc": "可用件数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 53, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203245, "name": "innoticeSubQty", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023203002, "array": false, "paramDesc": "入库通知件数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 54, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203246, "name": "outnoticeSubQty", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023203003, "array": false, "paramDesc": "出库通知件数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 55, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2200211570023203247, "name": "preretailSubQty", "apiId": "f91945f5461546fab9be6f4aae4163f3", "parentId": 2200211570023203186, "defParamId": 2200211570023203004, "array": false, "paramDesc": "预订零售件数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 56, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 2200211570023202943, "array": true, "paramDesc": "数据", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 2200211570023202935, "array": false, "paramDesc": "调用成功时的返回数据", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-02-12 13:32:18.000", "gmtUpdate": "2025-02-12 13:32:18.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "returnFormatType": "JSON", "paramConstDTOS": "", "paramConstMapDTOS": "", "apiDemoReturnDTOS": {"apiDemoReturnDTOS": [{"id": 2200211578613137419, "apiId": "f91945f5461546fab9be6f4aae4163f3", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"pubts\": \"\", \"recordList\": [ { \"id\": \"\", \"org\": \"\", \"org_name\": \"\", \"areaClass\": \"\", \"areaClass_name\": \"\", \"store\": \"\", \"store_codebianma\": \"\", \"store_name\": \"\", \"store_type\": \"\", \"warehouse\": \"\", \"warehouse_name\": \"\", \"productClass\": \"\", \"productClass_code\": \"\", \"productClass_name\": \"\", \"product\": \"\", \"product_cCode\": \"\", \"product_defaultAlbumId\": \"\", \"product_cName\": \"\", \"productsku\": \"\", \"product_modelDescription\": \"\", \"productsku_cCode\": \"\", \"productsku_skuName\": \"\", \"free1\": \"\", \"free2\": \"\", \"free3\": \"\", \"free4\": \"\", \"free5\": \"\", \"free6\": \"\", \"free7\": \"\", \"free8\": \"\", \"free9\": \"\", \"free10\": \"\", \"product_productProps\": { \"define1\": \"\", \"define2\": \"\", \"define3\": \"\", \"define4\": \"\", \"define5\": \"\", \"define6\": \"\", \"define7\": \"\", \"define8\": \"\", \"define9\": \"\", \"define10\": \"\", \"define11\": \"\", \"define12\": \"\", \"define13\": \"\", \"define14\": \"\", \"define15\": \"\", \"define16\": \"\", \"define17\": \"\", \"define18\": \"\", \"define19\": \"\", \"define20\": \"\", \"define21\": \"\", \"define22\": \"\", \"define23\": \"\", \"define24\": \"\", \"define25\": \"\", \"define26\": \"\", \"define27\": \"\", \"define28\": \"\", \"define29\": \"\", \"define30\": \"\" }, \"product_productSkuProps\": { \"define1\": \"\", \"define2\": \"\", \"define3\": \"\", \"define4\": \"\", \"define5\": \"\", \"define6\": \"\", \"define7\": \"\", \"define8\": \"\", \"define9\": \"\", \"define10\": \"\", \"define11\": \"\", \"define12\": \"\", \"define13\": \"\", \"define14\": \"\", \"define15\": \"\", \"define16\": \"\", \"define17\": \"\", \"define18\": \"\", \"define19\": \"\", \"define20\": \"\", \"define21\": \"\", \"define22\": \"\", \"define23\": \"\", \"define24\": \"\", \"define25\": \"\", \"define26\": \"\", \"define27\": \"\", \"define28\": \"\", \"define29\": \"\", \"define30\": \"\", \"define31\": \"\", \"define32\": \"\", \"define33\": \"\", \"define34\": \"\", \"define35\": \"\", \"define36\": \"\", \"define37\": \"\", \"define38\": \"\", \"define39\": \"\", \"define40\": \"\", \"define41\": \"\", \"define42\": \"\", \"define43\": \"\", \"define44\": \"\", \"define45\": \"\", \"define46\": \"\", \"define47\": \"\", \"define48\": \"\", \"define49\": \"\", \"define50\": \"\", \"define51\": \"\", \"define52\": \"\", \"define53\": \"\", \"define54\": \"\", \"define55\": \"\", \"define56\": \"\", \"define57\": \"\", \"define58\": \"\", \"define59\": \"\", \"define60\": \"\" }, \"batchno\": \"\", \"batchno_define1\": \"\", \"batchno_define2\": \"\", \"batchno_define3\": \"\", \"batchno_define4\": \"\", \"batchno_define5\": \"\", \"batchno_define6\": \"\", \"batchno_define7\": \"\", \"batchno_define8\": \"\", \"batchno_define9\": \"\", \"batchno_define10\": \"\", \"batchno_define11\": \"\", \"batchno_define12\": \"\", \"batchno_define13\": \"\", \"batchno_define14\": \"\", \"batchno_define15\": \"\", \"batchno_define16\": \"\", \"batchno_define17\": \"\", \"batchno_define18\": \"\", \"batchno_define19\": \"\", \"batchno_define20\": \"\", \"batchno_define21\": \"\", \"batchno_define22\": \"\", \"batchno_define23\": \"\", \"batchno_define24\": \"\", \"batchno_define25\": \"\", \"batchno_define26\": \"\", \"batchno_define27\": \"\", \"batchno_define28\": \"\", \"batchno_define29\": \"\", \"batchno_define30\": \"\", \"producedate\": \"\", \"invaliddate\": \"\", \"unitName\": \"\", \"currentqty\": 0, \"stockmoney\": 0, \"availableqty\": 0, \"costprice\": 0, \"innoticeqty\": 0, \"costmoney\": 0, \"outnoticeqty\": 0, \"preretailqty\": 0, \"acolytesUnit\": \"\", \"acolytesUnit_name\": \"\", \"currentSubQty\": 0, \"acolytesUnit_precision\": \"\", \"unit_precision\": \"\", \"invExchRate\": 0, \"availableSubQty\": 0, \"innoticeSubQty\": 0, \"outnoticeSubQty\": 0, \"preretailSubQty\": 0 } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2025-02-12 13:32:19.000", "gmtUpdate": "2025-02-12 13:32:19.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2200211578613137420, "apiId": "f91945f5461546fab9be6f4aae4163f3", "content": "", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2025-02-12 13:32:19.000", "gmtUpdate": "2025-02-12 13:32:19.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "apiDemoReturnDTOList": {"apiDemoReturnDTOList": [{"id": 2200211578613137419, "apiId": "f91945f5461546fab9be6f4aae4163f3", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"pubts\": \"\", \"recordList\": [ { \"id\": \"\", \"org\": \"\", \"org_name\": \"\", \"areaClass\": \"\", \"areaClass_name\": \"\", \"store\": \"\", \"store_codebianma\": \"\", \"store_name\": \"\", \"store_type\": \"\", \"warehouse\": \"\", \"warehouse_name\": \"\", \"productClass\": \"\", \"productClass_code\": \"\", \"productClass_name\": \"\", \"product\": \"\", \"product_cCode\": \"\", \"product_defaultAlbumId\": \"\", \"product_cName\": \"\", \"productsku\": \"\", \"product_modelDescription\": \"\", \"productsku_cCode\": \"\", \"productsku_skuName\": \"\", \"free1\": \"\", \"free2\": \"\", \"free3\": \"\", \"free4\": \"\", \"free5\": \"\", \"free6\": \"\", \"free7\": \"\", \"free8\": \"\", \"free9\": \"\", \"free10\": \"\", \"product_productProps\": { \"define1\": \"\", \"define2\": \"\", \"define3\": \"\", \"define4\": \"\", \"define5\": \"\", \"define6\": \"\", \"define7\": \"\", \"define8\": \"\", \"define9\": \"\", \"define10\": \"\", \"define11\": \"\", \"define12\": \"\", \"define13\": \"\", \"define14\": \"\", \"define15\": \"\", \"define16\": \"\", \"define17\": \"\", \"define18\": \"\", \"define19\": \"\", \"define20\": \"\", \"define21\": \"\", \"define22\": \"\", \"define23\": \"\", \"define24\": \"\", \"define25\": \"\", \"define26\": \"\", \"define27\": \"\", \"define28\": \"\", \"define29\": \"\", \"define30\": \"\" }, \"product_productSkuProps\": { \"define1\": \"\", \"define2\": \"\", \"define3\": \"\", \"define4\": \"\", \"define5\": \"\", \"define6\": \"\", \"define7\": \"\", \"define8\": \"\", \"define9\": \"\", \"define10\": \"\", \"define11\": \"\", \"define12\": \"\", \"define13\": \"\", \"define14\": \"\", \"define15\": \"\", \"define16\": \"\", \"define17\": \"\", \"define18\": \"\", \"define19\": \"\", \"define20\": \"\", \"define21\": \"\", \"define22\": \"\", \"define23\": \"\", \"define24\": \"\", \"define25\": \"\", \"define26\": \"\", \"define27\": \"\", \"define28\": \"\", \"define29\": \"\", \"define30\": \"\", \"define31\": \"\", \"define32\": \"\", \"define33\": \"\", \"define34\": \"\", \"define35\": \"\", \"define36\": \"\", \"define37\": \"\", \"define38\": \"\", \"define39\": \"\", \"define40\": \"\", \"define41\": \"\", \"define42\": \"\", \"define43\": \"\", \"define44\": \"\", \"define45\": \"\", \"define46\": \"\", \"define47\": \"\", \"define48\": \"\", \"define49\": \"\", \"define50\": \"\", \"define51\": \"\", \"define52\": \"\", \"define53\": \"\", \"define54\": \"\", \"define55\": \"\", \"define56\": \"\", \"define57\": \"\", \"define58\": \"\", \"define59\": \"\", \"define60\": \"\" }, \"batchno\": \"\", \"batchno_define1\": \"\", \"batchno_define2\": \"\", \"batchno_define3\": \"\", \"batchno_define4\": \"\", \"batchno_define5\": \"\", \"batchno_define6\": \"\", \"batchno_define7\": \"\", \"batchno_define8\": \"\", \"batchno_define9\": \"\", \"batchno_define10\": \"\", \"batchno_define11\": \"\", \"batchno_define12\": \"\", \"batchno_define13\": \"\", \"batchno_define14\": \"\", \"batchno_define15\": \"\", \"batchno_define16\": \"\", \"batchno_define17\": \"\", \"batchno_define18\": \"\", \"batchno_define19\": \"\", \"batchno_define20\": \"\", \"batchno_define21\": \"\", \"batchno_define22\": \"\", \"batchno_define23\": \"\", \"batchno_define24\": \"\", \"batchno_define25\": \"\", \"batchno_define26\": \"\", \"batchno_define27\": \"\", \"batchno_define28\": \"\", \"batchno_define29\": \"\", \"batchno_define30\": \"\", \"producedate\": \"\", \"invaliddate\": \"\", \"unitName\": \"\", \"currentqty\": 0, \"stockmoney\": 0, \"availableqty\": 0, \"costprice\": 0, \"innoticeqty\": 0, \"costmoney\": 0, \"outnoticeqty\": 0, \"preretailqty\": 0, \"acolytesUnit\": \"\", \"acolytesUnit_name\": \"\", \"currentSubQty\": 0, \"acolytesUnit_precision\": \"\", \"unit_precision\": \"\", \"invExchRate\": 0, \"availableSubQty\": 0, \"innoticeSubQty\": 0, \"outnoticeSubQty\": 0, \"preretailSubQty\": 0 } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2025-02-12 13:32:19.000", "gmtUpdate": "2025-02-12 13:32:19.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2200211578613137420, "apiId": "f91945f5461546fab9be6f4aae4163f3", "content": "", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2025-02-12 13:32:19.000", "gmtUpdate": "2025-02-12 13:32:19.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "routingStgy": 0, "routingStgyList": "", "apiDemoReturnDTO": {"id": 2200211578613137419, "apiId": "f91945f5461546fab9be6f4aae4163f3", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"pubts\": \"\", \"recordList\": [ { \"id\": \"\", \"org\": \"\", \"org_name\": \"\", \"areaClass\": \"\", \"areaClass_name\": \"\", \"store\": \"\", \"store_codebianma\": \"\", \"store_name\": \"\", \"store_type\": \"\", \"warehouse\": \"\", \"warehouse_name\": \"\", \"productClass\": \"\", \"productClass_code\": \"\", \"productClass_name\": \"\", \"product\": \"\", \"product_cCode\": \"\", \"product_defaultAlbumId\": \"\", \"product_cName\": \"\", \"productsku\": \"\", \"product_modelDescription\": \"\", \"productsku_cCode\": \"\", \"productsku_skuName\": \"\", \"free1\": \"\", \"free2\": \"\", \"free3\": \"\", \"free4\": \"\", \"free5\": \"\", \"free6\": \"\", \"free7\": \"\", \"free8\": \"\", \"free9\": \"\", \"free10\": \"\", \"product_productProps\": { \"define1\": \"\", \"define2\": \"\", \"define3\": \"\", \"define4\": \"\", \"define5\": \"\", \"define6\": \"\", \"define7\": \"\", \"define8\": \"\", \"define9\": \"\", \"define10\": \"\", \"define11\": \"\", \"define12\": \"\", \"define13\": \"\", \"define14\": \"\", \"define15\": \"\", \"define16\": \"\", \"define17\": \"\", \"define18\": \"\", \"define19\": \"\", \"define20\": \"\", \"define21\": \"\", \"define22\": \"\", \"define23\": \"\", \"define24\": \"\", \"define25\": \"\", \"define26\": \"\", \"define27\": \"\", \"define28\": \"\", \"define29\": \"\", \"define30\": \"\" }, \"product_productSkuProps\": { \"define1\": \"\", \"define2\": \"\", \"define3\": \"\", \"define4\": \"\", \"define5\": \"\", \"define6\": \"\", \"define7\": \"\", \"define8\": \"\", \"define9\": \"\", \"define10\": \"\", \"define11\": \"\", \"define12\": \"\", \"define13\": \"\", \"define14\": \"\", \"define15\": \"\", \"define16\": \"\", \"define17\": \"\", \"define18\": \"\", \"define19\": \"\", \"define20\": \"\", \"define21\": \"\", \"define22\": \"\", \"define23\": \"\", \"define24\": \"\", \"define25\": \"\", \"define26\": \"\", \"define27\": \"\", \"define28\": \"\", \"define29\": \"\", \"define30\": \"\", \"define31\": \"\", \"define32\": \"\", \"define33\": \"\", \"define34\": \"\", \"define35\": \"\", \"define36\": \"\", \"define37\": \"\", \"define38\": \"\", \"define39\": \"\", \"define40\": \"\", \"define41\": \"\", \"define42\": \"\", \"define43\": \"\", \"define44\": \"\", \"define45\": \"\", \"define46\": \"\", \"define47\": \"\", \"define48\": \"\", \"define49\": \"\", \"define50\": \"\", \"define51\": \"\", \"define52\": \"\", \"define53\": \"\", \"define54\": \"\", \"define55\": \"\", \"define56\": \"\", \"define57\": \"\", \"define58\": \"\", \"define59\": \"\", \"define60\": \"\" }, \"batchno\": \"\", \"batchno_define1\": \"\", \"batchno_define2\": \"\", \"batchno_define3\": \"\", \"batchno_define4\": \"\", \"batchno_define5\": \"\", \"batchno_define6\": \"\", \"batchno_define7\": \"\", \"batchno_define8\": \"\", \"batchno_define9\": \"\", \"batchno_define10\": \"\", \"batchno_define11\": \"\", \"batchno_define12\": \"\", \"batchno_define13\": \"\", \"batchno_define14\": \"\", \"batchno_define15\": \"\", \"batchno_define16\": \"\", \"batchno_define17\": \"\", \"batchno_define18\": \"\", \"batchno_define19\": \"\", \"batchno_define20\": \"\", \"batchno_define21\": \"\", \"batchno_define22\": \"\", \"batchno_define23\": \"\", \"batchno_define24\": \"\", \"batchno_define25\": \"\", \"batchno_define26\": \"\", \"batchno_define27\": \"\", \"batchno_define28\": \"\", \"batchno_define29\": \"\", \"batchno_define30\": \"\", \"producedate\": \"\", \"invaliddate\": \"\", \"unitName\": \"\", \"currentqty\": 0, \"stockmoney\": 0, \"availableqty\": 0, \"costprice\": 0, \"innoticeqty\": 0, \"costmoney\": 0, \"outnoticeqty\": 0, \"preretailqty\": 0, \"acolytesUnit\": \"\", \"acolytesUnit_name\": \"\", \"currentSubQty\": 0, \"acolytesUnit_precision\": \"\", \"unit_precision\": \"\", \"invExchRate\": 0, \"availableSubQty\": 0, \"innoticeSubQty\": 0, \"outnoticeSubQty\": 0, \"preretailSubQty\": 0 } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2025-02-12 13:32:19.000", "gmtUpdate": "2025-02-12 13:32:19.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, "apiDemoReturnDTOError": {"id": 2200211578613137420, "apiId": "f91945f5461546fab9be6f4aae4163f3", "content": "", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2025-02-12 13:32:19.000", "gmtUpdate": "2025-02-12 13:32:19.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}, "errorCodeDTOS": "", "displayCodeApiConfigDTOS": "", "tokenPlugin": "", "paramParsePlugin": "", "authPlugin": {"id": "09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "", "name": "友户通token认证业务扩展插件", "configurable": false, "description": "YonsuitBusinessExtendPlugin", "pluginType": "auth", "pluginTypeName": "业务扩展插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": false, "gmtCreate": "2020-05-22 00:00:00", "gmtUpdate": "2020-05-22 00:00:00", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "f91945f5461546fab9be6f4aae4163f3", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "resultParsePlugin": "", "mapReturnPluginConfig": "", "billNo": "", "domain": "", "apiCategory": "", "docUrl": "", "pathMatch": 0, "createUser": "96b6c7b9-2295-4fd8-96eb-924ea9af4075", "createUserName": "", "approvalStatus": 4, "publishTime": "2025-02-12 15:05:45", "pathJoin": false, "timeOut": 30, "tokenPluginName": "", "authPluginName": "", "resultPluginName": "", "apiDemoReturnRightDemo": "", "apiDemoReturnErrorDemo": "", "mock": false, "mockTimeout": "", "customUrl": "/stockanalysis/list", "fixedUrl": "/yonbip/scm", "apiCode": "f91945f5461546fab9be6f4aae4163f3", "tokenCheckType": 0, "enableMulti": false, "multiField": "", "idempotent": "non", "bidirectionalSSL": "", "ucgSchema": "HTTPS", "updateUserId": "99ea7655-00a2-4bda-b23c-19ade37ea574", "updateUserName": "u8c_vip管理员", "paramIsForce": "", "userIDPassthrough": true, "applyUser": "", "applyMsg": "", "dr": 0, "microServiceCode": "domain.yonbip-scm-stock", "applicationCode": "ST", "privacyCategory": 0, "privacyLevel": 4, "apiDesigned": 0, "serviceType": "", "integrateSchemeCode": "", "integrateSchemeName": "", "integrateObjectCode": "", "integrateObjectName": "", "integrateObjectCreatedType": "", "returnIntegObjId": "", "returnIntegObjName": "", "apiIntegrateDTOList": "", "apiRouteInfoDTOList": "", "arrayParam": false, "fileSize": "", "cc": false, "paramTransferMode": 2, "ytenantId": 0, "statusConf": "", "scene": 1, "version": "", "bizObjUri": "", "bizObjOperationType": "", "apiDefId": 2200211570023202823, "paramExtBizObjCode": "", "paramExtBizObjName": "", "paramExtRequest": 0, "paramExtResponse": 0, "paramExtInExtendKey": 0, "openScene": 1, "integrationScene": "", "apiType": "", "paramMark": "", "integrateSysId": "", "integrateSysName": "", "integrateSysCode": "", "dataZoneSetting": false, "reqDataZoneSetting": false, "respDataZoneSetting": false, "reqDataAllQuery": false, "reqDataAllBody": false, "respDataAllBody": false, "chargeStatus": 1, "beforeSpeed": 40, "afterSpeed": 80, "speedStatus": false, "reqDataRefPath": "", "respDataRefPath": "", "pubHistory": "", "deprecated": 0, "recommendedApiId": "", "recommendedApiName": "", "domainAppCode": "", "multiVersion": 0, "apiTag": ""}}, {"success": true, "code": 200, "message": "", "data": {"id": 2108770660671029249, "name": "用友YonBIP", "type": "integrateSys", "sort": 0, "enable": 0, "children": {"children": {"id": "SCC", "name": "供应链云", "type": 1, "sort": 0, "enable": 0, "children": {"children": {"id": "MM", "name": "采购供应", "type": 2, "sort": 0, "enable": 0, "children": {"children": {"id": "ST", "name": "库存管理", "type": 3, "sort": 0, "enable": 0, "children": {"children": {"id": "ustock.stock_stockanalysis", "name": "现存量表", "type": 4, "sort": 0, "enable": 0, "children": "", "parentId": "", "productId": "", "code": "ustock.stock_stockanalysis", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "ST", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "MM", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "SCC", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "current_yonbip_default_sys", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "isOrigin": 0, "hasChildren": 0, "order": 0}}]