# YS-API V3 架构文档

## 概述
YS-API V3 是一个从v2重构的全新项目，专注于数据同步、字段配置和数据库管理。采用现代架构设计，确保代码干净、可维护。

## 架构设计

### 1. 整体架构
- **前端**: 基于静态文件服务，通过FastAPI的`StaticFiles`模块提供服务。
- **后端**: 基于FastAPI框架，提供RESTful API服务。
- **数据库**: 使用SQL Server作为主数据库，Redis作为缓存层。
- **任务调度**: 集成异步任务处理机制，支持高并发数据写入。

### 2. 核心模块
- **配置管理**: 通过`config.py`统一管理应用配置，支持从V2的`config.ini`迁移。
- **数据写入**: `data_write_manager.py`负责数据写入逻辑，整合了V2的功能并优化性能。
- **字段配置**: `field_config_service.py`提供智能字段映射和配置功能。
- **API服务**: `main.py`作为入口文件，初始化FastAPI应用并加载中间件。

### 3. 技术栈
- **后端框架**: FastAPI
- **数据库**: SQL Server, Redis
- **缓存**: Redis
- **任务调度**: Asyncio
- **日志**: Structlog

## 项目结构
```
YS-API-V3/
├── backend/                # 后端代码
│   ├── app/               # 应用核心
│   │   ├── core/          # 核心模块（配置、数据库等）
│   │   ├── services/      # 服务层（数据写入、字段配置等）
│   │   └── main.py        # 应用入口
│   └── ...
├── frontend/              # 前端静态文件
├── config/                # 配置文件
├── docs/                  # 项目文档
└── ...
```

## 关键特性
1. **数据同步**: 支持15个业务模块的数据同步。
2. **字段配置**: 提供智能字段映射和配置功能。
3. **数据库管理**: 自动表创建和数据管理。
4. **进度监控**: 实时同步进度和状态监控。
5. **错误处理**: 完善的错误处理和日志记录机制。