import configparser
import os
import threading
import time
from dataclasses import dataclass
from pathlib import Path

import structlog

"""
YS-API V3.0 数据库管理器 - 优化版本
负责YSAPI数据库的检查、创建、表结构管理和连接管理
修复了主键溢出、SQL注入、连接泄漏、竞态条件等严重缺陷
"""


logger = structlog.get_logger()

# 系统常量定义
SYSTEM_COLUMNS =
   ['id',
     'created_at',
     'updated_at',
     'sync_status',
     'last_sync_time'
     ]
SYSTEM_COLUMN_DEFINITIONS = {
    'id': 'BIGINT PRIMARY KEY IDENTITY(1,1)',
    'created_at': 'DATETIME2 DEFAULT GETDATE()',
    'updated_at': 'DATETIME2 DEFAULT GETDATE()',
    'sync_status': 'NVARCHAR(20) DEFAULT \'active\'',
    'last_sync_time': 'DATETIME2 DEFAULT GETDATE()',
}

# 字段黑名单配置
FIELD_BLACKLIST = {
    'internal_id',
    'system_id',
    'temp_id',
    'cache_id',
    'debug_info',
    'log_data',
    'temp_data',
    'test_field',
}


@dataclass
class DatabaseConfig:
    """数据库配置"""

    server: str
    port: str
    database: str
    username: str
    password: str
    driver: str = "ODBC Driver 17 for SQL Server"
    pool_size: int = 10
    max_overflow: int = 20
    pool_pre_ping: bool = True
    pool_recycle: int = 3600

    @property
    def connection_string(self) -> str:
        """生成连接字符串"""
        return (
    f"mssql+pyodbc://{self.username}:{self.password}@{self.server}:{self.port}/"
    f"{self.database}?driver={self.driver.replace(' ', '+')}")

    @property
    def master_connection_string(self) -> str:
        """生成master数据库连接字符串"""
        return (
    f"mssql+pyodbc://{self.username}:{self.password}@{self.server}:{self.port}/"
    f"master?driver={self.driver.replace(' ', '+')}")


@dataclass
class TableInfo:
    """表信息"""

    table_name: str
    column_count: int
    exists: bool
    columns: List[Dict[str, Any]]


class DatabaseManager:
    """数据库管理器 - V3架构优化实现"""

    def __init___(self, config_file: str = None):
    """TODO: Add function description."""
       if config_file is None:
            # 智能查找config.ini文件路径
            possible_paths = [
                "config.ini",  # 当前目录
                "../config.ini",  # 上级目录
                "../../config.ini",  # 上上级目录
                str(
                    Path(__file__).parent.parent.parent.parent / "config.ini"
                ),  # 项目根目录
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    self.config_file = path
                    break
            else:
                # 如果都找不到，使用默认路径
                self.config_file = "config.ini"
        else:
            self.config_file = config_file

        self.db_config = self._load_config()
        self.engine = None
        self.master_engine = None

        # 线程锁 - 防止并发创建表的竞态条件
        self._table_lock = threading.Lock()
        self._database_lock = threading.Lock()

        # 表结构缓存 - 减少系统表查询
        self._table_cache = {}
        self._cache_timestamp = {}
        self._cache_ttl = 300  # 5分钟缓存

        # 完整的SQL类型映射
        self.sql_type_definitions = {
            'INT': 'INT',
            'BIGINT': 'BIGINT',
            'SMALLINT': 'SMALLINT',
            'TINYINT': 'TINYINT',
            'NVARCHAR(50)': 'NVARCHAR(600)',
            'NVARCHAR(100)': 'NVARCHAR(600)',
            'NVARCHAR(200)': 'NVARCHAR(600)',
            'NVARCHAR(350)': 'NVARCHAR(600)',
            'NVARCHAR(500)': 'NVARCHAR(600)',
            'NVARCHAR(1000)': 'NVARCHAR(1000)',
            'NVARCHAR(MAX)': 'NVARCHAR(MAX)',
            'VARCHAR(50)': 'VARCHAR(600)',
            'VARCHAR(100)': 'VARCHAR(600)',
            'VARCHAR(200)': 'VARCHAR(600)',
            'VARCHAR(500)': 'VARCHAR(600)',
            'VARCHAR(MAX)': 'VARCHAR(MAX)',
            'NTEXT': 'NTEXT',
            'TEXT': 'TEXT',
            'DECIMAL(28,8)': 'DECIMAL(28,8)',
            'DECIMAL(19,8)': 'DECIMAL(19,8)',
            'DECIMAL(18,2)': 'DECIMAL(18,2)',
            'DECIMAL(10,2)': 'DECIMAL(10,2)',
            'FLOAT': 'FLOAT',
            'REAL': 'REAL',
            'MONEY': 'MONEY',
            'SMALLMONEY': 'SMALLMONEY',
            'BIT': 'BIT',
            'DATETIME': 'DATETIME',
            'DATETIME2': 'DATETIME2',
            'DATETIMEOFFSET': 'DATETIMEOFFSET',
            'DATE': 'DATE',
            'TIME': 'TIME',
            'SMALLDATETIME': 'SMALLDATETIME',
            'UNIQUEIDENTIFIER': 'UNIQUEIDENTIFIER',
            'BINARY(50)': 'BINARY(50)',
            'VARBINARY(50)': 'VARBINARY(50)',
            'VARBINARY(MAX)': 'VARBINARY(MAX)',
            'IMAGE': 'IMAGE',
            'XML': 'XML',
            'SQL_VARIANT': 'SQL_VARIANT',
        }

    def _load_config(self) -> DatabaseConfig:
        """加载数据库配置"""
        if not os.path.exists(self.config_file):
            logger.error(f"配置文件不存在", file=self.config_file)
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")

        config = configparser.ConfigParser()
        config.read(self.config_file, encoding='utf-8')

        if 'database' not in config:
            raise ValueError("配置文件中缺少[database]部分")

        db_section = config['database']

        return DatabaseConfig(
            server=db_section.get('server', 'localhost'),
            port=db_section.get('port', '1433'),
            database=db_section.get('database', 'YSAPI'),
            username=db_section.get('username', ''),
            password=db_section.get('password', ''),
            driver=db_section.get('driver', 'ODBC Driver 17 for SQL Server'),
            pool_size=int(db_section.get('pool_size', '10')),
            max_overflow=int(db_section.get('max_overflow', '20')),
            pool_pre_ping=db_section.getboolean('pool_pre_ping', True),
            pool_recycle=int(db_section.get('pool_recycle', '3600')),
        )

    def _validate_table_name(self, table_name: str) -> bool:
        """验证表名安全性"""
        if not table_name or len(table_name) > 128:
            return False

        # 检查是否包含危险字符
        dangerous_chars = [';', '--', '/*', '*/', 'xp_', 'sp_', 'sys.']
        table_name_lower = table_name.lower()

        for char in dangerous_chars:
            if char in table_name_lower:
                return False

        # 检查是否为SQL关键字
        sql_keywords = {
            'select',
            'insert',
            'update',
            'delete',
            'drop',
            'create',
            'alter',
            'table',
            'database',
            'index',
            'view',
            'procedure',
            'function',
        }

        if table_name_lower in sql_keywords:
            return False

        return True

    def _validate_column_name(self, column_name: str) -> bool:
        """验证列名安全性"""
        if not column_name or len(column_name) > 128:
            return False

        # 检查是否在黑名单中
        if column_name.lower() in FIELD_BLACKLIST:
            return False

        # 检查是否包含危险字符
        dangerous_chars = [';', '--', '/*', '*/', 'xp_', 'sp_', 'sys.']
        column_name_lower = column_name.lower()

        for char in dangerous_chars:
            if char in column_name_lower:
                return False

        return True

    def _validate_data_type(self, data_type: str) -> bool:
        """验证数据类型安全性"""
        if not data_type:
            return False

        # 检查是否在允许的类型映射中
        return data_type.upper() in self.sql_type_definitions

    def initialize(self) -> bool:
        """
        初始化数据库连接

        Returns:
            bool: 初始化是否成功
        """
        logger.info("开始初始化数据库连接")

        try:
            # 1. 先连接到master数据库检查YSAPI数据库是否存在
            self.master_engine = create_engine(
                self.db_config.master_connection_string,
                pool_size=5,
                max_overflow=10,
                pool_pre_ping=self.db_config.pool_pre_ping,
                pool_recycle=self.db_config.pool_recycle,
            )

            # 2. 确保YSAPI数据库存在（使用锁防止竞态条件）
            with self._database_lock:
                if not self._database_exists():
                    logger.info("YSAPI数据库不存在，开始创建")
                    if not self._create_database():
                        return False

            # 3. 连接到YSAPI数据库
            self.engine = create_engine(
                self.db_config.connection_string,
                pool_size=self.db_config.pool_size,
                max_overflow=self.db_config.max_overflow,
                pool_pre_ping=self.db_config.pool_pre_ping,
                pool_recycle=self.db_config.pool_recycle,
            )

            # 4. 测试连接
            with self.engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()

            logger.info("数据库连接初始化成功")
            return True

        except Exception:
            logger.error(f"数据库连接初始化失败", error=str(e))
            return False

    def _database_exists(self) -> bool:
        """检查YSAPI数据库是否存在 - 使用SQLAlchemy统一接口"""
        try:
            with self.master_engine.connect() as conn:
                result = conn.execute(
                    text("SELECT COUNT(*) FROM sys.databases WHERE name = :name"),
                    {"name": self.db_config.database},
                )
                count = result.scalar()
                return count > 0
        except Exception:
            logger.error(f"检查数据库存在性失败", error=str(e))
            return False

    def _create_database(self) -> bool:
        """创建YSAPI数据库 - 使用SQLAlchemy统一接口"""
        try:
            # 使用SQLAlchemy执行DDL - 不在事务中执行
            create_ddl = DDL(f"CREATE DATABASE [{self.db_config.database}]")

            with self.master_engine.connect() as conn:
                # 设置自动提交模式，避免事务
                conn.execution_options(isolation_level="AUTOCOMMIT")
                conn.execute(create_ddl)

            logger.info(f"成功创建数据库", database=self.db_config.database)
            return True

        except Exception:
            logger.error(
                f"创建数据库失败", database=self.db_config.database, error=str(e)
            )
            return False

    def drop_database(self) -> bool:
        """删除YSAPI数据库 - 使用SQLAlchemy统一接口"""
        try:
            # 首先关闭现有连接
            if self.engine:
                self.engine.dispose()
                self.engine = None

            # 使用SQLAlchemy执行DDL - 不在事务中执行
            with self.master_engine.connect() as conn:
                # 设置自动提交模式，避免事务
                conn.execution_options(isolation_level="AUTOCOMMIT")

                # 强制关闭所有连接
                alter_ddl = DDL(
                    f"ALTER DATABASE [{self.db_config.database}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE"
                )
                conn.execute(alter_ddl)

                # 删除数据库
                drop_ddl = DDL(f"DROP DATABASE [{self.db_config.database}]")
                conn.execute(drop_ddl)

            logger.info(f"成功删除数据库", database=self.db_config.database)
            return True

        except Exception:
            logger.error(
                f"删除数据库失败", database=self.db_config.database, error=str(e)
            )
            return False

    def drop_all_tables(self) -> bool:
        """删除所有表"""
        try:
            if not self.initialize():
                return False

            # 获取所有表名
            with self.engine.connect() as conn:
                result = conn.execute(
                    text(
                        """
                    SELECT TABLE_NAME
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_TYPE = 'BASE TABLE'
                    AND TABLE_SCHEMA = 'dbo'
                """
                    )
                )

                tables = [row[0] for row in result.fetchall()]

                # 批量删除所有表
                for table in tables:
                    try:
                        drop_ddl = DDL(f"DROP TABLE [{table}]")
                        conn.execute(drop_ddl)
                        logger.debug(f"成功删除表", table=table)
                    except Exception:
                        logger.error(f"删除表失败", table=table, error=str(e))

                conn.commit()

            logger.info(f"成功删除所有表", table_count=len(tables))
            return True

        except Exception:
            logger.error(f"删除所有表失败", error=str(e))
            return False

    def reset_database(self) -> bool:
        """重置数据库 - 删除并重新创建"""
        logger.info("开始重置数据库")

        try:
            # 首先初始化连接（确保master_engine可用）
            if not self.initialize():
                logger.error("数据库连接初始化失败")
                return False

            # 删除数据库
            if not self.drop_database():
                logger.error("数据库删除失败")
                return False

            # 重新创建数据库
            if not self._create_database():
                logger.error("数据库重新创建失败")
                return False

            # 等待数据库完全创建
            if not self._wait_for_database_ready():
                logger.error("等待数据库就绪超时")
                return False

            # 重新初始化连接以连接到新创建的数据库
            if not self.initialize():
                logger.error("重新初始化数据库连接失败")
                return False

            logger.info("数据库重置完成")
            return True

        except Exception:
            logger.error("数据库重置失败", error=str(e))
            return False

    def _wait_for_database_ready(self, timeout: int = 30) -> bool:
        """等待数据库就绪"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                # 尝试连接到数据库
                test_engine = create_engine(
     self.db_config.connection_string, pool_size=1, max_overflow=0 )

                with test_engine.connect() as conn:
                    conn.execute(text("SELECT 1"))

                test_engine.dispose()
                return True

            except Exception:
                time.sleep(1)

        return False

    def create_module_table(self, module_config: ModuleConfig) -> bool:
        """
        根据模块配置创建数据库表

        Args:
            module_config: 模块配置

        Returns:
            bool: 创建是否成功
        """
        table_name = module_config.table_name

        # 验证表名安全性
        if not self._validate_table_name(table_name):
            logger.error(f"表名不安全", table=table_name)
            return False

        logger.info(f"开始创建模块表", table=table_name)

        if not self.engine:
            logger.error("数据库连接未初始化")
            return False

        # 使用锁防止并发创建表的竞态条件
        with self._table_lock:
            try:
                # 1. 检查表是否已存在
                if self._table_exists(table_name):
                    logger.info(f"表已存在，检查是否需要更新结构", table=table_name)
                    return self._update_table_structure(module_config)

                # 2. 构建CREATE TABLE语句
                create_sql = self._build_create_table_sql(module_config)

                # 3. 执行创建表语句
                with self.engine.connect() as conn:
                    conn.execute(text(create_sql))
                    conn.commit()

                # 清除表结构缓存
                self._clear_table_cache(table_name)

                logger.info(f"成功创建表", table=table_name)
                return True

            except Exception:
                logger.error(f"创建表失败", table=table_name, error=str(e))
                return False

    def _table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(
                    text("SELECT COUNT(*) FROM sys.tables WHERE name = :table_name"),
                    {"table_name": table_name},
                )
                count = result.scalar()
                return count > 0
        except Exception:
            logger.error(f"检查表存在性失败", table=table_name, error=str(e))
            return False

    def _build_create_table_sql(self, module_config: ModuleConfig) -> str:
        """构建CREATE TABLE SQL语句 - 使用BIGINT主键防止溢出"""
        table_name = module_config.table_name
        columns = []

        # 添加主键ID列 - 使用BIGINT防止溢出
        columns.append("id BIGINT PRIMARY KEY IDENTITY(1,1)")

        # 添加业务字段
        for field_name, field_config in module_config.fields.items():
            if field_config.is_selected:
                # 验证字段名和数据类型安全性
                if not self._validate_column_name(field_config.chinese_name):
                    logger.warning(
    f"跳过不安全的字段", field=field_config.chinese_name)
                    continue

                if not self._validate_data_type(field_config.data_type):
                    logger.warning(
                        f"跳过无效的数据类型",
                        field=field_config.chinese_name,
                        type=field_config.data_type,
                    )
                    continue

                column_def = self._build_column_definition(field_config)
                columns.append(column_def)

        # 添加系统字段
        for col_name, col_def in SYSTEM_COLUMN_DEFINITIONS.items():
            if col_name != 'id':  # id已经在上面添加了
                columns.append(f"{col_name} {col_def}")

        # 生成完整的CREATE TABLE语句
        create_sql = f"""
        CREATE TABLE [{table_name}] (
            {','.join(f'    {col}' for col in columns)}
        )
        """

        return create_sql

    def _build_column_definition(self, field_config: FieldConfig) -> str:
        """构建列定义 - 支持动态长度计算"""
        chinese_name = field_config.chinese_name
        data_type = field_config.data_type
        is_required = field_config.is_required

        # 验证数据类型
        if not self._validate_data_type(data_type):
            raise ValueError(f"无效的数据类型: {data_type}")

        # 获取映射后的数据类型
        mapped_type = self.sql_type_definitions.get(
            data_type.upper(), data_type)

        # 构建列定义
        column_def = f"[{chinese_name}] {mapped_type}"

        # 添加约束
        if is_required:
            column_def += " NOT NULL"

        # 添加默认值（如果是布尔类型）
        if mapped_type == "BIT" and not is_required:
            column_def += " DEFAULT 0"

        return column_def

    def _update_table_structure(self, module_config: ModuleConfig) -> bool:
        """更新表结构 - 批量执行DDL减少数据库交互"""
        table_name = module_config.table_name
        logger.info(f"开始更新表结构", table=table_name)

        try:
            # 1. 获取现有表结构
            existing_columns = self._get_table_columns(table_name)
            existing_column_names = {
    col['name'].lower() for col in existing_columns}

            # 2. 确定需要添加的列
            new_columns = []
            for field_name, field_config in module_config.fields.items():
                if field_config.is_selected:
                    chinese_name = field_config.chinese_name.lower()

                    # 验证字段安全性
                    if not self._validate_column_name(
                        field_config.chinese_name):
                        logger.warning(
                            f"跳过不安全的字段", field=field_config.chinese_name
                        )
                        continue

                    if not self._validate_data_type(field_config.data_type):
                        logger.warning(
                            f"跳过无效的数据类型",
                            field=field_config.chinese_name,
                            type=field_config.data_type,
                        )
                        continue

                    if chinese_name not in existing_column_names:
                        column_def = self._build_column_definition(
                            field_config)
                        new_columns.append(column_def)

            # 3. 批量添加新列
            if new_columns:
                with self.engine.connect() as conn:
                    # 批量执行ALTER TABLE语句
                    alter_sql = f"ALTER TABLE [{table_name}] ADD " + ", ".join(
                        new_columns
                    )
                    conn.execute(text(alter_sql))
                    conn.commit()

                # 清除表结构缓存
                self._clear_table_cache(table_name)

                logger.info(
                    f"成功添加新列", table=table_name, new_columns=len(new_columns)
                )
            else:
                logger.info(f"表结构无需更新", table=table_name)

            return True

        except Exception:
            logger.error(f"更新表结构失败", table=table_name, error=str(e))
            return False

    def _get_table_columns(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表的列信息 - 带缓存机制"""
        # 检查缓存
        current_time = time.time()
        if (
            table_name in self._table_cache
            and current_time - self._cache_timestamp.get(table_name, 0)
            < self._cache_ttl
        ):
            logger.debug(f"使用缓存的表结构", table=table_name)
            return self._table_cache[table_name]

        try:
            with self.engine.connect() as conn:
                result = conn.execute(
                    text(
                        """
                    SELECT
                        COLUMN_NAME as name,
                        DATA_TYPE as type,
                        CHARACTER_MAXIMUM_LENGTH as max_length,
                        IS_NULLABLE as nullable,
                        COLUMN_DEFAULT as default_value
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = :table_name
                    ORDER BY ORDINAL_POSITION
                """
                    ),
                    {"table_name": table_name},
                )

                columns = []
                for row in result:
                    columns.append(
                        {
                            'name': row.name,
                            'type': row.type,
                            'max_length': row.max_length,
                            'nullable': row.nullable == 'YES',
                            'default_value': row.default_value,
                        }
                    )

                # 更新缓存
                self._table_cache[table_name] = columns
                self._cache_timestamp[table_name] = current_time

                return columns

        except Exception:
            logger.error(f"获取表列信息失败", table=table_name, error=str(e))
            return []

    def _clear_table_cache(self, table_name: str = None):
        """清除表结构缓存"""
        if table_name:
            self._table_cache.pop(table_name, None)
            self._cache_timestamp.pop(table_name, None)
        else:
            self._table_cache.clear()
            self._cache_timestamp.clear()

    def create_all_module_tables(
        self, module_configs: Dict[str, ModuleConfig]
    ) -> Dict[str, bool]:
        """
        为所有模块创建数据库表

        Args:
            module_configs: 模块配置字典

        Returns:
            Dict[str, bool]: 模块名 -> 创建是否成功的映射
        """
        logger.info("开始创建所有模块表", total_modules=len(module_configs))

        results = {}

        for module_name, module_config in module_configs.items():
            try:
                success = self.create_module_table(module_config)
                results[module_name] = success

                if success:
                    logger.info(f"模块表创建成功", module=module_name)
                else:
                    logger.error(f"模块表创建失败", module=module_name)

            except Exception:
                logger.error(f"模块表创建异常", module=module_name, error=str(e))
                results[module_name] = False

        successful_count = sum(1 for success in results.values() if success)
        logger.info(
            f"完成所有模块表创建",
            successful=successful_count,
            total=len(module_configs),
        )

        return results

    def get_table_info(self, table_name: str) -> Optional[TableInfo]:
        """获取表信息"""
        if not self._table_exists(table_name):
            return TableInfo(
                table_name=table_name, column_count=0, exists=False, columns=[]
            )

        columns = self._get_table_columns(table_name)

        return TableInfo(
            table_name=table_name,
            column_count=len(columns),
            exists=True,
            columns=columns,
        )

    def validate_database_structure(
        self, module_configs: Dict[str, ModuleConfig]
    ) -> Dict[str, Dict[str, Any]]:
        """
        验证数据库结构完整性

        Args:
            module_configs: 模块配置字典

        Returns:
            Dict[str, Dict[str, Any]]: 验证结果
        """
        logger.info("开始验证数据库结构")

        validation_results = {}

        for module_name, module_config in module_configs.items():
            result = {
                'table_exists': False,
                'column_count': 0,
                'missing_columns': [],
                'extra_columns': [],
                'is_valid': False,
            }

            try:
                table_info = self.get_table_info(module_config.table_name)
                result['table_exists'] = table_info.exists

                if table_info.exists:
                    result['column_count'] = table_info.column_count

                    # 检查缺失和多余的列
                    expected_columns = set()
                    for field_config in module_config.fields.values():
                        if field_config.is_selected:
                            expected_columns.add(
    field_config.chinese_name.lower())

                    # 添加系统列
                    expected_columns.update(SYSTEM_COLUMNS)

                    actual_columns = {col['name'].lower()
                                                        for col in table_info.columns}

                    result['missing_columns'] = list(
                        expected_columns - actual_columns)
                    result['extra_columns'] = list(
                        actual_columns - expected_columns)

                    # 如果没有缺失列，则认为有效
                    result['is_valid'] = len(result['missing_columns']) == 0

                validation_results[module_name] = result

            except Exception:
                logger.error(f"验证表结构失败", module=module_name, error=str(e))
                result['error'] = str(e)
                validation_results[module_name] = result

        logger.info("完成数据库结构验证")
        return validation_results

    def execute_sql(self, sql: str, parameters: Optional[Dict] = None) -> Any:
        """
        执行SQL语句

        Args:
            sql: SQL语句
            parameters: 参数

        Returns:
            Any: 执行结果
        """
        if not self.engine:
            raise RuntimeError("数据库连接未初始化")

        try:
            with self.engine.connect() as conn:
                if parameters:
                    result = conn.execute(text(sql), parameters)
                else:
                    result = conn.execute(text(sql))

                conn.commit()
                return result

        except Exception:
            logger.error(f"SQL执行失败", sql=sql[:100], error=str(e))
            raise

    def get_connection(self):
        """获取数据库连接 - 已废弃，请使用上下文管理器"""
        logger.warning(
            "get_connection() 方法已废弃，请使用 with engine.connect() 上下文管理器"
        )
        if not self.engine:
            raise RuntimeError("数据库连接未初始化")

        return self.engine.connect()

    def close(self):
        """关闭数据库连接"""
        try:
            if self.engine:
                self.engine.dispose()
                self.engine = None

            if self.master_engine:
                self.master_engine.dispose()
                self.master_engine = None

            # 清除缓存
            self._clear_table_cache()

            logger.info("数据库连接已关闭")

        except Exception:
            logger.error(f"关闭数据库连接时发生错误", error=str(e))

    def __enter__(self):
        """上下文管理器入口"""
        if not self.initialize():
            raise RuntimeError("数据库初始化失败")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
