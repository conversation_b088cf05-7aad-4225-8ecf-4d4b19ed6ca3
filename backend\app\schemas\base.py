from datetime import datetime

from pydantic import BaseModel

"""
YS-API V3.0 基础数据模型
定义通用的响应格式和基础结构
"""


class APIResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    data: Optional[Any] = None
    timestamp: Optional[str] = None
    request_id: Optional[str] = None


class BaseResponse(BaseModel):
    """基础响应格式"""

    success: bool
    message: str
    timestamp: Optional[datetime] = None
    request_id: Optional[str] = None


class SuccessResponse(BaseResponse):
    """成功响应格式"""

    data: Any
    success: bool = True


class ErrorResponse(BaseResponse):
    """错误响应格式"""

    error: Dict[str, Any]
    success: bool = False


class PaginationInfo(BaseModel):
    """分页信息"""

    page: int
    page_size: int
    total: int
    total_pages: int
    has_next: bool
    has_prev: bool


class PaginatedResponse(BaseResponse):
    """分页响应格式"""

    data: Dict[str, Any]
    success: bool = True

    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "items": [],
                    "pagination": {
                        "page": 1,
                        "page_size": 20,
                        "total": 100,
                        "total_pages": 5,
                        "has_next": True,
                        "has_prev": False,
                    },
                },
                "message": "查询成功",
            }
        }


class ModuleInfo(BaseModel):
    """模块信息"""

    name: str
    display: str
    table_name: str
    api_endpoint: str
    md_file: str
    is_active: bool = True


class FieldInfo(BaseModel):
    """字段信息"""

    chinese_name: str
    data_type: str
    is_selected: bool = False
    is_required: bool = False
    field_order: int = 0
    importance_level: str = "中"
    description: Optional[str] = ""
    sample_value: Optional[str] = ""
    source: str = "api_auto"  # api_auto, manual, md_doc
    last_modified: Optional[datetime] = None


class ValidationResult(BaseModel):
    """验证结果"""

    is_valid: bool
    errors: List[str] = []
    warnings: List[str] = []


class TaskStatus(BaseModel):
    """任务状态"""

    task_id: str
    status: str  # pending, running, success, failed, cancelled
    module_name: Optional[str] = None
    sync_type: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[int] = None
    progress: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
