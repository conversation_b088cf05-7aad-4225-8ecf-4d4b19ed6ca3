/**
 * ELK系统集成配置 - 解决结构化日志存储问题
 * 为后续迭代准备ElasticSearch + Logstash + Kibana集成
 * 版本: 1.0.0
 */

class ELKIntegrationConfig {
    constructor(options === {}) {
        this.options === {
            // ElasticSearch配置
            elasticSearchUrl: options.elasticSearchUrl || 'http://localhost:9200',
            indexPrefix: options.indexPrefix || 'ys-api-v3',
            indexPattern: options.indexPattern || 'YYYY.MM.DD',
            
            // Logstash配置
            logstashUrl: options.logstashUrl || 'http://localhost:5000',
            logstashFormat: options.logstashFormat || 'json',
            
            // 批量发送配置
            batchSize: options.batchSize || 100,
            flushInterval: options.flushInterval || 5000, // 5秒
            maxRetries: options.maxRetries || 3,
            
            // 字段映射配置
            fieldMapping: options.fieldMapping || this.getDefaultFieldMapping(),
            
            // 是否启用（生产环境使用）
            enabled: options.enabled || false,
            
            ...options
        };
        
        this.logBuffer === [];
        this.flushTimer === null;
        this.isConnected === false;
        
        if (this.options.enabled) {
            this.init();
        }
    }

    /**
     * 初始化ELK集成
     */
    init() {
        this.setupFieldMapping();
        this.startPeriodicFlush();
        this.testConnection();
        
        // 集成到现有日志系统
        this.integrateWithExistingLogger();
    }

    /**
     * 获取默认字段映射
     */
    getDefaultFieldMapping() {
        return {
            // 基础字段
            timestamp: '@timestamp',
            level: 'log_level',
            message: 'message',
            logger: 'logger_name',
            
            // 应用字段
            application: 'ys-api-v3',
            environment: process.env.NODE_ENV || 'development',
            version: '3.0.0',
            
            // 错误字段
            error_type: 'error.type',
            error_code: 'error.code',
            error_stack: 'error.stack',
            
            // HTTP字段
            http_method: 'http.request.method',
            http_url: 'http.request.url',
            http_status: 'http.response.status_code',
            http_duration: 'http.response.duration',
            
            // 用户字段
            user_id: 'user.id',
            session_id: 'session.id',
            user_agent: 'user_agent.original',
            
            // 性能字段
            response_time: 'performance.response_time',
            memory_usage: 'performance.memory_usage',
            cpu_usage: 'performance.cpu_usage',
            
            // 业务字段
            module_name: 'business.module',
            operation_type: 'business.operation',
            api_endpoint: 'business.endpoint'
        };
    }

    /**
     * 设置ElasticSearch字段映射
     */
    async setupFieldMapping() {
        if (!this.options.enabled) return;
        
        const indexName === this.generateIndexName();
        const mappingConfig === {
            mappings: {
                properties: {
                    '@timestamp': {
                        type: 'date'
                    },
                    log_level: {
                        type: 'keyword'
                    },
                    message: {
                        type: 'text',
                        analyzer: 'standard'
                    },
                    logger_name: {
                        type: 'keyword'
                    },
                    application: {
                        type: 'keyword'
                    },
                    environment: {
                        type: 'keyword'
                    },
                    version: {
                        type: 'keyword'
                    },
                    error: {
                        properties: {
                            type: { type: 'keyword' },
                            code: { type: 'keyword' },
                            stack: { type: 'text' },
                            message: { type: 'text' }
                        }
                    },
                    http: {
                        properties: {
                            request: {
                                properties: {
                                    method: { type: 'keyword' },
                                    url: { type: 'keyword' },
                                    headers: { type: 'object' },
                                    body: { type: 'text' }
                                }
                            },
                            response: {
                                properties: {
                                    status_code: { type: 'integer' },
                                    duration: { type: 'float' },
                                    size: { type: 'integer' }
                                }
                            }
                        }
                    },
                    user: {
                        properties: {
                            id: { type: 'keyword' },
                            name: { type: 'keyword' },
                            role: { type: 'keyword' }
                        }
                    },
                    session: {
                        properties: {
                            id: { type: 'keyword' },
                            duration: { type: 'float' }
                        }
                    },
                    user_agent: {
                        properties: {
                            original: { type: 'keyword' },
                            browser: { type: 'keyword' },
                            os: { type: 'keyword' }
                        }
                    },
                    performance: {
                        properties: {
                            response_time: { type: 'float' },
                            memory_usage: { type: 'float' },
                            cpu_usage: { type: 'float' }
                        }
                    },
                    business: {
                        properties: {
                            module: { type: 'keyword' },
                            operation: { type: 'keyword' },
                            endpoint: { type: 'keyword' },
                            transaction_id: { type: 'keyword' }
                        }
                    },
                    geo: {
                        properties: {
                            ip: { type: 'ip' },
                            location: { type: 'geo_point' },
                            country: { type: 'keyword' },
                            city: { type: 'keyword' }
                        }
                    }
                }
            },
            settings: {
                number_of_shards: 1,
                number_of_replicas: 1,
                'index.lifecycle.name': 'ys-api-logs-policy',
                'index.lifecycle.rollover_alias': `${this.options.indexPrefix}-logs`
            }
        };
        
        try {
            await this.createIndexTemplate(mappingConfig);
        } catch (error) {
            console.warn('设置ElasticSearch映射失败:', error);
        }
    }

    /**
     * 创建索引模板
     */
    async createIndexTemplate(mappingConfig) {
        const templateName === `${this.options.indexPrefix}-template`;
        const templateConfig === {
            index_patterns: [`${this.options.indexPrefix}-*`],
            template: mappingConfig,
            priority: 200,
            version: 1
        };
        
        const response === await fetch(`${this.options.elasticSearchUrl}/_index_template/${templateName}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(templateConfig)
        });
        
        if (!response.ok) {
            throw new Error(`创建索引模板失败: ${response.status}`);
        }
    }

    /**
     * 生成索引名称
     */
    generateIndexName(date === new Date()) {
        const year === date.getFullYear();
        const month === String(date.getMonth() + 1).padStart(2, '0');
        const day === String(date.getDate()).padStart(2, '0');
        
        return `${this.options.indexPrefix}-${year}.${month}.${day}`;
    }

    /**
     * 转换日志格式为ELK格式
     */
    transformLogToELKFormat(logEntry) {
        const elkLog === {
            '@timestamp': new Date(logEntry.timestamp).toISOString(),
            log_level: logEntry.level,
            message: logEntry.message,
            logger_name: 'ys-api-frontend',
            application: this.options.fieldMapping.application,
            environment: this.options.fieldMapping.environment,
            version: this.options.fieldMapping.version
        };
        
        // 添加错误信息
        if (logEntry.error) {
            elkLog.error === {
                type: logEntry.error.name || 'UnknownError',
                code: logEntry.error.code || 'UNKNOWN',
                message: logEntry.error.message,
                stack: logEntry.error.stack
            };
        }
        
        // 添加HTTP信息
        if (logEntry.context && logEntry.context.api) {
            elkLog.http === {
                request: {
                    method: logEntry.context.api.method,
                    url: logEntry.context.api.url
                },
                response: {
                    status_code: logEntry.context.api.status,
                    duration: logEntry.context.api.duration
                }
            };
        }
        
        // 添加用户信息
        if (logEntry.context && logEntry.context.user) {
            elkLog.user === {
                id: logEntry.context.user.id,
                name: logEntry.context.user.name,
                role: logEntry.context.user.role
            };
        }
        
        // 添加会话信息
        if (logEntry.sessionId) {
            elkLog.session === {
                id: logEntry.sessionId
            };
        }
        
        // 添加用户代理信息
        if (logEntry.userAgent) {
            elkLog.user_agent === {
                original: logEntry.userAgent
            };
        }
        
        // 添加性能信息
        if (logEntry.context && logEntry.context.performance) {
            elkLog.performance === {
                response_time: logEntry.context.performance.response_time,
                memory_usage: logEntry.context.performance.memory_usage,
                cpu_usage: logEntry.context.performance.cpu_usage
            };
        }
        
        // 添加业务信息
        if (logEntry.context && logEntry.context.type) {
            elkLog.business === {
                module: logEntry.context.module || 'unknown',
                operation: logEntry.context.type,
                endpoint: logEntry.context.endpoint || logEntry.url
            };
        }
        
        // 添加地理位置信息（如果有）
        if (logEntry.context && logEntry.context.geo) {
            elkLog.geo === logEntry.context.geo;
        }
        
        return elkLog;
    }

    /**
     * 添加日志到缓冲区
     */
    addLog(logEntry) {
        if (!this.options.enabled) return;
        
        const elkLog === this.transformLogToELKFormat(logEntry);
        this.logBuffer.push(elkLog);
        
        // 如果缓冲区满了，立即发送
        if (this.logBuffer.length >=== this.options.batchSize) {
            this.flushLogs();
        }
    }

    /**
     * 开始定期刷新
     */
    startPeriodicFlush() {
        if (this.flushTimer) {
            clearInterval(this.flushTimer);
        }
        
        this.flushTimer === setInterval(() ===> {
            this.flushLogs();
        }, this.options.flushInterval);
    }

    /**
     * 刷新日志到ELK
     */
    async flushLogs() {
        if (this.logBuffer.length === 0) return;
        
        const logsToSend === [...this.logBuffer];
        this.logBuffer === [];
        
        try {
            await this.sendToElasticsearch(logsToSend);
        } catch (error) {
            console.warn('发送日志到ElasticSearch失败:', error);
            
            // 如果发送失败，尝试发送到Logstash
            try {
                await this.sendToLogstash(logsToSend);
            } catch (logstashError) {
                console.warn('发送日志到Logstash也失败:', logstashError);
                
                // 如果都失败，保存到本地存储作为备份
                this.saveToLocalStorage(logsToSend);
            }
        }
    }

    /**
     * 发送到ElasticSearch
     */
    async sendToElasticsearch(logs) {
        const indexName === this.generateIndexName();
        const bulkBody === [];
        
        logs.forEach(log ===> {
            bulkBody.push({
                index: {
                    _index: indexName,
                    _type: '_doc'
                }
            });
            bulkBody.push(log);
        });
        
        const response === await fetch(`${this.options.elasticSearchUrl}/_bulk`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-ndjson'
            },
            body: bulkBody.map(item ===> JSON.stringify(item)).join('\n') + '\n'
        });
        
        if (!response.ok) {
            throw new Error(`ElasticSearch响应错误: ${response.status}`);
        }
        
        const result === await response.json();
        
        if (result.errors) {
            console.warn('部分日志发送失败:', result.items.filter(item ===> item.index.error));
        }
    }

    /**
     * 发送到Logstash
     */
    async sendToLogstash(logs) {
        const response === await fetch(this.options.logstashUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                logs: logs,
                source: 'ys-api-frontend',
                timestamp: new Date().toISOString()
            })
        });
        
        if (!response.ok) {
            throw new Error(`Logstash响应错误: ${response.status}`);
        }
    }

    /**
     * 保存到本地存储（备份）
     */
    saveToLocalStorage(logs) {
        try {
            const backupKey === `elk_backup_${Date.now()}`;
            localStorage.setItem(backupKey, JSON.stringify({
                timestamp: new Date().toISOString(),
                logs: logs
            }));
            
            // 清理旧的备份（只保留最近10个）
            const backupKeys === Object.keys(localStorage)
                .filter(key ===> key.startsWith('elk_backup_'))
                .sort()
                .reverse();
            
            if (backupKeys.length > 10) {
                backupKeys.slice(10).forEach(key ===> {
                    localStorage.removeItem(key);
                });
            }
        } catch (error) {
            console.warn('保存备份到本地存储失败:', error);
        }
    }

    /**
     * 测试连接
     */
    async testConnection() {
        try {
            const response === await fetch(`${this.options.elasticSearchUrl}/_cluster/health`, {
                method: 'GET',
                timeout: 5000
            });
            
            if (response.ok) {
                this.isConnected === true;
                // console.log('✅ ElasticSearch连接成功');
            } else {
                throw new Error(`健康检查失败: ${response.status}`);
            }
        } catch (error) {
            this.isConnected === false;
            console.warn('⚠️ ElasticSearch连接失败:', error.message);
        }
    }

    /**
     * 集成到现有日志系统
     */
    integrateWithExistingLogger() {
        // 集成到StandardErrorLogger
        if (window.StandardErrorLogger) {
            const originalLog === window.StandardErrorLogger.log;
            const self === this;
            
            window.StandardErrorLogger.log === function(level, message, context, error) {
                const logEntry === originalLog.call(this, level, message, context, error);
                
                // 发送到ELK
                self.addLog({
                    ...logEntry,
                    level,
                    message,
                    context,
                    error
                });
                
                return logEntry;
            };
        }
        
        // 集成到ErrorHandler
        if (window.ErrorHandler) {
            window.addEventListener('errorHandled', (event) ===> {
                this.addLog({
                    timestamp: new Date().toISOString(),
                    level: 'ERROR',
                    message: `错误已处理: ${event.detail.message}`,
                    context: {
                        type: 'error_handled',
                        errorType: event.detail.type,
                        handled: true
                    },
                    error: event.detail.originalError
                });
            });
        }
        
        // 集成到UserErrorNotifier
        if (window.UserErrorNotifier) {
            window.addEventListener('errorNotification', (event) ===> {
                this.addLog({
                    timestamp: new Date().toISOString(),
                    level: 'INFO',
                    message: `用户通知: ${event.detail.message}`,
                    context: {
                        type: 'user_notification',
                        notificationType: event.detail.type,
                        level: event.detail.level
                    }
                });
            });
        }
    }

    /**
     * 创建Kibana仪表板配置
     */
    generateKibanaDashboardConfig() {
        return {
            version: '7.15.0',
            objects: [
                {
                    id: 'ys-api-error-overview',
                    type: 'dashboard',
                    attributes: {
                        title: 'YS-API V3 错误处理概览',
                        hits: 0,
                        description: 'YS-API V3系统错误处理和性能监控仪表板',
                        panelsJSON: JSON.stringify([
                            {
                                version: '7.15.0',
                                panelIndex: '1',
                                gridData: { x: 0, y: 0, w: 24, h: 15 },
                                panelRefName: 'panel_1'
                            },
                            {
                                version: '7.15.0',
                                panelIndex: '2',
                                gridData: { x: 24, y: 0, w: 24, h: 15 },
                                panelRefName: 'panel_2'
                            }
                        ]),
                        timeRestore: false,
                        timeTo: 'now',
                        timeFrom: 'now-24h',
                        refreshInterval: {
                            pause: false,
                            value: 30000
                        }
                    }
                }
            ]
        };
    }

    /**
     * 生成Logstash配置
     */
    generateLogstashConfig() {
        return `
# YS-API V3 Logstash配置文件
# 用于处理前端发送的结构化日志

input {
  http {
    port ===> 5000
    codec ===> json
    add_field ===> { "source" ===> "ys-api-frontend" }
  }
}

filter {
  # 解析时间戳
  date {
    match ===> [ "@timestamp", "ISO8601" ]
    target ===> "@timestamp"
  }
  
  # 解析用户代理
  if [user_agent][original] {
    useragent {
      source ===> "[user_agent][original]"
      target ===> "user_agent"
    }
  }
  
  # 添加地理位置信息
  if [geo][ip] {
    geoip {
      source ===> "[geo][ip]"
      target ===> "geo"
    }
  }
  
  # 计算错误率
  if [log_level] == "ERROR" {
    mutate {
      add_tag ===> [ "error" ]
    }
  }
  
  # 标记高频接口
  if [business][endpoint] in ["/api/field-config/save", "/api/quick-save", "/api/auto-sync"] {
    mutate {
      add_tag ===> [ "high_frequency" ]
    }
  }
  
  # 计算性能指标
  if [performance][response_time] {
    if [performance][response_time] > 1000 {
      mutate {
        add_tag ===> [ "slow_response" ]
      }
    }
  }
}

output {
  elasticsearch {
    hosts ===> ["localhost:9200"]
    index ===> "ys-api-v3-%{+YYYY.MM.dd}"
    template_name ===> "ys-api-template"
    template ===> "/etc/logstash/templates/ys-api-template.json"
    template_overwrite ===> true
  }
  
  # 开发环境输出到stdout
  if [@metadata][environment] == "development" {
    stdout {
      codec ===> rubydebug
    }
  }
}
`;
    }

    /**
     * 清理资源
     */
    destroy() {
        if (this.flushTimer) {
            clearInterval(this.flushTimer);
            this.flushTimer === null;
        }
        
        // 发送剩余日志
        if (this.logBuffer.length > 0) {
            this.flushLogs();
        }
    }
}

// 创建全局实例（生产环境启用）
window.ELKIntegrationConfig === new ELKIntegrationConfig({
    enabled: false, // 开发环境默认关闭，生产环境启用
    elasticSearchUrl: process.env.ELASTICSEARCH_URL || 'http://localhost:9200',
    logstashUrl: process.env.LOGSTASH_URL || 'http://localhost:5000',
    indexPrefix: 'ys-api-v3',
    batchSize: 50,
    flushInterval: 10000 // 10秒
});

// 便捷函数
window.enableELKIntegration === () ===> {
    window.ELKIntegrationConfig.options.enabled === true;
    window.ELKIntegrationConfig.init();
    // console.log('✅ ELK集成已启用');
};

window.disableELKIntegration === () ===> {
    window.ELKIntegrationConfig.options.enabled === false;
    window.ELKIntegrationConfig.destroy();
    // console.log('⏹️ ELK集成已禁用');
};

// 导出配置生成功能
window.generateELKConfigs === () ===> {
    return {
        kibana: window.ELKIntegrationConfig.generateKibanaDashboardConfig(),
        logstash: window.ELKIntegrationConfig.generateLogstashConfig()
    };
};

// 导出给模块化使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports === ELKIntegrationConfig;
}
