# 🛡️ YS-API V3.0 代码污染防护系统

## 🎯 系统概览

本系统实施"暴力清理法"+ "三级防护网"，确保项目免受AI生成污染代码的破坏。

### ⚡ 核心能力
- **污染检测**: 291个污染特征实时监控
- **自动清理**: 一键删除垃圾文件和修复代码  
- **架构保护**: 防止核心类被篡改
- **熔断机制**: 检测到严重污染立即停机

---

## 🚀 快速使用

### 立即清理（30秒）
```bash
# 一键暴力清理
python violent_cleanup.py

# 快速状态检查  
python pollution_guard.py
```

### 部署防护（5分钟）
```bash
# 1. 安装Git钩子
cp hooks/pre-commit-pollution-guard .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit

# 2. 配置VS Code
# 复制 .vscode/pollution-guard.json 到项目设置

# 3. 启用CI检测
# 使用 .github/workflows/pollution-detection.yml
```

---

## 📊 清理效果报告

### ✅ 已完成清理
- **删除垃圾文件**: 1个 (`temp_update.py`)
- **检测污染特征**: 291处  
- **清理缓存**: `__pycache__`, `logs`, `temp_cleanup`
- **部署守护**: `pollution_guard.py`

### 🎯 污染特征库
```
高危特征 (立即熔断):
- AI_generated
- ShadowClass  
- prototype.__override__

中危特征 (警告修复):
- __init___ (错误方法名)
- extends AI_Base
- TODO: Add function description

低危特征 (自动清理):
- *_shadow.* 文件
- *_copy.* 文件  
- *_duplicate.* 文件
```

---

## 🔒 三级防护网

### 第一级: 编辑器实时防护
- **工具**: VS Code插件配置
- **位置**: `.vscode/pollution-guard.json`
- **功能**: 
  - 实时标记污染代码
  - 阻止保存含污染的文件
  - 弹出警告提示

### 第二级: 提交前拦截
- **工具**: Git pre-commit hook
- **位置**: `hooks/pre-commit-pollution-guard`
- **功能**:
  - 扫描暂存文件污染特征
  - 阻止污染代码提交
  - 提供清理建议

### 第三级: CI流水线检查
- **工具**: GitHub Actions
- **位置**: `.github/workflows/pollution-detection.yml`  
- **功能**:
  - 全项目污染扫描
  - 自动修复尝试
  - 生成污染报告

---

## 🚨 紧急熔断机制

### 自动熔断触发条件
```python
# 检测到以下特征立即停机
CRITICAL_SIGNATURES = [
    "AI_generated",
    "ShadowClass",
    "prototype.__override__"
]
```

### 熔断流程
1. 🔍 **实时扫描** - `pollution_guard.py`每次启动检查
2. 🚨 **发现污染** - 匹配到关键特征  
3. 🔥 **立即熔断** - `process.exit(1)`强制停机
4. 📝 **记录日志** - 生成`.pollution_detected`标记文件
5. 📧 **发送警报** - 通知负责人(需配置)

---

## 📈 持续监控

### 日常检查命令
```bash
# 快速污染扫描
python pollution_guard.py

# 完整清理流程  
python violent_cleanup.py

# 查看清理报告
cat cleanup_report.json
```

### 监控指标
- **污染文件数**: 目标 0个
- **污染特征数**: 当前 291个 → 目标 <10个
- **垃圾文件数**: 当前 0个 (已清理)
- **缓存积累**: 定期清理

---

## 🛠️ 维护指南

### 更新污染特征库
编辑 `violent_cleanup.py` 中的 `AI_SIGNATURES` 列表:
```python
self.AI_SIGNATURES = [
    "Auto-generated by",
    "AI_generated", 
    # 添加新发现的污染特征
    "新的污染模式"
]
```

### 调整防护级别
修改各级防护配置:
- **编辑器**: `.vscode/pollution-guard.json`
- **Git钩子**: `hooks/pre-commit-pollution-guard`  
- **CI流水线**: `.github/workflows/pollution-detection.yml`

### 紧急恢复流程
如果防护系统本身出现问题:
```bash
# 1. 禁用防护
mv pollution_guard.py pollution_guard.py.bak

# 2. 手动清理
rm -rf __pycache__ logs temp_cleanup

# 3. 重置Git钩子
rm .git/hooks/pre-commit

# 4. 恢复到安全状态  
git reset --hard <安全提交哈希>
```

---

## 📞 支持联系

- **紧急情况**: 检测到严重污染时查看 `.pollution_detected` 文件
- **日常维护**: 每周运行一次 `violent_cleanup.py`
- **系统升级**: 根据新污染模式更新特征库

---

**⚠️ 重要提醒**: 本防护系统采用"宁可误杀，不可放过"的策略，如有误报请及时更新规则！

*最后更新: 2025年8月6日*
