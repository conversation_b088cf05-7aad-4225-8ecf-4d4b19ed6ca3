2025-08-02 20:50:54,877 - INFO - 🚀 开始构建 YS-API V3.0 CI/CD 流水线
2025-08-02 20:50:54,877 - INFO - ============================================================
2025-08-02 20:50:54,878 - INFO - 🔧 创建 GitHub Actions 工作流...
2025-08-02 20:50:54,879 - INFO - ✅ 创建 GitHub Actions 工作流: d:\OneDrive\Desktop\YS-API程序\v3\.github\workflows\main.yml
2025-08-02 20:50:54,879 - INFO - 🐳 创建 Docker 配置...
2025-08-02 20:50:54,880 - INFO - ✅ 创建 Docker 配置文件
2025-08-02 20:50:54,880 - INFO - 🚀 创建部署脚本...
2025-08-02 20:50:54,881 - INFO - ✅ 创建部署脚本
2025-08-02 20:50:54,881 - INFO - 📊 创建监控配置...
2025-08-02 20:50:54,882 - INFO - ✅ 创建监控配置
2025-08-02 20:50:54,882 - INFO - 📚 创建 CI/CD 文档...
2025-08-02 20:50:54,883 - INFO - ✅ 创建 CI/CD 文档: d:\OneDrive\Desktop\YS-API程序\v3\CICD-README.md
2025-08-02 20:50:54,884 - INFO - ============================================================
2025-08-02 20:50:54,884 - INFO - 🎉 CI/CD 流水线构建完成！
2025-08-02 20:50:54,884 - INFO - 📁 创建了 9 个配置文件
2025-08-02 20:50:54,884 - INFO - 📄 详细报告: d:\OneDrive\Desktop\YS-API程序\v3\cicd_build_report.md
