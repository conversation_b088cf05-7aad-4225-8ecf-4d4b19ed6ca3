#!/bin/bash
# YS-API V3.0 Production Deployment Script

set -e

echo "🚀 Starting YS-API V3.0 Production Deployment..."

# Configuration  
PROD_SERVERS=("prod1.ysapi.com" "prod2.ysapi.com")
PROD_USER="deploy"
APP_DIR="/opt/ysapi"
BACKUP_DIR="/opt/ysapi-backups"

for server in "${PROD_SERVERS[@]}"; do
    echo "🌐 Deploying to $server..."
    
    # Create backup
    echo "📦 Creating backup on $server..."
    ssh ${PROD_USER}@${server} "
        sudo mkdir -p ${BACKUP_DIR}
        sudo cp -r ${APP_DIR} ${BACKUP_DIR}/ysapi-$(date +%Y%m%d-%H%M%S)
    "
    
    # Stop application
    echo "⏹️ Stopping application on $server..."
    ssh ${PROD_USER}@${server} "
        sudo systemctl stop ysapi
        sudo docker-compose -f ${APP_DIR}/docker-compose.yml down || true
    "
    
    # Deploy new version
    echo "📋 Deploying new version to $server..."
    rsync -avz --exclude='.git' --exclude='logs/' . ${PROD_USER}@${server}:${APP_DIR}/
    
    # Update environment
    ssh ${PROD_USER}@${server} "
        cd ${APP_DIR}
        sudo docker-compose pull
        sudo docker-compose build
        sudo docker-compose up -d
        sudo systemctl start ysapi
    "
    
    # Health check
    echo "🏥 Performing health check on $server..."
    sleep 30
    if curl -f http://${server}:8000/health; then
        echo "✅ Deployment to $server successful!"
    else
        echo "❌ Deployment to $server failed, rolling back..."
        ssh ${PROD_USER}@${server} "
            sudo systemctl stop ysapi
            sudo rm -rf ${APP_DIR}
            sudo cp -r ${BACKUP_DIR}/ysapi-* ${APP_DIR}
            sudo systemctl start ysapi
        "
        exit 1
    fi
done

echo "🎉 Production deployment completed successfully!"
