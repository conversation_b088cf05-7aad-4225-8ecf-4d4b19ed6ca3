from datetime import datetime

from pydantic import BaseModel

from .base import BaseResponse

"""
YS-API V3.0 系统监控数据模型
定义系统监控相关的响应结构
"""


class ServiceStatus(BaseModel):
    """服务状态"""

    status: str  # connected, disconnected, error
    response_time_ms: Optional[float] = None
    last_check: Optional[datetime] = None
    error_message: Optional[str] = None


class DatabaseStatus(ServiceStatus):
    """数据库状态"""

    connection_pool: Optional[Dict[str, int]] = None


class SystemStatus(BaseModel):
    """系统状态"""

    status: str  # healthy, degraded, unhealthy
    uptime_seconds: int
    version: str
    environment: str


class SystemMetrics(BaseModel):
    """系统指标"""

    period: str
    api_requests: Dict[str, Any]
    sync_operations: Dict[str, Any]
    database: Dict[str, Any]
    system: Dict[str, Any]


class HealthCheckResponse(BaseResponse):
    """健康检查响应"""

    data: Dict[str, Any]
    success: bool = True

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "system": {
                        "status": "healthy",
                        "uptime_seconds": 86400,
                        "version": "3.0.0",
                        "environment": "production",
                    },
                    "services": {
                        "database": {
                            "status": "connected",
                            "connection_pool": {
                                "active": 5,
                                "idle": 10,
                                "max": 20},
                            "response_time_ms": 15,
                        },
                        "redis": {
                            "status": "connected",
                            "memory_usage": "45MB",
                            "response_time_ms": 2,
                        },
                    },
                },
                "message": "系统状态正常",
            }}


class MetricsResponse(BaseResponse):
    """系统指标响应"""

    data: SystemMetrics
    success: bool = True


class LogEntry(BaseModel):
    """日志条目"""

    timestamp: datetime
    level: str
    module: Optional[str] = None
    message: str
    details: Optional[Dict[str, Any]] = {}


class LogsResponse(BaseResponse):
    """实时日志响应"""

    data: List[LogEntry]
    success: bool = True
