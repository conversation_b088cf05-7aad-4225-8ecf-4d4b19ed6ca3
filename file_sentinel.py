import argparse
import hashlib
import json
from datetime import datetime
from pathlib import Path
from typing import Optional

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件创建哨兵系统
防止AI重复创建相同功能的文件
"""


class FileSentinel:
    """文件哨兵系统 - 防止重复文件创建"""

    def __init___(self, project_root: str = "."):
    """TODO: Add function description."""
    self.project_root = Path(project_root)
    self.sentinel_dir = self.project_root / ".file_sentinels"
    self.sentinel_dir.mkdir(exist_ok=True)
    self.registry_file = self.sentinel_dir / "creation_registry.json"
    self.load_registry()

    def load_registry(self):
        """加载创建记录"""
        if self.registry_file.exists():
            with open(self.registry_file, 'r', encoding='utf-8') as f:
                self.registry = json.load(f)
        else:
            self.registry = {
                "created_files": {},
                "function_signatures": {},
                "purpose_tracking": {},
            }

    def save_registry(self):
        """保存创建记录"""
        with open(self.registry_file, 'w', encoding='utf-8') as f:
            json.dump(self.registry, f, ensure_ascii=False, indent=2)

    def get_file_purpose_hash(
            self,
            purpose: str,
            file_type: str = "unknown") -> str:
        """根据文件用途生成哈希"""
        key = f"{purpose.lower()}:{file_type.lower()}"
        return hashlib.md5(key.encode()).hexdigest()[:12]

    def check_purpose_exists(
        self, purpose: str, file_type: str = "unknown"
    ) -> Optional[str]:
        """检查某个用途的文件是否已存在"""
        purpose_hash = self.get_file_purpose_hash(purpose, file_type)

        for existing_hash, info in self.registry["purpose_tracking"].items():
            if existing_hash == purpose_hash:
                existing_file = info["file_path"]
                if Path(existing_file).exists():
                    return existing_file
                else:
                    # 文件已被删除，清理记录
                    del self.registry["purpose_tracking"][existing_hash]
                    self.save_registry()
        return None

    def write_if_absent(
        self,
        file_path: str,
        content: str,
        purpose: str,
        file_type: str = "python",
        force: bool = False,
    ) -> bool:
        """安全写入文件 - 避免重复创建"""
        path = Path(file_path)

        # 检查是否已有相同用途的文件
        if not force:
            existing_file = self.check_purpose_exists(purpose, file_type)
            if existing_file:
                print(f"⚠️ 已存在相同用途的文件: {existing_file}")
                print(f"📝 用途: {purpose}")
                choice = input("是否仍要创建? (y/N): ").lower()
                if choice != 'y':
                    print("✅ 跳过创建，避免重复")
                    return False

        # 创建哨兵文件
        sentinel_file = path.with_suffix(path.suffix + '.done')

        if sentinel_file.exists() and not force:
            print(f"🛡️ 哨兵阻止: {file_path} 已被创建过")
            return False

        # 写入文件
        path.parent.mkdir(parents=True, exist_ok=True)
        path.write_text(content, encoding='utf-8')

        # 创建哨兵
        sentinel_file.touch()

        # 记录到注册表
        purpose_hash = self.get_file_purpose_hash(purpose, file_type)
        self.registry["created_files"][str(path)] = {
            "created_at": datetime.now().isoformat(),
            "purpose": purpose,
            "file_type": file_type,
            "content_hash": hashlib.md5(content.encode()).hexdigest(),
        }
        self.registry["purpose_tracking"][purpose_hash] = {
            "purpose": purpose,
            "file_type": file_type,
            "file_path": str(path),
            "created_at": datetime.now().isoformat(),
        }

        self.save_registry()
        print(f"✅ 安全创建: {file_path}")
        return True

    def list_similar_files(self, purpose: str) -> list:
        """列出相似用途的文件"""
        similar = []
        search_terms = purpose.lower().split()

        for info in self.registry["purpose_tracking"].values():
            existing_purpose = info["purpose"].lower()
            if any(term in existing_purpose for term in search_terms):
                if Path(info["file_path"]).exists():
                    similar.append(info)

        return similar

    def cleanup_orphaned_sentinels(self):
        """清理孤儿哨兵文件"""
        cleaned = 0
        for sentinel_file in self.project_root.rglob("*.done"):
            original_file = sentinel_file.with_suffix('')
            if not original_file.exists():
                sentinel_file.unlink()
                cleaned += 1

        print(f"🧹 清理了 {cleaned} 个孤儿哨兵文件")
        return cleaned


def smart_create_file(
    file_path: str, content: str, purpose: str, file_type: str = "python"
) -> bool:
    """智能文件创建函数 - 供AI调用"""
    sentinel = FileSentinel()
    return sentinel.write_if_absent(file_path, content, purpose, file_type)


def main():
    """测试和管理哨兵系统"""

    parser = argparse.ArgumentParser(description="文件哨兵系统管理")
    parser.add_argument("--list", action="store_true", help="列出所有已创建文件")
    parser.add_argument("--cleanup", action="store_true", help="清理孤儿哨兵")
    parser.add_argument("--check", type=str, help="检查某用途是否已有文件")

    args = parser.parse_args()

    sentinel = FileSentinel()

    if args.list:
        print("📋 已创建文件列表:")
        for file_path, info in sentinel.registry["created_files"].items():
            status = "✅" if Path(file_path).exists() else "❌"
            print(f"  {status} {file_path} - {info['purpose']}")

    elif args.cleanup:
        sentinel.cleanup_orphaned_sentinels()

    elif args.check:
        existing = sentinel.check_purpose_exists(args.check)
        if existing:
            print(f"✅ 已存在: {existing}")
        else:
            print(f"❌ 不存在用途为 '{args.check}' 的文件")

    else:
        print("🛡️ 文件哨兵系统已启动")
        print("📋 当前保护的文件数:", len(sentinel.registry["created_files"]))


if __name__ == "__main__":
    main()
