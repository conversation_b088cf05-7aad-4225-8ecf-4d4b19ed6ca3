============================================================
📊 YS-API V3.0 依赖关系分析
============================================================
📅 生成时间: 2025-08-06 22:38:01

📋 文件类型分布:
- Python文件: 378 个
- JavaScript文件: 33 个

🔥 高依赖文件 (导入>10个模块):
--------------------------------------------------------------------------------
文件路径                                                         导入数量      
--------------------------------------------------------------------------------
scripts\port_manager.py                                      28        
backend\start_server_fixed.py                                22        
backend\app\services\auto_recovery_manager_enhanced.py       19        
core\app\services\auto_recovery_manager_enhanced.py          19        
backend\app\services\data_write_manager.py                   16        
core\app\services\data_write_manager.py                      16        
scripts\migrate_purchase_order.py                            13        
backend\app\services\auto_sync_scheduler.py                  12        
core\app\services\auto_sync_scheduler.py                     12        
scripts\database_dual_writer.py                              12        
backend\app\services\database_table_manager.py               11        
core\app\main.py                                             11        
core\app\services\database_table_manager.py                  11        

🔗 内部依赖关系:
--------------------------------------------------------------------------------
backend\app\core\database.py:
  ├─ .database_manager
  ├─ .config

backend\app\core\database_connection_pool.py:
  ├─ .config

backend\app\main_original.py:
  ├─ .api.v1

backend\app\schemas\monitor.py:
  ├─ .base

backend\app\services\auto_sync_scheduler.py:
  ├─ .data_write_manager
  ├─ .retry_helper
  ├─ .auto_recovery_manager_enhanced

backend\app\services\data_processor.py:
  ├─ .field_config_service

backend\app\services\data_write_manager.py:
  ├─ .ys_api_client
  ├─ .realtime_log_service
  ├─ .sync_status_manager
  ├─ .field_config_service

backend\app\services\enhanced_response_processor.py:
  ├─ .response_format_standardizer
  ├─ .advanced_data_transformer

backend\app\services\enhanced_ys_api_client.py:
  ├─ .ys_api_client

backend\app\services\fast_sync_service.py:
  ├─ .ys_api_client
  ├─ .data_write_manager

backend\app\services\field_config_service.py:
  ├─ .enhanced_json_field_matcher

backend\app\services\material_master_scheduler.py:
  ├─ .data_write_manager

backend\app\services\zero_downtime_implementation.py:
  ├─ .sync_status_manager

core\app\core\database.py:
  ├─ .database_manager
  ├─ .config

core\app\core\database_connection_pool.py:
  ├─ .config

core\app\main_original.py:
  ├─ .api.v1

core\app\schemas\monitor.py:
  ├─ .base

core\app\services\auto_sync_scheduler.py:
  ├─ .data_write_manager
  ├─ .retry_helper
  ├─ .auto_recovery_manager_enhanced

core\app\services\data_processor.py:
  ├─ .field_config_service

core\app\services\data_write_manager.py:
  ├─ .ys_api_client
  ├─ .realtime_log_service
  ├─ .sync_status_manager
  ├─ .field_config_service

core\app\services\fast_sync_service.py:
  ├─ .ys_api_client
  ├─ .data_write_manager

core\app\services\field_config_service.py:
  ├─ .enhanced_json_field_matcher

core\app\services\zero_downtime_implementation.py:
  ├─ .sync_status_manager

new-system\modules\purchase_order\routes.py:
  ├─ .service

scripts\migrate_purchase_order.py:
  ├─ .service

📦 第三方库使用统计:
----------------------------------------
structlog                 116 次
asyncio                    44 次
fastapi                    33 次
subprocess                 28 次
dataclasses                27 次
sqlalchemy                 21 次
typing                     15 次
pydantic                   15 次
configparser               12 次
shutil                     12 次
enum                       12 次
sqlite3                    11 次
contextlib                 11 次
argparse                    9 次
hashlib                     9 次

🏝️ 孤立文件 (无导入依赖):
------------------------------------------------------------
- ai_code_review_system.py
- automation_validator.py
- backend\__init__.py
- backend\app\api\__init__.py
- backend\app\api\v1\__init__.py
- backend\app\core\__init__.py
- backend\app\schemas\__init__.py
- backend\app\services\__init__.py
- backend\app\services\async_task_manager.py
- backend\app\services\concurrent_processor.py
... 还有 156 个孤立文件

🎯 重构建议:
1. 优先重构高依赖文件，减少耦合
2. 检查孤立文件是否为无用代码
3. 梳理内部依赖关系，建立清晰的模块边界
4. 评估第三方库的必要性