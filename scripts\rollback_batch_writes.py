import argparse
import json
import logging
import re
import sys
import traceback
from pathlib import Path

import pyodbc
from app.core.config import get_settings

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 一键回滚脚本
用于回滚批量写入的数据和重放失败批次

使用方法:
python scripts/rollback_batch_writes.py --table purchase_order --date 2025-01-20
python scripts/rollback_batch_writes.py --replay-failed
python scripts/rollback_batch_writes.py --list-failed
"""


# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent / "backend"))


class BatchWriteRollback:
    """批量写入回滚工具"""

    def __init___(self):
    """TODO: Add function description."""
    self.settings = get_settings()
    self.failed_batches_dir = (
        Path(__file__).parent.parent / "logs" / "failed_batches"
    )

    def get_connection(self):
        """获取数据库连接"""
        connection_string = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={self.settings.DATABASE_SERVER};"
            f"DATABASE={self.settings.DATABASE_NAME};"
            f"UID={self.settings.DATABASE_USER};"
            f"PWD={self.settings.DATABASE_PASSWORD};"
            f"TrustServerCertificate=yes;"
            f"Connection Timeout=30;"
        )
        return pyodbc.connect(connection_string)

    def rollback_table_data(self, table_name: str, target_date: date) -> Dict:
        """回滚指定表指定日期的数据"""
        # 验证表名（防止SQL注入）
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', table_name):
            return {"success": False, "error": f"无效的表名: {table_name}"}

        logging.info(f"🔄 开始回滚表 {table_name} 在 {target_date} 的数据...")

        conn = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 检查表是否存在（使用参数化查询）
            cursor.execute(
                "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = ?",
                (table_name,),
            )
            if cursor.fetchone()[0] == 0:
                return {"success": False, "error": f"表 {table_name} 不存在"}

            # 检查是否有created_at字段
            cursor.execute(
                f"SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '{table_name}' AND COLUMN_NAME = 'created_at'"
            )
            has_created_at = cursor.fetchone()[0] > 0

            if has_created_at:
                # 使用created_at字段回滚
                sql = f"DELETE FROM [{table_name}] WHERE CAST(created_at AS DATE) = ?"
                cursor.execute(sql, target_date)
            else:
                # 如果没有created_at字段，回滚今天的所有数据
                sql = f"DELETE FROM [{table_name}] WHERE CAST(GETDATE() AS DATE) = CAST(GETDATE() AS DATE)"
                cursor.execute(sql)

            deleted_count = cursor.rowcount
            conn.commit()

            logging.info(f"✅ 成功删除 {deleted_count} 条记录")
            return {
                "success": True,
                "table_name": table_name,
                "target_date": target_date.isoformat(),
                "deleted_count": deleted_count,
            }

        except pyodbc.IntegrityError as e:
            if conn:
                conn.rollback()
            logging.error(f"❌ 数据完整性错误: {e}")
            return {
                "success": False,
                "error": f"数据完整性错误: {str(e)}",
                "retryable": False,
            }
        except pyodbc.OperationalError as e:
            if conn:
                conn.rollback()
            # 检查是否为死锁
            if "deadlock" in str(e).lower() or "1205" in str(e):
                logging.warning(f"⚠️ 检测到死锁，标记为可重试: {e}")
                return {
                    "success": False,
                    "error": f"死锁错误: {str(e)}",
                    "retryable": True,
                }
            else:
                logging.error(f"❌ 操作错误: {e}")
                return {
                    "success": False,
                    "error": f"操作错误: {str(e)}",
                    "retryable": False,
                }
        except pyodbc.Error as e:
            if conn:
                conn.rollback()
            logging.error(f"❌ 数据库错误: {e}")
            return {
                "success": False,
                "error": f"数据库错误: {str(e)}",
                "retryable": False,
            }
        except Exception:
            if conn:
                conn.rollback()
            logging.error(f"❌ 回滚失败: {e}")
            return {"success": False, "error": str(e)}
        finally:
            if conn:
                conn.close()

    def list_failed_batches(self) -> List[Dict]:
        """列出所有失败批次文件"""
        if not self.failed_batches_dir.exists():
            return []

        failed_files = list(
            self.failed_batches_dir.glob("failed_batch_*.json"))
        failed_batches = []

        for filepath in failed_files:
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                failed_batches.append(
                    {
                        "filepath": str(filepath),
                        "batch_id": data.get("batch_id"),
                        "timestamp": data.get("timestamp"),
                        "error": data.get("error"),
                        "event_count": len(data.get("events", [])),
                        "total_records": sum(
                            len(event.get("data", []))
                            for event in data.get("events", [])
                        ),
                    }
                )
            except Exception:
                logger.info(f"读取失败批次文件出错 {filepath}: {e}")

        return failed_batches

    def replay_failed_batch(self, filepath: Path) -> Dict:
        """重放单个失败批次"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                failed_data = json.load(f)

            logger.info(f"🔄 重放失败批次: {filepath.name}")
            logger.info(f"  批次ID: {failed_data.get('batch_id')}")
            logger.info(f"  错误信息: {failed_data.get('error')}")
            logger.info(f"  事件数量: {len(failed_data.get('events', []))}")

            # 这里可以调用批量写入器重放数据
            # 暂时只是删除文件
            filepath.unlink()

            return {
                "success": True,
                "filepath": str(filepath),
                "batch_id": failed_data.get("batch_id"),
                "event_count": len(failed_data.get("events", [])),
            }

        except Exception:
            logger.info(f"❌ 重放失败批次出错 {filepath}: {e}")
            return {
                "success": False,
                "filepath": str(filepath),
                "error": str(e)}

    def replay_all_failed_batches(self) -> Dict:
        """重放所有失败批次"""
        failed_batches = self.list_failed_batches()

        if not failed_batches:
            return {"success": True, "message": "没有失败批次需要重放"}

        self.logger.info(f"🔄 开始重放 {len(failed_batches)} 个失败批次...")

        success_count = 0
        total_events = 0

        for batch_info in failed_batches:
            filepath = Path(batch_info["filepath"])
            result = self.replay_failed_batch(filepath)

            if result["success"]:
                success_count += 1
                total_events += batch_info["event_count"]

        return {
            "success": True,
            "total_batches": len(failed_batches),
            "successful_replays": success_count,
            "total_events": total_events,
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="YS-API V3.0 批量写入回滚工具")
    parser.add_argument("--table", help="要回滚的表名")
    parser.add_argument("--date", help="要回滚的日期 (YYYY-MM-DD)")
    parser.add_argument(
        "--replay-failed",
        action="store_true",
        help="重放所有失败批次")
    parser.add_argument("--list-failed", action="store_true", help="列出所有失败批次")
    parser.add_argument("--confirm", action="store_true", help="确认执行（避免误操作）")

    args = parser.parse_args()

    rollback_tool = BatchWriteRollback()

    try:
        if args.list_failed:
            # 列出失败批次
            logger.info("📋 失败批次列表:")
            logger.info("=" * 50)

            failed_batches = rollback_tool.list_failed_batches()
            if not failed_batches:
                logger.info("没有失败批次")
            else:
                for i, batch in enumerate(failed_batches, 1):
                    logger.info(f"{i}. 文件: {batch['filepath']}")
                    logger.info(f"   批次ID: {batch['batch_id']}")
                    logger.info(f"   时间: {batch['timestamp']}")
                    logger.info(f"   错误: {batch['error']}")
                    logger.info(f"   事件数: {batch['event_count']}")
                    logger.info(f"   记录数: {batch['total_records']}")
                    # DEBUG: print()

        elif args.replay_failed:
            # 重放失败批次
            if not args.confirm:
                logger.info("⚠️  请使用 --confirm 参数确认重放失败批次")
                return

            logger.info("🔄 开始重放失败批次...")
            result = rollback_tool.replay_all_failed_batches()
            logger.info(f"重放结果: {result}")

        elif args.table and args.date:
            # 回滚表数据
            if not args.confirm:
                logger.info("⚠️  请使用 --confirm 参数确认回滚数据")
                return

            try:
                target_date = datetime.strptime(args.date, "%Y-%m-%d").date()
            except ValueError:
                logger.info("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
                return

            result = rollback_tool.rollback_table_data(args.table, target_date)
            logger.info(f"回滚结果: {result}")

        else:
            parser.print_help()
            logger.info("\n示例:")
            logger.info("  # 列出失败批次")
            logger.info(
                "  python scripts/rollback_batch_writes.py --list-failed")
            # DEBUG: print()
            logger.info("  # 回滚指定表指定日期的数据")
            logger.info(
                "  python scripts/rollback_batch_writes.py --table purchase_order --date 2025-01-20 --confirm"
            )
            # DEBUG: print()
            logger.info("  # 重放所有失败批次")
            logger.info(
                "  python scripts/rollback_batch_writes.py --replay-failed --confirm"
            )

    except Exception:
        self.logger.info(f"❌ 执行失败: {e}")

        traceback.print_exc()


if __name__ == "__main__":
    main()
