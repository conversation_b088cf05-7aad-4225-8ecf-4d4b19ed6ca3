{"module_name": "sales_out", "display_name": "销售出库", "version": "2.0.0", "source": "json_parser", "total_fields": 172, "created_at": "2025-07-28T20:12:24.861060", "last_updated": "2025-07-28T20:12:24.861060", "fields": {"code": {"api_field_name": "code", "chinese_name": "单据编码", "data_type": "NVARCHAR(500)", "param_desc": "单据编码", "path": "data.recordList.code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "message": {"api_field_name": "message", "chinese_name": "调用失败时的错误信息", "data_type": "NVARCHAR(500)", "param_desc": "调用失败时的错误信息", "path": "message", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "调用成功时的返回数据", "data_type": "NVARCHAR(MAX)", "param_desc": "调用成功时的返回数据", "path": "data", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageIndex": {"api_field_name": "pageIndex", "chinese_name": "页号", "data_type": "BIGINT", "param_desc": "页号", "path": "pageIndex", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageSize": {"api_field_name": "pageSize", "chinese_name": "每页行数", "data_type": "BIGINT", "param_desc": "每页行数", "path": "pageSize", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordCount": {"api_field_name": "recordCount", "chinese_name": "总记录数", "data_type": "BIGINT", "param_desc": "总记录数", "path": "data.recordCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordList": {"api_field_name": "recordList", "chinese_name": "返回数据列表", "data_type": "NVARCHAR(MAX)", "param_desc": "返回数据列表", "path": "data.recordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "cReceiveAddress": {"api_field_name": "cR<PERSON><PERSON>ve<PERSON><PERSON><PERSON>", "chinese_name": "收货地址", "data_type": "NVARCHAR(500)", "param_desc": "收货地址", "path": "data.recordList.cReceiveAddress", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriTax": {"api_field_name": "oriTax", "chinese_name": "税率", "data_type": "DECIMAL(18,4)", "param_desc": "税率", "path": "data.recordList.oriTax", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "details_stockUnitId": {"api_field_name": "details_stockUnitId", "chinese_name": "库存单位主键", "data_type": "BIGINT", "param_desc": "库存单位主键", "path": "data.recordList.details_stockUnitId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_cCode": {"api_field_name": "product_cCode", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "data.recordList.product_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "details_taxId": {"api_field_name": "details_taxId", "chinese_name": "税目主键", "data_type": "NVARCHAR(500)", "param_desc": "税目主键", "path": "data.recordList.details_taxId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natCurrency": {"api_field_name": "natCurrency", "chinese_name": "本币主键", "data_type": "NVARCHAR(500)", "param_desc": "本币主键", "path": "data.recordList.natCurrency", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "sourcesys": {"api_field_name": "sourcesys", "chinese_name": "来源单据领域", "data_type": "NVARCHAR(500)", "param_desc": "来源单据领域", "path": "data.recordList.sourcesys", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "tradeRouteID": {"api_field_name": "tradeRouteID", "chinese_name": "贸易路径id", "data_type": "BIGINT", "param_desc": "贸易路径id", "path": "data.recordList.tradeRouteID", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockUnitId_Precision": {"api_field_name": "stockUnitId_Precision", "chinese_name": "库存单位精度", "data_type": "BIGINT", "param_desc": "库存单位精度", "path": "data.recordList.stockUnitId_Precision", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "id": {"api_field_name": "id", "chinese_name": "主键", "data_type": "BIGINT", "param_desc": "主键", "path": "data.recordList.id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "status_mobile_row": {"api_field_name": "status_mobile_row", "chinese_name": "单据状态", "data_type": "BIGINT", "param_desc": "单据状态", "path": "data.recordList.status_mobile_row", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invoiceTitle": {"api_field_name": "invoiceTitle", "chinese_name": "发票抬头", "data_type": "NVARCHAR(500)", "param_desc": "发票抬头", "path": "data.recordList.invoiceTitle", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "details_priceUOM": {"api_field_name": "details_priceUOM", "chinese_name": "计价单位主键", "data_type": "BIGINT", "param_desc": "计价单位主键", "path": "data.recordList.details_priceUOM", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natSum": {"api_field_name": "natSum", "chinese_name": "合计本币金额", "data_type": "BIGINT", "param_desc": "合计本币金额", "path": "data.sumRecordList.natSum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isEndTrade": {"api_field_name": "isEndTrade", "chinese_name": "是否末级", "data_type": "NVARCHAR(500)", "param_desc": "是否末级", "path": "data.recordList.isEndTrade", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehouse": {"api_field_name": "warehouse", "chinese_name": "仓库id", "data_type": "NVARCHAR(MAX)", "param_desc": "仓库id", "path": "warehouse", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "srcBillType": {"api_field_name": "srcBillType", "chinese_name": "来源单据类型", "data_type": "NVARCHAR(500)", "param_desc": "来源单据类型", "path": "data.recordList.srcBillType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "diliverStatus": {"api_field_name": "diliver<PERSON>tatus", "chinese_name": "发货状态", "data_type": "NVARCHAR(500)", "param_desc": "发货状态", "path": "data.recordList.diliverStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "warehouse_name": {"api_field_name": "warehouse_name", "chinese_name": "仓库名称", "data_type": "NVARCHAR(500)", "param_desc": "仓库名称", "path": "data.recordList.warehouse_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_priceDigit": {"api_field_name": "natCurrency_priceDigit", "chinese_name": "本币精度", "data_type": "BIGINT", "param_desc": "本币精度", "path": "data.recordList.natCurrency_priceDigit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "exchRateType": {"api_field_name": "exchRateType", "chinese_name": "汇率类型枚举值", "data_type": "NVARCHAR(500)", "param_desc": "汇率类型枚举值", "path": "data.recordList.exchRateType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "tradeRouteLineno": {"api_field_name": "tradeRouteLineno", "chinese_name": "站点", "data_type": "NVARCHAR(500)", "param_desc": "站点", "path": "data.recordList.tradeRouteLineno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invExchRate": {"api_field_name": "invExchRate", "chinese_name": "单位转换率", "data_type": "BIGINT", "param_desc": "单位转换率", "path": "data.recordList.invExchRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_defaultAlbumId": {"api_field_name": "product_defaultAlbumId", "chinese_name": "物料图片", "data_type": "NVARCHAR(500)", "param_desc": "物料图片", "path": "data.recordList.product_defaultAlbumId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "impactStockTiming": {"api_field_name": "impactStockTiming", "chinese_name": "更新存量传财务时机,0:保存;1:审核", "data_type": "NVARCHAR(500)", "param_desc": "更新存量传财务时机,0:保存;1:审核", "path": "data.recordList.impactStockTiming", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "status": {"api_field_name": "status", "chinese_name": "单据状态；0开立，1已审核，3审核中", "data_type": "BIGINT", "param_desc": "单据状态；0开立，1已审核，3审核中", "path": "data.recordList.status", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currency_moneyDigit": {"api_field_name": "currency_moneyDigit", "chinese_name": "币种精度", "data_type": "BIGINT", "param_desc": "币种精度", "path": "data.recordList.currency_moneyDigit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invoiceCust_name": {"api_field_name": "invoiceCust_name", "chinese_name": "开票客户名称", "data_type": "NVARCHAR(500)", "param_desc": "开票客户名称", "path": "data.recordList.invoiceCust_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "details_productsku": {"api_field_name": "details_productsku", "chinese_name": "物料KSU主键", "data_type": "BIGINT", "param_desc": "物料KSU主键", "path": "data.recordList.details_productsku", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "salesOrg": {"api_field_name": "salesOrg", "chinese_name": "销售组织id", "data_type": "NVARCHAR(MAX)", "param_desc": "销售组织id", "path": "salesOrg", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invoiceOrg_name": {"api_field_name": "invoiceOrg_name", "chinese_name": "开票组织名称", "data_type": "NVARCHAR(500)", "param_desc": "开票组织名称", "path": "data.recordList.invoiceOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "tradeRoute_name": {"api_field_name": "tradeRoute_name", "chinese_name": "贸易路径", "data_type": "NVARCHAR(500)", "param_desc": "贸易路径", "path": "data.recordList.tradeRoute_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productsku_cName": {"api_field_name": "productsku_cName", "chinese_name": "物料SKU名称", "data_type": "NVARCHAR(500)", "param_desc": "物料SKU名称", "path": "data.recordList.productsku_cName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vouchdate": {"api_field_name": "vouchdate", "chinese_name": "单据日期 区间格式，2021-05-06|2021-05-06 23:00:00。若传入单个时间如：2021-05-06，则查询该时间之后，到当前时间之间的单据", "data_type": "NVARCHAR(500)", "param_desc": "单据日期 区间格式，2021-05-06|2021-05-06 23:00:00。若传入单个时间如：2021-05-06，则查询该时间之后，到当前时间之间的单据", "path": "vouchdate", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invPriceExchRate": {"api_field_name": "invPriceExchRate", "chinese_name": "计价单位转换率", "data_type": "BIGINT", "param_desc": "计价单位转换率", "path": "data.recordList.invPriceExchRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "currency": {"api_field_name": "currency", "chinese_name": "原币主键", "data_type": "NVARCHAR(500)", "param_desc": "原币主键", "path": "data.recordList.currency", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pubts": {"api_field_name": "pubts", "chinese_name": "时间戳", "data_type": "NVARCHAR(500)", "param_desc": "时间戳", "path": "data.pubts", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "org_name": {"api_field_name": "org_name", "chinese_name": "发货组织名称", "data_type": "NVARCHAR(500)", "param_desc": "发货组织名称", "path": "data.recordList.org_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "cReceiveMobile": {"api_field_name": "cReceiveMobile", "chinese_name": "收货电话", "data_type": "NVARCHAR(500)", "param_desc": "收货电话", "path": "data.recordList.cReceiveMobile", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "createDate": {"api_field_name": "createDate", "chinese_name": "创建日期", "data_type": "NVARCHAR(500)", "param_desc": "创建日期", "path": "data.recordList.createDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "creator": {"api_field_name": "creator", "chinese_name": "创建人", "data_type": "NVARCHAR(500)", "param_desc": "创建人", "path": "data.recordList.creator", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriSum": {"api_field_name": "oriSum", "chinese_name": "合计金额", "data_type": "BIGINT", "param_desc": "合计金额", "path": "data.sumRecordList.oriSum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "exchRateType_name": {"api_field_name": "exchRateType_name", "chinese_name": "汇率类型名称", "data_type": "NVARCHAR(500)", "param_desc": "汇率类型名称", "path": "data.recordList.exchRateType_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "accountOrg": {"api_field_name": "accountOrg", "chinese_name": "会计主体主键", "data_type": "NVARCHAR(500)", "param_desc": "会计主体主键", "path": "data.recordList.accountOrg", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stsalesOutExchangeInfo_d_key": {"api_field_name": "stsalesOutExchangeInfo_d_key", "chinese_name": "逻辑字段冗余", "data_type": "BIGINT", "param_desc": "逻辑字段冗余", "path": "data.recordList.stsalesOutExchangeInfo_d_key", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "cReceiver": {"api_field_name": "cR<PERSON><PERSON>ver", "chinese_name": "收货人", "data_type": "NVARCHAR(500)", "param_desc": "收货人", "path": "data.recordList.cReceiver", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "details_id": {"api_field_name": "details_id", "chinese_name": "子表主键", "data_type": "BIGINT", "param_desc": "子表主键", "path": "data.recordList.details_id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "priceQty": {"api_field_name": "priceQty", "chinese_name": "合计计价数量", "data_type": "BIGINT", "param_desc": "合计计价数量", "path": "data.sumRecordList.priceQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "createTime": {"api_field_name": "createTime", "chinese_name": "创建时间", "data_type": "NVARCHAR(500)", "param_desc": "创建时间", "path": "data.recordList.createTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "taxUnitPriceTag": {"api_field_name": "taxUnitPriceTag", "chinese_name": "价格含税标志", "data_type": "BIT", "param_desc": "价格含税标志", "path": "data.recordList.taxUnitPriceTag", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "details_product": {"api_field_name": "details_product", "chinese_name": "物料主键", "data_type": "BIGINT", "param_desc": "物料主键", "path": "data.recordList.details_product", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "taxNum": {"api_field_name": "taxNum", "chinese_name": "纳税识别号", "data_type": "NVARCHAR(500)", "param_desc": "纳税识别号", "path": "data.recordList.taxNum", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "department_name": {"api_field_name": "department_name", "chinese_name": "部门名称", "data_type": "NVARCHAR(500)", "param_desc": "部门名称", "path": "data.recordList.department_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "operator_name": {"api_field_name": "operator_name", "chinese_name": "业务员名称", "data_type": "NVARCHAR(500)", "param_desc": "业务员名称", "path": "data.recordList.operator_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invoiceAddress": {"api_field_name": "invoiceAddress", "chinese_name": "营业地址", "data_type": "NVARCHAR(500)", "param_desc": "营业地址", "path": "data.recordList.invoiceAddress", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "operator": {"api_field_name": "operator", "chinese_name": "业务员id", "data_type": "NVARCHAR(MAX)", "param_desc": "业务员id", "path": "operator", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bankAccount": {"api_field_name": "bankAccount", "chinese_name": "银行账号", "data_type": "NVARCHAR(500)", "param_desc": "银行账号", "path": "data.recordList.bankAccount", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "subBankName": {"api_field_name": "subBankName", "chinese_name": "开户支行", "data_type": "NVARCHAR(500)", "param_desc": "开户支行", "path": "data.recordList.subBankName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bankName": {"api_field_name": "bankName", "chinese_name": "开户银行", "data_type": "NVARCHAR(500)", "param_desc": "开户银行", "path": "data.recordList.bankName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invoiceTelephone": {"api_field_name": "invoiceTelephone", "chinese_name": "营业电话", "data_type": "NVARCHAR(500)", "param_desc": "营业电话", "path": "data.recordList.invoiceTelephone", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "department": {"api_field_name": "department", "chinese_name": "部门id", "data_type": "NVARCHAR(MAX)", "param_desc": "部门id", "path": "department", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "cust": {"api_field_name": "cust", "chinese_name": "客户id", "data_type": "NVARCHAR(MAX)", "param_desc": "客户id", "path": "cust", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invoiceUpcType": {"api_field_name": "invoiceUpcType", "chinese_name": "发票类型", "data_type": "NVARCHAR(500)", "param_desc": "发票类型", "path": "data.recordList.invoiceUpcType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natMoney": {"api_field_name": "natMoney", "chinese_name": "合计本币无税金额", "data_type": "DECIMAL(18,4)", "param_desc": "合计本币无税金额", "path": "data.sumRecordList.natMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currency_priceDigit": {"api_field_name": "currency_priceDigit", "chinese_name": "币种精度", "data_type": "BIGINT", "param_desc": "币种精度", "path": "data.recordList.currency_priceDigit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invoiceOrg": {"api_field_name": "invoiceOrg", "chinese_name": "开票组织ID", "data_type": "NVARCHAR(MAX)", "param_desc": "开票组织ID", "path": "invoiceOrg", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockUnit_name": {"api_field_name": "stockUnit_name", "chinese_name": "库存单位名称", "data_type": "NVARCHAR(500)", "param_desc": "库存单位名称", "path": "data.recordList.stockUnit_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "collaborationPolineno": {"api_field_name": "collaborationPolineno", "chinese_name": "协同来源单据行号", "data_type": "NVARCHAR(500)", "param_desc": "协同来源单据行号", "path": "data.recordList.collaborationPolineno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bustype_name": {"api_field_name": "bustype_name", "chinese_name": "交易类型名称", "data_type": "NVARCHAR(500)", "param_desc": "交易类型名称", "path": "data.recordList.bustype_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "modifier": {"api_field_name": "modifier", "chinese_name": "修改人", "data_type": "NVARCHAR(500)", "param_desc": "修改人", "path": "data.recordList.modifier", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "firstupcode": {"api_field_name": "firstupcode", "chinese_name": "源头单据编码", "data_type": "NVARCHAR(500)", "param_desc": "源头单据编码", "path": "data.recordList.firstupcode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "source": {"api_field_name": "source", "chinese_name": "来源单据类型", "data_type": "NVARCHAR(500)", "param_desc": "来源单据类型", "path": "data.recordList.source", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natTax": {"api_field_name": "natTax", "chinese_name": "本币税额", "data_type": "DECIMAL(18,4)", "param_desc": "本币税额", "path": "data.recordList.natTax", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "subQty": {"api_field_name": "subQty", "chinese_name": "合计副计量数量", "data_type": "BIGINT", "param_desc": "合计副计量数量", "path": "data.sumRecordList.subQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "taxItems": {"api_field_name": "taxItems", "chinese_name": "税率显示值", "data_type": "NVARCHAR(500)", "param_desc": "税率显示值", "path": "data.recordList.taxItems", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "modifyTime": {"api_field_name": "modifyTime", "chinese_name": "修改时间", "data_type": "NVARCHAR(500)", "param_desc": "修改时间", "path": "data.recordList.modifyTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_cName": {"api_field_name": "product_cName", "chinese_name": "物料id", "data_type": "NVARCHAR(500)", "param_desc": "物料id", "path": "product_cName", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invoiceTitleType": {"api_field_name": "invoiceTitleType", "chinese_name": "发票抬头类型", "data_type": "NVARCHAR(500)", "param_desc": "发票抬头类型", "path": "data.recordList.invoiceTitleType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "receiveContacterPhone": {"api_field_name": "receiveContacterPhone", "chinese_name": "收货人联系电话", "data_type": "NVARCHAR(500)", "param_desc": "收货人联系电话", "path": "data.recordList.receiveContacterPhone", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "modifyInvoiceType": {"api_field_name": "modifyInvoiceType", "chinese_name": "发票类型可改标志", "data_type": "NVARCHAR(500)", "param_desc": "发票类型可改标志", "path": "data.recordList.modifyInvoiceType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natCurrencyName": {"api_field_name": "natCurrencyName", "chinese_name": "本币名称", "data_type": "NVARCHAR(500)", "param_desc": "本币名称", "path": "data.recordList.natCurrencyName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "salesOrg_name": {"api_field_name": "salesOrg_name", "chinese_name": "销售组织名称", "data_type": "NVARCHAR(500)", "param_desc": "销售组织名称", "path": "data.recordList.salesOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "modifyDate": {"api_field_name": "modifyDate", "chinese_name": "修改日期", "data_type": "NVARCHAR(500)", "param_desc": "修改日期", "path": "data.recordList.modifyDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unitName": {"api_field_name": "unitName", "chinese_name": "主计量名称", "data_type": "NVARCHAR(500)", "param_desc": "主计量名称", "path": "data.recordList.unitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "contactName": {"api_field_name": "contactName", "chinese_name": "联系人名称", "data_type": "NVARCHAR(500)", "param_desc": "联系人名称", "path": "data.recordList.contactName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "srcBillNO": {"api_field_name": "srcBillNO", "chinese_name": "来源单据号", "data_type": "NVARCHAR(500)", "param_desc": "来源单据号", "path": "data.recordList.srcBillNO", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriUnitPrice": {"api_field_name": "oriUnitPrice", "chinese_name": "原币无税单价", "data_type": "DECIMAL(18,4)", "param_desc": "原币无税单价", "path": "data.recordList.oriUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "taxCode": {"api_field_name": "taxCode", "chinese_name": "税目编码", "data_type": "NVARCHAR(500)", "param_desc": "税目编码", "path": "data.recordList.taxCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "barCode": {"api_field_name": "barCode", "chinese_name": "单据码", "data_type": "NVARCHAR(500)", "param_desc": "单据码", "path": "data.recordList.barCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unit_name": {"api_field_name": "unit_name", "chinese_name": "主计量名称冗余", "data_type": "BIGINT", "param_desc": "主计量名称冗余", "path": "data.recordList.unit_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "taxRate": {"api_field_name": "taxRate", "chinese_name": "税率", "data_type": "BIGINT", "param_desc": "税率", "path": "data.recordList.taxRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unit": {"api_field_name": "unit", "chinese_name": "库存单位", "data_type": "NVARCHAR(500)", "param_desc": "库存单位", "path": "data.recordList.unit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productsku_cCode": {"api_field_name": "productsku_cCode", "chinese_name": "物料SKU编码", "data_type": "NVARCHAR(500)", "param_desc": "物料SKU编码", "path": "data.recordList.productsku_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_moneyDigit": {"api_field_name": "natCurrency_moneyDigit", "chinese_name": "本币精度", "data_type": "BIGINT", "param_desc": "本币精度", "path": "data.recordList.natCurrency_moneyDigit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "accountOrg_name": {"api_field_name": "accountOrg_name", "chinese_name": "会计主体名称", "data_type": "NVARCHAR(500)", "param_desc": "会计主体名称", "path": "data.recordList.accountOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "taxId": {"api_field_name": "taxId", "chinese_name": "税目编码", "data_type": "NVARCHAR(500)", "param_desc": "税目编码", "path": "data.recordList.taxId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invoiceCust": {"api_field_name": "invoiceCust", "chinese_name": "开票客户id", "data_type": "NVARCHAR(MAX)", "param_desc": "开票客户id", "path": "invoiceCust", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "qty": {"api_field_name": "qty", "chinese_name": "合计数量", "data_type": "BIGINT", "param_desc": "合计数量", "path": "data.sumRecordList.qty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unit_Precision": {"api_field_name": "unit_Precision", "chinese_name": "主计量精度", "data_type": "BIGINT", "param_desc": "主计量精度", "path": "data.recordList.unit_Precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriTaxUnitPrice": {"api_field_name": "oriTaxUnitPrice", "chinese_name": "原币含税单价", "data_type": "BIGINT", "param_desc": "原币含税单价", "path": "data.recordList.oriTaxUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriMoney": {"api_field_name": "oriMoney", "chinese_name": "合计原币无税金额", "data_type": "DECIMAL(18,4)", "param_desc": "合计原币无税金额", "path": "data.sumRecordList.oriMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "contactsPieces": {"api_field_name": "contactsPieces", "chinese_name": "合计应发件量", "data_type": "BIGINT", "param_desc": "合计应发件量", "path": "data.sumRecordList.contactsPieces", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "contactsQuantity": {"api_field_name": "contactsQuantity", "chinese_name": "合计应发数量", "data_type": "BIGINT", "param_desc": "合计应发数量", "path": "data.sumRecordList.contactsQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natUnitPrice": {"api_field_name": "natUnitPrice", "chinese_name": "本币无税单价", "data_type": "DECIMAL(18,4)", "param_desc": "本币无税单价", "path": "data.recordList.natUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "receiveAccountingBasis": {"api_field_name": "receiveAccountingBasis", "chinese_name": "立账开票依据", "data_type": "NVARCHAR(500)", "param_desc": "立账开票依据", "path": "data.recordList.receiveAccountingBasis", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "logistics": {"api_field_name": "logistics", "chinese_name": "物料单号", "data_type": "NVARCHAR(500)", "param_desc": "物料单号", "path": "data.recordList.logistics", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "exchRate": {"api_field_name": "exchRate", "chinese_name": "汇率", "data_type": "BIGINT", "param_desc": "汇率", "path": "data.recordList.exchRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "currencyName": {"api_field_name": "currencyName", "chinese_name": "币种名称", "data_type": "NVARCHAR(500)", "param_desc": "币种名称", "path": "data.recordList.currencyName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "cust_name": {"api_field_name": "cust_name", "chinese_name": "客户名称", "data_type": "NVARCHAR(500)", "param_desc": "客户名称", "path": "data.recordList.cust_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "org": {"api_field_name": "org", "chinese_name": "库存组织主键", "data_type": "NVARCHAR(500)", "param_desc": "库存组织主键", "path": "data.recordList.org", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "priceUOM_name": {"api_field_name": "priceUOM_name", "chinese_name": "计价单位名称", "data_type": "NVARCHAR(500)", "param_desc": "计价单位名称", "path": "data.recordList.priceUOM_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bustype": {"api_field_name": "bustype", "chinese_name": "交易类型主键", "data_type": "NVARCHAR(500)", "param_desc": "交易类型主键", "path": "data.recordList.bustype", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "receiveId": {"api_field_name": "receiveId", "chinese_name": "收货地址主键", "data_type": "BIGINT", "param_desc": "收货地址主键", "path": "data.recordList.receiveId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "upcode": {"api_field_name": "upcode", "chinese_name": "来源单据号", "data_type": "NVARCHAR(500)", "param_desc": "来源单据号", "path": "upcode", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "saleStyle": {"api_field_name": "saleStyle", "chinese_name": "商品售卖类型", "data_type": "NVARCHAR(500)", "param_desc": "商品售卖类型", "path": "data.recordList.saleStyle", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "iLogisticId": {"api_field_name": "iLogisticId", "chinese_name": "物流公司", "data_type": "BIGINT", "param_desc": "物流公司", "path": "data.recordList.iLogisticId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "status_mobile": {"api_field_name": "status_mobile", "chinese_name": "单据状态", "data_type": "BIGINT", "param_desc": "单据状态", "path": "data.recordList.status_mobile", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natTaxUnitPrice": {"api_field_name": "natTaxUnitPrice", "chinese_name": "本币含税单价", "data_type": "BIGINT", "param_desc": "本币含税单价", "path": "data.recordList.natTaxUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_id": {"api_field_name": "out_sys_id", "chinese_name": "外部来源线索", "data_type": "NVARCHAR(500)", "param_desc": "外部来源线索", "path": "data.recordList.out_sys_id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "out_sys_code": {"api_field_name": "out_sys_code", "chinese_name": "外部来源编码", "data_type": "NVARCHAR(500)", "param_desc": "外部来源编码", "path": "data.recordList.out_sys_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "out_sys_version": {"api_field_name": "out_sys_version", "chinese_name": "外部来源版本", "data_type": "NVARCHAR(500)", "param_desc": "外部来源版本", "path": "data.recordList.out_sys_version", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_type": {"api_field_name": "out_sys_type", "chinese_name": "外部来源类型", "data_type": "NVARCHAR(500)", "param_desc": "外部来源类型", "path": "data.recordList.out_sys_type", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_rowno": {"api_field_name": "out_sys_rowno", "chinese_name": "外部来源行号", "data_type": "NVARCHAR(500)", "param_desc": "外部来源行号", "path": "data.recordList.out_sys_rowno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_lineid": {"api_field_name": "out_sys_lineid", "chinese_name": "外部来源行", "data_type": "NVARCHAR(500)", "param_desc": "外部来源行", "path": "data.recordList.out_sys_lineid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "collaborationPocode": {"api_field_name": "collaborationPocode", "chinese_name": "协同来源单据号", "data_type": "NVARCHAR(500)", "param_desc": "协同来源单据号", "path": "data.recordList.collaborationPocode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "collaborationPoid": {"api_field_name": "collaborationPoid", "chinese_name": "协同来源单据id", "data_type": "BIGINT", "param_desc": "协同来源单据id", "path": "data.recordList.collaborationPoid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "collaborationPodetailid": {"api_field_name": "collaborationPodetailid", "chinese_name": "协同来源单据子表id", "data_type": "BIGINT", "param_desc": "协同来源单据子表id", "path": "data.recordList.collaborationPodetailid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "collaborationSource": {"api_field_name": "collaborationSource", "chinese_name": "协同来源类型, 0:无来源、st_purinrecord:采购入库单、1:发货单、2:销售订单、3:退货单、tradeorder:电商订单、refundorder:电商退换货订单、retailvouch:零售单、mallvouch:商城发货单", "data_type": "NVARCHAR(500)", "param_desc": "协同来源类型, 0:无来源、st_purinrecord:采购入库单、1:发货单、2:销售订单、3:退货单、tradeorder:电商订单、refundorder:电商退换货订单、retailvouch:零售单、mallvouch:商城发货单", "path": "data.recordList.collaborationSource", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "salesOutsExtend!coUpcode": {"api_field_name": "salesOutsExtend!coUpcode", "chinese_name": "协同源头单据号", "data_type": "NVARCHAR(500)", "param_desc": "协同源头单据号", "path": "data.recordList.salesOutsExtend!coUpcode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "salesOutsExtend!coSourceid": {"api_field_name": "salesOutsExtend!coSourceid", "chinese_name": "协同源头单据头id", "data_type": "BIGINT", "param_desc": "协同源头单据头id", "path": "data.recordList.salesOutsExtend!coSourceid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "salesOutsExtend!coSourceLineNo": {"api_field_name": "salesOutsExtend!coSourceLineNo", "chinese_name": "协同源头行号", "data_type": "NVARCHAR(500)", "param_desc": "协同源头行号", "path": "data.recordList.salesOutsExtend!coSourceLineNo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "salesOutsExtend!coSourceType": {"api_field_name": "salesOutsExtend!coSourceType", "chinese_name": "协同源头单据类型(upu.st_purchaseorder:采购订单,productionorder.po_subcontract_order:委外订单)", "data_type": "NVARCHAR(500)", "param_desc": "协同源头单据类型(upu.st_purchaseorder:采购订单,productionorder.po_subcontract_order:委外订单)", "path": "data.recordList.salesOutsExtend!coSourceType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "sumRecordList": {"api_field_name": "sumRecordList", "chinese_name": "合计信息", "data_type": "NVARCHAR(MAX)", "param_desc": "合计信息", "path": "data.sumRecordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalPieces": {"api_field_name": "totalPieces", "chinese_name": "合计件数", "data_type": "BIGINT", "param_desc": "合计件数", "path": "data.sumRecordList.totalPieces", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invoiceOriSum": {"api_field_name": "invoiceOriSum", "chinese_name": "合计开票金额", "data_type": "BIGINT", "param_desc": "合计开票金额", "path": "data.sumRecordList.invoiceOriSum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "saleReturnQty": {"api_field_name": "saleReturnQty", "chinese_name": "合计退货数量", "data_type": "BIGINT", "param_desc": "合计退货数量", "path": "data.sumRecordList.saleReturnQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalQuantity": {"api_field_name": "totalQuantity", "chinese_name": "合计数量", "data_type": "BIGINT", "param_desc": "合计数量", "path": "data.sumRecordList.totalQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invoiceQty": {"api_field_name": "invoiceQty", "chinese_name": "合计开票数量", "data_type": "BIGINT", "param_desc": "合计开票数量", "path": "data.sumRecordList.invoiceQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "pageCount": {"api_field_name": "pageCount", "chinese_name": "总页数", "data_type": "BIGINT", "param_desc": "总页数", "path": "data.pageCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "beginPageIndex": {"api_field_name": "beginPageIndex", "chinese_name": "开始页页号", "data_type": "BIGINT", "param_desc": "开始页页号", "path": "data.beginPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "endPageIndex": {"api_field_name": "endPageIndex", "chinese_name": "最终页页号", "data_type": "BIGINT", "param_desc": "最终页页号", "path": "data.endPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isdefault": {"api_field_name": "isdefault", "chinese_name": "该参数可忽略不管", "data_type": "NVARCHAR(500)", "param_desc": "该参数可忽略不管", "path": "isdefault", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockOrg": {"api_field_name": "stockOrg", "chinese_name": "库存组织id", "data_type": "NVARCHAR(MAX)", "param_desc": "库存组织id", "path": "stockOrg", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockMgr": {"api_field_name": "stockMgr", "chinese_name": "库管员id", "data_type": "NVARCHAR(MAX)", "param_desc": "库管员id", "path": "stockMgr", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bustype.name": {"api_field_name": "bustype.name", "chinese_name": "交易类型名称", "data_type": "NVARCHAR(MAX)", "param_desc": "交易类型名称", "path": "bustype.name", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_cName_ManageClass": {"api_field_name": "product_cName_ManageClass", "chinese_name": "物料分类id", "data_type": "NVARCHAR(MAX)", "param_desc": "物料分类id", "path": "product_cName_ManageClass", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isSum": {"api_field_name": "isSum", "chinese_name": "查询表头", "data_type": "BIT", "param_desc": "查询表头", "path": "isSum", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "simpleVOs": {"api_field_name": "simpleVOs", "chinese_name": "扩展查询条件", "data_type": "NVARCHAR(MAX)", "param_desc": "扩展查询条件", "path": "simpleVOs", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "op": {"api_field_name": "op", "chinese_name": "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )", "data_type": "NVARCHAR(500)", "param_desc": "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )", "path": "simpleVOs.op", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value2": {"api_field_name": "value2", "chinese_name": "值2(条件)如：\"2021-04-19 23:59:59\"", "data_type": "NVARCHAR(500)", "param_desc": "值2(条件)如：\"2021-04-19 23:59:59\"", "path": "simpleVOs.value2", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value1": {"api_field_name": "value1", "chinese_name": "值1(条件)如： \"2021-04-19 00:00:00\"", "data_type": "NVARCHAR(500)", "param_desc": "值1(条件)如： \"2021-04-19 00:00:00\"", "path": "simpleVOs.value1", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "field": {"api_field_name": "field", "chinese_name": "属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码details.product.cCode、表头自定义项headItem.define1、表体自定义项details.bodyItem.define1等)", "data_type": "NVARCHAR(500)", "param_desc": "属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码details.product.cCode、表头自定义项headItem.define1、表体自定义项details.bodyItem.define1等)", "path": "simpleVOs.field", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}}}