采购入库列表查询
发布时间:2024-09-28 01:58:18
采购入库列表查询

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	动态域名，获取方式详见 获取租户所在数据中心域名
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/purinrecord/list
请求方式	POST
ContentType	application/json
应用场景	开放API
API类别	
事务和幂等性	无
限流次数	60次/分钟

多语	不支持
2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
pageIndex	int	否	是	页号    默认值: 1
code	string	否	否	单据编号
pageSize	int	否	是	每页行数    默认值: 10
bustype_name	string	否	否	交易类型，需传入交易类型id
warehouse_name	string	否	否	仓库，需传入仓库名称
vendor_name	string	否	否	供应商，需传入供应商名称
org_id	string	是	否	库存组织id
org_name	string	否	否	库存组织名称
org_code	string	是	否	库存组织编码
purchaseOrg_name	string	是	否	采购组织，需传入采购组织id
inInvoiceOrg_name	string	是	否	收票组织，需传入收票组织id
stockMgr_name	string	是	否	库管员，需传入库管员id
operator_name	string	是	否	业务员，需传入业务员id
department_name	string	是	否	部门，需传部门id
project_name	string	是	否	项目，需传入项目id
product.productClass.name	long	是	否	物料分类，需传入物料分类id
pocode	string	否	否	源头单据编码
product_cName	long	是	否	物料，需传入物料id
open_vouchdate_begin	string	否	否	开始时间，日期格式：YYYY-MM-DD
open_vouchdate_end	string	否	否	结束时间，日期格式：YYYY-MM-DD
isSum	boolean	否	否	查询表头    示例: false    默认值: false
simpleVOs	object	是	否	扩展查询条件
field	string	否	否	属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码purInRecords.product.cCode、表头自定义项headItem.define1、表体自定义项purInRecords.bodyItem.define1等)
op	string	否	否	比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )
value1	string	否	否	值1(条件)
value2	string	否	否	值2(条件)
3. 请求示例
Url: /yonbip/scm/purinrecord/list?access_token=访问令牌
Body: {
	"pageIndex": 0,
	"code": "",
	"pageSize": 0,
	"bustype_name": "",
	"warehouse_name": "",
	"vendor_name": "",
	"org_id": [
		""
	],
	"org_name": "",
	"org_code": [
		""
	],
	"purchaseOrg_name": [
		""
	],
	"inInvoiceOrg_name": [
		""
	],
	"stockMgr_name": [
		""
	],
	"operator_name": [
		""
	],
	"department_name": [
		""
	],
	"project_name": [
		""
	],
	"product.productClass.name": [
		0
	],
	"pocode": "",
	"product_cName": [
		0
	],
	"open_vouchdate_begin": "",
	"open_vouchdate_end": "",
	"isSum": false,
	"simpleVOs": [
		{
			"field": "",
			"op": "",
			"value1": "",
			"value2": ""
		}
	]
}
4. 返回值参数
名称	类型	数组	描述
code	string	否	返回码，调用成功时返回200
message	string	否	调用失败时的错误信息
data	object	否	调用成功时的返回数据
pageIndex	int	否	当前页数
pageSize	int	否	当前页数
pageCount	int	否	页面数
beginPageIndex	int	否	开始页码（第一页）
endPageIndex	int	否	结束页码（有多少页）
recordCount	int	否	总数
pubts	string	否	时间戳字符串
recordList	object	是	返回结果对象
vouchdate	string	否	单据日期
code	string	否	单据编号
bustype_name	string	否	交易类型名称
vendor_name	string	否	供应商名称
warehouse_name	string	否	仓库名称
vendor_code	string	否	供应商编码
warehouse_code	string	否	仓库编码
stockMgr_name	string	否	库管员名称
status	int	否	单据状态, 0:未提交、1:已提交、
purchaseOrg_name	string	否	采购组织名称
department_name	string	否	部门名称
org_code	string	否	库存组织编码
org_name	string	否	库存组织名称
department_code	string	否	部门编码
operator_name	string	否	经办人名称
totalQuantity	double	否	整单数量
totalPieces	double	否	整单件数
inInvoiceOrg_name	string	否	收票组织名称
inInvoiceOrg	string	否	收票组织id
accountOrg	string	否	会计主体
isBeginning	boolean	否	是否期初, true:是、false:否、
bustype	string	否	业务类型id
vendor	long	否	供应商id
contact	string	否	联系人
warehouse	long	否	仓库id
operator	long	否	经办人id
purchaseOrg	string	否	采购组织IDid
org	string	否	库存组织id
department	string	否	部门id
stockMgr	string	否	库管员IDid
moneysum	double	否	金额
paymentsum	double	否	付款金额
unpaymentsum	double	否	未付款金额
store	string	否	门店id
store_name	string	否	门店名称
custom	long	否	客户id
payor	string	否	付款人id
payor_name	string	否	付款人名称
paytime	string	否	付款时间
paymentstatus	string	否	付款状态, 0:未完成、1:完成、
creator	string	否	创建人
createTime	string	否	创建时间
modifier	string	否	最后修改人
modifyTime	string	否	最后修改时间
auditor	string	否	提交人
auditTime	string	否	提交时间
memo	string	否	备注
id	string	否	主表ID
srcBill	string	否	来源单据id
pubts	string	否	时间戳
tplid	long	否	模板id
exchangestatus	string	否	交换状态
purInRecords_id	string	否	订单行id
product	string	否	物料id
product_cCode	string	否	物料编码
product_cName	string	否	物料名称
productsku	string	否	物料SKUid
tradeRoute_name	string	否	贸易路径
productsku_cCode	string	否	物料sku编码
productsku_cName	string	否	物料sku名称
productClass_code	string	否	物料分类编码
propertiesValue	string	否	规格
batchno	string	否	批次号
invaliddate	string	否	有效期至
producedate	string	否	生产日期
unit	string	否	单位id
qty	double	否	数量
unit_code	string	否	计量单位编码
unit_name	string	否	计量单位名称
subQty	double	否	件数
stockUnit_name	string	否	库存单位
project_code	string	否	项目编码
project_name	string	否	项目名称
oriUnitPrice	double	否	无税单价
oriTaxUnitPrice	double	否	含税单价
oriMoney	double	否	无税金额
oriSum	double	否	含税金额
oriTax	double	否	税额
taxRate	double	否	税率
billqty	double	否	累计开票数量
billSubQty	double	否	累计开票件数
sqty	double	否	累计结算数量
smoney	double	否	累计结算金额
sfee	double	否	累计结算费用
totalBillOriSum	double	否	累计开票含税金额
priceUOM	long	否	计价单位id
priceUOM_Code	string	否	计价单位编码
priceUOM_Name	string	否	计价单位名称
natCurrency_priceDigit	int	否	本币单价精度
natCurrency_moneyDigit	int	否	本币金额精度
currency_priceDigit	int	否	币种单价精度
currency_moneyDigit	int	否	币种金额精度
unit_Precision	int	否	主计量精度
priceUOM_Precision	int	否	计价单位精度
stockUnitId_Precision	int	否	库存单位精度
isGiftProduct	boolean	否	赠品, true:是、false:否、
bmake_st_purinvoice_red	string	否	流程红票
bmake_st_purinvoice	string	否	流程蓝票
bizFlow	string	否	流程ID
bizFlow_version	string	否	版本信息
isFlowCoreBill	string	否	是否流程核心单据
purInRecordDefineCharacter	特征组
st.purinrecord.PurInRecord	否	表头自定义项特征组
CG04	string	否	送货单号（单头）
U9002	string	否	U9采购订单号
id	string	否	特征id,主键,新增时无需填写,修改时必填
purInRecordsDefineCharacter	特征组
st.purinrecord.PurInRecords	否	表体自定义项特征组
CG00025	Decimal	否	未收数量2
CG01	string	否	供应商备注（扩展）
CG02	Decimal	否	检验数
CG03	Decimal	否	合格数
CG05	string	否	送货单号（单身）
WW	Date	否	委外交货日期
XS11	string	否	需求分类号test
XS15	string	否	顾客订单号（订单表体）
id	string	否	特征id,主键,新增时无需填写,修改时必填
purInRecordsCharacteristics	特征组
st.purinrecord.PurInRecords	否	自由项特征组
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
out_sys_id	string	否	外部来源线索
out_sys_code	string	否	外部来源编码
out_sys_version	string	否	外部来源版本
out_sys_type	string	否	外部来源类型
out_sys_rowno	string	否	外部来源行号
out_sys_lineid	string	否	外部来源行
tradeRouteID	long	否	贸易路径id
isEndTrade	short	否	是否末级(0:否,1:是)
tradeRouteLineno	string	否	站点
collaborationPolineno	string	否	协同来源单据行号
coSourceType	string	否	协同源头单据类型(productionorder.po_subcontract_order:委外订单)
coUpcode	string	否	协同源头单据号
coSourceLineNo	string	否	协同源头单据行号
coSourceid	string	否	协同来源单据id
coSourceautoid	long	否	协同来源单据子表id
collaborationPodetailid	long	否	协同来源单据行
collaborationPocode	string	否	协同来源单据号
collaborationPoid	long	否	协同来源单据主表id
collaborationSource	string	否	协同来源单据类型(st_salesout:销售出库)
totalOutStockQuantity	BigDecimal	否	累计销售出库数量
sumRecordList	object	否	合计对象
totalQuantity	double	否	整单数量
totalPieces	double	否	整单件数
subQty	double	否	件数
currency	string	否	币种id
paymentsum	double	否	付款金额
sfee	double	否	累计结算费用
unit_Precision	int	否	主计量精度
currency_priceDigit	int	否	币种单价精度
priceUOM_Precision	int	否	计价单位精度
moneysum	double	否	金额
oriMoney	double	否	无税金额
oriSum	double	否	含税金额
stockUnitId_Precision	int	否	库存单位精度
qty	int	否	数量
purInRecords_unit	long	否	计量单位
natCurrency	string	否	本币币种id
natCurrency_moneyDigit	int	否	本币金额精度
unpaymentsum	double	否	未付款金额
smoney	double	否	累计结算金额
sqty	double	否	累计结算数量
natCurrency_priceDigit	int	否	本币单价精度
purInRecords_stockUnitId	string	否	库存单位
purInRecords_priceUOM	long	否	计价单位
oriTax	double	否	税额
currency_moneyDigit	int	否	币种金额精度
billqty	double	否	累计开票数量
5. 正确返回示例
{
	"code": "",
	"message": "",
	"data": {
		"pageIndex": 0,
		"pageSize": 0,
		"pageCount": 0,
		"beginPageIndex": 0,
		"endPageIndex": 0,
		"recordCount": 0,
		"pubts": "",
		"recordList": [
			{
				"vouchdate": "",
				"code": "",
				"bustype_name": "",
				"vendor_name": "",
				"warehouse_name": "",
				"vendor_code": "",
				"warehouse_code": "",
				"stockMgr_name": "",
				"status": 0,
				"purchaseOrg_name": "",
				"department_name": "",
				"org_code": "",
				"org_name": "",
				"department_code": "",
				"operator_name": "",
				"totalQuantity": 0,
				"totalPieces": 0,
				"inInvoiceOrg_name": "",
				"inInvoiceOrg": "",
				"accountOrg": "",
				"isBeginning": true,
				"bustype": "",
				"vendor": 0,
				"contact": "",
				"warehouse": 0,
				"operator": 0,
				"purchaseOrg": "",
				"org": "",
				"department": "",
				"stockMgr": "",
				"moneysum": 0,
				"paymentsum": 0,
				"unpaymentsum": 0,
				"store": "",
				"store_name": "",
				"custom": 0,
				"payor": "",
				"payor_name": "",
				"paytime": "",
				"paymentstatus": "",
				"creator": "",
				"createTime": "",
				"modifier": "",
				"modifyTime": "",
				"auditor": "",
				"auditTime": "",
				"memo": "",
				"id": "",
				"srcBill": "",
				"pubts": "",
				"tplid": 0,
				"exchangestatus": "",
				"purInRecords_id": "",
				"product": "",
				"product_cCode": "",
				"product_cName": "",
				"productsku": "",
				"tradeRoute_name": "",
				"productsku_cCode": "",
				"productsku_cName": "",
				"productClass_code": "",
				"propertiesValue": "",
				"batchno": "",
				"invaliddate": "",
				"producedate": "",
				"unit": "",
				"qty": 0,
				"unit_code": "",
				"unit_name": "",
				"subQty": 0,
				"stockUnit_name": "",
				"project_code": "",
				"project_name": "",
				"oriUnitPrice": 0,
				"oriTaxUnitPrice": 0,
				"oriMoney": 0,
				"oriSum": 0,
				"oriTax": 0,
				"taxRate": 0,
				"billqty": 0,
				"billSubQty": 0,
				"sqty": 0,
				"smoney": 0,
				"sfee": 0,
				"totalBillOriSum": 0,
				"priceUOM": 0,
				"priceUOM_Code": "",
				"priceUOM_Name": "",
				"natCurrency_priceDigit": 0,
				"natCurrency_moneyDigit": 0,
				"currency_priceDigit": 0,
				"currency_moneyDigit": 0,
				"unit_Precision": 0,
				"priceUOM_Precision": 0,
				"stockUnitId_Precision": 0,
				"isGiftProduct": true,
				"bmake_st_purinvoice_red": "",
				"bmake_st_purinvoice": "",
				"bizFlow": "",
				"bizFlow_version": "",
				"isFlowCoreBill": "",
				"purInRecordDefineCharacter": {
					"CG04": "",
					"U9002": "",
					"id": ""
				},
				"purInRecordsDefineCharacter": {
					"CG00025": 0,
					"CG01": "",
					"CG02": 0,
					"CG03": 0,
					"CG05": "",
					"WW": "",
					"XS11": "",
					"XS15": "",
					"id": ""
				},
				"purInRecordsCharacteristics": {
					"XS15": "",
					"XXX0111": "",
					"id": ""
				},
				"out_sys_id": "",
				"out_sys_code": "",
				"out_sys_version": "",
				"out_sys_type": "",
				"out_sys_rowno": "",
				"out_sys_lineid": "",
				"tradeRouteID": 0,
				"isEndTrade": 0,
				"tradeRouteLineno": "",
				"collaborationPolineno": "",
				"coSourceType": "",
				"coUpcode": "",
				"coSourceLineNo": "",
				"coSourceid": "",
				"coSourceautoid": 0,
				"collaborationPodetailid": 0,
				"collaborationPocode": "",
				"collaborationPoid": 0,
				"collaborationSource": "",
				"totalOutStockQuantity": 0
			}
		]
	},
	"sumRecordList": {
		"totalQuantity": 0,
		"totalPieces": 0,
		"subQty": 0,
		"currency": "",
		"paymentsum": 0,
		"sfee": 0,
		"unit_Precision": 0,
		"currency_priceDigit": 0,
		"priceUOM_Precision": 0,
		"moneysum": 0,
		"oriMoney": 0,
		"oriSum": 0,
		"stockUnitId_Precision": 0,
		"qty": 0,
		"purInRecords_unit": 0,
		"natCurrency": "",
		"natCurrency_moneyDigit": 0,
		"unpaymentsum": 0,
		"smoney": 0,
		"sqty": 0,
		"natCurrency_priceDigit": 0,
		"purInRecords_stockUnitId": "",
		"purInRecords_priceUOM": 0,
		"oriTax": 0,
		"currency_moneyDigit": 0,
		"billqty": 0
	}
}