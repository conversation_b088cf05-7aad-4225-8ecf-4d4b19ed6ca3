# 日志指南文档
## YS-API V3 统一日志记录标准

**版本**: 1.0.0  
**更新日期**: 2024年12月  
**适用范围**: YS-API V3项目前后端  

---

## 📋 目录

1. [概述](#概述)
2. [日志级别定义](#日志级别定义)
3. [日志格式标准](#日志格式标准)
4. [日志分类规范](#日志分类规范)
5. [性能影响控制](#性能影响控制)
6. [存储策略](#存储策略)
7. [ELK集成配置](#elk集成配置)
8. [代码实现规范](#代码实现规范)
9. [最佳实践](#最佳实践)

---

## 🎯 概述

本文档定义了YS-API V3项目的统一日志记录标准，解决了之前存在的日志格式不规范、级别不统一、缺少结构化信息等问题。

### 核心目标

- **标准化**: 统一的日志格式和字段规范
- **结构化**: 便于搜索、分析和监控的结构化日志
- **性能优化**: 最小化日志记录对系统性能的影响
- **可追溯性**: 支持分布式系统的请求追踪
- **可扩展性**: 支持ELK、Prometheus等监控系统集成

---

## 📊 日志级别定义

### 级别层次结构

```
FATAL > ERROR > WARN > INFO > DEBUG > TRACE
```

### 详细级别说明

#### 1. FATAL (致命)
**数值**: 5  
**使用场景**: 系统崩溃、数据丢失、安全漏洞  
**处理方式**: 立即告警、人工介入  
**示例**:
```javascript
logger.fatal('数据库连接池耗尽，系统无法服务', {
    type: 'system_failure',
    connectionPool: {
        active: 100,
        idle: 0,
        max: 100
    }
});
```

#### 2. ERROR (错误)
**数值**: 4  
**使用场景**: API调用失败、业务逻辑错误、异常处理  
**处理方式**: 错误通知、自动重试  
**示例**:
```javascript
logger.error('配置保存失败', {
    type: 'business_error',
    operation: 'config_save',
    userId: 'user123',
    configId: 'config456',
    errorCode: 'VAL_REQUIRED_MISSING'
}, error);
```

#### 3. WARN (警告)
**数值**: 3  
**使用场景**: 性能问题、弃用功能、资源不足  
**处理方式**: 监控告警、性能优化  
**示例**:
```javascript
logger.warn('API响应时间过长', {
    type: 'performance_warning',
    endpoint: '/api/data',
    responseTime: 2500,
    threshold: 2000
});
```

#### 4. INFO (信息)
**数值**: 2  
**使用场景**: 业务流程、用户操作、系统状态  
**处理方式**: 业务分析、行为追踪  
**示例**:
```javascript
logger.info('用户登录成功', {
    type: 'user_action',
    userId: 'user123',
    loginMethod: 'password',
    clientIP: '*************'
});
```

#### 5. DEBUG (调试)
**数值**: 1  
**使用场景**: 开发调试、详细执行流程  
**处理方式**: 开发环境输出、问题排查  
**示例**:
```javascript
logger.debug('字段验证开始', {
    type: 'validation_debug',
    fieldName: 'email',
    value: '<EMAIL>',
    rules: ['required', 'email_format']
});
```

#### 6. TRACE (跟踪)
**数值**: 0  
**使用场景**: 最详细的执行路径、方法进入退出  
**处理方式**: 性能分析、深度调试  
**示例**:
```javascript
logger.trace('进入方法: validateField', {
    type: 'method_trace',
    method: 'validateField',
    parameters: { fieldName: 'email', value: '<EMAIL>' }
});
```

---

## 📝 日志格式标准

### 基础日志结构

```json
{
    "timestamp": "2024-12-15T10:30:00.123Z",
    "level": "ERROR",
    "message": "API调用失败",
    "logger": "ys-api-frontend",
    "sessionId": "session_12345",
    "requestId": "req_67890",
    "context": {
        "type": "api_error",
        "module": "field-config",
        "operation": "save"
    },
    "error": {
        "name": "APIError",
        "message": "HTTP 500: Internal Server Error",
        "stack": "APIError: HTTP 500...",
        "code": "API_INTERNAL_ERROR"
    },
    "performance": {
        "duration": 1200,
        "memory": 45.6,
        "retryCount": 2
    },
    "user": {
        "id": "user123",
        "role": "admin",
        "ip": "*************"
    },
    "system": {
        "hostname": "web01",
        "version": "3.0.0",
        "environment": "production"
    }
}
```

### 必需字段

| 字段名 | 类型 | 描述 | 示例 |
|-------|------|------|------|
| timestamp | ISO8601 | 日志时间戳 | "2024-12-15T10:30:00.123Z" |
| level | String | 日志级别 | "ERROR" |
| message | String | 日志消息 | "API调用失败" |
| logger | String | 记录器名称 | "ys-api-frontend" |

### 可选字段

| 字段名 | 类型 | 描述 | 使用场景 |
|-------|------|------|---------|
| sessionId | String | 会话ID | 用户会话追踪 |
| requestId | String | 请求ID | 分布式请求追踪 |
| userId | String | 用户ID | 用户行为分析 |
| traceId | String | 链路追踪ID | 微服务调用链 |
| spanId | String | 跨度ID | 服务内部调用 |

---

## 🏷️ 日志分类规范

### 1. 系统日志 (System Logs)
**前缀**: `SYS_`

| 分类 | 描述 | 示例场景 |
|------|------|---------|
| system_startup | 系统启动 | 服务启动、配置加载 |
| system_shutdown | 系统关闭 | 优雅关闭、资源清理 |
| system_health | 系统健康检查 | 健康检查、心跳监控 |
| system_resource | 资源使用 | 内存、CPU、磁盘使用 |

### 2. 业务日志 (Business Logs)
**前缀**: `BIZ_`

| 分类 | 描述 | 示例场景 |
|------|------|---------|
| user_action | 用户操作 | 登录、保存配置、查询数据 |
| data_operation | 数据操作 | CRUD操作、数据同步 |
| workflow | 业务流程 | 订单处理、审批流程 |
| business_rule | 业务规则 | 验证规则、计算逻辑 |

### 3. 技术日志 (Technical Logs)
**前缀**: `TECH_`

| 分类 | 描述 | 示例场景 |
|------|------|---------|
| api_call | API调用 | HTTP请求、响应 |
| database | 数据库操作 | 查询、连接、事务 |
| cache | 缓存操作 | 缓存命中、失效 |
| security | 安全相关 | 认证、授权、审计 |

### 4. 性能日志 (Performance Logs)
**前缀**: `PERF_`

| 分类 | 描述 | 示例场景 |
|------|------|---------|
| response_time | 响应时间 | API响应时间、页面加载时间 |
| throughput | 吞吐量 | 请求处理量、数据传输量 |
| resource_usage | 资源使用 | 内存、CPU、网络使用 |
| slow_operation | 慢操作 | 超时操作、性能瓶颈 |

### 5. 错误日志 (Error Logs)
**前缀**: `ERR_`

| 分类 | 描述 | 示例场景 |
|------|------|---------|
| network_error | 网络错误 | 连接失败、超时 |
| api_error | API错误 | HTTP错误、服务异常 |
| validation_error | 验证错误 | 数据验证失败 |
| system_error | 系统错误 | 内部错误、配置错误 |

---

## ⚡ 性能影响控制

### 日志性能优化策略

#### 1. 异步日志记录
```javascript
// 使用批量异步写入
class AsyncLogger {
    constructor() {
        this.buffer = [];
        this.flushInterval = 1000; // 1秒
        this.batchSize = 100;
        this.startBatchFlush();
    }
    
    log(entry) {
        this.buffer.push(entry);
        if (this.buffer.length >= this.batchSize) {
            this.flush();
        }
    }
    
    flush() {
        if (this.buffer.length > 0) {
            const batch = this.buffer.splice(0);
            this.sendBatch(batch);
        }
    }
}
```

#### 2. 日志级别过滤
```javascript
// 基于环境的级别控制
const LOG_LEVELS = {
    production: 'INFO',
    staging: 'DEBUG',
    development: 'TRACE'
};

const currentLevel = LOG_LEVELS[process.env.NODE_ENV] || 'INFO';
logger.setLevel(currentLevel);
```

#### 3. 智能采样
```javascript
// 高频日志采样
class SamplingLogger {
    constructor(sampleRate = 0.1) {
        this.sampleRate = sampleRate;
        this.counters = new Map();
    }
    
    shouldLog(type) {
        // 错误日志始终记录
        if (type.includes('error')) return true;
        
        // 高频日志进行采样
        if (type.includes('high_frequency')) {
            return Math.random() < this.sampleRate;
        }
        
        return true;
    }
}
```

### 性能基准

| 操作 | 目标性能 | 监控指标 |
|------|---------|---------|
| 同步日志写入 | < 1ms | 每次写入耗时 |
| 异步日志批量 | < 10ms/批 | 批量处理耗时 |
| 内存使用 | < 50MB | 日志缓冲区大小 |
| 网络传输 | < 100ms | ELK发送延迟 |

---

## 💾 存储策略

### 1. 本地存储
```javascript
// localStorage配置
const localStorageConfig = {
    maxSize: 1000,        // 最大条数
    maxAge: 24 * 60 * 60 * 1000,  // 24小时
    compression: true,    // 启用压缩
    encryption: false     // 生产环境启用
};
```

### 2. 远程存储
```javascript
// ElasticSearch配置
const elasticConfig = {
    url: 'http://localhost:9200',
    index: 'ys-api-logs',
    type: '_doc',
    maxRetries: 3,
    timeout: 5000
};
```

### 3. 日志轮转
```yaml
# 日志轮转策略
rotation:
  daily: true           # 按天轮转
  maxSize: 100MB       # 最大文件大小
  maxFiles: 30         # 保留30个文件
  compress: true       # 压缩旧文件
```

### 4. 存储层次
```
1. 实时日志 → 内存缓冲区 (1MB)
2. 近期日志 → 本地存储 (100MB)
3. 历史日志 → ElasticSearch (1TB)
4. 归档日志 → 冷存储 (无限制)
```

---

## 🔌 ELK集成配置

### ElasticSearch索引配置

#### 索引模板
```json
{
    "index_patterns": ["ys-api-logs-*"],
    "template": {
        "settings": {
            "number_of_shards": 1,
            "number_of_replicas": 1,
            "index.lifecycle.name": "ys-api-logs-policy"
        },
        "mappings": {
            "properties": {
                "@timestamp": { "type": "date" },
                "level": { "type": "keyword" },
                "message": { "type": "text" },
                "logger": { "type": "keyword" },
                "context.type": { "type": "keyword" },
                "error.code": { "type": "keyword" },
                "performance.duration": { "type": "float" },
                "user.id": { "type": "keyword" }
            }
        }
    }
}
```

#### 生命周期策略
```json
{
    "policy": {
        "phases": {
            "hot": {
                "actions": {
                    "rollover": {
                        "max_size": "1GB",
                        "max_age": "1d"
                    }
                }
            },
            "warm": {
                "min_age": "7d",
                "actions": {
                    "shrink": {
                        "number_of_shards": 1
                    }
                }
            },
            "cold": {
                "min_age": "30d",
                "actions": {
                    "freeze": {}
                }
            },
            "delete": {
                "min_age": "90d"
            }
        }
    }
}
```

### Logstash配置
```ruby
input {
    http {
        port => 5000
        codec => json
    }
}

filter {
    # 解析时间戳
    date {
        match => [ "timestamp", "ISO8601" ]
        target => "@timestamp"
    }
    
    # 添加地理位置
    if [user][ip] {
        geoip {
            source => "[user][ip]"
            target => "geo"
        }
    }
    
    # 错误级别标记
    if [level] == "ERROR" or [level] == "FATAL" {
        mutate {
            add_tag => [ "error" ]
        }
    }
    
    # 性能标记
    if [performance][duration] and [performance][duration] > 1000 {
        mutate {
            add_tag => [ "slow" ]
        }
    }
}

output {
    elasticsearch {
        hosts => ["localhost:9200"]
        index => "ys-api-logs-%{+YYYY.MM.dd}"
    }
}
```

### Kibana仪表板

#### 日志概览仪表板
- **错误趋势图**: 按时间显示错误数量
- **性能分布图**: 响应时间分布
- **用户活动热图**: 用户操作热力图
- **系统资源图**: CPU、内存使用情况

#### 告警配置
```json
{
    "trigger": {
        "schedule": {
            "interval": "1m"
        }
    },
    "input": {
        "search": {
            "request": {
                "search_type": "query_then_fetch",
                "indices": ["ys-api-logs-*"],
                "body": {
                    "query": {
                        "bool": {
                            "filter": [
                                {"term": {"level": "ERROR"}},
                                {"range": {"@timestamp": {"gte": "now-5m"}}}
                            ]
                        }
                    }
                }
            }
        }
    },
    "condition": {
        "compare": {
            "ctx.payload.hits.total": {
                "gt": 10
            }
        }
    }
}
```

---

## 💻 代码实现规范

### 1. 前端日志记录

#### 基础使用
```javascript
// 导入标准化日志器
import { StandardErrorLogger } from './js/common/standard-error-logger.js';

// 基础日志记录
StandardErrorLogger.info('用户登录', {
    type: 'user_action',
    userId: 'user123',
    method: 'password'
});

// 错误日志记录
StandardErrorLogger.error('API调用失败', {
    type: 'api_error',
    endpoint: '/api/data',
    method: 'GET',
    status: 500
}, error);

// 性能日志记录
StandardErrorLogger.logPerformance('page_load', 1200, {
    page: 'config',
    cached: false
});
```

#### API调用日志
```javascript
async function callAPI(endpoint, options = {}) {
    const requestId = generateRequestId();
    const startTime = performance.now();
    
    // 记录请求开始
    StandardErrorLogger.debug('API请求开始', {
        type: 'api_request_start',
        requestId,
        endpoint,
        method: options.method || 'GET'
    });
    
    try {
        const response = await fetch(endpoint, options);
        const duration = performance.now() - startTime;
        
        // 记录API调用
        StandardErrorLogger.logApiCall(
            options.method || 'GET',
            endpoint,
            options.body,
            duration,
            response
        );
        
        return response;
    } catch (error) {
        const duration = performance.now() - startTime;
        
        // 记录API错误
        StandardErrorLogger.error('API调用失败', {
            type: 'api_error',
            requestId,
            endpoint,
            method: options.method || 'GET',
            duration
        }, error);
        
        throw error;
    }
}
```

#### 用户操作日志
```javascript
function logUserAction(action, target, data = {}) {
    StandardErrorLogger.logUserAction(action, {
        tagName: target.tagName,
        id: target.id,
        className: target.className,
        text: target.textContent?.substring(0, 50)
    }, {
        ...data,
        timestamp: Date.now(),
        url: window.location.href
    });
}

// 使用示例
document.addEventListener('click', (event) => {
    logUserAction('click', event.target, {
        coordinates: { x: event.clientX, y: event.clientY }
    });
});
```

### 2. 后端日志记录（Python示例）

#### 结构化日志配置
```python
import logging
import json
from datetime import datetime

class StructuredLogger:
    def __init__(self, name):
        self.logger = logging.getLogger(name)
        self.setup_handler()
    
    def setup_handler(self):
        handler = logging.StreamHandler()
        formatter = StructuredFormatter()
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def log(self, level, message, context=None, error=None):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': level,
            'message': message,
            'logger': self.logger.name,
            'context': context or {},
        }
        
        if error:
            log_entry['error'] = {
                'type': type(error).__name__,
                'message': str(error),
                'traceback': traceback.format_exc()
            }
        
        self.logger.log(getattr(logging, level), json.dumps(log_entry))

# 使用示例
logger = StructuredLogger('ys-api-backend')

logger.log('INFO', '用户登录成功', {
    'type': 'user_action',
    'user_id': 'user123',
    'ip': '*************'
})
```

### 3. 日志聚合工具

#### 日志收集器
```javascript
class LogAggregator {
    constructor() {
        this.logs = [];
        this.filters = new Map();
        this.startCollection();
    }
    
    addFilter(name, filterFn) {
        this.filters.set(name, filterFn);
    }
    
    collect(logEntry) {
        // 应用过滤器
        for (const [name, filter] of this.filters) {
            if (!filter(logEntry)) {
                return; // 被过滤掉
            }
        }
        
        this.logs.push(logEntry);
        this.processLog(logEntry);
    }
    
    processLog(logEntry) {
        // 实时处理逻辑
        if (logEntry.level === 'ERROR') {
            this.sendAlert(logEntry);
        }
        
        if (logEntry.context?.type === 'performance' && 
            logEntry.context.duration > 2000) {
            this.reportSlowOperation(logEntry);
        }
    }
    
    export(format = 'json') {
        switch (format) {
            case 'csv':
                return this.exportCSV();
            case 'json':
                return JSON.stringify(this.logs, null, 2);
            default:
                return this.logs;
        }
    }
}
```

---

## 🎯 最佳实践

### 1. 日志内容规范

#### ✅ 推荐做法
```javascript
// 详细的上下文信息
logger.error('用户配置保存失败', {
    type: 'business_error',
    operation: 'config_save',
    userId: 'user123',
    configId: 'config456',
    validationErrors: ['required_field_missing'],
    retryCount: 2,
    timeElapsed: 1200
}, error);

// 性能关键路径日志
logger.info('数据库查询完成', {
    type: 'database_operation',
    query: 'SELECT * FROM users WHERE id = ?',
    duration: 156,
    rowsReturned: 1,
    cacheHit: false
});
```

#### ❌ 避免做法
```javascript
// 信息不足
logger.error('保存失败', error);

// 敏感信息泄露
logger.debug('用户登录', {
    username: 'admin',
    password: 'password123'  // ❌ 不要记录密码
});

// 过于频繁的日志
for (let i = 0; i < 1000; i++) {
    logger.debug(`处理第${i}条记录`);  // ❌ 循环中的日志
}
```

### 2. 性能优化

#### 日志级别控制
```javascript
// 基于环境的日志级别
const logLevel = {
    production: 'WARN',
    staging: 'INFO',
    development: 'DEBUG',
    test: 'ERROR'
}[process.env.NODE_ENV] || 'INFO';

logger.setLevel(logLevel);
```

#### 条件日志记录
```javascript
// 只在需要时计算详细信息
if (logger.isDebugEnabled()) {
    logger.debug('详细调试信息', {
        expensiveData: calculateExpensiveData()
    });
}
```

### 3. 安全考虑

#### 敏感信息过滤
```javascript
class SanitizedLogger {
    constructor(baseLogger) {
        this.baseLogger = baseLogger;
        this.sensitiveFields = [
            'password', 'token', 'secret', 'key',
            'creditCard', 'ssn', 'phone'
        ];
    }
    
    sanitize(data) {
        if (typeof data !== 'object') return data;
        
        const sanitized = { ...data };
        
        for (const field of this.sensitiveFields) {
            if (field in sanitized) {
                sanitized[field] = '[REDACTED]';
            }
        }
        
        return sanitized;
    }
    
    log(level, message, context, error) {
        const sanitizedContext = this.sanitize(context);
        this.baseLogger.log(level, message, sanitizedContext, error);
    }
}
```

### 4. 错误关联

#### 请求追踪
```javascript
// 生成追踪ID
function generateTraceId() {
    return 'trace_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 在请求开始时设置
const traceId = generateTraceId();
logger.setContext({ traceId });

// 所有相关日志自动包含追踪ID
logger.info('处理开始', { operation: 'save_config' });
logger.error('验证失败', { field: 'email' });
logger.info('处理完成', { success: false });
```

### 5. 监控集成

#### 指标提取
```javascript
class MetricsLogger {
    constructor() {
        this.metrics = new Map();
    }
    
    logWithMetrics(level, message, context) {
        // 记录常规日志
        logger.log(level, message, context);
        
        // 提取指标
        if (context.type === 'api_call') {
            this.updateMetric('api_calls_total', 1, {
                endpoint: context.endpoint,
                status: context.status
            });
            
            this.updateMetric('api_duration_seconds', context.duration / 1000, {
                endpoint: context.endpoint
            });
        }
        
        if (level === 'ERROR') {
            this.updateMetric('errors_total', 1, {
                type: context.type,
                code: context.errorCode
            });
        }
    }
    
    updateMetric(name, value, labels = {}) {
        const key = `${name}_${JSON.stringify(labels)}`;
        if (!this.metrics.has(key)) {
            this.metrics.set(key, { name, labels, value: 0, count: 0 });
        }
        
        const metric = this.metrics.get(key);
        metric.value += value;
        metric.count += 1;
    }
}
```

---

## 🔍 故障排查

### 常见问题解决

#### 1. 日志丢失
**症状**: 部分日志没有被记录  
**排查步骤**:
```javascript
// 检查日志级别
console.log('当前日志级别:', logger.getLevel());

// 检查缓冲区状态
console.log('缓冲区大小:', logger.getBufferSize());

// 检查发送状态
logger.getStatus().then(status => {
    console.log('发送状态:', status);
});
```

#### 2. 性能影响
**症状**: 日志记录导致应用变慢  
**优化方案**:
```javascript
// 启用异步模式
logger.configure({
    async: true,
    batchSize: 100,
    flushInterval: 5000
});

// 降低日志级别
logger.setLevel('WARN');

// 启用采样
logger.configure({
    sampling: {
        rate: 0.1,
        include: ['ERROR', 'FATAL']
    }
});
```

#### 3. ELK集成问题
**症状**: 日志无法发送到ElasticSearch  
**排查清单**:
- [ ] 检查网络连接
- [ ] 验证ElasticSearch状态
- [ ] 检查索引配置
- [ ] 验证字段映射
- [ ] 查看Logstash日志

---

## 📈 持续改进

### 日志质量评估

#### 评估指标
| 指标 | 目标值 | 监控方法 |
|------|-------|---------|
| 日志完整性 | > 99% | 对比业务操作和日志记录 |
| 结构化率 | > 95% | 检查JSON格式有效性 |
| 性能影响 | < 5% | 对比启用/禁用日志的性能 |
| 存储效率 | < 100GB/月 | 监控存储使用量 |

#### 改进流程
1. **每周Review**: 分析日志质量和使用情况
2. **月度优化**: 调整日志级别和存储策略
3. **季度升级**: 更新ELK组件和配置

---

## 🔗 相关文档

- [错误处理规范](./错误处理规范.md)
- [性能监控指南](./performance-monitoring.md)
- [ELK部署文档](../deployment/elk-setup.md)
- [Prometheus配置](../config/monitoring/prometheus.yml)

---

**文档维护者**: YS-API V3 开发团队  
**最后更新**: 2024年12月  
**下次Review**: 2024年12月（每周）
