#!/bin/bash
# YS-API V3.0 部署脚本

set -e

echo "🚀 Starting YS-API V3.0 Deployment..."

# 配置变量
SERVER_USER=${DEPLOY_USER:-deploy}
SERVER_HOST=${DEPLOY_HOST:-localhost}
APP_DIR=${APP_DIR:-/opt/ysapi}
BACKUP_DIR=${BACKUP_DIR:-/opt/ysapi-backups}

# 创建备份
echo "📦 Creating backup..."
ssh ${SERVER_USER}@${SERVER_HOST} "
    sudo mkdir -p ${BACKUP_DIR}
    if [ -d ${APP_DIR} ]; then
        sudo cp -r ${APP_DIR} ${BACKUP_DIR}/ysapi-$(date +%Y%m%d-%H%M%S)
    fi
"

# 停止应用
echo "⏹️ Stopping application..."
ssh ${SERVER_USER}@${SERVER_HOST} "
    cd ${APP_DIR}
    sudo docker-compose down || true
    sudo systemctl stop ysapi || true
"

# 部署新版本
echo "📋 Deploying new version..."
rsync -avz --exclude='.git' --exclude='logs/' . ${SERVER_USER}@${SERVER_HOST}:${APP_DIR}/

# 启动应用
echo "🔄 Starting application..."
ssh ${SERVER_USER}@${SERVER_HOST} "
    cd ${APP_DIR}
    sudo docker-compose up -d
"

# 健康检查
echo "🏥 Health check..."
sleep 30
if curl -f http://${SERVER_HOST}:8000/health; then
    echo "✅ Deployment successful!"
else
    echo "❌ Deployment failed!"
    exit 1
fi

echo "🎉 Deployment completed!"
