import sys
import time

import requests

#!/usr/bin/env python3
"""
MVP健康检查脚本
测试核心API端点是否正常响应
"""


def test_mvp_health():
    """测试MVP健康状态"""
    base_url = "http://127.0.0.1:8001"

    print("🔍 正在测试MVP健康状态...")

    # 测试的端点列表
    endpoints = [
        "/health",
        "/",
        "/api/v1/health",
    ]

    results = {}

    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        try:
            print(f"📡 测试端点: {url}")
            response = requests.get(url, timeout=5)
            results[endpoint] = {
                "status_code": response.status_code,
                "success": 200 <= response.status_code < 300,
                "response_time": response.elapsed.total_seconds(),
                "error": None,
            }
            if results[endpoint]["success"]:
                print(f"✅ {endpoint}: {response.status_code} - OK")
            else:
                print(f"⚠️ {endpoint}: {response.status_code} - 非200状态码")

        except requests.exceptions.ConnectionError:
            results[endpoint] = {
                "status_code": None,
                "success": False,
                "response_time": None,
                "error": "连接失败 - 服务器可能未启动",
            }
            print(f"❌ {endpoint}: 连接失败")
        except requests.exceptions.Timeout:
            results[endpoint] = {
                "status_code": None,
                "success": False,
                "response_time": None,
                "error": "请求超时",
            }
            print(f"⏰ {endpoint}: 请求超时")
        except Exception:
            results[endpoint] = {
                "status_code": None,
                "success": False,
                "response_time": None,
                "error": str(e),
            }
            print(f"💥 {endpoint}: {e}")

    # 统计结果
    successful_endpoints = sum(1 for r in results.values() if r["success"])
    total_endpoints = len(endpoints)
    success_rate = (successful_endpoints / total_endpoints) * 100

    print("\n" + "=" * 50)
    print("📊 健康检查结果")
    print("=" * 50)
    print(f"✅ 成功端点: {successful_endpoints}/{total_endpoints}")
    print(f"📈 成功率: {success_rate:.1f}%")

    if success_rate >= 70:
        print("🎉 MVP健康检查通过！")
        return True
    else:
        print("⚠️ MVP健康检查未达标（需要≥70%成功率）")
        return False


def wait_for_server(max_wait=30):
    """等待服务器启动"""
    print(f"⏳ 等待服务器启动（最多{max_wait}秒）...")

    for i in range(max_wait):
        try:
            response = requests.get("http://127.0.0.1:8001/health", timeout=2)
            if response.status_code == 200:
                print("✅ 服务器已启动！")
                return True
        except Exception:
            pass
        time.sleep(1)
        if i % 5 == 0:
            print(f"⏳ 继续等待... ({i}/{max_wait})")

    print("❌ 服务器启动超时")
    return False


if __name__ == "__main__":
    print("🚀 开始MVP健康检查")

    # 首先等待服务器启动
    if not wait_for_server():
        print("💡 提示：请先在另一个终端中运行 'python main.py' 启动服务器")
        sys.exit(1)

    # 执行健康检查
    success = test_mvp_health()

    if success:
        print("\n🎯 MVP验证完成 - 可以继续Day 3任务")
        sys.exit(0)
    else:
        print("\n🔧 需要修复MVP问题后再继续")
        sys.exit(1)
