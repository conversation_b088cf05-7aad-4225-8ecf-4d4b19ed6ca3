{"timestamp": "2025-08-06T03:35:42.346842", "scan_results": {"test_files": {"unit_tests": [], "integration_tests": [], "debug_tests": [], "temp_tests": ["d:\\OneDrive\\Desktop\\YS-API程序\\v3\\test_day4_proxy.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\test_mvp.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\frontend_test_server.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\production_test_runner.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\test_modules_direct.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\test_backend_automation.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\test_server.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\tests\\test_baseline_api.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\tests\\test_rollback_scripts.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\frontend\\js\\common\\test-data.js", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\module_functional_test.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\frontend\\migrated\\path-test.html", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\tests\\simple_test.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\tests\\test_md_to_json_converter.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\cleanup_test_code.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\test_health_checker.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\real_data_full_test.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\setup_test_env.py"]}, "mock_files": {"mock_data": [], "mock_modules": ["d:\\OneDrive\\Desktop\\YS-API程序\\v3\\dev-tools\\mock\\mock_utils.py"], "dummy_files": []}, "hardcoded_data": {"python_files": [["automation_validator.py", 190, "\"test_rounds\": test_rounds"], ["automation_validator.py", 153, "test_endpoint = \"/health\""], ["automation_validator.py", 154, "test_rounds = 5"], ["automation_validator.py", 161, "response = requests.get(f\"{self.proxy_base}{test_endpoint}\", timeout=10)"], ["automation_validator.py", 173, "response = requests.get(f\"{self.proxy_base}{test_endpoint}?version=new\", timeout=10)"], ["automation_validator.py", 205, "test_results = ["], ["auto_project_cleanup.py", 89, "test_patterns = ["], ["build_production_package.py", 75, "\"test_*\","], ["check_naming_conflicts.py", 16, "'test_files': re.compile(r'test_.*\\.py$'),"], ["check_naming_conflicts.py", 16, "'test_files': re.compile(r'test_.*\\.py$'),"], ["final_batch.py", 31, "\"test_passed\","], ["final_batch.py", 32, "\"test_files_deleted\","], ["production_readiness_report.py", 193, "f.write(f\"test_line_{i}\\n\")"], ["production_readiness_report.py", 32, "'test_info': {"], ["production_readiness_report.py", 34, "'test_date': start_time.strftime('%Y-%m-%d'),"], ["production_readiness_report.py", 35, "'test_time': start_time.strftime('%H:%M:%S'),"], ["production_readiness_report.py", 36, "'test_type': 'Production Readiness Assessment',"], ["production_readiness_report.py", 37, "'test_version': '1.0.0',"], ["production_readiness_report.py", 462, "'test_completion_time': end_time.isoformat(),"], ["production_readiness_report.py", 463, "'test_duration': str(duration).split('.')[0],"], ["production_readiness_report.py", 542, "- **项目名称**: {report_data['test_info']['project_name']}"], ["production_readiness_report.py", 543, "- **评估日期**: {report_data['test_info']['test_date']}"], ["production_readiness_report.py", 543, "- **评估日期**: {report_data['test_info']['test_date']}"], ["production_readiness_report.py", 544, "- **评估时间**: {report_data['test_info']['test_time']}"], ["production_readiness_report.py", 544, "- **评估时间**: {report_data['test_info']['test_time']}"], ["production_readiness_report.py", 545, "- **评估类型**: {report_data['test_info']['test_type']}"], ["production_readiness_report.py", 545, "- **评估类型**: {report_data['test_info']['test_type']}"], ["production_readiness_report.py", 546, "- **评估耗时**: {overall['test_duration']}"], ["production_readiness_report.py", 690, "*评估工具版本: {report_data['test_info']['test_version']}*"], ["production_readiness_report.py", 690, "*评估工具版本: {report_data['test_info']['test_version']}*"], ["production_readiness_report.py", 187, "test_file = Path(\"performance_test.tmp\")"], ["setup_test_env.py", 34, "DEBUG_MODE=false"], ["setup_test_env.py", 66, "cursor.execute(\"INSERT INTO test_data (name) VALUES (?)\", (\"test_entry\",))"], ["smart_file_creator.py", 36, "'test_runner': {"], ["smart_file_creator.py", 38, "'suggested_name': 'test_runner.py',"], ["systematic_duplicate_detector.py", 178, "temp_patterns = ['test_*.py', '*_test_*.py', 'debug_*.py', 'temp_*.py']"], ["test_day4_proxy.py", 106, "\"test_type\": \"Day4_proxy_test\","], ["test_day4_proxy.py", 34, "test_endpoint = \"/api/v1/health\""], ["test_day4_proxy.py", 39, "response = requests.get(f\"http://127.0.0.1:9000{test_endpoint}\", timeout=10)"], ["test_day4_proxy.py", 49, "response = requests.get(f\"http://127.0.0.1:9000{test_endpoint}?version=new\", timeout=10)"], ["test_day4_proxy.py", 105, "test_result = {"], ["test_day4_proxy.py", 118, "json.dump(test_result, f, indent=2, ensure_ascii=False)"], ["test_day4_proxy.py", 162, "results[test_name] = result"], ["test_day4_proxy.py", 165, "results[test_name] = False"], ["validate_month3_week1.py", 52, "test_modules = [\"purchase_order\", \"sales_order\", \"inventory\"]"], ["verify_database_enhancement.py", 67, "mock_db = MockDatabaseManager()"], ["verify_module.py", 65, "self.results[\"steps\"][\"test_pass\"] = {"], ["verify_module.py", 73, "self.results[\"steps\"][\"test_pass\"] = {"], ["verify_module.py", 85, "f\"test_{self.module_name}*.py\","], ["verify_module.py", 65, "self.results[\"steps\"][\"test_pass\"] = {"], ["verify_module.py", 73, "self.results[\"steps\"][\"test_pass\"] = {"], ["verify_module.py", 84, "test_patterns = ["], ["verify_module.py", 92, "test_files = list(Path(\".\").glob(f\"**/{pattern}\"))"], ["verify_module.py", 104, "self.results[\"steps\"][\"delete_test_files\"] = {"], ["verify_module.py", 115, "mock_patterns = ["], ["verify_module.py", 123, "mock_files = list(Path(\".\").glob(f\"**/{pattern}\"))"], ["verify_module.py", 135, "self.results[\"steps\"][\"delete_mock_data\"] = {"], ["backend\\app\\main_original.py", 294, "test_html_path = project_root / \"test_frontend.html\""], ["backend\\app\\main_original.py", 294, "test_html_path = project_root / \"test_frontend.html\""], ["backend\\app\\services\\database_manager.py", 40, "'test_field',"], ["backend\\app\\services\\database_manager.py", 453, "test_engine = create_engine("], ["backend\\app\\services\\integrated_ys_api_client.py", 471, "test_params = {'pageSize': 10, 'pageNum': 1}"], ["backend\\app\\services\\week3_validation_suite.py", 35, "self.test_data = self._generate_test_data()"], ["backend\\app\\services\\week3_validation_suite.py", 114, "'test_results': {},"], ["backend\\app\\services\\week3_validation_suite.py", 123, "validation_results['test_results']['data_transformer'] = transformer_results"], ["backend\\app\\services\\week3_validation_suite.py", 127, "validation_results['test_results']['format_standardizer'] = standardizer_results"], ["backend\\app\\services\\week3_validation_suite.py", 131, "validation_results['test_results']['quality_inspector'] = inspector_results"], ["backend\\app\\services\\week3_validation_suite.py", 135, "validation_results['test_results']['response_processor'] = processor_results"], ["backend\\app\\services\\week3_validation_suite.py", 139, "validation_results['test_results']['integration'] = integration_results"], ["backend\\app\\services\\week3_validation_suite.py", 150, "all_test_results = validation_results['test_results'].values()"], ["backend\\app\\services\\week3_validation_suite.py", 229, "test_data_with_types, 'test_module', {}"], ["backend\\app\\services\\week3_validation_suite.py", 366, "problem_data, 'test_module', {}"], ["backend\\app\\services\\week3_validation_suite.py", 527, "invalid_data, 'test_module', {}"], ["backend\\app\\services\\week3_validation_suite.py", 691, "test_results = validation_results.get('test_results', {})"], ["backend\\app\\services\\week3_validation_suite.py", 25, "self.test_results = []"], ["backend\\app\\services\\week3_validation_suite.py", 35, "self.test_data = self._generate_test_data()"], ["backend\\app\\services\\week3_validation_suite.py", 123, "validation_results['test_results']['data_transformer'] = transformer_results"], ["backend\\app\\services\\week3_validation_suite.py", 127, "validation_results['test_results']['format_standardizer'] = standardizer_results"], ["backend\\app\\services\\week3_validation_suite.py", 131, "validation_results['test_results']['quality_inspector'] = inspector_results"], ["backend\\app\\services\\week3_validation_suite.py", 135, "validation_results['test_results']['response_processor'] = processor_results"], ["backend\\app\\services\\week3_validation_suite.py", 139, "validation_results['test_results']['integration'] = integration_results"], ["backend\\app\\services\\week3_validation_suite.py", 150, "all_test_results = validation_results['test_results'].values()"], ["backend\\app\\services\\week3_validation_suite.py", 221, "test_data_with_types = {"], ["backend\\app\\services\\week3_validation_suite.py", 691, "test_results = validation_results.get('test_results', {})"], ["core\\app\\main_original.py", 294, "test_html_path = project_root / \"test_frontend.html\""], ["core\\app\\main_original.py", 294, "test_html_path = project_root / \"test_frontend.html\""], ["core\\app\\services\\database_manager.py", 40, "'test_field',"], ["core\\app\\services\\database_manager.py", 453, "test_engine = create_engine("], ["dev-tools\\mock\\mock_utils.py", 60, "\"module_name\": \"test_module\","], ["dev-tools\\mock\\mock_utils.py", 69, "\"user_id\": \"test_user\","], ["dev-tools\\mock\\mock_utils.py", 70, "\"module_name\": \"test_module\","], ["dev-tools\\mock\\mock_utils.py", 259, "\"field_config\", module_name=\"test_module\", field_count=10"], ["dev-tools\\mock\\mock_utils.py", 259, "\"field_config\", module_name=\"test_module\", field_count=10"], ["dev-tools\\mock\\mock_utils.py", 37, "def __init__(self, mock_data_dir: str = None):"], ["dev-tools\\mock\\mock_utils.py", 45, "mock_data_dir = Path(__file__).parent / \"mock_data\""], ["dev-tools\\mock\\mock_utils.py", 47, "self.mock_data_dir = Path(mock_data_dir)"], ["dev-tools\\mock\\mock_utils.py", 48, "self.mock_data_dir.mkdir(exist_ok=True)"], ["dev-tools\\mock\\mock_utils.py", 82, "def mock_baseline_save_api(self, success: bool = True) -> MockResponse:"], ["dev-tools\\mock\\mock_utils.py", 89, "def mock_user_config_save_api(self, success: bool = True) -> MockResponse:"], ["dev-tools\\mock\\mock_utils.py", 100, "mock_fields = {}"], ["dev-tools\\mock\\mock_utils.py", 103, "mock_fields[field_name] = {"], ["dev-tools\\mock\\mock_utils.py", 169, "self.is_mock_mode = self._detect_mock_mode()"], ["dev-tools\\mock\\mock_utils.py", 170, "self.mock_client = MockAPIClient() if self.is_mock_mode else None"], ["dev-tools\\mock\\mock_utils.py", 200, "mock_func = endpoint_mapping.get(endpoint)"], ["month2_config\\config_rollback\\manager.py", 104, "latest_version = self.version_history[module_name][-1]"], ["month2_config\\config_rollback\\manager.py", 105, "latest_config = self._load_config_by_version(module_name, latest_version[\"version_id\"])"], ["scripts\\auto_migration_pipeline.py", 321, "\"test_passed\","], ["scripts\\auto_migration_pipeline.py", 322, "\"test_files_deleted\","], ["scripts\\auto_migration_pipeline.py", 305, "test_file = f\"tests/module_migration/test_{module_name.replace(' ', '_').lower()}_migration.py\""], ["scripts\\batch_initialize_next.py", 99, "test_path = test_dir / f\"test_{module_name.replace(' ', '_').lower()}_migration.py\""], ["scripts\\batch_initialize_next.py", 96, "test_dir = Path(\"tests/module_migration\")"], ["scripts\\batch_initialize_next.py", 97, "test_dir.mkdir(parents=True, exist_ok=True)"], ["scripts\\batch_initialize_next.py", 99, "test_path = test_dir / f\"test_{module_name.replace(' ', '_').lower()}_migration.py\""], ["scripts\\batch_initialize_next.py", 101, "test_content = f'''#!/usr/bin/env python3"], ["scripts\\batch_initialize_next.py", 136, "with open(test_path, \"w\", encoding=\"utf-8\") as f:"], ["scripts\\batch_initialize_next.py", 156, "test_path = create_module_test(module_name)"], ["scripts\\batch_init_modules.py", 73, "\"test_passed\","], ["scripts\\cleanup_test_code.py", 37, "\"test_*.py\","], ["scripts\\cleanup_test_code.py", 78, "self.scan_results[\"test_files\"] = categorized"], ["scripts\\cleanup_test_code.py", 136, "r'\"test_.*?\"',"], ["scripts\\cleanup_test_code.py", 270, "test_files = self.scan_results.get(\"test_files\", {})"], ["scripts\\cleanup_test_code.py", 407, "total_test_files = sum(len(files) for files in self.scan_results.get(\"test_files\", {}).values())"], ["scripts\\cleanup_test_code.py", 447, "report_file = self.project_root / \"reports\" / \"test_code_cleanup_analysis.json\""], ["scripts\\cleanup_test_code.py", 131, "r'test_data\\s*=',"], ["scripts\\cleanup_test_code.py", 137, "r\"'test_.*?'\","], ["scripts\\cleanup_test_code.py", 138, "r'TEST_.*\\s*=',"], ["scripts\\cleanup_test_code.py", 33, "test_patterns = ["], ["scripts\\cleanup_test_code.py", 42, "test_files = []"], ["scripts\\cleanup_test_code.py", 48, "test_files = list(set(test_files))"], ["scripts\\cleanup_test_code.py", 78, "self.scan_results[\"test_files\"] = categorized"], ["scripts\\cleanup_test_code.py", 130, "test_data_patterns = ["], ["scripts\\cleanup_test_code.py", 131, "r'test_data\\s*=',"], ["scripts\\cleanup_test_code.py", 138, "r'TEST_.*\\s*=',"], ["scripts\\cleanup_test_code.py", 270, "test_files = self.scan_results.get(\"test_files\", {})"], ["scripts\\cleanup_test_code.py", 407, "total_test_files = sum(len(files) for files in self.scan_results.get(\"test_files\", {}).values())"], ["scripts\\cleanup_test_code.py", 85, "mock_patterns = ["], ["scripts\\cleanup_test_code.py", 92, "mock_files = []"], ["scripts\\cleanup_test_code.py", 122, "self.scan_results[\"mock_files\"] = categorized"], ["scripts\\cleanup_test_code.py", 133, "r'mock_response\\s*=',"], ["scripts\\cleanup_test_code.py", 139, "r'MOCK_.*\\s*='"], ["scripts\\cleanup_test_code.py", 279, "mock_files = self.scan_results.get(\"mock_files\", {})"], ["scripts\\cleanup_test_code.py", 408, "total_mock_files = sum(len(files) for files in self.scan_results.get(\"mock_files\", {}).values())"], ["scripts\\clean_hardcoded_data.py", 21, "(r'test_data\\s*=\\s*[{\\[].*?[}\\]]', '# 测试数据已移除'),"], ["scripts\\clean_hardcoded_data.py", 24, "(r'TEST_[A-Z_]+\\s*=\\s*[\\'\"][^\\'\\\"]*[\\'\"]', '# 测试常量已移除'),"], ["scripts\\clean_hardcoded_data.py", 20, "test_patterns_to_remove = ["], ["scripts\\clean_hardcoded_data.py", 21, "(r'test_data\\s*=\\s*[{\\[].*?[}\\]]', '# 测试数据已移除'),"], ["scripts\\clean_hardcoded_data.py", 24, "(r'TEST_[A-Z_]+\\s*=\\s*[\\'\"][^\\'\\\"]*[\\'\"]', '# 测试常量已移除'),"], ["scripts\\clean_hardcoded_data.py", 97, "test_files_to_remove = ["], ["scripts\\clean_hardcoded_data.py", 125, "html_test_files = ["], ["scripts\\clean_hardcoded_data.py", 22, "(r'mock_response\\s*=\\s*[{\\[].*?[}\\]]', '# 模拟响应已移除'),"], ["scripts\\complete_modules.py", 13, "\"test_passed\","], ["scripts\\complete_modules.py", 14, "\"test_files_deleted\","], ["scripts\\manual_cleanup.py", 18, "\"test_baseline_api.py\","], ["scripts\\manual_cleanup.py", 20, "\"test_sentinel.py\","], ["scripts\\manual_cleanup.py", 21, "\"test_anti_duplicate_system.py\","], ["scripts\\manual_cleanup.py", 23, "\"test_server.py\","], ["scripts\\manual_cleanup.py", 25, "\"test_day3_core.py\","], ["scripts\\manual_cleanup.py", 26, "\"test_proxy.py\","], ["scripts\\manual_cleanup.py", 27, "\"test_day3_offline.py\","], ["scripts\\manual_cleanup.py", 30, "\"test_real_data_validation.py\""], ["scripts\\migrate_purchase_order.py", 122, "self.project_root / \"tests\" / \"new_system\" / \"test_purchase_order.py\""], ["scripts\\migrate_purchase_order.py", 554, "/ \"test_采购订单列表_migration.py\""], ["scripts\\migrate_purchase_order.py", 619, "\"test_results\": test_results,"], ["scripts\\migrate_purchase_order.py", 120, "test_content = self.generate_test_code()"], ["scripts\\migrate_purchase_order.py", 121, "test_path = ("], ["scripts\\migrate_purchase_order.py", 124, "test_path.parent.mkdir(parents=True, exist_ok=True)"], ["scripts\\migrate_purchase_order.py", 126, "with open(test_path, \"w\", encoding=\"utf-8\") as f:"], ["scripts\\migrate_purchase_order.py", 550, "test_file = ("], ["scripts\\migrate_purchase_order.py", 561, "test_results = {"], ["scripts\\migrate_purchase_order.py", 582, "test_results = {"], ["scripts\\migrate_purchase_order.py", 590, "json.dump(test_results, f, ensure_ascii=False, indent=2)"], ["scripts\\migrate_purchase_order.py", 605, "test_results = self.step4_run_migration_tests()"], ["scripts\\module_functional_test.py", 110, "test_data = {"], ["scripts\\module_functional_test.py", 113, "\"test_mode\": True"], ["scripts\\module_functional_test.py", 21, "self.test_results = {}"], ["scripts\\module_functional_test.py", 59, "def test_module_api(self, module_name: str, endpoint: str, base_url: str = \"http://localhost:8000\") "], ["scripts\\module_functional_test.py", 110, "test_data = {"], ["scripts\\module_functional_test.py", 143, "def test_module_frontend(self, module_name: str, frontend_url: str = \"http://localhost:8080\") -> Dic"], ["scripts\\module_functional_test.py", 236, "def test_single_module(self, module_name: str, endpoint: str = None) -> Dict[str, Any]:"], ["scripts\\module_tracker.py", 51, "\"test_passed\": <PERSON><PERSON><PERSON>,"], ["scripts\\module_tracker.py", 52, "\"test_files_deleted\": <PERSON><PERSON><PERSON>,"], ["scripts\\module_tracker.py", 76, "\"test_passed\","], ["scripts\\module_tracker.py", 77, "\"test_files_deleted\","], ["scripts\\module_tracker.py", 106, "\"test_passed\","], ["scripts\\module_tracker.py", 107, "\"test_files_deleted\","], ["scripts\\module_tracker.py", 137, "verification_result[\"checkpoints\"][\"test_passed\"] = test_result"], ["scripts\\module_tracker.py", 141, "verification_result[\"checkpoints\"][\"test_files_deleted\"] = test_files_result"], ["scripts\\module_tracker.py", 188, "\"details\": {\"test_files\": []},"], ["scripts\\module_tracker.py", 196, "\"details\": {\"test_files\": [str(f) for f in test_files]},"], ["scripts\\module_tracker.py", 136, "test_result = self.check_test_passed(module_name)"], ["scripts\\module_tracker.py", 137, "verification_result[\"checkpoints\"][\"test_passed\"] = test_result"], ["scripts\\module_tracker.py", 140, "test_files_result = self.check_test_files_deleted(module_name)"], ["scripts\\module_tracker.py", 141, "verification_result[\"checkpoints\"][\"test_files_deleted\"] = test_files_result"], ["scripts\\module_tracker.py", 171, "test_files = list("], ["scripts\\module_tracker.py", 202, "temp_test_files = list("], ["scripts\\module_tracker.py", 333, "latest_report_path = ("], ["scripts\\module_tracker.py", 336, "with open(latest_report_path, \"w\", encoding=\"utf-8\") as f:"], ["scripts\\module_tracker.py", 412, "latest_md_path = self.project_root / \"reports\" / \"latest_progress_report.md\""], ["scripts\\module_tracker.py", 413, "with open(latest_md_path, \"w\", encoding=\"utf-8\") as f:"], ["scripts\\module_tracker.py", 144, "mock_data_result = self.check_mock_data_deleted(module_name)"], ["scripts\\module_tracker.py", 145, "verification_result[\"checkpoints\"][\"mock_data_deleted\"] = mock_data_result"], ["scripts\\module_tracker.py", 231, "mock_data_files = list("], ["scripts\\module_tracker.py", 245, "active_mock_files = [f for f in mock_data_files if \"graveyard\" not in str(f)]"], ["scripts\\module_tracker_simple.py", 53, "\"test_passed\": <PERSON><PERSON><PERSON>,"], ["scripts\\module_tracker_simple.py", 54, "\"test_files_deleted\": <PERSON><PERSON><PERSON>,"], ["scripts\\module_tracker_simple.py", 77, "\"test_passed\","], ["scripts\\module_tracker_simple.py", 78, "\"test_files_deleted\","], ["scripts\\module_tracker_simple.py", 94, "\"test_passed\","], ["scripts\\module_tracker_simple.py", 95, "\"test_files_deleted\","], ["scripts\\next_module.py", 174, "\"test_passed\","], ["scripts\\quick_batch_migrate.py", 83, "\"test_passed\","], ["scripts\\quick_batch_migrate.py", 84, "\"test_files_deleted\","], ["scripts\\simple_migrator.py", 141, "\"test_passed\","], ["scripts\\simple_migrator.py", 142, "\"test_files_deleted\","], ["scripts\\ultra_simple_migrate.py", 57, "\"test_passed\","], ["scripts\\ultra_simple_migrate.py", 58, "\"test_files_deleted\","], ["scripts\\validate_deployment.py", 289, "test_data = {"], ["scripts\\validate_deployment.py", 294, "\"field_name\": \"test_field\","], ["scripts\\validate_deployment.py", 297, "\"sample_data\": \"test_data\","], ["scripts\\validate_deployment.py", 118, "test_cases = ["], ["scripts\\validate_deployment.py", 288, "test_url = f\"{self.base_url}/api/v1/field-config/baselines/sales_order\""], ["scripts\\validate_deployment.py", 289, "test_data = {"], ["scripts\\validate_deployment.py", 309, "response = requests.post(test_url, json=test_data, timeout=10)"], ["tests\\test_baseline_api.py", 34, "response = get_mock_response(\"baseline_save\", success=True)"], ["tests\\test_baseline_api.py", 57, "response = get_mock_response(\"baseline_save\", success=False)"], ["tests\\test_baseline_api.py", 79, "response = get_mock_response(\"baseline_save\", success=True)"], ["tests\\test_baseline_api.py", 108, "response = get_mock_response(\"baseline_save\", success=True)"], ["tests\\test_baseline_api.py", 167, "response = get_mock_response(\"baseline_save\", success=False)"], ["tests\\test_md_to_json_converter.py", 32, "self.test_md_content = \"\"\""], ["tests\\test_md_to_json_converter.py", 238, "test_content = \"test content for hash calculation\""], ["tests\\test_md_to_json_converter.py", 276, "test_files = []"], ["tests\\test_md_to_json_converter.py", 192, "mock_parse.return_value = {"], ["tests\\test_md_to_json_converter.py", 371, "mock_sizeof.return_value = 1024 * 1024 * 1024  # 1GB"], ["tests\\test_rollback_scripts.py", 40, "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))"], ["tests\\test_rollback_scripts.py", 68, "'test_table; DROP TABLE users;', date(2025, 1, 20)"], ["tests\\test_rollback_scripts.py", 96, "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))"], ["tests\\test_rollback_scripts.py", 118, "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))"], ["tests\\test_rollback_scripts.py", 140, "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))"], ["tests\\test_rollback_scripts.py", 152, "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))"], ["tests\\test_rollback_scripts.py", 219, "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))"], ["tests\\test_rollback_scripts.py", 244, "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))"], ["tests\\test_rollback_scripts.py", 301, "result = self.rollback.rollback_table_data('test_table', future_date)"], ["tests\\test_rollback_scripts.py", 32, "mock_conn = Mock()"], ["tests\\test_rollback_scripts.py", 33, "mock_cursor = Mock()"], ["tests\\test_rollback_scripts.py", 34, "mock_conn.cursor.return_value = mock_cursor"], ["tests\\test_rollback_scripts.py", 35, "mock_connect.return_value = mock_conn"], ["tests\\test_rollback_scripts.py", 38, "mock_cursor.fetchone.side_effect = [(1,), (10,)]  # 表存在，删除10条记录"], ["tests\\test_rollback_scripts.py", 49, "mock_conn = Mock()"], ["tests\\test_rollback_scripts.py", 50, "mock_cursor = Mock()"], ["tests\\test_rollback_scripts.py", 51, "mock_conn.cursor.return_value = mock_cursor"], ["tests\\test_rollback_scripts.py", 52, "mock_connect.return_value = mock_conn"], ["tests\\test_rollback_scripts.py", 55, "mock_cursor.fetchone.return_value = (0,)"], ["tests\\test_rollback_scripts.py", 79, "mock_conn = Mock()"], ["tests\\test_rollback_scripts.py", 80, "mock_cursor = Mock()"], ["tests\\test_rollback_scripts.py", 81, "mock_conn.cursor.return_value = mock_cursor"], ["tests\\test_rollback_scripts.py", 82, "mock_connect.return_value = mock_conn"], ["tests\\test_rollback_scripts.py", 85, "mock_cursor.fetchone.return_value = (1,)"], ["tests\\test_rollback_scripts.py", 91, "mock_cursor.execute.side_effect = ["], ["tests\\test_rollback_scripts.py", 106, "mock_conn = Mock()"], ["tests\\test_rollback_scripts.py", 107, "mock_cursor = Mock()"], ["tests\\test_rollback_scripts.py", 108, "mock_conn.cursor.return_value = mock_cursor"], ["tests\\test_rollback_scripts.py", 109, "mock_connect.return_value = mock_conn"], ["tests\\test_rollback_scripts.py", 112, "mock_cursor.fetchone.return_value = (1,)"], ["tests\\test_rollback_scripts.py", 116, "mock_cursor.execute.side_effect = [None, integrity_error]"], ["tests\\test_rollback_scripts.py", 128, "mock_conn = Mock()"], ["tests\\test_rollback_scripts.py", 129, "mock_cursor = Mock()"], ["tests\\test_rollback_scripts.py", 130, "mock_conn.cursor.return_value = mock_cursor"], ["tests\\test_rollback_scripts.py", 131, "mock_connect.return_value = mock_conn"], ["tests\\test_rollback_scripts.py", 134, "mock_cursor.fetchone.return_value = (1,)"], ["tests\\test_rollback_scripts.py", 138, "mock_cursor.execute.side_effect = [None, db_error]"], ["tests\\test_rollback_scripts.py", 150, "mock_connect.side_effect = pyodbc.Error(\"Cannot connect to database\")"], ["tests\\test_rollback_scripts.py", 195, "mock_conn = Mock()"], ["tests\\test_rollback_scripts.py", 196, "mock_cursor = Mock()"], ["tests\\test_rollback_scripts.py", 197, "mock_conn.cursor.return_value = mock_cursor"], ["tests\\test_rollback_scripts.py", 198, "mock_connect.return_value = mock_conn"], ["tests\\test_rollback_scripts.py", 201, "mock_cursor.fetchone.side_effect = ["], ["tests\\test_rollback_scripts.py", 209, "mock_cursor.execute.side_effect = ["], ["tests\\test_rollback_scripts.py", 235, "mock_conn = Mock()"], ["tests\\test_rollback_scripts.py", 236, "mock_cursor = Mock()"], ["tests\\test_rollback_scripts.py", 237, "mock_conn.cursor.return_value = mock_cursor"], ["tests\\test_rollback_scripts.py", 238, "mock_connect.return_value = mock_conn"], ["tests\\test_rollback_scripts.py", 240, "mock_cursor.fetchone.return_value = (1,)"], ["tests\\test_rollback_scripts.py", 242, "mock_cursor.execute.side_effect = [None, integrity_error]"], ["tests\\test_rollback_scripts.py", 260, "mock_conn = Mock()"], ["tests\\test_rollback_scripts.py", 261, "mock_cursor = Mock()"], ["tests\\test_rollback_scripts.py", 262, "mock_conn.cursor.return_value = mock_cursor"], ["tests\\test_rollback_scripts.py", 263, "mock_connect.return_value = mock_conn"], ["tests\\test_rollback_scripts.py", 266, "mock_cursor.fetchone.side_effect = [(1,), (0,)]  # 表存在，删除0条记录"], ["tests\\test_rollback_scripts.py", 276, "mock_conn = Mock()"], ["tests\\test_rollback_scripts.py", 277, "mock_cursor = Mock()"], ["tests\\test_rollback_scripts.py", 278, "mock_conn.cursor.return_value = mock_cursor"], ["tests\\test_rollback_scripts.py", 279, "mock_connect.return_value = mock_conn"], ["tests\\test_rollback_scripts.py", 282, "mock_cursor.fetchone.side_effect = [(1,), (100000,)]  # 表存在，删除10万条记录"], ["tests\\test_rollback_scripts.py", 294, "mock_conn = Mock()"], ["tests\\test_rollback_scripts.py", 295, "mock_cursor = Mock()"], ["tests\\test_rollback_scripts.py", 296, "mock_conn.cursor.return_value = mock_cursor"], ["tests\\test_rollback_scripts.py", 297, "mock_connect.return_value = mock_conn"], ["tests\\test_rollback_scripts.py", 299, "mock_cursor.fetchone.side_effect = [(1,), (0,)]  # 表存在，但未来日期无数据"], ["tests\\week2_completion_report.py", 137, "\"test_integration\": \"✅ 完整测试覆盖\""], ["tests\\week2_completion_report.py", 176, "\"test_coverage\": \"全面覆盖\","]], "javascript_files": [["frontend\\js\\common\\test-data.js", 102, "id: 'test_user_001',"], ["frontend\\js\\common\\test-data.js", 125, "error_code: 'TEST_ERROR'"]], "config_files": []}, "temp_files": {"log_files": ["d:\\OneDrive\\Desktop\\YS-API程序\\v3\\build_script_fix.log", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\cicd_build.log", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\cicd_pipeline_build.log", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\comprehensive_fix.log", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\debug_code_cleanup.log", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\remaining_issues_fix.log", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\universal_code_fix.log", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\recovery_enhanced.log", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\logs\\auto_sync.log", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\logs\\material_master_sync.log", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\logs\\production_test\\production_test_20250802_210131.log", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\logs\\production_test\\production_test_20250802_210250.log", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\logs\\production_test\\production_test_20250802_210409.log", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\debug_code_cleanup.log"], "backup_files": ["d:\\OneDrive\\Desktop\\YS-API程序\\v3\\模块字段\\backup\\材料出库单列表查询.xml.backup"], "temp_files": [], "debug_files": []}}, "cleanup_recommendations": {"immediate_delete": [], "review_before_delete": ["d:\\OneDrive\\Desktop\\YS-API程序\\v3\\test_day4_proxy.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\test_mvp.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\frontend_test_server.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\production_test_runner.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\test_modules_direct.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\test_backend_automation.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\test_server.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\tests\\test_baseline_api.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\tests\\test_rollback_scripts.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\frontend\\js\\common\\test-data.js", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\module_functional_test.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\frontend\\migrated\\path-test.html", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\tests\\simple_test.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\tests\\test_md_to_json_converter.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\cleanup_test_code.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\test_health_checker.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\real_data_full_test.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\setup_test_env.py", "d:\\OneDrive\\Desktop\\YS-API程序\\v3\\模块字段\\backup\\材料出库单列表查询.xml.backup"], "keep_but_organize": [], "convert_to_config": [{"file": "automation_validator.py", "line": 190, "content": "\"test_rounds\": test_rounds", "type": "hardcoded_data"}, {"file": "automation_validator.py", "line": 153, "content": "test_endpoint = \"/health\"", "type": "hardcoded_data"}, {"file": "automation_validator.py", "line": 154, "content": "test_rounds = 5", "type": "hardcoded_data"}, {"file": "automation_validator.py", "line": 161, "content": "response = requests.get(f\"{self.proxy_base}{test_endpoint}\", timeout=10)", "type": "hardcoded_data"}, {"file": "automation_validator.py", "line": 173, "content": "response = requests.get(f\"{self.proxy_base}{test_endpoint}?version=new\", timeout=10)", "type": "hardcoded_data"}, {"file": "automation_validator.py", "line": 205, "content": "test_results = [", "type": "hardcoded_data"}, {"file": "auto_project_cleanup.py", "line": 89, "content": "test_patterns = [", "type": "hardcoded_data"}, {"file": "build_production_package.py", "line": 75, "content": "\"test_*\",", "type": "hardcoded_data"}, {"file": "check_naming_conflicts.py", "line": 16, "content": "'test_files': re.compile(r'test_.*\\.py$'),", "type": "hardcoded_data"}, {"file": "check_naming_conflicts.py", "line": 16, "content": "'test_files': re.compile(r'test_.*\\.py$'),", "type": "hardcoded_data"}, {"file": "final_batch.py", "line": 31, "content": "\"test_passed\",", "type": "hardcoded_data"}, {"file": "final_batch.py", "line": 32, "content": "\"test_files_deleted\",", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 193, "content": "f.write(f\"test_line_{i}\\n\")", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 32, "content": "'test_info': {", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 34, "content": "'test_date': start_time.strftime('%Y-%m-%d'),", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 35, "content": "'test_time': start_time.strftime('%H:%M:%S'),", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 36, "content": "'test_type': 'Production Readiness Assessment',", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 37, "content": "'test_version': '1.0.0',", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 462, "content": "'test_completion_time': end_time.isoformat(),", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 463, "content": "'test_duration': str(duration).split('.')[0],", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 542, "content": "- **项目名称**: {report_data['test_info']['project_name']}", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 543, "content": "- **评估日期**: {report_data['test_info']['test_date']}", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 543, "content": "- **评估日期**: {report_data['test_info']['test_date']}", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 544, "content": "- **评估时间**: {report_data['test_info']['test_time']}", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 544, "content": "- **评估时间**: {report_data['test_info']['test_time']}", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 545, "content": "- **评估类型**: {report_data['test_info']['test_type']}", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 545, "content": "- **评估类型**: {report_data['test_info']['test_type']}", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 546, "content": "- **评估耗时**: {overall['test_duration']}", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 690, "content": "*评估工具版本: {report_data['test_info']['test_version']}*", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 690, "content": "*评估工具版本: {report_data['test_info']['test_version']}*", "type": "hardcoded_data"}, {"file": "production_readiness_report.py", "line": 187, "content": "test_file = Path(\"performance_test.tmp\")", "type": "hardcoded_data"}, {"file": "setup_test_env.py", "line": 34, "content": "DEBUG_MODE=false", "type": "hardcoded_data"}, {"file": "setup_test_env.py", "line": 66, "content": "cursor.execute(\"INSERT INTO test_data (name) VALUES (?)\", (\"test_entry\",))", "type": "hardcoded_data"}, {"file": "smart_file_creator.py", "line": 36, "content": "'test_runner': {", "type": "hardcoded_data"}, {"file": "smart_file_creator.py", "line": 38, "content": "'suggested_name': 'test_runner.py',", "type": "hardcoded_data"}, {"file": "systematic_duplicate_detector.py", "line": 178, "content": "temp_patterns = ['test_*.py', '*_test_*.py', 'debug_*.py', 'temp_*.py']", "type": "hardcoded_data"}, {"file": "test_day4_proxy.py", "line": 106, "content": "\"test_type\": \"Day4_proxy_test\",", "type": "hardcoded_data"}, {"file": "test_day4_proxy.py", "line": 34, "content": "test_endpoint = \"/api/v1/health\"", "type": "hardcoded_data"}, {"file": "test_day4_proxy.py", "line": 39, "content": "response = requests.get(f\"http://127.0.0.1:9000{test_endpoint}\", timeout=10)", "type": "hardcoded_data"}, {"file": "test_day4_proxy.py", "line": 49, "content": "response = requests.get(f\"http://127.0.0.1:9000{test_endpoint}?version=new\", timeout=10)", "type": "hardcoded_data"}, {"file": "test_day4_proxy.py", "line": 105, "content": "test_result = {", "type": "hardcoded_data"}, {"file": "test_day4_proxy.py", "line": 118, "content": "json.dump(test_result, f, indent=2, ensure_ascii=False)", "type": "hardcoded_data"}, {"file": "test_day4_proxy.py", "line": 162, "content": "results[test_name] = result", "type": "hardcoded_data"}, {"file": "test_day4_proxy.py", "line": 165, "content": "results[test_name] = False", "type": "hardcoded_data"}, {"file": "validate_month3_week1.py", "line": 52, "content": "test_modules = [\"purchase_order\", \"sales_order\", \"inventory\"]", "type": "hardcoded_data"}, {"file": "verify_database_enhancement.py", "line": 67, "content": "mock_db = MockDatabaseManager()", "type": "hardcoded_data"}, {"file": "verify_module.py", "line": 65, "content": "self.results[\"steps\"][\"test_pass\"] = {", "type": "hardcoded_data"}, {"file": "verify_module.py", "line": 73, "content": "self.results[\"steps\"][\"test_pass\"] = {", "type": "hardcoded_data"}, {"file": "verify_module.py", "line": 85, "content": "f\"test_{self.module_name}*.py\",", "type": "hardcoded_data"}, {"file": "verify_module.py", "line": 65, "content": "self.results[\"steps\"][\"test_pass\"] = {", "type": "hardcoded_data"}, {"file": "verify_module.py", "line": 73, "content": "self.results[\"steps\"][\"test_pass\"] = {", "type": "hardcoded_data"}, {"file": "verify_module.py", "line": 84, "content": "test_patterns = [", "type": "hardcoded_data"}, {"file": "verify_module.py", "line": 92, "content": "test_files = list(Path(\".\").glob(f\"**/{pattern}\"))", "type": "hardcoded_data"}, {"file": "verify_module.py", "line": 104, "content": "self.results[\"steps\"][\"delete_test_files\"] = {", "type": "hardcoded_data"}, {"file": "verify_module.py", "line": 115, "content": "mock_patterns = [", "type": "hardcoded_data"}, {"file": "verify_module.py", "line": 123, "content": "mock_files = list(Path(\".\").glob(f\"**/{pattern}\"))", "type": "hardcoded_data"}, {"file": "verify_module.py", "line": 135, "content": "self.results[\"steps\"][\"delete_mock_data\"] = {", "type": "hardcoded_data"}, {"file": "backend\\app\\main_original.py", "line": 294, "content": "test_html_path = project_root / \"test_frontend.html\"", "type": "hardcoded_data"}, {"file": "backend\\app\\main_original.py", "line": 294, "content": "test_html_path = project_root / \"test_frontend.html\"", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\database_manager.py", "line": 40, "content": "'test_field',", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\database_manager.py", "line": 453, "content": "test_engine = create_engine(", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\integrated_ys_api_client.py", "line": 471, "content": "test_params = {'pageSize': 10, 'pageNum': 1}", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 35, "content": "self.test_data = self._generate_test_data()", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 114, "content": "'test_results': {},", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 123, "content": "validation_results['test_results']['data_transformer'] = transformer_results", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 127, "content": "validation_results['test_results']['format_standardizer'] = standardizer_results", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 131, "content": "validation_results['test_results']['quality_inspector'] = inspector_results", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 135, "content": "validation_results['test_results']['response_processor'] = processor_results", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 139, "content": "validation_results['test_results']['integration'] = integration_results", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 150, "content": "all_test_results = validation_results['test_results'].values()", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 229, "content": "test_data_with_types, 'test_module', {}", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 366, "content": "problem_data, 'test_module', {}", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 527, "content": "invalid_data, 'test_module', {}", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 691, "content": "test_results = validation_results.get('test_results', {})", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 25, "content": "self.test_results = []", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 35, "content": "self.test_data = self._generate_test_data()", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 123, "content": "validation_results['test_results']['data_transformer'] = transformer_results", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 127, "content": "validation_results['test_results']['format_standardizer'] = standardizer_results", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 131, "content": "validation_results['test_results']['quality_inspector'] = inspector_results", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 135, "content": "validation_results['test_results']['response_processor'] = processor_results", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 139, "content": "validation_results['test_results']['integration'] = integration_results", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 150, "content": "all_test_results = validation_results['test_results'].values()", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 221, "content": "test_data_with_types = {", "type": "hardcoded_data"}, {"file": "backend\\app\\services\\week3_validation_suite.py", "line": 691, "content": "test_results = validation_results.get('test_results', {})", "type": "hardcoded_data"}, {"file": "core\\app\\main_original.py", "line": 294, "content": "test_html_path = project_root / \"test_frontend.html\"", "type": "hardcoded_data"}, {"file": "core\\app\\main_original.py", "line": 294, "content": "test_html_path = project_root / \"test_frontend.html\"", "type": "hardcoded_data"}, {"file": "core\\app\\services\\database_manager.py", "line": 40, "content": "'test_field',", "type": "hardcoded_data"}, {"file": "core\\app\\services\\database_manager.py", "line": 453, "content": "test_engine = create_engine(", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 60, "content": "\"module_name\": \"test_module\",", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 69, "content": "\"user_id\": \"test_user\",", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 70, "content": "\"module_name\": \"test_module\",", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 259, "content": "\"field_config\", module_name=\"test_module\", field_count=10", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 259, "content": "\"field_config\", module_name=\"test_module\", field_count=10", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 37, "content": "def __init__(self, mock_data_dir: str = None):", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 45, "content": "mock_data_dir = Path(__file__).parent / \"mock_data\"", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 47, "content": "self.mock_data_dir = Path(mock_data_dir)", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 48, "content": "self.mock_data_dir.mkdir(exist_ok=True)", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 82, "content": "def mock_baseline_save_api(self, success: bool = True) -> MockResponse:", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 89, "content": "def mock_user_config_save_api(self, success: bool = True) -> MockResponse:", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 100, "content": "mock_fields = {}", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 103, "content": "mock_fields[field_name] = {", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 169, "content": "self.is_mock_mode = self._detect_mock_mode()", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 170, "content": "self.mock_client = MockAPIClient() if self.is_mock_mode else None", "type": "hardcoded_data"}, {"file": "dev-tools\\mock\\mock_utils.py", "line": 200, "content": "mock_func = endpoint_mapping.get(endpoint)", "type": "hardcoded_data"}, {"file": "month2_config\\config_rollback\\manager.py", "line": 104, "content": "latest_version = self.version_history[module_name][-1]", "type": "hardcoded_data"}, {"file": "month2_config\\config_rollback\\manager.py", "line": 105, "content": "latest_config = self._load_config_by_version(module_name, latest_version[\"version_id\"])", "type": "hardcoded_data"}, {"file": "scripts\\auto_migration_pipeline.py", "line": 321, "content": "\"test_passed\",", "type": "hardcoded_data"}, {"file": "scripts\\auto_migration_pipeline.py", "line": 322, "content": "\"test_files_deleted\",", "type": "hardcoded_data"}, {"file": "scripts\\auto_migration_pipeline.py", "line": 305, "content": "test_file = f\"tests/module_migration/test_{module_name.replace(' ', '_').lower()}_migration.py\"", "type": "hardcoded_data"}, {"file": "scripts\\batch_initialize_next.py", "line": 99, "content": "test_path = test_dir / f\"test_{module_name.replace(' ', '_').lower()}_migration.py\"", "type": "hardcoded_data"}, {"file": "scripts\\batch_initialize_next.py", "line": 96, "content": "test_dir = Path(\"tests/module_migration\")", "type": "hardcoded_data"}, {"file": "scripts\\batch_initialize_next.py", "line": 97, "content": "test_dir.mkdir(parents=True, exist_ok=True)", "type": "hardcoded_data"}, {"file": "scripts\\batch_initialize_next.py", "line": 99, "content": "test_path = test_dir / f\"test_{module_name.replace(' ', '_').lower()}_migration.py\"", "type": "hardcoded_data"}, {"file": "scripts\\batch_initialize_next.py", "line": 101, "content": "test_content = f'''#!/usr/bin/env python3", "type": "hardcoded_data"}, {"file": "scripts\\batch_initialize_next.py", "line": 136, "content": "with open(test_path, \"w\", encoding=\"utf-8\") as f:", "type": "hardcoded_data"}, {"file": "scripts\\batch_initialize_next.py", "line": 156, "content": "test_path = create_module_test(module_name)", "type": "hardcoded_data"}, {"file": "scripts\\batch_init_modules.py", "line": 73, "content": "\"test_passed\",", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 37, "content": "\"test_*.py\",", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 78, "content": "self.scan_results[\"test_files\"] = categorized", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 136, "content": "r'\"test_.*?\"',", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 270, "content": "test_files = self.scan_results.get(\"test_files\", {})", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 407, "content": "total_test_files = sum(len(files) for files in self.scan_results.get(\"test_files\", {}).values())", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 447, "content": "report_file = self.project_root / \"reports\" / \"test_code_cleanup_analysis.json\"", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 131, "content": "r'test_data\\s*=',", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 137, "content": "r\"'test_.*?'\",", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 138, "content": "r'TEST_.*\\s*=',", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 33, "content": "test_patterns = [", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 42, "content": "test_files = []", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 48, "content": "test_files = list(set(test_files))", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 78, "content": "self.scan_results[\"test_files\"] = categorized", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 130, "content": "test_data_patterns = [", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 131, "content": "r'test_data\\s*=',", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 138, "content": "r'TEST_.*\\s*=',", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 270, "content": "test_files = self.scan_results.get(\"test_files\", {})", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 407, "content": "total_test_files = sum(len(files) for files in self.scan_results.get(\"test_files\", {}).values())", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 85, "content": "mock_patterns = [", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 92, "content": "mock_files = []", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 122, "content": "self.scan_results[\"mock_files\"] = categorized", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 133, "content": "r'mock_response\\s*=',", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 139, "content": "r'MOCK_.*\\s*='", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 279, "content": "mock_files = self.scan_results.get(\"mock_files\", {})", "type": "hardcoded_data"}, {"file": "scripts\\cleanup_test_code.py", "line": 408, "content": "total_mock_files = sum(len(files) for files in self.scan_results.get(\"mock_files\", {}).values())", "type": "hardcoded_data"}, {"file": "scripts\\clean_hardcoded_data.py", "line": 21, "content": "(r'test_data\\s*=\\s*[{\\[].*?[}\\]]', '# 测试数据已移除'),", "type": "hardcoded_data"}, {"file": "scripts\\clean_hardcoded_data.py", "line": 24, "content": "(r'TEST_[A-Z_]+\\s*=\\s*[\\'\"][^\\'\\\"]*[\\'\"]', '# 测试常量已移除'),", "type": "hardcoded_data"}, {"file": "scripts\\clean_hardcoded_data.py", "line": 20, "content": "test_patterns_to_remove = [", "type": "hardcoded_data"}, {"file": "scripts\\clean_hardcoded_data.py", "line": 21, "content": "(r'test_data\\s*=\\s*[{\\[].*?[}\\]]', '# 测试数据已移除'),", "type": "hardcoded_data"}, {"file": "scripts\\clean_hardcoded_data.py", "line": 24, "content": "(r'TEST_[A-Z_]+\\s*=\\s*[\\'\"][^\\'\\\"]*[\\'\"]', '# 测试常量已移除'),", "type": "hardcoded_data"}, {"file": "scripts\\clean_hardcoded_data.py", "line": 97, "content": "test_files_to_remove = [", "type": "hardcoded_data"}, {"file": "scripts\\clean_hardcoded_data.py", "line": 125, "content": "html_test_files = [", "type": "hardcoded_data"}, {"file": "scripts\\clean_hardcoded_data.py", "line": 22, "content": "(r'mock_response\\s*=\\s*[{\\[].*?[}\\]]', '# 模拟响应已移除'),", "type": "hardcoded_data"}, {"file": "scripts\\complete_modules.py", "line": 13, "content": "\"test_passed\",", "type": "hardcoded_data"}, {"file": "scripts\\complete_modules.py", "line": 14, "content": "\"test_files_deleted\",", "type": "hardcoded_data"}, {"file": "scripts\\manual_cleanup.py", "line": 18, "content": "\"test_baseline_api.py\",", "type": "hardcoded_data"}, {"file": "scripts\\manual_cleanup.py", "line": 20, "content": "\"test_sentinel.py\",", "type": "hardcoded_data"}, {"file": "scripts\\manual_cleanup.py", "line": 21, "content": "\"test_anti_duplicate_system.py\",", "type": "hardcoded_data"}, {"file": "scripts\\manual_cleanup.py", "line": 23, "content": "\"test_server.py\",", "type": "hardcoded_data"}, {"file": "scripts\\manual_cleanup.py", "line": 25, "content": "\"test_day3_core.py\",", "type": "hardcoded_data"}, {"file": "scripts\\manual_cleanup.py", "line": 26, "content": "\"test_proxy.py\",", "type": "hardcoded_data"}, {"file": "scripts\\manual_cleanup.py", "line": 27, "content": "\"test_day3_offline.py\",", "type": "hardcoded_data"}, {"file": "scripts\\manual_cleanup.py", "line": 30, "content": "\"test_real_data_validation.py\"", "type": "hardcoded_data"}, {"file": "scripts\\migrate_purchase_order.py", "line": 122, "content": "self.project_root / \"tests\" / \"new_system\" / \"test_purchase_order.py\"", "type": "hardcoded_data"}, {"file": "scripts\\migrate_purchase_order.py", "line": 554, "content": "/ \"test_采购订单列表_migration.py\"", "type": "hardcoded_data"}, {"file": "scripts\\migrate_purchase_order.py", "line": 619, "content": "\"test_results\": test_results,", "type": "hardcoded_data"}, {"file": "scripts\\migrate_purchase_order.py", "line": 120, "content": "test_content = self.generate_test_code()", "type": "hardcoded_data"}, {"file": "scripts\\migrate_purchase_order.py", "line": 121, "content": "test_path = (", "type": "hardcoded_data"}, {"file": "scripts\\migrate_purchase_order.py", "line": 124, "content": "test_path.parent.mkdir(parents=True, exist_ok=True)", "type": "hardcoded_data"}, {"file": "scripts\\migrate_purchase_order.py", "line": 126, "content": "with open(test_path, \"w\", encoding=\"utf-8\") as f:", "type": "hardcoded_data"}, {"file": "scripts\\migrate_purchase_order.py", "line": 550, "content": "test_file = (", "type": "hardcoded_data"}, {"file": "scripts\\migrate_purchase_order.py", "line": 561, "content": "test_results = {", "type": "hardcoded_data"}, {"file": "scripts\\migrate_purchase_order.py", "line": 582, "content": "test_results = {", "type": "hardcoded_data"}, {"file": "scripts\\migrate_purchase_order.py", "line": 590, "content": "json.dump(test_results, f, ensure_ascii=False, indent=2)", "type": "hardcoded_data"}, {"file": "scripts\\migrate_purchase_order.py", "line": 605, "content": "test_results = self.step4_run_migration_tests()", "type": "hardcoded_data"}, {"file": "scripts\\module_functional_test.py", "line": 110, "content": "test_data = {", "type": "hardcoded_data"}, {"file": "scripts\\module_functional_test.py", "line": 113, "content": "\"test_mode\": True", "type": "hardcoded_data"}, {"file": "scripts\\module_functional_test.py", "line": 21, "content": "self.test_results = {}", "type": "hardcoded_data"}, {"file": "scripts\\module_functional_test.py", "line": 59, "content": "def test_module_api(self, module_name: str, endpoint: str, base_url: str = \"http://localhost:8000\") ", "type": "hardcoded_data"}, {"file": "scripts\\module_functional_test.py", "line": 110, "content": "test_data = {", "type": "hardcoded_data"}, {"file": "scripts\\module_functional_test.py", "line": 143, "content": "def test_module_frontend(self, module_name: str, frontend_url: str = \"http://localhost:8080\") -> Dic", "type": "hardcoded_data"}, {"file": "scripts\\module_functional_test.py", "line": 236, "content": "def test_single_module(self, module_name: str, endpoint: str = None) -> Dict[str, Any]:", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 51, "content": "\"test_passed\": <PERSON><PERSON><PERSON>,", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 52, "content": "\"test_files_deleted\": <PERSON><PERSON><PERSON>,", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 76, "content": "\"test_passed\",", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 77, "content": "\"test_files_deleted\",", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 106, "content": "\"test_passed\",", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 107, "content": "\"test_files_deleted\",", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 137, "content": "verification_result[\"checkpoints\"][\"test_passed\"] = test_result", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 141, "content": "verification_result[\"checkpoints\"][\"test_files_deleted\"] = test_files_result", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 188, "content": "\"details\": {\"test_files\": []},", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 196, "content": "\"details\": {\"test_files\": [str(f) for f in test_files]},", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 136, "content": "test_result = self.check_test_passed(module_name)", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 137, "content": "verification_result[\"checkpoints\"][\"test_passed\"] = test_result", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 140, "content": "test_files_result = self.check_test_files_deleted(module_name)", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 141, "content": "verification_result[\"checkpoints\"][\"test_files_deleted\"] = test_files_result", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 171, "content": "test_files = list(", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 202, "content": "temp_test_files = list(", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 333, "content": "latest_report_path = (", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 336, "content": "with open(latest_report_path, \"w\", encoding=\"utf-8\") as f:", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 412, "content": "latest_md_path = self.project_root / \"reports\" / \"latest_progress_report.md\"", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 413, "content": "with open(latest_md_path, \"w\", encoding=\"utf-8\") as f:", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 144, "content": "mock_data_result = self.check_mock_data_deleted(module_name)", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 145, "content": "verification_result[\"checkpoints\"][\"mock_data_deleted\"] = mock_data_result", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 231, "content": "mock_data_files = list(", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker.py", "line": 245, "content": "active_mock_files = [f for f in mock_data_files if \"graveyard\" not in str(f)]", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker_simple.py", "line": 53, "content": "\"test_passed\": <PERSON><PERSON><PERSON>,", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker_simple.py", "line": 54, "content": "\"test_files_deleted\": <PERSON><PERSON><PERSON>,", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker_simple.py", "line": 77, "content": "\"test_passed\",", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker_simple.py", "line": 78, "content": "\"test_files_deleted\",", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker_simple.py", "line": 94, "content": "\"test_passed\",", "type": "hardcoded_data"}, {"file": "scripts\\module_tracker_simple.py", "line": 95, "content": "\"test_files_deleted\",", "type": "hardcoded_data"}, {"file": "scripts\\next_module.py", "line": 174, "content": "\"test_passed\",", "type": "hardcoded_data"}, {"file": "scripts\\quick_batch_migrate.py", "line": 83, "content": "\"test_passed\",", "type": "hardcoded_data"}, {"file": "scripts\\quick_batch_migrate.py", "line": 84, "content": "\"test_files_deleted\",", "type": "hardcoded_data"}, {"file": "scripts\\simple_migrator.py", "line": 141, "content": "\"test_passed\",", "type": "hardcoded_data"}, {"file": "scripts\\simple_migrator.py", "line": 142, "content": "\"test_files_deleted\",", "type": "hardcoded_data"}, {"file": "scripts\\ultra_simple_migrate.py", "line": 57, "content": "\"test_passed\",", "type": "hardcoded_data"}, {"file": "scripts\\ultra_simple_migrate.py", "line": 58, "content": "\"test_files_deleted\",", "type": "hardcoded_data"}, {"file": "scripts\\validate_deployment.py", "line": 289, "content": "test_data = {", "type": "hardcoded_data"}, {"file": "scripts\\validate_deployment.py", "line": 294, "content": "\"field_name\": \"test_field\",", "type": "hardcoded_data"}, {"file": "scripts\\validate_deployment.py", "line": 297, "content": "\"sample_data\": \"test_data\",", "type": "hardcoded_data"}, {"file": "scripts\\validate_deployment.py", "line": 118, "content": "test_cases = [", "type": "hardcoded_data"}, {"file": "scripts\\validate_deployment.py", "line": 288, "content": "test_url = f\"{self.base_url}/api/v1/field-config/baselines/sales_order\"", "type": "hardcoded_data"}, {"file": "scripts\\validate_deployment.py", "line": 289, "content": "test_data = {", "type": "hardcoded_data"}, {"file": "scripts\\validate_deployment.py", "line": 309, "content": "response = requests.post(test_url, json=test_data, timeout=10)", "type": "hardcoded_data"}, {"file": "tests\\test_baseline_api.py", "line": 34, "content": "response = get_mock_response(\"baseline_save\", success=True)", "type": "hardcoded_data"}, {"file": "tests\\test_baseline_api.py", "line": 57, "content": "response = get_mock_response(\"baseline_save\", success=False)", "type": "hardcoded_data"}, {"file": "tests\\test_baseline_api.py", "line": 79, "content": "response = get_mock_response(\"baseline_save\", success=True)", "type": "hardcoded_data"}, {"file": "tests\\test_baseline_api.py", "line": 108, "content": "response = get_mock_response(\"baseline_save\", success=True)", "type": "hardcoded_data"}, {"file": "tests\\test_baseline_api.py", "line": 167, "content": "response = get_mock_response(\"baseline_save\", success=False)", "type": "hardcoded_data"}, {"file": "tests\\test_md_to_json_converter.py", "line": 32, "content": "self.test_md_content = \"\"\"", "type": "hardcoded_data"}, {"file": "tests\\test_md_to_json_converter.py", "line": 238, "content": "test_content = \"test content for hash calculation\"", "type": "hardcoded_data"}, {"file": "tests\\test_md_to_json_converter.py", "line": 276, "content": "test_files = []", "type": "hardcoded_data"}, {"file": "tests\\test_md_to_json_converter.py", "line": 192, "content": "mock_parse.return_value = {", "type": "hardcoded_data"}, {"file": "tests\\test_md_to_json_converter.py", "line": 371, "content": "mock_sizeof.return_value = 1024 * 1024 * 1024  # 1GB", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 40, "content": "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 68, "content": "'test_table; DROP TABLE users;', date(2025, 1, 20)", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 96, "content": "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 118, "content": "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 140, "content": "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 152, "content": "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 219, "content": "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 244, "content": "result = self.rollback.rollback_table_data('test_table', date(2025, 1, 20))", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 301, "content": "result = self.rollback.rollback_table_data('test_table', future_date)", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 32, "content": "mock_conn = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 33, "content": "mock_cursor = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 34, "content": "mock_conn.cursor.return_value = mock_cursor", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 35, "content": "mock_connect.return_value = mock_conn", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 38, "content": "mock_cursor.fetchone.side_effect = [(1,), (10,)]  # 表存在，删除10条记录", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 49, "content": "mock_conn = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 50, "content": "mock_cursor = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 51, "content": "mock_conn.cursor.return_value = mock_cursor", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 52, "content": "mock_connect.return_value = mock_conn", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 55, "content": "mock_cursor.fetchone.return_value = (0,)", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 79, "content": "mock_conn = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 80, "content": "mock_cursor = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 81, "content": "mock_conn.cursor.return_value = mock_cursor", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 82, "content": "mock_connect.return_value = mock_conn", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 85, "content": "mock_cursor.fetchone.return_value = (1,)", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 91, "content": "mock_cursor.execute.side_effect = [", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 106, "content": "mock_conn = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 107, "content": "mock_cursor = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 108, "content": "mock_conn.cursor.return_value = mock_cursor", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 109, "content": "mock_connect.return_value = mock_conn", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 112, "content": "mock_cursor.fetchone.return_value = (1,)", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 116, "content": "mock_cursor.execute.side_effect = [None, integrity_error]", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 128, "content": "mock_conn = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 129, "content": "mock_cursor = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 130, "content": "mock_conn.cursor.return_value = mock_cursor", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 131, "content": "mock_connect.return_value = mock_conn", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 134, "content": "mock_cursor.fetchone.return_value = (1,)", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 138, "content": "mock_cursor.execute.side_effect = [None, db_error]", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 150, "content": "mock_connect.side_effect = pyodbc.Error(\"Cannot connect to database\")", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 195, "content": "mock_conn = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 196, "content": "mock_cursor = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 197, "content": "mock_conn.cursor.return_value = mock_cursor", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 198, "content": "mock_connect.return_value = mock_conn", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 201, "content": "mock_cursor.fetchone.side_effect = [", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 209, "content": "mock_cursor.execute.side_effect = [", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 235, "content": "mock_conn = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 236, "content": "mock_cursor = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 237, "content": "mock_conn.cursor.return_value = mock_cursor", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 238, "content": "mock_connect.return_value = mock_conn", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 240, "content": "mock_cursor.fetchone.return_value = (1,)", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 242, "content": "mock_cursor.execute.side_effect = [None, integrity_error]", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 260, "content": "mock_conn = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 261, "content": "mock_cursor = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 262, "content": "mock_conn.cursor.return_value = mock_cursor", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 263, "content": "mock_connect.return_value = mock_conn", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 266, "content": "mock_cursor.fetchone.side_effect = [(1,), (0,)]  # 表存在，删除0条记录", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 276, "content": "mock_conn = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 277, "content": "mock_cursor = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 278, "content": "mock_conn.cursor.return_value = mock_cursor", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 279, "content": "mock_connect.return_value = mock_conn", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 282, "content": "mock_cursor.fetchone.side_effect = [(1,), (100000,)]  # 表存在，删除10万条记录", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 294, "content": "mock_conn = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 295, "content": "mock_cursor = Mock()", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 296, "content": "mock_conn.cursor.return_value = mock_cursor", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 297, "content": "mock_connect.return_value = mock_conn", "type": "hardcoded_data"}, {"file": "tests\\test_rollback_scripts.py", "line": 299, "content": "mock_cursor.fetchone.side_effect = [(1,), (0,)]  # 表存在，但未来日期无数据", "type": "hardcoded_data"}, {"file": "tests\\week2_completion_report.py", "line": 137, "content": "\"test_integration\": \"✅ 完整测试覆盖\"", "type": "hardcoded_data"}, {"file": "tests\\week2_completion_report.py", "line": 176, "content": "\"test_coverage\": \"全面覆盖\",", "type": "hardcoded_data"}, {"file": "frontend\\js\\common\\test-data.js", "line": 102, "content": "id: 'test_user_001',", "type": "hardcoded_data"}, {"file": "frontend\\js\\common\\test-data.js", "line": 125, "content": "error_code: 'TEST_ERROR'", "type": "hardcoded_data"}]}, "summary": {"total_test_files": 18, "total_mock_files": 1, "total_temp_files": 15, "total_hardcoded": 302, "immediate_delete": 0, "review_delete": 19, "organize": 0, "convert_config": 302}}