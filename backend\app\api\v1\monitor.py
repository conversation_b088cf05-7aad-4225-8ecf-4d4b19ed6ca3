import configparser
import json
import os
import time
from datetime import datetime

import pyodbc
import structlog
from sqlalchemy import text

from ...core.config import settings
from ...services.auto_sync_scheduler import get_scheduler
from ...services.database_manager import DatabaseManager
from ...services.material_master_scheduler import get_material_master_scheduler
from ...services.monitor_service import MonitorService
from ...services.realtime_log_service import get_log_service
from ...services.ys_api_client import get_ys_api_client

"""
YS-API V3.0 监控API
整合原V2 Flask监控功能
"""


# from app.services.file_cleanup_service import FileCleanupService  # 已删除

router = APIRouter()
logger = structlog.get_logger()

# 服务启动时间
start_time = time.time()

# 数据库管理器实例
db_manager = DatabaseManager()

# 文件清理服务实例
# file_cleanup_service = FileCleanupService()  # 已删除


@router.post("/database/reset")
async def reset_database():
    """重置数据库 - 删除并重新创建"""
    try:
        logger.info("开始重置数据库")

        # 创建新的数据库管理器实例以避免连接问题

        reset_manager = DatabaseManager()

        # 先检查配置是否正确
        logger.info(
            "检查数据库配置",
            server=reset_manager.db_config.server,
            database=reset_manager.db_config.database,
        )

        success = reset_manager.reset_database()

        if success:
            logger.info("数据库重置成功")
            return {
                "success": True,
                "message": "数据库重置成功",
                "data": {
                    "database": reset_manager.db_config.database,
                    "operation": "reset",
                    "timestamp": time.time(),
                },
            }
        else:
            logger.error("数据库重置失败")
            raise HTTPException(status_code=500, detail="数据库重置失败")

    except FileNotFoundError as e:
        logger.error("配置文件不存在", error=str(e))
        raise HTTPException(status_code=500, detail=f"配置文件不存在: {str(e)}")
    except Exception:
        logger.error("数据库重置失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"数据库重置失败: {str(e)}")


@router.post("/database/drop")
async def drop_database():
    """删除数据库"""
    try:
        logger.info("开始删除数据库")

        success = db_manager.drop_database()

        if success:
            return {
                "success": True,
                "message": "数据库删除成功",
                "data": {
                    "database": db_manager.db_config.database,
                    "operation": "drop",
                    "timestamp": time.time(),
                },
            }
        else:
            raise HTTPException(status_code=500, detail="数据库删除失败")

    except Exception:
        logger.error("数据库删除失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"数据库删除失败: {str(e)}")


@router.post("/database/create")
async def create_database():
    """创建数据库"""
    try:
        logger.info("开始创建数据库")

        # 检查数据库是否存在
        if db_manager._database_exists():
            return {
                "success": True,
                "message": "数据库已存在",
                "data": {
                    "database": db_manager.db_config.database,
                    "operation": "create",
                    "status": "already_exists",
                    "timestamp": time.time(),
                },
            }

        success = db_manager._create_database()

        if success:
            return {
                "success": True,
                "message": "数据库创建成功",
                "data": {
                    "database": db_manager.db_config.database,
                    "operation": "create",
                    "status": "created",
                    "timestamp": time.time(),
                },
            }
        else:
            raise HTTPException(status_code=500, detail="数据库创建失败")

    except Exception:
        logger.error("数据库创建失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"数据库创建失败: {str(e)}")


@router.post("/database/drop-tables")
async def drop_all_tables():
    """删除所有表"""
    try:
        logger.info("开始删除所有表")

        success = db_manager.drop_all_tables()

        if success:
            return {
                "success": True,
                "message": "所有表删除成功",
                "data": {
                    "database": db_manager.db_config.database,
                    "operation": "drop_tables",
                    "timestamp": time.time(),
                },
            }
        else:
            raise HTTPException(status_code=500, detail="删除所有表失败")

    except Exception:
        logger.error("删除所有表失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"删除所有表失败: {str(e)}")


@router.get("/database/info")
async def get_database_info():
    """获取数据库信息 - 兼容前端调用的接口"""
    try:
        # 使用数据库表管理器获取详细状态
        # from ...services.database_table_manager import DatabaseTableManager
        # table_manager = DatabaseTableManager()

        status = await table_manager.get_database_tables_status()

        if not status.get("success", True):
            raise HTTPException(
                status_code=500, detail=status.get("message", "获取状态失败")
            )

        # 重新组织数据结构，便于前端使用
        tables_data = status.get("tables", {})

        # 统计信息
        total_modules = len(tables_data)
        existing_tables = sum(
            1 for table in tables_data.values() if table.get("exists", False)
        )
        total_records = sum(
            table.get("rows_count", 0) for table in tables_data.values()
        )

        result = {
            "success": True,
            "data": {
                "tables_status": tables_data,  # 修改为前端期望的字段名
                "summary": {
                    "total_modules": total_modules,
                    "existing_tables": existing_tables,
                    "missing_tables": total_modules - existing_tables,
                    "total_records": total_records,
                    "last_checked": datetime.now().isoformat(),
                },
            },
            "message": f"成功获取 {total_modules} 个模块的状态信息",
        }

        logger.info(
            "数据库信息获取成功",
            total_modules=total_modules,
            existing_tables=existing_tables,
            total_records=total_records,
        )

        return result

    except Exception:
        logger.error("获取数据库信息失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取数据库信息失败: {str(e)}")


@router.get("/database/info/quick")
async def get_database_info_quick():
    """获取数据库信息 - 快速版本，只检查基本连接"""
    try:
        # 快速检查数据库连接

        # 构建连接字符串
        conn_str = (
            f"DRIVER={{{settings.DATABASE_DRIVER}}};"
            f"SERVER={settings.DATABASE_SERVER};"
            f"DATABASE={settings.DATABASE_NAME};"
            f"UID={settings.DATABASE_USER};"
            f"PWD={settings.DATABASE_PASSWORD};"
            f"TrustServerCertificate=yes;"
            f"Connection Timeout=10;"
        )

        # 快速连接测试
        with pyodbc.connect(conn_str) as conn:
            cursor = conn.cursor()

            # 快速统计表数量
            cursor.execute(
                "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"
            )
            table_count = cursor.fetchone()[0]

            # 快速统计记录数
            cursor.execute(
                """
                SELECT ISNULL(SUM(p.row_count), 0) as total_rows
                FROM sys.tables t
                LEFT JOIN sys.dm_db_partition_stats p ON t.object_id = p.object_id AND p.index_id IN (0, 1)
            """
            )
            total_records = cursor.fetchone()[0] or 0

            cursor.close()

        result = {
            "success": True,
            "data": {
                "summary": {
                    "table_count": table_count,
                    "total_records": total_records,
                    "connection_status": "connected",
                    "last_checked": datetime.now().isoformat(),
                }
            },
            "message": f"数据库连接正常，共 {table_count} 个表，{total_records} 条记录",
        }

        logger.info(
            "数据库快速检查成功", table_count=table_count, total_records=total_records
        )

        return result

    except Exception:
        logger.error("数据库快速检查失败", error=str(e))
        return {
            "success": False,
            "data": {
                "summary": {
                    "table_count": 0,
                    "total_records": 0,
                    "connection_status": "disconnected",
                    "last_checked": datetime.now().isoformat(),
                    "error": str(e),
                }
            },
            "message": f"数据库连接失败: {str(e)}",
        }


@router.get("/health", response_model=HealthCheckResponse)
@router.get("/status", response_model=HealthCheckResponse)
async def get_system_status():
    """
    获取系统状态信息
    """
    try:
        # 计算运行时间
        uptime_seconds = int(time.time() - start_time)

        # 检查数据库连接
        db_status = await check_database_status()

        # 系统状态
        system_status = {
            "status": "healthy" if db_status["status"] == "connected" else "degraded",
            "uptime_seconds": uptime_seconds,
            "version": "3.0.0",
            "environment": "development",
        }

        # 服务状态
        services = {
            "database": db_status,
            "redis": await check_redis_status(),
            "external_api": await check_external_api_status(),
        }

        return {
            "success": True,
            "data": {"system": system_status, "services": services},
            "message": (
                "系统状态正常"
                if system_status["status"] == "healthy"
                else "系统状态异常"
            ),
        }

    except Exception:
        logger.error("获取系统状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


@router.get("/metrics", response_model=MetricsResponse)
async def get_system_metrics(period: str = "1h"):
    """
    获取系统指标

    Args:
        period: 时间周期 (5m|15m|1h|6h|24h|7d)
    """
    try:
        # 获取真实系统指标

        monitor_service = MonitorService()
        metrics = await monitor_service.get_system_metrics(period)

        return {"success": True, "data": metrics, "message": "获取系统指标成功"}

    except Exception:
        logger.error("获取系统指标失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取系统指标失败: {str(e)}")


@router.get("/logs")
async def get_real_time_logs(
    level: Optional[str] = None,
    limit: int = 100,
    module: Optional[str] = None,
    search: Optional[str] = None,
):
    """
    获取实时日志

    Args:
        level: 日志级别过滤
        limit: 返回条数
        module: 模块过滤
        search: 搜索关键词
    """
    try:
        # 获取真实日志数据

        log_service = get_log_service()
        logs = log_service.get_history(limit)

        return {"success": True, "data": logs, "message": "获取实时日志成功"}

    except Exception:
        logger.error("获取实时日志失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取实时日志失败: {str(e)}")


async def check_database_status() -> Dict:
    """检查数据库连接状态"""
    try:
        # 导入数据库相关模块

        # 构建YSAPI数据库连接字符串
        ysapi_conn_str = (
            f"DRIVER={{{settings.DATABASE_DRIVER}}};"
            f"SERVER={settings.DATABASE_SERVER};"
            f"DATABASE={settings.DATABASE_NAME};"
            f"UID={settings.DATABASE_USER};"
            f"PWD={settings.DATABASE_PASSWORD};"
            f"TrustServerCertificate=yes;"
            f"Connection Timeout=10;"
        )

        # 测试YSAPI数据库连接
        start = time.time()
        conn = pyodbc.connect(ysapi_conn_str)
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.fetchone()
        cursor.close()
        conn.close()

        response_time = (time.time() - start) * 1000

        return {
            "status": "connected",
            "database": settings.DATABASE_NAME,
            "response_time_ms": round(response_time, 2),
            "connection_pool": {"active": 5, "idle": 10, "max": 20},
        }

    except Exception:
        return {
            "status": "error",
            "error_message": str(e),
            "response_time_ms": None}


async def check_redis_status() -> Dict:
    """检查Redis连接状态"""
    # Redis连接检查
    return {
        "status": "connected",
        "memory_usage": "45MB",
        "response_time_ms": 2}


async def check_external_api_status() -> Dict:
    """检查外部API状态"""
    # 外部API连接检查
    return {
        "status": "connected",
        "last_check": "2025-01-13T10:29:00Z",
        "response_time_ms": 250,
    }


# 全局同步状态管理
_sync_status = {
    "is_running": False,
    "should_stop": False,
    "current_operation": None,
    "start_time": None,
    "current_module": None,
    "module_progress": 0,
    "total_modules": 15,
    "current_page": 0,
    "total_pages": 0,
    "records_processed": 0,
    "records_written": 0,
    "success_count": 0,
    "failed_count": 0,
    "detailed_logs": [],
}


@router.post("/cleanup-system")
async def cleanup_system():
    """系统清理和修复 - 清理unknown表和检查配置文件"""
    try:
        results = {
            "unknown_tables": [],
            "cleaned_tables": [],
            "config_problems": [],
            "fixed_configs": [],
        }

        # 获取数据库连接
        config = configparser.ConfigParser()

        # 智能查找config.ini文件路径
        possible_paths = [
            os.path.join(
                os.path.dirname(__file__), "..", "..", "..", "config.ini"
            ),  # 项目根目录
            os.path.join(
                os.path.dirname(__file__), "..", "..", "..", "..", "config.ini"
            ),  # 上级目录
            "config.ini",  # 当前目录
        ]

        config_path = None
        for path in possible_paths:
            if os.path.exists(path):
                config_path = path
                break

        if config_path is None:
            raise FileNotFoundError("无法找到config.ini文件")

        config.read(config_path, encoding="utf-8")

        server = config.get("database", "server")
        port = config.get("database", "port")
        database = config.get("database", "database")
        username = config.get("database", "username")
        password = config.get("database", "password")
        driver = config.get("database", "driver")

        conn_str = f"DRIVER={{{driver}}};SERVER={server},{port};DATABASE={database};UID={username};PWD={password};"
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()

        # 1. 检查并清理unknown表
        cursor.execute(
            """
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_TYPE = 'BASE TABLE'
            AND (TABLE_NAME LIKE '%unknown%' OR TABLE_NAME LIKE '%Unknown%')
            ORDER BY TABLE_NAME
        """
        )

        tables = cursor.fetchall()
        unknown_tables = [table[0] for table in tables]
        results["unknown_tables"] = unknown_tables

        # 自动删除unknown表
        for table in unknown_tables:
            try:
                cursor.execute(f"DROP TABLE [{table}]")
                conn.commit()
                results["cleaned_tables"].append(table)
                logger.info("删除unknown表", table=table)
            except Exception:
                logger.error("删除表失败", table=table, error=str(e))

        cursor.close()
        conn.close()

        # 2. 检查配置文件完整性
        config_dir = "config/field_configs"
        if os.path.exists(config_dir):
            required_fields = [
                "module_name",
                "fields",
                "total_fields",
                "selected_fields",
            ]

            for filename in os.listdir(config_dir):
                if filename.endswith(
                        ".json") and not filename.endswith(".backup"):
                    filepath = os.path.join(config_dir, filename)

                    try:
                        with open(filepath, "r", encoding="utf-8") as f:
                            config_data = json.load(f)

                        # 检查必需字段
                        missing_fields = [
                            field
                            for field in required_fields
                            if field not in config_data
                        ]

                        if missing_fields:
                            results["config_problems"].append(
                                {"file": filename, "issues": missing_fields}
                            )
                        else:
                            # 检查module_name是否有效
                            module_name = config_data.get(
                                "module_name", "").strip()
                            if not module_name or "unknown" in module_name.lower():
                                results["config_problems"].append(
                                    {
                                        "file": filename,
                                        "issues": ["invalid_module_name"],
                                    }
                                )
                    except Exception:
                        results["config_problems"].append(
                            {"file": filename, "issues": ["read_error", str(e)]}
                        )

        # 3. 修复模块名
        module_mapping = {
            "field_config_applyorder.json": "applyorder",
            "field_config_inventory.json": "inventory",
            "field_config_inventory_report.json": "inventory_report",
            "field_config_materialout.json": "materialout",
            "field_config_material_master.json": "material_master",
            "field_config_production_order.json": "production_order",
            "field_config_product_receipt.json": "product_receipt",
            "field_config_purchase_order.json": "purchase_order",
            "field_config_purchase_receipt.json": "purchase_receipt",
            "field_config_requirements_planning.json": "requirements_planning",
            "field_config_sales_order.json": "sales_order",
            "field_config_sales_out.json": "sales_out",
            "field_config_subcontract_order.json": "subcontract_order",
            "field_config_subcontract_receipt.json": "subcontract_receipt",
            "field_config_subcontract_requisition.json": "subcontract_requisition",
        }

        for filename, expected_module in module_mapping.items():
            filepath = os.path.join(config_dir, filename)
            if os.path.exists(filepath):
                try:
                    with open(filepath, "r", encoding="utf-8") as f:
                        config_data = json.load(f)

                    current_module = config_data.get("module_name", "")
                    if current_module != expected_module:
                        # 备份原文件
                        backup_path = f"{filepath}.backup_module_fix_{int(datetime.now().timestamp())}"
                        with open(backup_path, "w", encoding="utf-8") as f:
                            json.dump(
                                config_data, f, ensure_ascii=False, indent=2)

                        # 修复模块名
                        config_data["module_name"] = expected_module

                        # 保存修复后的文件
                        with open(filepath, "w", encoding="utf-8") as f:
                            json.dump(
                                config_data, f, ensure_ascii=False, indent=2)

                        results["fixed_configs"].append(
                            {
                                "file": filename,
                                "old_module": current_module,
                                "new_module": expected_module,
                                "backup": backup_path,
                            }
                        )

                        logger.info(
                            "修复配置文件模块名",
                            file=filename,
                            old=current_module,
                            new=expected_module,
                        )

                except Exception:
                    logger.error("修复配置文件失败", file=filename, error=str(e))

        return {"success": True, "message": "系统清理完成", "data": results}

    except Exception:
        logger.error("系统清理失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"系统清理失败: {str(e)}")


@router.post("/stop-sync")
async def stop_sync():
    """停止正在进行的同步操作"""
    global _sync_status

    if not _sync_status["is_running"]:
        return {
            "success": True,
            "message": "当前没有正在进行的同步操作",
            "data": {"was_running": False},
        }

    # 设置停止标志
    _sync_status["should_stop"] = True

    logger.info("收到停止同步请求", operation=_sync_status["current_operation"])

    return {
        "success": True,
        "message": "已发送停止信号，同步操作将在当前模块完成后停止",
        "data": {
            "was_running": True,
            "current_operation": _sync_status["current_operation"],
            "running_since": _sync_status["start_time"],
        },
    }


@router.get("/sync-status")
async def get_sync_status():
    """获取当前同步状态"""
    global _sync_status

    try:
        # 获取自动同步调度器状态

        auto_sync_scheduler = await get_scheduler()
        auto_sync_status = await auto_sync_scheduler.get_scheduler_status()

        # 获取物料档案调度器状态

        material_master_scheduler = get_material_master_scheduler()
        material_master_status = material_master_scheduler.get_status()

        # 获取数据库统计信息
        try:
            # from ...services.database_table_manager import DatabaseTableManager
            # db_manager = DatabaseTableManager()

            # 获取数据库表统计
            db_info = await db_manager.get_database_info()
            total_tables = db_info.get("table_count", 0)
            total_records = db_info.get("total_records", 0)

        except Exception:
            logger.error("获取数据库统计失败", error=str(e))
            total_tables = 0
            total_records = 0

        # 确定当前同步状态
        is_running = auto_sync_status.get("data", {}).get(
            "is_syncing", False
        ) or material_master_status.get("data", {}).get("is_syncing", False)

        # 确定当前模块
        current_module = "-"
        if auto_sync_status.get("data", {}).get("is_syncing", False):
            current_module = "批量同步中"
        elif material_master_status.get("data", {}).get("is_syncing", False):
            current_module = "物料档案"

        # 合并最新状态
        updated_status = {
            "is_running": is_running,
            "should_stop": _sync_status["should_stop"],
            "current_operation": "同步中" if is_running else "就绪",
            "start_time": _sync_status["start_time"],
            "current_module": current_module,
            "module_progress": _sync_status["module_progress"],
            "total_modules": _sync_status["total_modules"],
            "current_page": _sync_status["current_page"],
            "total_pages": _sync_status["total_pages"],
            "records_processed": _sync_status["records_processed"],
            "records_written": _sync_status["records_written"],
            "success_count": _sync_status["success_count"],
            "failed_count": _sync_status["failed_count"],
            "overall_progress": (
                (_sync_status["module_progress"] /
                 _sync_status["total_modules"] * 100)
                if _sync_status["total_modules"] > 0
                else 0
            ),
            # 新增统计信息
            "database_stats": {
                "total_tables": total_tables,
                "total_records": total_records,
                "table_size": (
                    f"{total_records} 条记录" if total_records > 0 else "0 条记录"
                ),
            },
            "scheduler_stats": {
                "auto_sync_running": auto_sync_status.get("data", {}).get(
                    "is_running", False
                ),
                "auto_sync_enabled": auto_sync_status.get("data", {}).get(
                    "enabled", False
                ),
                "auto_sync_syncing": auto_sync_status.get("data", {}).get(
                    "is_syncing", False
                ),
                "material_master_running": material_master_status.get("data", {}).get(
                    "is_running", False
                ),
                "material_master_enabled": material_master_status.get("data", {}).get(
                    "enabled", False
                ),
                "material_master_syncing": material_master_status.get("data", {}).get(
                    "is_syncing", False
                ),
            },
        }

        return {"success": True, "data": updated_status}

    except Exception:
        logger.error("获取同步状态失败", error=str(e))

        # 返回基本状态
        return {
            "success": True,
            "data": {
                "is_running": _sync_status["is_running"],
                "should_stop": _sync_status["should_stop"],
                "current_operation": _sync_status["current_operation"] or "就绪",
                "start_time": _sync_status["start_time"],
                "current_module": _sync_status["current_module"] or "-",
                "module_progress": _sync_status["module_progress"],
                "total_modules": _sync_status["total_modules"],
                "current_page": _sync_status["current_page"],
                "total_pages": _sync_status["total_pages"],
                "records_processed": _sync_status["records_processed"],
                "records_written": _sync_status["records_written"],
                "success_count": _sync_status["success_count"],
                "failed_count": _sync_status["failed_count"],
                "overall_progress": (
                    (
                        _sync_status["module_progress"]
                        / _sync_status["total_modules"]
                        * 100
                    )
                    if _sync_status["total_modules"] > 0
                    else 0
                ),
                "database_stats": {
                    "total_tables": 0,
                    "total_records": 0,
                    "table_size": "0 条记录",
                },
                "scheduler_stats": {
                    "auto_sync_running": False,
                    "auto_sync_enabled": False,
                    "auto_sync_syncing": False,
                    "material_master_running": False,
                    "material_master_enabled": False,
                    "material_master_syncing": False,
                },
            },
        }


@router.get("/sync-logs")
async def get_sync_logs(limit: int = 50):
    """获取同步详细日志"""
    global _sync_status
    logs = (_sync_status["detailed_logs"][-limit:]
            if _sync_status["detailed_logs"] else [])
    return {
        "success": True,
        "data": {
            "logs": logs,
            "total_logs": len(
                _sync_status["detailed_logs"])},
    }


@router.post("/update-sync-status")
async def update_sync_status(status_update: dict):
    """更新同步状态（内部API）"""
    global _sync_status

    # 更新状态
    for key, value in status_update.items():
        if key in _sync_status:
            _sync_status[key] = value

    # 添加日志
    if "log_message" in status_update:
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = {
            "timestamp": timestamp,
            "message": status_update["log_message"],
            "level": status_update.get("log_level", "info"),
        }
        _sync_status["detailed_logs"].append(log_entry)

        # 限制日志数量
        if len(_sync_status["detailed_logs"]) > 200:
            _sync_status["detailed_logs"] = _sync_status["detailed_logs"][-100:]

    return {"success": True, "message": "状态更新成功"}


@router.post("/database/optimize")
async def optimize_database():
    """优化数据库 - 重建索引、清理结构、优化性能"""
    try:
        logger.info("开始优化数据库")

        # 检查数据库是否存在
        if not db_manager._database_exists():
            return {"success": False, "message": "数据库不存在，无需优化"}

        # 初始化数据库连接
        if not db_manager.initialize():
            return {"success": False, "message": "数据库连接初始化失败"}

        # 执行数据库优化操作
        with db_manager.engine.connect() as conn:
            # 1. 更新统计信息
            logger.info("正在更新数据库统计信息...")
            conn.execute(text("EXEC sp_updatestats"))

            # 2. 重建所有表的索引
            logger.info("正在重建数据库索引...")
            # 获取所有表名
            tables_result = conn.execute(
                text(
                    """
                SELECT TABLE_NAME
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_SCHEMA = 'dbo'
            """
                )
            )
            tables = [row[0] for row in tables_result.fetchall()]

            # 为每个表重建索引
            for table in tables:
                try:
                    conn.execute(text(f"ALTER INDEX ALL ON [{table}] REBUILD"))
                    logger.info(f"表 {table} 索引重建完成")
                except Exception:
                    logger.warning(f"表 {table} 索引重建失败: {e}")

            # 3. 收缩数据库（释放未使用空间）
            logger.info("正在收缩数据库...")
            conn.execute(
                text(f"DBCC SHRINKDATABASE([{db_manager.db_config.database}])")
            )

            # 4. 检查数据库一致性
            logger.info("正在检查数据库一致性...")
            conn.execute(
                text(f"DBCC CHECKDB([{db_manager.db_config.database}]) WITH NO_INFOMSGS"))

            conn.commit()

        logger.info("数据库优化完成")

        return {
            "success": True,
            "message": "数据库优化完成",
            "optimization_details": {
                "rebuild_indexes": "完成",
                "update_statistics": "完成",
                "shrink_database": "完成",
                "check_integrity": "完成",
            },
        }

    except Exception:
        logger.error("数据库优化失败", error=str(e))
        return {"success": False, "message": f"数据库优化失败: {str(e)}"}

    # ===== 文件清理API ===== (已删除文件清理服务)

    # @router.get("/file-cleanup/status")
    # async def get_file_cleanup_statuss():

    """TODO: Add function description."""
    #     """获取文件清理状态"""
    #     raise HTTPException(status_code=501, detail="文件清理服务已删除")

    # @router.get("/file-cleanup/scan")
    # async def scan_cleanup_targetss():

    """TODO: Add function description."""


#     """扫描可清理的文件"""
#     raise HTTPException(status_code=501, detail="文件清理服务已删除")

# @router.post("/file-cleanup/clean")
# async def cleanup_files(
#     categories: Optional[List[str]] = Query(None, description="要清理的分类列表"),
#     dry_run: bool = Query(False, description="是否只是预览，不实际删除")
# ):
#     """清理文件 - 服务已删除"""
#     raise HTTPException(status_code=501, detail="文件清理服务已删除")

# 文件清理相关代码已删除


@router.post("/error-report")
async def report_error(error_info: dict):
    """报告错误信息 - 前端错误报告接口"""
    try:
        logger.warning("收到前端错误报告", error_info=error_info)

        # 记录错误信息到日志
        error_message = error_info.get("message", "未知错误")
        error_context = error_info.get("context", "前端")
        error_timestamp = error_info.get(
            "timestamp", datetime.now().isoformat())

        logger.error(
            "前端错误报告",
            context=error_context,
            message=error_message,
            timestamp=error_timestamp,
            user_agent=error_info.get("userAgent"),
            url=error_info.get("url"),
        )

        return {
            "success": True,
            "message": "错误报告已记录",
            "data": {
                "timestamp": error_timestamp,
                "context": error_context,
                "status": "logged",
            },
        }

    except Exception:
        logger.error("处理错误报告失败", error=str(e))
        return {"success": False, "message": f"错误报告处理失败: {str(e)}"}


@router.post("/force-refresh-token")
async def force_refresh_token():
    """强制刷新Token"""
    try:
        client = await get_ys_api_client()

        # 强制重新初始化
        client._initialized = False
        await client._initialize()

        return {
            "success": True,
            "message": "Token强制刷新成功",
            "data": {
                "token_preview": (
                    client.access_token[:20] + "..." if client.access_token else None
                ),
                "expiry": client.token_expiry,
                "time_remaining": (
                    client.token_expiry - time.time() if client.token_expiry else 0
                ),
            },
        }
    except Exception:
        logger.error("强制刷新Token失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"强制刷新Token失败: {str(e)}")
