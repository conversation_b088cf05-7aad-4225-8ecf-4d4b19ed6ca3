# YS-API V3.0 数据流程标准规范

> **📋 文档版本**: v1.0  
> **最后更新**: 2025年8月5日  
> **重要性**: ⭐⭐⭐⭐⭐ 核心流程文档，避免开发混乱

---

## 🎯 流程概述

YS-API V3.0 采用**五步数据流程**，确保从API原始数据到最终数据库存储的完整链路标准化和可追溯。

## 📊 完整数据流程图

```mermaid
graph TD
    A[用友云API] -->|原始数据| B[步骤1: 基准文件生成]
    B --> C[步骤2: JSON匹配器增强]
    C --> D[步骤3: 智能字段分类]
    D --> E[步骤4: 数据库表创建]
    E --> F[步骤5: 数据ETL写入]
    
    B --> B1[config/baselines/]
    C --> C1[模块字段/json/]
    D --> D1[config/user_configs/]
    E --> E1[SQLite/SQL Server]
    F --> F1[业务数据表]
```

---

## 🔄 详细流程步骤

### 📥 步骤1：API原始字段获取与基准文件生成

**目标**: 保留API数据完整性，建立数据源真实镜像

**输入**: 用友云API原始响应数据  
**输出**: `config/baselines/{模块名}_baseline.json`

**处理原则**:
- ✅ **不做任何修改**，原样保存API返回的字段结构
- ✅ 保持完整的嵌套层级关系
- ✅ 记录字段路径、深度、数组标识
- ✅ 包含所有元数据信息

**关键字段**:
```json
{
  "api_field_name": "原始字段名",
  "path": "data.recordList.fieldName",
  "depth": 2,
  "is_array": false,
  "business_importance": "critical|high|medium|low"
}
```

**实现位置**: 
- 服务: `backend/app/services/field_extractor.py`
- API: `backend/app/api/v1/config.py`

---

### 🔍 步骤2：JSON匹配器增强处理

**目标**: 通过字典匹配获取字段的中文名称、数据类型等元数据

**输入**: 基准文件 + `模块字段/json/{模块名}.json`  
**输出**: 增强后的字段元数据

**处理逻辑**:
- ✅ 基于`EnhancedJSONFieldMatcher`服务
- ✅ 多策略匹配：直接匹配 → 末尾匹配 → 包含匹配 → 模糊匹配
- ✅ 获取字段信息：
  - `chinese_name`: 中文名称
  - `data_type`: 标准化数据类型
  - `param_desc`: 字段描述
  - `max_length`: 字段长度限制

**匹配策略优先级**:
1. **直接匹配**: `field_name == mapped_field_name`
2. **末尾匹配**: `mapped_field_name.endswith(f".{field_name}")`
3. **包含匹配**: `field_name in mapped_field_name`
4. **模糊匹配**: 去路径前缀比较

**实现位置**:
- 服务: `backend/app/services/enhanced_json_field_matcher.py`
- 字典: `模块字段/json/` 目录

---

### 🧠 步骤3：智能字段分类与用户配置生成

**目标**: 智能排除技术字段，根据业务重要性生成默认用户配置

**输入**: 增强后字段元数据  
**输出**: `config/user_configs/{用户ID}/{模块名}_config.json`

**智能分类规则**:

#### 🚫 技术字段排除列表
```javascript
const TECHNICAL_FIELDS = [
  'pageIndex', 'pageSize', 'pageCount',
  'beginPageIndex', 'endPageIndex',
  'message', 'data', 'recordList',
  'simpleVOs', 'queryOrders', 'isSum'
];
```

#### ⭐ 重要性分级与默认配置
```javascript
const IMPORTANCE_MAPPING = {
  'critical': { is_required: true },    // 必选：主键、编码、ID类
  'high': { is_required: true },       // 默认选中：金额、数量、状态
  'medium': { is_required: false },    // 可选：描述、名称类
  'low': { is_required: false }        // 默认不选：自定义项、扩展字段
};
```

#### 💾 用户配置结构
```json
{
  "module_name": "purchase_order",
  "user_id": "Alice",
  "fields": {
    "code": {
      "api_field_name": "code",
      "chinese_name": "单据编码",
      "data_type": "NVARCHAR(500)",
      "is_required": true,
      "business_importance": "critical"
    }
  }
}
```

**实现位置**:
- 前端: `frontend/js/user-config-save.js`
- 服务: `backend/app/services/intelligent_field_mapper.py`

---

### 🗄️ 步骤4：数据库表创建

**目标**: 基于用户配置中`is_required=true`的字段创建数据库表

**输入**: 用户配置文件  
**输出**: 数据库表结构

**创建规则**:
- ✅ 只处理`is_required: true`的字段
- ✅ 使用字段的**中文名称**作为数据库列名
- ✅ 根据`data_type`映射数据库字段类型
- ✅ 添加标准基础字段：`id`, `created_at`, `updated_at`

#### 🔄 数据类型映射表
```python
TYPE_MAPPING = {
    'NVARCHAR(500)': 'TEXT',
    'NVARCHAR(MAX)': 'TEXT', 
    'BIGINT': 'INTEGER',
    'BIT': 'INTEGER',
    'DECIMAL(18,4)': 'REAL',
    'DATETIME': 'TEXT'
}
```

#### 📋 表创建SQL示例
```sql
CREATE TABLE IF NOT EXISTS 采购订单 (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    单据编码 TEXT NOT NULL,
    物料编码 TEXT NOT NULL,
    供应商编码 TEXT NOT NULL,
    -- ... 其他 is_required=true 的字段
);
```

**实现位置**:
- 服务: `month2_database/table_creation/manager.py`
- 数据库: SQLite/SQL Server

---

### 💾 步骤5：数据ETL与存储

**目标**: API数据清洗转换后写入数据库表

**输入**: 用友云API返回数据 + 用户配置  
**输出**: 清洗后的业务数据存储

**ETL处理流程**:

#### 1️⃣ 字段过滤
```python
# 只处理用户配置中 is_required=true 的字段
selected_fields = [
    field for field, config in user_config['fields'].items() 
    if config.get('is_required', False)
]
```

#### 2️⃣ 数据类型转换
```python
def convert_data_type(value, data_type):
    if data_type == 'BIGINT':
        return int(value) if value else 0
    elif data_type == 'BIT':
        return 1 if str(value).lower() in ['true', '1'] else 0
    elif data_type.startswith('NVARCHAR'):
        return str(value)[:max_length] if value else ''
    return value
```

#### 3️⃣ 数据清洗规则
- ✅ 空值处理：根据字段类型设置默认值
- ✅ 长度截断：字符串按`max_length`截断
- ✅ 格式校验：日期、数字格式验证
- ✅ 特殊字符清理：SQL注入防护

#### 4️⃣ 批量写入
```python
def batch_insert(table_name, data_list, batch_size=1000):
    for i in range(0, len(data_list), batch_size):
        batch = data_list[i:i+batch_size]
        execute_batch_insert(table_name, batch)
```

**实现位置**:
- 服务: `backend/app/services/data_write_manager.py`
- 服务: `backend/app/services/sync_service.py`

---

## 🔧 关键组件说明

### 📁 目录结构映射

```
v3/
├── config/
│   ├── baselines/           # 步骤1：基准文件
│   └── user_configs/        # 步骤3：用户配置
├── 模块字段/
│   └── json/               # 步骤2：JSON字典
├── month2_database/
│   └── table_creation/     # 步骤4：表创建
└── backend/app/services/   # 步骤5：ETL服务
```

### 🔗 服务依赖关系

```
FieldExtractor ──→ EnhancedJSONFieldMatcher
       │                    │
       ▼                    ▼
  BaselineFile ──→ IntelligentFieldMapper
                           │
                           ▼
                   UserConfigSave
                           │
                           ▼
                 TableCreationManager
                           │
                           ▼
                   DataWriteManager
```

---

## ⚠️ 关键注意事项

### 🛡️ 数据完整性保证
1. **基准文件不可修改**: 始终保持API原始数据结构
2. **向后兼容**: 新版本配置兼容旧版本数据
3. **错误恢复**: 每步骤支持重试和回滚机制

### 🔒 安全性要求
1. **用户隔离**: 不同用户的配置完全隔离
2. **权限控制**: 关键配置需要管理员权限
3. **数据验证**: 所有输入数据严格验证

### 📈 性能优化
1. **批量处理**: 大数据量采用批量写入
2. **增量同步**: 支持增量更新机制
3. **缓存策略**: 字典数据内存缓存

### 🐛 错误处理
1. **分步骤日志**: 每个步骤详细记录处理日志
2. **异常捕获**: 完整的异常处理和报错信息
3. **数据回滚**: 失败时自动回滚到上一稳定状态

---

## 📚 相关文档

- [系统架构设计](./02-系统架构设计.md)
- [字段配置保存位置说明](./字段配置保存位置说明.md)
- [API接口规范](./05-API接口规范.md)
- [数据处理规范](./03-数据处理规范.md)

---

## 🎯 快速检查清单

开发过程中，请按以下清单检查：

- [ ] **步骤1**: 基准文件是否保持API原始结构？
- [ ] **步骤2**: JSON匹配器是否正确获取中文名称？
- [ ] **步骤3**: 技术字段是否被正确排除？
- [ ] **步骤4**: 数据库表是否只包含`is_required=true`字段？
- [ ] **步骤5**: ETL是否正确处理数据类型转换？
- [ ] **日志**: 每个步骤是否有详细日志记录？
- [ ] **错误**: 异常情况是否有适当处理？
- [ ] **测试**: 完整流程是否经过测试验证？

---

> **⚠️ 重要提醒**: 此文档为核心流程规范，任何修改需要团队确认并更新版本号。开发过程中如有疑问，优先参考此文档。
