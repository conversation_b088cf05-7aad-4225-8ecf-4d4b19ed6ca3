import json
import logging
import os
import platform
import shutil
import subprocess
import sys
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 生产环境打包脚本
自动安装依赖、收集文件、创建可执行文件
"""


# 设置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('production_build.log', encoding='utf-8'),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


class ProductionPackager:
    """生产环境打包器"""

    def __init___(self):
    """TODO: Add function description."""
    self.project_root = Path(__file__).parent
    self.build_dir = self.project_root / "build"
    self.dist_dir = self.project_root / "dist"
    self.package_name = "YS-API-V3.0"

    # 依赖列表
    self.dependencies = [
        "pywin32",
        "psutil",
        "structlog",
        "fastapi",
        "uvicorn",
        "sqlalchemy",
        "pyodbc",
        "pydantic-settings",
        "schedule",
        "pyinstaller",
    ]

    # 需要包含的文件和目录
    self.include_files = [
        "backend/",
        "config/",
        "frontend/",
        "start_production_with_port_check.py",
        "install_windows_service.py",
        "deploy_production.bat",
    ]

    # 需要排除的文件和目录
    self.exclude_patterns = [
        "__pycache__",
        "*.pyc",
        "*.pyo",
        "*.pyd",
        ".git",
        ".vscode",
        "*.log",
        "logs/",
        "cache/",
        "temp/",
        "test_*",
        "*.test",
        "*.bak",
    ]

    self.logger.info("🚀 YS-API V3.0 生产环境打包器")
    self.logger = logging.getLogger(__name__)
    self.logger.info("YS-API V3.0 生产环境打包器")
    self.logger.info("=" * 50)

    def check_environment(self):
        """检查环境"""
        self.logger.info("🔍 检查环境...")

        # 检查Python版本
        python_version = sys.version_info
        if python_version.major < 3 or (
            python_version.major == 3 and python_version.minor < 8
        ):
            raise Exception("需要Python 3.8或更高版本")

        self.logger.info(
            f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}"
        )

        # 检查操作系统
        if platform.system() != "Windows":
            self.logger.info("⚠️  警告: 此脚本专为Windows设计")

        self.logger.info(f"✅ 操作系统: {platform.system()}")

        # 检查必要目录
        for include_file in self.include_files:
            if not (self.project_root / include_file).exists():
                raise Exception(f"缺少必要文件: {include_file}")

        self.logger.info("✅ 项目文件检查完成")

    def install_dependencies(self):
        """安装依赖"""
        self.logger.info("\n📦 安装依赖...")

        for dep in self.dependencies:
            try:
                self.logger.info(f"正在安装: {dep}")
                subprocess.run(
                    [sys.executable, "-m", "pip", "install", dep, "--quiet"],
                    check=True,
                    capture_output=True,
                )
                self.logger.info(f"✅ {dep} 安装成功")
            except subprocess.CalledProcessError as e:
                self.logger.info(f"❌ {dep} 安装失败: {e}")
                raise

    def clean_build_dirs(self):
        """清理构建目录"""
        self.logger.info("\n🧹 清理构建目录...")

        for dir_path in [self.build_dir, self.dist_dir]:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                self.logger.info(f"已删除: {dir_path}")

        # 清理PyInstaller缓存
        cache_dir = self.project_root / "__pycache__"
        if cache_dir.exists():
            shutil.rmtree(cache_dir)
            self.logger.info("已清理Python缓存")

    def create_pyinstaller_spec(self):
        """创建PyInstaller配置文件"""
        self.logger.info("\n📝 创建PyInstaller配置...")

        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件
datas = [
    ('backend', 'backend'),
    ('config', 'config'),
    ('frontend', 'frontend'),
    ('start_production_with_port_check.py', '.'),
    ('install_windows_service.py', '.'),
    ('deploy_production.bat', '.'),
]

# 隐藏导入
hiddenimports = [
    'uvicorn.logging',
    'uvicorn.loops',
    'uvicorn.loops.auto',
    'uvicorn.protocols',
    'uvicorn.protocols.http',
    'uvicorn.protocols.http.auto',
    'uvicorn.protocols.websockets',
    'uvicorn.protocols.websockets.auto',
    'uvicorn.lifespan',
    'uvicorn.lifespan.on',
    'fastapi',
    'fastapi.middleware',
    'fastapi.middleware.cors',
    'fastapi.staticfiles',
    'fastapi.responses',
    'sqlalchemy',
    'sqlalchemy.orm',
    'sqlalchemy.ext.asyncio',
    'pyodbc',
    'structlog',
    'schedule',
    'psutil',
    'win32serviceutil',
    'win32service',
    'win32event',
    'servicemanager',
    'pydantic_settings',
]

# 排除模块
excludes = [
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'tkinter',
    'PyQt5',
    'PySide2',
    'IPython',
    'jupyter',
    'notebook',
    'pytest',
    'unittest',
]

a = Analysis(
    ['start_production_with_port_check.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.package_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''

        spec_file = self.project_root / f"{self.package_name}.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)

        self.logger.info(f"✅ 配置文件已创建: {spec_file}")
        return spec_file

    def run_pyinstaller(self, spec_file):
        """运行PyInstaller"""
        self.logger.info("\n🔨 开始打包...")

        try:
            subprocess.run(
                [
                    sys.executable,
                    "-m",
                    "PyInstaller",
                    "--clean",
                    "--noconfirm",
                    str(spec_file),
                ],
                check=True,
            )

            self.logger.info("✅ PyInstaller打包完成")

        except subprocess.CalledProcessError as e:
            self.logger.info(f"❌ PyInstaller打包失败: {e}")
            raise

    def create_deployment_package(self):
        """创建部署包"""
        self.logger.info("\n📦 创建部署包...")

        # 创建部署目录
        deploy_dir = self.dist_dir / f"{self.package_name}-Deploy"
        deploy_dir.mkdir(exist_ok=True)

        # 复制可执行文件
        exe_file = self.dist_dir / f"{self.package_name}.exe"
        if exe_file.exists():
            shutil.copy2(exe_file, deploy_dir)
            self.logger.info(f"✅ 复制可执行文件: {exe_file.name}")

        # 复制部署脚本
        deploy_script = self.project_root / "deploy_production.bat"
        if deploy_script.exists():
            shutil.copy2(deploy_script, deploy_dir)
            self.logger.info(f"✅ 复制部署脚本: {deploy_script.name}")

        # 创建启动脚本
        start_script = deploy_dir / "start.bat"
        with open(start_script, 'w', encoding='utf-8') as f:
            f.write(
                f'''@echo off
chcp 65001 >nul
echo =========================================
echo YS-API V3.0 启动脚本
echo =========================================

echo 🚀 启动YS-API服务...
"{self.package_name}.exe"

pause
'''
            )

        self.logger.info(f"✅ 创建启动脚本: start.bat")

        # 创建README
        readme_file = deploy_dir / "README.txt"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(
                f'''YS-API V3.0 生产环境部署包

📋 部署说明:
1. 解压到目标服务器目录
2. 修改 config.ini 中的数据库连接信息
3. 运行 deploy_production.bat 进行自动部署
4. 或直接运行 start.bat 启动服务

🔧 服务管理:
- 启动服务: net start YSAPIService
- 停止服务: net stop YSAPIService
- 重启服务: net stop YSAPIService && net start YSAPIService

🌐 访问地址:
- 管理界面: http://localhost:8000/frontend/database-v2.html
- 维护管理: http://localhost:8000/maintenance.html
- API文档: http://localhost:8000/docs

📁 日志文件:
- 服务日志: C:\\YS-API\\logs\\windows_service.log
- 应用日志: C:\\YS-API\\logs\\production_enhanced.log

⚠️  注意事项:
- 需要管理员权限安装Windows服务
- 确保SQL Server数据库服务正常运行
- 确保网络连接正常

技术支持: YS-API V3.0
'''
            )

        self.logger.info(f"✅ 创建说明文档: README.txt")

        return deploy_dir

    def create_installer_script(self, deploy_dir):
        """创建安装脚本"""
        self.logger.info("\n🔧 创建安装脚本...")

        installer_script = deploy_dir / "install.bat"
        with open(installer_script, 'w', encoding='utf-8') as f:
            f.write(
                f'''@echo off
chcp 65001 >nul
echo =========================================
echo YS-API V3.0 自动安装脚本
echo =========================================

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 请以管理员身份运行此脚本
    pause
    exit /b 1
)

:: 设置安装目录
set INSTALL_DIR=C:\\YS-API
set LOG_DIR=%INSTALL_DIR%\\logs

echo 📁 安装目录: %INSTALL_DIR%
echo 📁 日志目录: %LOG_DIR%

:: 创建目录
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

:: 复制文件
echo 🔄 复制程序文件...
copy "{self.package_name}.exe" "%INSTALL_DIR%\\"
copy "deploy_production.bat" "%INSTALL_DIR%\\"

:: 创建必要的目录
echo 📁 创建必要目录...
if not exist "%INSTALL_DIR%\\cache" mkdir "%INSTALL_DIR%\\cache"
if not exist "%INSTALL_DIR%\\logs\\temp" mkdir "%INSTALL_DIR%\\logs\\temp"
if not exist "%INSTALL_DIR%\\logs\\cache" mkdir "%INSTALL_DIR%\\logs\\cache"
if not exist "%INSTALL_DIR%\\downloads" mkdir "%INSTALL_DIR%\\downloads"
if not exist "%INSTALL_DIR%\\uploads\\temp" mkdir "%INSTALL_DIR%\\uploads\\temp"

:: 安装Windows服务
echo 🔧 安装Windows服务...
cd /d "%INSTALL_DIR%"
"{self.package_name}.exe" --install-service

if %errorLevel% equ 0 (
    echo ✅ Windows服务安装成功

    :: 启动服务
    echo 🚀 启动服务...
    "{self.package_name}.exe" --start-service

    if %errorLevel% equ 0 (
        echo ✅ 服务启动成功
        echo.
        echo 📋 服务信息:
        echo   服务名称: YSAPIService
        echo   显示名称: YS-API V3.0 Data Sync Service
        echo   端口: 8000
        echo   管理界面: http://localhost:8000/frontend/database-v2.html
        echo.
        echo 📝 管理命令:
        echo   启动服务: net start YSAPIService
        echo   停止服务: net stop YSAPIService
        echo   重启服务: net stop YSAPIService && net start YSAPIService
        echo.
        echo 📁 日志文件:
        echo   %LOG_DIR%\\windows_service.log
        echo   %LOG_DIR%\\production_enhanced.log
        echo   %LOG_DIR%\\auto_sync.log
    ) else (
        echo ❌ 服务启动失败
    )
) else (
    echo ❌ Windows服务安装失败
)

echo.
echo =========================================
echo 安装完成
echo =========================================
pause
'''
            )

        self.logger.info(f"✅ 创建安装脚本: install.bat")

    def generate_package_info(self, deploy_dir):
        """生成包信息"""
        self.logger.info("\n📊 生成包信息...")

        # 计算包大小
        total_size = 0
        file_count = 0

        for root, dirs, files in os.walk(deploy_dir):
            for file in files:
                file_path = os.path.join(root, file)
                total_size += os.path.getsize(file_path)
                file_count += 1

        # 创建包信息文件
        package_info = {
            "package_name": self.package_name,
            "version": "3.0.0",
            "build_time": str(Path().cwd()),
            "file_count": file_count,
            "total_size_mb": round(total_size / 1024 / 1024, 2),
            "included_modules": [
                "14个业务模块",
                "字段配置模块",
                "统一字段配置模块",
                "维护管理模块",
                "自动同步模块",
                "数据库管理模块",
                "Windows服务管理",
            ],
            "features": [
                "自动端口检查",
                "进程自动恢复",
                "定时文件清理",
                "缓存管理",
                "日志轮转",
                "健康监控",
            ],
        }

        info_file = deploy_dir / "package_info.json"
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(package_info, f, indent=2, ensure_ascii=False)

        self.logger.info(
            f"✅ 包信息: {file_count}个文件, {package_info['total_size_mb']}MB"
        )
        self.logger.info(f"✅ 包信息文件: package_info.json")

    def run(self):
        """运行完整打包流程"""
        try:
            # 1. 检查环境
            self.check_environment()

            # 2. 安装依赖
            self.install_dependencies()

            # 3. 清理构建目录
            self.clean_build_dirs()

            # 4. 创建PyInstaller配置
            spec_file = self.create_pyinstaller_spec()

            # 5. 运行PyInstaller
            self.run_pyinstaller(spec_file)

            # 6. 创建部署包
            deploy_dir = self.create_deployment_package()

            # 7. 创建安装脚本
            self.create_installer_script(deploy_dir)

            # 8. 生成包信息
            self.generate_package_info(deploy_dir)

            self.logger.info("\n" + "=" * 50)
            self.logger.info("🎉 打包完成!")
            self.logger.info(f"📦 部署包位置: {deploy_dir}")
            self.logger.info("📋 下一步:")
            self.logger.info("   1. 将部署包复制到目标服务器")
            self.logger.info("   2. 运行 install.bat 进行安装")
            self.logger.info("   3. 或运行 deploy_production.bat 进行部署")
            self.logger.info("=" * 50)

        except Exception:
            self.logger.info(f"\n❌ 打包失败: {e}")
            sys.exit(1)


if __name__ == "__main__":
    packager = ProductionPackager()
    packager.run()
