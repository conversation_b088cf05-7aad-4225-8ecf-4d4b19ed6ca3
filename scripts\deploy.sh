#!/bin/bash
# YS-API V3.0 部署脚本

set -e

echo "🚀 开始部署 YS-API V3.0..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker 未安装，请先安装 Docker${NC}"
    exit 1
fi

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Docker Compose 未安装，请先安装 Docker Compose${NC}"
    exit 1
fi

# 备份当前部署
if [ -f "backup/current_deployment.tar.gz" ]; then
    echo -e "${YELLOW}备份当前部署...${NC}"
    mkdir -p backup
    tar -czf "backup/deployment_$(date +%Y%m%d_%H%M%S).tar.gz" \
        backend/ config/ docker-compose.yml 2>/dev/null || true
fi

# 停止现有服务
echo -e "${YELLOW}停止现有服务...${NC}"
docker-compose down --remove-orphans || true

# 清理旧镜像
echo -e "${YELLOW}清理旧镜像...${NC}"
docker image prune -f

# 构建新镜像
echo -e "${YELLOW}构建应用镜像...${NC}"
docker-compose build

# 启动服务
echo -e "${YELLOW}启动服务...${NC}"
docker-compose up -d

# 等待服务启动
echo -e "${YELLOW}等待服务启动...${NC}"
sleep 30

# 健康检查
echo -e "${YELLOW}执行健康检查...${NC}"
for i in {1..10}; do
    if curl -f http://localhost:5000/health &>/dev/null; then
        echo -e "${GREEN}✅ 服务启动成功！${NC}"
        echo -e "${GREEN}🌐 应用访问地址: http://localhost:5000${NC}"
        echo -e "${GREEN}📊 前端页面: http://localhost:5000/static/index.html${NC}"
        exit 0
    fi
    echo "等待服务响应... ($i/10)"
    sleep 10
done

echo -e "${RED}❌ 服务启动失败！${NC}"
docker-compose logs
exit 1
