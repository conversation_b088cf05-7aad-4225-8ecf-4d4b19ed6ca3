import asyncio
import logging
import os
import sys
from pathlib import Path

import servicemanager
import win32event
import win32service
import win32serviceutil
from start_production_enhanced import ProductionServiceManager

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 Windows服务安装脚本
实现程序崩溃、服务器重启、断电后的自动运行
"""


# 添加项目路径
sys.path.append(str(Path(__file__).parent / "backend" / "app"))


class YSAPIService(win32serviceutil.ServiceFramework):
    """YS-API Windows服务"""

    _svc_name_ = "YSAPIService"
    _svc_display_name_ = "YS-API V3.0 Data Sync Service"
    _svc_description_ = "用友云API数据同步服务"

    def __init___(self, args):
    """TODO: Add function description."""
    win32serviceutil.ServiceFramework.__init__(self, args)
    self.stop_event = win32event.CreateEvent(None, 0, 0, None)
    self.service_manager = None
    self.running = False

    # 设置日志（使用环境变量或相对路径）
    log_dir = os.environ.get(
        'YS_API_LOG_DIR', os.path.join(os.path.dirname(__file__), 'logs')
    )
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, 'windows_service.log')

    logging.basicConfig(
        filename=log_file,
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    )
    self.logger = logging.getLogger(__name__)

    def SvcStop(self):
        """停止服务"""
        self.logger.info("收到停止服务信号")
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.stop_event)
        self.running = False

    def SvcDoRun(self):
        """运行服务"""
        self.logger.info("开始运行YS-API服务")
        self.running = True

        try:
            # 创建服务管理器
            self.service_manager = ProductionServiceManager()

            # 运行服务
            asyncio.run(self._run_services())

        except Exception:
            self.logger.error(f"服务运行失败: {str(e)}")
            self.running = False

    async def _run_services(self):
        """运行所有服务"""
        try:
            # 启动所有服务
            await self.service_manager.start_all_services()

            # 保持服务运行
            while self.running:
                # 检查停止信号
                if (
                    win32event.WaitForSingleObject(self.stop_event, 1000)
                    == win32event.WAIT_OBJECT_0
                ):
                    break

                # 健康检查
                await self.service_manager.health_check()

                # 等待1秒
                await asyncio.sleep(1)

            # 停止所有服务
            await self.service_manager.stop_all_services()

        except Exception:
            self.logger.error(f"服务运行异常: {str(e)}")
            raise


def install_service():
    """安装Windows服务"""
    try:
        win32serviceutil.InstallService(
            YSAPIService._svc_name_,
            YSAPIService._svc_display_name_,
            YSAPIService._svc_description_,
            startType=win32service.SERVICE_AUTO_START,
        )
        self.logger.info("✅ Windows服务安装成功")
        self.logger.info("服务名称: YSAPIService")
        self.logger.info("显示名称: YS-API V3.0 Data Sync Service")
        self.logger.info("启动类型: 自动启动")

    except Exception:
        logging.error(f"服务安装失败: {str(e)}")


def uninstall_service():
    """卸载Windows服务"""
    try:
        win32serviceutil.RemoveService(YSAPIService._svc_name_)
        self.logger.info("✅ Windows服务卸载成功")

    except Exception:
        logging.error(f"服务卸载失败: {str(e)}")


def start_service():
    """启动服务"""
    try:
        win32serviceutil.StartService(YSAPIService._svc_name_)
        self.logger.info("✅ 服务启动成功")

    except Exception:
        logging.error(f"服务启动失败: {str(e)}")


def stop_service():
    """停止服务"""
    try:
        win32serviceutil.StopService(YSAPIService._svc_name_)
        self.logger.info("✅ 服务停止成功")

    except Exception:
        logging.error(f"服务停止失败: {str(e)}")


def restart_service():
    """重启服务"""
    try:
        win32serviceutil.RestartService(YSAPIService._svc_name_)
        self.logger.info("✅ 服务重启成功")

    except Exception:
        logging.error(f"服务重启失败: {str(e)}")


if __name__ == '__main__':
    if len(sys.argv) == 1:
        # 直接运行服务
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(YSAPIService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        # 处理命令行参数
        if sys.argv[1] == 'install':
            install_service()
        elif sys.argv[1] == 'uninstall':
            uninstall_service()
        elif sys.argv[1] == 'start':
            start_service()
        elif sys.argv[1] == 'stop':
            stop_service()
        elif sys.argv[1] == 'restart':
            restart_service()
        else:
            win32serviceutil.HandleCommandLine(YSAPIService)
