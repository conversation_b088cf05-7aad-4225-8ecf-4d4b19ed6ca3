[{"success": true, "code": 200, "message": "", "data": {"fieldVersion": 20230210, "appCode": "", "tokenSet": false, "tokenDoc": "", "tenantId": 0, "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "id": 2152020559581937665, "name": "计划订单列表查询", "apiClassifyId": 1623462659853647878, "apiClassifyName": "需求计划", "apiClassifyCode": "requirementsplanning.mr_mps_plan_workbench_batch_import", "parentApiClassifies": "", "functionId": "", "openMode": "", "description": "提供按条件分页查询计划订单列表信息的服务。", "auth": true, "bodyPassthrough": "", "healthExam": false, "healthStatus": false, "responseResultPassthrough": false, "contentType": "application/json", "returnPassthrough": "", "completeProxyUrl": "/yonbip/mfg/requirementsplanning/getPlanOrderList", "connectUrl": "/api/v1/getPlanOrderByPage", "sort": 20, "handler": "openapi", "httpRequestType": "POST", "openApi": true, "preset": false, "productId": "4a176d6a681a4ebdbd053262493b5dff", "productCode": "", "proxyUrl": "/yonbip/mfg/requirementsplanning/getPlanOrderList", "requestParamsDemo": "Url: /yonbip/mfg/requirementsplanning/getPlanOrderList?access_token=访问令牌 Body: { \"data\": { \"orgId\": \"1608788551787872266\", \"orgCode\": \"122701\", \"planParamId\": \"3115402872983808\", \"planParamCode\": \"MRP2019110200002\", \"startDate\": \"2022-01-10\", \"endDate\": \"2022-01-10\", \"supplyOrgId\": \"1608788551787872266\", \"supplyOrgCode\": \"122701\", \"departmentId\": \"1608788551787872266\", \"departmentCode\": \"bumen\", \"warehouseId\": \"1608788551787872266\", \"warehouseCode\": \"000012\", \"productIds\": [ \"1550141821342973955\", \"1550139381815705608\" ], \"productCodes\": [ \"000123\", \"000124\" ], \"planProperty\": [ \"1\", \"2\" ] } }", "requestProtocol": "HTTP", "serviceHttpMethod": "POST", "publishStatus": true, "approvalMsg": "", "rpcAppName": "", "rpcServiceName": "", "rpcMethodName": "", "rpcServiceUrl": "", "ma": false, "gmtCreate": "2024-12-09 15:09:26.000", "gmtUpdate": "2025-06-24 20:00:20.000", "address": "https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/mfg/requirementsplanning/getPlanOrderList", "productName": "", "productClassifyId": "", "productClassifyCode": "", "productClassifyName": "", "paramDTOS": {"paramDTOS": {"id": 2172771101278994437, "name": "data", "apiId": 2152020559581937665, "parentId": "", "children": {"children": [{"id": 2172771101278994438, "name": "pageIndex", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152045332953300996, "array": false, "paramDesc": "页号", "paramType": "number", "requestParamType": "BodyParam", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": 1, "required": true, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 8, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": -1, "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994439, "name": "pageSize", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152045332953300997, "array": false, "paramDesc": "每页条数", "paramType": "number", "requestParamType": "BodyParam", "path": "", "example": 10, "fullName": "", "ytenantId": 0, "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": 10, "required": true, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 4, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": -1, "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994440, "name": "orgId", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937671, "array": false, "paramDesc": "组织id，组织 Id 与组织编码不能同时为空，优先级：orgId>orgCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": 1608788551787872266, "fullName": "", "ytenantId": 0, "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994441, "name": "orgCode", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937672, "array": false, "paramDesc": "组织编码，组织 Id 与组织编码不能同时为空，优先级：orgId>orgCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": 122701, "fullName": "", "ytenantId": 0, "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994442, "name": "planParamId", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937673, "array": false, "paramDesc": "计划名称id，优先级：planParamId > planParamCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": 3115402872983808, "fullName": "", "ytenantId": 0, "paramOrder": 4, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994443, "name": "planParamCode", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937674, "array": false, "paramDesc": "计划名称编码，优先级：planParamId > planParamCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "MRP2019110200002", "fullName": "", "ytenantId": 0, "paramOrder": 5, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994444, "name": "startDate", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937675, "array": false, "paramDesc": "开始时间", "paramType": "date", "requestParamType": "BodyParam", "path": "", "example": "2022-01-10", "fullName": "", "ytenantId": 0, "paramOrder": 6, "bizType": "", "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "yyyy-MM-dd", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994445, "name": "endDate", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937676, "array": false, "paramDesc": "结束时间", "paramType": "date", "requestParamType": "BodyParam", "path": "", "example": "2022-01-10", "fullName": "", "ytenantId": 0, "paramOrder": 7, "bizType": "", "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "yyyy-MM-dd", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994446, "name": "supplyOrgId", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937677, "array": false, "paramDesc": "供应组织id，优先级：supplyOrgId > supplyOrgCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": 1608788551787872266, "fullName": "", "ytenantId": 0, "paramOrder": 8, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994447, "name": "supplyOrgCode", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937678, "array": false, "paramDesc": "供应组织编码，优先级：supplyOrgId > supplyOrgCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": 122701, "fullName": "", "ytenantId": 0, "paramOrder": 9, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994448, "name": "departmentId", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937679, "array": false, "paramDesc": "部门id，优先级：departmentId > departmentCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": 1608788551787872266, "fullName": "", "ytenantId": 0, "paramOrder": 10, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994449, "name": "departmentCode", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937680, "array": false, "paramDesc": "部门编码，优先级：departmentId > departmentCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "bumen", "fullName": "", "ytenantId": 0, "paramOrder": 11, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994450, "name": "warehouseId", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937681, "array": false, "paramDesc": "仓库Id，优先级：warehouseId > warehouseCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": 1608788551787872266, "fullName": "", "ytenantId": 0, "paramOrder": 12, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994451, "name": "warehouseCode", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937682, "array": false, "paramDesc": "仓库编码，优先级:warehouseId > warehouseCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": 12, "fullName": "", "ytenantId": 0, "paramOrder": 13, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994452, "name": "productIds", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937683, "array": true, "paramDesc": "物料id，优先级:productIds > productCodes", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[\"1550141821342973955\",\"1550139381815705608\"]", "fullName": "", "ytenantId": 0, "paramOrder": 14, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994453, "name": "productCodes", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937684, "array": true, "paramDesc": "物料编码，优先级:productIds > productCodes", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[\"000123\",\"000124\"]", "fullName": "", "ytenantId": 0, "paramOrder": 15, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2172771101278994454, "name": "planProperty", "apiId": 2152020559581937665, "parentId": 2172771101278994437, "defParamId": 2152020559581937685, "array": true, "paramDesc": "计划属性 1,采购 ,2,委外 ,3,自制 ,4,调拨 ,5,组织间需求", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "[\"1\",\"2\"]", "fullName": "", "ytenantId": 0, "paramOrder": 16, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "defParamId": 2152020559581937670, "array": false, "paramDesc": "参数", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}}, "queryParamDTOS": "", "ysApi": false, "presetTokenApi": false, "applyFlag": false, "cover": false, "paramMapDTOS": {"paramMapDTOS": {"id": 2172771101278994455, "name": "data", "apiId": 2152020559581937665, "parentId": "", "children": {"children": [{"id": 2172771101278994456, "name": "pageIndex", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "页号", "paramType": "number", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "pageIndex", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "number", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994457, "name": "pageSize", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "页数大小", "paramType": "number", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "pageSize", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "number", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994458, "name": "orgId", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "组织id，组织 Id 与组织编码不能同时为空，优先级：orgId>orgCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "orgId", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994459, "name": "orgCode", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "组织编码，组织 Id 与组织编码不能同时为空，优先级：orgId>orgCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "orgCode", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994460, "name": "planParamId", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "计划名称id，优先级：planParamId > planParamCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 4, "baseType": true, "aggregatedValueObject": false, "mapName": "planParamId", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994461, "name": "planParamCode", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "计划名称编码，优先级：planParamId > planParamCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 5, "baseType": true, "aggregatedValueObject": false, "mapName": "planParamCode", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994462, "name": "startDate", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "开始时间", "paramType": "date", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 6, "baseType": true, "aggregatedValueObject": false, "mapName": "startDate", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "date", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994463, "name": "endDate", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "结束时间", "paramType": "date", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 7, "baseType": true, "aggregatedValueObject": false, "mapName": "endDate", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "date", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994464, "name": "supplyOrgId", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "供应组织id，优先级：supplyOrgId > supplyOrgCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 8, "baseType": true, "aggregatedValueObject": false, "mapName": "supplyOrgId", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994465, "name": "supplyOrgCode", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "供应组织编码，优先级：supplyOrgId > supplyOrgCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 9, "baseType": true, "aggregatedValueObject": false, "mapName": "supplyOrgCode", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994466, "name": "departmentId", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "部门id，优先级：departmentId > departmentCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 10, "baseType": true, "aggregatedValueObject": false, "mapName": "departmentId", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994467, "name": "departmentCode", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "部门编码，优先级：departmentId > departmentCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 11, "baseType": true, "aggregatedValueObject": false, "mapName": "departmentCode", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994468, "name": "warehouseId", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "仓库Id，优先级：warehouseId > warehouseCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 12, "baseType": true, "aggregatedValueObject": false, "mapName": "warehouseId", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994469, "name": "warehouseCode", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "仓库编码，优先级:warehouseId > warehouseCode", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 13, "baseType": true, "aggregatedValueObject": false, "mapName": "warehouseCode", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994470, "name": "productIds", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "物料id，优先级:productIds > productCodes", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 14, "baseType": true, "aggregatedValueObject": false, "mapName": "productIds", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994471, "name": "productCodes", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "物料编码，优先级:productIds > productCodes", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 15, "baseType": true, "aggregatedValueObject": false, "mapName": "productCodes", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2172771101278994472, "name": "planProperty", "apiId": 2152020559581937665, "parentId": 2172771101278994455, "defParamId": "", "array": false, "paramDesc": "计划属性 1,采购 ,2,委外 ,3,自制 ,4,调拨 ,5,组织间需求", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 16, "baseType": true, "aggregatedValueObject": false, "mapName": "planProperty", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "defParamId": "", "array": false, "paramDesc": "参数", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "data", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}}, "paramReturnDTOS": {"paramReturnDTOS": [{"id": 2172771101278994578, "name": "code", "apiId": 2152020559581937665, "parentId": "", "defParamId": 2152804708941037571, "array": false, "paramDesc": "返回状态码，200 成功 999 失败", "paramType": "string", "requestParamType": "", "path": "", "example": 200, "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994579, "name": "message", "apiId": 2152020559581937665, "parentId": "", "defParamId": 2152804708941037572, "array": false, "paramDesc": "操作通知信息", "paramType": "string", "requestParamType": "", "path": "", "example": "操作成功", "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994473, "name": "data", "apiId": 2152020559581937665, "parentId": "", "children": {"children": [{"id": 2172771101278994474, "name": "recordList", "apiId": 2152020559581937665, "parentId": 2172771101278994473, "children": {"children": [{"id": 2172771101278994511, "name": "id", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037575, "array": false, "paramDesc": "计划订单id", "paramType": "string", "requestParamType": "", "path": "", "example": 1630874665323855906, "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994512, "name": "orgId", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037576, "array": false, "paramDesc": "计划组织id", "paramType": "string", "requestParamType": "", "path": "", "example": 1608788551787872266, "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994513, "name": "orgCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037577, "array": false, "paramDesc": "计划组织编码", "paramType": "string", "requestParamType": "", "path": "", "example": 2301070001, "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994514, "name": "transTypeId", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037578, "array": false, "paramDesc": "交易类型id", "paramType": "string", "requestParamType": "", "path": "", "example": 1601383117764427779, "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994515, "name": "transTypeCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037579, "array": false, "paramDesc": "交易类型编码", "paramType": "string", "requestParamType": "", "path": "", "example": "GEN-11", "fullName": "", "ytenantId": 0, "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994516, "name": "planParamId", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037580, "array": false, "paramDesc": "计划名称id", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": 0, "paramOrder": 5, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994517, "name": "planParamCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037581, "array": false, "paramDesc": "计划名称编码", "paramType": "string", "requestParamType": "", "path": "", "example": "GEN0", "fullName": "", "ytenantId": 0, "paramOrder": 6, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994518, "name": "planParamName", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037582, "array": false, "paramDesc": "计划名称", "paramType": "string", "requestParamType": "", "path": "", "example": "LRP2***********", "fullName": "", "ytenantId": 0, "paramOrder": 7, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994519, "name": "createType", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037583, "array": false, "paramDesc": "创建类型", "paramType": "string", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": 0, "paramOrder": 8, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994520, "name": "code", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037584, "array": false, "paramDesc": "计划订单号", "paramType": "string", "requestParamType": "", "path": "", "example": "GEN00011", "fullName": "", "ytenantId": 0, "paramOrder": 9, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994521, "name": "productCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037585, "array": false, "paramDesc": "物料编码", "paramType": "string", "requestParamType": "", "path": "", "example": 45, "fullName": "", "ytenantId": 0, "paramOrder": 10, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994522, "name": "productId", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037586, "array": false, "paramDesc": "物料id", "paramType": "string", "requestParamType": "", "path": "", "example": 159192034273814118, "fullName": "", "ytenantId": 0, "paramOrder": 11, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994523, "name": "planProperty", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037587, "array": false, "paramDesc": "计划属性", "paramType": "string", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 12, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994524, "name": "bomId", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037588, "array": false, "paramDesc": "BOM唯一标识", "paramType": "string", "requestParamType": "", "path": "", "example": 1591920342738141189, "fullName": "", "ytenantId": 0, "paramOrder": 13, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994525, "name": "bomCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037589, "array": false, "paramDesc": "BOM编码", "paramType": "string", "requestParamType": "", "path": "", "example": "likun-M.Code-002（固）", "fullName": "", "ytenantId": 0, "paramOrder": 14, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994526, "name": "uom", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037590, "array": false, "paramDesc": "单位", "paramType": "string", "requestParamType": "", "path": "", "example": 1570768779829837833, "fullName": "", "ytenantId": 0, "paramOrder": 15, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994527, "name": "uomName", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037591, "array": false, "paramDesc": "单位名称", "paramType": "string", "requestParamType": "", "path": "", "example": "千克", "fullName": "", "ytenantId": 0, "paramOrder": 16, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994528, "name": "uomCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037592, "array": false, "paramDesc": "单位编码", "paramType": "string", "requestParamType": "", "path": "", "example": "kg", "fullName": "", "ytenantId": 0, "paramOrder": 17, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994529, "name": "assistUnit", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037593, "array": false, "paramDesc": "主计量单位", "paramType": "string", "requestParamType": "", "path": "", "example": 1570768797009707017, "fullName": "", "ytenantId": 0, "paramOrder": 18, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994530, "name": "assistUnitCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037594, "array": false, "paramDesc": "主计量单位编码", "paramType": "string", "requestParamType": "", "path": "", "example": "kg", "fullName": "", "ytenantId": 0, "paramOrder": 19, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994531, "name": "assistUnitName", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037595, "array": false, "paramDesc": "主计量单位名称", "paramType": "string", "requestParamType": "", "path": "", "example": "千克", "fullName": "", "ytenantId": 0, "paramOrder": 20, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994532, "name": "originQuantity", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037596, "array": false, "paramDesc": "原始数量", "paramType": "number", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": 0, "paramOrder": 21, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2172771101278994533, "name": "assistUnitCount", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037597, "array": false, "paramDesc": "主计量计划量", "paramType": "number", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": 0, "paramOrder": 22, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2172771101278994534, "name": "suggestPlanQuantity", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037598, "array": false, "paramDesc": "建议计划量", "paramType": "number", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": 0, "paramOrder": 23, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2172771101278994535, "name": "inputQty", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037599, "array": false, "paramDesc": "投入计划量", "paramType": "number", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": 0, "paramOrder": 24, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2172771101278994536, "name": "issuedQuantity", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037600, "array": false, "paramDesc": "已下达量", "paramType": "number", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": 0, "paramOrder": 25, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2172771101278994537, "name": "startDate", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037601, "array": false, "paramDesc": "开工日期", "paramType": "string", "requestParamType": "", "path": "", "example": "2023-01-06 00:00:00", "fullName": "", "ytenantId": 0, "paramOrder": 26, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994538, "name": "finishDate", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037602, "array": false, "paramDesc": "完工日期", "paramType": "string", "requestParamType": "", "path": "", "example": "2023-01-06 00:00:00", "fullName": "", "ytenantId": 0, "paramOrder": 27, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994539, "name": "status", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037603, "array": false, "paramDesc": "状态", "paramType": "string", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": 0, "paramOrder": 28, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994540, "name": "demandOrgId", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037604, "array": false, "paramDesc": "需求组织", "paramType": "string", "requestParamType": "", "path": "", "example": 1570766056850456576, "fullName": "", "ytenantId": 0, "paramOrder": 29, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994541, "name": "demandOrgCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037605, "array": false, "paramDesc": "需求组织编码", "paramType": "string", "requestParamType": "", "path": "", "example": 310, "fullName": "", "ytenantId": 0, "paramOrder": 30, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994542, "name": "supplyOrgId", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037606, "array": false, "paramDesc": "供应组织", "paramType": "string", "requestParamType": "", "path": "", "example": 1570766056850456576, "fullName": "", "ytenantId": 0, "paramOrder": 31, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994543, "name": "supplyOrgCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037607, "array": false, "paramDesc": "供应组织编码", "paramType": "string", "requestParamType": "", "path": "", "example": 310, "fullName": "", "ytenantId": 0, "paramOrder": 32, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994544, "name": "invOrgId", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037608, "array": false, "paramDesc": "入库组织", "paramType": "string", "requestParamType": "", "path": "", "example": 1570766056850456576, "fullName": "", "ytenantId": 0, "paramOrder": 33, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994545, "name": "invOrgCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037609, "array": false, "paramDesc": "入库组织编码", "paramType": "string", "requestParamType": "", "path": "", "example": 310, "fullName": "", "ytenantId": 0, "paramOrder": 34, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994546, "name": "source", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037610, "array": false, "paramDesc": "来源单据类型", "paramType": "string", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": 0, "paramOrder": 35, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994547, "name": "upcode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037611, "array": false, "paramDesc": "来源单据号", "paramType": "string", "requestParamType": "", "path": "", "example": "YCD20221228000006", "fullName": "", "ytenantId": 0, "paramOrder": 36, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994548, "name": "srcSourceProductId", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037612, "array": false, "paramDesc": "来源物料id", "paramType": "string", "requestParamType": "", "path": "", "example": 1570766056850456576, "fullName": "", "ytenantId": 0, "paramOrder": 37, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994549, "name": "srcSourceProductCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037613, "array": false, "paramDesc": "来源物料编码", "paramType": "string", "requestParamType": "", "path": "", "example": 1035000045, "fullName": "", "ytenantId": 0, "paramOrder": 38, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994550, "name": "firstsource", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037614, "array": false, "paramDesc": "源头单据类型", "paramType": "string", "requestParamType": "", "path": "", "example": 280, "fullName": "", "ytenantId": 0, "paramOrder": 39, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994551, "name": "firstupcode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037615, "array": false, "paramDesc": "源头单据号", "paramType": "string", "requestParamType": "", "path": "", "example": "YCD20221228000006", "fullName": "", "ytenantId": 0, "paramOrder": 40, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994552, "name": "firstsourceautoid", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037616, "array": false, "paramDesc": "源头单据子表id", "paramType": "string", "requestParamType": "", "path": "", "example": 1623614151955316744, "fullName": "", "ytenantId": 0, "paramOrder": 41, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994553, "name": "sourceMaterialId", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037617, "array": false, "paramDesc": "源头物料", "paramType": "string", "requestParamType": "", "path": "", "example": 1590995696384737289, "fullName": "", "ytenantId": 0, "paramOrder": 42, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994554, "name": "sourceMaterialCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037618, "array": false, "paramDesc": "源头物料编码", "paramType": "string", "requestParamType": "", "path": "", "example": 1035000045, "fullName": "", "ytenantId": 0, "paramOrder": 43, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994555, "name": "departmentId", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037619, "array": false, "paramDesc": "部门id", "paramType": "string", "requestParamType": "", "path": "", "example": 1570766056850456576, "fullName": "", "ytenantId": 0, "paramOrder": 44, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994556, "name": "departmentCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037620, "array": false, "paramDesc": "部门编码", "paramType": "string", "requestParamType": "", "path": "", "example": 1111, "fullName": "", "ytenantId": 0, "paramOrder": 45, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994557, "name": "departmentName", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037621, "array": false, "paramDesc": "部门名称", "paramType": "string", "requestParamType": "", "path": "", "example": "部门1", "fullName": "", "ytenantId": 0, "paramOrder": 46, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994558, "name": "warehouseId", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037622, "array": false, "paramDesc": "仓库id", "paramType": "string", "requestParamType": "", "path": "", "example": 1570766056850456576, "fullName": "", "ytenantId": 0, "paramOrder": 47, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994559, "name": "warehouseCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037623, "array": false, "paramDesc": "仓库编码", "paramType": "string", "requestParamType": "", "path": "", "example": 1111, "fullName": "", "ytenantId": 0, "paramOrder": 48, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994560, "name": "warehouseName", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037624, "array": false, "paramDesc": "仓库名称", "paramType": "string", "requestParamType": "", "path": "", "example": "仓库1", "fullName": "", "ytenantId": 0, "paramOrder": 49, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994561, "name": "isClosed", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037625, "array": false, "paramDesc": "关闭标识", "paramType": "boolean", "requestParamType": "", "path": "", "example": false, "fullName": "", "ytenantId": 0, "paramOrder": 50, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994562, "name": "remark", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037626, "array": false, "paramDesc": "备注", "paramType": "string", "requestParamType": "", "path": "", "example": "remark", "fullName": "", "ytenantId": 0, "paramOrder": 51, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994563, "name": "projectId", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037627, "array": false, "paramDesc": "项目id", "paramType": "string", "requestParamType": "", "path": "", "example": 1570766056850456576, "fullName": "", "ytenantId": 0, "paramOrder": 52, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994564, "name": "projectIdCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037628, "array": false, "paramDesc": "项目编码", "paramType": "string", "requestParamType": "", "path": "", "example": 1111, "fullName": "", "ytenantId": 0, "paramOrder": 53, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994565, "name": "projectIdName", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037629, "array": false, "paramDesc": "项目名称", "paramType": "string", "requestParamType": "", "path": "", "example": "项目一号", "fullName": "", "ytenantId": 0, "paramOrder": 54, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994566, "name": "wbs", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037630, "array": false, "paramDesc": "wbs任务id", "paramType": "string", "requestParamType": "", "path": "", "example": 1570766056850456576, "fullName": "", "ytenantId": 0, "paramOrder": 55, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994567, "name": "wbsCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037631, "array": false, "paramDesc": "wbs任务编码", "paramType": "string", "requestParamType": "", "path": "", "example": 1111, "fullName": "", "ytenantId": 0, "paramOrder": 56, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994568, "name": "wbsName", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037632, "array": false, "paramDesc": "wbs任务名称", "paramType": "string", "requestParamType": "", "path": "", "example": "wbs任务一号", "fullName": "", "ytenantId": 0, "paramOrder": 57, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994569, "name": "activity", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037633, "array": false, "paramDesc": "活动id", "paramType": "string", "requestParamType": "", "path": "", "example": 1570766056850456576, "fullName": "", "ytenantId": 0, "paramOrder": 58, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994570, "name": "activityCode", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037634, "array": false, "paramDesc": "活动编码", "paramType": "string", "requestParamType": "", "path": "", "example": 1111, "fullName": "", "ytenantId": 0, "paramOrder": 59, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994571, "name": "activityName", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "defParamId": 2152804708941037635, "array": false, "paramDesc": "活动名称", "paramType": "string", "requestParamType": "", "path": "", "example": "活动一号", "fullName": "", "ytenantId": 0, "paramOrder": 60, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994475, "name": "planOrderItem", "apiId": 2152020559581937665, "parentId": 2172771101278994474, "children": {"children": [{"id": 2172771101278994476, "name": "itemProductId", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037637, "array": false, "paramDesc": "物料id", "paramType": "string", "requestParamType": "", "path": "", "example": 1681453239576297481, "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994477, "name": "itemProductCode", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037638, "array": false, "paramDesc": "物料编码", "paramType": "string", "requestParamType": "", "path": "", "example": "wlfl014", "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994478, "name": "itemProductName", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037639, "array": false, "paramDesc": "物料名称", "paramType": "string", "requestParamType": "", "path": "", "example": "WC1", "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994479, "name": "mainUnitId", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037640, "array": false, "paramDesc": "主计量id", "paramType": "string", "requestParamType": "", "path": "", "example": 1674787939942400002, "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994480, "name": "mainUnitCode", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037641, "array": false, "paramDesc": "主计量编码", "paramType": "string", "requestParamType": "", "path": "", "example": "MKT", "fullName": "", "ytenantId": 0, "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994481, "name": "mainUnitName", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037642, "array": false, "paramDesc": "主计量名称", "paramType": "string", "requestParamType": "", "path": "", "example": "立方米", "fullName": "", "ytenantId": 0, "paramOrder": 5, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994482, "name": "stockUnitId", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037643, "array": false, "paramDesc": "BOM单位id", "paramType": "string", "requestParamType": "", "path": "", "example": 1674787939942400004, "fullName": "", "ytenantId": 0, "paramOrder": 6, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994483, "name": "stockUnitCode", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037644, "array": false, "paramDesc": "BOM单位编码", "paramType": "string", "requestParamType": "", "path": "", "example": "MTQ", "fullName": "", "ytenantId": 0, "paramOrder": 7, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994484, "name": "stockUnitName", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037645, "array": false, "paramDesc": "BOM单位名称", "paramType": "string", "requestParamType": "", "path": "", "example": "平方千米", "fullName": "", "ytenantId": 0, "paramOrder": 8, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994485, "name": "changeRate", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037646, "array": false, "paramDesc": "换算率", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 9, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2172771101278994486, "name": "requirementQuantity", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037647, "array": false, "paramDesc": "需求数量", "paramType": "number", "requestParamType": "", "path": "", "example": 5.01, "fullName": "", "ytenantId": 0, "paramOrder": 10, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2172771101278994487, "name": "auxiliaryRequirementQuantity", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037648, "array": false, "paramDesc": "需求件数", "paramType": "number", "requestParamType": "", "path": "", "example": 5.01, "fullName": "", "ytenantId": 0, "paramOrder": 11, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2172771101278994488, "name": "stockOrgId", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037649, "array": false, "paramDesc": "库存单位id", "paramType": "string", "requestParamType": "", "path": "", "example": 1681369238604349442, "fullName": "", "ytenantId": 0, "paramOrder": 12, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994489, "name": "stockOrgCode", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037650, "array": false, "paramDesc": "库存单位编码", "paramType": "string", "requestParamType": "", "path": "", "example": "zzw", "fullName": "", "ytenantId": 0, "paramOrder": 13, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994490, "name": "stockOrgName", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037651, "array": false, "paramDesc": "库存单位名称", "paramType": "string", "requestParamType": "", "path": "", "example": "库存w", "fullName": "", "ytenantId": 0, "paramOrder": 14, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994491, "name": "warehouseId", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037652, "array": false, "paramDesc": "仓库id", "paramType": "string", "requestParamType": "", "path": "", "example": 1681372373925232646, "fullName": "", "ytenantId": 0, "paramOrder": 15, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994492, "name": "warehouseCode", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037653, "array": false, "paramDesc": "仓库编码", "paramType": "string", "requestParamType": "", "path": "", "example": "w2", "fullName": "", "ytenantId": 0, "paramOrder": 16, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994493, "name": "warehouseName", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037654, "array": false, "paramDesc": "仓库名称", "paramType": "string", "requestParamType": "", "path": "", "example": "仓库w2", "fullName": "", "ytenantId": 0, "paramOrder": 17, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994494, "name": "reqDate", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037655, "array": false, "paramDesc": "需求日期", "paramType": "date", "requestParamType": "", "path": "", "example": "2024-04-08 23:59:59", "fullName": "", "ytenantId": 0, "paramOrder": 18, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "yyyy-MM-dd HH:mm:ss", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994495, "name": "remark", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037656, "array": false, "paramDesc": "备注", "paramType": "string", "requestParamType": "", "path": "", "example": "备注", "fullName": "", "ytenantId": 0, "paramOrder": 19, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994496, "name": "substituteFlag", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037657, "array": false, "paramDesc": "替代标识", "paramType": "string", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": 0, "paramOrder": 20, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994499, "name": "projectId", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037660, "array": false, "paramDesc": "项目id", "paramType": "string", "requestParamType": "", "path": "", "example": 1654046039626743861, "fullName": "", "ytenantId": 0, "paramOrder": 23, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994500, "name": "projectIdCode", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037661, "array": false, "paramDesc": "项目编码", "paramType": "string", "requestParamType": "", "path": "", "example": "pj1", "fullName": "", "ytenantId": 0, "paramOrder": 24, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994501, "name": "projectIdName", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037662, "array": false, "paramDesc": "项目名称", "paramType": "string", "requestParamType": "", "path": "", "example": "项目名称", "fullName": "", "ytenantId": 0, "paramOrder": 25, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994502, "name": "wbs", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037663, "array": false, "paramDesc": "wbs任务id", "paramType": "string", "requestParamType": "", "path": "", "example": 1570766056850456576, "fullName": "", "ytenantId": 0, "paramOrder": 26, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994503, "name": "wbsCode", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037664, "array": false, "paramDesc": "wbs任务编码", "paramType": "string", "requestParamType": "", "path": "", "example": 1111, "fullName": "", "ytenantId": 0, "paramOrder": 27, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994504, "name": "wbsName", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037665, "array": false, "paramDesc": "wbs任务名称", "paramType": "string", "requestParamType": "", "path": "", "example": "wbs任务一号", "fullName": "", "ytenantId": 0, "paramOrder": 28, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994505, "name": "activity", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037666, "array": false, "paramDesc": "活动id", "paramType": "string", "requestParamType": "", "path": "", "example": 1570766056850456575, "fullName": "", "ytenantId": 0, "paramOrder": 29, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994506, "name": "activityCode", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037667, "array": false, "paramDesc": "活动编码", "paramType": "string", "requestParamType": "", "path": "", "example": 1111, "fullName": "", "ytenantId": 0, "paramOrder": 30, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994507, "name": "activityName", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037668, "array": false, "paramDesc": "活动名称", "paramType": "string", "requestParamType": "", "path": "", "example": "活动一号", "fullName": "", "ytenantId": 0, "paramOrder": 31, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994508, "name": "reserveid", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037669, "array": false, "paramDesc": "跟踪线索id", "paramType": "string", "requestParamType": "", "path": "", "example": 1570766056850456574, "fullName": "", "ytenantId": 0, "paramOrder": 32, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994509, "name": "reserveTypeName", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037670, "array": false, "paramDesc": "需求跟踪方式", "paramType": "string", "requestParamType": "", "path": "", "example": "自定义", "fullName": "", "ytenantId": 0, "paramOrder": 33, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994510, "name": "reserveName", "apiId": 2152020559581937665, "parentId": 2172771101278994475, "defParamId": 2152804708941037671, "array": false, "paramDesc": "跟踪线索", "paramType": "string", "requestParamType": "", "path": "", "example": "跟踪线索", "fullName": "", "ytenantId": 0, "paramOrder": 34, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}]}, "defParamId": 2152804708941037636, "array": true, "paramDesc": "计划订单备料", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 61, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}]}, "defParamId": 2152804708941037574, "array": false, "paramDesc": "数据信息", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2172771101278994572, "name": "pageIndex", "apiId": 2152020559581937665, "parentId": 2172771101278994473, "defParamId": 2152804708941037672, "array": false, "paramDesc": "当前页码", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 0, "maxLength": 10, "enableMulti": false}, {"id": 2172771101278994573, "name": "pageSize", "apiId": 2152020559581937665, "parentId": 2172771101278994473, "defParamId": 2152804708941037673, "array": false, "paramDesc": "每页条数", "paramType": "number", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 0, "maxLength": 10, "enableMulti": false}, {"id": 2172771101278994574, "name": "recordCount", "apiId": 2152020559581937665, "parentId": 2172771101278994473, "defParamId": 2152804708941037674, "array": false, "paramDesc": "总条数", "paramType": "number", "requestParamType": "", "path": "", "example": 13, "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 0, "maxLength": 10, "enableMulti": false}, {"id": 2172771101278994575, "name": "pageCount", "apiId": 2152020559581937665, "parentId": 2172771101278994473, "defParamId": 2152804708941037675, "array": false, "paramDesc": "页码数", "paramType": "number", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": 0, "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 0, "maxLength": 10, "enableMulti": false}, {"id": 2172771101278994576, "name": "beginPageIndex", "apiId": 2152020559581937665, "parentId": 2172771101278994473, "defParamId": 2162369558160605191, "array": false, "paramDesc": "开始页码", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 5, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 0, "maxLength": 10, "enableMulti": false}, {"id": 2172771101278994577, "name": "endPageIndex", "apiId": 2152020559581937665, "parentId": 2172771101278994473, "defParamId": 2162369558160605192, "array": false, "paramDesc": "结束页码", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 6, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 0, "maxLength": 10, "enableMulti": false}]}, "defParamId": 2152804708941037573, "array": true, "paramDesc": "数据", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "defaultValue": "", "required": "", "visible": "", "gmtCreate": "2025-01-06 14:10:47.000", "gmtUpdate": "2025-01-06 14:10:47.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}]}, "returnFormatType": "JSON", "paramConstDTOS": "", "paramConstMapDTOS": "", "apiDemoReturnDTOS": {"apiDemoReturnDTOS": [{"id": 2172771109868929031, "apiId": 2152020559581937665, "content": "{ \"code\": \"200\", \"message\": \"操作成功\", \"data\": [ { \"recordList\": { \"id\": \"1630874665323855906\", \"orgId\": \"1608788551787872266\", \"orgCode\": \"***********\", \"transTypeId\": \"1601383117764427779\", \"transTypeCode\": \"GEN-11\", \"planParamId\": \"****************\", \"planParamCode\": \"GEN0\", \"planParamName\": \"LRP2***********\", \"createType\": \"2\", \"code\": \"GEN00011\", \"productCode\": \"000045\", \"productId\": \"159192034273814118\", \"planProperty\": \"1\", \"bomId\": \"1591920342738141189\", \"bomCode\": \"likun-M.Code-002（固）\", \"uom\": \"1570768779829837833\", \"uomName\": \"千克\", \"uomCode\": \"kg\", \"assistUnit\": \"1570768797009707017\", \"assistUnitCode\": \"kg\", \"assistUnitName\": \"千克\", \"originQuantity\": 10, \"assistUnitCount\": 10, \"suggestPlanQuantity\": 10, \"inputQty\": 10, \"issuedQuantity\": 0, \"startDate\": \"2023-01-06 00:00:00\", \"finishDate\": \"2023-01-06 00:00:00\", \"status\": \"0\", \"demandOrgId\": \"1570766056850456576\", \"demandOrgCode\": \"00310\", \"supplyOrgId\": \"1570766056850456576\", \"supplyOrgCode\": \"00310\", \"invOrgId\": \"1570766056850456576\", \"invOrgCode\": \"00310\", \"source\": \"10\", \"upcode\": \"YCD20221228000006\", \"srcSourceProductId\": \"1570766056850456576\", \"srcSourceProductCode\": \"1035000045\", \"firstsource\": \"280\", \"firstupcode\": \"YCD20221228000006\", \"firstsourceautoid\": \"1623614151955316744\", \"sourceMaterialId\": \"1590995696384737289\", \"sourceMaterialCode\": \"1035000045\", \"departmentId\": \"1570766056850456576\", \"departmentCode\": \"001111\", \"departmentName\": \"部门1\", \"warehouseId\": \"1570766056850456576\", \"warehouseCode\": \"001111\", \"warehouseName\": \"仓库1\", \"isClosed\": false, \"remark\": \"remark\", \"projectId\": \"1570766056850456576\", \"projectIdCode\": \"001111\", \"projectIdName\": \"项目一号\", \"wbs\": \"1570766056850456576\", \"wbsCode\": \"001111\", \"wbsName\": \"wbs任务一号\", \"activity\": \"1570766056850456576\", \"activityCode\": \"001111\", \"activityName\": \"活动一号\", \"planOrderItem\": [ { \"itemProductId\": \"1681453239576297481\", \"itemProductCode\": \"wlfl014\", \"itemProductName\": \"WC1\", \"mainUnitId\": \"1674787939942400002\", \"mainUnitCode\": \"MKT\", \"mainUnitName\": \"立方米\", \"stockUnitId\": \"1674787939942400004\", \"stockUnitCode\": \"MTQ\", \"stockUnitName\": \"平方千米\", \"changeRate\": 1, \"requirementQuantity\": 5.01, \"auxiliaryRequirementQuantity\": 5.01, \"stockOrgId\": \"1681369238604349442\", \"stockOrgCode\": \"zzw\", \"stockOrgName\": \"库存w\", \"warehouseId\": \"1681372373925232646\", \"warehouseCode\": \"w2\", \"warehouseName\": \"仓库w2\", \"reqDate\": \"2024-04-08 23:59:59\", \"remark\": \"备注\", \"substituteFlag\": \"0\", \"itemUserDefineCharacter\": { \"ytenant\": \"0000LDTXR6979CPCME0000\", \"id\": \"1976091036898295816\", \"dadw\": false, \"wsz\": 1 }, \"itemFreeCharacteristics\": \"{ \\\"wjbda\\\": \\\"1654047774817124367\\\", \\\"ytenant\\\": \\\"0000LDTXR6979CPCME0000\\\", \\\"id\\\": \\\"1970249434717487214\\\", \\\"wbe\\\": false, \\\"pubts\\\": \\\"2024-04-08\", \"projectId\": \"1654046039626743861\", \"projectIdCode\": \"pj1\", \"projectIdName\": \"项目名称\", \"wbs\": \"1570766056850456576\", \"wbsCode\": \"00001111\", \"wbsName\": \"wbs任务一号\", \"activity\": \"1570766056850456575\", \"activityCode\": \"001111\", \"activityName\": \"活动一号\", \"reserveid\": \"1570766056850456574\", \"reserveTypeName\": \"自定义\", \"reserveName\": \"跟踪线索\" } ] }, \"pageIndex\": 1, \"pageSize\": 10, \"recordCount\": 13, \"pageCount\": 2, \"beginPageIndex\": 1, \"endPageIndex\": 1 } ] }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2025-01-06 14:10:48.000", "gmtUpdate": "2025-01-06 14:10:48.000", "apiName": "", "edit": false, "ytenantId": 0, "right": true}, {"id": 2172771109868929032, "apiId": 2152020559581937665, "content": "{ \"code\": \"310008\", \"message\": \"参数校验失败，参数[data]是必填的。\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2025-01-06 14:10:48.000", "gmtUpdate": "2025-01-06 14:10:48.000", "apiName": "", "edit": false, "ytenantId": 0, "right": false}]}, "apiDemoReturnDTOList": {"apiDemoReturnDTOList": [{"id": 2172771109868929031, "apiId": 2152020559581937665, "content": "{ \"code\": \"200\", \"message\": \"操作成功\", \"data\": [ { \"recordList\": { \"id\": \"1630874665323855906\", \"orgId\": \"1608788551787872266\", \"orgCode\": \"***********\", \"transTypeId\": \"1601383117764427779\", \"transTypeCode\": \"GEN-11\", \"planParamId\": \"****************\", \"planParamCode\": \"GEN0\", \"planParamName\": \"LRP2***********\", \"createType\": \"2\", \"code\": \"GEN00011\", \"productCode\": \"000045\", \"productId\": \"159192034273814118\", \"planProperty\": \"1\", \"bomId\": \"1591920342738141189\", \"bomCode\": \"likun-M.Code-002（固）\", \"uom\": \"1570768779829837833\", \"uomName\": \"千克\", \"uomCode\": \"kg\", \"assistUnit\": \"1570768797009707017\", \"assistUnitCode\": \"kg\", \"assistUnitName\": \"千克\", \"originQuantity\": 10, \"assistUnitCount\": 10, \"suggestPlanQuantity\": 10, \"inputQty\": 10, \"issuedQuantity\": 0, \"startDate\": \"2023-01-06 00:00:00\", \"finishDate\": \"2023-01-06 00:00:00\", \"status\": \"0\", \"demandOrgId\": \"1570766056850456576\", \"demandOrgCode\": \"00310\", \"supplyOrgId\": \"1570766056850456576\", \"supplyOrgCode\": \"00310\", \"invOrgId\": \"1570766056850456576\", \"invOrgCode\": \"00310\", \"source\": \"10\", \"upcode\": \"YCD20221228000006\", \"srcSourceProductId\": \"1570766056850456576\", \"srcSourceProductCode\": \"1035000045\", \"firstsource\": \"280\", \"firstupcode\": \"YCD20221228000006\", \"firstsourceautoid\": \"1623614151955316744\", \"sourceMaterialId\": \"1590995696384737289\", \"sourceMaterialCode\": \"1035000045\", \"departmentId\": \"1570766056850456576\", \"departmentCode\": \"001111\", \"departmentName\": \"部门1\", \"warehouseId\": \"1570766056850456576\", \"warehouseCode\": \"001111\", \"warehouseName\": \"仓库1\", \"isClosed\": false, \"remark\": \"remark\", \"projectId\": \"1570766056850456576\", \"projectIdCode\": \"001111\", \"projectIdName\": \"项目一号\", \"wbs\": \"1570766056850456576\", \"wbsCode\": \"001111\", \"wbsName\": \"wbs任务一号\", \"activity\": \"1570766056850456576\", \"activityCode\": \"001111\", \"activityName\": \"活动一号\", \"planOrderItem\": [ { \"itemProductId\": \"1681453239576297481\", \"itemProductCode\": \"wlfl014\", \"itemProductName\": \"WC1\", \"mainUnitId\": \"1674787939942400002\", \"mainUnitCode\": \"MKT\", \"mainUnitName\": \"立方米\", \"stockUnitId\": \"1674787939942400004\", \"stockUnitCode\": \"MTQ\", \"stockUnitName\": \"平方千米\", \"changeRate\": 1, \"requirementQuantity\": 5.01, \"auxiliaryRequirementQuantity\": 5.01, \"stockOrgId\": \"1681369238604349442\", \"stockOrgCode\": \"zzw\", \"stockOrgName\": \"库存w\", \"warehouseId\": \"1681372373925232646\", \"warehouseCode\": \"w2\", \"warehouseName\": \"仓库w2\", \"reqDate\": \"2024-04-08 23:59:59\", \"remark\": \"备注\", \"substituteFlag\": \"0\", \"itemUserDefineCharacter\": { \"ytenant\": \"0000LDTXR6979CPCME0000\", \"id\": \"1976091036898295816\", \"dadw\": false, \"wsz\": 1 }, \"itemFreeCharacteristics\": \"{ \\\"wjbda\\\": \\\"1654047774817124367\\\", \\\"ytenant\\\": \\\"0000LDTXR6979CPCME0000\\\", \\\"id\\\": \\\"1970249434717487214\\\", \\\"wbe\\\": false, \\\"pubts\\\": \\\"2024-04-08\", \"projectId\": \"1654046039626743861\", \"projectIdCode\": \"pj1\", \"projectIdName\": \"项目名称\", \"wbs\": \"1570766056850456576\", \"wbsCode\": \"00001111\", \"wbsName\": \"wbs任务一号\", \"activity\": \"1570766056850456575\", \"activityCode\": \"001111\", \"activityName\": \"活动一号\", \"reserveid\": \"1570766056850456574\", \"reserveTypeName\": \"自定义\", \"reserveName\": \"跟踪线索\" } ] }, \"pageIndex\": 1, \"pageSize\": 10, \"recordCount\": 13, \"pageCount\": 2, \"beginPageIndex\": 1, \"endPageIndex\": 1 } ] }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2025-01-06 14:10:48.000", "gmtUpdate": "2025-01-06 14:10:48.000", "apiName": "", "edit": false, "ytenantId": 0, "right": true}, {"id": 2172771109868929032, "apiId": 2152020559581937665, "content": "{ \"code\": \"310008\", \"message\": \"参数校验失败，参数[data]是必填的。\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2025-01-06 14:10:48.000", "gmtUpdate": "2025-01-06 14:10:48.000", "apiName": "", "edit": false, "ytenantId": 0, "right": false}]}, "routingStgy": 0, "routingStgyList": "", "apiDemoReturnDTO": {"id": 2172771109868929031, "apiId": 2152020559581937665, "content": "{ \"code\": \"200\", \"message\": \"操作成功\", \"data\": [ { \"recordList\": { \"id\": \"1630874665323855906\", \"orgId\": \"1608788551787872266\", \"orgCode\": \"***********\", \"transTypeId\": \"1601383117764427779\", \"transTypeCode\": \"GEN-11\", \"planParamId\": \"****************\", \"planParamCode\": \"GEN0\", \"planParamName\": \"LRP2***********\", \"createType\": \"2\", \"code\": \"GEN00011\", \"productCode\": \"000045\", \"productId\": \"159192034273814118\", \"planProperty\": \"1\", \"bomId\": \"1591920342738141189\", \"bomCode\": \"likun-M.Code-002（固）\", \"uom\": \"1570768779829837833\", \"uomName\": \"千克\", \"uomCode\": \"kg\", \"assistUnit\": \"1570768797009707017\", \"assistUnitCode\": \"kg\", \"assistUnitName\": \"千克\", \"originQuantity\": 10, \"assistUnitCount\": 10, \"suggestPlanQuantity\": 10, \"inputQty\": 10, \"issuedQuantity\": 0, \"startDate\": \"2023-01-06 00:00:00\", \"finishDate\": \"2023-01-06 00:00:00\", \"status\": \"0\", \"demandOrgId\": \"1570766056850456576\", \"demandOrgCode\": \"00310\", \"supplyOrgId\": \"1570766056850456576\", \"supplyOrgCode\": \"00310\", \"invOrgId\": \"1570766056850456576\", \"invOrgCode\": \"00310\", \"source\": \"10\", \"upcode\": \"YCD20221228000006\", \"srcSourceProductId\": \"1570766056850456576\", \"srcSourceProductCode\": \"1035000045\", \"firstsource\": \"280\", \"firstupcode\": \"YCD20221228000006\", \"firstsourceautoid\": \"1623614151955316744\", \"sourceMaterialId\": \"1590995696384737289\", \"sourceMaterialCode\": \"1035000045\", \"departmentId\": \"1570766056850456576\", \"departmentCode\": \"001111\", \"departmentName\": \"部门1\", \"warehouseId\": \"1570766056850456576\", \"warehouseCode\": \"001111\", \"warehouseName\": \"仓库1\", \"isClosed\": false, \"remark\": \"remark\", \"projectId\": \"1570766056850456576\", \"projectIdCode\": \"001111\", \"projectIdName\": \"项目一号\", \"wbs\": \"1570766056850456576\", \"wbsCode\": \"001111\", \"wbsName\": \"wbs任务一号\", \"activity\": \"1570766056850456576\", \"activityCode\": \"001111\", \"activityName\": \"活动一号\", \"planOrderItem\": [ { \"itemProductId\": \"1681453239576297481\", \"itemProductCode\": \"wlfl014\", \"itemProductName\": \"WC1\", \"mainUnitId\": \"1674787939942400002\", \"mainUnitCode\": \"MKT\", \"mainUnitName\": \"立方米\", \"stockUnitId\": \"1674787939942400004\", \"stockUnitCode\": \"MTQ\", \"stockUnitName\": \"平方千米\", \"changeRate\": 1, \"requirementQuantity\": 5.01, \"auxiliaryRequirementQuantity\": 5.01, \"stockOrgId\": \"1681369238604349442\", \"stockOrgCode\": \"zzw\", \"stockOrgName\": \"库存w\", \"warehouseId\": \"1681372373925232646\", \"warehouseCode\": \"w2\", \"warehouseName\": \"仓库w2\", \"reqDate\": \"2024-04-08 23:59:59\", \"remark\": \"备注\", \"substituteFlag\": \"0\", \"itemUserDefineCharacter\": { \"ytenant\": \"0000LDTXR6979CPCME0000\", \"id\": \"1976091036898295816\", \"dadw\": false, \"wsz\": 1 }, \"itemFreeCharacteristics\": \"{ \\\"wjbda\\\": \\\"1654047774817124367\\\", \\\"ytenant\\\": \\\"0000LDTXR6979CPCME0000\\\", \\\"id\\\": \\\"1970249434717487214\\\", \\\"wbe\\\": false, \\\"pubts\\\": \\\"2024-04-08\", \"projectId\": \"1654046039626743861\", \"projectIdCode\": \"pj1\", \"projectIdName\": \"项目名称\", \"wbs\": \"1570766056850456576\", \"wbsCode\": \"00001111\", \"wbsName\": \"wbs任务一号\", \"activity\": \"1570766056850456575\", \"activityCode\": \"001111\", \"activityName\": \"活动一号\", \"reserveid\": \"1570766056850456574\", \"reserveTypeName\": \"自定义\", \"reserveName\": \"跟踪线索\" } ] }, \"pageIndex\": 1, \"pageSize\": 10, \"recordCount\": 13, \"pageCount\": 2, \"beginPageIndex\": 1, \"endPageIndex\": 1 } ] }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2025-01-06 14:10:48.000", "gmtUpdate": "2025-01-06 14:10:48.000", "apiName": "", "edit": false, "ytenantId": 0, "right": true}, "apiDemoReturnDTOError": {"id": 2172771109868929032, "apiId": 2152020559581937665, "content": "{ \"code\": \"310008\", \"message\": \"参数校验失败，参数[data]是必填的。\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2025-01-06 14:10:48.000", "gmtUpdate": "2025-01-06 14:10:48.000", "apiName": "", "edit": false, "ytenantId": 0, "right": false}, "errorCodeDTOS": {"errorCodeDTOS": {"id": 2172771109868929029, "apiId": 2152020559581937665, "errorCode": 310008, "errorMessage": "取决于错误类型，不同错误信息不同", "errorType": "API", "errorcodeDesc": "", "gmtCreate": "2025-01-06 14:10:48.000", "gmtUpdate": "2025-01-06 14:10:48.000", "apiName": "", "edit": false, "defErrorId": 2152020559581937902, "ytenantId": 0, "displayCodeId": ""}}, "displayCodeApiConfigDTOS": "", "tokenPlugin": "", "paramParsePlugin": "", "authPlugin": {"id": "09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "", "name": "友户通token认证业务扩展插件", "configurable": false, "description": "YonsuitBusinessExtendPlugin", "pluginType": "auth", "pluginTypeName": "业务扩展插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": false, "gmtCreate": "2020-05-22 00:00:00", "gmtUpdate": "2020-05-22 00:00:00", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": 2152020559581937665, "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "resultParsePlugin": "", "mapReturnPluginConfig": "", "billNo": "", "domain": "", "apiCategory": "", "docUrl": "", "pathMatch": 0, "createUser": "3970ff8d-d4d1-4e03-9b63-f59412cf4886", "createUserName": "昵称-***********", "approvalStatus": 1, "publishTime": "2025-06-24 20:00:33", "pathJoin": false, "timeOut": 30, "tokenPluginName": "", "authPluginName": "", "resultPluginName": "", "apiDemoReturnRightDemo": "", "apiDemoReturnErrorDemo": "", "mock": false, "mockTimeout": 0, "customUrl": "requirementsplanning/getPlanOrderList", "fixedUrl": "/yonbip/mfg/", "apiCode": 2152020559581937665, "tokenCheckType": 0, "enableMulti": false, "multiField": "", "idempotent": "non", "bidirectionalSSL": false, "ucgSchema": "HTTPS", "updateUserId": "ced88565-facc-4067-b773-e9b1337f3f4e", "updateUserName": "PJJ", "paramIsForce": true, "userIDPassthrough": true, "applyUser": "", "applyMsg": "", "dr": 0, "microServiceCode": "domain.yonbip-mm-mfmr", "applicationCode": "MR", "privacyCategory": 1, "privacyLevel": 3, "apiDesigned": 0, "serviceType": 0, "integrateSchemeCode": "", "integrateSchemeName": "", "integrateObjectCode": "", "integrateObjectName": "", "integrateObjectCreatedType": -1, "returnIntegObjId": "", "returnIntegObjName": "", "apiIntegrateDTOList": "", "apiRouteInfoDTOList": "", "arrayParam": false, "fileSize": 0, "cc": true, "paramTransferMode": 1, "ytenantId": 0, "statusConf": "", "scene": 1, "version": "", "bizObjUri": "", "bizObjOperationType": "", "apiDefId": 2152020559581937668, "paramExtBizObjCode": "", "paramExtBizObjName": "", "paramExtRequest": 0, "paramExtResponse": 0, "paramExtInExtendKey": 0, "openScene": 1, "integrationScene": 0, "apiType": "", "paramMark": "", "integrateSysId": "", "integrateSysName": "", "integrateSysCode": "", "dataZoneSetting": false, "reqDataZoneSetting": false, "respDataZoneSetting": false, "reqDataAllQuery": false, "reqDataAllBody": false, "respDataAllBody": false, "chargeStatus": 2, "beforeSpeed": "", "afterSpeed": "", "speedStatus": false, "reqDataRefPath": "", "respDataRefPath": "", "pubHistory": {"pubHistory": {"id": 2298378158575976458, "apiId": 2152020559581937665, "apiName": "计划订单列表查询", "applyReason": "", "publishUserName": "", "version": 20250624200033, "operationTime": "2025-06-24", "gmtCreate": "", "gmtUpdate": ""}}, "deprecated": 0, "recommendedApiId": "", "recommendedApiName": "", "domainAppCode": "requirementsplanning.mr_mps_plan_workbench_batch_import", "multiVersion": 0, "apiTag": ""}}, {"success": true, "code": 200, "message": "", "data": {"id": 2108770660671029249, "name": "用友YonBIP", "type": "integrateSys", "sort": 0, "enable": 0, "children": {"children": {"id": "MFC", "name": "制造云", "type": 1, "sort": 0, "enable": 0, "children": {"children": {"id": "MF", "name": "生产制造", "type": 2, "sort": 0, "enable": 0, "children": {"children": {"id": "MR", "name": "生产计划", "type": 3, "sort": 0, "enable": 0, "children": {"children": {"id": "requirementsplanning.mr_mps_plan_workbench_batch_import", "name": "计划订单", "type": 4, "sort": 0, "enable": 0, "children": "", "parentId": "", "productId": "", "code": "requirementsplanning.mr_mps_plan_workbench_batch_import", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "MR", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "MF", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "MFC", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "current_yonbip_default_sys", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "isOrigin": 0, "hasChildren": 0, "order": 0}}, {"success": true, "code": 200, "message": "", "data": [{"id": "fe16c539-4839-4f12-98cb-54960ac7806d", "name": "XS11", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求分类号test", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:53:18", "gmtUpdate": "2025-07-26 17:53:18", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "7d259f5d-7a1d-41e3-a3a5-27570d5c2dc4", "name": "XS15", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "顾客订单号（订单表体）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:53:18", "gmtUpdate": "2025-07-26 17:53:18", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:53:18", "gmtUpdate": "2025-07-26 17:53:18", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}]