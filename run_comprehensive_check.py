import configparser
import json
import sqlite3
from datetime import datetime
from pathlib import Path

import requests

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 全面检查执行脚本
根据TASK.md清单执行系统性检查并生成详细报告
"""


class ComprehensiveChecker:
    def __init___(self, project_root: str):
    """TODO: Add function description."""
    self.project_root = Path(project_root)
    self.check_results = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "project_name": "YS-API V3.0",
        "overall_score": 0,
        "total_checks": 0,
        "passed_checks": 0,
        "failed_checks": 0,
        "warnings": [],
        "critical_issues": [],
        "detailed_results": {},
    }

    def log_check(
            self,
            category: str,
            check_name: str,
            status: str,
            details: str = ""):
        """记录检查结果"""
        self.check_results["total_checks"] += 1

        if status == "PASS":
            self.check_results["passed_checks"] += 1
        elif status == "FAIL":
            self.check_results["failed_checks"] += 1
            self.check_results["critical_issues"].append(
                {"category": category, "check": check_name, "details": details}
            )
        elif status == "WARN":
            self.check_results["warnings"].append(
                {"category": category, "check": check_name, "details": details}
            )

        if category not in self.check_results["detailed_results"]:
            self.check_results["detailed_results"][category] = []

        self.check_results["detailed_results"][category].append(
            {"check": check_name, "status": status, "details": details}
        )

    def check_python_code_quality(self):
        """检查Python代码质量"""
        print("🔍 检查Python代码质量...")

        python_files = list(self.project_root.rglob("*.py"))
        issues = []

        # 检查代码规范
        for py_file in python_files:
            if "__pycache__" not in str(py_file):
                try:
                    with open(py_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 检查TODO/FIXME
                    if "TODO" in content:
                        issues.append(f"{py_file.name} 包含TODO注释")
                    if "FIXME" in content:
                        issues.append(f"{py_file.name} 包含FIXME注释")

                    # 检查print语句
                    lines = content.split('\n')
                    for i, line in enumerate(lines, 1):
                        if "print(" in line and "console.log" not in line:
                            issues.append(
                                f"{py_file.name}:{i} 使用print而非logging")

                except Exception:
                    issues.append(f"无法读取 {py_file}: {e}")

        if issues:
            for issue in issues[:5]:  # 只显示前5个问题
                self.log_check("代码质量", "Python代码规范", "WARN", issue)
        else:
            self.log_check("代码质量", "Python代码规范", "PASS", "代码质量良好")

    def check_security_issues(self):
        """检查安全问题"""
        print("🔒 检查安全问题...")

        # 检查配置文件中的敏感信息
        config_files = [
            self.project_root / "config.ini",
            self.project_root / "backend" / "config.ini",
        ]

        sensitive_patterns = ["password", "secret", "key", "token", "api_key"]

        for config_file in config_files:
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read().lower()

                    for pattern in sensitive_patterns:
                        if pattern in content:
                            self.log_check(
                                "安全性",
                                "敏感信息检查",
                                "WARN",
                                f"配置文件可能包含敏感信息: {config_file.name}",
                            )
                            break
                    else:
                        self.log_check(
                            "安全性",
                            "敏感信息检查",
                            "PASS",
                            f"{config_file.name} 未发现敏感信息",
                        )

                except Exception:
                    self.log_check(
                        "安全性", "敏感信息检查", "FAIL", f"无法读取配置文件: {e}"
                    )

    def check_database_integrity(self):
        """检查数据库完整性"""
        print("🗄️ 检查数据库完整性...")

        db_file = self.project_root / "backend" / "ysapi.db"

        if not db_file.exists():
            self.log_check("数据库", "数据库文件", "FAIL", "数据库文件不存在")
            return

        try:
            conn = sqlite3.connect(str(db_file))
            cursor = conn.cursor()

            # 检查表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()

            if len(tables) < 5:
                self.log_check("数据库", "表数量", "WARN", f"表数量较少: {len(tables)}")
            else:
                self.log_check("数据库", "表结构", "PASS", f"发现 {len(tables)} 个表")

            conn.close()

        except Exception:
            self.log_check("数据库", "数据库连接", "FAIL", str(e))

    def check_file_structure(self):
        """检查文件结构完整性"""
        print("📁 检查文件结构...")

        required_structure = {
            "backend": ["start_simple.py", "requirements.txt", "config.ini"],
            "frontend": ["migration-test-fixed.html", "migrated"],
            "config": ["modules.json"],
            "docs": ["项目架构文档.md", "12-生产部署指南.md"],
            "logs": [],
            "excel": ["物料创建.xlsx"],
        }

        for dir_name, required_files in required_structure.items():
            dir_path = self.project_root / dir_name

            if not dir_path.exists():
                self.log_check("文件结构", dir_name, "FAIL", f"缺少目录: {dir_name}")
                continue

            for file_name in required_files:
                file_path = dir_path / file_name

                if file_name.endswith("/"):  # 检查目录
                    sub_dir = dir_path / file_name.rstrip("/")
                    if not sub_dir.exists():
                        self.log_check(
                            "文件结构",
                            file_name.rstrip("/"),
                            "WARN",
                            f"缺少子目录: {dir_name}/{file_name}",
                        )
                else:  # 检查文件
                    if not file_path.exists():
                        self.log_check(
                            "文件结构",
                            file_name,
                            "WARN",
                            f"缺少文件: {dir_name}/{file_name}",
                        )

    def check_api_endpoints(self):
        """检查API端点可用性"""
        print("🔗 检查API端点...")

        # 尝试启动后端服务并检查健康端点
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                self.log_check("API", "健康检查", "PASS", "API服务正常运行")
            else:
                self.log_check(
                    "API", "健康检查", "WARN", f"API返回状态码: {response.status_code}"
                )
        except requests.exceptions.ConnectionError:
            self.log_check("API", "服务可用性", "FAIL", "无法连接到API服务")
        except Exception:
            self.log_check("API", "服务检查", "FAIL", str(e))

    def check_configuration(self):
        """检查配置完整性"""
        print("⚙️ 检查配置完整性...")

        config_files = [
            self.project_root / "config.ini",
            self.project_root / "backend" / "config.ini",
        ]

        for config_file in config_files:
            if config_file.exists():
                try:

                    config = configparser.ConfigParser()
                    config.read(config_file, encoding='utf-8')

                    required_sections = ['database', 'api']
                    for section in required_sections:
                        if not config.has_section(section):
                            self.log_check(
                                "配置",
                                f"{config_file.name} - {section}",
                                "WARN",
                                f"缺少配置节: {section}",
                            )
                        else:
                            self.log_check(
                                "配置",
                                f"{config_file.name} - {section}",
                                "PASS",
                                f"配置节 {section} 存在",
                            )

                except Exception:
                    self.log_check(
                        "配置", config_file.name, "FAIL", f"读取配置失败: {e}"
                    )

    def check_performance(self):
        """检查性能指标"""
        print("⚡ 检查性能指标...")

        # 检查文件大小
        large_files = []
        for file_path in self.project_root.rglob("*"):
            if file_path.is_file() and file_path.suffix in [
                    '.log', '.db', '.tmp']:
                size_mb = file_path.stat().st_size / (1024 * 1024)
                if size_mb > 50:  # 大于50MB
                    large_files.append(f"{file_path.name}: {size_mb:.1f}MB")

        if large_files:
            for file_info in large_files[:3]:
                self.log_check("性能", "大文件检查", "WARN", f"发现大文件: {file_info}")
        else:
            self.log_check("性能", "文件大小", "PASS", "未发现异常大文件")

    def check_documentation(self):
        """检查文档完整性"""
        print("📚 检查文档完整性...")

        required_docs = [
            "README.md",
            "项目架构文档.md",
            "12-生产部署指南.md",
            "05-API接口规范.md",
        ]

        for doc in required_docs:
            doc_path = self.project_root / doc
            if not doc_path.exists():
                # 检查是否在docs目录
                doc_path = self.project_root / "docs" / doc

            if doc_path.exists():
                self.log_check("文档", doc, "PASS", "文档存在")
            else:
                self.log_check("文档", doc, "WARN", "文档缺失")

    def generate_comprehensive_report(self):
        """生成综合检查报告"""
        # 计算总体得分
        total = self.check_results["total_checks"]
        passed = self.check_results["passed_checks"]

        if total > 0:
            score = (passed / total) * 100
            self.check_results["overall_score"] = round(score, 1)

        # 生成报告文件
        report_file = self.project_root / "comprehensive_check_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.check_results, f, ensure_ascii=False, indent=2)

        # 生成可读报告
        readable_report = self.project_root / "comprehensive_check_report.md"
        with open(readable_report, 'w', encoding='utf-8') as f:
            f.write(self._generate_readable_report())

        return report_file, readable_report

    def _generate_readable_report(self) -> str:
        """生成可读格式的报告"""
        report = f"""# YS-API V3.0 全面检查报告

**检查时间**: {self.check_results['timestamp']}
**总体评分**: {self.check_results['overall_score']}/100
**检查总数**: {self.check_results['total_checks']}
**通过项**: {self.check_results['passed_checks']}
**失败项**: {self.check_results['failed_checks']}

## 📊 总体评估

### 系统健康状态
- **评分**: {self._get_health_status()}
- **建议**: {self._get_recommendation()}

### 关键发现
- **严重问题**: {len(self.check_results['critical_issues'])}
- **警告项目**: {len(self.check_results['warnings'])}

## 🔍 详细检查结果

"""

        for category, checks in self.check_results["detailed_results"].items():
            report += f"### {category}\n\n"
            for check in checks:
                status_icon = {"PASS": "✅", "FAIL": "❌", "WARN": "⚠️"}
                report += f"{status_icon[check['status']]} **{check['check']}**: {check['details']}\n\n"

        if self.check_results["critical_issues"]:
            report += "## 🚨 严重问题\n\n"
            for issue in self.check_results["critical_issues"]:
                report += f"- **{issue['category']} - {issue['check']}**: {issue['details']}\n"

        if self.check_results["warnings"]:
            report += "## ⚠️ 警告项目\n\n"
            for warning in self.check_results["warnings"]:
                report += f"- **{warning['category']} - {warning['check']}**: {warning['details']}\n"

        report += f"""
## 🛠️ 立即行动建议

1. **优先处理严重问题**: 解决所有标记为"FAIL"的问题
2. **关注警告项目**: 评估并处理"WARN"标记的项目
3. **持续监控**: 建立定期检查机制
4. **文档更新**: 根据检查结果更新相关文档

## 📞 支持信息

- **检查工具**: run_comprehensive_check.py
- **自动修复**: fix_issues.py
- **健康检查**: project_health_check.py
- **报告文件**: comprehensive_check_report.json

---
*报告由自动化检查工具生成，如有疑问请联系开发团队*
"""
        return report

    def _get_health_status(self) -> str:
        """获取健康状态描述"""
        score = self.check_results["overall_score"]
        if score >= 90:
            return "优秀 🟢"
        elif score >= 75:
            return "良好 🟡"
        elif score >= 60:
            return "一般 🟠"
        else:
            return "需要改进 🔴"

    def _get_recommendation(self) -> str:
        """获取改进建议"""
        critical_count = len(self.check_results["critical_issues"])
        if critical_count > 5:
            return "立即暂停部署，集中修复关键问题"
        elif critical_count > 0:
            return "优先修复关键问题，然后逐步改进"
        elif len(self.check_results["warnings"]) > 10:
            return "系统基本可用，建议持续优化"
        else:
            return "系统状态良好，建议定期维护"

    def run_all_checks(self):
        """运行所有检查"""
        print("🚀 开始YS-API V3.0全面检查...")
        print("=" * 50)

        checks = [
            self.check_python_code_quality,
            self.check_security_issues,
            self.check_database_integrity,
            self.check_file_structure,
            self.check_api_endpoints,
            self.check_configuration,
            self.check_performance,
            self.check_documentation,
        ]

        for check in checks:
            try:
                check()
                print()  # 空行分隔
            except Exception:
                print(f"❌ 检查失败: {e}")
                self.log_check("系统", "检查执行", "FAIL", f"检查失败: {str(e)}")

        # 生成报告
        json_report, md_report = self.generate_comprehensive_report()

        print("✅ 全面检查完成！")
        print(f"📊 总体评分: {self.check_results['overall_score']}/100")
        print(f"📄 详细报告: {md_report}")
        print(f"📋 JSON报告: {json_report}")


if __name__ == "__main__":
    project_root = Path(__file__).parent
    checker = ComprehensiveChecker(str(project_root))
    checker.run_all_checks()
