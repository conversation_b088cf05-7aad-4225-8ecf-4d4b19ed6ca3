repos:
  # 检测代码重复
  - repo: local
    hooks:
      - id: check-duplicates
        name: 检查重复文件和代码
        entry: python file_duplicate_checker.py
        language: system
        files: \.(py|js|html|css)$
        
  # JavaScript/TypeScript重复检查
  - repo: https://github.com/kucherenko/jscpd
    rev: v3.5.10
    hooks:
      - id: jscpd
        name: JavaScript代码重复检查
        args: ['--threshold', '5', '--reporters', 'console']
        files: \.(js|ts|jsx|tsx)$
        
  # Python代码质量检查
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        language_version: python3.10
        
  # 文件命名检查
  - repo: local
    hooks:
      - id: check-naming-conflicts
        name: 检查文件命名冲突
        entry: python check_naming_conflicts.py
        language: system
        files: \.py$
        
  # 防止临时文件提交
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: check-yaml
      - id: check-json
        
  # 自定义重复函数检查
  - repo: local
    hooks:
      - id: check-duplicate-functions
        name: 检查重复函数定义
        entry: python function_duplicate_checker.py
        language: system
        files: \.py$
