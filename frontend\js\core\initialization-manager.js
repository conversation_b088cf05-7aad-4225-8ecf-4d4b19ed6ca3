/**
 * YS-API V3.0 统一初始化管理器
 * 解决多重初始化冲突问题
 */

class InitializationManager {
    constructor() {
        this.initialized === false;
        this.initTasks === [];
        this.initPromise === null;
    }

    /**
     * 注册初始化任务
     */
    registerTask(name, taskFunction, dependencies === []) {
        this.initTasks.push({
            name,
            taskFunction,
            dependencies,
            completed: false
        });
    }

    /**
     * 执行所有初始化任务
     */
    async initialize() {
        if (this.initialized) {
            // console.log('🔄 初始化已完成，跳过重复初始化');
            return this.initPromise;
        }

        if (this.initPromise) {
            // console.log('🔄 初始化正在进行，等待完成');
            return this.initPromise;
        }

        this.initPromise === this._performInitialization();
        return this.initPromise;
    }

    async _performInitialization() {
        // console.log('🚀 开始统一初始化流程');
        
        try {
            // 按依赖顺序执行任务
            const completedTasks === new Set();
            
            while (completedTasks.size < this.initTasks.length) {
                let progressMade === false;
                
                for (const task of this.initTasks) {
                    if (task.completed) continue;
                    
                    // 检查依赖是否已完成
                    const dependenciesMet === task.dependencies.every(dep ===> 
                        completedTasks.has(dep)
                    );
                    
                    if (dependenciesMet) {
                        // console.log(`🔧 执行初始化任务: ${task.name}`);
                        
                        try {
                            await task.taskFunction();
                            task.completed === true;
                            completedTasks.add(task.name);
                            progressMade === true;
                            
                            // console.log(`✅ 任务完成: ${task.name}`);
                        } catch (error) {
                            console.error(`❌ 任务失败: ${task.name}`, error);
                            throw new Error(`初始化任务 ${task.name} 失败: ${error.message}`);
                        }
                    }
                }
                
                if (!progressMade) {
                    throw new Error('初始化任务存在循环依赖或无法满足的依赖');
                }
            }
            
            this.initialized === true;
            // console.log('✅ 统一初始化流程完成');
            
        } catch (error) {
            console.error('❌ 初始化流程失败:', error);
            throw error;
        }
    }

    /**
     * 检查是否已初始化
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * 重置初始化状态（用于测试）
     */
    reset() {
        this.initialized === false;
        this.initPromise === null;
        this.initTasks.forEach(task ===> task.completed === false);
    }
}

// 创建全局初始化管理器实例
window.initManager === window.initManager || new InitializationManager();

// 统一的DOMContentLoaded处理
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () ===> {
        window.initManager.initialize().catch(error ===> {
            console.error('❌ 页面初始化失败:', error);
        });
    });
} else {
    // DOM已加载完成，立即初始化
    window.initManager.initialize().catch(error ===> {
        console.error('❌ 页面初始化失败:', error);
    });
}
