<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>a321db2a1d7f4e4ba4859f28a57a9da6</id>
<name>委外入库列表查询</name>
<apiClassifyId>9a11bbb107934f28814208338724658a</apiClassifyId>
<apiClassifyName>委外入库单</apiClassifyName>
<apiClassifyCode>OSMIN</apiClassifyCode>
<parentApiClassifies/>
<functionId/>
<openMode/>
<description>委外入库列表查询</description>
<auth>true</auth>
<bodyPassthrough>false</bodyPassthrough>
<healthExam/>
<healthStatus/>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/scm/osminrecord/list</completeProxyUrl>
<connectUrl>/bill/list</connectUrl>
<sort>20</sort>
<handler>openapi</handler>
<httpRequestType>POST</httpRequestType>
<openApi>true</openApi>
<preset>false</preset>
<productId>710a0be3edff4f9092e35f63fd3b9bae</productId>
<productCode>scm</productCode>
<proxyUrl>/yonbip/scm/osminrecord/list</proxyUrl>
<requestParamsDemo>Url: /yonbip/scm/osminrecord/list?access_token=访问令牌 Body: { "isSum": true, "code": "OSMI20220317000001", "pageSize": 20, "pageIndex": 1, "open_vouchdate_begin": "2022-03-17 00:00:00", "open_vouchdate_end": "2022-03-21 00:00:00", "status": "", "simpleVOs": [ { "field": "", "op": "", "value1": "", "value2": "" } ] }</requestParamsDemo>
<requestProtocol>HTTP</requestProtocol>
<serviceHttpMethod>POST</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2020-12-12 14:50:36.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:00.000</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/osminrecord/list</address>
<productName>采购供应</productName>
<productClassifyId>yonsuite</productClassifyId>
<productClassifyCode>yonbip</productClassifyCode>
<productClassifyName>用友 YonBIP</productClassifyName>
<paramDTOS>
<paramDTOS>
<id>1872936969107931145</id>
<name>isSum</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId>1861195559319633926</defParamId>
<array>false</array>
<paramDesc>是否按照表头查询 true:表头 false:表头+明细 默认为false</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>false</defaultValue>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:00.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:00.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>1872936969107931146</id>
<name>code</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId>1861195559319633927</defParamId>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>OSMI20220317000001</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:00.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:00.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>1872936969107931147</id>
<name>pageSize</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId>1861195559319633928</defParamId>
<array>false</array>
<paramDesc>每页显示数据数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>20</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>20</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:00.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:00.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>1872936969107931148</id>
<name>pageIndex</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId>1861195559319633929</defParamId>
<array>false</array>
<paramDesc>当前页数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>1</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:00.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:00.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>1872936969107931149</id>
<name>open_vouchdate_begin</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId>1861195559319633930</defParamId>
<array>false</array>
<paramDesc>单据开始日期</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-03-17 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:00.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:00.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>1872936969107931150</id>
<name>open_vouchdate_end</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId>1861195559319633931</defParamId>
<array>false</array>
<paramDesc>单据结束日期</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-03-21 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:00.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:00.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>1872936969107931151</id>
<name>status</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId>1861195559319633932</defParamId>
<array>false</array>
<paramDesc>单据状态，0 开立 1已审核 3 审核中</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:00.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:00.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>1872936969107931140</id>
<name>simpleVOs</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<children>
<children>
<id>1872936969107931141</id>
<name>field</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936969107931140</parentId>
<defParamId>1861195559319633934</defParamId>
<array>false</array>
<paramDesc>属性名(条件传属性的名称，如单据编号code、单据日期vouchdate、收货组织org.code、委外组织osmOrg.code、收票组织inInvoiceOrg.code、委外供应商vendor.code、仓库编码warehouse.code、物料编码osmInRecords.product.cCode、物料分类osmInRecords.product.manageClass.code、物料SKU编码osmInRecords.productsku.cCode等)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:00.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:00.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>1872936969107931142</id>
<name>op</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936969107931140</parentId>
<defParamId>1861195559319633935</defParamId>
<array>false</array>
<paramDesc>比较符(in:包含;eq:等于;lt:小于;gt:大于;like:模糊匹配;between:介于)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:00.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:00.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>1872936969107931143</id>
<name>value1</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936969107931140</parentId>
<defParamId>1861195559319633936</defParamId>
<array>false</array>
<paramDesc>值1(条件)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:00.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:00.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>1872936969107931144</id>
<name>value2</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936969107931140</parentId>
<defParamId>1861195559319633937</defParamId>
<array>false</array>
<paramDesc>值2(条件)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:00.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:00.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>1861195559319633933</defParamId>
<array>true</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:00.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:00.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>1872936977697865733</id>
<name>isSum</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>是否按照表头查询 true:表头 false:表头+明细 默认为false</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>isSum</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>boolean</serviceParamType>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>1872936977697865734</id>
<name>code</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>code</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>1872936977697865735</id>
<name>pageSize</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>每页显示数据数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageSize</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>1872936977697865736</id>
<name>pageIndex</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>当前页数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageIndex</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>1872936977697865737</id>
<name>open_vouchdate_begin</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据开始日期</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_vouchdate_begin</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>1872936977697865738</id>
<name>open_vouchdate_end</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据结束日期</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_vouchdate_end</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>1872936977697865739</id>
<name>status</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据状态，0 开立 1已审核 3 审核中</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>status</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>1872936977697865728</id>
<name>simpleVOs</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<children>
<children>
<id>1872936977697865729</id>
<name>field</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865728</parentId>
<defParamId/>
<array>false</array>
<paramDesc>属性名(条件传属性的名称，如单据编号code、单据日期vouchdate、收货组织org.code、委外组织osmOrg.code、收票组织inInvoiceOrg.code、委外供应商vendor.code、仓库编码warehouse.code、物料编码osmInRecords.product.cCode、物料分类osmInRecords.product.manageClass.code、物料SKU编码osmInRecords.productsku.cCode等)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865730</id>
<name>op</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865728</parentId>
<defParamId/>
<array>false</array>
<paramDesc>比较符(in:包含;eq:等于;lt:小于;gt:大于;like:模糊匹配;between:介于)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>op</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865731</id>
<name>value1</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865728</parentId>
<defParamId/>
<array>false</array>
<paramDesc>值1(条件)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value1</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865732</id>
<name>value2</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865728</parentId>
<defParamId/>
<array>false</array>
<paramDesc>值2(条件)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value2</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>simpleVOs</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>1872936977697865877</id>
<name>code</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId>1861195559319633950</defParamId>
<array>false</array>
<paramDesc>返回码，调用成功时返回200。</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>1872936977697865878</id>
<name>message</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<defParamId>1861195559319633951</defParamId>
<array>false</array>
<paramDesc>调用失败时的错误信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>1872936977697865740</id>
<name>data</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId/>
<children>
<children>
<id>1872936977697865871</id>
<name>pageIndex</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865740</parentId>
<defParamId>1861195559319633953</defParamId>
<array>false</array>
<paramDesc>当前页</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865872</id>
<name>pageSize</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865740</parentId>
<defParamId>1861195559319633954</defParamId>
<array>false</array>
<paramDesc>每页显示数据数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865873</id>
<name>pageCount</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865740</parentId>
<defParamId>1861195559319633955</defParamId>
<array>false</array>
<paramDesc>总页数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865874</id>
<name>beginPageIndex</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865740</parentId>
<defParamId>1861195559319633956</defParamId>
<array>false</array>
<paramDesc>开始页</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865875</id>
<name>endPageIndex</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865740</parentId>
<defParamId>1861195559319633957</defParamId>
<array>false</array>
<paramDesc>结束页</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865876</id>
<name>recordCount</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865740</parentId>
<defParamId>1861195559319633958</defParamId>
<array>false</array>
<paramDesc>总记录数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865741</id>
<name>recordList</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865740</parentId>
<children>
<children>
<id>1872936977697865754</id>
<name>osmInRecords_productionType</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633960</defParamId>
<array>false</array>
<paramDesc>产出类型</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865755</id>
<name>vendor_code</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633961</defParamId>
<array>false</array>
<paramDesc>委外供应商编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865756</id>
<name>oriTax</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633962</defParamId>
<array>false</array>
<paramDesc>税额</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865757</id>
<name>pocode</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633963</defParamId>
<array>false</array>
<paramDesc>委外订单编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865758</id>
<name>product_cCode</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633964</defParamId>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865759</id>
<name>invoiceVendor</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633965</defParamId>
<array>false</array>
<paramDesc>开票供应商ID</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865760</id>
<name>sfee</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633966</defParamId>
<array>false</array>
<paramDesc>累计结算费用</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865761</id>
<name>priceUOM_Precision</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633967</defParamId>
<array>false</array>
<paramDesc>计价单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865762</id>
<name>memo</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633968</defParamId>
<array>false</array>
<paramDesc>备注</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865763</id>
<name>stockStatusDoc_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633969</defParamId>
<array>false</array>
<paramDesc>库存状态</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865764</id>
<name>priceUOM_Code</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633970</defParamId>
<array>false</array>
<paramDesc>计价单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865765</id>
<name>totalQuantity</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633971</defParamId>
<array>false</array>
<paramDesc>整单数量</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865766</id>
<name>natCurrency</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633972</defParamId>
<array>false</array>
<paramDesc>本币ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865767</id>
<name>taxitems_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633973</defParamId>
<array>false</array>
<paramDesc>税目名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865768</id>
<name>stockUnitId_Precision</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633974</defParamId>
<array>false</array>
<paramDesc>库存单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865769</id>
<name>costMoney</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633975</defParamId>
<array>false</array>
<paramDesc>成本金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865770</id>
<name>id</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633976</defParamId>
<array>false</array>
<paramDesc>单据主表id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865771</id>
<name>tplid</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633977</defParamId>
<array>false</array>
<paramDesc>模板id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865772</id>
<name>isWfControlled</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633978</defParamId>
<array>false</array>
<paramDesc>是否审批流控制（true:审批流控制 false:非审批流控制）</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865773</id>
<name>natSum</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633979</defParamId>
<array>false</array>
<paramDesc>本币含税金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865774</id>
<name>warehouse</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633980</defParamId>
<array>false</array>
<paramDesc>仓库id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865775</id>
<name>isAutomaticVerify</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633981</defParamId>
<array>false</array>
<paramDesc>是否自动核销，true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865776</id>
<name>warehouse_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633982</defParamId>
<array>false</array>
<paramDesc>仓库</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865777</id>
<name>auditTime</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633983</defParamId>
<array>false</array>
<paramDesc>审核时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865778</id>
<name>natCurrency_priceDigit</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633984</defParamId>
<array>false</array>
<paramDesc>本币单价精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865779</id>
<name>exchRateType</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633985</defParamId>
<array>false</array>
<paramDesc>汇率类型ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865780</id>
<name>billqty</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633986</defParamId>
<array>false</array>
<paramDesc>累计开票数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865781</id>
<name>invExchRate</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633987</defParamId>
<array>false</array>
<paramDesc>换算率</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865782</id>
<name>status</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633988</defParamId>
<array>false</array>
<paramDesc>单据状态，0 开立 1已审核 3 审核中</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865783</id>
<name>isGiftProduct</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633989</defParamId>
<array>false</array>
<paramDesc>赠品，true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865784</id>
<name>returncount</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633990</defParamId>
<array>false</array>
<paramDesc>退回次数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865785</id>
<name>verifystate</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633991</defParamId>
<array>false</array>
<paramDesc>审批状态 （0：未提交 1：已提交 2：已审核）</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865786</id>
<name>invoicingDocEntryAndMgmt</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633992</defParamId>
<array>false</array>
<paramDesc>立账开票依据</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865787</id>
<name>isVerification</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633993</defParamId>
<array>false</array>
<paramDesc>核销标识</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865788</id>
<name>currency_moneyDigit</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633994</defParamId>
<array>false</array>
<paramDesc>币种金额精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865789</id>
<name>warehouse_code</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633995</defParamId>
<array>false</array>
<paramDesc>仓库编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865790</id>
<name>stockStatusDoc</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633996</defParamId>
<array>false</array>
<paramDesc>库存状态id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865791</id>
<name>productsku_cName</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633997</defParamId>
<array>false</array>
<paramDesc>物料sku名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865792</id>
<name>osmOrg_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633998</defParamId>
<array>false</array>
<paramDesc>委外组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865793</id>
<name>vouchdate</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319633999</defParamId>
<array>false</array>
<paramDesc>单据日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865794</id>
<name>receiptDocEntryAndMgmt</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634000</defParamId>
<array>false</array>
<paramDesc>入库立账方式</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865795</id>
<name>natCurrency_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634001</defParamId>
<array>false</array>
<paramDesc>本币名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865796</id>
<name>invoiceVendor_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634002</defParamId>
<array>false</array>
<paramDesc>开票供应商</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865797</id>
<name>invPriceExchRate</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634003</defParamId>
<array>false</array>
<paramDesc>计价换算率</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865798</id>
<name>vendor</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634004</defParamId>
<array>false</array>
<paramDesc>委外供应商id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865799</id>
<name>sqty</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634005</defParamId>
<array>false</array>
<paramDesc>累计结算数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865800</id>
<name>currency</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634006</defParamId>
<array>false</array>
<paramDesc>币种ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865801</id>
<name>pubts</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634007</defParamId>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865802</id>
<name>smoney</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634008</defParamId>
<array>false</array>
<paramDesc>累计结算金额</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865803</id>
<name>org_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634009</defParamId>
<array>false</array>
<paramDesc>收货组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865804</id>
<name>isFlowCoreBill</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634010</defParamId>
<array>false</array>
<paramDesc>是否流程核心单据,true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865805</id>
<name>auditDate</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634011</defParamId>
<array>false</array>
<paramDesc>审核日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865806</id>
<name>creator</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634012</defParamId>
<array>false</array>
<paramDesc>创建人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865807</id>
<name>product</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634013</defParamId>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865808</id>
<name>oriSum</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634014</defParamId>
<array>false</array>
<paramDesc>含税金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865809</id>
<name>inInvoiceOrg_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634015</defParamId>
<array>false</array>
<paramDesc>收票组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865810</id>
<name>exchRateType_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634016</defParamId>
<array>false</array>
<paramDesc>汇率类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865811</id>
<name>department_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634017</defParamId>
<array>false</array>
<paramDesc>委外部门</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865812</id>
<name>auditor</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634018</defParamId>
<array>false</array>
<paramDesc>审核人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865813</id>
<name>accountOrg</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634019</defParamId>
<array>false</array>
<paramDesc>会计主体</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865814</id>
<name>priceQty</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634020</defParamId>
<array>false</array>
<paramDesc>计价数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865815</id>
<name>createTime</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634021</defParamId>
<array>false</array>
<paramDesc>创建时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865816</id>
<name>natMoney</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634022</defParamId>
<array>false</array>
<paramDesc>本币无税金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>62</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865817</id>
<name>taxitems_code</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634023</defParamId>
<array>false</array>
<paramDesc>税目编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>63</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865818</id>
<name>department_code</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634024</defParamId>
<array>false</array>
<paramDesc>委外部门编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>64</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865819</id>
<name>osmInRecords_osmType</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634025</defParamId>
<array>false</array>
<paramDesc>委外类型</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>65</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865820</id>
<name>currency_priceDigit</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634026</defParamId>
<array>false</array>
<paramDesc>币种单价精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>66</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865821</id>
<name>stockUnit_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634027</defParamId>
<array>false</array>
<paramDesc>库存单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>67</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865822</id>
<name>isBeginning</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634028</defParamId>
<array>false</array>
<paramDesc>是否期初，,true:是、false:否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>68</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865823</id>
<name>bustype_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634029</defParamId>
<array>false</array>
<paramDesc>交易类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>69</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865824</id>
<name>modifier</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634030</defParamId>
<array>false</array>
<paramDesc>修改人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>70</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865825</id>
<name>natTax</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634031</defParamId>
<array>false</array>
<paramDesc>本币税额</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>71</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865826</id>
<name>source</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634032</defParamId>
<array>false</array>
<paramDesc>上游单据类型，po_subcontract_order：委外订单，po_osm_arrive_order：委外到货单</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>72</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865827</id>
<name>srcBill</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634033</defParamId>
<array>false</array>
<paramDesc>来源单据id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>73</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865828</id>
<name>subQty</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634034</defParamId>
<array>false</array>
<paramDesc>件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>74</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865829</id>
<name>modifyTime</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634035</defParamId>
<array>false</array>
<paramDesc>修改时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>75</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865830</id>
<name>inInvoiceOrg</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634036</defParamId>
<array>false</array>
<paramDesc>收票组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>76</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865831</id>
<name>product_cName</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634037</defParamId>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>77</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865832</id>
<name>vendor_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634038</defParamId>
<array>false</array>
<paramDesc>委外供应商</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>78</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865833</id>
<name>oriUnitPrice</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634039</defParamId>
<array>false</array>
<paramDesc>无税单价</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>79</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865834</id>
<name>barCode</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634040</defParamId>
<array>false</array>
<paramDesc>单据条码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>80</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865835</id>
<name>unit_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634041</defParamId>
<array>false</array>
<paramDesc>计量单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>81</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865836</id>
<name>taxRate</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634042</defParamId>
<array>false</array>
<paramDesc>税率</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>82</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865837</id>
<name>unit</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634043</defParamId>
<array>false</array>
<paramDesc>单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>83</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865838</id>
<name>productsku</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634044</defParamId>
<array>false</array>
<paramDesc>物料SKUid</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>84</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865839</id>
<name>productsku_cCode</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634045</defParamId>
<array>false</array>
<paramDesc>物料sku编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>85</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865840</id>
<name>natCurrency_moneyDigit</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634046</defParamId>
<array>false</array>
<paramDesc>本币金额精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>86</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865841</id>
<name>accountOrg_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634047</defParamId>
<array>false</array>
<paramDesc>会计主体</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>87</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865842</id>
<name>qty</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634048</defParamId>
<array>false</array>
<paramDesc>数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>88</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865843</id>
<name>unit_Precision</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634049</defParamId>
<array>false</array>
<paramDesc>主计量精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>89</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865844</id>
<name>oriTaxUnitPrice</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634050</defParamId>
<array>false</array>
<paramDesc>含税单价</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>90</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865845</id>
<name>oriMoney</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634051</defParamId>
<array>false</array>
<paramDesc>无税金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>91</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865846</id>
<name>contactsPieces</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634052</defParamId>
<array>false</array>
<paramDesc>应收件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>92</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865847</id>
<name>contactsQuantity</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634053</defParamId>
<array>false</array>
<paramDesc>应收数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>93</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865848</id>
<name>natUnitPrice</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634054</defParamId>
<array>false</array>
<paramDesc>本币无税单价</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>94</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865849</id>
<name>code</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634055</defParamId>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>95</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865850</id>
<name>exchRate</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634056</defParamId>
<array>false</array>
<paramDesc>汇率</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>96</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865851</id>
<name>osmInRecords_id</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634057</defParamId>
<array>false</array>
<paramDesc>订单行id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>97</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865852</id>
<name>priceUOM</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634058</defParamId>
<array>false</array>
<paramDesc>计价单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>98</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865853</id>
<name>department</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634059</defParamId>
<array>false</array>
<paramDesc>委外部门ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>99</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865854</id>
<name>currency_name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634060</defParamId>
<array>false</array>
<paramDesc>币种名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>100</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865855</id>
<name>org</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634061</defParamId>
<array>false</array>
<paramDesc>收货组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>101</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865856</id>
<name>custom</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634062</defParamId>
<array>false</array>
<paramDesc>客户id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>102</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865857</id>
<name>osmOrg</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634063</defParamId>
<array>false</array>
<paramDesc>委外组织ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>103</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865858</id>
<name>bustype</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634064</defParamId>
<array>false</array>
<paramDesc>交易类型id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>104</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865859</id>
<name>costUnitPrice</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634065</defParamId>
<array>false</array>
<paramDesc>成本单价</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>105</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865860</id>
<name>upcode</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634066</defParamId>
<array>false</array>
<paramDesc>上游单据号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>106</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865861</id>
<name>priceUOM_Name</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634067</defParamId>
<array>false</array>
<paramDesc>计价单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>107</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865862</id>
<name>taxitems</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634068</defParamId>
<array>false</array>
<paramDesc>税目id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>108</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865863</id>
<name>natTaxUnitPrice</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634069</defParamId>
<array>false</array>
<paramDesc>本币含税单价</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>109</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865864</id>
<name>unDeductTaxRate</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634070</defParamId>
<array>false</array>
<paramDesc>不可抵扣税率</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>110</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865865</id>
<name>unDeductTax</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634071</defParamId>
<array>false</array>
<paramDesc>不可抵扣税额</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>111</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865866</id>
<name>oriUnDeductTax</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634072</defParamId>
<array>false</array>
<paramDesc>原币不可抵扣税额</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>112</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865742</id>
<name>bodyItem</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<children>
<children>
<id>1872936977697865743</id>
<name>define1</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865742</parentId>
<defParamId>1861195559319634074</defParamId>
<array>false</array>
<paramDesc>单据体自定义(单据体自定义项最多可设置60个)</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1861195559319634073</defParamId>
<array>false</array>
<paramDesc>单据体自定义项</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>113</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865744</id>
<name>headItem</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<children>
<children>
<id>1872936977697865745</id>
<name>define1</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865744</parentId>
<defParamId>1861195559319634076</defParamId>
<array>false</array>
<paramDesc>单据头自定义(单据头自定义项最多可设置60个)</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1861195559319634075</defParamId>
<array>false</array>
<paramDesc>单据头自定义项</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>114</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865746</id>
<name>osmInRecords</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<children>
<children>
<id>1872936977697865747</id>
<name>opSn</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865746</parentId>
<defParamId>1861195559319634081</defParamId>
<array>false</array>
<paramDesc>工序顺序号</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865748</id>
<name>operationId</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865746</parentId>
<defParamId>1861195559319634082</defParamId>
<array>false</array>
<paramDesc>工序</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865749</id>
<name>endOp</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865746</parentId>
<defParamId>1861195559319634083</defParamId>
<array>false</array>
<paramDesc>末序(false:否,true:是)</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865750</id>
<name>sourcePoOrderCode</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865746</parentId>
<defParamId>1861195559319634084</defParamId>
<array>false</array>
<paramDesc>生产订单号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865751</id>
<name>sourcePoOrderProductRowno</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865746</parentId>
<defParamId>1861195559319634085</defParamId>
<array>false</array>
<paramDesc>生产订单行号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865752</id>
<name>sourcePoOrderId</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865746</parentId>
<defParamId>1861195559319634086</defParamId>
<array>false</array>
<paramDesc>生产订单ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865753</id>
<name>sourcePoOrderProductId</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865746</parentId>
<defParamId>1861195559319634087</defParamId>
<array>false</array>
<paramDesc>生产订单行ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1861195559319634080</defParamId>
<array>false</array>
<paramDesc>委外入库单子表</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>118</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>1872936977697865870</id>
<name>costAccountingMethod</name>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<parentId>1872936977697865741</parentId>
<defParamId>1861195559319634098</defParamId>
<array>false</array>
<paramDesc>委外成本核算方式(0:按委外入库核算成本,1:按委外订单核算成本)</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>119</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1861195559319633959</defParamId>
<array>true</array>
<paramDesc>返回结果对象</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1861195559319633952</defParamId>
<array>false</array>
<paramDesc>调用成功时的返回数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>1872936977697865884</id>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "recordList": [ { "osmInRecords_productionType": 0, "vendor_code": "", "oriTax": 0, "pocode": "", "product_cCode": "", "invoiceVendor": 0, "sfee": 0, "priceUOM_Precision": 0, "memo": "", "stockStatusDoc_name": "", "priceUOM_Code": "", "totalQuantity": 0, "natCurrency": "", "taxitems_name": "", "stockUnitId_Precision": 0, "costMoney": 0, "id": 0, "tplid": 0, "isWfControlled": true, "natSum": 0, "warehouse": 0, "isAutomaticVerify": true, "warehouse_name": "", "auditTime": "", "natCurrency_priceDigit": 0, "exchRateType": "", "billqty": 0, "invExchRate": 0, "status": 0, "isGiftProduct": true, "returncount": 0, "verifystate": 0, "invoicingDocEntryAndMgmt": "", "isVerification": 0, "currency_moneyDigit": 0, "warehouse_code": "", "stockStatusDoc": 0, "productsku_cName": "", "osmOrg_name": "", "vouchdate": "", "receiptDocEntryAndMgmt": "", "natCurrency_name": "", "invoiceVendor_name": "", "invPriceExchRate": 0, "vendor": 0, "sqty": 0, "currency": "", "pubts": "", "smoney": 0, "org_name": "", "isFlowCoreBill": true, "auditDate": "", "creator": "", "product": 0, "oriSum": 0, "inInvoiceOrg_name": "", "exchRateType_name": "", "department_name": "", "auditor": "", "accountOrg": "", "priceQty": 0, "createTime": "", "natMoney": 0, "taxitems_code": "", "department_code": "", "osmInRecords_osmType": 0, "currency_priceDigit": 0, "stockUnit_name": "", "isBeginning": true, "bustype_name": "", "modifier": "", "natTax": 0, "source": "", "srcBill": "", "subQty": 0, "modifyTime": "", "inInvoiceOrg": "", "product_cName": "", "vendor_name": "", "oriUnitPrice": 0, "barCode": "", "unit_name": "", "taxRate": 0, "unit": 0, "productsku": 0, "productsku_cCode": "", "natCurrency_moneyDigit": 0, "accountOrg_name": "", "qty": 0, "unit_Precision": 0, "oriTaxUnitPrice": 0, "oriMoney": 0, "contactsPieces": 0, "contactsQuantity": 0, "natUnitPrice": 0, "code": "", "exchRate": 0, "osmInRecords_id": 0, "priceUOM": 0, "department": "", "currency_name": "", "org": "", "custom": 0, "osmOrg": "", "bustype": "", "costUnitPrice": 0, "upcode": "", "priceUOM_Name": "", "taxitems": "", "natTaxUnitPrice": 0, "unDeductTaxRate": 0, "unDeductTax": 0, "oriUnDeductTax": 0, "bodyItem": { "define1": "" }, "headItem": { "define1": "" }, "osmInRecordsCharacteristics": 0, "osmInRecordsDefineCharacter": 0, "osmInRecordDefineCharacter": 0, "osmInRecords": { "opSn": 0, "operationId": 0, "endOp": true, "sourcePoOrderCode": "", "sourcePoOrderProductRowno": "", "sourcePoOrderId": 0, "sourcePoOrderProductId": 0 }, "costAccountingMethod": "" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>1872936977697865885</id>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<content>{ "code": "999", "message": "No enum constant org.imeta.core.base.ConditionOperator.2" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>1872936977697865884</id>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "recordList": [ { "osmInRecords_productionType": 0, "vendor_code": "", "oriTax": 0, "pocode": "", "product_cCode": "", "invoiceVendor": 0, "sfee": 0, "priceUOM_Precision": 0, "memo": "", "stockStatusDoc_name": "", "priceUOM_Code": "", "totalQuantity": 0, "natCurrency": "", "taxitems_name": "", "stockUnitId_Precision": 0, "costMoney": 0, "id": 0, "tplid": 0, "isWfControlled": true, "natSum": 0, "warehouse": 0, "isAutomaticVerify": true, "warehouse_name": "", "auditTime": "", "natCurrency_priceDigit": 0, "exchRateType": "", "billqty": 0, "invExchRate": 0, "status": 0, "isGiftProduct": true, "returncount": 0, "verifystate": 0, "invoicingDocEntryAndMgmt": "", "isVerification": 0, "currency_moneyDigit": 0, "warehouse_code": "", "stockStatusDoc": 0, "productsku_cName": "", "osmOrg_name": "", "vouchdate": "", "receiptDocEntryAndMgmt": "", "natCurrency_name": "", "invoiceVendor_name": "", "invPriceExchRate": 0, "vendor": 0, "sqty": 0, "currency": "", "pubts": "", "smoney": 0, "org_name": "", "isFlowCoreBill": true, "auditDate": "", "creator": "", "product": 0, "oriSum": 0, "inInvoiceOrg_name": "", "exchRateType_name": "", "department_name": "", "auditor": "", "accountOrg": "", "priceQty": 0, "createTime": "", "natMoney": 0, "taxitems_code": "", "department_code": "", "osmInRecords_osmType": 0, "currency_priceDigit": 0, "stockUnit_name": "", "isBeginning": true, "bustype_name": "", "modifier": "", "natTax": 0, "source": "", "srcBill": "", "subQty": 0, "modifyTime": "", "inInvoiceOrg": "", "product_cName": "", "vendor_name": "", "oriUnitPrice": 0, "barCode": "", "unit_name": "", "taxRate": 0, "unit": 0, "productsku": 0, "productsku_cCode": "", "natCurrency_moneyDigit": 0, "accountOrg_name": "", "qty": 0, "unit_Precision": 0, "oriTaxUnitPrice": 0, "oriMoney": 0, "contactsPieces": 0, "contactsQuantity": 0, "natUnitPrice": 0, "code": "", "exchRate": 0, "osmInRecords_id": 0, "priceUOM": 0, "department": "", "currency_name": "", "org": "", "custom": 0, "osmOrg": "", "bustype": "", "costUnitPrice": 0, "upcode": "", "priceUOM_Name": "", "taxitems": "", "natTaxUnitPrice": 0, "unDeductTaxRate": 0, "unDeductTax": 0, "oriUnDeductTax": 0, "bodyItem": { "define1": "" }, "headItem": { "define1": "" }, "osmInRecordsCharacteristics": 0, "osmInRecordsDefineCharacter": 0, "osmInRecordDefineCharacter": 0, "osmInRecords": { "opSn": 0, "operationId": 0, "endOp": true, "sourcePoOrderCode": "", "sourcePoOrderProductRowno": "", "sourcePoOrderId": 0, "sourcePoOrderProductId": 0 }, "costAccountingMethod": "" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>1872936977697865885</id>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<content>{ "code": "999", "message": "No enum constant org.imeta.core.base.ConditionOperator.2" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>1872936977697865884</id>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "recordList": [ { "osmInRecords_productionType": 0, "vendor_code": "", "oriTax": 0, "pocode": "", "product_cCode": "", "invoiceVendor": 0, "sfee": 0, "priceUOM_Precision": 0, "memo": "", "stockStatusDoc_name": "", "priceUOM_Code": "", "totalQuantity": 0, "natCurrency": "", "taxitems_name": "", "stockUnitId_Precision": 0, "costMoney": 0, "id": 0, "tplid": 0, "isWfControlled": true, "natSum": 0, "warehouse": 0, "isAutomaticVerify": true, "warehouse_name": "", "auditTime": "", "natCurrency_priceDigit": 0, "exchRateType": "", "billqty": 0, "invExchRate": 0, "status": 0, "isGiftProduct": true, "returncount": 0, "verifystate": 0, "invoicingDocEntryAndMgmt": "", "isVerification": 0, "currency_moneyDigit": 0, "warehouse_code": "", "stockStatusDoc": 0, "productsku_cName": "", "osmOrg_name": "", "vouchdate": "", "receiptDocEntryAndMgmt": "", "natCurrency_name": "", "invoiceVendor_name": "", "invPriceExchRate": 0, "vendor": 0, "sqty": 0, "currency": "", "pubts": "", "smoney": 0, "org_name": "", "isFlowCoreBill": true, "auditDate": "", "creator": "", "product": 0, "oriSum": 0, "inInvoiceOrg_name": "", "exchRateType_name": "", "department_name": "", "auditor": "", "accountOrg": "", "priceQty": 0, "createTime": "", "natMoney": 0, "taxitems_code": "", "department_code": "", "osmInRecords_osmType": 0, "currency_priceDigit": 0, "stockUnit_name": "", "isBeginning": true, "bustype_name": "", "modifier": "", "natTax": 0, "source": "", "srcBill": "", "subQty": 0, "modifyTime": "", "inInvoiceOrg": "", "product_cName": "", "vendor_name": "", "oriUnitPrice": 0, "barCode": "", "unit_name": "", "taxRate": 0, "unit": 0, "productsku": 0, "productsku_cCode": "", "natCurrency_moneyDigit": 0, "accountOrg_name": "", "qty": 0, "unit_Precision": 0, "oriTaxUnitPrice": 0, "oriMoney": 0, "contactsPieces": 0, "contactsQuantity": 0, "natUnitPrice": 0, "code": "", "exchRate": 0, "osmInRecords_id": 0, "priceUOM": 0, "department": "", "currency_name": "", "org": "", "custom": 0, "osmOrg": "", "bustype": "", "costUnitPrice": 0, "upcode": "", "priceUOM_Name": "", "taxitems": "", "natTaxUnitPrice": 0, "unDeductTaxRate": 0, "unDeductTax": 0, "oriUnDeductTax": 0, "bodyItem": { "define1": "" }, "headItem": { "define1": "" }, "osmInRecordsCharacteristics": 0, "osmInRecordsDefineCharacter": 0, "osmInRecordDefineCharacter": 0, "osmInRecords": { "opSn": 0, "operationId": 0, "endOp": true, "sourcePoOrderCode": "", "sourcePoOrderProductRowno": "", "sourcePoOrderId": 0, "sourcePoOrderProductId": 0 }, "costAccountingMethod": "" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>1872936977697865885</id>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<content>{ "code": "999", "message": "No enum constant org.imeta.core.base.ConditionOperator.2" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS>
<errorCodeDTOS>
<id>1872936977697865881</id>
<apiId>a321db2a1d7f4e4ba4859f28a57a9da6</apiId>
<errorCode>999</errorCode>
<errorMessage>服务端逻辑异常</errorMessage>
<errorType>API</errorType>
<errorcodeDesc/>
<gmtCreate>2023-11-29 14:16:01.000</gmtCreate>
<gmtUpdate>2023-11-29 14:16:01.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<defErrorId>1861195559319634248</defErrorId>
<ytenantId/>
<displayCodeId/>
</errorCodeDTOS>
</errorCodeDTOS>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>友户通token认证业务扩展插件</name>
<configurable>false</configurable>
<description>YonsuitBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>a321db2a1d7f4e4ba4859f28a57a9da6</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin>
<id>w181ed01-1e9b-4350-b994-71a66f017555</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code>resultParse</code>
<name>返回参数转换插件</name>
<configurable>false</configurable>
<description>解决返回值中带！的，转换为json</description>
<pluginType>resultParse</pluginType>
<pluginTypeName>返回值解析插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.result.ResultMapTransferParsePlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>true</visible>
<gmtCreate>2020-07-29 00:00:00</gmtCreate>
<gmtUpdate/>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>a321db2a1d7f4e4ba4859f28a57a9da6</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</resultParsePlugin>
<mapReturnPluginConfig/>
<billNo/>
<domain/>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser>36a8b72b-d965-404d-a02d-66ff4a7afeb3</createUser>
<createUserName/>
<approvalStatus>1</approvalStatus>
<publishTime>2023-11-29 14:16:14</publishTime>
<pathJoin>false</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout>0</mockTimeout>
<customUrl>osminrecord/list</customUrl>
<fixedUrl>/yonbip/scm/</fixedUrl>
<apiCode>a321db2a1d7f4e4ba4859f28a57a9da6</apiCode>
<tokenCheckType/>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL/>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>36a8b72b-d965-404d-a02d-66ff4a7afeb3</updateUserId>
<updateUserName>昵称-王章宇</updateUserName>
<paramIsForce>true</paramIsForce>
<userIDPassthrough>true</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.yonbip-scm-stock</microServiceCode>
<applicationCode>yonbip-scm-stock</applicationCode>
<privacyCategory>1</privacyCategory>
<privacyLevel>3</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType>0</serviceType>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType/>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>false</arrayParam>
<fileSize/>
<cc>true</cc>
<paramTransferMode>2</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId>1861195559319633924</apiDefId>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>1</paramExtRequest>
<paramExtResponse>1</paramExtResponse>
<paramExtInExtendKey>1</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene/>
<apiType/>
<paramMark/>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>false</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>false</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>1</chargeStatus>
<beforeSpeed>40</beforeSpeed>
<afterSpeed>80</afterSpeed>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath/>
<pubHistory/>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode/>
<multiVersion>0</multiVersion>
<apiTag/>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>2108770660671029249</id>
<name>用友YonBIP</name>
<type>integrateSys</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>SCC</id>
<name>供应链云</name>
<type>1</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MM</id>
<name>采购供应</name>
<type>2</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>ST</id>
<name>库存管理</name>
<type>3</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>ustock.st_osminrecord</id>
<name>委外入库单</name>
<type>4</type>
<sort>0</sort>
<enable>0</enable>
<children/>
<parentId/>
<productId/>
<code>ustock.st_osminrecord</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>ST</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MM</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>SCC</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>current_yonbip_default_sys</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<isOrigin>0</isOrigin>
<hasChildren>0</hasChildren>
<order>0</order>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>72113971-ae4c-4188-bc55-44b6173f4e0b</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:29:59</gmtCreate>
<gmtUpdate>2025-07-26 17:29:59</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>b946709d-f4d9-4a43-a551-f55beee7f3d5</id>
<name>XXX0111</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类项</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:29:59</gmtCreate>
<gmtUpdate>2025-07-26 17:29:59</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:29:59</gmtCreate>
<gmtUpdate>2025-07-26 17:29:59</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>97f27951-ce5e-460b-964a-a1af5fbfd786</id>
<name>CG02</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>检验数</paramDesc>
<paramType>Decimal</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>number</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:30:10</gmtCreate>
<gmtUpdate>2025-07-26 17:30:10</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>24</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>16056259-e88e-4cf0-9ccb-62d881fb9426</id>
<name>CG03</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>合格数</paramDesc>
<paramType>Decimal</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>number</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:30:10</gmtCreate>
<gmtUpdate>2025-07-26 17:30:10</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>24</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>4d8e812b-5658-461e-8fe1-988462ebfc9c</id>
<name>CG05</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>送货单号（单身）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:30:10</gmtCreate>
<gmtUpdate>2025-07-26 17:30:10</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>26422e53-b04e-48b1-bbfa-a190bf54f59b</id>
<name>WW</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>委外交货日期</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>date</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:30:10</gmtCreate>
<gmtUpdate>2025-07-26 17:30:10</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>eded1c3e-5903-49a9-8615-4986e9abc719</id>
<name>XS11</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类号test</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:30:10</gmtCreate>
<gmtUpdate>2025-07-26 17:30:10</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:30:10</gmtCreate>
<gmtUpdate>2025-07-26 17:30:10</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>36470ffe-96dd-4e1c-b3eb-9424e51cf835</id>
<name>CG04</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>送货单号（单头）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:30:20</gmtCreate>
<gmtUpdate>2025-07-26 17:30:20</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:30:20</gmtCreate>
<gmtUpdate>2025-07-26 17:30:20</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>