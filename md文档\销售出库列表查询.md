销售出库列表查询
发布时间:2024-09-28 01:57:55
销售出库列表查询

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	动态域名，获取方式详见 获取租户所在数据中心域名
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/salesout/list
请求方式	POST
ContentType	application/json
应用场景	开放API
API类别	
事务和幂等性	无
限流次数	60次/分钟

用户身份	支持传递普通用户身份，详细说明见开放平台用户认证接入规范
多语	不支持
2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
isdefault	string	否	否	该参数可忽略不管
pageIndex	int	否	是	页号    默认值: 1
code	string	否	否	单据编号
pageSize	int	否	是	每页行数    默认值: 10
vouchdate	string	否	否	单据日期 区间格式，2021-05-06|2021-05-06 23:00:00。若传入单个时间如：2021-05-06，则查询该时间之后，到当前时间之间的单据
stockOrg	object	否	否	库存组织id
salesOrg	object	否	否	销售组织id
invoiceOrg	object	否	否	开票组织ID
invoiceCust	object	否	否	开票客户id
upcode	string	否	否	来源单据号
department	object	否	否	部门id
operator	object	否	否	业务员id
warehouse	object	否	否	仓库id
stockMgr	object	否	否	库管员id
cust	object	否	否	客户id
product_cName	string	否	否	物料id
bustype.name	object	否	否	交易类型名称
product_cName_ManageClass	object	否	否	物料分类id
isSum	boolean	否	否	查询表头    示例: false    默认值: false
simpleVOs	object	是	否	扩展查询条件
op	string	否	否	比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )
value2	string	否	否	值2(条件)如："2021-04-19 23:59:59"
value1	string	否	否	值1(条件)如： "2021-04-19 00:00:00"
field	string	否	否	属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码details.product.cCode、表头自定义项headItem.define1、表体自定义项details.bodyItem.define1等)
3. 请求示例
Url: /yonbip/scm/salesout/list?access_token=访问令牌
Body: {
	"isdefault": "",
	"pageIndex": 0,
	"code": "",
	"pageSize": 0,
	"vouchdate": "",
	"stockOrg": {},
	"salesOrg": {},
	"invoiceOrg": {},
	"invoiceCust": {},
	"upcode": "",
	"department": {},
	"operator": {},
	"warehouse": {},
	"stockMgr": {},
	"cust": {},
	"product_cName": "",
	"bustype.name": {},
	"product_cName_ManageClass": {},
	"isSum": false,
	"simpleVOs": [
		{
			"op": "",
			"value2": "",
			"value1": "",
			"field": ""
		}
	]
}
4. 返回值参数
名称	类型	数组	描述
code	string	否	返回码，调用成功时返回200
message	string	否	调用失败时的错误信息
data	object	否	调用成功时的返回数据
pageIndex	long	否	当前页
pageSize	long	否	分页大小
recordCount	long	否	总记录数
recordList	object	是	返回数据列表
cReceiveAddress	string	否	收货地址
oriTax	double	否	税率
details_stockUnitId	long	否	库存单位主键
product_cCode	string	否	物料编码
details_taxId	string	否	税目主键
natCurrency	string	否	本币主键
sourcesys	string	否	来源单据领域
tradeRouteID	long	否	贸易路径id
stockUnitId_Precision	long	否	库存单位精度
id	long	否	主键
status_mobile_row	long	否	单据状态
invoiceTitle	string	否	发票抬头
details_priceUOM	long	否	计价单位主键
natSum	long	否	本币含税金额
isEndTrade	short	否	是否末级
warehouse	long	否	仓库主键
srcBillType	string	否	来源单据类型
diliverStatus	string	否	发货状态
warehouse_name	string	否	仓库名称
natCurrency_priceDigit	long	否	本币精度
exchRateType	string	否	汇率类型枚举值
tradeRouteLineno	string	否	站点
invExchRate	long	否	单位转换率
product_defaultAlbumId	string	否	物料图片
status	long	否	单据状态；0开立，1已审核，3审核中
currency_moneyDigit	long	否	币种精度
invoiceCust_name	string	否	开票客户名称
details_productsku	long	否	物料KSU主键
salesOrg	string	否	销售组织主键
invoiceOrg_name	string	否	开票组织名称
tradeRoute_name	string	否	贸易路径
productsku_cName	string	否	物料SKU名称
vouchdate	string	否	单据日期
invPriceExchRate	long	否	计价单位转换率
currency	string	否	原币主键
pubts	string	否	时间戳
org_name	string	否	发货组织名称
cReceiveMobile	string	否	收货电话
createDate	string	否	创建日期
creator	string	否	创建人
oriSum	long	否	原币含税金额
exchRateType_name	string	否	汇率类型名称
accountOrg	string	否	会计主体主键
stsalesOutExchangeInfo_d_key	long	否	逻辑字段冗余
cReceiver	string	否	收货人
details_id	long	否	子表主键
priceQty	long	否	计价数量
createTime	string	否	创建时间
taxUnitPriceTag	boolean	否	价格含税标志
details_product	long	否	物料主键
taxNum	string	否	纳税识别号
department_name	string	否	部门名称
operator_name	string	否	业务员名称
invoiceAddress	string	否	营业地址
operator	long	否	业务员主键
bankAccount	string	否	银行账号
subBankName	string	否	开户支行
bankName	string	否	开户银行
invoiceTelephone	string	否	营业电话
department	string	否	部门主键
cust	long	否	客户主键
invoiceUpcType	string	否	发票类型
natMoney	double	否	本币无税金额
currency_priceDigit	long	否	币种精度
invoiceOrg	string	否	开票客户主键
stockUnit_name	string	否	库存单位名称
collaborationPolineno	string	否	协同来源单据行号
bustype_name	string	否	交易类型名称
modifier	string	否	修改人
firstupcode	string	否	源头单据编码
source	string	否	来源单据类型
natTax	double	否	本币税额
subQty	long	否	件数
taxItems	string	否	税率显示值
modifyTime	string	否	修改时间
product_cName	string	否	物料名称
invoiceTitleType	string	否	发票抬头类型
receiveContacterPhone	string	否	收货人联系电话
modifyInvoiceType	string	否	发票类型可改标志
natCurrencyName	string	否	本币名称
salesOrg_name	string	否	销售组织名称
modifyDate	string	否	修改日期
unitName	string	否	主计量名称
contactName	string	否	联系人名称
srcBillNO	string	否	来源单据号
oriUnitPrice	double	否	原币无税单价
taxCode	string	否	税目编码
barCode	string	否	单据码
unit_name	long	否	主计量名称冗余
taxRate	long	否	税率
unit	string	否	库存单位
productsku_cCode	string	否	物料SKU编码
natCurrency_moneyDigit	long	否	本币精度
accountOrg_name	string	否	会计主体名称
taxId	string	否	税目编码
invoiceCust	long	否	开票客户主键
qty	long	否	数量
unit_Precision	long	否	主计量精度
oriTaxUnitPrice	long	否	原币含税单价
oriMoney	double	否	原币无税金额
contactsPieces	long	否	应发件数
contactsQuantity	long	否	应发数量
natUnitPrice	double	否	本币无税单价
code	string	否	单据编码
receiveAccountingBasis	string	否	立账开票依据
logistics	string	否	物料单号
exchRate	long	否	汇率
currencyName	string	否	币种名称
cust_name	string	否	客户名称
org	string	否	库存组织主键
priceUOM_name	string	否	计价单位名称
bustype	string	否	交易类型主键
receiveId	long	否	收货地址主键
upcode	string	否	来源单据号
saleStyle	string	否	商品售卖类型
iLogisticId	long	否	物流公司
status_mobile	long	否	单据状态
natTaxUnitPrice	long	否	本币含税单价
salesOutDefineCharacter	特征组
st.salesout.SalesOut	否	表头自定义项特征组
id	string	否	特征id,主键,新增时无需填写,修改时必填
salesOutsDefineCharacter	特征组
st.salesout.SalesOuts	否	表体自定义项特征组
SF04	string	否	发票号（表体）
SF05	Date	否	发货单日期
SF06	string	否	发货单号
XS15	string	否	顾客订单号（订单表体）
id	string	否	特征id,主键,新增时无需填写,修改时必填
salesOutsCharacteristics	特征组
st.salesout.SalesOuts	否	自由特征组
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
out_sys_id	string	否	外部来源线索
out_sys_code	string	否	外部来源编码
out_sys_version	string	否	外部来源版本
out_sys_type	string	否	外部来源类型
out_sys_rowno	string	否	外部来源行号
out_sys_lineid	string	否	外部来源行
collaborationPocode	string	否	协同来源单据号
collaborationPoid	long	否	协同来源单据id
collaborationPodetailid	long	否	协同来源单据子表id
collaborationSource	string	否	协同来源类型, 0:无来源、st_purinrecord:采购入库单、1:发货单、2:销售订单、3:退货单、tradeorder:电商订单、refundorder:电商退换货订单、retailvouch:零售单、mallvouch:商城发货单
salesOutsExtend!coUpcode	string	否	协同源头单据号
salesOutsExtend!coSourceid	long	否	协同源头单据头id
salesOutsExtend!coSourceLineNo	string	否	协同源头行号
salesOutsExtend!coSourceType	string	否	协同源头单据类型(upu.st_purchaseorder:采购订单,productionorder.po_subcontract_order:委外订单)
sumRecordList	object	是	合计信息
totalPieces	long	否	合计件数
oriSum	long	否	合计金额
invoiceOriSum	long	否	合计开票金额
saleReturnQty	long	否	合计退货数量
natSum	long	否	合计本币金额
subQty	long	否	合计副计量数量
totalQuantity	long	否	合计数量
priceQty	long	否	合计计价数量
qty	long	否	合计数量
oriMoney	double	否	合计原币无税金额
invoiceQty	long	否	合计开票数量
contactsPieces	long	否	合计应发件量
contactsQuantity	long	否	合计应发数量
natMoney	double	否	合计本币无税金额
pageCount	long	否	总页数
beginPageIndex	long	否	开始页页号
endPageIndex	long	否	最终页页号
pubts	string	否	时间戳
5. 正确返回示例
{
	"code": "200",
	"message": "操作成功",
	"data": {
		"pageIndex": 1,
		"pageSize": 10,
		"recordCount": 26,
		"recordList": [
			{
				"cReceiveAddress": "1111",
				"oriTax": 0.19,
				"details_stockUnitId": ****************,
				"product_cCode": "hy母件002",
				"details_taxId": "8b99f589-bc47-4c8a-bfqw-13d78caa20b0",
				"natCurrency": "G001ZM0000DEFAULTCURRENCT00000000001",
				"sourcesys": "udinghuo",
				"tradeRouteID": 0,
				"stockUnitId_Precision": 2,
				"id": ****************,
				"status_mobile_row": 0,
				"invoiceTitle": "123抬头",
				"details_priceUOM": ****************,
				"natSum": 4,
				"isEndTrade": 0,
				"warehouse": 1825292664836352,
				"srcBillType": "1",
				"diliverStatus": "DELIVERING",
				"warehouse_name": "调入仓库B",
				"natCurrency_priceDigit": 3,
				"exchRateType": "sfaju9kr",
				"tradeRouteLineno": "",
				"invExchRate": 1,
				"product_defaultAlbumId": "",
				"status": 0,
				"currency_moneyDigit": 2,
				"invoiceCust_name": "张三啊",
				"details_productsku": ****************,
				"salesOrg": "****************",
				"invoiceOrg_name": "hy组织001",
				"tradeRoute_name": "",
				"productsku_cName": "hy母件002",
				"vouchdate": "2021-06-01 00:00:00",
				"invPriceExchRate": 1,
				"currency": "G001ZM0000DEFAULTCURRENCT00000000001",
				"pubts": "2021-06-02 15:10:23",
				"org_name": "hy组织001",
				"cReceiveMobile": "4353",
				"createDate": "2021-06-01 00:00:00",
				"creator": "rtduanhy",
				"oriSum": 4,
				"exchRateType_name": "基准汇率",
				"accountOrg": "****************",
				"stsalesOutExchangeInfo_d_key": ****************,
				"cReceiver": "43543",
				"details_id": ****************,
				"priceQty": 2,
				"createTime": "2021-06-01 20:24:08",
				"taxUnitPriceTag": true,
				"details_product": ****************,
				"taxNum": "**********",
				"department_name": "XX部门",
				"operator_name": "某某",
				"invoiceAddress": "某地区街道",
				"operator": ***********,
				"bankAccount": "***********",
				"subBankName": "某某支行",
				"bankName": "某银行",
				"invoiceTelephone": "*********",
				"department": "*********",
				"cust": ****************,
				"invoiceUpcType": "0",
				"natMoney": 3.81,
				"currency_priceDigit": 3,
				"invoiceOrg": "****************",
				"stockUnit_name": "件",
				"collaborationPolineno": "",
				"bustype_name": "销售出库",
				"modifier": "rtduanhy",
				"firstupcode": "UO-test20210601000012",
				"source": "1",
				"natTax": 0.19,
				"subQty": 2,
				"taxItems": "5%",
				"modifyTime": "2021-06-02 15:10:23",
				"product_cName": "hy母件002",
				"invoiceTitleType": "0",
				"receiveContacterPhone": "***********",
				"modifyInvoiceType": "1",
				"natCurrencyName": "人民币",
				"salesOrg_name": "hy组织001",
				"modifyDate": "2021-06-02 00:00:00",
				"unitName": "件",
				"contactName": "张三",
				"srcBillNO": "******************",
				"oriUnitPrice": 1.905,
				"taxCode": "VAT5",
				"barCode": "st_salesout|****************",
				"unit_name": ****************,
				"taxRate": 5,
				"unit": "件",
				"productsku_cCode": "hy母件002",
				"natCurrency_moneyDigit": 2,
				"accountOrg_name": "hy组织001",
				"taxId": "VAT5",
				"invoiceCust": ****************,
				"qty": 2,
				"unit_Precision": 2,
				"oriTaxUnitPrice": 2,
				"oriMoney": 3.81,
				"contactsPieces": 2,
				"contactsQuantity": 2,
				"natUnitPrice": 1.905,
				"code": "XSCK20210601000001",
				"receiveAccountingBasis": "voucher_delivery",
				"logistics": "XSCK20210601000001",
				"exchRate": 1,
				"currencyName": "人民币",
				"cust_name": "张三啊",
				"org": "****************",
				"priceUOM_name": "件",
				"bustype": "***************",
				"receiveId": ****************,
				"upcode": "******************",
				"saleStyle": "SALE",
				"iLogisticId": 0,
				"status_mobile": 0,
				"natTaxUnitPrice": 2,
				"salesOutDefineCharacter": {
					"id": ""
				},
				"salesOutsDefineCharacter": {
					"SF04": "",
					"SF05": "",
					"SF06": "",
					"XS15": "",
					"id": ""
				},
				"salesOutsCharacteristics": {
					"XS15": "",
					"XXX0111": "",
					"id": ""
				},
				"out_sys_id": "",
				"out_sys_code": "",
				"out_sys_version": "",
				"out_sys_type": "",
				"out_sys_rowno": "",
				"out_sys_lineid": "",
				"collaborationPocode": "",
				"collaborationPoid": 0,
				"collaborationPodetailid": 0,
				"collaborationSource": "",
				"salesOutsExtend!coUpcode": "",
				"salesOutsExtend!coSourceid": 0,
				"salesOutsExtend!coSourceLineNo": "",
				"salesOutsExtend!coSourceType": ""
			}
		],
		"sumRecordList": [
			{
				"totalPieces": 2,
				"oriSum": 51222,
				"invoiceOriSum": 27922,
				"saleReturnQty": 34,
				"natSum": 51222,
				"subQty": 492,
				"totalQuantity": 2,
				"priceQty": 492,
				"qty": 492,
				"oriMoney": 49705.27,
				"invoiceQty": 298,
				"contactsPieces": 481,
				"contactsQuantity": 481,
				"natMoney": 49705.27
			}
		],
		"pageCount": 3,
		"beginPageIndex": 1,
		"endPageIndex": 3,
		"pubts": "2021-06-02 16:37:29"
	}
}
6. 错误返回码
错误码	错误信息	描述
999	列表查询失败	检查查询条件和单据编码是否正确
7. 错误返回示例
{"code":999,"message":"列表查询失败"}