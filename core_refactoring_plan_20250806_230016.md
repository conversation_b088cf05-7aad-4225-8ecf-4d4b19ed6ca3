# YS-API V3.0 核心重构计划

**生成时间**: 2025-08-06 23:00:16

## 📊 当前状态分析

- **总文件数**: 411
- **核心文件**: 0 (0.0%)
- **垃圾文件**: 290 (70.6%)

### 🔍 关键发现

- 🚨 **严重问题**: 没有识别出核心文件(得分≥0.7)
- ⚠️  **依赖混乱**: 最高得分文件仅0.29分
- 🗑️ **代码冗余**: 70.6%的文件被识别为垃圾代码
- 🔄 **重复结构**: backend和core目录大量重复
- 📦 **依赖分散**: 高依赖文件分布在scripts中

## 🎯 重构策略

### 阶段1: 保留核心架构
- ✅ `backend/start_server.py`
- ✅ `backend/start_server_fixed.py`
- ✅ `backend/app/main.py`
- ✅ `config.ini`
- ✅ `backend/requirements.txt`
- ✅ `README.md`

### 阶段2: 保留业务逻辑
- ⚡ `backend/app/services/auto_recovery_manager_enhanced.py`
- ⚡ `backend/app/services/data_write_manager.py`
- ⚡ `backend/app/services/auto_sync_scheduler.py`
- ⚡ `backend/app/services/database_table_manager.py`
- ⚡ `scripts/port_manager.py`
- ⚡ `scripts/migrate_purchase_order.py`

### 阶段3: 选择性保留API
- 🔧 `backend/app/api/v1/sync.py`
- 🔧 `backend/app/api/v1/database.py`
- 🔧 `backend/app/api/v1/auth.py`
- 🔧 `backend/app/core/database.py`
- 🔧 `backend/app/core/config.py`

## 🗑️ 删除计划

### 高优先级删除
- 🔴 `core/`
- 🔴 `frontend/`
- 🔴 `tests/`
- 🔴 `tools/`
- 🔴 `**/test_*.py`
- 🔴 `**/temp_*.py`
- 🔴 `**/backup*.py`
- 🔴 `**/copy*.py`
- 🔴 `**/old*.py`

## 🏗️ 目标项目结构

```
YS-API-V3/
├── root
│   ├── config.ini
│   ├── README.md
│   ├── start_production.py
├── backend/
│   ├── requirements.txt
│   ├── start_server.py
│   ├── app/main.py
│   ├── app/core/database.py
│   ├── app/core/config.py
│   ├── app/services/data_manager.py
│   ├── app/services/sync_manager.py
│   ├── app/api/v1/main_api.py
├── config/
│   ├── modules.json
│   ├── monitoring_config.json
├── scripts/
│   ├── port_manager.py
│   ├── production_deploy.py
```

## 🚀 执行命令

```bash
git checkout -b refactoring-backup
git add .
git commit -m 'Pre-refactoring backup'
rm -rf core/
rm -rf frontend/
rm -rf tests/
rm -rf tools/
rm -rf logs/
rm -rf 'md文档/'
rm -rf excel/
find . -name '*test*.py' -delete
find . -name '*temp*.py' -delete
find . -name '*backup*.py' -delete
find . -name '*copy*.py' -delete
find . -name '*old*.py' -delete
mkdir -p backend/app/core
mkdir -p backend/app/services
mkdir -p backend/app/api/v1
# 手动合并 data_write_manager.py 和相关服务到 data_manager.py
# 手动合并 auto_sync_scheduler.py 到 sync_manager.py
# 手动合并所有API端点到 main_api.py
python backend/start_server.py
# 验证核心功能是否正常
```

## ⚠️ 风险提示

1. **备份重要**: 执行前确保Git备份
2. **测试验证**: 每个阶段后测试核心功能
3. **渐进执行**: 不要一次性删除所有文件
4. **依赖检查**: 确认删除文件没有被其他地方引用

## 📈 预期收益

- **文件数量**: 从411个减少到约25个 (减少94%)
- **代码质量**: 消除重复，提高可维护性
- **性能提升**: 减少启动时间和内存占用
- **开发效率**: 清晰的项目结构，便于后续开发
