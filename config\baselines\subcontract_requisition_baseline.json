{"module_name": "subcontract_requisition", "display_name": "委外申请", "version": "2.0.0", "source": "json_parser", "total_fields": 69, "created_at": "2025-07-28T20:12:24.837976", "last_updated": "2025-07-28T20:12:24.837976", "fields": {"code": {"api_field_name": "code", "chinese_name": "委外申请单号", "data_type": "NVARCHAR(500)", "param_desc": "委外申请单号", "path": "data.recordList.code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "message": {"api_field_name": "message", "chinese_name": "接口返回信息", "data_type": "NVARCHAR(500)", "param_desc": "接口返回信息", "path": "message", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "接口返回数据", "data_type": "NVARCHAR(MAX)", "param_desc": "接口返回数据", "path": "data", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageIndex": {"api_field_name": "pageIndex", "chinese_name": "页号 默认值:1", "data_type": "BIGINT", "param_desc": "页号 默认值:1", "path": "pageIndex", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageSize": {"api_field_name": "pageSize", "chinese_name": "每页行数 默认值:10", "data_type": "BIGINT", "param_desc": "每页行数 默认值:10", "path": "pageSize", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordCount": {"api_field_name": "recordCount", "chinese_name": "记录总数", "data_type": "BIGINT", "param_desc": "记录总数", "path": "data.recordCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordList": {"api_field_name": "recordList", "chinese_name": "返回数据对象", "data_type": "NVARCHAR(MAX)", "param_desc": "返回数据对象", "path": "data.recordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "id": {"api_field_name": "id", "chinese_name": "主键", "data_type": "NVARCHAR(500)", "param_desc": "主键", "path": "data.recordList.subcontractRequisitionProduct.id", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orgId": {"api_field_name": "orgId", "chinese_name": "组织ID", "data_type": "NVARCHAR(500)", "param_desc": "组织ID", "path": "orgId", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orgName": {"api_field_name": "orgName", "chinese_name": "需求组织", "data_type": "NVARCHAR(500)", "param_desc": "需求组织", "path": "data.recordList.orgName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vouchdate": {"api_field_name": "vouchdate", "chinese_name": "单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "data_type": "NVARCHAR(500)", "param_desc": "单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "path": "vouchdate", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "status": {"api_field_name": "status", "chinese_name": "申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。", "data_type": "NVARCHAR(500)", "param_desc": "申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。", "path": "status", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "transTypeId": {"api_field_name": "transTypeId", "chinese_name": "交易类型ID", "data_type": "NVARCHAR(500)", "param_desc": "交易类型ID", "path": "transTypeId", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "transTypeName": {"api_field_name": "transTypeName", "chinese_name": "交易类型", "data_type": "NVARCHAR(500)", "param_desc": "交易类型", "path": "data.recordList.transTypeName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "transTypeExtendAttrsJson": {"api_field_name": "transTypeExtendAttrsJson", "chinese_name": "交易类型扩展属性", "data_type": "NVARCHAR(500)", "param_desc": "交易类型扩展属性", "path": "data.recordList.transTypeExtendAttrsJson", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "departmentId": {"api_field_name": "departmentId", "chinese_name": "需求部门ID", "data_type": "NVARCHAR(500)", "param_desc": "需求部门ID", "path": "departmentId", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "departmentName": {"api_field_name": "departmentName", "chinese_name": "需求部门", "data_type": "NVARCHAR(500)", "param_desc": "需求部门", "path": "data.recordList.departmentName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "operatorId": {"api_field_name": "operatorId", "chinese_name": "需求人ID", "data_type": "NVARCHAR(500)", "param_desc": "需求人ID", "path": "operatorId", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operatorName": {"api_field_name": "operatorName", "chinese_name": "需求人名称", "data_type": "NVARCHAR(500)", "param_desc": "需求人名称", "path": "data.recordList.operatorName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "sourceType": {"api_field_name": "sourceType", "chinese_name": "来源类别。0：手工添加，1：计划订单。", "data_type": "NVARCHAR(500)", "param_desc": "来源类别。0：手工添加，1：计划订单。", "path": "data.recordList.sourceType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isWfControlled": {"api_field_name": "isWfControlled", "chinese_name": "是否审批流控制：false-否，true-是。", "data_type": "BIT", "param_desc": "是否审批流控制：false-否，true-是。", "path": "data.recordList.isWfControlled", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "verifystate": {"api_field_name": "verifystate", "chinese_name": "审批状态：0-开立，1-已提交，2-已审批，-1-驳回", "data_type": "BIGINT", "param_desc": "审批状态：0-开立，1-已提交，2-已审批，-1-驳回", "path": "data.recordList.verifystate", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "pubts": {"api_field_name": "pubts", "chinese_name": "时间戳", "data_type": "NVARCHAR(500)", "param_desc": "时间戳", "path": "data.recordList.pubts", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "subcontractRequisitionProduct": {"api_field_name": "subcontractRequisitionProduct", "chinese_name": "行信息", "data_type": "NVARCHAR(MAX)", "param_desc": "行信息", "path": "data.recordList.subcontractRequisitionProduct", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "materialId": {"api_field_name": "materialId", "chinese_name": "制造物料ID", "data_type": "BIGINT", "param_desc": "制造物料ID", "path": "materialId", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productId": {"api_field_name": "productId", "chinese_name": "物料ID", "data_type": "BIGINT", "param_desc": "物料ID", "path": "productId", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "outsourceOrgId": {"api_field_name": "outsourceOrgId", "chinese_name": "委外组织ID", "data_type": "NVARCHAR(500)", "param_desc": "委外组织ID", "path": "data.recordList.subcontractRequisitionProduct.outsourceOrgId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "rcvOrgId": {"api_field_name": "rcvOrgId", "chinese_name": "收货组织ID", "data_type": "NVARCHAR(500)", "param_desc": "收货组织ID", "path": "data.recordList.subcontractRequisitionProduct.rcvOrgId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productCode": {"api_field_name": "productCode", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "data.recordList.subcontractRequisitionProduct.productCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productName": {"api_field_name": "productName", "chinese_name": "物料名称", "data_type": "NVARCHAR(500)", "param_desc": "物料名称", "path": "data.recordList.subcontractRequisitionProduct.productName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "demandQuantityDU": {"api_field_name": "demandQuantityDU", "chinese_name": "需求件数", "data_type": "BIGINT", "param_desc": "需求件数", "path": "data.recordList.subcontractRequisitionProduct.demandQuantityDU", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "demandUnitId": {"api_field_name": "demandUnitId", "chinese_name": "需求单位ID", "data_type": "BIGINT", "param_desc": "需求单位ID", "path": "data.recordList.subcontractRequisitionProduct.demandUnitId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "demandUnitName": {"api_field_name": "demandUnitName", "chinese_name": "需求单位", "data_type": "NVARCHAR(500)", "param_desc": "需求单位", "path": "data.recordList.subcontractRequisitionProduct.demandUnitName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "demandUnitTruncationType": {"api_field_name": "demandUnitTruncationType", "chinese_name": "需求单位舍位", "data_type": "BIGINT", "param_desc": "需求单位舍位", "path": "data.recordList.subcontractRequisitionProduct.demandUnitTruncationType", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "demandUnitPrecision": {"api_field_name": "demandUnitPrecision", "chinese_name": "需求单位精度", "data_type": "BIGINT", "param_desc": "需求单位精度", "path": "data.recordList.subcontractRequisitionProduct.demandUnitPrecision", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "demandQuantityMU": {"api_field_name": "demandQuantityMU", "chinese_name": "需求数量", "data_type": "BIGINT", "param_desc": "需求数量", "path": "data.recordList.subcontractRequisitionProduct.demandQuantityMU", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "mainUnitId": {"api_field_name": "mainUnitId", "chinese_name": "需求主单位ID", "data_type": "BIGINT", "param_desc": "需求主单位ID", "path": "data.recordList.subcontractRequisitionProduct.mainUnitId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "mainUnitName": {"api_field_name": "mainUnitName", "chinese_name": "需求主计量单位", "data_type": "NVARCHAR(500)", "param_desc": "需求主计量单位", "path": "data.recordList.subcontractRequisitionProduct.mainUnitName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "mainUnitTruncationType": {"api_field_name": "mainUnitTruncationType", "chinese_name": "需求主计量单位舍位", "data_type": "BIGINT", "param_desc": "需求主计量单位舍位", "path": "data.recordList.subcontractRequisitionProduct.mainUnitTruncationType", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "mainUnitPrecision": {"api_field_name": "mainUnitPrecision", "chinese_name": "需求主计量单位精度", "data_type": "BIGINT", "param_desc": "需求主计量单位精度", "path": "data.recordList.subcontractRequisitionProduct.mainUnitPrecision", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "requisitionDate": {"api_field_name": "requisitionDate", "chinese_name": "需求日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "data_type": "NVARCHAR(500)", "param_desc": "需求日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "path": "requisitionDate", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "rcvOrgName": {"api_field_name": "rcvOrgName", "chinese_name": "收货组织", "data_type": "NVARCHAR(500)", "param_desc": "收货组织", "path": "data.recordList.subcontractRequisitionProduct.rcvOrgName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "outsourceOrgName": {"api_field_name": "outsourceOrgName", "chinese_name": "委外组织", "data_type": "NVARCHAR(500)", "param_desc": "委外组织", "path": "data.recordList.subcontractRequisitionProduct.outsourceOrgName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "sumRecordList": {"api_field_name": "sumRecordList", "chinese_name": "合计字段集合", "data_type": "NVARCHAR(MAX)", "param_desc": "合计字段集合", "path": "data.sumRecordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageCount": {"api_field_name": "pageCount", "chinese_name": "总页数", "data_type": "BIGINT", "param_desc": "总页数", "path": "data.pageCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "beginPageIndex": {"api_field_name": "beginPageIndex", "chinese_name": "开始页码", "data_type": "BIGINT", "param_desc": "开始页码", "path": "data.beginPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "endPageIndex": {"api_field_name": "endPageIndex", "chinese_name": "结束页码", "data_type": "BIGINT", "param_desc": "结束页码", "path": "data.endPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "simple": {"api_field_name": "simple", "chinese_name": "扩展参数", "data_type": "NVARCHAR(MAX)", "param_desc": "扩展参数", "path": "simple", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "open_pubts_begin": {"api_field_name": "open_pubts_begin", "chinese_name": "时间戳，开始时间", "data_type": "DATE", "param_desc": "时间戳，开始时间", "path": "simple.open_pubts_begin", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_pubts_end": {"api_field_name": "open_pubts_end", "chinese_name": "时间戳，结束时间", "data_type": "DATE", "param_desc": "时间戳，结束时间", "path": "simple.open_pubts_end", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "simpleVOs": {"api_field_name": "simpleVOs", "chinese_name": "扩展查询条件", "data_type": "NVARCHAR(MAX)", "param_desc": "扩展查询条件", "path": "simpleVOs", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "field": {"api_field_name": "field", "chinese_name": "属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts", "data_type": "NVARCHAR(500)", "param_desc": "属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts", "path": "simpleVOs.conditions.field", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "op": {"api_field_name": "op", "chinese_name": "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or", "data_type": "NVARCHAR(500)", "param_desc": "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or", "path": "simpleVOs.conditions.op", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value1": {"api_field_name": "value1", "chinese_name": "查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)", "data_type": "NVARCHAR(500)", "param_desc": "查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)", "path": "simpleVOs.conditions.value1", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value2": {"api_field_name": "value2", "chinese_name": "查询条件值2", "data_type": "NVARCHAR(500)", "param_desc": "查询条件值2", "path": "simpleVOs.conditions.value2", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "logicOp": {"api_field_name": "logicOp", "chinese_name": "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or", "data_type": "NVARCHAR(500)", "param_desc": "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or", "path": "simpleVOs.logicOp", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "conditions": {"api_field_name": "conditions", "chinese_name": "下级查询条件", "data_type": "NVARCHAR(MAX)", "param_desc": "下级查询条件", "path": "simpleVOs.conditions", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}}}