/**
 * 错误处理和恢复机制系统样式
 * 包括错误提示、用户引导、恢复操作等UI组件的样式
 */

/* 错误引导模态框 */
.error-guidance-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.error-guidance-modal.show {
  opacity: 1;
  visibility: visible;
}

.error-guidance-modal.hide {
  opacity: 0;
  visibility: hidden;
}

.error-guidance-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.error-guidance-content {
  position: relative;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  transition: transform 0.3s ease;
}

.error-guidance-modal.show .error-guidance-content {
  transform: scale(1) translateY(0);
}

.error-guidance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  border-bottom: 1px solid #e1e8ed;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
}

.error-guidance-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.error-icon {
  font-size: 24px;
}

.error-guidance-close {
  background: none;
  border: none;
  color: white;
  font-size: 28px;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.error-guidance-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.error-guidance-body {
  padding: 32px;
  max-height: 60vh;
  overflow-y: auto;
}

.error-details {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border-left: 4px solid #ff6b6b;
}

.error-details p {
  margin: 8px 0;
  font-size: 14px;
  color: #5a6c7d;
}

.error-details strong {
  color: #2c3e50;
  font-weight: 600;
}

.error-guidance-section {
  margin-bottom: 24px;
}

.error-guidance-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.error-guidance-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.error-guidance-list li {
  padding: 8px 0;
  padding-left: 24px;
  position: relative;
  color: #5a6c7d;
  line-height: 1.5;
}

.error-guidance-list li::before {
  content: '💡';
  position: absolute;
  left: 0;
  top: 8px;
}

.error-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e1e8ed;
}

.error-action-btn {
  padding: 12px 24px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  background: white;
  color: #5a6c7d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
}

.error-action-btn:hover {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-1px);
}

.error-action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
}

.error-action-btn.primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 备用错误显示 */
.fallback-error {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  background: #ff6b6b;
  color: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
  max-width: 400px;
  animation: slideInRight 0.3s ease;
}

.fallback-error-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.fallback-error-content p {
  margin: 0 0 12px 0;
  font-size: 14px;
  opacity: 0.9;
}

.fallback-error-content button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.fallback-error-content button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 关键错误显示 */
.critical-error {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10001;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.critical-error-content {
  background: #dc3545;
  color: white;
  padding: 40px;
  border-radius: 16px;
  text-align: center;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 60px rgba(220, 53, 69, 0.5);
}

.critical-error-content h3 {
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 700;
}

.critical-error-content p {
  margin: 0 0 24px 0;
  font-size: 16px;
  line-height: 1.5;
  opacity: 0.9;
}

.critical-error-content button {
  background: white;
  color: #dc3545;
  border: none;
  padding: 16px 32px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.critical-error-content button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

/* 错误状态指示器 */
.error-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.error-indicator.network-error {
  background: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.error-indicator.api-error {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.error-indicator.validation-error {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border: 1px solid rgba(102, 126, 234, 0.3);
}

.error-indicator.permission-error {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.3);
}

/* 错误恢复状态 */
.recovery-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  margin: 16px 0;
  transition: all 0.3s ease;
}

.recovery-status.attempting {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.recovery-status.success {
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.recovery-status.failed {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.recovery-icon {
  font-size: 20px;
}

.recovery-message {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.recovery-status.attempting .recovery-message {
  color: #856404;
}

.recovery-status.success .recovery-message {
  color: #155724;
}

.recovery-status.failed .recovery-message {
  color: #721c24;
}

/* 重试按钮 */
.retry-button {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.retry-button:hover {
  background: linear-gradient(135deg, #e0a800 0%, #e68900 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.retry-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.retry-button .retry-icon {
  animation: spin 1s linear infinite;
}

.retry-button:not(.retrying) .retry-icon {
  animation: none;
}

/* 错误日志查看器 */
.error-log-viewer {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 20px 0;
}

.error-log-header {
  background: #f8f9fa;
  padding: 16px 20px;
  border-bottom: 1px solid #e1e8ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.error-log-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.error-log-controls {
  display: flex;
  gap: 8px;
}

.error-log-btn {
  padding: 6px 12px;
  border: 1px solid #e1e8ed;
  border-radius: 6px;
  background: white;
  color: #5a6c7d;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.error-log-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.error-log-content {
  max-height: 400px;
  overflow-y: auto;
}

.error-log-entry {
  padding: 12px 20px;
  border-bottom: 1px solid #f8f9fa;
  transition: background-color 0.2s ease;
}

.error-log-entry:hover {
  background: #f8f9fa;
}

.error-log-entry:last-child {
  border-bottom: none;
}

.error-log-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.error-log-timestamp {
  font-size: 12px;
  color: #7f8c8d;
  font-family: 'Courier New', monospace;
}

.error-log-type {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.error-log-severity {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.error-log-severity.critical {
  background: #dc3545;
  color: white;
}

.error-log-severity.high {
  background: #fd7e14;
  color: white;
}

.error-log-severity.medium {
  background: #ffc107;
  color: #212529;
}

.error-log-severity.low {
  background: #28a745;
  color: white;
}

.error-log-message {
  font-size: 14px;
  color: #2c3e50;
  margin-bottom: 4px;
}

.error-log-details {
  font-size: 12px;
  color: #7f8c8d;
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
  word-break: break-all;
}

/* 错误统计面板 */
.error-stats-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin: 20px 0;
}

.error-stats-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
}

.error-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.error-stat-item {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

.error-stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
  display: block;
  margin-bottom: 4px;
}

.error-stat-label {
  font-size: 14px;
  color: #7f8c8d;
}

/* 动画效果 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-guidance-content {
    width: 95%;
    margin: 20px;
  }
  
  .error-guidance-header {
    padding: 20px;
  }
  
  .error-guidance-body {
    padding: 20px;
  }
  
  .error-actions {
    flex-direction: column;
  }
  
  .error-action-btn {
    width: 100%;
  }
  
  .error-stats-grid {
    grid-template-columns: 1fr;
  }
  
  .fallback-error {
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .critical-error-content {
    padding: 30px 20px;
  }
}

/* 无障碍访问支持 */
@media (prefers-reduced-motion: reduce) {
  .error-guidance-modal,
  .error-guidance-content,
  .fallback-error,
  .critical-error,
  .recovery-status,
  .retry-button,
  .error-log-entry {
    animation: none;
    transition: none;
  }
  
  .retry-button .retry-icon {
    animation: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .error-guidance-content {
    border: 2px solid #000;
  }
  
  .error-action-btn {
    border-width: 2px;
  }
  
  .error-indicator {
    border-width: 2px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .error-guidance-content {
    background: #2c3e50;
    color: white;
  }
  
  .error-guidance-header {
    border-bottom-color: #34495e;
  }
  
  .error-details {
    background: #34495e;
    border-left-color: #ff6b6b;
  }
  
  .error-details p {
    color: #bdc3c7;
  }
  
  .error-details strong {
    color: white;
  }
  
  .error-guidance-list li {
    color: #bdc3c7;
  }
  
  .error-actions {
    border-top-color: #34495e;
  }
  
  .error-action-btn {
    background: #34495e;
    border-color: #4a5f7a;
    color: #bdc3c7;
  }
  
  .error-action-btn:hover {
    border-color: #667eea;
    color: #667eea;
  }
  
  .error-log-viewer {
    background: #2c3e50;
  }
  
  .error-log-header {
    background: #34495e;
    border-bottom-color: #4a5f7a;
  }
  
  .error-log-title {
    color: white;
  }
  
  .error-log-btn {
    background: #34495e;
    border-color: #4a5f7a;
    color: #bdc3c7;
  }
  
  .error-log-entry {
    border-bottom-color: #34495e;
  }
  
  .error-log-entry:hover {
    background: #34495e;
  }
  
  .error-log-message {
    color: white;
  }
  
  .error-log-details {
    background: #34495e;
    color: #bdc3c7;
  }
  
  .error-stats-panel {
    background: #2c3e50;
  }
  
  .error-stats-title {
    color: white;
  }
  
  .error-stat-item {
    background: #34495e;
  }
  
  .error-stat-label {
    color: #bdc3c7;
  }
}