#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 核心重构推荐器
=============================

基于代码价值评估结果，提供精准的重构建议和实施计划
"""

import json
import logging
from datetime import datetime
from pathlib import Path


class CoreRefactoringRecommender:
    """核心重构推荐器"""

    def __init__(self, workspace_root="d:\\OneDrive\\Desktop\\YS-API程序\\v3"):
        self.workspace_root = Path(workspace_root)
        self.logger = self._setup_logger()

        # 核心保留模式 - 这些是绝对不能删除的
        self.critical_patterns = [
            r"backend.*start_server\.py",
            r"backend.*main\.py",
            r"config\.ini",
            r"requirements\.txt",
            r"backend.*app.*core.*",
            r"backend.*app.*services.*",
            r"README\.md",
        ]

        # 高价值依赖文件
        self.high_value_files = [
            "scripts/port_manager.py",  # 28依赖
            "backend/start_server_fixed.py",  # 22依赖
            "backend/app/services/auto_recovery_manager_enhanced.py",  # 19依赖
            "backend/app/services/data_write_manager.py",  # 16依赖
            "scripts/migrate_purchase_order.py",  # 13依赖
            "backend/app/services/auto_sync_scheduler.py",  # 12依赖
            "backend/app/services/database_table_manager.py",  # 11依赖
            "core/app/main.py",  # 11依赖
        ]

    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger("CoreRefactoringRecommender")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def analyze_current_state(self):
        """分析当前项目状态"""
        analysis_result = {
            "total_files": 411,
            "core_files": 0,  # 0.0% - 没有高价值核心文件
            "important_files": 9,  # 2.2% - 少量重要文件
            "optional_files": 112,  # 27.3% - 可选文件
            "junk_files": 290,  # 70.6% - 大量垃圾文件
        }

        # 关键发现
        findings = [
            "🚨 **严重问题**: 没有识别出核心文件(得分≥0.7)",
            "⚠️  **依赖混乱**: 最高得分文件仅0.29分",
            "🗑️ **代码冗余**: 70.6%的文件被识别为垃圾代码",
            "🔄 **重复结构**: backend和core目录大量重复",
            "📦 **依赖分散**: 高依赖文件分布在scripts中",
        ]

        return analysis_result, findings

    def generate_core_preservation_plan(self):
        """生成核心保留计划"""

        # 阶段1: 绝对保留 - 系统基础架构
        phase1_keep = [
            "backend/start_server.py",  # 服务启动入口
            "backend/start_server_fixed.py",  # 修复版服务启动
            "backend/app/main.py",  # 应用主入口
            "config.ini",  # 配置文件
            "backend/requirements.txt",  # 依赖声明
            "README.md",  # 项目文档
        ]

        # 阶段2: 高价值保留 - 核心业务逻辑
        phase2_keep = [
            "backend/app/services/auto_recovery_manager_enhanced.py",  # 自动恢复
            "backend/app/services/data_write_manager.py",  # 数据写入
            "backend/app/services/auto_sync_scheduler.py",  # 同步调度
            "backend/app/services/database_table_manager.py",  # 数据库管理
            "scripts/port_manager.py",  # 端口管理
            "scripts/migrate_purchase_order.py",  # 采购单迁移
        ]

        # 阶段3: 选择性保留 - API接口
        phase3_keep = [
            "backend/app/api/v1/sync.py",  # 同步API
            "backend/app/api/v1/database.py",  # 数据库API
            "backend/app/api/v1/auth.py",  # 认证API
            "backend/app/core/database.py",  # 数据库核心
            "backend/app/core/config.py",  # 配置核心
        ]

        return phase1_keep, phase2_keep, phase3_keep

    def generate_deletion_plan(self):
        """生成删除计划"""

        # 优先删除 - 明显的重复和测试文件
        high_priority_delete = [
            "core/",  # 整个core目录(与backend重复)
            "frontend/",  # 前端相关(非API核心)
            "tests/",  # 测试文件
            "tools/",  # 工具脚本
            "**/test_*.py",  # 所有测试文件
            "**/temp_*.py",  # 临时文件
            "**/backup*.py",  # 备份文件
            "**/copy*.py",  # 复制文件
            "**/old*.py",  # 旧版本文件
        ]

        # 中等删除 - 分析后的低价值文件
        medium_priority_delete = [
            "logs/",  # 日志目录
            "scripts/*_test.py",  # 脚本测试
            "md文档/",  # 临时文档
            "excel/",  # Excel样例
        ]

        # 低优先级删除 - 需要审查的文件
        low_priority_delete = [
            "config/auto_sync_config*.json",  # 多余配置
            "模块字段/backup/",  # 备份字段
            "docs/",  # 文档(需要审查)
        ]

        return (
            high_priority_delete,
            medium_priority_delete,
            low_priority_delete,
        )

    def create_minimal_project_structure(self):
        """创建最小化项目结构"""

        minimal_structure = {
            "root": [
                "config.ini",
                "README.md",
                "start_production.py",  # 新的统一启动脚本
            ],
            "backend/": [
                "requirements.txt",
                "start_server.py",
                "app/main.py",
                "app/core/database.py",
                "app/core/config.py",
                "app/services/data_manager.py",  # 合并数据服务
                "app/services/sync_manager.py",  # 合并同步服务
                "app/api/v1/main_api.py",  # 合并API端点
            ],
            "config/": ["modules.json", "monitoring_config.json"],
            "scripts/": [
                "port_manager.py",  # 保留高依赖脚本
                "production_deploy.py",  # 新的部署脚本
            ],
        }

        return minimal_structure

    def generate_refactoring_commands(self):
        """生成重构命令"""

        commands = [
            # 第一步: 创建备份
            "git checkout -b refactoring-backup",
            "git add .",
            "git commit -m 'Pre-refactoring backup'",
            # 第二步: 删除重复目录
            "rm -rf core/",
            "rm -rf frontend/",
            "rm -rf tests/",
            "rm -rf tools/",
            "rm -rf logs/",
            "rm -rf 'md文档/'",
            "rm -rf excel/",
            # 第三步: 清理临时文件
            "find . -name '*test*.py' -delete",
            "find . -name '*temp*.py' -delete",
            "find . -name '*backup*.py' -delete",
            "find . -name '*copy*.py' -delete",
            "find . -name '*old*.py' -delete",
            # 第四步: 重组核心文件
            "mkdir -p backend/app/core",
            "mkdir -p backend/app/services",
            "mkdir -p backend/app/api/v1",
            # 第五步: 合并相似服务
            "# 手动合并 data_write_manager.py 和相关服务到 data_manager.py",
            "# 手动合并 auto_sync_scheduler.py 到 sync_manager.py",
            "# 手动合并所有API端点到 main_api.py",
            # 第六步: 验证和测试
            "python backend/start_server.py",
            "# 验证核心功能是否正常",
        ]

        return commands

    def save_refactoring_plan(self):
        """保存重构计划"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 获取分析数据
        current_state, findings = self.analyze_current_state()
        phase1, phase2, phase3 = self.generate_core_preservation_plan()
        high_del, med_del, low_del = self.generate_deletion_plan()
        minimal_structure = self.create_minimal_project_structure()
        commands = self.generate_refactoring_commands()

        # 生成详细计划
        plan_file = (
            self.workspace_root / f"core_refactoring_plan_{timestamp}.md"
        )

        with open(plan_file, "w", encoding="utf-8") as f:
            f.write("# YS-API V3.0 核心重构计划\n\n")
            f.write(
                f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            )

            # 当前状态分析
            f.write("## 📊 当前状态分析\n\n")
            f.write(f"- **总文件数**: {current_state['total_files']}\n")
            f.write(f"- **核心文件**: {current_state['core_files']} (0.0%)\n")
            f.write(
                f"- **垃圾文件**: {current_state['junk_files']} (70.6%)\n\n"
            )

            f.write("### 🔍 关键发现\n\n")
            for finding in findings:
                f.write(f"- {finding}\n")
            f.write("\n")

            # 重构策略
            f.write("## 🎯 重构策略\n\n")
            f.write("### 阶段1: 保留核心架构\n")
            for file in phase1:
                f.write(f"- ✅ `{file}`\n")
            f.write("\n")

            f.write("### 阶段2: 保留业务逻辑\n")
            for file in phase2:
                f.write(f"- ⚡ `{file}`\n")
            f.write("\n")

            f.write("### 阶段3: 选择性保留API\n")
            for file in phase3:
                f.write(f"- 🔧 `{file}`\n")
            f.write("\n")

            # 删除计划
            f.write("## 🗑️ 删除计划\n\n")
            f.write("### 高优先级删除\n")
            for item in high_del:
                f.write(f"- 🔴 `{item}`\n")
            f.write("\n")

            # 目标结构
            f.write("## 🏗️ 目标项目结构\n\n")
            f.write("```\n")
            f.write("YS-API-V3/\n")
            for folder, files in minimal_structure.items():
                f.write(f"├── {folder}\n")
                for file in files:
                    f.write(f"│   ├── {file}\n")
            f.write("```\n\n")

            # 执行命令
            f.write("## 🚀 执行命令\n\n")
            f.write("```bash\n")
            for cmd in commands:
                f.write(f"{cmd}\n")
            f.write("```\n\n")

            # 风险提示
            f.write("## ⚠️ 风险提示\n\n")
            f.write("1. **备份重要**: 执行前确保Git备份\n")
            f.write("2. **测试验证**: 每个阶段后测试核心功能\n")
            f.write("3. **渐进执行**: 不要一次性删除所有文件\n")
            f.write("4. **依赖检查**: 确认删除文件没有被其他地方引用\n\n")

            # 预期收益
            f.write("## 📈 预期收益\n\n")
            f.write("- **文件数量**: 从411个减少到约25个 (减少94%)\n")
            f.write("- **代码质量**: 消除重复，提高可维护性\n")
            f.write("- **性能提升**: 减少启动时间和内存占用\n")
            f.write("- **开发效率**: 清晰的项目结构，便于后续开发\n")

        return plan_file

    def run_recommendation(self):
        """运行推荐分析"""
        self.logger.info("🚀 开始生成核心重构推荐...")

        # 生成推荐计划
        plan_file = self.save_refactoring_plan()

        self.logger.info(f"✅ 核心重构计划已生成: {plan_file}")

        # 输出关键建议
        print("\n🎯 **核心重构建议**:")
        print("1. 🗑️ 立即删除 core/ 目录 (与backend完全重复)")
        print("2. 🧹 清理70.6%的垃圾文件 (测试、临时、备份文件)")
        print("3. 🔧 合并分散的服务文件到统一的服务管理器")
        print("4. 📦 保留8个高依赖核心文件作为重构基础")
        print("5. 🏗️ 重建清晰的项目结构 (目标: 25个核心文件)")

        print(f"\n📄 详细计划文档: {plan_file}")

        return plan_file


def main():
    """主函数"""
    recommender = CoreRefactoringRecommender()
    plan_file = recommender.run_recommendation()

    print(f"\n🎉 核心重构推荐完成!")
    print(f"📊 基于411个文件的价值评估")
    print(f"🎯 目标: 减少94%文件数量，保留核心功能")
    print(f"📋 执行计划: {plan_file}")


if __name__ == "__main__":
    main()
