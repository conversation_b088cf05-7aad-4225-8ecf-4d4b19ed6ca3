This XML file does not appear to have any style information associated with it. The document tree is shown below.
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>c45eecf33c024999987c391d6a0c7fed</id>
<name>生产订单列表查询</name>
<apiClassifyId>9bb3b7ec4c8a476bacad06da3b48d496</apiClassifyId>
<apiClassifyName>生产订单</apiClassifyName>
<apiClassifyCode>productionorder.po_production_order</apiClassifyCode>
<parentApiClassifies/>
<functionId/>
<openMode>0</openMode>
<description>可以根据输入的订单ID信息，查询生产订单的详细信息，包括订单、产品、材料和工艺等内容。</description>
<auth>true</auth>
<bodyPassthrough>false</bodyPassthrough>
<healthExam>false</healthExam>
<healthStatus>true</healthStatus>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/mfg/productionorder/list</completeProxyUrl>
<connectUrl>/api/list</connectUrl>
<sort>20</sort>
<handler>openapi</handler>
<httpRequestType>POST</httpRequestType>
<openApi>true</openApi>
<preset>true</preset>
<productId>4a176d6a681a4ebdbd053262493b5dff</productId>
<productCode/>
<proxyUrl>/yonbip/mfg/productionorder/list</proxyUrl>
<requestParamsDemo>Url: /yonbip/mfg/productionorder/list?access_token=访问令牌 Body: { "pageIndex": 0, "pageSize": 0, "id": [ 0 ], "code": "", "status": "", "transTypeId": [ "" ], "orgId": [ "" ], "productionDepartmentId": [ "" ], "OrderProduct!materialId": [ 0 ], "OrderProduct!productId": [ 0 ], "OrderProduct!startDate": "2021-03-02|2021-03-02 23:59:59", "OrderProduct!finishDate": "2021-03-02|2021-03-02 23:59:59", "createTime": "2021-03-02|2021-03-02 23:59:59", "vouchdate": "2021-03-02|2021-03-02 23:59:59", "isShowProcess": 0, "isShowMaterial": true, "OrderProduct!completedFlag": "", "simple": { "orderProduct.productId.code": [ "" ], "open_pubts_begin": "2022-04-01 00:00:00", "open_pubts_end": "2022-04-20 00:00:00", "orderProduct.materialApplyStatus": "0", "orderProduct.materialStatus": "1", "orderProduct.finishedWorkApplyStatus": "0", "orderProduct.stockStatus": "2", "open_auditTime_begin": "2023-02-21 11:22:51", "open_auditTime_end": "2023-02-21 11:22:55", "open_auditDate_begin": "2023-02-21", "open_auditDate_end": "2023-02-22", "orderProduct.retMaterialApplyFlag": 0 } }</requestParamsDemo>
<requestProtocol>HTTP</requestProtocol>
<serviceHttpMethod>POST</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2020-02-25 20:00:08.000</gmtCreate>
<gmtUpdate>2025-07-01 16:46:53.645</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/mfg/productionorder/list</address>
<productName/>
<productClassifyId/>
<productClassifyCode/>
<productClassifyName/>
<paramDTOS>
<paramDTOS>
<id>2303473535963627531</id>
<name>pageIndex</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>页号 默认值:1</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>1</defaultValue>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag>[pageIndex]</paramTag>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627532</id>
<name>pageSize</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>每页行数 默认值:10</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>10</defaultValue>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag>[pageSize]</paramTag>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627533</id>
<name>id</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>true</array>
<paramDesc>生产订单</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627534</id>
<name>code</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>生产订单号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627535</id>
<name>status</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627536</id>
<name>transTypeId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>true</array>
<paramDesc>交易类型</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627537</id>
<name>orgId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>true</array>
<paramDesc>工厂</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627538</id>
<name>productionDepartmentId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>true</array>
<paramDesc>部门</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627539</id>
<name>OrderProduct!materialId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>true</array>
<paramDesc>制造物料</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627540</id>
<name>OrderProduct!productId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>true</array>
<paramDesc>物料id,当物料id和物料编码同时填写时,取交集</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627541</id>
<name>OrderProduct!startDate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>开工日期（区间，格式2021-03-02|2021-03-02 23:59:59）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2021-03-02|2021-03-02 23:59:59</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627542</id>
<name>OrderProduct!finishDate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>完工日期（区间，格式2021-03-02|2021-03-02 23:59:59）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2021-03-02|2021-03-02 23:59:59</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627543</id>
<name>createTime</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>创建时间（区间，格式2021-03-02|2021-03-02 23:59:59）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2021-03-02|2021-03-02 23:59:59</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627544</id>
<name>vouchdate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2021-03-02|2021-03-02 23:59:59</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627545</id>
<name>isShowProcess</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>是否展示工序，0-不展示；1-全部工序，2-全部工序（含执行信息）；3-未执行完工序</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>0</defaultValue>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627546</id>
<name>isShowMaterial</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>是否展示材料:true-是,false-否</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>false</defaultValue>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627547</id>
<name>isShowByProduct</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>是否展示联副产品:true-是,false-否</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>false</defaultValue>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627548</id>
<name>isShowActivity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>是否展示作业:true-是,false-否</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>17</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>false</defaultValue>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627549</id>
<name>OrderProduct!completedFlag</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>启用完工报告：false-否，true-是</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>18</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627550</id>
<name>simple</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<children>
<children>
<id>2303473535963627551</id>
<name>orderProduct.productId.code</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627550</parentId>
<defParamId/>
<array>true</array>
<paramDesc>物料编码,当物料id和物料编码同时填写时,取交集</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627552</id>
<name>open_pubts_begin</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627550</parentId>
<defParamId/>
<array>false</array>
<paramDesc>时间戳，开始时间</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-04-01 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag>[lastUpdateTime]</paramTag>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627553</id>
<name>open_pubts_end</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627550</parentId>
<defParamId/>
<array>false</array>
<paramDesc>时间戳，结束时间</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-04-20 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag>[thisSyncTime]</paramTag>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627554</id>
<name>orderProduct.materialApplyStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627550</parentId>
<defParamId/>
<array>false</array>
<paramDesc>领料申请状态：0-未申领，1-部分申领，2-全部申领，3-无需申领</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627555</id>
<name>orderProduct.materialStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627550</parentId>
<defParamId/>
<array>false</array>
<paramDesc>领料状态：0-未领料，1-部分领用，2-全部领用，3-无需领料，4-超额领料</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627556</id>
<name>orderProduct.finishedWorkApplyStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627550</parentId>
<defParamId/>
<array>false</array>
<paramDesc>完工申报状态：0-未申报，1-部分申报，2-全部申报</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627557</id>
<name>orderProduct.stockStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627550</parentId>
<defParamId/>
<array>false</array>
<paramDesc>入库状态：0-未入库，1-部分入库，2-全部入库</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627558</id>
<name>open_auditTime_begin</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627550</parentId>
<defParamId/>
<array>false</array>
<paramDesc>审核时间,开始时间</paramDesc>
<paramType>DateTime</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2023-02-21 11:22:51</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627559</id>
<name>open_auditTime_end</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627550</parentId>
<defParamId/>
<array>false</array>
<paramDesc>审核时间,结束时间</paramDesc>
<paramType>DateTime</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2023-02-21 11:22:55</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627560</id>
<name>open_auditDate_begin</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627550</parentId>
<defParamId/>
<array>false</array>
<paramDesc>审核日期,开始日期</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2023-02-21</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627561</id>
<name>open_auditDate_end</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627550</parentId>
<defParamId/>
<array>false</array>
<paramDesc>审核日期,结束日期</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2023-02-22</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627562</id>
<name>orderProduct.retMaterialApplyFlag</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627550</parentId>
<defParamId/>
<array>false</array>
<paramDesc>退料申请标识，0-否，1-是</paramDesc>
<paramType>short</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>simple</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>19</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-01 16:46:54.065</gmtCreate>
<gmtUpdate>2025-07-01 16:46:54.065</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2303473535963627563</id>
<name>simpleVOs</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<children>
<children>
<id>2303473535963627564</id>
<name>field</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627563</parentId>
<defParamId/>
<array>false</array>
<paramDesc>属性名(条件)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627565</id>
<name>op</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627563</parentId>
<defParamId/>
<array>false</array>
<paramDesc>比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627566</id>
<name>value1</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627563</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627567</id>
<name>value2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627563</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627568</id>
<name>logicOp</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627563</parentId>
<defParamId/>
<array>false</array>
<paramDesc>逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>and</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627569</id>
<name>conditions</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627563</parentId>
<children>
<children>
<id>2303473535963627570</id>
<name>field</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627569</parentId>
<defParamId/>
<array>false</array>
<paramDesc>属性名(条件)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627571</id>
<name>op</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627569</parentId>
<defParamId/>
<array>false</array>
<paramDesc>逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627572</id>
<name>value1</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627569</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2303473535963627573</id>
<name>value2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627569</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>下级查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-01 16:46:54.115</gmtCreate>
<gmtUpdate>2025-07-01 16:46:54.115</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>20</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-01 16:46:54.098</gmtCreate>
<gmtUpdate>2025-07-01 16:46:54.098</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627944</id>
<name>pageIndex</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>页号 默认值:1</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageIndex</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627945</id>
<name>pageSize</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>每页行数 默认值:10</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageSize</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627946</id>
<name>id</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>生产订单</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>id</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627947</id>
<name>code</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>生产订单号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>code</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627948</id>
<name>status</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>status</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627949</id>
<name>transTypeId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>交易类型</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>transTypeId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627950</id>
<name>orgId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>工厂</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>orgId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627951</id>
<name>productionDepartmentId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>部门</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>productionDepartmentId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627952</id>
<name>OrderProduct!materialId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>制造物料</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>OrderProduct!materialId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627953</id>
<name>OrderProduct!productId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料id,当物料id和物料编码同时填写时,取交集</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>OrderProduct!productId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627954</id>
<name>OrderProduct!startDate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>开工日期（区间，格式2021-03-02|2021-03-02 23:59:59）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>OrderProduct!startDate</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627955</id>
<name>OrderProduct!finishDate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>完工日期（区间，格式2021-03-02|2021-03-02 23:59:59）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>OrderProduct!finishDate</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627956</id>
<name>createTime</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>创建时间（区间，格式2021-03-02|2021-03-02 23:59:59）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>createTime</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627957</id>
<name>vouchdate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>vouchdate</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627958</id>
<name>isShowProcess</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>是否展示工序，0-不展示；1-全部工序，2-全部工序（含执行信息）；3-未执行完工序</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>isShowProcess</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627959</id>
<name>isShowMaterial</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>是否展示材料:true-是,false-否</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>isShowMaterial</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>boolean</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627960</id>
<name>isShowByProduct</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>是否展示联副产品:true-是,false-否</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>isShowByProduct</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>boolean</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627961</id>
<name>isShowActivity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>是否展示作业:true-是,false-否</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>isShowActivity</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>boolean</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627962</id>
<name>OrderProduct!completedFlag</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>启用完工报告：false-否，true-是</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>OrderProduct!completedFlag</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627963</id>
<name>simple</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<children>
<children>
<id>2303473535963627964</id>
<name>orderProduct.productId.code</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627963</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料编码,当物料id和物料编码同时填写时,取交集</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>orderProduct.productId.code</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627965</id>
<name>open_pubts_begin</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627963</parentId>
<defParamId/>
<array>false</array>
<paramDesc>时间戳，开始时间</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_pubts_begin</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627966</id>
<name>open_pubts_end</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627963</parentId>
<defParamId/>
<array>false</array>
<paramDesc>时间戳，结束时间</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_pubts_end</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627967</id>
<name>orderProduct.materialApplyStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627963</parentId>
<defParamId/>
<array>false</array>
<paramDesc>领料申请状态：0-未申领，1-部分申领，2-全部申领，3-无需申领</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>orderProduct.materialApplyStatus</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627968</id>
<name>orderProduct.materialStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627963</parentId>
<defParamId/>
<array>false</array>
<paramDesc>领料状态：0-未领料，1-部分领用，2-全部领用，3-无需领料，4-超额领料</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>orderProduct.materialStatus</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627969</id>
<name>orderProduct.finishedWorkApplyStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627963</parentId>
<defParamId/>
<array>false</array>
<paramDesc>完工申报状态：0-未申报，1-部分申报，2-全部申报</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>orderProduct.finishedWorkApplyStatus</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627970</id>
<name>orderProduct.stockStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627963</parentId>
<defParamId/>
<array>false</array>
<paramDesc>入库状态：0-未入库，1-部分入库，2-全部入库</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>orderProduct.stockStatus</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627971</id>
<name>open_auditTime_begin</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627963</parentId>
<defParamId/>
<array>false</array>
<paramDesc>审核时间,开始时间</paramDesc>
<paramType>DateTime</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_auditTime_begin</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>DateTime</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627972</id>
<name>open_auditTime_end</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627963</parentId>
<defParamId/>
<array>false</array>
<paramDesc>审核时间,结束时间</paramDesc>
<paramType>DateTime</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_auditTime_end</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>DateTime</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627973</id>
<name>open_auditDate_begin</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627963</parentId>
<defParamId/>
<array>false</array>
<paramDesc>审核日期,开始日期</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_auditDate_begin</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>Date</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627974</id>
<name>open_auditDate_end</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627963</parentId>
<defParamId/>
<array>false</array>
<paramDesc>审核日期,结束日期</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_auditDate_end</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>Date</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627975</id>
<name>orderProduct.retMaterialApplyFlag</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627963</parentId>
<defParamId/>
<array>false</array>
<paramDesc>退料申请标识，0-否，1-是</paramDesc>
<paramType>short</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>orderProduct.retMaterialApplyFlag</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>short</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>simple</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>simple</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-07-01 16:46:54.193</gmtCreate>
<gmtUpdate>2025-07-01 16:46:54.193</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2303473535963627976</id>
<name>simpleVOs</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<children>
<children>
<id>2303473535963627977</id>
<name>field</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627976</parentId>
<defParamId/>
<array>false</array>
<paramDesc>属性名(条件)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627978</id>
<name>op</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627976</parentId>
<defParamId/>
<array>false</array>
<paramDesc>比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>op</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627979</id>
<name>value1</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627976</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value1</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627980</id>
<name>value2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627976</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value2</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627981</id>
<name>logicOp</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627976</parentId>
<defParamId/>
<array>false</array>
<paramDesc>逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>logicOp</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627982</id>
<name>conditions</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627976</parentId>
<children>
<children>
<id>2303473535963627983</id>
<name>field</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627982</parentId>
<defParamId/>
<array>false</array>
<paramDesc>属性名(条件)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627984</id>
<name>op</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627982</parentId>
<defParamId/>
<array>false</array>
<paramDesc>逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>op</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627985</id>
<name>value1</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627982</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value1</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627986</id>
<name>value2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627982</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value2</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>下级查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>conditions</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-07-01 16:46:54.237</gmtCreate>
<gmtUpdate>2025-07-01 16:46:54.237</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>simpleVOs</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-07-01 16:46:54.222</gmtCreate>
<gmtUpdate>2025-07-01 16:46:54.222</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>2303473535963627574</id>
<name>code</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>返回码，成功时返回200</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>200</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2303473535963627575</id>
<name>message</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>调用失败时的错误信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>操作成功</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2303473535963627576</id>
<name>data</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId/>
<children>
<children>
<id>2303473535963627577</id>
<name>pageIndex</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627576</parentId>
<defParamId/>
<array>false</array>
<paramDesc>当前页</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627578</id>
<name>pageSize</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627576</parentId>
<defParamId/>
<array>false</array>
<paramDesc>页大小</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627579</id>
<name>recordCount</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627576</parentId>
<defParamId/>
<array>false</array>
<paramDesc>记录总数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627580</id>
<name>recordList</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627576</parentId>
<children>
<children>
<id>2303473535963627581</id>
<name>OrderProduct_materialName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>自行车</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627582</id>
<name>OrderProduct_startDate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>开工日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-24 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627586</id>
<name>OrderProduct_lineNo</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>行号</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627587</id>
<name>productUnitPrecision</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>3</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627588</id>
<name>OrderProduct_scrap</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>废品率%</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627589</id>
<name>OrderProduct_orgId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627590</id>
<name>OrderProduct_skuCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料SKU编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>jq01000001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627591</id>
<name>OrderProduct_materialId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>制造物料Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627592</id>
<name>transTypeId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>交易类型ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627593</id>
<name>mainUnitPrecision</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>3</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627594</id>
<name>id</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产订单Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627595</id>
<name>OrderProduct_sourceType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>来源单据类型：1-无来源，2-计划订单，3-销售订单，4-生产订单，5-完工报告，18-项目物资清单</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627596</id>
<name>OrderProduct_productId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627597</id>
<name>OrderProduct_mrpQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>净算量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627598</id>
<name>OrderProduct_changeType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>换算方式：0-固定换算，1-浮动换算</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627599</id>
<name>departmentName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产部门</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>生产部</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627600</id>
<name>orgName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工厂</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>L工厂1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627601</id>
<name>auditTime</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>审核时间</paramDesc>
<paramType>DateTime</paramType>
<requestParamType/>
<path/>
<example>2023-02-21 11:22:55</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627602</id>
<name>auditDate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>审核日期</paramDesc>
<paramType>Date</paramType>
<requestParamType/>
<path/>
<example>2023-02-22</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627603</id>
<name>isWfControlled</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>是否审批流控制：false-否，true-是</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627604</id>
<name>OrderProduct_quantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627605</id>
<name>OrderProduct_completedQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>已完工数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1231</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627606</id>
<name>OrderProduct_incomingQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计入库数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627607</id>
<name>isHold</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>挂起状态：false-否，true-是</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627608</id>
<name>OrderProduct_skuName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料SKU名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>自行车</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627609</id>
<name>routingVersion</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工艺路线版本</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1.0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627610</id>
<name>routingCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工艺路线编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>dfasdaf</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627611</id>
<name>routingId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工艺路线Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627612</id>
<name>OrderProduct_completedFlag</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>启用完工报告：false-否，true-是</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627613</id>
<name>OrderProduct_materialCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>jq01000001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627614</id>
<name>OrderProduct_productionUnitId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627615</id>
<name>status</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627616</id>
<name>returncount</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>退回次数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627617</id>
<name>routingName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工艺路线名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>工艺</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627618</id>
<name>verifystate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>审批状态：0-开立，1-已提交，2-已审批，-1-驳回</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627619</id>
<name>code</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产订单号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>SCDD20210324000003</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627620</id>
<name>creatorId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>创建人Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627621</id>
<name>orderProduct_id</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>订单产品行Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627622</id>
<name>orgId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工厂Id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627623</id>
<name>vouchdate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>单据日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-24 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627624</id>
<name>OrderProduct_auxiliaryQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产件数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627625</id>
<name>OrderProduct_materialApplyFlag</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>启用领料申请：false-否，true-是</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627626</id>
<name>OrderProduct_mainUnit</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627627</id>
<name>OrderProduct_mainUnitTruncationType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量舍位方式: 0-入位，1-舍位，4-四舍五入</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627628</id>
<name>transTypeName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>交易类型名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>标准生产</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627629</id>
<name>pubts</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-24 11:40:13</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627630</id>
<name>OrderProduct_skuId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料SKUId</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627631</id>
<name>OrderProduct_productUnitTruncationType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位舍位方式: 0-入位，1-舍位，4-四舍五入</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627632</id>
<name>entrustProcessType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>受托加工方式:2-全程受托加工方式;3-工序受托加工方式</paramDesc>
<paramType>short</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627633</id>
<name>OrderProduct_retMaterialApplyFlag</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>启用退料申请：0-否,1-是</paramDesc>
<paramType>short</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627634</id>
<name>OrderProduct_orgName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>L工厂1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627635</id>
<name>creator</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>创建人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>***********</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627636</id>
<name>OrderProduct_finishDate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>完工日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-26 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627637</id>
<name>OrderProduct_changeRate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>换算率</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627638</id>
<name>OrderProduct_isHold</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>挂起状态：false-否，true-是</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627639</id>
<name>entrustCustomer</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>受托客户id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>********</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627640</id>
<name>OrderProduct_versionCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>BOM版本</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>A1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627641</id>
<name>OrderProduct_bomId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料清单Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627642</id>
<name>OrderProduct_productUnitName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>件</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627643</id>
<name>OrderProduct_mainUnitName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>件</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>62</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627644</id>
<name>OrderProduct_materialApplyStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>领料申请状态：0-未申领，1-部分申领，2-全部申领</paramDesc>
<paramType>short</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>63</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627645</id>
<name>OrderProduct_materialStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>领料状态：0-未领料，1-部分领用，2-全部领用</paramDesc>
<paramType>short</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>64</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627646</id>
<name>entrustCustomerName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>受托客户</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>受托客户</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>65</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627647</id>
<name>OrderProduct_finishedWorkApplyStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>完工申报状态：0-未申报，1-部分申报，2-全部申报</paramDesc>
<paramType>short</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>66</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627648</id>
<name>OrderProduct_stockStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>入库状态：0-未入库，1-部分入库，2-全部入库</paramDesc>
<paramType>short</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>67</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627649</id>
<name>createTime</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>创建时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-24 11:40:12</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>68</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627650</id>
<name>productionDepartmentId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产部门Id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1870534089855232</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>69</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627651</id>
<name>offChartReceiptIsAllowed</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>允许表外产出:false-否，true-是</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>70</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627652</id>
<name>apsLock</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>排程状态:0-未锁定,1-已锁定</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>71</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627653</id>
<name>dailyschQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>排产数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>72</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627654</id>
<name>dailyschStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>排产状态：0-未排产，1-部分排产，2-已排产</paramDesc>
<paramType>short</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>73</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627655</id>
<name>dailyschConquantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>排产确认数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>74</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627656</id>
<name>transTypeCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>交易类型编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>PO-001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>75</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627657</id>
<name>orderMaterial</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<children>
<children>
<id>2303473535963627660</id>
<name>isWholeSet</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>齐套标识：false-否，true-是</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627661</id>
<name>receivedQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>已领数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>40</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627662</id>
<name>recipientQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>应领数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>40</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627663</id>
<name>numeratorQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>分子用量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627664</id>
<name>stockUnitPrecision</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>7</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627665</id>
<name>mainUnitTruncationType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量舍位方式：0-入位，1-舍位，4-四舍五入</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627666</id>
<name>stockUnitName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>个 </example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627667</id>
<name>unitUseQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>单位使用数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>0.33333333</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627668</id>
<name>auxiliaryReceivedQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>已领件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>40</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627669</id>
<name>auxiliaryRecipientQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>应领件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>40</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627670</id>
<name>orgId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1870887948554496</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627671</id>
<name>skuName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料SKU名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>车轮</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627672</id>
<name>stockUnitTruncationType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存单位舍位方式：0-入位，1-舍位，4-四舍五入</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627673</id>
<name>scrap</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>废品率%</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627674</id>
<name>lineNo</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>行号</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627675</id>
<name>supplyType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>发料方式：0-领用，1-入库倒冲，2-不发料</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627676</id>
<name>mainUnitPrecision</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量精度</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>7</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627677</id>
<name>truncUp</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>向上取整：0-否，1-是</paramDesc>
<paramType>short</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627678</id>
<name>substituteFlag</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>BOM替代标识</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627679</id>
<name>id</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>订单材料Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2184924571914498</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627680</id>
<name>changeRate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>换算率</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627681</id>
<name>pubts</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-24 11:40:13</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627682</id>
<name>skuId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料SKUId</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2062992424030464</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627683</id>
<name>denominatorQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>分母用量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>3</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627684</id>
<name>bomId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料清单Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627685</id>
<name>mainUnit</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1986620623900928</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627686</id>
<name>fixedQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>固定用量：0-否，1-是</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627687</id>
<name>orgName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>qing-gc001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627688</id>
<name>productName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627689</id>
<name>productCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627690</id>
<name>productId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2062992410120448</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627691</id>
<name>changeType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>换算方式：0-固定换算，1-浮动换算</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627692</id>
<name>materialCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>jq01000003</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627693</id>
<name>orderProductId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>订单成产品Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627694</id>
<name>materialId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>制造物料Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2062992427503872</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627695</id>
<name>bomMaterialId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料清单子件Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2173985857769729</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627696</id>
<name>mainUnitName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>个 </example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627697</id>
<name>materialName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>车轮</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627698</id>
<name>requirementDate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>需求日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-24 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627699</id>
<name>skuCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料SKU编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>jq01000003</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627700</id>
<name>stockUnitId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存单位Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1986620623900928</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627701</id>
<name>mustLossQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>固定损耗</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627702</id>
<name>calcCostFlag</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计算成本；0-否，1-是</paramDesc>
<paramType>short</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627703</id>
<name>orderMaterialExpinfo!id</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>材料信息扩展信息id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1471560962252734505</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627704</id>
<name>excessAppliedQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>超额申请数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>25</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627705</id>
<name>auxiliaryExcessAppliedQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>超额申请件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>25</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627706</id>
<name>excessRecipientQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>已超领数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>25</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627707</id>
<name>auxiliaryExcessRecipientQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>已超领件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>25</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627708</id>
<name>orderMaterialExpinfo!excessQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>可超额数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>25</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627709</id>
<name>orderMaterialExpinfo!auxiliaryExcessQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>可超额件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>25</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627710</id>
<name>orderMaterialExpinfo!isExcess</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>超额标识</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627711</id>
<name>orderMaterialExpinfo!excessType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>超额类型：1 比例，2 数量，3 不控制</paramDesc>
<paramType>short</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627712</id>
<name>orderMaterialExpinfo!excessRate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>超额比例(%)</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>50</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627713</id>
<name>orderMaterialExpinfo!fixedExcessQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>固定超额量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>25</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627714</id>
<name>orderMaterialExpinfo!designator</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>位置号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>EW32421FDS3232S</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627715</id>
<name>orderMaterialExpinfo!wholePoint</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>齐套检查点：1 订单完工，2订单入库，3订单开工，4工序完工</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627716</id>
<name>appliedRetQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>退料申请数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627717</id>
<name>auxiliaryAppliedRetQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>退料申请件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627718</id>
<name>appliedRetRestQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>退料申请未退库数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627719</id>
<name>auxiliaryAppliedRetRestQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>退料申请未退库件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627720</id>
<name>excessAppliedRetQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>超额退料申请数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>62</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627721</id>
<name>auxiliaryExcessAppliedRetQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>超额退料申请件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>63</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627722</id>
<name>excessAppliedRetRestQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>超额退料申请未退库数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>64</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627723</id>
<name>auxiliaryExcessAppliedRetRestQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>超额退料申请未退库件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>65</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627724</id>
<name>appliedRestQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>领料申请未出库数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>66</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627725</id>
<name>auxiliaryAppliedRestQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>领料申请未出库件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>67</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627726</id>
<name>excessAppliedRestQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>超额领料申请未出库数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>68</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627727</id>
<name>auxiliaryExcessAppliedRestQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>超额领料申请未出库件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>69</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627728</id>
<name>projectId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>项目Id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>70</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627729</id>
<name>projectCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>项目编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>71</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627730</id>
<name>projectName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>项目名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>72</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627731</id>
<name>wbs</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>wbs</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>73</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627732</id>
<name>wbsCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>WBS任务编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>74</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627733</id>
<name>wbsName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>WBS任务名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>75</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627734</id>
<name>activity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>活动</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>76</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627735</id>
<name>activityCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>活动编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>77</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627736</id>
<name>activityName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>活动名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>78</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627737</id>
<name>cfmReceivedQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认已领数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>79</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627738</id>
<name>cfmAuxReceivedQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认已领件数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>80</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627739</id>
<name>cfmExcessRecipientQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认已超领数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>81</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627740</id>
<name>cfmExcessAuxQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认已超领件数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>82</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627741</id>
<name>cfmReceivedKit</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627657</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认领料套数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>83</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>材料信息</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>76</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-01 16:46:54.365</gmtCreate>
<gmtUpdate>2025-07-01 16:46:54.365</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627742</id>
<name>orderActivity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<children>
<children>
<id>2303473535963627743</id>
<name>id</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>作业ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627744</id>
<name>lineNum</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>行号</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627745</id>
<name>orderId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产订单ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1471560962252734500</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627746</id>
<name>orderProductId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>产品行ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1471560962252734500</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627747</id>
<name>activityId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>作业标准ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2499381355729664</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627748</id>
<name>activityType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>作业类型ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2499380178506496</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627749</id>
<name>activityTypeCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>作业类别编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627750</id>
<name>activityTypeName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>作业类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627751</id>
<name>orderProcessId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工序ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1471560962252734500</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627752</id>
<name>opSn</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工序顺序号</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627753</id>
<name>activityClass</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>作业类别；0-人工，1-设备，2-委外，3-空闲，4-其他</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627754</id>
<name>workCenterId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工作中心ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627755</id>
<name>workCenterCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工作中心编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>C0001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627756</id>
<name>workCenterName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工作中心名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>车间管理-机加工</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627757</id>
<name>operationId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>标准工序ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627758</id>
<name>operationCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工序编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>*********</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627759</id>
<name>operationName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工序名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>工序1234</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627760</id>
<name>activityUnit</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>数量单位ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1998025388839168</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627761</id>
<name>activityUnitName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>数量单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>箱</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627762</id>
<name>activityUnitPrecision</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>数量单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>5</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627763</id>
<name>activityUnitTruncationType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>数量单位舍入方式；同BigDecimal舍入方式</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627764</id>
<name>usageUnit</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计量单位ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1998025388839168</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627765</id>
<name>usageUnitName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计量单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>箱</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627766</id>
<name>usageUnitPrecision</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计量单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>5</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627767</id>
<name>usageUnitTruncationType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计量单位舍入方式；同BigDecimal舍入方式</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627768</id>
<name>stdUsageQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>额定总用量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>144.0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627769</id>
<name>planUsageQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划总用量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>156.0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627770</id>
<name>denominatorQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>母件底数</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1.0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627771</id>
<name>usageQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>标准作业量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627772</id>
<name>isCalcCost</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计算成本</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627773</id>
<name>activityQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>13.0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627774</id>
<name>usageBasis</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计量基础;0-物料，1-批次</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627775</id>
<name>isAutoCreate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>自动创建；0-否，1-是</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627776</id>
<name>pubts</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627742</parentId>
<defParamId/>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2022-06-06 18:45:06</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:22.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:22.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>作业信息</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>77</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-01 16:46:54.416</gmtCreate>
<gmtUpdate>2025-07-01 16:46:54.416</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627777</id>
<name>orderByProduct</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<children>
<children>
<id>2303473535963627778</id>
<name>id</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>联副产品ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1471560962252734500</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627779</id>
<name>orderProductLineNo</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>行号</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627780</id>
<name>manufacturingSpecification</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料规格</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>Amy测试:A</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627781</id>
<name>numeratorQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>分子用量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627782</id>
<name>productionType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>产出类型；1-联产品，2-副产品</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627783</id>
<name>productUnitPrecision</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>8</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627784</id>
<name>mainUnitTruncationType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量舍位方式；同BigDecimal的舍入方式</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627785</id>
<name>warehouseName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>预入仓库</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>倒冲仓</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627786</id>
<name>unitUseQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>单位产出数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1.00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627787</id>
<name>orgId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627788</id>
<name>orgName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>Amy测试</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627789</id>
<name>productName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627790</id>
<name>productCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627791</id>
<name>productId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2037661108424960</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627794</id>
<name>skuCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>SKU编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>10350000010001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627795</id>
<name>skuName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>SKU名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>台式机A</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627796</id>
<name>lineNo</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>行号</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627797</id>
<name>productionDate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>产出日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2022-06-06 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627798</id>
<name>isBatchManage</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>是否批次管理</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627799</id>
<name>isExpiryDateManage</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>是否效期管理</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627800</id>
<name>mainUnitPrecision</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>8</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627801</id>
<name>changeRate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>换算率</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1.0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627802</id>
<name>pubts</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2022-06-06 18:44:04</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627803</id>
<name>skuId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>skuId</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2037661108424961</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627804</id>
<name>denominatorQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>分母用量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1.0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627805</id>
<name>mainUnit</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627806</id>
<name>quantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>产出数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>12</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627807</id>
<name>changeType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>换算方式；0-固定换算，1-浮动换算</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627808</id>
<name>productUnitTruncationType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>库存单位舍位方式；同BigDecimal的舍入方式</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627809</id>
<name>orderProductId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>订单成产品id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1471560962252734500</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627810</id>
<name>materialId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>制造物料id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2037661323661568</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627811</id>
<name>productionUnitId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627812</id>
<name>mainUnitName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>台</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627813</id>
<name>materialName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>台式机</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627814</id>
<name>warehouseId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>预入仓库Id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2533365513196032</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627815</id>
<name>offChartReceipt</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>表外产出:false-否，true-是</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627816</id>
<name>productUnitName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>台</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627817</id>
<name>cfmIncomingQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认累计入库数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627818</id>
<name>cfmIncomingAuxQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认累计入库件数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627819</id>
<name>cfmScrapStockQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认报废入库数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627820</id>
<name>cfmScrapStockAuxQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627777</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认报废入库件数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>联副产品信息</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>78</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-01 16:46:54.449</gmtCreate>
<gmtUpdate>2025-07-01 16:46:54.449</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627821</id>
<name>orderProcess</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<children>
<children>
<id>2303473535963627822</id>
<name>id</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工序ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1471560962252734500</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627824</id>
<name>orderId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>订单ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1471560962252734500</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627825</id>
<name>orderProductId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产订单行ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1471560962252734500</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627826</id>
<name>operationControlId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工序控制码ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627827</id>
<name>operationCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工序编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>*********</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627828</id>
<name>productUnitPrecision</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>8</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627829</id>
<name>nextId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>后序ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1471560962252734500</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627830</id>
<name>doScheduling</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>参与调度；0-否，1-是</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627831</id>
<name>transferProcplanProdQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>转工序作业计划件数</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627832</id>
<name>finishGoodsId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>完工库位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1471560962252734500</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627833</id>
<name>finishWarehouseId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>完工仓库id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1471560962252734500</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627834</id>
<name>preSn</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>前序顺序号</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627835</id>
<name>transferProcplanQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>转工序作业计划数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627836</id>
<name>workCenterId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工作中心ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627837</id>
<name>mainUnitPrecision</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>8</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627838</id>
<name>executeOrgName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>执行组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>Amy测试</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627839</id>
<name>outUnitId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>产出单位ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627840</id>
<name>outUnitTruncationType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>产出单位舍位方式；同BigDecimal的舍入方式</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627841</id>
<name>mainUnitId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量单位ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627842</id>
<name>checkType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>质检方式；0-自检，1-车间检验</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627843</id>
<name>orgName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>Amy测试</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627844</id>
<name>routingOperationId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工艺路线行ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627845</id>
<name>mainUnitName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>台</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627846</id>
<name>prepareTime</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划准备时间</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>0.3</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627847</id>
<name>routingOperationProcessTime</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>单批加工时间</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627848</id>
<name>qty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划生产数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>12</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627849</id>
<name>occupyProduction</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>占用产能；0-否，1-是</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627850</id>
<name>computingCosts</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计算成本；0-否，1-是</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627851</id>
<name>mainChangeRate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产-主计量换算率</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1.0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627852</id>
<name>immediateHandover</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>即时交接；0-否，1-是</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627853</id>
<name>operationIdRouteDesc</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工艺描述</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>官方警告</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627854</id>
<name>outUnitName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>产出单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>台</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627855</id>
<name>operationControlName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工序控制码名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>车间检验</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627856</id>
<name>outChangeRate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>产出-主计量换算率</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1.0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627857</id>
<name>mainUnitTruncationType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量舍位方式；同BigDecimal的舍入方式</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627858</id>
<name>orgId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>组织ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627859</id>
<name>processTime</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划加工时间</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>24.0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627860</id>
<name>procPlanCreate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工序作业计划创建；0-否，1-是</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627861</id>
<name>nextSn</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>后序顺序号</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>20.0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627862</id>
<name>prodQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划生产件数</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>12.0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627863</id>
<name>operationId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工序ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627864</id>
<name>processType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>加工类型；0-正常加工，1-返工生产，2-报废补投</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627865</id>
<name>sn</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>顺序号</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627866</id>
<name>pubts</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2022-06-06 18:44:04</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627867</id>
<name>planStartDate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划开工时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2022-06-06 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627868</id>
<name>planEndDate</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划完工时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2022-06-06 23:59:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627869</id>
<name>workCenterName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工作中心名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>车间管理-机加工</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627870</id>
<name>timeUnit</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>时间单位；0-天，1-小时，2-分，3-秒</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627871</id>
<name>operationControlCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工序控制码编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>002</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627872</id>
<name>firstCheck</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>首检；0-否，1-是</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627873</id>
<name>changeType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>换算方式；0-固定换算，1-浮动换算</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627874</id>
<name>productUnitTruncationType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位舍位方式；同BigDecimal的舍入方式</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627875</id>
<name>isOutsource</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>委外；0-否，1-是</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627876</id>
<name>operationName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工序名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>工序1234</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627877</id>
<name>productionUnitId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627878</id>
<name>outUnitPrecision</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>产出单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>8</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627879</id>
<name>workCenterCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>工作中心编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>C0001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627880</id>
<name>executeOrgId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>执行组织ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627881</id>
<name>productUnitName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>台</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627882</id>
<name>reportWork</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>报工；0-否，1-是</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627883</id>
<name>scheduleProdNum2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>计划加工数量（产出单位）</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627884</id>
<name>totalCompleteNum</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计完成数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>62</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627885</id>
<name>totalCompleteNum1</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计完成件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>63</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627886</id>
<name>totalCompleteNum2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计完成数量（产出单位）</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>64</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627887</id>
<name>totalQualifiedNum</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计合格数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>65</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627888</id>
<name>totalQualifiedNum1</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计合格件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>66</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627889</id>
<name>totalQualifiedNum2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计合格数量 （产出单位）</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>67</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627890</id>
<name>totalScrapNum</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计报废数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>68</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627891</id>
<name>totalScrapNum1</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计报废件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>69</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627892</id>
<name>totalScrapNum2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计报废数量 （产出单位）</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>70</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627893</id>
<name>totalReworkNum</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计待返工数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>71</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627894</id>
<name>totalReworkNum1</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计待返工件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>72</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627895</id>
<name>totalReworkNum2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计待返工数量 （产出单位）</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>73</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627896</id>
<name>totalReworkProcessNum</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计返工处理数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>74</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627897</id>
<name>totalReworkProcessNum1</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计返工处理件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>75</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627898</id>
<name>totalReworkProcessNum2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计返工处理数量 （产出单位）</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>76</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627899</id>
<name>totalTurnNum</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计转出数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>77</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627900</id>
<name>totalTurnNum1</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计转出件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>78</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627901</id>
<name>totalTurnNum2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计转出数量 （产出单位）</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>79</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627902</id>
<name>totalQualifiedTurnNum</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计合格转出数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>80</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627903</id>
<name>totalQualifiedTurnNum1</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计合格转出件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>81</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627904</id>
<name>totalQualifiedTurnNum2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计合格转出数量 （产出单位）</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>82</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627905</id>
<name>totalReworkTurnNum</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计返工转出数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>83</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627906</id>
<name>totalReworkTurnNum1</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计返工转出件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>84</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627907</id>
<name>totalReworkTurnNum2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计返工转出数量 （产出单位）</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>85</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627908</id>
<name>scrapInNum</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计报废转出数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>86</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627909</id>
<name>scrapInNum1</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计报废转出件数</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>87</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627910</id>
<name>scrapInNum2</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627821</parentId>
<defParamId/>
<array>false</array>
<paramDesc>累计报废转出数量 （产出数量）</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>88</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>工序信息</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>79</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-01 16:46:54.531</gmtCreate>
<gmtUpdate>2025-07-01 16:46:54.531</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627911</id>
<name>out_sys_id</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>外部来源Id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2297527422652672</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>80</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627912</id>
<name>out_sys_code</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>外部来源编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>SCDD20230101</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>81</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627913</id>
<name>out_sys_version</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>外部系统版本</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>01</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>82</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627914</id>
<name>out_sys_type</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>外部来源类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>u8c</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>83</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627915</id>
<name>OrderProduct_projectId</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>项目Id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>84</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627916</id>
<name>out_sys_rowno</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>外部来源行号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>85</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627917</id>
<name>out_sys_lineid</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>外部来源行</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>86</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627918</id>
<name>OrderProduct_projectCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>项目编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>87</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627919</id>
<name>OrderProduct_projectName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>项目名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>88</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627920</id>
<name>OrderProduct_wbs</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>wbs</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>89</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627921</id>
<name>OrderProduct_wbsCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>WBS任务编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>90</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627922</id>
<name>OrderProduct_wbsName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>WBS任务名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>91</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627923</id>
<name>OrderProduct_activity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>活动</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>92</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627924</id>
<name>OrderProduct_activityCode</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>活动编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>93</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627925</id>
<name>OrderProduct_activityName</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>活动名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>94</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627926</id>
<name>cfmIncomingQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认累计入库数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>95</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627927</id>
<name>cfmIncomingAuxQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认累计入库件数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>96</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627928</id>
<name>cfmScrapStockQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认报废入库数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>97</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627929</id>
<name>cfmScrapStockAuxQty</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认报废入库件数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>98</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627930</id>
<name>cfmReceivedKit</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>确认已领套数</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>99</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627931</id>
<name>firstCheck</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>首检；0-否，1-是</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>100</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627932</id>
<name>firstCheckType</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>首检控制方式；0-不控制，1-严格</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>101</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627933</id>
<name>firstCheckStatus</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627580</parentId>
<defParamId/>
<array>false</array>
<paramDesc>首检状态；0-无需首检，1-待首检，2-首检中，3-首检合格，4-首检不合格</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>102</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate/>
<gmtUpdate/>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>返回数据对象</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-01 16:46:54.354</gmtCreate>
<gmtUpdate>2025-07-01 16:46:54.354</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag>respData</paramTag>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627934</id>
<name>sumRecordList</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627576</parentId>
<children>
<children>
<id>2303473535963627935</id>
<name>OrderProduct_auxiliaryQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627934</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产件数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627936</id>
<name>OrderProduct_quantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627934</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627937</id>
<name>mainUnitPrecision</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627934</parentId>
<defParamId/>
<array>false</array>
<paramDesc>主计量精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>3</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627938</id>
<name>productUnitPrecision</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627934</parentId>
<defParamId/>
<array>false</array>
<paramDesc>生产单位精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>3</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627939</id>
<name>OrderProduct_mrpQuantity</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627934</parentId>
<defParamId/>
<array>false</array>
<paramDesc>净算量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>120</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>true</array>
<paramDesc>合计字段集合</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-01 16:46:54.613</gmtCreate>
<gmtUpdate>2025-07-01 16:46:54.613</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627940</id>
<name>pageCount</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627576</parentId>
<defParamId/>
<array>false</array>
<paramDesc>总页数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627941</id>
<name>beginPageIndex</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627576</parentId>
<defParamId/>
<array>false</array>
<paramDesc>开始页码</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627942</id>
<name>endPageIndex</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627576</parentId>
<defParamId/>
<array>false</array>
<paramDesc>结束页码</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2303473535963627943</id>
<name>pubts</name>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<parentId>2303473535963627576</parentId>
<defParamId/>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-03-24 15:11:10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>调用成功时的返回数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-01 16:46:54.344</gmtCreate>
<gmtUpdate>2025-07-01 16:46:54.344</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2303473535963627527</id>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<content>{ "code": "200", "message": "操作成功", "data": { "pageIndex": 1, "pageSize": 20, "recordCount": 0, "recordList": [ { "OrderProduct_materialName": "自行车", "OrderProduct_startDate": "2021-03-24 00:00:00", "productDefineDts": 0, "freeCharacteristics": 0, "defineDts": 0, "OrderProduct_lineNo": 10, "productUnitPrecision": 3, "OrderProduct_scrap": 0, "OrderProduct_orgId": "****************", "OrderProduct_skuCode": "jq01000001", "OrderProduct_materialId": ****************, "transTypeId": "****************", "mainUnitPrecision": 3, "id": ****************, "OrderProduct_sourceType": "1", "OrderProduct_productId": ****************, "OrderProduct_mrpQuantity": 120, "OrderProduct_changeType": 0, "departmentName": "生产部", "orgName": "L工厂1", "auditTime": "2023-02-21 11:22:55", "auditDate": "2023-02-22", "isWfControlled": false, "OrderProduct_quantity": 120, "OrderProduct_completedQuantity": 1231, "OrderProduct_incomingQuantity": 120, "isHold": false, "OrderProduct_skuName": "自行车", "routingVersion": "1.0", "routingCode": "dfasdaf", "routingId": ****************, "OrderProduct_completedFlag": true, "OrderProduct_materialCode": "jq01000001", "OrderProduct_productionUnitId": ****************, "status": 0, "returncount": 0, "routingName": "工艺", "verifystate": 0, "code": "SCDD20210324000003", "creatorId": ****************, "orderProduct_id": ****************, "orgId": "****************", "vouchdate": "2021-03-24 00:00:00", "OrderProduct_auxiliaryQuantity": 120, "OrderProduct_materialApplyFlag": false, "OrderProduct_mainUnit": ****************, "OrderProduct_mainUnitTruncationType": 4, "transTypeName": "标准生产", "pubts": "2021-03-24 11:40:13", "OrderProduct_skuId": ****************, "OrderProduct_productUnitTruncationType": 4, "entrustProcessType": 2, "OrderProduct_retMaterialApplyFlag": 0, "OrderProduct_orgName": "L工厂1", "creator": "***********", "OrderProduct_finishDate": "2021-03-26 00:00:00", "OrderProduct_changeRate": 1, "OrderProduct_isHold": false, "entrustCustomer": ********, "OrderProduct_versionCode": "A1", "OrderProduct_bomId": ****************, "OrderProduct_productUnitName": "件", "OrderProduct_mainUnitName": "件", "OrderProduct_materialApplyStatus": 1, "OrderProduct_materialStatus": 0, "entrustCustomerName": "受托客户", "OrderProduct_finishedWorkApplyStatus": 2, "OrderProduct_stockStatus": 1, "createTime": "2021-03-24 11:40:12", "productionDepartmentId": "1870534089855232", "offChartReceiptIsAllowed": true, "apsLock": 0, "dailyschQuantity": 10, "dailyschStatus": 0, "dailyschConquantity": 10, "transTypeCode": "PO-001", "orderMaterial": [ { "materialDefineDts": 0, "freeCharacteristics": 0, "isWholeSet": false, "receivedQuantity": 40, "recipientQuantity": 40, "numeratorQuantity": 1, "stockUnitPrecision": 7, "mainUnitTruncationType": 1, "stockUnitName": "个 ", "unitUseQuantity": 0.33333333, "auxiliaryReceivedQuantity": 40, "auxiliaryRecipientQuantity": 40, "orgId": "1870887948554496", "skuName": "车轮", "stockUnitTruncationType": 1, "scrap": 0, "lineNo": 10, "supplyType": "0", "mainUnitPrecision": 7, "truncUp": 0, "substituteFlag": 1, "id": 2184924571914498, "changeRate": 1, "pubts": "2021-03-24 11:40:13", "skuId": 2062992424030464, "denominatorQuantity": 3, "bomId": ****************, "mainUnit": 1986620623900928, "fixedQuantity": 0, "orgName": "qing-gc001", "productName": "", "productCode": "", "productId": 2062992410120448, "changeType": 0, "materialCode": "jq01000003", "orderProductId": ****************, "materialId": 2062992427503872, "bomMaterialId": 2173985857769729, "mainUnitName": "个 ", "materialName": "车轮", "requirementDate": "2021-03-24 00:00:00", "skuCode": "jq01000003", "stockUnitId": 1986620623900928, "mustLossQuantity": 0, "calcCostFlag": 1, "orderMaterialExpinfo!id": 1471560962252734500, "excessAppliedQty": 25, "auxiliaryExcessAppliedQty": 25, "excessRecipientQty": 25, "auxiliaryExcessRecipientQty": 25, "orderMaterialExpinfo!excessQty": 25, "orderMaterialExpinfo!auxiliaryExcessQty": 25, "orderMaterialExpinfo!isExcess": false, "orderMaterialExpinfo!excessType": 1, "orderMaterialExpinfo!excessRate": 50, "orderMaterialExpinfo!fixedExcessQty": 25, "orderMaterialExpinfo!designator": "EW32421FDS3232S", "orderMaterialExpinfo!wholePoint": "1", "appliedRetQuantity": 20, "auxiliaryAppliedRetQuantity": 20, "appliedRetRestQuantity": 20, "auxiliaryAppliedRetRestQuantity": 20, "excessAppliedRetQty": 20, "auxiliaryExcessAppliedRetQty": 20, "excessAppliedRetRestQty": 20, "auxiliaryExcessAppliedRetRestQty": 20, "appliedRestQuantity": 20, "auxiliaryAppliedRestQuantity": 20, "excessAppliedRestQty": 20, "auxiliaryExcessAppliedRestQty": 20, "projectId": "", "projectCode": "", "projectName": "", "wbs": "", "wbsCode": "", "wbsName": "", "activity": 0, "activityCode": "", "activityName": "" } ], "orderActivity": [ { "id": 0, "lineNum": 10, "orderId": 1471560962252734500, "orderProductId": 1471560962252734500, "activityId": 2499381355729664, "activityType": 2499380178506496, "activityTypeCode": "001", "activityTypeName": "001", "orderProcessId": 1471560962252734500, "opSn": 10, "activityClass": 4, "workCenterId": ****************, "workCenterCode": "C0001", "workCenterName": "车间管理-机加工", "operationId": ****************, "operationCode": "*********", "operationName": "工序1234", "activityUnit": 1998025388839168, "activityUnitName": "箱", "activityUnitPrecision": 5, "activityUnitTruncationType": 4, "usageUnit": 1998025388839168, "usageUnitName": "箱", "usageUnitPrecision": 5, "usageUnitTruncationType": 4, "stdUsageQty": 144, "planUsageQty": 156, "denominatorQuantity": 1, "usageQty": 1, "isCalcCost": true, "activityQty": 13, "usageBasis": 0, "isAutoCreate": 1, "pubts": "2022-06-06 18:45:06" } ], "orderByProduct": [ { "id": 1471560962252734500, "orderProductLineNo": 10, "manufacturingSpecification": "Amy测试:A", "numeratorQuantity": 1, "productionType": "1", "productUnitPrecision": 8, "mainUnitTruncationType": 4, "warehouseName": "倒冲仓", "unitUseQuantity": 1, "orgId": "****************", "orgName": "Amy测试", "productName": "", "productCode": "", "productId": 2037661108424960, "freeCharacteristics": 0, "byProductDefineDts": 0, "skuCode": "10350000010001", "skuName": "台式机A", "lineNo": 10, "productionDate": "2022-06-06 00:00:00", "isBatchManage": false, "isExpiryDateManage": false, "mainUnitPrecision": 8, "changeRate": 1, "pubts": "2022-06-06 18:44:04", "skuId": 2037661108424961, "denominatorQuantity": 1, "mainUnit": ****************, "quantity": 12, "changeType": 0, "productUnitTruncationType": 4, "orderProductId": 1471560962252734500, "materialId": 2037661323661568, "productionUnitId": ****************, "mainUnitName": "台", "materialName": "台式机", "warehouseId": 2533365513196032, "offChartReceipt": true, "productUnitName": "台" } ], "orderProcess": [ { "id": 1471560962252734500, "processDefineDts": 0, "orderId": 1471560962252734500, "orderProductId": 1471560962252734500, "operationControlId": ****************, "operationCode": "*********", "productUnitPrecision": 8, "nextId": 1471560962252734500, "doScheduling": 1, "transferProcplanProdQty": 1, "finishGoodsId": 1471560962252734500, "finishWarehouseId": 1471560962252734500, "preSn": 1, "transferProcplanQty": 1, "workCenterId": ****************, "mainUnitPrecision": 8, "executeOrgName": "Amy测试", "outUnitId": ****************, "outUnitTruncationType": 4, "mainUnitId": ****************, "checkType": 0, "orgName": "Amy测试", "routingOperationId": ****************, "mainUnitName": "台", "prepareTime": 0.3, "routingOperationProcessTime": 2, "qty": 12, "occupyProduction": 0, "computingCosts": 0, "mainChangeRate": 1, "immediateHandover": 1, "operationIdRouteDesc": "官方警告", "outUnitName": "台", "operationControlName": "车间检验", "outChangeRate": 1, "mainUnitTruncationType": 4, "orgId": "****************", "processTime": 24, "procPlanCreate": 0, "nextSn": 20, "prodQty": 12, "operationId": ****************, "processType": 0, "sn": 10, "pubts": "2022-06-06 18:44:04", "planStartDate": "2022-06-06 00:00:00", "planEndDate": "2022-06-06 23:59:00", "workCenterName": "车间管理-机加工", "timeUnit": 1, "operationControlCode": "002", "firstCheck": 0, "changeType": 0, "productUnitTruncationType": 4, "isOutsource": 0, "operationName": "工序1234", "productionUnitId": ****************, "outUnitPrecision": 8, "workCenterCode": "C0001", "executeOrgId": "****************", "productUnitName": "台", "reportWork": 0, "scheduleProdNum2": 1, "totalCompleteNum": 1, "totalCompleteNum1": 1, "totalCompleteNum2": 1, "totalQualifiedNum": 1, "totalQualifiedNum1": 1, "totalQualifiedNum2": 1, "totalScrapNum": 1, "totalScrapNum1": 1, "totalScrapNum2": 1, "totalReworkNum": 1, "totalReworkNum1": 1, "totalReworkNum2": 1, "totalReworkProcessNum": 1, "totalReworkProcessNum1": 1, "totalReworkProcessNum2": 1, "totalTurnNum": 1, "totalTurnNum1": 1, "totalTurnNum2": 1, "totalQualifiedTurnNum": 1, "totalQualifiedTurnNum1": 1, "totalQualifiedTurnNum2": 1, "totalReworkTurnNum": 1, "totalReworkTurnNum1": 1, "totalReworkTurnNum2": 1, "scrapInNum": 1, "scrapInNum1": 1, "scrapInNum2": 1 } ], "out_sys_id": "2297527422652672", "out_sys_code": "SCDD20230101", "out_sys_version": "01", "out_sys_type": "u8c", "OrderProduct_projectId": "", "out_sys_rowno": "", "out_sys_lineid": "", "OrderProduct_projectCode": "", "OrderProduct_projectName": "", "OrderProduct_wbs": "", "OrderProduct_wbsCode": "", "OrderProduct_wbsName": "", "OrderProduct_activity": 0, "OrderProduct_activityCode": "", "OrderProduct_activityName": "" } ], "sumRecordList": [ { "OrderProduct_auxiliaryQuantity": 120, "OrderProduct_quantity": 120, "mainUnitPrecision": 3, "productUnitPrecision": 3, "OrderProduct_mrpQuantity": 120 } ], "pageCount": 0, "beginPageIndex": 1, "endPageIndex": 0, "pubts": "2021-03-24 15:11:10" } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2303473535963627528</id>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<content>{ "code": "999", "message": "非法的时间： 11111" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2303473535963627527</id>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<content>{ "code": "200", "message": "操作成功", "data": { "pageIndex": 1, "pageSize": 20, "recordCount": 0, "recordList": [ { "OrderProduct_materialName": "自行车", "OrderProduct_startDate": "2021-03-24 00:00:00", "productDefineDts": 0, "freeCharacteristics": 0, "defineDts": 0, "OrderProduct_lineNo": 10, "productUnitPrecision": 3, "OrderProduct_scrap": 0, "OrderProduct_orgId": "****************", "OrderProduct_skuCode": "jq01000001", "OrderProduct_materialId": ****************, "transTypeId": "****************", "mainUnitPrecision": 3, "id": ****************, "OrderProduct_sourceType": "1", "OrderProduct_productId": ****************, "OrderProduct_mrpQuantity": 120, "OrderProduct_changeType": 0, "departmentName": "生产部", "orgName": "L工厂1", "auditTime": "2023-02-21 11:22:55", "auditDate": "2023-02-22", "isWfControlled": false, "OrderProduct_quantity": 120, "OrderProduct_completedQuantity": 1231, "OrderProduct_incomingQuantity": 120, "isHold": false, "OrderProduct_skuName": "自行车", "routingVersion": "1.0", "routingCode": "dfasdaf", "routingId": ****************, "OrderProduct_completedFlag": true, "OrderProduct_materialCode": "jq01000001", "OrderProduct_productionUnitId": ****************, "status": 0, "returncount": 0, "routingName": "工艺", "verifystate": 0, "code": "SCDD20210324000003", "creatorId": ****************, "orderProduct_id": ****************, "orgId": "****************", "vouchdate": "2021-03-24 00:00:00", "OrderProduct_auxiliaryQuantity": 120, "OrderProduct_materialApplyFlag": false, "OrderProduct_mainUnit": ****************, "OrderProduct_mainUnitTruncationType": 4, "transTypeName": "标准生产", "pubts": "2021-03-24 11:40:13", "OrderProduct_skuId": ****************, "OrderProduct_productUnitTruncationType": 4, "entrustProcessType": 2, "OrderProduct_retMaterialApplyFlag": 0, "OrderProduct_orgName": "L工厂1", "creator": "***********", "OrderProduct_finishDate": "2021-03-26 00:00:00", "OrderProduct_changeRate": 1, "OrderProduct_isHold": false, "entrustCustomer": ********, "OrderProduct_versionCode": "A1", "OrderProduct_bomId": ****************, "OrderProduct_productUnitName": "件", "OrderProduct_mainUnitName": "件", "OrderProduct_materialApplyStatus": 1, "OrderProduct_materialStatus": 0, "entrustCustomerName": "受托客户", "OrderProduct_finishedWorkApplyStatus": 2, "OrderProduct_stockStatus": 1, "createTime": "2021-03-24 11:40:12", "productionDepartmentId": "1870534089855232", "offChartReceiptIsAllowed": true, "apsLock": 0, "dailyschQuantity": 10, "dailyschStatus": 0, "dailyschConquantity": 10, "transTypeCode": "PO-001", "orderMaterial": [ { "materialDefineDts": 0, "freeCharacteristics": 0, "isWholeSet": false, "receivedQuantity": 40, "recipientQuantity": 40, "numeratorQuantity": 1, "stockUnitPrecision": 7, "mainUnitTruncationType": 1, "stockUnitName": "个 ", "unitUseQuantity": 0.33333333, "auxiliaryReceivedQuantity": 40, "auxiliaryRecipientQuantity": 40, "orgId": "1870887948554496", "skuName": "车轮", "stockUnitTruncationType": 1, "scrap": 0, "lineNo": 10, "supplyType": "0", "mainUnitPrecision": 7, "truncUp": 0, "substituteFlag": 1, "id": 2184924571914498, "changeRate": 1, "pubts": "2021-03-24 11:40:13", "skuId": 2062992424030464, "denominatorQuantity": 3, "bomId": ****************, "mainUnit": 1986620623900928, "fixedQuantity": 0, "orgName": "qing-gc001", "productName": "", "productCode": "", "productId": 2062992410120448, "changeType": 0, "materialCode": "jq01000003", "orderProductId": ****************, "materialId": 2062992427503872, "bomMaterialId": 2173985857769729, "mainUnitName": "个 ", "materialName": "车轮", "requirementDate": "2021-03-24 00:00:00", "skuCode": "jq01000003", "stockUnitId": 1986620623900928, "mustLossQuantity": 0, "calcCostFlag": 1, "orderMaterialExpinfo!id": 1471560962252734500, "excessAppliedQty": 25, "auxiliaryExcessAppliedQty": 25, "excessRecipientQty": 25, "auxiliaryExcessRecipientQty": 25, "orderMaterialExpinfo!excessQty": 25, "orderMaterialExpinfo!auxiliaryExcessQty": 25, "orderMaterialExpinfo!isExcess": false, "orderMaterialExpinfo!excessType": 1, "orderMaterialExpinfo!excessRate": 50, "orderMaterialExpinfo!fixedExcessQty": 25, "orderMaterialExpinfo!designator": "EW32421FDS3232S", "orderMaterialExpinfo!wholePoint": "1", "appliedRetQuantity": 20, "auxiliaryAppliedRetQuantity": 20, "appliedRetRestQuantity": 20, "auxiliaryAppliedRetRestQuantity": 20, "excessAppliedRetQty": 20, "auxiliaryExcessAppliedRetQty": 20, "excessAppliedRetRestQty": 20, "auxiliaryExcessAppliedRetRestQty": 20, "appliedRestQuantity": 20, "auxiliaryAppliedRestQuantity": 20, "excessAppliedRestQty": 20, "auxiliaryExcessAppliedRestQty": 20, "projectId": "", "projectCode": "", "projectName": "", "wbs": "", "wbsCode": "", "wbsName": "", "activity": 0, "activityCode": "", "activityName": "" } ], "orderActivity": [ { "id": 0, "lineNum": 10, "orderId": 1471560962252734500, "orderProductId": 1471560962252734500, "activityId": 2499381355729664, "activityType": 2499380178506496, "activityTypeCode": "001", "activityTypeName": "001", "orderProcessId": 1471560962252734500, "opSn": 10, "activityClass": 4, "workCenterId": ****************, "workCenterCode": "C0001", "workCenterName": "车间管理-机加工", "operationId": ****************, "operationCode": "*********", "operationName": "工序1234", "activityUnit": 1998025388839168, "activityUnitName": "箱", "activityUnitPrecision": 5, "activityUnitTruncationType": 4, "usageUnit": 1998025388839168, "usageUnitName": "箱", "usageUnitPrecision": 5, "usageUnitTruncationType": 4, "stdUsageQty": 144, "planUsageQty": 156, "denominatorQuantity": 1, "usageQty": 1, "isCalcCost": true, "activityQty": 13, "usageBasis": 0, "isAutoCreate": 1, "pubts": "2022-06-06 18:45:06" } ], "orderByProduct": [ { "id": 1471560962252734500, "orderProductLineNo": 10, "manufacturingSpecification": "Amy测试:A", "numeratorQuantity": 1, "productionType": "1", "productUnitPrecision": 8, "mainUnitTruncationType": 4, "warehouseName": "倒冲仓", "unitUseQuantity": 1, "orgId": "****************", "orgName": "Amy测试", "productName": "", "productCode": "", "productId": 2037661108424960, "freeCharacteristics": 0, "byProductDefineDts": 0, "skuCode": "10350000010001", "skuName": "台式机A", "lineNo": 10, "productionDate": "2022-06-06 00:00:00", "isBatchManage": false, "isExpiryDateManage": false, "mainUnitPrecision": 8, "changeRate": 1, "pubts": "2022-06-06 18:44:04", "skuId": 2037661108424961, "denominatorQuantity": 1, "mainUnit": ****************, "quantity": 12, "changeType": 0, "productUnitTruncationType": 4, "orderProductId": 1471560962252734500, "materialId": 2037661323661568, "productionUnitId": ****************, "mainUnitName": "台", "materialName": "台式机", "warehouseId": 2533365513196032, "offChartReceipt": true, "productUnitName": "台" } ], "orderProcess": [ { "id": 1471560962252734500, "processDefineDts": 0, "orderId": 1471560962252734500, "orderProductId": 1471560962252734500, "operationControlId": ****************, "operationCode": "*********", "productUnitPrecision": 8, "nextId": 1471560962252734500, "doScheduling": 1, "transferProcplanProdQty": 1, "finishGoodsId": 1471560962252734500, "finishWarehouseId": 1471560962252734500, "preSn": 1, "transferProcplanQty": 1, "workCenterId": ****************, "mainUnitPrecision": 8, "executeOrgName": "Amy测试", "outUnitId": ****************, "outUnitTruncationType": 4, "mainUnitId": ****************, "checkType": 0, "orgName": "Amy测试", "routingOperationId": ****************, "mainUnitName": "台", "prepareTime": 0.3, "routingOperationProcessTime": 2, "qty": 12, "occupyProduction": 0, "computingCosts": 0, "mainChangeRate": 1, "immediateHandover": 1, "operationIdRouteDesc": "官方警告", "outUnitName": "台", "operationControlName": "车间检验", "outChangeRate": 1, "mainUnitTruncationType": 4, "orgId": "****************", "processTime": 24, "procPlanCreate": 0, "nextSn": 20, "prodQty": 12, "operationId": ****************, "processType": 0, "sn": 10, "pubts": "2022-06-06 18:44:04", "planStartDate": "2022-06-06 00:00:00", "planEndDate": "2022-06-06 23:59:00", "workCenterName": "车间管理-机加工", "timeUnit": 1, "operationControlCode": "002", "firstCheck": 0, "changeType": 0, "productUnitTruncationType": 4, "isOutsource": 0, "operationName": "工序1234", "productionUnitId": ****************, "outUnitPrecision": 8, "workCenterCode": "C0001", "executeOrgId": "****************", "productUnitName": "台", "reportWork": 0, "scheduleProdNum2": 1, "totalCompleteNum": 1, "totalCompleteNum1": 1, "totalCompleteNum2": 1, "totalQualifiedNum": 1, "totalQualifiedNum1": 1, "totalQualifiedNum2": 1, "totalScrapNum": 1, "totalScrapNum1": 1, "totalScrapNum2": 1, "totalReworkNum": 1, "totalReworkNum1": 1, "totalReworkNum2": 1, "totalReworkProcessNum": 1, "totalReworkProcessNum1": 1, "totalReworkProcessNum2": 1, "totalTurnNum": 1, "totalTurnNum1": 1, "totalTurnNum2": 1, "totalQualifiedTurnNum": 1, "totalQualifiedTurnNum1": 1, "totalQualifiedTurnNum2": 1, "totalReworkTurnNum": 1, "totalReworkTurnNum1": 1, "totalReworkTurnNum2": 1, "scrapInNum": 1, "scrapInNum1": 1, "scrapInNum2": 1 } ], "out_sys_id": "2297527422652672", "out_sys_code": "SCDD20230101", "out_sys_version": "01", "out_sys_type": "u8c", "OrderProduct_projectId": "", "out_sys_rowno": "", "out_sys_lineid": "", "OrderProduct_projectCode": "", "OrderProduct_projectName": "", "OrderProduct_wbs": "", "OrderProduct_wbsCode": "", "OrderProduct_wbsName": "", "OrderProduct_activity": 0, "OrderProduct_activityCode": "", "OrderProduct_activityName": "" } ], "sumRecordList": [ { "OrderProduct_auxiliaryQuantity": 120, "OrderProduct_quantity": 120, "mainUnitPrecision": 3, "productUnitPrecision": 3, "OrderProduct_mrpQuantity": 120 } ], "pageCount": 0, "beginPageIndex": 1, "endPageIndex": 0, "pubts": "2021-03-24 15:11:10" } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2303473535963627528</id>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<content>{ "code": "999", "message": "非法的时间： 11111" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>2303473535963627527</id>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<content>{ "code": "200", "message": "操作成功", "data": { "pageIndex": 1, "pageSize": 20, "recordCount": 0, "recordList": [ { "OrderProduct_materialName": "自行车", "OrderProduct_startDate": "2021-03-24 00:00:00", "productDefineDts": 0, "freeCharacteristics": 0, "defineDts": 0, "OrderProduct_lineNo": 10, "productUnitPrecision": 3, "OrderProduct_scrap": 0, "OrderProduct_orgId": "****************", "OrderProduct_skuCode": "jq01000001", "OrderProduct_materialId": ****************, "transTypeId": "****************", "mainUnitPrecision": 3, "id": ****************, "OrderProduct_sourceType": "1", "OrderProduct_productId": ****************, "OrderProduct_mrpQuantity": 120, "OrderProduct_changeType": 0, "departmentName": "生产部", "orgName": "L工厂1", "auditTime": "2023-02-21 11:22:55", "auditDate": "2023-02-22", "isWfControlled": false, "OrderProduct_quantity": 120, "OrderProduct_completedQuantity": 1231, "OrderProduct_incomingQuantity": 120, "isHold": false, "OrderProduct_skuName": "自行车", "routingVersion": "1.0", "routingCode": "dfasdaf", "routingId": ****************, "OrderProduct_completedFlag": true, "OrderProduct_materialCode": "jq01000001", "OrderProduct_productionUnitId": ****************, "status": 0, "returncount": 0, "routingName": "工艺", "verifystate": 0, "code": "SCDD20210324000003", "creatorId": ****************, "orderProduct_id": ****************, "orgId": "****************", "vouchdate": "2021-03-24 00:00:00", "OrderProduct_auxiliaryQuantity": 120, "OrderProduct_materialApplyFlag": false, "OrderProduct_mainUnit": ****************, "OrderProduct_mainUnitTruncationType": 4, "transTypeName": "标准生产", "pubts": "2021-03-24 11:40:13", "OrderProduct_skuId": ****************, "OrderProduct_productUnitTruncationType": 4, "entrustProcessType": 2, "OrderProduct_retMaterialApplyFlag": 0, "OrderProduct_orgName": "L工厂1", "creator": "***********", "OrderProduct_finishDate": "2021-03-26 00:00:00", "OrderProduct_changeRate": 1, "OrderProduct_isHold": false, "entrustCustomer": ********, "OrderProduct_versionCode": "A1", "OrderProduct_bomId": ****************, "OrderProduct_productUnitName": "件", "OrderProduct_mainUnitName": "件", "OrderProduct_materialApplyStatus": 1, "OrderProduct_materialStatus": 0, "entrustCustomerName": "受托客户", "OrderProduct_finishedWorkApplyStatus": 2, "OrderProduct_stockStatus": 1, "createTime": "2021-03-24 11:40:12", "productionDepartmentId": "1870534089855232", "offChartReceiptIsAllowed": true, "apsLock": 0, "dailyschQuantity": 10, "dailyschStatus": 0, "dailyschConquantity": 10, "transTypeCode": "PO-001", "orderMaterial": [ { "materialDefineDts": 0, "freeCharacteristics": 0, "isWholeSet": false, "receivedQuantity": 40, "recipientQuantity": 40, "numeratorQuantity": 1, "stockUnitPrecision": 7, "mainUnitTruncationType": 1, "stockUnitName": "个 ", "unitUseQuantity": 0.33333333, "auxiliaryReceivedQuantity": 40, "auxiliaryRecipientQuantity": 40, "orgId": "1870887948554496", "skuName": "车轮", "stockUnitTruncationType": 1, "scrap": 0, "lineNo": 10, "supplyType": "0", "mainUnitPrecision": 7, "truncUp": 0, "substituteFlag": 1, "id": 2184924571914498, "changeRate": 1, "pubts": "2021-03-24 11:40:13", "skuId": 2062992424030464, "denominatorQuantity": 3, "bomId": ****************, "mainUnit": 1986620623900928, "fixedQuantity": 0, "orgName": "qing-gc001", "productName": "", "productCode": "", "productId": 2062992410120448, "changeType": 0, "materialCode": "jq01000003", "orderProductId": ****************, "materialId": 2062992427503872, "bomMaterialId": 2173985857769729, "mainUnitName": "个 ", "materialName": "车轮", "requirementDate": "2021-03-24 00:00:00", "skuCode": "jq01000003", "stockUnitId": 1986620623900928, "mustLossQuantity": 0, "calcCostFlag": 1, "orderMaterialExpinfo!id": 1471560962252734500, "excessAppliedQty": 25, "auxiliaryExcessAppliedQty": 25, "excessRecipientQty": 25, "auxiliaryExcessRecipientQty": 25, "orderMaterialExpinfo!excessQty": 25, "orderMaterialExpinfo!auxiliaryExcessQty": 25, "orderMaterialExpinfo!isExcess": false, "orderMaterialExpinfo!excessType": 1, "orderMaterialExpinfo!excessRate": 50, "orderMaterialExpinfo!fixedExcessQty": 25, "orderMaterialExpinfo!designator": "EW32421FDS3232S", "orderMaterialExpinfo!wholePoint": "1", "appliedRetQuantity": 20, "auxiliaryAppliedRetQuantity": 20, "appliedRetRestQuantity": 20, "auxiliaryAppliedRetRestQuantity": 20, "excessAppliedRetQty": 20, "auxiliaryExcessAppliedRetQty": 20, "excessAppliedRetRestQty": 20, "auxiliaryExcessAppliedRetRestQty": 20, "appliedRestQuantity": 20, "auxiliaryAppliedRestQuantity": 20, "excessAppliedRestQty": 20, "auxiliaryExcessAppliedRestQty": 20, "projectId": "", "projectCode": "", "projectName": "", "wbs": "", "wbsCode": "", "wbsName": "", "activity": 0, "activityCode": "", "activityName": "" } ], "orderActivity": [ { "id": 0, "lineNum": 10, "orderId": 1471560962252734500, "orderProductId": 1471560962252734500, "activityId": 2499381355729664, "activityType": 2499380178506496, "activityTypeCode": "001", "activityTypeName": "001", "orderProcessId": 1471560962252734500, "opSn": 10, "activityClass": 4, "workCenterId": ****************, "workCenterCode": "C0001", "workCenterName": "车间管理-机加工", "operationId": ****************, "operationCode": "*********", "operationName": "工序1234", "activityUnit": 1998025388839168, "activityUnitName": "箱", "activityUnitPrecision": 5, "activityUnitTruncationType": 4, "usageUnit": 1998025388839168, "usageUnitName": "箱", "usageUnitPrecision": 5, "usageUnitTruncationType": 4, "stdUsageQty": 144, "planUsageQty": 156, "denominatorQuantity": 1, "usageQty": 1, "isCalcCost": true, "activityQty": 13, "usageBasis": 0, "isAutoCreate": 1, "pubts": "2022-06-06 18:45:06" } ], "orderByProduct": [ { "id": 1471560962252734500, "orderProductLineNo": 10, "manufacturingSpecification": "Amy测试:A", "numeratorQuantity": 1, "productionType": "1", "productUnitPrecision": 8, "mainUnitTruncationType": 4, "warehouseName": "倒冲仓", "unitUseQuantity": 1, "orgId": "****************", "orgName": "Amy测试", "productName": "", "productCode": "", "productId": 2037661108424960, "freeCharacteristics": 0, "byProductDefineDts": 0, "skuCode": "10350000010001", "skuName": "台式机A", "lineNo": 10, "productionDate": "2022-06-06 00:00:00", "isBatchManage": false, "isExpiryDateManage": false, "mainUnitPrecision": 8, "changeRate": 1, "pubts": "2022-06-06 18:44:04", "skuId": 2037661108424961, "denominatorQuantity": 1, "mainUnit": ****************, "quantity": 12, "changeType": 0, "productUnitTruncationType": 4, "orderProductId": 1471560962252734500, "materialId": 2037661323661568, "productionUnitId": ****************, "mainUnitName": "台", "materialName": "台式机", "warehouseId": 2533365513196032, "offChartReceipt": true, "productUnitName": "台" } ], "orderProcess": [ { "id": 1471560962252734500, "processDefineDts": 0, "orderId": 1471560962252734500, "orderProductId": 1471560962252734500, "operationControlId": ****************, "operationCode": "*********", "productUnitPrecision": 8, "nextId": 1471560962252734500, "doScheduling": 1, "transferProcplanProdQty": 1, "finishGoodsId": 1471560962252734500, "finishWarehouseId": 1471560962252734500, "preSn": 1, "transferProcplanQty": 1, "workCenterId": ****************, "mainUnitPrecision": 8, "executeOrgName": "Amy测试", "outUnitId": ****************, "outUnitTruncationType": 4, "mainUnitId": ****************, "checkType": 0, "orgName": "Amy测试", "routingOperationId": ****************, "mainUnitName": "台", "prepareTime": 0.3, "routingOperationProcessTime": 2, "qty": 12, "occupyProduction": 0, "computingCosts": 0, "mainChangeRate": 1, "immediateHandover": 1, "operationIdRouteDesc": "官方警告", "outUnitName": "台", "operationControlName": "车间检验", "outChangeRate": 1, "mainUnitTruncationType": 4, "orgId": "****************", "processTime": 24, "procPlanCreate": 0, "nextSn": 20, "prodQty": 12, "operationId": ****************, "processType": 0, "sn": 10, "pubts": "2022-06-06 18:44:04", "planStartDate": "2022-06-06 00:00:00", "planEndDate": "2022-06-06 23:59:00", "workCenterName": "车间管理-机加工", "timeUnit": 1, "operationControlCode": "002", "firstCheck": 0, "changeType": 0, "productUnitTruncationType": 4, "isOutsource": 0, "operationName": "工序1234", "productionUnitId": ****************, "outUnitPrecision": 8, "workCenterCode": "C0001", "executeOrgId": "****************", "productUnitName": "台", "reportWork": 0, "scheduleProdNum2": 1, "totalCompleteNum": 1, "totalCompleteNum1": 1, "totalCompleteNum2": 1, "totalQualifiedNum": 1, "totalQualifiedNum1": 1, "totalQualifiedNum2": 1, "totalScrapNum": 1, "totalScrapNum1": 1, "totalScrapNum2": 1, "totalReworkNum": 1, "totalReworkNum1": 1, "totalReworkNum2": 1, "totalReworkProcessNum": 1, "totalReworkProcessNum1": 1, "totalReworkProcessNum2": 1, "totalTurnNum": 1, "totalTurnNum1": 1, "totalTurnNum2": 1, "totalQualifiedTurnNum": 1, "totalQualifiedTurnNum1": 1, "totalQualifiedTurnNum2": 1, "totalReworkTurnNum": 1, "totalReworkTurnNum1": 1, "totalReworkTurnNum2": 1, "scrapInNum": 1, "scrapInNum1": 1, "scrapInNum2": 1 } ], "out_sys_id": "2297527422652672", "out_sys_code": "SCDD20230101", "out_sys_version": "01", "out_sys_type": "u8c", "OrderProduct_projectId": "", "out_sys_rowno": "", "out_sys_lineid": "", "OrderProduct_projectCode": "", "OrderProduct_projectName": "", "OrderProduct_wbs": "", "OrderProduct_wbsCode": "", "OrderProduct_wbsName": "", "OrderProduct_activity": 0, "OrderProduct_activityCode": "", "OrderProduct_activityName": "" } ], "sumRecordList": [ { "OrderProduct_auxiliaryQuantity": 120, "OrderProduct_quantity": 120, "mainUnitPrecision": 3, "productUnitPrecision": 3, "OrderProduct_mrpQuantity": 120 } ], "pageCount": 0, "beginPageIndex": 1, "endPageIndex": 0, "pubts": "2021-03-24 15:11:10" } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>2303473535963627528</id>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<content>{ "code": "999", "message": "非法的时间： 11111" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS>
<errorCodeDTOS>
<id>2303473535963627529</id>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<errorCode>999</errorCode>
<errorMessage>取决于错误类型，不同错误信息不同</errorMessage>
<errorType>API</errorType>
<errorcodeDesc/>
<gmtCreate>2025-03-20 20:03:23.000</gmtCreate>
<gmtUpdate>2025-03-20 20:03:23.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<defErrorId/>
<ytenantId>0</ytenantId>
<displayCodeId/>
</errorCodeDTOS>
</errorCodeDTOS>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>友户通token认证业务扩展插件</name>
<configurable>false</configurable>
<description>YonsuitBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>c45eecf33c024999987c391d6a0c7fed</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin/>
<mapReturnPluginConfig/>
<billNo/>
<domain/>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser/>
<createUserName/>
<approvalStatus>1</approvalStatus>
<publishTime>2025-07-01 16:48:38</publishTime>
<pathJoin>true</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout/>
<customUrl>/productionorder/list</customUrl>
<fixedUrl>/yonbip/mfg</fixedUrl>
<apiCode>c45eecf33c024999987c391d6a0c7fed</apiCode>
<tokenCheckType>0</tokenCheckType>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL/>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>5a1c7c07-8cf3-463a-8dfd-b6a91aa23a6d</updateUserId>
<updateUserName>昵称-刘稳舵</updateUserName>
<paramIsForce/>
<userIDPassthrough>true</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.yonbip-mm-mfpo</microServiceCode>
<applicationCode>yonbip-mm-mfpo</applicationCode>
<privacyCategory>1</privacyCategory>
<privacyLevel>1</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType>0</serviceType>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType/>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>false</arrayParam>
<fileSize/>
<cc>true</cc>
<paramTransferMode>2</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId/>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>1</paramExtRequest>
<paramExtResponse>1</paramExtResponse>
<paramExtInExtendKey>1</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene>1</integrationScene>
<apiType>3</apiType>
<paramMark>
<request>
<request>
<id>2303473535963627987</id>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<paramCode>pageIndex</paramCode>
<refCode>pageIndex</refCode>
<paramPosition>request</paramPosition>
<ytenantId>0</ytenantId>
<creator>5a1c7c07-8cf3-463a-8dfd-b6a91aa23a6d</creator>
<createTime>2025-07-01T08:46:55.000+00:00</createTime>
<modifier>5a1c7c07-8cf3-463a-8dfd-b6a91aa23a6d</modifier>
<pubts>2025-07-01T08:46:55.000+00:00</pubts>
<modifyTime>2025-07-01T08:46:55.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</request>
<request>
<id>2303473535963627988</id>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<paramCode>pageSize</paramCode>
<refCode>pageSize</refCode>
<paramPosition>request</paramPosition>
<ytenantId>0</ytenantId>
<creator>5a1c7c07-8cf3-463a-8dfd-b6a91aa23a6d</creator>
<createTime>2025-07-01T08:46:55.000+00:00</createTime>
<modifier>5a1c7c07-8cf3-463a-8dfd-b6a91aa23a6d</modifier>
<pubts>2025-07-01T08:46:55.000+00:00</pubts>
<modifyTime>2025-07-01T08:46:55.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</request>
<request>
<id>2303473535963627989</id>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<paramCode>lastUpdateTime</paramCode>
<refCode>simple.open_pubts_begin</refCode>
<paramPosition>request</paramPosition>
<ytenantId>0</ytenantId>
<creator>5a1c7c07-8cf3-463a-8dfd-b6a91aa23a6d</creator>
<createTime>2025-07-01T08:46:55.000+00:00</createTime>
<modifier>5a1c7c07-8cf3-463a-8dfd-b6a91aa23a6d</modifier>
<pubts>2025-07-01T08:46:55.000+00:00</pubts>
<modifyTime>2025-07-01T08:46:55.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</request>
<request>
<id>2303473535963627990</id>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<paramCode>thisSyncTime</paramCode>
<refCode>simple.open_pubts_end</refCode>
<paramPosition>request</paramPosition>
<ytenantId>0</ytenantId>
<creator>5a1c7c07-8cf3-463a-8dfd-b6a91aa23a6d</creator>
<createTime>2025-07-01T08:46:55.000+00:00</createTime>
<modifier>5a1c7c07-8cf3-463a-8dfd-b6a91aa23a6d</modifier>
<pubts>2025-07-01T08:46:55.000+00:00</pubts>
<modifyTime>2025-07-01T08:46:55.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</request>
</request>
<response>
<response>
<id>2303473535963627991</id>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<paramCode>respData</paramCode>
<refCode>data.recordList</refCode>
<paramPosition>response</paramPosition>
<ytenantId>0</ytenantId>
<creator>5a1c7c07-8cf3-463a-8dfd-b6a91aa23a6d</creator>
<createTime>2025-07-01T08:46:55.000+00:00</createTime>
<modifier>5a1c7c07-8cf3-463a-8dfd-b6a91aa23a6d</modifier>
<pubts>2025-07-01T08:46:55.000+00:00</pubts>
<modifyTime>2025-07-01T08:46:55.000+00:00</modifyTime>
<parentId/>
<paramOrder/>
<replicable/>
<order>0</order>
</response>
</response>
</paramMark>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>true</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>true</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>1</chargeStatus>
<beforeSpeed>60</beforeSpeed>
<afterSpeed>120</afterSpeed>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath>data.recordList</respDataRefPath>
<pubHistory>
<pubHistory>
<id>2303474437917769734</id>
<apiId>c45eecf33c024999987c391d6a0c7fed</apiId>
<apiName>生产订单列表查询</apiName>
<applyReason>UCMFG-259274：添加入参描述</applyReason>
<publishUserName/>
<version>20250701164838</version>
<operationTime>2025-07-01</operationTime>
<gmtCreate/>
<gmtUpdate/>
<changes>
<changes>
<changePosition>paramDTOS</changePosition>
<newList/>
<updateList>
<updateList>
<changeProperty>orderProduct.materialApplyStatus</changeProperty>
<oldValue>{"id":"2247791509766144058","name":"orderProduct.materialApplyStatus","apiId":"c45eecf33c024999987c391d6a0c7fed","parentId":"2247791509766144050","defParamId":"2247791509766144059","array":false,"paramDesc":"领料申请状态：0-未申领，1-部分申领，2-全部申领","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simple.orderProduct.materialApplyStatus","example":"0","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</oldValue>
<newValue>{"id":"2303473535963627554","name":"orderProduct.materialApplyStatus","apiId":"c45eecf33c024999987c391d6a0c7fed","parentId":"2303473535963627550","array":false,"paramDesc":"领料申请状态：0-未申领，1-部分申领，2-全部申领，3-无需申领","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simple.orderProduct.materialApplyStatus","example":"0","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>orderProduct.materialStatus</changeProperty>
<oldValue>{"id":"2247791509766144060","name":"orderProduct.materialStatus","apiId":"c45eecf33c024999987c391d6a0c7fed","parentId":"2247791509766144050","defParamId":"2247791509766144061","array":false,"paramDesc":"领料状态：0-未领料，1-部分领用，2-全部领用","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simple.orderProduct.materialStatus","example":"1","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</oldValue>
<newValue>{"id":"2303473535963627555","name":"orderProduct.materialStatus","apiId":"c45eecf33c024999987c391d6a0c7fed","parentId":"2303473535963627550","array":false,"paramDesc":"领料状态：0-未领料，1-部分领用，2-全部领用，3-无需领料，4-超额领料","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simple.orderProduct.materialStatus","example":"1","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
</updateList>
<deleteList/>
</changes>
</changes>
</pubHistory>
</pubHistory>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode>productionorder.po_production_order</domainAppCode>
<multiVersion>0</multiVersion>
<apiTag/>
</data>
</ResultVO>
This XML file does not appear to have any style information associated with it. The document tree is shown below.
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>2108770660671029249</id>
<name>用友YonBIP</name>
<type>integrateSys</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MFC</id>
<name>制造云</name>
<type>1</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MF</id>
<name>生产制造</name>
<type>2</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>PO</id>
<name>生产管理</name>
<type>3</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>productionorder.po_production_order</id>
<name>生产订单</name>
<type>4</type>
<sort>0</sort>
<enable>0</enable>
<children/>
<parentId/>
<productId/>
<code>productionorder.po_production_order</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>PO</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MF</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MFC</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>current_yonbip_default_sys</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<isOrigin>0</isOrigin>
<hasChildren>0</hasChildren>
<order>0</order>
</data>
</ResultVO>
This XML file does not appear to have any style information associated with it. The document tree is shown below.
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>23f313c7-499f-45bc-8609-462541f2565b</id>
<name>WW</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>委外交货日期</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>date</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:43:40</gmtCreate>
<gmtUpdate>2025-07-26 17:43:40</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>7e54ba52-fd35-458a-bd63-9cb7802d39c6</id>
<name>XS11</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类号test</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:43:40</gmtCreate>
<gmtUpdate>2025-07-26 17:43:40</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>fb3bfbb9-3da1-4b8b-8019-59a8adcf0e68</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:43:40</gmtCreate>
<gmtUpdate>2025-07-26 17:43:40</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:43:40</gmtCreate>
<gmtUpdate>2025-07-26 17:43:40</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
This XML file does not appear to have any style information associated with it. The document tree is shown below.
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>72113971-ae4c-4188-bc55-44b6173f4e0b</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:43:52</gmtCreate>
<gmtUpdate>2025-07-26 17:43:52</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>b946709d-f4d9-4a43-a551-f55beee7f3d5</id>
<name>XXX0111</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类项</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:43:52</gmtCreate>
<gmtUpdate>2025-07-26 17:43:52</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:43:52</gmtCreate>
<gmtUpdate>2025-07-26 17:43:52</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
This XML file does not appear to have any style information associated with it. The document tree is shown below.
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>51329822-0099-4762-ba26-a3fa29828ed4</id>
<name>U9003</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>U9生产订单号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:44:01</gmtCreate>
<gmtUpdate>2025-07-26 17:44:01</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>100</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:44:01</gmtCreate>
<gmtUpdate>2025-07-26 17:44:01</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
