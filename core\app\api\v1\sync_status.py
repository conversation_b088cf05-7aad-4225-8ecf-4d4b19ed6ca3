import structlog

from ...services.sync_status_manager import get_sync_status_manager

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步状态API端点
提供同步状态查询和管理功能
"""


logger = structlog.get_logger()
router = APIRouter()


@router.get("/sync-status/locks")
async def get_all_locks():
    """获取所有表锁状态"""
    try:
        sync_manager = get_sync_status_manager()
        locks = await sync_manager.get_all_locks_status()

        return {
            "success": True,
            "data": {"locks": locks, "total_locks": len(locks)},
            "message": "获取锁状态成功",
        }

    except Exception:
        logger.error("获取锁状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sync-status/table/{table_name}")
async def get_table_lock_info(table_name: str):
    """获取特定表的锁信息"""
    try:
        sync_manager = get_sync_status_manager()
        lock_info = await sync_manager.get_table_lock_info(table_name)

        if lock_info:
            return {
                "success": True,
                "data": lock_info,
                "message": f"表 {table_name} 锁信息获取成功",
            }
        else:
            return {
                "success": True,
                "data": None,
                "message": f"表 {table_name} 未被锁定",
            }

    except Exception:
        logger.error(f"获取表 {table_name} 锁信息失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sync-status/cleanup-expired")
async def cleanup_expired_locks():
    """清理过期锁"""
    try:
        sync_manager = get_sync_status_manager()
        await sync_manager.cleanup_expired_locks()

        return {"success": True, "message": "过期锁清理完成"}

    except Exception:
        logger.error("清理过期锁失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sync-status/health")
async def sync_status_health():
    """同步状态健康检查"""
    try:
        sync_manager = get_sync_status_manager()
        locks = await sync_manager.get_all_locks_status()

        # 检查是否有长时间运行的锁
        long_running_locks = [
            lock for lock in locks if lock["duration_seconds"] > 1800  # 30分钟
        ]

        return {
            "success": True,
            "data": {
                "total_locks": len(locks),
                "long_running_locks": len(long_running_locks),
                "health_status": (
                    "healthy" if len(long_running_locks) == 0 else "warning"
                ),
            },
            "message": "同步状态健康检查完成",
        }

    except Exception:
        logger.error("同步状态健康检查失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))
