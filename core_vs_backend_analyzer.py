#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 核心vs后端代码差异分析器
=====================================

详细比较core/和backend/目录，识别哪个是更新版本
"""

import hashlib
from pathlib import Path
from datetime import datetime
import logging


class CoreBackendAnalyzer:
    """Core vs Backend 差异分析器"""

    def __init__(self, workspace_root="d:\\OneDrive\\Desktop\\YS-API程序\\v3"):
        self.workspace_root = Path(workspace_root)
        self.logger = self._setup_logger()
        self.core_dir = self.workspace_root / "core"
        self.backend_dir = self.workspace_root / "backend"

    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger("CoreBackendAnalyzer")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def get_file_stats(self, directory):
        """获取目录文件统计"""
        stats = {
            "total_files": 0,
            "python_files": 0,
            "total_size": 0,
            "last_modified": None,
            "files": [],
        }

        if not directory.exists():
            return stats

        for file_path in directory.rglob("*"):
            if file_path.is_file():
                stats["total_files"] += 1
                file_size = file_path.stat().st_size
                stats["total_size"] += file_size
                modified_time = datetime.fromtimestamp(
                    file_path.stat().st_mtime
                )

                if (
                    stats["last_modified"] is None
                    or modified_time > stats["last_modified"]
                ):
                    stats["last_modified"] = modified_time

                if file_path.suffix == ".py":
                    stats["python_files"] += 1

                stats["files"].append(
                    {
                        "path": str(file_path.relative_to(directory)),
                        "size": file_size,
                        "modified": modified_time,
                    }
                )

        return stats

    def get_file_hash(self, file_path):
        """获取文件MD5哈希"""
        try:
            with open(file_path, "rb") as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return None

    def compare_directories(self):
        """比较两个目录结构"""
        core_stats = self.get_file_stats(self.core_dir)
        backend_stats = self.get_file_stats(self.backend_dir)

        # 找出共同文件和独有文件
        core_files = {f["path"] for f in core_stats["files"]}
        backend_files = {f["path"] for f in backend_stats["files"]}

        common_files = core_files & backend_files
        core_only = core_files - backend_files
        backend_only = backend_files - core_files

        return {
            "core_stats": core_stats,
            "backend_stats": backend_stats,
            "common_files": common_files,
            "core_only": core_only,
            "backend_only": backend_only,
        }

    def analyze_file_differences(self, common_files):
        """分析共同文件的差异"""
        differences = []

        for file_path in common_files:
            core_file = self.core_dir / file_path
            backend_file = self.backend_dir / file_path

            # 比较文件大小和修改时间
            core_stat = core_file.stat()
            backend_stat = backend_file.stat()

            core_hash = self.get_file_hash(core_file)
            backend_hash = self.get_file_hash(backend_file)

            if core_hash != backend_hash:
                differences.append(
                    {
                        "file": file_path,
                        "core_size": core_stat.st_size,
                        "backend_size": backend_stat.st_size,
                        "core_modified": datetime.fromtimestamp(
                            core_stat.st_mtime
                        ),
                        "backend_modified": datetime.fromtimestamp(
                            backend_stat.st_mtime
                        ),
                        "size_diff": core_stat.st_size - backend_stat.st_size,
                        "newer_version": (
                            "core"
                            if core_stat.st_mtime > backend_stat.st_mtime
                            else "backend"
                        ),
                    }
                )

        return differences

    def analyze_code_quality(self, directory):
        """分析代码质量特征"""
        quality_metrics = {
            "has_main_entry": False,
            "has_api_structure": False,
            "has_services": False,
            "has_config": False,
            "modern_patterns": 0,
            "import_quality": 0,
        }

        if not directory.exists():
            return quality_metrics

        for py_file in directory.rglob("*.py"):
            try:
                with open(py_file, "r", encoding="utf-8") as f:
                    content = f.read()

                # 检查结构特征
                if "main.py" in py_file.name:
                    quality_metrics["has_main_entry"] = True

                if "api" in str(py_file):
                    quality_metrics["has_api_structure"] = True

                if "services" in str(py_file):
                    quality_metrics["has_services"] = True

                if "config" in str(py_file):
                    quality_metrics["has_config"] = True

                # 检查现代化模式
                modern_patterns = [
                    "async def",
                    "await ",
                    "FastAPI",
                    "pydantic",
                    "structlog",
                    "typing",
                    "dataclass",
                    "@app.",
                    "uvicorn",
                ]

                for pattern in modern_patterns:
                    if pattern in content:
                        quality_metrics["modern_patterns"] += 1

                # 检查导入质量
                lines = content.split("\n")
                import_lines = [
                    line
                    for line in lines
                    if line.strip().startswith(("import ", "from "))
                ]
                quality_metrics["import_quality"] += len(import_lines)

            except Exception:
                continue

        return quality_metrics

    def generate_analysis_report(self):
        """生成分析报告"""
        self.logger.info("🔍 开始分析core和backend目录差异...")

        comparison = self.compare_directories()
        differences = self.analyze_file_differences(comparison["common_files"])

        core_quality = self.analyze_code_quality(self.core_dir)
        backend_quality = self.analyze_code_quality(self.backend_dir)

        # 生成报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = (
            self.workspace_root / f"core_vs_backend_analysis_{timestamp}.md"
        )

        with open(report_file, "w", encoding="utf-8") as f:
            f.write("# Core vs Backend 目录差异分析报告\n\n")
            f.write(
                f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            )

            # 基本统计
            f.write("## 📊 基本统计对比\n\n")
            f.write("| 指标 | Core目录 | Backend目录 | 差异 |\n")
            f.write("|------|----------|-------------|------|\n")

            core_stats = comparison["core_stats"]
            backend_stats = comparison["backend_stats"]

            f.write(
                f"| 总文件数 | {core_stats['total_files']} | {backend_stats['total_files']} | {core_stats['total_files'] - backend_stats['total_files']} |\n"
            )
            f.write(
                f"| Python文件 | {core_stats['python_files']} | {backend_stats['python_files']} | {core_stats['python_files'] - backend_stats['python_files']} |\n"
            )
            f.write(
                f"| 总大小(KB) | {core_stats['total_size']//1024} | {backend_stats['total_size']//1024} | {(core_stats['total_size'] - backend_stats['total_size'])//1024} |\n"
            )

            if core_stats["last_modified"]:
                f.write(
                    f"| 最后修改 | {core_stats['last_modified'].strftime('%Y-%m-%d %H:%M')} | {backend_stats['last_modified'].strftime('%Y-%m-%d %H:%M')} | - |\n"
                )

            f.write("\n")

            # 文件分布分析
            f.write("## 📁 文件分布分析\n\n")
            f.write(f"- **共同文件**: {len(comparison['common_files'])} 个\n")
            f.write(f"- **Core独有**: {len(comparison['core_only'])} 个\n")
            f.write(
                f"- **Backend独有**: {len(comparison['backend_only'])} 个\n\n"
            )

            # Core独有文件
            if comparison["core_only"]:
                f.write("### Core目录独有文件:\n")
                for file in sorted(comparison["core_only"])[:20]:
                    f.write(f"- `{file}`\n")
                if len(comparison["core_only"]) > 20:
                    f.write(
                        f"- *... 还有 {len(comparison['core_only']) - 20} 个文件*\n"
                    )
                f.write("\n")

            # Backend独有文件
            if comparison["backend_only"]:
                f.write("### Backend目录独有文件:\n")
                for file in sorted(comparison["backend_only"])[:20]:
                    f.write(f"- `{file}`\n")
                if len(comparison["backend_only"]) > 20:
                    f.write(
                        f"- *... 还有 {len(comparison['backend_only']) - 20} 个文件*\n"
                    )
                f.write("\n")

            # 文件差异分析
            if differences:
                f.write("## 🔄 文件内容差异 (Top 20)\n\n")
                f.write(
                    "| 文件 | Core大小 | Backend大小 | 更新版本 | 修改时间差异 |\n"
                )
                f.write(
                    "|------|----------|-------------|----------|----------------|\n"
                )

                sorted_diffs = sorted(
                    differences,
                    key=lambda x: abs(x["size_diff"]),
                    reverse=True,
                )
                for diff in sorted_diffs[:20]:
                    time_diff = abs(
                        (diff["core_modified"] - diff["backend_modified"]).days
                    )
                    f.write(
                        f"| `{diff['file']}` | {diff['core_size']} | {diff['backend_size']} | **{diff['newer_version']}** | {time_diff}天 |\n"
                    )
                f.write("\n")

            # 代码质量对比
            f.write("## 🏗️ 代码质量对比\n\n")
            f.write("| 质量指标 | Core | Backend | 优势 |\n")
            f.write("|----------|------|---------|------|\n")
            f.write(
                f"| 主入口点 | {'✅' if core_quality['has_main_entry'] else '❌'} | {'✅' if backend_quality['has_main_entry'] else '❌'} | {'Core' if core_quality['has_main_entry'] and not backend_quality['has_main_entry'] else 'Backend' if backend_quality['has_main_entry'] and not core_quality['has_main_entry'] else '相同'} |\n"
            )
            f.write(
                f"| API结构 | {'✅' if core_quality['has_api_structure'] else '❌'} | {'✅' if backend_quality['has_api_structure'] else '❌'} | {'Core' if core_quality['has_api_structure'] and not backend_quality['has_api_structure'] else 'Backend' if backend_quality['has_api_structure'] and not core_quality['has_api_structure'] else '相同'} |\n"
            )
            f.write(
                f"| 服务层 | {'✅' if core_quality['has_services'] else '❌'} | {'✅' if backend_quality['has_services'] else '❌'} | {'Core' if core_quality['has_services'] and not backend_quality['has_services'] else 'Backend' if backend_quality['has_services'] and not core_quality['has_services'] else '相同'} |\n"
            )
            f.write(
                f"| 现代化模式 | {core_quality['modern_patterns']} | {backend_quality['modern_patterns']} | {'Core' if core_quality['modern_patterns'] > backend_quality['modern_patterns'] else 'Backend' if backend_quality['modern_patterns'] > core_quality['modern_patterns'] else '相同'} |\n"
            )
            f.write(
                f"| 导入复杂度 | {core_quality['import_quality']} | {backend_quality['import_quality']} | {'Core' if core_quality['import_quality'] > backend_quality['import_quality'] else 'Backend' if backend_quality['import_quality'] > core_quality['import_quality'] else '相同'} |\n"
            )

            # 推荐结论
            f.write("\n## 🎯 分析结论与推荐\n\n")

            # 判断哪个更新
            core_advantages = 0
            backend_advantages = 0

            if core_stats["last_modified"] and backend_stats["last_modified"]:
                if (
                    core_stats["last_modified"]
                    > backend_stats["last_modified"]
                ):
                    core_advantages += 1
                    f.write("- 📅 **修改时间**: Core目录更新 (优势: Core)\n")
                else:
                    backend_advantages += 1
                    f.write(
                        "- 📅 **修改时间**: Backend目录更新 (优势: Backend)\n"
                    )

            if core_stats["total_files"] > backend_stats["total_files"]:
                core_advantages += 1
                f.write("- 📁 **文件数量**: Core目录更多 (优势: Core)\n")
            elif backend_stats["total_files"] > core_stats["total_files"]:
                backend_advantages += 1
                f.write("- 📁 **文件数量**: Backend目录更多 (优势: Backend)\n")

            if (
                core_quality["modern_patterns"]
                > backend_quality["modern_patterns"]
            ):
                core_advantages += 1
                f.write("- 🔧 **现代化程度**: Core更现代化 (优势: Core)\n")
            elif (
                backend_quality["modern_patterns"]
                > core_quality["modern_patterns"]
            ):
                backend_advantages += 1
                f.write(
                    "- 🔧 **现代化程度**: Backend更现代化 (优势: Backend)\n"
                )

            f.write(f"\n### 🏆 总体推荐\n\n")
            if core_advantages > backend_advantages:
                f.write("**推荐保留: Core目录** 🎯\n")
                f.write("- Core目录在多个维度表现更好\n")
                f.write("- 建议删除Backend目录，以Core为主体进行重构\n")
            elif backend_advantages > core_advantages:
                f.write("**推荐保留: Backend目录** 🎯\n")
                f.write("- Backend目录在多个维度表现更好\n")
                f.write("- 建议删除Core目录，以Backend为主体进行重构\n")
            else:
                f.write("**需要详细分析** ⚖️\n")
                f.write("- 两个目录各有优势，需要进一步分析\n")
                f.write("- 建议手动检查关键文件的具体实现\n")

        return report_file, {
            "core_advantages": core_advantages,
            "backend_advantages": backend_advantages,
            "comparison": comparison,
            "differences": differences,
            "core_quality": core_quality,
            "backend_quality": backend_quality,
        }

    def run_analysis(self):
        """运行分析"""
        self.logger.info("🚀 开始Core vs Backend差异分析...")

        report_file, analysis_data = self.generate_analysis_report()

        self.logger.info(f"✅ 分析完成: {report_file}")

        # 输出关键结论
        if (
            analysis_data["core_advantages"]
            > analysis_data["backend_advantages"]
        ):
            print("\n🎯 **推荐结论**: 保留Core目录，删除Backend目录")
            print("📊 Core目录在多个维度表现更好")
        elif (
            analysis_data["backend_advantages"]
            > analysis_data["core_advantages"]
        ):
            print("\n🎯 **推荐结论**: 保留Backend目录，删除Core目录")
            print("📊 Backend目录在多个维度表现更好")
        else:
            print("\n⚖️ **需要进一步分析**: 两个目录各有优势")

        comparison = analysis_data["comparison"]
        print(f"\n📈 统计对比:")
        print(
            f"- Core: {comparison['core_stats']['total_files']}个文件, {comparison['core_stats']['total_size']//1024}KB"
        )
        print(
            f"- Backend: {comparison['backend_stats']['total_files']}个文件, {comparison['backend_stats']['total_size']//1024}KB"
        )
        print(f"- 共同文件: {len(comparison['common_files'])}个")
        print(f"- Core独有: {len(comparison['core_only'])}个")
        print(f"- Backend独有: {len(comparison['backend_only'])}个")

        return report_file


def main():
    """主函数"""
    analyzer = CoreBackendAnalyzer()
    report_file = analyzer.run_analysis()

    print(f"\n📋 详细分析报告: {report_file}")


if __name__ == "__main__":
    main()
