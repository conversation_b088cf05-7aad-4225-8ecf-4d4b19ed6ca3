import json
import logging
import os
import sqlite3
import sys
from datetime import datetime
from pathlib import Path

import psutil

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速生产环境验证
Quick Production Environment Validation
"""


def quick_health_check():
    """快速健康检查"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    logger.info("🏥 YS-API V3.0 快速健康检查")
    logger.info("=" * 50)

    results = {
        'timestamp': datetime.now().isoformat(),
        'checks': {},
        'score': 0,
        'issues': [],
    }

    # 1. Python环境检查
    logger.info("📋 1. Python环境检查")
    try:
        python_version = sys.version
        logger.info(f"   ✅ Python版本: {python_version.split()[0]}")
        results['checks']['python'] = {
            'status': 'ok',
            'version': python_version.split()[0],
        }
        results['score'] += 20
    except Exception:
        logger.error(f"   ❌ Python检查失败: {e}")
        results['checks']['python'] = {'status': 'error', 'error': str(e)}
        results['issues'].append("Python环境异常")

    # 2. 文件系统检查
    # DEBUG: print("\n📁 2. 文件系统检查")
    critical_paths = ["backend/", "config/", "frontend/", "docs/"]

    file_score = 0
    for path in critical_paths:
        if Path(path).exists():
            # DEBUG: print(f"   ✅ {path}")
            file_score += 5
        else:
            # DEBUG: print(f"   ❌ {path} (缺失)")
            results['issues'].append(f"缺失关键目录: {path}")

    results['checks']['filesystem'] = {
        'score': file_score,
        'max_score': len(critical_paths) * 5,
    }
    results['score'] += file_score

    # 3. 数据库检查
    logger.info("💾 3. 数据库检查")
    db_path = Path("backend/ysapi.db")
    if db_path.exists():
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            conn.close()

            logger.info(f"   ✅ 数据库连接正常")
            logger.info(f"   📊 表数量: {len(tables)}")
            results['checks']['database'] =
            {'status': 'ok',
             'tables': len(tables)
             }
            results['score'] += 20
        except Exception:
            logger.error(f"   ❌ 数据库连接失败: {e}")
            results['checks']['database'] =
            {'status': 'error',
             'error': str(e)
             }
            results['issues'].append("数据库连接异常")
    else:
        logger.warning("   ⚠️ 数据库文件不存在")
        results['checks']['database'] = {'status': 'missing'}
        results['issues'].append("数据库文件缺失")

    # 4. 配置文件检查
    logger.info("⚙️ 4. 配置文件检查")
    config_files =
    ["backend/.env",
     "config/modules.json",
     "backend/config.ini"
     ]

    config_score = 0
    for config_file in config_files:
        if Path(config_file).exists():
            logger.info(f"   ✅ {config_file}")
            config_score += 10
        else:
            logger.info(f"   ⚠️ {config_file} (可选)")

    results['checks']['configuration'] = {
        'score': config_score,
        'max_score': len(config_files) * 10,
    }
    results['score'] += config_score

    # 5. 系统资源检查
    logger.info("🖥️ 5. 系统资源检查")
    try:

        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/' if os.name != 'nt' else 'C:')

        logger.info(f"   💻 CPU使用率: {cpu_percent:.1f}%")
        logger.info(f"   🧠 内存使用率: {memory.percent:.1f}%")
        logger.info(f"   💽 磁盘使用率: {(disk.used/disk.total*100):.1f}%")

        resource_score = 10
        if cpu_percent > 80:
            results['issues'].append("CPU使用率过高")
            resource_score -= 3
        if memory.percent > 85:
            results['issues'].append("内存使用率过高")
            resource_score -= 3
        if (disk.used / disk.total * 100) > 90:
            results['issues'].append("磁盘空间不足")
            resource_score -= 4

        results['checks']['resources'] = {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'disk_percent': disk.used / disk.total * 100,
            'score': resource_score,
        }
        results['score'] += resource_score

    except ImportError:
        logger.warning("   ⚠️ psutil库未安装，跳过资源检查")
        results['checks']['resources'] = {
            'status': 'skipped',
            'reason': 'psutil not available',
        }
    except Exception:
        logger.error(f"   ❌ 资源检查失败: {e}")
        results['checks']['resources'] = {'status': 'error', 'error': str(e)}

    # 6. 安全检查
    logger.info("🛡️ 6. 安全检查")
    security_score = 20

    # 检查敏感文件
    if Path("backend/.env").exists():
        logger.info("   ✅ 环境变量文件存在")
    else:
        logger.warning("   ⚠️ 环境变量文件缺失")
        security_score -= 5
        results['issues'].append("缺少环境变量配置")

    # 检查文件权限 (仅Unix系统)
    if os.name != 'nt':
        sensitive_files = ["backend/.env", "backend/config.ini"]
        for file_path in sensitive_files:
            if Path(file_path).exists():
                stat = os.stat(file_path)
                if stat.st_mode & 0o077:
                    logger.warning(f"   ⚠️ {file_path} 权限过宽")
                    security_score -= 3
                    results['issues'].append(f"文件权限不安全: {file_path}")
                else:
                    logger.info(f"   ✅ {file_path} 权限安全")

    results['checks']['security'] = {'score': security_score, 'max_score': 20}
    results['score'] += security_score

    # 生成总结
    logger.info("=" * 50)
    logger.info("📊 健康检查总结")

    max_score = 100
    percentage = (results['score'] / max_score) * 100

    if percentage >= 90:
        grade = "A+ 优秀"
        status = "🟢 生产就绪"
    elif percentage >= 80:
        grade = "A 良好"
        status = "🟡 基本就绪"
    elif percentage >= 70:
        grade = "B 合格"
        status = "🟡 需要改进"
    elif percentage >= 60:
        grade = "C 勉强"
        status = "🟠 需要修复"
    else:
        grade = "D 不合格"
        status = "🔴 不可用"

    logger.info(f"📈 总体评分: {results['score']}/{max_score} ({percentage:.1f}%)")
    logger.info(f"🏆 等级评定: {grade}")
    logger.info(f"🎯 状态评估: {status}")

    if results['issues']:
        logger.warning(f"⚠️ 发现问题 ({len(results['issues'])} 个):")
        for i, issue in enumerate(results['issues'], 1):
            logger.warning(f"   {i}. {issue}")
    else:
        logger.info("✅ 未发现重大问题")

    # 保存结果
    report_file = f"quick_health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    logger.info(f"📋 详细报告已保存: {report_file}")

    return results['score'], grade, status


def generate_recommendations(score, issues):
    """生成改进建议"""
    # DEBUG: print("\n💡 改进建议:")

    recommendations = []

    if score < 60:
        recommendations.extend(
            ["🔧 修复所有发现的问题", "📚 完善项目文档和配置", "🧪 增加测试覆盖率"]
        )
    elif score < 80:
        recommendations.extend(
            ["⚡ 优化系统性能", "🔒 加强安全配置", "📊 完善监控体系"]
        )
    else:
        recommendations.extend(
            ["🚀 系统状态良好，可投入生产", "📈 建立持续监控", "🔄 定期健康检查"]
        )

    # 基于具体问题的建议
    for issue in issues:
        if "数据库" in issue:
            recommendations.append("💾 检查数据库配置和连接")
        elif "环境变量" in issue:
            recommendations.append("⚙️ 配置环境变量文件")
        elif "权限" in issue:
            recommendations.append("🔐 修复文件权限问题")
        elif "CPU" in issue or "内存" in issue:
            recommendations.append("🖥️ 优化系统资源使用")

    for i, rec in enumerate(recommendations, 1):
        logging.getLogger(__name__).info(f"   {i}. {rec}")


def main():
    """主函数"""
    logger = logging.getLogger(__name__)
    try:
        score, grade, status = quick_health_check()

        # 根据结果生成建议
        issues = []  # 这里应该从results中获取，为简化暂时为空
        generate_recommendations(score, issues)

        logger.info(f"🎉 检查完成! 最终评分: {score}/100 ({grade})")

        # 返回退出码
        if score >= 80:
            sys.exit(0)  # 优秀
        elif score >= 60:
            sys.exit(1)  # 需要改进
        else:
            sys.exit(2)  # 不合格

    except Exception:
        logger.error(f"❌ 检查过程中发生错误: {e}")
        sys.exit(3)


if __name__ == "__main__":
    main()
