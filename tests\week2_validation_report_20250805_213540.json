{"start_time": "2025-08-05T21:35:40.421456", "tests": {"standard_request_builder": {"component": "StandardRequestBuilder", "start_time": "2025-08-05T21:35:40.421456", "tests": {"template_availability": {"total_modules": 15, "available_templates": 15, "missing_templates": [], "template_details": {"material_outbound": {"available": true, "parameter_count": 4, "has_api_endpoint": true}, "purchase_order": {"available": true, "parameter_count": 4, "has_api_endpoint": true}, "purchase_inbound": {"available": true, "parameter_count": 4, "has_api_endpoint": true}, "product_inbound": {"available": true, "parameter_count": 4, "has_api_endpoint": true}, "purchase_request": {"available": true, "parameter_count": 4, "has_api_endpoint": true}, "production_order": {"available": true, "parameter_count": 6, "has_api_endpoint": true}, "subcontract_order": {"available": true, "parameter_count": 4, "has_api_endpoint": true}, "subcontract_inbound": {"available": true, "parameter_count": 4, "has_api_endpoint": true}, "subcontract_request": {"available": true, "parameter_count": 4, "has_api_endpoint": true}, "material_master": {"available": true, "parameter_count": 3, "has_api_endpoint": true}, "inventory_report": {"available": true, "parameter_count": 3, "has_api_endpoint": true}, "sales_outbound": {"available": true, "parameter_count": 4, "has_api_endpoint": true}, "sales_order": {"available": true, "parameter_count": 4, "has_api_endpoint": true}, "demand_plan": {"available": true, "parameter_count": 6, "has_api_endpoint": true}, "business_log": {"available": true, "parameter_count": 4, "has_api_endpoint": true}}, "coverage_rate": 1.0, "passed": true}, "parameter_validation": {"validation_tests": 5, "passed_validations": 2, "test_cases": {"case_1": {"module": "material_outbound", "params": {"pageSize": 10, "pageNum": 1}, "expected_pass": true, "actual_pass": false, "passed": false, "error": "参数验证失败: 缺少必需参数: orgId"}, "case_2": {"module": "material_outbound", "params": {"pageSize": 0, "pageNum": 1}, "expected_pass": false, "actual_pass": false, "passed": true, "error": "参数验证失败: 参数 pageSize 不满足规则: 1 <= pageSize <= 1000; 缺少必需参数: orgId"}, "case_3": {"module": "purchase_order", "params": {"pageSize": 50, "pageNum": 2, "startDate": "2024-01-01"}, "expected_pass": true, "actual_pass": false, "passed": false, "error": "参数验证失败: 缺少必需参数: orgId"}, "case_4": {"module": "inventory_report", "params": {"materialCode": "TEST001"}, "expected_pass": true, "actual_pass": false, "passed": false, "error": "参数验证失败: 缺少必需参数: org"}, "case_5": {"module": "invalid_module", "params": {"pageSize": 10}, "expected_pass": false, "actual_pass": false, "passed": true, "error": "未知模块: invalid_module"}}, "accuracy_rate": 0.4, "passed": false}, "request_building": {"build_tests": 3, "successful_builds": 0, "build_details": {"material_outbound": {"success": false, "error": "参数验证失败: 缺少必需参数: orgId"}, "purchase_order": {"success": false, "error": "参数验证失败: 缺少必需参数: orgId"}, "inventory_report": {"success": false, "error": "参数验证失败: 缺少必需参数: org"}}, "success_rate": 0.0, "passed": false}}, "success_rate": 0.3333333333333333, "passed": false}, "enhanced_authenticator": {"component": "EnhancedAuthenticator", "start_time": "2025-08-05T21:35:40.425456", "tests": {"signature_generation": {"generation_tests": 3, "successful_generations": 3, "signature_details": {"test_1": {"success": true, "timestamp": "1754400940", "signature_length": 44, "is_base64": true}, "test_2": {"success": true, "timestamp": "1754400840", "signature_length": 44, "is_base64": true}, "test_3": {"success": true, "timestamp": "1754401040", "signature_length": 44, "is_base64": true}}, "success_rate": 1.0, "passed": true}, "url_building": {"url_tests": 1, "successful_builds": 1, "url_details": {"success": true, "url_length": 169, "contains_required_params": true}, "success_rate": 1.0, "passed": true}, "auth_flow": {"overall_success": true, "timestamp_generation": true, "signature_generation": true, "url_construction": true, "errors": [], "passed": true}}, "success_rate": 1.0, "passed": true}, "response_parser": {"component": "EnhancedResponseParser", "start_time": "2025-08-05T21:35:40.427456", "tests": {"response_parsing": {"parsing_tests": 3, "successful_parses": 3, "parse_details": {"valid_material_outbound": {"expected_success": true, "actual_success": true, "passed": true, "data_count": 2, "parsing_time": 0.0, "errors": []}, "invalid_json": {"expected_success": false, "actual_success": false, "passed": true, "data_count": 0, "parsing_time": 0.001, "errors": ["JSON解析失败"]}, "error_response": {"expected_success": false, "actual_success": false, "passed": true, "data_count": 0, "parsing_time": 0.0, "errors": ["响应验证失败: 字段值不允许: code, 允许值: ['00000'], 必填字段缺失: data, 必填字段缺失: data.list"]}}, "success_rate": 1.0, "passed": true}, "data_validation": {"validation_tests": 1, "successful_validations": 1, "success_rate": 1.0, "passed": true, "note": "数据验证功能已集成在解析过程中"}}, "success_rate": 1.0, "passed": true}, "integrated_client": {"component": "IntegratedYSAPIClient", "start_time": "2025-08-05T21:35:40.428456", "tests": {"flow_validation": {"authentication": true, "request_building": true, "response_parsing": true, "overall_health": true, "errors": ["流程验证异常: 'EnhancedErrorHandler' object has no attribute 'get_error_statistics'"], "passed": true}, "statistics": {"stats_complete": false, "error": "'EnhancedErrorHandler' object has no attribute 'get_error_statistics'", "passed": false}}, "success_rate": 0.5, "passed": false}}, "summary": {"total_components": 4, "passed_components": 2, "success_rate": 0.5, "overall_passed": false, "accuracy_target_met": false, "end_time": "2025-08-05T21:35:40.430456"}, "accuracy_metrics": {"target_accuracy": 1.0, "component_accuracies": {"standard_request_builder": 0.3333333333333333, "enhanced_authenticator": 1.0, "response_parser": 1.0, "integrated_client": 0.5}, "overall_accuracy": 0.7083333333333333, "meets_target": false}}