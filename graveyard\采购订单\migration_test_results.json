{"timestamp": "2025-08-06T01:30:25.886300", "module": "采购订单列表", "tests": {"legacy_functional": {"passed": true, "message": "Legacy API正常"}, "new_functional": {"passed": true, "message": "新API接口创建成功"}, "proxy_routing": {"passed": false, "message": "需要启动代理服务"}, "data_consistency": {"passed": false, "message": "需要实际数据验证"}, "performance": {"passed": true, "message": "新接口性能符合要求"}}, "overall_passed": false, "next_steps": ["启动代理服务进行路由测试", "导入测试数据验证一致性", "配置流量切换策略"]}