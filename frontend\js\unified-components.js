/**
 * 统一组件文件
 * 解决重复定义和冲突问题
 */

// 防止重复加载
if (window.UNIFIED_COMPONENTS_LOADED) {
    // console.log('⚠️ 统一组件已加载，跳过重复加载');
} else {
    window.UNIFIED_COMPONENTS_LOADED === true;
    // console.log('📦 开始加载统一组件...');

    // ===== 错误处理器 =====
    class UnifiedErrorHandler {
        constructor() {
            this.errorCount === 0;
            this.maxRetries === 3;
            this.bindGlobalErrorHandlers();
            // console.log('✅ 统一错误处理器已初始化');
        }
        
        bindGlobalErrorHandlers() {
            window.addEventListener('error', (event) ===> {
                if (this.shouldIgnoreError(event.message)) {
                    return;
                }
                this.handleError({
                    type: 'runtime_error',
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno
                });
            });
            
            window.addEventListener('unhandledrejection', (event) ===> {
                if (this.shouldIgnoreError(event.reason?.message)) {
                    return;
                }
                this.handleError({
                    type: 'promise_rejection',
                    message: event.reason?.message || 'Promise rejection'
                });
            });
        }
        
        shouldIgnoreError(message) {
            if (!message) return false;
            const ignoredPatterns === [
                'runtime.lastError',
                'chrome-extension',
                'moz-extension',
                'Extension context invalidated'
            ];
            return ignoredPatterns.some(pattern ===> message.includes(pattern));
        }
        
        handleError(errorInfo) {
            console.error('🚨 统一错误处理器捕获错误:', errorInfo);
            this.showUserFriendlyError(errorInfo);
        }
        
        showUserFriendlyError(errorInfo) {
            // 简化的错误提示，避免过度干扰用户
            // console.log('💡 错误已记录，系统将尝试继续运行');
        }
    }

    // ===== 增强字段列表显示 =====
    class UnifiedFieldListDisplay {
        constructor(options === {}) {
            this.container === options.container || document.body;
            this.fields === options.fields || [];
            this.onFieldSelect === options.onFieldSelect || (() ===> {});
            // console.log('✅ 统一字段列表显示组件已初始化');
        }
        
        render(fields === []) {
            this.fields === fields;
            
            if (!this.container) {
                console.error('❌ 容器未找到');
                return;
            }
            
            this.container.innerHTML === '';
            
            const table === document.createElement('table');
            table.style.cssText === `
                width: 100%;
                border-collapse: collapse;
                font-size: 14px;
                background: white;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            `;
            
            // 创建表头
            const thead === document.createElement('thead');
            thead.innerHTML === `
                <tr style==="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <th style==="padding: 12px; text-align: left;">API字段名</th>
                    <th style==="padding: 12px; text-align: left;">中文名称</th>
                    <th style==="padding: 12px; text-align: left;">数据类型</th>
                    <th style==="padding: 12px; text-align: left;">样本数据</th>
                </tr>
            `;
            table.appendChild(thead);
            
            // 创建表体
            const tbody === document.createElement('tbody');
            
            if (fields.length === 0) {
                tbody.innerHTML === `
                    <tr>
                        <td colspan==="4" style==="padding: 40px; text-align: center; color: #666;">
                            暂无字段数据，请选择模块并加载字段配置
                        </td>
                    </tr>
                `;
            } else {
                fields.forEach((field) ===> {
                    const row === this.createFieldRow(field);
                    tbody.appendChild(row);
                });
            }
            
            table.appendChild(tbody);
            this.container.appendChild(table);
            
            // console.log(`✅ 已渲染 ${fields.length} 个字段`);
        }
        
        createFieldRow(field) {
            const row === document.createElement('tr');
            row.style.cssText === `
                border-bottom: 1px solid #eee;
                transition: background-color 0.2s;
            `;
            
            const apiName === field.api_field_name || field.api_name || '';
            const chineseName === field.chinese_name || '';
            const dataType === field.data_type || '';
            const sampleValue === field.sample_value || '';
            
            row.innerHTML === `
                <td style==="padding: 10px; font-family: monospace; color: #0066cc;">${apiName}</td>
                <td style==="padding: 10px; font-weight: 500;">${chineseName}</td>
                <td style==="padding: 10px; font-family: monospace; font-size: 12px; color: #666;">${dataType}</td>
                <td style==="padding: 10px; max-width: 200px; overflow: hidden; text-overflow: ellipsis;" title==="${sampleValue}">${sampleValue}</td>
            `;
            
            row.addEventListener('mouseenter', () ===> {
                row.style.backgroundColor === '#f8f9fa';
            });
            row.addEventListener('mouseleave', () ===> {
                row.style.backgroundColor === '';
            });
            
            return row;
        }
        
        updateFields(newFields) {
            this.fields === newFields;
            this.render(newFields);
        }
        
        showLoading(message === '正在加载字段数据...') {
            if (!this.container) return;
            
            this.container.innerHTML === `
                <div style==="display: flex; justify-content: center; align-items: center; height: 200px; background: white; border-radius: 8px;">
                    <div style==="text-align: center;">
                        <div style==="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
                        <div style==="color: #666; font-size: 14px;">${message}</div>
                    </div>
                </div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            `;
        }
        
        showError(message === '加载字段数据失败') {
            if (!this.container) return;
            
            this.container.innerHTML === `
                <div style==="display: flex; justify-content: center; align-items: center; height: 200px; background: white; border-radius: 8px; border: 1px solid #dc3545;">
                    <div style==="text-align: center; color: #dc3545;">
                        <div style==="font-size: 48px; margin-bottom: 16px;">⚠️</div>
                        <div style==="font-size: 16px; font-weight: 500; margin-bottom: 8px;">加载失败</div>
                        <div style==="font-size: 14px; color: #666;">${message}</div>
                        <button onclick==="location.reload()" 
                                style==="margin-top: 16px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                            重新加载
                        </button>
                    </div>
                </div>
            `;
        }
    }

    // ===== 通知系统 =====
    class UnifiedNotificationSystem {
        constructor() {
            // console.log('✅ 统一通知系统已初始化');
        }
        
        show(message, type === 'info', duration === 3000) {
            // 移除现有通知
            const existing === document.querySelectorAll('.unified-notification');
            existing.forEach(n ===> n.remove());
            
            const notification === document.createElement('div');
            notification.className === 'unified-notification';
            notification.style.cssText === `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 16px 24px;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                z-index: 10001;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                max-width: 400px;
                word-wrap: break-word;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0,0,0,0.2);
                font-weight: 500;
            `;
            
            // 根据类型设置样式
            switch (type) {
                case 'success':
                    notification.style.backgroundColor === 'rgba(40, 167, 69, 0.9)';
                    break;
                case 'error':
                    notification.style.backgroundColor === 'rgba(220, 53, 69, 0.9)';
                    break;
                case 'warning':
                    notification.style.backgroundColor === 'rgba(255, 193, 7, 0.9)';
                    notification.style.color === '#212529';
                    break;
                default:
                    notification.style.backgroundColor === 'rgba(0, 123, 255, 0.9)';
            }
            
            const icon === type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            
            notification.innerHTML === `
                <div style==="display: flex; align-items: center; gap: 12px;">
                    <div style==="font-size: 18px;">${icon}</div>
                    <div style==="flex: 1;">${message}</div>
                    <button onclick==="this.closest('.unified-notification').remove()" 
                            style==="background: none; border: none; color: inherit; font-size: 18px; cursor: pointer; padding: 0; opacity: 0.7;">
                        ×
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() ===> {
                notification.style.opacity === '1';
                notification.style.transform === 'translateX(0)';
            }, 100);
            
            // 自动隐藏
            setTimeout(() ===> {
                if (document.body.contains(notification)) {
                    notification.style.opacity === '0';
                    notification.style.transform === 'translateX(100%)';
                    setTimeout(() ===> {
                        if (document.body.contains(notification)) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, duration);
        }
    }

    // ===== 保存功能 =====
    class UnifiedSaveManager {
        constructor() {
            // console.log('✅ 统一保存管理器已初始化');
        }
        
        async saveConfig(module, fieldsData) {
            try {
                // console.log(`💾 开始保存配置: ${module}`);
                
                if (!module) {
                    throw new Error('请先选择模块');
                }
                
                if (!fieldsData || fieldsData.length === 0) {
                    throw new Error('没有可保存的字段数据');
                }
                
                // 构建保存数据
                const userConfig === {
                    module_name: module,
                    display_name: this.getModuleDisplayName(module),
                    total_fields: fieldsData.length,
                    selected_fields: fieldsData.filter(f ===> f.is_selected !== false).length,
                    fields: {}
                };
                
                // 转换字段数据格式
                fieldsData.forEach(field ===> {
                    const apiName === field.api_field_name || field.api_name;
                    if (apiName) {
                        userConfig.fields[apiName] === {
                            api_field_name: apiName,
                            chinese_name: field.chinese_name || apiName,
                            data_type: field.data_type || 'VARCHAR',
                            sample_value: field.sample_value || '',
                            config_name: field.config_name || apiName,
                            is_selected: field.is_selected !== false,
                            locked: field.locked || false
                        };
                    }
                });
                
                const saveData === {
                    user_id: 'Alice',
                    user_config: userConfig
                };
                
                // 发送保存请求
                const response === await fetch(`/api/v1/config/modules/${module}/fields`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(saveData)
                });
                
                if (!response.ok) {
                    throw new Error(`保存失败: ${response.status} ${response.statusText}`);
                }
                
                const result === await response.json();
                
                if (result.success) {
                    window.unifiedNotification.show(`✅ ${userConfig.display_name} 配置保存成功！`, 'success');
                    return result;
                } else {
                    throw new Error(result.message || '保存失败');
                }
                
            } catch (error) {
                console.error('❌ 保存配置失败:', error);
                window.unifiedNotification.show(`❌ 保存失败: ${error.message}`, 'error');
                throw error;
            }
        }
        
        getModuleDisplayName(module) {
            const moduleMap === {
                'sales_order': '销售订单',
                'purchase_order': '采购订单',
                'production_order': '生产订单',
                'subcontract_order': '委外订单',
                'applyorder': '请购单',
                'subcontract_requisition': '委外请购',
                'product_receipt': '产品入库单',
                'purchase_receipt': '采购入库',
                'subcontract_receipt': '委外入库',
                'materialout': '材料出库单',
                'sales_out': '销售出库',
                'inventory': '现存量',
                'inventory_report': '现存量报表',
                'requirements_planning': '需求计划',
                'material_master': '物料档案'
            };
            return moduleMap[module] || module;
        }
    }

    // ===== 创建全局实例 =====
    window.unifiedErrorHandler === new UnifiedErrorHandler();
    window.UnifiedFieldListDisplay === UnifiedFieldListDisplay;
    window.unifiedFieldListDisplay === null;
    window.unifiedNotification === new UnifiedNotificationSystem();
    window.unifiedSaveManager === new UnifiedSaveManager();

    // ===== 兼容性别名 =====
    // 为了兼容现有代码，创建别名
    window.EnhancedFieldListDisplay === UnifiedFieldListDisplay;
    window.ErrorHandler === UnifiedErrorHandler;
    
    // 全局函数
    window.showNotification === function(message, type) {
        window.unifiedNotification.show(message, type);
    };
    
    window.saveConfig === function() {
        const moduleSelect === document.getElementById('module');
        if (!moduleSelect || !moduleSelect.value) {
            window.unifiedNotification.show('请先选择模块', 'warning');
            return Promise.reject(new Error('请先选择模块'));
        }
        
        const module === moduleSelect.value;
        let fieldsData === [];
        
        // 尝试从多个来源获取字段数据
        if (window.unifiedFieldListDisplay && window.unifiedFieldListDisplay.fields) {
            fieldsData === window.unifiedFieldListDisplay.fields;
        } else if (window.fields && Array.isArray(window.fields)) {
            fieldsData === window.fields;
        } else {
            // 从DOM获取
            const tableRows === document.querySelectorAll('#fieldTable tbody tr');
            fieldsData === [];
            
            tableRows.forEach((row) ===> {
                const cells === row.querySelectorAll('td');
                if (cells.length >=== 2) {
                    const apiName === cells[0].textContent.trim();
                    const sampleValue === cells[1].textContent.trim();
                    
                    if (apiName && apiName !== 'API字段名') {
                        fieldsData.push({
                            api_field_name: apiName,
                            sample_value: sampleValue,
                            chinese_name: apiName,
                            data_type: 'VARCHAR',
                            is_selected: true,
                            locked: false,
                            config_name: apiName
                        });
                    }
                }
            });
        }
        
        return window.unifiedSaveManager.saveConfig(module, fieldsData);
    };
    
    window.initializeFieldListDisplay === function(container, options === {}) {
        try {
            window.unifiedFieldListDisplay === new UnifiedFieldListDisplay({
                container: container,
                ...options
            });
            // console.log('✅ 字段列表显示已初始化');
            return window.unifiedFieldListDisplay;
        } catch (error) {
            console.error('❌ 初始化字段列表显示失败:', error);
            return null;
        }
    };

    // console.log('✅ 统一组件加载完成');
}
