import json
import os
import re
from datetime import datetime

import structlog

"""
YS-API V3.0 智能字段映射器
整合API字段提取和MD文档解析结果，生成完整的字段映射配置
"""


# from .field_extractor import FieldExtractor, FieldMetadata
# from .md_parser import MDParser, MDFieldInfo


# 临时定义缺失的类
@dataclass
class FieldMetadata:
    field_name: str
    data_type: str = ""
    sample_value: str = ""


@dataclass
class MDFieldInfo:
    field_name: str
    chinese_name: str = ""
    data_type: str = ""


logger = structlog.get_logger()


@dataclass
class FieldConfig:
    """字段配置"""

    api_field_name: str
    chinese_name: str
    data_type: str
    is_selected: bool
    is_required: bool
    is_primary_key: bool
    field_order: int
    importance_level: str
    description: str
    sample_value: str
    value_mapping: Dict = None
    validation_rules: Dict = None
    source: str = "auto"  # auto, manual, md_doc
    last_modified: str = ""


@dataclass
class ModuleConfig:
    """模块配置"""

    module_name: str
    display_name: str
    table_name: str
    api_endpoint: str
    version: str
    last_updated: str
    total_fields: int
    selected_fields: int
    fields: Dict[str, FieldConfig]


class IntelligentFieldMapper:
    """智能字段映射器 - V3架构实现"""

    def __init__(self, config_base_path: str = "v3/backend/config"):
        """
        初始化智能字段映射器

        Args:
            config_base_path: 配置文件基础路径
        """
        self.config_base_path = config_base_path
        # self.field_extractor = FieldExtractor()
        # self.md_parser = MDParser()

        # 确保配置目录存在
        os.makedirs(
            os.path.join(
                config_base_path,
                "field_configs"),
            exist_ok=True)

        # 添加当前模块名称跟踪
        self._current_module_name = None

        logger.info("智能字段映射器初始化完成", config_path=config_base_path)

    def set_current_module(self, module_name: str):
        """设置当前处理的模块名称"""
        self._current_module_name = module_name

    def generate_module_config(
        self,
        module_name: str,
        api_response: Optional[Any] = None,
        force_regenerate: bool = False,
    ) -> ModuleConfig:
        """
        生成完整的模块配置

        Args:
            module_name: 模块名称
            api_response: API响应数据，可选
            force_regenerate: 是否强制重新生成

        Returns:
            ModuleConfig: 完整的模块配置
        """
        logger.info("开始生成模块配置", module=module_name)

        # 设置当前模块名称
        self.set_current_module(module_name)

        try:
            # 1. 加载现有配置（如果存在）
            existing_config = self._load_existing_config(module_name)

            # 2. 从API提取字段信息（如果提供了API响应）
            api_fields = []
            # if api_response:
            #     api_fields = self.field_extractor.extract_fields_from_api(module_name, api_response)

            # 3. 从MD文档解析字段信息
            md_fields = []
            # md_fields = self.md_parser.parse_module_fields(module_name)

            # 4. 智能合并字段信息
            merged_fields = self._merge_field_sources(
                module_name, api_fields, md_fields, existing_config, force_regenerate)

            # 5. 生成最终配置
            module_config = self._create_module_config(
                module_name, merged_fields, existing_config
            )

            # 6. 保存配置文件
            self._save_module_config(module_config)

            logger.info(
                f"成功生成模块配置",
                module=module_name,
                total_fields=module_config.total_fields,
                selected_fields=module_config.selected_fields,
            )

            return module_config

        except Exception:
            logger.error(f"生成模块配置失败", module=module_name, error=str(e))
            raise

    def generate_all_modules_config(
        self, force_regenerate: bool = False
    ) -> Dict[str, ModuleConfig]:
        """
        生成所有15个模块的字段配置

        Args:
            force_regenerate: 是否强制重新生成

        Returns:
            Dict[str, ModuleConfig]: 模块名 -> 模块配置的映射
        """
        logger.info("开始生成所有模块配置", force_regenerate=force_regenerate)

        all_configs = {}

        # 获取15个模块列表
        modules = list(self.api_endpoints.keys())

        for module_name in modules:
            try:
                config = self.generate_module_config(
                    module_name, force_regenerate=force_regenerate
                )
                all_configs[module_name] = config

            except Exception:
                logger.error(f"生成模块配置失败", module=module_name, error=str(e))
                continue

        logger.info(f"完成所有模块配置生成", successful_modules=len(all_configs))
        return all_configs

    def _load_existing_config(
            self,
            module_name: str) -> Optional[ModuleConfig]:
        """加载现有的模块配置"""
        config_file = os.path.join(
            self.field_configs_path, f"field_config_{module_name}.json"
        )

        if not os.path.exists(config_file):
            return None

        try:
            with open(config_file, "r", encoding="utf-8") as f:
                config_data = json.load(f)

            # 转换为ModuleConfig对象
            fields = {}
            for field_name, field_data in config_data.get(
                    "fields", {}).items():
                fields[field_name] = FieldConfig(**field_data)

            return ModuleConfig(
                module_name=config_data["module_name"],
                display_name=config_data["display_name"],
                table_name=config_data["table_name"],
                api_endpoint=config_data["api_endpoint"],
                version=config_data["version"],
                last_updated=config_data["last_updated"],
                total_fields=config_data["total_fields"],
                selected_fields=config_data["selected_fields"],
                fields=fields,
            )

        except Exception:
            logger.warning(f"加载现有配置失败", module=module_name, error=str(e))
            return None

    def _merge_field_sources(
        self,
        module_name: str,
        api_fields: List[FieldMetadata],
        md_fields: List[MDFieldInfo],
        existing_config: Optional[ModuleConfig],
        force_regenerate: bool,
    ) -> Dict[str, FieldConfig]:
        """
        智能合并不同来源的字段信息

        Args:
            module_name: 模块名称
            api_fields: API提取的字段
            md_fields: MD文档解析的字段
            existing_config: 现有配置
            force_regenerate: 是否强制重新生成

        Returns:
            Dict[str, FieldConfig]: 合并后的字段配置
        """
        merged_fields = {}

        # 创建MD字段的快速查找映射
        md_field_map = {field.field_name: field for field in md_fields}

        # 现有字段映射
        existing_fields = {}
        if existing_config:
            existing_fields = existing_config.fields

        # 处理API字段
        field_order = 1
        processed_fields = set()

        for api_field in api_fields:
            field_name = api_field.api_field_name
            processed_fields.add(field_name)

            # 检查是否有现有配置且不强制重新生成
            if field_name in existing_fields and not force_regenerate:
                existing_field = existing_fields[field_name]

                # 如果是手动修改的字段，保留原配置，只更新部分属性
                if existing_field.source == "manual":
                    merged_fields[field_name] = existing_field
                    # 更新sample_value（可能会变化）
                    merged_fields[field_name].sample_value = api_field.sample_value
                    continue

            # 查找对应的MD字段信息
            md_field = md_field_map.get(field_name)

            # 生成中文名称
            chinese_name = self._determine_chinese_name(api_field, md_field)

            # 确定数据类型
            data_type = self._determine_data_type(api_field, md_field)

            # 创建字段配置
            field_config = FieldConfig(
                api_field_name=field_name,
                chinese_name=chinese_name,
                data_type=data_type,
                is_selected=self._should_select_field(
                    api_field.importance_level),
                is_required=False,  # 默认不必填
                is_primary_key=False,
                field_order=field_order,
                importance_level=api_field.importance_level,
                description=md_field.description if md_field else "",
                sample_value=api_field.sample_value,
                value_mapping={},
                validation_rules=self._generate_validation_rules(
                    api_field, data_type),
                source="md_doc" if md_field else "auto",
                last_modified=datetime.now().isoformat(),
            )

            merged_fields[field_name] = field_config
            field_order += 1

        # 处理只在MD文档中存在的字段
        for md_field in md_fields:
            if md_field.field_name not in processed_fields:
                field_name = md_field.field_name

                # 检查现有配置
                if field_name in existing_fields and not force_regenerate:
                    existing_field = existing_fields[field_name]
                    if existing_field.source == "manual":
                        merged_fields[field_name] = existing_field
                        continue

                # 创建基于MD文档的字段配置
                field_config = FieldConfig(
                    api_field_name=field_name,
                    chinese_name=md_field.chinese_name or field_name,
                    data_type=self._map_md_type_to_sql(md_field.field_type),
                    is_selected=False,  # MD独有字段默认不选择
                    is_required=False,
                    is_primary_key=False,
                    field_order=field_order,
                    importance_level="中",
                    description=md_field.description,
                    sample_value="",
                    value_mapping={},
                    validation_rules={},
                    source="md_doc",
                    last_modified=datetime.now().isoformat(),
                )

                merged_fields[field_name] = field_config
                field_order += 1

        # 保留现有配置中的手动修改字段（如果API和MD中都没有）
        if existing_config and not force_regenerate:
            for field_name, existing_field in existing_fields.items():
                if (
                    field_name not in merged_fields
                    and existing_field.source == "manual"
                ):
                    merged_fields[field_name] = existing_field

        return merged_fields

    def _determine_chinese_name(
        self, api_field: FieldMetadata, md_field: Optional[MDFieldInfo]
    ) -> str:
        """
        确定中文字段名 - V3修复：严格只从md_mappings.json完全匹配

        规则：
        1. 只从md_mappings.json完全匹配API字段名
        2. 匹配到 → 返回值
        3. 匹配不到 → 返回空
        4. 不使用前缀匹配、不使用其他逻辑
        """
        # 加载md_mappings.json（如果还没有加载）
        if not hasattr(self, "md_mappings"):
            try:
                # 直接使用md_mappings.json文件
                project_root = os.path.abspath(
                    os.path.join(
                        os.path.dirname(__file__),
                        "..",
                        "..",
                        "..",
                        ".."))
                md_mappings_path = os.path.join(
                    project_root, "v3", "logic", "md_mappings.json"
                )

                if os.path.exists(md_mappings_path):
                    with open(md_mappings_path, "r", encoding="utf-8") as f:
                        self.md_mappings = json.load(f)
                else:
                    self.md_mappings = {}
            except Exception:
                logger.warning("加载md_mappings.json失败", error=str(e))
                self.md_mappings = {}

        # 只从md_mappings.json中完全匹配API字段名
        if (
            hasattr(self, "_current_module_name")
            and self._current_module_name
            and self.md_mappings
        ):
            module_mappings = self.md_mappings.get(
                self._current_module_name, {})
            field_name = api_field.api_field_name

            # 只进行完全匹配，不使用前缀匹配或其他逻辑
            if field_name in module_mappings:
                chinese_name = module_mappings[field_name]
                if chinese_name and chinese_name != field_name:
                    return chinese_name

        # 匹配不到返回空字符串
        return ""

    def _determine_data_type(
        self, api_field: FieldMetadata, md_field: Optional[MDFieldInfo]
    ) -> str:
        """
        确定数据类型
        优先级：MD文档类型 > API样本值推断 > 默认类型
        """
        # 🚨 修复：优先使用MD文档中的数据类型
        if md_field and md_field.field_type:
            md_sql_type = self._map_md_type_to_sql(md_field.field_type)
            logger.debug(
                "使用MD文档数据类型",
                field_name=api_field.api_field_name,
                md_type=md_field.field_type,
                sql_type=md_sql_type,
            )
            return md_sql_type

        # 如果没有MD文档类型，使用API提取的数据类型
        logger.debug(
            "使用API推断数据类型",
            field_name=api_field.api_field_name,
            api_type=api_field.data_type,
        )
        return api_field.data_type

    def _map_md_type_to_sql(self, md_type: str) -> str:
        """
        将MD文档中的类型映射到SQL类型
        🚨 修复：更准确的类型映射，避免数据类型冲突
        """
        md_type_lower = md_type.lower()

        # 改进的类型映射
        type_mapping = {
            # 字符串类型
            "string": "NVARCHAR(200)",
            "varchar": "NVARCHAR(200)",
            "char": "NVARCHAR(50)",
            "text": "NTEXT",
            # 整数类型 - 🚨 关键修复：API返回字符串，所以用NVARCHAR
            "long": "NVARCHAR(50)",  # 长整数，API返回字符串
            "int": "NVARCHAR(20)",  # 整数，API返回字符串
            "integer": "NVARCHAR(20)",  # 整数，API返回字符串
            "bigint": "NVARCHAR(50)",  # 大整数，API返回字符串
            # 小数类型
            "number": "DECIMAL(28,8)",
            "decimal": "DECIMAL(28,8)",
            "float": "DECIMAL(28,8)",
            "double": "DECIMAL(28,8)",
            # 布尔类型
            "boolean": "BIT",
            "bool": "BIT",
            # 日期时间类型
            "date": "DATETIME2",
            "datetime": "DATETIME2",
            "timestamp": "DATETIME2",
            "time": "DATETIME2",
            # 其他类型
            "object": "NVARCHAR(500)",  # 对象类型
            "array": "NVARCHAR(500)",  # 数组类型
            "json": "NVARCHAR(500)",  # JSON类型
        }

        # 处理带长度的类型，如 string(50)
        if "(" in md_type_lower:
            base_type = md_type_lower.split("(")[0].strip()
            if base_type in ["string", "varchar", "char"]:
                return f"NVARCHAR({md_type_lower.split('(')[1].split(')')[0]})"

        return type_mapping.get(md_type_lower, "NVARCHAR(200)")

    def _should_select_field(self, importance_level: str) -> bool:
        """根据重要性级别决定是否默认选择字段"""
        return importance_level in ["高", "中"]

    def _generate_validation_rules(
        self, api_field: FieldMetadata, data_type: str
    ) -> Dict:
        """生成验证规则"""
        rules = {}

        # 字符串长度限制
        if data_type.startswith("NVARCHAR"):
            max_length_match = re.search(r"NVARCHAR\((\d+)\)", data_type)
            if max_length_match:
                rules["max_length"] = int(max_length_match.group(1))

        # 数值范围限制
        if data_type in ["INT", "BIGINT"]:
            rules["type"] = "integer"
        elif data_type.startswith("DECIMAL"):
            rules["type"] = "decimal"

        # 特殊字段的格式验证
        field_name_lower = api_field.api_field_name.lower()
        if "email" in field_name_lower:
            rules["pattern"] = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        elif "phone" in field_name_lower:
            rules["pattern"] = r"^[0-9\-\+\(\)\s]+$"
        elif "code" in field_name_lower:
            rules["pattern"] = r"^[A-Z0-9\-_]+$"

        return rules

    def _create_module_config(
        self,
        module_name: str,
        fields: Dict[str, FieldConfig],
        existing_config: Optional[ModuleConfig],
    ) -> ModuleConfig:
        """创建模块配置"""

        # 计算统计信息
        total_fields = len(fields)
        selected_fields = sum(
            1 for field in fields.values() if field.is_selected)

        # 确定版本号
        version = "1.0.0"
        if existing_config:
            # 增加版本号
            current_version = existing_config.version
            try:
                major, minor, patch = map(int, current_version.split("."))
                version = f"{major}.{minor}.{patch + 1}"
            except Exception:
                version = "1.0.1"

        return ModuleConfig(
            module_name=module_name,
            display_name=module_name,
            # self.md_parser.get_module_display_name(module_name),
            table_name=module_name,
            api_endpoint=self.api_endpoints.get(module_name, ""),
            version=version,
            last_updated=datetime.now().isoformat(),
            total_fields=total_fields,
            selected_fields=selected_fields,
            fields=fields,
        )

    def _save_module_config(self, module_config: ModuleConfig):
        """保存模块配置到JSON文件"""
        config_file = os.path.join(
            self.field_configs_path,
            f"field_config_{module_config.module_name}.json")

        # 转换为可序列化的格式
        config_dict = {
            "module_name": module_config.module_name,
            "display_name": module_config.display_name,
            "table_name": module_config.table_name,
            "api_endpoint": module_config.api_endpoint,
            "version": module_config.version,
            "last_updated": module_config.last_updated,
            "total_fields": module_config.total_fields,
            "selected_fields": module_config.selected_fields,
            "fields": {
                field_name: asdict(field_config)
                for field_name, field_config in module_config.fields.items()
            },
        }

        # 备份现有配置
        if os.path.exists(config_file):
            try:
                backup_file = (
                    f"{config_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                )
                os.rename(config_file, backup_file)
            except Exception:
                pass

        # 保存新配置
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump(config_dict, f, ensure_ascii=False, indent=2)

        logger.info(
            f"成功保存模块配置", module=module_config.module_name, file=config_file
        )

    def validate_all_configs(self) -> Dict[str, Dict[str, Any]]:
        """验证所有模块配置的完整性"""
        validation_results = {}

        for module_name in self.api_endpoints.keys():
            result = self.validate_module_config(module_name)
            validation_results[module_name] = result

        return validation_results

    def validate_module_config(self, module_name: str) -> Dict[str, Any]:
        """验证单个模块配置"""
        result = {
            "is_valid": False,
            "errors": [],
            "warnings": [],
            "field_count": 0,
            "selected_count": 0,
        }

        try:
            config = self._load_existing_config(module_name)

            if not config:
                result["errors"].append("配置文件不存在")
                return result

            # 验证必填字段
            if not config.module_name:
                result["errors"].append("缺少模块名称")

            if not config.display_name:
                result["errors"].append("缺少显示名称")

            if not config.fields:
                result["errors"].append("没有字段配置")

            # 统计信息
            result["field_count"] = len(config.fields)
            result["selected_count"] = sum(
                1 for f in config.fields.values() if f.is_selected
            )

            # 验证字段配置
            chinese_names = []
            for field_name, field_config in config.fields.items():
                if not field_config.chinese_name:
                    result["warnings"].append(f"字段 {field_name} 缺少中文名称")

                if field_config.chinese_name in chinese_names:
                    result["errors"].append(
                        f"重复的中文字段名: {field_config.chinese_name}"
                    )

                chinese_names.append(field_config.chinese_name)

            # 如果没有错误，则配置有效
            result["is_valid"] = len(result["errors"]) == 0

        except Exception:
            result["errors"].append(f"验证过程中出错: {str(e)}")

        return result
