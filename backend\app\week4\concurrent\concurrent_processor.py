import threading
import time
from datetime import datetime
from enum import Enum

import structlog

"""
YS-API V3.0 并发处理器
Month 3 Week 4: 高性能并发处理架构
"""


logger = structlog.get_logger()


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class Task:
    """任务定义"""
    task_id: str
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    timeout: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    created_at: datetime = field(default_factory=datetime.now)
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error: Optional[Exception] = None


@dataclass
class WorkerStats:
    """工作线程统计"""
    worker_id: str
    tasks_completed: int = 0
    tasks_failed: int = 0
    total_processing_time: float = 0.0
    average_processing_time: float = 0.0
    last_task_time: Optional[datetime] = None
    is_active: bool = True


class ConcurrentProcessor:
    """并发处理器"""

    def __init___(
        self,
        min_workers: int = 5,
        max_workers: int = 50,
        queue_size: int = 10000,
        worker_timeout: float = 300.0,
        auto_scale: bool = True
    ):
    """TODO: Add function description."""
    self.min_workers = min_workers
    self.max_workers = max_workers
    self.queue_size = queue_size
    self.worker_timeout = worker_timeout
    self.auto_scale = auto_scale

    # 任务队列
    self.task_queue = Queue(maxsize=queue_size)
    self.priority_queue = Queue(maxsize=queue_size // 4)

    # 线程池
    self.executor = ThreadPoolExecutor(
        max_workers=max_workers,
        thread_name_prefix="concurrent_worker"
    )

    # 工作线程管理
    self.workers: Dict[str, threading.Thread] = {}
    self.worker_stats: Dict[str, WorkerStats] = {}
    self.active_tasks: Dict[str, Task] = {}

    # 控制标志
    self.is_running = False
    self.shutdown_event = threading.Event()

    # 统计信息
    self.total_tasks_processed = 0
    self.total_tasks_failed = 0
    self.total_processing_time = 0.0
    self.peak_concurrent_tasks = 0
    self.start_time = datetime.now()

    # 自动扩缩容
    self.scale_lock = threading.Lock()
    self.last_scale_time = datetime.now()
    self.scale_cooldown = 30.0  # 30秒冷却期

    # 监控
    self.monitor_thread = None

    logger.info(
        "并发处理器初始化完成",
        min_workers=min_workers,
        max_workers=max_workers,
        queue_size=queue_size
    )

    def start(self):
        """启动并发处理器"""
        if self.is_running:
            logger.warning("并发处理器已在运行")
            return

        self.is_running = True
        self.shutdown_event.clear()

        # 启动最小数量的工作线程
        for i in range(self.min_workers):
            self._create_worker(f"worker_{i}")

        # 启动监控线程
        self.monitor_thread = threading.Thread(
            target=self._monitor_performance,
            name="performance_monitor",
            daemon=True
        )
        self.monitor_thread.start()

        logger.info("并发处理器已启动", active_workers=len(self.workers))

    def stop(self, timeout: float = 30.0):
        """停止并发处理器"""
        if not self.is_running:
            return

        logger.info("正在停止并发处理器...")

        self.is_running = False
        self.shutdown_event.set()

        # 等待所有任务完成
        self._wait_for_tasks_completion(timeout)

        # 关闭线程池
        self.executor.shutdown(wait=True)

        # 清理资源
        self.workers.clear()
        self.worker_stats.clear()
        self.active_tasks.clear()

        logger.info("并发处理器已停止")

    def submit_task(
        self,
        func: Callable,
        *args,
        priority: TaskPriority = TaskPriority.NORMAL,
        timeout: Optional[float] = None,
        **kwargs
    ) -> str:
        """提交任务"""
        if not self.is_running:
            raise RuntimeError("并发处理器未运行")

        task_id = f"task_{int(time.time() * 1000000)}"
        task = Task(
            task_id=task_id,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            timeout=timeout
        )

        try:
            # 根据优先级选择队列
            if priority in [TaskPriority.HIGH, TaskPriority.URGENT]:
                self.priority_queue.put(task, timeout=1.0)
            else:
                self.task_queue.put(task, timeout=1.0)

            logger.debug(
                "任务已提交",
                task_id=task_id,
                priority=priority.name,
                queue_size=self.get_queue_size()
            )

            # 检查是否需要自动扩容
            if self.auto_scale:
                self._check_auto_scale()

            return task_id

        except Exception:
            logger.error("任务提交失败", task_id=task_id, error=str(e))
            raise

    def get_task_status(self, task_id: str) -> Optional[Task]:
        """获取任务状态"""
        return self.active_tasks.get(task_id)

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        task = self.active_tasks.get(task_id)
        if task and task.status == TaskStatus.PENDING:
            task.status = TaskStatus.CANCELLED
            return True
        return False

    def get_queue_size(self) -> Dict[str, int]:
        """获取队列大小"""
        return {
            "normal_queue": self.task_queue.qsize(),
            "priority_queue": self.priority_queue.qsize(),
            "total": self.task_queue.qsize() + self.priority_queue.qsize()
        }

    def get_worker_count(self) -> int:
        """获取当前工作线程数量"""
        return len([w for w in self.workers.values() if w.is_alive()])

    def get_statistics(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        current_time = datetime.now()
        uptime = (current_time - self.start_time).total_seconds()

        active_workers = self.get_worker_count()
        queue_sizes = self.get_queue_size()

        avg_processing_time = (
            self.total_processing_time / self.total_tasks_processed
            if self.total_tasks_processed > 0 else 0
        )

        throughput = self.total_tasks_processed / uptime if uptime > 0 else 0

        return {
            "uptime_seconds": uptime,
            "active_workers": active_workers,
            "total_tasks_processed": self.total_tasks_processed,
            "total_tasks_failed": self.total_tasks_failed,
            "success_rate": (
                (self.total_tasks_processed - self.total_tasks_failed) /
                max(1, self.total_tasks_processed)
            ),
            "average_processing_time": avg_processing_time,
            "throughput_tps": throughput,
            "peak_concurrent_tasks": self.peak_concurrent_tasks,
            "queue_sizes": queue_sizes,
            "worker_stats": {
                worker_id: {
                    "tasks_completed": stats.tasks_completed,
                    "tasks_failed": stats.tasks_failed,
                    "average_processing_time": stats.average_processing_time,
                    "is_active": stats.is_active
                }
                for worker_id, stats in self.worker_stats.items()
            }
        }

    def _create_worker(self, worker_id: str):
        """创建工作线程"""
        worker = threading.Thread(
            target=self._worker_loop,
            args=(worker_id,),
            name=worker_id,
            daemon=True
        )

        self.workers[worker_id] = worker
        self.worker_stats[worker_id] = WorkerStats(worker_id=worker_id)

        worker.start()

        logger.debug("工作线程已创建", worker_id=worker_id)

    def _worker_loop(self, worker_id: str):
        """工作线程主循环"""
        stats = self.worker_stats[worker_id]

        logger.debug("工作线程开始运行", worker_id=worker_id)

        while self.is_running and not self.shutdown_event.is_set():
            try:
                # 优先处理高优先级任务
                task = self._get_next_task()

                if task is None:
                    time.sleep(0.1)  # 没有任务时短暂休眠
                    continue

                # 执行任务
                self._execute_task(task, worker_id, stats)

            except Exception:
                logger.error(
                    "工作线程异常",
                    worker_id=worker_id,
                    error=str(e)
                )
                time.sleep(1.0)  # 异常后休眠1秒

        stats.is_active = False
        logger.debug("工作线程已停止", worker_id=worker_id)

    def _get_next_task(self) -> Optional[Task]:
        """获取下一个任务"""
        try:
            # 先检查优先级队列
            if not self.priority_queue.empty():
                return self.priority_queue.get_nowait()

            # 再检查普通队列
            if not self.task_queue.empty():
                return self.task_queue.get(timeout=0.5)

        except Empty:
            pass

        return None

    def _execute_task(self, task: Task, worker_id: str, stats: WorkerStats):
        """执行任务"""
        start_time = time.time()
        task.status = TaskStatus.RUNNING
        self.active_tasks[task.task_id] = task

        # 更新峰值并发任务数
        current_active = len(self.active_tasks)
        if current_active > self.peak_concurrent_tasks:
            self.peak_concurrent_tasks = current_active

        try:
            logger.debug(
                "开始执行任务",
                task_id=task.task_id,
                worker_id=worker_id,
                priority=task.priority.name
            )

            # 执行任务函数
            if task.timeout:
                # 有超时限制的执行
                future = self.executor.submit(
                    task.func, *task.args, **task.kwargs)
                task.result = future.result(timeout=task.timeout)
            else:
                # 直接执行
                task.result = task.func(*task.args, **task.kwargs)

            task.status = TaskStatus.COMPLETED

            # 更新统计
            processing_time = time.time() - start_time
            self._update_worker_stats(stats, processing_time, success=True)
            self.total_tasks_processed += 1
            self.total_processing_time += processing_time

            logger.debug(
                "任务执行成功",
                task_id=task.task_id,
                worker_id=worker_id,
                processing_time=processing_time
            )

        except Exception:
            task.status = TaskStatus.FAILED
            task.error = e

            # 重试逻辑
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.PENDING

                # 重新提交任务
                try:
                    if task.priority in [
                            TaskPriority.HIGH, TaskPriority.URGENT]:
                        self.priority_queue.put(task, timeout=1.0)
                    else:
                        self.task_queue.put(task, timeout=1.0)

                    logger.info(
                        "任务重试",
                        task_id=task.task_id,
                        retry_count=task.retry_count,
                        max_retries=task.max_retries
                    )

                except Exception:
                    # 重新提交失败，标记为最终失败
                    task.status = TaskStatus.FAILED
                    self._handle_task_failure(task, stats, e, start_time)
            else:
                # 重试次数用尽
                self._handle_task_failure(task, stats, e, start_time)

        finally:
            # 清理活跃任务记录
            self.active_tasks.pop(task.task_id, None)

    def _handle_task_failure(
            self,
            task: Task,
            stats: WorkerStats,
            error: Exception,
            start_time: float):
        """处理任务失败"""
        processing_time = time.time() - start_time
        self._update_worker_stats(stats, processing_time, success=False)
        self.total_tasks_failed += 1

        logger.error(
            "任务执行失败",
            task_id=task.task_id,
            error=str(error),
            retry_count=task.retry_count,
            max_retries=task.max_retries
        )

    def _update_worker_stats(
            self,
            stats: WorkerStats,
            processing_time: float,
            success: bool):
        """更新工作线程统计"""
        if success:
            stats.tasks_completed += 1
        else:
            stats.tasks_failed += 1

        stats.total_processing_time += processing_time
        total_tasks = stats.tasks_completed + stats.tasks_failed
        if total_tasks > 0:
            stats.average_processing_time = stats.total_processing_time / total_tasks

        stats.last_task_time = datetime.now()

    def _check_auto_scale(self):
        """检查自动扩缩容"""
        if not self.auto_scale:
            return

        with self.scale_lock:
            current_time = datetime.now()
            if (current_time -
                    self.last_scale_time).total_seconds() < self.scale_cooldown:
                return

            queue_sizes = self.get_queue_size()
            total_queue_size = queue_sizes["total"]
            active_workers = self.get_worker_count()

            # 扩容条件：队列积压 > 工作线程数 * 2
            if total_queue_size > active_workers * 2 and active_workers < self.max_workers:
                new_workers = min(
                    self.max_workers - active_workers,
                    max(1, total_queue_size // 10)  # 按队列大小计算需要的新线程数
                )

                for i in range(new_workers):
                    worker_id = f"auto_worker_{active_workers +
                                               i}_{int(time.time())}"
                    self._create_worker(worker_id)

                self.last_scale_time = current_time

                logger.info(
                    "自动扩容",
                    new_workers=new_workers,
                    total_workers=self.get_worker_count(),
                    queue_size=total_queue_size
                )

            # 缩容条件：队列为空且工作线程数 > 最小值
            elif total_queue_size == 0 and active_workers > self.min_workers:
                # 简单缩容策略：移除空闲时间最长的线程
                # 这里简化处理，实际实现可以更复杂
                excess_workers = active_workers - self.min_workers
                if excess_workers > 0:
                    logger.info(
                        "检测到可缩容条件",
                        excess_workers=excess_workers,
                        current_workers=active_workers
                    )

    def _monitor_performance(self):
        """性能监控线程"""
        logger.info("性能监控线程已启动")

        while self.is_running and not self.shutdown_event.is_set():
            try:
                stats = self.get_statistics()

                # 记录关键指标
                logger.info(
                    "性能监控",
                    active_workers=stats["active_workers"],
                    queue_total=stats["queue_sizes"]["total"],
                    throughput_tps=round(
                        stats["throughput_tps"],
                        2),
                    success_rate=round(
                        stats["success_rate"],
                        3),
                    avg_processing_time=round(
                        stats["average_processing_time"],
                        3))

                # 检查性能告警
                self._check_performance_alerts(stats)

            except Exception:
                logger.error("性能监控异常", error=str(e))

            # 每30秒监控一次
            self.shutdown_event.wait(30)

        logger.info("性能监控线程已停止")

    def _check_performance_alerts(self, stats: Dict[str, Any]):
        """检查性能告警"""
        # 队列积压告警
        total_queue = stats["queue_sizes"]["total"]
        if total_queue > self.queue_size * 0.8:
            logger.warning(
                "队列积压告警",
                queue_size=total_queue,
                threshold=self.queue_size * 0.8
            )

        # 成功率告警
        if stats["success_rate"] < 0.95:
            logger.warning(
                "成功率告警",
                success_rate=stats["success_rate"],
                threshold=0.95
            )

        # 响应时间告警
        if stats["average_processing_time"] > 1.0:
            logger.warning(
                "响应时间告警",
                avg_processing_time=stats["average_processing_time"],
                threshold=1.0
            )

    def _wait_for_tasks_completion(self, timeout: float):
        """等待任务完成"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            queue_sizes = self.get_queue_size()
            active_tasks = len(self.active_tasks)

            if queue_sizes["total"] == 0 and active_tasks == 0:
                logger.info("所有任务已完成")
                break

            logger.info(
                "等待任务完成",
                queue_total=queue_sizes["total"],
                active_tasks=active_tasks,
                remaining_time=timeout - (time.time() - start_time)
            )

            time.sleep(1.0)
        else:
            logger.warning("等待任务完成超时")


def create_concurrent_processor(
    min_workers: int = 5,
    max_workers: int = 50,
    queue_size: int = 10000,
    auto_scale: bool = True
) -> ConcurrentProcessor:
    """创建并发处理器实例"""
    return ConcurrentProcessor(
        min_workers=min_workers,
        max_workers=max_workers,
        queue_size=queue_size,
        auto_scale=auto_scale
    )
