from datetime import datetime

from .service import PurchaseOrderService

"""
采购订单API路由 - RESTful设计
"""


router = APIRouter(prefix="/api/v2/modules/purchase_order", tags=["采购订单"])


@router.get("/", response_model=PurchaseOrderListResponse)
async def get_purchase_orders(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    status: Optional[str] = Query(None, description="订单状态"),
    supplier_id: Optional[int] = Query(None, description="供应商ID"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    service: PurchaseOrderService = Depends(),
):
    """获取采购订单列表"""
    try:
        result = await service.get_purchase_orders(
            page=page,
            size=size,
            status=status,
            supplier_id=supplier_id,
            start_date=start_date,
            end_date=end_date,
        )
        return result
    except Exception:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{order_id}", response_model=PurchaseOrderResponse)
async def get_purchase_order(order_id: int,
                             service: PurchaseOrderService = Depends()):
    """获取单个采购订单详情"""
    try:
        order = await service.get_purchase_order_by_id(order_id)
        if not order:
            raise HTTPException(status_code=404, detail="采购订单不存在")
        return order
    except HTTPException:
        raise
    except Exception:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/data", response_model=dict)
async def get_purchase_orders_data(service: PurchaseOrderService = Depends()):
    """获取采购订单数据 - 用于数据一致性测试"""
    try:
        result = await service.get_all_orders_for_consistency_check()
        return {
            "items": result,
            "count": len(result),
            "timestamp": datetime.now().isoformat(),
        }
    except Exception:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "module": "purchase_order",
        "timestamp": datetime.now().isoformat(),
    }
