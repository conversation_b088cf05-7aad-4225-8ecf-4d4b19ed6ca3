# 字段配置保存位置说明

## 📁 配置文件存储结构

YS-API V3.0 字段配置系统采用分层存储架构，支持多种配置类型和用户级别的管理。

### 1. 模块级配置（用于数据库同步）

**位置**: `v3/config/field_configs/`

**文件命名**: `field_config_{模块名}.json`

**用途**: 
- 存储模块的完整字段配置
- 用于数据库同步和批量写入
- 包含所有字段的元数据和选择状态

**示例文件**:
```
v3/config/field_configs/
├── field_config_purchase_order.json
├── field_config_sales_order.json
├── field_config_production_order.json
└── ...
```

### 2. 用户级配置（个性化设置）

**位置**: `v3/config/data/user_field_config/{用户ID}/`

**文件命名**: `{模块名}.json`

**用途**:
- 存储用户个人的字段选择偏好
- 支持多用户独立配置
- 包含用户自定义的字段名称和选择状态

**示例文件**:
```
v3/config/data/user_field_config/
├── Alice/
│   ├── purchase_order.json
│   ├── sales_order.json
│   └── production_order.json
├── Bob/
│   ├── purchase_order.json
│   └── sales_order.json
└── ...
```

### 3. 基准配置（新系统）

**位置**: `v3/config/baselines/`

**文件命名**: `{模块名}_baseline.json`

**用途**:
- 存储模块的基准字段配置
- 作为用户配置的基础模板
- 包含字段重要性自动分类

**示例文件**:
```
v3/config/baselines/
├── purchase_order_baseline.json
├── sales_order_baseline.json
├── production_order_baseline.json
└── ...
```

### 4. 用户配置（新系统）

**位置**: `v3/config/user_configs/{用户ID}/`

**文件命名**: `{模块名}_config.json`

**用途**:
- 存储用户在新系统中的字段选择
- 与基准配置合并生成最终配置
- 支持字段选择历史记录

**示例文件**:
```
v3/config/user_configs/
├── Alice/
│   ├── purchase_order_config.json
│   ├── sales_order_config.json
│   └── production_order_config.json
├── Bob/
│   ├── purchase_order_config.json
│   └── sales_order_config.json
└── ...
```

## 🔄 配置同步逻辑

### 一键获取配置流程

1. **前端调用**: 点击"一键获取所有模块"按钮
2. **API处理**: 调用 `/api/v1/config/modules/{module}/fields` 接口
3. **字段提取**: 从API获取字段数据，应用智能选择规则
4. **配置保存**: 保存到用户级配置目录
5. **文件位置**: `v3/config/data/user_field_config/Alice/{模块名}.json`

### 单个模块获取流程

1. **前端调用**: 选择模块并点击"保存配置"按钮
2. **API处理**: 调用 `/api/v1/config/modules/{module}/fields` 接口
3. **字段提取**: 从API获取字段数据，应用智能选择规则
4. **配置保存**: 保存到用户级配置目录
5. **文件位置**: `v3/config/data/user_field_config/Alice/{模块名}.json`

### 配置优先级

1. **用户级配置** (最高优先级)
   - 位置: `v3/config/data/user_field_config/{用户ID}/`
   - 包含用户个人选择和自定义设置

2. **模块级配置** (中等优先级)
   - 位置: `v3/config/field_configs/`
   - 用于数据库同步的完整配置

3. **基准配置** (基础模板)
   - 位置: `v3/config/baselines/`
   - 作为配置的基础模板

## 📊 配置文件内容结构

### 用户级配置文件示例

```json
{
  "module_name": "purchase_order",
  "display_name": "采购订单",
  "user_id": "Alice",
  "last_updated": "2024-12-19T10:30:00",
  "total_fields": 150,
  "selected_fields": 45,
  "fields": {
    "code": {
      "api_field_name": "code",
      "chinese_name": "订单号",
      "data_type": "string",
      "sample_value": "PO20241219001",
      "etl_score": 9.5,
      "config_name": "订单号",
      "is_selected": true,
      "locked": false
    },
    "orderDate": {
      "api_field_name": "orderDate",
      "chinese_name": "订单日期",
      "data_type": "datetime",
      "sample_value": "2024-12-19",
      "etl_score": 8.0,
      "config_name": "订单日期",
      "is_selected": true,
      "locked": false
    }
  }
}
```

### 模块级配置文件示例

```json
{
  "module_name": "purchase_order",
  "display_name": "采购订单",
  "table_name": "purchase_orders",
  "api_endpoint": "/api/v1/purchase-orders",
  "version": "1.0.0",
  "last_updated": "2024-12-19T10:30:00",
  "total_fields": 150,
  "selected_fields": 45,
  "fields": {
    "code": {
      "api_field_name": "code",
      "chinese_name": "订单号",
      "data_type": "string",
      "sample_value": "PO20241219001",
      "etl_score": 9.5,
      "config_name": "订单号",
      "is_selected": true,
      "locked": false,
      "importance": "high"
    }
  }
}
```

## 🔧 配置管理操作

### 保存配置

- **用户级保存**: 前端保存按钮 → `v3/config/data/user_field_config/Alice/`
- **模块级保存**: 系统自动同步 → `v3/config/field_configs/`

### 加载配置

- **优先级**: 用户级配置 > 模块级配置 > 基准配置
- **合并策略**: 用户选择覆盖系统默认选择

### 同步配置

- **从旧系统同步**: `v3/config/field_configs/` → `v3/config/baselines/`
- **用户配置同步**: `v3/config/baselines/` + `v3/config/data/user_field_config/Alice/` → 最终配置

## 📝 注意事项

1. **文件编码**: 所有配置文件使用 UTF-8 编码
2. **备份机制**: 重要操作前自动备份原配置文件
3. **权限控制**: 用户只能访问自己的配置文件
4. **版本控制**: 配置文件包含版本信息，支持升级迁移
5. **数据完整性**: 保存时自动验证配置文件的完整性

## 🚀 快速操作指南

### 查看当前配置
```bash
# 查看用户配置
ls v3/config/data/user_field_config/Alice/

# 查看模块配置
ls v3/config/field_configs/

# 查看基准配置
ls v3/config/baselines/
```

### 备份配置
```bash
# 备份用户配置
cp -r v3/config/data/user_field_config/Alice/ v3/config/data/user_field_config/Alice_backup/

# 备份模块配置
cp v3/config/field_configs/field_config_purchase_order.json v3/config/field_configs/field_config_purchase_order.json.backup
```

### 恢复配置
```bash
# 恢复用户配置
cp -r v3/config/data/user_field_config/Alice_backup/* v3/config/data/user_field_config/Alice/

# 恢复模块配置
cp v3/config/field_configs/field_config_purchase_order.json.backup v3/config/field_configs/field_config_purchase_order.json
```

---

**📋 文档版本**: v1.0  
**最后更新**: 2024年12月19日  
**维护人员**: YS-API 开发团队 