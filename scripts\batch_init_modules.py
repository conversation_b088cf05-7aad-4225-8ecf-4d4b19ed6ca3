import json
import subprocess
from datetime import datetime
from pathlib import Path

#!/usr/bin/env python3
"""
批量模块迁移启动器
一次性初始化多个模块的迁移流程
"""


def get_next_n_modules(n=3):
    """获取接下来n个未开始的模块"""
    status_file = Path("tasks/module_status.json")

    if not status_file.exists():
        print("❌ 状态文件不存在")
        return []

    with open(status_file, "r", encoding="utf-8") as f:
        status = json.load(f)

    modules = status.get("modules", {})

    # 找到completion_rate为0的模块
    not_started = []
    for module_name, module_data in modules.items():
        if module_data.get("completion_rate", 0) == 0:
            not_started.append(module_name)

    return not_started[:n]


def batch_initialize_modules(modules):
    """批量初始化模块"""
    print(f"🚀 批量初始化 {len(modules)} 个模块...")

    results = []

    for i, module in enumerate(modules, 1):
        print(f"\n📋 [{i}/{len(modules)}] 处理模块: {module}")

        try:
            # 生成测试用例
            print("  📝 生成测试用例...")
            result = subprocess.run(
                [
                    "python",
                    "tests/module_migration/test_generator.py",
                    "--generate",
                    module,
                ],
                capture_output=True,
                text=True,
            )

            # 使用next_module.py生成迁移脚本
            print("  🔨 生成迁移脚本...")
            result = subprocess.run(
                ["python", "scripts/next_module.py"], capture_output=True, text=True
            )

            # 标记开始状态
            print("  📊 更新模块状态...")
            result = subprocess.run(
                [
                    "python",
                    "scripts/module_tracker_simple.py",
                    "--update",
                    module,
                    "test_passed",
                    "true",
                    "--notes",
                    f"批量初始化 {datetime.now().strftime('%H:%M:%S')}",
                ],
                capture_output=True,
                text=True,
            )

            results.append(
                {
                    "module": module,
                    "status": "initialized",
                    "timestamp": datetime.now().isoformat(),
                }
            )

            print(f"  ✅ 模块 {module} 初始化完成")

        except Exception:
            print(f"  ❌ 模块 {module} 初始化失败: {e}")
            results.append(
                {
                    "module": module,
                    "status": "failed",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat(),
                }
            )

    return results


def main():
    """主函数"""
    print("🔍 批量模块迁移启动器")

    # 获取下一批模块
    next_modules = get_next_n_modules(5)  # 一次处理5个模块

    if not next_modules:
        print("🎉 没有更多模块需要初始化！")
        return

    print(f"📋 发现 {len(next_modules)} 个待初始化模块:")
    for i, module in enumerate(next_modules, 1):
        print(f"  {i}. {module}")

    # 确认是否继续
    confirm = input("\n是否继续批量初始化？(y/N): ")
    if confirm.lower() not in ["y", "yes"]:
        print("取消操作")
        return

    # 执行批量初始化
    results = batch_initialize_modules(next_modules)

    # 生成报告
    successful = len([r for r in results if r["status"] == "initialized"])
    failed = len(results) - successful

    print(f"\n📊 批量初始化完成:")
    print(f"  ✅ 成功: {successful} 个模块")
    print(f"  ❌ 失败: {failed} 个模块")

    # 保存结果
    report_path = (
        Path("reports")
        / f"batch_init_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    )
    report_path.parent.mkdir(exist_ok=True)

    with open(report_path, "w", encoding="utf-8") as f:
        json.dump(
            {
                "timestamp": datetime.now().isoformat(),
                "total_modules": len(next_modules),
                "successful": successful,
                "failed": failed,
                "results": results,
            },
            f,
            ensure_ascii=False,
            indent=2,
        )

    print(f"📄 详细报告: {report_path}")

    # 生成进度报告
    print("\n📊 生成最新进度报告...")
    subprocess.run(
        ["python", "scripts/module_tracker_simple.py", "--markdown"])

    print("\n🎯 下一步建议:")
    print("  1. 检查生成的迁移脚本")
    print("  2. 启动Docker环境: ./start_strangler.sh")
    print("  3. 逐个运行模块迁移脚本")
    print("  4. 监控系统状态")


if __name__ == "__main__":
    main()
