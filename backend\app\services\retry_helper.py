import asyncio
import functools
import random
import time

import structlog

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重试助手 - 同步任务容错机制
提供智能重试、断点续传、失败熔断功能
"""


logger = structlog.get_logger()


class RetryConfig:
    """重试配置"""

    def __init___(
        self,
        max_attempts: int = 3,
        base_delay: float = 20.0,
        max_delay: float = 300.0,
        exponential_base: float = 2.0,
        jitter: bool = True,
    ):
    """TODO: Add function description."""
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter


class RetryState:
    """重试状态管理"""

    def __init___(self):
    """TODO: Add function description."""
        self.attempts: Dict[str, int] = {}
        self.last_success: Dict[str, float] = {}
        self.failure_reasons: Dict[str, list] = {}

    def record_attempt(
    self,
    key: str,
    success: bool,
     error: Optional[str] = None):
        """记录重试尝试"""
        if key not in self.attempts:
            self.attempts[key] = 0
            self.failure_reasons[key] = []

        self.attempts[key] += 1

        if success:
            self.last_success[key] = time.time()
            # 成功后重置计数
            self.attempts[key] = 0
            self.failure_reasons[key] = []
        elif error:
            self.failure_reasons[key].append(error)

    def should_retry(self, key: str, max_attempts: int) -> bool:
        """判断是否应该重试"""
        return self.attempts.get(key, 0) < max_attempts

    def get_delay(self, key: str, config: RetryConfig) -> float:
        """计算延迟时间"""
        attempt = self.attempts.get(key, 0)
        delay = config.base_delay * (config.exponential_base ** (attempt - 1))
        delay = min(delay, config.max_delay)

        if config.jitter:

            delay *= 0.5 + random.random() * 0.5  # 添加50%的随机抖动

        return delay


# 全局重试状态
_retry_state = RetryState()


def sync_retry_wrapper(
    config: Optional[RetryConfig] = None, key_func: Optional[Callable[..., str]] = None
) -> Callable:
    """
    同步重试装饰器

    Args:
        config: 重试配置
        key_func: 生成重试键的函数
    """
    if config is None:
        config = RetryConfig()

    def decorator(func: Callable[[], Awaitable]) -> Callable[[], Awaitable]:
        @functools.wraps(func)
        async def wrapperr(*args, **kwargs):
    """TODO: Add function description."""
            # 生成重试键
            if key_func:
                retry_key = key_func(*args, **kwargs)
            else:
                retry_key = f"{func.__name__}_{id(func)}"

            last_error = None

            while _retry_state.should_retry(retry_key, config.max_attempts):
                try:
                    logger.info(
                        "开始执行同步任务",
                        function=func.__name__,
                        retry_key=retry_key,
                        attempt=_retry_state.attempts.get(retry_key, 0) + 1,
                        max_attempts=config.max_attempts,
                    )

                    result = await func(*args, **kwargs)

                    # 记录成功
                    _retry_state.record_attempt(retry_key, True)

                    logger.info(
                        "同步任务执行成功", function=func.__name__, retry_key=retry_key
                    )

                    return result

                except Exception:
                    last_error = e
                    error_msg = str(e)

                    # 记录失败
                    _retry_state.record_attempt(retry_key, False, error_msg)

                    attempt = _retry_state.attempts[retry_key]

                    logger.warning(
                        "同步任务执行失败",
                        function=func.__name__,
                        retry_key=retry_key,
                        attempt=attempt,
                        max_attempts=config.max_attempts,
                        error=error_msg,
                    )

                    # 如果还能重试，等待后继续
                    if _retry_state.should_retry(
                        retry_key, config.max_attempts):
                        delay = _retry_state.get_delay(retry_key, config)
                        logger.info(
                            "准备重试",
                            function=func.__name__,
                            retry_key=retry_key,
                            delay_seconds=delay,
                            next_attempt=attempt + 1,
                        )
                        await asyncio.sleep(delay)
                    else:
                        # 达到最大重试次数
                        failure_reasons = _retry_state.failure_reasons.get(
                            retry_key, []
                        )
                        logger.error(
                            "同步任务重试次数耗尽",
                            function=func.__name__,
                            retry_key=retry_key,
                            total_attempts=attempt,
                            failure_reasons=failure_reasons,
                        )
                        break

            # 如果到这里说明重试失败
            if last_error:
                raise last_error
            else:
                raise Exception(f"同步任务失败，重试次数耗尽: {retry_key}")

        return wrapper

    return decorator


def module_retry_key(*args, **kwargs) -> str:
    """为模块同步生成重试键"""
    if args and hasattr(args[0], '__class__'):
        # 如果是类方法调用
        if len(args) > 1:
            return f"module_sync_{args[1]}"  # args[1] 通常是 module_name

    # 从 kwargs 中获取模块名
    module_name = kwargs.get('module_name', 'unknown')
    return f"module_sync_{module_name}"


# 预定义的重试配置
SYNC_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=30.0,  # 30秒基础延迟
    max_delay=300.0,  # 最大5分钟延迟
    exponential_base=2.0,
    jitter=True,
)

FAST_RETRY_CONFIG = RetryConfig(
    max_attempts=2,
    base_delay=10.0,  # 10秒基础延迟
    max_delay=60.0,  # 最大1分钟延迟
    exponential_base=1.5,
    jitter=True,
)


def get_retry_statistics() -> Dict[str, Any]:
    """获取重试统计信息"""
    return {
        "total_keys": len(_retry_state.attempts),
        "active_retries": {k: v for k, v in _retry_state.attempts.items() if v > 0},
        "last_success_times": _retry_state.last_success,
        "failure_reasons": _retry_state.failure_reasons,
    }


def reset_retry_state(key: Optional[str] = None):
    """重置重试状态"""
    if key:
        _retry_state.attempts.pop(key, None)
        _retry_state.last_success.pop(key, None)
        _retry_state.failure_reasons.pop(key, None)
        logger.info("重置重试状态", key=key)
    else:
        _retry_state.attempts.clear()
        _retry_state.last_success.clear()
        _retry_state.failure_reasons.clear()
        logger.info("重置所有重试状态")
