@echo off
chcp 65001 >nul
title YS-API V3.0 后端服务
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 YS-API V3.0 后端服务                    ║
echo ║                      Backend Service                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📍 当前目录: %cd%
echo 🔧 启动后端服务...

if not exist "backend\start_server.py" (
    echo ❌ 错误: 找不到 backend\start_server.py 文件
    echo 💡 请确保在项目根目录下运行此脚本
    pause
    exit /b 1
)

cd backend

echo 🔍 检查 Python 环境...
python --version
if errorlevel 1 (
    echo ❌ 错误: Python 未安装或未加入 PATH 环境变量
    echo � 请安装 Python 3.7+ 并将其加入 PATH
    pause
    exit /b 1
)

echo 📦 检查依赖包...
if exist requirements.txt (
    echo 🔧 安装依赖包...
    pip install -r requirements.txt
)

echo 🗄️ 检查数据库...
if not exist "ysapi.db" (
    echo 📊 创建数据库...
    cd ..
    python setup_test_env.py
    cd backend
)

echo.
echo ✅ 准备工作完成
echo 🚀 启动后端API服务...
echo 📡 服务地址: http://localhost:8000
echo � 健康检查: http://localhost:8000/health
echo.
echo ⏹️ 按 Ctrl+C 停止服务
echo.

python start_server.py

echo.
echo 🔄 服务已停止
pause
