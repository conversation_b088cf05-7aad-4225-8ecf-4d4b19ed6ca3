@echo off
chcp 65001 > nul
echo ============================================================
echo 启动系统并开始模块功能验证
echo ============================================================
echo.

echo 第三阶段进展汇报：
echo   基础启动验证：完成
echo   测试代码清理：75.7%%完成（从74个减少到18个）
echo   硬编码数据清理：40.8%%完成（从510处减少到302处）
echo   下一步：模块功能验证
echo.

echo 启动后端服务...
echo ------------------------------------------------------------
echo 请选择后端启动方式：
echo   1. 完整功能版本 (start_server.py)
echo   2. 简化版本 (start_simple.py)
echo   3. Docker方式
echo.
set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo 启动完整功能版本...
    echo 正在启动后端服务...
    start "YS-API后端服务" cmd /k "cd backend && python start_server.py"
    echo 服务地址: http://localhost:8000
    timeout /t 3 > nul
) else if "%choice%"=="2" (
    echo 启动简化版本...
    echo 正在启动后端服务...
    start "YS-API后端服务" cmd /k "cd backend && python start_simple.py"
    echo 服务地址: http://localhost:5000
    timeout /t 3 > nul
) else if "%choice%"=="3" (
    echo 启动Docker环境...
    echo 正在启动Docker服务...
    start "YS-API Docker服务" cmd /k "docker-compose -f docker-compose.strangler.yml up"
    echo 服务地址: http://localhost:8000
    timeout /t 3 > nul
) else (
    echo 无效选择，默认使用简化版本
    echo 正在启动后端服务...
    start "YS-API后端服务" cmd /k "cd backend && python start_simple.py"
    timeout /t 3 > nul
)

echo.
echo 启动前端服务...
echo ------------------------------------------------------------
echo 正在启动前端服务...
start "YS-API前端服务" cmd /k "cd frontend && python -m http.server 8080"
echo 前端地址: http://localhost:8080
timeout /t 3 > nul
echo.

echo 模块功能测试指南
echo ------------------------------------------------------------
echo 等待服务启动完成（约5秒）...
timeout /t 5 > nul
echo.
echo 现在开始模块功能验证...
echo 测试所有模块:
python scripts/module_functional_test.py --all
echo.
echo 测试单个模块示例:
echo   python scripts/module_functional_test.py --module "材料出库单列表查询"
echo   python scripts/module_functional_test.py --module "采购订单列表"
echo   python scripts/module_functional_test.py --module "产品入库单列表查询"
echo.

echo 📊 验证检查清单
echo ------------------------------------------------------------
echo 请确认以下各项：
echo   □ 后端服务正常启动（访问健康检查端点）
echo   □ 前端页面正常加载
echo   □ 数据库连接正常
echo   □ API端点响应正常
echo   □ 15个模块功能验证通过
echo.

echo ⚠️ 重要提醒
echo ------------------------------------------------------------
echo 1. 使用真实数据进行测试，确保业务逻辑无变化
echo 2. 重点验证数据处理逻辑的一致性
echo 3. 检查新系统的性能表现
echo 4. 记录任何发现的问题以便修复
echo.

echo 📋 完成后下一步
echo ------------------------------------------------------------
echo 1. 性能基准测试
echo 2. 数据一致性深度验证  
echo 3. 错误处理机制测试
echo 4. 生产环境部署准备
echo.

pause
