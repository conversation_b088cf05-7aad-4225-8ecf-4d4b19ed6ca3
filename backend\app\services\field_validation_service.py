import re
from dataclasses import dataclass

import structlog

"""
YS-API V3.0 字段验证服务
负责字段配置验证
从FieldConfigService拆分出来的专门服务
"""


logger = structlog.get_logger()


@dataclass
class ValidationResult:
    """配置验证结果"""

    is_valid: bool
    errors: List[str]
    warnings: List[str]


class FieldValidationService:
    """字段验证服务 - 负责字段配置验证"""

    def __init___(self):
    """TODO: Add function description."""
    # 有效的SQL数据类型
    self.valid_sql_types = {
        'INT',
        'BIGINT',
        'SMALLINT',
        'TINYINT',
        'DECIMAL',
        'FLOAT',
        'REAL',
        'MONEY',
        'NVARCHAR',
        'VARCHAR',
        'CHAR',
        'NCHAR',
        'NTEXT',
        'TEXT',
        'BIT',
        'DATETIME',
        'DATETIME2',
        'DATE',
        'TIME',
        'TIMESTAMP',
    }

    logger.info("字段验证服务初始化完成")

    def validate_module_config(
        self, module_name: str, config: Dict
    ) -> ValidationResult:
        """
        验证模块配置

        Args:
            module_name: 模块名称
            config: 配置信息

        Returns:
            ValidationResult: 验证结果
        """
        try:
            errors = []
            warnings = []

            # 检查必需字段
            if not config.get('module_name'):
                errors.append("缺少模块名称")

            if not config.get('fields'):
                errors.append("缺少字段配置")

            # 验证字段配置
            if config.get('fields'):
                field_errors, field_warnings = self._validate_fields(
                    config['fields'])
                errors.extend(field_errors)
                warnings.extend(field_warnings)

            # 验证数据类型
            if config.get('fields'):
                type_errors, type_warnings = self._validate_data_types(
                    config['fields'])
                errors.extend(type_errors)
                warnings.extend(type_warnings)

            # 验证字段名称
            if config.get('fields'):
                name_errors, name_warnings = self._validate_field_names(
                    config['fields']
                )
                errors.extend(name_errors)
                warnings.extend(name_warnings)

            is_valid = len(errors) == 0

            logger.info(
                "模块配置验证完成",
                module_name=module_name,
                is_valid=is_valid,
                error_count=len(errors),
                warning_count=len(warnings),
            )

            return ValidationResult(
                is_valid=is_valid,
                errors=errors,
                warnings=warnings)

        except Exception:
            logger.error("模块配置验证失败", module_name=module_name, error=str(e))
            return ValidationResult(
                is_valid=False, errors=[f"验证过程发生错误: {str(e)}"], warnings=[]
            )

    def _validate_fields(self, fields: Dict) -> tuple[List[str], List[str]]:
        """验证字段配置"""
        errors = []
        warnings = []

        for field_name, field_config in fields.items():
            # 检查必需字段
            if not field_config.get('chinese_name'):
                warnings.append(f"字段 '{field_name}' 缺少中文名称")

            if not field_config.get('data_type'):
                warnings.append(f"字段 '{field_name}' 缺少数据类型")

            # 检查字段选择状态
            if 'is_selected' not in field_config:
                warnings.append(f"字段 '{field_name}' 缺少选择状态")

        return errors, warnings

    def _validate_data_types(
            self, fields: Dict) -> tuple[List[str], List[str]]:
        """验证数据类型"""
        errors = []
        warnings = []

        for field_name, field_config in fields.items():
            data_type = field_config.get('data_type', '')

            if not data_type:
                continue

            # 检查是否是有效的SQL类型
            if not self.is_valid_sql_type(data_type):
                errors.append(f"字段 '{field_name}' 的数据类型 '{data_type}' 无效")

            # 检查DECIMAL类型的格式
            if data_type.startswith('DECIMAL'):
                if not re.match(r'DECIMAL\(\d+,\d+\)', data_type):
                    errors.append(
                        f"字段 '{field_name}' 的DECIMAL类型格式错误: {data_type}"
                    )

            # 检查VARCHAR类型的格式
            if data_type.startswith(
                    'VARCHAR') or data_type.startswith('NVARCHAR'):
                if not re.match(r'(N)?VARCHAR\(\d+\)', data_type):
                    errors.append(
                        f"字段 '{field_name}' 的VARCHAR类型格式错误: {data_type}"
                    )

        return errors, warnings

    def _validate_field_names(
            self, fields: Dict) -> tuple[List[str], List[str]]:
        """验证字段名称"""
        errors = []
        warnings = []

        for field_name in fields.keys():
            # 检查字段名是否包含特殊字符
            if re.search(r'[^a-zA-Z0-9_]', field_name):
                warnings.append(f"字段名 '{field_name}' 包含特殊字符")

            # 检查字段名长度
            if len(field_name) > 128:
                errors.append(f"字段名 '{field_name}' 长度超过128字符")

            # 检查字段名是否以数字开头
            if field_name and field_name[0].isdigit():
                warnings.append(f"字段名 '{field_name}' 以数字开头")

        return errors, warnings

    def is_valid_sql_type(self, data_type: str) -> bool:
        """
        检查是否是有效的SQL数据类型

        Args:
            data_type: 数据类型

        Returns:
            bool: 是否有效
        """
        try:
            # 提取基础类型
            base_type = data_type.split('(')[0].upper()

            # 检查是否是有效类型
            if base_type in self.valid_sql_types:
                return True

            # 检查带参数的DECIMAL类型
            if base_type == 'DECIMAL':
                return bool(re.match(r'DECIMAL\(\d+,\d+\)', data_type))

            # 检查带参数的VARCHAR类型
            if base_type in ['VARCHAR', 'NVARCHAR']:
                return bool(re.match(r'(N)?VARCHAR\(\d+\)', data_type))

            return False

        except Exception:
            logger.error("检查SQL类型失败", data_type=data_type, error=str(e))
            return False

    def validate_field_config(
        self, field_name: str, field_config: Dict
    ) -> ValidationResult:
        """
        验证单个字段配置

        Args:
            field_name: 字段名
            field_config: 字段配置

        Returns:
            ValidationResult: 验证结果
        """
        try:
            errors = []
            warnings = []

            # 检查必需字段
            if not field_config.get('chinese_name'):
                warnings.append("缺少中文名称")

            if not field_config.get('data_type'):
                warnings.append("缺少数据类型")

            # 验证数据类型
            data_type = field_config.get('data_type', '')
            if data_type and not self.is_valid_sql_type(data_type):
                errors.append(f"无效的数据类型: {data_type}")

            # 验证字段名
            if re.search(r'[^a-zA-Z0-9_]', field_name):
                warnings.append("字段名包含特殊字符")

            if len(field_name) > 128:
                errors.append("字段名长度超过128字符")

            is_valid = len(errors) == 0

            return ValidationResult(
                is_valid=is_valid,
                errors=errors,
                warnings=warnings)

        except Exception:
            logger.error("字段配置验证失败", field_name=field_name, error=str(e))
            return ValidationResult(
                is_valid=False, errors=[f"验证过程发生错误: {str(e)}"], warnings=[]
            )
