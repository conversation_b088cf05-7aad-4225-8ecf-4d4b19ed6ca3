import hashlib
import json
import logging
import re
from collections import defaultdict
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统化重复文件识别工具
按照识别 → 验证 → 清理流程
"""


class DuplicateFileDetector:
    def __init___(self, project_root="."):
    """TODO: Add function description."""
    self.project_root = Path(project_root)
    self.duplicates = defaultdict(list)
    self.analysis_report = {
        'hash_duplicates': {},
        'name_pattern_duplicates': {},
        'content_similarity': {},
        'backup_files': [],
        'temp_files': [],
    }

    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s')
    self.logger = logging.getLogger(__name__)

    def calculate_file_hash(self, file_path):
        """计算文件哈希值"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return None

    def find_hash_duplicates(self):
        """基于哈希值查找完全重复的文件"""
        self.logger.info("🔍 Step 1: 基于内容哈希查找重复文件...")

        hash_map = defaultdict(list)

        for file_path in self.project_root.rglob('*'):
            if file_path.is_file():
                # 跳过特定类型文件
                if file_path.suffix in ['.git', '.log', '.tmp']:
                    continue
                if any(part.startswith('.') for part in file_path.parts):
                    continue

                file_hash = self.calculate_file_hash(file_path)
                if file_hash:
                    hash_map[file_hash].append(str(file_path))

        # 找出重复的文件
        duplicates =
        {h: files for h,
         files in hash_map.items() if len(files) > 1
         }
        self.analysis_report['hash_duplicates'] = duplicates

        self.logger.info(f"   ✅ 发现 {len(duplicates)} 组哈希重复文件")
        for hash_val, files in duplicates.items():
            self.logger.info(f"      重复组: {len(files)} 个文件")
            for file in files:
                self.logger.info(f"        - {file}")

    def find_name_pattern_duplicates(self):
        """基于文件名模式查找可能重复的文件"""
        self.logger.info("🔍 Step 2: 基于命名模式查找可疑重复文件...")

        patterns = [
            r'.*_backup.*',
            r'.*_copy.*',
            r'.*_副本.*',
            r'.*\.backup$',
            r'.*\.bak$',
            r'.*\.old$',
            r'.*_v\d+.*',
            r'.*_\d{8}.*',  # 日期格式
            r'.*test.*',
            r'.*temp.*',
            r'.*_fixed.*',
            r'.*_migrated.*',
        ]

        pattern_matches = defaultdict(list)

        for file_path in self.project_root.rglob('*'):
            if file_path.is_file():
                for pattern in patterns:
                    if re.search(pattern, str(file_path), re.IGNORECASE):
                        pattern_matches[pattern].append(str(file_path))
                        break

        self.analysis_report['name_pattern_duplicates'] = dict(pattern_matches)

        self.logger.info(
            f"   ✅ 发现 {sum(len(files) for files in pattern_matches.values())} 个命名可疑文件"
        )
        for pattern, files in pattern_matches.items():
            if files:
                self.logger.info(f"      模式 {pattern}: {len(files)} 个文件")

    def find_similar_content_files(self):
        """查找内容相似的代码文件"""
        self.logger.info("🔍 Step 3: 检查代码内容相似性...")

        # 检查Python文件的函数重复
        python_files = list(self.project_root.rglob('*.py'))
        function_signatures = defaultdict(list)

        for py_file in python_files:
            try:
                content = py_file.read_text(encoding='utf-8')

                # 提取函数定义
                functions = re.findall(r'def\s+(\w+)\s*\([^)]*\):', content)
                for func in functions:
                    function_signatures[func].append(str(py_file))

            except Exception:
                continue

        # 找出重复的函数名
        duplicate_functions = {
            func: files
            for func, files in function_signatures.items()
            if len(set(files)) > 1
        }

        self.analysis_report['content_similarity'] = duplicate_functions

        self.logger.info(f"   ✅ 发现 {len(duplicate_functions)} 个重复函数名")

        # 找出可能的工具脚本重复
        tool_keywords =
        ['fixer',
         'cleaner',
         'checker',
         'test',
         'builder',
         'generator'
         ]
        tool_files = defaultdict(list)

        for py_file in python_files:
            filename = py_file.stem.lower()
            for keyword in tool_keywords:
                if keyword in filename:
                    tool_files[keyword].append(str(py_file))

        self.logger.info("📋 工具脚本分析:")
        for keyword, files in tool_files.items():
            if len(files) > 1:
                self.logger.info(f"      {keyword} 相关: {len(files)} 个文件")
                for file in files:
                    self.logger.info(f"        - {file}")

    def identify_backup_and_temp_files(self):
        """识别明显的备份和临时文件"""
        self.logger.info("🔍 Step 4: 识别备份和临时文件...")

        backup_patterns = [
            '*.backup',
            '*.bak',
            '*_backup_*',
            '*_copy_*',
            '*.old',
            '*.tmp',
            '*.temp',
            '*~',
        ]

        backup_files = []
        for pattern in backup_patterns:
            backup_files.extend(self.project_root.rglob(pattern))

        self.analysis_report['backup_files'] = [str(f) for f in backup_files]

        # 查找明显的临时测试文件
        temp_files = []
        temp_patterns = ['test_*.py', '*_test_*.py', 'debug_*.py', 'temp_*.py']

        for pattern in temp_patterns:
            temp_files.extend(self.project_root.rglob(pattern))

        self.analysis_report['temp_files'] = [
            str(f) for f in temp_files if 'tests/' not in str(f)
        ]

        self.logger.info(f"   ✅ 发现 {len(backup_files)} 个备份文件")
        self.logger.info(
            f"   ✅ 发现 {len(self.analysis_report['temp_files'])} 个临时文件"
        )

    def generate_cleanup_plan(self):
        """生成清理计划"""
        self.logger.info("📋 生成清理计划...")

        cleanup_plan = {
            'safe_to_delete': [],
            'needs_verification': [],
            'merge_candidates': [],
            'rename_suggestions': [],
        }

        # 安全删除：明显的备份文件
        cleanup_plan['safe_to_delete'].extend(
            self.analysis_report['backup_files'])

        # 需要验证：哈希重复文件
        for hash_val, files in self.analysis_report['hash_duplicates'].items():
            cleanup_plan['needs_verification'].append(
                {
                    'type': 'hash_duplicate',
                    'files': files,
                    'action': 'keep_one_delete_others',
                }
            )

        # 合并候选：相似功能的文件
        function_groups = defaultdict(list)
        for func, files in self.analysis_report['content_similarity'].items():
            if len(set(files)) > 2:  # 3个或更多文件有相同函数
                function_groups[func] = list(set(files))

        for func, files in function_groups.items():
            cleanup_plan['merge_candidates'].append(
                {
                    'function': func,
                    'files': files,
                    'suggestion': f'考虑将{func}函数统一到一个文件中',
                }
            )

        # 保存清理计划
        plan_file = "duplicate_cleanup_plan.json"
        with open(plan_file, 'w', encoding='utf-8') as f:
            json.dump(
                {'analysis_report': self.analysis_report,
                    'cleanup_plan': cleanup_plan},
                f,
                ensure_ascii=False,
                indent=2,
            )

        self.logger.info(f"   ✅ 清理计划已保存到: {plan_file}")

        return cleanup_plan

    def execute_safe_cleanup(self, cleanup_plan, dry_run=True):
        """执行安全清理"""
        self.logger.info(f"🧹 执行清理 (dry_run={dry_run})...")

        if dry_run:
            self.logger.info("   ⚠️ 这是预演模式，不会实际删除文件")

        # 创建垃圾文件夹
        trash_dir = Path(".cleanup_trash")
        if not dry_run:
            trash_dir.mkdir(exist_ok=True)

        deleted_count = 0

        # 删除明显的备份文件
        for file_path in cleanup_plan['safe_to_delete']:
            path_obj = Path(file_path)
            if path_obj.exists():
                if dry_run:
                    self.logger.info(f"      [DRY RUN] 会删除: {file_path}")
                else:
                    # 移动到垃圾文件夹而不是直接删除
                    trash_file = trash_dir / path_obj.name
                    path_obj.rename(trash_file)
                    self.logger.info(f"      ✅ 已移动到垃圾箱: {file_path}")
                deleted_count += 1

        self.logger.info(
            f"   📊 {'预计' if dry_run else '实际'}清理文件数: {deleted_count}"
        )

        # 处理哈希重复文件
        for item in cleanup_plan['needs_verification']:
            if item['type'] == 'hash_duplicate':
                files = item['files']
                self.logger.info(f"   🔍 重复文件组 ({len(files)} 个):")
                for i, file_path in enumerate(files):
                    status = "👑 保留" if i == 0 else "🗑️ 删除"
                    self.logger.info(f"      {status}: {file_path}")

                if not dry_run:
                    # 保留第一个，删除其余
                    for file_path in files[1:]:
                        path_obj = Path(file_path)
                        if path_obj.exists():
                            trash_file = trash_dir / f"dup_{path_obj.name}"
                            path_obj.rename(trash_file)

        return deleted_count


def main():
    """主函数"""
    print("🔧 YS-API V3.0 系统化重复文件清理工具")
    print("=" * 60)

    detector = DuplicateFileDetector()

    # 执行识别流程
    detector.find_hash_duplicates()
    detector.find_name_pattern_duplicates()
    detector.find_similar_content_files()
    detector.identify_backup_and_temp_files()

    # 生成清理计划
    cleanup_plan = detector.generate_cleanup_plan()

    # 询问是否执行清理
    print("\n" + "=" * 60)
    print("📋 清理计划总结:")
    print(f"   🗑️ 安全删除: {len(cleanup_plan['safe_to_delete'])} 个文件")
    print(f"   🔍 需要验证: {len(cleanup_plan['needs_verification'])} 组重复")
    print(f"   🔄 合并建议: {len(cleanup_plan['merge_candidates'])} 个函数组")

    choice = input("\n是否执行安全清理? (y/n/d=dry_run): ").lower()

    if choice == 'y':
        detector.execute_safe_cleanup(cleanup_plan, dry_run=False)
        print("✅ 清理完成！垃圾文件已移动到 .cleanup_trash/ 目录")
    elif choice == 'd':
        detector.execute_safe_cleanup(cleanup_plan, dry_run=True)
    else:
        print("⏹️ 清理已取消")


if __name__ == "__main__":
    main()
