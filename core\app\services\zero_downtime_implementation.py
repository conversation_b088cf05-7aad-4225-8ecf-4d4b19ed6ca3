import asyncio
import random
import string
from pathlib import Path

import structlog
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from backend.app.services.ys_api_client import YSAPIClient

from .sync_status_manager import get_sync_status_manager

#!/usr/bin/env python3
"""
YS-API 零停机数据同步实现
基于双缓存（影子表）方案，实现真正的零停机数据同步
"""


logger = structlog.get_logger()


class ZeroDowntimeDataWriter:
    """零停机数据写入器"""

    def __init___(self, module_name: str, session_factory):
    """TODO: Add function description."""
    self.module_name = module_name
    self.new_table_name = f"{module_name}_New"
    self.tpl_table_name = f"{module_name}_Tpl"
    self.session_factory = session_factory

    # 集成同步状态管理器
    self.sync_manager = get_sync_status_manager()

    def _generate_backup_table_name(self, module_name: str) -> str:
        """生成唯一的备份表名称"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        random_suffix = ''.join(
            random.choices(
                string.ascii_lowercase +
                string.digits,
                k=4))
        return f"{module_name}_Bak_{timestamp}_{random_suffix}"

    async def write_with_zero_downtime(self, data: List[Dict]) -> Dict:
        """零停机写入数据 - 集成状态管理"""
        start_time = datetime.now()

        try:
            logger.info(f"开始零停机写入模块: {self.module_name}",
                        data_count=len(data))

            # Step 1: 检查表锁状态
            table_name = self.module_name
            if await self.sync_manager.is_table_locked(table_name):
                lock_info = await self.sync_manager.get_table_lock_info(table_name)
                raise Exception(
                    f"表 {table_name} 被模块 {lock_info.get('module')} 锁定")

            # Step 2: 防呆检查
            await self._pre_switch_checks()

            # Step 3: 写入影子表（不清空主表）
            await self._write_to_shadow_table(data)

            # Step 4: 将影子表加入切换队列
            shadow_table_name = self.new_table_name
            await self.sync_manager.enqueue_shadow_table(table_name, shadow_table_name, self.module_name)

            # Step 5: 处理影子表队列（串行切换）
            await self.sync_manager.process_shadow_table_queue(table_name, self._execute_atomic_switch_callback)

            # Step 4: 清理旧备份表
            await self._cleanup_old_backups()

            # Step 5: 记录切换日志
            duration_ms = int(
                (datetime.now() - start_time).total_seconds() * 1000)
            await self._log_switch_success(duration_ms)

            logger.info(f"零停机写入成功: {self.module_name}",
                        duration_ms=duration_ms,
                        data_count=len(data))

            return {
                "success": True,
                "message": "零停机写入成功",
                "duration_ms": duration_ms,
                "data_count": len(data)
            }

        except Exception:
            logger.error(f"零停机写入失败: {self.module_name}", error=str(e))
            await self._log_switch_failure(str(e))
            return {"success": False, "message": str(e)}

    async def _pre_switch_checks(self):
        """切换前防呆检查"""
        async with self.session_factory() as session:
            # 1. 检查长事务锁
            lock_check_sql = """
            SELECT COUNT(*) as lock_count
            FROM sys.dm_tran_locks
            WHERE resource_associated_entity_id = OBJECT_ID(:table_name)
              AND request_mode IN ('S','X','Sch-M')
            """

            result = await session.execute(
                text(lock_check_sql),
                {"table_name": f"dbo.{self.module_name}"}
            )
            lock_count = result.scalar()

            if lock_count > 0:
                raise Exception(f"存在阻塞锁，无法执行切换: {lock_count}个锁")

            # 2. 检查影子表是否存在
            table_check_sql = """
            SELECT COUNT(*) as table_count
            FROM sys.tables
            WHERE name = :table_name
            """

            result = await session.execute(
                text(table_check_sql),
                {"table_name": self.new_table_name}
            )
            table_count = result.scalar()

            if table_count == 0:
                raise Exception(f"影子表不存在: {self.new_table_name}")

            # 3. 检查影子表是否为空
            row_check_sql = f"SELECT COUNT(*) as row_count FROM dbo.{self.new_table_name}"
            result = await session.execute(text(row_check_sql))
            row_count = result.scalar()

            if row_count > 0:
                logger.warning(f"影子表不为空，将清空: {row_count}行")
                await session.execute(text(f"TRUNCATE TABLE dbo.{self.new_table_name}"))
                await session.commit()

            # 4. 结构一致性校验
            await self._validate_structure_consistency(session)

    async def _validate_structure_consistency(self, session: AsyncSession):
        """验证结构一致性"""
        structure_check_sql = """
        SELECT
            c1.column_name,
            c1.data_type,
            c1.character_maximum_length,
            c1.is_nullable
        FROM INFORMATION_SCHEMA.COLUMNS c1
        WHERE c1.table_name = :tpl_table
        EXCEPT
        SELECT
            c2.column_name,
            c2.data_type,
            c2.character_maximum_length,
            c2.is_nullable
        FROM INFORMATION_SCHEMA.COLUMNS c2
        WHERE c2.table_name = :new_table
        """

        result = await session.execute(
            text(structure_check_sql),
            {
                "tpl_table": self.tpl_table_name,
                "new_table": self.new_table_name
            }
        )

        differences = result.fetchall()
        if differences:
            diff_info = [
                f"{row.column_name}({row.data_type})" for row in differences]
            raise Exception(f"结构不一致: {', '.join(diff_info)}")

    async def _write_to_shadow_table(self, data: List[Dict]):
        """写入影子表"""
        if not data:
            logger.warning(f"模块 {self.module_name} 没有数据需要写入")
            return

        async with self.session_factory() as session:
            # 清空影子表
            await session.execute(text(f"TRUNCATE TABLE dbo.{self.new_table_name}"))

            # 批量写入数据
            batch_size = 1000
            for i in range(0, len(data), batch_size):
                batch = data[i:i + batch_size]
                await self._insert_batch(session, batch)

                if i % 10000 == 0:
                    logger.info(f"已写入 {i + len(batch)} 条记录到影子表")

            await session.commit()
            logger.info(f"影子表写入完成: {len(data)} 条记录")

    async def _insert_batch(self, session: AsyncSession, batch: List[Dict]):
        """批量插入数据"""
        if not batch:
            return

        # 构建动态INSERT语句
        columns = list(batch[0].keys())
        placeholders = [f":{col}" for col in columns]

        insert_sql = f"""
        INSERT INTO dbo.{self.new_table_name}
        ({', '.join(columns)})
        VALUES ({', '.join(placeholders)})
        """

        # 执行批量插入
        for row in batch:
            await session.execute(text(insert_sql), row)

    async def _execute_atomic_switch(self):
        """执行原子切换（增强事务安全）"""
        backup_table_name = self._generate_backup_table_name(self.module_name)

        sync_manager = get_sync_status_manager()

        lock_id = await sync_manager.acquire_table_lock(
            self.module_name, f"atomic_switch_{self.module_name}"
        )
        if not lock_id:
            raise Exception(
                f"无法获取表 {self.module_name} 的锁，可能有其他同步操作正在进行"
            )

        switch_sql = f"""
        SET XACT_ABORT ON;
        BEGIN TRY
            BEGIN TRAN;
                -- 验证影子表数据完整性
                DECLARE @shadow_count INT = (SELECT COUNT(*) FROM dbo.{self.new_table_name});
                IF @shadow_count = 0
                    THROW 50001, '影子表为空，无法执行切换', 1;

                -- 重命名当前表为备份表
                EXEC sp_rename 'dbo.{self.module_name}', '{backup_table_name}';

                -- 重命名影子表为当前表
                EXEC sp_rename 'dbo.{self.new_table_name}', '{self.module_name}';

                -- 验证切换后的表存在性
                IF OBJECT_ID('dbo.{self.module_name}') IS NULL
                    THROW 50002, '切换后主表不存在', 1;

                -- 重新授权（保持权限一致）
                GRANT SELECT ON dbo.{self.module_name} TO [YS-API-Reader];
                GRANT INSERT, UPDATE, DELETE ON dbo.{self.module_name} TO [YS-API-Writer];

                -- 验证数据完整性
                DECLARE @new_count INT = (SELECT COUNT(*) FROM dbo.{self.module_name});
                IF @new_count != @shadow_count
                    THROW 50003, '切换后数据行数不匹配', 1;

            COMMIT TRAN;
            SELECT 'SUCCESS' as status,
                   @shadow_count as row_count,
                   '{backup_table_name}' as backup_table;
        END TRY
        BEGIN CATCH
            IF @@TRANCOUNT > 0
                ROLLBACK TRAN;

            DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
            -- 尝试恢复原表名
            BEGIN TRY
                IF OBJECT_ID('dbo.{self.module_name}') IS NULL
                   AND OBJECT_ID('dbo.{backup_table_name}') IS NOT NULL
                    EXEC sp_rename 'dbo.{backup_table_name}', '{self.module_name}';
            END TRY
            BEGIN CATCH
                SELECT 'RECOVERY_FAILED' as status, @ErrorMessage as error_message;
            END CATCH
            THROW;
        END CATCH
        """

        session = await self.session_factory()
        try:
            result = await session.execute(text(switch_sql))
            row = result.fetchone()
            if row.status == "SUCCESS":
                await session.commit()
                logger.info(
                    f"原子切换完成: {self.module_name} -> {backup_table_name}, 行数: {row.row_count}"
                )
            else:
                await session.rollback()
                raise Exception(f"切换失败: {row.error_message}")
        except Exception:
            await session.rollback()
            logger.error(f"原子切换失败: {self.module_name}", error=str(e))
            raise
        finally:
            await session.close()
            # 释放表锁
            await sync_manager.release_table_lock(self.module_name, lock_id)

    async def _pre_switch_validation(self):
        """切换前验证"""
        session = await self.session_factory()
        try:
            # 验证影子表存在且有数据
            validation_sql = f"""
            SELECT
                (SELECT COUNT(*) FROM dbo.{self.new_table_name}) as shadow_count,
                (SELECT COUNT(*) FROM dbo.{self.module_name}) as original_count,
                (SELECT COUNT(*) FROM sys.indexes WHERE object_id = OBJECT_ID('dbo.{self.new_table_name}')) as index_count
            """

            result = await session.execute(text(validation_sql))
            row = result.fetchone()

            if row.shadow_count == 0:
                raise Exception("影子表为空，无法执行切换")

            if row.index_count == 0:
                logger.warning(f"影子表 {self.new_table_name} 没有索引，可能影响性能")

            logger.info(
                f"切换前验证通过: 影子表{row.shadow_count}行, 原表{row.original_count}行, 索引{row.index_count}个")

        finally:
            await session.close()

    async def _cleanup_old_backups(self):
        """清理2天前的备份表"""
        cleanup_sql = f"""
        DECLARE @sql NVARCHAR(MAX) = '';
        SELECT @sql += 'DROP TABLE ' + QUOTENAME(name) + ';'
        FROM sys.tables
        WHERE name LIKE '{self.module_name}_Bak[_]%'
          AND create_date < DATEADD(day, -2, GETDATE());

        IF LEN(@sql) > 0
            EXEC (@sql);
        """

        async with self.session_factory() as session:
            await session.execute(text(cleanup_sql))
            await session.commit()

    async def _log_switch_success(self, duration_ms: int):
        """记录成功切换日志"""
        log_sql = """
        INSERT INTO dbo.sync_switch_log (
            module_name,
            switch_time,
            status,
            duration_ms,
            backup_table_name
        ) VALUES (
            :module_name,
            :switch_time,
            'SUCCESS',
            :duration_ms,
            :backup_table_name
        )
        """

        backup_table_name = self._generate_backup_table_name(self.module_name)

        async with self.session_factory() as session:
            await session.execute(
                text(log_sql),
                {
                    "module_name": self.module_name,
                    "switch_time": datetime.now(),
                    "duration_ms": duration_ms,
                    "backup_table_name": backup_table_name
                }
            )
            await session.commit()

    async def _log_switch_failure(self, error_message: str):
        """记录失败切换日志"""
        log_sql = """
        INSERT INTO dbo.sync_switch_log (
            module_name,
            switch_time,
            status,
            error_message
        ) VALUES (
            :module_name,
            :switch_time,
            'FAILED',
            :error_message
        )
        """

        async with self.session_factory() as session:
            await session.execute(
                text(log_sql),
                {
                    "module_name": self.module_name,
                    "switch_time": datetime.now(),
                    "error_message": error_message
                }
            )
            await session.commit()


class ZeroDowntimeAutoSyncScheduler:
    """零停机自动同步调度器"""

    def __init___(self, data_write_manager, session_factory):
    """TODO: Add function description."""
    self.data_write_manager = data_write_manager
    self.session_factory = session_factory
    self.is_syncing = False
    self.zero_downtime_writers = {}

    async def execute_zero_downtime_sync(self, modules: List[str]) -> Dict:
        """执行零停机同步"""
        if self.is_syncing:
            logger.warning("已有同步任务在执行中，跳过此次同步")
            return {"success": False, "message": "已有同步任务在执行中"}

        self.is_syncing = True
        start_time = datetime.now()

        try:
            results = {}

            for module_name in modules:
                try:
                    logger.info(f"开始零停机同步模块: {module_name}")

                    # 获取模块数据
                    module_data = await self._fetch_module_data(module_name)

                    # 使用零停机写入器
                    writer = ZeroDowntimeDataWriter(
                        module_name, self.session_factory)
                    result = await writer.write_with_zero_downtime(module_data)

                    results[module_name] = result

                    if result.get("success"):
                        logger.info(f"模块 {module_name} 零停机同步成功")
                    else:
                        logger.error(
                            f"模块 {module_name} 零停机同步失败: {result.get('message')}")

                except Exception:
                    logger.error(f"模块 {module_name} 同步异常", error=str(e))
                    results[module_name] = {
                        "success": False,
                        "message": str(e)
                    }

            # 计算总体结果
            success_count = sum(
                1 for r in results.values() if r.get(
                    "success", False))
            failed_count = len(results) - success_count

            overall_success = failed_count == 0

            duration = (datetime.now() - start_time).total_seconds()

            logger.info("零停机同步完成",
                        total_modules=len(modules),
                        success_count=success_count,
                        failed_count=failed_count,
                        duration=duration)

            return {
                "success": overall_success,
                "total_modules": len(modules),
                "success_count": success_count,
                "failed_count": failed_count,
                "duration_seconds": duration,
                "results": results
            }

        except Exception:
            logger.error("零停机同步执行异常", error=str(e))
            return {"success": False, "message": str(e)}
        finally:
            self.is_syncing = False

    async def _fetch_module_data(self, module_name: str) -> List[Dict]:
        """获取模块数据"""
        # 这里调用现有的数据获取逻辑
        # 可以根据实际情况调整
        try:
            # 使用现有的API客户端获取数据

            client = YSAPIClient()
            data = await client.fetch_module_data(module_name)

            return data or []

        except Exception:
            logger.error(f"获取模块 {module_name} 数据失败", error=str(e))
            return []

    async def _execute_atomic_switch_callback(
            self, table_name: str, shadow_table_name: str) -> Dict:
        """原子切换回调方法"""
        try:
            # 执行原子切换
            await self._execute_atomic_switch()

            # 延迟5分钟删除旧表
            asyncio.create_task(self._delayed_cleanup_old_table(table_name))

            return {"success": True, "message": "原子切换成功"}

        except Exception:
            logger.error(f"原子切换失败: {table_name}", error=str(e))
            return {"success": False, "message": str(e)}

    async def _delayed_cleanup_old_table(self, table_name: str):
        """延迟清理旧表"""
        try:
            # 等待5分钟，让活跃事务完成
            await asyncio.sleep(300)

            # 清理旧备份表
            await self._cleanup_old_backups()

            logger.info(f"延迟清理完成: {table_name}")

        except Exception:
            logger.error(f"延迟清理失败: {table_name}", error=str(e))


class ZeroDowntimeManager:
    """零停机管理器"""

    def __init___(self, session_factory):
    """TODO: Add function description."""
    self.session_factory = session_factory

    async def initialize_zero_downtime_tables(self, module_name: str) -> Dict:
        """初始化零停机表结构"""
        try:
            async with self.session_factory() as session:
                # 1. 创建影子表
                await self._create_shadow_table(session, module_name)

                # 2. 创建模板表
                await self._create_template_table(session, module_name)

                # 3. 创建切换日志表
                await self._create_switch_log_table(session)

                logger.info(f"零停机表结构初始化完成: {module_name}")
                return {"success": True, "message": "初始化完成"}

        except Exception:
            logger.error(f"零停机表结构初始化失败: {module_name}", error=str(e))
            return {"success": False, "message": str(e)}

    async def _create_shadow_table(
            self,
            session: AsyncSession,
            module_name: str):
        """创建影子表（包含索引和约束复制）"""
        try:
            # 1. 创建基础表结构
            create_sql = f"""
            IF OBJECT_ID('dbo.{module_name}_New') IS NULL
            BEGIN
                SELECT TOP 0 * INTO dbo.{module_name}_New FROM dbo.{module_name};
            END
            """

            await session.execute(text(create_sql))

            # 2. 复制索引
            await self._copy_indexes(session, module_name, f"{module_name}_New")

            # 3. 复制约束
            await self._copy_constraints(session, module_name, f"{module_name}_New")

            # 4. 复制触发器
            await self._copy_triggers(session, module_name, f"{module_name}_New")

            await session.commit()
            logger.info(f"影子表创建完成: {module_name}_New (包含索引、约束、触发器)")

        except Exception:
            await session.rollback()
            logger.error(f"创建影子表失败: {module_name}_New", error=str(e))
            raise

    async def _create_template_table(
            self,
            session: AsyncSession,
            module_name: str):
        """创建模板表"""
        create_sql = f"""
        IF OBJECT_ID('dbo.{module_name}_Tpl') IS NULL
        BEGIN
            SELECT TOP 0 * INTO dbo.{module_name}_Tpl FROM dbo.{module_name};
        END
        """

        await session.execute(text(create_sql))
        await session.commit()

    async def _create_switch_log_table(self, session: AsyncSession):
        """创建切换日志表"""
        create_sql = """
        IF OBJECT_ID('dbo.sync_switch_log') IS NULL
        BEGIN
            CREATE TABLE dbo.sync_switch_log (
                id INT IDENTITY(1,1) PRIMARY KEY,
                module_name NVARCHAR(100) NOT NULL,
                switch_time DATETIME2 NOT NULL,
                status NVARCHAR(20) NOT NULL,
                duration_ms INT,
                backup_table_name NVARCHAR(200),
                error_message NVARCHAR(MAX)
            );

            CREATE INDEX IX_sync_switch_log_module_time
            ON dbo.sync_switch_log(module_name, switch_time);

            -- 添加清理策略：保留最近30天的日志
            CREATE INDEX IX_sync_switch_log_cleanup
            ON dbo.sync_switch_log(switch_time)
            WHERE switch_time < DATEADD(day, -30, GETDATE());
        END
        """

        await session.execute(text(create_sql))
        await session.commit()

    async def _copy_indexes(
            self,
            session: AsyncSession,
            source_table: str,
            target_table: str):
        """复制索引"""
        try:
            # 获取源表的所有索引
            index_sql = """
            SELECT
                i.name as index_name,
                i.type_desc as index_type,
                i.is_unique,
                i.is_primary_key,
                STUFF((
                    SELECT ', ' + c.name + CASE WHEN ic.is_descending_key = 1 THEN ' DESC' ELSE ' ASC' END
                    FROM sys.index_columns ic
                    JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
                    WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id
                    ORDER BY ic.key_ordinal
                    FOR XML PATH('')
                ), 1, 2, '') as columns
            FROM sys.indexes i
            WHERE i.object_id = OBJECT_ID(:source_table)
              AND i.is_hypothetical = 0
              AND i.name IS NOT NULL
            """

            result = await session.execute(text(index_sql), {"source_table": f"dbo.{source_table}"})
            indexes = result.fetchall()

            for index in indexes:
                if index.is_primary_key:
                    # 主键索引
                    create_index_sql = f"""
                    ALTER TABLE dbo.{target_table}
                    ADD CONSTRAINT PK_{target_table} PRIMARY KEY ({index.columns})
                    """
                elif index.is_unique:
                    # 唯一索引
                    create_index_sql = f"""
                    CREATE UNIQUE INDEX {index.index_name}
                    ON dbo.{target_table} ({index.columns})
                    """
                else:
                    # 普通索引
                    create_index_sql = f"""
                    CREATE INDEX {index.index_name}
                    ON dbo.{target_table} ({index.columns})
                    """

                await session.execute(text(create_index_sql))
                logger.debug(f"复制索引: {index.index_name} -> {target_table}")

        except Exception:
            logger.error(
                f"复制索引失败: {source_table} -> {target_table}",
                error=str(e))
            raise

    async def _copy_constraints(
            self,
            session: AsyncSession,
            source_table: str,
            target_table: str):
        """复制约束"""
        try:
            # 获取源表的所有约束
            constraint_sql = """
            SELECT
                c.name as constraint_name,
                c.type as constraint_type,
                cc.definition as check_definition,
                dc.definition as default_definition
            FROM sys.objects c
            LEFT JOIN sys.check_constraints cc ON c.object_id = cc.object_id
            LEFT JOIN sys.default_constraints dc ON c.object_id = dc.object_id
            WHERE c.parent_object_id = OBJECT_ID(:source_table)
              AND c.type IN ('C', 'D', 'F', 'UQ')
            """

            result = await session.execute(text(constraint_sql), {"source_table": f"dbo.{source_table}"})
            constraints = result.fetchall()

            for constraint in constraints:
                if constraint.constraint_type == 'C':  # Check约束
                    create_constraint_sql = f"""
                    ALTER TABLE dbo.{target_table}
                    ADD CONSTRAINT {constraint.constraint_name}
                    CHECK ({constraint.check_definition})
                    """
                elif constraint.constraint_type == 'D':  # Default约束
                    create_constraint_sql = f"""
                    ALTER TABLE dbo.{target_table}
                    ADD CONSTRAINT {constraint.constraint_name}
                    DEFAULT {constraint.default_definition}
                    """
                elif constraint.constraint_type == 'F':  # Foreign Key约束
                    # 外键约束需要特殊处理，这里简化处理
                    logger.warning(
                        f"跳过外键约束复制: {constraint.constraint_name} (需要手动处理)")
                    continue
                elif constraint.constraint_type == 'UQ':  # Unique约束
                    # 唯一约束已在索引复制中处理
                    continue

                await session.execute(text(create_constraint_sql))
                logger.debug(
                    f"复制约束: {constraint.constraint_name} -> {target_table}")

        except Exception:
            logger.error(
                f"复制约束失败: {source_table} -> {target_table}",
                error=str(e))
            raise

    async def _copy_triggers(
            self,
            session: AsyncSession,
            source_table: str,
            target_table: str):
        """复制触发器"""
        try:
            # 获取源表的所有触发器
            trigger_sql = """
            SELECT
                t.name as trigger_name,
                m.definition as trigger_definition
            FROM sys.triggers t
            JOIN sys.sql_modules m ON t.object_id = m.object_id
            WHERE t.parent_id = OBJECT_ID(:source_table)
            """

            result = await session.execute(text(trigger_sql), {"source_table": f"dbo.{source_table}"})
            triggers = result.fetchall()

            for trigger in triggers:
                # 替换触发器定义中的表名
                trigger_def = trigger.trigger_definition.replace(
                    f"[dbo].[{source_table}]",
                    f"[dbo].[{target_table}]"
                )

                create_trigger_sql = f"""
                {trigger_def}
                """

                await session.execute(text(create_trigger_sql))
                logger.debug(
                    f"复制触发器: {trigger.trigger_name} -> {target_table}")

        except Exception:
            logger.error(
                f"复制触发器失败: {source_table} -> {target_table}",
                error=str(e))
            raise

    async def emergency_rollback(self, module_name: str) -> Dict:
        """应急回滚（增强事务安全）"""
        try:
            rollback_sql = f"""
            SET XACT_ABORT ON;
            BEGIN TRY
                DECLARE @BakTableName NVARCHAR(200) = (
                    SELECT TOP 1 name FROM sys.tables
                    WHERE name LIKE '{module_name}_Bak[_]%'
                    ORDER BY name DESC
                );

                IF @BakTableName IS NOT NULL
                BEGIN
                    BEGIN TRAN;
                        -- 验证备份表存在
                        IF OBJECT_ID(@BakTableName) IS NULL
                            THROW 50004, '备份表不存在', 1;

                        -- 验证当前表存在
                        IF OBJECT_ID('dbo.{module_name}') IS NULL
                            THROW 50005, '当前表不存在', 1;

                        EXEC sp_rename 'dbo.{module_name}', '{module_name}_Rollback';
                        EXEC sp_rename @BakTableName, '{module_name}';

                        -- 验证回滚后的表存在
                        IF OBJECT_ID('dbo.{module_name}') IS NULL
                            THROW 50006, '回滚后主表不存在', 1;

                    COMMIT TRAN;

                    SELECT 'SUCCESS' as status, @BakTableName as backup_table;
                END
                ELSE
                BEGIN
                    SELECT 'FAILED' as status, 'No backup table found' as message;
                END
            END TRY
            BEGIN CATCH
                IF @@TRANCOUNT > 0
                    ROLLBACK TRAN;

                DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
                SELECT 'ERROR' as status, @ErrorMessage as error_message;
            END CATCH
            """

            async with self.session_factory() as session:
                result = await session.execute(text(rollback_sql))
                row = result.fetchone()

                if row.status == 'SUCCESS':
                    await session.commit()
                    logger.info(f"应急回滚成功: {module_name} <- {row.backup_table}")
                    return {"success": True, "backup_table": row.backup_table}
                else:
                    await session.rollback()
                    error_msg = row.error_message if hasattr(
                        row, 'error_message') else "没有找到备份表"
                    logger.error(f"应急回滚失败: {module_name} - {error_msg}")
                    return {"success": False, "message": error_msg}

        except Exception:
            logger.error(f"应急回滚异常: {module_name}", error=str(e))
            return {"success": False, "message": str(e)}

    async def get_switch_history(
            self,
            module_name: str = None,
            limit: int = 10) -> Dict:
        """获取切换历史"""
        try:
            if module_name:
                query_sql = """
                SELECT TOP (:limit)
                    module_name,
                    switch_time,
                    status,
                    duration_ms,
                    backup_table_name,
                    error_message
                FROM dbo.sync_switch_log
                WHERE module_name = :module_name
                ORDER BY switch_time DESC
                """
                params = {"limit": limit, "module_name": module_name}
            else:
                query_sql = """
                SELECT TOP (:limit)
                    module_name,
                    switch_time,
                    status,
                    duration_ms,
                    backup_table_name,
                    error_message
                FROM dbo.sync_switch_log
                ORDER BY switch_time DESC
                """
                params = {"limit": limit}

            async with self.session_factory() as session:
                result = await session.execute(text(query_sql), params)
                rows = result.fetchall()

                history = []
                for row in rows:
                    history.append({
                        "module_name": row.module_name,
                        "switch_time": row.switch_time.isoformat() if row.switch_time else None,
                        "status": row.status,
                        "duration_ms": row.duration_ms,
                        "backup_table_name": row.backup_table_name,
                        "error_message": row.error_message
                    })

                return {"success": True, "history": history}

        except Exception:
            logger.error("获取切换历史失败", error=str(e))
            return {"success": False, "message": str(e)}

# 使用示例


async def main():
    """使用示例"""
    # 这里需要传入实际的session_factory
    # session_factory = get_async_session_factory()

    # 创建零停机管理器
    # manager = ZeroDowntimeManager(session_factory)

    # 初始化表结构
    # await manager.initialize_zero_downtime_tables("purchase_order")

    # 创建零停机调度器
    # scheduler = ZeroDowntimeAutoSyncScheduler(data_write_manager, session_factory)

    # 执行零停机同步
    # result = await scheduler.execute_zero_downtime_sync(["purchase_order",
    # "sales_order"])

    print("零停机数据同步实现完成")

if __name__ == "__main__":
    asyncio.run(main())
