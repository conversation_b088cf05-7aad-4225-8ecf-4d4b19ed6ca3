import os
import sqlite3
import subprocess
import sys
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 自动修复脚本
根据健康检查结果自动修复常见问题
"""


class AutoFixer:
    def __init___(self):
    """TODO: Add function description."""
    self.project_root = Path(__file__).parent
    self.fixed_issues = []

    def fix_missing_dependencies(self):
        """修复缺失的依赖包"""
        self.logger.info("🔧 修复缺失的依赖包...")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r",
                str(self.project_root / "backend" / "requirements.txt")
            ], check=True)
            self.fixed_issues.append("✅ 已安装缺失的依赖包")
        except Exception:
            logger.info(f"❌ 安装依赖包失败: {e}")

    def fix_database(self):
        """修复数据库问题"""
        self.logger.info("🔧 修复数据库...")
        db_file = self.project_root / "backend" / "ysapi.db"
        if not db_file.exists():
            try:
                conn = sqlite3.connect(str(db_file))
                conn.close()
                self.fixed_issues.append("✅ 已创建数据库文件")
            except Exception:
                logger.info(f"❌ 创建数据库失败: {e}")

    def fix_temp_files(self):
        """清理临时文件"""
        self.logger.info("🔧 清理临时文件...")
        temp_patterns = ["*.tmp", "*.temp", "*.bak", "*~", "*.path_backup"]
        cleaned = 0

        for pattern in temp_patterns:
            for file_path in self.project_root.rglob(pattern):
                try:
                    file_path.unlink()
                    cleaned += 1
                except Exception:
                    pass

        if cleaned > 0:
            self.fixed_issues.append(f"✅ 已清理 {cleaned} 个临时文件")

    def run_all_fixes(self):
        """运行所有修复"""
        self.logger.info("🛠️ 开始自动修复...")

        self.fix_missing_dependencies()
        self.fix_database()
        self.fix_temp_files()

        if self.fixed_issues:
            logger.info("
                        修复完成: ")
            for issue in self.fixed_issues:
                logger.info(f"  {issue}")
        else:
            logger.info("✅ 无需修复")


if __name__ == "__main__":
    fixer = AutoFixer()
    fixer.run_all_fixes()
