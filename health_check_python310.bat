@echo off
REM YS-API V3.0 健康检查脚本 - 使用Python 3.10
echo ========================================
echo YS-API V3.0 健康检查 (Python 3.10)
echo ========================================

REM 切换到项目目录
cd /d "%~dp0"

REM 运行快速健康检查
echo 运行快速健康检查...
py -3.10 quick_health_check.py

echo.
echo ========================================
echo 运行综合项目检查...
echo ========================================
py -3.10 run_comprehensive_check.py

pause
