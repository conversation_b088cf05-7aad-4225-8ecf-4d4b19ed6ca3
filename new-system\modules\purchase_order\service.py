from datetime import datetime

from database import get_db
from sqlalchemy.orm import Session

"""
采购订单服务层 - 业务逻辑处理
"""


class PurchaseOrderService:
    """采购订单服务"""

    def __init__(self, db: Session = Depends(get_db)):
        self.db = db

    async def get_purchase_orders(
        self,
        page: int = 1,
        size: int = 20,
        status: Optional[str] = None,
        supplier_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """获取采购订单列表"""

        # 构建查询条件
        conditions = []

        if status:
            conditions.append(PurchaseOrder.status == status)

        if supplier_id:
            conditions.append(PurchaseOrder.supplier_id == supplier_id)

        if start_date:
            conditions.append(PurchaseOrder.order_date >= start_date)

        if end_date:
            conditions.append(PurchaseOrder.order_date <= end_date)

        # 执行查询
        query = self.db.query(PurchaseOrder)

        if conditions:
            query = query.filter(and_(*conditions))

        # 分页
        total = query.count()
        orders = query.offset((page - 1) * size).limit(size).all()

        return {
            "items": [order.to_dict() for order in orders],
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size,
        }

    async def get_purchase_order_by_id(
            self, order_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取采购订单"""
        order = (self.db.query(PurchaseOrder).filter(
            PurchaseOrder.id == order_id).first())

        if not order:
            return None

        # 获取订单明细
        items = (
            self.db.query(PurchaseOrderItem)
            .filter(PurchaseOrderItem.order_id == order_id)
            .all()
        )

        result = order.to_dict()
        result["items"] = [item.to_dict() for item in items]

        return result

    async def get_all_orders_for_consistency_check(
            self) -> List[Dict[str, Any]]:
        """获取所有订单数据用于一致性检查"""
        orders = self.db.query(PurchaseOrder).all()
        return [order.to_dict() for order in orders]
