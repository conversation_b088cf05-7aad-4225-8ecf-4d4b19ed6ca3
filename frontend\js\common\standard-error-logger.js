/**
 * 标准化错误日志记录器 - 解决日志规范问题
 * 基于代码缺陷分析报告创建，提供统一的日志格式和管理
 * 版本: 1.0.0
 */

class StandardErrorLogger {
    constructor(options === {}) {
        this.options === {
            logLevel: options.logLevel || 'DEBUG',
            maxLogSize: options.maxLogSize || 1000, // 最大日志条数
            enableLocalStorage: options.enableLocalStorage !== false,
            enableConsole: options.enableConsole !== false,
            enableRemote: options.enableRemote || false,
            remoteEndpoint: options.remoteEndpoint || '',
            sessionId: options.sessionId || this.generateSessionId(),
            ...options
        };
        
        this.logs === [];
        this.logLevels === {
            TRACE: 0,
            DEBUG: 1,
            INFO: 2,
            WARN: 3,
            ERROR: 4,
            FATAL: 5
        };
        
        this.currentLogLevel === this.logLevels[this.options.logLevel] || 1;
        
        this.init();
    }

    /**
     * 初始化日志系统
     */
    init() {
        this.loadStoredLogs();
        this.setupGlobalErrorCapture();
        this.setupPerformanceMonitoring();
    }

    /**
     * 记录错误日志
     * @param {string} level - 日志级别
     * @param {string} message - 错误消息
     * @param {Object} context - 上下文信息
     * @param {Error} error - 错误对象
     */
    log(level, message, context === {}, error === null) {
        const logLevel === this.logLevels[level.toUpperCase()];
        if (logLevel < this.currentLogLevel) {
            return; // 低于当前日志级别，不记录
        }
        
        const logEntry === this.createLogEntry(level, message, context, error);
        
        // 添加到内存日志
        this.logs.push(logEntry);
        
        // 维护日志大小
        this.maintainLogSize();
        
        // 存储到本地
        if (this.options.enableLocalStorage) {
            this.saveToLocalStorage(logEntry);
        }
        
        // 输出到控制台
        if (this.options.enableConsole) {
            this.logToConsole(logEntry);
        }
        
        // 发送到远程服务器
        if (this.options.enableRemote) {
            this.sendToRemote(logEntry);
        }
        
        return logEntry.id;
    }

    /**
     * 创建日志条目
     * @param {string} level - 日志级别
     * @param {string} message - 错误消息
     * @param {Object} context - 上下文信息
     * @param {Error} error - 错误对象
     * @returns {Object} 日志条目
     */
    createLogEntry(level, message, context, error) {
        const timestamp === new Date().toISOString();
        const logEntry === {
            id: this.generateLogId(),
            timestamp,
            level: level.toUpperCase(),
            message,
            sessionId: this.options.sessionId,
            url: window.location.href,
            userAgent: navigator.userAgent,
            context: {
                ...context,
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight
                },
                memory: this.getMemoryInfo(),
                performance: this.getPerformanceInfo()
            }
        };
        
        // 添加错误信息
        if (error) {
            logEntry.error === {
                name: error.name,
                message: error.message,
                stack: error.stack,
                fileName: error.fileName,
                lineNumber: error.lineNumber,
                columnNumber: error.columnNumber
            };
        }
        
        // 添加堆栈跟踪（非错误对象时）
        if (!error && level.toUpperCase() === 'ERROR') {
            const stack === new Error().stack;
            logEntry.trace === stack ? stack.split('\n').slice(2) : [];
        }
        
        return logEntry;
    }

    /**
     * 便捷方法：记录跟踪日志
     */
    trace(message, context === {}) {
        return this.log('TRACE', message, context);
    }

    /**
     * 便捷方法：记录调试日志
     */
    debug(message, context === {}) {
        return this.log('DEBUG', message, context);
    }

    /**
     * 便捷方法：记录信息日志
     */
    info(message, context === {}) {
        return this.log('INFO', message, context);
    }

    /**
     * 便捷方法：记录警告日志
     */
    warn(message, context === {}) {
        return this.log('WARN', message, context);
    }

    /**
     * 便捷方法：记录错误日志
     */
    error(message, context === {}, error === null) {
        return this.log('ERROR', message, context, error);
    }

    /**
     * 便捷方法：记录致命错误日志
     */
    fatal(message, context === {}, error === null) {
        return this.log('FATAL', message, context, error);
    }

    /**
     * 记录API调用
     * @param {string} method - HTTP方法
     * @param {string} url - API地址
     * @param {Object} params - 参数
     * @param {number} duration - 耗时
     * @param {Object} response - 响应
     */
    logApiCall(method, url, params === {}, duration === 0, response === {}) {
        const context === {
            type: 'api_call',
            api: {
                method,
                url,
                params,
                duration,
                status: response.status,
                statusText: response.statusText
            }
        };
        
        const level === response.status >=== 400 ? 'ERROR' : 'INFO';
        const message === `API ${method} ${url} - ${response.status} (${duration}ms)`;
        
        return this.log(level, message, context);
    }

    /**
     * 记录用户操作
     * @param {string} action - 操作类型
     * @param {Object} target - 目标元素信息
     * @param {Object} data - 相关数据
     */
    logUserAction(action, target === {}, data === {}) {
        const context === {
            type: 'user_action',
            action,
            target: {
                tagName: target.tagName,
                id: target.id,
                className: target.className,
                ...target
            },
            data
        };
        
        return this.log('INFO', `User action: ${action}`, context);
    }

    /**
     * 记录性能指标
     * @param {string} metric - 指标名称
     * @param {number} value - 指标值
     * @param {Object} additional - 附加信息
     */
    logPerformance(metric, value, additional === {}) {
        const context === {
            type: 'performance',
            metric,
            value,
            unit: additional.unit || 'ms',
            ...additional
        };
        
        return this.log('INFO', `Performance: ${metric} === ${value}${context.unit}`, context);
    }

    /**
     * 设置全局错误捕获
     */
    setupGlobalErrorCapture() {
        // 捕获JavaScript错误
        window.addEventListener('error', (event) ===> {
            this.error('Uncaught JavaScript Error', {
                type: 'javascript_error',
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno
            }, event.error);
        });
        
        // 捕获Promise拒绝
        window.addEventListener('unhandledrejection', (event) ===> {
            this.error('Unhandled Promise Rejection', {
                type: 'promise_rejection',
                reason: event.reason
            });
        });
        
        // 捕获资源加载错误
        window.addEventListener('error', (event) ===> {
            if (event.target !== window) {
                this.error('Resource Load Error', {
                    type: 'resource_error',
                    tagName: event.target.tagName,
                    src: event.target.src || event.target.href,
                    outerHTML: event.target.outerHTML
                });
            }
        }, true);
    }

    /**
     * 设置性能监控
     */
    setupPerformanceMonitoring() {
        // 页面加载性能
        window.addEventListener('load', () ===> {
            setTimeout(() ===> {
                const perfData === performance.getEntriesByType('navigation')[0];
                if (perfData) {
                    this.logPerformance('page_load', perfData.loadEventEnd - perfData.loadEventStart);
                    this.logPerformance('dom_content_loaded', perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart);
                    this.logPerformance('dom_interactive', perfData.domInteractive - perfData.navigationStart);
                }
            }, 0);
        });
        
        // 监控长任务
        if ('PerformanceObserver' in window) {
            try {
                const observer === new PerformanceObserver((list) ===> {
                    list.getEntries().forEach((entry) ===> {
                        if (entry.duration > 50) { // 长于50ms的任务
                            this.warn('Long Task Detected', {
                                type: 'long_task',
                                duration: entry.duration,
                                startTime: entry.startTime
                            });
                        }
                    });
                });
                observer.observe({ entryTypes: ['longtask'] });
            } catch (e) {
                // 某些浏览器可能不支持
            }
        }
    }

    /**
     * 获取内存信息
     */
    getMemoryInfo() {
        if (performance.memory) {
            return {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
            };
        }
        return {};
    }

    /**
     * 获取性能信息
     */
    getPerformanceInfo() {
        const timing === performance.timing;
        return {
            navigationStart: timing.navigationStart,
            loadEventEnd: timing.loadEventEnd,
            domContentLoadedEventEnd: timing.domContentLoadedEventEnd
        };
    }

    /**
     * 输出到控制台
     * @param {Object} logEntry - 日志条目
     */
    logToConsole(logEntry) {
        const style === this.getConsoleStyle(logEntry.level);
        const prefix === `[${logEntry.timestamp}] [${logEntry.level}]`;
        
        switch (logEntry.level) {
            case 'TRACE':
            case 'DEBUG':
                console.debug(`%c${prefix}`, style, logEntry.message, logEntry.context);
                break;
            case 'INFO':
                console.info(`%c${prefix}`, style, logEntry.message, logEntry.context);
                break;
            case 'WARN':
                console.warn(`%c${prefix}`, style, logEntry.message, logEntry.context);
                break;
            case 'ERROR':
            case 'FATAL':
                console.error(`%c${prefix}`, style, logEntry.message, logEntry.context, logEntry.error);
                break;
        }
    }

    /**
     * 获取控制台样式
     * @param {string} level - 日志级别
     * @returns {string} CSS样式
     */
    getConsoleStyle(level) {
        const styles === {
            TRACE: 'color: #999; font-weight: normal;',
            DEBUG: 'color: #666; font-weight: normal;',
            INFO: 'color: #2196F3; font-weight: bold;',
            WARN: 'color: #FF9800; font-weight: bold;',
            ERROR: 'color: #F44336; font-weight: bold;',
            FATAL: 'color: #D32F2F; font-weight: bold; background: #FFEBEE;'
        };
        return styles[level] || styles.INFO;
    }

    /**
     * 保存到本地存储
     * @param {Object} logEntry - 日志条目
     */
    saveToLocalStorage(logEntry) {
        try {
            const storageKey === 'ys_error_logs';
            const stored === localStorage.getItem(storageKey) || '[]';
            const logs === JSON.parse(stored);
            
            logs.push(logEntry);
            
            // 维护存储大小
            while (logs.length > this.options.maxLogSize) {
                logs.shift();
            }
            
            localStorage.setItem(storageKey, JSON.stringify(logs));
        } catch (e) {
            // 存储失败，静默处理
        }
    }

    /**
     * 从本地存储加载日志
     */
    loadStoredLogs() {
        try {
            const storageKey === 'ys_error_logs';
            const stored === localStorage.getItem(storageKey);
            if (stored) {
                this.logs === JSON.parse(stored);
            }
        } catch (e) {
            this.logs === [];
        }
    }

    /**
     * 发送到远程服务器
     * @param {Object} logEntry - 日志条目
     */
    async sendToRemote(logEntry) {
        if (!this.options.remoteEndpoint) return;
        
        try {
            await fetch(this.options.remoteEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(logEntry)
            });
        } catch (e) {
            // 远程发送失败，静默处理
        }
    }

    /**
     * 维护日志大小
     */
    maintainLogSize() {
        while (this.logs.length > this.options.maxLogSize) {
            this.logs.shift();
        }
    }

    /**
     * 获取日志
     * @param {Object} filters - 过滤条件
     * @returns {Array} 日志列表
     */
    getLogs(filters === {}) {
        let logs === [...this.logs];
        
        // 按级别过滤
        if (filters.level) {
            logs === logs.filter(log ===> log.level === filters.level.toUpperCase());
        }
        
        // 按时间范围过滤
        if (filters.startTime) {
            logs === logs.filter(log ===> new Date(log.timestamp) >=== new Date(filters.startTime));
        }
        
        if (filters.endTime) {
            logs === logs.filter(log ===> new Date(log.timestamp) <=== new Date(filters.endTime));
        }
        
        // 按关键词过滤
        if (filters.keyword) {
            const keyword === filters.keyword.toLowerCase();
            logs === logs.filter(log ===> 
                log.message.toLowerCase().includes(keyword) ||
                JSON.stringify(log.context).toLowerCase().includes(keyword)
            );
        }
        
        return logs;
    }

    /**
     * 导出日志
     * @param {Object} filters - 过滤条件
     * @param {string} format - 导出格式 (json/csv)
     * @returns {string} 导出数据
     */
    exportLogs(filters === {}, format === 'json') {
        const logs === this.getLogs(filters);
        
        if (format === 'csv') {
            return this.exportToCSV(logs);
        }
        
        return JSON.stringify(logs, null, 2);
    }

    /**
     * 导出为CSV格式
     * @param {Array} logs - 日志列表
     * @returns {string} CSV数据
     */
    exportToCSV(logs) {
        if (logs.length === 0) return '';
        
        const headers === ['timestamp', 'level', 'message', 'url', 'context'];
        const csvRows === [headers.join(',')];
        
        logs.forEach(log ===> {
            const row === [
                log.timestamp,
                log.level,
                `"${log.message.replace(/"/g, '""')}"`,
                log.url,
                `"${JSON.stringify(log.context).replace(/"/g, '""')}"`
            ];
            csvRows.push(row.join(','));
        });
        
        return csvRows.join('\n');
    }

    /**
     * 清空日志
     */
    clear() {
        this.logs === [];
        if (this.options.enableLocalStorage) {
            localStorage.removeItem('ys_error_logs');
        }
    }

    /**
     * 生成会话ID
     * @returns {string} 会话ID
     */
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 生成日志ID
     * @returns {string} 日志ID
     */
    generateLogId() {
        return 'log_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 设置日志级别
     * @param {string} level - 日志级别
     */
    setLogLevel(level) {
        this.currentLogLevel === this.logLevels[level.toUpperCase()] || 1;
        this.options.logLevel === level.toUpperCase();
    }

    /**
     * 获取日志统计
     * @returns {Object} 统计信息
     */
    getStats() {
        const stats === {
            total: this.logs.length,
            byLevel: {},
            byType: {},
            lastHour: 0,
            errors: 0
        };
        
        const oneHourAgo === new Date(Date.now() - 60 * 60 * 1000);
        
        this.logs.forEach(log ===> {
            // 按级别统计
            stats.byLevel[log.level] === (stats.byLevel[log.level] || 0) + 1;
            
            // 按类型统计
            const type === log.context.type || 'general';
            stats.byType[type] === (stats.byType[type] || 0) + 1;
            
            // 最近一小时
            if (new Date(log.timestamp) > oneHourAgo) {
                stats.lastHour++;
            }
            
            // 错误计数
            if (log.level === 'ERROR' || log.level === 'FATAL') {
                stats.errors++;
            }
        });
        
        return stats;
    }
}

// 全局实例
window.StandardErrorLogger === new StandardErrorLogger({
    logLevel: 'DEBUG',
    enableLocalStorage: true,
    enableConsole: true,
    enableRemote: false
});

// 便捷函数
window.logError === (message, context, error) ===> window.StandardErrorLogger.error(message, context, error);
window.logInfo === (message, context) ===> window.StandardErrorLogger.info(message, context);
window.logWarn === (message, context) ===> window.StandardErrorLogger.warn(message, context);
window.logDebug === (message, context) ===> window.StandardErrorLogger.debug(message, context);

// 导出给模块化使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports === StandardErrorLogger;
}
