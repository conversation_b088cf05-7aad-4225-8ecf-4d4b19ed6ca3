<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YS-API V3.0 模块功能验证指南</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .status-bar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .module-section {
            margin-bottom: 25px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }

        .module-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .module-content {
            padding: 20px;
            display: none;
        }

        .module-content.active {
            display: block;
        }

        .test-step {
            background: #f0f8ff;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin: 10px 0;
        }

        .verification-checklist {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }

        .success {
            color: #4CAF50;
            font-weight: bold;
        }

        .warning {
            color: #ff9800;
            font-weight: bold;
        }

        .error {
            color: #f44336;
            font-weight: bold;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            background: #45a049;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .code-block {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }

        .progress-indicator {
            width: 100%;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-bar {
            height: 20px;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🚀 YS-API V3.0 模块功能验证指南</h1>
            <p>页面端操作验证 - 真实数据测试</p>
        </div>

        <div class="status-bar">
            <h3>📊 第三阶段进展状态</h3>
            <p>✅ 基础启动验证：完成</p>
            <p>✅ 测试代码清理：75.7% 完成（74→18个文件）</p>
            <p>✅ 硬编码数据清理：40.8% 完成（510→302处）</p>
            <p>🔄 当前阶段：模块功能验证</p>

            <div class="progress-indicator">
                <div class="progress-bar" id="overallProgress"></div>
            </div>
            <p id="progressText">总体进度: 0/15 模块验证完成</p>
        </div>

        <div class="verification-checklist">
            <h3>🎯 开始验证前的准备工作</h3>
            <ol>
                <li><strong>启动后端服务</strong>：在终端运行 <code>cd backend && python start_simple.py</code></li>
                <li><strong>启动前端服务</strong>：在终端运行 <code>cd frontend && python -m http.server 8080</code></li>
                <li><strong>访问前端</strong>：打开浏览器访问 <a href="http://localhost:8080"
                        target="_blank">http://localhost:8080</a></li>
                <li><strong>检查后端</strong>：访问 <a href="http://localhost:5000/docs"
                        target="_blank">http://localhost:5000/docs</a> 查看API文档</li>
            </ol>
        </div>

        <!-- 15个模块的验证指南 -->
        <div class="module-section">
            <div class="module-header" onclick="toggleModule('module1')">
                <span>1. 材料出库单列表查询</span>
                <span class="status" id="status1">🔄 待验证</span>
            </div>
            <div class="module-content" id="module1">
                <div class="test-step">
                    <h4>🔍 页面端验证步骤</h4>
                    <ol>
                        <li>访问前端页面，找到"材料出库单"菜单</li>
                        <li>点击"查询"按钮，观察是否正常加载数据</li>
                        <li>测试筛选条件：日期范围、仓库、物料等</li>
                        <li>测试导出功能（如果有）</li>
                        <li>检查分页功能是否正常</li>
                    </ol>
                </div>
                <div class="verification-checklist">
                    <h4>✅ 验证要点</h4>
                    <ul>
                        <li>数据格式是否正确（出库单号、物料、数量、日期等）</li>
                        <li>查询响应时间是否合理（&lt;3秒）</li>
                        <li>数据与原系统是否一致</li>
                        <li>错误处理是否正常（无数据时的提示）</li>
                    </ul>
                </div>
                <button class="btn" onclick="markComplete('module1', 1)">✅ 验证通过</button>
                <button class="btn btn-secondary" onclick="markIssue('module1', 1)">❌ 发现问题</button>
            </div>
        </div>

        <div class="module-section">
            <div class="module-header" onclick="toggleModule('module2')">
                <span>2. 采购订单列表</span>
                <span class="status" id="status2">🔄 待验证</span>
            </div>
            <div class="module-content" id="module2">
                <div class="test-step">
                    <h4>🔍 页面端验证步骤</h4>
                    <ol>
                        <li>进入采购订单模块</li>
                        <li>测试订单列表加载</li>
                        <li>验证订单详情查看功能</li>
                        <li>测试订单状态筛选</li>
                        <li>检查订单金额计算是否正确</li>
                    </ol>
                </div>
                <div class="verification-checklist">
                    <h4>✅ 验证要点</h4>
                    <ul>
                        <li>订单号、供应商、订单金额显示正确</li>
                        <li>订单状态（待审核、已审核、已执行等）</li>
                        <li>订单明细数据完整性</li>
                        <li>与财务系统数据一致性</li>
                    </ul>
                </div>
                <button class="btn" onclick="markComplete('module2', 2)">✅ 验证通过</button>
                <button class="btn btn-secondary" onclick="markIssue('module2', 2)">❌ 发现问题</button>
            </div>
        </div>

        <div class="module-section">
            <div class="module-header" onclick="toggleModule('module3')">
                <span>3. 采购入库单列表</span>
                <span class="status" id="status3">🔄 待验证</span>
            </div>
            <div class="module-content" id="module3">
                <div class="test-step">
                    <h4>🔍 页面端验证步骤</h4>
                    <ol>
                        <li>查看采购入库单列表</li>
                        <li>验证入库数量与采购订单的关联</li>
                        <li>测试入库日期筛选</li>
                        <li>检查供应商信息的准确性</li>
                        <li>验证库存更新是否正确</li>
                    </ol>
                </div>
                <div class="verification-checklist">
                    <h4>✅ 验证要点</h4>
                    <ul>
                        <li>入库单号、关联采购订单号正确</li>
                        <li>入库数量、单价、金额计算准确</li>
                        <li>仓库、货位信息完整</li>
                        <li>质检状态（如适用）</li>
                    </ul>
                </div>
                <button class="btn" onclick="markComplete('module3', 3)">✅ 验证通过</button>
                <button class="btn btn-secondary" onclick="markIssue('module3', 3)">❌ 发现问题</button>
            </div>
        </div>

        <div class="module-section">
            <div class="module-header" onclick="toggleModule('module4')">
                <span>4. 产品入库单列表查询</span>
                <span class="status" id="status4">🔄 待验证</span>
            </div>
            <div class="module-content" id="module4">
                <div class="test-step">
                    <h4>🔍 页面端验证步骤</h4>
                    <ol>
                        <li>访问产品入库单模块</li>
                        <li>测试生产完工入库查询</li>
                        <li>验证产品批次管理</li>
                        <li>检查质量检验记录</li>
                        <li>测试成本核算相关数据</li>
                    </ol>
                </div>
                <div class="verification-checklist">
                    <h4>✅ 验证要点</h4>
                    <ul>
                        <li>产品入库单号、生产订单关联</li>
                        <li>产品规格、数量、批次号</li>
                        <li>质检结果、合格数量</li>
                        <li>成本价格、标准成本对比</li>
                    </ul>
                </div>
                <button class="btn" onclick="markComplete('module4', 4)">✅ 验证通过</button>
                <button class="btn btn-secondary" onclick="markIssue('module4', 4)">❌ 发现问题</button>
            </div>
        </div>

        <div class="module-section">
            <div class="module-header" onclick="toggleModule('module5')">
                <span>5. 请购单列表查询</span>
                <span class="status" id="status5">🔄 待验证</span>
            </div>
            <div class="module-content" id="module5">
                <div class="test-step">
                    <h4>🔍 页面端验证步骤</h4>
                    <ol>
                        <li>查看请购单列表</li>
                        <li>测试请购审批流程状态</li>
                        <li>验证请购转采购订单功能</li>
                        <li>检查预算控制信息</li>
                        <li>测试紧急请购标识</li>
                    </ol>
                </div>
                <div class="verification-checklist">
                    <h4>✅ 验证要点</h4>
                    <ul>
                        <li>请购单号、申请部门、申请人</li>
                        <li>物料需求信息、需求日期</li>
                        <li>审批状态、审批历史</li>
                        <li>预算占用、可用预算</li>
                    </ul>
                </div>
                <button class="btn" onclick="markComplete('module5', 5)">✅ 验证通过</button>
                <button class="btn btn-secondary" onclick="markIssue('module5', 5)">❌ 发现问题</button>
            </div>
        </div>

        <!-- 继续其他10个模块... -->
        <div style="text-align: center; margin: 30px 0;">
            <h3>📋 验证总结</h3>
            <div id="summarySection">
                <p>请逐一验证每个模块，完成后会显示总体验证结果</p>
            </div>
        </div>

        <div class="verification-checklist">
            <h3>📝 验证注意事项</h3>
            <ul>
                <li><strong>数据一致性</strong>：对比新旧系统的关键数据，确保迁移无误</li>
                <li><strong>性能表现</strong>：关注页面加载速度，查询响应时间</li>
                <li><strong>用户体验</strong>：界面操作流畅性，错误提示友好性</li>
                <li><strong>业务逻辑</strong>：各种业务规则执行是否正确</li>
                <li><strong>权限控制</strong>：不同角色用户的访问权限</li>
            </ul>
        </div>
    </div>

    <script>
        let completedModules = 0;
        const totalModules = 15;

        function toggleModule(moduleId) {
            const content = document.getElementById(moduleId);
            content.classList.toggle('active');
        }

        function markComplete(moduleId, moduleNum) {
            const status = document.getElementById('status' + moduleNum);
            status.textContent = '✅ 验证通过';
            status.className = 'status success';

            completedModules++;
            updateProgress();
        }

        function markIssue(moduleId, moduleNum) {
            const status = document.getElementById('status' + moduleNum);
            status.textContent = '❌ 发现问题';
            status.className = 'status error';

            // 可以添加问题记录功能
            const issue = prompt('请描述发现的问题：');
            if (issue) {
                console.log(`模块${moduleNum}问题：`, issue);
                // 这里可以发送到后端记录问题
            }
        }

        function updateProgress() {
            const progress = (completedModules / totalModules) * 100;
            document.getElementById('overallProgress').style.width = progress + '%';
            document.getElementById('progressText').textContent =
                `总体进度: ${completedModules}/${totalModules} 模块验证完成`;

            if (completedModules === totalModules) {
                document.getElementById('summarySection').innerHTML =
                    '<div class="success">🎉 所有模块验证完成！可以进入下一阶段：性能基准测试</div>';
            }
        }

        // 自动展开第一个模块
        document.addEventListener('DOMContentLoaded', function () {
            document.getElementById('module1').classList.add('active');
        });
    </script>
</body>

</html>