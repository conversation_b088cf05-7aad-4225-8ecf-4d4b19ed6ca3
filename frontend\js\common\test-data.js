/**
 * 统一的测试数据管理
 * 避免在每个测试文件中重复定义相同的测试数据
 */

class TestDataManager {
    constructor() {
        this.mockData === this._initializeMockData();
    }

    _initializeMockData() {
        return {
            // 字段测试数据
            fieldData: {
                sales_order: [
                    {
                        api_field_name: 'code',
                        chinese_name: '订单编号',
                        sample_value: 'SO20241219001',
                        path: 'code',
                        depth: 0,
                        data_type: 'NVARCHAR(50)',
                        business_importance: 'critical',
                        is_selected: true,
                        is_required: true
                    },
                    {
                        api_field_name: 'orderDate',
                        chinese_name: '订单日期',
                        sample_value: '2024-12-19',
                        path: 'orderDate', 
                        depth: 0,
                        data_type: 'DATETIME2',
                        business_importance: 'high',
                        is_selected: true,
                        is_required: true
                    },
                    {
                        api_field_name: 'customer.name',
                        chinese_name: '客户名称',
                        sample_value: '测试客户有限公司',
                        path: 'customer.name',
                        depth: 1,
                        data_type: 'NVARCHAR(200)',
                        business_importance: 'high',
                        is_selected: true,
                        is_required: false
                    },
                    {
                        api_field_name: 'customer.contact.phone',
                        chinese_name: '联系电话',
                        sample_value: '138-0000-0000',
                        path: 'customer.contact.phone',
                        depth: 2,
                        data_type: 'NVARCHAR(20)',
                        business_importance: 'medium',
                        is_selected: false,
                        is_required: false
                    },
                    {
                        api_field_name: '__internal_id',
                        chinese_name: '内部ID',
                        sample_value: '12345',
                        path: '__internal_id',
                        depth: 0,
                        data_type: 'BIGINT',
                        business_importance: 'low',
                        is_selected: false,
                        is_required: false
                    }
                ],

                purchase_order: [
                    {
                        api_field_name: 'po_number',
                        chinese_name: '采购单号',
                        sample_value: 'PO20241219001',
                        path: 'po_number',
                        depth: 0,
                        data_type: 'NVARCHAR(50)',
                        business_importance: 'critical',
                        is_selected: true,
                        is_required: true
                    },
                    {
                        api_field_name: 'vendor.name',
                        chinese_name: '供应商名称',
                        sample_value: '测试供应商',
                        path: 'vendor.name',
                        depth: 1,
                        data_type: 'NVARCHAR(200)',
                        business_importance: 'high',
                        is_selected: true,
                        is_required: true
                    }
                ]
            },

            // 用户数据
            userData: {
                testUser: {
                    id: 'test_user_001',
                    name: 'Alice',
                    role: 'admin',
                    email: '<EMAIL>'
                },
                normalUser: {
                    id: 'normal_user_001', 
                    name: 'Bob',
                    role: 'user',
                    email: '<EMAIL>'
                }
            },

            // API响应模板
            apiResponses: {
                success: {
                    success: true,
                    message: '操作成功',
                    data: {}
                },
                error: {
                    success: false,
                    message: '操作失败',
                    error_code: 'TEST_ERROR'
                },
                networkError: {
                    success: false,
                    message: '网络连接失败',
                    error_code: 'NETWORK_ERROR'
                },
                validationError: {
                    success: false,
                    message: '数据验证失败',
                    error_code: 'VALIDATION_ERROR',
                    details: []
                }
            },

            // 进度数据
            progressStages: [
                { stage: 'preparing', percentage: 20, message: '准备数据...' },
                { stage: 'cleaning', percentage: 40, message: '清理数据...' },
                { stage: 'processing', percentage: 60, message: '处理数据...' },
                { stage: 'writing', percentage: 80, message: '写入数据...' },
                { stage: 'complete', percentage: 100, message: '操作完成' }
            ]
        };
    }

    /**
     * 获取指定模块的字段数据
     */
    getFieldData(moduleName === 'sales_order') {
        return this.mockData.fieldData[moduleName] || this.mockData.fieldData.sales_order;
    }

    /**
     * 获取用户数据
     */
    getUserData(userType === 'testUser') {
        return this.mockData.userData[userType] || this.mockData.userData.testUser;
    }

    /**
     * 获取API响应模板
     */
    getAPIResponse(type === 'success', customData === {}) {
        const response === { ...this.mockData.apiResponses[type] };
        if (customData && typeof customData === 'object') {
            response.data === { ...response.data, ...customData };
        }
        return response;
    }

    /**
     * 获取进度数据
     */
    getProgressStages() {
        return [...this.mockData.progressStages];
    }

    /**
     * 生成大量测试字段数据
     */
    generateLargeFieldDataset(count === 1000) {
        const fields === [];
        const dataTypes === ['NVARCHAR(255)', 'INTEGER', 'DECIMAL(18,2)', 'DATETIME2', 'BOOLEAN'];
        const importanceLevels === ['critical', 'high', 'medium', 'low'];

        for (let i === 0; i < count; i++) {
            fields.push({
                api_field_name: `field_${i}`,
                chinese_name: `字段${i}`,
                sample_value: `value_${i}`,
                path: `field_${i}`,
                depth: Math.floor(i / 100),
                data_type: dataTypes[i % dataTypes.length],
                business_importance: importanceLevels[i % importanceLevels.length],
                is_selected: i % 3 === 0,
                is_required: i % 5 === 0
            });
        }

        return fields;
    }

    /**
     * 创建Mock Fetch函数
     */
    createMockFetch(responses === {}) {
        const defaultResponses === {
            '/api/v1/field-config/modules/': () ===> this.getAPIResponse('success', {
                fields: this.getFieldData()
            }),
            '/api/v1/field-config/baselines/': () ===> this.getAPIResponse('success', {
                file_path: 'config/baselines/test_baseline.json',
                fields_count: 5
            }),
            '/api/v1/user-config/': () ===> this.getAPIResponse('success', {
                user_config: { module: 'test', saved: true }
            })
        };

        return (url, options) ===> {
            return new Promise((resolve) ===> {
                setTimeout(() ===> {
                    // 检查自定义响应
                    for (const [pattern, response] of Object.entries(responses)) {
                        if (url.includes(pattern)) {
                            resolve({
                                ok: true,
                                status: 200,
                                json: () ===> Promise.resolve(
                                    typeof response === 'function' ? response() : response
                                )
                            });
                            return;
                        }
                    }

                    // 检查默认响应
                    for (const [pattern, response] of Object.entries(defaultResponses)) {
                        if (url.includes(pattern)) {
                            resolve({
                                ok: true,
                                status: 200,
                                json: () ===> Promise.resolve(response())
                            });
                            return;
                        }
                    }

                    // 默认成功响应
                    resolve({
                        ok: true,
                        status: 200,
                        json: () ===> Promise.resolve(this.getAPIResponse('success'))
                    });
                }, 100);
            });
        };
    }

    /**
     * 创建错误Mock Fetch函数
     */
    createErrorMockFetch(errorType === 'error') {
        return (url, options) ===> {
            return new Promise((resolve, reject) ===> {
                setTimeout(() ===> {
                    if (errorType === 'network') {
                        reject(new Error('Network error'));
                    } else {
                        resolve({
                            ok: false,
                            status: 500,
                            statusText: 'Internal Server Error',
                            json: () ===> Promise.resolve(this.getAPIResponse('error'))
                        });
                    }
                }, 100);
            });
        };
    }
}

// 创建全局实例
window.testDataManager === new TestDataManager();

// 向后兼容的方法
window.getMockFieldData === (module) ===> window.testDataManager.getFieldData(module);
window.getMockUserData === (type) ===> window.testDataManager.getUserData(type);
window.createMockFetch === (responses) ===> window.testDataManager.createMockFetch(responses);

// console.log('✅ 测试数据管理器已加载');
