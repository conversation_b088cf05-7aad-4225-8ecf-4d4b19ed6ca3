import hashlib
import json
import time
from pathlib import Path

#!/usr/bin/env python3
"""
两步保存机制实现
1. 临时保存：用户修改时立即保存到临时区域
2. 持久化保存：用户确认后正式保存到数据库
"""


class TwoStepSaveManager:
    """两步保存管理器"""

    def __init___(
            self,
            temp_dir="temp_configs",
            persistent_dir="persistent_configs"):
    """TODO: Add function description."""
    self.temp_dir = Path(temp_dir)
    self.persistent_dir = Path(persistent_dir)

    # 确保目录存在
    self.temp_dir.mkdir(exist_ok=True)
    self.persistent_dir.mkdir(exist_ok=True)

    # 内存缓存
    self.temp_storage = {}
    self.persistent_storage = {}

    # 加载现有的持久化配置
    self._load_persistent_configs()

    def _generate_config_id(self, module_name: str, user_id: str = "default"):
        """生成配置ID"""
        data = f"{module_name}_{user_id}_{time.time()}"
        return hashlib.md5(data.encode()).hexdigest()[:8]

    def _load_persistent_configs(self):
        """加载持久化配置到内存"""
        for config_file in self.persistent_dir.glob("*.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    module_name = config.get('module_name')
                    if module_name:
                        self.persistent_storage[module_name] = config
            except Exception:
                print(f"加载配置失败 {config_file}: {e}")

    def temp_save(self, module_name: str, config_data: Dict[str, Any],
                  user_id: str = "default") -> str:
        """临时保存配置"""
        config_id = self._generate_config_id(module_name, user_id)

        # 构建完整的配置对象
        temp_config = {
            "config_id": config_id,
            "module_name": module_name,
            "user_id": user_id,
            "config_data": config_data,
            "created_at": time.time(),
            "status": "temp",
            "last_modified": time.time()
        }

        # 保存到内存
        if module_name not in self.temp_storage:
            self.temp_storage[module_name] = []
        self.temp_storage[module_name].append(temp_config)

        # 保存到文件
        temp_file = self.temp_dir / f"{module_name}_{config_id}.json"
        try:
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(temp_config, f, indent=2, ensure_ascii=False)
            print(f"✅ 临时保存成功: {module_name} -> {temp_file}")
            return config_id
        except Exception:
            print(f"❌ 临时保存失败: {e}")
            return ""

    def persistent_save(self, module_name: str, config_id: str = None) -> bool:
        """持久化保存配置"""
        # 如果没有指定config_id，使用最新的临时配置
        if config_id is None:
            if module_name in self.temp_storage and self.temp_storage[module_name]:
                temp_config = self.temp_storage[module_name][-1]
                config_id = temp_config["config_id"]
            else:
                print(f"❌ 没有找到 {module_name} 的临时配置")
                return False

        # 查找指定的临时配置
        temp_config = None
        if module_name in self.temp_storage:
            for config in self.temp_storage[module_name]:
                if config["config_id"] == config_id:
                    temp_config = config
                    break

        if not temp_config:
            print(f"❌ 没有找到配置ID {config_id}")
            return False

        # 转换为持久化配置
        persistent_config = temp_config.copy()
        persistent_config["status"] = "persistent"
        persistent_config["persistent_at"] = time.time()

        # 保存到持久化存储
        persistent_file = self.persistent_dir / f"{module_name}.json"
        try:
            with open(persistent_file, 'w', encoding='utf-8') as f:
                json.dump(persistent_config, f, indent=2, ensure_ascii=False)

            # 更新内存中的持久化存储
            self.persistent_storage[module_name] = persistent_config

            # 清理临时配置
            self._cleanup_temp_config(module_name, config_id)

            print(f"✅ 持久化保存成功: {module_name} -> {persistent_file}")
            return True
        except Exception:
            print(f"❌ 持久化保存失败: {e}")
            return False

    def rollback_temp(self, module_name: str, config_id: str = None) -> bool:
        """回滚临时配置"""
        if config_id is None:
            # 回滚所有临时配置
            if module_name in self.temp_storage:
                temp_configs = self.temp_storage[module_name].copy()
                for config in temp_configs:
                    self._cleanup_temp_config(module_name, config["config_id"])
                print(f"✅ 回滚所有临时配置: {module_name}")
                return True
        else:
            # 回滚指定配置
            return self._cleanup_temp_config(module_name, config_id)

        return False

    def _cleanup_temp_config(self, module_name: str, config_id: str) -> bool:
        """清理临时配置"""
        try:
            # 从内存中移除
            if module_name in self.temp_storage:
                self.temp_storage[module_name] = [
                    config for config in self.temp_storage[module_name]
                    if config["config_id"] != config_id
                ]

            # 删除文件
            temp_file = self.temp_dir / f"{module_name}_{config_id}.json"
            if temp_file.exists():
                temp_file.unlink()

            print(f"✅ 清理临时配置: {module_name}#{config_id}")
            return True
        except Exception:
            print(f"❌ 清理临时配置失败: {e}")
            return False

    def get_temp_configs(self, module_name: str) -> list:
        """获取模块的所有临时配置"""
        return self.temp_storage.get(module_name, [])

    def get_persistent_config(self, module_name: str) -> Optional[Dict]:
        """获取模块的持久化配置"""
        return self.persistent_storage.get(module_name)

    def list_all_configs(self) -> Dict[str, Dict]:
        """列出所有配置状态"""
        result = {}

        # 所有涉及的模块
        all_modules = set(
            self.temp_storage.keys()) | set(
            self.persistent_storage.keys())

        for module in all_modules:
            result[module] = {
                "persistent": self.persistent_storage.get(module),
                "temp": self.temp_storage.get(module, []),
                "temp_count": len(self.temp_storage.get(module, []))
            }

        return result


def demo_two_step_save():
    """演示两步保存机制"""
    manager = TwoStepSaveManager()

    print("🚀 两步保存机制演示")
    print("=" * 50)

    # 模拟用户配置数据
    module_name = "材料出库单列表查询"
    config_data = {
        "field_mapping": {
            "material_code": "物料编码",
            "warehouse": "仓库",
            "quantity": "数量"
        },
        "api_settings": {
            "endpoint": "/api/material/outbound",
            "timeout": 30,
            "retry_count": 3
        },
        "display_settings": {
            "page_size": 20,
            "sort_field": "create_time",
            "sort_order": "desc"
        }
    }

    # 步骤1: 临时保存
    print("\n📝 步骤1: 临时保存配置")
    config_id = manager.temp_save(module_name, config_data)

    # 步骤2: 查看临时配置
    print("\n👀 步骤2: 查看临时配置")
    temp_configs = manager.get_temp_configs(module_name)
    print(f"临时配置数量: {len(temp_configs)}")

    # 步骤3: 持久化保存
    print("\n💾 步骤3: 持久化保存")
    success = manager.persistent_save(module_name, config_id)

    # 步骤4: 查看所有配置状态
    print("\n📊 步骤4: 配置状态总览")
    all_configs = manager.list_all_configs()
    for module, configs in all_configs.items():
        print(f"{module}:")
        print(f"  持久化配置: {'有' if configs['persistent'] else '无'}")
        print(f"  临时配置数: {configs['temp_count']}")


if __name__ == "__main__":
    demo_two_step_save()
