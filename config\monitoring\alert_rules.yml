# YS-API V3 错误处理告警规则
# 监控重试率、错误率等关键指标
# 版本: 1.0.0

groups:
  - name: ys-api-error-handling
    interval: 30s
    rules:
      # 错误率告警
      - alert: HighErrorRate
        expr: (rate(ys_api_errors_total[5m]) / rate(ys_api_requests_total[5m])) * 100 > 5
        for: 2m
        labels:
          severity: warning
          service: ys-api-v3
        annotations:
          summary: "YS-API V3 错误率过高"
          description: "在过去5分钟内，错误率超过5%，当前值: {{ $value }}%"

      # 高重试率告警
      - alert: HighRetryRate
        expr: rate(ys_api_retries_total[5m]) / rate(ys_api_requests_total[5m]) > 0.3
        for: 1m
        labels:
          severity: warning
          service: ys-api-v3
        annotations:
          summary: "YS-API V3 重试率过高"
          description: "在过去5分钟内，重试率超过30%，当前值: {{ $value }}"

      # 响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(ys_api_request_duration_seconds_bucket[5m])) > 2
        for: 3m
        labels:
          severity: warning
          service: ys-api-v3
        annotations:
          summary: "YS-API V3 响应时间过长"
          description: "95%分位数响应时间超过2秒，当前值: {{ $value }}s"

      # 高频接口性能告警
      - alert: HighFrequencyEndpointSlow
        expr: histogram_quantile(0.90, rate(ys_api_request_duration_seconds_bucket{endpoint=~"/api/(field-config/save|quick-save|auto-sync)"}[5m])) > 0.5
        for: 2m
        labels:
          severity: critical
          service: ys-api-v3
          endpoint_type: high_frequency
        annotations:
          summary: "高频接口性能下降"
          description: "高频接口 {{ $labels.endpoint }} 的90%分位数响应时间超过500ms，当前值: {{ $value }}s"

      # 重试延迟影响告警（基于您提到的+50ms问题）
      - alert: RetryDelayImpact
        expr: (ys_api_retry_delay_total / ys_api_retries_total) > 0.05
        for: 2m
        labels:
          severity: warning
          service: ys-api-v3
        annotations:
          summary: "重试机制延迟影响显著"
          description: "平均重试延迟超过50ms，当前值: {{ $value }}s，建议优化重试策略"

      # 错误处理失败告警
      - alert: ErrorHandlingFailure
        expr: rate(ys_api_error_handling_failures_total[5m]) > 0
        for: 1m
        labels:
          severity: critical
          service: ys-api-v3
        annotations:
          summary: "错误处理机制失败"
          description: "错误处理系统本身出现故障，需要立即检查"

      # 内存使用告警
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "系统内存使用率过高"
          description: "内存使用率超过80%，当前值: {{ $value }}%"

      # CPU使用告警
      - alert: HighCPUUsage
        expr: 100 - (avg(rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "系统CPU使用率过高"
          description: "CPU使用率超过80%，当前值: {{ $value }}%"

      # 磁盘空间告警
      - alert: LowDiskSpace
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) * 100 < 20
        for: 10m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "磁盘空间不足"
          description: "根分区可用空间低于20%，当前值: {{ $value }}%"

  - name: ys-api-business-metrics
    interval: 60s
    rules:
      # 配置保存失败率告警
      - alert: ConfigSaveFailureRate
        expr: (rate(ys_api_config_save_failures_total[10m]) / rate(ys_api_config_save_total[10m])) * 100 > 2
        for: 3m
        labels:
          severity: critical
          service: ys-api-v3
          operation: config_save
        annotations:
          summary: "配置保存失败率过高"
          description: "配置保存操作失败率超过2%，当前值: {{ $value }}%，可能影响用户体验"

      # 数据同步失败告警
      - alert: DataSyncFailure
        expr: rate(ys_api_sync_failures_total[15m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: ys-api-v3
          operation: data_sync
        annotations:
          summary: "数据同步频繁失败"
          description: "数据同步失败率: {{ $value }}/min，需要检查同步机制"

      # API可用性告警
      - alert: APIUnavailable
        expr: up{job="ys-api-backend"} == 0
        for: 1m
        labels:
          severity: critical
          service: ys-api-v3
        annotations:
          summary: "YS-API V3 服务不可用"
          description: "API服务已停止响应，需要立即检查"

      # 数据库连接告警
      - alert: DatabaseConnectionFailure
        expr: ys_api_database_connections_failed_total > 0
        for: 2m
        labels:
          severity: critical
          service: ys-api-v3
          component: database
        annotations:
          summary: "数据库连接失败"
          description: "数据库连接出现问题，失败次数: {{ $value }}"

  - name: ys-api-performance-sla
    interval: 30s
    rules:
      # SLA违反告警 - 99%可用性
      - alert: SLAViolation99Percent
        expr: (1 - rate(ys_api_errors_total[1h]) / rate(ys_api_requests_total[1h])) * 100 < 99
        for: 5m
        labels:
          severity: critical
          service: ys-api-v3
          sla: availability_99
        annotations:
          summary: "SLA违反 - 可用性低于99%"
          description: "过去1小时可用性: {{ $value }}%，违反99%可用性SLA"

      # SLA违反告警 - P95响应时间
      - alert: SLAViolationP95ResponseTime
        expr: histogram_quantile(0.95, rate(ys_api_request_duration_seconds_bucket[30m])) > 1.5
        for: 10m
        labels:
          severity: warning
          service: ys-api-v3
          sla: response_time_p95
        annotations:
          summary: "SLA违反 - P95响应时间超标"
          description: "过去30分钟P95响应时间: {{ $value }}s，超过1.5s SLA标准"

      # 错误预算消耗告警
      - alert: ErrorBudgetExhaustion
        expr: (1 - (rate(ys_api_requests_total[24h]) - rate(ys_api_errors_total[24h])) / rate(ys_api_requests_total[24h])) > 0.01
        for: 30m
        labels:
          severity: warning
          service: ys-api-v3
          sla: error_budget
        annotations:
          summary: "错误预算快速消耗"
          description: "24小时错误率: {{ $value }}，接近1%错误预算上限"
