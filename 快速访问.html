<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YS-API 快速访问</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .status-bar {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.3em;
        }
        
        .card p {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .card .features {
            list-style: none;
            margin-bottom: 20px;
        }
        
        .card .features li {
            padding: 5px 0;
            color: #555;
            position: relative;
            padding-left: 20px;
        }
        
        .card .features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e9ecef;
        }
        
        .api-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .api-info h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .api-endpoints {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .endpoint {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .endpoint .method {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .endpoint .path {
            font-family: monospace;
            color: #333;
            font-size: 0.9em;
        }
        
        .endpoint .desc {
            color: #666;
            font-size: 0.85em;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>YS-API 统一字段配置系统</h1>
            <p>企业级业务模块字段配置管理平台</p>
        </div>
        
        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>服务器运行正常 - 端口 8000</span>
            </div>
            <div>
                <span>版本: V3.0 | 更新时间: 2025-07-19</span>
            </div>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>🚀 快速访问</h2>
                <div class="grid">
                    <div class="card" onclick="window.open('http://localhost:8000/field-config.html', '_blank')">
                        <h3>API获取字段配置中心</h3>
                        <p>从用友API接口获取API字段数据，支持智能分析和配置</p>
                        <ul class="features">
                            <li>实时API字段获取</li>
                            <li>智能字段分析</li>
                            <li>深度嵌套字段处理</li>
                            <li>字段配置管理</li>
                        </ul>
                        <a href="http://localhost:8000/field-config.html" class="btn" target="_blank">立即访问</a>
                    </div>
                    
                    <div class="card" onclick="window.open('http://localhost:8000/unified-field-config.html', '_blank')">
                        <h3>统一字段配置</h3>
                        <p>管理和配置各种业务模块的字段映射关系</p>
                        <ul class="features">
                            <li>15个业务模块支持</li>
                            <li>实时配置同步</li>
                            <li>字段映射管理</li>
                            <li>配置导入导出</li>
                        </ul>
                        <a href="http://localhost:8000/unified-field-config.html" class="btn" target="_blank">立即访问</a>
                    </div>
                    
                    <div class="card" onclick="window.open('http://localhost:8000/database-v2.html', '_blank')">
                        <h3>数据库管理</h3>
                        <p>查看数据库表结构和同步状态</p>
                        <ul class="features">
                            <li>表结构查看</li>
                            <li>同步状态监控</li>
                            <li>实时日志查看</li>
                            <li>数据管理工具</li>
                        </ul>
                        <a href="http://localhost:8000/database-v2.html" class="btn" target="_blank">立即访问</a>
                    </div>
                    
                    <div class="card" onclick="window.open('http://localhost:8000/excel-translation.html', '_blank')">
                        <h3>Excel智能翻译</h3>
                        <p>Excel文件字段翻译和批量处理（已优化升级）</p>
                        <ul class="features">
                            <li>Excel文件上传</li>
                            <li>智能字段翻译</li>
                            <li>JSON翻译同步</li>
                            <li>批量数据处理</li>
                        </ul>
                        <a href="http://localhost:8000/excel-translation.html" class="btn" target="_blank">立即访问</a>
                    </div>
                    
                    <div class="card" onclick="window.open('http://localhost:8000/realtime-log-demo.html', '_blank')">
                        <h3>实时日志</h3>
                        <p>实时监控系统日志和API调用状态</p>
                        <ul class="features">
                            <li>实时日志流</li>
                            <li>API调用监控</li>
                            <li>错误追踪</li>
                            <li>性能分析</li>
                        </ul>
                        <a href="http://localhost:8000/realtime-log-demo.html" class="btn" target="_blank">立即访问</a>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🔧 API接口</h2>
                <div class="api-info">
                    <h3>主要API端点</h3>
                    <div class="api-endpoints">
                        <div class="endpoint">
                            <div class="method">GET</div>
                            <div class="path">/health</div>
                            <div class="desc">健康检查</div>
                        </div>
                        <div class="endpoint">
                            <div class="method">GET</div>
                            <div class="path">/api/v1/unified/modules</div>
                            <div class="desc">获取模块列表</div>
                        </div>
                        <div class="endpoint">
                            <div class="method">GET</div>
                            <div class="path">/api/v1/unified/diagnostic</div>
                            <div class="desc">系统诊断</div>
                        </div>
                        <div class="endpoint">
                            <div class="method">GET</div>
                            <div class="path">/api/v1/logs/stream</div>
                            <div class="desc">实时日志流</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>📋 支持的业务模块</h2>
                <div class="grid">
                    <div class="card">
                        <h3>采购管理</h3>
                        <ul class="features">
                            <li>采购订单 (purchase_order)</li>
                            <li>采购入库 (purchase_receipt)</li>
                            <li>请购单 (applyorder)</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h3>销售管理</h3>
                        <ul class="features">
                            <li>销售订单 (sales_order)</li>
                            <li>销售出库 (sales_out)</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h3>生产管理</h3>
                        <ul class="features">
                            <li>生产订单 (production_order)</li>
                            <li>产品入库 (product_receipt)</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h3>库存管理</h3>
                        <ul class="features">
                            <li>库存管理 (inventory)</li>
                            <li>现存量报表 (inventory_report)</li>
                            <li>材料出库 (materialout)</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h3>委外管理</h3>
                        <ul class="features">
                            <li>委外订单 (subcontract_order)</li>
                            <li>委外入库 (subcontract_receipt)</li>
                            <li>委外申请 (subcontract_requisition)</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <h3>基础数据</h3>
                        <ul class="features">
                            <li>物料主数据 (material_master)</li>
                            <li>需求计划 (requirements_planning)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2025 YS-API开发团队 | 版本 V3.0 | 技术支持: 统一字段配置系统</p>
        </div>
    </div>
    
    <script>
        // 检查服务器状态
        async function checkServerStatus() {
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    console.log('服务器运行正常');
                } else {
                    console.log('服务器响应异常');
                }
            } catch (error) {
                console.log('无法连接到服务器');
            }
        }
        
        // 页面加载时检查状态
        window.addEventListener('load', checkServerStatus);
        
        // 每30秒检查一次服务器状态
        setInterval(checkServerStatus, 30000);
    </script>
</body>
</html> 