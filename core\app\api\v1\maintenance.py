import structlog

from ...services.maintenance_manager import get_maintenance_manager

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 维护管理API
提供文件清理和缓存管理功能
"""


logger = structlog.get_logger()
router = APIRouter(prefix="/maintenance", tags=["维护管理"])


@router.get("/status")
async def get_maintenance_status():
    """获取维护状态"""
    try:
        maintenance_manager = get_maintenance_manager()
        status = await maintenance_manager.get_cleanup_status()

        return status

    except Exception:
        logger.error("获取维护状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取维护状态失败: {str(e)}")


@router.post("/cleanup/cache")
async def manual_cleanup_cache():
    """手动清理缓存"""
    try:
        maintenance_manager = get_maintenance_manager()
        await maintenance_manager.manual_cleanup_cache()

        return {"success": True, "message": "缓存清理完成"}

    except Exception:
        logger.error("手动清理缓存失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"缓存清理失败: {str(e)}")


@router.post("/cleanup/temp")
async def manual_cleanup_temp():
    """手动清理临时文件"""
    try:
        maintenance_manager = get_maintenance_manager()
        await maintenance_manager.manual_cleanup_temp()

        return {"success": True, "message": "临时文件清理完成"}

    except Exception:
        logger.error("手动清理临时文件失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"临时文件清理失败: {str(e)}")


@router.post("/cleanup/logs")
async def manual_cleanup_logs():
    """手动清理日志文件"""
    try:
        maintenance_manager = get_maintenance_manager()
        await maintenance_manager.manual_cleanup_logs()

        return {"success": True, "message": "日志文件清理完成"}

    except Exception:
        logger.error("手动清理日志文件失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"日志文件清理失败: {str(e)}")


@router.post("/cleanup/all")
async def manual_cleanup_all():
    """手动清理所有文件"""
    try:
        maintenance_manager = get_maintenance_manager()

        # 依次清理
        await maintenance_manager.manual_cleanup_cache()
        await maintenance_manager.manual_cleanup_temp()
        await maintenance_manager.manual_cleanup_logs()

        return {"success": True, "message": "所有文件清理完成"}

    except Exception:
        logger.error("手动清理所有文件失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"文件清理失败: {str(e)}")


@router.get("/health")
async def get_maintenance_health():
    """获取维护管理器健康状态"""
    try:
        maintenance_manager = get_maintenance_manager()
        health = await maintenance_manager.health_check()

        return {"success": True, "data": health, "message": "维护管理器健康检查完成"}

    except Exception:
        logger.error("获取维护管理器健康状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")
