#!/bin/bash
# 逐步流量切换脚本

echo "📊 开始逐步切换流量..."

# 检查参数
if [ $# -lt 2 ]; then
    echo "使用方法: $0 <模块名称> <目标流量百分比>"
    echo "示例: $0 '材料出库单列表查询' 50"
    exit 1
fi

MODULE_NAME="$1"
TARGET_PERCENTAGE="$2"

# 验证目标百分比
if ! [[ "$TARGET_PERCENTAGE" =~ ^[0-9]+$ ]] || [ "$TARGET_PERCENTAGE" -lt 0 ] || [ "$TARGET_PERCENTAGE" -gt 100 ]; then
    echo "❌ 错误: 流量百分比必须是0-100之间的整数"
    exit 1
fi

# 检查配置文件
CONFIG_FILE="config/migration_status.json"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 错误: 迁移配置文件不存在: $CONFIG_FILE"
    echo "请先运行 ./create_strangler_proxy.sh"
    exit 1
fi

# 读取当前流量分配
CURRENT_PERCENTAGE=$(python3 -c "
import json
with open('$CONFIG_FILE', 'r') as f:
    config = json.load(f)
print(config.get('modules', {}).get('$MODULE_NAME', {}).get('traffic_split', 0))
")

echo "📋 模块: $MODULE_NAME"
echo "📊 当前流量分配: $CURRENT_PERCENTAGE%"
echo "🎯 目标流量分配: $TARGET_PERCENTAGE%"

# 确认操作
read -p "确认执行流量切换？[y/N] " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 操作已取消"
    exit 1
fi

# 逐步切换函数
gradual_switch() {
    local current=$1
    local target=$2
    local module=$3
    
    if [ "$current" -eq "$target" ]; then
        echo "✅ 流量已达到目标值: $target%"
        return 0
    fi
    
    # 计算步长（每次最多调整10%）
    local step=10
    if [ "$target" -gt "$current" ]; then
        # 增加流量
        while [ "$current" -lt "$target" ]; do
            local next=$((current + step))
            if [ "$next" -gt "$target" ]; then
                next=$target
            fi
            
            echo "🔄 调整流量: $current% → $next%"
            update_traffic_split "$module" "$next"
            
            if [ "$next" -lt "$target" ]; then
                echo "⏳ 等待30秒进行监控..."
                sleep 30
                
                # 检查错误率
                if ! check_error_rate "$module"; then
                    echo "❌ 错误率过高，回滚到 $current%"
                    update_traffic_split "$module" "$current"
                    exit 1
                fi
            fi
            
            current=$next
        done
    else
        # 减少流量
        while [ "$current" -gt "$target" ]; do
            local next=$((current - step))
            if [ "$next" -lt "$target" ]; then
                next=$target
            fi
            
            echo "🔄 调整流量: $current% → $next%"
            update_traffic_split "$module" "$next"
            
            if [ "$next" -gt "$target" ]; then
                echo "⏳ 等待30秒进行监控..."
                sleep 30
            fi
            
            current=$next
        done
    fi
}

# 更新流量分配函数
update_traffic_split() {
    local module=$1
    local percentage=$2
    
    python3 -c "
import json
import sys
from datetime import datetime

try:
    with open('$CONFIG_FILE', 'r') as f:
        config = json.load(f)
    
    if '$module' not in config.get('modules', {}):
        print('错误: 模块不存在: $module')
        sys.exit(1)
    
    config['modules']['$module']['traffic_split'] = $percentage
    config['modules']['$module']['last_updated'] = datetime.now().isoformat()
    
    if $percentage == 100:
        config['modules']['$module']['migration_stage'] = 'completed'
        config['modules']['$module']['migrated'] = True
    elif $percentage > 0:
        config['modules']['$module']['migration_stage'] = 'in_progress'
    else:
        config['modules']['$module']['migration_stage'] = 'not_started'
        config['modules']['$module']['migrated'] = False
    
    with open('$CONFIG_FILE', 'w') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print('✅ 流量分配已更新')
    
except Exception as e:
    print(f'错误: {e}')
    sys.exit(1)
"
}

# 检查错误率函数
check_error_rate() {
    local module=$1
    local max_error_rate=5  # 最大错误率5%
    
    # 检查最近5分钟的日志
    local log_file="logs/strangling/$(date +%Y-%m-%d).jsonl"
    if [ ! -f "$log_file" ]; then
        echo "⚠️ 警告: 日志文件不存在，跳过错误率检查"
        return 0
    fi
    
    local error_rate=$(python3 -c "
import json
import sys
from datetime import datetime, timedelta

module = '$module'
max_error_rate = $max_error_rate

try:
    # 检查最近5分钟的请求
    cutoff_time = datetime.now() - timedelta(minutes=5)
    
    total_requests = 0
    error_requests = 0
    
    with open('$log_file', 'r') as f:
        for line in f:
            try:
                entry = json.loads(line.strip())
                if entry.get('module') != module:
                    continue
                
                # 只检查新系统的请求
                if entry.get('system') != 'new':
                    continue
                
                entry_time = datetime.fromisoformat(entry['timestamp'].replace('Z', '+00:00').replace('+00:00', ''))
                if entry_time < cutoff_time:
                    continue
                
                total_requests += 1
                if not entry.get('success', True):
                    error_requests += 1
                    
            except (json.JSONDecodeError, ValueError, KeyError):
                continue
    
    if total_requests == 0:
        print('0')  # 没有请求，认为错误率为0
    else:
        error_rate = (error_requests / total_requests) * 100
        print(f'{error_rate:.2f}')
        
except Exception as e:
    print('0')  # 出错时认为错误率为0
"
)
    
    echo "📊 最近5分钟错误率: ${error_rate}%"
    
    # 使用bc进行浮点数比较
    if command -v bc &> /dev/null; then
        local is_high=$(echo "$error_rate > $max_error_rate" | bc -l)
        if [ "$is_high" -eq 1 ]; then
            echo "❌ 错误率过高: ${error_rate}% > ${max_error_rate}%"
            return 1
        fi
    else
        # 如果没有bc，使用整数比较
        local error_rate_int=${error_rate%.*}
        if [ "$error_rate_int" -gt "$max_error_rate" ]; then
            echo "❌ 错误率过高: ${error_rate_int}% > ${max_error_rate}%"
            return 1
        fi
    fi
    
    echo "✅ 错误率正常: ${error_rate}% ≤ ${max_error_rate}%"
    return 0
}

# 生成切换报告函数
generate_switch_report() {
    local module=$1
    local from_percentage=$2
    local to_percentage=$3
    
    cat > "reports/traffic_switch_$(date +%Y%m%d_%H%M%S).md" << EOF
# 流量切换报告

## 切换信息
- **模块名称**: $module
- **切换时间**: $(date)
- **流量变化**: $from_percentage% → $to_percentage%
- **操作人员**: $(whoami)

## 切换过程
$(if [ "$to_percentage" -gt "$from_percentage" ]; then
    echo "✅ 逐步增加新系统流量"
else
    echo "⏪ 逐步减少新系统流量"
fi)

## 后续监控
请持续监控以下指标：
- 错误率 < 5%
- 响应时间变化 < 20%
- 用户投诉数量

## 回滚方案
如发现问题，执行以下命令立即回滚：
\`\`\`bash
./gradual_traffic_switch.sh "$module" $from_percentage
\`\`\`
EOF
    
    echo "📄 切换报告已生成: reports/traffic_switch_$(date +%Y%m%d_%H%M%S).md"
}

# 主执行流程
echo "🚀 开始逐步切换流量..."

# 创建必要目录
mkdir -p reports logs/strangling

# 执行逐步切换
gradual_switch "$CURRENT_PERCENTAGE" "$TARGET_PERCENTAGE" "$MODULE_NAME"

# 生成报告
generate_switch_report "$MODULE_NAME" "$CURRENT_PERCENTAGE" "$TARGET_PERCENTAGE"

echo "✅ 流量切换完成！"
echo "📊 当前状态: $MODULE_NAME → $TARGET_PERCENTAGE%"
echo "🔍 监控命令: python scripts/monitor_strangling.py"
