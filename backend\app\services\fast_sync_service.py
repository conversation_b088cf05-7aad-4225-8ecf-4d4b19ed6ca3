import asyncio
import time
from collections import deque
from datetime import datetime

import structlog

from .data_write_manager import DataWriteManager
from .ys_api_client import YSAPIClient

"""
YS-API V3.0 快速同步服务 - 优化版本
基于V2高性能架构重新设计，从30分钟优化到3分钟

设计原则：
1. 简化流程：去除复杂的任务管理和进度跟踪
2. 直接处理：减少数据处理层次
3. 批量操作：一次性完成数据库操作
4. 分层限流：按模块类型设置不同的API限制
5. 流式处理：避免内存溢出，支持大数据量
6. 智能重试：指数退避和错误分类处理
"""


logger = structlog.get_logger()


class ModuleSpecificRateLimiter:
    """模块特定限流器 - 分层限流策略优化版本"""

    def __init___(self):
    """TODO: Add function description."""
    # 基于MD文档的模块限制配置
    self.module_limits = {
         # 高频率模块 (60次/分钟) - 设置55次/分钟，安全余量8.3%
          "purchase_order": 55,
         "sales_order": 55,
            "production_order": 55,
            "subcontract_order": 55,
            "purchase_receipt": 55,
            "sales_out": 55,
            "inventory": 55,
            # 中频率模块 (40次/分钟) - 设置35次/分钟，安全余量12.5%
            "applyorder": 35,
            "subcontract_requisition": 35,
            "subcontract_receipt": 35,
            "product_receipt": 35,
            "materialout": 35,
            # 未透出模块 - 保守策略30次/分钟
            "requirements_planning": 30,
            "inventory_report": 30,
            "material_master": 30,
         }

     # 添加模块类型标识（用于监控和报告）
     self.module_types = {
          "high": [
               "purchase_order",
                "sales_order",
                "production_order",
                "subcontract_order",
                "purchase_receipt",
                "sales_out",
                "inventory",
               ],
           "medium": [
               "applyorder",
                "subcontract_requisition",
                "subcontract_receipt",
                "product_receipt",
                "materialout",
               ],
           "unknown": [
               "requirements_planning",
                "inventory_report",
                "material_master"],
          }

      # 为每个模块创建独立的限流器
      self.rate_limiters = {}

       # 监控指标
       self.metrics = {}

        logger.info(
            "模块特定限流器初始化完成",
            high_frequency_modules=len(self.module_types["high"]),
            medium_frequency_modules=len(self.module_types["medium"]),
            unknown_modules=len(self.module_types["unknown"]),
        )

    def get_module_type(self, module_name: str) -> str:
        """获取模块类型"""
        if module_name in self.module_types["high"]:
            return "high"
        elif module_name in self.module_types["medium"]:
            return "medium"
        else:
            return "unknown"

    def get_limiter(self, module_name: str) -> 'APIRateLimiter':
        """获取模块特定的限流器（启用自适应限流器）"""
        if module_name not in self.rate_limiters:
            limit = self.module_limits.get(module_name, 30)
            module_type = self.get_module_type(module_name)

            # 使用自适应限流器（传入模块类型）
            self.rate_limiters[module_name] = AdaptiveAPIRateLimiter(
                limit, module_type)

            # 初始化监控指标
            self.metrics[module_name] = {
                "total_requests": 0,
                "total_wait_time": 0.0,
                "rate_limit_triggers": 0,
                "last_request_time": 0.0,
                "module_type": module_type,
                "adaptive_limit": limit,  # 初始值
            }

            logger.info(
                "创建模块限流器",
                module_name=module_name,
                module_type=module_type,
                initial_limit=limit,
            )

        return self.rate_limiters[module_name]

    def record_metrics(
        self, module_name: str, wait_time: float, was_rate_limited: bool
    ):
        """记录监控指标（修复版本）"""
        if module_name in self.metrics:
            self.metrics[module_name]["total_requests"] += 1
            self.metrics[module_name]["total_wait_time"] += wait_time
            self.metrics[module_name]["last_request_time"] = now()

            # 修复：正确区分正常等待和限流触发
            # was_rate_limited 应该由调用方根据实际限流情况传递
            if was_rate_limited:
                self.metrics[module_name]["rate_limit_triggers"] += 1

    def get_metrics(self, module_name: str) -> Dict[str, Any]:
        """获取模块监控指标"""
        if module_name in self.metrics:
            metrics = self.metrics[module_name].copy()

            # 添加自适应限流器的当前状态
            if module_name in self.rate_limiters:
                limiter = self.rate_limiters[module_name]
                if isinstance(limiter, AdaptiveAPIRateLimiter):
                    metrics.update(
                        {
                            "current_adaptive_limit": limiter.current_limit,
                            "min_limit": limiter.min_limit,
                            "max_limit": limiter.max_limit,
                            "success_count": limiter.success_count,
                            "error_count": limiter.error_count,
                        }
                    )

            return metrics
        return {}

    def get_all_metrics(self) -> Dict[str, Any]:
        """获取所有模块的监控指标"""
        return {
            "module_limits": self.module_limits,
            "module_types": self.module_types,
            "metrics": self.metrics,
            "adaptive_adjustments": self._get_adaptive_adjustments(),
        }

    def _get_adaptive_adjustments(self) -> Dict[str, Dict[str, Any]]:
        """获取自适应调整情况"""
        adjustments = {}
        for module_name, limiter in self.rate_limiters.items():
            if isinstance(limiter, AdaptiveAPIRateLimiter):
                initial_limit = self.module_limits.get(module_name, 30)
                adjustments[module_name] = {
                    "initial_limit": initial_limit,
                    "final_limit": limiter.current_limit,
                    "adjustment_direction": (
                        "up" if limiter.current_limit > initial_limit else "down"),
                    "adjustment_percentage": (
                        (limiter.current_limit - initial_limit) / initial_limit) * 100,
                }
        return adjustments


class APIRateLimiter:
    """API频率限制器 - 优化版本（修复并发竞态条件）"""

    def __init___(
            self,
            max_requests_per_minute: int = 55,
            time_window: int = 60):
    """TODO: Add function description."""
    self.max_requests = max_requests_per_minute
     self.time_window = time_window
      self.requests = deque()
       self.total_wait_time = 0.0
        self.total_requests = 0
        self.rate_limit_triggers = 0
        self._lock = threading.Lock()  # 添加线程锁

    def wait_if_needed(self) -> float:
        """等待以避免超过频率限制，返回等待时间（线程安全版本）"""
        start_time = now()
        current_time = start_time

        with self._lock:  # 原子操作保护
            # 清理过期的请求记录
            while self.requests and current_time - \
                    self.requests[0] > self.time_window:
                self.requests.popleft()

            # 如果超过限制，等待
            if len(self.requests) >= self.max_requests:
                wait_time = self.time_window - \
                    (current_time - self.requests[0])
                if wait_time > 0:
                    logger.warning(
                        f"⏰ API频率控制：等待 {wait_time:.1f} 秒避免限流..."
                    )
                    # 释放锁后再等待，避免阻塞其他线程
                    should_wait = True
                    wait_duration = wait_time
                else:
                    should_wait = False
                    wait_duration = 0
            else:
                should_wait = False
                wait_duration = 0

            # 记录请求
            self.requests.append(current_time)
            self.total_requests += 1

        # 在锁外执行等待操作
        if should_wait:
            time.sleep(wait_duration)
            self.rate_limit_triggers += 1
            current_time = now()

        # 计算并返回等待时间
        wait_time = current_time - start_time
        self.total_wait_time += wait_time

        return wait_time

    async def wait_if_needed_async(self) -> float:
        """异步等待以避免超过频率限制，返回等待时间（非阻塞版本）"""
        start_time = now()
        current_time = start_time

        with self._lock:  # 原子操作保护
            # 清理过期的请求记录
            while self.requests and current_time - \
                    self.requests[0] > self.time_window:
                self.requests.popleft()

            # 如果超过限制，等待
            if len(self.requests) >= self.max_requests:
                wait_time = self.time_window - \
                    (current_time - self.requests[0])
                if wait_time > 0:
                    logger.warning(
                        f"⏰ API频率控制：等待 {wait_time:.1f} 秒避免限流..."
                    )
                    # 释放锁后再等待，避免阻塞其他协程
                    should_wait = True
                    wait_duration = wait_time
                else:
                    should_wait = False
                    wait_duration = 0
            else:
                should_wait = False
                wait_duration = 0

            # 记录请求
            self.requests.append(current_time)
            self.total_requests += 1

        # 在锁外执行异步等待操作
        if should_wait:
            await asyncio.sleep(wait_duration)
            self.rate_limit_triggers += 1
            current_time = now()

        # 计算并返回等待时间
        wait_time = current_time - start_time
        self.total_wait_time += wait_time

        return wait_time

    def record_request(self):
        """记录请求（兼容性方法）"""
        with self._lock:
            self.requests.append(now())
            self.total_requests += 1

    def get_metrics(self) -> Dict[str, Any]:
        """获取限流器指标"""
        return {
            "total_requests": self.total_requests,
            "total_wait_time": self.total_wait_time,
            "rate_limit_triggers": self.rate_limit_triggers,
            "current_queue_size": len(self.requests),
            "max_requests_per_minute": self.max_requests,
        }


class AdaptiveAPIRateLimiter(APIRateLimiter):
    """自适应API频率限制器 - 模块类型感知版本"""

    def __init___(self, base_limit: int = 55, module_type: str = "unknown"):
    """TODO: Add function description."""
    super().__init__(base_limit, 60)
     self.base_limit = base_limit
      self.module_type = module_type
       self.current_limit = base_limit
        self.success_count = 0
        self.error_count = 0
        self.last_adjustment_time = now()
        self.adjustment_interval = 60  # 60秒调整一次
        self._lock = threading.Lock()  # 添加线程锁

        # 基于模块类型设置不同的限流范围
        if module_type == "high":
            self.min_limit = 40
            self.max_limit = 60  # 允许试探性突破基础限制
        elif module_type == "medium":
            self.min_limit = 25
            self.max_limit = 45
        else:  # unknown
            self.min_limit = 20
            self.max_limit = 35

        logger.info(
            "自适应限流器初始化",
            module_type=module_type,
            base_limit=base_limit,
            min_limit=self.min_limit,
            max_limit=self.max_limit,
        )

    def record_outcome(self, success: bool):
        """记录请求结果，用于动态调整限流值（线程安全版本）"""
        with self._lock:  # 保护状态修改
            current_time = now()
            time_since_last_adjust = current_time - self.last_adjustment_time

            if success:
                self.success_count += 1
                self.error_count = 0

                # 成功时试探性增加限额（仅当超过调整间隔时）
                if time_since_last_adjust >= self.adjustment_interval:
                    # 连续成功次数越多，增加的幅度越大
                    increment = min(1 + (self.success_count // 5), 5)
                    self.current_limit = min(
                        self.current_limit + increment, self.max_limit
                    )
                    self.last_adjustment_time = current_time
                    logger.info(
                        "自适应限流器调整：成功率良好，限额提升",
                        module_type=self.module_type,
                        new_limit=self.current_limit,
                        success_count=self.success_count,
                    )
            else:
                self.error_count += 1
                self.success_count = 0

                # 失败时保守降低限额（立即调整）
                decrement = min(2 + (self.error_count // 2), 10)
                self.current_limit = max(
                    self.min_limit, self.current_limit - decrement)
                self.last_adjustment_time = current_time
                logger.warning(
                    "自适应限流器调整：错误率过高，限额降低",
                    module_type=self.module_type,
                    new_limit=self.current_limit,
                    error_count=self.error_count,
                )

    async def wait_if_needed_async(self) -> float:
        """使用当前动态限额进行异步等待"""
        # 临时使用动态限额
        original_max_requests = self.max_requests
        self.max_requests = self.current_limit

        try:
            return await super().wait_if_needed_async()
        finally:
            # 恢复原始限额
            self.max_requests = original_max_requests

    def get_metrics(self) -> Dict[str, Any]:
        """获取限流器指标"""
        return {
            "base_limit": self.base_limit,
            "current_limit": self.current_limit,
            "module_type": self.module_type,
            "min_limit": self.min_limit,
            "max_limit": self.max_limit,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "last_adjustment_time": self.last_adjustment_time,
        }


class PageLevelRateLimiter:
    """页级并发控制器 - 突破单模块串行瓶颈"""

    def __init___(self, module_name: str, base_limiter: APIRateLimiter):
    """TODO: Add function description."""
    self.module_name = module_name
     self.base_limiter = base_limiter

      # 根据模块类型设置并发页数
      module_limit = base_limiter.max_requests
       if module_limit >= 50:  # 高频率模块
            self.concurrent_pages = 2  # 2页并发
            self.pages_per_minute = module_limit // 2  # 每页27.5次/分钟
        else:  # 中低频率模块
            self.concurrent_pages = 1  # 1页并发
            self.pages_per_minute = module_limit

        self.semaphore = asyncio.Semaphore(self.concurrent_pages)
        self.page_metrics = {
            "total_pages": 0,
            "concurrent_pages": self.concurrent_pages,
            "pages_per_minute": self.pages_per_minute,
            "active_pages": 0,
        }

        logger.info(
            "页级并发控制器初始化",
            module_name=module_name,
            concurrent_pages=self.concurrent_pages,
            pages_per_minute=self.pages_per_minute,
        )

    async def fetch_page(self, page: int, fetch_fn, *args, **kwargs):
        """并发获取页面数据"""
        async with self.semaphore:
            self.page_metrics["active_pages"] += 1
            self.page_metrics["total_pages"] += 1

            try:
                # 使用基础限流器控制API调用频率
                wait_time = await self.base_limiter.wait_if_needed_async()

                # 执行页面获取
                result = await fetch_fn(page, *args, **kwargs)

                logger.debug(
                    "页级并发获取完成",
                    module_name=self.module_name,
                    page=page,
                    wait_time=wait_time,
                    active_pages=self.page_metrics["active_pages"],
                )

                return result

            finally:
                self.page_metrics["active_pages"] -= 1

    def get_metrics(self) -> Dict[str, Any]:
        """获取页级并发指标"""
        return {
            **self.page_metrics,
            "base_limiter_metrics": self.base_limiter.get_metrics(),
        }


class FastSyncService:
    """快速同步服务 - 优化版本"""

    def __init___(self):
    """TODO: Add function description."""
    self.ys_client = YSAPIClient()
     self.data_write_manager = DataWriteManager()
      self.module_rate_limiter = ModuleSpecificRateLimiter()  # 模块特定限流器

       # 支持的模块列表
       self.modules = [
            "purchase_order",
            "sales_order",
            "production_order",
            "subcontract_order",
            "applyorder",
            "subcontract_requisition",
            "product_receipt",
            "purchase_receipt",
            "subcontract_receipt",
            "materialout",
            "sales_out",
            "inventory",
            "inventory_report",
            "requirements_planning",
            "material_master",
        ]

        # 同步结果
        self.sync_results = {
            "start_time": None,
            "end_time": None,
            "total_duration": 0,
            "modules": {},
            "summary": {},
            "metrics": {},
        }

        # 配置参数
        self.batch_size = 10000  # 分批处理大小
        self.max_retries = 3  # 最大重试次数
        self.concurrent_modules = 3  # 并发模块数

        logger.info("快速同步服务初始化完成")

    def get_optimal_page_size(
        self, module_name: str, estimated_total: Optional[int] = None
    ) -> int:
        """
        获取最优页面大小 - 动态批大小优化

        Args:
            module_name: 模块名称
            estimated_total: 预估总记录数

        Returns:
            int: 最优页面大小
        """
        # 如果没有预估总数，使用默认策略
        if estimated_total is None:
            # 根据模块类型设置默认页面大小
            if module_name in ["inventory", "material_master"]:
                return 500  # 大数据模块使用较小页面
            else:
                return 1000  # 其他模块使用1000条/页

        # 根据预估总数动态调整页面大小
        if estimated_total < 1000:
            page_size = 1000  # 小模块：1页完成
        elif estimated_total < 5000:
            page_size = 2000  # 中等模块：2-3页
        elif estimated_total < 20000:
            page_size = 5000  # 大模块：减少页数
        else:
            page_size = 10000  # 超大模块：大幅减少页数

        # 确保页面大小不超过模块限制
        module_limit = self.module_rate_limiter.module_limits.get(
            module_name, 30)
        if module_limit < 40:  # 低频率模块使用较小页面
            page_size = min(page_size, 1000)

        logger.info(
            "动态批大小优化",
            module_name=module_name,
            estimated_total=estimated_total,
            optimal_page_size=page_size,
            expected_pages=(
                (estimated_total + page_size - 1) // page_size
                if estimated_total
                else "unknown"
            ),
        )

        return page_size

    async def sync_all_modules(
        self,
        record_limit: Optional[int] = None,
        force_recreate_tables: bool = False,
        clear_existing_data: bool = True,
    ) -> Dict[str, Any]:
        """
        快速同步所有模块 - 优化版本 (异步版本)

        Args:
            record_limit: 记录数限制
            force_recreate_tables: 是否强制重建表

        Returns:
            Dict: 同步结果
        """
        start_time = datetime.now()
        self.sync_results["start_time"] = start_time.isoformat()

        logger.info("🚀 开始快速全量同步", modules_count=len(self.modules))

        # 创建信号量控制并发度
        semaphore = asyncio.Semaphore(self.concurrent_modules)

        async def sync_wrapper(module_name: str):
            """模块同步包装器"""
            async with semaphore:
                return await self._sync_single_module_fast(
                    module_name,
                    record_limit,
                    force_recreate_tables,
                    clear_existing_data,
                )

        # 并发处理模块
        tasks = [sync_wrapper(module) for module in self.modules]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for module_name, result in zip(self.modules, results):
            if isinstance(result, Exception):
                self.sync_results["modules"][module_name] = {
                    "module_name": module_name,
                    "success": False,
                    "error_message": str(result),
                    "start_time": datetime.now().isoformat(),
                    "end_time": datetime.now().isoformat(),
                    "duration": 0,
                    "api_records": 0,
                    "written_records": 0,
                }
            else:
                self.sync_results["modules"][module_name] = result

        # 完成统计
        end_time = datetime.now()
        self.sync_results["end_time"] = end_time.isoformat()
        self.sync_results["total_duration"] = (
            end_time - start_time).total_seconds()

        # 收集监控指标
        self._collect_metrics()
        self._generate_summary()

        logger.info(
            f"✅ 快速全量同步完成，同步总耗时: {self.sync_results['total_duration']:.1f}秒"
        )

        return self.sync_results

    async def _sync_single_module_fast(
        self,
        module_name: str,
        record_limit: Optional[int] = None,
        force_recreate_table: bool = False,
        clear_existing_data: bool = True,
    ) -> Dict[str, Any]:
        """
        快速同步单个模块 - 集成所有优化

        Args:
            module_name: 模块名称
            record_limit: 记录数限制
            force_recreate_table: 是否强制重建表

        Returns:
            Dict[str, Any]: 同步结果
        """
        start_time = time.time()

        try:
            logger.info(
                "开始快速同步模块",
                module_name=module_name,
                record_limit=record_limit,
                force_recreate_table=force_recreate_table,
            )

            # 1. 获取模块特定的限流器
            base_limiter = self.module_rate_limiter.get_limiter(module_name)

            # 2. 创建页级并发控制器
            page_limiter = PageLevelRateLimiter(module_name, base_limiter)

            # 3. 获取第一页数据，估算总记录数
            first_page_data = await self._fetch_single_page_with_retry(
                module_name, 1, 1000, base_limiter
            )

            if not first_page_data:
                logger.warning("模块无数据", module_name=module_name)
                return {
                    "module_name": module_name,
                    "success": True,
                    "records_written": 0,
                    "pages_processed": 0,
                    "duration": time.time() - start_time,
                    "message": "模块无数据",
                }

            # 4. 动态计算最优页面大小
            estimated_total = len(first_page_data) * 10  # 粗略估算
            optimal_page_size = self.get_optimal_page_size(
                module_name, estimated_total)

            # 5. 创建预拉取缓存器
            prefetch_fetcher = PrefetchPageFetcher(
                module_name,
                page_limiter,
                self._fetch_single_page_with_retry,
                optimal_page_size,
            )

            # 6. 创建异步重试队列
            retry_queue = RetryPageQueue(
                module_name, lambda data: self._write_batch_directly(
                    module_name, data, force_recreate_table, clear_existing_data), )

            # 7. 启动预拉取和重试工作线程
            await prefetch_fetcher.start_prefetch()
            await retry_queue.start_retry_worker()

            # 8. 处理第一页数据
            all_records = []
            pages_processed = 0
            error_pages = []

            # 处理第一页
            if first_page_data:
                all_records.extend(first_page_data)
                pages_processed += 1

                # 检查记录限制
                if record_limit and len(all_records) >= record_limit:
                    all_records = all_records[:record_limit]
                    logger.info(
                        "已达到记录限制",
                        module_name=module_name,
                        records_count=len(all_records),
                        limit=record_limit,
                    )
                    return await self._finalize_sync_result(
                        module_name,
                        all_records,
                        pages_processed,
                        start_time,
                        prefetch_fetcher,
                        retry_queue,
                        page_limiter,
                    )

            # 9. 使用预拉取处理剩余页面
            max_pages = 10  # 限制最大页数，避免死循环
            while pages_processed < max_pages:
                # 从预拉取缓存获取下一页
                page_info = await prefetch_fetcher.get_next_page()

                if page_info is None:
                    # 预拉取超时或完成
                    break

                page_num, page_data = page_info

                if not page_data:
                    # 空页面，已到末尾
                    logger.info(
                        "遇到空页面，停止处理", module_name=module_name, page=page_num
                    )
                    break

                # 处理页面数据
                all_records.extend(page_data)
                pages_processed += 1

                # 检查记录限制
                if record_limit and len(all_records) >= record_limit:
                    all_records = all_records[:record_limit]
                    logger.info(
                        "已达到记录限制",
                        module_name=module_name,
                        records_count=len(all_records),
                        limit=record_limit,
                    )
                    break

                # 额外安全检查：如果单页数据量很大，也要限制
                if len(all_records) > 10000:
                    logger.info(
                        "数据量过大，停止处理",
                        module_name=module_name,
                        records_count=len(all_records),
                    )
                    break

            # 10. 等待重试队列完成
            await retry_queue.wait_for_completion()

            # 11. 写入数据
            if all_records:
                write_result = await self._write_batch_directly(
                    module_name, all_records, force_recreate_table, clear_existing_data
                )
            else:
                write_result = {"success": True, "records_written": 0}

            # 12. 停止预拉取和重试工作线程
            await prefetch_fetcher.stop_prefetch()
            await retry_queue.stop_retry_worker()

            # 13. 生成最终结果
            return await self._finalize_sync_result(
                module_name,
                all_records,
                pages_processed,
                start_time,
                prefetch_fetcher,
                retry_queue,
                page_limiter,
                write_result,
                error_pages,
            )

        except Exception:
            logger.error("快速同步模块失败", module_name=module_name, error=str(e))
            return {
                "module_name": module_name,
                "success": False,
                "error": str(e),
                "duration": time.time() - start_time,
            }

    async def _finalize_sync_result(
        self,
        module_name: str,
        all_records: List[Dict],
        pages_processed: int,
        start_time: float,
        prefetch_fetcher: 'PrefetchPageFetcher',
        retry_queue: 'RetryPageQueue',
        page_limiter: 'PageLevelRateLimiter',
        write_result: Optional[Dict] = None,
        error_pages: Optional[List] = None,
    ) -> Dict[str, Any]:
        """生成最终的同步结果"""
        duration = time.time() - start_time

        # 收集各种指标
        prefetch_metrics = prefetch_fetcher.get_cache_status()
        retry_metrics = retry_queue.get_metrics()
        page_limiter_metrics = page_limiter.get_metrics()

        result = {
            "module_name": module_name,
            "success": True,
            "records_written": len(all_records),
            "pages_processed": pages_processed,
            "duration": duration,
            "performance_metrics": {
                "prefetch_metrics": prefetch_metrics,
                "retry_metrics": retry_metrics,
                "page_limiter_metrics": page_limiter_metrics,
                "error_pages": error_pages or [],
            },
        }

        if write_result:
            result.update(write_result)

        logger.info(
            "快速同步模块完成",
            module_name=module_name,
            records_written=len(all_records),
            pages_processed=pages_processed,
            duration=f"{duration:.2f}s",
            prefetch_cache_hits=prefetch_metrics.get(
                "cache_size",
                0),
            retry_success_rate=f"{retry_metrics.get('successful_retries', 0)}/{retry_metrics.get('total_retries', 0)}",
        )

        return result

    async def _fetch_all_pages_optimized(
        self, module_name: str, record_limit: Optional[int] = None
    ) -> List[Dict]:
        """
        优化的分页数据获取 - 流式处理版本

        Args:
            module_name: 模块名称
            record_limit: 记录数限制

        Returns:
            List[Dict]: 所有数据记录
        """
        try:
            # 获取模块特定的限流器
            rate_limiter = self.module_rate_limiter.get_limiter(module_name)

            # 动态页面大小：大数据模块使用较小页面
            if module_name in ["inventory", "material_master"]:
                page_size = 500  # 大数据模块使用500条/页
            else:
                page_size = 1000  # 其他模块使用1000条/页

            logger.info(
                "开始分页数据获取",
                module_name=module_name,
                page_size=page_size,
                record_limit=record_limit,
            )

            # 获取第一页数据
            first_page_data = await self._fetch_single_page_with_retry(
                module_name, 1, page_size, rate_limiter
            )

            if not first_page_data:
                logger.warning("第一页数据为空", module_name=module_name)
                return []

            # 尝试提取总页数
            total_pages = self._extract_total_pages(first_page_data, page_size)

            if total_pages is None:
                # 使用探测模式：逐页获取直到空页
                logger.info("使用探测模式获取数据", module_name=module_name)
                return await self._fetch_pages_with_probe(
                    module_name, page_size, rate_limiter, record_limit
                )
            elif total_pages <= 0:
                logger.warning("无法确定总页数，终止获取", module_name=module_name)
                return []

            logger.info(
                "开始批量获取数据",
                module_name=module_name,
                total_pages=total_pages,
                page_size=page_size,
            )

            # 流式处理：逐页获取并立即处理，避免大列表缓存
            all_records = []
            error_pages = []
            total_records = 0

            # 处理第一页数据
            all_records.extend(first_page_data)
            total_records += len(first_page_data)

            # 检查记录限制
            if record_limit and total_records >= record_limit:
                logger.info(
                    "已达到记录限制",
                    module_name=module_name,
                    total_records=total_records,
                    limit=record_limit,
                )
                return all_records[:record_limit]

            # 获取剩余页面
            for page in range(2, total_pages + 1):
                try:
                    # 等待限流
                    wait_time = await rate_limiter.wait_if_needed_async()
                    if wait_time > 0:
                        self.module_rate_limiter.record_metrics(
                            module_name, wait_time, True
                        )

                    # 获取页面数据
                    page_data = await self._fetch_single_page_with_retry(
                        module_name, page, page_size, rate_limiter
                    )

                    if page_data:
                        all_records.extend(page_data)
                        total_records += len(page_data)

                        # 记录成功
                        rate_limiter.record_outcome(True)

                        # 检查记录限制
                        if record_limit and total_records >= record_limit:
                            logger.info(
                                "已达到记录限制",
                                module_name=module_name,
                                total_records=total_records,
                                limit=record_limit,
                            )
                            return all_records[:record_limit]
                    else:
                        # 空页面，可能已到末尾
                        logger.info(
                            "遇到空页面，停止获取", module_name=module_name, page=page
                        )
                        break

                except Exception:
                    error_pages.append(page)
                    rate_limiter.record_outcome(False)

                    # 容忍10%的错误率
                    if len(error_pages) > 0.1 * total_pages:
                        logger.error(
                            "错误页面过多，停止获取",
                            module_name=module_name,
                            error_pages=error_pages,
                            total_pages=total_pages,
                        )
                        break

                    logger.warning(
                        "页面获取失败，继续下一页",
                        module_name=module_name,
                        page=page,
                        error=str(e),
                    )
                    continue

            # 记录错误页面
            if error_pages:
                logger.warning(
                    "部分页面获取失败",
                    module_name=module_name,
                    error_pages=error_pages,
                    total_pages=total_pages,
                )

            logger.info(
                "数据获取完成",
                module_name=module_name,
                total_records=len(all_records),
                error_pages=len(error_pages),
            )

            return all_records

        except Exception:
            logger.error("分页数据获取失败", module_name=module_name, error=str(e))
            return []

    async def _fetch_pages_with_probe(
        self,
        module_name: str,
        page_size: int,
        rate_limiter: APIRateLimiter,
        record_limit: Optional[int] = None,
    ) -> List[Dict]:
        """
        探测模式：逐页获取直到空页

        Args:
            module_name: 模块名称
            page_size: 页面大小
            rate_limiter: 限流器
            record_limit: 记录数限制

        Returns:
            List[Dict]: 所有数据记录
        """
        all_records = []
        page = 1
        total_records = 0

        while True:
            try:
                # 等待限流
                wait_time = await rate_limiter.wait_if_needed_async()
                if wait_time > 0:
                    self.module_rate_limiter.record_metrics(
                        module_name, wait_time, True
                    )

                # 获取页面数据
                page_data = await self._fetch_single_page_with_retry(
                    module_name, page, page_size, rate_limiter
                )

                if not page_data:
                    # 空页面，已到末尾
                    logger.info(
                        "探测到空页面，停止获取", module_name=module_name, page=page
                    )
                    break

                all_records.extend(page_data)
                total_records += len(page_data)

                # 记录成功
                rate_limiter.record_outcome(True)

                # 检查记录限制
                if record_limit and total_records >= record_limit:
                    logger.info(
                        "已达到记录限制",
                        module_name=module_name,
                        total_records=total_records,
                        limit=record_limit,
                    )
                    return all_records[:record_limit]

                page += 1

                # 安全限制：最多获取1000页
                if page > 1000:
                    logger.warning(
                        "达到最大页数限制，停止获取",
                        module_name=module_name,
                        max_pages=1000,
                    )
                    break

            except Exception:
                rate_limiter.record_outcome(False)
                logger.warning(
                    "页面获取失败，停止探测",
                    module_name=module_name,
                    page=page,
                    error=str(e),
                )
                break

        logger.info(
            "探测模式获取完成",
            module_name=module_name,
            total_records=len(all_records),
            pages_fetched=page - 1,
        )

        return all_records

    async def _fetch_single_page_with_retry(
        self,
        module_name: str,
        page: int,
        page_size: int,
        rate_limiter: APIRateLimiter,
        max_retries: int = 3,
    ) -> List[Dict]:
        """
        带重试的单页获取

        Args:
            module_name: 模块名称
            page: 页码
            page_size: 页大小
            rate_limiter: 限流器
            max_retries: 最大重试次数

        Returns:
            List[Dict]: 页面数据
        """
        for attempt in range(max_retries):
            try:
                wait_time = await rate_limiter.wait_if_needed_async()

                # 记录监控指标
                self.module_rate_limiter.record_metrics(
                    module_name, wait_time, wait_time > 0
                )

                page_data = await self.ys_client.fetch_module_data(
                    module_name=module_name,
                    limit=page_size,
                    filters={"pageIndex": page},
                )

                return page_data

            except asyncio.TimeoutError:
                if attempt < max_retries - 1:
                    wait = 2**attempt  # 指数退避
                    logger.warning(
                        f"第{page}页获取超时，重试中...",
                        module_name=module_name,
                        attempt=attempt + 1,
                        wait_sec=wait,
                    )
                    await asyncio.sleep(wait)
                else:
                    logger.error(
                        f"第{page}页获取超时，已达最大重试次数", module_name=module_name
                    )
                    raise

            except Exception:
                error_msg = str(e).lower()
                if "限流" in error_msg or "429" in error_msg:
                    # 限流时等待1分钟
                    logger.warning(
                        f"第{page}页触发限流，等待1分钟...", module_name=module_name
                    )
                    await asyncio.sleep(60)
                elif attempt < max_retries - 1:
                    wait = 2**attempt  # 指数退避
                    logger.warning(
                        f"第{page}页获取失败，重试中...",
                        module_name=module_name,
                        attempt=attempt + 1,
                        wait_sec=wait,
                        error=str(e),
                    )
                    await asyncio.sleep(wait)
                else:
                    logger.error(
                        f"第{page}页获取失败，已达最大重试次数",
                        module_name=module_name,
                        error=str(e),
                    )
                    raise

        return []

    def _extract_total_pages(
        self, first_page_data: List[Dict], page_size: int
    ) -> Optional[int]:
        """
        增强的页数提取逻辑 - 支持多种API响应结构

        Args:
            first_page_data: 第一页数据
            page_size: 页面大小

        Returns:
            Optional[int]: 总页数，如果无法确定则返回None
        """
        try:
            # 如果第一页数据为空，无法确定总页数
            if not first_page_data:
                logger.warning("第一页数据为空，无法确定总页数")
                return None

            # 尝试从数据中提取分页信息
            # 检查是否有分页元数据字段
            if isinstance(first_page_data, list) and len(first_page_data) > 0:
                first_record = first_page_data[0]
                if isinstance(first_record, dict):
                    # 检查常见的分页字段
                    pagination_fields = [
                        "pagination",
                        "pageInfo",
                        "page_info",
                        "meta",
                        "metadata",
                    ]

                    for field in pagination_fields:
                        if field in first_record:
                            pagination = first_record[field]
                            if isinstance(pagination, dict):
                                total = (
                                    pagination.get("totalCount")
                                    or pagination.get("recordCount")
                                    or pagination.get("total")
                                )
                                if total and isinstance(total, (int, str)):
                                    total = int(total)
                                    if total > 0:
                                        total_pages = (
                                            total + page_size - 1
                                        ) // page_size
                                        logger.info(
                                            "从分页字段提取总页数",
                                            field=field,
                                            total=total,
                                            total_pages=total_pages,
                                        )
                                        return total_pages

            # 如果第一页数据量等于页面大小，说明可能还有更多页
            if len(first_page_data) == page_size:
                logger.info("第一页数据量等于页面大小，假设还有更多页，将使用探测模式")
                return None  # 返回None，使用探测模式

            # 如果第一页数据量小于页面大小，说明只有一页
            if len(first_page_data) < page_size:
                logger.info(
                    "第一页数据量小于页面大小，确定只有一页",
                    data_count=len(first_page_data),
                    page_size=page_size,
                )
                return 1

            # 无法确定，返回None使用探测模式
            logger.warning("无法确定总页数，将使用探测模式")
            return None

        except Exception:
            logger.error("提取总页数时发生错误", error=str(e))
            return None

    async def _execute_write_sync_optimized(
        self,
        module_name: str,
        record_limit: Optional[int] = None,
        force_recreate_table: bool = False,
    ) -> Dict[str, Any]:
        """
        优化的写入同步 - 支持流式处理和分批写入

        Args:
            module_name: 模块名称
            record_limit: 记录数限制
            force_recreate_table: 是否强制重建表

        Returns:
            Dict: 写入结果
        """
        try:
            logger.info(f"🚀 使用优化同步", module_name=module_name)

            # 1. 使用优化分页获取数据
            all_data = await self._fetch_all_pages_optimized(module_name, record_limit)

            if not all_data:
                return {
                    "success": True,
                    "message": f"模块 '{module_name}' 无数据",
                    "records_processed": 0,
                    "records_written": 0,
                }

            # 2. 分批写入数据（避免内存溢出）
            result = await self._write_data_in_batches(
                module_name, all_data, force_recreate_table
            )

            # 3. 数据一致性校验
            if result.get("success", False):
                consistency_result = await self._verify_data_consistency(
                    module_name, len(all_data)
                )
                result["consistency_check"] = consistency_result

            return result

        except Exception:
            logger.error("写入同步执行失败", module_name=module_name, error=str(e))
            return {"success": False, "message": f"写入同步失败: {str(e)}"}

    async def _write_data_in_batches(
        self, module_name: str, api_data: List[Dict], force_recreate_table: bool = False
    ) -> Dict[str, Any]:
        """
        分批写入数据 - 避免内存溢出

        Args:
            module_name: 模块名称
            api_data: API数据
            force_recreate_table: 是否强制重建表

        Returns:
            Dict: 写入结果
        """
        total_records = len(api_data)
        total_written = 0

        logger.info(
            f"📝 开始分批写入",
            module_name=module_name,
            total_records=total_records,
            batch_size=self.batch_size,
        )

        # 表重建应该在所有批次写入前执行
        if force_recreate_table:
            logger.info(f"🔄 强制重建表", module_name=module_name)
            await self.data_write_manager.recreate_table(module_name)

        # 分批处理
        for i in range(0, total_records, self.batch_size):
            batch_data = api_data[i: i + self.batch_size]
            batch_num = i // self.batch_size + 1
            total_batches = (
                total_records + self.batch_size - 1) // self.batch_size

            logger.info(
                f"📝 写入批次 {batch_num}/{total_batches}",
                module_name=module_name,
                batch_records=len(batch_data),
            )

            try:
                # 写入当前批次（表重建已在前面完成）
                batch_result = await self._write_batch_directly(
                    module_name, batch_data, False, True
                )

                if batch_result.get("success"):
                    total_written += batch_result.get("records_written", 0)
                    logger.info(
                        f"✅ 批次 {batch_num} 写入成功",
                        module_name=module_name,
                        written=batch_result.get("records_written", 0),
                    )
                else:
                    logger.error(
                        f"❌ 批次 {batch_num} 写入失败",
                        module_name=module_name,
                        error=batch_result.get("message"),
                    )
                    return {
                        "success": False,
                        "message": f"批次 {batch_num} 写入失败: {batch_result.get('message')}",
                        "records_processed": total_records,
                        "records_written": total_written,
                    }

            except Exception:
                logger.error(
                    f"❌ 批次 {batch_num} 写入异常",
                    module_name=module_name,
                    error=str(e),
                )
                return {
                    "success": False,
                    "message": f"批次 {batch_num} 写入异常: {str(e)}",
                    "records_processed": total_records,
                    "records_written": total_written,
                }

        logger.info(
            f"✅ 分批写入完成", module_name=module_name, total_written=total_written
        )

        return {
            "success": True,
            "message": f"成功写入 {total_written} 条记录",
            "records_processed": total_records,
            "records_written": total_written,
        }

    async def _write_batch_directly(
        self,
        module_name: str,
        batch_data: List[Dict],
        force_recreate_table: bool = False,
        clear_existing_data: bool = True,
    ) -> Dict[str, Any]:
        """
        直接写入批次数据

        Args:
            module_name: 模块名称
            batch_data: 批次数据
            force_recreate_table: 是否强制重建表

        Returns:
            Dict: 写入结果
        """
        try:
            # 直接调用写入管理器，使用真实API数据
            result = await self.data_write_manager.write_single_module(
                module_name=module_name,
                record_limit=None,
                force_recreate_table=force_recreate_table,
                clear_existing_data=clear_existing_data,
            )

            return result

        except Exception:
            logger.error("批次写入失败", module_name=module_name, error=str(e))
            return {"success": False, "message": f"批次写入失败: {str(e)}"}

    async def _verify_data_consistency(
        self, module_name: str, api_total: int
    ) -> Dict[str, Any]:
        """
        验证数据一致性：对比API总记录数与数据库写入数

        Args:
            module_name: 模块名称
            api_total: API返回的总记录数

        Returns:
            Dict: 一致性检查结果
        """
        try:
            # 获取数据库中的记录数
            db_total = await self.data_write_manager.count_records(module_name)

            # 计算差异
            difference = abs(api_total - db_total)
            consistency_rate = (
                (min(api_total, db_total) / max(api_total, db_total)) * 100
                if max(api_total, db_total) > 0
                else 100
            )

            result = {
                "consistent": difference == 0,
                "api_total": api_total,
                "db_total": db_total,
                "difference": difference,
                "consistency_rate": consistency_rate,
                "status": "✅ 数据一致" if difference == 0 else "⚠️ 数据不一致",
            }

            if difference > 0:
                logger.warning(
                    f"⚠️ 数据一致性检查失败",
                    module_name=module_name,
                    api_total=api_total,
                    db_total=db_total,
                    difference=difference,
                    consistency_rate=f"{consistency_rate:.1f}%",
                )
            else:
                logger.info(
                    f"✅ 数据一致性检查通过",
                    module_name=module_name,
                    total_records=api_total,
                )

            return result

        except Exception:
            logger.error(
                f"❌ 数据一致性检查异常", module_name=module_name, error=str(e)
            )
            return {
                "consistent": False,
                "api_total": api_total,
                "db_total": 0,
                "difference": api_total,
                "consistency_rate": 0,
                "status": f"❌ 检查异常: {str(e)}",
            }

    def _collect_metrics(self):
        """收集监控指标"""
        self.sync_results["metrics"] = {
            "module_limits": self.module_rate_limiter.module_limits,
            "rate_limiter_metrics": {},
        }

        for module_name in self.modules:
            if module_name in self.module_rate_limiter.rate_limiters:
                limiter = self.module_rate_limiter.rate_limiters[module_name]
                self.sync_results["metrics"]["rate_limiter_metrics"][module_name] = {
                    "limiter_metrics": limiter.get_metrics(),
                    "module_metrics": self.module_rate_limiter.get_metrics(module_name),
                }

    def _generate_summary(self):
        """生成同步汇总 - 优化版本"""
        total_modules = len(self.sync_results["modules"])
        successful_modules = sum(
            1
            for result in self.sync_results["modules"].values()
            if result.get("success", False)
        )
        failed_modules = total_modules - successful_modules

        total_records = sum(
            result.get("written_records", 0)
            for result in self.sync_results["modules"].values()
        )

        success_rate = (
            (successful_modules /
             total_modules *
             100) if total_modules > 0 else 0)

        # 计算平均响应时间
        total_duration = self.sync_results["total_duration"]
        avg_duration_per_module = (
            total_duration / total_modules if total_modules > 0 else 0
        )

        self.sync_results["summary"] = {
            "total_modules": total_modules,
            "successful_modules": successful_modules,
            "failed_modules": failed_modules,
            "success_rate": f"{success_rate:.1f}%",
            "total_records": total_records,
            "avg_duration_per_module": avg_duration_per_module,
            "concurrent_modules": self.concurrent_modules,
            "batch_size": self.batch_size,
            "max_retries": self.max_retries,
        }

    def get_sync_report(self) -> Dict[str, Any]:
        """获取同步报告"""
        return self.sync_results

    async def _streaming_write_sync(
        self,
        module_name: str,
        record_limit: Optional[int] = None,
        force_recreate_table: bool = False,
    ) -> Dict[str, Any]:
        """
        流式写入同步 - 避免全量数据缓存，逐页获取并直接写入

        Args:
            module_name: 模块名称
            record_limit: 记录数限制
            force_recreate_table: 是否强制重建表

        Returns:
            Dict: 写入结果
        """
        try:
            logger.info(f"🌊 使用流式同步", module_name=module_name)

            # 获取模块特定限流器
            self.module_rate_limiter.get_limiter(module_name)

            # 分页大小自适应
            if module_name in ["material_master", "inventory_report"]:
                page_size = 1000  # 大数据量模块使用1000条/页
            else:
                page_size = 500  # 其他模块使用500条/页

            total_written = 0
            total_pages = 0
            error_pages = []

            # 表重建应该在所有写入前执行
            if force_recreate_table:
                logger.info(f"🔄 强制重建表", module_name=module_name)
                await self.data_write_manager.recreate_table(module_name)

            # 流式处理：逐页获取并直接写入
            page = 1
            while True:
                try:
                    # 获取当前页数据
                    page_response = await self.ys_client.fetch_module_data(
                        module_name=module_name,
                        limit=page_size,
                        filters={"pageIndex": page},
                    )

                    # 从响应中提取数据
                    if isinstance(
                            page_response,
                            dict) and "data" in page_response:
                        page_data = page_response.get("data", [])
                    else:
                        page_data = (
                            page_response if isinstance(
                                page_response, list) else [])

                    if not page_data or len(page_data) == 0:
                        # 无数据，说明已经是最后一页
                        logger.info(
                            f"📄 第{page}页无数据，流式同步完成",
                            module_name=module_name,
                            total_pages=total_pages,
                            total_written=total_written,
                        )
                        break

                    # 直接写入当前页数据
                    batch_result = await self._write_batch_directly(
                        module_name, page_data, False, True
                    )

                    if batch_result.get("success"):
                        written_count = batch_result.get("records_written", 0)
                        total_written += written_count
                        total_pages += 1

                        logger.info(
                            f"📄 第{page}页流式写入成功",
                            module_name=module_name,
                            written=written_count,
                            cumulative_written=total_written,
                        )
                    else:
                        logger.error(
                            f"❌ 第{page}页流式写入失败",
                            module_name=module_name,
                            error=batch_result.get("message"),
                        )
                        error_pages.append(page)

                    # 如果设置了记录限制且已达到，停止获取
                    if record_limit and total_written >= record_limit:
                        logger.info(
                            f"📄 已达到记录限制 {record_limit}，流式同步停止",
                            module_name=module_name,
                        )
                        break

                    page += 1

                    # API调用间隔（避免触发限流）
                    await asyncio.sleep(1.0)

                except Exception:
                    # 错误页跳过机制
                    logger.error(
                        f"📄 第{page}页获取失败，跳过此页",
                        module_name=module_name,
                        page=page,
                        error=str(e),
                    )
                    error_pages.append(page)
                    page += 1

                    # 如果连续错误页过多，停止获取
                    if len(error_pages) >= 5:
                        logger.error(
                            f"📄 连续错误页过多，流式同步停止",
                            module_name=module_name,
                            error_pages=error_pages,
                        )
                        break

            # 数据一致性校验
            consistency_result = await self._verify_data_consistency(
                module_name, total_written
            )

            return {
                "success": len(error_pages) == 0,
                "message": f"流式同步完成，写入 {total_written} 条记录",
                "records_processed": total_written,
                "records_written": total_written,
                "total_pages": total_pages,
                "error_pages": error_pages,
                "consistency_check": consistency_result,
            }

        except Exception:
            logger.error("流式同步执行失败", module_name=module_name, error=str(e))
            return {"success": False, "message": f"流式同步失败: {str(e)}"}


class PrefetchPageFetcher:
    """预拉取页面缓存器 - 减少等待时间"""

    def __init___(
        self,
        module_name: str,
        page_limiter: PageLevelRateLimiter,
        fetch_fn,
        page_size: int,
        ys_client=None,
    ):
    """TODO: Add function description."""
    self.module_name = module_name
     self.page_limiter = page_limiter
      self.fetch_fn = fetch_fn
       self.page_size = page_size
        self.ys_client = ys_client

        # 缓存队列：最多缓存2页数据
        self.cache = asyncio.Queue(maxsize=2)
        self.prefetch_task = None
        self.is_running = False
        self.current_page = 1

        logger.info("预拉取缓存器初始化", module_name=module_name, cache_size=2)

    async def start_prefetch(self):
        """启动预拉取任务"""
        if self.is_running:
            return

        self.is_running = True
        self.prefetch_task = asyncio.create_task(self._prefetch_worker())

        logger.info("预拉取任务已启动", module_name=self.module_name)

    async def stop_prefetch(self):
        """停止预拉取任务"""
        self.is_running = False
        if self.prefetch_task:
            self.prefetch_task.cancel()
            try:
                await self.prefetch_task
            except asyncio.CancelledError:
                pass

        logger.info("预拉取任务已停止", module_name=self.module_name)

    async def _prefetch_worker(self):
        """预拉取工作线程"""
        while self.is_running:
            try:
                # 使用页级并发控制器获取页面
                page_data = await self.page_limiter.fetch_page(
                    self.current_page,
                    self._fetch_single_page,
                    self.current_page,
                    self.page_size,
                )

                # 将数据放入缓存
                await self.cache.put((self.current_page, page_data))

                # 如果页面为空，停止预拉取
                if not page_data:
                    logger.info(
                        "预拉取遇到空页面，停止预拉取",
                        module_name=self.module_name,
                        page=self.current_page,
                    )
                    break

                self.current_page += 1

                # 避免缓存队列满时阻塞
                if self.cache.qsize() >= 2:
                    await asyncio.sleep(0.1)

            except asyncio.CancelledError:
                break
            except Exception:
                logger.error(
                    "预拉取任务异常",
                    module_name=self.module_name,
                    page=self.current_page,
                    error=str(e),
                )
                # 继续尝试下一页
                self.current_page += 1
                await asyncio.sleep(1)

    async def _fetch_single_page(
            self,
            page: int,
            page_size: int) -> List[Dict]:
        """获取单页数据"""
        try:
            # 使用传入的fetch_fn函数，传递正确的参数
            page_data = await self.fetch_fn(
                self.module_name, page, page_size, self.page_limiter.base_limiter
            )
            return page_data if page_data else []
        except Exception:
            logger.error(
                "预拉取页面获取失败",
                module_name=self.module_name,
                page=page,
                error=str(e),
            )
            return []

    async def get_next_page(self) -> Optional[Tuple[int, List[Dict]]]:
        """获取下一页数据"""
        try:
            # 设置超时，避免无限等待
            page_info = await asyncio.wait_for(self.cache.get(), timeout=30.0)
            return page_info
        except asyncio.TimeoutError:
            logger.warning("预拉取缓存超时", module_name=self.module_name)
            return None
        except Exception:
            logger.error(
                "获取预拉取数据失败", module_name=self.module_name, error=str(e)
            )
            return None

    def get_cache_status(self) -> Dict[str, Any]:
        """获取缓存状态"""
        return {
            "cache_size": self.cache.qsize(),
            "max_cache_size": self.cache.maxsize,
            "current_page": self.current_page,
            "is_running": self.is_running,
        }


class RetryPageQueue:
    """异步重试队列 - 不阻塞主流程"""

    def __init___(self, module_name: str, write_fn):
    """TODO: Add function description."""
    self.module_name = module_name
     self.write_fn = write_fn
      self.queue = asyncio.Queue()
       self.retry_task = None
        self.is_running = False
        self.retry_metrics = {
            "total_retries": 0,
            "successful_retries": 0,
            "failed_retries": 0,
            "queued_pages": 0,
        }

        logger.info("异步重试队列初始化", module_name=module_name)

    async def start_retry_worker(self):
        """启动重试工作线程"""
        if self.is_running:
            return

        self.is_running = True
        self.retry_task = asyncio.create_task(self._retry_worker())

        logger.info("重试工作线程已启动", module_name=self.module_name)

    async def stop_retry_worker(self):
        """停止重试工作线程"""
        self.is_running = False
        if self.retry_task:
            self.retry_task.cancel()
            try:
                await self.retry_task
            except asyncio.CancelledError:
                pass

        logger.info("重试工作线程已停止", module_name=self.module_name)

    async def add_retry(
        self, page: int, fetch_fn, page_size: int, retry_count: int = 0
    ):
        """添加重试任务"""
        await self.queue.put((page, fetch_fn, page_size, retry_count))
        self.retry_metrics["queued_pages"] += 1

        logger.debug(
            "添加重试任务",
            module_name=self.module_name,
            page=page,
            retry_count=retry_count,
        )

    async def _retry_worker(self):
        """重试工作线程"""
        while self.is_running:
            try:
                # 获取重试任务
                page, fetch_fn, page_size, retry_count = await self.queue.get()
                self.retry_metrics["total_retries"] += 1

                try:
                    # 指数退避等待
                    if retry_count > 0:
                        wait_time = min(2**retry_count, 60)  # 最大等待60秒
                        await asyncio.sleep(wait_time)

                    # 重试获取页面数据
                    page_data = await fetch_fn(page, page_size)

                    if page_data:
                        # 写入数据
                        await self.write_fn(page_data)
                        self.retry_metrics["successful_retries"] += 1

                        logger.info(
                            "重试成功",
                            module_name=self.module_name,
                            page=page,
                            retry_count=retry_count,
                            records_count=len(page_data),
                        )
                    else:
                        raise Exception("Empty page data")

                except Exception:
                    self.retry_metrics["failed_retries"] += 1

                    # 如果重试次数未达到上限，继续重试
                    if retry_count < 3:
                        logger.warning(
                            "重试失败，继续重试",
                            module_name=self.module_name,
                            page=page,
                            retry_count=retry_count,
                            error=str(e),
                        )
                        await self.add_retry(page, fetch_fn, page_size, retry_count + 1)
                    else:
                        logger.error(
                            "重试失败，已达最大重试次数",
                            module_name=self.module_name,
                            page=page,
                            retry_count=retry_count,
                            error=str(e),
                        )

                finally:
                    self.queue.task_done()

            except asyncio.CancelledError:
                break
            except Exception:
                logger.error(
                    "重试工作线程异常", module_name=self.module_name, error=str(e)
                )
                await asyncio.sleep(1)

    async def wait_for_completion(self):
        """等待所有重试任务完成"""
        await self.queue.join()

        logger.info(
            "重试队列任务完成",
            module_name=self.module_name,
            metrics=self.retry_metrics)

    def get_metrics(self) -> Dict[str, Any]:
        """获取重试队列指标"""
        return {
            **self.retry_metrics,
            "queue_size": self.queue.qsize(),
            "is_running": self.is_running,
        }


# 全局实例
fast_sync_service = FastSyncService()


def run_fast_sync(
    record_limit: Optional[int] = None,
    force_recreate_tables: bool = False,
    clear_existing_data: bool = True,
) -> Dict[str, Any]:
    """
    运行快速同步服务（修复版本）

    Args:
        record_limit: 记录数限制
        force_recreate_tables: 是否强制重建表

    Returns:
        Dict[str, Any]: 同步结果
    """
    try:
        # 创建事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # 运行同步任务
            result = loop.run_until_complete(
                fast_sync_service.sync_all_modules(
                    record_limit, force_recreate_tables, clear_existing_data
                )
            )
            return result
        except Exception:
            logger.error("同步任务执行失败", error=str(e))
            return {
                "success": False,
                "message": f"同步任务执行失败: {str(e)}",
                "error": str(e),
                "completed_at": datetime.now().isoformat(),
            }
        finally:
            # 确保事件循环被正确关闭
            try:
                # 取消所有待处理的任务
                pending = asyncio.all_tasks(loop)
                for task in pending:
                    task.cancel()

                # 等待所有任务完成
                if pending:
                    loop.run_until_complete(
                        asyncio.gather(*pending, return_exceptions=True)
                    )

                # 关闭事件循环
                loop.close()
            except Exception:
                logger.warning("清理事件循环时发生错误", error=str(cleanup_error))

    except Exception:
        logger.error("创建事件循环失败", error=str(e))
        return {
            "success": False,
            "message": f"创建事件循环失败: {str(e)}",
            "error": str(e),
            "completed_at": datetime.now().isoformat(),
        }
