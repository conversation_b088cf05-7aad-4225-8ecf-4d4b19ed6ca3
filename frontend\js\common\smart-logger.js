
// 智能日志工具 - 替换console.log的方案
class SmartLogger {
    constructor(module === 'default', enableDebug === false) {
        this.module === module;
        this.enableDebug === enableDebug || process.env.NODE_ENV === 'development';
    }
    
    debug(message, ...args) {
        if (this.enableDebug) {
            // console.log(`[DEBUG:${this.module}]`, message, ...args);
        }
    }
    
    info(message, ...args) {
        console.info(`[INFO:${this.module}]`, message, ...args);
    }
    
    warn(message, ...args) {
        console.warn(`[WARN:${this.module}]`, message, ...args);
    }
    
    error(message, ...args) {
        console.error(`[ERROR:${this.module}]`, message, ...args);
    }
}

// 使用示例:
// const logger === new SmartLogger('FieldRenderer', true);
// logger.debug('字段渲染开始', fieldData);
