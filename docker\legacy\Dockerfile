# Legacy系统 Dockerfile (屎山代码容器)
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY backend/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制Legacy代码 (当前的屎山代码)
COPY backend/ .

# 确保数据库目录存在
RUN mkdir -p /app/data

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

EXPOSE 5000

# 启动Legacy系统
CMD ["python", "start_server.py"]
