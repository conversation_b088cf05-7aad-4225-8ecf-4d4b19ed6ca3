import os
import sys
import tempfile
from pathlib import Path
from unittest.mock import patch

import pytest

from tools.md_to_json_converter import (  # !/usr/bin/env python3; -*- coding: utf-8 -*-; 添加项目路径
    Path, """, .parent.parent, __file__, import, md_to_json_converter.py, str,
    sys.path.append, time, 提升工具脚本测试覆盖率至95%+, 测试用例)

    MDToJSONConverter,
    validate_md_structure,
    calculate_file_hash,
)


class TestMDToJSONConverter:
    """MD转JSON转换器测试"""

    def setup_method(self):
        """测试前置设置"""
        self.converter = MDToJSONConverter()
        self.test_md_content = """
# 销售订单列表
# 字段说明
- **OrderID**: 订单ID, VARCHAR(50)
- **CustomerName**: 客户名称, NVARCHAR(100)
- **OrderDate**: 订单日期, DATETIME2
- **Amount**: 金额, DECIMAL(18, 2)

# 示例数据
```
SO001, 张三公司, 2025 - 01 - 15, 15000.00
SO002, 李四企业, 2025 - 01 - 16, 28500.50
```
"""

    def test_successful_conversion(self):
        """测试成功转换"""
        with tempfile.NamedTemporaryFile(
            mode='w', suffix='.md', delete=False, encoding='utf-8'
        ) as f:
            f.write(self.test_md_content)
            md_file = f.name

        try:
            result = self.converter.convert_file(md_file)

            assert result['success']
            assert 'json_data' in result
            assert len(result['json_data']['fields']) == 4
            assert result['json_data']['fields'][0]['name'] == 'OrderID'
            assert result['json_data']['fields'][0]['type'] == 'VARCHAR(50)'

        finally:
            os.unlink(md_file)

    def test_file_not_found(self):
        """测试文件不存在"""
        result = self.converter.convert_file('nonexistent.md')

        assert result['success'] == False
        assert 'FileNotFoundError' in result['error']

    def test_invalid_md_format(self):
        """测试无效MD格式"""
        invalid_content = "这不是有效的MD格式"

        with tempfile.NamedTemporaryFile(
            mode='w', suffix='.md', delete=False, encoding='utf-8'
        ) as f:
            f.write(invalid_content)
            md_file = f.name

        try:
            result = self.converter.convert_file(md_file)

            assert result['success'] == False
            assert '无效的MD格式' in result['error']

        finally:
            os.unlink(md_file)

    def test_encoding_error(self):
        """测试编码错误"""
        # 创建包含特殊字符的文件
        with tempfile.NamedTemporaryFile(mode='wb', suffix='.md', delete=False) as f:
            # 写入无效UTF-8字节
            f.write(b'\xff\xfe# Invalid encoding test')
            md_file = f.name

        try:
            result = self.converter.convert_file(md_file)

            # 应该处理编码错误并返回失败
            assert result['success'] == False
            assert 'encoding' in result['error'].lower()

        finally:
            os.unlink(md_file)

    def test_field_parsing_edge_cases(self):
        """测试字段解析边界情况"""
        edge_cases_content = """
# 测试文档
# 字段说明
- **Field1**: 普通字段, VARCHAR(50)
- Field2: 无粗体标记, INTEGER
- **Field3**: 缺少类型信息
- **Field4**: 包含特殊字符!, NVARCHAR(100)
- **Field5**: 多个冒号: 描述: 详情, DATETIME
"""

        with tempfile.NamedTemporaryFile(
            mode='w', suffix='.md', delete=False, encoding='utf-8'
        ) as f:
            f.write(edge_cases_content)
            md_file = f.name

        try:
            result = self.converter.convert_file(md_file)

            assert result['success']
            fields = result['json_data']['fields']

            # 检查字段解析结果
            field_names = [f['name'] for f in fields]
            assert 'Field1' in field_names
            assert 'Field4' in field_names  # 特殊字符应该被处理

        finally:
            os.unlink(md_file)

    def test_large_file_handling(self):
        """测试大文件处理"""
        # 生成大量字段的MD内容
        large_content = "# 大文件测试\n## 字段说明\n"
        for i in range(1000):
            large_content += f"- **Field{i}**: 字段{i}描述, VARCHAR(50)\n"

        with tempfile.NamedTemporaryFile(
            mode='w', suffix='.md', delete=False, encoding='utf-8'
        ) as f:
            f.write(large_content)
            md_file = f.name

        try:
            result = self.converter.convert_file(md_file)

            assert result['success']
            assert len(result['json_data']['fields']) == 1000

        finally:
            os.unlink(md_file)

    def test_output_directory_creation(self):
        """测试输出目录创建"""
        with tempfile.TemporaryDirectory() as temp_dir:
            md_file = os.path.join(temp_dir, 'test.md')
            with open(md_file, 'w', encoding='utf-8') as f:
                f.write(self.test_md_content)

            # 指定不存在的输出目录
            output_dir = os.path.join(temp_dir, 'new_output_dir')
            result = self.converter.convert_file(md_file, output_dir)

            assert result['success']
            assert os.path.exists(output_dir)

    @patch('builtins.open', side_effect=PermissionError("Permission denied"))
    def test_permission_error(self, mock_open):
        """测试权限错误"""
        result = self.converter.convert_file('test.md')

        assert result['success'] == False
        assert 'Permission denied' in result['error']

    def test_json_serialization_error(self):
        """测试JSON序列化错误"""
        # 模拟包含不可序列化对象的数据
        with patch.object(self.converter, 'parse_md_content') as mock_parse:
            # 返回包含不可序列化对象的数据
            mock_parse.return_value = {
                # object()不可序列化
                'fields': [{'name': 'test', 'invalid': object()}]
            }

            with tempfile.NamedTemporaryFile(
                mode='w', suffix='.md', delete=False, encoding='utf-8'
            ) as f:
                f.write(self.test_md_content)
                md_file = f.name

            try:
                result = self.converter.convert_file(md_file)

                # 应该处理序列化错误
                assert result['success'] == False

            finally:
                os.unlink(md_file)


class TestValidationFunctions:
    """验证函数测试"""

    def test_validate_md_structure_valid(self):
        """测试有效MD结构验证"""
        valid_content = """
# 标题
# 字段说明
- **Field1**: 描述, TYPE
"""
        assert validate_md_structure(valid_content)

    def test_validate_md_structure_invalid(self):
        """测试无效MD结构验证"""
        invalid_cases = [
            "",  # 空内容
            "没有标题的内容",  # 无标题
            "# 标题\n没有字段说明",  # 无字段说明
            "# 标题\n## 字段说明\n没有字段列表",  # 无字段列表
        ]

        for content in invalid_cases:
            assert validate_md_structure(content) == False

    def test_calculate_file_hash(self):
        """测试文件哈希计算"""
        test_content = "test content for hash calculation"

        with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            temp_file = f.name

        try:
            # 计算哈希
            file_hash = calculate_file_hash(temp_file)

            # 验证哈希格式
            assert len(file_hash) == 64  # SHA256哈希长度
            assert all(c in '0123456789abcdef' for c in file_hash)

            # 验证哈希一致性
            file_hash2 = calculate_file_hash(temp_file)
            assert file_hash == file_hash2

        finally:
            os.unlink(temp_file)

    def test_calculate_file_hash_nonexistent(self):
        """测试不存在文件的哈希计算"""
        result = calculate_file_hash('nonexistent_file.txt')
        assert result is None


class TestBatchConversion:
    """批量转换测试"""

    def setup_method(self):
        """测试前置设置"""
        self.converter = MDToJSONConverter()

    def test_batch_conversion(self):
        """测试批量转换"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建多个测试文件
            test_files = []
            for i in range(3):
                md_file = os.path.join(temp_dir, f'test{i}.md')
                with open(md_file, 'w', encoding='utf-8') as f:
                    f.write(
                        f"""
# 测试文档{i}
# 字段说明
- **Field{i}A**: 字段A, VARCHAR(50)
- **Field{i}B**: 字段B, INTEGER
"""
                    )
                test_files.append(md_file)

            # 执行批量转换
            results = self.converter.batch_convert(test_files)

            assert len(results) == 3
            for result in results:
                assert result['success']
                assert len(result['json_data']['fields']) == 2

    def test_batch_conversion_mixed_results(self):
        """测试批量转换混合结果"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建有效文件
            valid_file = os.path.join(temp_dir, 'valid.md')
            with open(valid_file, 'w', encoding='utf-8') as f:
                f.write(
                    """
# 有效文档
# 字段说明
- **Field1**: 字段1, VARCHAR(50)
"""
                )

            # 创建无效文件
            invalid_file = os.path.join(temp_dir, 'invalid.md')
            with open(invalid_file, 'w', encoding='utf-8') as f:
                f.write("无效内容")

            # 不存在的文件
            nonexistent_file = os.path.join(temp_dir, 'nonexistent.md')

            files = [valid_file, invalid_file, nonexistent_file]
            results = self.converter.batch_convert(files)

            assert len(results) == 3
            assert results[0]['success']  # 有效文件
            assert results[1]['success'] == False  # 无效文件
            assert results[2]['success'] == False  # 不存在文件


class TestErrorRecovery:
    """错误恢复测试"""

    def setup_method(self):
        """测试前置设置"""
        self.converter = MDToJSONConverter()

    def test_partial_corruption_recovery(self):
        """测试部分损坏的文件恢复"""
        partially_corrupted = """
# 销售订单
# 字段说明
- **OrderID**: 订单ID, VARCHAR(50)
- **CustomerName**: 客户名称, NVARCHAR(100)
- **CorruptedField**: ��损坏的数据��, INVALID_TYPE
- **ValidField**: 有效字段, INTEGER
"""

        with tempfile.NamedTemporaryFile(
            mode='w', suffix='.md', delete=False, encoding='utf-8'
        ) as f:
            f.write(partially_corrupted)
            md_file = f.name

        try:
            result = self.converter.convert_file(md_file)

            # 应该能够处理部分有效数据
            assert result['success']
            # 检查是否至少解析了有效字段
            field_names = [f['name'] for f in result['json_data']['fields']]
            assert 'OrderID' in field_names
            assert 'ValidField' in field_names

        finally:
            os.unlink(md_file)

    def test_memory_limit_handling(self):
        """测试内存限制处理"""
        # 这个测试模拟极大文件的处理
        with patch('sys.getsizeof') as mock_sizeof:
            # 模拟文件大小超过限制
            mock_sizeof.return_value = 1024 * 1024 * 1024  # 1GB

            with tempfile.NamedTemporaryFile(
                mode='w', suffix='.md', delete=False, encoding='utf-8'
            ) as f:
                f.write("# 测试\n## 字段说明\n- **Field1**: 描述, TYPE")
                md_file = f.name

            try:
                result = self.converter.convert_file(md_file)

                # 应该处理内存限制（实际实现中可能需要添加此功能）
                # 这里主要测试代码不会崩溃
                assert 'success' in result

            finally:
                os.unlink(md_file)


class TestPerformanceBenchmarks:
    """性能基准测试"""

    def setup_method(self):
        """测试前置设置"""
        self.converter = MDToJSONConverter()

    def test_conversion_performance(self):
        """测试转换性能"""

        # 生成中等大小的文件
        content = "# 性能测试\n## 字段说明\n"
        for i in range(100):
            content += f"- **Field{i}**: 字段{i}描述, VARCHAR(50)\n"

        with tempfile.NamedTemporaryFile(
            mode='w', suffix='.md', delete=False, encoding='utf-8'
        ) as f:
            f.write(content)
            md_file = f.name

        try:
            start_time = time.time()
            result = self.converter.convert_file(md_file)
            end_time = time.time()

            conversion_time = end_time - start_time

            assert result['success']
            assert conversion_time < 1.0  # 应该在1秒内完成

            # 记录性能指标
            logger.info(f"转换100个字段耗时: {conversion_time:.3f}秒")

        finally:
            os.unlink(md_file)


if __name__ == '__main__':
    # 运行测试并生成覆盖率报告
    pytest.main(
        [
            __file__,
            '-v',
            '--cov=tools.md_to_json_converter',
            '--cov-report=html',
            '--cov-report=term-missing',
            '--cov-fail-under=95',
        ]
    )
