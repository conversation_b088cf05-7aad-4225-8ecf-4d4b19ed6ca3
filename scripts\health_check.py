import json
import subprocess
import sys
from datetime import datetime
from pathlib import Path

#!/usr/bin/env python3
"""
系统健康检查工具
检查屎山绞杀系统的整体健康状况
"""


def check_file_structure():
    """检查文件结构"""
    print("🔍 检查文件结构...")

    required_dirs =
    ["scripts",
     "new-system/modules",
     "graveyard",
     "reports",
     "tasks"
     ]

    missing_dirs = []

    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)

    if missing_dirs:
        print(f"❌ 缺少目录: {', '.join(missing_dirs)}")
        return False
    else:
        print("✅ 文件结构完整")
        return True


def check_modules_status():
    """检查模块状态"""
    print("🔍 检查模块状态...")

    try:
        result = subprocess.run(
            [sys.executable, "scripts/module_tracker_simple.py", "--report"],
            capture_output=True,
            text=True,
            timeout=30,
        )

        if result.returncode == 0:
            # 解析输出中的JSON
            output_lines = result.stdout.strip().split("\n")
            json_line = None

            for line in output_lines:
                if line.startswith("{"):
                    json_line = line
                    break

            if json_line:
                status = json.loads(json_line)
                progress = status.get("总体进度", {})
                completion = progress.get("完成百分比", 0)

                print(f"✅ 模块状态正常，完成度: {completion}%")
                return completion == 100.0
            else:
                print("⚠️ 无法解析模块状态")
                return False
        else:
            print(f"❌ 模块状态检查失败: {result.stderr}")
            return False

    except Exception:
        print(f"❌ 模块状态检查异常: {e}")
        return False


def check_scripts():
    """检查关键脚本"""
    print("🔍 检查关键脚本...")

    key_scripts = [
        "scripts/module_tracker_simple.py",
        "scripts/database_dual_writer_simple.py",
        "scripts/graveyard_safety_analyzer.py",
    ]

    missing_scripts = []

    for script in key_scripts:
        if not Path(script).exists():
            missing_scripts.append(script)

    if missing_scripts:
        print(f"❌ 缺少脚本: {', '.join(missing_scripts)}")
        return False
    else:
        print("✅ 关键脚本齐全")
        return True


def check_docker_services():
    """检查Docker服务"""
    print("🔍 检查Docker服务...")

    try:
        # 检查Docker是否运行
        result = subprocess.run(
            ["docker", "--version"], capture_output=True, text=True, timeout=10
        )

        if result.returncode != 0:
            print("❌ Docker未安装或未启动")
            return False

        # 检查docker-compose文件
        if not Path("docker-compose.strangler.yml").exists():
            print("⚠️ docker-compose.strangler.yml不存在")
            return False

        print("✅ Docker环境就绪")
        return True

    except Exception:
        print(f"❌ Docker检查失败: {e}")
        return False


def run_data_consistency_check():
    """运行数据一致性检查"""
    print("🔍 运行数据一致性检查...")

    try:
        result = subprocess.run(
            [sys.executable, "scripts/database_dual_writer_simple.py", "--check"],
            capture_output=True,
            text=True,
            timeout=60,
        )

        if result.returncode == 0:
            print("✅ 数据一致性检查通过")
            return True
        else:
            print(f"❌ 数据一致性检查失败: {result.stderr}")
            return False

    except Exception:
        print(f"❌ 数据一致性检查异常: {e}")
        return False


def main():
    """主函数"""
    print("🏥 屎山绞杀系统健康检查")
    print("=" * 50)

    checks = [
        ("文件结构", check_file_structure),
        ("关键脚本", check_scripts),
        ("模块状态", check_modules_status),
        ("Docker环境", check_docker_services),
        ("数据一致性", run_data_consistency_check),
    ]

    results = []
    passed = 0

    for check_name, check_func in checks:
        print(f"\n📋 {check_name}检查:")
        try:
            success = check_func()
            results.append(
                {"check": check_name, "status": "✅" if success else "❌"})
            if success:
                passed += 1
        except Exception:
            print(f"❌ {check_name}检查异常: {e}")
            results.append(
                {"check": check_name, "status": "❌", "error": str(e)})

    # 生成健康报告
    health_report = {
        "timestamp": datetime.now().isoformat(),
        "total_checks": len(checks),
        "passed_checks": passed,
        "health_score": passed / len(checks) * 100,
        "results": results,
    }

    # 保存报告
    report_path = Path("reports/health_check_report.json")
    report_path.parent.mkdir(exist_ok=True)

    with open(report_path, "w", encoding="utf-8") as f:
        json.dump(health_report, f, ensure_ascii=False, indent=2)

    # 显示总结
    print("\n" + "=" * 50)
    print("📊 健康检查总结:")
    print(f"  通过检查: {passed}/{len(checks)}")
    print(f"  健康评分: {health_report['health_score']:.1f}%")

    for result in results:
        status_icon = result["status"]
        check_name = result["check"]
        print(f"  {status_icon} {check_name}")

    print(f"\n📄 详细报告: {report_path}")

    if passed == len(checks):
        print("\n🎉 系统完全健康！")
        return 0
    elif passed >= len(checks) * 0.8:
        print("\n⚠️ 系统基本健康，有轻微问题")
        return 1
    else:
        print("\n❌ 系统存在严重问题，需要修复")
        return 2


if __name__ == "__main__":
    sys.exit(main())
