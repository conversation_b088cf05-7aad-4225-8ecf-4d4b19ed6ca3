{
  "success" : true,
  "code" : "200",
  "message" : "",
  "data" : {
    "fieldVersion" : "20230210",
    "appCode" : "",
    "tokenSet" : false,
    "tokenDoc" : null,
    "tenantId" : "0",
    "isvId" : "75c13acc-ae53-4694-9a6e-3ddcce26d7f3",
    "id" : "21e0f5ca26724acf810ff6323980e234",
    "name" : "委外订单列表查询",
    "apiClassifyId" : "eede93e42c8d4a2ca1623db52b47a9e1",
    "apiClassifyName" : "委外订单",
    "apiClassifyCode" : "productionorder.osm_OSMOrder_OSMOrder",
    "parentApiClassifies" : null,
    "functionId" : "",
    "openMode" : 0,
    "description" : "可以根据输入的委外组织，订单状态，单据日期等过滤参数信息，可以查询到委外订单列表的内容，包括审核日期，委外部门，产品行ID等信息。",
    "auth" : true,
    "bodyPassthrough" : false,
    "healthExam" : false,
    "healthStatus" : true,
    "responseResultPassthrough" : false,
    "contentType" : "application/json",
    "returnPassthrough" : null,
    "completeProxyUrl" : "/yonbip/mfg/subcontractorder/list",
    "connectUrl" : "/api/list",
    "sort" : 20,
    "handler" : "openapi",
    "httpRequestType" : "POST",
    "openApi" : true,
    "preset" : true,
    "productId" : "4a176d6a681a4ebdbd053262493b5dff",
    "productCode" : "mfg",
    "proxyUrl" : "/yonbip/mfg/subcontractorder/list",
    "requestParamsDemo" : "Url: /yonbip/mfg/subcontractorder/list?access_token=访问令牌  \nBody: {\n\t\"pageIndex\": 1,\n\t\"pageSize\": 10,\n\t\"code\": \"WWDD202105010001\",\n\t\"status\": \"0\",\n\t\"transTypeId\": [\n\t\t\"1866605942578527\"\n\t],\n\t\"orgId\": [\n\t\t\"1866605942198782\"\n\t],\n\t\"subcontractVendorId\": [\n\t\t\"1866605942197864\"\n\t],\n\t\"OrderProduct!materialId\": [\n\t\t1866605942197885\n\t],\n\t\"OrderProduct!productId\": [\n\t\t1866605942115973\n\t],\n\t\"OrderProductSubcontract!deliveryDate\": \"2021-03-02|2021-03-02 23:59:59\",\n\t\"vouchdate\": \"2021-03-02|2021-03-02 23:59:59\",\n\t\"isShowMaterial\": false,\n\t\"simple\": {\n\t\t\"open_pubts_begin\": \"2022-04-01 00:00:00\",\n\t\t\"open_pubts_end\": \"2022-04-20 00:00:00\",\n\t\t\"orderProduct.wbs\": \"1866605942198447\",\n\t\t\"orderProduct.projectId\": \"1866605942178545\",\n\t\t\"orderProduct.activity\": 1866605427458631\n\t}\n}",
    "requestProtocol" : "HTTP",
    "serviceHttpMethod" : "POST",
    "publishStatus" : true,
    "approvalMsg" : null,
    "rpcAppName" : "",
    "rpcServiceName" : "",
    "rpcMethodName" : "",
    "rpcServiceUrl" : "",
    "ma" : false,
    "gmtCreate" : "2021-08-17 11:20:14.000",
    "gmtUpdate" : "2025-07-17 11:37:45.635",
    "address" : "https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/mfg/subcontractorder/list",
    "productName" : "生产制造",
    "productClassifyId" : "yonsuite",
    "productClassifyCode" : "yonbip",
    "productClassifyName" : "用友YonBIP",
    "paramDTOS" : [ {
      "id" : "2315188935444660237",
      "name" : "pageIndex",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "页号 默认值:1",
      "paramType" : "int",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : "1",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 0,
      "bizType" : null,
      "baseType" : true,
      "defaultValue" : "1",
      "required" : true,
      "visible" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "entityId" : null,
      "entityCode" : null,
      "apiName" : null,
      "maxLength" : "0",
      "childId" : null,
      "edit" : false,
      "regularRule" : "",
      "mapName" : null,
      "mapRequestParamType" : null,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : "1800201964255969283",
      "format" : null,
      "decimals" : null,
      "paramTag" : "[pageIndex]",
      "rowLimit" : null,
      "enableMulti" : false,
      "extend" : false
    }, {
      "id" : "2315188935444660238",
      "name" : "pageSize",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "每页行数    默认值:10",
      "paramType" : "int",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : "10",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 1,
      "bizType" : null,
      "baseType" : true,
      "defaultValue" : "10",
      "required" : true,
      "visible" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "entityId" : null,
      "entityCode" : null,
      "apiName" : null,
      "maxLength" : "0",
      "childId" : null,
      "edit" : false,
      "regularRule" : "",
      "mapName" : null,
      "mapRequestParamType" : null,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : "1800201964255969283",
      "format" : null,
      "decimals" : null,
      "paramTag" : "[pageSize]",
      "rowLimit" : null,
      "enableMulti" : false,
      "extend" : false
    }, {
      "id" : "2315188935444660239",
      "name" : "code",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "委外订单号",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : "WWDD202105010001",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 2,
      "bizType" : null,
      "baseType" : true,
      "defaultValue" : "",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "entityId" : null,
      "entityCode" : null,
      "apiName" : null,
      "maxLength" : "",
      "childId" : null,
      "edit" : false,
      "regularRule" : "",
      "mapName" : null,
      "mapRequestParamType" : null,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : "1800201964255969283",
      "format" : null,
      "decimals" : null,
      "paramTag" : null,
      "rowLimit" : null,
      "enableMulti" : false,
      "extend" : false
    }, {
      "id" : "2315188935444660240",
      "name" : "status",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : "0",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 3,
      "bizType" : null,
      "baseType" : true,
      "defaultValue" : "",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "entityId" : null,
      "entityCode" : null,
      "apiName" : null,
      "maxLength" : "0",
      "childId" : null,
      "edit" : false,
      "regularRule" : "",
      "mapName" : null,
      "mapRequestParamType" : null,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : "1800201964255969283",
      "format" : null,
      "decimals" : null,
      "paramTag" : null,
      "rowLimit" : null,
      "enableMulti" : false,
      "extend" : false
    }, {
      "id" : "2315188935444660241",
      "name" : "transTypeId",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : true,
      "paramDesc" : "交易类型",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : "[\"1866605942578527\"]",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 4,
      "bizType" : null,
      "baseType" : true,
      "defaultValue" : "",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "entityId" : null,
      "entityCode" : null,
      "apiName" : null,
      "maxLength" : "0",
      "childId" : null,
      "edit" : false,
      "regularRule" : "",
      "mapName" : null,
      "mapRequestParamType" : null,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : "1800201964255969283",
      "format" : null,
      "decimals" : null,
      "paramTag" : null,
      "rowLimit" : null,
      "enableMulti" : false,
      "extend" : false
    }, {
      "id" : "2315188935444660242",
      "name" : "orgId",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : true,
      "paramDesc" : "组织",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : "[\"1866605942198782\"]",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 5,
      "bizType" : null,
      "baseType" : true,
      "defaultValue" : "",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "entityId" : null,
      "entityCode" : null,
      "apiName" : null,
      "maxLength" : "0",
      "childId" : null,
      "edit" : false,
      "regularRule" : "",
      "mapName" : null,
      "mapRequestParamType" : null,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : "1800201964255969283",
      "format" : null,
      "decimals" : null,
      "paramTag" : null,
      "rowLimit" : null,
      "enableMulti" : false,
      "extend" : false
    }, {
      "id" : "2315188935444660243",
      "name" : "subcontractVendorId",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : true,
      "paramDesc" : "委外商",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : "[\"1866605942197864\"]",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 6,
      "bizType" : null,
      "baseType" : true,
      "defaultValue" : "",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "entityId" : null,
      "entityCode" : null,
      "apiName" : null,
      "maxLength" : "0",
      "childId" : null,
      "edit" : false,
      "regularRule" : "",
      "mapName" : null,
      "mapRequestParamType" : null,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : "1800201964255969283",
      "format" : null,
      "decimals" : null,
      "paramTag" : null,
      "rowLimit" : null,
      "enableMulti" : false,
      "extend" : false
    }, {
      "id" : "2315188935444660245",
      "name" : "OrderProduct!productId",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : true,
      "paramDesc" : "物料id",
      "paramType" : "long",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : "[1866605942115973]",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 8,
      "bizType" : null,
      "baseType" : true,
      "defaultValue" : "",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "entityId" : null,
      "entityCode" : null,
      "apiName" : null,
      "maxLength" : "",
      "childId" : null,
      "edit" : false,
      "regularRule" : "",
      "mapName" : null,
      "mapRequestParamType" : null,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : "1800201964255969283",
      "format" : null,
      "decimals" : null,
      "paramTag" : null,
      "rowLimit" : null,
      "enableMulti" : false,
      "extend" : false
    }, {
      "id" : "2315188935444660246",
      "name" : "OrderProductSubcontract!deliveryDate",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "交货日期（区间，格式2021-03-02|2021-03-02 23:59:59）",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : "2021-03-02|2021-03-02 23:59:59",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 9,
      "bizType" : null,
      "baseType" : true,
      "defaultValue" : "",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "entityId" : null,
      "entityCode" : null,
      "apiName" : null,
      "maxLength" : "0",
      "childId" : null,
      "edit" : false,
      "regularRule" : "",
      "mapName" : null,
      "mapRequestParamType" : null,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : "1800201964255969283",
      "format" : null,
      "decimals" : null,
      "paramTag" : null,
      "rowLimit" : null,
      "enableMulti" : false,
      "extend" : false
    }, {
      "id" : "2315188935444660247",
      "name" : "vouchdate",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : "2021-03-02|2021-03-02 23:59:59",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 10,
      "bizType" : null,
      "baseType" : true,
      "defaultValue" : "",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "entityId" : null,
      "entityCode" : null,
      "apiName" : null,
      "maxLength" : "",
      "childId" : null,
      "edit" : false,
      "regularRule" : "",
      "mapName" : null,
      "mapRequestParamType" : null,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : "1800201964255969283",
      "format" : null,
      "decimals" : null,
      "paramTag" : null,
      "rowLimit" : null,
      "enableMulti" : false,
      "extend" : false
    }, {
      "id" : "2315188935444660248",
      "name" : "isShowMaterial",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "是否展示材料:true-是,false-否",
      "paramType" : "boolean",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : "false",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 11,
      "bizType" : null,
      "baseType" : true,
      "defaultValue" : "false",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "entityId" : null,
      "entityCode" : null,
      "apiName" : null,
      "maxLength" : "",
      "childId" : null,
      "edit" : false,
      "regularRule" : "",
      "mapName" : null,
      "mapRequestParamType" : null,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : "1800201964255969283",
      "format" : null,
      "decimals" : null,
      "paramTag" : null,
      "rowLimit" : null,
      "enableMulti" : false,
      "extend" : false
    }, {
      "id" : "2315188935444660249",
      "name" : "simple",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : [ {
        "id" : "2315188935444660250",
        "name" : "open_pubts_begin",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660249",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "时间戳，开始时间",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : "2022-04-01 00:00:00",
        "fullName" : "",
        "ytenantId" : "0",
        "paramOrder" : 0,
        "bizType" : null,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2024-05-31 14:45:31.000",
        "gmtUpdate" : "2024-05-31 14:45:31.000",
        "entityId" : null,
        "entityCode" : null,
        "apiName" : null,
        "maxLength" : "",
        "childId" : null,
        "edit" : false,
        "regularRule" : "",
        "mapName" : null,
        "mapRequestParamType" : null,
        "refType" : false,
        "refTypeContext" : "",
        "defineHidden" : 0,
        "integrateObjectId" : "1800201964255969283",
        "format" : null,
        "decimals" : null,
        "paramTag" : "[lastUpdateTime]",
        "rowLimit" : null,
        "enableMulti" : false,
        "extend" : false
      }, {
        "id" : "2315188935444660251",
        "name" : "open_pubts_end",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660249",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "时间戳，结束时间",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : "2022-04-20 00:00:00",
        "fullName" : "",
        "ytenantId" : "0",
        "paramOrder" : 1,
        "bizType" : null,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2024-05-31 14:45:31.000",
        "gmtUpdate" : "2024-05-31 14:45:31.000",
        "entityId" : null,
        "entityCode" : null,
        "apiName" : null,
        "maxLength" : "",
        "childId" : null,
        "edit" : false,
        "regularRule" : "",
        "mapName" : null,
        "mapRequestParamType" : null,
        "refType" : false,
        "refTypeContext" : "",
        "defineHidden" : 0,
        "integrateObjectId" : "1800201964255969283",
        "format" : null,
        "decimals" : null,
        "paramTag" : "[thisSyncTime]",
        "rowLimit" : null,
        "enableMulti" : false,
        "extend" : false
      }, {
        "id" : "2315188935444660252",
        "name" : "orderProduct.wbs",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660249",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "wbs",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : "1866605942198447",
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 2,
        "bizType" : null,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2024-05-31 14:45:31.000",
        "gmtUpdate" : "2024-05-31 14:45:31.000",
        "entityId" : null,
        "entityCode" : null,
        "apiName" : null,
        "maxLength" : null,
        "childId" : null,
        "edit" : false,
        "regularRule" : "",
        "mapName" : null,
        "mapRequestParamType" : null,
        "refType" : false,
        "refTypeContext" : null,
        "defineHidden" : 0,
        "integrateObjectId" : "1800201964255969283",
        "format" : null,
        "decimals" : null,
        "paramTag" : null,
        "rowLimit" : null,
        "enableMulti" : false,
        "extend" : false
      }, {
        "id" : "2315188935444660253",
        "name" : "orderProduct.projectId",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660249",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "项目Id",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : "1866605942178545",
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 3,
        "bizType" : null,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2024-05-31 14:45:31.000",
        "gmtUpdate" : "2024-05-31 14:45:31.000",
        "entityId" : null,
        "entityCode" : null,
        "apiName" : null,
        "maxLength" : null,
        "childId" : null,
        "edit" : false,
        "regularRule" : "",
        "mapName" : null,
        "mapRequestParamType" : null,
        "refType" : false,
        "refTypeContext" : null,
        "defineHidden" : 0,
        "integrateObjectId" : "1800201964255969283",
        "format" : null,
        "decimals" : null,
        "paramTag" : null,
        "rowLimit" : null,
        "enableMulti" : false,
        "extend" : false
      }, {
        "id" : "2315188935444660254",
        "name" : "orderProduct.activity",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660249",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "活动id",
        "paramType" : "number",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : "1866605427458631",
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 4,
        "bizType" : null,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2024-05-31 14:45:31.000",
        "gmtUpdate" : "2024-05-31 14:45:31.000",
        "entityId" : null,
        "entityCode" : null,
        "apiName" : null,
        "maxLength" : "32",
        "childId" : null,
        "edit" : false,
        "regularRule" : "",
        "mapName" : null,
        "mapRequestParamType" : null,
        "refType" : false,
        "refTypeContext" : null,
        "defineHidden" : 0,
        "integrateObjectId" : "1800201964255969283",
        "format" : null,
        "decimals" : 0,
        "paramTag" : null,
        "rowLimit" : null,
        "enableMulti" : false,
        "extend" : false
      } ],
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "simple",
      "paramType" : "object",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : "",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 12,
      "bizType" : null,
      "baseType" : true,
      "defaultValue" : "",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2025-07-17 11:37:45.889",
      "gmtUpdate" : "2025-07-17 11:37:45.889",
      "entityId" : null,
      "entityCode" : null,
      "apiName" : null,
      "maxLength" : "",
      "childId" : null,
      "edit" : false,
      "regularRule" : "",
      "mapName" : null,
      "mapRequestParamType" : null,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : "1800201964255969283",
      "format" : null,
      "decimals" : null,
      "paramTag" : null,
      "rowLimit" : null,
      "enableMulti" : false,
      "extend" : false
    }, {
      "id" : "2315188935444660255",
      "name" : "simpleVOs",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : [ {
        "id" : "2315188935444660256",
        "name" : "field",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660255",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 示例：pubts",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : "",
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 0,
        "bizType" : null,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "entityId" : null,
        "entityCode" : null,
        "apiName" : null,
        "maxLength" : null,
        "childId" : null,
        "edit" : false,
        "regularRule" : "",
        "mapName" : null,
        "mapRequestParamType" : null,
        "refType" : false,
        "refTypeContext" : null,
        "defineHidden" : 0,
        "integrateObjectId" : "1800201964255969283",
        "format" : null,
        "decimals" : null,
        "paramTag" : null,
        "rowLimit" : null,
        "enableMulti" : false,
        "extend" : false
      }, {
        "id" : "2315188935444660257",
        "name" : "op",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660255",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : "",
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 1,
        "bizType" : null,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "entityId" : null,
        "entityCode" : null,
        "apiName" : null,
        "maxLength" : null,
        "childId" : null,
        "edit" : false,
        "regularRule" : "",
        "mapName" : null,
        "mapRequestParamType" : null,
        "refType" : false,
        "refTypeContext" : null,
        "defineHidden" : 0,
        "integrateObjectId" : "1800201964255969283",
        "format" : null,
        "decimals" : null,
        "paramTag" : null,
        "rowLimit" : null,
        "enableMulti" : false,
        "extend" : false
      }, {
        "id" : "2315188935444660258",
        "name" : "value1",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660255",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : "",
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 2,
        "bizType" : null,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "entityId" : null,
        "entityCode" : null,
        "apiName" : null,
        "maxLength" : null,
        "childId" : null,
        "edit" : false,
        "regularRule" : "",
        "mapName" : null,
        "mapRequestParamType" : null,
        "refType" : false,
        "refTypeContext" : null,
        "defineHidden" : 0,
        "integrateObjectId" : "1800201964255969283",
        "format" : null,
        "decimals" : null,
        "paramTag" : null,
        "rowLimit" : null,
        "enableMulti" : false,
        "extend" : false
      }, {
        "id" : "2315188935444660259",
        "name" : "value2",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660255",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "查询条件值2",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : "",
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 3,
        "bizType" : null,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "entityId" : null,
        "entityCode" : null,
        "apiName" : null,
        "maxLength" : null,
        "childId" : null,
        "edit" : false,
        "regularRule" : "",
        "mapName" : null,
        "mapRequestParamType" : null,
        "refType" : false,
        "refTypeContext" : null,
        "defineHidden" : 0,
        "integrateObjectId" : "1800201964255969283",
        "format" : null,
        "decimals" : null,
        "paramTag" : null,
        "rowLimit" : null,
        "enableMulti" : false,
        "extend" : false
      }, {
        "id" : "2315188935444660260",
        "name" : "logicOp",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660255",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : "",
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 4,
        "bizType" : null,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "entityId" : null,
        "entityCode" : null,
        "apiName" : null,
        "maxLength" : null,
        "childId" : null,
        "edit" : false,
        "regularRule" : "",
        "mapName" : null,
        "mapRequestParamType" : null,
        "refType" : false,
        "refTypeContext" : null,
        "defineHidden" : 0,
        "integrateObjectId" : "1800201964255969283",
        "format" : null,
        "decimals" : null,
        "paramTag" : null,
        "rowLimit" : null,
        "enableMulti" : false,
        "extend" : false
      }, {
        "id" : "2315188935444660261",
        "name" : "conditions",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660255",
        "children" : [ {
          "id" : "2315188935444660262",
          "name" : "field",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660261",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )",
          "paramType" : "string",
          "requestParamType" : "BodyParam",
          "path" : null,
          "example" : "",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 0,
          "bizType" : null,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : null,
          "gmtUpdate" : null,
          "entityId" : null,
          "entityCode" : null,
          "apiName" : null,
          "maxLength" : null,
          "childId" : null,
          "edit" : false,
          "regularRule" : "",
          "mapName" : null,
          "mapRequestParamType" : null,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : "1800201964255969283",
          "format" : null,
          "decimals" : null,
          "paramTag" : null,
          "rowLimit" : null,
          "enableMulti" : false,
          "extend" : false
        }, {
          "id" : "2315188935444660263",
          "name" : "op",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660261",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or",
          "paramType" : "string",
          "requestParamType" : "BodyParam",
          "path" : null,
          "example" : "",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 1,
          "bizType" : null,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : null,
          "gmtUpdate" : null,
          "entityId" : null,
          "entityCode" : null,
          "apiName" : null,
          "maxLength" : null,
          "childId" : null,
          "edit" : false,
          "regularRule" : "",
          "mapName" : null,
          "mapRequestParamType" : null,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : "1800201964255969283",
          "format" : null,
          "decimals" : null,
          "paramTag" : null,
          "rowLimit" : null,
          "enableMulti" : false,
          "extend" : false
        }, {
          "id" : "2315188935444660264",
          "name" : "value1",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660261",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)",
          "paramType" : "string",
          "requestParamType" : "BodyParam",
          "path" : null,
          "example" : "",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 2,
          "bizType" : null,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : null,
          "gmtUpdate" : null,
          "entityId" : null,
          "entityCode" : null,
          "apiName" : null,
          "maxLength" : null,
          "childId" : null,
          "edit" : false,
          "regularRule" : "",
          "mapName" : null,
          "mapRequestParamType" : null,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : "1800201964255969283",
          "format" : null,
          "decimals" : null,
          "paramTag" : null,
          "rowLimit" : null,
          "enableMulti" : false,
          "extend" : false
        }, {
          "id" : "2315188935444660265",
          "name" : "value2",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660261",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "查询条件值2",
          "paramType" : "string",
          "requestParamType" : "BodyParam",
          "path" : null,
          "example" : "",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 3,
          "bizType" : null,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : null,
          "gmtUpdate" : null,
          "entityId" : null,
          "entityCode" : null,
          "apiName" : null,
          "maxLength" : null,
          "childId" : null,
          "edit" : false,
          "regularRule" : "",
          "mapName" : null,
          "mapRequestParamType" : null,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : "1800201964255969283",
          "format" : null,
          "decimals" : null,
          "paramTag" : null,
          "rowLimit" : null,
          "enableMulti" : false,
          "extend" : false
        } ],
        "defParamId" : null,
        "array" : true,
        "paramDesc" : "下级查询条件",
        "paramType" : "object",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : "",
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 5,
        "bizType" : null,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2025-07-17 11:37:45.919",
        "gmtUpdate" : "2025-07-17 11:37:45.919",
        "entityId" : null,
        "entityCode" : null,
        "apiName" : null,
        "maxLength" : null,
        "childId" : null,
        "edit" : false,
        "regularRule" : "",
        "mapName" : null,
        "mapRequestParamType" : null,
        "refType" : false,
        "refTypeContext" : null,
        "defineHidden" : 0,
        "integrateObjectId" : "1800201964255969283",
        "format" : null,
        "decimals" : null,
        "paramTag" : null,
        "rowLimit" : null,
        "enableMulti" : false,
        "extend" : false
      } ],
      "defParamId" : null,
      "array" : true,
      "paramDesc" : "扩展查询条件",
      "paramType" : "object",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : "",
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 13,
      "bizType" : null,
      "baseType" : true,
      "defaultValue" : "",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2025-07-17 11:37:45.912",
      "gmtUpdate" : "2025-07-17 11:37:45.912",
      "entityId" : null,
      "entityCode" : null,
      "apiName" : null,
      "maxLength" : null,
      "childId" : null,
      "edit" : false,
      "regularRule" : "",
      "mapName" : null,
      "mapRequestParamType" : null,
      "refType" : false,
      "refTypeContext" : null,
      "defineHidden" : 0,
      "integrateObjectId" : "1800201964255969283",
      "format" : null,
      "decimals" : null,
      "paramTag" : null,
      "rowLimit" : null,
      "enableMulti" : false,
      "extend" : false
    } ],
    "queryParamDTOS" : [ ],
    "ysApi" : false,
    "presetTokenApi" : false,
    "applyFlag" : false,
    "cover" : false,
    "ysParams" : null,
    "paramMapDTOS" : [ {
      "id" : "2315188935444660408",
      "name" : "pageIndex",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "页号 默认值:1",
      "paramType" : "int",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 0,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "pageIndex",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "int",
      "gmtCreate" : null,
      "gmtUpdate" : null,
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    }, {
      "id" : "2315188935444660409",
      "name" : "pageSize",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "每页行数    默认值:10",
      "paramType" : "int",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 1,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "pageSize",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "int",
      "gmtCreate" : null,
      "gmtUpdate" : null,
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    }, {
      "id" : "2315188935444660410",
      "name" : "code",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "委外订单号",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 2,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "code",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "string",
      "gmtCreate" : null,
      "gmtUpdate" : null,
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    }, {
      "id" : "2315188935444660411",
      "name" : "status",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 3,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "status",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "string",
      "gmtCreate" : null,
      "gmtUpdate" : null,
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    }, {
      "id" : "2315188935444660412",
      "name" : "transTypeId",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "交易类型",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 4,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "transTypeId",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "string",
      "gmtCreate" : null,
      "gmtUpdate" : null,
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    }, {
      "id" : "2315188935444660413",
      "name" : "orgId",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "组织",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 5,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "orgId",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "string",
      "gmtCreate" : null,
      "gmtUpdate" : null,
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    }, {
      "id" : "2315188935444660414",
      "name" : "subcontractVendorId",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "委外商",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 6,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "subcontractVendorId",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "string",
      "gmtCreate" : null,
      "gmtUpdate" : null,
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    }, {
      "id" : "2315188935444660415",
      "name" : "OrderProduct!materialId",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "制造物料id",
      "paramType" : "long",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 7,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "OrderProduct!materialId",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "long",
      "gmtCreate" : null,
      "gmtUpdate" : null,
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    }, {
      "id" : "2315188935444660416",
      "name" : "OrderProduct!productId",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "物料id",
      "paramType" : "long",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 8,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "OrderProduct!productId",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "long",
      "gmtCreate" : null,
      "gmtUpdate" : null,
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    }, {
      "id" : "2315188935444660417",
      "name" : "OrderProductSubcontract!deliveryDate",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "交货日期（区间，格式2021-03-02|2021-03-02 23:59:59）",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 9,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "OrderProductSubcontract!deliveryDate",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "string",
      "gmtCreate" : null,
      "gmtUpdate" : null,
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    }, {
      "id" : "2315188935444660418",
      "name" : "vouchdate",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）",
      "paramType" : "string",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 10,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "vouchdate",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "string",
      "gmtCreate" : null,
      "gmtUpdate" : null,
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    }, {
      "id" : "2315188935444660419",
      "name" : "isShowMaterial",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "是否展示材料:true-是,false-否",
      "paramType" : "boolean",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 11,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "isShowMaterial",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "boolean",
      "gmtCreate" : null,
      "gmtUpdate" : null,
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    }, {
      "id" : "2315188935444660420",
      "name" : "simple",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : [ {
        "id" : "2315188935444660421",
        "name" : "open_pubts_begin",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660420",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "时间戳，开始时间",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : null,
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 0,
        "baseType" : true,
        "aggregatedValueObject" : false,
        "mapName" : "open_pubts_begin",
        "mapRequestParamType" : "BodyParam",
        "paramList" : null,
        "primitive" : false,
        "serviceParamType" : "string",
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "apiName" : null,
        "entityId" : null,
        "entityDTO" : null,
        "childId" : null,
        "edit" : false,
        "defaultValue" : null,
        "enableMulti" : false
      }, {
        "id" : "2315188935444660422",
        "name" : "open_pubts_end",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660420",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "时间戳，结束时间",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : null,
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 1,
        "baseType" : true,
        "aggregatedValueObject" : false,
        "mapName" : "open_pubts_end",
        "mapRequestParamType" : "BodyParam",
        "paramList" : null,
        "primitive" : false,
        "serviceParamType" : "string",
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "apiName" : null,
        "entityId" : null,
        "entityDTO" : null,
        "childId" : null,
        "edit" : false,
        "defaultValue" : null,
        "enableMulti" : false
      }, {
        "id" : "2315188935444660423",
        "name" : "orderProduct.wbs",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660420",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "wbs",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : null,
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 2,
        "baseType" : true,
        "aggregatedValueObject" : false,
        "mapName" : "orderProduct.wbs",
        "mapRequestParamType" : "BodyParam",
        "paramList" : null,
        "primitive" : false,
        "serviceParamType" : "string",
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "apiName" : null,
        "entityId" : null,
        "entityDTO" : null,
        "childId" : null,
        "edit" : false,
        "defaultValue" : null,
        "enableMulti" : false
      }, {
        "id" : "2315188935444660424",
        "name" : "orderProduct.projectId",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660420",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "项目Id",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : null,
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 3,
        "baseType" : true,
        "aggregatedValueObject" : false,
        "mapName" : "orderProduct.projectId",
        "mapRequestParamType" : "BodyParam",
        "paramList" : null,
        "primitive" : false,
        "serviceParamType" : "string",
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "apiName" : null,
        "entityId" : null,
        "entityDTO" : null,
        "childId" : null,
        "edit" : false,
        "defaultValue" : null,
        "enableMulti" : false
      }, {
        "id" : "2315188935444660425",
        "name" : "orderProduct.activity",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660420",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "活动id",
        "paramType" : "number",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : null,
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 4,
        "baseType" : true,
        "aggregatedValueObject" : false,
        "mapName" : "orderProduct.activity",
        "mapRequestParamType" : "BodyParam",
        "paramList" : null,
        "primitive" : false,
        "serviceParamType" : "number",
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "apiName" : null,
        "entityId" : null,
        "entityDTO" : null,
        "childId" : null,
        "edit" : false,
        "defaultValue" : null,
        "enableMulti" : false
      } ],
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "simple",
      "paramType" : "object",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 12,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "simple",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "object",
      "gmtCreate" : "2025-07-17 11:37:45.976",
      "gmtUpdate" : "2025-07-17 11:37:45.976",
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    }, {
      "id" : "2315188935444660426",
      "name" : "simpleVOs",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : [ {
        "id" : "2315188935444660427",
        "name" : "field",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660426",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 示例：pubts",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : null,
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 0,
        "baseType" : true,
        "aggregatedValueObject" : false,
        "mapName" : "field",
        "mapRequestParamType" : "BodyParam",
        "paramList" : null,
        "primitive" : false,
        "serviceParamType" : "string",
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "apiName" : null,
        "entityId" : null,
        "entityDTO" : null,
        "childId" : null,
        "edit" : false,
        "defaultValue" : null,
        "enableMulti" : false
      }, {
        "id" : "2315188935444660428",
        "name" : "op",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660426",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : null,
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 1,
        "baseType" : true,
        "aggregatedValueObject" : false,
        "mapName" : "op",
        "mapRequestParamType" : "BodyParam",
        "paramList" : null,
        "primitive" : false,
        "serviceParamType" : "string",
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "apiName" : null,
        "entityId" : null,
        "entityDTO" : null,
        "childId" : null,
        "edit" : false,
        "defaultValue" : null,
        "enableMulti" : false
      }, {
        "id" : "2315188935444660429",
        "name" : "value1",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660426",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : null,
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 2,
        "baseType" : true,
        "aggregatedValueObject" : false,
        "mapName" : "value1",
        "mapRequestParamType" : "BodyParam",
        "paramList" : null,
        "primitive" : false,
        "serviceParamType" : "string",
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "apiName" : null,
        "entityId" : null,
        "entityDTO" : null,
        "childId" : null,
        "edit" : false,
        "defaultValue" : null,
        "enableMulti" : false
      }, {
        "id" : "2315188935444660430",
        "name" : "value2",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660426",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "查询条件值2",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : null,
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 3,
        "baseType" : true,
        "aggregatedValueObject" : false,
        "mapName" : "value2",
        "mapRequestParamType" : "BodyParam",
        "paramList" : null,
        "primitive" : false,
        "serviceParamType" : "string",
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "apiName" : null,
        "entityId" : null,
        "entityDTO" : null,
        "childId" : null,
        "edit" : false,
        "defaultValue" : null,
        "enableMulti" : false
      }, {
        "id" : "2315188935444660431",
        "name" : "logicOp",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660426",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or",
        "paramType" : "string",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : null,
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 4,
        "baseType" : true,
        "aggregatedValueObject" : false,
        "mapName" : "logicOp",
        "mapRequestParamType" : "BodyParam",
        "paramList" : null,
        "primitive" : false,
        "serviceParamType" : "string",
        "gmtCreate" : null,
        "gmtUpdate" : null,
        "apiName" : null,
        "entityId" : null,
        "entityDTO" : null,
        "childId" : null,
        "edit" : false,
        "defaultValue" : null,
        "enableMulti" : false
      }, {
        "id" : "2315188935444660432",
        "name" : "conditions",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660426",
        "children" : [ {
          "id" : "2315188935444660433",
          "name" : "field",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660432",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )",
          "paramType" : "string",
          "requestParamType" : "BodyParam",
          "path" : null,
          "example" : null,
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 0,
          "baseType" : true,
          "aggregatedValueObject" : false,
          "mapName" : "field",
          "mapRequestParamType" : "BodyParam",
          "paramList" : null,
          "primitive" : false,
          "serviceParamType" : "string",
          "gmtCreate" : null,
          "gmtUpdate" : null,
          "apiName" : null,
          "entityId" : null,
          "entityDTO" : null,
          "childId" : null,
          "edit" : false,
          "defaultValue" : null,
          "enableMulti" : false
        }, {
          "id" : "2315188935444660434",
          "name" : "op",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660432",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or",
          "paramType" : "string",
          "requestParamType" : "BodyParam",
          "path" : null,
          "example" : null,
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 1,
          "baseType" : true,
          "aggregatedValueObject" : false,
          "mapName" : "op",
          "mapRequestParamType" : "BodyParam",
          "paramList" : null,
          "primitive" : false,
          "serviceParamType" : "string",
          "gmtCreate" : null,
          "gmtUpdate" : null,
          "apiName" : null,
          "entityId" : null,
          "entityDTO" : null,
          "childId" : null,
          "edit" : false,
          "defaultValue" : null,
          "enableMulti" : false
        }, {
          "id" : "2315188935444660435",
          "name" : "value1",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660432",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)",
          "paramType" : "string",
          "requestParamType" : "BodyParam",
          "path" : null,
          "example" : null,
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 2,
          "baseType" : true,
          "aggregatedValueObject" : false,
          "mapName" : "value1",
          "mapRequestParamType" : "BodyParam",
          "paramList" : null,
          "primitive" : false,
          "serviceParamType" : "string",
          "gmtCreate" : null,
          "gmtUpdate" : null,
          "apiName" : null,
          "entityId" : null,
          "entityDTO" : null,
          "childId" : null,
          "edit" : false,
          "defaultValue" : null,
          "enableMulti" : false
        }, {
          "id" : "2315188935444660436",
          "name" : "value2",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660432",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "查询条件值2",
          "paramType" : "string",
          "requestParamType" : "BodyParam",
          "path" : null,
          "example" : null,
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 3,
          "baseType" : true,
          "aggregatedValueObject" : false,
          "mapName" : "value2",
          "mapRequestParamType" : "BodyParam",
          "paramList" : null,
          "primitive" : false,
          "serviceParamType" : "string",
          "gmtCreate" : null,
          "gmtUpdate" : null,
          "apiName" : null,
          "entityId" : null,
          "entityDTO" : null,
          "childId" : null,
          "edit" : false,
          "defaultValue" : null,
          "enableMulti" : false
        } ],
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "下级查询条件",
        "paramType" : "object",
        "requestParamType" : "BodyParam",
        "path" : null,
        "example" : null,
        "fullName" : null,
        "ytenantId" : "0",
        "paramOrder" : 5,
        "baseType" : true,
        "aggregatedValueObject" : false,
        "mapName" : "conditions",
        "mapRequestParamType" : "BodyParam",
        "paramList" : null,
        "primitive" : false,
        "serviceParamType" : "object",
        "gmtCreate" : "2025-07-17 11:37:46.006",
        "gmtUpdate" : "2025-07-17 11:37:46.006",
        "apiName" : null,
        "entityId" : null,
        "entityDTO" : null,
        "childId" : null,
        "edit" : false,
        "defaultValue" : null,
        "enableMulti" : false
      } ],
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "扩展查询条件",
      "paramType" : "object",
      "requestParamType" : "BodyParam",
      "path" : null,
      "example" : null,
      "fullName" : null,
      "ytenantId" : "0",
      "paramOrder" : 13,
      "baseType" : true,
      "aggregatedValueObject" : false,
      "mapName" : "simpleVOs",
      "mapRequestParamType" : "BodyParam",
      "paramList" : null,
      "primitive" : false,
      "serviceParamType" : "object",
      "gmtCreate" : "2025-07-17 11:37:45.998",
      "gmtUpdate" : "2025-07-17 11:37:45.998",
      "apiName" : null,
      "entityId" : null,
      "entityDTO" : null,
      "childId" : null,
      "edit" : false,
      "defaultValue" : null,
      "enableMulti" : false
    } ],
    "paramReturnDTOS" : [ {
      "id" : "2315188935444660266",
      "name" : "code",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "返回码，成功时返回200",
      "paramType" : "long",
      "requestParamType" : null,
      "path" : null,
      "example" : "200",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 0,
      "baseType" : true,
      "defaultValue" : "",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "apiName" : null,
      "entityId" : null,
      "entityCode" : null,
      "edit" : false,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : null,
      "paramTag" : null,
      "format" : null,
      "decimals" : null,
      "maxLength" : "0",
      "enableMulti" : false
    }, {
      "id" : "2315188935444660267",
      "name" : "message",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : null,
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "接口返回信息",
      "paramType" : "string",
      "requestParamType" : null,
      "path" : null,
      "example" : "操作成功",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 1,
      "baseType" : true,
      "defaultValue" : "",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "apiName" : null,
      "entityId" : null,
      "entityCode" : null,
      "edit" : false,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : null,
      "paramTag" : null,
      "format" : null,
      "decimals" : null,
      "maxLength" : "0",
      "enableMulti" : false
    }, {
      "id" : "2315188935444660268",
      "name" : "data",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "parentId" : null,
      "children" : [ {
        "id" : "2315188935444660269",
        "name" : "pageIndex",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660268",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "当前页",
        "paramType" : "long",
        "requestParamType" : null,
        "path" : null,
        "example" : "1",
        "fullName" : "",
        "ytenantId" : "0",
        "paramOrder" : 0,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2024-05-31 14:45:31.000",
        "gmtUpdate" : "2024-05-31 14:45:31.000",
        "apiName" : null,
        "entityId" : null,
        "entityCode" : null,
        "edit" : false,
        "refType" : false,
        "refTypeContext" : "",
        "defineHidden" : 0,
        "integrateObjectId" : null,
        "paramTag" : null,
        "format" : null,
        "decimals" : null,
        "maxLength" : "0",
        "enableMulti" : false
      }, {
        "id" : "2315188935444660270",
        "name" : "pageSize",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660268",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "页大小",
        "paramType" : "long",
        "requestParamType" : null,
        "path" : null,
        "example" : "20",
        "fullName" : "",
        "ytenantId" : "0",
        "paramOrder" : 1,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2024-05-31 14:45:31.000",
        "gmtUpdate" : "2024-05-31 14:45:31.000",
        "apiName" : null,
        "entityId" : null,
        "entityCode" : null,
        "edit" : false,
        "refType" : false,
        "refTypeContext" : "",
        "defineHidden" : 0,
        "integrateObjectId" : null,
        "paramTag" : null,
        "format" : null,
        "decimals" : null,
        "maxLength" : "0",
        "enableMulti" : false
      }, {
        "id" : "2315188935444660271",
        "name" : "recordCount",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660268",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "记录总数",
        "paramType" : "long",
        "requestParamType" : null,
        "path" : null,
        "example" : "0",
        "fullName" : "",
        "ytenantId" : "0",
        "paramOrder" : 2,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2024-05-31 14:45:31.000",
        "gmtUpdate" : "2024-05-31 14:45:31.000",
        "apiName" : null,
        "entityId" : null,
        "entityCode" : null,
        "edit" : false,
        "refType" : false,
        "refTypeContext" : "",
        "defineHidden" : 0,
        "integrateObjectId" : null,
        "paramTag" : null,
        "format" : null,
        "decimals" : null,
        "maxLength" : "0",
        "enableMulti" : false
      }, {
        "id" : "2315188935444660272",
        "name" : "recordList",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660268",
        "children" : [ {
          "id" : "2315188935444660273",
          "name" : "id",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "委外订单Id",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "****************",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 0,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660274",
          "name" : "transTypeId",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "交易类型ID",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "****************",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 1,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660275",
          "name" : "isWfControlled",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "是否审批流控制：false-否，true-是",
          "paramType" : "boolean",
          "requestParamType" : null,
          "path" : null,
          "example" : "false",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 2,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660276",
          "name" : "orgName",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "组织",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "L工厂1",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 3,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660277",
          "name" : "status",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "0",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 4,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660278",
          "name" : "isHold",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "挂起状态：false-否，true-是",
          "paramType" : "boolean",
          "requestParamType" : null,
          "path" : null,
          "example" : "false",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 5,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660279",
          "name" : "code",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "委外订单号",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "SCDD20210324000003",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 6,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660280",
          "name" : "verifystate",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "审批状态：0-开立，1-已提交，2-已审批，-1-驳回",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "0",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 7,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660281",
          "name" : "vouchdate",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "单据日期",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "2021-03-24 00:00:00",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 8,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660282",
          "name" : "creatorId",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "创建人Id",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "****************",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 9,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660283",
          "name" : "creator",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "创建人",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "***********",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 10,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660284",
          "name" : "orgId",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "组织Id",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "****************",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 11,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660285",
          "name" : "transTypeName",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "交易类型名称",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "标准生产",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 12,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660286",
          "name" : "transTypeCode",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "交易类型编码",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "PO-001",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 13,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660287",
          "name" : "subcontractVendorId",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "委外商Id",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "****************",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 14,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660288",
          "name" : "subcontractVendorName",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "委外商",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "供应商",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 15,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660289",
          "name" : "pubts",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "时间戳",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "2021-03-24 11:40:13",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 16,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660290",
          "name" : "createTime",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "创建时间",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "2021-03-24 11:40:12",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 17,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660291",
          "name" : "orderProduct_id",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "订单产品行Id",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "2184924571914497",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 18,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660292",
          "name" : "OrderProduct_productId",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "物料Id",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "2061736079708416",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 19,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660294",
          "name" : "OrderProduct_materialName",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "物料名称",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "自行车",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 21,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660295",
          "name" : "OrderProduct_materialCode",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "制造物料编码",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "jq01000001",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 22,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660296",
          "name" : "orderProduct_orderSubcontractProduct_subcontractUnitId",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "委外单位Id",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "1869676091724032",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 23,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660297",
          "name" : "orderProduct_orderSubcontractProduct_priceUnitId",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "计价单位Id",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "1869676091724032",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 24,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660298",
          "name" : "mainUnitPrecision",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "主计量精度",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "3",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 25,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660299",
          "name" : "priceUnitPrecision",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "计价单位精度",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "3",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 26,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660300",
          "name" : "subcontractUnitPrecision",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "委外单位精度",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "4",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 27,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660301",
          "name" : "OrderProduct_sourceType",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "来源单据类型：1-无来源，2-计划订单，3-销售订单，4-生产订单，5-完工报告",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "1",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 28,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660302",
          "name" : "OrderProduct_deliveryDate",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "交货日期",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "2021-03-24 00:00:00",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 29,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660303",
          "name" : "OrderProduct_subcontractQuantityMU",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "委外数量",
          "paramType" : "double",
          "requestParamType" : null,
          "path" : null,
          "example" : "120",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 30,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660304",
          "name" : "OrderProduct_subcontractQuantitySU",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "委外件数",
          "paramType" : "double",
          "requestParamType" : null,
          "path" : null,
          "example" : "120",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 31,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660305",
          "name" : "OrderProduct_subcontractQuantityPU",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "计价数量",
          "paramType" : "double",
          "requestParamType" : null,
          "path" : null,
          "example" : "120",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 32,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660306",
          "name" : "OrderProduct_changeRate",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "换算率",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "1",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 33,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660307",
          "name" : "OrderProduct_isHold",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "挂起状态：false-否，true-是",
          "paramType" : "boolean",
          "requestParamType" : null,
          "path" : null,
          "example" : "false",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 34,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660308",
          "name" : "OrderProduct_mainUnitName",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "主计量",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "件",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 35,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660309",
          "name" : "OrderProduct_subcontractUnitName",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "委外单位",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "件",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 36,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660310",
          "name" : "OrderProduct_priceUnitName",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "计价单位",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "件",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 37,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660311",
          "name" : "OrderProduct_bomId",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "物料清单Id",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "2173985857769728",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 38,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660312",
          "name" : "OrderProduct_version",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "BOM版本",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "A1",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 39,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660313",
          "name" : "freeCharacteristics",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "自由项特征组",
          "paramType" : "characteristic",
          "requestParamType" : null,
          "path" : null,
          "example" : "",
          "fullName" : "po.order.OrderProduct",
          "ytenantId" : "0",
          "paramOrder" : 40,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660314",
          "name" : "productDefineDts",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "产品行自定义项特征组",
          "paramType" : "characteristic",
          "requestParamType" : null,
          "path" : null,
          "example" : "",
          "fullName" : "po.order.OrderProduct",
          "ytenantId" : "0",
          "paramOrder" : 41,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660315",
          "name" : "defineDts",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "自定义项特征组",
          "paramType" : "characteristic",
          "requestParamType" : null,
          "path" : null,
          "example" : "",
          "fullName" : "po.order.Order",
          "ytenantId" : "0",
          "paramOrder" : 42,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660316",
          "name" : "out_sys_id",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "外部来源线索",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "100000",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 43,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660317",
          "name" : "out_sys_code",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "外部来源编码",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "WBWWDD001",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 44,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660318",
          "name" : "out_sys_version",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "外部来源版本",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "V1.0.0",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 45,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660319",
          "name" : "out_sys_type",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "外部来源类型",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "Type001",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 46,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660320",
          "name" : "OrderProduct_out_sys_lineid",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "外部来源行",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "10010",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 47,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660321",
          "name" : "OrderProduct_out_sys_rowno",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "外部来源行号",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "10",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 48,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660322",
          "name" : "orderMaterial",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : [ {
            "id" : "2315188935444660323",
            "name" : "isWholeSet",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "齐套标识：false-否，true-是",
            "paramType" : "boolean",
            "requestParamType" : null,
            "path" : null,
            "example" : "false",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 0,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660324",
            "name" : "id",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "订单材料Id",
            "paramType" : "long",
            "requestParamType" : null,
            "path" : null,
            "example" : "订单材料Id",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 1,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660325",
            "name" : "lineNo",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "行号",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "10",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 2,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660326",
            "name" : "productCode",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "物料编码，特征租户返回",
            "paramType" : "string",
            "requestParamType" : null,
            "path" : null,
            "example" : "788044",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 3,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660327",
            "name" : "productName",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "物料名称，特征租户返回",
            "paramType" : "string",
            "requestParamType" : null,
            "path" : null,
            "example" : "成套领料-材料001",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 4,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660328",
            "name" : "productId",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "物料id",
            "paramType" : "long",
            "requestParamType" : null,
            "path" : null,
            "example" : "1696909495433691137",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 5,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660332",
            "name" : "mainUnit",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "主单位id",
            "paramType" : "long",
            "requestParamType" : null,
            "path" : null,
            "example" : "2652431423640064",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 9,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660333",
            "name" : "mainUnitName",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "主单位",
            "paramType" : "string",
            "requestParamType" : null,
            "path" : null,
            "example" : "千克",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 10,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660334",
            "name" : "mainUnitPrecision",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "主单位精度",
            "paramType" : "int",
            "requestParamType" : null,
            "path" : null,
            "example" : "1",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 11,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660338",
            "name" : "stockUnitName",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "库存单位",
            "paramType" : "string",
            "requestParamType" : null,
            "path" : null,
            "example" : "千克",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 15,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660339",
            "name" : "stockUnitId",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "库存单位id",
            "paramType" : "long",
            "requestParamType" : null,
            "path" : null,
            "example" : "2652431423640064",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 16,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660340",
            "name" : "stockUnitPrecision",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "库存单位精度",
            "paramType" : "int",
            "requestParamType" : null,
            "path" : null,
            "example" : "6",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 17,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660341",
            "name" : "changeRate",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "换算率",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "1",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 18,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660342",
            "name" : "changeType",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "换算方式: 0-固定换算，1-浮动换算",
            "paramType" : "int",
            "requestParamType" : null,
            "path" : null,
            "example" : "0",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 19,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660343",
            "name" : "recipientQuantity",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "应领数量",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "1500",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 20,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660344",
            "name" : "auxiliaryRecipientQuantity",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "应领件数",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "1500",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 21,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660345",
            "name" : "bomUnitName",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "物料清单单位",
            "paramType" : "string",
            "requestParamType" : null,
            "path" : null,
            "example" : "千克",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 22,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660346",
            "name" : "bomUnitId",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "物料清单单位id",
            "paramType" : "long",
            "requestParamType" : null,
            "path" : null,
            "example" : "2652431423640064",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 23,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660347",
            "name" : "bomUnitPrecision",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "物料清单单位精度",
            "paramType" : "int",
            "requestParamType" : null,
            "path" : null,
            "example" : "6",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 24,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660348",
            "name" : "bomUnitChangeRate",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "物料清单单位换算率",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "1",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 25,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660349",
            "name" : "bomAuxiliaryRecipientQty",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "应领件数（bom单位）",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "1500",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 26,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660350",
            "name" : "bomId",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "物料清单Id",
            "paramType" : "long",
            "requestParamType" : null,
            "path" : null,
            "example" : "1699890469018796032",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 27,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660351",
            "name" : "bomMaterialId",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "物料清单子件行id",
            "paramType" : "long",
            "requestParamType" : null,
            "path" : null,
            "example" : "1699890469018796033",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 28,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660352",
            "name" : "numeratorQuantity",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "分子用量",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "1",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 29,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660353",
            "name" : "denominatorQuantity",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "母件底数",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "1",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 30,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660354",
            "name" : "scrap",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "子件损耗率",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "0",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 31,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660355",
            "name" : "mustLossQuantity",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "固定损耗",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "0",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 32,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660356",
            "name" : "bomUnitUseQuantity",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "单位使用量（bom单位）",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "1",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 33,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660357",
            "name" : "mainNumeratorQuantity",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "分子用量（主单位）",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "1",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 34,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660358",
            "name" : "mainDenominatorQuantity",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "母件底数（主单位）",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "1",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 35,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660359",
            "name" : "unitUseQuantity",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "单位使用量",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "1",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 36,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660360",
            "name" : "fixedQuantity",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "固定用量：0-否，1-是",
            "paramType" : "int",
            "requestParamType" : null,
            "path" : null,
            "example" : "",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 37,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660361",
            "name" : "orderProductId",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "产品行id",
            "paramType" : "long",
            "requestParamType" : null,
            "path" : null,
            "example" : "1700060163165650954",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 38,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660362",
            "name" : "orgId",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "库存组织id",
            "paramType" : "string",
            "requestParamType" : null,
            "path" : null,
            "example" : "1478105315702997001",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 39,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660363",
            "name" : "orgName",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "库存组织",
            "paramType" : "string",
            "requestParamType" : null,
            "path" : null,
            "example" : "测试qss质检组织",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 40,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660364",
            "name" : "warehouseId",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "供应仓库id",
            "paramType" : "long",
            "requestParamType" : null,
            "path" : null,
            "example" : "1504817297772511237",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 41,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660365",
            "name" : "warehouseName",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "供应仓库",
            "paramType" : "string",
            "requestParamType" : null,
            "path" : null,
            "example" : "委外仓qss",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 42,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660366",
            "name" : "requirementDate",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "需求日期",
            "paramType" : "DateTime",
            "requestParamType" : null,
            "path" : null,
            "example" : "2023-04-28 00:00:00",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 43,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660367",
            "name" : "verificationBy",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "核销依据：0-按订单应发核销 1-按不含损耗的BOM用量核销 2-按含损耗的BOM用量核销",
            "paramType" : "string",
            "requestParamType" : null,
            "path" : null,
            "example" : "0",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 44,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660368",
            "name" : "subcontractSupplyType",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "委外供应方式：0-我方",
            "paramType" : "int",
            "requestParamType" : null,
            "path" : null,
            "example" : "0",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 45,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660369",
            "name" : "supplyType",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "供应方式: 0-领用 1-入库倒冲 2-不发料",
            "paramType" : "string",
            "requestParamType" : null,
            "path" : null,
            "example" : "0",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 46,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660370",
            "name" : "supDirectShip",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "供应商直运",
            "paramType" : "boolean",
            "requestParamType" : null,
            "path" : null,
            "example" : "0",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 47,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660371",
            "name" : "doubleReplenish",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "补货倍量：0-否，1-是",
            "paramType" : "int",
            "requestParamType" : null,
            "path" : null,
            "example" : "0",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 48,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660372",
            "name" : "replenishAdjustQuantity",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "补货调整数量",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "1500",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 49,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660373",
            "name" : "auxiliaryReplenishAdjustQuantity",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "补货调整件数",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "1500",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 50,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660374",
            "name" : "excessAppliedQty",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "超额申请数量",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "0",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 51,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660375",
            "name" : "auxiliaryExcessAppliedQty",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "超额申请件数",
            "paramType" : "Decimal",
            "requestParamType" : null,
            "path" : null,
            "example" : "0",
            "fullName" : "",
            "ytenantId" : "0",
            "paramOrder" : 52,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660376",
            "name" : "materialDefineDts",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "自定义特征组",
            "paramType" : "characteristic",
            "requestParamType" : null,
            "path" : null,
            "example" : "",
            "fullName" : "po.order.OrderMaterial",
            "ytenantId" : "0",
            "paramOrder" : 53,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          }, {
            "id" : "2315188935444660377",
            "name" : "freeCharacteristics",
            "apiId" : "21e0f5ca26724acf810ff6323980e234",
            "parentId" : "2315188935444660322",
            "children" : null,
            "defParamId" : null,
            "array" : false,
            "paramDesc" : "自由项特征组",
            "paramType" : "characteristic",
            "requestParamType" : null,
            "path" : null,
            "example" : "",
            "fullName" : "po.order.OrderMaterial",
            "ytenantId" : "0",
            "paramOrder" : 54,
            "baseType" : true,
            "defaultValue" : "",
            "required" : false,
            "visible" : true,
            "gmtCreate" : "2024-05-31 14:45:31.000",
            "gmtUpdate" : "2024-05-31 14:45:31.000",
            "apiName" : null,
            "entityId" : null,
            "entityCode" : null,
            "edit" : false,
            "refType" : false,
            "refTypeContext" : "",
            "defineHidden" : 0,
            "integrateObjectId" : null,
            "paramTag" : null,
            "format" : null,
            "decimals" : null,
            "maxLength" : "0",
            "enableMulti" : false
          } ],
          "defParamId" : null,
          "array" : true,
          "paramDesc" : "材料信息，入参isShowMaterial为true时显示",
          "paramType" : "object",
          "requestParamType" : null,
          "path" : null,
          "example" : "",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 49,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2025-07-17 11:37:46.077",
          "gmtUpdate" : "2025-07-17 11:37:46.077",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660378",
          "name" : "orderSubcontract_tradePath",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "贸易路径id",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "1852042374122307592",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 50,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660379",
          "name" : "orderSubcontract_tradePathName",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "贸易路径",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "01路径",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 51,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660380",
          "name" : "orderSubcontractProduct_tradePath",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "产品行贸易路径id",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "1852042374122307592",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 52,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660381",
          "name" : "orderSubcontractProduct_tradePathName",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "产品行贸易路径",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "1852042374122307592",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 53,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660382",
          "name" : "orderSubcontractProduct_cooperateDocNo",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "协同单号",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "UO-20231102000019",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 54,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660383",
          "name" : "orderSubcontractProduct_cooperateDocId",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "协同单id",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "1852955192105369602",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 55,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660384",
          "name" : "orderSubcontractProduct_cooperateLineNo",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "协同单行号",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "10",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 56,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660385",
          "name" : "orderSubcontractProduct_cooperateLineId",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "协同单行id",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "1852955192105369603",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 57,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660386",
          "name" : "orderSubcontract_tcOrgId",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "产品行收票组织id",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "1631969031461273603",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 58,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660387",
          "name" : "orderSubcontractProduct_tcOrgIdSon_name",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "产品行收票组织名称",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "收票组织",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 59,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660388",
          "name" : "orderSubcontract_tcOrgAccount",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "产品行跨组织委外结算: 1-是;0-否",
          "paramType" : "int",
          "requestParamType" : null,
          "path" : null,
          "example" : "0",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 60,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660389",
          "name" : "wbs",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "wbs",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 61,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : null,
          "enableMulti" : false
        }, {
          "id" : "2315188935444660390",
          "name" : "wbsCode",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "WBS任务编码",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 62,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : null,
          "enableMulti" : false
        }, {
          "id" : "2315188935444660391",
          "name" : "wbsName",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "WBS任务名称",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 63,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : null,
          "enableMulti" : false
        }, {
          "id" : "2315188935444660392",
          "name" : "activity",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "活动",
          "paramType" : "number",
          "requestParamType" : null,
          "path" : null,
          "example" : "",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 64,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : 0,
          "maxLength" : "36",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660393",
          "name" : "activityCode",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "活动编码",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 65,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : null,
          "enableMulti" : false
        }, {
          "id" : "2315188935444660394",
          "name" : "activityTaskName",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "活动名称",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 66,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : null,
          "enableMulti" : false
        }, {
          "id" : "2315188935444660395",
          "name" : "OrderProduct_projectId",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "项目id",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 67,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : null,
          "enableMulti" : false
        }, {
          "id" : "2315188935444660396",
          "name" : "OrderProduct_projectCode",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "项目编码",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 68,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : null,
          "enableMulti" : false
        }, {
          "id" : "2315188935444660397",
          "name" : "OrderProduct_projectName",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "项目",
          "paramType" : "string",
          "requestParamType" : null,
          "path" : null,
          "example" : "",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 69,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : null,
          "enableMulti" : false
        }, {
          "id" : "2315188935444660398",
          "name" : "isBeginning",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660272",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "期初订单标识:0-否，1-是",
          "paramType" : "number",
          "requestParamType" : null,
          "path" : null,
          "example" : "0",
          "fullName" : null,
          "ytenantId" : "0",
          "paramOrder" : 70,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : null,
          "gmtUpdate" : null,
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : null,
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : 0,
          "maxLength" : "1",
          "enableMulti" : false
        } ],
        "defParamId" : null,
        "array" : true,
        "paramDesc" : "返回数据对象",
        "paramType" : "object",
        "requestParamType" : null,
        "path" : null,
        "example" : "",
        "fullName" : "",
        "ytenantId" : "0",
        "paramOrder" : 3,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2025-07-17 11:37:46.070",
        "gmtUpdate" : "2025-07-17 11:37:46.070",
        "apiName" : null,
        "entityId" : null,
        "entityCode" : null,
        "edit" : false,
        "refType" : false,
        "refTypeContext" : "",
        "defineHidden" : 0,
        "integrateObjectId" : null,
        "paramTag" : "respData",
        "format" : null,
        "decimals" : null,
        "maxLength" : "0",
        "enableMulti" : false
      }, {
        "id" : "2315188935444660399",
        "name" : "sumRecordList",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660268",
        "children" : [ {
          "id" : "2315188935444660400",
          "name" : "OrderProduct_subcontractQuantityMU",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660399",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "委外数量",
          "paramType" : "double",
          "requestParamType" : null,
          "path" : null,
          "example" : "186.8874",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 0,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660401",
          "name" : "OrderProduct_subcontractQuantitySU",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660399",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "委外件数",
          "paramType" : "double",
          "requestParamType" : null,
          "path" : null,
          "example" : "120",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 1,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660402",
          "name" : "OrderProduct_subcontractQuantityPU",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660399",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "计价数量",
          "paramType" : "double",
          "requestParamType" : null,
          "path" : null,
          "example" : "120",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 2,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        }, {
          "id" : "2315188935444660403",
          "name" : "mainUnitPrecision",
          "apiId" : "21e0f5ca26724acf810ff6323980e234",
          "parentId" : "2315188935444660399",
          "children" : null,
          "defParamId" : null,
          "array" : false,
          "paramDesc" : "主计量精度",
          "paramType" : "long",
          "requestParamType" : null,
          "path" : null,
          "example" : "1",
          "fullName" : "",
          "ytenantId" : "0",
          "paramOrder" : 3,
          "baseType" : true,
          "defaultValue" : "",
          "required" : false,
          "visible" : true,
          "gmtCreate" : "2024-05-31 14:45:31.000",
          "gmtUpdate" : "2024-05-31 14:45:31.000",
          "apiName" : null,
          "entityId" : null,
          "entityCode" : null,
          "edit" : false,
          "refType" : false,
          "refTypeContext" : "",
          "defineHidden" : 0,
          "integrateObjectId" : null,
          "paramTag" : null,
          "format" : null,
          "decimals" : null,
          "maxLength" : "0",
          "enableMulti" : false
        } ],
        "defParamId" : null,
        "array" : true,
        "paramDesc" : "合计字段集合",
        "paramType" : "object",
        "requestParamType" : null,
        "path" : null,
        "example" : "",
        "fullName" : "",
        "ytenantId" : "0",
        "paramOrder" : 4,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2025-07-17 11:37:46.134",
        "gmtUpdate" : "2025-07-17 11:37:46.134",
        "apiName" : null,
        "entityId" : null,
        "entityCode" : null,
        "edit" : false,
        "refType" : false,
        "refTypeContext" : "",
        "defineHidden" : 0,
        "integrateObjectId" : null,
        "paramTag" : null,
        "format" : null,
        "decimals" : null,
        "maxLength" : "0",
        "enableMulti" : false
      }, {
        "id" : "2315188935444660404",
        "name" : "pageCount",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660268",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "总页数",
        "paramType" : "long",
        "requestParamType" : null,
        "path" : null,
        "example" : "0",
        "fullName" : "",
        "ytenantId" : "0",
        "paramOrder" : 5,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2024-05-31 14:45:31.000",
        "gmtUpdate" : "2024-05-31 14:45:31.000",
        "apiName" : null,
        "entityId" : null,
        "entityCode" : null,
        "edit" : false,
        "refType" : false,
        "refTypeContext" : "",
        "defineHidden" : 0,
        "integrateObjectId" : null,
        "paramTag" : null,
        "format" : null,
        "decimals" : null,
        "maxLength" : "0",
        "enableMulti" : false
      }, {
        "id" : "2315188935444660405",
        "name" : "beginPageIndex",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660268",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "开始页码",
        "paramType" : "long",
        "requestParamType" : null,
        "path" : null,
        "example" : "1",
        "fullName" : "",
        "ytenantId" : "0",
        "paramOrder" : 6,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2024-05-31 14:45:31.000",
        "gmtUpdate" : "2024-05-31 14:45:31.000",
        "apiName" : null,
        "entityId" : null,
        "entityCode" : null,
        "edit" : false,
        "refType" : false,
        "refTypeContext" : "",
        "defineHidden" : 0,
        "integrateObjectId" : null,
        "paramTag" : null,
        "format" : null,
        "decimals" : null,
        "maxLength" : "0",
        "enableMulti" : false
      }, {
        "id" : "2315188935444660406",
        "name" : "endPageIndex",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660268",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "结束页码",
        "paramType" : "long",
        "requestParamType" : null,
        "path" : null,
        "example" : "0",
        "fullName" : "",
        "ytenantId" : "0",
        "paramOrder" : 7,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2024-05-31 14:45:31.000",
        "gmtUpdate" : "2024-05-31 14:45:31.000",
        "apiName" : null,
        "entityId" : null,
        "entityCode" : null,
        "edit" : false,
        "refType" : false,
        "refTypeContext" : "",
        "defineHidden" : 0,
        "integrateObjectId" : null,
        "paramTag" : null,
        "format" : null,
        "decimals" : null,
        "maxLength" : "0",
        "enableMulti" : false
      }, {
        "id" : "2315188935444660407",
        "name" : "pubts",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "parentId" : "2315188935444660268",
        "children" : null,
        "defParamId" : null,
        "array" : false,
        "paramDesc" : "时间戳",
        "paramType" : "string",
        "requestParamType" : null,
        "path" : null,
        "example" : "2021-03-24 15:11:10",
        "fullName" : "",
        "ytenantId" : "0",
        "paramOrder" : 8,
        "baseType" : true,
        "defaultValue" : "",
        "required" : false,
        "visible" : true,
        "gmtCreate" : "2024-05-31 14:45:31.000",
        "gmtUpdate" : "2024-05-31 14:45:31.000",
        "apiName" : null,
        "entityId" : null,
        "entityCode" : null,
        "edit" : false,
        "refType" : false,
        "refTypeContext" : "",
        "defineHidden" : 0,
        "integrateObjectId" : null,
        "paramTag" : null,
        "format" : null,
        "decimals" : null,
        "maxLength" : "0",
        "enableMulti" : false
      } ],
      "defParamId" : null,
      "array" : false,
      "paramDesc" : "接口返回信息",
      "paramType" : "object",
      "requestParamType" : null,
      "path" : null,
      "example" : "",
      "fullName" : "",
      "ytenantId" : "0",
      "paramOrder" : 2,
      "baseType" : true,
      "defaultValue" : "",
      "required" : false,
      "visible" : true,
      "gmtCreate" : "2025-07-17 11:37:46.063",
      "gmtUpdate" : "2025-07-17 11:37:46.063",
      "apiName" : null,
      "entityId" : null,
      "entityCode" : null,
      "edit" : false,
      "refType" : false,
      "refTypeContext" : "",
      "defineHidden" : 0,
      "integrateObjectId" : null,
      "paramTag" : null,
      "format" : null,
      "decimals" : null,
      "maxLength" : "0",
      "enableMulti" : false
    } ],
    "returnFormatType" : "JSON",
    "paramConstDTOS" : [ ],
    "paramConstMapDTOS" : [ ],
    "apiDemoReturnDTOS" : [ {
      "id" : "2315188935444660233",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "content" : "{\n\t\"code\": 200,\n\t\"message\": \"操作成功\",\n\t\"data\": {\n\t\t\"pageIndex\": 1,\n\t\t\"pageSize\": 20,\n\t\t\"recordCount\": 0,\n\t\t\"recordList\": [\n\t\t\t{\n\t\t\t\t\"id\": ****************,\n\t\t\t\t\"transTypeId\": \"****************\",\n\t\t\t\t\"isWfControlled\": false,\n\t\t\t\t\"orgName\": \"L工厂1\",\n                \"isBeginning\": 1,\n\t\t\t\t\"status\": 0,\n\t\t\t\t\"isHold\": false,\n\t\t\t\t\"code\": \"SCDD20210324000003\",\n\t\t\t\t\"verifystate\": 0,\n\t\t\t\t\"vouchdate\": \"2021-03-24 00:00:00\",\n\t\t\t\t\"creatorId\": ****************,\n\t\t\t\t\"creator\": \"***********\",\n\t\t\t\t\"orgId\": \"****************\",\n\t\t\t\t\"transTypeName\": \"标准生产\",\n\t\t\t\t\"transTypeCode\": \"PO-001\",\n\t\t\t\t\"subcontractVendorId\": \"****************\",\n\t\t\t\t\"subcontractVendorName\": \"供应商\",\n\t\t\t\t\"pubts\": \"2021-03-24 11:40:13\",\n\t\t\t\t\"createTime\": \"2021-03-24 11:40:12\",\n\t\t\t\t\"orderProduct_id\": 2184924571914497,\n\t\t\t\t\"OrderProduct_productId\": 2061736079708416,\n\t\t\t\t\"OrderProduct_materialId\": 2061736111296768,\n\t\t\t\t\"OrderProduct_materialName\": \"自行车\",\n\t\t\t\t\"OrderProduct_materialCode\": \"jq01000001\",\n\t\t\t\t\"orderProduct_orderSubcontractProduct_subcontractUnitId\": 1869676091724032,\n\t\t\t\t\"orderProduct_orderSubcontractProduct_priceUnitId\": 1869676091724032,\n\t\t\t\t\"mainUnitPrecision\": 3,\n\t\t\t\t\"priceUnitPrecision\": 3,\n\t\t\t\t\"subcontractUnitPrecision\": 4,\n\t\t\t\t\"OrderProduct_sourceType\": \"1\",\n\t\t\t\t\"OrderProduct_deliveryDate\": \"2021-03-24 00:00:00\",\n\t\t\t\t\"OrderProduct_subcontractQuantityMU\": 120,\n\t\t\t\t\"OrderProduct_subcontractQuantitySU\": 120,\n\t\t\t\t\"OrderProduct_subcontractQuantityPU\": 120,\n\t\t\t\t\"OrderProduct_changeRate\": 1,\n\t\t\t\t\"OrderProduct_isHold\": false,\n\t\t\t\t\"OrderProduct_mainUnitName\": \"件\",\n\t\t\t\t\"OrderProduct_subcontractUnitName\": \"件\",\n\t\t\t\t\"OrderProduct_priceUnitName\": \"件\",\n\t\t\t\t\"OrderProduct_bomId\": 2173985857769728,\n\t\t\t\t\"OrderProduct_version\": \"A1\",\n\t\t\t\t\"out_sys_id\":\"10000\",\n\t\t\t\t\"out_sys_code\":\"WBWWDD001\",\n\t\t\t\t\"out_sys_version\":\"V1.0.0\",\n\t\t\t\t\"out_sys_type\":\"Type001\",\n\t\t\t\t\"OrderProduct_out_sys_lineid\":\"10010\",\n\t\t\t\t\"OrderProduct_out_sys_rowno\":\"10\",\n                \"orderSubcontract_tradePath\":1852042374122307592,\n                \"orderSubcontract_tradePathName\":\"01路径\",\n                \"orderSubcontractProduct_tradePathName\":\"01路径\",\n                \"orderSubcontractProduct_tradePath\":1852042374122307592,\n                \"orderSubcontractProduct_cooperateDocNo\": \"UO-20231102000019\",\n                \"orderSubcontractProduct_cooperateDocId\": 1852955192105369602,\n                \"orderSubcontractProduct_cooperateLineNo\": 10,\n                \"orderSubcontractProduct_cooperateLineId\": 1852955192105369603\n\t\t\t}\n\t\t],\n\t\t\"sumRecordList\": [\n\t\t\t{\n\t\t\t\t\"OrderProduct_subcontractQuantityMU\": 186.8874,\n\t\t\t\t\"OrderProduct_subcontractQuantitySU\": 120,\n\t\t\t\t\"OrderProduct_subcontractQuantityPU\": 120,\n\t\t\t\t\"mainUnitPrecision\": 1\n\t\t\t}\n\t\t],\n\t\t\"pageCount\": 0,\n\t\t\"beginPageIndex\": 1,\n\t\t\"endPageIndex\": 0,\n\t\t\"pubts\": \"2021-03-24 15:11:10\"\n\t}\n}",
      "returnType" : "JSON",
      "apiDemoReturnDesc" : "",
      "rightOrNot" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "apiName" : null,
      "edit" : false,
      "ytenantId" : "0",
      "right" : true
    }, {
      "id" : "2315188935444660234",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "content" : "{\r\n \"code\": \"999\",\r\n \"message\": \"非法的时间： 11111\"\r\n}",
      "returnType" : "JSON",
      "apiDemoReturnDesc" : "",
      "rightOrNot" : false,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "apiName" : null,
      "edit" : false,
      "ytenantId" : "0",
      "right" : false
    } ],
    "apiDemoReturnDTOList" : [ {
      "id" : "2315188935444660233",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "content" : "{\n\t\"code\": 200,\n\t\"message\": \"操作成功\",\n\t\"data\": {\n\t\t\"pageIndex\": 1,\n\t\t\"pageSize\": 20,\n\t\t\"recordCount\": 0,\n\t\t\"recordList\": [\n\t\t\t{\n\t\t\t\t\"id\": ****************,\n\t\t\t\t\"transTypeId\": \"****************\",\n\t\t\t\t\"isWfControlled\": false,\n\t\t\t\t\"orgName\": \"L工厂1\",\n                \"isBeginning\": 1,\n\t\t\t\t\"status\": 0,\n\t\t\t\t\"isHold\": false,\n\t\t\t\t\"code\": \"SCDD20210324000003\",\n\t\t\t\t\"verifystate\": 0,\n\t\t\t\t\"vouchdate\": \"2021-03-24 00:00:00\",\n\t\t\t\t\"creatorId\": ****************,\n\t\t\t\t\"creator\": \"***********\",\n\t\t\t\t\"orgId\": \"****************\",\n\t\t\t\t\"transTypeName\": \"标准生产\",\n\t\t\t\t\"transTypeCode\": \"PO-001\",\n\t\t\t\t\"subcontractVendorId\": \"****************\",\n\t\t\t\t\"subcontractVendorName\": \"供应商\",\n\t\t\t\t\"pubts\": \"2021-03-24 11:40:13\",\n\t\t\t\t\"createTime\": \"2021-03-24 11:40:12\",\n\t\t\t\t\"orderProduct_id\": 2184924571914497,\n\t\t\t\t\"OrderProduct_productId\": 2061736079708416,\n\t\t\t\t\"OrderProduct_materialId\": 2061736111296768,\n\t\t\t\t\"OrderProduct_materialName\": \"自行车\",\n\t\t\t\t\"OrderProduct_materialCode\": \"jq01000001\",\n\t\t\t\t\"orderProduct_orderSubcontractProduct_subcontractUnitId\": 1869676091724032,\n\t\t\t\t\"orderProduct_orderSubcontractProduct_priceUnitId\": 1869676091724032,\n\t\t\t\t\"mainUnitPrecision\": 3,\n\t\t\t\t\"priceUnitPrecision\": 3,\n\t\t\t\t\"subcontractUnitPrecision\": 4,\n\t\t\t\t\"OrderProduct_sourceType\": \"1\",\n\t\t\t\t\"OrderProduct_deliveryDate\": \"2021-03-24 00:00:00\",\n\t\t\t\t\"OrderProduct_subcontractQuantityMU\": 120,\n\t\t\t\t\"OrderProduct_subcontractQuantitySU\": 120,\n\t\t\t\t\"OrderProduct_subcontractQuantityPU\": 120,\n\t\t\t\t\"OrderProduct_changeRate\": 1,\n\t\t\t\t\"OrderProduct_isHold\": false,\n\t\t\t\t\"OrderProduct_mainUnitName\": \"件\",\n\t\t\t\t\"OrderProduct_subcontractUnitName\": \"件\",\n\t\t\t\t\"OrderProduct_priceUnitName\": \"件\",\n\t\t\t\t\"OrderProduct_bomId\": 2173985857769728,\n\t\t\t\t\"OrderProduct_version\": \"A1\",\n\t\t\t\t\"out_sys_id\":\"10000\",\n\t\t\t\t\"out_sys_code\":\"WBWWDD001\",\n\t\t\t\t\"out_sys_version\":\"V1.0.0\",\n\t\t\t\t\"out_sys_type\":\"Type001\",\n\t\t\t\t\"OrderProduct_out_sys_lineid\":\"10010\",\n\t\t\t\t\"OrderProduct_out_sys_rowno\":\"10\",\n                \"orderSubcontract_tradePath\":1852042374122307592,\n                \"orderSubcontract_tradePathName\":\"01路径\",\n                \"orderSubcontractProduct_tradePathName\":\"01路径\",\n                \"orderSubcontractProduct_tradePath\":1852042374122307592,\n                \"orderSubcontractProduct_cooperateDocNo\": \"UO-20231102000019\",\n                \"orderSubcontractProduct_cooperateDocId\": 1852955192105369602,\n                \"orderSubcontractProduct_cooperateLineNo\": 10,\n                \"orderSubcontractProduct_cooperateLineId\": 1852955192105369603\n\t\t\t}\n\t\t],\n\t\t\"sumRecordList\": [\n\t\t\t{\n\t\t\t\t\"OrderProduct_subcontractQuantityMU\": 186.8874,\n\t\t\t\t\"OrderProduct_subcontractQuantitySU\": 120,\n\t\t\t\t\"OrderProduct_subcontractQuantityPU\": 120,\n\t\t\t\t\"mainUnitPrecision\": 1\n\t\t\t}\n\t\t],\n\t\t\"pageCount\": 0,\n\t\t\"beginPageIndex\": 1,\n\t\t\"endPageIndex\": 0,\n\t\t\"pubts\": \"2021-03-24 15:11:10\"\n\t}\n}",
      "returnType" : "JSON",
      "apiDemoReturnDesc" : "",
      "rightOrNot" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "apiName" : null,
      "edit" : false,
      "ytenantId" : "0",
      "right" : true
    }, {
      "id" : "2315188935444660234",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "content" : "{\r\n \"code\": \"999\",\r\n \"message\": \"非法的时间： 11111\"\r\n}",
      "returnType" : "JSON",
      "apiDemoReturnDesc" : "",
      "rightOrNot" : false,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "apiName" : null,
      "edit" : false,
      "ytenantId" : "0",
      "right" : false
    } ],
    "routingStgy" : 0,
    "routingStgyList" : [ ],
    "apiDemoReturnDTO" : {
      "id" : "2315188935444660233",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "content" : "{\n\t\"code\": 200,\n\t\"message\": \"操作成功\",\n\t\"data\": {\n\t\t\"pageIndex\": 1,\n\t\t\"pageSize\": 20,\n\t\t\"recordCount\": 0,\n\t\t\"recordList\": [\n\t\t\t{\n\t\t\t\t\"id\": ****************,\n\t\t\t\t\"transTypeId\": \"****************\",\n\t\t\t\t\"isWfControlled\": false,\n\t\t\t\t\"orgName\": \"L工厂1\",\n                \"isBeginning\": 1,\n\t\t\t\t\"status\": 0,\n\t\t\t\t\"isHold\": false,\n\t\t\t\t\"code\": \"SCDD20210324000003\",\n\t\t\t\t\"verifystate\": 0,\n\t\t\t\t\"vouchdate\": \"2021-03-24 00:00:00\",\n\t\t\t\t\"creatorId\": ****************,\n\t\t\t\t\"creator\": \"***********\",\n\t\t\t\t\"orgId\": \"****************\",\n\t\t\t\t\"transTypeName\": \"标准生产\",\n\t\t\t\t\"transTypeCode\": \"PO-001\",\n\t\t\t\t\"subcontractVendorId\": \"****************\",\n\t\t\t\t\"subcontractVendorName\": \"供应商\",\n\t\t\t\t\"pubts\": \"2021-03-24 11:40:13\",\n\t\t\t\t\"createTime\": \"2021-03-24 11:40:12\",\n\t\t\t\t\"orderProduct_id\": 2184924571914497,\n\t\t\t\t\"OrderProduct_productId\": 2061736079708416,\n\t\t\t\t\"OrderProduct_materialId\": 2061736111296768,\n\t\t\t\t\"OrderProduct_materialName\": \"自行车\",\n\t\t\t\t\"OrderProduct_materialCode\": \"jq01000001\",\n\t\t\t\t\"orderProduct_orderSubcontractProduct_subcontractUnitId\": 1869676091724032,\n\t\t\t\t\"orderProduct_orderSubcontractProduct_priceUnitId\": 1869676091724032,\n\t\t\t\t\"mainUnitPrecision\": 3,\n\t\t\t\t\"priceUnitPrecision\": 3,\n\t\t\t\t\"subcontractUnitPrecision\": 4,\n\t\t\t\t\"OrderProduct_sourceType\": \"1\",\n\t\t\t\t\"OrderProduct_deliveryDate\": \"2021-03-24 00:00:00\",\n\t\t\t\t\"OrderProduct_subcontractQuantityMU\": 120,\n\t\t\t\t\"OrderProduct_subcontractQuantitySU\": 120,\n\t\t\t\t\"OrderProduct_subcontractQuantityPU\": 120,\n\t\t\t\t\"OrderProduct_changeRate\": 1,\n\t\t\t\t\"OrderProduct_isHold\": false,\n\t\t\t\t\"OrderProduct_mainUnitName\": \"件\",\n\t\t\t\t\"OrderProduct_subcontractUnitName\": \"件\",\n\t\t\t\t\"OrderProduct_priceUnitName\": \"件\",\n\t\t\t\t\"OrderProduct_bomId\": 2173985857769728,\n\t\t\t\t\"OrderProduct_version\": \"A1\",\n\t\t\t\t\"out_sys_id\":\"10000\",\n\t\t\t\t\"out_sys_code\":\"WBWWDD001\",\n\t\t\t\t\"out_sys_version\":\"V1.0.0\",\n\t\t\t\t\"out_sys_type\":\"Type001\",\n\t\t\t\t\"OrderProduct_out_sys_lineid\":\"10010\",\n\t\t\t\t\"OrderProduct_out_sys_rowno\":\"10\",\n                \"orderSubcontract_tradePath\":1852042374122307592,\n                \"orderSubcontract_tradePathName\":\"01路径\",\n                \"orderSubcontractProduct_tradePathName\":\"01路径\",\n                \"orderSubcontractProduct_tradePath\":1852042374122307592,\n                \"orderSubcontractProduct_cooperateDocNo\": \"UO-20231102000019\",\n                \"orderSubcontractProduct_cooperateDocId\": 1852955192105369602,\n                \"orderSubcontractProduct_cooperateLineNo\": 10,\n                \"orderSubcontractProduct_cooperateLineId\": 1852955192105369603\n\t\t\t}\n\t\t],\n\t\t\"sumRecordList\": [\n\t\t\t{\n\t\t\t\t\"OrderProduct_subcontractQuantityMU\": 186.8874,\n\t\t\t\t\"OrderProduct_subcontractQuantitySU\": 120,\n\t\t\t\t\"OrderProduct_subcontractQuantityPU\": 120,\n\t\t\t\t\"mainUnitPrecision\": 1\n\t\t\t}\n\t\t],\n\t\t\"pageCount\": 0,\n\t\t\"beginPageIndex\": 1,\n\t\t\"endPageIndex\": 0,\n\t\t\"pubts\": \"2021-03-24 15:11:10\"\n\t}\n}",
      "returnType" : "JSON",
      "apiDemoReturnDesc" : "",
      "rightOrNot" : true,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "apiName" : null,
      "edit" : false,
      "ytenantId" : "0",
      "right" : true
    },
    "apiDemoReturnDTOError" : {
      "id" : "2315188935444660234",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "content" : "{\r\n \"code\": \"999\",\r\n \"message\": \"非法的时间： 11111\"\r\n}",
      "returnType" : "JSON",
      "apiDemoReturnDesc" : "",
      "rightOrNot" : false,
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "apiName" : null,
      "edit" : false,
      "ytenantId" : "0",
      "right" : false
    },
    "errorCodeDTOS" : [ {
      "id" : "2315188935444660235",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "errorCode" : "999",
      "errorMessage" : "取决于错误类型，不同错误信息不同",
      "errorType" : "API",
      "errorcodeDesc" : "",
      "gmtCreate" : "2024-05-31 14:45:31.000",
      "gmtUpdate" : "2024-05-31 14:45:31.000",
      "apiName" : null,
      "edit" : false,
      "defErrorId" : null,
      "ytenantId" : "0",
      "displayCodeId" : null
    } ],
    "displayCodeApiConfigDTOS" : [ ],
    "tokenPlugin" : null,
    "paramParsePlugin" : null,
    "authPlugin" : {
      "id" : "09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b",
      "isvId" : "75c13acc-ae53-4694-9a6e-3ddcce26d7f3",
      "code" : "",
      "name" : "友户通token认证业务扩展插件",
      "configurable" : false,
      "description" : "YonsuitBusinessExtendPlugin",
      "pluginType" : "auth",
      "pluginTypeName" : "业务扩展插件",
      "pluginLevel" : "publishapp",
      "classPath" : "com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin",
      "expansionConf" : "",
      "defaultPlugin" : true,
      "open" : true,
      "visible" : false,
      "gmtCreate" : "2020-05-22 00:00:00",
      "gmtUpdate" : "2020-05-22 00:00:00",
      "packagePath" : null,
      "useScene" : 1,
      "runStatus" : "run",
      "runVersion" : "",
      "toDeploy" : false,
      "levelApi" : true,
      "levelApp" : false,
      "levelPublishapp" : true,
      "levelSystem" : false,
      "containsDefault" : null,
      "levels" : [ "levelApi", "levelPublishapp" ],
      "levelsDesc" : null,
      "notInIds" : null,
      "customProperties" : null,
      "deployStatus" : "deploy",
      "deployVersion" : "",
      "custom" : false,
      "strategyId" : "",
      "strategyName" : "",
      "superiorId" : "21e0f5ca26724acf810ff6323980e234",
      "ytenantId" : "0",
      "unPluginCode" : null,
      "runStatusDesc" : "UID:P_UCG_177A9F3E05D0015D"
    },
    "resultParsePlugin" : null,
    "mapReturnPluginConfig" : null,
    "billNo" : "",
    "domain" : "",
    "apiCategory" : null,
    "docUrl" : "",
    "pathMatch" : 0,
    "createUser" : "3970ff8d-d4d1-4e03-9b63-f59412cf4886",
    "createUserName" : null,
    "approvalStatus" : 1,
    "publishTime" : "2025-07-17 11:38:18",
    "pathJoin" : true,
    "timeOut" : "20",
    "tokenPluginName" : null,
    "authPluginName" : null,
    "resultPluginName" : null,
    "apiDemoReturnRightDemo" : null,
    "apiDemoReturnErrorDemo" : null,
    "mock" : false,
    "mockTimeout" : null,
    "customUrl" : "subcontractorder/list",
    "fixedUrl" : "/yonbip/mfg/",
    "apiCode" : "21e0f5ca26724acf810ff6323980e234",
    "tokenCheckType" : 0,
    "enableMulti" : false,
    "multiField" : "",
    "idempotent" : "non",
    "bidirectionalSSL" : null,
    "ucgSchema" : "HTTPS",
    "updateUserId" : "99ea7655-00a2-4bda-b23c-19ade37ea574",
    "updateUserName" : "昵某-13662080373",
    "paramIsForce" : true,
    "userIDPassthrough" : false,
    "applyUser" : null,
    "applyMsg" : null,
    "dr" : 0,
    "microServiceCode" : "domain.yonbip-mm-mfpo",
    "applicationCode" : "yonbip-mm-mfpo",
    "privacyCategory" : 1,
    "privacyLevel" : 1,
    "apiDesigned" : 0,
    "serviceType" : 0,
    "integrateSchemeCode" : "",
    "integrateSchemeName" : "",
    "integrateObjectCode" : "1800201964255969283",
    "integrateObjectName" : "委外订单-委外订单列表查询",
    "integrateObjectCreatedType" : 1,
    "returnIntegObjId" : null,
    "returnIntegObjName" : null,
    "apiIntegrateDTOList" : [ ],
    "apiRouteInfoDTOList" : [ ],
    "arrayParam" : false,
    "fileSize" : null,
    "cc" : true,
    "paramTransferMode" : 2,
    "ytenantId" : "0",
    "statusConf" : null,
    "scene" : 1,
    "version" : null,
    "bizObjUri" : "",
    "bizObjOperationType" : "",
    "apiDefId" : null,
    "paramExtBizObjCode" : null,
    "paramExtBizObjName" : null,
    "paramExtRequest" : 1,
    "paramExtResponse" : 1,
    "paramExtInExtendKey" : 1,
    "openScene" : 1,
    "integrationScene" : 1,
    "apiType" : 3,
    "paramMark" : {
      "request" : [ {
        "id" : "2315188935444660437",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "paramCode" : "pageIndex",
        "refCode" : "pageIndex",
        "paramPosition" : "request",
        "ytenantId" : "0",
        "creator" : "99ea7655-00a2-4bda-b23c-19ade37ea574",
        "createTime" : 1752723466000,
        "modifier" : "99ea7655-00a2-4bda-b23c-19ade37ea574",
        "pubts" : 1752723466000,
        "modifyTime" : 1752723466000,
        "children" : null,
        "parentId" : null,
        "paramOrder" : null,
        "replicable" : null,
        "order" : 0
      }, {
        "id" : "2315188935444660438",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "paramCode" : "pageSize",
        "refCode" : "pageSize",
        "paramPosition" : "request",
        "ytenantId" : "0",
        "creator" : "99ea7655-00a2-4bda-b23c-19ade37ea574",
        "createTime" : 1752723466000,
        "modifier" : "99ea7655-00a2-4bda-b23c-19ade37ea574",
        "pubts" : 1752723466000,
        "modifyTime" : 1752723466000,
        "children" : null,
        "parentId" : null,
        "paramOrder" : null,
        "replicable" : null,
        "order" : 0
      }, {
        "id" : "2315188935444660439",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "paramCode" : "lastUpdateTime",
        "refCode" : "simple.open_pubts_begin",
        "paramPosition" : "request",
        "ytenantId" : "0",
        "creator" : "99ea7655-00a2-4bda-b23c-19ade37ea574",
        "createTime" : 1752723466000,
        "modifier" : "99ea7655-00a2-4bda-b23c-19ade37ea574",
        "pubts" : 1752723466000,
        "modifyTime" : 1752723466000,
        "children" : null,
        "parentId" : null,
        "paramOrder" : null,
        "replicable" : null,
        "order" : 0
      }, {
        "id" : "2315188935444660440",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "paramCode" : "thisSyncTime",
        "refCode" : "simple.open_pubts_end",
        "paramPosition" : "request",
        "ytenantId" : "0",
        "creator" : "99ea7655-00a2-4bda-b23c-19ade37ea574",
        "createTime" : 1752723466000,
        "modifier" : "99ea7655-00a2-4bda-b23c-19ade37ea574",
        "pubts" : 1752723466000,
        "modifyTime" : 1752723466000,
        "children" : null,
        "parentId" : null,
        "paramOrder" : null,
        "replicable" : null,
        "order" : 0
      } ],
      "response" : [ {
        "id" : "2315188935444660441",
        "apiId" : "21e0f5ca26724acf810ff6323980e234",
        "paramCode" : "respData",
        "refCode" : "data.recordList",
        "paramPosition" : "response",
        "ytenantId" : "0",
        "creator" : "99ea7655-00a2-4bda-b23c-19ade37ea574",
        "createTime" : 1752723466000,
        "modifier" : "99ea7655-00a2-4bda-b23c-19ade37ea574",
        "pubts" : 1752723466000,
        "modifyTime" : 1752723466000,
        "children" : null,
        "parentId" : null,
        "paramOrder" : null,
        "replicable" : null,
        "order" : 0
      } ]
    },
    "integrateSysId" : null,
    "integrateSysName" : null,
    "integrateSysCode" : null,
    "dataZoneSetting" : true,
    "reqDataZoneSetting" : false,
    "respDataZoneSetting" : true,
    "reqDataAllQuery" : false,
    "reqDataAllBody" : false,
    "respDataAllBody" : false,
    "chargeStatus" : 1,
    "beforeSpeed" : 60,
    "afterSpeed" : 120,
    "speedStatus" : false,
    "reqDataRefPath" : null,
    "respDataRefPath" : "data.recordList",
    "pubHistory" : [ {
      "id" : "2315189218912501760",
      "apiId" : "21e0f5ca26724acf810ff6323980e234",
      "apiName" : "委外订单列表查询",
      "applyReason" : "支持simpleVOs查询",
      "publishUserName" : null,
      "version" : "20250717113818",
      "operationTime" : "2025-07-17",
      "gmtCreate" : null,
      "gmtUpdate" : null,
      "changes" : [ {
        "changePosition" : "baseInfo",
        "newList" : [ ],
        "updateList" : [ {
          "changeProperty" : "enableMulti",
          "oldValue" : null,
          "newValue" : "false"
        } ],
        "deleteList" : [ ]
      }, {
        "changePosition" : "paramDTOS",
        "newList" : [ {
          "changeProperty" : "simpleVOs",
          "oldValue" : null,
          "newValue" : "{\"id\":\"2315188935444660255\",\"name\":\"simpleVOs\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":true,\"paramDesc\":\"扩展查询条件\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs\",\"example\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"
        }, {
          "changeProperty" : "field",
          "oldValue" : null,
          "newValue" : "{\"id\":\"2315188935444660256\",\"name\":\"field\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660255\",\"array\":false,\"paramDesc\":\"属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 示例：pubts\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.field\",\"example\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"
        }, {
          "changeProperty" : "op",
          "oldValue" : null,
          "newValue" : "{\"id\":\"2315188935444660257\",\"name\":\"op\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660255\",\"array\":false,\"paramDesc\":\"比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.op\",\"example\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"
        }, {
          "changeProperty" : "value1",
          "oldValue" : null,
          "newValue" : "{\"id\":\"2315188935444660258\",\"name\":\"value1\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660255\",\"array\":false,\"paramDesc\":\"查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.value1\",\"example\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"
        }, {
          "changeProperty" : "value2",
          "oldValue" : null,
          "newValue" : "{\"id\":\"2315188935444660259\",\"name\":\"value2\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660255\",\"array\":false,\"paramDesc\":\"查询条件值2\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.value2\",\"example\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"
        }, {
          "changeProperty" : "logicOp",
          "oldValue" : null,
          "newValue" : "{\"id\":\"2315188935444660260\",\"name\":\"logicOp\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660255\",\"array\":false,\"paramDesc\":\"逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.logicOp\",\"example\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"
        }, {
          "changeProperty" : "conditions",
          "oldValue" : null,
          "newValue" : "{\"id\":\"2315188935444660261\",\"name\":\"conditions\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660255\",\"array\":true,\"paramDesc\":\"下级查询条件\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions\",\"example\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"
        }, {
          "changeProperty" : "field",
          "oldValue" : null,
          "newValue" : "{\"id\":\"2315188935444660262\",\"name\":\"field\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660261\",\"array\":false,\"paramDesc\":\"比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions.field\",\"example\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"
        }, {
          "changeProperty" : "op",
          "oldValue" : null,
          "newValue" : "{\"id\":\"2315188935444660263\",\"name\":\"op\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660261\",\"array\":false,\"paramDesc\":\"逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions.op\",\"example\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"
        }, {
          "changeProperty" : "value1",
          "oldValue" : null,
          "newValue" : "{\"id\":\"2315188935444660264\",\"name\":\"value1\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660261\",\"array\":false,\"paramDesc\":\"查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions.value1\",\"example\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"
        }, {
          "changeProperty" : "value2",
          "oldValue" : null,
          "newValue" : "{\"id\":\"2315188935444660265\",\"name\":\"value2\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660261\",\"array\":false,\"paramDesc\":\"查询条件值2\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simpleVOs.conditions.value2\",\"example\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"
        } ],
        "updateList" : [ {
          "changeProperty" : "pageIndex",
          "oldValue" : "{\"id\":\"2046156196500472293\",\"name\":\"pageIndex\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"defParamId\":\"2046156196500471967\",\"array\":false,\"paramDesc\":\"页号 默认值:1\",\"paramType\":\"int\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_pageIndex\",\"example\":\"1\",\"fullName\":\"\",\"required\":true,\"defaultValue\":\"1\",\"visible\":true,\"maxLength\":\"0\"}",
          "newValue" : "{\"id\":\"2315188935444660237\",\"name\":\"pageIndex\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":false,\"paramDesc\":\"页号 默认值:1\",\"paramType\":\"int\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_pageIndex\",\"example\":\"1\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":true,\"defaultValue\":\"1\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"
        }, {
          "changeProperty" : "pageSize",
          "oldValue" : "{\"id\":\"2046156196500472294\",\"name\":\"pageSize\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"defParamId\":\"2046156196500471968\",\"array\":false,\"paramDesc\":\"每页行数    默认值:10\",\"paramType\":\"int\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_pageSize\",\"example\":\"10\",\"fullName\":\"\",\"required\":true,\"defaultValue\":\"10\",\"visible\":true,\"maxLength\":\"0\"}",
          "newValue" : "{\"id\":\"2315188935444660238\",\"name\":\"pageSize\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":false,\"paramDesc\":\"每页行数    默认值:10\",\"paramType\":\"int\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_pageSize\",\"example\":\"10\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":true,\"defaultValue\":\"10\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"
        }, {
          "changeProperty" : "code",
          "oldValue" : "{\"id\":\"2046156196500472295\",\"name\":\"code\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"defParamId\":\"2046156196500471969\",\"array\":false,\"paramDesc\":\"委外订单号\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_code\",\"example\":\"WWDD202105010001\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}",
          "newValue" : "{\"id\":\"2315188935444660239\",\"name\":\"code\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":false,\"paramDesc\":\"委外订单号\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_code\",\"example\":\"WWDD202105010001\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"
        }, {
          "changeProperty" : "status",
          "oldValue" : "{\"id\":\"2046156196500472296\",\"name\":\"status\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"defParamId\":\"2046156196500471970\",\"array\":false,\"paramDesc\":\"订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_status\",\"example\":\"0\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}",
          "newValue" : "{\"id\":\"2315188935444660240\",\"name\":\"status\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":false,\"paramDesc\":\"订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_status\",\"example\":\"0\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"
        }, {
          "changeProperty" : "transTypeId",
          "oldValue" : "{\"id\":\"2046156196500472297\",\"name\":\"transTypeId\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"defParamId\":\"2046156196500471971\",\"array\":true,\"paramDesc\":\"交易类型\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_transTypeId\",\"example\":\"[\\\"1866605942578527\\\"]\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}",
          "newValue" : "{\"id\":\"2315188935444660241\",\"name\":\"transTypeId\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":true,\"paramDesc\":\"交易类型\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_transTypeId\",\"example\":\"[\\\"1866605942578527\\\"]\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"
        }, {
          "changeProperty" : "orgId",
          "oldValue" : "{\"id\":\"2046156196500472298\",\"name\":\"orgId\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"defParamId\":\"2046156196500471972\",\"array\":true,\"paramDesc\":\"组织\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_orgId\",\"example\":\"[\\\"1866605942198782\\\"]\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}",
          "newValue" : "{\"id\":\"2315188935444660242\",\"name\":\"orgId\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":true,\"paramDesc\":\"组织\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_orgId\",\"example\":\"[\\\"1866605942198782\\\"]\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"
        }, {
          "changeProperty" : "subcontractVendorId",
          "oldValue" : "{\"id\":\"2046156196500472299\",\"name\":\"subcontractVendorId\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"defParamId\":\"2046156196500471973\",\"array\":true,\"paramDesc\":\"委外商\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_subcontractVendorId\",\"example\":\"[\\\"1866605942197864\\\"]\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}",
          "newValue" : "{\"id\":\"2315188935444660243\",\"name\":\"subcontractVendorId\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":true,\"paramDesc\":\"委外商\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_subcontractVendorId\",\"example\":\"[\\\"1866605942197864\\\"]\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"
        }, {
          "changeProperty" : "OrderProduct!materialId",
          "oldValue" : "{\"id\":\"2046156196500472300\",\"name\":\"OrderProduct!materialId\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"defParamId\":\"2046156196500471974\",\"array\":true,\"paramDesc\":\"制造物料id\",\"paramType\":\"long\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_OrderProduct!materialId\",\"example\":\"[1866605942197885]\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}",
          "newValue" : "{\"id\":\"2315188935444660244\",\"name\":\"OrderProduct!materialId\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":true,\"paramDesc\":\"制造物料id\",\"paramType\":\"long\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_OrderProduct!materialId\",\"example\":\"[1866605942197885]\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"
        }, {
          "changeProperty" : "OrderProduct!productId",
          "oldValue" : "{\"id\":\"2046156196500472301\",\"name\":\"OrderProduct!productId\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"defParamId\":\"2046156196500471975\",\"array\":true,\"paramDesc\":\"物料id\",\"paramType\":\"long\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_OrderProduct!productId\",\"example\":\"[1866605942115973]\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}",
          "newValue" : "{\"id\":\"2315188935444660245\",\"name\":\"OrderProduct!productId\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":true,\"paramDesc\":\"物料id\",\"paramType\":\"long\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_OrderProduct!productId\",\"example\":\"[1866605942115973]\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"
        }, {
          "changeProperty" : "OrderProductSubcontract!deliveryDate",
          "oldValue" : "{\"id\":\"2046156196500472302\",\"name\":\"OrderProductSubcontract!deliveryDate\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"defParamId\":\"2046156196500471976\",\"array\":false,\"paramDesc\":\"交货日期（区间，格式2021-03-02|2021-03-02 23:59:59）\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_OrderProductSubcontract!deliveryDate\",\"example\":\"2021-03-02|2021-03-02 23:59:59\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}",
          "newValue" : "{\"id\":\"2315188935444660246\",\"name\":\"OrderProductSubcontract!deliveryDate\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":false,\"paramDesc\":\"交货日期（区间，格式2021-03-02|2021-03-02 23:59:59）\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_OrderProductSubcontract!deliveryDate\",\"example\":\"2021-03-02|2021-03-02 23:59:59\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"
        }, {
          "changeProperty" : "vouchdate",
          "oldValue" : "{\"id\":\"2046156196500472303\",\"name\":\"vouchdate\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"defParamId\":\"2046156196500471977\",\"array\":false,\"paramDesc\":\"单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_vouchdate\",\"example\":\"2021-03-02|2021-03-02 23:59:59\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}",
          "newValue" : "{\"id\":\"2315188935444660247\",\"name\":\"vouchdate\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":false,\"paramDesc\":\"单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_vouchdate\",\"example\":\"2021-03-02|2021-03-02 23:59:59\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"
        }, {
          "changeProperty" : "isShowMaterial",
          "oldValue" : "{\"id\":\"2046156196500472304\",\"name\":\"isShowMaterial\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"defParamId\":\"2046156196500471978\",\"array\":false,\"paramDesc\":\"是否展示材料:true-是,false-否\",\"paramType\":\"boolean\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_isShowMaterial\",\"example\":\"false\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"false\",\"visible\":true,\"maxLength\":\"\"}",
          "newValue" : "{\"id\":\"2315188935444660248\",\"name\":\"isShowMaterial\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":false,\"paramDesc\":\"是否展示材料:true-是,false-否\",\"paramType\":\"boolean\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_isShowMaterial\",\"example\":\"false\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"false\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"
        }, {
          "changeProperty" : "simple",
          "oldValue" : "{\"id\":\"2046156196500472287\",\"name\":\"simple\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"defParamId\":\"2046156196500471979\",\"array\":false,\"paramDesc\":\"simple\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple\",\"example\":\"\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}",
          "newValue" : "{\"id\":\"2315188935444660249\",\"name\":\"simple\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"array\":false,\"paramDesc\":\"simple\",\"paramType\":\"object\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple\",\"example\":\"\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"
        }, {
          "changeProperty" : "open_pubts_begin",
          "oldValue" : "{\"id\":\"2046156196500472288\",\"name\":\"open_pubts_begin\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2046156196500472287\",\"defParamId\":\"2046156196500471980\",\"array\":false,\"paramDesc\":\"时间戳，开始时间\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.open_pubts_begin\",\"example\":\"2022-04-01 00:00:00\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}",
          "newValue" : "{\"id\":\"2315188935444660250\",\"name\":\"open_pubts_begin\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660249\",\"array\":false,\"paramDesc\":\"时间戳，开始时间\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.open_pubts_begin\",\"example\":\"2022-04-01 00:00:00\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"
        }, {
          "changeProperty" : "open_pubts_end",
          "oldValue" : "{\"id\":\"2046156196500472289\",\"name\":\"open_pubts_end\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2046156196500472287\",\"defParamId\":\"2046156196500471981\",\"array\":false,\"paramDesc\":\"时间戳，结束时间\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.open_pubts_end\",\"example\":\"2022-04-20 00:00:00\",\"fullName\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"\"}",
          "newValue" : "{\"id\":\"2315188935444660251\",\"name\":\"open_pubts_end\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660249\",\"array\":false,\"paramDesc\":\"时间戳，结束时间\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.open_pubts_end\",\"example\":\"2022-04-20 00:00:00\",\"fullName\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"\"}"
        }, {
          "changeProperty" : "orderProduct.wbs",
          "oldValue" : "{\"id\":\"2046156196500472290\",\"name\":\"orderProduct.wbs\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2046156196500472287\",\"defParamId\":\"2046156196500471982\",\"array\":false,\"paramDesc\":\"wbs\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.orderProduct.wbs\",\"example\":\"1866605942198447\",\"required\":false,\"defaultValue\":\"\",\"visible\":true}",
          "newValue" : "{\"id\":\"2315188935444660252\",\"name\":\"orderProduct.wbs\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660249\",\"array\":false,\"paramDesc\":\"wbs\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.orderProduct.wbs\",\"example\":\"1866605942198447\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"
        }, {
          "changeProperty" : "orderProduct.projectId",
          "oldValue" : "{\"id\":\"2046156196500472291\",\"name\":\"orderProduct.projectId\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2046156196500472287\",\"defParamId\":\"2046156196500471983\",\"array\":false,\"paramDesc\":\"项目Id\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.orderProduct.projectId\",\"example\":\"1866605942178545\",\"required\":false,\"defaultValue\":\"\",\"visible\":true}",
          "newValue" : "{\"id\":\"2315188935444660253\",\"name\":\"orderProduct.projectId\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660249\",\"array\":false,\"paramDesc\":\"项目Id\",\"paramType\":\"string\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.orderProduct.projectId\",\"example\":\"1866605942178545\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"
        }, {
          "changeProperty" : "orderProduct.activity",
          "oldValue" : "{\"id\":\"2046156196500472292\",\"name\":\"orderProduct.activity\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2046156196500472287\",\"defParamId\":\"2046156196500471984\",\"array\":false,\"paramDesc\":\"活动id\",\"paramType\":\"number\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.orderProduct.activity\",\"example\":\"1866605427458631\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"decimals\":0,\"maxLength\":\"32\"}",
          "newValue" : "{\"id\":\"2315188935444660254\",\"name\":\"orderProduct.activity\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660249\",\"array\":false,\"paramDesc\":\"活动id\",\"paramType\":\"number\",\"requestParamType\":\"BodyParam\",\"path\":\"BodyParam_simple.orderProduct.activity\",\"example\":\"1866605427458631\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"decimals\":0,\"maxLength\":\"32\"}"
        } ],
        "deleteList" : [ ]
      }, {
        "changePosition" : "paramReturnDTOS",
        "newList" : [ {
          "changeProperty" : "isBeginning",
          "oldValue" : null,
          "newValue" : "{\"id\":\"2315188935444660398\",\"name\":\"isBeginning\",\"apiId\":\"21e0f5ca26724acf810ff6323980e234\",\"parentId\":\"2315188935444660272\",\"array\":false,\"paramDesc\":\"期初订单标识:0-否，1-是\",\"paramType\":\"number\",\"path\":\"null_data.recordList.isBeginning\",\"example\":\"0\",\"ytenantId\":\"0\"}"
        } ],
        "updateList" : [ ],
        "deleteList" : [ ]
      } ]
    } ],
    "deprecated" : 0,
    "recommendedApiId" : null,
    "recommendedApiName" : null,
    "rePublishDcList" : null,
    "domainAppCode" : "productionorder.osm_OSMOrder_OSMOrder",
    "multiVersion" : 0,
    "apiTag" : null
  }
}
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>2108770660671029249</id>
<name>用友YonBIP</name>
<type>integrateSys</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MFC</id>
<name>制造云</name>
<type>1</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MF</id>
<name>生产制造</name>
<type>2</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>OSM</id>
<name>委外管理</name>
<type>3</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>productionorder.osm_OSMOrder_OSMOrder</id>
<name>委外订单</name>
<type>4</type>
<sort>0</sort>
<enable>0</enable>
<children/>
<parentId/>
<productId/>
<code>productionorder.osm_OSMOrder_OSMOrder</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>OSM</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MF</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MFC</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>current_yonbip_default_sys</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<isOrigin>0</isOrigin>
<hasChildren>0</hasChildren>
<order>0</order>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>72113971-ae4c-4188-bc55-44b6173f4e0b</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:18:18</gmtCreate>
<gmtUpdate>2025-07-26 17:18:18</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>b946709d-f4d9-4a43-a551-f55beee7f3d5</id>
<name>XXX0111</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类项</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:18:18</gmtCreate>
<gmtUpdate>2025-07-26 17:18:18</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:18:18</gmtCreate>
<gmtUpdate>2025-07-26 17:18:18</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>23f313c7-499f-45bc-8609-462541f2565b</id>
<name>WW</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>委外交货日期</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>date</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:18:29</gmtCreate>
<gmtUpdate>2025-07-26 17:18:29</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>7e54ba52-fd35-458a-bd63-9cb7802d39c6</id>
<name>XS11</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类号test</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:18:29</gmtCreate>
<gmtUpdate>2025-07-26 17:18:29</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>fb3bfbb9-3da1-4b8b-8019-59a8adcf0e68</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:18:29</gmtCreate>
<gmtUpdate>2025-07-26 17:18:29</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:18:29</gmtCreate>
<gmtUpdate>2025-07-26 17:18:29</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>51329822-0099-4762-ba26-a3fa29828ed4</id>
<name>U9003</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>U9生产订单号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:18:41</gmtCreate>
<gmtUpdate>2025-07-26 17:18:41</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>100</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:18:41</gmtCreate>
<gmtUpdate>2025-07-26 17:18:41</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>51329822-0099-4762-ba26-a3fa29828ed4</id>
<name>U9003</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>U9生产订单号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:22:21</gmtCreate>
<gmtUpdate>2025-07-26 17:22:21</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>100</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:22:21</gmtCreate>
<gmtUpdate>2025-07-26 17:22:21</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
