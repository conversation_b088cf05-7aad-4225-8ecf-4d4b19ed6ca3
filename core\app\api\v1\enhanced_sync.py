import asyncio
import time
from datetime import datetime

import structlog
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.config import MODULES_CONFIG
from ...core.database import async_engine
from ...services.data_write_manager import DataWriteManager
from ...services.zero_downtime_implementation import ZeroDowntimeDataWriter

"""
YS-API V3.0 增强版同步API
提供更灵活的同步选项和更好的性能
"""


router = APIRouter(tags=["增强版同步"])
logger = structlog.get_logger()


# 请求模型
class EnhancedSyncRequest(BaseModel):
    """增强版同步请求"""

    module_name: str = Field(..., description="模块名称")
    record_limit: int = Field(10, description="记录数限制", ge=1)
    use_zero_downtime: bool = Field(True, description="是否使用零停机模式")
    timeout_seconds: int = Field(60, description="超时时间（秒）", ge=5)
    clear_existing_data: bool = Field(False, description="是否在同步前清空现有数据")


# 全局写入管理器
write_manager = DataWriteManager()

# 后台任务状态
background_tasks = {}


@router.post(
    "/enhanced",
    summary="增强版单模块同步",
    description="使用增强版选项同步单个模块数据",
)
async def enhanced_sync(request: EnhancedSyncRequest):
    """增强版单模块同步"""
    try:
        # 验证模块名称
        valid_modules = [m["name"] for m in MODULES_CONFIG]
        if request.module_name not in valid_modules:
            raise HTTPException(
                status_code=400,
                detail=f"无效的模块名称 '{request.module_name}'，可用模块: {valid_modules}",
            )

        # 创建任务ID
        task_id = f"{request.module_name}_{int(time.time())}"

        # 检查是否已有同步任务
        if request.module_name in background_tasks:
            task_info = background_tasks[request.module_name]
            if not task_info["completed"]:
                return {
                    "success": True,
                    "message": f"模块 {request.module_name} 已有同步任务在进行中",
                    "task_id": task_info["task_id"],
                    "status": "running",
                    "start_time": task_info["start_time"].isoformat(),
                    "progress": task_info["progress"],
                }

        # 创建后台任务状态
        background_tasks[request.module_name] = {
            "task_id": task_id,
            "start_time": datetime.now(),
            "completed": False,
            "success": None,
            "progress": 0,
            "message": "初始化中",
            "result": None,
        }

        # 创建后台任务
        background_task = asyncio.create_task(
            _run_sync_with_timeout(
                request.module_name,
                request.record_limit,
                request.use_zero_downtime,
                request.timeout_seconds,
                request.clear_existing_data,
                task_id,
            )
        )

        # 不等待任务完成
        return {
            "success": True,
            "message": f"模块 {request.module_name} 同步任务已启动",
            "task_id": task_id,
            "status": "started",
            "timeout_seconds": request.timeout_seconds,
        }

    except HTTPException:
        raise
    except Exception:
        logger.error(
    "增强版同步API异常",
    module_name=request.module_name,
     error=str(e))
        raise HTTPException(status_code=500, detail=f"同步启动失败: {str(e)}")


@router.get(
    "/status/{task_id}",
    summary="获取同步任务状态",
    description="获取指定同步任务的状态",
)
async def get_sync_status(task_id: str):
    """获取同步任务状态"""
    # 查找任务
    for module_name, task_info in background_tasks.items():
        if task_info["task_id"] == task_id:
            return {
                "success": True,
                "task_id": task_id,
                "module_name": module_name,
                "status": "completed" if task_info["completed"] else "running",
                "start_time": task_info["start_time"].isoformat(),
                "progress": task_info["progress"],
                "message": task_info["message"],
                "result": task_info["result"] if task_info["completed"] else None,
            }

    # 任务不存在
    raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")


@router.get(
    "/modules/{module_name}/status",
    summary="获取模块同步状态",
    description="获取指定模块的最新同步状态",
)
async def get_module_sync_status(module_name: str):
    """获取模块同步状态"""
    if module_name in background_tasks:
        task_info = background_tasks[module_name]
        return {
            "success": True,
            "module_name": module_name,
            "has_task": True,
            "task_id": task_info["task_id"],
            "status": "completed" if task_info["completed"] else "running",
            "start_time": task_info["start_time"].isoformat(),
            "progress": task_info["progress"],
            "message": task_info["message"],
        }
    else:
        return {"success": True, "module_name": module_name, "has_task": False}


async def _run_sync_with_timeout(
    module_name: str,
    record_limit: int,
    use_zero_downtime: bool,
    timeout_seconds: int,
    clear_existing_data: bool,
    task_id: str,
):
    """带超时的同步执行"""
    try:
        # 更新状态
        background_tasks[module_name]["message"] = "获取数据中"
        background_tasks[module_name]["progress"] = 10

        # 获取模块数据（带超时）
        start_time = time.time()
        try:
            # 获取数据
            data = await asyncio.wait_for(
                write_manager.ys_client.fetch_module_data(
                    module_name=module_name, limit=record_limit
                ),
                timeout=timeout_seconds,
            )

            if not data:
                background_tasks[module_name].update(
                    {
                        "completed": True,
                        "success": True,
                        "progress": 100,
                        "message": "无数据需要同步",
                        "result": {
                            "success": True,
                            "message": f"模块 {module_name} 无数据需要同步",
                            "records_processed": 0,
                            "records_written": 0,
                            "duration_seconds": time.time() - start_time,
                        },
                    }
                )
                return

            # 更新状态
            background_tasks[module_name][
                "message"
            ] = f"获取到 {len(data)} 条记录，准备写入"
            background_tasks[module_name]["progress"] = 40

            # 写入数据
            if use_zero_downtime:
                # 使用零停机写入

                async def session_factoryy():
    """TODO: Add function description."""
                    return AsyncSession(async_engine)

                writer = ZeroDowntimeDataWriter(module_name, session_factory)
                result = await writer.write_with_zero_downtime(data)
            else:
                # 使用普通写入
                result = await write_manager.write_single_module(
                    module_name=module_name,
                    record_limit=record_limit,
                    force_recreate_table=False,
                    clear_existing_data=clear_existing_data,
                )

            # 更新状态
            background_tasks[module_name].update(
                {
                    "completed": True,
                    "success": result.get("success", False),
                    "progress": 100,
                    "message": (
                        "同步完成" if result.get("success", False) else "同步失败"
                    ),
                    "result": {**result, "duration_seconds": time.time() - start_time},
                }
            )

        except asyncio.TimeoutError:
            # 超时处理
            background_tasks[module_name].update(
                {
                    "completed": True,
                    "success": False,
                    "progress": 100,
                    "message": f"同步超时（{timeout_seconds}秒）",
                    "result": {
                        "success": False,
                        "message": f"模块 {module_name} 同步超时（{timeout_seconds}秒）",
                        "error": "timeout",
                        "duration_seconds": time.time() - start_time,
                    },
                }
            )

    except Exception:
        # 异常处理
        background_tasks[module_name].update(
            {
                "completed": True,
                "success": False,
                "progress": 100,
                "message": f"同步异常: {str(e)}",
                "result": {
                    "success": False,
                    "message": f"模块 {module_name} 同步异常",
                    "error": str(e),
                    "duration_seconds": (
                        time.time() - start_time if 'start_time' in locals() else 0
                    ),
                },
            }
        )
