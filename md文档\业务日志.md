业务日志
发布时间:2025-06-30 17:45:10
可以根据此接口获取本租户日志数据，做数据分析使用。

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	动态域名，获取方式详见 获取租户所在数据中心域名
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/digitalModel/log-pub/business/rest/query
请求方式	GET
ContentType	application/json
应用场景	开放API
API类别	
事务和幂等性	无
限流次数	
60次/分钟
购买API加速包，升级至120次/分钟
立即购买
2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
content	string	query	否	日志内容
busiObjType	string	query	否	业务对象类型
busiObjCode	string	query	否	业务对象编码
busiObjName	string	query	否	业务对象名称
operator	string	query	否	操作人(id)，多个用逗号分隔
startDate	string	query	否	开始时间（时间戳）
endDate	string	query	否	结束时间（时间戳）
page	int	query	是	页码    示例: 1
size	int	query	是	每页数量    示例: 10
operNameResid	string	query	否	操作类型
3. 请求示例
Url: /yonbip/digitalModel/log-pub/business/rest/query?access_token=访问令牌&content=null&busiObjType=null&busiObjCode=null&busiObjName=null&operator=null&startDate=null&endDate=null&page=1&size=10&operNameResid=
4. 返回值参数
名称	类型	数组	描述
status	number
小数位数:2,最大长度:10	否	状态码
data	object	否	数据
number	number
小数位数:2,最大长度:10	否	页码
totalPages	number
小数位数:2,最大长度:10	否	总页数
content	object	是	内容
totalElements	number
小数位数:2,最大长度:10	否	总条数
displayCode	string	否	异常码
level	number
小数位数:2,最大长度:10	否	异常等级
5. 正确返回示例
{
	"code": "200",
	"message": "",
	"data": {
		"status": 1,
		"data": {
			"number": 1,
			"totalPages": 10000,
			"content": [
				{
					"busiObjCode": "YHT-2262631-22630211596791945093",
					"busiObjName": "黄家成",
					"operationDate": "2020-08-29T08:24:59.989+0000",
					"operResult": "success",
					"operCode": "",
					"detail": "***********在2020-08-29 16:24:59对黄家成设置管理员",
					"busiObjTypeName": "租户管理员",
					"busiObjId": "2f2c6672-87c7-44e7-a4d3-ef4b27b3597e",
					"operationName": "设置管理员",
					"busiObjTypeCode": "bd_user_manager",
					"tenantId": "s4adr3x4",
					"newBusiObj": "{\"begindate\":1598689499633}",
					"sysId": "aps",
					"operator": "other",
					"businessId": "ca2ea9e9-5835-44f2-8fdd-35a2b72bb4f1",
					"operatorName": "",
					"ip": ""
				}
			],
			"totalElements": 10000
		},
		"displayCode": "",
		"level": 0
	}
}
6. 业务异常码
查看业务异常码
异常码	异常码信息	描述

暂时没有数据哦~
7. 错误返回码
错误码	错误信息	描述
status	0	状态值
msg	查询日志失败	
errorCode	000000	
8. 错误返回示例
{
    "status": 0,
    "msg": "查询日志失败",
    "displayCode":"XXX-XXX-XXXXXX",
    "level":0,
    "errorCode": "000000"
}
9. 接口变更日志
序号	修改时间	变更内容概要
1	2025-06-30	
更新
请求说明
更新
请求参数
10个
新增
返回参数
displayCode
新增
返回参数
level