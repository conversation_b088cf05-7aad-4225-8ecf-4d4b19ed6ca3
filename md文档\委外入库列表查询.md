委外入库列表查询
发布时间:2024-01-29 16:02:59
委外入库列表查询

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	动态域名，获取方式详见 获取租户所在数据中心域名
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/osminrecord/list
请求方式	POST
ContentType	application/json
应用场景	开放API
API类别	
事务和幂等性	无
限流次数	40次/分钟

用户身份	支持传递普通用户身份，详细说明见开放平台用户认证接入规范
多语	不支持
2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
isSum	boolean	否	否	是否按照表头查询 true:表头 false:表头+明细 默认为false    默认值: false
code	string	否	否	单据编号    示例: OSMI20220317000001
pageSize	int	否	是	每页显示数据数    示例: 20    默认值: 20
pageIndex	int	否	是	当前页数    示例: 1    默认值: 1
open_vouchdate_begin	string	否	否	单据开始日期    示例: 2022-03-17 00:00:00
open_vouchdate_end	string	否	否	单据结束日期    示例: 2022-03-21 00:00:00
status	string	否	否	单据状态，0 开立 1已审核 3 审核中
simpleVOs	object	是	否	扩展查询条件
field	string	否	否	属性名(条件传属性的名称，如单据编号code、单据日期vouchdate、收货组织org.code、委外组织osmOrg.code、收票组织inInvoiceOrg.code、委外供应商vendor.code、仓库编码warehouse.code、物料编码osmInRecords.product.cCode、物料分类osmInRecords.product.manageClass.code、物料SKU编码osmInRecords.productsku.cCode等)
op	string	否	否	比较符(in:包含;eq:等于;lt:小于;gt:大于;like:模糊匹配;between:介于)
value1	string	否	否	值1(条件)
value2	string	否	否	值2(条件)
3. 请求示例
Url: /yonbip/scm/osminrecord/list?access_token=访问令牌
Body: {
	"isSum": true,
	"code": "OSMI20220317000001",
	"pageSize": 20,
	"pageIndex": 1,
	"open_vouchdate_begin": "2022-03-17 00:00:00",
	"open_vouchdate_end": "2022-03-21 00:00:00",
	"status": "",
	"simpleVOs": [
		{
			"field": "",
			"op": "",
			"value1": "",
			"value2": ""
		}
	]
}
4. 返回值参数
名称	类型	数组	描述
code	string	否	返回码，调用成功时返回200。
message	string	否	调用失败时的错误信息
data	object	否	调用成功时的返回数据
pageIndex	int	否	当前页
pageSize	int	否	每页显示数据数
pageCount	int	否	总页数
beginPageIndex	int	否	开始页
endPageIndex	int	否	结束页
recordCount	long	否	总记录数
recordList	object	是	返回结果对象
osmInRecords_productionType	int	否	产出类型
vendor_code	string	否	委外供应商编码
oriTax	BigDecimal	否	税额
pocode	string	否	委外订单编码
product_cCode	string	否	物料编码
invoiceVendor	int	否	开票供应商ID
sfee	int	否	累计结算费用
priceUOM_Precision	int	否	计价单位精度
memo	string	否	备注
stockStatusDoc_name	string	否	库存状态
priceUOM_Code	string	否	计价单位编码
totalQuantity	int	否	整单数量
natCurrency	string	否	本币ID
taxitems_name	string	否	税目名称
stockUnitId_Precision	int	否	库存单位精度
costMoney	double	否	成本金额
id	long	否	单据主表id
tplid	long	否	模板id
isWfControlled	boolean	否	是否审批流控制（true:审批流控制 false:非审批流控制）
natSum	double	否	本币含税金额
warehouse	long	否	仓库id
isAutomaticVerify	boolean	否	是否自动核销，true:是、false:否
warehouse_name	string	否	仓库
auditTime	string	否	审核时间
natCurrency_priceDigit	long	否	本币单价精度
exchRateType	string	否	汇率类型ID
billqty	long	否	累计开票数量
invExchRate	BigDecimal	否	换算率
status	long	否	单据状态，0 开立 1已审核 3 审核中
isGiftProduct	boolean	否	赠品，true:是、false:否
returncount	long	否	退回次数
verifystate	long	否	审批状态 （0：未提交 1：已提交 2：已审核）
invoicingDocEntryAndMgmt	string	否	立账开票依据
isVerification	long	否	核销标识
currency_moneyDigit	long	否	币种金额精度
warehouse_code	string	否	仓库编码
stockStatusDoc	long	否	库存状态id
productsku_cName	string	否	物料sku名称
osmOrg_name	string	否	委外组织
vouchdate	string	否	单据日期
receiptDocEntryAndMgmt	string	否	入库立账方式
natCurrency_name	string	否	本币名称
invoiceVendor_name	string	否	开票供应商
invPriceExchRate	long	否	计价换算率
vendor	long	否	委外供应商id
sqty	BigDecimal	否	累计结算数量
currency	string	否	币种ID
pubts	string	否	时间戳
smoney	BigDecimal	否	累计结算金额
org_name	string	否	收货组织
isFlowCoreBill	boolean	否	是否流程核心单据,true:是、false:否
auditDate	string	否	审核日期
creator	string	否	创建人
product	long	否	物料id
oriSum	double	否	含税金额
inInvoiceOrg_name	string	否	收票组织
exchRateType_name	string	否	汇率类型
department_name	string	否	委外部门
auditor	string	否	审核人
accountOrg	string	否	会计主体
priceQty	BigDecimal	否	计价数量
createTime	string	否	创建时间
natMoney	double	否	本币无税金额
taxitems_code	string	否	税目编码
department_code	string	否	委外部门编码
osmInRecords_osmType	long	否	委外类型
currency_priceDigit	long	否	币种单价精度
stockUnit_name	string	否	库存单位
isBeginning	boolean	否	是否期初，,true:是、false:否
bustype_name	string	否	交易类型
modifier	string	否	修改人
natTax	BigDecimal	否	本币税额
source	string	否	上游单据类型，po_subcontract_order：委外订单，po_osm_arrive_order：委外到货单
srcBill	string	否	来源单据id
subQty	BigDecimal	否	件数
modifyTime	string	否	修改时间
inInvoiceOrg	string	否	收票组织
product_cName	string	否	物料名称
vendor_name	string	否	委外供应商
oriUnitPrice	double	否	无税单价
barCode	string	否	单据条码
unit_name	string	否	计量单位
taxRate	long	否	税率
unit	long	否	单位id
productsku	long	否	物料SKUid
productsku_cCode	string	否	物料sku编码
natCurrency_moneyDigit	long	否	本币金额精度
accountOrg_name	string	否	会计主体
qty	BigDecimal	否	数量
unit_Precision	long	否	主计量精度
oriTaxUnitPrice	double	否	含税单价
oriMoney	double	否	无税金额
contactsPieces	BigDecimal	否	应收件数
contactsQuantity	BigDecimal	否	应收数量
natUnitPrice	double	否	本币无税单价
code	string	否	单据编号
exchRate	BigDecimal	否	汇率
osmInRecords_id	long	否	订单行id
priceUOM	long	否	计价单位id
department	string	否	委外部门ID
currency_name	string	否	币种名称
org	string	否	收货组织
custom	long	否	客户id
osmOrg	string	否	委外组织ID
bustype	string	否	交易类型id
costUnitPrice	double	否	成本单价
upcode	string	否	上游单据号
priceUOM_Name	string	否	计价单位名称
taxitems	string	否	税目id
natTaxUnitPrice	double	否	本币含税单价
unDeductTaxRate	BigDecimal	否	不可抵扣税率
unDeductTax	BigDecimal	否	不可抵扣税额
oriUnDeductTax	BigDecimal	否	原币不可抵扣税额
osmInRecordsCharacteristics	特征组
st.osminrecord.OsmInRecords	否	自由项特征组
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
osmInRecordsDefineCharacter	特征组
st.osminrecord.OsmInRecords	否	子表自定义项特征组
CG02	Decimal	否	检验数
CG03	Decimal	否	合格数
CG05	string	否	送货单号（单身）
WW	Date	否	委外交货日期
XS11	string	否	需求分类号test
id	string	否	特征id,主键,新增时无需填写,修改时必填
osmInRecordDefineCharacter	特征组
st.osminrecord.OsmInRecord	否	主表自定义项特征组
CG04	string	否	送货单号（单头）
id	string	否	特征id,主键,新增时无需填写,修改时必填
osmInRecords	object	否	委外入库单子表
opSn	BigDecimal	否	工序顺序号
operationId	long	否	工序
endOp	boolean	否	末序(false:否,true:是)
sourcePoOrderCode	string	否	生产订单号
sourcePoOrderProductRowno	string	否	生产订单行号
sourcePoOrderId	long	否	生产订单ID
sourcePoOrderProductId	long	否	生产订单行ID
costAccountingMethod	string	否	委外成本核算方式(0:按委外入库核算成本,1:按委外订单核算成本)
5. 正确返回示例
{
	"code": "",
	"message": "",
	"data": {
		"pageIndex": 0,
		"pageSize": 0,
		"pageCount": 0,
		"beginPageIndex": 0,
		"endPageIndex": 0,
		"recordCount": 0,
		"recordList": [
			{
				"osmInRecords_productionType": 0,
				"vendor_code": "",
				"oriTax": 0,
				"pocode": "",
				"product_cCode": "",
				"invoiceVendor": 0,
				"sfee": 0,
				"priceUOM_Precision": 0,
				"memo": "",
				"stockStatusDoc_name": "",
				"priceUOM_Code": "",
				"totalQuantity": 0,
				"natCurrency": "",
				"taxitems_name": "",
				"stockUnitId_Precision": 0,
				"costMoney": 0,
				"id": 0,
				"tplid": 0,
				"isWfControlled": true,
				"natSum": 0,
				"warehouse": 0,
				"isAutomaticVerify": true,
				"warehouse_name": "",
				"auditTime": "",
				"natCurrency_priceDigit": 0,
				"exchRateType": "",
				"billqty": 0,
				"invExchRate": 0,
				"status": 0,
				"isGiftProduct": true,
				"returncount": 0,
				"verifystate": 0,
				"invoicingDocEntryAndMgmt": "",
				"isVerification": 0,
				"currency_moneyDigit": 0,
				"warehouse_code": "",
				"stockStatusDoc": 0,
				"productsku_cName": "",
				"osmOrg_name": "",
				"vouchdate": "",
				"receiptDocEntryAndMgmt": "",
				"natCurrency_name": "",
				"invoiceVendor_name": "",
				"invPriceExchRate": 0,
				"vendor": 0,
				"sqty": 0,
				"currency": "",
				"pubts": "",
				"smoney": 0,
				"org_name": "",
				"isFlowCoreBill": true,
				"auditDate": "",
				"creator": "",
				"product": 0,
				"oriSum": 0,
				"inInvoiceOrg_name": "",
				"exchRateType_name": "",
				"department_name": "",
				"auditor": "",
				"accountOrg": "",
				"priceQty": 0,
				"createTime": "",
				"natMoney": 0,
				"taxitems_code": "",
				"department_code": "",
				"osmInRecords_osmType": 0,
				"currency_priceDigit": 0,
				"stockUnit_name": "",
				"isBeginning": true,
				"bustype_name": "",
				"modifier": "",
				"natTax": 0,
				"source": "",
				"srcBill": "",
				"subQty": 0,
				"modifyTime": "",
				"inInvoiceOrg": "",
				"product_cName": "",
				"vendor_name": "",
				"oriUnitPrice": 0,
				"barCode": "",
				"unit_name": "",
				"taxRate": 0,
				"unit": 0,
				"productsku": 0,
				"productsku_cCode": "",
				"natCurrency_moneyDigit": 0,
				"accountOrg_name": "",
				"qty": 0,
				"unit_Precision": 0,
				"oriTaxUnitPrice": 0,
				"oriMoney": 0,
				"contactsPieces": 0,
				"contactsQuantity": 0,
				"natUnitPrice": 0,
				"code": "",
				"exchRate": 0,
				"osmInRecords_id": 0,
				"priceUOM": 0,
				"department": "",
				"currency_name": "",
				"org": "",
				"custom": 0,
				"osmOrg": "",
				"bustype": "",
				"costUnitPrice": 0,
				"upcode": "",
				"priceUOM_Name": "",
				"taxitems": "",
				"natTaxUnitPrice": 0,
				"unDeductTaxRate": 0,
				"unDeductTax": 0,
				"oriUnDeductTax": 0,
				"osmInRecordsCharacteristics": {
					"XS15": "",
					"XXX0111": "",
					"id": ""
				},
				"osmInRecordsDefineCharacter": {
					"CG02": 0,
					"CG03": 0,
					"CG05": "",
					"WW": "",
					"XS11": "",
					"id": ""
				},
				"osmInRecordDefineCharacter": {
					"CG04": "",
					"id": ""
				},
				"osmInRecords": {
					"opSn": 0,
					"operationId": 0,
					"endOp": true,
					"sourcePoOrderCode": "",
					"sourcePoOrderProductRowno": "",
					"sourcePoOrderId": 0,
					"sourcePoOrderProductId": 0
				},
				"costAccountingMethod": ""
			}
		]
	}
}
6. 错误返回码
错误码	错误信息	描述
999	服务端逻辑异常	
7. 错误返回示例
{
 "code": "999",
 "message": "No enum constant org.imeta.core.base.ConditionOperator.2"
}