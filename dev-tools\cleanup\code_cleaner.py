import os
import re
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码清理工具 - 处理项目中的临时代码和调试信息
根据用户分析报告创建，专门清理console.log、TODO和deprecated代码
"""


class CodeCleaner:
    """代码清理器 - 处理临时代码和调试信息"""

    def __init___(self, project_root: str):
    """TODO: Add function description."""
    self.project_root = Path(project_root)
    self.stats = {
        "console_logs_found": 0,
        "console_logs_cleaned": 0,
        "todos_found": 0,
        "deprecated_found": 0,
        "files_processed": 0,
    }

    # 定义需要处理的模式
    self.patterns = {
        "console_log": r'console\.log\([^)]*\);?',
        "console_debug": r'console\.(debug|info|warn|error)\([^)]*\);?',
        "todo": r'//\s*TODO:?.*$',
        "deprecated": r'@deprecated.*$',
    }

    # 不需要清理的目录（第三方库等）
    self.exclude_dirs = {
        'node_modules',
        'dist',
        'build',
        '.git',
        '__pycache__',
        'vendor',
    }

    # 只处理项目自身的文件类型
    self.include_extensions =
    {'.js',
     '.html',
     '.vue',
     '.ts',
     '.jsx',
     '.tsx'
     }

    def scan_project(self) -> Dict[str, List[Dict]]:
        """扫描项目，找出所有需要清理的代码"""
        issues = {"console_logs": [], "todos": [], "deprecated": []}

        for file_path in self._get_project_files():
            self.stats["files_processed"] += 1
            issues_in_file = self._scan_file(file_path)

            for issue_type, file_issues in issues_in_file.items():
                issues[issue_type].extend(file_issues)

        return issues

    def _get_project_files(self) -> List[Path]:
        """获取需要扫描的项目文件"""
        files = []

        for root, dirs, file_list in os.walk(self.project_root):
            # 排除不需要的目录
            dirs[:] = [d for d in dirs if d not in self.exclude_dirs]

            for file_name in file_list:
                file_path = Path(root) / file_name
                if file_path.suffix in self.include_extensions:
                    files.append(file_path)

        return files

    def _scan_file(self, file_path: Path) -> Dict[str, List[Dict]]:
        """扫描单个文件"""
        issues = {"console_logs": [], "todos": [], "deprecated": []}

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for line_num, line in enumerate(lines, 1):
                # 检查console.log
                if re.search(
                        self.patterns["console_log"],
                        line,
                        re.IGNORECASE):
                    self.stats["console_logs_found"] += 1
                    issues["console_logs"].append(
                        {
                            "file": str(file_path.relative_to(self.project_root)),
                            "line": line_num,
                            "content": line.strip(),
                            "type": "console.log",
                        }
                    )

                # 检查TODO注释
                if re.search(self.patterns["todo"], line, re.IGNORECASE):
                    self.stats["todos_found"] += 1
                    issues["todos"].append(
                        {
                            "file": str(file_path.relative_to(self.project_root)),
                            "line": line_num,
                            "content": line.strip(),
                            "type": "TODO",
                        }
                    )

                # 检查deprecated标记
                if re.search(self.patterns["deprecated"], line, re.IGNORECASE):
                    self.stats["deprecated_found"] += 1
                    issues["deprecated"].append(
                        {
                            "file": str(file_path.relative_to(self.project_root)),
                            "line": line_num,
                            "content": line.strip(),
                            "type": "deprecated",
                        }
                    )

        except Exception:
            logger.info(f"警告: 无法读取文件 {file_path}: {e}")

        return issues

    def clean_console_logs(self, file_path: Path, dry_run: bool = True) -> int:
        """清理文件中的console.log语句"""
        if not file_path.exists():
            return 0

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 替换console.log为智能日志
            original_content = content

            # 针对测试文件和普通文件采用不同策略
            if 'test' in str(file_path).lower():
                # 测试文件：替换为测试专用日志
                content = re.sub(
                    r'console\.log\((.*?)\);?',
                    r'// 测试日志: \1',
                    content,
                    flags=re.MULTILINE,
                )
            else:
                # 普通文件：替换为条件日志或移除
                content = re.sub(
                    r'console\.log\((.*?)\);?',
                    r'// DEBUG: \1',
                    content,
                    flags=re.MULTILINE,
                )

            cleaned_count = len(re.findall(r'console\.log', original_content))

            if not dry_run and content != original_content:
                # 创建备份
                backup_path = file_path.with_suffix(
                    file_path.suffix + '.backup')
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)

                # 写入清理后的内容
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.stats["console_logs_cleaned"] += cleaned_count

            return cleaned_count

        except Exception:
            logger.info(f"错误: 清理文件 {file_path} 失败: {e}")
            return 0

    def generate_cleanup_report(self, issues: Dict[str, List[Dict]]) -> str:
        """生成清理报告"""
        report = []
        report.append("# 代码清理报告")
        report.append(f"生成时间: {Path(__file__).stat().st_mtime}")
        report.append(f"扫描文件数: {self.stats['files_processed']}")
        report.append("")

        # Console.log统计
        report.append("## 🐛 Console.log 调试代码")
        report.append(f"发现数量: {self.stats['console_logs_found']} 处")
        if issues["console_logs"]:
            report.append("\n### 详细位置:")
            for issue in issues["console_logs"][:20]:  # 只显示前20个
                report.append(
                    f"- `{issue['file']}:{issue['line']}` - {issue['content']}"
                )
            if len(issues["console_logs"]) > 20:
                report.append(f"- ... 还有 {len(issues['console_logs']) - 20} 处")

        # TODO注释统计
        report.append(f"\n## 📝 TODO 注释")
        report.append(f"发现数量: {self.stats['todos_found']} 处")
        if issues["todos"]:
            report.append("\n### 详细位置:")
            for issue in issues["todos"][:10]:  # 只显示前10个
                report.append(
                    f"- `{issue['file']}:{issue['line']}` - {issue['content']}"
                )

        # Deprecated标记统计
        report.append(f"\n## ⚠️ 已弃用代码")
        report.append(f"发现数量: {self.stats['deprecated_found']} 处")
        if issues["deprecated"]:
            report.append("\n### 详细位置:")
            for issue in issues["deprecated"][:10]:
                report.append(
                    f"- `{issue['file']}:{issue['line']}` - {issue['content']}"
                )

        # 建议
        report.append("\n## 💡 清理建议")
        report.append("1. **Console.log清理**: 使用专业日志框架替换console.log")
        report.append("2. **TODO处理**: 将TODO转换为具体的任务或移除已完成的项目")
        report.append("3. **Deprecated清理**: 移除或替换已弃用的代码")

        return "\n".join(report)

    def create_smart_logger_snippet(self) -> str:
        """创建智能日志代码片段"""
        return '''
// 智能日志工具 - 替换console.log的方案


class SmartLogger {
    constructor(module = 'default', enableDebug = false) {
        this.module = module;
        this.enableDebug = enableDebug || process.env.NODE_ENV === 'development';
    }

    debug(message, ...args) {
        if (this.enableDebug) {
            console.log(`[DEBUG:${this.module}]`, message, ...args);
        }
    }

    info(message, ...args) {
        console.info(`[INFO:${this.module}]`, message, ...args);
    }

    warn(message, ...args) {
        console.warn(`[WARN:${this.module}]`, message, ...args);
    }

    error(message, ...args) {
        console.error(`[ERROR:${this.module}]`, message, ...args);
    }
}

// 使用示例:
// const logger = new SmartLogger('FieldRenderer', true);
// logger.debug('字段渲染开始', fieldData);
'''


def main():
    """主函数"""
    project_root = Path(__file__).parent.parent.parent  # 回到v3目录

    logger.info("🧹 代码清理工具启动...")
    logger.info(f"📁 项目目录: {project_root}")

    cleaner = CodeCleaner(str(project_root))

    # 扫描项目
    logger.info("🔍 扫描项目中...")
    issues = cleaner.scan_project()

    # 生成报告
    report = cleaner.generate_cleanup_report(issues)

    # 保存报告
    report_file = project_root / "code-cleanup-report.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)

    logger.info(f"✅ 扫描完成！")
    logger.info(f"📊 发现 {cleaner.stats['console_logs_found']} 处 console.log")
    logger.info(f"📝 发现 {cleaner.stats['todos_found']} 处 TODO 注释")
    logger.info(f"⚠️ 发现 {cleaner.stats['deprecated_found']} 处 已弃用代码")
    logger.info(f"📄 详细报告已保存到: {report_file}")

    # 创建智能日志工具
    logger_file = project_root / "frontend" / "js" / "common" / "smart-logger.js"
    with open(logger_file, 'w', encoding='utf-8') as f:
        f.write(cleaner.create_smart_logger_snippet())
    logger.info(f"🔧 智能日志工具已创建: {logger_file}")


if __name__ == "__main__":
    main()
