import json
from pathlib import Path

import structlog

from .enhanced_json_field_matcher import EnhancedJSONFieldMatcher

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版字段配置服务 - 专注于JSON匹配
移除了翻译、MD文档等复杂逻辑
"""


logger = structlog.get_logger()


class FieldConfigService:
    """字段配置服务"""

    def __init__(self):
        """初始化服务"""
        self.base_dir = Path(__file__).parent.parent.parent.parent
        self.config_dir = self.base_dir / "config" / "field_configs"
        self.json_folder = self.base_dir / "模块字段" / "json"

        # 初始化JSON匹配器

        self.json_matcher = EnhancedJSONFieldMatcher()

        # 当前模块
        self.current_module = None

        logger.info("字段配置服务初始化完成", json_fields_count=sum(len(fields)
                                                         for fields in self.json_matcher.field_mappings.values()), )

    def set_current_module(self, module_name: str):
        """设置当前模块"""
        self.current_module = module_name

    def generate_chinese_name(self, field_name: str, field_info: Dict) -> str:
        """生成中文名称"""
        # 使用JSON匹配器获取中文名称
        json_info = self.json_matcher.match_field(
            self.current_module or "", field_name)
        if json_info and json_info.get("chinese_name"):
            return json_info["chinese_name"]

        # 返回字段名作为默认值
        return field_name

    async def extract_fields_from_api(
        self,
        module_name: str,
        api_data: Optional[List[Dict]] = None,
        record_id: Optional[str] = None,
        force_refresh: bool = False,
        max_depth: int = 10,
    ) -> Dict:
        """
        从API数据中提取字段 - 简化版

        主要流程：
        1. 如果有API数据，从API数据中提取字段结构
        2. 使用JSON匹配器提供中文名称、数据类型等信息
        3. 生成SQL Server 2012兼容的字段配置
        """
        try:
            self.set_current_module(module_name)

            logger.info(
                "开始字段提取 - 简化版",
                module_name=module_name,
                has_api_data=bool(api_data),
                max_depth=max_depth,
            )

            extracted_fields = {}

            if api_data and len(api_data) > 0:
                # 🚀 从API数据中提取字段结构
                logger.info("从API数据提取字段结构", records_count=len(api_data))

                for record in api_data:
                    record_fields = self._extract_fields_from_record(
                        record, max_depth)

                    for field_name, field_info in record_fields.items():
                        if field_name not in extracted_fields:
                            # 使用JSON匹配器获取中文名称和数据类型
                            json_info = self.json_matcher.match_field(
                                module_name, field_name
                            )

                            chinese_name = (
                                json_info.get("chinese_name", field_name)
                                if json_info
                                else field_name
                            )
                            data_type = (
                                json_info.get("data_type", "text")
                                if json_info
                                else "text"
                            )

                            extracted_fields[field_name] = {
                                "api_field_name": field_name,
                                "chinese_name": chinese_name,
                                "data_type": self._get_sql_server_type(data_type),
                                "sample_value": str(
                                    field_info.get(
                                        "sample_value",
                                        ""))[
                                    :100],
                                "source": "api_with_json_match",
                                "path": field_info.get(
                                    "path",
                                    field_name),
                                "depth": field_info.get(
                                    "depth",
                                    0),
                            }

                logger.info(
                    "API数据字段提取完成", extracted_count=len(extracted_fields)
                )

            else:
                # 🔄 备选：直接使用JSON匹配器的字段映射
                logger.info("使用JSON匹配器字段映射")

                json_fields = self.json_matcher.field_mappings.get(
                    module_name, {})

                for field_path, field_info in json_fields.items():
                    field_name = field_path.split(".")[-1]

                    # 只过滤明显的技术字段
                    if self._is_technical_field(field_name):
                        continue

                    extracted_fields[field_name] = {
                        "api_field_name": field_name,
                        "chinese_name": field_info.get(
                            "chinese_name",
                            field_name),
                        "data_type": self._get_sql_server_type(
                            field_info.get(
                                "data_type",
                                "text")),
                        "sample_value": "",
                        "source": "json_matcher",
                        "path": field_path,
                        "depth": field_path.count("."),
                    }

                logger.info(
                    "JSON匹配器字段提取完成", extracted_count=len(extracted_fields)
                )

            return extracted_fields

        except Exception:
            logger.error("字段提取失败", module_name=module_name, error=str(e))
            return {}

    def _extract_fields_from_record(
        self,
        record: Any,
        max_depth: int,
        current_path: str = "",
        current_depth: int = 0,
    ) -> Dict[str, Dict]:
        """从API记录中提取字段信息"""
        fields = {}

        if current_depth >= max_depth:
            return fields

        if isinstance(record, dict):
            for key, value in record.items():
                field_path = f"{current_path}.{key}" if current_path else key

                if self._is_technical_field(key):
                    continue

                if isinstance(value, dict) and current_depth < max_depth:
                    nested_fields = self._extract_fields_from_record(
                        value, max_depth, field_path, current_depth + 1
                    )
                    fields.update(nested_fields)
                elif (
                    isinstance(value, list)
                    and len(value) > 0
                    and current_depth < max_depth
                ):
                    if isinstance(value[0], dict):
                        array_fields = self._extract_fields_from_record(
                            value[0], max_depth, field_path, current_depth + 1
                        )
                        fields.update(array_fields)
                    else:
                        fields[key] = {
                            "sample_value": (
                                str(value[0])[:100] if value[0] is not None else ""
                            ),
                            "path": field_path,
                            "depth": current_depth,
                        }
                else:
                    fields[key] = {
                        "sample_value": str(value)[
                            :100] if value is not None else "",
                        "path": field_path,
                        "depth": current_depth,
                    }

        elif isinstance(record, list) and len(record) > 0:
            if isinstance(record[0], dict):
                fields.update(
                    self._extract_fields_from_record(
                        record[0], max_depth, current_path, current_depth
                    )
                )

        return fields

    def _is_technical_field(self, field_name: str) -> bool:
        """判断是否为技术字段"""
        field_lower = field_name.lower()
        technical_keywords = [
            "ytenant",
            "tenant_id",
            "hash",
            "token",
            "session",
            "cache",
            "temp",
            "tmp",
            "debug",
            "test",
            "mock",
            "version",
            "build",
            "commit",
            "__",
        ]
        return any(keyword in field_lower for keyword in technical_keywords)

    def _get_sql_server_type(self, data_type: str) -> str:
        """获取SQL Server 2012兼容的数据类型"""
        type_mapping = {
            "text": "NVARCHAR(500)",
            "string": "NVARCHAR(500)",
            "number": "DECIMAL(18,4)",
            "integer": "BIGINT",
            "boolean": "BIT",
            "date": "DATE",
            "datetime": "DATETIME",  # SQL Server 2012兼容
            "object": "NVARCHAR(MAX)",
            "array": "NVARCHAR(MAX)",
        }
        return type_mapping.get(data_type.lower(), "NVARCHAR(500)")

    async def generate_field_config(
        self,
        module_name: str,
        max_depth: int = 100,
        query_field: Optional[str] = None,
        query_value: Optional[str] = None,
    ) -> Dict:
        """
        生成字段配置 - 简化版，真正无限制深度
        """
        try:
            logger.info(
                "开始生成字段配置 - 简化版",
                module_name=module_name,
                max_depth=max_depth,
            )

            # 提取字段，使用真正无限制深度
            extracted_fields = await self.extract_fields_from_api(
                module_name, max_depth=max_depth
            )

            if not extracted_fields:
                logger.warning("未提取到任何字段", module_name=module_name)
                return {
                    "fields": {},
                    "module_name": module_name,
                    "display_name": module_name,
                }

            # 生成配置
            config = {
                "module_name": module_name,
                "display_name": self._get_module_display_name(module_name),
                "fields": {},
            }

            for field_name, field_info in extracted_fields.items():
                config["fields"][field_name] = {
                    "api_field_name": field_name,
                    "chinese_name": field_info.get("chinese_name", field_name),
                    "data_type": field_info.get("data_type", "NVARCHAR(500)"),
                    "sample_value": field_info.get("sample_value", ""),
                    "etl_score": 0,
                    "config_name": field_info.get("chinese_name", field_name),
                    "is_selected": True,  # 默认选中
                    "locked": False,
                    "user_modified": False,
                }

            logger.info(
                "字段配置生成完成",
                module_name=module_name,
                fields_count=len(config["fields"]),
            )

            return config

        except Exception:
            logger.error("字段配置生成失败", module_name=module_name, error=str(e))
            return {
                "fields": {},
                "module_name": module_name,
                "display_name": module_name,
            }

    def _get_module_display_name(self, module_name: str) -> str:
        """获取模块显示名称"""
        display_names = {
            "sales_order": "销售订单",
            "purchase_order": "采购订单",
            "production_order": "生产订单",
            "subcontract_order": "委外订单",
            "material_master": "物料档案",
            "purchase_receipt": "采购入库",
            "sales_out": "销售出库",
            "product_receipt": "产品入库",
            "materialout": "材料出库",
            "subcontract_receipt": "委外入库",
            "subcontract_requisition": "委外申请",
            "applyorder": "请购单",
            "inventory_report": "现存量报表",
            "requirements_planning": "需求计划",
        }
        return display_names.get(module_name, module_name)

    async def save_module_config(self, module_name: str, config: Dict):
        """保存模块配置"""
        try:
            config_file = self.config_dir / f"field_config_{module_name}.json"

            # 确保目录存在
            config_file.parent.mkdir(parents=True, exist_ok=True)

            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            logger.info(
                "模块配置保存成功",
                module_name=module_name,
                config_file=str(config_file),
                fields_count=len(config.get("fields", {})),
            )

        except Exception:
            logger.error("模块配置保存失败", module_name=module_name, error=str(e))

    async def load_module_config(self, module_name: str) -> Optional[Dict]:
        """加载模块配置"""
        try:
            config_file = self.config_dir / f"field_config_{module_name}.json"

            if not config_file.exists():
                return None

            with open(config_file, "r", encoding="utf-8") as f:
                config = json.load(f)

            logger.info(
                "模块配置加载成功",
                module_name=module_name,
                fields_count=len(config.get("fields", {})),
            )

            return config

        except Exception:
            logger.error("模块配置加载失败", module_name=module_name, error=str(e))
            return None
