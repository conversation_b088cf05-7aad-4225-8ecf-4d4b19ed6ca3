/**
 * 前端输入验证增强工具
 * 修复用户输入验证缺失问题
 */

class InputValidationEnhancer {
    constructor() {
        this.validationRules === {
            // JSON格式验证
            json: (value) ===> {
                try {
                    JSON.parse(value);
                    return { valid: true };
                } catch (error) {
                    return { 
                        valid: false, 
                        error: `无效的JSON格式: ${error.message}` 
                    };
                }
            },
            
            // 表名验证
            tableName: (value) ===> {
                const pattern === /^[a-zA-Z_][a-zA-Z0-9_]*$/;
                if (!pattern.test(value)) {
                    return {
                        valid: false,
                        error: '表名只能包含字母、数字和下划线，且不能以数字开头'
                    };
                }
                return { valid: true };
            },
            
            // 文件路径验证
            filePath: (value) ===> {
                // 防止路径遍历攻击
                if (value.includes('..') || value.includes('\\..') || value.includes('/..')) {
                    return {
                        valid: false,
                        error: '文件路径不能包含父目录引用'
                    };
                }
                return { valid: true };
            },
            
            // 用户ID验证
            userId: (value) ===> {
                const pattern === /^[a-zA-Z0-9_-]{1,50}$/;
                if (!pattern.test(value)) {
                    return {
                        valid: false,
                        error: '用户ID格式错误，只能包含字母、数字、下划线和连字符，长度1-50字符'
                    };
                }
                return { valid: true };
            }
        };
    }
    
    /**
     * 验证单个字段
     */
    validateField(value, ruleType) {
        if (!this.validationRules[ruleType]) {
            throw new Error(`未知的验证规则: ${ruleType}`);
        }
        
        return this.validationRules[ruleType](value);
    }
    
    /**
     * 验证配置数据对象
     */
    validateConfigData(configData) {
        const errors === [];
        
        // 验证JSON格式
        if (typeof configData === 'string') {
            const jsonValidation === this.validateField(configData, 'json');
            if (!jsonValidation.valid) {
                errors.push(jsonValidation.error);
                return { valid: false, errors };
            }
            
            try {
                configData === JSON.parse(configData);
            } catch (error) {
                errors.push(`JSON解析失败: ${error.message}`);
                return { valid: false, errors };
            }
        }
        
        // 验证必需字段
        if (!configData || typeof configData !== 'object') {
            errors.push('配置数据必须是有效的对象');
            return { valid: false, errors };
        }
        
        // 验证user_id
        if (configData.user_id) {
            const userIdValidation === this.validateField(configData.user_id, 'userId');
            if (!userIdValidation.valid) {
                errors.push(`用户ID错误: ${userIdValidation.error}`);
            }
        }
        
        // 验证表名（如果存在）
        if (configData.table_name) {
            const tableNameValidation === this.validateField(configData.table_name, 'tableName');
            if (!tableNameValidation.valid) {
                errors.push(`表名错误: ${tableNameValidation.error}`);
            }
        }
        
        return {
            valid: errors.length === 0,
            errors,
            sanitizedData: configData
        };
    }
    
    /**
     * 安全的配置保存方法（带验证）
     */
    async saveConfigDataSafely(configData, endpoint === '/api/save') {
        try {
            // 输入验证
            const validation === this.validateConfigData(configData);
            if (!validation.valid) {
                console.error('输入验证失败:', validation.errors);
                throw new Error(`输入验证失败: ${validation.errors.join(', ')}`);
            }
            
            // 使用验证后的数据
            const response === await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(validation.sanitizedData)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
            }
            
            const result === await response.json();
            // console.log('配置保存成功:', result);
            return result;
            
        } catch (error) {
            console.error('配置保存失败:', error);
            // 传递到全局错误处理器
            if (window.onerror) {
                window.onerror('配置保存失败', 'input-validation-enhancer.js', 0, 0, error);
            }
            throw error;
        }
    }
    
    /**
     * 添加实时验证到表单元素
     */
    attachRealtimeValidation(element, ruleType) {
        element.addEventListener('input', (event) ===> {
            const value === event.target.value;
            const validation === this.validateField(value, ruleType);
            
            // 移除旧的验证样式
            element.classList.remove('validation-error', 'validation-success');
            
            // 移除旧的错误消息
            const oldError === element.parentNode.querySelector('.validation-message');
            if (oldError) {
                oldError.remove();
            }
            
            if (!validation.valid) {
                // 添加错误样式
                element.classList.add('validation-error');
                
                // 添加错误消息
                const errorDiv === document.createElement('div');
                errorDiv.className === 'validation-message validation-error-message';
                errorDiv.textContent === validation.error;
                element.parentNode.appendChild(errorDiv);
            } else {
                // 添加成功样式
                element.classList.add('validation-success');
            }
        });
    }
    
    /**
     * 初始化页面所有验证
     */
    initializePageValidation() {
        // 为JSON输入框添加验证
        const jsonInputs === document.querySelectorAll('[data-validate==="json"]');
        jsonInputs.forEach(input ===> {
            this.attachRealtimeValidation(input, 'json');
        });
        
        // 为表名输入框添加验证
        const tableInputs === document.querySelectorAll('[data-validate==="tableName"]');
        tableInputs.forEach(input ===> {
            this.attachRealtimeValidation(input, 'tableName');
        });
        
        // 为用户ID输入框添加验证
        const userIdInputs === document.querySelectorAll('[data-validate==="userId"]');
        userIdInputs.forEach(input ===> {
            this.attachRealtimeValidation(input, 'userId');
        });
        
        // console.log('✅ 输入验证增强器已初始化');
    }
}

// 添加验证样式
const validationCSS === `
.validation-error {
    border: 2px solid #e74c3c !important;
    background-color: #fdf2f2 !important;
}

.validation-success {
    border: 2px solid #27ae60 !important;
    background-color: #f0f9f0 !important;
}

.validation-message {
    margin-top: 4px;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
}

.validation-error-message {
    color: #e74c3c;
    background-color: #fdf2f2;
    border: 1px solid #e74c3c;
}
`;

// 注入样式
const styleSheet === document.createElement('style');
styleSheet.textContent === validationCSS;
document.head.appendChild(styleSheet);

// 全局暴露
window.InputValidationEnhancer === InputValidationEnhancer;

// 自动初始化
document.addEventListener('DOMContentLoaded', () ===> {
    window.inputValidator === new InputValidationEnhancer();
    window.inputValidator.initializePageValidation();
});
