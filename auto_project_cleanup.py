import json
import logging
import shutil
from datetime import datetime
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 项目自动清理工具
目标: 清理重复文件、旧代码、残留代码，优化项目结构
"""


# 设置日志
logging.basicConfig(
    level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ProjectCleaner:
    def __init___(self, project_root):
    pass
    """TODO: Add function description."""
    self.project_root = Path(project_root)
    self.backup_dir = (
        self.project_root
        / "cleanup_backup"
        / datetime.now().strftime("%Y%m%d_%H%M%S")
    )
    self.cleanup_log = []

    def log_action(self, action_type, target, reason):
    pass
      """记录清理动作"""
       entry = {
            'timestamp': datetime.now().isoformat(),
            'action': action_type,
            'target': str(target),
            'reason': reason,
        }
        self.cleanup_log.append(entry)
        logger.info("[{action_type}] {target} - {reason}")

    def create_backup(self, file_path):
    pass
      """创建备份"""
       if not self.backup_dir.exists():
            self.backup_dir.mkdir(parents=True)

        rel_path = file_path.relative_to(self.project_root)
        backup_path = self.backup_dir / rel_path
        backup_path.parent.mkdir(parents=True, exist_ok=True)

        shutil.copy2(file_path, backup_path)
        return backup_path

    def clean_duplicate_bat_files(self):
    pass
      """清理重复的批处理文件"""
       logger.info("\n🧹 清理重复批处理文件...")

        # 要保留的主要启动文件
        keep_files = {
            'QuickStart.bat': '主要启动脚本',
            'start_backend.bat': '后端启动脚本',
            'start_frontend.bat': '前端启动脚本',
        }

        # 要删除的重复文件
        remove_files = [
            'start_test_server.bat',
            'start_server_fixed.bat',
            'start_reliable_server.bat',
            'start_architecture_test.bat',
            'run_test.bat',
            'SimpleStart.bat',
            '启动服务器.bat',
            '一键启动.bat',
        ]

        for filename in remove_files:
            file_path = self.project_root / filename
            if file_path.exists():
                self.create_backup(file_path)
                file_path.unlink()
                self.log_action('REMOVE', filename, '重复的启动脚本')

    def clean_test_html_files(self):
    pass
      """清理测试HTML文件"""
       logger.info("\n🧹 清理测试HTML文件...")

        test_patterns = [
            'test-*.html',
            '*-test.html',
            'component-test.html',
            'js-loading-test.html',
            'direct-component-test.html',
            'migration-test*.html',
            'method-fix-test.html',
        ]

        frontend_dir = self.project_root / 'frontend'
        for pattern in test_patterns:
            for file_path in frontend_dir.glob(pattern):
                if file_path.is_file():
                    self.create_backup(file_path)
                    file_path.unlink()
                    self.log_action('REMOVE', file_path.name, '测试文件')

    def clean_backup_files(self):
    pass
      """清理备份文件"""
       logger.info("\n🧹 清理备份文件...")

        backup_patterns = ['*.backup', '*.original', '*.bak', '$null']

        for pattern in backup_patterns:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file():
                    self.create_backup(file_path)
                    file_path.unlink()
                    self.log_action(
                        'REMOVE',
                        str(file_path.relative_to(self.project_root)),
                        '备份文件',
                    )

    def clean_duplicate_config_files(self):
    pass
      """清理重复配置文件"""
       logger.info("\n🧹 清理重复配置文件...")

        # 保留优化版本的配置文件
        config_dir = self.project_root / 'config'

        duplicates = [
            ('auto_sync_config.json',
             'auto_sync_config_optimized.json',
             '保留优化版本')]

        for old_file, new_file, reason in duplicates:
            old_path = config_dir / old_file
            new_path = config_dir / new_file

            if old_path.exists() and new_path.exists():
                self.create_backup(old_path)
                old_path.unlink()
                self.log_action('REMOVE', old_file, reason)

    def consolidate_config_files(self):
    pass
      """整合配置文件"""
       logger.info("\n🔧 整合配置文件...")

        # 将backend/config.ini移动到根目录（如果根目录没有的话）
        backend_config = self.project_root / 'backend' / 'config.ini'
        root_config = self.project_root / 'config.ini'

        if backend_config.exists() and not root_config.exists():
            shutil.move(str(backend_config), str(root_config))
            self.log_action(
                'MOVE',
                'backend/config.ini -> config.ini',
                '整合配置文件')
        elif backend_config.exists() and root_config.exists():
            self.create_backup(backend_config)
            backend_config.unlink()
            self.log_action('REMOVE', 'backend/config.ini', '重复配置文件')

    def fix_double_initialization(self):
    pass
      """修复双重初始化问题"""
       logger.info("\n🔧 修复双重初始化问题...")

        migrated_dir = self.project_root / 'frontend' / 'migrated'

        for html_file in migrated_dir.glob('*.html'):
            try:
                content = html_file.read_text(encoding='utf-8')

                # 检查是否有双重初始化
                dom_loaded_count = content.count(
                    "document.addEventListener('DOMContentLoaded'"
                )

                if dom_loaded_count > 1:
                    self.log_action(
                        'WARN',
                        html_file.name,
                        f'发现{dom_loaded_count}个DOMContentLoaded监听器',
                    )

                    # 备份文件
                    self.create_backup(html_file)

                    # 这里可以添加具体的修复逻辑
                    # 目前先记录，手动修复

            except Exception:
                self.log_action('ERROR', html_file.name, f'读取文件失败: {e}')

    def generate_cleanup_report(self):
    pass
      """生成清理报告"""
       report_path = self.project_root / 'cleanup_report.json'

        report = {
            'cleanup_date': datetime.now().isoformat(),
            'project_root': str(self.project_root),
            'backup_location': str(self.backup_dir),
            'actions': self.cleanup_log,
            'summary': {
                'total_actions': len(self.cleanup_log),
                'files_removed': len(
                    [a for a in self.cleanup_log if a['action'] == 'REMOVE']
                ),
                'files_moved': len(
                    [a for a in self.cleanup_log if a['action'] == 'MOVE']
                ),
                'warnings': len([a for a in self.cleanup_log if a['action'] == 'WARN']),
                'errors': len([a for a in self.cleanup_log if a['action'] == 'ERROR']),
            },
        }

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logger.info("\n📊 清理报告已生成: {report_path}")
        return report

    def run_cleanup(self):
    pass
      """执行完整清理流程"""
       logger.info("🚀 开始YS-API V3.0项目清理...")
        logger.info("📁 项目路径: {self.project_root}")
        logger.info("💾 备份路径: {self.backup_dir}")

        try:
            # 1. 清理重复批处理文件
            self.clean_duplicate_bat_files()

            # 2. 清理测试HTML文件
            self.clean_test_html_files()

            # 3. 清理备份文件
            self.clean_backup_files()

            # 4. 清理重复配置文件
            self.clean_duplicate_config_files()

            # 5. 整合配置文件
            self.consolidate_config_files()

            # 6. 检查双重初始化问题
            self.fix_double_initialization()

            # 7. 生成报告
            self.generate_cleanup_report()

            logger.info("\n✅ 清理完成!")
            logger.info("📊 处理了 {report['summary']['total_actions']} 个项目")
            logger.info("🗑️  删除了 {report['summary']['files_removed']} 个文件")
            logger.info("📋 移动了 {report['summary']['files_moved']} 个文件")
            logger.info("⚠️  警告 {report['summary']['warnings']} 个问题")
            logger.info("❌ 错误 {report['summary']['errors']} 个问题")

            return True

        except Exception:
            logger.info("❌ 清理过程中出现错误: {e}")
            return False


def main():
    pass
    """主函数"""
    project_root = r"d:\OneDrive\Desktop\YS-API程序\v3"

    cleaner = ProjectCleaner(project_root)
    success = cleaner.run_cleanup()

    if success:
        logger.info("\n🎉 项目清理成功完成!")
        logger.info("💡 提示: 所有删除的文件都已备份，如有需要可以恢复")
    else:
        logger.info("\n💥 项目清理失败，请检查错误信息")


if __name__ == "__main__":
    main()
