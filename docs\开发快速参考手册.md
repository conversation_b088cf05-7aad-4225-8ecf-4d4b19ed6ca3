# YS-API V3.0 开发快速参考手册

> **🚀 目的**: 避免开发过程中忘记流程导致混乱  
> **📖 使用**: 开发时打开此文档对照检查

---

## 🎯 核心流程速查

### 五步数据流程 ⭐⭐⭐⭐⭐
```
API原始数据 → 基准文件 → JSON增强 → 用户配置 → 数据库表 → ETL写入
```

| 步骤 | 输入 | 输出 | 关键原则 |
|------|------|------|----------|
| 1️⃣ | 用友云API | `baselines/` | **不修改原始数据** |
| 2️⃣ | 基准文件+JSON字典 | 增强元数据 | **多策略匹配** |
| 3️⃣ | 增强数据 | `user_configs/` | **智能分类过滤** |
| 4️⃣ | 用户配置 | 数据库表 | **只用is_required=true** |
| 5️⃣ | API数据+配置 | 业务数据 | **类型转换+清洗** |

---

## 📁 目录对照表

```
v3/
├── config/baselines/           ← 步骤1: API原始数据存储
├── config/user_configs/        ← 步骤3: 用户字段选择
├── 模块字段/json/              ← 步骤2: 中文名称字典
├── month2_database/            ← 步骤4: 数据库表创建
└── backend/app/services/       ← 步骤5: ETL处理服务
```

---

## 🔧 关键服务类快查

| 功能 | 服务类 | 文件位置 |
|------|--------|----------|
| 字段提取 | `FieldExtractor` | `services/field_extractor.py` |
| JSON匹配 | `EnhancedJSONFieldMatcher` | `services/enhanced_json_field_matcher.py` |
| 智能映射 | `IntelligentFieldMapper` | `services/intelligent_field_mapper.py` |
| 表创建 | `TableCreationManager` | `month2_database/table_creation/manager.py` |
| 数据写入 | `DataWriteManager` | `services/data_write_manager.py` |

---

## ⚡ 开发检查点

### 🟢 步骤1检查 - 基准文件生成
```python
# ✅ 正确：保持原始结构
{
  "api_field_name": "product_cCode",
  "path": "data.recordList.product_cCode",
  "depth": 2,
  "is_array": false
}

# ❌ 错误：不要修改字段名或结构
{
  "field_name": "产品编码",  # 错误：不要翻译
  "simplified_path": "product_cCode"  # 错误：不要简化路径
}
```

### 🟡 步骤2检查 - JSON匹配增强
```python
# ✅ 正确：获取中文名称和类型
field_info = {
  "chinese_name": "物料编码",
  "data_type": "NVARCHAR(500)",
  "param_desc": "物料编码",
  "max_length": 500
}

# ❌ 错误：不要硬编码或猜测
field_info = {
  "chinese_name": "product_cCode",  # 错误：未匹配到中文
  "data_type": "string"  # 错误：未标准化类型
}
```

### 🔵 步骤3检查 - 智能字段分类
```python
# ✅ 正确：技术字段排除
TECHNICAL_FIELDS = ['pageIndex', 'pageSize', 'message', 'data']

# ✅ 正确：重要性分级
if field_name in ['id', 'code', '*_id', '*_code']:
    importance = 'critical'
    is_required = True
elif field_name in ['*money*', '*qty*', '*amount*']:
    importance = 'high'
    is_required = True
```

### 🟣 步骤4检查 - 数据库表创建
```sql
-- ✅ 正确：使用中文列名，只包含is_required=true字段
CREATE TABLE 采购订单 (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    单据编码 TEXT NOT NULL,  -- is_required=true
    物料编码 TEXT NOT NULL   -- is_required=true
);

-- ❌ 错误：使用英文名或包含is_required=false字段
CREATE TABLE purchase_order (
    product_cCode TEXT,     -- 错误：英文字段名
    description TEXT        -- 错误：is_required=false字段
);
```

### 🟠 步骤5检查 - ETL数据处理
```python
# ✅ 正确：字段过滤和类型转换
def process_data(api_data, user_config):
    result = {}
    for field, config in user_config['fields'].items():
        if config.get('is_required', False):  # 只处理必需字段
            value = extract_field_value(api_data, config['path'])
            result[config['chinese_name']] = convert_type(value, config['data_type'])
    return result

# ❌ 错误：处理所有字段或忽略类型转换
def process_data(api_data, user_config):
    return api_data  # 错误：未过滤和转换
```

---

## 🚨 常见错误预防

### ❌ 容易出错的地方

1. **基准文件修改原始数据**
   ```python
   # 错误示例
   field_data['chinese_name'] = translate(field_data['api_field_name'])
   ```

2. **用户配置包含技术字段**
   ```json
   // 错误示例
   {
     "pageIndex": {"is_required": true}  // 技术字段不应该出现
   }
   ```

3. **数据库表使用英文字段名**
   ```sql
   -- 错误示例  
   CREATE TABLE table1 (
     product_cCode TEXT  -- 应该用中文"物料编码"
   );
   ```

4. **ETL处理is_required=false字段**
   ```python
   # 错误示例
   for field in all_fields:  # 应该只处理is_required=true
     process_field(field)
   ```

### ✅ 正确做法

1. **基准文件保持原样**
   ```python
   # 正确示例
   baseline_data = api_response  # 直接保存，不做修改
   ```

2. **智能排除技术字段**
   ```python
   # 正确示例
   if field_name in TECHNICAL_FIELDS:
     continue  # 跳过技术字段
   ```

3. **使用中文字段名建表**
   ```sql
   -- 正确示例
   CREATE TABLE 采购订单 (
     物料编码 TEXT NOT NULL
   );
   ```

4. **ETL只处理必需字段**
   ```python
   # 正确示例
   if config.get('is_required', False):
     process_field(field, config)
   ```

---

## 🎯 调试技巧

### 🔍 数据流追踪
```python
# 在关键步骤添加日志
logger.info(f"步骤1-基准文件: {len(baseline_fields)} 个字段")
logger.info(f"步骤2-JSON增强: {enhanced_count} 个字段获得中文名")
logger.info(f"步骤3-用户配置: {required_count} 个必需字段")
logger.info(f"步骤4-数据库表: {table_columns} 个列")
logger.info(f"步骤5-ETL处理: {processed_records} 条记录")
```

### 🔧 配置验证
```python
# 验证用户配置完整性
def validate_user_config(config):
    assert 'fields' in config
    for field, field_config in config['fields'].items():
        assert 'chinese_name' in field_config
        assert 'data_type' in field_config
        assert 'is_required' in field_config
```

### 📊 数据质量检查
```python
# 检查数据转换质量
def check_data_quality(original, processed):
    assert len(processed) <= len(original)  # 处理后数据不应增加
    for field in processed:
        assert field in user_config_chinese_names  # 必须是中文字段名
```

---

## 📞 问题求助

### 🆘 遇到问题时的检查顺序

1. **查看日志**: 检查每个步骤的处理日志
2. **对照规范**: 参考[数据流程标准规范.md](./数据流程标准规范.md)
3. **验证配置**: 检查相关配置文件格式和内容
4. **数据追踪**: 从源头追踪数据流转过程
5. **单元测试**: 编写测试验证单个组件功能

### 📋 问题报告模板
```
【问题描述】: 简述问题现象
【出现步骤】: 在哪个步骤出现问题(1-5)
【错误日志】: 贴出相关错误信息
【输入数据】: 提供输入数据样例
【期望结果】: 说明期望的正确结果
【已尝试方案】: 列出已经尝试的解决方案
```

---

## 🏁 开发完成检查

### ✅ 提交代码前的最终检查

- [ ] 基准文件保持API原始结构
- [ ] JSON匹配器正确获取中文名称
- [ ] 技术字段被正确排除
- [ ] 用户配置只包含业务字段
- [ ] 数据库表使用中文列名
- [ ] ETL只处理is_required=true字段
- [ ] 所有步骤有详细日志
- [ ] 错误处理机制完善
- [ ] 单元测试覆盖核心功能
- [ ] 文档更新到最新版本

---

> **💡 提示**: 开发时建议将此文档置顶打开，随时对照检查，避免偏离标准流程。
