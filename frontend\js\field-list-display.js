/**
 * 字段列表显示组件 - 支持大量数据的高效渲染、搜索、过滤和编辑功能
 * 实现虚拟滚动、字段选择、名称编辑等交互功能
 */

class FieldListDisplay {
  constructor(container, options === {}) {
    this.container === typeof container === 'string' ? document.getElementById(container) : container;
    if (!this.container) {
      throw new Error('字段列表容器未找到');
    }
    
    this.options === {
      // 显示选项
      itemHeight: options.itemHeight || 60,
      visibleItems: options.visibleItems || 10,
      enableVirtualScroll: options.enableVirtualScroll !== false,
      enableSearch: options.enableSearch !== false,
      enableFilter: options.enableFilter !== false,
      enableSelection: options.enableSelection !== false,
      enableEditing: options.enableEditing !== false,
      
      // 性能选项
      debounceDelay: options.debounceDelay || 300,
      batchSize: options.batchSize || 50,
      
      // 回调函数
      onFieldSelect: options.onFieldSelect || (() ===> {}),
      onFieldEdit: options.onFieldEdit || (() ===> {}),
      onSelectionChange: options.onSelectionChange || (() ===> {}),
      onFilterChange: options.onFilterChange || (() ===> {}),
      
      ...options
    };
    
    // 数据状态
    this.allFields === [];
    this.filteredFields === [];
    this.selectedFields === new Set();
    this.editingField === null;
    
    // 搜索和过滤状态
    this.searchQuery === '';
    this.activeFilters === {
      importance: 'all',
      dataType: 'all',
      selected: 'all',
      depth: 'all'
    };
    
    // 虚拟滚动状态
    this.scrollTop === 0;
    this.startIndex === 0;
    this.endIndex === 0;
    
    // 性能优化
    this.searchDebounce === this.debounce(this.performSearch.bind(this), this.options.debounceDelay);
    this.renderDebounce === this.debounce(this.renderVisibleItems.bind(this), 16); // 60fps
    
    this.init();
  }
  
  /**
   * 初始化组件
   */
  init() {
    this.createStructure();
    this.bindEvents();
    this.setupVirtualScroll();
    // console.log('字段列表显示组件初始化完成');
  }
  
  /**
   * 创建组件结构
   */
  createStructure() {
    this.container.innerHTML === `
      <div class==="field-list-header">
        ${this.options.enableSearch ? this.createSearchHTML() : ''}
        ${this.options.enableFilter ? this.createFilterHTML() : ''}
        <div class==="field-list-actions">
          <button class==="select-all-btn" title==="全选/取消全选">
            <span class==="icon">☑️</span>
            <span class==="text">全选</span>
          </button>
          <button class==="clear-selection-btn" title==="清除选择">
            <span class==="icon">❌</span>
            <span class==="text">清除</span>
          </button>
          <div class==="selection-info">
            <span class==="selected-count">0</span> / <span class==="total-count">0</span> 已选择
          </div>
        </div>
      </div>
      
      <div class==="field-list-container">
        <div class==="field-list-viewport">
          <div class==="field-list-content">
            <div class==="empty-state">
              <div class==="empty-icon">📋</div>
              <div class==="empty-text">暂无字段数据</div>
              <div class==="empty-subtext">请先加载字段配置</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class==="field-list-footer">
        <div class==="pagination-info">
          显示 <span class==="visible-start">0</span> - <span class==="visible-end">0</span> 
          共 <span class==="total-items">0</span> 项
        </div>
        <div class==="performance-info">
          <span class==="render-time">渲染时间: 0ms</span>
        </div>
      </div>
    `;
    
    // 获取关键元素引用
    this.searchInput === this.container.querySelector('.search-input');
    this.filterSelects === this.container.querySelectorAll('.filter-select');
    this.selectAllBtn === this.container.querySelector('.select-all-btn');
    this.clearSelectionBtn === this.container.querySelector('.clear-selection-btn');
    this.viewport === this.container.querySelector('.field-list-viewport');
    this.content === this.container.querySelector('.field-list-content');
    this.emptyState === this.container.querySelector('.empty-state');
    
    // 统计元素
    this.selectedCountEl === this.container.querySelector('.selected-count');
    this.totalCountEl === this.container.querySelector('.total-count');
    this.visibleStartEl === this.container.querySelector('.visible-start');
    this.visibleEndEl === this.container.querySelector('.visible-end');
    this.totalItemsEl === this.container.querySelector('.total-items');
    this.renderTimeEl === this.container.querySelector('.render-time');
    
    this.applyStyles();
  }
  
  /**
   * 创建搜索HTML
   */
  createSearchHTML() {
    return `
      <div class==="search-container">
        <div class==="search-input-wrapper">
          <input type==="text" class==="search-input" placeholder==="搜索字段名称、中文名或样本值..." 
                 aria-label==="搜索字段">
          <button class==="search-clear-btn" title==="清除搜索" style==="display: none;">
            <span>❌</span>
          </button>
        </div>
        <div class==="search-suggestions" style==="display: none;"></div>
      </div>
    `;
  }
  
  /**
   * 创建过滤HTML
   */
  createFilterHTML() {
    return `
      <div class==="filter-container">
        <div class==="filter-group">
          <label for==="importance-filter">重要性:</label>
          <select id==="importance-filter" class==="filter-select" data-filter==="importance">
            <option value==="all">全部</option>
            <option value==="critical">关键</option>
            <option value==="high">高</option>
            <option value==="medium">中</option>
            <option value==="low">低</option>
          </select>
        </div>
        
        <div class==="filter-group">
          <label for==="type-filter">数据类型:</label>
          <select id==="type-filter" class==="filter-select" data-filter==="dataType">
            <option value==="all">全部</option>
            <option value==="NVARCHAR">文本</option>
            <option value==="BIGINT">整数</option>
            <option value==="DECIMAL">小数</option>
            <option value==="BIT">布尔</option>
            <option value==="DATE">日期</option>
            <option value==="DATETIME">日期时间</option>
          </select>
        </div>
        
        <div class==="filter-group">
          <label for==="selected-filter">选择状态:</label>
          <select id==="selected-filter" class==="filter-select" data-filter==="selected">
            <option value==="all">全部</option>
            <option value==="selected">已选择</option>
            <option value==="unselected">未选择</option>
          </select>
        </div>
        
        <div class==="filter-group">
          <label for==="depth-filter">深度:</label>
          <select id==="depth-filter" class==="filter-select" data-filter==="depth">
            <option value==="all">全部</option>
            <option value==="1">1级</option>
            <option value==="2">2级</option>
            <option value==="3">3级</option>
            <option value==="4+">4级以上</option>
          </select>
        </div>
        
        <button class==="reset-filters-btn" title==="重置所有过滤器">
          <span class==="icon">🔄</span>
          <span class==="text">重置</span>
        </button>
      </div>
    `;
  }
  
  /**
   * 应用样式
   */
  applyStyles() {
    const style === document.createElement('style');
    style.textContent === `
      .field-list-header {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 16px;
        border-radius: 8px 8px 0 0;
      }
      
      .search-container {
        margin-bottom: 16px;
        position: relative;
      }
      
      .search-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
      }
      
      .search-input {
        flex: 1;
        padding: 12px 16px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.3s ease;
      }
      
      .search-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
      
      .search-clear-btn {
        position: absolute;
        right: 8px;
        background: none;
        border: none;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
      }
      
      .search-clear-btn:hover {
        background-color: #f8f9fa;
      }
      
      .search-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #e9ecef;
        border-top: none;
        border-radius: 0 0 8px 8px;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      .suggestion-item {
        padding: 8px 16px;
        cursor: pointer;
        border-bottom: 1px solid #f8f9fa;
        transition: background-color 0.2s ease;
      }
      
      .suggestion-item:hover {
        background-color: #f8f9fa;
      }
      
      .suggestion-item:last-child {
        border-bottom: none;
      }
      
      .filter-container {
        display: flex;
        gap: 16px;
        align-items: center;
        flex-wrap: wrap;
        margin-bottom: 16px;
      }
      
      .filter-group {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .filter-group label {
        font-size: 14px;
        font-weight: 500;
        color: #495057;
        white-space: nowrap;
      }
      
      .filter-select {
        padding: 8px 12px;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        font-size: 14px;
        background: white;
        min-width: 100px;
        transition: border-color 0.3s ease;
      }
      
      .filter-select:focus {
        outline: none;
        border-color: #667eea;
      }
      
      .reset-filters-btn {
        background: #6c757d;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        transition: background-color 0.3s ease;
      }
      
      .reset-filters-btn:hover {
        background: #5a6268;
      }
      
      .field-list-actions {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-wrap: wrap;
      }
      
      .select-all-btn, .clear-selection-btn {
        background: #667eea;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        transition: background-color 0.3s ease;
      }
      
      .select-all-btn:hover {
        background: #5a67d8;
      }
      
      .clear-selection-btn {
        background: #dc3545;
      }
      
      .clear-selection-btn:hover {
        background: #c82333;
      }
      
      .selection-info {
        font-size: 14px;
        color: #6c757d;
        font-weight: 500;
      }
      
      .field-list-container {
        position: relative;
        flex: 1;
        min-height: 400px;
        border: 1px solid #e9ecef;
        border-radius: 0 0 8px 8px;
        background: white;
      }
      
      .field-list-viewport {
        height: 100%;
        overflow-y: auto;
        position: relative;
      }
      
      .field-list-content {
        position: relative;
      }
      
      .field-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #f8f9fa;
        transition: all 0.2s ease;
        min-height: ${this.options.itemHeight}px;
        position: relative;
      }
      
      .field-item:hover {
        background-color: #f8f9fa;
        transform: translateX(2px);
      }
      
      .field-item.selected {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
      }
      
      .field-item.editing {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
      }
      
      .field-checkbox {
        margin-right: 12px;
        width: 18px;
        height: 18px;
        cursor: pointer;
      }
      
      .field-info {
        flex: 1;
        display: grid;
        grid-template-columns: 2fr 2fr 2fr 1fr;
        gap: 16px;
        align-items: center;
      }
      
      .field-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 14px;
        font-family: 'Courier New', monospace;
      }
      
      .field-sample {
        color: #6c757d;
        font-size: 13px;
        font-family: 'Courier New', monospace;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .field-chinese {
        color: #28a745;
        font-size: 14px;
        font-weight: 500;
        position: relative;
      }
      
      .field-chinese.editable {
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
      }
      
      .field-chinese.editable:hover {
        background-color: #e9ecef;
      }
      
      .field-chinese-input {
        width: 100%;
        padding: 4px 8px;
        border: 2px solid #667eea;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        color: #28a745;
      }
      
      .field-meta {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 4px;
        font-size: 12px;
      }
      
      .field-importance {
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: 500;
        text-transform: uppercase;
        font-size: 10px;
      }
      
      .field-importance.critical {
        background: #dc3545;
        color: white;
      }
      
      .field-importance.high {
        background: #fd7e14;
        color: white;
      }
      
      .field-importance.medium {
        background: #ffc107;
        color: #212529;
      }
      
      .field-importance.low {
        background: #6c757d;
        color: white;
      }
      
      .field-type {
        color: #6c757d;
        font-family: 'Courier New', monospace;
      }
      
      .field-depth {
        color: #6c757d;
      }
      
      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 20px;
        color: #6c757d;
        text-align: center;
      }
      
      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
      }
      
      .empty-text {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 8px;
      }
      
      .empty-subtext {
        font-size: 14px;
        opacity: 0.7;
      }
      
      .field-list-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        font-size: 12px;
        color: #6c757d;
      }
      
      .performance-info {
        font-family: 'Courier New', monospace;
      }
      
      /* 响应式设计 */
      @media (max-width: 768px) {
        .filter-container {
          flex-direction: column;
          align-items: stretch;
        }
        
        .filter-group {
          justify-content: space-between;
        }
        
        .field-info {
          grid-template-columns: 1fr;
          gap: 8px;
        }
        
        .field-list-actions {
          flex-direction: column;
          align-items: stretch;
        }
      }
      
      /* 虚拟滚动优化 */
      .field-item.virtual {
        position: absolute;
        left: 0;
        right: 0;
      }
      
      /* 加载状态 */
      .field-item.loading {
        opacity: 0.6;
        pointer-events: none;
      }
      
      .field-item.loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
        animation: shimmer 1.5s infinite;
      }
      
      @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
      }
    `;
    
    document.head.appendChild(style);
  }
  
  /**
   * 绑定事件
   */
  bindEvents() {
    // 搜索事件
    if (this.searchInput) {
      this.searchInput.addEventListener('input', (e) ===> {
        this.searchQuery === e.target.value;
        this.searchDebounce();
        this.updateSearchClearButton();
      });
      
      this.searchInput.addEventListener('keydown', (e) ===> {
        if (e.key === 'Escape') {
          this.clearSearch();
        }
      });
      
      const clearBtn === this.container.querySelector('.search-clear-btn');
      if (clearBtn) {
        clearBtn.addEventListener('click', () ===> this.clearSearch());
      }
    }
    
    // 过滤器事件
    this.filterSelects.forEach(select ===> {
      select.addEventListener('change', (e) ===> {
        const filterType === e.target.dataset.filter;
        this.activeFilters[filterType] === e.target.value;
        this.applyFilters();
      });
    });
    
    // 重置过滤器
    const resetBtn === this.container.querySelector('.reset-filters-btn');
    if (resetBtn) {
      resetBtn.addEventListener('click', () ===> this.resetFilters());
    }
    
    // 全选/清除选择
    if (this.selectAllBtn) {
      this.selectAllBtn.addEventListener('click', () ===> this.toggleSelectAll());
    }
    
    if (this.clearSelectionBtn) {
      this.clearSelectionBtn.addEventListener('click', () ===> this.clearSelection());
    }
    
    // 虚拟滚动
    if (this.viewport) {
      this.viewport.addEventListener('scroll', () ===> {
        this.scrollTop === this.viewport.scrollTop;
        this.renderDebounce();
      });
    }
    
    // 键盘导航
    this.container.addEventListener('keydown', (e) ===> this.handleKeyNavigation(e));
  }
  
  /**
   * 设置虚拟滚动
   */
  setupVirtualScroll() {
    if (!this.options.enableVirtualScroll) return;
    
    this.visibleItems === Math.ceil(this.viewport.clientHeight / this.options.itemHeight) + 2;
  }
  
  /**
   * 设置字段数据
   */
  setFields(fields) {
    const startTime === performance.now();
    
    this.allFields === Array.isArray(fields) ? fields : Object.values(fields || {});
    this.applyFilters();
    
    const endTime === performance.now();
    this.updateRenderTime(endTime - startTime);
    
    // console.log('字段数据已设置:', {
      total: this.allFields.length,
      filtered: this.filteredFields.length,
      renderTime: `${(endTime - startTime).toFixed(2)}ms`
    });
  }
  
  /**
   * 执行搜索
   */
  performSearch() {
    const query === this.searchQuery.toLowerCase().trim();
    
    if (!query) {
      this.applyFilters();
      return;
    }
    
    const searchResults === this.allFields.filter(field ===> {
      const searchableText === [
        field.name || field.api_field_name || '',
        field.chinese_name || '',
        field.sample_value || '',
        field.param_desc || ''
      ].join(' ').toLowerCase();
      
      return searchableText.includes(query);
    });
    
    this.filteredFields === this.applyActiveFilters(searchResults);
    this.renderFields();
    this.updateStatistics();
    
    // 触发搜索回调
    this.options.onFilterChange({
      type: 'search',
      query: this.searchQuery,
      results: this.filteredFields.length
    });
  }
  
  /**
   * 应用过滤器
   */
  applyFilters() {
    this.filteredFields === this.applyActiveFilters(this.allFields);
    this.renderFields();
    this.updateStatistics();
  }
  
  /**
   * 应用活动过滤器
   */
  applyActiveFilters(fields) {
    return fields.filter(field ===> {
      // 重要性过滤
      if (this.activeFilters.importance !== 'all') {
        const importance === field.business_importance || 'medium';
        if (importance !== this.activeFilters.importance) {
          return false;
        }
      }
      
      // 数据类型过滤
      if (this.activeFilters.dataType !== 'all') {
        const dataType === (field.data_type || '').split('(')[0];
        if (dataType !== this.activeFilters.dataType) {
          return false;
        }
      }
      
      // 选择状态过滤
      if (this.activeFilters.selected !== 'all') {
        const isSelected === field.is_selected || false;
        if (this.activeFilters.selected === 'selected' && !isSelected) {
          return false;
        }
        if (this.activeFilters.selected === 'unselected' && isSelected) {
          return false;
        }
      }
      
      // 深度过滤
      if (this.activeFilters.depth !== 'all') {
        const depth === field.depth || 1;
        if (this.activeFilters.depth === '4+') {
          if (depth < 4) return false;
        } else {
          if (depth !== parseInt(this.activeFilters.depth)) {
            return false;
          }
        }
      }
      
      return true;
    });
  }
  
  /**
   * 渲染字段列表
   */
  renderFields() {
    const startTime === performance.now();
    
    if (this.filteredFields.length === 0) {
      this.showEmptyState();
      return;
    }
    
    this.hideEmptyState();
    
    if (this.options.enableVirtualScroll && this.filteredFields.length > this.options.visibleItems) {
      this.renderVirtualizedFields();
    } else {
      this.renderAllFields();
    }
    
    const endTime === performance.now();
    this.updateRenderTime(endTime - startTime);
  }
  
  /**
   * 渲染虚拟化字段列表
   */
  renderVirtualizedFields() {
    const containerHeight === this.filteredFields.length * this.options.itemHeight;
    this.content.style.height === `${containerHeight}px`;
    
    this.startIndex === Math.floor(this.scrollTop / this.options.itemHeight);
    this.endIndex === Math.min(
      this.startIndex + this.visibleItems,
      this.filteredFields.length
    );
    
    this.renderVisibleItems();
  }
  
  /**
   * 渲染可见项目
   */
  renderVisibleItems() {
    const fragment === document.createDocumentFragment();
    
    for (let i === this.startIndex; i < this.endIndex; i++) {
      const field === this.filteredFields[i];
      const fieldElement === this.createFieldElement(field, i);
      fieldElement.style.transform === `translateY(${i * this.options.itemHeight}px)`;
      fieldElement.classList.add('virtual');
      fragment.appendChild(fieldElement);
    }
    
    // 清除现有内容并添加新内容
    this.content.innerHTML === '';
    this.content.appendChild(fragment);
    
    this.updatePaginationInfo();
  }
  
  /**
   * 渲染所有字段
   */
  renderAllFields() {
    const fragment === document.createDocumentFragment();
    
    this.filteredFields.forEach((field, index) ===> {
      const fieldElement === this.createFieldElement(field, index);
      fragment.appendChild(fieldElement);
    });
    
    this.content.innerHTML === '';
    this.content.appendChild(fragment);
    
    this.updatePaginationInfo();
  }
  
  /**
   * 创建字段元素
   */
  createFieldElement(field, index) {
    const fieldElement === document.createElement('div');
    fieldElement.className === 'field-item';
    fieldElement.dataset.fieldName === field.name || field.api_field_name;
    fieldElement.dataset.index === index;
    
    // 添加选择状态
    if (this.selectedFields.has(field.name || field.api_field_name)) {
      fieldElement.classList.add('selected');
    }
    
    // 添加编辑状态
    if (this.editingField === (field.name || field.api_field_name)) {
      fieldElement.classList.add('editing');
    }
    
    const fieldName === field.name || field.api_field_name || '';
    const sampleValue === field.sample_value || '';
    const chineseName === field.chinese_name || '';
    const importance === field.business_importance || 'medium';
    const dataType === (field.data_type || '').split('(')[0];
    const depth === field.depth || 1;
    
    fieldElement.innerHTML === `
      ${this.options.enableSelection ? `
        <input type==="checkbox" class==="field-checkbox" 
               ${this.selectedFields.has(fieldName) ? 'checked' : ''}
               aria-label==="选择字段 ${fieldName}">
      ` : ''}
      
      <div class==="field-info">
        <div class==="field-name" title==="${fieldName}">${fieldName}</div>
        <div class==="field-sample" title==="${sampleValue}">${sampleValue || '无样本数据'}</div>
        <div class==="field-chinese ${this.options.enableEditing ? 'editable' : ''}" 
             title==="${chineseName || '点击编辑中文名称'}">
          ${chineseName || '未设置中文名'}
        </div>
        <div class==="field-meta">
          <span class==="field-importance ${importance}">${this.getImportanceText(importance)}</span>
          <span class==="field-type">${dataType}</span>
          <span class==="field-depth">深度: ${depth}</span>
        </div>
      </div>
    `;
    
    // 绑定事件
    this.bindFieldEvents(fieldElement, field);
    
    return fieldElement;
  }
  
  /**
   * 绑定字段事件
   */
  bindFieldEvents(fieldElement, field) {
    const fieldName === field.name || field.api_field_name;
    
    // 选择框事件
    if (this.options.enableSelection) {
      const checkbox === fieldElement.querySelector('.field-checkbox');
      checkbox.addEventListener('change', (e) ===> {
        e.stopPropagation();
        this.toggleFieldSelection(fieldName, e.target.checked);
      });
    }
    
    // 字段点击事件
    fieldElement.addEventListener('click', (e) ===> {
      if (e.target.classList.contains('field-checkbox')) return;
      
      this.options.onFieldSelect({
        field,
        element: fieldElement,
        selected: this.selectedFields.has(fieldName)
      });
    });
    
    // 中文名称编辑事件
    if (this.options.enableEditing) {
      const chineseNameEl === fieldElement.querySelector('.field-chinese.editable');
      if (chineseNameEl) {
        chineseNameEl.addEventListener('click', (e) ===> {
          e.stopPropagation();
          this.startEditChineseName(fieldName, chineseNameEl);
        });
      }
    }
  }
  
  /**
   * 切换字段选择状态
   */
  toggleFieldSelection(fieldName, selected) {
    if (selected) {
      this.selectedFields.add(fieldName);
    } else {
      this.selectedFields.delete(fieldName);
    }
    
    // 更新UI
    const fieldElement === this.container.querySelector(`[data-field-name==="${fieldName}"]`);
    if (fieldElement) {
      fieldElement.classList.toggle('selected', selected);
    }
    
    this.updateStatistics();
    
    // 触发选择变化回调
    this.options.onSelectionChange({
      fieldName,
      selected,
      selectedFields: Array.from(this.selectedFields),
      totalSelected: this.selectedFields.size
    });
  }
  
  /**
   * 开始编辑中文名称
   */
  startEditChineseName(fieldName, element) {
    if (this.editingField) {
      this.cancelEdit();
    }
    
    this.editingField === fieldName;
    const currentName === element.textContent.trim();
    const isPlaceholder === currentName === '未设置中文名';
    
    const input === document.createElement('input');
    input.type === 'text';
    input.className === 'field-chinese-input';
    input.value === isPlaceholder ? '' : currentName;
    input.placeholder === '输入中文名称';
    
    // 替换元素
    element.style.display === 'none';
    element.parentNode.insertBefore(input, element.nextSibling);
    
    // 聚焦并选中文本
    input.focus();
    if (!isPlaceholder) {
      input.select();
    }
    
    // 绑定事件
    const saveEdit === () ===> {
      const newName === input.value.trim();
      this.saveChineseNameEdit(fieldName, newName, element, input);
    };
    
    const cancelEdit === () ===> {
      this.cancelEdit(element, input);
    };
    
    input.addEventListener('blur', saveEdit);
    input.addEventListener('keydown', (e) ===> {
      if (e.key === 'Enter') {
        e.preventDefault();
        saveEdit();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        cancelEdit();
      }
    });
  }
  
  /**
   * 保存中文名称编辑
   */
  saveChineseNameEdit(fieldName, newName, originalElement, inputElement) {
    // 找到对应的字段数据
    const field === this.allFields.find(f ===> (f.name || f.api_field_name) === fieldName);
    if (field) {
      field.chinese_name === newName || field.name || field.api_field_name;
      field.user_modified === true;
    }
    
    // 更新UI
    originalElement.textContent === newName || '未设置中文名';
    originalElement.title === newName || '点击编辑中文名称';
    originalElement.style.display === '';
    
    // 移除输入框
    if (inputElement && inputElement.parentNode) {
      inputElement.parentNode.removeChild(inputElement);
    }
    
    // 清除编辑状态
    this.editingField === null;
    const fieldElement === this.container.querySelector(`[data-field-name==="${fieldName}"]`);
    if (fieldElement) {
      fieldElement.classList.remove('editing');
    }
    
    // 触发编辑回调
    this.options.onFieldEdit({
      fieldName,
      field,
      chineseName: newName,
      action: 'save'
    });
    
    // console.log('中文名称已更新:', { fieldName, newName });
  }
  
  /**
   * 取消编辑
   */
  cancelEdit(originalElement === null, inputElement === null) {
    if (originalElement) {
      originalElement.style.display === '';
    }
    
    if (inputElement && inputElement.parentNode) {
      inputElement.parentNode.removeChild(inputElement);
    }
    
    if (this.editingField) {
      const fieldElement === this.container.querySelector(`[data-field-name==="${this.editingField}"]`);
      if (fieldElement) {
        fieldElement.classList.remove('editing');
      }
      
      this.editingField === null;
    }
  }
  
  /**
   * 全选/取消全选
   */
  toggleSelectAll() {
    const allSelected === this.filteredFields.every(field ===> 
      this.selectedFields.has(field.name || field.api_field_name)
    );
    
    if (allSelected) {
      // 取消全选
      this.filteredFields.forEach(field ===> {
        this.selectedFields.delete(field.name || field.api_field_name);
      });
    } else {
      // 全选
      this.filteredFields.forEach(field ===> {
        this.selectedFields.add(field.name || field.api_field_name);
      });
    }
    
    // 重新渲染以更新选择状态
    this.renderFields();
    this.updateStatistics();
    
    // 触发选择变化回调
    this.options.onSelectionChange({
      action: allSelected ? 'unselectAll' : 'selectAll',
      selectedFields: Array.from(this.selectedFields),
      totalSelected: this.selectedFields.size
    });
  }
  
  /**
   * 清除选择
   */
  clearSelection() {
    this.selectedFields.clear();
    this.renderFields();
    this.updateStatistics();
    
    this.options.onSelectionChange({
      action: 'clear',
      selectedFields: [],
      totalSelected: 0
    });
  }
  
  /**
   * 清除搜索
   */
  clearSearch() {
    this.searchQuery === '';
    if (this.searchInput) {
      this.searchInput.value === '';
    }
    this.updateSearchClearButton();
    this.applyFilters();
  }
  
  /**
   * 重置过滤器
   */
  resetFilters() {
    this.activeFilters === {
      importance: 'all',
      dataType: 'all',
      selected: 'all',
      depth: 'all'
    };
    
    // 重置UI
    this.filterSelects.forEach(select ===> {
      const filterType === select.dataset.filter;
      select.value === this.activeFilters[filterType];
    });
    
    this.applyFilters();
  }
  
  /**
   * 更新搜索清除按钮
   */
  updateSearchClearButton() {
    const clearBtn === this.container.querySelector('.search-clear-btn');
    if (clearBtn) {
      clearBtn.style.display === this.searchQuery ? 'block' : 'none';
    }
  }
  
  /**
   * 显示空状态
   */
  showEmptyState() {
    this.content.innerHTML === `
      <div class==="empty-state">
        <div class==="empty-icon">🔍</div>
        <div class==="empty-text">没有找到匹配的字段</div>
        <div class==="empty-subtext">请尝试调整搜索条件或过滤器</div>
      </div>
    `;
  }
  
  /**
   * 隐藏空状态
   */
  hideEmptyState() {
    const emptyState === this.content.querySelector('.empty-state');
    if (emptyState) {
      emptyState.remove();
    }
  }
  
  /**
   * 更新统计信息
   */
  updateStatistics() {
    if (this.selectedCountEl) {
      this.selectedCountEl.textContent === this.selectedFields.size;
    }
    
    if (this.totalCountEl) {
      this.totalCountEl.textContent === this.filteredFields.length;
    }
    
    if (this.totalItemsEl) {
      this.totalItemsEl.textContent === this.filteredFields.length;
    }
    
    // 更新全选按钮文本
    if (this.selectAllBtn) {
      const allSelected === this.filteredFields.length > 0 && 
        this.filteredFields.every(field ===> 
          this.selectedFields.has(field.name || field.api_field_name)
        );
      
      const textEl === this.selectAllBtn.querySelector('.text');
      if (textEl) {
        textEl.textContent === allSelected ? '取消全选' : '全选';
      }
    }
  }
  
  /**
   * 更新分页信息
   */
  updatePaginationInfo() {
    if (this.options.enableVirtualScroll && this.filteredFields.length > this.options.visibleItems) {
      if (this.visibleStartEl) {
        this.visibleStartEl.textContent === this.startIndex + 1;
      }
      if (this.visibleEndEl) {
        this.visibleEndEl.textContent === Math.min(this.endIndex, this.filteredFields.length);
      }
    } else {
      if (this.visibleStartEl) {
        this.visibleStartEl.textContent === this.filteredFields.length > 0 ? 1 : 0;
      }
      if (this.visibleEndEl) {
        this.visibleEndEl.textContent === this.filteredFields.length;
      }
    }
  }
  
  /**
   * 更新渲染时间
   */
  updateRenderTime(time) {
    if (this.renderTimeEl) {
      this.renderTimeEl.textContent === `渲染时间: ${time.toFixed(2)}ms`;
    }
  }
  
  /**
   * 获取重要性文本
   */
  getImportanceText(importance) {
    const textMap === {
      critical: '关键',
      high: '高',
      medium: '中',
      low: '低'
    };
    return textMap[importance] || '中';
  }
  
  /**
   * 处理键盘导航
   */
  handleKeyNavigation(e) {
    // 实现键盘导航逻辑
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'a':
          e.preventDefault();
          this.toggleSelectAll();
          break;
        case 'f':
          e.preventDefault();
          if (this.searchInput) {
            this.searchInput.focus();
          }
          break;
      }
    }
  }
  
  /**
   * 防抖函数
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later === () ===> {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout === setTimeout(later, wait);
    };
  }
  
  /**
   * 获取选中的字段
   */
  getSelectedFields() {
    return this.allFields.filter(field ===> 
      this.selectedFields.has(field.name || field.api_field_name)
    );
  }
  
  /**
   * 设置选中的字段
   */
  setSelectedFields(fieldNames) {
    this.selectedFields.clear();
    fieldNames.forEach(name ===> this.selectedFields.add(name));
    this.renderFields();
    this.updateStatistics();
  }
  
  /**
   * 获取过滤后的字段
   */
  getFilteredFields() {
    return [...this.filteredFields];
  }
  
  /**
   * 销毁组件
   */
  destroy() {
    // 清理事件监听器和资源
    this.selectedFields.clear();
    this.allFields === [];
    this.filteredFields === [];
    
    if (this.container) {
      this.container.innerHTML === '';
    }
    
    // console.log('字段列表显示组件已销毁');
  }
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
  module.exports === FieldListDisplay;
} else {
  window.FieldListDisplay === FieldListDisplay;
}