产品入库列表查询
发布时间:2024-12-26 23:37:06
产品入库列表查询

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	开放API: 动态域名，获取方式详见 获取租户所在数据中心域名
集成API: 详细域名信息，请见 连接配置
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/storeprorecord/list
请求方式	POST
ContentType	application/json
应用场景	开放API/集成API
API类别	列表查询
事务和幂等性	无
限流次数	40次/分钟

多语	不支持
2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
pageIndex	int	否	是	页号    默认值: 1
code	string	否	否	单据编号
pageSize	int	否	是	每页行数    默认值: 10
warehouse_name	string	是	否	仓库名字
bustype_name	string	否	否	交易类型
stockMgr_name	string	是	否	库管人名字列表
operator	string	是	否	操作员id
department	string	是	否	部门id
org	string	否	否	组织id
product_cName	string	是	否	物料ID
open_hopeReceiveDate_begin	string	否	否	区间查询开始时间 : "2020-03-02"
open_hopeReceiveDate_end	string	否	否	区间查询结束时间 :"2020-03-02 23:59:59"
simpleVOs	object	是	否	扩展查询条件
field	string	否	否	属性名(条件),子表加前缀storeProRecords.
op	string	否	否	条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)
value1	string	否	否	值1(条件),单条件时仅使用这个配置
value2	string	否	否	值2(条件),单条件时此配置无效
3. 请求示例
Url: /yonbip/scm/storeprorecord/list?access_token=访问令牌
Body: {
	"pageIndex": 0,
	"code": "",
	"pageSize": 0,
	"warehouse_name": [
		""
	],
	"bustype_name": "",
	"stockMgr_name": [
		""
	],
	"operator": [
		""
	],
	"department": [
		""
	],
	"org": "",
	"product_cName": [
		""
	],
	"open_hopeReceiveDate_begin": "",
	"open_hopeReceiveDate_end": "",
	"simpleVOs": [
		{
			"field": "",
			"op": "",
			"value1": "",
			"value2": ""
		}
	]
}
4. 返回值参数
名称	类型	数组	描述
code	string	否	返回码，调用成功时返回200
message	string	否	调用失败时的错误信息
data	object	否	调用成功时的返回数据
salesOrgId_name	string	否	销售组织名称
sumRecordList	object	是	sum合计信息
pageIndex	int	否	当前页
pageSize	int	否	分页大小
pageCount	int	否	总页数
beginPageIndex	int	否	开始页码
endPageIndex	int	否	结束页码
recordCount	int	否	总记录数
pubts	string	否	时间戳
synSourceOrg	string	否	协同来源组织id
synSourceOrg	string	否	协同来源组织id
recordList	object	是	返回结果对象
factoryFiOrg	string	否	完工组织id
storeProRecords_product	string	否	物料id
currency	string	否	币种id
storeProRecords_unit	string	否	主计量
storeProRecords_productsku	string	否	物料sku
storeProRecords_stockUnitId	string	否	库存单位id
vouchdate	string	否	单据日期
code	string	否	单据编号
department_name	string	否	部门名称
accountOrg	string	否	会计主体id
org_name	string	否	库存组织名称
stockMgr_name	string	否	库管员名称
department	string	否	部门id
totalPieces	string	否	整单件数(废弃)
org	string	否	库存组织id
stockMgr	string	否	库管员IDid
store	string	否	门店id
store_name	string	否	门店名称
warehouse	string	否	仓库id
warehouse_name	string	否	仓库名称
bustype	string	否	业务类型id
accountOrg_name	string	否	会计主体名称
bustype_name	string	否	交易类型名称
status	string	否	单据状态, 0:未提交、1:已提交、
operator	string	否	经办人id
totalQuantity	int	否	整单数量(废弃)
totalMaterial	string	否	已材料出, true:是、false:否、(废弃)
creator	string	否	创建人
createTime	string	否	创建时间
modifier	string	否	最后修改人
modifyTime	string	否	最后修改时间
auditor	string	否	提交人
auditTime	string	否	提交时间
memo	string	否	备注
auditorId	string	否	审批人
creatorId	string	否	创建人
id	string	否	主表id
modifierId	string	否	修改人
pubts	string	否	时间戳
tplid	string	否	模板id(废弃)
storeProRecords_id	string	否	子表id
product_cCode	string	否	物料编码
product_cName	string	否	物料名称
storeProRecordsCharacteristics	特征组
st.storeprorecord.StoreProRecords	否	自由项特征组
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
productsku_cCode	string	否	sku编码
productsku_cName	string	否	sku名称
product_modelDescription	string	否	规格型号
qty	int	否	数量
product_unitName	string	否	计量单位
subQty	double	否	件数
stockUnit_name	string	否	库存单位
project_name	string	否	项目名称
natUnitPrice	int	否	单价
natMoney	int	否	金额
natCurrency_priceDigit	int	否	币种单价精度
natCurrency_moneyDigit	int	否	币种金额精度
unit_Precision	int	否	主计量精度
stockUnitId_Precision	int	否	库存单位精度
storeProRecordsDefineCharacter	特征组
st.storeprorecord.StoreProRecords	否	子表自定义项特征组
XS11	string	否	需求分类号test
id	string	否	特征id,主键,新增时无需填写,修改时必填
storeProRecordDefineCharacter	特征组
st.storeprorecord.StoreProRecord	否	主表自定义项特征组
id	string	否	特征id,主键,新增时无需填写,修改时必填
out_sys_id	string	否	外部来源线索
out_sys_code	string	否	外部来源编码
out_sys_version	string	否	外部来源版本
out_sys_type	string	否	外部来源类型
out_sys_rowno	string	否	外部来源行号
out_sys_lineid	string	否	外部来源行
5. 正确返回示例
{
	"code": "",
	"message": "",
	"data": {
		"sumRecordList": [
			{
				"totalQuantity": "",
				"qty": "",
				"totalPieces": "",
				"subQty": ""
			}
		],
		"pageIndex": 0,
		"pageSize": 0,
		"pageCount": 0,
		"beginPageIndex": 0,
		"endPageIndex": 0,
		"recordCount": 0,
		"pubts": "格式：yyyy-MM-dd HH:mm:ss",
		"recordList": [
			{
				"factoryFiOrg": "",
				"storeProRecords_product": "",
				"currency": "",
				"storeProRecords_unit": "",
				"storeProRecords_productsku": "",
				"storeProRecords_stockUnitId": "",
				"vouchdate": "",
				"code": "",
				"department_name": "",
				"accountOrg": "",
				"org_name": "",
				"stockMgr_name": "",
				"department": "",
				"totalPieces": "",
				"org": "",
				"stockMgr": "",
				"store": "",
				"store_name": "",
				"warehouse": "",
				"warehouse_name": "",
				"bustype": "",
				"accountOrg_name": "",
				"bustype_name": "",
				"status": "",
				"operator": "",
				"totalQuantity": 0,
				"totalMaterial": "",
				"creator": "",
				"createTime": "",
				"modifier": "",
				"modifyTime": "",
				"auditor": "",
				"auditTime": "",
				"memo": "",
				"auditorId": "",
				"creatorId": "",
				"id": "",
				"modifierId": "",
				"pubts": "",
				"tplid": "",
				"storeProRecords_id": "",
				"product_cCode": "",
				"product_cName": "",
				"storeProRecordsCharacteristics": {
					"XS15": "",
					"XXX0111": "",
					"id": ""
				},
				"productsku_cCode": "",
				"productsku_cName": "",
				"product_modelDescription": "",
				"qty": 0,
				"product_unitName": "",
				"subQty": 0,
				"stockUnit_name": "",
				"project_name": "",
				"natUnitPrice": 0,
				"natMoney": 0,
				"natCurrency_priceDigit": 0,
				"natCurrency_moneyDigit": 0,
				"unit_Precision": 0,
				"stockUnitId_Precision": 0,
				"storeProRecordsDefineCharacter": {
					"XS11": "",
					"id": ""
				},
				"storeProRecordDefineCharacter": {
					"id": ""
				},
				"out_sys_id": "",
				"out_sys_code": "",
				"out_sys_version": "",
				"out_sys_type": "",
				"out_sys_rowno": "",
				"out_sys_lineid": ""
			}
		]
	}
}
6. 错误返回码
错误码	错误信息	描述
999	入参错误等异常	根据返回错误信息做出相应调整
7. 错误返回示例
{
 "code": "999",
 "message": "No enum constant org.imeta.core.base.ConditionOperator.2"
}