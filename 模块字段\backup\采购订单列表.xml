<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>b20cfa042e5848309e96c689158c17d1</id>
<name>采购订单列表查询</name>
<apiClassifyId>1936116925877714946</apiClassifyId>
<apiClassifyName>采购订单</apiClassifyName>
<apiClassifyCode/>
<parentApiClassifies/>
<functionId/>
<openMode>0</openMode>
<description>根据表头模式还是表头明细模式、分页条件和自定义条件查询采购订单列表数据信息</description>
<auth>true</auth>
<bodyPassthrough>false</bodyPassthrough>
<healthExam>false</healthExam>
<healthStatus>true</healthStatus>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/scm/purchaseorder/list</completeProxyUrl>
<connectUrl>/bill/list</connectUrl>
<sort>20</sort>
<handler>openapi</handler>
<httpRequestType>POST</httpRequestType>
<openApi>true</openApi>
<preset>false</preset>
<productId>710a0be3edff4f9092e35f63fd3b9bae</productId>
<productCode>scm</productCode>
<proxyUrl>/yonbip/scm/purchaseorder/list</proxyUrl>
<requestParamsDemo>Url: /yonbip/scm/purchaseorder/list?access_token=访问令牌 Body: { "pageIndex": 1, "pageSize": 10, "isSum": false, "simpleVOs": [ { "field": "code", "op": "eq", "value1": "CGA20005000456" } ], "queryOrders": [ { "field": "id", "order": "asc" } ] }</requestParamsDemo>
<requestProtocol>HTTP</requestProtocol>
<serviceHttpMethod>POST</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2020-01-16 16:52:41</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/purchaseorder/list</address>
<productName>采购供应</productName>
<productClassifyId>yonsuite</productClassifyId>
<productClassifyCode>yonbip</productClassifyCode>
<productClassifyName>用友 YonBIP</productClassifyName>
<paramDTOS>
<paramDTOS>
<id>2094836196761927695</id>
<name>pageIndex</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId/>
<defParamId>1998541234691375110</defParamId>
<array>false</array>
<paramDesc>页码</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>1</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2094836196761927696</id>
<name>pageSize</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId/>
<defParamId>1998541234691375111</defParamId>
<array>false</array>
<paramDesc>每页数</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>10</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>10</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2094836196761927697</id>
<name>isSum</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId/>
<defParamId>1998541234691375112</defParamId>
<array>false</array>
<paramDesc>查询表头</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>false</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>false</defaultValue>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2094836196761927688</id>
<name>simpleVOs</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId/>
<children>
<children>
<id>2094836196761927689</id>
<name>field</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927688</parentId>
<defParamId>1998541234691375114</defParamId>
<array>false</array>
<paramDesc>查询字段（条件）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>code</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2094836196761927690</id>
<name>op</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927688</parentId>
<defParamId>1998541234691375115</defParamId>
<array>false</array>
<paramDesc>比较符：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>eq</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2094836196761927691</id>
<name>value1</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927688</parentId>
<defParamId>1998541234691375116</defParamId>
<array>false</array>
<paramDesc>参数值1</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>CGA20005000456</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>1998541234691375113</defParamId>
<array>true</array>
<paramDesc>查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2094836196761927692</id>
<name>queryOrders</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId/>
<children>
<children>
<id>2094836196761927693</id>
<name>field</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927692</parentId>
<defParamId>1998541234691375118</defParamId>
<array>false</array>
<paramDesc>排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：purchaseOrders.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>id</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2094836196761927694</id>
<name>order</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927692</parentId>
<defParamId>1998541234691375119</defParamId>
<array>false</array>
<paramDesc>顺序：正序(asc)、倒序(desc)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>asc</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>1998541234691375117</defParamId>
<array>true</array>
<paramDesc>排序字段</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>2094836196761927705</id>
<name>pageIndex</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>页码</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageIndex</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2094836196761927706</id>
<name>pageSize</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>每页数</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageSize</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2094836196761927707</id>
<name>isSum</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>查询表头</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>isSum</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>boolean</serviceParamType>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2094836196761927698</id>
<name>simpleVOs</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId/>
<children>
<children>
<id>2094836196761927699</id>
<name>field</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927698</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询字段（条件）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927700</id>
<name>op</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927698</parentId>
<defParamId/>
<array>false</array>
<paramDesc>比较符：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>op</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927701</id>
<name>value1</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927698</parentId>
<defParamId/>
<array>false</array>
<paramDesc>参数值1</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value1</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>simpleVOs</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2094836196761927702</id>
<name>queryOrders</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId/>
<children>
<children>
<id>2094836196761927703</id>
<name>field</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927702</parentId>
<defParamId/>
<array>false</array>
<paramDesc>排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：purchaseOrders.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927704</id>
<name>order</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927702</parentId>
<defParamId/>
<array>false</array>
<paramDesc>顺序：正序(asc)、倒序(desc)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>order</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>排序字段</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>queryOrders</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>2094836196761927820</id>
<name>code</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId/>
<defParamId>1998541234691375130</defParamId>
<array>false</array>
<paramDesc>编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>200</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2094836196761927821</id>
<name>message</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId/>
<defParamId>1998541234691375131</defParamId>
<array>false</array>
<paramDesc>返回信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>操作成功</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2094836196761927708</id>
<name>data</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId/>
<children>
<children>
<id>2094836196761927813</id>
<name>pageIndex</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927708</parentId>
<defParamId>1998541234691375133</defParamId>
<array>false</array>
<paramDesc>页码</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927814</id>
<name>pageSize</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927708</parentId>
<defParamId>1998541234691375134</defParamId>
<array>false</array>
<paramDesc>每页数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927815</id>
<name>recordCount</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927708</parentId>
<defParamId>1998541234691375135</defParamId>
<array>false</array>
<paramDesc>数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927709</id>
<name>recordList</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927708</parentId>
<children>
<children>
<id>2094836196761927713</id>
<name>product_cCode</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375137</defParamId>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>********</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927714</id>
<name>invoiceVendor</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375138</defParamId>
<array>false</array>
<paramDesc>开票供应商id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927715</id>
<name>priceUOM_Precision</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375139</defParamId>
<array>false</array>
<paramDesc>计价单位精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927716</id>
<name>modifyStatus</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375140</defParamId>
<array>false</array>
<paramDesc>变更状态：0：未变更、1：变更中、2：变更完成</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927717</id>
<name>receiveStatus</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375141</defParamId>
<array>false</array>
<paramDesc>收货状态：0：开立、1：已审核、10：已入库、11：待入库、12：待下单14：待结算15：已结算2：已关闭、3：审核中、4：锁定、5：未发货、6：已发货、7：已完成、8：待收、9：已收齐</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927718</id>
<name>listOriSum</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375142</defParamId>
<array>false</array>
<paramDesc>含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927719</id>
<name>priceUOM_Code</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375143</defParamId>
<array>false</array>
<paramDesc>计价单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>001</example>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927720</id>
<name>totalInTaxMoney</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375144</defParamId>
<array>false</array>
<paramDesc>累计入库含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927721</id>
<name>totalQuantity</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375145</defParamId>
<array>false</array>
<paramDesc>整单数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927722</id>
<name>natCurrency</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375146</defParamId>
<array>false</array>
<paramDesc>本币</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>G001ZM0000DEFAULTCURRENCT00000000001</example>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927723</id>
<name>listTotalPayOriMoney</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375147</defParamId>
<array>false</array>
<paramDesc>累计付款核销金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927724</id>
<name>unit_code</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375148</defParamId>
<array>false</array>
<paramDesc>主计量编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>001</example>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927728</id>
<name>id</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375152</defParamId>
<array>false</array>
<paramDesc>主表id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927729</id>
<name>isWfControlled</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375153</defParamId>
<array>false</array>
<paramDesc>是否审批流控制：true or false</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927730</id>
<name>totalArrivedTaxMoney</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375154</defParamId>
<array>false</array>
<paramDesc>累计到货含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927731</id>
<name>purchaseOrders_arrivedStatus</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375155</defParamId>
<array>false</array>
<paramDesc>到货状态：1：到货完成、2：未到货、3：部分到货、4：到货完成</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927732</id>
<name>bmake_st_purinvoice</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375156</defParamId>
<array>false</array>
<paramDesc>流程订货订单开蓝票</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927733</id>
<name>realProductAttribute</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375157</defParamId>
<array>false</array>
<paramDesc>实物商品属性</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927734</id>
<name>purchaseOrders_inWHStatus</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375158</defParamId>
<array>false</array>
<paramDesc>入库状态：1：入库完成、2：未入库、3：部分入库、4：入库结束</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927735</id>
<name>totalSendQty</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375159</defParamId>
<array>false</array>
<paramDesc>发货数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927736</id>
<name>natCurrency_priceDigit</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375160</defParamId>
<array>false</array>
<paramDesc>本币</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>6</example>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927737</id>
<name>bmake_st_purinrecord_red</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375161</defParamId>
<array>false</array>
<paramDesc>流程退库</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927738</id>
<name>status</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375162</defParamId>
<array>false</array>
<paramDesc>状态：0：开立、1：已审核、2：已关闭、3：审核中</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927739</id>
<name>currency_moneyDigit</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375163</defParamId>
<array>false</array>
<paramDesc>本币金额精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>6</example>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927740</id>
<name>listTotalPayApplyAmount</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375164</defParamId>
<array>false</array>
<paramDesc>累计付款申请金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927741</id>
<name>currency_code</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375165</defParamId>
<array>false</array>
<paramDesc>币种编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>CNY</example>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927742</id>
<name>vouchdate</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375166</defParamId>
<array>false</array>
<paramDesc>单据日期，格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-04-23 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927743</id>
<name>invoiceVendor_name</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375167</defParamId>
<array>false</array>
<paramDesc>开票供应商</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>达利园供货目录转移专用供应商</example>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927744</id>
<name>vendor</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375168</defParamId>
<array>false</array>
<paramDesc>供货供应商id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927745</id>
<name>purchaseOrders_payStatus</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375169</defParamId>
<array>false</array>
<paramDesc>核销状态：1：核销完成、2：未核销、3：部分核销</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927746</id>
<name>purchaseOrders_warehouse_code</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375170</defParamId>
<array>false</array>
<paramDesc>仓库编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>000001</example>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927747</id>
<name>listOriMoney</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375171</defParamId>
<array>false</array>
<paramDesc>无税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927748</id>
<name>currency</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375172</defParamId>
<array>false</array>
<paramDesc>币种</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>G001ZM0000DEFAULTCURRENCT00000000001</example>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927749</id>
<name>pubts</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375173</defParamId>
<array>false</array>
<paramDesc>时间戳，格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-04-23 11:34:02</example>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927750</id>
<name>org_name</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375174</defParamId>
<array>false</array>
<paramDesc>采购组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>eflong</example>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927751</id>
<name>generalPurchaseOrderType</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375175</defParamId>
<array>false</array>
<paramDesc>交易类型扩展参数</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927752</id>
<name>isFlowCoreBill</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375176</defParamId>
<array>false</array>
<paramDesc>是否流程核心单据</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927753</id>
<name>creator</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375177</defParamId>
<array>false</array>
<paramDesc>创建者</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>17600880447</example>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927754</id>
<name>product</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375178</defParamId>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1730491724599552</example>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927755</id>
<name>oriSum</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375179</defParamId>
<array>false</array>
<paramDesc>含税金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927756</id>
<name>inInvoiceOrg_name</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375180</defParamId>
<array>false</array>
<paramDesc>收票组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>eflong</example>
<fullName/>
<ytenantId/>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927757</id>
<name>product_defaultAlbumId</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375181</defParamId>
<array>false</array>
<paramDesc>物料首图片</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>http://ys-yxy-testres.yonyoucloud.com/fa813c9d-a182-457c-ab2a-a0c40ab113ea.jpg</example>
<fullName/>
<ytenantId/>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927758</id>
<name>purchaseOrders_id</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375182</defParamId>
<array>false</array>
<paramDesc>订单行id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2227385816011009</example>
<fullName/>
<ytenantId/>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927759</id>
<name>totalRecieveQty</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375183</defParamId>
<array>false</array>
<paramDesc>累计到货数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927760</id>
<name>demandOrg_name</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375184</defParamId>
<array>false</array>
<paramDesc>需求组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>eflong</example>
<fullName/>
<ytenantId/>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927761</id>
<name>createTime</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375185</defParamId>
<array>false</array>
<paramDesc>创建时间，格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-04-23 11:34:01</example>
<fullName/>
<ytenantId/>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927762</id>
<name>purUOM_Precision</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375186</defParamId>
<array>false</array>
<paramDesc>采购单位精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927763</id>
<name>currency_priceDigit</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375187</defParamId>
<array>false</array>
<paramDesc>币种单价精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>6</example>
<fullName/>
<ytenantId/>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927764</id>
<name>bEffectStock</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375188</defParamId>
<array>false</array>
<paramDesc>影响可用量</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId/>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927765</id>
<name>inOrg</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375189</defParamId>
<array>false</array>
<paramDesc>收货组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1730475987734784</example>
<fullName/>
<ytenantId/>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927766</id>
<name>bustype_name</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375190</defParamId>
<array>false</array>
<paramDesc>交易类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>普通订货-订单开票</example>
<fullName/>
<ytenantId/>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927767</id>
<name>purchaseOrders_invPriceExchRate</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375191</defParamId>
<array>false</array>
<paramDesc>计价单位换算率</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>19</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927768</id>
<name>subQty</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375192</defParamId>
<array>false</array>
<paramDesc>采购数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927769</id>
<name>inInvoiceOrg</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375193</defParamId>
<array>false</array>
<paramDesc>收票组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1730475987734784</example>
<fullName/>
<ytenantId/>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927770</id>
<name>product_cName</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375194</defParamId>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>eflong-规格1</example>
<fullName/>
<ytenantId/>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927771</id>
<name>listTaxRate</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375195</defParamId>
<array>false</array>
<paramDesc>税率</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>19</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927772</id>
<name>bmake_st_purinvoice_red</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375196</defParamId>
<array>false</array>
<paramDesc>流程订货订单开红票</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId/>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927773</id>
<name>product_model</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375197</defParamId>
<array>false</array>
<paramDesc>型号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>型号</example>
<fullName/>
<ytenantId/>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927774</id>
<name>storagenum</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375198</defParamId>
<array>false</array>
<paramDesc>已入库数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927775</id>
<name>purchaseOrders_invExchRate</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375199</defParamId>
<array>false</array>
<paramDesc>采购换算率</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>62</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>19</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927776</id>
<name>vendor_name</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375200</defParamId>
<array>false</array>
<paramDesc>供应商</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>达利园供货目录转移专用供应商</example>
<fullName/>
<ytenantId/>
<paramOrder>63</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927777</id>
<name>vendor_code</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375201</defParamId>
<array>false</array>
<paramDesc>供应商编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0001000101</example>
<fullName/>
<ytenantId/>
<paramOrder>64</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927778</id>
<name>oriUnitPrice</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375202</defParamId>
<array>false</array>
<paramDesc>无税单价</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>65</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927779</id>
<name>barCode</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375203</defParamId>
<array>false</array>
<paramDesc>单据条码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>st_purchaseorder|****************</example>
<fullName/>
<ytenantId/>
<paramOrder>66</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927780</id>
<name>isContract</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375204</defParamId>
<array>false</array>
<paramDesc>是否需要与供应商协同：true：是、false：否</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId/>
<paramOrder>67</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927781</id>
<name>unit_name</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375205</defParamId>
<array>false</array>
<paramDesc>主计量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>个</example>
<fullName/>
<ytenantId/>
<paramOrder>68</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927782</id>
<name>unit</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375206</defParamId>
<array>false</array>
<paramDesc>主计量id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1730486466924800</example>
<fullName/>
<ytenantId/>
<paramOrder>69</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927783</id>
<name>purchaseOrders_invoiceStatus</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375207</defParamId>
<array>false</array>
<paramDesc>发票状态：1：开票完成、2：未开票、3：部分开票、4：开票结束</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>70</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927784</id>
<name>natCurrency_moneyDigit</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375208</defParamId>
<array>false</array>
<paramDesc>本币</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>6</example>
<fullName/>
<ytenantId/>
<paramOrder>71</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927785</id>
<name>qty</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375209</defParamId>
<array>false</array>
<paramDesc>数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>72</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927786</id>
<name>unit_Precision</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375210</defParamId>
<array>false</array>
<paramDesc>主计量精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>73</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927787</id>
<name>oriTaxUnitPrice</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375211</defParamId>
<array>false</array>
<paramDesc>含税单价</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>74</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927788</id>
<name>moneysum</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375212</defParamId>
<array>false</array>
<paramDesc>金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>75</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927789</id>
<name>natCurrency_code</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375213</defParamId>
<array>false</array>
<paramDesc>本币</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>CNY</example>
<fullName/>
<ytenantId/>
<paramOrder>76</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927790</id>
<name>product_modelDescription</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375214</defParamId>
<array>false</array>
<paramDesc>规格说明</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>说明2</example>
<fullName/>
<ytenantId/>
<paramOrder>77</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927791</id>
<name>code</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375215</defParamId>
<array>false</array>
<paramDesc>单据编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>CGA20005000456</example>
<fullName/>
<ytenantId/>
<paramOrder>78</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927792</id>
<name>demandOrg</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375216</defParamId>
<array>false</array>
<paramDesc>需求组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1730475987734784</example>
<fullName/>
<ytenantId/>
<paramOrder>79</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927793</id>
<name>bizFlow</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375217</defParamId>
<array>false</array>
<paramDesc>流程ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>f596bd30-aee8-11ea-8d5f-0624d60000dc</example>
<fullName/>
<ytenantId/>
<paramOrder>80</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927794</id>
<name>realProductAttributeType</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375218</defParamId>
<array>false</array>
<paramDesc>实物商品属性</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>81</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927795</id>
<name>priceUOM</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375219</defParamId>
<array>false</array>
<paramDesc>计价单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1730486466924800</example>
<fullName/>
<ytenantId/>
<paramOrder>82</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927796</id>
<name>bizstatus</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375220</defParamId>
<array>false</array>
<paramDesc>状态：0：初始开立、1：审批中、2：审批完成、3：不通过流程终止、4：驳回到制单</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>83</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927797</id>
<name>totalInQty</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375221</defParamId>
<array>false</array>
<paramDesc>累计入库数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>84</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927798</id>
<name>bizFlow_version</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375222</defParamId>
<array>false</array>
<paramDesc>版本信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>V1.0</example>
<fullName/>
<ytenantId/>
<paramOrder>85</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927799</id>
<name>currency_name</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375223</defParamId>
<array>false</array>
<paramDesc>币种</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>人民币</example>
<fullName/>
<ytenantId/>
<paramOrder>86</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927800</id>
<name>org</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375224</defParamId>
<array>false</array>
<paramDesc>采购组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1730475987734784</example>
<fullName/>
<ytenantId/>
<paramOrder>87</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927801</id>
<name>bmake_st_purinrecord</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375225</defParamId>
<array>false</array>
<paramDesc>流程入库</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId/>
<paramOrder>88</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927802</id>
<name>purchaseOrders_purUOM</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375226</defParamId>
<array>false</array>
<paramDesc>采购单位编码</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1730486466924800</example>
<fullName/>
<ytenantId/>
<paramOrder>89</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927803</id>
<name>bustype</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375227</defParamId>
<array>false</array>
<paramDesc>交易类型id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1785637352591616</example>
<fullName/>
<ytenantId/>
<paramOrder>90</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927804</id>
<name>listOriTax</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375228</defParamId>
<array>false</array>
<paramDesc>税额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>91</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927805</id>
<name>retailInvestors</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375229</defParamId>
<array>false</array>
<paramDesc>是否散户：true or false</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId/>
<paramOrder>92</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927806</id>
<name>inOrg_name</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375230</defParamId>
<array>false</array>
<paramDesc>收货组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>eflong</example>
<fullName/>
<ytenantId/>
<paramOrder>93</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927807</id>
<name>listTotalPayAmount</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375231</defParamId>
<array>false</array>
<paramDesc>累计付款金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>94</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927808</id>
<name>priceUOM_Name</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375232</defParamId>
<array>false</array>
<paramDesc>计价单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>个</example>
<fullName/>
<ytenantId/>
<paramOrder>95</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927809</id>
<name>listTotalPayNATMoney</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375233</defParamId>
<array>false</array>
<paramDesc>本币累计付款核销金额</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>96</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927810</id>
<name>approvenum</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375234</defParamId>
<array>false</array>
<paramDesc>已审批数量</paramDesc>
<paramType>number</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>97</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals>8</decimals>
<maxLength>28</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927811</id>
<name>listdiscountTaxType</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375235</defParamId>
<array>false</array>
<paramDesc>扣税类别：0：应税外加、1：应税内含</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>98</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927812</id>
<name>bizFlow_name</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<defParamId>1998541234691375236</defParamId>
<array>false</array>
<paramDesc>流程名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>普通订货（订单开票）</example>
<fullName/>
<ytenantId/>
<paramOrder>99</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927710</id>
<name>headItem</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927709</parentId>
<children>
<children>
<id>2094836196761927711</id>
<name>id</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927710</parentId>
<defParamId>1998541234691375238</defParamId>
<array>false</array>
<paramDesc>id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927712</id>
<name>define46</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927710</parentId>
<defParamId>1998541234691375239</defParamId>
<array>false</array>
<paramDesc>自定义项</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>红色</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1998541234691375237</defParamId>
<array>false</array>
<paramDesc>表头自定义项</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>100</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1998541234691375136</defParamId>
<array>true</array>
<paramDesc>返回信息</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927816</id>
<name>pageCount</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927708</parentId>
<defParamId>1998541234691375240</defParamId>
<array>false</array>
<paramDesc>页数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927817</id>
<name>beginPageIndex</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927708</parentId>
<defParamId>1998541234691375241</defParamId>
<array>false</array>
<paramDesc>起始页</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927818</id>
<name>endPageIndex</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927708</parentId>
<defParamId>1998541234691375242</defParamId>
<array>false</array>
<paramDesc>结束页</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2094836196761927819</id>
<name>pubts</name>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<parentId>2094836196761927708</parentId>
<defParamId>1998541234691375243</defParamId>
<array>false</array>
<paramDesc>时间戳，格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-04-23 12:40:06</example>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1998541234691375132</defParamId>
<array>false</array>
<paramDesc>数据项</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2094836196761927827</id>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<content>{ "code": "200", "message": "操作成功", "data": { "pageIndex": 1, "pageSize": 10, "recordCount": 1, "recordList": [ { "product_cCode": "********", "invoiceVendor": ****************, "priceUOM_Precision": 1, "modifyStatus": 0, "receiveStatus": 1, "listOriSum": 1, "priceUOM_Code": "001", "totalInTaxMoney": 0, "totalQuantity": 1, "natCurrency": "G001ZM0000DEFAULTCURRENCT00000000001", "listTotalPayOriMoney": 0, "unit_code": "001", "purchaseOrdersCharacteristics": 0, "purchaseOrdersDefineCharacter": 0, "purchaseOrderDefineCharacter": 0, "id": ****************, "isWfControlled": false, "totalArrivedTaxMoney": 0, "purchaseOrders_arrivedStatus": 2, "bmake_st_purinvoice": true, "realProductAttribute": 1, "purchaseOrders_inWHStatus": 2, "totalSendQty": 0, "natCurrency_priceDigit": 6, "bmake_st_purinrecord_red": true, "status": null, "currency_moneyDigit": 6, "listTotalPayApplyAmount": 0, "currency_code": "CNY", "vouchdate": "2021-04-23 00:00:00", "invoiceVendor_name": "达利园供货目录转移专用供应商", "vendor": ****************, "purchaseOrders_payStatus": 2, "purchaseOrders_warehouse_code": "000001", "listOriMoney": 1, "currency": "G001ZM0000DEFAULTCURRENCT00000000001", "pubts": "2021-04-23 11:34:02", "org_name": "eflong", "generalPurchaseOrderType": "1", "isFlowCoreBill": true, "creator": "17600880447", "product": 1730491724599552, "oriSum": 1, "inInvoiceOrg_name": "eflong", "product_defaultAlbumId": "http://ys-yxy-testres.yonyoucloud.com/fa813c9d-a182-457c-ab2a-a0c40ab113ea.jpg", "purchaseOrders_id": 2227385816011009, "totalRecieveQty": 0, "demandOrg_name": "eflong", "createTime": "2021-04-23 11:34:01", "purUOM_Precision": 1, "currency_priceDigit": 6, "bEffectStock": true, "inOrg": "1730475987734784", "bustype_name": "普通订货-订单开票", "purchaseOrders_invPriceExchRate": 1, "subQty": 1, "inInvoiceOrg": "1730475987734784", "product_cName": "eflong-规格1", "listTaxRate": 0, "bmake_st_purinvoice_red": true, "product_model": "型号", "storagenum": 0, "purchaseOrders_invExchRate": 1, "vendor_name": "达利园供货目录转移专用供应商", "vendor_code": "0001000101", "oriUnitPrice": 1, "barCode": "st_purchaseorder|****************", "isContract": false, "unit_name": "个", "unit": 1730486466924800, "purchaseOrders_invoiceStatus": 2, "natCurrency_moneyDigit": 6, "qty": 1, "unit_Precision": 1, "oriTaxUnitPrice": 1, "moneysum": 1, "natCurrency_code": "CNY", "product_modelDescription": "说明2", "code": "CGA20005000456", "demandOrg": "1730475987734784", "bizFlow": "f596bd30-aee8-11ea-8d5f-0624d60000dc", "realProductAttributeType": 1, "priceUOM": 1730486466924800, "bizstatus": 0, "totalInQty": 0, "bizFlow_version": "V1.0", "currency_name": "人民币", "org": "1730475987734784", "bmake_st_purinrecord": true, "purchaseOrders_purUOM": 1730486466924800, "bustype": "1785637352591616", "listOriTax": 0, "retailInvestors": false, "inOrg_name": "eflong", "listTotalPayAmount": 0, "priceUOM_Name": "个", "listTotalPayNATMoney": 0, "approvenum": 0, "listdiscountTaxType": "0", "bizFlow_name": "普通订货（订单开票）", "headItem": { "id": ****************, "define46": "红色" } } ], "pageCount": 1, "beginPageIndex": 1, "endPageIndex": 1, "pubts": "2021-04-23 12:40:06" } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2094836196761927828</id>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<content>{ "code": 999, "message": "服务端逻辑异常" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2094836196761927827</id>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<content>{ "code": "200", "message": "操作成功", "data": { "pageIndex": 1, "pageSize": 10, "recordCount": 1, "recordList": [ { "product_cCode": "********", "invoiceVendor": ****************, "priceUOM_Precision": 1, "modifyStatus": 0, "receiveStatus": 1, "listOriSum": 1, "priceUOM_Code": "001", "totalInTaxMoney": 0, "totalQuantity": 1, "natCurrency": "G001ZM0000DEFAULTCURRENCT00000000001", "listTotalPayOriMoney": 0, "unit_code": "001", "purchaseOrdersCharacteristics": 0, "purchaseOrdersDefineCharacter": 0, "purchaseOrderDefineCharacter": 0, "id": ****************, "isWfControlled": false, "totalArrivedTaxMoney": 0, "purchaseOrders_arrivedStatus": 2, "bmake_st_purinvoice": true, "realProductAttribute": 1, "purchaseOrders_inWHStatus": 2, "totalSendQty": 0, "natCurrency_priceDigit": 6, "bmake_st_purinrecord_red": true, "status": null, "currency_moneyDigit": 6, "listTotalPayApplyAmount": 0, "currency_code": "CNY", "vouchdate": "2021-04-23 00:00:00", "invoiceVendor_name": "达利园供货目录转移专用供应商", "vendor": ****************, "purchaseOrders_payStatus": 2, "purchaseOrders_warehouse_code": "000001", "listOriMoney": 1, "currency": "G001ZM0000DEFAULTCURRENCT00000000001", "pubts": "2021-04-23 11:34:02", "org_name": "eflong", "generalPurchaseOrderType": "1", "isFlowCoreBill": true, "creator": "17600880447", "product": 1730491724599552, "oriSum": 1, "inInvoiceOrg_name": "eflong", "product_defaultAlbumId": "http://ys-yxy-testres.yonyoucloud.com/fa813c9d-a182-457c-ab2a-a0c40ab113ea.jpg", "purchaseOrders_id": 2227385816011009, "totalRecieveQty": 0, "demandOrg_name": "eflong", "createTime": "2021-04-23 11:34:01", "purUOM_Precision": 1, "currency_priceDigit": 6, "bEffectStock": true, "inOrg": "1730475987734784", "bustype_name": "普通订货-订单开票", "purchaseOrders_invPriceExchRate": 1, "subQty": 1, "inInvoiceOrg": "1730475987734784", "product_cName": "eflong-规格1", "listTaxRate": 0, "bmake_st_purinvoice_red": true, "product_model": "型号", "storagenum": 0, "purchaseOrders_invExchRate": 1, "vendor_name": "达利园供货目录转移专用供应商", "vendor_code": "0001000101", "oriUnitPrice": 1, "barCode": "st_purchaseorder|****************", "isContract": false, "unit_name": "个", "unit": 1730486466924800, "purchaseOrders_invoiceStatus": 2, "natCurrency_moneyDigit": 6, "qty": 1, "unit_Precision": 1, "oriTaxUnitPrice": 1, "moneysum": 1, "natCurrency_code": "CNY", "product_modelDescription": "说明2", "code": "CGA20005000456", "demandOrg": "1730475987734784", "bizFlow": "f596bd30-aee8-11ea-8d5f-0624d60000dc", "realProductAttributeType": 1, "priceUOM": 1730486466924800, "bizstatus": 0, "totalInQty": 0, "bizFlow_version": "V1.0", "currency_name": "人民币", "org": "1730475987734784", "bmake_st_purinrecord": true, "purchaseOrders_purUOM": 1730486466924800, "bustype": "1785637352591616", "listOriTax": 0, "retailInvestors": false, "inOrg_name": "eflong", "listTotalPayAmount": 0, "priceUOM_Name": "个", "listTotalPayNATMoney": 0, "approvenum": 0, "listdiscountTaxType": "0", "bizFlow_name": "普通订货（订单开票）", "headItem": { "id": ****************, "define46": "红色" } } ], "pageCount": 1, "beginPageIndex": 1, "endPageIndex": 1, "pubts": "2021-04-23 12:40:06" } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2094836196761927828</id>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<content>{ "code": 999, "message": "服务端逻辑异常" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>2094836196761927827</id>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<content>{ "code": "200", "message": "操作成功", "data": { "pageIndex": 1, "pageSize": 10, "recordCount": 1, "recordList": [ { "product_cCode": "********", "invoiceVendor": ****************, "priceUOM_Precision": 1, "modifyStatus": 0, "receiveStatus": 1, "listOriSum": 1, "priceUOM_Code": "001", "totalInTaxMoney": 0, "totalQuantity": 1, "natCurrency": "G001ZM0000DEFAULTCURRENCT00000000001", "listTotalPayOriMoney": 0, "unit_code": "001", "purchaseOrdersCharacteristics": 0, "purchaseOrdersDefineCharacter": 0, "purchaseOrderDefineCharacter": 0, "id": ****************, "isWfControlled": false, "totalArrivedTaxMoney": 0, "purchaseOrders_arrivedStatus": 2, "bmake_st_purinvoice": true, "realProductAttribute": 1, "purchaseOrders_inWHStatus": 2, "totalSendQty": 0, "natCurrency_priceDigit": 6, "bmake_st_purinrecord_red": true, "status": null, "currency_moneyDigit": 6, "listTotalPayApplyAmount": 0, "currency_code": "CNY", "vouchdate": "2021-04-23 00:00:00", "invoiceVendor_name": "达利园供货目录转移专用供应商", "vendor": ****************, "purchaseOrders_payStatus": 2, "purchaseOrders_warehouse_code": "000001", "listOriMoney": 1, "currency": "G001ZM0000DEFAULTCURRENCT00000000001", "pubts": "2021-04-23 11:34:02", "org_name": "eflong", "generalPurchaseOrderType": "1", "isFlowCoreBill": true, "creator": "17600880447", "product": 1730491724599552, "oriSum": 1, "inInvoiceOrg_name": "eflong", "product_defaultAlbumId": "http://ys-yxy-testres.yonyoucloud.com/fa813c9d-a182-457c-ab2a-a0c40ab113ea.jpg", "purchaseOrders_id": 2227385816011009, "totalRecieveQty": 0, "demandOrg_name": "eflong", "createTime": "2021-04-23 11:34:01", "purUOM_Precision": 1, "currency_priceDigit": 6, "bEffectStock": true, "inOrg": "1730475987734784", "bustype_name": "普通订货-订单开票", "purchaseOrders_invPriceExchRate": 1, "subQty": 1, "inInvoiceOrg": "1730475987734784", "product_cName": "eflong-规格1", "listTaxRate": 0, "bmake_st_purinvoice_red": true, "product_model": "型号", "storagenum": 0, "purchaseOrders_invExchRate": 1, "vendor_name": "达利园供货目录转移专用供应商", "vendor_code": "0001000101", "oriUnitPrice": 1, "barCode": "st_purchaseorder|****************", "isContract": false, "unit_name": "个", "unit": 1730486466924800, "purchaseOrders_invoiceStatus": 2, "natCurrency_moneyDigit": 6, "qty": 1, "unit_Precision": 1, "oriTaxUnitPrice": 1, "moneysum": 1, "natCurrency_code": "CNY", "product_modelDescription": "说明2", "code": "CGA20005000456", "demandOrg": "1730475987734784", "bizFlow": "f596bd30-aee8-11ea-8d5f-0624d60000dc", "realProductAttributeType": 1, "priceUOM": 1730486466924800, "bizstatus": 0, "totalInQty": 0, "bizFlow_version": "V1.0", "currency_name": "人民币", "org": "1730475987734784", "bmake_st_purinrecord": true, "purchaseOrders_purUOM": 1730486466924800, "bustype": "1785637352591616", "listOriTax": 0, "retailInvestors": false, "inOrg_name": "eflong", "listTotalPayAmount": 0, "priceUOM_Name": "个", "listTotalPayNATMoney": 0, "approvenum": 0, "listdiscountTaxType": "0", "bizFlow_name": "普通订货（订单开票）", "headItem": { "id": ****************, "define46": "红色" } } ], "pageCount": 1, "beginPageIndex": 1, "endPageIndex": 1, "pubts": "2021-04-23 12:40:06" } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>2094836196761927828</id>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<content>{ "code": 999, "message": "服务端逻辑异常" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS>
<errorCodeDTOS>
<id>2094836196761927824</id>
<apiId>b20cfa042e5848309e96c689158c17d1</apiId>
<errorCode>999</errorCode>
<errorMessage>服务端逻辑异常</errorMessage>
<errorType>API</errorType>
<errorcodeDesc/>
<gmtCreate>2024-09-23 13:57:10.000</gmtCreate>
<gmtUpdate>2024-09-23 13:57:10.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<defErrorId>1998541234691375358</defErrorId>
<ytenantId/>
<displayCodeId/>
</errorCodeDTOS>
</errorCodeDTOS>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>友户通token认证业务扩展插件</name>
<configurable>false</configurable>
<description>YonsuitBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>b20cfa042e5848309e96c689158c17d1</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin>
<id>w181ed01-1e9b-4350-b994-71a66f017555</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code>resultParse</code>
<name>返回参数转换插件</name>
<configurable>false</configurable>
<description>解决返回值中带！的，转换为json</description>
<pluginType>resultParse</pluginType>
<pluginTypeName>返回值解析插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.result.ResultMapTransferParsePlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>true</visible>
<gmtCreate>2020-07-29 00:00:00</gmtCreate>
<gmtUpdate/>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>b20cfa042e5848309e96c689158c17d1</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</resultParsePlugin>
<mapReturnPluginConfig/>
<billNo>st_purchaseorderlist</billNo>
<domain>upu</domain>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser/>
<createUserName/>
<approvalStatus>1</approvalStatus>
<publishTime>2024-09-23 13:57:57</publishTime>
<pathJoin>true</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout/>
<customUrl>/purchaseorder/list</customUrl>
<fixedUrl>/yonbip/scm</fixedUrl>
<apiCode>b20cfa042e5848309e96c689158c17d1</apiCode>
<tokenCheckType>0</tokenCheckType>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL/>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>53434e39-298d-4db4-8896-cdd073972fff</updateUserId>
<updateUserName>昵称-17600880447</updateUserName>
<paramIsForce/>
<userIDPassthrough>false</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.yonbip-scm-pu</microServiceCode>
<applicationCode>yonbip-scm-pubiz</applicationCode>
<privacyCategory>0</privacyCategory>
<privacyLevel>3</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType>0</serviceType>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType/>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>false</arrayParam>
<fileSize/>
<cc>true</cc>
<paramTransferMode>2</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId>1998541234691375108</apiDefId>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>1</paramExtRequest>
<paramExtResponse>1</paramExtResponse>
<paramExtInExtendKey>1</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene/>
<apiType/>
<paramMark/>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>false</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>false</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>1</chargeStatus>
<beforeSpeed>60</beforeSpeed>
<afterSpeed>120</afterSpeed>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath/>
<pubHistory/>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode/>
<multiVersion>0</multiVersion>
<apiTag/>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>2108770660671029249</id>
<name>用友YonBIP</name>
<type>integrateSys</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>SCC</id>
<name>供应链云</name>
<type>1</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MM</id>
<name>采购供应</name>
<type>2</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>PU</id>
<name>采购管理</name>
<type>3</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>upu.st_purchaseorder</id>
<name>采购订单</name>
<type>4</type>
<sort>0</sort>
<enable>0</enable>
<children/>
<parentId/>
<productId/>
<code>upu.st_purchaseorder</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>PU</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MM</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>SCC</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>current_yonbip_default_sys</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<isOrigin>0</isOrigin>
<hasChildren>0</hasChildren>
<order>0</order>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>60182ff8-30c4-46a4-96c6-3ce0bebe4fee</id>
<name>U9002</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>U9采购订单号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:09:56</gmtCreate>
<gmtUpdate>2025-07-26 17:09:56</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>100</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>8c558603-24fe-4b30-86ba-bcf97d4f8599</id>
<name>XS31</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>更改次数</paramDesc>
<paramType>Decimal</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>number</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:09:56</gmtCreate>
<gmtUpdate>2025-07-26 17:09:56</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>24</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:09:56</gmtCreate>
<gmtUpdate>2025-07-26 17:09:56</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>31be15d2-ceb1-4d59-a557-3351a8d14ffe</id>
<name>AA</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>批次号（扩展）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:14:41</gmtCreate>
<gmtUpdate>2025-07-26 17:14:41</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>7421f1ce-8268-44b1-a8eb-49ffdcff5afb</id>
<name>CG00025</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>未收数量2</paramDesc>
<paramType>Decimal</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>number</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:14:41</gmtCreate>
<gmtUpdate>2025-07-26 17:14:41</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>24</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>176f1ed5-5d35-4fa7-9e52-a513b8e5b169</id>
<name>CG01</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>供应商备注（扩展）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:14:41</gmtCreate>
<gmtUpdate>2025-07-26 17:14:41</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>15ffdb7a-ab8b-498d-b7c5-55b5089a4dfe</id>
<name>WW</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>委外交货日期</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>date</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:14:41</gmtCreate>
<gmtUpdate>2025-07-26 17:14:41</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>ae2457c2-816f-49ff-a4d5-bf06196d59a6</id>
<name>XS11</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类号test</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:14:41</gmtCreate>
<gmtUpdate>2025-07-26 17:14:41</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>a3d1773f-3d33-4a0e-bc02-95f55ec955c9</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:14:41</gmtCreate>
<gmtUpdate>2025-07-26 17:14:41</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:14:41</gmtCreate>
<gmtUpdate>2025-07-26 17:14:41</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>60182ff8-30c4-46a4-96c6-3ce0bebe4fee</id>
<name>U9002</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>U9采购订单号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:14:58</gmtCreate>
<gmtUpdate>2025-07-26 17:14:58</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>100</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>8c558603-24fe-4b30-86ba-bcf97d4f8599</id>
<name>XS31</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>更改次数</paramDesc>
<paramType>Decimal</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>number</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:14:58</gmtCreate>
<gmtUpdate>2025-07-26 17:14:58</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>24</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:14:58</gmtCreate>
<gmtUpdate>2025-07-26 17:14:58</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>f458f625-a28f-44ee-82dd-07d4e8c6315e</id>
<name>XS11</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类号test</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:15:52</gmtCreate>
<gmtUpdate>2025-07-26 17:15:52</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>62cb3b8d-2fe7-4fab-8456-14260b6434cc</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:15:52</gmtCreate>
<gmtUpdate>2025-07-26 17:15:52</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:15:52</gmtCreate>
<gmtUpdate>2025-07-26 17:15:52</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>72113971-ae4c-4188-bc55-44b6173f4e0b</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:16:03</gmtCreate>
<gmtUpdate>2025-07-26 17:16:03</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>b946709d-f4d9-4a43-a551-f55beee7f3d5</id>
<name>XXX0111</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类项</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:16:03</gmtCreate>
<gmtUpdate>2025-07-26 17:16:03</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:16:03</gmtCreate>
<gmtUpdate>2025-07-26 17:16:03</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>