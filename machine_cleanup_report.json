{"cleaned_files": [], "formatted_files": [], "removed_imports": [], "duplicate_files": [], "errors": [], "error": ["autoflake失败: [<PERSON><PERSON><PERSON> 13] Permission denied: '.\\\\backend\\\\app\\\\services\\\\zero_downtime_implementation.py'\n", "black格式化失败: reformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\.cleanup_trash\\__init__.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\sync_status.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\schemas\\__init__.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\middleware\\access_log.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\__init__.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\core\\database.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\analyze_project_stats.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\__init__.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\schemas\\database.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\__init__.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\schemas\\base.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\schemas\\realtime_log.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\schemas\\monitor.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\auth.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\schemas\\sync.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\database_health.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\schemas\\config.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\auto_project_cleanup.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\enhanced_sync.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\core\\optimized_retry.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\maintenance.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\main.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\database.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\excel_translation.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\main_original.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\core\\database_manager.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\business_translation_rules.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\realtime_logs.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\config_persistence_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\excel_field_matcher_pretranslated.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\core\\config.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\enhanced_json_field_matcher.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\data_processor.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\monitor_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\log_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\core\\exceptions.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\realtime_log_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\token_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\task_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\core\\database_connection_pool.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\field_config_api.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\field_config_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\field_validation_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\start_server.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\excel_field_matcher.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\auto_sync_scheduler.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\config.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\field_analysis_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\auto_fix_comprehensive_issues.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\tasks.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\config_environmentizer.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\retry_helper.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\sync_status_manager.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\start_simple.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\config_environmentizer_clean.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\auto_recovery_manager_enhanced.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\maintenance_manager.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\check_naming_conflicts.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\core\\code_quality.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\material_master_scheduler.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\status_mapping_service.py\nerror: cannot format D:\\OneDrive\\Desktop\\YS-API程序\\v3\\fix_issues.py: Cannot parse: 70:24:             logger.info(\"\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\intelligent_field_mapper.py\nerror: cannot format D:\\OneDrive\\Desktop\\YS-API程序\\v3\\project_health_check.py: Cannot parse: 69:0:         \"\"\"检查Python环境\"\"\"\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\build_production_package.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\function_duplicate_checker.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\unified_field_config.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\dev-tools\\cleanup\\code_cleaner.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\fix_execute_task_script.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\install_windows_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\file_sentinel.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\database_manager.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\fix_build_script.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\final_project_fixer.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\add_api_config.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\dev-tools\\mock\\mock_utils.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\frontend_test_server.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\field_extractor_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\field_value_mapping_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\fix_migrated_paths.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\reliable_server.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\clean_debug_code.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\setup_test_env.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cicd_pipeline_builder_optimized.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\md_parser.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\test_health_checker.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\test_sentinel.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\machine_cleanup.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\cicd_pipeline_builder.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\fix_css_paths.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\robust_json_parser.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\diagnose_migration.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\verify_fixes.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\unified_field_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\start_quick_test.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\tests\\api_test_server.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\test_anti_duplicate_system.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\quick_health_check.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\fix_task_issues.py\nerror: cannot format D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\zero_downtime_implementation.py: [Errno 13] Permission denied: 'D:\\\\OneDrive\\\\Desktop\\\\YS-API程序\\\\v3\\\\backend\\\\app\\\\services\\\\zero_downtime_implementation.py'\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\monitor.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\unused_import_checker.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\rollback_batch_writes.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\unified_field_manager.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\run_comprehensive_check.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\remaining_issues_fixer.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\tools\\md_to_json_converter.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\api\\v1\\sync.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\smart_duplicate_cleaner.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\smart_file_creator.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\execute_task_checklist.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\tests\\test_baseline_api.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\tests\\test_rollback_scripts.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\tests\\locust_stress_test.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\comprehensive_production_test.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\auto_migration.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\systematic_duplicate_detector.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\production_readiness_report.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\database_table_manager.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\validate_deployment.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\tests\\test_md_to_json_converter.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\scripts\\test_elk_connection.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\tools\\error_handling_load_test.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\ys_api_client.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\fast_sync_service.py\nreformatted D:\\OneDrive\\Desktop\\YS-API程序\\v3\\backend\\app\\services\\data_write_manager.py\n\nOh no! \\U0001f4a5 \\U0001f494 \\U0001f4a5\n123 files reformatted, 9 files left unchanged, 3 files failed to reformat.\n"], "duplicate_removal": ["删除重复文件: .\\test_health_checker.py.done (原文件: .\\check_output.txt)", "删除重复文件: .\\test_sentinel.py.done (原文件: .\\check_output.txt)", "删除重复文件: .\\templates\\dockerignore.template (原文件: .\\.dockerignore)"], "backup_cleanup": ["删除备份文件: .\\config_environmentizer.py.broken_backup", "删除备份文件: .\\final_project_fixer.py.backup_20250802_202310", "删除备份文件: .\\fix_build_script.py.backup_20250802_202310", "删除备份文件: .\\fix_execute_task_script.py.backup_20250802_202310", "删除备份文件: .\\fix_issues.py.backup_20250802_202310", "删除备份文件: .\\fix_task_issues.py.backup_20250802_202310", "删除备份文件: .\\install_windows_service.py.backup_20250802_202310", "删除备份文件: .\\project_health_check.py.backup_20250802_202310", "删除备份文件: .\\project_health_check.py.backup_final_print", "删除备份文件: .\\project_health_check.py.backup_todo_fixme", "删除备份文件: .\\project_health_checker.py.backup_20250802_202310", "删除备份文件: .\\run_comprehensive_check.py.backup_20250802_202310", "删除备份文件: .\\run_comprehensive_check.py.backup_todo_fixme", "删除备份文件: .\\start_quick_test.py.backup_20250802_202310", "删除备份文件: .\\test_baseline_api.py.backup_20250802_202310", "删除备份文件: .\\universal_code_quality_fixer.py.backup_20250802_202310", "删除备份文件: .\\backend\\config.ini.backup_env", "删除备份文件: .\\backend\\start_server.py.backup_20250802_202310", "删除备份文件: .\\backend\\start_simple.py.backup_20250802_202310", "删除备份文件: .\\backend\\app\\main.py.backup_20250802_202310", "删除备份文件: .\\backend\\app\\api\\v1\\config.py.backup_20250802_202310", "删除备份文件: .\\backend\\app\\core\\config.py.backup_20250802_202310", "删除备份文件: .\\backend\\app\\services\\robust_json_parser.py.backup_20250802_202310", "删除备份文件: .\\backend\\app\\services\\unified_field_service.py.backup_20250802_202310", "删除备份文件: .\\dev-tools\\cleanup\\code_cleaner.py.backup_20250802_202310", "删除备份文件: .\\dev-tools\\mock\\mock_utils.py.backup_20250802_202310", "删除备份文件: .\\scripts\\add_api_config.py.backup_20250802_202310", "删除备份文件: .\\scripts\\auto_migration.py.backup_20250802_202310", "删除备份文件: .\\scripts\\diagnose_migration.py.backup_20250802_202310", "删除备份文件: .\\scripts\\fix_css_paths.py.backup_20250802_202310", "删除备份文件: .\\scripts\\fix_migrated_paths.py.backup_20250802_202310", "删除备份文件: .\\scripts\\reliable_server.py.backup_20250802_202310", "删除备份文件: .\\scripts\\rollback_batch_writes.py.backup_20250802_202310", "删除备份文件: .\\scripts\\test_elk_connection.py.backup_20250802_202310", "删除备份文件: .\\scripts\\test_server.py.backup_20250802_202310", "删除备份文件: .\\scripts\\validate_deployment.py.backup_20250802_202310", "删除备份文件: .\\scripts\\verify_fixes.py.backup_20250802_202310", "删除备份文件: .\\tests\\test_md_to_json_converter.py.backup_20250802_202310", "删除备份文件: .\\tools\\error_handling_load_test.py.backup_20250802_202310"], "cache_cleanup": ["删除缓存目录: .\\__pycache__", "删除缓存目录: .\\backend\\__pycache__", "删除缓存目录: .\\backend\\app\\api\\__pycache__", "删除缓存目录: .\\backend\\app\\api\\v1\\__pycache__", "删除缓存目录: .\\backend\\app\\core\\__pycache__", "删除缓存目录: .\\backend\\app\\middleware\\__pycache__", "删除缓存目录: .\\backend\\app\\schemas\\__pycache__", "删除缓存目录: .\\backend\\app\\services\\__pycache__", "删除缓存目录: .\\scripts\\__pycache__", "删除缓存目录: .\\tests\\__pycache__", "删除缓存目录: .\\tools\\__pycache__"]}