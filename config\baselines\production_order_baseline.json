{"module_name": "production_order", "display_name": "生产订单", "version": "2.0.0", "source": "json_parser", "total_fields": 335, "created_at": "2025-07-28T20:12:24.847968", "last_updated": "2025-07-28T20:12:24.847968", "fields": {"code": {"api_field_name": "code", "chinese_name": "生产订单号", "data_type": "NVARCHAR(500)", "param_desc": "生产订单号", "path": "data.recordList.code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "message": {"api_field_name": "message", "chinese_name": "调用失败时的错误信息", "data_type": "NVARCHAR(500)", "param_desc": "调用失败时的错误信息", "path": "message", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "调用成功时的返回数据", "data_type": "NVARCHAR(MAX)", "param_desc": "调用成功时的返回数据", "path": "data", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageIndex": {"api_field_name": "pageIndex", "chinese_name": "页号 默认值:1", "data_type": "BIGINT", "param_desc": "页号 默认值:1", "path": "pageIndex", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageSize": {"api_field_name": "pageSize", "chinese_name": "每页行数 默认值:10", "data_type": "BIGINT", "param_desc": "每页行数 默认值:10", "path": "pageSize", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordCount": {"api_field_name": "recordCount", "chinese_name": "记录总数", "data_type": "BIGINT", "param_desc": "记录总数", "path": "data.recordCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordList": {"api_field_name": "recordList", "chinese_name": "返回数据对象", "data_type": "NVARCHAR(MAX)", "param_desc": "返回数据对象", "path": "data.recordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_materialName": {"api_field_name": "OrderProduct_materialName", "chinese_name": "物料名称", "data_type": "NVARCHAR(500)", "param_desc": "物料名称", "path": "data.recordList.OrderProduct_materialName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_startDate": {"api_field_name": "OrderProduct_startDate", "chinese_name": "开工日期", "data_type": "NVARCHAR(500)", "param_desc": "开工日期", "path": "data.recordList.OrderProduct_startDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_lineNo": {"api_field_name": "OrderProduct_lineNo", "chinese_name": "行号", "data_type": "BIGINT", "param_desc": "行号", "path": "data.recordList.OrderProduct_lineNo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productUnitPrecision": {"api_field_name": "productUnitPrecision", "chinese_name": "生产单位精度", "data_type": "BIGINT", "param_desc": "生产单位精度", "path": "data.sumRecordList.productUnitPrecision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_scrap": {"api_field_name": "OrderProduct_scrap", "chinese_name": "废品率%", "data_type": "BIGINT", "param_desc": "废品率%", "path": "data.recordList.OrderProduct_scrap", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_orgId": {"api_field_name": "OrderProduct_orgId", "chinese_name": "库存组织id", "data_type": "NVARCHAR(500)", "param_desc": "库存组织id", "path": "data.recordList.OrderProduct_orgId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_skuCode": {"api_field_name": "OrderProduct_skuCode", "chinese_name": "物料SKU编码", "data_type": "NVARCHAR(500)", "param_desc": "物料SKU编码", "path": "data.recordList.OrderProduct_skuCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_materialId": {"api_field_name": "OrderProduct_materialId", "chinese_name": "制造物料Id", "data_type": "BIGINT", "param_desc": "制造物料Id", "path": "data.recordList.OrderProduct_materialId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "transTypeId": {"api_field_name": "transTypeId", "chinese_name": "交易类型", "data_type": "NVARCHAR(500)", "param_desc": "交易类型", "path": "transTypeId", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "mainUnitPrecision": {"api_field_name": "mainUnitPrecision", "chinese_name": "主计量精度", "data_type": "BIGINT", "param_desc": "主计量精度", "path": "data.sumRecordList.mainUnitPrecision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "id": {"api_field_name": "id", "chinese_name": "生产订单", "data_type": "BIGINT", "param_desc": "生产订单", "path": "id", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_sourceType": {"api_field_name": "OrderProduct_sourceType", "chinese_name": "来源单据类型：1-无来源，2-计划订单，3-销售订单，4-生产订单，5-完工报告，18-项目物资清单", "data_type": "NVARCHAR(500)", "param_desc": "来源单据类型：1-无来源，2-计划订单，3-销售订单，4-生产订单，5-完工报告，18-项目物资清单", "path": "data.recordList.OrderProduct_sourceType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_productId": {"api_field_name": "OrderProduct_productId", "chinese_name": "物料Id", "data_type": "BIGINT", "param_desc": "物料Id", "path": "data.recordList.OrderProduct_productId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_mrpQuantity": {"api_field_name": "OrderProduct_mrpQuantity", "chinese_name": "净算量", "data_type": "BIGINT", "param_desc": "净算量", "path": "data.sumRecordList.OrderProduct_mrpQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_changeType": {"api_field_name": "OrderProduct_changeType", "chinese_name": "换算方式：0-固定换算，1-浮动换算", "data_type": "BIGINT", "param_desc": "换算方式：0-固定换算，1-浮动换算", "path": "data.recordList.OrderProduct_changeType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "departmentName": {"api_field_name": "departmentName", "chinese_name": "生产部门", "data_type": "NVARCHAR(500)", "param_desc": "生产部门", "path": "data.recordList.departmentName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orgName": {"api_field_name": "orgName", "chinese_name": "组织", "data_type": "NVARCHAR(500)", "param_desc": "组织", "path": "data.recordList.orderProcess.orgName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "auditTime": {"api_field_name": "auditTime", "chinese_name": "审核时间", "data_type": "DATETIME", "param_desc": "审核时间", "path": "data.recordList.auditTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auditDate": {"api_field_name": "auditDate", "chinese_name": "审核日期", "data_type": "DATE", "param_desc": "审核日期", "path": "data.recordList.auditDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isWfControlled": {"api_field_name": "isWfControlled", "chinese_name": "是否审批流控制：false-否，true-是", "data_type": "BIT", "param_desc": "是否审批流控制：false-否，true-是", "path": "data.recordList.isWfControlled", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_quantity": {"api_field_name": "OrderProduct_quantity", "chinese_name": "生产数量", "data_type": "BIGINT", "param_desc": "生产数量", "path": "data.sumRecordList.OrderProduct_quantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_completedQuantity": {"api_field_name": "OrderProduct_completedQuantity", "chinese_name": "已完工数量", "data_type": "DECIMAL(18,4)", "param_desc": "已完工数量", "path": "data.recordList.OrderProduct_completedQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_incomingQuantity": {"api_field_name": "OrderProduct_incomingQuantity", "chinese_name": "累计入库数量", "data_type": "DECIMAL(18,4)", "param_desc": "累计入库数量", "path": "data.recordList.OrderProduct_incomingQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isHold": {"api_field_name": "isHold", "chinese_name": "挂起状态：false-否，true-是", "data_type": "BIT", "param_desc": "挂起状态：false-否，true-是", "path": "data.recordList.isHold", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_skuName": {"api_field_name": "OrderProduct_skuName", "chinese_name": "物料SKU名称", "data_type": "NVARCHAR(500)", "param_desc": "物料SKU名称", "path": "data.recordList.OrderProduct_skuName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "routingVersion": {"api_field_name": "routingVersion", "chinese_name": "工艺路线版本", "data_type": "NVARCHAR(500)", "param_desc": "工艺路线版本", "path": "data.recordList.routingVersion", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "routingCode": {"api_field_name": "routingCode", "chinese_name": "工艺路线编码", "data_type": "NVARCHAR(500)", "param_desc": "工艺路线编码", "path": "data.recordList.routingCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "routingId": {"api_field_name": "routingId", "chinese_name": "工艺路线Id", "data_type": "BIGINT", "param_desc": "工艺路线Id", "path": "data.recordList.routingId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_completedFlag": {"api_field_name": "OrderProduct_completedFlag", "chinese_name": "启用完工报告：false-否，true-是", "data_type": "BIT", "param_desc": "启用完工报告：false-否，true-是", "path": "data.recordList.OrderProduct_completedFlag", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_materialCode": {"api_field_name": "OrderProduct_materialCode", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "data.recordList.OrderProduct_materialCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_productionUnitId": {"api_field_name": "OrderProduct_productionUnitId", "chinese_name": "生产单位ID", "data_type": "BIGINT", "param_desc": "生产单位ID", "path": "data.recordList.OrderProduct_productionUnitId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "status": {"api_field_name": "status", "chinese_name": "订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工", "data_type": "NVARCHAR(500)", "param_desc": "订单状态： 0-开立，1-已审核，2-已关闭，3-审核中，4-已锁定，5-已开工", "path": "status", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "returncount": {"api_field_name": "returncount", "chinese_name": "退回次数", "data_type": "BIGINT", "param_desc": "退回次数", "path": "data.recordList.returncount", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "routingName": {"api_field_name": "routingName", "chinese_name": "工艺路线名称", "data_type": "NVARCHAR(500)", "param_desc": "工艺路线名称", "path": "data.recordList.routingName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "verifystate": {"api_field_name": "verifystate", "chinese_name": "审批状态：0-开立，1-已提交，2-已审批，-1-驳回", "data_type": "BIGINT", "param_desc": "审批状态：0-开立，1-已提交，2-已审批，-1-驳回", "path": "data.recordList.verifystate", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "creatorId": {"api_field_name": "creatorId", "chinese_name": "创建人Id", "data_type": "BIGINT", "param_desc": "创建人Id", "path": "data.recordList.creatorId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderProduct_id": {"api_field_name": "orderProduct_id", "chinese_name": "订单产品行Id", "data_type": "BIGINT", "param_desc": "订单产品行Id", "path": "data.recordList.orderProduct_id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orgId": {"api_field_name": "orgId", "chinese_name": "工厂", "data_type": "NVARCHAR(500)", "param_desc": "工厂", "path": "orgId", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "vouchdate": {"api_field_name": "vouchdate", "chinese_name": "单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "data_type": "NVARCHAR(500)", "param_desc": "单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "path": "vouchdate", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_auxiliaryQuantity": {"api_field_name": "OrderProduct_auxiliaryQuantity", "chinese_name": "生产件数", "data_type": "BIGINT", "param_desc": "生产件数", "path": "data.sumRecordList.OrderProduct_auxiliaryQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_materialApplyFlag": {"api_field_name": "OrderProduct_materialApplyFlag", "chinese_name": "启用领料申请：false-否，true-是", "data_type": "BIT", "param_desc": "启用领料申请：false-否，true-是", "path": "data.recordList.OrderProduct_materialApplyFlag", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_mainUnit": {"api_field_name": "OrderProduct_mainUnit", "chinese_name": "主计量Id", "data_type": "BIGINT", "param_desc": "主计量Id", "path": "data.recordList.OrderProduct_mainUnit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_mainUnitTruncationType": {"api_field_name": "OrderProduct_mainUnitTruncationType", "chinese_name": "主计量舍位方式: 0-入位，1-舍位，4-四舍五入", "data_type": "BIGINT", "param_desc": "主计量舍位方式: 0-入位，1-舍位，4-四舍五入", "path": "data.recordList.OrderProduct_mainUnitTruncationType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "transTypeName": {"api_field_name": "transTypeName", "chinese_name": "交易类型名称", "data_type": "NVARCHAR(500)", "param_desc": "交易类型名称", "path": "data.recordList.transTypeName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pubts": {"api_field_name": "pubts", "chinese_name": "时间戳", "data_type": "NVARCHAR(500)", "param_desc": "时间戳", "path": "data.pubts", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_skuId": {"api_field_name": "OrderProduct_skuId", "chinese_name": "物料SKUId", "data_type": "BIGINT", "param_desc": "物料SKUId", "path": "data.recordList.OrderProduct_skuId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_productUnitTruncationType": {"api_field_name": "OrderProduct_productUnitTruncationType", "chinese_name": "生产单位舍位方式: 0-入位，1-舍位，4-四舍五入", "data_type": "BIGINT", "param_desc": "生产单位舍位方式: 0-入位，1-舍位，4-四舍五入", "path": "data.recordList.OrderProduct_productUnitTruncationType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "entrustProcessType": {"api_field_name": "entrustProcessType", "chinese_name": "受托加工方式:2-全程受托加工方式;3-工序受托加工方式", "data_type": "NVARCHAR(500)", "param_desc": "受托加工方式:2-全程受托加工方式;3-工序受托加工方式", "path": "data.recordList.entrustProcessType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_retMaterialApplyFlag": {"api_field_name": "OrderProduct_retMaterialApplyFlag", "chinese_name": "启用退料申请：0-否,1-是", "data_type": "NVARCHAR(500)", "param_desc": "启用退料申请：0-否,1-是", "path": "data.recordList.OrderProduct_retMaterialApplyFlag", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_orgName": {"api_field_name": "OrderProduct_orgName", "chinese_name": "库存组织", "data_type": "NVARCHAR(500)", "param_desc": "库存组织", "path": "data.recordList.OrderProduct_orgName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "creator": {"api_field_name": "creator", "chinese_name": "创建人", "data_type": "NVARCHAR(500)", "param_desc": "创建人", "path": "data.recordList.creator", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_finishDate": {"api_field_name": "OrderProduct_finishDate", "chinese_name": "完工日期", "data_type": "NVARCHAR(500)", "param_desc": "完工日期", "path": "data.recordList.OrderProduct_finishDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_changeRate": {"api_field_name": "OrderProduct_changeRate", "chinese_name": "换算率", "data_type": "BIGINT", "param_desc": "换算率", "path": "data.recordList.OrderProduct_changeRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_isHold": {"api_field_name": "OrderProduct_isHold", "chinese_name": "挂起状态：false-否，true-是", "data_type": "BIT", "param_desc": "挂起状态：false-否，true-是", "path": "data.recordList.OrderProduct_isHold", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "entrustCustomer": {"api_field_name": "entrustCustomer", "chinese_name": "受托客户id", "data_type": "BIGINT", "param_desc": "受托客户id", "path": "data.recordList.entrustCustomer", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_versionCode": {"api_field_name": "OrderProduct_versionCode", "chinese_name": "BOM版本", "data_type": "NVARCHAR(500)", "param_desc": "BOM版本", "path": "data.recordList.OrderProduct_versionCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_bomId": {"api_field_name": "OrderProduct_bomId", "chinese_name": "物料清单Id", "data_type": "BIGINT", "param_desc": "物料清单Id", "path": "data.recordList.OrderProduct_bomId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_productUnitName": {"api_field_name": "OrderProduct_productUnitName", "chinese_name": "生产单位", "data_type": "NVARCHAR(500)", "param_desc": "生产单位", "path": "data.recordList.OrderProduct_productUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_mainUnitName": {"api_field_name": "OrderProduct_mainUnitName", "chinese_name": "主计量", "data_type": "NVARCHAR(500)", "param_desc": "主计量", "path": "data.recordList.OrderProduct_mainUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_materialApplyStatus": {"api_field_name": "OrderProduct_materialApplyStatus", "chinese_name": "领料申请状态：0-未申领，1-部分申领，2-全部申领", "data_type": "NVARCHAR(500)", "param_desc": "领料申请状态：0-未申领，1-部分申领，2-全部申领", "path": "data.recordList.OrderProduct_materialApplyStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_materialStatus": {"api_field_name": "OrderProduct_materialStatus", "chinese_name": "领料状态：0-未领料，1-部分领用，2-全部领用", "data_type": "NVARCHAR(500)", "param_desc": "领料状态：0-未领料，1-部分领用，2-全部领用", "path": "data.recordList.OrderProduct_materialStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "entrustCustomerName": {"api_field_name": "entrustCustomerName", "chinese_name": "受托客户", "data_type": "NVARCHAR(500)", "param_desc": "受托客户", "path": "data.recordList.entrustCustomerName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_finishedWorkApplyStatus": {"api_field_name": "OrderProduct_finishedWorkApplyStatus", "chinese_name": "完工申报状态：0-未申报，1-部分申报，2-全部申报", "data_type": "NVARCHAR(500)", "param_desc": "完工申报状态：0-未申报，1-部分申报，2-全部申报", "path": "data.recordList.OrderProduct_finishedWorkApplyStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_stockStatus": {"api_field_name": "OrderProduct_stockStatus", "chinese_name": "入库状态：0-未入库，1-部分入库，2-全部入库", "data_type": "NVARCHAR(500)", "param_desc": "入库状态：0-未入库，1-部分入库，2-全部入库", "path": "data.recordList.OrderProduct_stockStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "createTime": {"api_field_name": "createTime", "chinese_name": "创建时间（区间，格式2021-03-02|2021-03-02 23:59:59）", "data_type": "NVARCHAR(500)", "param_desc": "创建时间（区间，格式2021-03-02|2021-03-02 23:59:59）", "path": "createTime", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productionDepartmentId": {"api_field_name": "productionDepartmentId", "chinese_name": "部门", "data_type": "NVARCHAR(500)", "param_desc": "部门", "path": "productionDepartmentId", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "offChartReceiptIsAllowed": {"api_field_name": "offChartReceiptIsAllowed", "chinese_name": "允许表外产出:false-否，true-是", "data_type": "BIT", "param_desc": "允许表外产出:false-否，true-是", "path": "data.recordList.offChartReceiptIsAllowed", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "apsLock": {"api_field_name": "apsLock", "chinese_name": "排程状态:0-未锁定,1-已锁定", "data_type": "BIGINT", "param_desc": "排程状态:0-未锁定,1-已锁定", "path": "data.recordList.apsLock", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "dailyschQuantity": {"api_field_name": "dailyschQuantity", "chinese_name": "排产数量", "data_type": "NVARCHAR(500)", "param_desc": "排产数量", "path": "data.recordList.dailyschQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "dailyschStatus": {"api_field_name": "dailyschStatus", "chinese_name": "排产状态：0-未排产，1-部分排产，2-已排产", "data_type": "NVARCHAR(500)", "param_desc": "排产状态：0-未排产，1-部分排产，2-已排产", "path": "data.recordList.dailyschStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "dailyschConquantity": {"api_field_name": "dailyschConquantity", "chinese_name": "排产确认数量", "data_type": "NVARCHAR(500)", "param_desc": "排产确认数量", "path": "data.recordList.dailyschConquantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "transTypeCode": {"api_field_name": "transTypeCode", "chinese_name": "交易类型编码", "data_type": "NVARCHAR(500)", "param_desc": "交易类型编码", "path": "data.recordList.transTypeCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderMaterial": {"api_field_name": "orderMaterial", "chinese_name": "材料信息", "data_type": "NVARCHAR(MAX)", "param_desc": "材料信息", "path": "data.recordList.orderMaterial", "depth": 2, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isWholeSet": {"api_field_name": "isWholeSet", "chinese_name": "齐套标识：false-否，true-是", "data_type": "BIT", "param_desc": "齐套标识：false-否，true-是", "path": "data.recordList.orderMaterial.isWholeSet", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "receivedQuantity": {"api_field_name": "receivedQuantity", "chinese_name": "已领数量", "data_type": "NVARCHAR(500)", "param_desc": "已领数量", "path": "data.recordList.orderMaterial.receivedQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "recipientQuantity": {"api_field_name": "recipientQuantity", "chinese_name": "应领数量", "data_type": "NVARCHAR(500)", "param_desc": "应领数量", "path": "data.recordList.orderMaterial.recipientQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "numeratorQuantity": {"api_field_name": "numeratorQuantity", "chinese_name": "分子用量", "data_type": "DECIMAL(18,4)", "param_desc": "分子用量", "path": "data.recordList.orderByProduct.numeratorQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockUnitPrecision": {"api_field_name": "stockUnitPrecision", "chinese_name": "库存单位精度", "data_type": "BIGINT", "param_desc": "库存单位精度", "path": "data.recordList.orderMaterial.stockUnitPrecision", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "mainUnitTruncationType": {"api_field_name": "mainUnitTruncationType", "chinese_name": "主计量舍位方式；同BigDecimal的舍入方式", "data_type": "BIGINT", "param_desc": "主计量舍位方式；同BigDecimal的舍入方式", "path": "data.recordList.orderProcess.mainUnitTruncationType", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockUnitName": {"api_field_name": "stockUnitName", "chinese_name": "库存单位", "data_type": "NVARCHAR(500)", "param_desc": "库存单位", "path": "data.recordList.orderMaterial.stockUnitName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unitUseQuantity": {"api_field_name": "unitUseQuantity", "chinese_name": "单位产出数量", "data_type": "DECIMAL(18,4)", "param_desc": "单位产出数量", "path": "data.recordList.orderByProduct.unitUseQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auxiliaryReceivedQuantity": {"api_field_name": "auxiliaryReceivedQuantity", "chinese_name": "已领件数", "data_type": "NVARCHAR(500)", "param_desc": "已领件数", "path": "data.recordList.orderMaterial.auxiliaryReceivedQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auxiliaryRecipientQuantity": {"api_field_name": "auxiliaryRecipientQuantity", "chinese_name": "应领件数", "data_type": "NVARCHAR(500)", "param_desc": "应领件数", "path": "data.recordList.orderMaterial.auxiliaryRecipientQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "skuName": {"api_field_name": "skuName", "chinese_name": "SKU名称", "data_type": "NVARCHAR(500)", "param_desc": "SKU名称", "path": "data.recordList.orderByProduct.skuName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockUnitTruncationType": {"api_field_name": "stockUnitTruncationType", "chinese_name": "库存单位舍位方式：0-入位，1-舍位，4-四舍五入", "data_type": "BIGINT", "param_desc": "库存单位舍位方式：0-入位，1-舍位，4-四舍五入", "path": "data.recordList.orderMaterial.stockUnitTruncationType", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "scrap": {"api_field_name": "scrap", "chinese_name": "废品率%", "data_type": "NVARCHAR(500)", "param_desc": "废品率%", "path": "data.recordList.orderMaterial.scrap", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "lineNo": {"api_field_name": "lineNo", "chinese_name": "行号", "data_type": "DECIMAL(18,4)", "param_desc": "行号", "path": "data.recordList.orderByProduct.lineNo", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "supplyType": {"api_field_name": "supplyType", "chinese_name": "发料方式：0-领用，1-入库倒冲，2-不发料", "data_type": "NVARCHAR(500)", "param_desc": "发料方式：0-领用，1-入库倒冲，2-不发料", "path": "data.recordList.orderMaterial.supplyType", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "truncUp": {"api_field_name": "truncUp", "chinese_name": "向上取整：0-否，1-是", "data_type": "NVARCHAR(500)", "param_desc": "向上取整：0-否，1-是", "path": "data.recordList.orderMaterial.truncUp", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "substituteFlag": {"api_field_name": "substituteFlag", "chinese_name": "BOM替代标识", "data_type": "BIGINT", "param_desc": "BOM替代标识", "path": "data.recordList.orderMaterial.substituteFlag", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "changeRate": {"api_field_name": "changeRate", "chinese_name": "换算率", "data_type": "DECIMAL(18,4)", "param_desc": "换算率", "path": "data.recordList.orderByProduct.changeRate", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "skuId": {"api_field_name": "skuId", "chinese_name": "skuId", "data_type": "BIGINT", "param_desc": "skuId", "path": "data.recordList.orderByProduct.skuId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "denominatorQuantity": {"api_field_name": "denominatorQuantity", "chinese_name": "分母用量", "data_type": "DECIMAL(18,4)", "param_desc": "分母用量", "path": "data.recordList.orderByProduct.denominatorQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bomId": {"api_field_name": "bomId", "chinese_name": "物料清单Id", "data_type": "BIGINT", "param_desc": "物料清单Id", "path": "data.recordList.orderMaterial.bomId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "mainUnit": {"api_field_name": "mainUnit", "chinese_name": "主计量ID", "data_type": "BIGINT", "param_desc": "主计量ID", "path": "data.recordList.orderByProduct.mainUnit", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "fixedQuantity": {"api_field_name": "fixedQuantity", "chinese_name": "固定用量：0-否，1-是", "data_type": "NVARCHAR(500)", "param_desc": "固定用量：0-否，1-是", "path": "data.recordList.orderMaterial.fixedQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productName": {"api_field_name": "productName", "chinese_name": "物料名称", "data_type": "NVARCHAR(500)", "param_desc": "物料名称", "path": "data.recordList.orderByProduct.productName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productCode": {"api_field_name": "productCode", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "data.recordList.orderByProduct.productCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productId": {"api_field_name": "productId", "chinese_name": "物料id", "data_type": "BIGINT", "param_desc": "物料id", "path": "data.recordList.orderByProduct.productId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "changeType": {"api_field_name": "changeType", "chinese_name": "换算方式；0-固定换算，1-浮动换算", "data_type": "BIGINT", "param_desc": "换算方式；0-固定换算，1-浮动换算", "path": "data.recordList.orderProcess.changeType", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "materialCode": {"api_field_name": "materialCode", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "data.recordList.orderMaterial.materialCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderProductId": {"api_field_name": "orderProductId", "chinese_name": "生产订单行ID", "data_type": "BIGINT", "param_desc": "生产订单行ID", "path": "data.recordList.orderProcess.orderProductId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "materialId": {"api_field_name": "materialId", "chinese_name": "制造物料id", "data_type": "BIGINT", "param_desc": "制造物料id", "path": "data.recordList.orderByProduct.materialId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bomMaterialId": {"api_field_name": "bomMaterialId", "chinese_name": "物料清单子件Id", "data_type": "BIGINT", "param_desc": "物料清单子件Id", "path": "data.recordList.orderMaterial.bomMaterialId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "mainUnitName": {"api_field_name": "mainUnitName", "chinese_name": "主计量单位", "data_type": "NVARCHAR(500)", "param_desc": "主计量单位", "path": "data.recordList.orderProcess.mainUnitName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "materialName": {"api_field_name": "materialName", "chinese_name": "物料名称", "data_type": "NVARCHAR(500)", "param_desc": "物料名称", "path": "data.recordList.orderByProduct.materialName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "requirementDate": {"api_field_name": "requirementDate", "chinese_name": "需求日期", "data_type": "NVARCHAR(500)", "param_desc": "需求日期", "path": "data.recordList.orderMaterial.requirementDate", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "skuCode": {"api_field_name": "skuCode", "chinese_name": "SKU编码", "data_type": "NVARCHAR(500)", "param_desc": "SKU编码", "path": "data.recordList.orderByProduct.skuCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockUnitId": {"api_field_name": "stockUnitId", "chinese_name": "库存单位Id", "data_type": "BIGINT", "param_desc": "库存单位Id", "path": "data.recordList.orderMaterial.stockUnitId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "mustLossQuantity": {"api_field_name": "mustLossQuantity", "chinese_name": "固定损耗", "data_type": "NVARCHAR(500)", "param_desc": "固定损耗", "path": "data.recordList.orderMaterial.mustLossQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "calcCostFlag": {"api_field_name": "calcCostFlag", "chinese_name": "计算成本；0-否，1-是", "data_type": "NVARCHAR(500)", "param_desc": "计算成本；0-否，1-是", "path": "data.recordList.orderMaterial.calcCostFlag", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderMaterialExpinfo!id": {"api_field_name": "orderMaterialExpinfo!id", "chinese_name": "材料信息扩展信息id", "data_type": "BIGINT", "param_desc": "材料信息扩展信息id", "path": "data.recordList.orderMaterial.orderMaterialExpinfo!id", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "excessAppliedQty": {"api_field_name": "excessAppliedQty", "chinese_name": "超额申请数量", "data_type": "NVARCHAR(500)", "param_desc": "超额申请数量", "path": "data.recordList.orderMaterial.excessAppliedQty", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auxiliaryExcessAppliedQty": {"api_field_name": "auxiliaryExcessAppliedQty", "chinese_name": "超额申请件数", "data_type": "NVARCHAR(500)", "param_desc": "超额申请件数", "path": "data.recordList.orderMaterial.auxiliaryExcessAppliedQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "excessRecipientQty": {"api_field_name": "excessRecipientQty", "chinese_name": "已超领数量", "data_type": "NVARCHAR(500)", "param_desc": "已超领数量", "path": "data.recordList.orderMaterial.excessRecipientQty", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auxiliaryExcessRecipientQty": {"api_field_name": "auxiliaryExcessRecipientQty", "chinese_name": "已超领件数", "data_type": "NVARCHAR(500)", "param_desc": "已超领件数", "path": "data.recordList.orderMaterial.auxiliaryExcessRecipientQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderMaterialExpinfo!excessQty": {"api_field_name": "orderMaterialExpinfo!excessQty", "chinese_name": "可超额数量", "data_type": "NVARCHAR(500)", "param_desc": "可超额数量", "path": "data.recordList.orderMaterial.orderMaterialExpinfo!excessQty", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderMaterialExpinfo!auxiliaryExcessQty": {"api_field_name": "orderMaterialExpinfo!auxiliaryExcessQty", "chinese_name": "可超额件数", "data_type": "NVARCHAR(500)", "param_desc": "可超额件数", "path": "data.recordList.orderMaterial.orderMaterialExpinfo!auxiliaryExcessQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderMaterialExpinfo!isExcess": {"api_field_name": "orderMaterialExpinfo!isExcess", "chinese_name": "超额标识", "data_type": "BIT", "param_desc": "超额标识", "path": "data.recordList.orderMaterial.orderMaterialExpinfo!isExcess", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderMaterialExpinfo!excessType": {"api_field_name": "orderMaterialExpinfo!excessType", "chinese_name": "超额类型：1 比例，2 数量，3 不控制", "data_type": "NVARCHAR(500)", "param_desc": "超额类型：1 比例，2 数量，3 不控制", "path": "data.recordList.orderMaterial.orderMaterialExpinfo!excessType", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderMaterialExpinfo!excessRate": {"api_field_name": "orderMaterialExpinfo!excessRate", "chinese_name": "超额比例(%)", "data_type": "NVARCHAR(500)", "param_desc": "超额比例(%)", "path": "data.recordList.orderMaterial.orderMaterialExpinfo!excessRate", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderMaterialExpinfo!fixedExcessQty": {"api_field_name": "orderMaterialExpinfo!fixedExcessQty", "chinese_name": "固定超额量", "data_type": "NVARCHAR(500)", "param_desc": "固定超额量", "path": "data.recordList.orderMaterial.orderMaterialExpinfo!fixedExcessQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderMaterialExpinfo!designator": {"api_field_name": "orderMaterialExpinfo!designator", "chinese_name": "位置号", "data_type": "NVARCHAR(500)", "param_desc": "位置号", "path": "data.recordList.orderMaterial.orderMaterialExpinfo!designator", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderMaterialExpinfo!wholePoint": {"api_field_name": "orderMaterialExpinfo!wholePoint", "chinese_name": "齐套检查点：1 订单完工，2订单入库，3订单开工，4工序完工", "data_type": "NVARCHAR(500)", "param_desc": "齐套检查点：1 订单完工，2订单入库，3订单开工，4工序完工", "path": "data.recordList.orderMaterial.orderMaterialExpinfo!wholePoint", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "appliedRetQuantity": {"api_field_name": "appliedRetQuantity", "chinese_name": "退料申请数量", "data_type": "NVARCHAR(500)", "param_desc": "退料申请数量", "path": "data.recordList.orderMaterial.appliedRetQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auxiliaryAppliedRetQuantity": {"api_field_name": "auxiliaryAppliedRetQuantity", "chinese_name": "退料申请件数", "data_type": "NVARCHAR(500)", "param_desc": "退料申请件数", "path": "data.recordList.orderMaterial.auxiliaryAppliedRetQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "appliedRetRestQuantity": {"api_field_name": "appliedRetRestQuantity", "chinese_name": "退料申请未退库数量", "data_type": "NVARCHAR(500)", "param_desc": "退料申请未退库数量", "path": "data.recordList.orderMaterial.appliedRetRestQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auxiliaryAppliedRetRestQuantity": {"api_field_name": "auxiliaryAppliedRetRestQuantity", "chinese_name": "退料申请未退库件数", "data_type": "NVARCHAR(500)", "param_desc": "退料申请未退库件数", "path": "data.recordList.orderMaterial.auxiliaryAppliedRetRestQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "excessAppliedRetQty": {"api_field_name": "excessAppliedRetQty", "chinese_name": "超额退料申请数量", "data_type": "NVARCHAR(500)", "param_desc": "超额退料申请数量", "path": "data.recordList.orderMaterial.excessAppliedRetQty", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auxiliaryExcessAppliedRetQty": {"api_field_name": "auxiliaryExcessAppliedRetQty", "chinese_name": "超额退料申请件数", "data_type": "NVARCHAR(500)", "param_desc": "超额退料申请件数", "path": "data.recordList.orderMaterial.auxiliaryExcessAppliedRetQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "excessAppliedRetRestQty": {"api_field_name": "excessAppliedRetRestQty", "chinese_name": "超额退料申请未退库数量", "data_type": "NVARCHAR(500)", "param_desc": "超额退料申请未退库数量", "path": "data.recordList.orderMaterial.excessAppliedRetRestQty", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auxiliaryExcessAppliedRetRestQty": {"api_field_name": "auxiliaryExcessAppliedRetRestQty", "chinese_name": "超额退料申请未退库件数", "data_type": "NVARCHAR(500)", "param_desc": "超额退料申请未退库件数", "path": "data.recordList.orderMaterial.auxiliaryExcessAppliedRetRestQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "appliedRestQuantity": {"api_field_name": "appliedRestQuantity", "chinese_name": "领料申请未出库数量", "data_type": "NVARCHAR(500)", "param_desc": "领料申请未出库数量", "path": "data.recordList.orderMaterial.appliedRestQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auxiliaryAppliedRestQuantity": {"api_field_name": "auxiliaryAppliedRestQuantity", "chinese_name": "领料申请未出库件数", "data_type": "NVARCHAR(500)", "param_desc": "领料申请未出库件数", "path": "data.recordList.orderMaterial.auxiliaryAppliedRestQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "excessAppliedRestQty": {"api_field_name": "excessAppliedRestQty", "chinese_name": "超额领料申请未出库数量", "data_type": "NVARCHAR(500)", "param_desc": "超额领料申请未出库数量", "path": "data.recordList.orderMaterial.excessAppliedRestQty", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auxiliaryExcessAppliedRestQty": {"api_field_name": "auxiliaryExcessAppliedRestQty", "chinese_name": "超额领料申请未出库件数", "data_type": "NVARCHAR(500)", "param_desc": "超额领料申请未出库件数", "path": "data.recordList.orderMaterial.auxiliaryExcessAppliedRestQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "projectId": {"api_field_name": "projectId", "chinese_name": "项目Id", "data_type": "NVARCHAR(500)", "param_desc": "项目Id", "path": "data.recordList.orderMaterial.projectId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "projectCode": {"api_field_name": "projectCode", "chinese_name": "项目编码", "data_type": "NVARCHAR(500)", "param_desc": "项目编码", "path": "data.recordList.orderMaterial.projectCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "projectName": {"api_field_name": "projectName", "chinese_name": "项目名称", "data_type": "NVARCHAR(500)", "param_desc": "项目名称", "path": "data.recordList.orderMaterial.projectName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "wbs": {"api_field_name": "wbs", "chinese_name": "wbs", "data_type": "NVARCHAR(500)", "param_desc": "wbs", "path": "data.recordList.orderMaterial.wbs", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "wbsCode": {"api_field_name": "wbsCode", "chinese_name": "WBS任务编码", "data_type": "NVARCHAR(500)", "param_desc": "WBS任务编码", "path": "data.recordList.orderMaterial.wbsCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "wbsName": {"api_field_name": "wbsName", "chinese_name": "WBS任务名称", "data_type": "NVARCHAR(500)", "param_desc": "WBS任务名称", "path": "data.recordList.orderMaterial.wbsName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "activity": {"api_field_name": "activity", "chinese_name": "活动", "data_type": "BIGINT", "param_desc": "活动", "path": "data.recordList.orderMaterial.activity", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "activityCode": {"api_field_name": "activityCode", "chinese_name": "活动编码", "data_type": "NVARCHAR(500)", "param_desc": "活动编码", "path": "data.recordList.orderMaterial.activityCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "activityName": {"api_field_name": "activityName", "chinese_name": "活动名称", "data_type": "NVARCHAR(500)", "param_desc": "活动名称", "path": "data.recordList.orderMaterial.activityName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "cfmReceivedQty": {"api_field_name": "cfmReceivedQty", "chinese_name": "确认已领数量", "data_type": "NVARCHAR(500)", "param_desc": "确认已领数量", "path": "data.recordList.orderMaterial.cfmReceivedQty", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "cfmAuxReceivedQty": {"api_field_name": "cfmAuxReceivedQty", "chinese_name": "确认已领件数", "data_type": "NVARCHAR(500)", "param_desc": "确认已领件数", "path": "data.recordList.orderMaterial.cfmAuxReceivedQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "cfmExcessRecipientQty": {"api_field_name": "cfmExcessRecipientQty", "chinese_name": "确认已超领数量", "data_type": "NVARCHAR(500)", "param_desc": "确认已超领数量", "path": "data.recordList.orderMaterial.cfmExcessRecipientQty", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "cfmExcessAuxQty": {"api_field_name": "cfmExcessAuxQty", "chinese_name": "确认已超领件数", "data_type": "NVARCHAR(500)", "param_desc": "确认已超领件数", "path": "data.recordList.orderMaterial.cfmExcessAuxQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "cfmReceivedKit": {"api_field_name": "cfmReceivedKit", "chinese_name": "确认已领套数", "data_type": "NVARCHAR(500)", "param_desc": "确认已领套数", "path": "data.recordList.cfmReceivedKit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderActivity": {"api_field_name": "orderActivity", "chinese_name": "作业信息", "data_type": "NVARCHAR(MAX)", "param_desc": "作业信息", "path": "data.recordList.orderActivity", "depth": 2, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "lineNum": {"api_field_name": "lineNum", "chinese_name": "行号", "data_type": "DECIMAL(18,4)", "param_desc": "行号", "path": "data.recordList.orderActivity.lineNum", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderId": {"api_field_name": "orderId", "chinese_name": "订单ID", "data_type": "BIGINT", "param_desc": "订单ID", "path": "data.recordList.orderProcess.orderId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "activityId": {"api_field_name": "activityId", "chinese_name": "作业标准ID", "data_type": "BIGINT", "param_desc": "作业标准ID", "path": "data.recordList.orderActivity.activityId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "activityType": {"api_field_name": "activityType", "chinese_name": "作业类型ID", "data_type": "BIGINT", "param_desc": "作业类型ID", "path": "data.recordList.orderActivity.activityType", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "activityTypeCode": {"api_field_name": "activityTypeCode", "chinese_name": "作业类别编码", "data_type": "NVARCHAR(500)", "param_desc": "作业类别编码", "path": "data.recordList.orderActivity.activityTypeCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "activityTypeName": {"api_field_name": "activityTypeName", "chinese_name": "作业类型", "data_type": "NVARCHAR(500)", "param_desc": "作业类型", "path": "data.recordList.orderActivity.activityTypeName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderProcessId": {"api_field_name": "orderProcessId", "chinese_name": "工序ID", "data_type": "BIGINT", "param_desc": "工序ID", "path": "data.recordList.orderActivity.orderProcessId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "opSn": {"api_field_name": "opSn", "chinese_name": "工序顺序号", "data_type": "DECIMAL(18,4)", "param_desc": "工序顺序号", "path": "data.recordList.orderActivity.opSn", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "activityClass": {"api_field_name": "activityClass", "chinese_name": "作业类别；0-人工，1-设备，2-委外，3-空闲，4-其他", "data_type": "BIGINT", "param_desc": "作业类别；0-人工，1-设备，2-委外，3-空闲，4-其他", "path": "data.recordList.orderActivity.activityClass", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "workCenterId": {"api_field_name": "workCenterId", "chinese_name": "工作中心ID", "data_type": "BIGINT", "param_desc": "工作中心ID", "path": "data.recordList.orderProcess.workCenterId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "workCenterCode": {"api_field_name": "workCenterCode", "chinese_name": "工作中心编码", "data_type": "NVARCHAR(500)", "param_desc": "工作中心编码", "path": "data.recordList.orderProcess.workCenterCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "workCenterName": {"api_field_name": "workCenterName", "chinese_name": "工作中心名称", "data_type": "NVARCHAR(500)", "param_desc": "工作中心名称", "path": "data.recordList.orderProcess.workCenterName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "operationId": {"api_field_name": "operationId", "chinese_name": "工序ID", "data_type": "BIGINT", "param_desc": "工序ID", "path": "data.recordList.orderProcess.operationId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operationCode": {"api_field_name": "operationCode", "chinese_name": "工序编码", "data_type": "NVARCHAR(500)", "param_desc": "工序编码", "path": "data.recordList.orderProcess.operationCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operationName": {"api_field_name": "operationName", "chinese_name": "工序名称", "data_type": "NVARCHAR(500)", "param_desc": "工序名称", "path": "data.recordList.orderProcess.operationName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "activityUnit": {"api_field_name": "activityUnit", "chinese_name": "数量单位ID", "data_type": "BIGINT", "param_desc": "数量单位ID", "path": "data.recordList.orderActivity.activityUnit", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "activityUnitName": {"api_field_name": "activityUnitName", "chinese_name": "数量单位", "data_type": "NVARCHAR(500)", "param_desc": "数量单位", "path": "data.recordList.orderActivity.activityUnitName", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "activityUnitPrecision": {"api_field_name": "activityUnitPrecision", "chinese_name": "数量单位精度", "data_type": "BIGINT", "param_desc": "数量单位精度", "path": "data.recordList.orderActivity.activityUnitPrecision", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "activityUnitTruncationType": {"api_field_name": "activityUnitTruncationType", "chinese_name": "数量单位舍入方式；同BigDecimal舍入方式", "data_type": "BIGINT", "param_desc": "数量单位舍入方式；同BigDecimal舍入方式", "path": "data.recordList.orderActivity.activityUnitTruncationType", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "usageUnit": {"api_field_name": "usageUnit", "chinese_name": "计量单位ID", "data_type": "BIGINT", "param_desc": "计量单位ID", "path": "data.recordList.orderActivity.usageUnit", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "usageUnitName": {"api_field_name": "usageUnitName", "chinese_name": "计量单位", "data_type": "NVARCHAR(500)", "param_desc": "计量单位", "path": "data.recordList.orderActivity.usageUnitName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "usageUnitPrecision": {"api_field_name": "usageUnitPrecision", "chinese_name": "计量单位精度", "data_type": "BIGINT", "param_desc": "计量单位精度", "path": "data.recordList.orderActivity.usageUnitPrecision", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "usageUnitTruncationType": {"api_field_name": "usageUnitTruncationType", "chinese_name": "计量单位舍入方式；同BigDecimal舍入方式", "data_type": "BIGINT", "param_desc": "计量单位舍入方式；同BigDecimal舍入方式", "path": "data.recordList.orderActivity.usageUnitTruncationType", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stdUsageQty": {"api_field_name": "stdUsageQty", "chinese_name": "额定总用量", "data_type": "DECIMAL(18,4)", "param_desc": "额定总用量", "path": "data.recordList.orderActivity.stdUsageQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "planUsageQty": {"api_field_name": "planUsageQty", "chinese_name": "计划总用量", "data_type": "DECIMAL(18,4)", "param_desc": "计划总用量", "path": "data.recordList.orderActivity.planUsageQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "usageQty": {"api_field_name": "usageQty", "chinese_name": "标准作业量", "data_type": "DECIMAL(18,4)", "param_desc": "标准作业量", "path": "data.recordList.orderActivity.usageQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isCalcCost": {"api_field_name": "isCalcCost", "chinese_name": "计算成本", "data_type": "BIT", "param_desc": "计算成本", "path": "data.recordList.orderActivity.isCalcCost", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "activityQty": {"api_field_name": "activityQty", "chinese_name": "数量", "data_type": "DECIMAL(18,4)", "param_desc": "数量", "path": "data.recordList.orderActivity.activityQty", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "usageBasis": {"api_field_name": "usageBasis", "chinese_name": "计量基础;0-物料，1-批次", "data_type": "BIGINT", "param_desc": "计量基础;0-物料，1-批次", "path": "data.recordList.orderActivity.usageBasis", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isAutoCreate": {"api_field_name": "isAutoCreate", "chinese_name": "自动创建；0-否，1-是", "data_type": "BIGINT", "param_desc": "自动创建；0-否，1-是", "path": "data.recordList.orderActivity.isAutoCreate", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderByProduct": {"api_field_name": "orderByProduct", "chinese_name": "联副产品信息", "data_type": "NVARCHAR(MAX)", "param_desc": "联副产品信息", "path": "data.recordList.orderByProduct", "depth": 2, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderProductLineNo": {"api_field_name": "orderProductLineNo", "chinese_name": "行号", "data_type": "BIGINT", "param_desc": "行号", "path": "data.recordList.orderByProduct.orderProductLineNo", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "manufacturingSpecification": {"api_field_name": "manufacturingSpecification", "chinese_name": "物料规格", "data_type": "NVARCHAR(500)", "param_desc": "物料规格", "path": "data.recordList.orderByProduct.manufacturingSpecification", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productionType": {"api_field_name": "productionType", "chinese_name": "产出类型；1-联产品，2-副产品", "data_type": "NVARCHAR(500)", "param_desc": "产出类型；1-联产品，2-副产品", "path": "data.recordList.orderByProduct.productionType", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehouseName": {"api_field_name": "warehouseName", "chinese_name": "预入仓库", "data_type": "NVARCHAR(500)", "param_desc": "预入仓库", "path": "data.recordList.orderByProduct.warehouseName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productionDate": {"api_field_name": "productionDate", "chinese_name": "产出日期", "data_type": "NVARCHAR(500)", "param_desc": "产出日期", "path": "data.recordList.orderByProduct.productionDate", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isBatchManage": {"api_field_name": "isBatchManage", "chinese_name": "是否批次管理", "data_type": "BIT", "param_desc": "是否批次管理", "path": "data.recordList.orderByProduct.isBatchManage", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isExpiryDateManage": {"api_field_name": "isExpiryDateManage", "chinese_name": "是否效期管理", "data_type": "BIT", "param_desc": "是否效期管理", "path": "data.recordList.orderByProduct.isExpiryDateManage", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "quantity": {"api_field_name": "quantity", "chinese_name": "产出数量", "data_type": "DECIMAL(18,4)", "param_desc": "产出数量", "path": "data.recordList.orderByProduct.quantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productUnitTruncationType": {"api_field_name": "productUnitTruncationType", "chinese_name": "生产单位舍位方式；同BigDecimal的舍入方式", "data_type": "BIGINT", "param_desc": "生产单位舍位方式；同BigDecimal的舍入方式", "path": "data.recordList.orderProcess.productUnitTruncationType", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productionUnitId": {"api_field_name": "productionUnitId", "chinese_name": "生产单位ID", "data_type": "BIGINT", "param_desc": "生产单位ID", "path": "data.recordList.orderProcess.productionUnitId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "warehouseId": {"api_field_name": "warehouseId", "chinese_name": "预入仓库Id", "data_type": "BIGINT", "param_desc": "预入仓库Id", "path": "data.recordList.orderByProduct.warehouseId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "offChartReceipt": {"api_field_name": "offChartReceipt", "chinese_name": "表外产出:false-否，true-是", "data_type": "BIT", "param_desc": "表外产出:false-否，true-是", "path": "data.recordList.orderByProduct.offChartReceipt", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productUnitName": {"api_field_name": "productUnitName", "chinese_name": "生产单位", "data_type": "NVARCHAR(500)", "param_desc": "生产单位", "path": "data.recordList.orderProcess.productUnitName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "cfmIncomingQty": {"api_field_name": "cfmIncomingQty", "chinese_name": "确认累计入库数量", "data_type": "NVARCHAR(500)", "param_desc": "确认累计入库数量", "path": "data.recordList.cfmIncomingQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "cfmIncomingAuxQty": {"api_field_name": "cfmIncomingAuxQty", "chinese_name": "确认累计入库件数", "data_type": "NVARCHAR(500)", "param_desc": "确认累计入库件数", "path": "data.recordList.cfmIncomingAuxQty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "cfmScrapStockQty": {"api_field_name": "cfmScrapStockQty", "chinese_name": "确认报废入库数量", "data_type": "NVARCHAR(500)", "param_desc": "确认报废入库数量", "path": "data.recordList.cfmScrapStockQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "cfmScrapStockAuxQty": {"api_field_name": "cfmScrapStockAuxQty", "chinese_name": "确认报废入库件数", "data_type": "NVARCHAR(500)", "param_desc": "确认报废入库件数", "path": "data.recordList.cfmScrapStockAuxQty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderProcess": {"api_field_name": "orderProcess", "chinese_name": "工序信息", "data_type": "NVARCHAR(MAX)", "param_desc": "工序信息", "path": "data.recordList.orderProcess", "depth": 2, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "operationControlId": {"api_field_name": "operationControlId", "chinese_name": "工序控制码ID", "data_type": "BIGINT", "param_desc": "工序控制码ID", "path": "data.recordList.orderProcess.operationControlId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "nextId": {"api_field_name": "nextId", "chinese_name": "后序ID", "data_type": "BIGINT", "param_desc": "后序ID", "path": "data.recordList.orderProcess.nextId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "doScheduling": {"api_field_name": "doScheduling", "chinese_name": "参与调度；0-否，1-是", "data_type": "BIGINT", "param_desc": "参与调度；0-否，1-是", "path": "data.recordList.orderProcess.doScheduling", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "transferProcplanProdQty": {"api_field_name": "transferProcplanProdQty", "chinese_name": "转工序作业计划件数", "data_type": "DECIMAL(18,4)", "param_desc": "转工序作业计划件数", "path": "data.recordList.orderProcess.transferProcplanProdQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "finishGoodsId": {"api_field_name": "finishGoodsId", "chinese_name": "完工库位id", "data_type": "BIGINT", "param_desc": "完工库位id", "path": "data.recordList.orderProcess.finishGoodsId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "finishWarehouseId": {"api_field_name": "finishWarehouseId", "chinese_name": "完工仓库id", "data_type": "BIGINT", "param_desc": "完工仓库id", "path": "data.recordList.orderProcess.finishWarehouseId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "preSn": {"api_field_name": "preSn", "chinese_name": "前序顺序号", "data_type": "DECIMAL(18,4)", "param_desc": "前序顺序号", "path": "data.recordList.orderProcess.preSn", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "transferProcplanQty": {"api_field_name": "transferProcplanQty", "chinese_name": "转工序作业计划数量", "data_type": "DECIMAL(18,4)", "param_desc": "转工序作业计划数量", "path": "data.recordList.orderProcess.transferProcplanQty", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "executeOrgName": {"api_field_name": "executeOrgName", "chinese_name": "执行组织", "data_type": "NVARCHAR(500)", "param_desc": "执行组织", "path": "data.recordList.orderProcess.executeOrgName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "outUnitId": {"api_field_name": "outUnitId", "chinese_name": "产出单位ID", "data_type": "BIGINT", "param_desc": "产出单位ID", "path": "data.recordList.orderProcess.outUnitId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "outUnitTruncationType": {"api_field_name": "outUnitTruncationType", "chinese_name": "产出单位舍位方式；同BigDecimal的舍入方式", "data_type": "BIGINT", "param_desc": "产出单位舍位方式；同BigDecimal的舍入方式", "path": "data.recordList.orderProcess.outUnitTruncationType", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "mainUnitId": {"api_field_name": "mainUnitId", "chinese_name": "主计量单位ID", "data_type": "BIGINT", "param_desc": "主计量单位ID", "path": "data.recordList.orderProcess.mainUnitId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "checkType": {"api_field_name": "checkType", "chinese_name": "质检方式；0-自检，1-车间检验", "data_type": "BIGINT", "param_desc": "质检方式；0-自检，1-车间检验", "path": "data.recordList.orderProcess.checkType", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "routingOperationId": {"api_field_name": "routingOperationId", "chinese_name": "工艺路线行ID", "data_type": "BIGINT", "param_desc": "工艺路线行ID", "path": "data.recordList.orderProcess.routingOperationId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "prepareTime": {"api_field_name": "prepareTime", "chinese_name": "计划准备时间", "data_type": "DECIMAL(18,4)", "param_desc": "计划准备时间", "path": "data.recordList.orderProcess.prepareTime", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "routingOperationProcessTime": {"api_field_name": "routingOperationProcessTime", "chinese_name": "单批加工时间", "data_type": "BIGINT", "param_desc": "单批加工时间", "path": "data.recordList.orderProcess.routingOperationProcessTime", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "qty": {"api_field_name": "qty", "chinese_name": "计划生产数量", "data_type": "DECIMAL(18,4)", "param_desc": "计划生产数量", "path": "data.recordList.orderProcess.qty", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "occupyProduction": {"api_field_name": "occupyProduction", "chinese_name": "占用产能；0-否，1-是", "data_type": "BIGINT", "param_desc": "占用产能；0-否，1-是", "path": "data.recordList.orderProcess.occupyProduction", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "computingCosts": {"api_field_name": "computingCosts", "chinese_name": "计算成本；0-否，1-是", "data_type": "BIGINT", "param_desc": "计算成本；0-否，1-是", "path": "data.recordList.orderProcess.computingCosts", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "mainChangeRate": {"api_field_name": "mainChangeRate", "chinese_name": "生产-主计量换算率", "data_type": "DECIMAL(18,4)", "param_desc": "生产-主计量换算率", "path": "data.recordList.orderProcess.mainChangeRate", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "immediateHandover": {"api_field_name": "immediateHandover", "chinese_name": "即时交接；0-否，1-是", "data_type": "BIGINT", "param_desc": "即时交接；0-否，1-是", "path": "data.recordList.orderProcess.immediateHandover", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "operationIdRouteDesc": {"api_field_name": "operationIdRouteDesc", "chinese_name": "工艺描述", "data_type": "NVARCHAR(500)", "param_desc": "工艺描述", "path": "data.recordList.orderProcess.operationIdRouteDesc", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "outUnitName": {"api_field_name": "outUnitName", "chinese_name": "产出单位", "data_type": "NVARCHAR(500)", "param_desc": "产出单位", "path": "data.recordList.orderProcess.outUnitName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "operationControlName": {"api_field_name": "operationControlName", "chinese_name": "工序控制码名称", "data_type": "NVARCHAR(500)", "param_desc": "工序控制码名称", "path": "data.recordList.orderProcess.operationControlName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "outChangeRate": {"api_field_name": "outChangeRate", "chinese_name": "产出-主计量换算率", "data_type": "DECIMAL(18,4)", "param_desc": "产出-主计量换算率", "path": "data.recordList.orderProcess.outChangeRate", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "processTime": {"api_field_name": "processTime", "chinese_name": "计划加工时间", "data_type": "DECIMAL(18,4)", "param_desc": "计划加工时间", "path": "data.recordList.orderProcess.processTime", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "procPlanCreate": {"api_field_name": "procPlanCreate", "chinese_name": "工序作业计划创建；0-否，1-是", "data_type": "BIGINT", "param_desc": "工序作业计划创建；0-否，1-是", "path": "data.recordList.orderProcess.procPlanCreate", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "nextSn": {"api_field_name": "nextSn", "chinese_name": "后序顺序号", "data_type": "DECIMAL(18,4)", "param_desc": "后序顺序号", "path": "data.recordList.orderProcess.nextSn", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "prodQty": {"api_field_name": "prodQty", "chinese_name": "计划生产件数", "data_type": "DECIMAL(18,4)", "param_desc": "计划生产件数", "path": "data.recordList.orderProcess.prodQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "processType": {"api_field_name": "processType", "chinese_name": "加工类型；0-正常加工，1-返工生产，2-报废补投", "data_type": "BIGINT", "param_desc": "加工类型；0-正常加工，1-返工生产，2-报废补投", "path": "data.recordList.orderProcess.processType", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "sn": {"api_field_name": "sn", "chinese_name": "顺序号", "data_type": "DECIMAL(18,4)", "param_desc": "顺序号", "path": "data.recordList.orderProcess.sn", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "planStartDate": {"api_field_name": "planStartDate", "chinese_name": "计划开工时间", "data_type": "NVARCHAR(500)", "param_desc": "计划开工时间", "path": "data.recordList.orderProcess.planStartDate", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "planEndDate": {"api_field_name": "planEndDate", "chinese_name": "计划完工时间", "data_type": "NVARCHAR(500)", "param_desc": "计划完工时间", "path": "data.recordList.orderProcess.planEndDate", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "timeUnit": {"api_field_name": "timeUnit", "chinese_name": "时间单位；0-天，1-小时，2-分，3-秒", "data_type": "BIGINT", "param_desc": "时间单位；0-天，1-小时，2-分，3-秒", "path": "data.recordList.orderProcess.timeUnit", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operationControlCode": {"api_field_name": "operationControlCode", "chinese_name": "工序控制码编码", "data_type": "NVARCHAR(500)", "param_desc": "工序控制码编码", "path": "data.recordList.orderProcess.operationControlCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "firstCheck": {"api_field_name": "firstCheck", "chinese_name": "首检；0-否，1-是", "data_type": "NVARCHAR(500)", "param_desc": "首检；0-否，1-是", "path": "data.recordList.firstCheck", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isOutsource": {"api_field_name": "isOutsource", "chinese_name": "委外；0-否，1-是", "data_type": "BIGINT", "param_desc": "委外；0-否，1-是", "path": "data.recordList.orderProcess.isOutsource", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "outUnitPrecision": {"api_field_name": "outUnitPrecision", "chinese_name": "产出单位精度", "data_type": "BIGINT", "param_desc": "产出单位精度", "path": "data.recordList.orderProcess.outUnitPrecision", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "executeOrgId": {"api_field_name": "executeOrgId", "chinese_name": "执行组织ID", "data_type": "NVARCHAR(500)", "param_desc": "执行组织ID", "path": "data.recordList.orderProcess.executeOrgId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "reportWork": {"api_field_name": "reportWork", "chinese_name": "报工；0-否，1-是", "data_type": "BIGINT", "param_desc": "报工；0-否，1-是", "path": "data.recordList.orderProcess.reportWork", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "scheduleProdNum2": {"api_field_name": "scheduleProdNum2", "chinese_name": "计划加工数量（产出单位）", "data_type": "NVARCHAR(500)", "param_desc": "计划加工数量（产出单位）", "path": "data.recordList.orderProcess.scheduleProdNum2", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalCompleteNum": {"api_field_name": "totalCompleteNum", "chinese_name": "累计完成数量", "data_type": "NVARCHAR(500)", "param_desc": "累计完成数量", "path": "data.recordList.orderProcess.totalCompleteNum", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalCompleteNum1": {"api_field_name": "totalCompleteNum1", "chinese_name": "累计完成件数", "data_type": "NVARCHAR(500)", "param_desc": "累计完成件数", "path": "data.recordList.orderProcess.totalCompleteNum1", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalCompleteNum2": {"api_field_name": "totalCompleteNum2", "chinese_name": "累计完成数量（产出单位）", "data_type": "NVARCHAR(500)", "param_desc": "累计完成数量（产出单位）", "path": "data.recordList.orderProcess.totalCompleteNum2", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalQualifiedNum": {"api_field_name": "totalQualifiedNum", "chinese_name": "累计合格数量", "data_type": "NVARCHAR(500)", "param_desc": "累计合格数量", "path": "data.recordList.orderProcess.totalQualifiedNum", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalQualifiedNum1": {"api_field_name": "totalQualifiedNum1", "chinese_name": "累计合格件数", "data_type": "NVARCHAR(500)", "param_desc": "累计合格件数", "path": "data.recordList.orderProcess.totalQualifiedNum1", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalQualifiedNum2": {"api_field_name": "totalQualifiedNum2", "chinese_name": "累计合格数量 （产出单位）", "data_type": "NVARCHAR(500)", "param_desc": "累计合格数量 （产出单位）", "path": "data.recordList.orderProcess.totalQualifiedNum2", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalScrapNum": {"api_field_name": "totalScrapNum", "chinese_name": "累计报废数量", "data_type": "NVARCHAR(500)", "param_desc": "累计报废数量", "path": "data.recordList.orderProcess.totalScrapNum", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalScrapNum1": {"api_field_name": "totalScrapNum1", "chinese_name": "累计报废件数", "data_type": "NVARCHAR(500)", "param_desc": "累计报废件数", "path": "data.recordList.orderProcess.totalScrapNum1", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalScrapNum2": {"api_field_name": "totalScrapNum2", "chinese_name": "累计报废数量 （产出单位）", "data_type": "NVARCHAR(500)", "param_desc": "累计报废数量 （产出单位）", "path": "data.recordList.orderProcess.totalScrapNum2", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalReworkNum": {"api_field_name": "totalReworkNum", "chinese_name": "累计待返工数量", "data_type": "NVARCHAR(500)", "param_desc": "累计待返工数量", "path": "data.recordList.orderProcess.totalReworkNum", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalReworkNum1": {"api_field_name": "totalReworkNum1", "chinese_name": "累计待返工件数", "data_type": "NVARCHAR(500)", "param_desc": "累计待返工件数", "path": "data.recordList.orderProcess.totalReworkNum1", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalReworkNum2": {"api_field_name": "totalReworkNum2", "chinese_name": "累计待返工数量 （产出单位）", "data_type": "NVARCHAR(500)", "param_desc": "累计待返工数量 （产出单位）", "path": "data.recordList.orderProcess.totalReworkNum2", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalReworkProcessNum": {"api_field_name": "totalReworkProcessNum", "chinese_name": "累计返工处理数量", "data_type": "NVARCHAR(500)", "param_desc": "累计返工处理数量", "path": "data.recordList.orderProcess.totalReworkProcessNum", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalReworkProcessNum1": {"api_field_name": "totalReworkProcessNum1", "chinese_name": "累计返工处理件数", "data_type": "NVARCHAR(500)", "param_desc": "累计返工处理件数", "path": "data.recordList.orderProcess.totalReworkProcessNum1", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalReworkProcessNum2": {"api_field_name": "totalReworkProcessNum2", "chinese_name": "累计返工处理数量 （产出单位）", "data_type": "NVARCHAR(500)", "param_desc": "累计返工处理数量 （产出单位）", "path": "data.recordList.orderProcess.totalReworkProcessNum2", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalTurnNum": {"api_field_name": "totalTurnNum", "chinese_name": "累计转出数量", "data_type": "NVARCHAR(500)", "param_desc": "累计转出数量", "path": "data.recordList.orderProcess.totalTurnNum", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalTurnNum1": {"api_field_name": "totalTurnNum1", "chinese_name": "累计转出件数", "data_type": "NVARCHAR(500)", "param_desc": "累计转出件数", "path": "data.recordList.orderProcess.totalTurnNum1", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalTurnNum2": {"api_field_name": "totalTurnNum2", "chinese_name": "累计转出数量 （产出单位）", "data_type": "NVARCHAR(500)", "param_desc": "累计转出数量 （产出单位）", "path": "data.recordList.orderProcess.totalTurnNum2", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalQualifiedTurnNum": {"api_field_name": "totalQualifiedTurnNum", "chinese_name": "累计合格转出数量", "data_type": "NVARCHAR(500)", "param_desc": "累计合格转出数量", "path": "data.recordList.orderProcess.totalQualifiedTurnNum", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalQualifiedTurnNum1": {"api_field_name": "totalQualifiedTurnNum1", "chinese_name": "累计合格转出件数", "data_type": "NVARCHAR(500)", "param_desc": "累计合格转出件数", "path": "data.recordList.orderProcess.totalQualifiedTurnNum1", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalQualifiedTurnNum2": {"api_field_name": "totalQualifiedTurnNum2", "chinese_name": "累计合格转出数量 （产出单位）", "data_type": "NVARCHAR(500)", "param_desc": "累计合格转出数量 （产出单位）", "path": "data.recordList.orderProcess.totalQualifiedTurnNum2", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalReworkTurnNum": {"api_field_name": "totalReworkTurnNum", "chinese_name": "累计返工转出数量", "data_type": "NVARCHAR(500)", "param_desc": "累计返工转出数量", "path": "data.recordList.orderProcess.totalReworkTurnNum", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalReworkTurnNum1": {"api_field_name": "totalReworkTurnNum1", "chinese_name": "累计返工转出件数", "data_type": "NVARCHAR(500)", "param_desc": "累计返工转出件数", "path": "data.recordList.orderProcess.totalReworkTurnNum1", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalReworkTurnNum2": {"api_field_name": "totalReworkTurnNum2", "chinese_name": "累计返工转出数量 （产出单位）", "data_type": "NVARCHAR(500)", "param_desc": "累计返工转出数量 （产出单位）", "path": "data.recordList.orderProcess.totalReworkTurnNum2", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "scrapInNum": {"api_field_name": "scrapInNum", "chinese_name": "累计报废转出数量", "data_type": "NVARCHAR(500)", "param_desc": "累计报废转出数量", "path": "data.recordList.orderProcess.scrapInNum", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "scrapInNum1": {"api_field_name": "scrapInNum1", "chinese_name": "累计报废转出件数", "data_type": "NVARCHAR(500)", "param_desc": "累计报废转出件数", "path": "data.recordList.orderProcess.scrapInNum1", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "scrapInNum2": {"api_field_name": "scrapInNum2", "chinese_name": "累计报废转出数量 （产出数量）", "data_type": "NVARCHAR(500)", "param_desc": "累计报废转出数量 （产出数量）", "path": "data.recordList.orderProcess.scrapInNum2", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "out_sys_id": {"api_field_name": "out_sys_id", "chinese_name": "外部来源Id", "data_type": "NVARCHAR(500)", "param_desc": "外部来源Id", "path": "data.recordList.out_sys_id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "out_sys_code": {"api_field_name": "out_sys_code", "chinese_name": "外部来源编码", "data_type": "NVARCHAR(500)", "param_desc": "外部来源编码", "path": "data.recordList.out_sys_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "out_sys_version": {"api_field_name": "out_sys_version", "chinese_name": "外部系统版本", "data_type": "NVARCHAR(500)", "param_desc": "外部系统版本", "path": "data.recordList.out_sys_version", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_type": {"api_field_name": "out_sys_type", "chinese_name": "外部来源类型", "data_type": "NVARCHAR(500)", "param_desc": "外部来源类型", "path": "data.recordList.out_sys_type", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_projectId": {"api_field_name": "OrderProduct_projectId", "chinese_name": "项目Id", "data_type": "NVARCHAR(500)", "param_desc": "项目Id", "path": "data.recordList.OrderProduct_projectId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "out_sys_rowno": {"api_field_name": "out_sys_rowno", "chinese_name": "外部来源行号", "data_type": "NVARCHAR(500)", "param_desc": "外部来源行号", "path": "data.recordList.out_sys_rowno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_lineid": {"api_field_name": "out_sys_lineid", "chinese_name": "外部来源行", "data_type": "NVARCHAR(500)", "param_desc": "外部来源行", "path": "data.recordList.out_sys_lineid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_projectCode": {"api_field_name": "OrderProduct_projectCode", "chinese_name": "项目编码", "data_type": "NVARCHAR(500)", "param_desc": "项目编码", "path": "data.recordList.OrderProduct_projectCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_projectName": {"api_field_name": "OrderProduct_projectName", "chinese_name": "项目名称", "data_type": "NVARCHAR(500)", "param_desc": "项目名称", "path": "data.recordList.OrderProduct_projectName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_wbs": {"api_field_name": "OrderProduct_wbs", "chinese_name": "wbs", "data_type": "NVARCHAR(500)", "param_desc": "wbs", "path": "data.recordList.OrderProduct_wbs", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_wbsCode": {"api_field_name": "OrderProduct_wbsCode", "chinese_name": "WBS任务编码", "data_type": "NVARCHAR(500)", "param_desc": "WBS任务编码", "path": "data.recordList.OrderProduct_wbsCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_wbsName": {"api_field_name": "OrderProduct_wbsName", "chinese_name": "WBS任务名称", "data_type": "NVARCHAR(500)", "param_desc": "WBS任务名称", "path": "data.recordList.OrderProduct_wbsName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_activity": {"api_field_name": "OrderProduct_activity", "chinese_name": "活动", "data_type": "BIGINT", "param_desc": "活动", "path": "data.recordList.OrderProduct_activity", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_activityCode": {"api_field_name": "OrderProduct_activityCode", "chinese_name": "活动编码", "data_type": "NVARCHAR(500)", "param_desc": "活动编码", "path": "data.recordList.OrderProduct_activityCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct_activityName": {"api_field_name": "OrderProduct_activityName", "chinese_name": "活动名称", "data_type": "NVARCHAR(500)", "param_desc": "活动名称", "path": "data.recordList.OrderProduct_activityName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "firstCheckType": {"api_field_name": "firstCheckType", "chinese_name": "首检控制方式；0-不控制，1-严格", "data_type": "NVARCHAR(500)", "param_desc": "首检控制方式；0-不控制，1-严格", "path": "data.recordList.firstCheckType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "firstCheckStatus": {"api_field_name": "firstCheckStatus", "chinese_name": "首检状态；0-无需首检，1-待首检，2-首检中，3-首检合格，4-首检不合格", "data_type": "NVARCHAR(500)", "param_desc": "首检状态；0-无需首检，1-待首检，2-首检中，3-首检合格，4-首检不合格", "path": "data.recordList.firstCheckStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "sumRecordList": {"api_field_name": "sumRecordList", "chinese_name": "合计字段集合", "data_type": "NVARCHAR(MAX)", "param_desc": "合计字段集合", "path": "data.sumRecordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageCount": {"api_field_name": "pageCount", "chinese_name": "总页数", "data_type": "BIGINT", "param_desc": "总页数", "path": "data.pageCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "beginPageIndex": {"api_field_name": "beginPageIndex", "chinese_name": "开始页码", "data_type": "BIGINT", "param_desc": "开始页码", "path": "data.beginPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "endPageIndex": {"api_field_name": "endPageIndex", "chinese_name": "结束页码", "data_type": "BIGINT", "param_desc": "结束页码", "path": "data.endPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct!materialId": {"api_field_name": "OrderProduct!materialId", "chinese_name": "制造物料", "data_type": "BIGINT", "param_desc": "制造物料", "path": "OrderProduct!materialId", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct!productId": {"api_field_name": "OrderProduct!productId", "chinese_name": "物料id,当物料id和物料编码同时填写时,取交集", "data_type": "BIGINT", "param_desc": "物料id,当物料id和物料编码同时填写时,取交集", "path": "OrderProduct!productId", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct!startDate": {"api_field_name": "OrderProduct!startDate", "chinese_name": "开工日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "data_type": "NVARCHAR(500)", "param_desc": "开工日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "path": "OrderProduct!startDate", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct!finishDate": {"api_field_name": "OrderProduct!finishDate", "chinese_name": "完工日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "data_type": "NVARCHAR(500)", "param_desc": "完工日期（区间，格式2021-03-02|2021-03-02 23:59:59）", "path": "OrderProduct!finishDate", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isShowProcess": {"api_field_name": "isShowProcess", "chinese_name": "是否展示工序，0-不展示；1-全部工序，2-全部工序（含执行信息）；3-未执行完工序", "data_type": "BIGINT", "param_desc": "是否展示工序，0-不展示；1-全部工序，2-全部工序（含执行信息）；3-未执行完工序", "path": "isShowProcess", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isShowMaterial": {"api_field_name": "isShowMaterial", "chinese_name": "是否展示材料:true-是,false-否", "data_type": "BIT", "param_desc": "是否展示材料:true-是,false-否", "path": "isShowMaterial", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isShowByProduct": {"api_field_name": "isShowByProduct", "chinese_name": "是否展示联副产品:true-是,false-否", "data_type": "BIT", "param_desc": "是否展示联副产品:true-是,false-否", "path": "isShowByProduct", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isShowActivity": {"api_field_name": "isShowActivity", "chinese_name": "是否展示作业:true-是,false-否", "data_type": "BIT", "param_desc": "是否展示作业:true-是,false-否", "path": "isShowActivity", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "OrderProduct!completedFlag": {"api_field_name": "OrderProduct!completedFlag", "chinese_name": "启用完工报告：false-否，true-是", "data_type": "NVARCHAR(500)", "param_desc": "启用完工报告：false-否，true-是", "path": "OrderProduct!completedFlag", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "simple": {"api_field_name": "simple", "chinese_name": "simple", "data_type": "NVARCHAR(MAX)", "param_desc": "simple", "path": "simple", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderProduct.productId.code": {"api_field_name": "orderProduct.productId.code", "chinese_name": "物料编码,当物料id和物料编码同时填写时,取交集", "data_type": "NVARCHAR(500)", "param_desc": "物料编码,当物料id和物料编码同时填写时,取交集", "path": "simple.orderProduct.productId.code", "depth": 3, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_pubts_begin": {"api_field_name": "open_pubts_begin", "chinese_name": "时间戳，开始时间", "data_type": "NVARCHAR(500)", "param_desc": "时间戳，开始时间", "path": "simple.open_pubts_begin", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_pubts_end": {"api_field_name": "open_pubts_end", "chinese_name": "时间戳，结束时间", "data_type": "NVARCHAR(500)", "param_desc": "时间戳，结束时间", "path": "simple.open_pubts_end", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderProduct.materialApplyStatus": {"api_field_name": "orderProduct.materialApplyStatus", "chinese_name": "领料申请状态：0-未申领，1-部分申领，2-全部申领，3-无需申领", "data_type": "NVARCHAR(500)", "param_desc": "领料申请状态：0-未申领，1-部分申领，2-全部申领，3-无需申领", "path": "simple.orderProduct.materialApplyStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderProduct.materialStatus": {"api_field_name": "orderProduct.materialStatus", "chinese_name": "领料状态：0-未领料，1-部分领用，2-全部领用，3-无需领料，4-超额领料", "data_type": "NVARCHAR(500)", "param_desc": "领料状态：0-未领料，1-部分领用，2-全部领用，3-无需领料，4-超额领料", "path": "simple.orderProduct.materialStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderProduct.finishedWorkApplyStatus": {"api_field_name": "orderProduct.finishedWorkApplyStatus", "chinese_name": "完工申报状态：0-未申报，1-部分申报，2-全部申报", "data_type": "NVARCHAR(500)", "param_desc": "完工申报状态：0-未申报，1-部分申报，2-全部申报", "path": "simple.orderProduct.finishedWorkApplyStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderProduct.stockStatus": {"api_field_name": "orderProduct.stockStatus", "chinese_name": "入库状态：0-未入库，1-部分入库，2-全部入库", "data_type": "NVARCHAR(500)", "param_desc": "入库状态：0-未入库，1-部分入库，2-全部入库", "path": "simple.orderProduct.stockStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_auditTime_begin": {"api_field_name": "open_auditTime_begin", "chinese_name": "审核时间,开始时间", "data_type": "DATETIME", "param_desc": "审核时间,开始时间", "path": "simple.open_auditTime_begin", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_auditTime_end": {"api_field_name": "open_auditTime_end", "chinese_name": "审核时间,结束时间", "data_type": "DATETIME", "param_desc": "审核时间,结束时间", "path": "simple.open_auditTime_end", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_auditDate_begin": {"api_field_name": "open_auditDate_begin", "chinese_name": "审核日期,开始日期", "data_type": "DATE", "param_desc": "审核日期,开始日期", "path": "simple.open_auditDate_begin", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_auditDate_end": {"api_field_name": "open_auditDate_end", "chinese_name": "审核日期,结束日期", "data_type": "DATE", "param_desc": "审核日期,结束日期", "path": "simple.open_auditDate_end", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderProduct.retMaterialApplyFlag": {"api_field_name": "orderProduct.retMaterialApplyFlag", "chinese_name": "退料申请标识，0-否，1-是", "data_type": "NVARCHAR(500)", "param_desc": "退料申请标识，0-否，1-是", "path": "simple.orderProduct.retMaterialApplyFlag", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "simpleVOs": {"api_field_name": "simpleVOs", "chinese_name": "扩展查询条件", "data_type": "NVARCHAR(MAX)", "param_desc": "扩展查询条件", "path": "simpleVOs", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "field": {"api_field_name": "field", "chinese_name": "属性名(条件)", "data_type": "NVARCHAR(500)", "param_desc": "属性名(条件)", "path": "simpleVOs.conditions.field", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "op": {"api_field_name": "op", "chinese_name": "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or", "data_type": "NVARCHAR(500)", "param_desc": "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or", "path": "simpleVOs.conditions.op", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value1": {"api_field_name": "value1", "chinese_name": "查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)", "data_type": "NVARCHAR(500)", "param_desc": "查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)", "path": "simpleVOs.conditions.value1", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value2": {"api_field_name": "value2", "chinese_name": "查询条件值2", "data_type": "NVARCHAR(500)", "param_desc": "查询条件值2", "path": "simpleVOs.conditions.value2", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "logicOp": {"api_field_name": "logicOp", "chinese_name": "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or", "data_type": "NVARCHAR(500)", "param_desc": "逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or", "path": "simpleVOs.logicOp", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "conditions": {"api_field_name": "conditions", "chinese_name": "下级查询条件", "data_type": "NVARCHAR(MAX)", "param_desc": "下级查询条件", "path": "simpleVOs.conditions", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}}}