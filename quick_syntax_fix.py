#!/usr/bin/env python3
"""
快速修复最严重的语法错误
"""

import os
import re
import subprocess


def fix_syntax_errors():
    """修复最严重的语法错误"""

    # 1. 修复 auto_fix_comprehensive_issues.py 的缩进问题
    try:
        with open("auto_fix_comprehensive_issues.py", "r", encoding="utf-8") as f:
            content = f.read()

        # 修复缩进问题
        content = content.replace(
            "    def __init___(self",
            "    def __init__(self")

        with open("auto_fix_comprehensive_issues.py", "w", encoding="utf-8") as f:
            f.write(content)
        print("✅ 修复 auto_fix_comprehensive_issues.py")
    except Exception as e:
        print(f"❌ 修复 auto_fix_comprehensive_issues.py 失败: {e}")

    # 2. 修复 auto_project_cleanup.py
    try:
        with open("auto_project_cleanup.py", "r", encoding="utf-8") as f:
            content = f.read()

        # 简单修复缩进问题 - 添加 pass 语句
        lines = content.split("\n")
        fixed_lines = []

        for i, line in enumerate(lines):
            fixed_lines.append(line)
            # 如果是函数定义后面没有内容，添加 pass
            if line.strip().endswith(":") and "def " in line:
                if i + \
                        1 < len(lines) and not lines[i + 1].strip().startswith(" "):
                    fixed_lines.append("    pass")

        with open("auto_project_cleanup.py", "w", encoding="utf-8") as f:
            f.write("\n".join(fixed_lines))
        print("✅ 修复 auto_project_cleanup.py")
    except Exception as e:
        print(f"❌ 修复 auto_project_cleanup.py 失败: {e}")

    # 3. 修复 backend API 文件的导入问题
    api_files = [
        "backend/app/api/v1/auth.py",
        "backend/app/api/v1/database.py",
        "backend/app/api/v1/config.py",
    ]

    for file_path in api_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                # 添加缺失的导入
                imports_to_add = []
                if "APIRouter" in content and "from fastapi import" not in content:
                    imports_to_add.append(
                        "from fastapi import APIRouter, HTTPException, Depends"
                    )

                if imports_to_add:
                    # 在文件开头添加导入
                    new_content = "\n".join(imports_to_add) + "\n\n" + content

                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write(new_content)
                    print(f"✅ 修复 {file_path}")

            except Exception as e:
                print(f"❌ 修复 {file_path} 失败: {e}")

    # 4. 修复异常变量问题
    try:
        # 找到所有Python文件并修复 except Exception as e: 但不使用 e 的情况
        for root, dirs, files in os.walk("."):
            dirs[:] = [
                d
                for d in dirs
                if d not in {"__pycache__", ".git", "logs", "temp_cleanup"}
            ]

            for file in files:
                if file.endswith(".py"):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, "r", encoding="utf-8") as f:
                            content = f.read()

                        # 简单替换未使用的异常变量
                        original_content = content

                        # 修复 except Exception as e: 但不使用 e 的情况
                        content = re.sub(
                            r"except\s+Exception\s+as\s+e:\s*\n(\s+)pass",
                            r"except Exception:\n\1pass",
                            content,
                        )

                        content = re.sub(
                            r"except\s+Exception\s+as\s+e:\s*\n(\s+)return",
                            r"except Exception:\n\1return",
                            content,
                        )

                        if content != original_content:
                            with open(file_path, "w", encoding="utf-8") as f:
                                f.write(content)

                    except Exception:
                        pass  # 忽略无法处理的文件

        print("✅ 批量修复异常变量问题")
    except Exception as e:
        print(f"❌ 批量修复异常变量失败: {e}")


def run_quick_fixes():
    """运行快速修复"""
    print("🚀 开始快速修复严重语法错误...")

    fix_syntax_errors()

    # 检查修复效果
    try:
        result = subprocess.run(
            [
                "python",
                "-m",
                "flake8",
                ".",
                "--exclude=__pycache__,logs,temp_cleanup,.git",
                "--select=E999,F821",  # 只检查语法错误和未定义名称
                "--count",
            ],
            capture_output=True,
            text=True,
        )

        if result.stdout.strip():
            error_count = int(result.stdout.strip())
            print(f"⚠️ 还有 {error_count} 个严重错误需要处理")
        else:
            print("🎉 所有严重语法错误已修复！")

    except Exception as e:
        print(f"❌ 检查修复效果失败: {e}")


if __name__ == "__main__":
    run_quick_fixes()
