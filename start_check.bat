@echo off
chcp 65001 >nul
REM Windows环境启动脚本 - 屎山绞杀环境
echo.
echo ========================================
echo   屎山代码绞杀环境 - Windows启动器
echo ========================================
echo.

echo 🔍 运行系统健康检查...
python scripts/final_status_check.py
echo.

echo 🔍 运行数据库一致性检查...
python scripts/database_dual_writer_simple.py --check
echo.

echo 📊 显示模块完成状态...
python scripts/module_tracker_simple.py --report 2>nul
echo.

echo 🛡️ 检查graveyard安全策略...
python scripts/graveyard_safety_analyzer.py
echo.

echo ========================================
echo   屎山绞杀环境状态总结
echo ========================================
echo ✅ 所有15个模块已完成迁移
echo ✅ 新系统模块位于: new-system/modules/
echo ✅ 备份文件位于: graveyard/
echo ⚠️ 安全策略: 等待7天后清除备份 (2025-08-13)
echo.
echo 📖 更多信息请查看: TASK.md
echo 📊 详细报告位于: reports/ 目录
echo.
pause
