@echo off
chcp 65001 >nul
title YS-API V3.0 前端服务
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🌐 YS-API V3.0 前端服务                    ║
echo ║                      Frontend Service                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📍 当前目录: %cd%
echo 🔧 启动前端服务...

if not exist "frontend" (
    echo ❌ 错误: 找不到 frontend 目录
    echo 💡 请确保在项目根目录下运行此脚本
    pause
    exit /b 1
)

echo 🔍 检查 Python 环境...
python --version
if errorlevel 1 (
    echo ❌ 错误: Python 未安装或未加入 PATH 环境变量
    echo 💡 请安装 Python 3.7+ 并将其加入 PATH
    pause
    exit /b 1
)

cd frontend

echo 📁 检查前端文件...
if exist "index.html" (
    echo ✅ 主页文件存在
) else (
    echo ⚠️ 未找到 index.html，将列出可用页面
)

echo.
echo ✅ 准备工作完成
echo 🌐 启动前端HTTP服务...
echo 📱 前端地址: http://localhost:3000
echo 🏠 主页: http://localhost:3000/index.html
echo 🛠️ 字段配置: http://localhost:3000/field-config.html
echo 💾 数据库管理: http://localhost:3000/database-v2.html
echo.
echo ⏹️ 按 Ctrl+C 停止服务
echo.

python -m http.server 3000

echo.
echo 🔄 服务已停止
pause
