import re
import sqlite3
import xml.etree.ElementTree as ET
from pathlib import Path

#!/usr/bin/env python3
"""
15模块表创建管理
基于验证完成的15个模块创建对应数据表
"""


class TableCreationManager:
    """表创建管理器"""

    def __init___(self, db_path="module_tables.db",
                  xml_backup_dir="../../模块字段/backup"):
    """TODO: Add function description."""
    self.db_path = Path(db_path)
    self.xml_backup_dir = Path(xml_backup_dir)

    # 15个已验证的模块
    self.modules = [
        "材料出库单列表查询",
        "采购订单列表",
        "采购入库单列表",
        "产品入库列表查询",
        "请购单列表查询",
        "生产订单列表查询",
        "委外订单列表",
        "委外入库列表查询",
        "委外申请列表查询",
        "销售出库列表查询",
        "销售订单",
        "需求计划",
        "业务日志"
    ]

    # 字段类型映射
    self.field_type_mapping = {
        "String": "TEXT",
        "Integer": "INTEGER",
        "Float": "REAL",
        "Boolean": "INTEGER",  # SQLite用INTEGER存储布尔值
        "Date": "TEXT",        # ISO格式日期字符串
        "DateTime": "TEXT",    # ISO格式日期时间字符串
        "Json": "TEXT"         # JSON字符串
    }

    self.conn = None
    self._init_database()

    def _init_database(self):
        """初始化数据库连接"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.execute("PRAGMA foreign_keys = ON")  # 启用外键
            print(f"✅ 数据库连接成功: {self.db_path}")
        except Exception:
            print(f"❌ 数据库连接失败: {e}")

    def parse_module_xml(self, module_name: str) -> Optional[Dict[str, Any]]:
        """解析模块XML配置文件"""
        xml_file = self.xml_backup_dir / f"{module_name}.xml"
        if not xml_file.exists():
            print(f"⚠️ XML文件不存在: {xml_file}")
            return None

        try:
            # 读取并处理XML内容
            with open(xml_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 处理多根元素问题
            if content.count('<ResultVO>') > 1:
                first_end = content.find('</ResultVO>')
                if first_end != -1:
                    content = content[:first_end + len('</ResultVO>')]

            root = ET.fromstring(content)

            # 提取基本信息
            module_info = {
                "name": self._get_text(root, ".//n", "未知"),
                "description": self._get_text(root, ".//description", ""),
                "api_classify": self._get_text(root, ".//apiClassifyName", ""),
                "fields": self._extract_fields(root)
            }

            return module_info

        except Exception:
            print(f"❌ 解析XML失败 {module_name}: {e}")
            return None

    def _get_text(self, root, xpath: str, default: str = "") -> str:
        """安全获取XML元素文本"""
        element = root.find(xpath)
        return element.text if element is not None and element.text else default

    def _extract_fields(self, root) -> List[Dict[str, Any]]:
        """从XML中提取字段信息"""
        fields = []

        # 查找所有字段元素
        field_elements = root.findall(".//children")

        for field_elem in field_elements:
            field_info = {
                "name": self._get_text(
                    field_elem,
                    ".//n",
                    "unknown_field"),
                "caption": self._get_text(
                    field_elem,
                    ".//caption",
                    ""),
                "data_type": self._get_text(
                    field_elem,
                    ".//dataType",
                    "String"),
                "max_length": self._get_text(
                    field_elem,
                    ".//maxLength",
                    "255"),
                "required": self._get_text(
                    field_elem,
                    ".//required",
                    "false").lower() == "true",
                "visible": self._get_text(
                    field_elem,
                    ".//visible",
                    "true").lower() == "true",
                "description": self._get_text(
                    field_elem,
                    ".//description",
                    "")}

            # 只添加有效字段
            if field_info["name"] and field_info["name"] != "unknown_field":
                fields.append(field_info)

        return fields[:20]  # 限制字段数量

    def generate_table_schema(self, module_info: Dict[str, Any]) -> str:
        """生成表的SQL创建语句"""
        table_name = self._sanitize_table_name(module_info["name"])

        # 基础字段
        columns = [
            "id INTEGER PRIMARY KEY AUTOINCREMENT",
            "created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP",
            "updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP",
            "module_name TEXT NOT NULL",
            "data_source TEXT"
        ]

        # 添加模块特定字段
        for field in module_info["fields"]:
            field_name = self._sanitize_field_name(field["name"])
            data_type = self.field_type_mapping.get(field["data_type"], "TEXT")

            # 构建字段定义
            field_def = f"{field_name} {data_type}"

            # 添加约束
            if field["required"]:
                field_def += " NOT NULL"

            # 添加长度限制（仅对TEXT类型）
            if data_type == "TEXT" and field["max_length"] and field["max_length"].isdigit(
            ):
                max_len = int(field["max_length"])
                if max_len > 0 and max_len < 10000:  # 合理的长度限制
                    field_def += f" CHECK(length({field_name}) <= {max_len})"

            columns.append(field_def)

        # 生成CREATE TABLE语句
        sql = f"""
CREATE TABLE IF NOT EXISTS {table_name} (
    {','.join([f'    {col}' for col in columns])}
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_{table_name}_created_at ON {table_name}(created_at);
CREATE INDEX IF NOT EXISTS idx_{table_name}_module_name ON {table_name}(module_name);
"""

        return sql

    def _sanitize_table_name(self, name: str) -> str:
        """清理表名，确保符合SQL标准"""
        # 移除特殊字符，替换为下划线
        sanitized = re.sub(r'[^\w\u4e00-\u9fff]', '_', name)
        # 确保不以数字开头
        if sanitized and sanitized[0].isdigit():
            sanitized = f"tbl_{sanitized}"
        return sanitized or "unknown_table"

    def _sanitize_field_name(self, name: str) -> str:
        """清理字段名，确保符合SQL标准"""
        sanitized = re.sub(r'[^\w\u4e00-\u9fff]', '_', name)
        # 确保不以数字开头
        if sanitized and sanitized[0].isdigit():
            sanitized = f"field_{sanitized}"
        # 避免SQL关键字
        sql_keywords = ['order', 'group', 'select', 'where', 'from', 'table']
        if sanitized.lower() in sql_keywords:
            sanitized = f"{sanitized}_field"
        return sanitized or "unknown_field"

    def create_tables_for_module(self, module_name: str) -> bool:
        """为指定模块创建数据表"""
        print(f"📊 创建表: {module_name}")

        # 解析模块配置
        module_info = self.parse_module_xml(module_name)
        if not module_info:
            print(f"❌ 无法解析模块配置: {module_name}")
            return False

        # 生成表结构
        sql = self.generate_table_schema(module_info)

        try:
            # 执行SQL
            self.conn.executescript(sql)
            self.conn.commit()

            table_name = self._sanitize_table_name(module_info["name"])
            print(f"✅ 表创建成功: {table_name} ({len(module_info['fields'])} 字段)")

            # 保存表结构信息
            self._save_table_info(module_name, module_info, table_name, sql)

            return True

        except Exception:
            print(f"❌ 表创建失败 {module_name}: {e}")
            self.conn.rollback()
            return False

    def _save_table_info(
            self,
            module_name: str,
            module_info: Dict,
            table_name: str,
            sql: str):
        """保存表信息到元数据表"""
        try:
            # 创建元数据表
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS table_metadata (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    module_name TEXT UNIQUE NOT NULL,
                    table_name TEXT NOT NULL,
                    field_count INTEGER NOT NULL,
                    creation_sql TEXT NOT NULL,
                    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    module_description TEXT
                )
            """)

            # 插入表信息
            self.conn.execute("""
                INSERT OR REPLACE INTO table_metadata
                (module_name, table_name, field_count, creation_sql, module_description)
                VALUES (?, ?, ?, ?, ?)
            """, (
                module_name,
                table_name,
                len(module_info['fields']),
                sql,
                module_info['description']
            ))

            self.conn.commit()

        except Exception:
            print(f"⚠️ 保存表元数据失败: {e}")

    def create_all_tables(self) -> Dict[str, bool]:
        """创建所有模块的数据表"""
        print("🚀 开始创建所有模块的数据表")
        print("=" * 50)

        results = {}
        success_count = 0

        for module in self.modules:
            result = self.create_tables_for_module(module)
            results[module] = result
            if result:
                success_count += 1
            print()  # 空行分隔

        print("=" * 50)
        print(f"📊 表创建统计:")
        print(f"   总模块数: {len(self.modules)}")
        print(f"   成功创建: {success_count}")
        print(f"   失败数量: {len(self.modules) - success_count}")
        print(f"   成功率: {success_count/len(self.modules)*100:.1f}%")

        return results

    def list_created_tables(self) -> List[Dict[str, Any]]:
        """列出已创建的表"""
        try:
            cursor = self.conn.execute("""
                SELECT module_name, table_name, field_count, created_at, module_description
                FROM table_metadata
                ORDER BY created_at DESC
            """)

            tables = []
            for row in cursor.fetchall():
                tables.append({
                    "module_name": row[0],
                    "table_name": row[1],
                    "field_count": row[2],
                    "created_at": row[3],
                    "description": row[4]
                })

            return tables

        except Exception:
            print(f"❌ 查询表列表失败: {e}")
            return []

    def get_table_info(self, module_name: str) -> Optional[Dict[str, Any]]:
        """获取指定模块的表信息"""
        try:
            cursor = self.conn.execute("""
                SELECT table_name, field_count, creation_sql, created_at, module_description
                FROM table_metadata
                WHERE module_name = ?
            """, (module_name,))

            row = cursor.fetchone()
            if row:
                return {
                    "table_name": row[0],
                    "field_count": row[1],
                    "creation_sql": row[2],
                    "created_at": row[3],
                    "description": row[4]
                }
            return None

        except Exception:
            print(f"❌ 查询表信息失败: {e}")
            return None

    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            print("✅ 数据库连接已关闭")


def demo_table_creation():
    """演示表创建功能"""
    manager = TableCreationManager()

    print("🚀 15模块表创建演示")
    print("=" * 50)

    # 创建前3个模块的表作为演示
    demo_modules = ["材料出库单列表查询", "采购订单列表", "采购入库单列表"]

    for module in demo_modules:
        print(f"\n📋 演示模块: {module}")
        success = manager.create_tables_for_module(module)
        if success:
            # 查看表信息
            table_info = manager.get_table_info(module)
            if table_info:
                print(f"   表名: {table_info['table_name']}")
                print(f"   字段数: {table_info['field_count']}")

    # 列出所有已创建的表
    print(f"\n📊 已创建的表:")
    tables = manager.list_created_tables()
    for table in tables:
        print(
            f"   {table['module_name']}: {table['table_name']} ({table['field_count']} 字段)")

    manager.close()


if __name__ == "__main__":
    demo_table_creation()
