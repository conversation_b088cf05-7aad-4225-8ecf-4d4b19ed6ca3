from datetime import datetime

from sqlalchemy.ext.declarative import declarative_base

"""
采购订单数据模型 - 重构后的干净代码
"""


Base = declarative_base()


class PurchaseOrder(Base):
    """采购订单模型"""

    __tablename__ = "purchase_orders"

    id = Column(Integer, primary_key=True)
    order_number = Column(
        String(50),
        unique=True,
        nullable=False,
        comment="订单编号")
    supplier_id = Column(Integer, nullable=False, comment="供应商ID")
    supplier_name = Column(String(100), nullable=False, comment="供应商名称")
    order_date = Column(DateTime, default=datetime.now, comment="订单日期")
    delivery_date = Column(DateTime, comment="要求交货日期")
    total_amount = Column(Decimal(15, 2), default=0, comment="订单总金额")
    status = Column(String(20), default="draft", comment="订单状态")
    notes = Column(Text, comment="备注")
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "order_number": self.order_number,
            "supplier_id": self.supplier_id,
            "supplier_name": self.supplier_name,
            "order_date": self.order_date.isoformat() if self.order_date else None,
            "delivery_date": (
                self.delivery_date.isoformat() if self.delivery_date else None),
            "total_amount": float(
                self.total_amount) if self.total_amount else 0,
            "status": self.status,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class PurchaseOrderItem(Base):
    """采购订单明细"""

    __tablename__ = "purchase_order_items"

    id = Column(Integer, primary_key=True)
    order_id = Column(Integer, nullable=False, comment="订单ID")
    material_id = Column(Integer, nullable=False, comment="物料ID")
    material_name = Column(String(100), nullable=False, comment="物料名称")
    quantity = Column(Decimal(15, 3), nullable=False, comment="数量")
    unit_price = Column(Decimal(15, 4), nullable=False, comment="单价")
    total_price = Column(Decimal(15, 2), nullable=False, comment="总价")
    unit = Column(String(20), comment="单位")

    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "order_id": self.order_id,
            "material_id": self.material_id,
            "material_name": self.material_name,
            "quantity": float(self.quantity),
            "unit_price": float(self.unit_price),
            "total_price": float(self.total_price),
            "unit": self.unit,
        }
