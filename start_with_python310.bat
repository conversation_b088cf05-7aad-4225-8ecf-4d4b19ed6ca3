@echo off
REM YS-API V3.0 启动脚本 - 使用Python 3.10
echo ========================================
echo YS-API V3.0 启动脚本 (Python 3.10)
echo ========================================

REM 切换到项目目录
cd /d "%~dp0"

REM 显示Python版本
echo 检查Python版本...
py -3.10 --version

REM 启动后端服务
echo.
echo 启动后端API服务...
start "YS-API Backend" cmd /k "cd backend && py -3.10 start_server.py"

REM 等待后端启动
echo 等待后端服务启动...
timeout /t 3

REM 启动前端服务器
echo.
echo 启动前端服务器...
start "YS-API Frontend" cmd /k "cd frontend && py -3.10 -m http.server 8080"

echo.
echo ========================================
echo 服务启动完成！
echo 后端API: http://localhost:8000
echo 前端界面: http://localhost:8080
echo ========================================
pause
