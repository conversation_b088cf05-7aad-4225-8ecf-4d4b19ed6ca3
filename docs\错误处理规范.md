# 错误处理规范文档
## YS-API V3 统一错误处理标准

**版本**: 1.0.0  
**更新日期**: 2024年12月  
**适用范围**: YS-API V3项目前后端  

---

## 📋 目录

1. [概述](#概述)
2. [错误分类](#错误分类)
3. [错误处理流程](#错误处理流程)
4. [重试策略](#重试策略)
5. [错误通知规范](#错误通知规范)
6. [性能影响控制](#性能影响控制)
7. [代码实现规范](#代码实现规范)
8. [测试要求](#测试要求)
9. [监控指标](#监控指标)

---

## 🎯 概述

本文档定义了YS-API V3项目的统一错误处理标准，旨在解决之前存在的错误处理机制不统一、用户反馈不友好、重试策略不合理等问题。

### 核心原则

- **统一性**: 所有错误处理使用统一的接口和格式
- **用户友好**: 向用户提供易懂的错误信息和解决建议
- **性能优先**: 错误处理机制不应显著影响系统性能
- **可观测性**: 所有错误都应被记录和监控
- **可恢复性**: 提供自动重试和人工介入机制

---

## 🏷️ 错误分类

### 1. 网络错误 (Network Errors)
**错误码前缀**: `NET_`

| 错误类型 | 错误码 | 描述 | 重试策略 |
|---------|-------|------|---------|
| 连接超时 | NET_TIMEOUT | 网络请求超时 | 快速重试 |
| 连接失败 | NET_UNREACHABLE | 网络连接不可达 | 渐进重试 |
| DNS解析失败 | NET_DNS_FAILED | 域名解析失败 | 延迟重试 |
| 连接重置 | NET_CONNECTION_RESET | 连接被重置 | 快速重试 |

### 2. API错误 (API Errors)
**错误码前缀**: `API_`

| HTTP状态码 | 错误码 | 描述 | 重试策略 |
|-----------|-------|------|---------|
| 400 | API_BAD_REQUEST | 请求参数错误 | 不重试 |
| 401 | API_UNAUTHORIZED | 身份认证失败 | 不重试 |
| 403 | API_FORBIDDEN | 权限不足 | 不重试 |
| 404 | API_NOT_FOUND | 资源不存在 | 不重试 |
| 429 | API_RATE_LIMITED | 请求频率限制 | 延迟重试 |
| 500 | API_INTERNAL_ERROR | 服务器内部错误 | 渐进重试 |
| 502 | API_BAD_GATEWAY | 网关错误 | 快速重试 |
| 503 | API_UNAVAILABLE | 服务不可用 | 延迟重试 |

### 3. 验证错误 (Validation Errors)
**错误码前缀**: `VAL_`

| 错误类型 | 错误码 | 描述 | 处理方式 |
|---------|-------|------|---------|
| 必填字段缺失 | VAL_REQUIRED_MISSING | 必填字段为空 | 用户提示 |
| 格式错误 | VAL_INVALID_FORMAT | 数据格式不正确 | 用户提示 |
| 长度超限 | VAL_LENGTH_EXCEEDED | 数据长度超出限制 | 用户提示 |
| 非法字符 | VAL_ILLEGAL_CHARS | 包含非法字符 | 用户提示 |
| 唯一性冲突 | VAL_DUPLICATE | 数据重复 | 用户提示 |

### 4. 认证错误 (Authentication Errors)
**错误码前缀**: `AUTH_`

| 错误类型 | 错误码 | 描述 | 处理方式 |
|---------|-------|------|---------|
| 登录失败 | AUTH_LOGIN_FAILED | 用户名或密码错误 | 用户提示 |
| 会话过期 | AUTH_SESSION_EXPIRED | 会话已过期 | 自动跳转登录 |
| 权限不足 | AUTH_INSUFFICIENT_PRIVILEGE | 权限不足 | 用户提示 |
| 账号锁定 | AUTH_ACCOUNT_LOCKED | 账号被锁定 | 用户提示 |

### 5. 文件IO错误 (File I/O Errors)
**错误码前缀**: `FILE_`

| 错误类型 | 错误码 | 描述 | 重试策略 |
|---------|-------|------|---------|
| 文件不存在 | FILE_NOT_FOUND | 文件不存在 | 不重试 |
| 权限不足 | FILE_PERMISSION_DENIED | 文件权限不足 | 不重试 |
| 磁盘空间不足 | FILE_DISK_FULL | 磁盘空间不足 | 不重试 |
| 文件大小超限 | FILE_SIZE_EXCEEDED | 文件大小超过限制 | 不重试 |

### 6. 数据库错误 (Database Errors)
**错误码前缀**: `DB_`

| 错误类型 | 错误码 | 描述 | 重试策略 |
|---------|-------|------|---------|
| 连接失败 | DB_CONNECTION_FAILED | 数据库连接失败 | 渐进重试 |
| 查询超时 | DB_QUERY_TIMEOUT | 查询超时 | 快速重试 |
| 死锁 | DB_DEADLOCK | 数据库死锁 | 快速重试 |
| 约束违反 | DB_CONSTRAINT_VIOLATION | 数据约束违反 | 不重试 |

---

## 🔄 错误处理流程

### 1. 错误捕获
```javascript
try {
    // 业务逻辑
    const result = await performOperation();
    return result;
} catch (error) {
    // 统一错误处理入口
    await window.ErrorHandler.handleError(error, {
        operation: 'performOperation',
        context: { /* 上下文信息 */ }
    });
    throw error; // 重新抛出或返回默认值
}
```

### 2. 错误分类识别
- 根据错误类型、HTTP状态码、错误消息自动分类
- 设置错误级别（Critical, High, Medium, Low）
- 确定是否需要重试

### 3. 重试决策
- 检查错误类型是否允许重试
- 计算重试次数和延迟时间
- 执行重试逻辑

### 4. 用户通知
- 生成用户友好的错误消息
- 显示错误通知（可关闭）
- 提供可能的解决方案

### 5. 日志记录
- 记录详细的错误信息和上下文
- 发送到监控系统
- 更新错误统计

---

## ⚡ 重试策略

### 重试策略类型

#### 1. 快速重试 (Fast Retry)
**适用场景**: 高频接口、网络波动
```javascript
{
    maxRetries: 2,
    initialDelay: 200,
    backoffMultiplier: 1.5,
    maxDelay: 1000
}
```

#### 2. 标准重试 (Standard Retry)
**适用场景**: 一般API调用
```javascript
{
    maxRetries: 3,
    initialDelay: 1000,
    backoffMultiplier: 1.5,
    maxDelay: 3000
}
```

#### 3. 渐进重试 (Progressive Retry)
**适用场景**: 服务器错误、数据库连接
```javascript
{
    maxRetries: 3,
    initialDelay: 1000,
    backoffMultiplier: 2.0,
    maxDelay: 5000
}
```

#### 4. 谨慎重试 (Cautious Retry)
**适用场景**: 高频且高延迟接口
```javascript
{
    maxRetries: 1,
    initialDelay: 500,
    backoffMultiplier: 1.0,
    maxDelay: 2000
}
```

### 性能影响控制

根据您提到的重试机制可能导致+50ms延迟问题，特制定以下控制措施：

1. **高频接口优化**
   - `/api/field-config/save`: 使用快速重试策略
   - `/api/quick-save`: 最多重试1次
   - `/api/auto-sync`: 基于历史性能动态调整

2. **延迟预算控制**
   - 总重试延迟不超过原响应时间的50%
   - 高频接口重试延迟不超过100ms
   - 实时监控并动态调整重试参数

3. **智能重试策略**
   - 基于接口历史性能数据优化重试参数
   - 错误率高的接口减少重试次数
   - 成功率高的接口使用更积极的重试策略

---

## 📢 错误通知规范

### 通知级别

#### 1. Critical (严重)
- **颜色**: 红色
- **图标**: 🔴
- **声音**: 启用
- **自动关闭**: 10秒
- **示例**: 系统崩溃、数据丢失

#### 2. High (重要)
- **颜色**: 橙色
- **图标**: 🟠
- **声音**: 启用
- **自动关闭**: 8秒
- **示例**: API调用失败、认证错误

#### 3. Medium (一般)
- **颜色**: 黄色
- **图标**: 🟡
- **声音**: 禁用
- **自动关闭**: 5秒
- **示例**: 验证错误、权限不足

#### 4. Low (提示)
- **颜色**: 蓝色
- **图标**: 🔵
- **声音**: 禁用
- **自动关闭**: 3秒
- **示例**: 操作成功、信息提示

### 用户友好消息模板

#### 网络错误
```
原始错误: "Network request failed"
用户消息: "网络连接不稳定，请检查网络后重试"
解决建议: "检查网络连接 | 稍后重试 | 联系技术支持"
```

#### API错误
```
原始错误: "HTTP 500 Internal Server Error"
用户消息: "服务器暂时无法处理请求，请稍后重试"
解决建议: "刷新页面 | 稍后重试 | 联系管理员"
```

#### 验证错误
```
原始错误: "Field 'email' is required"
用户消息: "请填写邮箱地址"
解决建议: "填写有效邮箱 | 检查格式 | 查看帮助"
```

---

## 🛠️ 代码实现规范

### 1. 统一错误处理接口

#### 前端调用规范
```javascript
// ✅ 推荐做法
try {
    const response = await fetch('/api/data');
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    const data = await response.json();
    return data;
} catch (error) {
    await window.ErrorHandler.handleApiError({
        type: 'api',
        message: 'API调用失败',
        status: error.status,
        originalError: error,
        endpoint: '/api/data'
    });
    throw error;
}

// ❌ 避免做法
try {
    const data = await fetchData();
} catch (error) {
    console.error('Error:', error);
    alert('操作失败');
}
```

#### 表单验证规范
```javascript
// ✅ 推荐做法
function validateField(fieldName, value, rules) {
    try {
        for (const rule of rules) {
            if (!rule.validate(value)) {
                throw new ValidationError(rule.message, fieldName, rule.code);
            }
        }
        return true;
    } catch (error) {
        window.ErrorHandler.handleValidationError({
            type: 'validation',
            message: error.message,
            field: error.field,
            code: error.code,
            value: value
        });
        return false;
    }
}

// ❌ 避免做法
function validateField(fieldName, value) {
    if (!value) {
        alert('字段不能为空');
        return false;
    }
}
```

### 2. 日志记录规范

#### 错误日志
```javascript
// ✅ 推荐做法
window.StandardErrorLogger.error('API调用失败', {
    type: 'api_error',
    endpoint: '/api/data',
    method: 'GET',
    status: 500,
    duration: 1200,
    retryCount: 2
}, error);

// ❌ 避免做法
console.error('API error:', error);
```

#### 性能日志
```javascript
// ✅ 推荐做法
window.StandardErrorLogger.logPerformance('api_response_time', duration, {
    endpoint: '/api/data',
    method: 'GET',
    status: 200
});

// ❌ 避免做法
console.log(`API took ${duration}ms`);
```

### 3. 组件集成规范

#### HTML页面集成
```html
<!DOCTYPE html>
<html>
<head>
    <!-- 错误处理组件 -->
    <script src="js/common/error-handler.js"></script>
    <script src="js/common/user-error-notifier.js"></script>
    <script src="js/common/standard-error-logger.js"></script>
    <script src="js/common/smart-retry-optimizer.js"></script>
</head>
<body>
    <!-- 页面内容 -->
    <script>
        // 配置错误处理
        document.addEventListener('DOMContentLoaded', () => {
            window.ErrorHandler.configure({
                enableRetry: true,
                enableNotifications: true,
                enableLogging: true
            });
        });
    </script>
</body>
</html>
```

#### Vue.js组件集成
```javascript
// main.js
import { ErrorHandler } from './js/common/error-handler.js';

const app = createApp(App);

// 全局错误处理
app.config.errorHandler = async (error, instance, info) => {
    await ErrorHandler.handleGeneralError(error, {
        component: instance?.$options.name || 'Unknown',
        info: info,
        type: 'vue_error'
    });
};
```

---

## 🧪 测试要求

### 1. 单元测试

#### 错误处理器测试
```javascript
describe('ErrorHandler', () => {
    test('应该正确处理API错误', async () => {
        const errorInfo = {
            type: 'api',
            message: 'API调用失败',
            status: 500
        };
        
        const result = await ErrorHandler.handleApiError(errorInfo);
        
        expect(result.handled).toBe(true);
        expect(result.retryCount).toBeGreaterThan(0);
    });
    
    test('应该在重试限制内停止', async () => {
        // 模拟连续失败
        const result = await ErrorHandler.handleApiError({
            type: 'api',
            status: 500,
            maxRetries: 2
        });
        
        expect(result.retryCount).toBeLessThanOrEqual(2);
    });
});
```

#### 重试策略测试
```javascript
describe('SmartRetryOptimizer', () => {
    test('高频接口应使用快速重试策略', () => {
        const strategy = SmartRetryOptimizer.getOptimizedStrategy('/api/field-config/save');
        
        expect(strategy.type).toBe('fast');
        expect(strategy.maxRetries).toBeLessThanOrEqual(2);
        expect(strategy.initialDelay).toBeLessThan(500);
    });
});
```

### 2. 集成测试

#### 端到端错误场景测试
```javascript
describe('错误处理集成测试', () => {
    test('网络失败场景', async () => {
        // 模拟网络失败
        mockNetworkFailure();
        
        const response = await makeApiCall('/api/data');
        
        // 验证错误被正确处理
        expect(screen.getByText('网络连接不稳定')).toBeInTheDocument();
        expect(getLogEntries()).toContain('network_error');
    });
    
    test('服务器错误场景', async () => {
        // 模拟服务器错误
        mockServerError(500);
        
        const response = await makeApiCall('/api/data');
        
        // 验证重试机制
        expect(getRetryCount()).toBeGreaterThan(0);
        expect(screen.getByText('服务器暂时无法处理请求')).toBeInTheDocument();
    });
});
```

### 3. 性能测试

#### 重试延迟影响测试
```javascript
describe('性能影响测试', () => {
    test('高频接口重试延迟应在可接受范围内', async () => {
        const startTime = performance.now();
        
        // 模拟需要重试的情况
        await makeApiCall('/api/field-config/save');
        
        const totalTime = performance.now() - startTime;
        const baseResponseTime = 100; // 假设基础响应时间
        const additionalDelay = totalTime - baseResponseTime;
        
        // 验证额外延迟不超过50ms
        expect(additionalDelay).toBeLessThan(50);
    });
});
```

### 4. 压力测试

使用创建的 `error_handling_load_test.py` 工具进行压力测试：

```bash
# 运行压力测试
python tools/error_handling_load_test.py \
    --url http://localhost:8000 \
    --users 50 \
    --duration 300 \
    --output load_test_report.txt
```

**验证指标**:
- 错误率 < 1%
- P95响应时间 < 2秒
- 重试率 < 30%
- 平均额外延迟 < 50ms

---

## 📊 监控指标

### 1. 错误相关指标

| 指标名称 | 类型 | 描述 | 告警阈值 |
|---------|------|------|---------|
| `ys_api_errors_total` | Counter | 总错误数 | 错误率 > 5% |
| `ys_api_retries_total` | Counter | 总重试次数 | 重试率 > 30% |
| `ys_api_error_handling_duration` | Histogram | 错误处理耗时 | P95 > 100ms |
| `ys_api_retry_delay_total` | Counter | 重试总延迟 | 平均延迟 > 50ms |

### 2. 性能相关指标

| 指标名称 | 类型 | 描述 | 告警阈值 |
|---------|------|------|---------|
| `ys_api_request_duration_seconds` | Histogram | 请求响应时间 | P95 > 2s |
| `ys_api_high_frequency_endpoints_duration` | Histogram | 高频接口响应时间 | P90 > 0.5s |
| `ys_api_error_notifications_total` | Counter | 错误通知总数 | - |
| `ys_api_user_error_interactions_total` | Counter | 用户错误交互次数 | - |

### 3. 业务相关指标

| 指标名称 | 类型 | 描述 | 告警阈值 |
|---------|------|------|---------|
| `ys_api_config_save_total` | Counter | 配置保存总数 | - |
| `ys_api_config_save_failures_total` | Counter | 配置保存失败数 | 失败率 > 2% |
| `ys_api_sync_operations_total` | Counter | 同步操作总数 | - |
| `ys_api_sync_failures_total` | Counter | 同步失败总数 | 失败率 > 1% |

### 4. Prometheus查询示例

#### 错误率计算
```promql
# 5分钟错误率
rate(ys_api_errors_total[5m]) / rate(ys_api_requests_total[5m]) * 100

# 按端点分组的错误率
rate(ys_api_errors_total[5m]) by (endpoint) / rate(ys_api_requests_total[5m]) by (endpoint) * 100
```

#### 重试影响分析
```promql
# 平均重试延迟
rate(ys_api_retry_delay_total[5m]) / rate(ys_api_retries_total[5m])

# 重试率
rate(ys_api_retries_total[5m]) / rate(ys_api_requests_total[5m])
```

#### 高频接口性能监控
```promql
# 高频接口P90响应时间
histogram_quantile(0.90, rate(ys_api_request_duration_seconds_bucket{endpoint=~"/api/(field-config/save|quick-save|auto-sync)"}[5m]))
```

---

## 📈 持续改进

### 1. 定期Review
- **频率**: 每周
- **参与者**: 开发团队、测试团队、运维团队
- **Review内容**: 错误趋势、性能影响、用户反馈

### 2. 策略优化
- 基于监控数据调整重试策略
- 优化错误分类和用户消息
- 改进性能影响控制机制

### 3. 文档更新
- 根据新的错误类型更新分类
- 补充最佳实践案例
- 更新监控指标定义

---

## 🔗 相关文档

- [日志指南文档](./logging-guidelines.md)
- [性能优化指南](./performance-optimization.md)
- [监控配置文档](../config/monitoring/README.md)
- [测试用例文档](../tests/error-handling/README.md)

---

**文档维护者**: YS-API V3 开发团队  
**最后更新**: 2024年12月  
**下次Review**: 2024年12月（每周）

