import os
import re
from dataclasses import dataclass
from difflib import SequenceMatcher

import structlog

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Excel字段需求匹配服务
基于Excel文件定义的字段需求，智能匹配API字段并自动选中
"""


logger = structlog.get_logger()


@dataclass
class ExcelFieldRequirement:
    """Excel字段需求定义"""

    chinese_name: str
    is_required: bool = True
    data_type: Optional[str] = None
    description: Optional[str] = None
    sample_data: Optional[str] = None


@dataclass
class FieldMatchResult:
    """字段匹配结果"""

    api_field: str
    excel_field: str
    match_type: str
    confidence: float
    chinese_name: str


class ExcelFieldMatcher:
    """Excel字段需求匹配器"""

    def __init___(self, project_root: str = None):
    """TODO: Add function description."""
       if project_root is None:
            project_root = os.path.abspath(
                os.path.join(os.path.dirname(__file__), "..", "..", "..", "..")
            )

        self.project_root = project_root
        self.excel_dir = os.path.join(project_root, "v3", "excel")

        # 翻译字典（常用字段翻译）- 扩展版
        self.translation_dict = {
            # 基础字段
            "id": "标识",
            "code": "编码",
            "name": "名称",
            "number": "编号",
            "amount": "金额",
            "quantity": "数量",
            "qty": "数量",
            "price": "价格",
            "total": "合计",
            "subtotal": "小计",
            "date": "日期",
            "time": "时间",
            "datetime": "日期时间",
            "status": "状态",
            "state": "状态",
            "type": "类型",
            "category": "类别",
            "description": "描述",
            "remark": "备注",
            "note": "说明",
            "comment": "备注",
            # 组织相关
            "vendor": "供应商",
            "supplier": "供应商",
            "customer": "客户",
            "client": "客户",
            "org": "组织",
            "organization": "组织",
            "company": "公司",
            "department": "部门",
            "division": "部门",
            "branch": "分支",
            # 产品物料
            "product": "产品",
            "material": "物料",
            "item": "物料",
            "goods": "货物",
            "commodity": "商品",
            "article": "物品",
            "part": "零件",
            "component": "组件",
            "assembly": "装配件",
            # 仓库库存
            "warehouse": "仓库",
            "stock": "库存",
            "inventory": "库存",
            "location": "库位",
            "position": "位置",
            "shelf": "货架",
            "bin": "仓位",
            # 业务单据
            "order": "订单",
            "bill": "单据",
            "document": "单据",
            "voucher": "凭证",
            "invoice": "发票",
            "receipt": "收据",
            "delivery": "发货",
            "shipment": "发货",
            "dispatch": "派送",
            "purchase": "采购",
            "procurement": "采购",
            "sales": "销售",
            "sale": "销售",
            "requisition": "申请",
            "application": "申请",
            "request": "请求",
            # 生产制造
            "production": "生产",
            "manufacture": "制造",
            "process": "工艺",
            "operation": "作业",
            "routing": "工艺路线",
            "bom": "物料清单",
            "formula": "配方",
            "recipe": "配方",
            "yield": "产出",
            "output": "产出",
            "input": "投入",
            "batch": "批次",
            "lot": "批号",
            "serial": "序列号",
            # 委外外协
            "subcontract": "委外",
            "outsource": "外包",
            "contractor": "承包商",
            "outsourcing": "外包",
            # 计划相关
            "plan": "计划",
            "schedule": "计划",
            "forecast": "预测",
            "demand": "需求",
            "requirement": "需求",
            "capacity": "产能",
            "resource": "资源",
            # 用户操作
            "creator": "创建人",
            "created": "创建",
            "modifier": "修改人",
            "modified": "修改",
            "updated": "更新",
            "deleted": "删除",
            "operator": "操作员",
            "user": "用户",
            "employee": "员工",
            "staff": "员工",
            # 时间相关
            "create": "创建",
            "modify": "修改",
            "update": "更新",
            "delete": "删除",
            "start": "开始",
            "end": "结束",
            "finish": "完成",
            "complete": "完成",
            "due": "到期",
            "expire": "过期",
            # 财务相关
            "cost": "成本",
            "expense": "费用",
            "revenue": "收入",
            "profit": "利润",
            "tax": "税金",
            "discount": "折扣",
            "commission": "佣金",
            "fee": "费用",
            "charge": "费用",
            "payment": "付款",
            "settlement": "结算",
            # 质量相关
            "quality": "质量",
            "grade": "等级",
            "level": "级别",
            "standard": "标准",
            "specification": "规格",
            "tolerance": "公差",
            "defect": "缺陷",
            "reject": "拒收",
            "accept": "接收",
            "approve": "批准",
            "confirm": "确认",
            # 单位相关
            "unit": "单位",
            "uom": "计量单位",
            "measure": "计量",
            "weight": "重量",
            "volume": "体积",
            "length": "长度",
            "width": "宽度",
            "height": "高度",
            "size": "尺寸",
            "dimension": "尺寸",
        }

    def find_excel_file(self, module_name: str) -> Optional[str]:
        """查找模块对应的Excel字段需求文件"""
        try:
            # 模块名到中文名的映射
            module_chinese_mapping = {
                'purchase_order': '采购订单',
                'sales_order': '销售订单',
                'production_order': '生产订单',
                'subcontract_order': '委外订单',
                'applyorder': '请购单',
                'subcontract_requisition': '委外申请单',
                'product_receipt': '产品入库',
                'purchase_receipt': '采购入库',
                'subcontract_receipt': '委外入库',
                'materialout': '材料出库',
                'sales_out': '销售出库',
                'inventory': '现存量',
                'inventory_report': '现存量报表',
                'material_master': '物料创建',
                'demand_plan': '需求计划',
            }

            # 搜索模式：优先匹配中文名，然后匹配英文名
            chinese_name = module_chinese_mapping.get(module_name, module_name)

            for file in os.listdir(self.excel_dir):
                if file.endswith('.xlsx'):
                    # 优先匹配中文名
                    if chinese_name in file:
                        logger.info(
                            "找到Excel字段需求文件（中文匹配）",
                            module_name=module_name,
                            chinese_name=chinese_name,
                            excel_file=file,
                        )
                        return os.path.join(self.excel_dir, file)

                    # 备选匹配英文名
                    if module_name in file.lower():
                        logger.info(
                            "找到Excel字段需求文件（英文匹配）",
                            module_name=module_name,
                            excel_file=file,
                        )
                        return os.path.join(self.excel_dir, file)

            logger.warning(
                "未找到Excel字段需求文件",
                module_name=module_name,
                chinese_name=chinese_name,
                search_dir=self.excel_dir,
            )
            return None

        except Exception:
            logger.error("搜索Excel文件失败", module_name=module_name, error=str(e))
            return None

    def parse_excel_requirements(
        self, excel_file: str
    ) -> Dict[str, ExcelFieldRequirement]:
        """解析Excel字段需求文件 - 支持ERP导出格式"""
        try:
            # 读取Excel文件
            df = pd.read_excel(excel_file)
            logger.info(
    "读取Excel文件",
    file=excel_file,
    rows=len(df),
    columns=list(
        df.columns) )

            requirements = {}

            # ERP导出格式：第一行是字段名，所有字段都是必需的
            # 例如：物料编码 | 物料名称 | 金额
            #      A0101  | 风扇    | 1000

            # 从列名（表头）中提取字段需求
            for column_name in df.columns:
                field_name = str(column_name).strip()
                if not field_name or field_name == 'nan':
                    continue

                # 获取该列的示例数据（第一个非空值）
                sample_data = None
                for value in df[column_name].dropna():
                    if pd.notna(value):
                        sample_data = str(value).strip()
                        break

                # 根据示例数据推断数据类型
                data_type = self._infer_data_type_from_sample(sample_data)

                requirements[field_name] = ExcelFieldRequirement(
                    chinese_name=field_name,
                    is_required=True,  # ERP导出的所有字段都是必需的
                    data_type=data_type,
                    description=f"来自ERP导出表格",
                    sample_data=sample_data,
                )

            logger.info(
                "Excel需求解析完成（ERP导出格式）",
                file=excel_file,
                total_requirements=len(requirements),
                all_required=True,
                sample_fields=list(requirements.keys())[:5],
            )  # 显示前5个字段示例

            return requirements

        except Exception:
            logger.error("解析Excel文件失败", file=excel_file, error=str(e))
            raise

    def _infer_data_type_from_sample(self, sample_value: str) -> str:
        """根据示例数据推断字段数据类型"""
        if not sample_value:
            return "NVARCHAR(350)"

        # 尝试判断数据类型
        try:
            # 检查是否为整数
            int(sample_value)
            return "BIGINT"
        except ValueError:
            pass

        try:
            # 检查是否为小数
            float(sample_value)
            return "DECIMAL(18,4)"
        except ValueError:
            pass

        # 检查是否为日期

        date_patterns = [
            r'\d{4}-\d{2}-\d{2}',  # 2024-01-01
            r'\d{4}/\d{2}/\d{2}',  # 2024/01/01
            r'\d{2}-\d{2}-\d{4}',  # 01-01-2024
            r'\d{2}/\d{2}/\d{4}',  # 01/01/2024
        ]

        for pattern in date_patterns:
            if re.match(pattern, sample_value):
                return "DATETIME"

        # 根据字段名推断类型
        field_name_lower = sample_value.lower()
        if any(
            keyword in field_name_lower
            for keyword in ['金额', '价格', '数量', '单价', '总额']
        ):
            return "DECIMAL(18,4)"
        elif any(keyword in field_name_lower for keyword in ['日期', '时间']):
            return "DATETIME"
        elif any(keyword in field_name_lower for keyword in ['状态', '是否']):
            return "BIT"

        # 根据字符串长度决定NVARCHAR长度
        if len(sample_value) > 200:
            return "NVARCHAR(500)"
        elif len(sample_value) > 100:
            return "NVARCHAR(200)"
        elif len(sample_value) > 50:
            return "NVARCHAR(350)"
        else:
            return "NVARCHAR(600)"

    def _find_column(
        self, df: pd.DataFrame, possible_names: List[str]
    ) -> Optional[str]:
        """查找DataFrame中匹配的列名"""
        for col in df.columns:
            if str(col).strip() in possible_names:
                return col
        return None

    def match_fields(
        self, api_fields: Dict, excel_requirements: Dict[str, ExcelFieldRequirement]
    ) -> List[FieldMatchResult]:
        """匹配API字段与Excel需求"""
        matches = []

        logger.info(
            "开始字段匹配",
            api_fields_count=len(api_fields),
            excel_requirements_count=len(excel_requirements),
        )

        for excel_field, requirement in excel_requirements.items():
            best_match = self._find_best_match(excel_field, api_fields)
            if best_match:
                matches.append(best_match)

        # 按匹配置信度排序
        matches.sort(key=lambda x: x.confidence, reverse=True)

        logger.info(
            "字段匹配完成",
            total_matches=len(matches),
            high_confidence=len([m for m in matches if m.confidence >= 0.8]),
            medium_confidence=len([m for m in matches if 0.5 <= m.confidence < 0.8]),
        )

        return matches

    def _find_best_match(
        self, excel_field: str, api_fields: Dict
    ) -> Optional[FieldMatchResult]:
        """为Excel字段找到最佳的API字段匹配"""
        best_match = None
        best_confidence = 0.0

        for api_field, field_info in api_fields.items():
            chinese_name = field_info.get('chinese_name', '')

            # 1. 完全匹配
            if chinese_name == excel_field:
                return FieldMatchResult(
                    api_field=api_field,
                    excel_field=excel_field,
                    match_type="完全匹配",
                    confidence=1.0,
                    chinese_name=chinese_name,
                )

            # 2. 包含匹配
            if chinese_name and excel_field in chinese_name:
                confidence = 0.9
                if confidence > best_confidence:
                    best_match = FieldMatchResult(
                        api_field=api_field,
                        excel_field=excel_field,
                        match_type="包含匹配",
                        confidence=confidence,
                        chinese_name=chinese_name,
                    )
                    best_confidence = confidence

            # 3. 翻译匹配
            translated = self._translate_field(api_field)
            if translated and excel_field in translated:
                confidence = 0.7
                if confidence > best_confidence:
                    best_match = FieldMatchResult(
                        api_field=api_field,
                        excel_field=excel_field,
                        match_type="翻译匹配",
                        confidence=confidence,
                        chinese_name=chinese_name or translated,
                    )
                    best_confidence = confidence

            # 4. 相似度匹配
            if chinese_name:
                similarity = SequenceMatcher(
    None, chinese_name, excel_field).ratio()
                if similarity >= 0.6 and similarity > best_confidence:
                    best_match = FieldMatchResult(
                        api_field=api_field,
                        excel_field=excel_field,
                        match_type="相似度匹配",
                        confidence=similarity,
                        chinese_name=chinese_name,
                    )
                    best_confidence = similarity

        return best_match

    def _translate_field(self, field_name: str) -> str:
        """简单翻译API字段名"""
        # 分割字段名（处理 snake_case 和 camelCase）

        parts = re.split(r'[_\-]|(?=[A-Z])', field_name.lower())

        translated_parts = []
        for part in parts:
            if part in self.translation_dict:
                translated_parts.append(self.translation_dict[part])
            else:
                translated_parts.append(part)

        return ''.join(translated_parts)

    def apply_matches(
        self,
        api_fields: Dict,
        matches: List[FieldMatchResult],
        confidence_threshold: float = 0.6,
    ) -> Dict:
        """应用匹配结果到API字段配置"""

        matched_fields = set()

        for match in matches:
            if match.confidence >= confidence_threshold:
                api_field = match.api_field
                if api_field in api_fields:
                    # 自动选中匹配的字段
                    api_fields[api_field]['is_selected'] = True
                    api_fields[api_field]['excel_match'] = {
                        'excel_field': match.excel_field,
                        'match_type': match.match_type,
                        'confidence': match.confidence,
                    }
                    matched_fields.add(api_field)

        logger.info(
            "应用匹配结果",
            total_api_fields=len(api_fields),
            matched_fields=len(matched_fields),
            confidence_threshold=confidence_threshold,
        )

        return api_fields

    async def process_module_with_excel(
        self, module_name: str, api_fields: Dict
    ) -> Tuple[Dict, Dict]:
        """使用Excel需求文件处理模块字段"""
        try:
            # 查找Excel文件
            excel_file = self.find_excel_file(module_name)
            if not excel_file:
                logger.warning(
                    "模块无Excel需求文件，跳过Excel匹配", module_name=module_name
                )
                return api_fields, {}

            # 解析Excel需求
            requirements = self.parse_excel_requirements(excel_file)

            # 执行字段匹配
            matches = self.match_fields(api_fields, requirements)

            # 应用匹配结果
            updated_fields = self.apply_matches(api_fields, matches)

            # 生成匹配报告
            report = {
    'excel_file': excel_file,
    'requirements_count': len(requirements),
    'matches_count': len(matches),
    'high_confidence_matches': [
        m for m in matches if m.confidence >= 0.8],
        'medium_confidence_matches': [
            m for m in matches if 0.5 <= m.confidence < 0.8 ],
            'low_confidence_matches': [
                m for m in matches if m.confidence < 0.5],
                 }

            logger.info(
                "Excel字段匹配完成",
                module_name=module_name,
                **{k: len(v) if isinstance(v, list) else v for k, v in report.items()},
            )

            return updated_fields, report

        except Exception:
            logger.error("Excel字段匹配失败", module_name=module_name, error=str(e))
            return api_fields, {'error': str(e)}
