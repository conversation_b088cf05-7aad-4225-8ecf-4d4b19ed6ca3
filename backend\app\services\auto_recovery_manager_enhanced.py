import asyncio
import contextlib
import fcntl
import logging
import logging.handlers
import os
import random
import signal
import subprocess
import sys
import time
from collections import deque
from datetime import datetime
from pathlib import Path

import psutil
import redis
import structlog
import win32api
import win32event

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 增强版自动恢复管理器
实施错误检测、自动重启和健康检查

功能特性：
1. 实时健康检查
2. 自动错误恢复
3. 服务重启机制
4. 故障转移
5. 监控告警
"""


# 设置日志轮转（防止日志爆炸）
handler = logging.handlers.RotatingFileHandler(
    "recovery_enhanced.log", maxBytes=50 * 1024 * 1024, backupCount=5
)
structlog.configure(
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
    processors=[structlog.stdlib.ProcessorFormatter.wrap_for_formatter],
)
logging.basicConfig(handlers=[handler], level=logging.INFO)

logger = structlog.get_logger()

# Windows锁功能检查
if sys.platform != "win32":

    WINDOWS_LOCK_AVAILABLE = False
else:
    try:

        WINDOWS_LOCK_AVAILABLE = True
    except ImportError:
        WINDOWS_LOCK_AVAILABLE = False
        logger.warning("Windows锁功能不可用，将使用文件锁作为备选")

# 进程锁文件路径
LOCK_FILE = '/tmp/recovery_enhanced.lock'
if sys.platform == "win32":
    LOCK_FILE = 'C:/temp/recovery_enhanced.lock'


def _calculate_backoff(attempt: int, base: int = 30, cap: int = 600) -> int:
    """计算指数回退时间"""
    return min(cap, base * (2**attempt)) + random.randint(0, 10)


def _check_resource(
    current: float, threshold: float, name: str
) -> Tuple[bool, List[str]]:
    """检查资源使用情况"""
    ok = current < threshold
    return ok, [] if ok else [f"{name}使用率过高: {current:.1f}%"]


class ProcessLock:
    """进程锁管理器（跨平台支持）"""

    def __init___(self, lock_file: str):
    """TODO: Add function description."""
    self.lock_file = lock_file
    self.lock_handle = None
    self.windows_mutex = None

    def acquire(self) -> bool:
        """获取进程锁"""
        try:
            if sys.platform == "win32" and WINDOWS_LOCK_AVAILABLE:
                return self._acquire_windows_lock()
            else:
                return self._acquire_file_lock()
        except Exception:
            logger.error("进程锁获取失败", error=str(e), lock_file=self.lock_file)
            return False

    def _acquire_windows_lock(self) -> bool:
        """Windows下使用Mutex获取锁"""
        try:
            # 创建全局Mutex
            mutex_name = f"Global\\YS_API_Recovery_{os.path.basename(self.lock_file)}"
            self.windows_mutex = win32event.CreateMutex(
                None, False, mutex_name)

            # 尝试获取Mutex
            result = win32event.WaitForSingleObject(self.windows_mutex, 0)
            if result == win32event.WAIT_OBJECT_0:
                # 成功获取锁
                logger.info(
                    "Windows Mutex锁获取成功",
                    pid=os.getpid(),
                    mutex_name=mutex_name)
                return True
            else:
                # 锁被占用
                logger.error("Windows Mutex锁被占用", mutex_name=mutex_name)
                return False

        except Exception:
            logger.error("Windows Mutex锁获取失败", error=str(e))
            return False

    def _acquire_file_lock(self) -> bool:
        """使用文件锁（跨平台）"""
        try:
            # 确保目录存在
            lock_dir = os.path.dirname(self.lock_file)
            if lock_dir and not os.path.exists(lock_dir):
                os.makedirs(lock_dir, exist_ok=True)

            # 检查锁文件是否已存在
            if os.path.exists(self.lock_file):
                # 检查PID是否还在运行
                try:
                    with open(self.lock_file, 'rb') as f:
                        pid_str = f.read().decode().strip()
                        if pid_str:
                            pid = int(pid_str)
                            # 检查进程是否存在
                            try:
                                if sys.platform != "win32":
                                    os.kill(pid, 0)  # 发送信号0检查进程存在
                                else:
                                    # Windows下使用psutil检查进程
                                    if psutil.pid_exists(pid):
                                        logger.error(
                                            "进程锁被占用",
                                            pid=pid,
                                            lock_file=self.lock_file,
                                        )
                                        return False
                            except OSError:
                                # 进程不存在，可以获取锁
                                pass
                except Exception:
                    pass

            self.lock_handle = open(self.lock_file, 'wb')

            if sys.platform != "win32":
                fcntl.flock(self.lock_handle, fcntl.LOCK_EX | fcntl.LOCK_NB)

            # 写入当前PID
            self.lock_handle.write(str(os.getpid()).encode())
            self.lock_handle.flush()

            logger.info("文件锁获取成功", pid=os.getpid(), lock_file=self.lock_file)
            return True

        except (IOError, OSError) as e:
            logger.error("文件锁获取失败", error=str(e), lock_file=self.lock_file)
            if self.lock_handle:
                self.lock_handle.close()
            return False

    def release(self):
        """释放进程锁"""
        try:
            if (
                sys.platform == "win32"
                and WINDOWS_LOCK_AVAILABLE
                and self.windows_mutex
            ):
                # 释放Windows Mutex
                win32api.CloseHandle(self.windows_mutex)
                self.windows_mutex = None
                logger.info("Windows Mutex锁释放成功")
            elif self.lock_handle:
                # 释放文件锁
                if sys.platform != "win32":
                    fcntl.flock(self.lock_handle, fcntl.LOCK_UN)
                self.lock_handle.close()
                with contextlib.suppress(FileNotFoundError):
                    if os.path.exists(self.lock_file):
                        os.remove(self.lock_file)
                logger.info("文件锁释放成功")
        except Exception:
            logger.error("进程锁释放失败", error=str(e))


class CircuitBreaker:
    """熔断器模式"""

    def __init___(self, max_failures: int = 5, reset_timeout: int = 60):
    """TODO: Add function description."""
    self.max_failures = max_failures
    self.reset_timeout = reset_timeout
    self.failure_count = 0
    self.last_failure_time = 0
    self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    def allow_execution(self) -> bool:
        """检查是否允许执行"""
        current_time = time.time()

        if self.state == "OPEN":
            if current_time - self.last_failure_time > self.reset_timeout:
                self.state = "HALF_OPEN"
                logger.info("熔断器状态变为半开")
                return True
            return False

        return True

    def on_success(self):
        """执行成功回调"""
        if self.state == "HALF_OPEN":
            self.state = "CLOSED"
            self.failure_count = 0
            logger.info("熔断器重置为关闭状态")

    def on_failure(self):
        """执行失败回调"""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.max_failures:
            self.state = "OPEN"
            logger.warning("熔断器打开", failure_count=self.failure_count)


class EnhancedHealthChecker:
    """增强版健康检查器"""

    def __init___(self):
    """TODO: Add function description."""
    self.checks = {}
    self.last_check_time = {}
    self.check_interval = int(os.getenv("HEALTH_CHECK_INTERVAL", "30"))
    self.failure_threshold = int(
        os.getenv("HEALTH_FAILURE_THRESHOLD", "3"))
    self.failure_counts = {}
    self.history = deque(maxlen=1000)  # 存储最近1000次检查结果
    self.circuit_breakers = {}

    def register_check(
        self,
        name: str,
        check_func: Callable,
        interval: int = None,
        max_failures: int = 5,
    ):
        """注册健康检查"""
        self.checks[name] = {
            "func": check_func,
            "interval": interval or self.check_interval,
            "last_check": 0,
            "status": "unknown",
        }
        self.failure_counts[name] = 0
        self.circuit_breakers[name] = CircuitBreaker(max_failures)
        logger.info("注册健康检查", check_name=name, interval=interval)

    async def _execute_single_check(self, name: str, check_info: Dict) -> Dict:
        """执行单个健康检查"""
        circuit_breaker = self.circuit_breakers[name]

        if not circuit_breaker.allow_execution():
            return {
                "status": "circuit_open",
                "result": {"healthy": False, "reason": "circuit_breaker_open"},
                "failure_count": self.failure_counts[name],
                "is_critical": False,
            }

        try:
            # 执行健康检查
            if asyncio.iscoroutinefunction(check_info["func"]):
                result = await check_info["func"]()
            else:
                result = check_info["func"]()

            # 更新状态
            if result.get("healthy", False):
                check_info["status"] = "healthy"
                self.failure_counts[name] = 0
                circuit_breaker.on_success()
            else:
                check_info["status"] = "unhealthy"
                self.failure_counts[name] += 1
                circuit_breaker.on_failure()

            check_info["last_check"] = time.time()

            # 记录历史
            self.history.append(
                {
                    "timestamp": datetime.utcnow(),
                    "check_name": name,
                    "result": result,
                    "status": check_info["status"],
                }
            )

            return {
                "status": check_info["status"],
                "result": result,
                "failure_count": self.failure_counts[name],
                "is_critical": self.failure_counts[name] >= self.failure_threshold,
            }

        except Exception:
            check_info["status"] = "error"
            self.failure_counts[name] += 1
            circuit_breaker.on_failure()

            logger.error(
                "健康检查出错",
                check_name=name,
                error=str(e),
                failure_count=self.failure_counts[name],
            )

            return {
                "status": "error",
                "error": str(e),
                "failure_count": self.failure_counts[name],
                "is_critical": self.failure_counts[name] >= self.failure_threshold,
            }

    async def run_health_checks(self) -> Dict:
        """并发运行所有健康检查"""
        current_time = time.time()
        tasks = []
        task_names = []

        # 创建并发任务
        for name, check_info in self.checks.items():
            if current_time - \
                    check_info["last_check"] >= check_info["interval"]:
                task = asyncio.create_task(
                    self._execute_single_check(
                        name, check_info))
                tasks.append(task)
                task_names.append(name)

        if not tasks:
            return {}

        # 并发执行所有检查
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        final_results = {}
        for name, result in zip(task_names, results):
            if isinstance(result, Exception):
                final_results[name] = {
                    "status": "error",
                    "error": str(result),
                    "failure_count": self.failure_counts.get(name, 0) + 1,
                    "is_critical": True,
                }
            else:
                final_results[name] = result

        return final_results


class EnhancedAutoRecoveryManager:
    """增强版自动恢复管理器"""

    def __init___(self):
    """TODO: Add function description."""
    self.health_checker = EnhancedHealthChecker()
    self.running = False
    self.recovery_actions = {}
    self.max_recovery_attempts = int(
        os.getenv("MAX_RECOVERY_ATTEMPTS", "3"))
    self.recovery_cooldown = int(os.getenv("RECOVERY_COOLDOWN", "300"))
    self.last_recovery_time = {}
    self.recovery_counts = {}
    self.process_lock = ProcessLock(LOCK_FILE)
    self.heartbeat_task = None

    # 注册默认健康检查
    self._register_default_checks()

    def _register_default_checks(self):
        """注册默认健康检查"""
        # 系统资源检查（非阻塞）
        self.health_checker.register_check(
            "system_resources", self._check_system_resources, interval=60
        )

        # 内存使用检查
        self.health_checker.register_check(
            "memory_usage", self._check_memory_usage, interval=30
        )

        # 磁盘空间检查
        self.health_checker.register_check(
            "disk_space", self._check_disk_space, interval=300
        )

    def _check_system_resources(self) -> Dict:
        """检查系统资源（非阻塞）"""
        try:
            # 非阻塞CPU采样
            cpu_percent = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()

            # 使用公共资源检查方法
            healthy_cpu, issues_cpu = _check_resource(cpu_percent, 90, "CPU")
            healthy_mem, issues_mem = _check_resource(memory.percent, 90, "内存")

            healthy = healthy_cpu and healthy_mem
            issues = issues_cpu + issues_mem

            return {
                "healthy": healthy,
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "issues": issues,
            }
        except Exception:
            return {"healthy": False, "error": str(e)}

    def _check_memory_usage(self) -> Dict:
        """检查内存使用"""
        try:
            memory = psutil.virtual_memory()
            process = psutil.Process()
            process_memory = process.memory_info()

            # 检查系统内存
            system_healthy, system_issues = _check_resource(
                memory.percent, 85, "系统内存"
            )
            # 检查进程内存（如果超过2GB）
            process_healthy = process_memory.rss < 2 * 1024 * 1024 * 1024
            process_issues = (
                []
                if process_healthy
                else [
                    f"进程内存使用过高: {process_memory.rss / 1024 / 1024 / 1024:.1f}GB"
                ]
            )

            healthy = system_healthy and process_healthy
            issues = system_issues + process_issues

            return {
                "healthy": healthy,
                "system_memory_percent": memory.percent,
                "process_memory_gb": process_memory.rss / 1024 / 1024 / 1024,
                "issues": issues,
            }
        except Exception:
            return {"healthy": False, "error": str(e)}

    def _check_disk_space(self) -> Dict:
        """检查磁盘空间"""
        try:
            disk = psutil.disk_usage('/')
            free_gb = disk.free / 1024 / 1024 / 1024

            healthy = free_gb > 5  # 至少5GB可用空间
            issues = []

            if not healthy:
                issues.append(f"磁盘空间不足: {free_gb:.1f}GB")

            return {
                "healthy": healthy,
                "free_gb": free_gb,
                "total_gb": disk.total / 1024 / 1024 / 1024,
                "issues": issues,
            }
        except Exception:
            return {"healthy": False, "error": str(e)}

    def register_recovery_action(
        self,
        name: str,
        action_func: Callable,
        critical_checks: List[str] = None,
        dependencies: List[str] = None,
        priority: int = 0,
    ):
        """注册恢复动作"""
        self.recovery_actions[name] = {
            "func": action_func,
            "critical_checks": critical_checks or [],
            "dependencies": dependencies or [],
            "priority": priority,
            "last_attempt": 0,
            "attempt_count": 0,
        }
        logger.info(
            "注册恢复动作",
            action_name=name,
            critical_checks=critical_checks,
            dependencies=dependencies,
            priority=priority,
        )

    async def _heartbeat(self):
        """心跳检测"""
        while self.running:
            try:
                status = self.get_status()
                logger.info("HEARTBEAT", status=status)
                await asyncio.sleep(60)
            except Exception:
                logger.error("心跳检测出错", error=str(e))
                await asyncio.sleep(10)

    async def start_monitoring(self):
        """启动监控"""
        # 获取进程锁
        if not self.process_lock.acquire():
            logger.error("无法获取进程锁，退出")
            return

        self.running = True
        logger.info("增强版自动恢复管理器启动")

        # 启动心跳检测
        self.heartbeat_task = asyncio.create_task(self._heartbeat())

        try:
            while self.running:
                try:
                    # 运行健康检查
                    health_results = await self.health_checker.run_health_checks()

                    # 检查是否需要恢复
                    await self._check_and_recover(health_results)

                    # 等待下次检查
                    await asyncio.sleep(30)

                except Exception:
                    logger.error("监控循环出错", error=str(e))
                    await asyncio.sleep(10)
        finally:
            # 清理资源
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
            self.process_lock.release()

    async def _check_and_recover(self, health_results: Dict):
        """检查并执行恢复动作（带指数回退）"""
        current_time = time.time()

        # 定期清理日志
        if current_time % 3600 < 30:  # 每小时清理一次
            await self._cleanup_logs()

        # 按优先级排序恢复动作
        sorted_actions = sorted(
            self.recovery_actions.items(),
            key=lambda x: x[1]["priority"],
            reverse=True)

        for action_name, action_info in sorted_actions:
            # 检查冷却时间（指数回退）
            backoff = _calculate_backoff(action_info["attempt_count"])
            if current_time - action_info["last_attempt"] < backoff:
                continue

            # 检查是否超过最大尝试次数
            if action_info["attempt_count"] >= self.max_recovery_attempts:
                logger.warning(
                    "恢复动作已达到最大尝试次数",
                    action_name=action_name,
                    max_attempts=self.max_recovery_attempts,
                )
                continue

            # 检查关键健康检查
            should_recover = False
            critical_issues = []

            for check_name in action_info["critical_checks"]:
                if check_name in health_results:
                    check_result = health_results[check_name]
                    if check_result.get("is_critical", False):
                        should_recover = True
                        critical_issues.append(check_name)

            if should_recover:
                logger.warning(
                    "检测到关键问题，准备执行恢复动作",
                    action_name=action_name,
                    critical_issues=critical_issues,
                    backoff=backoff,
                    extra={
                        "action": action_name,
                        "attempt": action_info["attempt_count"],
                    },
                )

                try:
                    # 执行恢复动作
                    if asyncio.iscoroutinefunction(action_info["func"]):
                        result = await action_info["func"]()
                    else:
                        result = action_info["func"]()

                    action_info["last_attempt"] = current_time
                    action_info["attempt_count"] += 1

                    logger.info(
                        "恢复动作执行完成",
                        action_name=action_name,
                        result=result,
                        attempt_count=action_info["attempt_count"],
                        extra={
                            "action": action_name,
                            "attempt": action_info["attempt_count"],
                        },
                    )

                except Exception:
                    action_info["last_attempt"] = current_time
                    action_info["attempt_count"] += 1

                    logger.error(
                        "恢复动作执行失败",
                        action_name=action_name,
                        error=str(e),
                        attempt_count=action_info["attempt_count"],
                        extra={
                            "action": action_name,
                            "attempt": action_info["attempt_count"],
                        },
                    )

    async def _cleanup_logs(self):
        """清理日志文件"""
        try:
            # 清理健康检查历史（保留最近1000条）
            if len(self.health_checker.history) > 1000:
                # 保留最近1000条记录
                self.health_checker.history = deque(
                    list(self.health_checker.history)[-1000:], maxlen=1000
                )
                logger.info("健康检查历史已清理，保留最近1000条记录")

            # 清理过期的恢复计数（7天前）
            current_time = time.time()
            expired_keys = []
            for key, last_time in self.last_recovery_time.items():
                if current_time - last_time > 7 * 24 * 3600:  # 7天
                    expired_keys.append(key)

            for key in expired_keys:
                del self.last_recovery_time[key]
                if key in self.recovery_counts:
                    del self.recovery_counts[key]

            if expired_keys:
                logger.info(f"清理过期恢复记录: {len(expired_keys)}个")

        except Exception:
            logger.error("清理日志失败", error=str(e))

    async def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        self.process_lock.release()
        logger.info("增强版自动恢复管理器停止")

    def get_status(self) -> Dict:
        """获取状态"""
        return {
            "running": self.running,
            "health_checks": len(self.health_checker.checks),
            "recovery_actions": len(self.recovery_actions),
            "recovery_counts": self.recovery_counts.copy(),
            "history_size": len(self.health_checker.history),
            "process_pid": os.getpid(),
        }


class EnhancedServiceRestartManager:
    """增强版服务重启管理器"""

    def __init___(self, script_path: str):
    """TODO: Add function description."""
    self.script_path = Path(script_path)
    self.restart_count = 0
    self.max_restarts = int(os.getenv("MAX_RESTARTS", "5"))
    self.restart_cooldown = int(os.getenv("RESTART_COOLDOWN", "600"))
    self.last_restart_time = 0
    self.current_pid = os.getpid()

    async def restart_service(self) -> Dict:
        """重启服务（带指数回退）"""
        current_time = time.time()

        # 检查重启次数限制
        if self.restart_count >= self.max_restarts:
            return {
                "success": False,
                "error": f"已达到最大重启次数: {self.max_restarts}",
                "restart_count": self.restart_count,
            }

        # 指数回退检查
        backoff = _calculate_backoff(self.restart_count)
        if current_time - self.last_restart_time < backoff:
            return {
                "success": False,
                "error": f"重启冷却时间未到，需要等待 {backoff}s",
                "restart_count": self.restart_count,
                "backoff": backoff,
            }

        try:
            logger.warning(
                "准备重启服务",
                script_path=str(self.script_path),
                restart_count=self.restart_count + 1,
                backoff=backoff,
            )

            # 先终止当前进程（优雅关闭）
            if sys.platform != "win32":
                try:
                    os.kill(self.current_pid, signal.SIGTERM)
                    # 等待1秒后检查进程是否退出
                    await asyncio.sleep(1)
                    try:
                        os.kill(self.current_pid, 0)  # 检查进程是否还存在
                        # 如果还存在，强制终止
                        os.kill(self.current_pid, signal.SIGKILL)
                    except OSError:
                        # 进程已经退出
                        pass
                except ProcessLookupError:
                    pass

            # 启动新进程
            if sys.platform == "win32":
                # Windows
                subprocess.Popen(
                    ["python", str(self.script_path)],
                    creationflags=subprocess.CREATE_NEW_CONSOLE,
                )
            else:
                # Linux/Mac
                subprocess.Popen(
                    ["python3", str(self.script_path)], start_new_session=True
                )

            self.restart_count += 1
            self.last_restart_time = current_time

            logger.info(
                "服务重启成功",
                script_path=str(self.script_path),
                restart_count=self.restart_count,
            )

            return {
                "success": True,
                "restart_count": self.restart_count,
                "message": "服务重启成功",
                "backoff": backoff,
            }

        except Exception:
            logger.error(
                "服务重启失败", script_path=str(self.script_path), error=str(e)
            )

            return {
                "success": False,
                "error": str(e),
                "restart_count": self.restart_count,
            }

    def reset_restart_count(self):
        """重置重启计数"""
        self.restart_count = 0
        logger.info("重启计数已重置")


# 全局实例
_global_enhanced_recovery_manager: Optional[EnhancedAutoRecoveryManager] = None


async def get_enhanced_recovery_manager() -> EnhancedAutoRecoveryManager:
    """获取全局增强版恢复管理器实例"""
    global _global_enhanced_recovery_manager
    if _global_enhanced_recovery_manager is None:
        _global_enhanced_recovery_manager = EnhancedAutoRecoveryManager()
    return _global_enhanced_recovery_manager


async def start_enhanced_auto_recovery():
    """启动增强版自动恢复"""
    manager = await get_enhanced_recovery_manager()
    await manager.start_monitoring()


async def stop_enhanced_auto_recovery():
    """停止增强版自动恢复"""
    global _global_enhanced_recovery_manager
    if _global_enhanced_recovery_manager:
        await _global_enhanced_recovery_manager.stop_monitoring()
        _global_enhanced_recovery_manager = None


# 分布式锁支持（Redis）
class DistributedLock:
    """分布式锁管理器"""

    def __init___(self, redis_host: str = "localhost", redis_port: int = 6379):
    """TODO: Add function description."""
    self.redis_host = redis_host
    self.redis_port = redis_port
    self.redis_client = None

    async def acquire_lock(self, lock_key: str, timeout: int = 60) -> bool:
        """获取分布式锁"""
        try:

            if not self.redis_client:
                self.redis_client = redis.Redis(
                    host=self.redis_host,
                    port=self.redis_port,
                    decode_responses=True)

            # 使用asyncio.to_thread避免阻塞
            return await asyncio.to_thread(
                self.redis_client.set, lock_key, "1", nx=True, ex=timeout
            )
        except Exception:
            logger.error("获取分布式锁失败", error=str(e))
            return False

    async def release_lock(self, lock_key: str):
        """释放分布式锁"""
        try:
            if self.redis_client:
                await asyncio.to_thread(self.redis_client.delete, lock_key)
        except Exception:
            logger.error("释放分布式锁失败", error=str(e))


# 使用示例
async def example_usage():
    """使用示例"""
    # 获取管理器
    manager = await get_enhanced_recovery_manager()

    # 注册自定义恢复动作

    async def custom_recovery_actionn():
    """TODO: Add function description."""
    logger.info("执行自定义恢复动作")
    return {"status": "success"}

    manager.register_recovery_action(
        name="custom_recovery",
        action_func=custom_recovery_action,
        critical_checks=["system_resources", "memory_usage"],
        priority=1,
    )

    # 启动监控
    await manager.start_monitoring()


if __name__ == "__main__":
    # 测试运行
    asyncio.run(example_usage())
