# 代码重构建议

## 重复函数分析

### 函数: main
出现在: auto_project_cleanup.py, final_project_fixer.py, project_health_checker.py, add_api_config.py, auto_migration.py, diagnose_migration.py, fix_css_paths.py, fix_migrated_paths.py, reliable_server.py, rollback_batch_writes.py, test_elk_connection.py, validate_deployment.py, code_cleaner.py

### 函数: __init__
出现在: auto_project_cleanup.py, build_production_package.py, execute_task_checklist.py, final_project_fixer.py, fix_task_issues.py, install_windows_service.py, project_health_check.py, project_health_checker.py, run_comprehensive_check.py, auto_migration.py, rollback_batch_writes.py, test_elk_connection.py, validate_deployment.py, locust_stress_test.py, locust_stress_test.py, error_handling_load_test.py, code_cleaner.py, mock_utils.py, mock_utils.py, mock_utils.py, code_quality.py, code_quality.py, code_quality.py, database_connection_pool.py, database_manager.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, exceptions.py, optimized_retry.py, auto_recovery_manager_enhanced.py, auto_recovery_manager_enhanced.py, auto_recovery_manager_enhanced.py, auto_recovery_manager_enhanced.py, auto_recovery_manager_enhanced.py, auto_recovery_manager_enhanced.py, auto_sync_scheduler.py, auto_sync_scheduler.py, auto_sync_scheduler.py, auto_sync_scheduler.py, auto_sync_scheduler.py, business_translation_rules.py, config_persistence_service.py, database_manager.py, database_table_manager.py, data_processor.py, data_write_manager.py, enhanced_json_field_matcher.py, excel_field_matcher.py, excel_field_matcher_pretranslated.py, fast_sync_service.py, fast_sync_service.py, fast_sync_service.py, fast_sync_service.py, fast_sync_service.py, fast_sync_service.py, fast_sync_service.py, field_analysis_service.py, field_config_service.py, field_extractor_service.py, field_validation_service.py, field_value_mapping_service.py, intelligent_field_mapper.py, log_service.py, maintenance_manager.py, material_master_scheduler.py, md_parser.py, monitor_service.py, realtime_log_service.py, realtime_log_service.py, retry_helper.py, retry_helper.py, robust_json_parser.py, status_mapping_service.py, sync_status_manager.py, task_service.py, unified_field_manager.py, unified_field_service.py, ys_api_client.py, ys_api_client.py, zero_downtime_implementation.py, zero_downtime_implementation.py, zero_downtime_implementation.py

### 函数: generate_cleanup_report
出现在: auto_project_cleanup.py, code_cleaner.py

### 函数: run_all_checks
出现在: project_health_check.py, run_comprehensive_check.py

### 函数: check_file_structure
出现在: project_health_check.py, run_comprehensive_check.py

### 函数: check_api_endpoints
出现在: project_health_check.py, run_comprehensive_check.py

### 函数: generate_report
出现在: project_health_check.py, error_handling_load_test.py

### 函数: end_headers
出现在: start_quick_test.py, reliable_server.py, test_server.py

### 函数: do_OPTIONS
出现在: start_quick_test.py, test_server.py

### 函数: _calculate_complexity
出现在: auto_migration.py, code_quality.py

### 函数: get_connection
出现在: rollback_batch_writes.py, database_manager.py

### 函数: on_start
出现在: locust_stress_test.py, locust_stress_test.py, locust_stress_test.py, locust_stress_test.py

### 函数: load_field_config
出现在: locust_stress_test.py, excel_field_matcher_pretranslated.py

### 函数: decorator
出现在: locust_stress_test.py, optimized_retry.py, retry_helper.py

### 函数: __enter__
出现在: locust_stress_test.py, database_manager.py

### 函数: __exit__
出现在: locust_stress_test.py, database_manager.py

### 函数: setup_method
出现在: test_md_to_json_converter.py, test_md_to_json_converter.py, test_md_to_json_converter.py, test_md_to_json_converter.py, test_rollback_scripts.py, test_rollback_scripts.py, test_rollback_scripts.py

### 函数: should_retry
出现在: exceptions.py, retry_helper.py

### 函数: record_attempt
出现在: optimized_retry.py, retry_helper.py

### 函数: allow_execution
出现在: auto_recovery_manager_enhanced.py, auto_sync_scheduler.py

### 函数: on_success
出现在: auto_recovery_manager_enhanced.py, auto_sync_scheduler.py

### 函数: on_failure
出现在: auto_recovery_manager_enhanced.py, auto_sync_scheduler.py

### 函数: get_status
出现在: auto_recovery_manager_enhanced.py, database_table_manager.py, material_master_scheduler.py

### 函数: get_cache_stats
出现在: config_persistence_service.py, field_extractor_service.py

### 函数: clear_cache
出现在: config_persistence_service.py, field_extractor_service.py

### 函数: _load_config
出现在: database_manager.py, material_master_scheduler.py

### 函数: __post_init__
出现在: database_table_manager.py, robust_json_parser.py

### 函数: _extract_field_value
出现在: data_processor.py, data_write_manager.py

### 函数: _clean_and_convert_value
出现在: data_processor.py, data_write_manager.py

### 函数: _parse_datetime_string
出现在: data_processor.py, data_write_manager.py

### 函数: get_metrics
出现在: fast_sync_service.py, fast_sync_service.py, fast_sync_service.py, fast_sync_service.py, fast_sync_service.py

### 函数: record_request
出现在: fast_sync_service.py, ys_api_client.py

### 函数: set_current_module
出现在: field_config_service.py, field_extractor_service.py, intelligent_field_mapper.py

### 函数: _extract_fields_from_record
出现在: field_config_service.py, field_extractor_service.py

### 函数: _is_technical_field
出现在: field_config_service.py, field_config_api.py

### 函数: _get_module_display_name
出现在: field_config_service.py, unified_field_service.py, field_config_api.py

### 函数: validate_module_config
出现在: field_validation_service.py, intelligent_field_mapper.py

### 函数: get_field_mapping
出现在: field_value_mapping_service.py, robust_json_parser.py

### 函数: get_mappable_fields
出现在: field_value_mapping_service.py, status_mapping_service.py

### 函数: parse_all_modules
出现在: md_parser.py, robust_json_parser.py

### 函数: _create_empty_user_config
出现在: unified_field_manager.py, unified_field_service.py

### 函数: _determine_importance
出现在: unified_field_manager.py, unified_field_service.py

### 函数: _merge_configs
出现在: unified_field_manager.py, unified_field_service.py

## 重构建议

1. 将通用函数提取到公共模块
2. 创建基础类或工具类
3. 使用装饰器减少重复代码
4. 实施代码复用策略
