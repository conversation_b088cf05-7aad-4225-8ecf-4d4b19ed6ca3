import json
import os
from pathlib import Path

#!/usr/bin/env python3
"""
简单的代码统计工具 - 替代cloc
生成项目屎山地图
"""


def count_lines_in_file(file_path):
    """统计文件行数"""
    try:
        with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
            lines = f.readlines()
            total_lines = len(lines)
            code_lines = len(
                [
                    line
                    for line in lines
                    if line.strip() and not line.strip().startswith("#")
                ]
            )
            comment_lines = len(
                [line for line in lines if line.strip().startswith("#")]
            )
            blank_lines = total_lines - code_lines - comment_lines
            return {
                "total": total_lines,
                "code": code_lines,
                "comments": comment_lines,
                "blank": blank_lines,
            }
    except Exception:
        return {
            "total": 0,
            "code": 0,
            "comments": 0,
            "blank": 0,
            "error": str(e)}


def get_file_extension(file_path):
    """获取文件扩展名"""
    return Path(file_path).suffix.lower()


def scan_directory(root_path):
    """扫描目录，统计所有文件"""
    stats = defaultdict(
        lambda: {
            "files": 0,
            "total_lines": 0,
            "code_lines": 0,
            "comment_lines": 0,
            "blank_lines": 0,
        }
    )

    file_list = []
    ignore_dirs = {".git", "__pycache__", "node_modules", ".vscode", "logs"}

    for root, dirs, files in os.walk(root_path):
        # 过滤掉不需要的目录
        dirs[:] = [d for d in dirs if d not in ignore_dirs]

        for file in files:
            file_path = os.path.join(root, file)
            ext = get_file_extension(file)

            # 跳过二进制文件和特殊文件
            if ext in [
                ".pyc",
                ".exe",
                ".dll",
                ".so",
                ".db",
                ".sqlite",
                ".log",
                ".png",
                ".jpg",
                ".jpeg",
                ".gif",
                ".ico",
            ]:
                continue

            file_stats = count_lines_in_file(file_path)

            if "error" not in file_stats:
                stats[ext]["files"] += 1
                stats[ext]["total_lines"] += file_stats["total"]
                stats[ext]["code_lines"] += file_stats["code"]
                stats[ext]["comment_lines"] += file_stats["comments"]
                stats[ext]["blank_lines"] += file_stats["blank"]

                file_list.append(
                    {
                        "path": os.path.relpath(file_path, root_path),
                        "extension": ext,
                        "lines": file_stats["total"],
                        "code_lines": file_stats["code"],
                    }
                )

    return stats, file_list


def generate_report(root_path):
    """生成详细的代码统计报告"""
    print("🔍 正在扫描代码库...")
    stats, file_list = scan_directory(root_path)

    # 生成文本报告
    report = []
    report.append("=" * 60)
    report.append("📊 YS-API V3.0 代码统计报告（屎山地图）")
    report.append("=" * 60)
    report.append(
        f"📅 生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    )
    report.append("")

    # 按扩展名统计
    report.append("📋 按文件类型统计:")
    report.append("-" * 60)
    report.append(
        f"{'文件类型':<15} {'文件数':<8} {'总行数':<10} {'代码行':<10} {'注释行':<10} {'空行':<8}"
    )
    report.append("-" * 60)

    total_files = 0
    total_lines = 0
    total_code = 0

    # 排序：按代码行数降序
    sorted_stats = sorted(
        stats.items(),
        key=lambda x: x[1]["code_lines"],
        reverse=True)

    for ext, stat in sorted_stats:
        ext_name = ext if ext else "(无扩展名)"
        report.append(
            f"{ext_name:<15} {stat['files']:<8} {stat['total_lines']:<10} {stat['code_lines']:<10} {stat['comment_lines']:<10} {stat['blank_lines']:<8}"
        )
        total_files += stat["files"]
        total_lines += stat["total_lines"]
        total_code += stat["code_lines"]

    report.append("-" * 60)
    report.append(
        f"{'总计':<15} {total_files:<8} {total_lines:<10} {total_code:<10}")
    report.append("")

    # 大文件列表（超过100行的文件）
    big_files = sorted(
        [f for f in file_list if f["lines"] > 100],
        key=lambda x: x["lines"],
        reverse=True,
    )
    if big_files:
        report.append("🔥 大文件清单（>100行）:")
        report.append("-" * 60)
        report.append(f"{'文件路径':<50} {'总行数':<8} {'代码行':<8}")
        report.append("-" * 60)
        for file in big_files[:20]:  # 只显示前20个最大的文件
            report.append(
                f"{file['path']:<50} {file['lines']:<8} {file['code_lines']:<8}"
            )
        report.append("")

    # 目录结构分析
    dir_stats = defaultdict(int)
    for file in file_list:
        dir_path = os.path.dirname(file["path"])
        if dir_path:
            dir_stats[dir_path.split("/")[0]] += file["lines"]

    report.append("📁 目录代码量分布:")
    report.append("-" * 40)
    for dir_name, lines in sorted(
            dir_stats.items(), key=lambda x: x[1], reverse=True):
        report.append(f"{dir_name:<30} {lines:>8} 行")

    report.append("")
    report.append("🎯 重构建议:")
    report.append("- 优先重构大文件（>500行）")
    report.append("- 关注代码量最多的目录")
    report.append("- 检查无扩展名文件的用途")
    report.append("")

    return "\n".join(report), stats, file_list


if __name__ == "__main__":
    root_path = "."
    report_text, stats_data, files_data = generate_report(root_path)

    # 保存文本报告
    with open("stats.txt", "w", encoding="utf-8") as f:
        f.write(report_text)

    # 保存JSON数据供后续分析
    analysis_data = {
        "generation_time": __import__("datetime").datetime.now().isoformat(),
        "summary": {
            "total_files": sum(
                s["files"] for s in stats_data.values()),
            "total_lines": sum(
                s["total_lines"] for s in stats_data.values()),
            "total_code_lines": sum(
                s["code_lines"] for s in stats_data.values()),
        },
        "by_extension": dict(stats_data),
        "files": files_data,
    }

    with open("code_analysis.json", "w", encoding="utf-8") as f:
        json.dump(analysis_data, f, indent=2, ensure_ascii=False)

    print("✅ 代码统计完成！")
    print("📄 文本报告: stats.txt")
    print("📊 详细数据: code_analysis.json")
    print("")
    print("📋 快速摘要:")
    print(f"- 总文件数: {analysis_data['summary']['total_files']}")
    print(f"- 总代码行: {analysis_data['summary']['total_code_lines']}")
    print(
        f"- 平均每文件: {analysis_data['summary']['total_code_lines'] // max(1, analysis_data['summary']['total_files'])} 行"
    )
