#!/bin/bash
# 屎山代码绞杀 - 快速启动脚本
# 一键启动完整的Strangler Fig环境

set -e

echo "🔥 屎山代码绞杀系统启动中..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查docker-compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose未安装"
    exit 1
fi

# 设置环境变量
export COMPOSE_PROJECT_NAME=strangler-fig
export TRAFFIC_SPLIT_PERCENT=${TRAFFIC_SPLIT_PERCENT:-0}

echo "📊 初始流量分配: ${TRAFFIC_SPLIT_PERCENT}% -> 新系统"

# 创建必要的目录
mkdir -p logs reports tasks test-results graveyard
mkdir -p new-system  # 新系统代码目录

# 检查必要文件
echo "🔍 检查必要文件..."
required_files=(
    "backend/requirements.txt"
    "docker-compose.strangler.yml"
    "monitoring/prometheus.yml"
    "monitoring/alert_rules.yml"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "⚠️ 缺少文件: $file"
        case $file in
            "backend/requirements.txt")
                echo "flask==2.3.2" > backend/requirements.txt
                echo "redis==4.5.4" >> backend/requirements.txt
                echo "prometheus-client==0.16.0" >> backend/requirements.txt
                ;;
            "new-system/requirements.txt")
                mkdir -p new-system
                echo "fastapi==0.100.0" > new-system/requirements.txt
                echo "psycopg2-binary==2.9.6" >> new-system/requirements.txt
                echo "redis==4.5.4" >> new-system/requirements.txt
                ;;
        esac
    fi
done

# 构建镜像
echo "🏗️ 构建Docker镜像..."
docker-compose -f docker-compose.strangler.yml build

# 启动基础设施
echo "🚀 启动基础设施服务..."
docker-compose -f docker-compose.strangler.yml up -d postgres redis

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 10

# 启动应用服务
echo "🔄 启动应用服务..."
docker-compose -f docker-compose.strangler.yml up -d legacy-api new-api strangler-proxy

# 启动监控服务
echo "📊 启动监控服务..."
docker-compose -f docker-compose.strangler.yml up -d prometheus grafana

# 可选启动日志服务
if [ "${ENABLE_ELK}" = "true" ]; then
    echo "📝 启动ELK日志栈..."
    docker-compose -f docker-compose.strangler.yml up -d elasticsearch logstash kibana
fi

# 等待服务启动
echo "⏳ 等待服务启动完成..."
sleep 20

# 健康检查
echo "🔍 执行健康检查..."
services=(
    "http://localhost:5001/health|Legacy API"
    "http://localhost:5002/health|New API"  
    "http://localhost:8080/health|Strangler Proxy"
    "http://localhost:9090/-/healthy|Prometheus"
    "http://localhost:3000/api/health|Grafana"
)

for service in "${services[@]}"; do
    url="${service%|*}"
    name="${service#*|}"
    
    if curl -f "$url" >/dev/null 2>&1; then
        echo "✅ $name: 健康"
    else
        echo "❌ $name: 不健康"
    fi
done

# 初始化模块追踪器
echo "📋 初始化模块状态追踪器..."
if [ -f "scripts/module_tracker_simple.py" ]; then
    python scripts/module_tracker_simple.py --init
else
    echo "⚠️ 模块追踪器文件不存在"
fi

# 显示服务地址
echo ""
echo "🎉 屎山代码绞杀系统启动完成！"
echo ""
echo "📍 服务地址:"
echo "  🔄 代理层 (主入口):    http://localhost:8080"
echo "  🏚️ Legacy API:        http://localhost:5001"
echo "  🆕 New API:           http://localhost:5002"
echo "  📊 Prometheus:        http://localhost:9090"
echo "  📈 Grafana:           http://localhost:3000 (admin/admin)"
if [ "${ENABLE_ELK}" = "true" ]; then
    echo "  📝 Kibana:            http://localhost:5601"
fi
echo ""
echo "🔧 管理命令:"
echo "  查看日志:   docker-compose -f docker-compose.strangler.yml logs -f"
echo "  停止服务:   ./stop_strangler.sh"
echo "  切换流量:   ./scripts/gradual_traffic_switch.sh --percentage 10"
echo "  查看状态:   python scripts/module_tracker_simple.py --report"
echo ""
echo "📚 下一步:"
echo "  1. 访问 http://localhost:8080 确认代理层工作正常"
echo "  2. 运行模块测试: python tests/module_migration/test_generator.py --all"
echo "  3. 开始迁移第2个模块: 采购订单列表"
echo ""
