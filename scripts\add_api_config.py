import glob
import logging
import os

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
为所有迁移页面添加API配置修复
"""


# 设置日志
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)


def add_api_config_to_html(file_path):
    """为HTML文件添加API配置修复脚本"""
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    logger.info(f"\n处理: {os.path.basename(file_path)}")

    # 检查是否已经添加过
    if "api-config-fix.js" in content:
        logger.info("  ℹ️  API配置已存在，跳过")
        return

    # 找到app-bootstrap.js的位置，在其后添加API配置
    bootstrap_line = '    <script src="../js/core/app-bootstrap.js"></script>'
    api_config_line = '    <script src="../js/api-config-fix.js"></script>'

    if bootstrap_line in content:
        # 在app-bootstrap.js后添加API配置
        content = content.replace(
            bootstrap_line, bootstrap_line + "\n" + api_config_line
        )

        # 保存文件
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)

        logger.info("  ✅ 已添加API配置修复脚本")
    else:
        logger.info("  ❌ 未找到app-bootstrap.js引用")


def main():
    """主函数"""
    logger.info("=== 为迁移页面添加API配置修复 ===")

    # 获取所有迁移的HTML文件
    migrated_dir = os.path.join(
        os.path.dirname(os.path.dirname(__file__)), "frontend", "migrated"
    )
    html_files = glob.glob(os.path.join(migrated_dir, "*.html"))

    # 排除测试文件
    html_files = [f for f in html_files if "path-test.html" not in f]

    logger.info(f"找到 {len(html_files)} 个需要处理的HTML文件")

    for file_path in html_files:
        try:
            add_api_config_to_html(file_path)
        except Exception:
            logger.info(f"❌ 处理失败 {file_path}: {e}")

    logger.info(f"\n=== API配置添加完成 ===")
    logger.info("现在迁移页面应该能够正确连接到后端API服务器")
    logger.info("\n下一步:")
    logger.info("1. 启动后端服务器: cd backend && python start_server.py")
    logger.info("2. 刷新前端页面测试API功能")


if __name__ == "__main__":
    main()
