# YS-API V3 错误处理监控配置
# Prometheus监控重试率和错误率
# 版本: 1.0.0

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'ys-api-v3'
    environment: 'production'

# 告警规则文件
rule_files:
  - "alert_rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 数据采集配置
scrape_configs:
  # YS-API V3 后端监控
  - job_name: 'ys-api-backend'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: localhost:8000

  # 前端错误处理监控（通过后端代理）
  - job_name: 'ys-api-frontend-errors'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/api/metrics/frontend'
    scrape_interval: 30s
    honor_labels: true
    params:
      module: ['frontend_errors']

  # 系统资源监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
    scrape_interval: 15s

  # 数据库监控（如果使用PostgreSQL）
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['localhost:9187']
    scrape_interval: 30s

  # Nginx监控（如果使用）
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['localhost:9113']
    scrape_interval: 15s

# 远程写入配置（可选，用于长期存储）
# remote_write:
#   - url: "http://cortex:8080/api/prom/push"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500
