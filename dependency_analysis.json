{"generation_time": "2025-08-06T22:38:01.842242", "dependencies": {"ai_code_review_system.py": [], "analyze_dependencies.py": ["re", "json", "collections", "os"], "analyze_project_stats.py": ["collections", "os"], "automation_validator.py": [], "auto_fix_comprehensive_issues.py": ["typing", "configparser", "pathlib", "re", "json", "datetime", "logging"], "auto_project_cleanup.py": ["shutil", "pathlib", "json", "datetime", "logging"], "batch_code_quality_fix.py": ["typing", "re", "os", "pathlib", "ast"], "batch_flake8_fix.py": ["glob", "subprocess"], "batch_validate_modules.py": ["pathlib", "subprocess", "json", "time"], "build_production_package.py": ["shutil", "subprocess", "platform", "pathlib", "sys", "json", "os", "logging"], "check_code_quality.py": ["subprocess", "os"], "check_naming_conflicts.py": ["pathlib", "sys", "re", "collections"], "check_sonarqube_status.py": ["pathlib"], "cicd_builder_simple.py": [], "cicd_pipeline_builder.py": ["yaml", "pathlib", "sys", "json", "os", "logging", "datetime"], "cicd_pipeline_builder_optimized.py": ["yaml", "pathlib", "sys", "json", "os", "logging", "datetime"], "comprehensive_code_fixer.py": [], "comprehensive_production_test.py": [], "config_environmentizer.py": ["logging", "pathlib", "configparser", "os"], "config_environmentizer_clean.py": ["logging", "pathlib", "configparser", "os"], "core_mvp_extractor.py": [], "database_enhancement_demo.py": [], "day3_final_test.py": [], "day4_proxy_builder.py": [], "day5_final_validator.py": [], "execute_task_checklist.py": ["configparser", "pathlib", "re", "datetime", "logging", "ast"], "file_sentinel.py": ["typing", "<PERSON><PERSON><PERSON><PERSON>", "json", "<PERSON><PERSON><PERSON>", "datetime", "pathlib"], "final_batch.py": ["pathlib", "subprocess", "sys"], "final_project_fixer.py": [], "final_verification.py": ["pathlib", "subprocess"], "fix_all_flake8.py": ["subprocess", "sys", "re", "os", "pathlib"], "fix_build_script.py": ["logging", "re", "pathlib"], "fix_execute_task_script.py": ["logging", "re", "pathlib"], "fix_issues.py": ["subprocess", "sqlite3", "sys", "os", "pathlib"], "fix_sonarqube_issues.py": ["pathlib", "sys", "re", "os"], "fix_task_issues.py": ["pathlib", "ast", "re", "datetime"], "fix_xml_files.py": ["pathlib", "sys", "re", "xml.etree.ElementTree"], "flake8_stats.py": ["subprocess", "re", "collections"], "function_duplicate_checker.py": ["pathlib", "ast", "collections", "sys"], "generate_stats.py": ["pathlib", "json", "os"], "install_windows_service.py": ["asyncio", "win32event", "start_production_enhanced", "pathlib", "sys", "win32serviceutil", "servicemanager", "os", "logging", "win32service"], "machine_cleanup.py": ["shutil", "subprocess", "json", "<PERSON><PERSON><PERSON>", "os"], "mvp_launcher.py": [], "pollution_guard.py": ["time", "sys", "os", "pathlib", "signal"], "port_locker.py": ["subprocess", "time", "socket", "sys", "json", "os", "pathlib"], "production_readiness_report.py": ["time", "pathlib", "sqlite3", "json", "os", "logging", "datetime"], "production_test_runner.py": [], "project_health_check.py": ["subprocess", "configparser", "pathlib", "sqlite3", "requests", "sys", "os", "datetime", "importlib.util"], "project_health_checker.py": [], "proxy_strangler.py": [], "python_version_diagnostic.py": [], "quick_health_check.py": ["pathlib", "sqlite3", "sys", "json", "os", "logging", "psutil", "datetime"], "quick_health_check_fixed.py": ["py_compile", "sys", "json", "os"], "quick_status_check.py": ["subprocess"], "quick_syntax_fix.py": ["subprocess", "re", "os"], "quick_test.py": [], "real_data_full_test.py": [], "refactor_data_write_manager.py": [], "refactor_fast_sync_service.py": [], "refactor_ys_api_client.py": [], "remaining_issues_fixer.py": ["logging", "pathlib", "re", "datetime"], "run_comprehensive_check.py": ["configparser", "sqlite3", "requests", "json", "datetime", "pathlib"], "setup_ide_security_plugins.py": [], "setup_test_env.py": [], "shit_mountain_mapper.py": [], "smart_duplicate_cleaner.py": ["logging", "shutil", "json", "pathlib"], "smart_file_creator.py": ["pathlib", "file_sentinel", "sys", "datetime"], "sonarqube_cleanup.py": ["pathlib", "shutil", "re", "os"], "start_month1_validation.py": ["pathlib", "subprocess", "time"], "start_month2.py": ["pathlib", "time"], "start_quick_test.py": [], "status_check.py": [], "systematic_duplicate_detector.py": ["collections", "pathlib", "re", "json", "<PERSON><PERSON><PERSON>", "logging"], "systematic_migration_starter.py": [], "system_integration_final.py": [], "test_anti_duplicate_system.py": [], "test_backend.py": [], "test_day3_core.py": [], "test_day3_offline.py": [], "test_day4_proxy.py": [], "test_frontend.py": [], "test_imports.py": [], "test_modules_direct.py": [], "test_mvp.py": [], "test_proxy.py": [], "three_step_fix.py": ["subprocess", "time"], "universal_code_quality_fixer.py": [], "unused_import_checker.py": ["ast", "os"], "verify_enhancements.py": [], "verify_field_fetching.py": [], "verify_fixes.py": ["pathlib", "inspect", "port_manager", "sys"], "verify_module.py": ["time", "sys", "json", "pathlib", "xml.etree.ElementTree"], "verify_startup.py": ["pathlib", "sys"], "verify_startup_clean.py": ["pathlib", "sys", "os"], "violent_cleanup.py": ["shutil", "subprocess", "time", "typing", "sys", "re", "json", "os", "pathlib", "signal"], "week1_completion_report.py": [], "week1_completion_week2_planning.py": [], "week1_quick_start.py": [], "week1_week2_integration_demo.py": [], "week2_completion_report.py": [], "week3_integration_final.py": [], "backend\\start_server.py": ["logging", "u<PERSON><PERSON>", "sys", "pathlib"], "backend\\start_server_clean.py": ["pathlib", "port_manager", "sys", "logging", "u<PERSON><PERSON>"], "backend\\start_server_fixed.py": ["fastapi.middleware.cors", "n", "CORSMiddleware", "json", "os", "logging", "nfrom", "Response", "fastapi.responses", "Path", "<PERSON><PERSON><PERSON>", "u<PERSON><PERSON>", "xml.etree.ElementTree", "fastapi.staticfiles", "import", "port_manager", "pathlib", "FastAPI", "nimport", "sys", "StaticFiles", "PortManager"], "backend\\start_simple.py": ["fastapi.responses", "<PERSON><PERSON><PERSON>", "fastapi.staticfiles", "pathlib", "json", "logging", "u<PERSON><PERSON>", "xml.etree.ElementTree"], "backend\\test_backend_automation.py": [], "backend\\__init__.py": [], "backend\\app\\main.py": ["fastapi.middleware.cors", "codecs", "fastapi.responses", "fastapi.staticfiles", "pathlib", "sys", "json", "os", "logging", "datetime"], "backend\\app\\main_original.py": ["contextlib", "fastapi.middleware.cors", "structlog", "fastapi.staticfiles", ".api.v1", "os", "pathlib"], "backend\\app\\api\\__init__.py": [], "backend\\app\\api\\v1\\auth.py": ["<PERSON><PERSON><PERSON>"], "backend\\app\\api\\v1\\config.py": ["<PERSON><PERSON><PERSON>", "structlog", "typing"], "backend\\app\\api\\v1\\database.py": ["<PERSON><PERSON><PERSON>", "structlog", "typing", "datetime"], "backend\\app\\api\\v1\\database_health.py": ["structlog"], "backend\\app\\api\\v1\\enhanced_sync.py": ["asyncio", "time", "structlog", "sqlalchemy.ext.asyncio", "datetime"], "backend\\app\\api\\v1\\excel_translation.py": ["batch_pretranslation", "structlog", "tempfile", "fastapi.responses", "sys", "re", "os", "pathlib"], "backend\\app\\api\\v1\\field_config_api.py": ["structlog", "json", "datetime", "pathlib", "pydantic"], "backend\\app\\api\\v1\\maintenance.py": ["structlog"], "backend\\app\\api\\v1\\monitor.py": ["time", "structlog", "sqlalchemy", "configparser", "pyodbc", "json", "os", "datetime"], "backend\\app\\api\\v1\\realtime_logs.py": ["asyncio", "structlog", "fastapi.responses", "json", "datetime"], "backend\\app\\api\\v1\\sync.py": ["asyncio", "structlog", "sqlalchemy.ext.asyncio", "typing", "datetime", "uuid", "traceback"], "backend\\app\\api\\v1\\sync_status.py": ["structlog"], "backend\\app\\api\\v1\\tasks.py": ["structlog"], "backend\\app\\api\\v1\\unified_field_config.py": ["structlog", "fastapi.responses"], "backend\\app\\api\\v1\\__init__.py": [], "backend\\app\\core\\code_quality.py": ["dataclasses", "ast", "structlog", "pathlib"], "backend\\app\\core\\config.py": ["structlog", "typing", "pydantic_settings", "configparser", "pathlib"], "backend\\app\\core\\database.py": ["sqlalchemy.orm", ".database_manager", "structlog", ".config"], "backend\\app\\core\\database_connection_pool.py": ["contextlib", "asyncio", "structlog", "time", ".config", "pyodbc", "dataclasses"], "backend\\app\\core\\database_manager.py": ["contextlib", "asyncio", "structlog", "sqlalchemy"], "backend\\app\\core\\exceptions.py": ["asyncio", "time", "structlog", "enum", "dataclasses", "traceback"], "backend\\app\\core\\optimized_retry.py": ["functools", "asyncio", "time", "random", "logging"], "backend\\app\\core\\__init__.py": [], "backend\\app\\middleware\\access_log.py": ["time", "structlog", "typing", "starlette.middleware.base", "uuid"], "backend\\app\\schemas\\base.py": ["pydantic", "datetime"], "backend\\app\\schemas\\config.py": ["datetime"], "backend\\app\\schemas\\database.py": ["pydantic"], "backend\\app\\schemas\\monitor.py": [".base", "pydantic", "datetime"], "backend\\app\\schemas\\realtime_log.py": ["pydantic", "enum"], "backend\\app\\schemas\\sync.py": ["datetime"], "backend\\app\\schemas\\__init__.py": [], "backend\\app\\services\\advanced_data_transformer.py": ["structlog", "enum", "dataclasses", "re", "json", "datetime"], "backend\\app\\services\\async_task_manager.py": [], "backend\\app\\services\\auto_recovery_manager_enhanced.py": ["subprocess", "logging.handlers", "os", "logging", "datetime", "structlog", "win32event", "signal", "redis", "fcntl", "win32api", "time", "collections", "pathlib", "contextlib", "asyncio", "random", "sys", "psutil"], "backend\\app\\services\\auto_sync_scheduler.py": ["asyncio", "time", "structlog", ".data_write_manager", "sys", "json", ".retry_helper", "os", "pathlib", ".auto_recovery_manager_enhanced", "traceback", "psutil"], "backend\\app\\services\\business_translation_rules.py": ["dataclasses", "re", "structlog"], "backend\\app\\services\\concurrent_processor.py": [], "backend\\app\\services\\config_persistence_service.py": ["shutil", "structlog", "json", "datetime", "pathlib"], "backend\\app\\services\\connection_pool_optimizer.py": [], "backend\\app\\services\\database_connection_pool.py": [], "backend\\app\\services\\database_enhancement.py": ["types", "structlog", "sqlalchemy"], "backend\\app\\services\\database_manager.py": ["structlog", "time", "configparser", "dataclasses", "threading", "os", "pathlib"], "backend\\app\\services\\database_table_manager.py": ["shutil", "contextlib", "asyncio", "time", "structlog", "pathlib", "sqlite3", "dataclasses", "json", "os", "datetime"], "backend\\app\\services\\data_processor.py": [".field_config_service", "re", "structlog", "datetime"], "backend\\app\\services\\data_quality_inspector.py": ["dataclasses", "enum", "re", "structlog"], "backend\\app\\services\\data_write_manager.py": ["asyncio", "time", "structlog", "decimal", "sqlalchemy.ext.asyncio", ".ys_api_client", "pyodbc", "pathlib", ".realtime_log_service", ".sync_status_manager", "re", "json", "datetime", "logging", ".field_config_service", "httpx"], "backend\\app\\services\\enhanced_authenticator.py": ["base64", "structlog", "time", "hmac", "urllib.parse", "<PERSON><PERSON><PERSON>"], "backend\\app\\services\\enhanced_connection_pool.py": [], "backend\\app\\services\\enhanced_error_handler.py": ["asyncio", "time", "structlog", "enum", "dataclasses"], "backend\\app\\services\\enhanced_json_field_matcher.py": ["json", "structlog", "os"], "backend\\app\\services\\enhanced_response_parser.py": [], "backend\\app\\services\\enhanced_response_processor.py": ["time", "structlog", ".response_format_standardizer", ".advanced_data_transformer", "datetime"], "backend\\app\\services\\enhanced_transaction_manager.py": [], "backend\\app\\services\\enhanced_ys_api_client.py": ["asyncio", "structlog", ".ys_api_client"], "backend\\app\\services\\excel_field_matcher.py": ["structlog", "difflib", "dataclasses", "re", "os"], "backend\\app\\services\\excel_field_matcher_pretranslated.py": ["structlog", "difflib", "dataclasses", "re", "json", "os"], "backend\\app\\services\\fast_sync_service.py": ["asyncio", "time", "structlog", ".ys_api_client", ".data_write_manager", "collections", "datetime"], "backend\\app\\services\\field_analysis_service.py": ["re", "structlog"], "backend\\app\\services\\field_config_service.py": ["pathlib", ".enhanced_json_field_matcher", "json", "structlog"], "backend\\app\\services\\field_extractor_service.py": ["time", "structlog", "re", "json", "os", "pathlib"], "backend\\app\\services\\field_validation_service.py": ["dataclasses", "re", "structlog"], "backend\\app\\services\\field_value_mapping_service.py": ["pathlib", "json", "structlog"], "backend\\app\\services\\integrated_ys_api_client.py": [], "backend\\app\\services\\intelligent_field_mapper.py": ["structlog", "os", "re", "json", "datetime"], "backend\\app\\services\\load_balancer.py": [], "backend\\app\\services\\log_service.py": ["datetime", "glob", "structlog", "os"], "backend\\app\\services\\maintenance_manager.py": ["asyncio", "time", "structlog", "schedule", "os"], "backend\\app\\services\\material_master_scheduler.py": ["structlog", "apscheduler.triggers.cron", ".data_write_manager", "json", "apscheduler.schedulers.asyncio", "pathlib"], "backend\\app\\services\\md_parser.py": ["dataclasses", "re", "structlog", "os"], "backend\\app\\services\\monitor_service.py": ["structlog", "psutil"], "backend\\app\\services\\realtime_log_service.py": ["asyncio", "structlog", "collections", "json", "datetime"], "backend\\app\\services\\response_format_standardizer.py": ["structlog", "enum", "re", "json", "datetime"], "backend\\app\\services\\retry_helper.py": ["functools", "asyncio", "time", "structlog", "random"], "backend\\app\\services\\robust_json_parser.py": ["structlog", "dataclasses", "re", "json", "pathlib"], "backend\\app\\services\\standard_request_builder.py": [], "backend\\app\\services\\status_mapping_service.py": ["pathlib", "json", "structlog"], "backend\\app\\services\\sync_status_manager.py": ["asyncio", "time", "structlog", "collections"], "backend\\app\\services\\task_service.py": ["datetime", "glob", "structlog", "os"], "backend\\app\\services\\token_service.py": ["base64", "time", "hmac", "urllib.parse", "<PERSON><PERSON><PERSON>", "requests"], "backend\\app\\services\\transaction_manager.py": [], "backend\\app\\services\\unified_field_manager.py": ["pathlib", "json", "structlog", "datetime"], "backend\\app\\services\\unified_field_service.py": ["asyncio", "structlog", "sys", "json", "datetime", "pathlib"], "backend\\app\\services\\week3_validation_suite.py": [], "backend\\app\\services\\ys_api_client.py": [], "backend\\app\\services\\zero_downtime_implementation.py": ["asyncio", "string", "structlog", "sqlalchemy", "sqlalchemy.ext.asyncio", "random", "backend.app.services.ys_api_client", "pathlib", ".sync_status_manager"], "backend\\app\\services\\__init__.py": [], "backend\\app\\strangler_proxy\\config.py": [], "backend\\app\\week4\\__init__.py": ["dataclasses", "structlog"], "backend\\app\\week4\\concurrent\\async_task_manager.py": ["asyncio", "time", "structlog", "enum", "collections", "uuid"], "backend\\app\\week4\\concurrent\\concurrent_processor.py": ["structlog", "time", "enum", "threading", "datetime"], "backend\\app\\week4\\concurrent\\connection_pool_optimizer.py": ["asyncio", "time", "structlog", "enum", "pymssql", "collections", "sqlite3", "threading"], "backend\\app\\week4\\concurrent\\load_balancer.py": ["asyncio", "time", "structlog", "enum", "statistics", "random", "<PERSON><PERSON><PERSON>", "threading"], "backend\\app\\week4\\concurrent\\__init__.py": [], "backend\\app\\week4\\integration\\week4_adapter.py": ["contextlib", "asyncio", "structlog"], "core\\health_check.py": ["sys", "time", "requests"], "core\\main.py": ["logging", "u<PERSON><PERSON>", "sys", "pathlib"], "core\\app\\main.py": ["fastapi.middleware.cors", "codecs", "fastapi.responses", "fastapi.staticfiles", "pathlib", "sys", "json", "os", "logging", "u<PERSON><PERSON>", "datetime"], "core\\app\\main_original.py": ["contextlib", "fastapi.middleware.cors", "structlog", "fastapi.staticfiles", ".api.v1", "os", "pathlib"], "core\\app\\api\\__init__.py": [], "core\\app\\api\\v1\\auth.py": ["<PERSON><PERSON><PERSON>"], "core\\app\\api\\v1\\config.py": ["structlog", "typing"], "core\\app\\api\\v1\\database.py": ["structlog", "typing", "datetime"], "core\\app\\api\\v1\\database_health.py": ["structlog"], "core\\app\\api\\v1\\enhanced_sync.py": ["asyncio", "time", "structlog", "sqlalchemy.ext.asyncio", "datetime"], "core\\app\\api\\v1\\excel_translation.py": ["batch_pretranslation", "structlog", "tempfile", "fastapi.responses", "sys", "re", "os", "pathlib"], "core\\app\\api\\v1\\field_config_api.py": ["structlog", "json", "datetime", "pathlib", "pydantic"], "core\\app\\api\\v1\\maintenance.py": ["structlog"], "core\\app\\api\\v1\\monitor.py": ["time", "structlog", "sqlalchemy", "configparser", "pyodbc", "json", "os", "datetime"], "core\\app\\api\\v1\\realtime_logs.py": ["asyncio", "structlog", "fastapi.responses", "json", "datetime"], "core\\app\\api\\v1\\sync.py": ["asyncio", "structlog", "sqlalchemy.ext.asyncio", "typing", "datetime", "uuid", "traceback"], "core\\app\\api\\v1\\sync_status.py": ["structlog"], "core\\app\\api\\v1\\tasks.py": ["structlog"], "core\\app\\api\\v1\\unified_field_config.py": ["structlog", "fastapi.responses"], "core\\app\\api\\v1\\__init__.py": [], "core\\app\\core\\code_quality.py": ["dataclasses", "ast", "structlog", "pathlib"], "core\\app\\core\\config.py": ["pathlib", "pydantic_settings", "configparser", "typing"], "core\\app\\core\\database.py": ["sqlalchemy.orm", ".database_manager", "structlog", ".config"], "core\\app\\core\\database_connection_pool.py": ["contextlib", "asyncio", "structlog", "time", ".config", "pyodbc", "dataclasses"], "core\\app\\core\\database_manager.py": ["contextlib", "asyncio", "structlog", "sqlalchemy"], "core\\app\\core\\exceptions.py": ["asyncio", "time", "structlog", "enum", "dataclasses", "traceback"], "core\\app\\core\\optimized_retry.py": ["functools", "asyncio", "time", "random", "logging"], "core\\app\\core\\__init__.py": [], "core\\app\\middleware\\access_log.py": ["time", "structlog", "typing", "starlette.middleware.base", "uuid"], "core\\app\\schemas\\base.py": ["pydantic", "datetime"], "core\\app\\schemas\\config.py": ["datetime"], "core\\app\\schemas\\database.py": ["pydantic"], "core\\app\\schemas\\monitor.py": [".base", "pydantic", "datetime"], "core\\app\\schemas\\realtime_log.py": ["pydantic", "enum"], "core\\app\\schemas\\sync.py": ["datetime"], "core\\app\\schemas\\__init__.py": [], "core\\app\\services\\auto_recovery_manager_enhanced.py": ["subprocess", "logging.handlers", "os", "logging", "datetime", "structlog", "win32event", "signal", "redis", "fcntl", "win32api", "time", "collections", "pathlib", "contextlib", "asyncio", "random", "sys", "psutil"], "core\\app\\services\\auto_sync_scheduler.py": ["asyncio", "time", "structlog", ".data_write_manager", "sys", "json", ".retry_helper", "os", "pathlib", ".auto_recovery_manager_enhanced", "traceback", "psutil"], "core\\app\\services\\business_translation_rules.py": ["dataclasses", "re", "structlog"], "core\\app\\services\\config_persistence_service.py": ["shutil", "structlog", "json", "datetime", "pathlib"], "core\\app\\services\\database_manager.py": ["structlog", "time", "configparser", "dataclasses", "threading", "os", "pathlib"], "core\\app\\services\\database_table_manager.py": ["shutil", "contextlib", "asyncio", "time", "structlog", "pathlib", "sqlite3", "dataclasses", "json", "os", "datetime"], "core\\app\\services\\data_processor.py": [".field_config_service", "re", "structlog", "datetime"], "core\\app\\services\\data_write_manager.py": ["asyncio", "time", "structlog", "decimal", "sqlalchemy.ext.asyncio", ".ys_api_client", "pyodbc", "pathlib", ".realtime_log_service", ".sync_status_manager", "re", "json", "datetime", "logging", ".field_config_service", "httpx"], "core\\app\\services\\enhanced_json_field_matcher.py": ["json", "structlog", "os"], "core\\app\\services\\excel_field_matcher.py": ["structlog", "difflib", "dataclasses", "re", "os"], "core\\app\\services\\excel_field_matcher_pretranslated.py": ["structlog", "difflib", "dataclasses", "re", "json", "os"], "core\\app\\services\\fast_sync_service.py": ["asyncio", "time", "structlog", ".ys_api_client", ".data_write_manager", "collections", "datetime"], "core\\app\\services\\field_analysis_service.py": ["re", "structlog"], "core\\app\\services\\field_config_service.py": ["pathlib", ".enhanced_json_field_matcher", "json", "structlog"], "core\\app\\services\\field_extractor_service.py": ["time", "structlog", "re", "json", "os", "pathlib"], "core\\app\\services\\field_validation_service.py": ["dataclasses", "re", "structlog"], "core\\app\\services\\field_value_mapping_service.py": ["pathlib", "json", "structlog"], "core\\app\\services\\intelligent_field_mapper.py": ["structlog", "os", "re", "json", "datetime"], "core\\app\\services\\log_service.py": ["datetime", "glob", "structlog", "os"], "core\\app\\services\\maintenance_manager.py": ["asyncio", "time", "structlog", "schedule", "os"], "core\\app\\services\\md_parser.py": ["dataclasses", "re", "structlog", "os"], "core\\app\\services\\monitor_service.py": ["structlog", "psutil"], "core\\app\\services\\retry_helper.py": ["functools", "asyncio", "time", "structlog", "random"], "core\\app\\services\\robust_json_parser.py": ["structlog", "dataclasses", "re", "json", "pathlib"], "core\\app\\services\\status_mapping_service.py": ["pathlib", "json", "structlog"], "core\\app\\services\\sync_status_manager.py": ["asyncio", "time", "structlog", "collections"], "core\\app\\services\\task_service.py": ["datetime", "glob", "structlog", "os"], "core\\app\\services\\token_service.py": ["base64", "time", "hmac", "urllib.parse", "<PERSON><PERSON><PERSON>", "requests"], "core\\app\\services\\unified_field_manager.py": ["pathlib", "json", "structlog", "datetime"], "core\\app\\services\\zero_downtime_implementation.py": ["asyncio", "string", "structlog", "sqlalchemy", "sqlalchemy.ext.asyncio", "random", "backend.app.services.ys_api_client", "pathlib", ".sync_status_manager"], "core\\app\\services\\__init__.py": [], "core\\backend\\start_server.py": [], "core\\backend\\app\\__init__.py": [], "core\\backend\\app\\api\\__init__.py": [], "core\\backend\\app\\api\\v1\\__init__.py": [], "core\\backend\\app\\core\\__init__.py": [], "core\\backend\\app\\services\\__init__.py": [], "dev-tools\\cleanup\\code_cleaner.py": ["pathlib", "re", "os"], "dev-tools\\mock\\mock_utils.py": ["pathlib", "json", "os"], "frontend\\field-deduplication-enhancer.js": [], "frontend\\input-validation-enhancer.js": [], "frontend\\start_frontend.py": [], "frontend\\start_frontend_clean.py": ["webbrowser", "time", "socketserver", "port_manager", "sys", "pathlib", "http.server"], "frontend\\start_frontend_fixed.py": ["port_manager"], "frontend\\unified-field-config-fix.js": [], "frontend\\js\\api-config-fix.js": [], "frontend\\js\\api-config.js": [], "frontend\\js\\api-unified.js": [], "frontend\\js\\baseline-save.js": [], "frontend\\js\\element-plus-icons.iife.min.js": [], "frontend\\js\\element-plus.js": ["util", "vue"], "frontend\\js\\field-list-display.js": [], "frontend\\js\\field-statistics.js": [], "frontend\\js\\notification-system.js": [], "frontend\\js\\performance-optimizer.js": [], "frontend\\js\\realtime-log.js": [], "frontend\\js\\unified-components.js": [], "frontend\\js\\user-config-save.js": [], "frontend\\js\\vue.global.js": [], "frontend\\js\\common\\api-client.js": [], "frontend\\js\\common\\elk-integration-config.js": [], "frontend\\js\\common\\error-handler.js": [], "frontend\\js\\common\\field-renderer.js": [], "frontend\\js\\common\\field-utils.js": [], "frontend\\js\\common\\smart-logger.js": [], "frontend\\js\\common\\smart-retry-optimizer.js": [], "frontend\\js\\common\\standard-error-logger.js": [], "frontend\\js\\common\\test-data.js": [], "frontend\\js\\common\\user-error-notifier.js": [], "frontend\\js\\common\\validation-utils.js": [], "frontend\\js\\core\\app-bootstrap.js": [], "frontend\\js\\core\\component-manager.js": [], "frontend\\js\\core\\component-migration-tool.js": [], "frontend\\js\\core\\initialization-manager.js": [], "frontend\\js\\core\\page-migration-assistant.js": [], "migration\\week1_analysis\\tests\\test_batch_processor.py": [], "migration\\week1_analysis\\tests\\test_data_writer_core.py": [], "migration\\week2_analysis\\refactored\\conflict_resolver.py": [], "migration\\week2_analysis\\refactored\\performance_monitor.py": [], "migration\\week2_analysis\\refactored\\refactored_fast_sync_service.py": [], "migration\\week2_analysis\\refactored\\sync_scheduler.py": [], "migration\\week3_analysis\\refactored\\data_transformer.py": [], "migration\\week3_analysis\\refactored\\endpoint_manager.py": [], "migration\\week3_analysis\\refactored\\request_builder.py": [], "migration\\week3_analysis\\refactored\\response_parser.py": [], "month2_config\\config_rollback\\manager.py": ["time", "difflib", "json", "<PERSON><PERSON><PERSON>", "pathlib"], "month2_config\\two_step_save\\manager.py": ["pathlib", "json", "<PERSON><PERSON><PERSON>", "time"], "month2_database\\table_creation\\manager.py": ["sqlite3", "re", "xml.etree.ElementTree", "pathlib"], "new-system\\backup\\modules\\物料档案批量详情查询\\__init__.py": [], "new-system\\backup\\modules\\现存量报表查询\\api.py": [], "new-system\\backup\\modules\\现存量报表查询\\__init__.py": [], "new-system\\modules\\purchase_order\\models.py": ["sqlalchemy.ext.declarative", "datetime"], "new-system\\modules\\purchase_order\\routes.py": [".service", "datetime"], "new-system\\modules\\purchase_order\\schema.py": ["datetime"], "new-system\\modules\\purchase_order\\service.py": ["sqlalchemy.orm", "database", "datetime"], "new-system\\modules\\purchase_order\\__init__.py": [], "new-system\\modules\\业务日志\\api.py": [], "new-system\\modules\\业务日志\\__init__.py": [], "new-system\\modules\\产品入库单列表查询\\api.py": ["<PERSON><PERSON><PERSON>"], "new-system\\modules\\产品入库单列表查询\\models.py": ["pydantic"], "new-system\\modules\\产品入库单列表查询\\service.py": [], "new-system\\modules\\产品入库单列表查询\\__init__.py": [], "new-system\\modules\\委外入库列表查询\\__init__.py": [], "new-system\\modules\\委外申请列表查询\\__init__.py": [], "new-system\\modules\\委外订单列表\\api.py": [], "new-system\\modules\\委外订单列表\\models.py": [], "new-system\\modules\\委外订单列表\\__init__.py": [], "new-system\\modules\\物料档案批量详情查询\\__init__.py": [], "new-system\\modules\\生产订单列表查询\\api.py": [], "new-system\\modules\\生产订单列表查询\\models.py": [], "new-system\\modules\\生产订单列表查询\\__init__.py": [], "new-system\\modules\\请购单列表查询\\api.py": [], "new-system\\modules\\请购单列表查询\\models.py": [], "new-system\\modules\\请购单列表查询\\__init__.py": [], "new-system\\modules\\销售出库列表查询\\api.py": [], "new-system\\modules\\销售出库列表查询\\__init__.py": [], "new-system\\modules\\销售订单\\api.py": [], "new-system\\modules\\销售订单\\__init__.py": [], "new-system\\modules\\需求计划\\api.py": [], "new-system\\modules\\需求计划\\__init__.py": [], "scripts\\add_api_config.py": ["logging", "glob", "os"], "scripts\\auto_migration.py": ["shutil", "<PERSON><PERSON><PERSON><PERSON>", "re", "json", "datetime", "pathlib"], "scripts\\auto_migration_pipeline.py": ["asyncio", "<PERSON><PERSON><PERSON><PERSON>", "sys", "json", "datetime", "pathlib"], "scripts\\batch_initialize_next.py": ["pathlib", "json", "datetime"], "scripts\\batch_init_modules.py": ["pathlib", "subprocess", "json", "datetime"], "scripts\\cleanup_test_code.py": ["os", "sys", "re", "json", "datetime", "pathlib"], "scripts\\clean_debug_code.py": ["logging", "re", "pathlib"], "scripts\\clean_hardcoded_data.py": ["pathlib", "re"], "scripts\\complete_modules.py": ["subprocess", "sys"], "scripts\\database_dual_writer.py": ["redis", "asyncio", "time", "psycopg2.pool", "psycopg2.extras", "psycopg2", "pathlib", "sqlite3", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "json", "logging"], "scripts\\database_dual_writer_simple.py": ["sqlite3", "<PERSON><PERSON><PERSON><PERSON>", "sys", "json", "datetime", "pathlib"], "scripts\\diagnose_migration.py": [], "scripts\\final_status_check.py": ["pathlib", "sys", "json", "datetime"], "scripts\\fix_css_paths.py": [], "scripts\\fix_migrated_paths.py": [], "scripts\\graveyard_safety_analyzer.py": ["pathlib", "json"], "scripts\\health_check.py": ["subprocess", "sys", "json", "datetime", "pathlib"], "scripts\\manual_cleanup.py": ["pathlib", "shutil"], "scripts\\migrate_purchase_order.py": ["pytest", "sqlalchemy.orm", "time", ".service", "database", "pathlib", "new_system.modules.purchase_order.service", "sys", "json", "fastapi.testclient", "os", "datetime", "sqlalchemy.ext.declarative"], "scripts\\migrate_产品入库单列表查询.py": ["<PERSON><PERSON><PERSON>", "json", "datetime", "pathlib", "pydantic"], "scripts\\migrate_产品入库单列表查询_fixed.py": ["<PERSON><PERSON><PERSON>", "json", "datetime", "pathlib", "pydantic"], "scripts\\migrate_生产订单列表查询.py": ["<PERSON><PERSON><PERSON>", "json", "datetime", "pathlib", "pydantic"], "scripts\\migrate_请购单列表查询.py": ["<PERSON><PERSON><PERSON>", "json", "datetime", "pathlib", "pydantic"], "scripts\\migrate_采购入库单列表.py": ["datetime", "pathlib", "json", "os"], "scripts\\module_functional_test.py": [], "scripts\\module_tracker.py": ["<PERSON><PERSON><PERSON><PERSON>", "sys", "json", "datetime", "pathlib"], "scripts\\module_tracker_simple.py": ["<PERSON><PERSON><PERSON><PERSON>", "sys", "json", "datetime", "pathlib"], "scripts\\next_module.py": ["pathlib", "subprocess", "json", "sys"], "scripts\\phase3_verification.py": [], "scripts\\port_manager.py": ["subprocess", "n", "self.logger", "levelname", "os", "logging", "logging.FileHandler", "asctime", "nfrom", "Path", "logging.getLogger", "self.log_file", "s", "handlers", "def", "time", "message", "import", "pathlib", "socket", "nimport", "self", "logging.StreamHandler", "sys", "format", "is_port_in_use", "encoding", "__name__"], "scripts\\port_manager_clean.py": ["subprocess", "time", "socket", "pathlib", "os", "logging"], "scripts\\quick_batch_migrate.py": ["subprocess", "os", "sys", "json", "datetime", "pathlib"], "scripts\\quick_migrate.py": ["datetime", "subprocess", "sys"], "scripts\\reliable_server.py": [], "scripts\\rollback_batch_writes.py": ["pyodbc", "pathlib", "<PERSON><PERSON><PERSON><PERSON>", "sys", "re", "json", "app.core.config", "logging", "traceback"], "scripts\\simple_migrator.py": ["asyncio", "<PERSON><PERSON><PERSON><PERSON>", "sys", "json", "datetime", "pathlib"], "scripts\\test_elk_connection.py": [], "scripts\\test_server.py": [], "scripts\\ultra_simple_migrate.py": ["subprocess", "sys", "json", "datetime", "pathlib"], "scripts\\validate_deployment.py": [], "scripts\\verify_fixes.py": [], "tests\\locust_stress_test.py": [], "tests\\simple_test.py": [], "tests\\test_baseline_api.py": [], "tests\\test_md_to_json_converter.py": ["pytest", "tempfile", "tools.md_to_json_converter", "sys", "os", "pathlib", "unittest.mock"], "tests\\test_rollback_scripts.py": ["pytest", "scripts.rollback_batch_writes", "pyodbc", "sys", "datetime", "pathlib"], "tests\\test_week2_validation.py": [], "tests\\week2_completion_report.py": [], "tests\\module_migration\\test_generator.py": [], "tools\\error_handling_load_test.py": [], "tools\\md_to_json_converter.py": ["typing", "sys", "re", "json", "os", "logging", "app.core.config"]}, "file_stats": {"ai_code_review_system.py": {"type": "python", "imports": 0, "size": 0}, "analyze_dependencies.py": {"type": "python", "imports": 4, "size": 9455}, "analyze_project_stats.py": {"type": "python", "imports": 2, "size": 2215}, "automation_validator.py": {"type": "python", "imports": 0, "size": 0}, "auto_fix_comprehensive_issues.py": {"type": "python", "imports": 7, "size": 17343}, "auto_project_cleanup.py": {"type": "python", "imports": 5, "size": 10003}, "batch_code_quality_fix.py": {"type": "python", "imports": 5, "size": 10165}, "batch_flake8_fix.py": {"type": "python", "imports": 2, "size": 4858}, "batch_validate_modules.py": {"type": "python", "imports": 4, "size": 7261}, "build_production_package.py": {"type": "python", "imports": 8, "size": 15924}, "check_code_quality.py": {"type": "python", "imports": 2, "size": 3905}, "check_naming_conflicts.py": {"type": "python", "imports": 4, "size": 4087}, "check_sonarqube_status.py": {"type": "python", "imports": 1, "size": 2686}, "cicd_builder_simple.py": {"type": "python", "imports": 0, "size": 0}, "cicd_pipeline_builder.py": {"type": "python", "imports": 7, "size": 24930}, "cicd_pipeline_builder_optimized.py": {"type": "python", "imports": 7, "size": 24789}, "comprehensive_code_fixer.py": {"type": "python", "imports": 0, "size": 0}, "comprehensive_production_test.py": {"type": "python", "imports": 0, "size": 0}, "config_environmentizer.py": {"type": "python", "imports": 4, "size": 3709}, "config_environmentizer_clean.py": {"type": "python", "imports": 4, "size": 3499}, "core_mvp_extractor.py": {"type": "python", "imports": 0, "size": 0}, "database_enhancement_demo.py": {"type": "python", "imports": 0, "size": 0}, "day3_final_test.py": {"type": "python", "imports": 0, "size": 0}, "day4_proxy_builder.py": {"type": "python", "imports": 0, "size": 0}, "day5_final_validator.py": {"type": "python", "imports": 0, "size": 0}, "execute_task_checklist.py": {"type": "python", "imports": 6, "size": 23590}, "file_sentinel.py": {"type": "python", "imports": 6, "size": 6723}, "final_batch.py": {"type": "python", "imports": 3, "size": 1770}, "final_project_fixer.py": {"type": "python", "imports": 0, "size": 0}, "final_verification.py": {"type": "python", "imports": 2, "size": 4670}, "fix_all_flake8.py": {"type": "python", "imports": 5, "size": 11467}, "fix_build_script.py": {"type": "python", "imports": 3, "size": 10071}, "fix_execute_task_script.py": {"type": "python", "imports": 3, "size": 6922}, "fix_issues.py": {"type": "python", "imports": 5, "size": 2548}, "fix_sonarqube_issues.py": {"type": "python", "imports": 4, "size": 9682}, "fix_task_issues.py": {"type": "python", "imports": 4, "size": 15554}, "fix_xml_files.py": {"type": "python", "imports": 4, "size": 5436}, "flake8_stats.py": {"type": "python", "imports": 3, "size": 2604}, "function_duplicate_checker.py": {"type": "python", "imports": 4, "size": 3095}, "generate_stats.py": {"type": "python", "imports": 3, "size": 7554}, "install_windows_service.py": {"type": "python", "imports": 10, "size": 5433}, "machine_cleanup.py": {"type": "python", "imports": 5, "size": 11423}, "mvp_launcher.py": {"type": "python", "imports": 0, "size": 0}, "pollution_guard.py": {"type": "python", "imports": 5, "size": 2023}, "port_locker.py": {"type": "python", "imports": 7, "size": 7634}, "production_readiness_report.py": {"type": "python", "imports": 7, "size": 23651}, "production_test_runner.py": {"type": "python", "imports": 0, "size": 0}, "project_health_check.py": {"type": "python", "imports": 9, "size": 22084}, "project_health_checker.py": {"type": "python", "imports": 0, "size": 0}, "proxy_strangler.py": {"type": "python", "imports": 0, "size": 0}, "python_version_diagnostic.py": {"type": "python", "imports": 0, "size": 0}, "quick_health_check.py": {"type": "python", "imports": 8, "size": 9845}, "quick_health_check_fixed.py": {"type": "python", "imports": 4, "size": 1938}, "quick_status_check.py": {"type": "python", "imports": 1, "size": 1598}, "quick_syntax_fix.py": {"type": "python", "imports": 3, "size": 5633}, "quick_test.py": {"type": "python", "imports": 0, "size": 0}, "real_data_full_test.py": {"type": "python", "imports": 0, "size": 0}, "refactor_data_write_manager.py": {"type": "python", "imports": 0, "size": 0}, "refactor_fast_sync_service.py": {"type": "python", "imports": 0, "size": 0}, "refactor_ys_api_client.py": {"type": "python", "imports": 0, "size": 0}, "remaining_issues_fixer.py": {"type": "python", "imports": 4, "size": 14784}, "run_comprehensive_check.py": {"type": "python", "imports": 6, "size": 16735}, "setup_ide_security_plugins.py": {"type": "python", "imports": 0, "size": 0}, "setup_test_env.py": {"type": "python", "imports": 0, "size": 0}, "shit_mountain_mapper.py": {"type": "python", "imports": 0, "size": 0}, "smart_duplicate_cleaner.py": {"type": "python", "imports": 4, "size": 6988}, "smart_file_creator.py": {"type": "python", "imports": 4, "size": 6937}, "sonarqube_cleanup.py": {"type": "python", "imports": 4, "size": 10726}, "start_month1_validation.py": {"type": "python", "imports": 3, "size": 6820}, "start_month2.py": {"type": "python", "imports": 2, "size": 7332}, "start_quick_test.py": {"type": "python", "imports": 0, "size": 0}, "status_check.py": {"type": "python", "imports": 0, "size": 0}, "systematic_duplicate_detector.py": {"type": "python", "imports": 6, "size": 12078}, "systematic_migration_starter.py": {"type": "python", "imports": 0, "size": 0}, "system_integration_final.py": {"type": "python", "imports": 0, "size": 0}, "test_anti_duplicate_system.py": {"type": "python", "imports": 0, "size": 0}, "test_backend.py": {"type": "python", "imports": 0, "size": 0}, "test_day3_core.py": {"type": "python", "imports": 0, "size": 0}, "test_day3_offline.py": {"type": "python", "imports": 0, "size": 0}, "test_day4_proxy.py": {"type": "python", "imports": 0, "size": 0}, "test_frontend.py": {"type": "python", "imports": 0, "size": 0}, "test_imports.py": {"type": "python", "imports": 0, "size": 0}, "test_modules_direct.py": {"type": "python", "imports": 0, "size": 0}, "test_mvp.py": {"type": "python", "imports": 0, "size": 0}, "test_proxy.py": {"type": "python", "imports": 0, "size": 0}, "three_step_fix.py": {"type": "python", "imports": 2, "size": 4736}, "universal_code_quality_fixer.py": {"type": "python", "imports": 0, "size": 0}, "unused_import_checker.py": {"type": "python", "imports": 2, "size": 3456}, "verify_enhancements.py": {"type": "python", "imports": 0, "size": 0}, "verify_field_fetching.py": {"type": "python", "imports": 0, "size": 0}, "verify_fixes.py": {"type": "python", "imports": 4, "size": 2565}, "verify_module.py": {"type": "python", "imports": 5, "size": 8711}, "verify_startup.py": {"type": "python", "imports": 2, "size": 2132}, "verify_startup_clean.py": {"type": "python", "imports": 3, "size": 2143}, "violent_cleanup.py": {"type": "python", "imports": 10, "size": 13285}, "week1_completion_report.py": {"type": "python", "imports": 0, "size": 0}, "week1_completion_week2_planning.py": {"type": "python", "imports": 0, "size": 0}, "week1_quick_start.py": {"type": "python", "imports": 0, "size": 0}, "week1_week2_integration_demo.py": {"type": "python", "imports": 0, "size": 0}, "week2_completion_report.py": {"type": "python", "imports": 0, "size": 0}, "week3_integration_final.py": {"type": "python", "imports": 0, "size": 0}, "backend\\start_server.py": {"type": "python", "imports": 4, "size": 1588}, "backend\\start_server_clean.py": {"type": "python", "imports": 5, "size": 2202}, "backend\\start_server_fixed.py": {"type": "python", "imports": 22, "size": 16108}, "backend\\start_simple.py": {"type": "python", "imports": 8, "size": 9937}, "backend\\test_backend_automation.py": {"type": "python", "imports": 0, "size": 0}, "backend\\__init__.py": {"type": "python", "imports": 0, "size": 25}, "backend\\app\\main.py": {"type": "python", "imports": 10, "size": 16916}, "backend\\app\\main_original.py": {"type": "python", "imports": 7, "size": 16768}, "backend\\app\\api\\__init__.py": {"type": "python", "imports": 0, "size": 68}, "backend\\app\\api\\v1\\auth.py": {"type": "python", "imports": 1, "size": 383}, "backend\\app\\api\\v1\\config.py": {"type": "python", "imports": 3, "size": 28532}, "backend\\app\\api\\v1\\database.py": {"type": "python", "imports": 4, "size": 10761}, "backend\\app\\api\\v1\\database_health.py": {"type": "python", "imports": 1, "size": 2339}, "backend\\app\\api\\v1\\enhanced_sync.py": {"type": "python", "imports": 5, "size": 9800}, "backend\\app\\api\\v1\\excel_translation.py": {"type": "python", "imports": 8, "size": 12061}, "backend\\app\\api\\v1\\field_config_api.py": {"type": "python", "imports": 5, "size": 20040}, "backend\\app\\api\\v1\\maintenance.py": {"type": "python", "imports": 1, "size": 3358}, "backend\\app\\api\\v1\\monitor.py": {"type": "python", "imports": 8, "size": 36733}, "backend\\app\\api\\v1\\realtime_logs.py": {"type": "python", "imports": 5, "size": 3852}, "backend\\app\\api\\v1\\sync.py": {"type": "python", "imports": 7, "size": 53875}, "backend\\app\\api\\v1\\sync_status.py": {"type": "python", "imports": 1, "size": 3041}, "backend\\app\\api\\v1\\tasks.py": {"type": "python", "imports": 1, "size": 16403}, "backend\\app\\api\\v1\\unified_field_config.py": {"type": "python", "imports": 2, "size": 20122}, "backend\\app\\api\\v1\\__init__.py": {"type": "python", "imports": 0, "size": 499}, "backend\\app\\core\\code_quality.py": {"type": "python", "imports": 4, "size": 23970}, "backend\\app\\core\\config.py": {"type": "python", "imports": 5, "size": 10057}, "backend\\app\\core\\database.py": {"type": "python", "imports": 4, "size": 5572}, "backend\\app\\core\\database_connection_pool.py": {"type": "python", "imports": 7, "size": 17975}, "backend\\app\\core\\database_manager.py": {"type": "python", "imports": 4, "size": 5647}, "backend\\app\\core\\exceptions.py": {"type": "python", "imports": 6, "size": 25706}, "backend\\app\\core\\optimized_retry.py": {"type": "python", "imports": 5, "size": 11296}, "backend\\app\\core\\__init__.py": {"type": "python", "imports": 0, "size": 25}, "backend\\app\\middleware\\access_log.py": {"type": "python", "imports": 5, "size": 1712}, "backend\\app\\schemas\\base.py": {"type": "python", "imports": 2, "size": 2821}, "backend\\app\\schemas\\config.py": {"type": "python", "imports": 1, "size": 8230}, "backend\\app\\schemas\\database.py": {"type": "python", "imports": 1, "size": 1489}, "backend\\app\\schemas\\monitor.py": {"type": "python", "imports": 3, "size": 2627}, "backend\\app\\schemas\\realtime_log.py": {"type": "python", "imports": 2, "size": 1788}, "backend\\app\\schemas\\sync.py": {"type": "python", "imports": 1, "size": 5173}, "backend\\app\\schemas\\__init__.py": {"type": "python", "imports": 0, "size": 96}, "backend\\app\\services\\advanced_data_transformer.py": {"type": "python", "imports": 6, "size": 29492}, "backend\\app\\services\\async_task_manager.py": {"type": "python", "imports": 0, "size": 0}, "backend\\app\\services\\auto_recovery_manager_enhanced.py": {"type": "python", "imports": 19, "size": 31212}, "backend\\app\\services\\auto_sync_scheduler.py": {"type": "python", "imports": 12, "size": 27019}, "backend\\app\\services\\business_translation_rules.py": {"type": "python", "imports": 3, "size": 15081}, "backend\\app\\services\\concurrent_processor.py": {"type": "python", "imports": 0, "size": 0}, "backend\\app\\services\\config_persistence_service.py": {"type": "python", "imports": 5, "size": 12580}, "backend\\app\\services\\connection_pool_optimizer.py": {"type": "python", "imports": 0, "size": 0}, "backend\\app\\services\\database_connection_pool.py": {"type": "python", "imports": 0, "size": 0}, "backend\\app\\services\\database_enhancement.py": {"type": "python", "imports": 3, "size": 12919}, "backend\\app\\services\\database_manager.py": {"type": "python", "imports": 7, "size": 31620}, "backend\\app\\services\\database_table_manager.py": {"type": "python", "imports": 11, "size": 50935}, "backend\\app\\services\\data_processor.py": {"type": "python", "imports": 4, "size": 18236}, "backend\\app\\services\\data_quality_inspector.py": {"type": "python", "imports": 4, "size": 35278}, "backend\\app\\services\\data_write_manager.py": {"type": "python", "imports": 16, "size": 86828}, "backend\\app\\services\\enhanced_authenticator.py": {"type": "python", "imports": 6, "size": 12163}, "backend\\app\\services\\enhanced_connection_pool.py": {"type": "python", "imports": 0, "size": 0}, "backend\\app\\services\\enhanced_error_handler.py": {"type": "python", "imports": 5, "size": 22471}, "backend\\app\\services\\enhanced_json_field_matcher.py": {"type": "python", "imports": 3, "size": 9273}, "backend\\app\\services\\enhanced_response_parser.py": {"type": "python", "imports": 0, "size": 0}, "backend\\app\\services\\enhanced_response_processor.py": {"type": "python", "imports": 5, "size": 21864}, "backend\\app\\services\\enhanced_transaction_manager.py": {"type": "python", "imports": 0, "size": 0}, "backend\\app\\services\\enhanced_ys_api_client.py": {"type": "python", "imports": 3, "size": 8002}, "backend\\app\\services\\excel_field_matcher.py": {"type": "python", "imports": 5, "size": 20130}, "backend\\app\\services\\excel_field_matcher_pretranslated.py": {"type": "python", "imports": 6, "size": 9281}, "backend\\app\\services\\fast_sync_service.py": {"type": "python", "imports": 7, "size": 75885}, "backend\\app\\services\\field_analysis_service.py": {"type": "python", "imports": 2, "size": 17755}, "backend\\app\\services\\field_config_service.py": {"type": "python", "imports": 4, "size": 14645}, "backend\\app\\services\\field_extractor_service.py": {"type": "python", "imports": 6, "size": 22380}, "backend\\app\\services\\field_validation_service.py": {"type": "python", "imports": 3, "size": 8569}, "backend\\app\\services\\field_value_mapping_service.py": {"type": "python", "imports": 3, "size": 32259}, "backend\\app\\services\\integrated_ys_api_client.py": {"type": "python", "imports": 0, "size": 0}, "backend\\app\\services\\intelligent_field_mapper.py": {"type": "python", "imports": 5, "size": 23203}, "backend\\app\\services\\load_balancer.py": {"type": "python", "imports": 0, "size": 0}, "backend\\app\\services\\log_service.py": {"type": "python", "imports": 4, "size": 1892}, "backend\\app\\services\\maintenance_manager.py": {"type": "python", "imports": 5, "size": 12184}, "backend\\app\\services\\material_master_scheduler.py": {"type": "python", "imports": 6, "size": 19450}, "backend\\app\\services\\md_parser.py": {"type": "python", "imports": 4, "size": 26830}, "backend\\app\\services\\monitor_service.py": {"type": "python", "imports": 2, "size": 1659}, "backend\\app\\services\\realtime_log_service.py": {"type": "python", "imports": 5, "size": 6485}, "backend\\app\\services\\response_format_standardizer.py": {"type": "python", "imports": 5, "size": 19149}, "backend\\app\\services\\retry_helper.py": {"type": "python", "imports": 5, "size": 7605}, "backend\\app\\services\\robust_json_parser.py": {"type": "python", "imports": 5, "size": 22746}, "backend\\app\\services\\standard_request_builder.py": {"type": "python", "imports": 0, "size": 0}, "backend\\app\\services\\status_mapping_service.py": {"type": "python", "imports": 3, "size": 14032}, "backend\\app\\services\\sync_status_manager.py": {"type": "python", "imports": 4, "size": 8511}, "backend\\app\\services\\task_service.py": {"type": "python", "imports": 4, "size": 3270}, "backend\\app\\services\\token_service.py": {"type": "python", "imports": 6, "size": 1840}, "backend\\app\\services\\transaction_manager.py": {"type": "python", "imports": 0, "size": 0}, "backend\\app\\services\\unified_field_manager.py": {"type": "python", "imports": 4, "size": 37277}, "backend\\app\\services\\unified_field_service.py": {"type": "python", "imports": 6, "size": 19557}, "backend\\app\\services\\week3_validation_suite.py": {"type": "python", "imports": 0, "size": 0}, "backend\\app\\services\\ys_api_client.py": {"type": "python", "imports": 0, "size": 0}, "backend\\app\\services\\zero_downtime_implementation.py": {"type": "python", "imports": 9, "size": 35340}, "backend\\app\\services\\__init__.py": {"type": "python", "imports": 0, "size": 183}, "backend\\app\\strangler_proxy\\config.py": {"type": "python", "imports": 0, "size": 892}, "backend\\app\\week4\\__init__.py": {"type": "python", "imports": 2, "size": 2162}, "backend\\app\\week4\\concurrent\\async_task_manager.py": {"type": "python", "imports": 6, "size": 23944}, "backend\\app\\week4\\concurrent\\concurrent_processor.py": {"type": "python", "imports": 5, "size": 19376}, "backend\\app\\week4\\concurrent\\connection_pool_optimizer.py": {"type": "python", "imports": 8, "size": 34214}, "backend\\app\\week4\\concurrent\\load_balancer.py": {"type": "python", "imports": 8, "size": 30231}, "backend\\app\\week4\\concurrent\\__init__.py": {"type": "python", "imports": 0, "size": 398}, "backend\\app\\week4\\integration\\week4_adapter.py": {"type": "python", "imports": 3, "size": 10886}, "core\\health_check.py": {"type": "python", "imports": 3, "size": 3775}, "core\\main.py": {"type": "python", "imports": 4, "size": 1320}, "core\\app\\main.py": {"type": "python", "imports": 11, "size": 16702}, "core\\app\\main_original.py": {"type": "python", "imports": 7, "size": 16768}, "core\\app\\api\\__init__.py": {"type": "python", "imports": 0, "size": 68}, "core\\app\\api\\v1\\auth.py": {"type": "python", "imports": 1, "size": 383}, "core\\app\\api\\v1\\config.py": {"type": "python", "imports": 2, "size": 28477}, "core\\app\\api\\v1\\database.py": {"type": "python", "imports": 3, "size": 10706}, "core\\app\\api\\v1\\database_health.py": {"type": "python", "imports": 1, "size": 2339}, "core\\app\\api\\v1\\enhanced_sync.py": {"type": "python", "imports": 5, "size": 9800}, "core\\app\\api\\v1\\excel_translation.py": {"type": "python", "imports": 8, "size": 12061}, "core\\app\\api\\v1\\field_config_api.py": {"type": "python", "imports": 5, "size": 20040}, "core\\app\\api\\v1\\maintenance.py": {"type": "python", "imports": 1, "size": 3358}, "core\\app\\api\\v1\\monitor.py": {"type": "python", "imports": 8, "size": 36733}, "core\\app\\api\\v1\\realtime_logs.py": {"type": "python", "imports": 5, "size": 3852}, "core\\app\\api\\v1\\sync.py": {"type": "python", "imports": 7, "size": 53875}, "core\\app\\api\\v1\\sync_status.py": {"type": "python", "imports": 1, "size": 3041}, "core\\app\\api\\v1\\tasks.py": {"type": "python", "imports": 1, "size": 16403}, "core\\app\\api\\v1\\unified_field_config.py": {"type": "python", "imports": 2, "size": 20122}, "core\\app\\api\\v1\\__init__.py": {"type": "python", "imports": 0, "size": 499}, "core\\app\\core\\code_quality.py": {"type": "python", "imports": 4, "size": 23970}, "core\\app\\core\\config.py": {"type": "python", "imports": 4, "size": 10593}, "core\\app\\core\\database.py": {"type": "python", "imports": 4, "size": 5572}, "core\\app\\core\\database_connection_pool.py": {"type": "python", "imports": 7, "size": 17975}, "core\\app\\core\\database_manager.py": {"type": "python", "imports": 4, "size": 5647}, "core\\app\\core\\exceptions.py": {"type": "python", "imports": 6, "size": 25706}, "core\\app\\core\\optimized_retry.py": {"type": "python", "imports": 5, "size": 11296}, "core\\app\\core\\__init__.py": {"type": "python", "imports": 0, "size": 25}, "core\\app\\middleware\\access_log.py": {"type": "python", "imports": 5, "size": 1712}, "core\\app\\schemas\\base.py": {"type": "python", "imports": 2, "size": 2821}, "core\\app\\schemas\\config.py": {"type": "python", "imports": 1, "size": 8230}, "core\\app\\schemas\\database.py": {"type": "python", "imports": 1, "size": 1489}, "core\\app\\schemas\\monitor.py": {"type": "python", "imports": 3, "size": 2627}, "core\\app\\schemas\\realtime_log.py": {"type": "python", "imports": 2, "size": 1788}, "core\\app\\schemas\\sync.py": {"type": "python", "imports": 1, "size": 5173}, "core\\app\\schemas\\__init__.py": {"type": "python", "imports": 0, "size": 96}, "core\\app\\services\\auto_recovery_manager_enhanced.py": {"type": "python", "imports": 19, "size": 31212}, "core\\app\\services\\auto_sync_scheduler.py": {"type": "python", "imports": 12, "size": 27019}, "core\\app\\services\\business_translation_rules.py": {"type": "python", "imports": 3, "size": 15081}, "core\\app\\services\\config_persistence_service.py": {"type": "python", "imports": 5, "size": 12580}, "core\\app\\services\\database_manager.py": {"type": "python", "imports": 7, "size": 31620}, "core\\app\\services\\database_table_manager.py": {"type": "python", "imports": 11, "size": 50935}, "core\\app\\services\\data_processor.py": {"type": "python", "imports": 4, "size": 18236}, "core\\app\\services\\data_write_manager.py": {"type": "python", "imports": 16, "size": 86828}, "core\\app\\services\\enhanced_json_field_matcher.py": {"type": "python", "imports": 3, "size": 9109}, "core\\app\\services\\excel_field_matcher.py": {"type": "python", "imports": 5, "size": 20130}, "core\\app\\services\\excel_field_matcher_pretranslated.py": {"type": "python", "imports": 6, "size": 9281}, "core\\app\\services\\fast_sync_service.py": {"type": "python", "imports": 7, "size": 75885}, "core\\app\\services\\field_analysis_service.py": {"type": "python", "imports": 2, "size": 17755}, "core\\app\\services\\field_config_service.py": {"type": "python", "imports": 4, "size": 14645}, "core\\app\\services\\field_extractor_service.py": {"type": "python", "imports": 6, "size": 22380}, "core\\app\\services\\field_validation_service.py": {"type": "python", "imports": 3, "size": 8569}, "core\\app\\services\\field_value_mapping_service.py": {"type": "python", "imports": 3, "size": 32259}, "core\\app\\services\\intelligent_field_mapper.py": {"type": "python", "imports": 5, "size": 23203}, "core\\app\\services\\log_service.py": {"type": "python", "imports": 4, "size": 1892}, "core\\app\\services\\maintenance_manager.py": {"type": "python", "imports": 5, "size": 12184}, "core\\app\\services\\md_parser.py": {"type": "python", "imports": 4, "size": 26584}, "core\\app\\services\\monitor_service.py": {"type": "python", "imports": 2, "size": 1659}, "core\\app\\services\\retry_helper.py": {"type": "python", "imports": 5, "size": 7605}, "core\\app\\services\\robust_json_parser.py": {"type": "python", "imports": 5, "size": 22622}, "core\\app\\services\\status_mapping_service.py": {"type": "python", "imports": 3, "size": 14032}, "core\\app\\services\\sync_status_manager.py": {"type": "python", "imports": 4, "size": 8511}, "core\\app\\services\\task_service.py": {"type": "python", "imports": 4, "size": 3270}, "core\\app\\services\\token_service.py": {"type": "python", "imports": 6, "size": 1840}, "core\\app\\services\\unified_field_manager.py": {"type": "python", "imports": 4, "size": 37277}, "core\\app\\services\\zero_downtime_implementation.py": {"type": "python", "imports": 9, "size": 35340}, "core\\app\\services\\__init__.py": {"type": "python", "imports": 0, "size": 183}, "core\\backend\\start_server.py": {"type": "python", "imports": 0, "size": 0}, "core\\backend\\app\\__init__.py": {"type": "python", "imports": 0, "size": 0}, "core\\backend\\app\\api\\__init__.py": {"type": "python", "imports": 0, "size": 0}, "core\\backend\\app\\api\\v1\\__init__.py": {"type": "python", "imports": 0, "size": 0}, "core\\backend\\app\\core\\__init__.py": {"type": "python", "imports": 0, "size": 0}, "core\\backend\\app\\services\\__init__.py": {"type": "python", "imports": 0, "size": 0}, "dev-tools\\cleanup\\code_cleaner.py": {"type": "python", "imports": 3, "size": 10906}, "dev-tools\\mock\\mock_utils.py": {"type": "python", "imports": 3, "size": 9269}, "frontend\\field-deduplication-enhancer.js": {"type": "javascript", "imports": 0, "size": 21588}, "frontend\\input-validation-enhancer.js": {"type": "javascript", "imports": 0, "size": 8698}, "frontend\\start_frontend.py": {"type": "python", "imports": 0, "size": 0}, "frontend\\start_frontend_clean.py": {"type": "python", "imports": 7, "size": 4076}, "frontend\\start_frontend_fixed.py": {"type": "python", "imports": 1, "size": 4326}, "frontend\\unified-field-config-fix.js": {"type": "javascript", "imports": 0, "size": 0}, "frontend\\js\\api-config-fix.js": {"type": "javascript", "imports": 0, "size": 2936}, "frontend\\js\\api-config.js": {"type": "javascript", "imports": 0, "size": 10564}, "frontend\\js\\api-unified.js": {"type": "javascript", "imports": 0, "size": 6849}, "frontend\\js\\baseline-save.js": {"type": "javascript", "imports": 0, "size": 9199}, "frontend\\js\\element-plus-icons.iife.min.js": {"type": "javascript", "imports": 0, "size": 213485}, "frontend\\js\\element-plus.js": {"type": "javascript", "imports": 2, "size": 2318025}, "frontend\\js\\field-list-display.js": {"type": "javascript", "imports": 0, "size": 38540}, "frontend\\js\\field-statistics.js": {"type": "javascript", "imports": 0, "size": 20373}, "frontend\\js\\notification-system.js": {"type": "javascript", "imports": 0, "size": 21212}, "frontend\\js\\performance-optimizer.js": {"type": "javascript", "imports": 0, "size": 24614}, "frontend\\js\\realtime-log.js": {"type": "javascript", "imports": 0, "size": 14500}, "frontend\\js\\unified-components.js": {"type": "javascript", "imports": 0, "size": 19470}, "frontend\\js\\user-config-save.js": {"type": "javascript", "imports": 0, "size": 18045}, "frontend\\js\\vue.global.js": {"type": "javascript", "imports": 0, "size": 594088}, "frontend\\js\\common\\api-client.js": {"type": "javascript", "imports": 0, "size": 6393}, "frontend\\js\\common\\elk-integration-config.js": {"type": "javascript", "imports": 0, "size": 23488}, "frontend\\js\\common\\error-handler.js": {"type": "javascript", "imports": 0, "size": 13535}, "frontend\\js\\common\\field-renderer.js": {"type": "javascript", "imports": 0, "size": 11357}, "frontend\\js\\common\\field-utils.js": {"type": "javascript", "imports": 0, "size": 14506}, "frontend\\js\\common\\smart-logger.js": {"type": "javascript", "imports": 0, "size": 890}, "frontend\\js\\common\\smart-retry-optimizer.js": {"type": "javascript", "imports": 0, "size": 18057}, "frontend\\js\\common\\standard-error-logger.js": {"type": "javascript", "imports": 0, "size": 18973}, "frontend\\js\\common\\test-data.js": {"type": "javascript", "imports": 0, "size": 10938}, "frontend\\js\\common\\user-error-notifier.js": {"type": "javascript", "imports": 0, "size": 18426}, "frontend\\js\\common\\validation-utils.js": {"type": "javascript", "imports": 0, "size": 17781}, "frontend\\js\\core\\app-bootstrap.js": {"type": "javascript", "imports": 0, "size": 13065}, "frontend\\js\\core\\component-manager.js": {"type": "javascript", "imports": 0, "size": 11915}, "frontend\\js\\core\\component-migration-tool.js": {"type": "javascript", "imports": 0, "size": 13020}, "frontend\\js\\core\\initialization-manager.js": {"type": "javascript", "imports": 0, "size": 3960}, "frontend\\js\\core\\page-migration-assistant.js": {"type": "javascript", "imports": 0, "size": 18823}, "migration\\week1_analysis\\tests\\test_batch_processor.py": {"type": "python", "imports": 0, "size": 0}, "migration\\week1_analysis\\tests\\test_data_writer_core.py": {"type": "python", "imports": 0, "size": 0}, "migration\\week2_analysis\\refactored\\conflict_resolver.py": {"type": "python", "imports": 0, "size": 0}, "migration\\week2_analysis\\refactored\\performance_monitor.py": {"type": "python", "imports": 0, "size": 0}, "migration\\week2_analysis\\refactored\\refactored_fast_sync_service.py": {"type": "python", "imports": 0, "size": 0}, "migration\\week2_analysis\\refactored\\sync_scheduler.py": {"type": "python", "imports": 0, "size": 0}, "migration\\week3_analysis\\refactored\\data_transformer.py": {"type": "python", "imports": 0, "size": 0}, "migration\\week3_analysis\\refactored\\endpoint_manager.py": {"type": "python", "imports": 0, "size": 0}, "migration\\week3_analysis\\refactored\\request_builder.py": {"type": "python", "imports": 0, "size": 0}, "migration\\week3_analysis\\refactored\\response_parser.py": {"type": "python", "imports": 0, "size": 0}, "month2_config\\config_rollback\\manager.py": {"type": "python", "imports": 5, "size": 15554}, "month2_config\\two_step_save\\manager.py": {"type": "python", "imports": 4, "size": 8741}, "month2_database\\table_creation\\manager.py": {"type": "python", "imports": 4, "size": 13649}, "new-system\\backup\\modules\\物料档案批量详情查询\\__init__.py": {"type": "python", "imports": 0, "size": 40}, "new-system\\backup\\modules\\现存量报表查询\\api.py": {"type": "python", "imports": 0, "size": 29}, "new-system\\backup\\modules\\现存量报表查询\\__init__.py": {"type": "python", "imports": 0, "size": 31}, "new-system\\modules\\purchase_order\\models.py": {"type": "python", "imports": 2, "size": 2989}, "new-system\\modules\\purchase_order\\routes.py": {"type": "python", "imports": 2, "size": 2470}, "new-system\\modules\\purchase_order\\schema.py": {"type": "python", "imports": 1, "size": 1091}, "new-system\\modules\\purchase_order\\service.py": {"type": "python", "imports": 3, "size": 2453}, "new-system\\modules\\purchase_order\\__init__.py": {"type": "python", "imports": 0, "size": 22}, "new-system\\modules\\业务日志\\api.py": {"type": "python", "imports": 0, "size": 20}, "new-system\\modules\\业务日志\\__init__.py": {"type": "python", "imports": 0, "size": 22}, "new-system\\modules\\产品入库单列表查询\\api.py": {"type": "python", "imports": 1, "size": 294}, "new-system\\modules\\产品入库单列表查询\\models.py": {"type": "python", "imports": 1, "size": 153}, "new-system\\modules\\产品入库单列表查询\\service.py": {"type": "python", "imports": 0, "size": 275}, "new-system\\modules\\产品入库单列表查询\\__init__.py": {"type": "python", "imports": 0, "size": 37}, "new-system\\modules\\委外入库列表查询\\__init__.py": {"type": "python", "imports": 0, "size": 34}, "new-system\\modules\\委外申请列表查询\\__init__.py": {"type": "python", "imports": 0, "size": 34}, "new-system\\modules\\委外订单列表\\api.py": {"type": "python", "imports": 0, "size": 26}, "new-system\\modules\\委外订单列表\\models.py": {"type": "python", "imports": 0, "size": 29}, "new-system\\modules\\委外订单列表\\__init__.py": {"type": "python", "imports": 0, "size": 28}, "new-system\\modules\\物料档案批量详情查询\\__init__.py": {"type": "python", "imports": 0, "size": 0}, "new-system\\modules\\生产订单列表查询\\api.py": {"type": "python", "imports": 0, "size": 32}, "new-system\\modules\\生产订单列表查询\\models.py": {"type": "python", "imports": 0, "size": 35}, "new-system\\modules\\生产订单列表查询\\__init__.py": {"type": "python", "imports": 0, "size": 34}, "new-system\\modules\\请购单列表查询\\api.py": {"type": "python", "imports": 0, "size": 29}, "new-system\\modules\\请购单列表查询\\models.py": {"type": "python", "imports": 0, "size": 32}, "new-system\\modules\\请购单列表查询\\__init__.py": {"type": "python", "imports": 0, "size": 31}, "new-system\\modules\\销售出库列表查询\\api.py": {"type": "python", "imports": 0, "size": 32}, "new-system\\modules\\销售出库列表查询\\__init__.py": {"type": "python", "imports": 0, "size": 34}, "new-system\\modules\\销售订单\\api.py": {"type": "python", "imports": 0, "size": 20}, "new-system\\modules\\销售订单\\__init__.py": {"type": "python", "imports": 0, "size": 22}, "new-system\\modules\\需求计划\\api.py": {"type": "python", "imports": 0, "size": 20}, "new-system\\modules\\需求计划\\__init__.py": {"type": "python", "imports": 0, "size": 22}, "scripts\\add_api_config.py": {"type": "python", "imports": 3, "size": 2571}, "scripts\\auto_migration.py": {"type": "python", "imports": 6, "size": 22587}, "scripts\\auto_migration_pipeline.py": {"type": "python", "imports": 6, "size": 18091}, "scripts\\batch_initialize_next.py": {"type": "python", "imports": 3, "size": 6062}, "scripts\\batch_init_modules.py": {"type": "python", "imports": 4, "size": 5122}, "scripts\\cleanup_test_code.py": {"type": "python", "imports": 6, "size": 19362}, "scripts\\clean_debug_code.py": {"type": "python", "imports": 3, "size": 5823}, "scripts\\clean_hardcoded_data.py": {"type": "python", "imports": 2, "size": 6700}, "scripts\\complete_modules.py": {"type": "python", "imports": 2, "size": 1376}, "scripts\\database_dual_writer.py": {"type": "python", "imports": 12, "size": 17848}, "scripts\\database_dual_writer_simple.py": {"type": "python", "imports": 6, "size": 9099}, "scripts\\diagnose_migration.py": {"type": "python", "imports": 0, "size": 0}, "scripts\\final_status_check.py": {"type": "python", "imports": 4, "size": 5040}, "scripts\\fix_css_paths.py": {"type": "python", "imports": 0, "size": 0}, "scripts\\fix_migrated_paths.py": {"type": "python", "imports": 0, "size": 0}, "scripts\\graveyard_safety_analyzer.py": {"type": "python", "imports": 2, "size": 4143}, "scripts\\health_check.py": {"type": "python", "imports": 5, "size": 6298}, "scripts\\manual_cleanup.py": {"type": "python", "imports": 2, "size": 3273}, "scripts\\migrate_purchase_order.py": {"type": "python", "imports": 13, "size": 22613}, "scripts\\migrate_产品入库单列表查询.py": {"type": "python", "imports": 5, "size": 2303}, "scripts\\migrate_产品入库单列表查询_fixed.py": {"type": "python", "imports": 5, "size": 2287}, "scripts\\migrate_生产订单列表查询.py": {"type": "python", "imports": 5, "size": 2258}, "scripts\\migrate_请购单列表查询.py": {"type": "python", "imports": 5, "size": 2213}, "scripts\\migrate_采购入库单列表.py": {"type": "python", "imports": 4, "size": 2188}, "scripts\\module_functional_test.py": {"type": "python", "imports": 0, "size": 0}, "scripts\\module_tracker.py": {"type": "python", "imports": 5, "size": 18257}, "scripts\\module_tracker_simple.py": {"type": "python", "imports": 5, "size": 10418}, "scripts\\next_module.py": {"type": "python", "imports": 4, "size": 5890}, "scripts\\phase3_verification.py": {"type": "python", "imports": 0, "size": 0}, "scripts\\port_manager.py": {"type": "python", "imports": 28, "size": 5433}, "scripts\\port_manager_clean.py": {"type": "python", "imports": 6, "size": 5639}, "scripts\\quick_batch_migrate.py": {"type": "python", "imports": 6, "size": 3468}, "scripts\\quick_migrate.py": {"type": "python", "imports": 3, "size": 1802}, "scripts\\reliable_server.py": {"type": "python", "imports": 0, "size": 0}, "scripts\\rollback_batch_writes.py": {"type": "python", "imports": 9, "size": 11683}, "scripts\\simple_migrator.py": {"type": "python", "imports": 6, "size": 8623}, "scripts\\test_elk_connection.py": {"type": "python", "imports": 0, "size": 0}, "scripts\\test_server.py": {"type": "python", "imports": 0, "size": 0}, "scripts\\ultra_simple_migrate.py": {"type": "python", "imports": 5, "size": 2774}, "scripts\\validate_deployment.py": {"type": "python", "imports": 0, "size": 0}, "scripts\\verify_fixes.py": {"type": "python", "imports": 0, "size": 0}, "tests\\locust_stress_test.py": {"type": "python", "imports": 0, "size": 0}, "tests\\simple_test.py": {"type": "python", "imports": 0, "size": 0}, "tests\\test_baseline_api.py": {"type": "python", "imports": 0, "size": 0}, "tests\\test_md_to_json_converter.py": {"type": "python", "imports": 7, "size": 14156}, "tests\\test_rollback_scripts.py": {"type": "python", "imports": 6, "size": 11732}, "tests\\test_week2_validation.py": {"type": "python", "imports": 0, "size": 0}, "tests\\week2_completion_report.py": {"type": "python", "imports": 0, "size": 0}, "tests\\module_migration\\test_generator.py": {"type": "python", "imports": 0, "size": 0}, "tools\\error_handling_load_test.py": {"type": "python", "imports": 0, "size": 0}, "tools\\md_to_json_converter.py": {"type": "python", "imports": 7, "size": 5254}}, "summary": {"total_files_analyzed": 411, "python_files": 378, "javascript_files": 33, "high_dependency_files": 13}}