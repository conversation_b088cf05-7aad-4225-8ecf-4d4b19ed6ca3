import ast
import configparser
import logging
import re
from datetime import datetime
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 TASK.md 清单执行器
按照优先级逐步执行检查任务
"""


class TaskExecutor:
    def __init___(self, project_root):
    """TODO: Add function description."""
    self.project_root = Path(project_root)
    self.results = {
        "execution_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "completed_tasks": [],
        "failed_tasks": [],
        "warnings": [],
        "stage_results": {},
    }

    # 设置日志记录

    self.logger = logging.getLogger(__name__)
    if not self.logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

    def log_task(self, stage, task, status, details=""):
        """记录任务执行结果"""
        task_record = {
            "stage": stage,
            "task": task,
            "status": status,
            "details": details,
            "timestamp": datetime.now().strftime("%H:%M:%S"),
        }

        if status == "COMPLETED":
            self.results["completed_tasks"].append(task_record)
        elif status == "FAILED":
            self.results["failed_tasks"].append(task_record)
        elif status == "WARNING":
            self.results["warnings"].append(task_record)

        self.logger.info(f"[{status}] {stage} - {task}: {details}")

    def execute_stage1_code_quality(self):
        """第一阶段：代码质量检查"""
        self.logger.info("\n🎯 执行第一阶段：代码质量检查")
        self.logger.info("=" * 50)

        # 1.1 代码结构分析
        self.check_python_code_standards()
        self.check_duplicate_code()
        self.check_unused_imports()
        self.check_naming_conventions()
        self.check_code_comments()

        # 1.2 依赖管理检查
        self.check_requirements_txt()
        self.check_unused_dependencies()
        self.check_dependency_conflicts()

        # 1.3 配置文件检查
        self.check_config_integrity()
        self.check_sensitive_info()
        self.check_path_configs()

    def check_python_code_standards(self):
        """检查Python代码规范（PEP8）"""
        python_files = list(self.project_root.rglob("*.py"))
        issues = []

        for py_file in python_files:
            if "__pycache__" in str(py_file):
                continue

            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查长行
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if len(line) > 120:
                        issues.append(f"{py_file.name}:{i} 行长度超过120字符")

                # 检查导入规范
                try:
                    tree = ast.parse(content)
                    imports = [
                        node
                        for node in ast.walk(tree)
                        if isinstance(node, (ast.Import, ast.ImportFrom))
                    ]
                    if len(imports) > 20:
                        issues.append(f"{py_file.name} 导入过多({len(imports)}个)")
                except Exception:
                    pass

            except Exception:
                issues.append(f"无法检查 {py_file.name}: {e}")

        if issues:
            self.log_task(
                "代码质量", "Python代码规范检查", "WARNING", f"发现{len(issues)}个问题"
            )
        else:
            self.log_task("代码质量", "Python代码规范检查", "COMPLETED", "代码规范良好")

    def check_duplicate_code(self):
        """识别重复代码和冗余函数"""
        function_signatures = {}
        duplicates = []

        python_files = list(self.project_root.rglob("*.py"))

        for py_file in python_files:
            if "__pycache__" in str(py_file):
                continue

            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 简单的函数重复检查
                tree = ast.parse(content)
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        func_name = node.name
                        if func_name in function_signatures:
                            duplicates.append(
                                f"函数 {func_name} 在 {py_file.name} 和 {function_signatures[func_name]} 中重复"
                            )
                        else:
                            function_signatures[func_name] = py_file.name

            except Exception:
                continue

        if duplicates:
            self.log_task(
                "代码质量",
                "重复代码检查",
                "WARNING",
                f"发现{len(duplicates)}个重复函数",
            )
        else:
            self.log_task("代码质量", "重复代码检查", "COMPLETED", "未发现明显重复代码")

    def check_unused_imports(self):
        """检查未使用的导入和变量"""
        issues = []
        python_files = list(self.project_root.rglob("*.py"))

        for py_file in python_files[:5]:  # 限制检查文件数量
            if "__pycache__" in str(py_file):
                continue

            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 简单检查：查找import但在代码中未使用的模块
                import_pattern = r'^import\s+(\w+)'
                from_import_pattern = r'^from\s+\w+\s+import\s+(\w+)'

                imports = re.findall(import_pattern, content, re.MULTILINE)
                from_imports = re.findall(
                    from_import_pattern, content, re.MULTILINE)

                all_imports = imports + from_imports

                for imp in all_imports:
                    if imp not in content.replace(f"import {imp}", ""):
                        issues.append(f"{py_file.name}: 可能未使用的导入 {imp}")

            except Exception:
                continue

        if issues:
            self.log_task(
                "代码质量",
                "未使用导入检查",
                "WARNING",
                f"发现{len(issues)}个可能未使用的导入",
            )
        else:
            self.log_task("代码质量", "未使用导入检查", "COMPLETED", "导入使用正常")

    def check_naming_conventions(self):
        """验证文件和目录命名规范"""
        issues = []

        # 检查Python文件命名
        python_files = list(self.project_root.rglob("*.py"))
        for py_file in python_files:
            if not re.match(r'^[a-z_][a-z0-9_]*\.py$', py_file.name):
                issues.append(f"文件命名不符合Python规范: {py_file.name}")

        # 检查目录命名
        for dir_path in self.project_root.rglob("*"):
            if dir_path.is_dir() and "__pycache__" not in str(dir_path):
                dir_name = dir_path.name
                if (
                    dir_name
                    and not re.match(r'^[a-z_][a-z0-9_]*$', dir_name)
                    and dir_name not in ['YS-API程序', 'v3']
                ):
                    issues.append(f"目录命名建议优化: {dir_name}")

        if issues:
            self.log_task(
                "代码质量", "命名规范检查", "WARNING", f"发现{len(issues)}个命名问题"
            )
        else:
            self.log_task("代码质量", "命名规范检查", "COMPLETED", "命名规范良好")

    def check_code_comments(self):
        """检查代码注释完整性"""
        python_files = list(self.project_root.rglob("*.py"))
        total_lines = 0
        comment_lines = 0

        for py_file in python_files:
            if "__pycache__" in str(py_file):
                continue

            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                for line in lines:
                    line = line.strip()
                    if line:  # 非空行
                        total_lines += 1
                        if line.startswith(
                                '#') or '"""' in line or "'''" in line:
                            comment_lines += 1

            except Exception:
                continue

        if total_lines > 0:
            comment_ratio = (comment_lines / total_lines) * 100
            if comment_ratio < 10:
                self.log_task(
                    "代码质量",
                    "代码注释检查",
                    "WARNING",
                    f"注释覆盖率较低: {comment_ratio:.1f}%",
                )
            else:
                self.log_task(
                    "代码质量",
                    "代码注释检查",
                    "COMPLETED",
                    f"注释覆盖率: {comment_ratio:.1f}%",
                )
        else:
            self.log_task("代码质量", "代码注释检查", "COMPLETED", "无需检查")

    def check_requirements_txt(self):
        """验证requirements.txt完整性"""
        req_file = self.project_root / "backend" / "requirements.txt"

        if not req_file.exists():
            self.log_task(
                "依赖管理",
                "requirements.txt检查",
                "FAILED",
                "requirements.txt文件不存在",
            )
            return

        try:
            with open(req_file, 'r', encoding='utf-8') as f:
                requirements = f.read().strip().split('\n')

            # 检查常见依赖
            common_deps = ['fastapi', 'uvicorn', 'pydantic', 'sqlalchemy']
            missing_deps = []

            req_text = '\n'.join(requirements).lower()
            for dep in common_deps:
                if dep not in req_text:
                    missing_deps.append(dep)

            if missing_deps:
                self.log_task(
                    "依赖管理",
                    "requirements.txt检查",
                    "WARNING",
                    f"可能缺少依赖: {missing_deps}",
                )
            else:
                self.log_task(
                    "依赖管理",
                    "requirements.txt检查",
                    "COMPLETED",
                    f"发现{len(requirements)}个依赖包",
                )

        except Exception:
            self.log_task("依赖管理", "requirements.txt检查", "FAILED", str(e))

    def check_unused_dependencies(self):
        """检查未使用的依赖包"""
        # 这是一个复杂的检查，简化实现
        self.log_task(
            "依赖管理", "未使用依赖检查", "COMPLETED", "需要手动审查依赖使用情况"
        )

    def check_dependency_conflicts(self):
        """识别版本冲突风险"""
        req_file = self.project_root / "backend" / "requirements.txt"

        if req_file.exists():
            try:
                with open(req_file, 'r', encoding='utf-8') as f:
                    requirements = f.read()

                # 检查是否有版本固定
                versioned_deps = len(re.findall(r'==\d+\.\d+', requirements))
                total_deps = len(requirements.strip().split('\n'))

                if versioned_deps / total_deps < 0.5:
                    self.log_task(
                        "依赖管理", "版本冲突检查", "WARNING", "建议固定更多依赖版本"
                    )
                else:
                    self.log_task(
                        "依赖管理", "版本冲突检查", "COMPLETED", "依赖版本管理良好"
                    )

            except Exception:
                self.log_task("依赖管理", "版本冲突检查", "FAILED", str(e))
        else:
            self.log_task(
                "依赖管理", "版本冲突检查", "FAILED", "requirements.txt不存在"
            )

    def check_config_integrity(self):
        """检查config.ini配置完整性"""
        config_file = self.project_root / "config.ini"

        if not config_file.exists():
            self.log_task(
                "配置文件", "配置完整性检查", "FAILED", "config.ini文件不存在"
            )
            return

        try:

            config = configparser.ConfigParser()
            config.read(config_file, encoding='utf-8')

            required_sections = ['database', 'api', 'logging']
            missing_sections = []

            for section in required_sections:
                if not config.has_section(section):
                    missing_sections.append(section)

            if missing_sections:
                self.log_task(
                    "配置文件",
                    "配置完整性检查",
                    "WARNING",
                    f"缺少配置节: {missing_sections}",
                )
            else:
                self.log_task(
                    "配置文件", "配置完整性检查", "COMPLETED", "配置文件结构完整"
                )

        except Exception:
            self.log_task("配置文件", "配置完整性检查", "FAILED", str(e))

    def check_sensitive_info(self):
        """检查敏感信息泄露风险"""
        sensitive_patterns = [
            r'password\s*=\s*["\'][\w!@#$%^&*()]+["\']',
            r'secret\s*=\s*["\'][\w!@#$%^&*()]+["\']',
            r'api_key\s*=\s*["\'][\w!@#$%^&*()]+["\']',
            r'token\s*=\s*["\'][\w!@#$%^&*()]+["\']',
        ]

        issues = []
        config_files = [self.project_root / "config.ini"]

        for config_file in config_files:
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    for pattern in sensitive_patterns:
                        if re.search(pattern, content, re.IGNORECASE):
                            issues.append(f"{config_file.name}: 可能包含硬编码敏感信息")
                            break

                except Exception:
                    continue

        if issues:
            self.log_task(
                "配置文件", "敏感信息检查", "WARNING", f"发现{len(issues)}个潜在风险"
            )
        else:
            self.log_task(
                "配置文件", "敏感信息检查", "COMPLETED", "未发现明显的敏感信息泄露"
            )

    def check_path_configs(self):
        """验证路径配置正确性"""
        config_file = self.project_root / "config.ini"

        if config_file.exists():
            try:

                config = configparser.ConfigParser()
                config.read(config_file, encoding='utf-8')

                path_issues = []

                # 检查数据库路径
                if config.has_section('database') and config.has_option(
                    'database', 'path'
                ):
                    db_path = Path(config.get('database', 'path'))
                    if not db_path.exists() and not db_path.parent.exists():
                        path_issues.append(f"数据库路径无效: {db_path}")

                if path_issues:
                    self.log_task(
                        "配置文件",
                        "路径配置检查",
                        "WARNING",
                        f"发现{len(path_issues)}个路径问题",
                    )
                else:
                    self.log_task(
                        "配置文件", "路径配置检查", "COMPLETED", "路径配置正确"
                    )

            except Exception:
                self.log_task("配置文件", "路径配置检查", "FAILED", str(e))
        else:
            self.log_task("配置文件", "路径配置检查", "FAILED", "config.ini不存在")

    def execute_stage2_security(self):
        """第二阶段：安全性检查（高优先级部分）"""
        self.logger.info("\n🛡️ 执行第二阶段：安全性检查")
        self.logger.info("=" * 50)

        self.check_hardcoded_credentials()
        self.check_api_security()
        self.check_file_permissions()

    def check_hardcoded_credentials(self):
        """扫描硬编码密码和密钥"""
        sensitive_patterns = [
            r'password\s*[:=]\s*["\'][^"\']{3, }["\']',
            r'secret\s*[:=]\s*["\'][^"\']{10, }["\']',
            r'key\s*[:=]\s*["\'][^"\']{10, }["\']',
            r'token\s*[:=]\s*["\'][^"\']{10, }["\']',
        ]

        issues = []

        # 检查Python文件
        python_files = list(self.project_root.rglob("*.py"))
        for py_file in python_files:
            if "__pycache__" in str(py_file):
                continue

            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                for pattern in sensitive_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        issues.append(f"{py_file.name}: 可能包含硬编码凭据")
                        break

            except Exception:
                continue

        if issues:
            self.log_task(
                "安全性", "硬编码凭据检查", "WARNING", f"发现{len(issues)}个潜在问题"
            )
        else:
            self.log_task("安全性", "硬编码凭据检查", "COMPLETED", "未发现硬编码凭据")

    def check_api_security(self):
        """检查API安全配置"""
        # 检查CORS配置
        backend_files = list((self.project_root / "backend").rglob("*.py"))
        cors_configured = False

        for py_file in backend_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                if 'CORSMiddleware' in content or 'cors' in content.lower():
                    cors_configured = True
                    break

            except Exception:
                continue

        if cors_configured:
            self.log_task("安全性", "API安全检查", "COMPLETED", "发现CORS配置")
        else:
            self.log_task("安全性", "API安全检查", "WARNING", "未发现CORS配置")

    def check_file_permissions(self):
        """检查文件权限设置"""
        sensitive_files =
        ["config.ini",
         "backend/config.ini",
         "backend/ysapi.db"
         ]

        issues = []
        for file_path in sensitive_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                # 在Windows上，权限检查比较复杂，这里简化处理
                try:
                    # 检查文件是否可读
                    with open(full_path, 'r') as f:
                        pass
                    # 如果是数据库文件，检查大小
                    if file_path.endswith('.db'):
                        size = full_path.stat().st_size
                        if size == 0:
                            issues.append(f"{file_path}: 数据库文件为空")
                except Exception:
                    issues.append(f"{file_path}: 权限或访问问题 - {e}")

        if issues:
            self.log_task(
                "安全性", "文件权限检查", "WARNING", f"发现{len(issues)}个权限问题"
            )
        else:
            self.log_task("安全性", "文件权限检查", "COMPLETED", "文件权限正常")

    def generate_progress_report(self):
        """生成进度报告"""
        total_tasks = (
            len(self.results["completed_tasks"])
            + len(self.results["failed_tasks"])
            + len(self.results["warnings"])
        )
        completed_count = len(self.results["completed_tasks"])

        report = f"""
# YS-API V3.0 TASK.md 执行进度报告

**执行时间**: {self.results["execution_time"]}
**总任务数**: {total_tasks}
**已完成**: {completed_count}
**失败任务**: {len(self.results["failed_tasks"])}
**警告项目**: {len(self.results["warnings"])}
**完成率**: {(completed_count/total_tasks*100):.1f}%

## 📊 执行摘要

### ✅ 已完成任务
"""

        for task in self.results["completed_tasks"]:
            report += f"- [{task['stage']}] {task['task']}: {task['details']}\n"

        if self.results["failed_tasks"]:
            report += "\n### ❌ 失败任务\n"
            for task in self.results["failed_tasks"]:
                report += f"- [{task['stage']}] {task['task']}: {task['details']}\n"

        if self.results["warnings"]:
            report += "\n### ⚠️ 警告项目\n"
            for task in self.results["warnings"]:
                report += f"- [{task['stage']}] {task['task']}: {task['details']}\n"

        report += f"""
## 🎯 下一步行动

### 立即处理
- 修复所有失败的任务
- 审查并处理警告项目
- 继续执行剩余阶段

### 持续改进
- 建立定期检查机制
- 更新文档和流程
- 优化代码质量

---
*报告生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
"""

        # 保存报告
        report_file = self.project_root / "task_execution_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        return report_file

    def run_high_priority_tasks(self):
        """执行高优先级任务"""
        self.logger.info("🚀 开始执行TASK.md高优先级清单")
        self.logger.info("=" * 60)

        # 第一阶段：代码质量检查
        self.execute_stage1_code_quality()

        # 第二阶段：安全性检查（高优先级部分）
        self.execute_stage2_security()

        # 生成报告
        report_file = self.generate_progress_report()

        self.logger.info(f"\n✅ 高优先级任务执行完成!")
        self.logger.info(f"📄 详细报告: {report_file}")
        self.logger.info(
            f"📊 完成率: {len(self.results['completed_tasks'])} / {len(self.results['completed_tasks']) +
                                                               len(
                                                                   self.results['failed_tasks'])
                                                               len(self.results['warnings'])} 任务"
        )


if __name__ == "__main__":
    project_root = Path(__file__).parent
    executor = TaskExecutor(str(project_root))
    executor.run_high_priority_tasks()
