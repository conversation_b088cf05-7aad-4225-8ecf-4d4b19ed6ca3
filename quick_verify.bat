echo "🧪 验证修复结果"
echo "=================="

echo "1. 检查PortManager方法..."
python -c "
import sys
sys.path.append('scripts')
from port_manager import PortManager
import inspect
pm = PortManager()
sig = inspect.signature(pm.ensure_port_available)
params = list(sig.parameters.keys())
print(f'PortManager.ensure_port_available 参数: {params}')
if len(params) == 2: 
    print('✅ 方法签名正确')
else: 
    print('❌ 方法签名错误')
"

echo ""
echo "2. 检查ErrorHandler文件..."
findstr "window.ErrorHandler" frontend\js\common\error-handler.js
if %ERRORLEVEL% == 0 (
    echo "✅ ErrorHandler文件包含window.ErrorHandler"
) else (
    echo "❌ ErrorHandler文件缺少window.ErrorHandler"
)

echo ""
echo "🎯 现在可以尝试启动服务："
echo "  后端: python backend/start_server_fixed.py"
echo "  前端: python frontend/start_frontend_fixed.py"
