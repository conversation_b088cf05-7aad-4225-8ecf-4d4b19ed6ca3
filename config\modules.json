[{"name": "production_order", "display_name": "生产订单", "enabled": true, "api_endpoint": "/production-orders", "table_name": "production_orders"}, {"name": "material_master", "display_name": "物料档案", "enabled": true, "api_endpoint": "/material-master", "table_name": "material_master"}, {"name": "purchase_order", "display_name": "采购订单", "enabled": true, "api_endpoint": "/purchase-orders", "table_name": "purchase_orders"}, {"name": "sales_order", "display_name": "销售订单", "enabled": true, "api_endpoint": "/sales-orders", "table_name": "sales_orders"}, {"name": "inventory", "display_name": "库存管理", "enabled": false, "api_endpoint": "/inventory", "table_name": "inventory"}, {"name": "purchase_receipt", "display_name": "采购入库", "enabled": true, "api_endpoint": "/purchase-receipts", "table_name": "purchase_receipts"}, {"name": "sales_out", "display_name": "销售出库", "enabled": true, "api_endpoint": "/sales-out", "table_name": "sales_out"}, {"name": "product_receipt", "display_name": "产品入库", "enabled": true, "api_endpoint": "/product-receipts", "table_name": "product_receipts"}, {"name": "materialout", "display_name": "材料出库", "enabled": true, "api_endpoint": "/material-out", "table_name": "material_out"}, {"name": "subcontract_order", "display_name": "委外订单", "enabled": true, "api_endpoint": "/subcontract-orders", "table_name": "subcontract_orders"}, {"name": "subcontract_receipt", "display_name": "委外入库", "enabled": true, "api_endpoint": "/subcontract-receipts", "table_name": "subcontract_receipts"}, {"name": "subcontract_requisition", "display_name": "委外申请", "enabled": true, "api_endpoint": "/subcontract-requisitions", "table_name": "subcontract_requisitions"}, {"name": "applyorder", "display_name": "请购单", "enabled": true, "api_endpoint": "/apply-orders", "table_name": "apply_orders"}, {"name": "inventory_report", "display_name": "现存量报表", "enabled": true, "api_endpoint": "/inventory-reports", "table_name": "inventory_reports"}, {"name": "requirements_planning", "display_name": "需求计划", "enabled": true, "api_endpoint": "/requirements-planning", "table_name": "requirements_planning"}]