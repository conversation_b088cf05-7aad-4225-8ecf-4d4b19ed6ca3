import re

import structlog

"""
YS-API V3.0 字段分析服务
负责字段数据质量分析和ETL建议
从FieldConfigService拆分出来的专门服务
"""


logger = structlog.get_logger()


class FieldAnalysisService:
    """字段分析服务 - 负责字段数据质量分析和ETL建议"""

    def __init___(self):
    """TODO: Add function description."""
    logger.info("字段分析服务初始化完成")

    def analyze_field_data_quality(
        self, api_fields: Dict, api_data: List[Dict]
    ) -> Dict:
        """
        分析字段数据质量

        Args:
            api_fields: API字段信息
            api_data: API数据

        Returns:
            Dict: 数据质量分析结果
        """
        try:
            quality_analysis = {}

            for field_name, field_info in api_fields.items():
                # 提取字段值
                values = self._extract_field_values(api_data, field_name)

                if not values:
                    continue

                # 分析数据质量
                quality_info = {
                    'total_records': len(values),
                    'non_null_count': sum(1 for v in values if v is not None),
                    'null_count': sum(1 for v in values if v is None),
                    'unique_count': len(set(v for v in values if v is not None)),
                    'type_consistency': self._check_type_consistency(values),
                    'data_patterns': self._analyze_data_patterns(values),
                    'quality_score': 0.0,
                }

                # 计算质量评分
                quality_info['quality_score'] = self._calculate_quality_score(
                    quality_info
                )

                quality_analysis[field_name] = quality_info

            logger.info("字段数据质量分析完成", fields_count=len(quality_analysis))
            return quality_analysis

        except Exception:
            logger.error("字段数据质量分析失败", error=str(e))
            return {}

    def generate_etl_recommendations(
        self, api_fields: Dict, quality_analysis: Dict
    ) -> Dict:
        """
        生成ETL建议

        Args:
            api_fields: API字段信息
            quality_analysis: 数据质量分析结果

        Returns:
            Dict: ETL建议
        """
        try:
            etl_recommendations = {}

            for field_name, field_info in api_fields.items():
                quality_info = quality_analysis.get(field_name, {})

                # 生成ETL建议
                etl_suggestions = {
                    'data_cleaning': self._suggest_data_cleaning(
                        field_info, quality_info
                    ),
                    'transformation': self._suggest_transformation(
                        field_info, quality_info
                    ),
                    'validation': self._suggest_validation(field_info, quality_info),
                    'indexing': self._suggest_indexing(field_info, quality_info),
                    'priority': self._calculate_etl_priority(field_info, quality_info),
                }

                etl_recommendations[field_name] = etl_suggestions

            logger.info("ETL建议生成完成", fields_count=len(etl_recommendations))
            return etl_recommendations

        except Exception:
            logger.error("ETL建议生成失败", error=str(e))
            return {}

    def analyze_business_value(
            self,
            field_name: str,
            field_info: Dict) -> Dict:
        """
        分析字段的业务价值

        Args:
            field_name: 字段名
            field_info: 字段信息

        Returns:
            Dict: 业务价值分析
        """
        try:
            business_value = {
                'importance_score': self._calculate_business_importance(field_name),
                'business_category': self._categorize_business_field(field_name),
                'reporting_value': self._assess_reporting_value(field_name),
                'analytics_value': self._assess_analytics_value(field_name),
                'overall_score': 0.0,
            }

            # 计算总体评分
            business_value['overall_score'] = (
                business_value['importance_score'] * 0.4
                + business_value['reporting_value'] * 0.3
                + business_value['analytics_value'] * 0.3
            )

            return business_value

        except Exception:
            logger.error("业务价值分析失败", field_name=field_name, error=str(e))
            return {'overall_score': 0.0}

    def _extract_field_values(
            self,
            api_data: List[Dict],
            field_name: str) -> List[Any]:
        """从API数据中提取字段值"""
        try:
            values = []
            for record in api_data:
                # 处理嵌套字段（使用点号分隔）
                if '.' in field_name:
                    parts = field_name.split('.')
                    value = record
                    for part in parts:
                        if isinstance(value, dict) and part in value:
                            value = value[part]
                        else:
                            value = None
                            break
                    values.append(value)
                else:
                    values.append(record.get(field_name))

            return values

        except Exception:
            logger.error("提取字段值失败", field_name=field_name, error=str(e))
            return []

    def _check_type_consistency(self, values: List[Any]) -> float:
        """检查类型一致性"""
        try:
            if not values:
                return 0.0

            # 统计类型分布
            type_counts = {}
            for value in values:
                if value is not None:
                    value_type = type(value).__name__
                    type_counts[value_type] = type_counts.get(
                        value_type, 0) + 1

            if not type_counts:
                return 0.0

            # 计算主要类型的占比
            total_non_null = sum(type_counts.values())
            max_type_count = max(type_counts.values())

            return max_type_count / total_non_null

        except Exception:
            logger.error("检查类型一致性失败", error=str(e))
            return 0.0

    def _analyze_data_patterns(self, values: List[Any]) -> Dict:
        """分析数据模式"""
        try:
            patterns = {
                'has_numeric': False,
                'has_date': False,
                'has_boolean': False,
                'has_text': False,
                'avg_length': 0.0,
                'max_length': 0,
                'min_length': 0,
            }

            non_null_values = [v for v in values if v is not None]
            if not non_null_values:
                return patterns

            # 分析数据类型模式
            for value in non_null_values:
                if isinstance(value, (int, float)):
                    patterns['has_numeric'] = True
                elif isinstance(value, bool):
                    patterns['has_boolean'] = True
                elif isinstance(value, str):
                    patterns['has_text'] = True
                    # 检查是否是日期
                    if self._is_date_string(value):
                        patterns['has_date'] = True

            # 计算文本长度统计
            text_values =
            [str(v) for v in non_null_values if isinstance(v,
                                                           str)
             ]
            if text_values:
                lengths = [len(v) for v in text_values]
                patterns['avg_length'] = sum(lengths) / len(lengths)
                patterns['max_length'] = max(lengths)
                patterns['min_length'] = min(lengths)

            return patterns

        except Exception:
            logger.error("分析数据模式失败", error=str(e))
            return {}

    def _is_date_string(self, value: str) -> bool:
        """检查字符串是否是日期格式"""
        try:

            date_patterns = [
                r'\d{4}-\d{2}-\d{2}',
                r'\d{4}/\d{2}/\d{2}',
                r'\d{2}-\d{2}-\d{4}',
                r'\d{2}/\d{2}/\d{4}',
            ]

            for pattern in date_patterns:
                if re.match(pattern, value):
                    return True

            return False

        except Exception:
            return False

    def _calculate_quality_score(self, quality_info: Dict) -> float:
        """计算数据质量评分"""
        try:
            total_records = quality_info.get('total_records', 0)
            if total_records == 0:
                return 0.0

            # 完整性评分
            completeness = quality_info.get(
                'non_null_count', 0) / total_records

            # 唯一性评分
            unique_ratio = quality_info.get(
                'unique_count', 0) / quality_info.get('non_null_count', 1)
            uniqueness = min(unique_ratio, 1.0)  # 避免过度唯一性

            # 一致性评分
            consistency = quality_info.get('type_consistency', 0.0)

            # 综合评分
            quality_score = completeness * 0.4 + uniqueness * 0.3 + consistency * 0.3

            return round(quality_score, 2)

        except Exception:
            logger.error("计算质量评分失败", error=str(e))
            return 0.0

    def _suggest_data_cleaning(
            self,
            field_info: Dict,
            quality_info: Dict) -> List[str]:
        """建议数据清理方法"""
        suggestions = []

        try:
            # 检查空值处理
            null_ratio = quality_info.get('null_count', 0) / quality_info.get(
                'total_records', 1
            )
            if null_ratio > 0.1:
                suggestions.append("考虑空值填充策略")

            # 检查类型一致性
            if quality_info.get('type_consistency', 0) < 0.8:
                suggestions.append("需要类型转换和标准化")

            # 检查数据长度
            patterns = quality_info.get('data_patterns', {})
            if patterns.get('max_length', 0) > 1000:
                suggestions.append("考虑截断或分段处理长文本")

            return suggestions

        except Exception:
            logger.error("生成数据清理建议失败", error=str(e))
            return []

    def _suggest_transformation(
        self, field_info: Dict, quality_info: Dict
    ) -> List[str]:
        """建议数据转换方法"""
        suggestions = []

        try:
            patterns = quality_info.get('data_patterns', {})

            # 日期转换
            if patterns.get('has_date'):
                suggestions.append("标准化日期格式")

            # 数值转换
            if patterns.get('has_numeric'):
                suggestions.append("统一数值精度和格式")

            # 文本转换
            if patterns.get('has_text'):
                suggestions.append("文本标准化和清理")

            return suggestions

        except Exception:
            logger.error("生成转换建议失败", error=str(e))
            return []

    def _suggest_validation(
            self,
            field_info: Dict,
            quality_info: Dict) -> List[str]:
        """建议数据验证方法"""
        suggestions = []

        try:
            # 范围验证
            suggestions.append("添加数值范围验证")

            # 格式验证
            suggestions.append("添加数据格式验证")

            # 业务规则验证
            suggestions.append("添加业务逻辑验证")

            return suggestions

        except Exception:
            logger.error("生成验证建议失败", error=str(e))
            return []

    def _suggest_indexing(
            self,
            field_info: Dict,
            quality_info: Dict) -> List[str]:
        """建议索引策略"""
        suggestions = []

        try:
            field_name = field_info.get('api_field_name', '')
            field_name_lower = field_name.lower()

            # 主键字段
            if 'id' in field_name_lower:
                suggestions.append("创建主键索引")

            # 外键字段
            if any(
                keyword in field_name_lower for keyword in [
                    'code',
                    'no',
                    'ref']):
                suggestions.append("创建外键索引")

            # 查询频繁字段
            if any(
                keyword in field_name_lower for keyword in [
                    'name',
                    'date',
                    'status']):
                suggestions.append("创建普通索引")

            return suggestions

        except Exception:
            logger.error("生成索引建议失败", error=str(e))
            return []

    def _calculate_etl_priority(
            self,
            field_info: Dict,
            quality_info: Dict) -> str:
        """计算ETL优先级"""
        try:
            quality_score = quality_info.get('quality_score', 0.0)

            if quality_score < 0.5:
                return "高"
            elif quality_score < 0.8:
                return "中"
            else:
                return "低"

        except Exception:
            logger.error("计算ETL优先级失败", error=str(e))
            return "中"

    def _calculate_business_importance(self, field_name: str) -> float:
        """计算业务重要性评分"""
        try:
            field_name_lower = field_name.lower()

            # 高重要性关键词
            high_importance = [
                'id',
                'code',
                'name',
                'number',
                'date',
                'amount',
                'quantity',
            ]
            # 中重要性关键词
            medium_importance = ['type', 'status', 'description', 'remark']

            for keyword in high_importance:
                if keyword in field_name_lower:
                    return 0.9

            for keyword in medium_importance:
                if keyword in field_name_lower:
                    return 0.6

            return 0.3

        except Exception:
            logger.error("计算业务重要性失败", field_name=field_name, error=str(e))
            return 0.3

    def _categorize_business_field(self, field_name: str) -> str:
        """分类业务字段"""
        try:
            field_name_lower = field_name.lower()

            if any(
                keyword in field_name_lower for keyword in [
                    'id',
                    'code',
                    'no']):
                return "标识字段"
            elif any(keyword in field_name_lower for keyword in ['name', 'title']):
                return "名称字段"
            elif any(keyword in field_name_lower for keyword in ['date', 'time']):
                return "时间字段"
            elif any(
                keyword in field_name_lower
                for keyword in ['amount', 'price', 'quantity']
            ):
                return "数值字段"
            elif any(keyword in field_name_lower for keyword in ['status', 'type']):
                return "状态字段"
            else:
                return "其他字段"

        except Exception:
            logger.error("分类业务字段失败", field_name=field_name, error=str(e))
            return "其他字段"

    def _assess_reporting_value(self, field_name: str) -> float:
        """评估报表价值"""
        try:
            field_name_lower = field_name.lower()

            # 报表常用字段
            reporting_fields = [
                'id',
                'code',
                'name',
                'date',
                'amount',
                'quantity',
                'status',
                'type',
            ]

            for field in reporting_fields:
                if field in field_name_lower:
                    return 0.8

            return 0.4

        except Exception:
            logger.error("评估报表价值失败", field_name=field_name, error=str(e))
            return 0.4

    def _assess_analytics_value(self, field_name: str) -> float:
        """评估分析价值"""
        try:
            field_name_lower = field_name.lower()

            # 分析常用字段
            analytics_fields = [
                'amount',
                'quantity',
                'price',
                'count',
                'sum',
                'avg',
                'date',
            ]

            for field in analytics_fields:
                if field in field_name_lower:
                    return 0.9

            return 0.3

        except Exception:
            logger.error("评估分析价值失败", field_name=field_name, error=str(e))
            return 0.3
