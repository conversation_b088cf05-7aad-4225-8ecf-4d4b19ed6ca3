{"module_name": "sales_order", "display_name": "销售订单", "version": "2.0.0", "source": "json_parser", "total_fields": 309, "created_at": "2025-07-28T20:12:24.863694", "last_updated": "2025-07-28T20:12:24.863694", "fields": {"code": {"api_field_name": "code", "chinese_name": "单据编号", "data_type": "NVARCHAR(500)", "param_desc": "单据编号", "path": "data.recordList.code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "message": {"api_field_name": "message", "chinese_name": "调用失败时的错误信息", "data_type": "NVARCHAR(500)", "param_desc": "调用失败时的错误信息", "path": "message", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "调用成功时的返回数据", "data_type": "NVARCHAR(MAX)", "param_desc": "调用成功时的返回数据", "path": "data", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageIndex": {"api_field_name": "pageIndex", "chinese_name": "页号", "data_type": "BIGINT", "param_desc": "页号", "path": "pageIndex", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageSize": {"api_field_name": "pageSize", "chinese_name": "每页行数", "data_type": "BIGINT", "param_desc": "每页行数", "path": "pageSize", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordCount": {"api_field_name": "recordCount", "chinese_name": "总共记录数", "data_type": "BIGINT", "param_desc": "总共记录数", "path": "data.recordCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordList": {"api_field_name": "recordList", "chinese_name": "记录列表", "data_type": "NVARCHAR(MAX)", "param_desc": "记录列表", "path": "data.recordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vouchdate": {"api_field_name": "vouchdate", "chinese_name": "单据日期", "data_type": "NVARCHAR(500)", "param_desc": "单据日期", "path": "data.recordList.vouchdate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "id": {"api_field_name": "id", "chinese_name": "表体自定义项id", "data_type": "BIGINT", "param_desc": "表体自定义项id", "path": "data.recordList.bodyItem.id", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "parentOrderNo": {"api_field_name": "parentOrderNo", "chinese_name": "父级订单号", "data_type": "NVARCHAR(500)", "param_desc": "父级订单号", "path": "data.recordList.parentOrderNo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "salesOrgId": {"api_field_name": "salesOrgId", "chinese_name": "销售组织id", "data_type": "NVARCHAR(500)", "param_desc": "销售组织id", "path": "data.recordList.salesOrgId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "salesOrgId_name": {"api_field_name": "salesOrgId_name", "chinese_name": "销售组织名称", "data_type": "NVARCHAR(500)", "param_desc": "销售组织名称", "path": "data.recordList.salesOrgId_name", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "saleDepartmentId": {"api_field_name": "saleDepartmentId", "chinese_name": "销售部门id", "data_type": "NVARCHAR(500)", "param_desc": "销售部门id", "path": "data.recordList.saleDepartmentId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "transactionTypeId": {"api_field_name": "transactionTypeId", "chinese_name": "交易类型id", "data_type": "NVARCHAR(500)", "param_desc": "交易类型id", "path": "data.recordList.transactionTypeId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "transactionTypeId_name": {"api_field_name": "transactionTypeId_name", "chinese_name": "交易类型名称", "data_type": "NVARCHAR(500)", "param_desc": "交易类型名称", "path": "data.recordList.transactionTypeId_name", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "agentId": {"api_field_name": "agentId", "chinese_name": "客户id", "data_type": "BIGINT", "param_desc": "客户id", "path": "data.recordList.agentId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "agentId_name": {"api_field_name": "agentId_name", "chinese_name": "客户名称", "data_type": "NVARCHAR(500)", "param_desc": "客户名称", "path": "data.recordList.agentId_name", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "receiveContacter": {"api_field_name": "receiveContacter", "chinese_name": "客户联系人", "data_type": "NVARCHAR(500)", "param_desc": "客户联系人", "path": "data.recordList.receiveContacter", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "receiveContacterPhone": {"api_field_name": "receiveContacterPhone", "chinese_name": "客户联系人电话", "data_type": "NVARCHAR(500)", "param_desc": "客户联系人电话", "path": "data.recordList.receiveContacterPhone", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "receievInvoiceMobile": {"api_field_name": "receievInvoiceMobile", "chinese_name": "收票手机号", "data_type": "NVARCHAR(500)", "param_desc": "收票手机号", "path": "data.recordList.receievInvoiceMobile", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "receievInvoiceEmail": {"api_field_name": "receievInvoiceEmail", "chinese_name": "收票邮箱", "data_type": "NVARCHAR(500)", "param_desc": "收票邮箱", "path": "data.recordList.receievInvoiceEmail", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "purchaseNo": {"api_field_name": "purchaseNo", "chinese_name": "客户采购订单号", "data_type": "NVARCHAR(500)", "param_desc": "客户采购订单号", "path": "data.recordList.purchaseNo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "corpContact": {"api_field_name": "corpContact", "chinese_name": "销售业务员id", "data_type": "NVARCHAR(500)", "param_desc": "销售业务员id", "path": "data.recordList.corpContact", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "corpContactUserName": {"api_field_name": "corpContactUserName", "chinese_name": "销售业务员", "data_type": "NVARCHAR(500)", "param_desc": "销售业务员", "path": "data.recordList.corpContactUserName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "settlementOrgId_name": {"api_field_name": "settlementOrgId_name", "chinese_name": "开票组织名称", "data_type": "NVARCHAR(500)", "param_desc": "开票组织名称", "path": "data.recordList.settlementOrgId_name", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "corpContactUserErpCode": {"api_field_name": "corpContactUserErpCode", "chinese_name": "业务员erp编码", "data_type": "NVARCHAR(500)", "param_desc": "业务员erp编码", "path": "data.recordList.corpContactUserErpCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderPrices": {"api_field_name": "orderPrices", "chinese_name": "订单金额", "data_type": "NVARCHAR(MAX)", "param_desc": "订单金额", "path": "data.sumRecordList.orderPrices", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currency": {"api_field_name": "currency", "chinese_name": "币种id", "data_type": "NVARCHAR(500)", "param_desc": "币种id", "path": "data.recordList.orderPrices.currency", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currency_priceDigit": {"api_field_name": "currency_priceDigit", "chinese_name": "原币单价精度", "data_type": "BIGINT", "param_desc": "原币单价精度", "path": "data.recordList.orderPrices.currency_priceDigit", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "currency_moneyDigit": {"api_field_name": "currency_moneyDigit", "chinese_name": "原币金额精度", "data_type": "BIGINT", "param_desc": "原币金额精度", "path": "data.recordList.orderPrices.currency_moneyDigit", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "originalName": {"api_field_name": "originalName", "chinese_name": "币种", "data_type": "NVARCHAR(500)", "param_desc": "币种", "path": "data.recordList.orderPrices.originalName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natCurrency": {"api_field_name": "natCurrency", "chinese_name": "本币pk", "data_type": "NVARCHAR(500)", "param_desc": "本币pk", "path": "data.recordList.orderPrices.natCurrency", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_priceDigit": {"api_field_name": "natCurrency_priceDigit", "chinese_name": "本币单价精度", "data_type": "BIGINT", "param_desc": "本币单价精度", "path": "data.recordList.orderPrices.natCurrency_priceDigit", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_moneyDigit": {"api_field_name": "natCurrency_moneyDigit", "chinese_name": "本币金额精度", "data_type": "BIGINT", "param_desc": "本币金额精度", "path": "data.recordList.orderPrices.natCurrency_moneyDigit", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "domesticCode": {"api_field_name": "domesticCode", "chinese_name": "本币简称", "data_type": "NVARCHAR(500)", "param_desc": "本币简称", "path": "data.recordList.orderPrices.domesticCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "domesticName": {"api_field_name": "domesticName", "chinese_name": "本币", "data_type": "NVARCHAR(500)", "param_desc": "本币", "path": "data.recordList.orderPrices.domesticName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "exchRate": {"api_field_name": "exchRate", "chinese_name": "汇率", "data_type": "BIGINT", "param_desc": "汇率", "path": "data.recordList.orderPrices.exchRate", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "exchangeRateType_name": {"api_field_name": "exchangeRateType_name", "chinese_name": "汇率类型名称", "data_type": "NVARCHAR(500)", "param_desc": "汇率类型名称", "path": "data.recordList.orderPrices.exchangeRateType_name", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "exchangeRateType": {"api_field_name": "exchangeRateType", "chinese_name": "汇率类型Idid", "data_type": "NVARCHAR(500)", "param_desc": "汇率类型Idid", "path": "data.recordList.orderPrices.exchangeRateType", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "ctTplId": {"api_field_name": "ctTplId", "chinese_name": "合同模板id", "data_type": "NVARCHAR(500)", "param_desc": "合同模板id", "path": "data.recordList.orderPrices.ctTplId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "ctTplCode": {"api_field_name": "ctTplCode", "chinese_name": "合同模板编码", "data_type": "NVARCHAR(500)", "param_desc": "合同模板编码", "path": "data.recordList.orderPrices.ctTplCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "ctTplName": {"api_field_name": "ctTplName", "chinese_name": "合同模板", "data_type": "NVARCHAR(500)", "param_desc": "合同模板", "path": "data.recordList.orderPrices.ctTplName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "signFileId": {"api_field_name": "signFileId", "chinese_name": "待签署合同文件", "data_type": "NVARCHAR(500)", "param_desc": "待签署合同文件", "path": "data.recordList.orderPrices.signFileId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "signStatus": {"api_field_name": "signStatus", "chinese_name": "电子签署状态", "data_type": "NVARCHAR(500)", "param_desc": "电子签署状态", "path": "data.recordList.orderPrices.signStatus", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "payMoney": {"api_field_name": "payMoney", "chinese_name": "合计含税金额", "data_type": "NVARCHAR(500)", "param_desc": "合计含税金额", "path": "data.recordList.payMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderPayMoney": {"api_field_name": "orderPayMoney", "chinese_name": "合计商品实付金额", "data_type": "NVARCHAR(500)", "param_desc": "合计商品实付金额", "path": "data.sumRecordList.orderPayMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "realMoney": {"api_field_name": "realMoney", "chinese_name": "合计应收金额", "data_type": "NVARCHAR(500)", "param_desc": "合计应收金额", "path": "data.sumRecordList.realMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderRealMoney": {"api_field_name": "orderRealMoney", "chinese_name": "合计商品应付金额", "data_type": "NVARCHAR(500)", "param_desc": "合计商品应付金额", "path": "data.sumRecordList.orderRealMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "statusCode": {"api_field_name": "statusCode", "chinese_name": "订单当前状态码", "data_type": "NVARCHAR(500)", "param_desc": "订单当前状态码", "path": "data.recordList.statusCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "nextStatus": {"api_field_name": "nextStatus", "chinese_name": "订单状态, CONFIRMORDER:开立、DELIVERY_PART:部分发货、DELIVERY_TAKE_PART:部分发货待收货、DELIVERGOODS:待发货、TAKEDELIVERY:待收货、ENDORDER:已完成、OPPOSE:已取消、APPROVING:审批中、", "data_type": "NVARCHAR(500)", "param_desc": "订单状态, CONFIRMORDER:开立、DELIVERY_PART:部分发货、DELIVERY_TAKE_PART:部分发货待收货、DELIVERGOODS:待发货、TAKEDELIVERY:待收货、ENDORDER:已完成、OPPOSE:已取消、APPROVING:审批中、", "path": "data.recordList.nextStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currentStatus": {"api_field_name": "currentStatus", "chinese_name": "当前状态位置", "data_type": "BIGINT", "param_desc": "当前状态位置", "path": "data.recordList.currentStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "payStatusCode": {"api_field_name": "payStatusCode", "chinese_name": "付款状态, NOTPAYMENT:未付款、PARTPAYMENT:部分付款、CONFIRMPAYMENT:部分付款待确认、CONFIRMPAYMENT_ALL:付款待确认、FINISHPAYMENT:付款完成、", "data_type": "NVARCHAR(500)", "param_desc": "付款状态, NOTPAYMENT:未付款、PARTPAYMENT:部分付款、CONFIRMPAYMENT:部分付款待确认、CONFIRMPAYMENT_ALL:付款待确认、FINISHPAYMENT:付款完成、", "path": "data.recordList.payStatusCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "settlementOrgId": {"api_field_name": "settlementOrgId", "chinese_name": "财务组织id", "data_type": "NVARCHAR(500)", "param_desc": "财务组织id", "path": "data.recordList.settlementOrgId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "lockIn": {"api_field_name": "lockIn", "chinese_name": "标记锁", "data_type": "BIT", "param_desc": "标记锁", "path": "data.recordList.lockIn", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "confirmDate": {"api_field_name": "confirmDate", "chinese_name": "订单确认时间", "data_type": "NVARCHAR(500)", "param_desc": "订单确认时间", "path": "data.recordList.confirmDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "payDate": {"api_field_name": "payDate", "chinese_name": "订单付款时间", "data_type": "NVARCHAR(500)", "param_desc": "订单付款时间", "path": "data.recordList.payDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderPayType": {"api_field_name": "orderPayType", "chinese_name": "支付方式", "data_type": "NVARCHAR(500)", "param_desc": "支付方式", "path": "data.recordList.orderPayType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "settlement": {"api_field_name": "settlement", "chinese_name": "结算方式id", "data_type": "NVARCHAR(500)", "param_desc": "结算方式id", "path": "data.recordList.settlement", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "shippingChoiceId": {"api_field_name": "shippingChoiceId", "chinese_name": "发运方式id", "data_type": "NVARCHAR(500)", "param_desc": "发运方式id", "path": "data.recordList.shippingChoiceId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "sendDate": {"api_field_name": "sendDate", "chinese_name": "预计发货日期", "data_type": "NVARCHAR(500)", "param_desc": "预计发货日期", "path": "data.recordList.sendDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "hopeReceiveDate": {"api_field_name": "hopeReceiveDate", "chinese_name": "期望收货日期", "data_type": "NVARCHAR(500)", "param_desc": "期望收货日期", "path": "data.recordList.hopeReceiveDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "opposeMemo": {"api_field_name": "opposeMemo", "chinese_name": "驳回批注", "data_type": "NVARCHAR(500)", "param_desc": "驳回批注", "path": "data.recordList.opposeMemo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "haveDelivery": {"api_field_name": "haveDelivery", "chinese_name": "是否存在发货单", "data_type": "BIT", "param_desc": "是否存在发货单", "path": "data.recordList.haveDelivery", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "occupyInventory": {"api_field_name": "occupyInventory", "chinese_name": "库存占用时机标识", "data_type": "NVARCHAR(500)", "param_desc": "库存占用时机标识", "path": "data.recordList.occupyInventory", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "separatePromotionType": {"api_field_name": "separatePromotionType", "chinese_name": "拆单规则标识", "data_type": "NVARCHAR(500)", "param_desc": "拆单规则标识", "path": "data.recordList.separatePromotionType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "reight": {"api_field_name": "reight", "chinese_name": "运费", "data_type": "NVARCHAR(500)", "param_desc": "运费", "path": "data.recordList.reight", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "synSourceOrg": {"api_field_name": "synSourceOrg", "chinese_name": "协同来源组织id", "data_type": "NVARCHAR(500)", "param_desc": "协同来源组织id", "path": "data.recordList.synSourceOrg", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "synSourceTenant": {"api_field_name": "synSourceTenant", "chinese_name": "协同来源租户", "data_type": "NVARCHAR(500)", "param_desc": "协同来源租户", "path": "data.recordList.synSourceTenant", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "synSourceOrg_name": {"api_field_name": "synSourceOrg_name", "chinese_name": "协同来源组织名称", "data_type": "NVARCHAR(500)", "param_desc": "协同来源组织名称", "path": "data.recordList.synSourceOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalMoney": {"api_field_name": "totalMoney", "chinese_name": "总金额", "data_type": "NVARCHAR(500)", "param_desc": "总金额", "path": "data.recordList.totalMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "tagName": {"api_field_name": "tagName", "chinese_name": "采购组织弹框", "data_type": "NVARCHAR(500)", "param_desc": "采购组织弹框", "path": "data.recordList.tagName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "rebateCashMoney": {"api_field_name": "rebateCashMoney", "chinese_name": "抵现金额", "data_type": "NVARCHAR(500)", "param_desc": "抵现金额", "path": "data.recordList.rebateCashMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "particularlyMoney": {"api_field_name": "particularly<PERSON><PERSON>", "chinese_name": "特殊优惠", "data_type": "NVARCHAR(500)", "param_desc": "特殊优惠", "path": "data.recordList.particularlyMoney", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "promotionMoney": {"api_field_name": "promotionMoney", "chinese_name": "促销", "data_type": "NVARCHAR(500)", "param_desc": "促销", "path": "data.recordList.promotionMoney", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unConfirmPrice": {"api_field_name": "unConfirmPrice", "chinese_name": "未审核的金额", "data_type": "NVARCHAR(500)", "param_desc": "未审核的金额", "path": "data.recordList.unConfirmPrice", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "confirmPrice": {"api_field_name": "confirmPrice", "chinese_name": "已支付金额", "data_type": "NVARCHAR(500)", "param_desc": "已支付金额", "path": "data.recordList.confirmPrice", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bizId": {"api_field_name": "bizId", "chinese_name": "商家id", "data_type": "NVARCHAR(500)", "param_desc": "商家id", "path": "data.recordList.bizId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bizName": {"api_field_name": "bizName", "chinese_name": "商家名称", "data_type": "NVARCHAR(500)", "param_desc": "商家名称", "path": "data.recordList.bizName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "agentRelationId": {"api_field_name": "agentRelationId", "chinese_name": "客户交易关系id", "data_type": "BIGINT", "param_desc": "客户交易关系id", "path": "data.recordList.agentRelationId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "points": {"api_field_name": "points", "chinese_name": "积分", "data_type": "NVARCHAR(500)", "param_desc": "积分", "path": "data.recordList.points", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pubts": {"api_field_name": "pubts", "chinese_name": "时间戳,格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "时间戳,格式为:yyyy-MM-dd HH:mm:ss", "path": "data.recordList.pubts", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "pubuts": {"api_field_name": "pubuts", "chinese_name": "时间戳", "data_type": "NVARCHAR(500)", "param_desc": "时间戳", "path": "data.recordList.pubuts", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderInvoice": {"api_field_name": "orderInvoice", "chinese_name": "发票信息", "data_type": "NVARCHAR(500)", "param_desc": "发票信息", "path": "data.recordList.orderInvoice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderShippingAddress": {"api_field_name": "orderShippingAddress", "chinese_name": "收货地址信息", "data_type": "NVARCHAR(500)", "param_desc": "收货地址信息", "path": "data.recordList.orderShippingAddress", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderErp": {"api_field_name": "orderErp", "chinese_name": "订单erp", "data_type": "NVARCHAR(500)", "param_desc": "订单erp", "path": "data.recordList.orderErp", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "deliveryDate": {"api_field_name": "deliveryDate", "chinese_name": "交货日期", "data_type": "NVARCHAR(500)", "param_desc": "交货日期", "path": "data.recordList.deliveryDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isWfControlled": {"api_field_name": "isWfControlled", "chinese_name": "是否审批流控制", "data_type": "BIT", "param_desc": "是否审批流控制", "path": "data.recordList.isWfControlled", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "verifystate": {"api_field_name": "verifystate", "chinese_name": "审批状态", "data_type": "BIGINT", "param_desc": "审批状态", "path": "data.recordList.verifystate", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "status": {"api_field_name": "status", "chinese_name": "状态", "data_type": "BIGINT", "param_desc": "状态", "path": "data.recordList.status", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "headItem": {"api_field_name": "headItem", "chinese_name": "表头自定义项", "data_type": "NVARCHAR(MAX)", "param_desc": "表头自定义项", "path": "data.recordList.headItem", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define1": {"api_field_name": "define1", "chinese_name": "表体自定义项1-60", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项1-60", "path": "data.recordList.bodyItem.define1", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isFinishDelivery": {"api_field_name": "isFinishDelivery", "chinese_name": "订单是否发完货", "data_type": "BIT", "param_desc": "订单是否发完货", "path": "data.recordList.isFinishDelivery", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productId_pbatchName": {"api_field_name": "productId_pbatchName", "chinese_name": "商品包装单位", "data_type": "BIGINT", "param_desc": "商品包装单位", "path": "data.recordList.productId_pbatchName", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "idKey": {"api_field_name": "id<PERSON><PERSON>", "chinese_name": "行标识", "data_type": "NVARCHAR(500)", "param_desc": "行标识", "path": "data.recordList.idKey", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productId": {"api_field_name": "productId", "chinese_name": "商品id", "data_type": "BIGINT", "param_desc": "商品id", "path": "data.recordList.productId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "priceMark": {"api_field_name": "priceMark", "chinese_name": "价格标识", "data_type": "BIT", "param_desc": "价格标识", "path": "data.recordList.priceMark", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isBatchManage": {"api_field_name": "isBatchManage", "chinese_name": "是否批次管理", "data_type": "BIT", "param_desc": "是否批次管理", "path": "data.recordList.isBatchManage", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isExpiryDateManage": {"api_field_name": "isExpiryDateManage", "chinese_name": "是否有效期管理", "data_type": "BIT", "param_desc": "是否有效期管理", "path": "data.recordList.isExpiryDateManage", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "expireDateNo": {"api_field_name": "expireDateNo", "chinese_name": "保质期", "data_type": "NVARCHAR(500)", "param_desc": "保质期", "path": "data.recordList.expireDateNo", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "expireDateUnit": {"api_field_name": "expireDateUnit", "chinese_name": "保质期单位", "data_type": "NVARCHAR(500)", "param_desc": "保质期单位", "path": "data.recordList.expireDateUnit", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "skuId": {"api_field_name": "skuId", "chinese_name": "商品SKUid", "data_type": "BIGINT", "param_desc": "商品SKUid", "path": "data.recordList.skuId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "erpCode": {"api_field_name": "erpCode", "chinese_name": "skuERP编码", "data_type": "NVARCHAR(500)", "param_desc": "skuERP编码", "path": "data.recordList.erpCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderProductType": {"api_field_name": "orderProductType", "chinese_name": "商品售卖类型, SALE:销售品、GIFT:赠品、MARKUP:加价购、REBATE:返利商品、", "data_type": "NVARCHAR(500)", "param_desc": "商品售卖类型, SALE:销售品、GIFT:赠品、MARKUP:加价购、REBATE:返利商品、", "path": "data.recordList.orderProductType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productCode": {"api_field_name": "productCode", "chinese_name": "商品编码", "data_type": "NVARCHAR(500)", "param_desc": "商品编码", "path": "data.recordList.productCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productName": {"api_field_name": "productName", "chinese_name": "商品名称", "data_type": "NVARCHAR(500)", "param_desc": "商品名称", "path": "data.recordList.productName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "skuCode": {"api_field_name": "skuCode", "chinese_name": "SKU编码", "data_type": "NVARCHAR(500)", "param_desc": "SKU编码", "path": "data.recordList.skuCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "specDescription": {"api_field_name": "specDescription", "chinese_name": "规格描述", "data_type": "NVARCHAR(500)", "param_desc": "规格描述", "path": "data.recordList.specDescription", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "projectId": {"api_field_name": "projectId", "chinese_name": "项目id", "data_type": "NVARCHAR(500)", "param_desc": "项目id", "path": "data.recordList.projectId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unitExchangeType": {"api_field_name": "unitExchangeType", "chinese_name": "浮动（计价）", "data_type": "BIGINT", "param_desc": "浮动（计价）", "path": "data.recordList.unitExchangeType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unitExchangeTypePrice": {"api_field_name": "unitExchangeTypePrice", "chinese_name": "浮动（销售）", "data_type": "BIGINT", "param_desc": "浮动（销售）", "path": "data.recordList.unitExchangeTypePrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productAuxUnitName": {"api_field_name": "productAuxUnitName", "chinese_name": "销售单位", "data_type": "NVARCHAR(500)", "param_desc": "销售单位", "path": "data.recordList.productAuxUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invExchRate": {"api_field_name": "invExchRate", "chinese_name": "销售换算率", "data_type": "NVARCHAR(500)", "param_desc": "销售换算率", "path": "data.recordList.invExchRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "subQty": {"api_field_name": "subQty", "chinese_name": "合计销售数量", "data_type": "NVARCHAR(500)", "param_desc": "合计销售数量", "path": "data.sumRecordList.subQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productUnitName": {"api_field_name": "productUnitName", "chinese_name": "计价单位", "data_type": "NVARCHAR(500)", "param_desc": "计价单位", "path": "data.recordList.productUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invPriceExchRate": {"api_field_name": "invPriceExchRate", "chinese_name": "计价换算率", "data_type": "NVARCHAR(500)", "param_desc": "计价换算率", "path": "data.recordList.invPriceExchRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "priceQty": {"api_field_name": "priceQty", "chinese_name": "合计计价数量", "data_type": "NVARCHAR(500)", "param_desc": "合计计价数量", "path": "data.sumRecordList.priceQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "qtyName": {"api_field_name": "qtyName", "chinese_name": "主计量", "data_type": "NVARCHAR(500)", "param_desc": "主计量", "path": "data.recordList.qtyName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "qty": {"api_field_name": "qty", "chinese_name": "合计数量", "data_type": "NVARCHAR(500)", "param_desc": "合计数量", "path": "data.sumRecordList.qty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "variantconfigctsId": {"api_field_name": "variantconfigctsId", "chinese_name": "选配结果清单id", "data_type": "NVARCHAR(500)", "param_desc": "选配结果清单id", "path": "data.recordList.variantconfigctsId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderDetailPrices": {"api_field_name": "orderDetailPrices", "chinese_name": "订单详情金额", "data_type": "NVARCHAR(MAX)", "param_desc": "订单详情金额", "path": "data.sumRecordList.orderDetailPrices", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "saleCost_orig_taxfree": {"api_field_name": "saleCost_orig_taxfree", "chinese_name": "原币无税合计", "data_type": "NVARCHAR(500)", "param_desc": "原币无税合计", "path": "data.recordList.orderDetailPrices.saleCost_orig_taxfree", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriUnitPrice": {"api_field_name": "oriUnitPrice", "chinese_name": "无税成交价", "data_type": "NVARCHAR(500)", "param_desc": "无税成交价", "path": "data.recordList.orderDetailPrices.oriUnitPrice", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriMoney": {"api_field_name": "oriMoney", "chinese_name": "合计无税金额", "data_type": "NVARCHAR(500)", "param_desc": "合计无税金额", "path": "data.sumRecordList.orderDetailPrices.oriMoney", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "oriTax": {"api_field_name": "oriTax", "chinese_name": "合计税额", "data_type": "NVARCHAR(500)", "param_desc": "合计税额", "path": "data.sumRecordList.orderDetailPrices.oriTax", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natSum": {"api_field_name": "natSum", "chinese_name": "合计本币含税金额", "data_type": "NVARCHAR(500)", "param_desc": "合计本币含税金额", "path": "data.sumRecordList.orderDetailPrices.natSum", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natTaxUnitPrice": {"api_field_name": "natTaxUnitPrice", "chinese_name": "本币含税单价", "data_type": "NVARCHAR(500)", "param_desc": "本币含税单价", "path": "data.recordList.orderDetailPrices.natTaxUnitPrice", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natMoney": {"api_field_name": "natMoney", "chinese_name": "合计本币无税金额", "data_type": "NVARCHAR(500)", "param_desc": "合计本币无税金额", "path": "data.sumRecordList.orderDetailPrices.natMoney", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natUnitPrice": {"api_field_name": "natUnitPrice", "chinese_name": "本币无税单价", "data_type": "NVARCHAR(500)", "param_desc": "本币无税单价", "path": "data.recordList.orderDetailPrices.natUnitPrice", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natTax": {"api_field_name": "natTax", "chinese_name": "合计本币税额", "data_type": "NVARCHAR(500)", "param_desc": "合计本币税额", "path": "data.sumRecordList.orderDetailPrices.natTax", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderDetailId": {"api_field_name": "orderDetailId", "chinese_name": "主体ID", "data_type": "BIGINT", "param_desc": "主体ID", "path": "data.recordList.orderDetailId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "salePrice_orig_taxfree": {"api_field_name": "salePrice_orig_taxfree", "chinese_name": "无税单价", "data_type": "NVARCHAR(500)", "param_desc": "无税单价", "path": "data.recordList.orderDetailPrices.salePrice_orig_taxfree", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "rebateMoneyOrigTaxfree": {"api_field_name": "rebateMoneyOrigTaxfree", "chinese_name": "无税分摊返利", "data_type": "NVARCHAR(500)", "param_desc": "无税分摊返利", "path": "data.recordList.orderDetailPrices.rebateMoneyOrigTaxfree", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "particularlyMoneyOrigTaxfree": {"api_field_name": "particularlyMoneyOrigTaxfree", "chinese_name": "无税特殊优惠", "data_type": "NVARCHAR(500)", "param_desc": "无税特殊优惠", "path": "data.recordList.orderDetailPrices.particularlyMoneyOrigTaxfree", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "promotionMoneyOrigTaxfree": {"api_field_name": "promotionMoneyOrigTaxfree", "chinese_name": "无税促销优惠", "data_type": "NVARCHAR(500)", "param_desc": "无税促销优惠", "path": "data.recordList.orderDetailPrices.promotionMoneyOrigTaxfree", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pointsMoneyOrigTaxfree": {"api_field_name": "pointsMoneyOrigTaxfree", "chinese_name": "无税积分抵扣", "data_type": "NVARCHAR(500)", "param_desc": "无税积分抵扣", "path": "data.recordList.orderDetailPrices.pointsMoneyOrigTaxfree", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "saleCost_domestic": {"api_field_name": "saleCost_domestic", "chinese_name": "报价本币含税金额", "data_type": "NVARCHAR(500)", "param_desc": "报价本币含税金额", "path": "data.recordList.orderDetailPrices.saleCost_domestic", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "salePrice_domestic": {"api_field_name": "salePrice_domestic", "chinese_name": "报价本币含税单价", "data_type": "NVARCHAR(500)", "param_desc": "报价本币含税单价", "path": "data.recordList.orderDetailPrices.salePrice_domestic", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "rebateMoneyDomestic": {"api_field_name": "rebateMoneyDomestic", "chinese_name": "本币分摊返利", "data_type": "NVARCHAR(500)", "param_desc": "本币分摊返利", "path": "data.recordList.orderDetailPrices.rebateMoneyDomestic", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "particularlyMoneyDomestic": {"api_field_name": "particularlyMoneyDomestic", "chinese_name": "本币特殊优惠", "data_type": "NVARCHAR(500)", "param_desc": "本币特殊优惠", "path": "data.recordList.orderDetailPrices.particularlyMoneyDomestic", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "promotionMoneyDomestic": {"api_field_name": "promotionMoneyDomestic", "chinese_name": "本币促销优惠", "data_type": "NVARCHAR(500)", "param_desc": "本币促销优惠", "path": "data.recordList.orderDetailPrices.promotionMoneyDomestic", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pointsMoneyDomestic": {"api_field_name": "pointsMoneyDomestic", "chinese_name": "本币积分抵扣", "data_type": "NVARCHAR(500)", "param_desc": "本币积分抵扣", "path": "data.recordList.orderDetailPrices.pointsMoneyDomestic", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "saleCost_domestic_taxfree": {"api_field_name": "saleCost_domestic_taxfree", "chinese_name": "报价本币无税金额", "data_type": "NVARCHAR(500)", "param_desc": "报价本币无税金额", "path": "data.recordList.orderDetailPrices.saleCost_domestic_taxfree", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "salePrice_domestic_taxfree": {"api_field_name": "salePrice_domestic_taxfree", "chinese_name": "报价本币无税单价", "data_type": "NVARCHAR(500)", "param_desc": "报价本币无税单价", "path": "data.recordList.orderDetailPrices.salePrice_domestic_taxfree", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "rebateMoneyDomesticTaxfree": {"api_field_name": "rebateMoneyDomesticTaxfree", "chinese_name": "本币无税分摊返利", "data_type": "NVARCHAR(500)", "param_desc": "本币无税分摊返利", "path": "data.recordList.orderDetailPrices.rebateMoneyDomesticTaxfree", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "particularlyMoneyDomesticTaxfree": {"api_field_name": "particularlyMoneyDomesticTaxfree", "chinese_name": "本币无税特殊优惠", "data_type": "NVARCHAR(500)", "param_desc": "本币无税特殊优惠", "path": "data.recordList.orderDetailPrices.particularlyMoneyDomesticTaxfree", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "promotionMoneyDomesticTaxfree": {"api_field_name": "promotionMoneyDomesticTaxfree", "chinese_name": "本币无税促销优惠", "data_type": "NVARCHAR(500)", "param_desc": "本币无税促销优惠", "path": "data.recordList.orderDetailPrices.promotionMoneyDomesticTaxfree", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pointsMoneyDomesticTaxfree": {"api_field_name": "pointsMoneyDomesticTaxfree", "chinese_name": "本币无税积分抵扣", "data_type": "NVARCHAR(500)", "param_desc": "本币无税积分抵扣", "path": "data.recordList.orderDetailPrices.pointsMoneyDomesticTaxfree", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "prepayInvRvnRecogBkgMeth": {"api_field_name": "prepayInvRvnRecogBkgMeth", "chinese_name": "预收款开票应收入账方式 (1: 预收款开票-税额记应收 ; 2 : 预收款开票-全额记应收 )", "data_type": "NVARCHAR(500)", "param_desc": "预收款开票应收入账方式 (1: 预收款开票-税额记应收 ; 2 : 预收款开票-全额记应收 )", "path": "data.recordList.orderDetailPrices.prepayInvRvnRecogBkgMeth", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "checkByRevenueManagement": {"api_field_name": "checkByRevenueManagement", "chinese_name": "收入管理核算", "data_type": "NVARCHAR(500)", "param_desc": "收入管理核算", "path": "data.recordList.orderDetailPrices.checkByRevenueManagement", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "revPerformObligation": {"api_field_name": "revPerformObligation", "chinese_name": "已生成收入履约义务", "data_type": "NVARCHAR(500)", "param_desc": "已生成收入履约义务", "path": "data.recordList.orderDetailPrices.revPerformObligation", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "serviceStartDate": {"api_field_name": "serviceStartDate", "chinese_name": "服务起始日期", "data_type": "NVARCHAR(500)", "param_desc": "服务起始日期", "path": "data.recordList.orderDetailPrices.serviceStartDate", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "serviceEndDate": {"api_field_name": "serviceEndDate", "chinese_name": "服务结束日期", "data_type": "NVARCHAR(500)", "param_desc": "服务结束日期", "path": "data.recordList.orderDetailPrices.serviceEndDate", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "optionalQuotationId": {"api_field_name": "optionalQuotationId", "chinese_name": "报价配置清单ID", "data_type": "NVARCHAR(500)", "param_desc": "报价配置清单ID", "path": "data.recordList.orderDetailPrices.optionalQuotationId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "optionalQuotationId_code": {"api_field_name": "optionalQuotationId_code", "chinese_name": "报价配置清单编码", "data_type": "NVARCHAR(500)", "param_desc": "报价配置清单编码", "path": "data.recordList.orderDetailPrices.optionalQuotationId_code", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "variantconfigctsCode": {"api_field_name": "variantconfigctsCode", "chinese_name": "配置号", "data_type": "NVARCHAR(500)", "param_desc": "配置号", "path": "data.recordList.orderDetailPrices.variantconfigctsCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "variantconfigctsVersion": {"api_field_name": "variantconfigctsVersion", "chinese_name": "配置清单版本", "data_type": "NVARCHAR(500)", "param_desc": "配置清单版本", "path": "data.recordList.orderDetailPrices.variantconfigctsVersion", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "calBase": {"api_field_name": "calBase", "chinese_name": "计算基准", "data_type": "NVARCHAR(500)", "param_desc": "计算基准", "path": "data.recordList.orderDetailPrices.calBase", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockName": {"api_field_name": "stockName", "chinese_name": "发货仓库", "data_type": "NVARCHAR(500)", "param_desc": "发货仓库", "path": "data.recordList.stockName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockOrgId_name": {"api_field_name": "stockOrgId_name", "chinese_name": "库存组织", "data_type": "NVARCHAR(500)", "param_desc": "库存组织", "path": "data.recordList.stockOrgId_name", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "consignTime": {"api_field_name": "consignTime", "chinese_name": "计划发货日期", "data_type": "NVARCHAR(500)", "param_desc": "计划发货日期", "path": "data.recordList.consignTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "projectId_name": {"api_field_name": "projectId_name", "chinese_name": "项目名称", "data_type": "NVARCHAR(500)", "param_desc": "项目名称", "path": "data.recordList.projectId_name", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "projectId_code": {"api_field_name": "projectId_code", "chinese_name": "项目编码", "data_type": "NVARCHAR(500)", "param_desc": "项目编码", "path": "data.recordList.projectId_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "noTaxSalePrice": {"api_field_name": "noTaxSalePrice", "chinese_name": "无税报价", "data_type": "NVARCHAR(500)", "param_desc": "无税报价", "path": "data.recordList.noTaxSalePrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "salePrice": {"api_field_name": "salePrice", "chinese_name": "含税报价", "data_type": "NVARCHAR(500)", "param_desc": "含税报价", "path": "data.recordList.salePrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "taxId": {"api_field_name": "taxId", "chinese_name": "数目税率id", "data_type": "NVARCHAR(500)", "param_desc": "数目税率id", "path": "data.recordList.taxId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "costCurrencyName": {"api_field_name": "costCurrencyName", "chinese_name": "成本币种", "data_type": "NVARCHAR(500)", "param_desc": "成本币种", "path": "data.recordList.costCurrencyName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "costAmt": {"api_field_name": "costAmt", "chinese_name": "成本金额", "data_type": "NVARCHAR(500)", "param_desc": "成本金额", "path": "data.recordList.costAmt", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "costPrice": {"api_field_name": "costPrice", "chinese_name": "成本价", "data_type": "NVARCHAR(500)", "param_desc": "成本价", "path": "data.recordList.costPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "noTaxSaleCost": {"api_field_name": "noTaxSaleCost", "chinese_name": "合计报价无税金额", "data_type": "NVARCHAR(500)", "param_desc": "合计报价无税金额", "path": "data.sumRecordList.noTaxSaleCost", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "saleCost": {"api_field_name": "saleCost", "chinese_name": "合计报价含税金额", "data_type": "NVARCHAR(500)", "param_desc": "合计报价含税金额", "path": "data.sumRecordList.saleCost", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockId": {"api_field_name": "stockId", "chinese_name": "仓库ID", "data_type": "NVARCHAR(500)", "param_desc": "仓库ID", "path": "data.recordList.stockId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "oriTaxUnitPrice": {"api_field_name": "oriTaxUnitPrice", "chinese_name": "含税成交价", "data_type": "NVARCHAR(500)", "param_desc": "含税成交价", "path": "data.recordList.oriTaxUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "lineno": {"api_field_name": "lineno", "chinese_name": "行号", "data_type": "BIGINT", "param_desc": "行号", "path": "data.recordList.lineno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderDetails_stockOrgId": {"api_field_name": "orderDetails_stockOrgId", "chinese_name": "库存组织id", "data_type": "NVARCHAR(500)", "param_desc": "库存组织id", "path": "data.recordList.orderDetails_stockOrgId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "oriSum": {"api_field_name": "oriSum", "chinese_name": "合计含税金额", "data_type": "NVARCHAR(500)", "param_desc": "合计含税金额", "path": "data.sumRecordList.oriSum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "taxRate": {"api_field_name": "taxRate", "chinese_name": "税率", "data_type": "NVARCHAR(500)", "param_desc": "税率", "path": "data.recordList.taxRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "taxItems": {"api_field_name": "taxItems", "chinese_name": "税目", "data_type": "NVARCHAR(500)", "param_desc": "税目", "path": "data.recordList.taxItems", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "taxCode": {"api_field_name": "taxCode", "chinese_name": "税目税率编码", "data_type": "NVARCHAR(500)", "param_desc": "税目税率编码", "path": "data.recordList.taxCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "rebateMoney": {"api_field_name": "rebateMoney", "chinese_name": "合计返利分摊金额", "data_type": "NVARCHAR(500)", "param_desc": "合计返利分摊金额", "path": "data.sumRecordList.rebateMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "pointsMoney": {"api_field_name": "pointsMoney", "chinese_name": "积分", "data_type": "NVARCHAR(500)", "param_desc": "积分", "path": "data.recordList.pointsMoney", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "shoppingCartId": {"api_field_name": "shoppingCartId", "chinese_name": "购物车id", "data_type": "NVARCHAR(500)", "param_desc": "购物车id", "path": "data.recordList.shoppingCartId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "groupId": {"api_field_name": "groupId", "chinese_name": "分组Id", "data_type": "BIGINT", "param_desc": "分组Id", "path": "data.recordList.groupId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "rebateReturnProductId": {"api_field_name": "rebateReturnProductId", "chinese_name": "返货单商品id", "data_type": "NVARCHAR(500)", "param_desc": "返货单商品id", "path": "data.recordList.rebateReturnProductId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "mutualActivities": {"api_field_name": "mutualActivities", "chinese_name": "活动的对象,用于校验互斥活动", "data_type": "NVARCHAR(500)", "param_desc": "活动的对象,用于校验互斥活动", "path": "data.recordList.mutualActivities", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "activities": {"api_field_name": "activities", "chinese_name": "包含的类型,用于校验互斥", "data_type": "NVARCHAR(500)", "param_desc": "包含的类型,用于校验互斥", "path": "data.recordList.activities", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "sendPayMoney": {"api_field_name": "send<PERSON>ayMoney", "chinese_name": "合计发货金额", "data_type": "NVARCHAR(500)", "param_desc": "合计发货金额", "path": "data.sumRecordList.sendPayMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invoiceQty": {"api_field_name": "invoiceQty", "chinese_name": "合计累计开票数量", "data_type": "NVARCHAR(500)", "param_desc": "合计累计开票数量", "path": "data.sumRecordList.invoiceQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invoiceOriSum": {"api_field_name": "invoiceOriSum", "chinese_name": "累计开票含税金额", "data_type": "NVARCHAR(500)", "param_desc": "累计开票含税金额", "path": "data.recordList.invoiceOriSum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bizProductId": {"api_field_name": "bizProductId", "chinese_name": "商家商品id", "data_type": "BIGINT", "param_desc": "商家商品id", "path": "data.recordList.bizProductId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "takeQuantity": {"api_field_name": "takeQuantity", "chinese_name": "已审核收数量", "data_type": "NVARCHAR(500)", "param_desc": "已审核收数量", "path": "data.recordList.takeQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bizSkuId": {"api_field_name": "bizSkuId", "chinese_name": "商家skuid", "data_type": "NVARCHAR(500)", "param_desc": "商家skuid", "path": "data.recordList.bizSkuId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "takeSalePayMoney": {"api_field_name": "takeSalePayMoney", "chinese_name": "已审核收金额", "data_type": "NVARCHAR(500)", "param_desc": "已审核收金额", "path": "data.recordList.takeSalePayMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orderDetailPrice": {"api_field_name": "orderDetailPrice", "chinese_name": "订单金额", "data_type": "NVARCHAR(500)", "param_desc": "订单金额", "path": "data.recordList.orderDetailPrice", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "sendQty": {"api_field_name": "sendQty", "chinese_name": "合计累计已发货数量", "data_type": "NVARCHAR(500)", "param_desc": "合计累计已发货数量", "path": "data.sumRecordList.sendQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "closedSumMoney": {"api_field_name": "closedSumMoney", "chinese_name": "关闭总金额", "data_type": "NVARCHAR(500)", "param_desc": "关闭总金额", "path": "data.recordList.closedSumMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "closedRowCount": {"api_field_name": "closedRowCount", "chinese_name": "行关闭数量", "data_type": "NVARCHAR(500)", "param_desc": "行关闭数量", "path": "data.recordList.closedRowCount", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "iDeleted": {"api_field_name": "iDeleted", "chinese_name": "是否删除", "data_type": "BIT", "param_desc": "是否删除", "path": "data.recordList.iDeleted", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "iOrgId": {"api_field_name": "iOrgId", "chinese_name": "组织ID", "data_type": "NVARCHAR(500)", "param_desc": "组织ID", "path": "data.recordList.iOrgId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "memo": {"api_field_name": "memo", "chinese_name": "备注", "data_type": "NVARCHAR(500)", "param_desc": "备注", "path": "data.recordList.memo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "createDate": {"api_field_name": "createDate", "chinese_name": "创建日期", "data_type": "NVARCHAR(500)", "param_desc": "创建日期", "path": "data.recordList.createDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "creatorId": {"api_field_name": "creatorId", "chinese_name": "创建人", "data_type": "BIGINT", "param_desc": "创建人", "path": "data.recordList.creatorId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auditorId": {"api_field_name": "auditorId", "chinese_name": "审核人ID", "data_type": "NVARCHAR(500)", "param_desc": "审核人ID", "path": "data.recordList.auditorId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auditDate": {"api_field_name": "auditDate", "chinese_name": "审批日期", "data_type": "NVARCHAR(500)", "param_desc": "审批日期", "path": "data.recordList.auditDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "closerId": {"api_field_name": "closerId", "chinese_name": "关闭人ID", "data_type": "NVARCHAR(500)", "param_desc": "关闭人ID", "path": "data.recordList.closerId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "closeDate": {"api_field_name": "closeDate", "chinese_name": "关闭日期", "data_type": "NVARCHAR(500)", "param_desc": "关闭日期", "path": "data.recordList.closeDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "modifierId": {"api_field_name": "modifierId", "chinese_name": "修改人id", "data_type": "NVARCHAR(500)", "param_desc": "修改人id", "path": "data.recordList.modifierId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "modifyDate": {"api_field_name": "modifyDate", "chinese_name": "修改日期", "data_type": "NVARCHAR(500)", "param_desc": "修改日期", "path": "data.recordList.modifyDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "cCreator": {"api_field_name": "cCreator", "chinese_name": "创建人", "data_type": "NVARCHAR(500)", "param_desc": "创建人", "path": "data.recordList.cCreator", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "iProductAuxUnitId": {"api_field_name": "iProductAuxUnitId", "chinese_name": "销售单位id", "data_type": "BIGINT", "param_desc": "销售单位id", "path": "data.recordList.iProductAuxUnitId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "iProductUnitId": {"api_field_name": "iProductUnitId", "chinese_name": "计价单位id", "data_type": "BIGINT", "param_desc": "计价单位id", "path": "data.recordList.iProductUnitId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "masterUnitId": {"api_field_name": "masterUnitId", "chinese_name": "主计量单位id", "data_type": "BIGINT", "param_desc": "主计量单位id", "path": "data.recordList.masterUnitId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purUOM_Precision": {"api_field_name": "purUOM_Precision", "chinese_name": "销售单位精度", "data_type": "BIGINT", "param_desc": "销售单位精度", "path": "data.recordList.purUOM_Precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "priceUOM_Precision": {"api_field_name": "priceUOM_Precision", "chinese_name": "计价单位精度", "data_type": "BIGINT", "param_desc": "计价单位精度", "path": "data.recordList.priceUOM_Precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unit_Precision": {"api_field_name": "unit_Precision", "chinese_name": "主计量单位精度", "data_type": "BIGINT", "param_desc": "主计量单位精度", "path": "data.recordList.unit_Precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "cBizName": {"api_field_name": "cBizName", "chinese_name": "供应商名称", "data_type": "NVARCHAR(500)", "param_desc": "供应商名称", "path": "data.recordList.cBizName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderId": {"api_field_name": "orderId", "chinese_name": "订单ID", "data_type": "BIGINT", "param_desc": "订单ID", "path": "data.recordList.orderId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bodyItem": {"api_field_name": "bodyItem", "chinese_name": "表体自定义项", "data_type": "NVARCHAR(MAX)", "param_desc": "表体自定义项", "path": "data.recordList.bodyItem", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define60": {"api_field_name": "define60", "chinese_name": "表体自定义项60", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项60", "path": "data.recordList.bodyItem.define60", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "creator": {"api_field_name": "creator", "chinese_name": "创建人", "data_type": "NVARCHAR(500)", "param_desc": "创建人", "path": "data.recordList.creator", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "createTime": {"api_field_name": "createTime", "chinese_name": "创建时间", "data_type": "NVARCHAR(500)", "param_desc": "创建时间", "path": "data.recordList.createTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auditor": {"api_field_name": "auditor", "chinese_name": "审批人", "data_type": "NVARCHAR(500)", "param_desc": "审批人", "path": "data.recordList.auditor", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "auditTime": {"api_field_name": "auditTime", "chinese_name": "审批时间", "data_type": "NVARCHAR(500)", "param_desc": "审批时间", "path": "data.recordList.auditTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "closeTime": {"api_field_name": "closeTime", "chinese_name": "关闭时间", "data_type": "NVARCHAR(500)", "param_desc": "关闭时间", "path": "data.recordList.closeTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "closer": {"api_field_name": "closer", "chinese_name": "关闭人", "data_type": "NVARCHAR(500)", "param_desc": "关闭人", "path": "data.recordList.closer", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "modifier": {"api_field_name": "modifier", "chinese_name": "修改人", "data_type": "NVARCHAR(500)", "param_desc": "修改人", "path": "data.recordList.modifier", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "modifyTime": {"api_field_name": "modifyTime", "chinese_name": "修改时间", "data_type": "NVARCHAR(500)", "param_desc": "修改时间", "path": "data.recordList.modifyTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bmake_st_salesout": {"api_field_name": "bmake_st_salesout", "chinese_name": "流程入库", "data_type": "NVARCHAR(500)", "param_desc": "流程入库", "path": "data.recordList.bmake_st_salesout", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bmake_voucher_delivery": {"api_field_name": "bmake_voucher_delivery", "chinese_name": "流程发货", "data_type": "NVARCHAR(500)", "param_desc": "流程发货", "path": "data.recordList.bmake_voucher_delivery", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bizFlow": {"api_field_name": "bizFlow", "chinese_name": "流程ID", "data_type": "BIGINT", "param_desc": "流程ID", "path": "data.recordList.bizFlow", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bmake_voucher_saleinvoice": {"api_field_name": "bmake_voucher_saleinvoice", "chinese_name": "流程开票", "data_type": "NVARCHAR(500)", "param_desc": "流程开票", "path": "data.recordList.bmake_voucher_saleinvoice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isFlowCoreBill": {"api_field_name": "isFlowCoreBill", "chinese_name": "是否流程核心单据", "data_type": "BIT", "param_desc": "是否流程核心单据", "path": "data.recordList.isFlowCoreBill", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bizFlow_version": {"api_field_name": "bizFlow_version", "chinese_name": "版本信息", "data_type": "NVARCHAR(500)", "param_desc": "版本信息", "path": "data.recordList.bizFlow_version", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchNo": {"api_field_name": "batchNo", "chinese_name": "批次号", "data_type": "NVARCHAR(500)", "param_desc": "批次号", "path": "data.recordList.batchNo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productDate": {"api_field_name": "productDate", "chinese_name": "生产日期", "data_type": "NVARCHAR(500)", "param_desc": "生产日期", "path": "data.recordList.productDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invalidDate": {"api_field_name": "invalidDate", "chinese_name": "有效期至", "data_type": "NVARCHAR(500)", "param_desc": "有效期至", "path": "data.recordList.invalidDate", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isAdvRecInv": {"api_field_name": "isAdvRecInv", "chinese_name": "预收款开票", "data_type": "BIT", "param_desc": "预收款开票", "path": "data.recordList.isAdvRecInv", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "advRecInvMoney": {"api_field_name": "advRecInvMoney", "chinese_name": "累计预收款开票金额", "data_type": "NVARCHAR(500)", "param_desc": "累计预收款开票金额", "path": "data.sumRecordList.advRecInvMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "advRecInvQty": {"api_field_name": "advRecInvQty", "chinese_name": "累计预收款开票数量", "data_type": "NVARCHAR(500)", "param_desc": "累计预收款开票数量", "path": "data.sumRecordList.advRecInvQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natAdvRecInvMoney": {"api_field_name": "natAdvRecInvMoney", "chinese_name": "累计预收款开票本币金额", "data_type": "NVARCHAR(500)", "param_desc": "累计预收款开票本币金额", "path": "data.sumRecordList.natAdvRecInvMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "advRecInvTaxMoney": {"api_field_name": "advRecInvTaxMoney", "chinese_name": "累计预收款开票税额", "data_type": "NVARCHAR(500)", "param_desc": "累计预收款开票税额", "path": "data.sumRecordList.advRecInvTaxMoney", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natAdvRecInvTaxMoney": {"api_field_name": "natAdvRecInvTaxMoney", "chinese_name": "累计预收款开票本币税额", "data_type": "NVARCHAR(500)", "param_desc": "累计预收款开票本币税额", "path": "data.sumRecordList.natAdvRecInvTaxMoney", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "offsetAdvRecInvMoney": {"api_field_name": "offsetAdvRecInvMoney", "chinese_name": "累计冲抵预收款开票金额", "data_type": "NVARCHAR(500)", "param_desc": "累计冲抵预收款开票金额", "path": "data.sumRecordList.offsetAdvRecInvMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "offsetAdvRecInvQty": {"api_field_name": "offsetAdvRecInvQty", "chinese_name": "累计冲抵预收款开票数量", "data_type": "NVARCHAR(500)", "param_desc": "累计冲抵预收款开票数量", "path": "data.sumRecordList.offsetAdvRecInvQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "offsetNatAdvRecInvMoney": {"api_field_name": "offsetNatAdvRecInvMoney", "chinese_name": "累计冲抵预收款开票本币金额", "data_type": "NVARCHAR(500)", "param_desc": "累计冲抵预收款开票本币金额", "path": "data.sumRecordList.offsetNatAdvRecInvMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "offsetAdvRecInvTaxMoney": {"api_field_name": "offsetAdvRecInvTaxMoney", "chinese_name": "累计冲抵预收款开票税额", "data_type": "NVARCHAR(500)", "param_desc": "累计冲抵预收款开票税额", "path": "data.sumRecordList.offsetAdvRecInvTaxMoney", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "offsetNatAdvRecInvTaxMoney": {"api_field_name": "offsetNatAdvRecInvTaxMoney", "chinese_name": "累计冲抵预收款开票本币税额", "data_type": "NVARCHAR(500)", "param_desc": "累计冲抵预收款开票本币税额", "path": "data.sumRecordList.offsetNatAdvRecInvTaxMoney", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "tradeRouteID": {"api_field_name": "tradeRouteID", "chinese_name": "贸易路径ID", "data_type": "NVARCHAR(500)", "param_desc": "贸易路径ID", "path": "data.recordList.tradeRouteID", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "tradeRouteID_code": {"api_field_name": "tradeRouteID_code", "chinese_name": "贸易路径编码", "data_type": "NVARCHAR(500)", "param_desc": "贸易路径编码", "path": "data.recordList.tradeRouteID_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "tradeRouteID_name": {"api_field_name": "tradeRouteID_name", "chinese_name": "贸易路径", "data_type": "NVARCHAR(500)", "param_desc": "贸易路径", "path": "data.recordList.tradeRouteID_name", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isEndTrade": {"api_field_name": "isEndTrade", "chinese_name": "是否末级站点", "data_type": "BIGINT", "param_desc": "是否末级站点", "path": "data.recordList.isEndTrade", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "tradeRouteLineno": {"api_field_name": "tradeRouteLineno", "chinese_name": "站点", "data_type": "NVARCHAR(500)", "param_desc": "站点", "path": "data.recordList.tradeRouteLineno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "collaborationPocode": {"api_field_name": "collaborationPocode", "chinese_name": "协同来源单据号", "data_type": "NVARCHAR(500)", "param_desc": "协同来源单据号", "path": "data.recordList.collaborationPocode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "collaborationPodetailid": {"api_field_name": "collaborationPodetailid", "chinese_name": "协同来源单据子表id", "data_type": "NVARCHAR(500)", "param_desc": "协同来源单据子表id", "path": "data.recordList.collaborationPodetailid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "collaborationPoid": {"api_field_name": "collaborationPoid", "chinese_name": "协同来源单据主表id", "data_type": "NVARCHAR(500)", "param_desc": "协同来源单据主表id", "path": "data.recordList.collaborationPoid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "collaborationPorowno": {"api_field_name": "collaborationPorowno", "chinese_name": "协同来源单据行号", "data_type": "NVARCHAR(500)", "param_desc": "协同来源单据行号", "path": "data.recordList.collaborationPorowno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "collaborationSource": {"api_field_name": "collaborationSource", "chinese_name": "协同来源单据类型", "data_type": "NVARCHAR(500)", "param_desc": "协同来源单据类型", "path": "data.recordList.collaborationSource", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "entireDeliveryStatus": {"api_field_name": "entireDeliveryStatus", "chinese_name": "整单发货状态", "data_type": "NVARCHAR(500)", "param_desc": "整单发货状态", "path": "data.recordList.entireDeliveryStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "entireIssueStatus": {"api_field_name": "entireIssueStatus", "chinese_name": "整单出库状态", "data_type": "NVARCHAR(500)", "param_desc": "整单出库状态", "path": "data.recordList.entireIssueStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "entireInvoiceStatus": {"api_field_name": "entireInvoiceStatus", "chinese_name": "整单发票状态", "data_type": "NVARCHAR(500)", "param_desc": "整单发票状态", "path": "data.recordList.entireInvoiceStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "entireSignConfirmStatus": {"api_field_name": "entireSignConfirmStatus", "chinese_name": "整单签收状态", "data_type": "NVARCHAR(500)", "param_desc": "整单签收状态", "path": "data.recordList.entireSignConfirmStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "deliveryStatus": {"api_field_name": "deliveryStatus", "chinese_name": "发货状态", "data_type": "NVARCHAR(500)", "param_desc": "发货状态", "path": "data.recordList.deliveryStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "issueStatus": {"api_field_name": "issueStatus", "chinese_name": "出库状态", "data_type": "NVARCHAR(500)", "param_desc": "出库状态", "path": "data.recordList.issueStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invoiceStatus": {"api_field_name": "invoiceStatus", "chinese_name": "发票状态", "data_type": "NVARCHAR(500)", "param_desc": "发票状态", "path": "data.recordList.invoiceStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "signConfirmStatus": {"api_field_name": "signConfirmStatus", "chinese_name": "签收状态", "data_type": "NVARCHAR(500)", "param_desc": "签收状态", "path": "data.recordList.signConfirmStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "sumRecordList": {"api_field_name": "sumRecordList", "chinese_name": "合计", "data_type": "NVARCHAR(MAX)", "param_desc": "合计", "path": "data.sumRecordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderPurchaseQty": {"api_field_name": "orderPurchaseQty", "chinese_name": "合计累计采购数量", "data_type": "NVARCHAR(500)", "param_desc": "合计累计采购数量", "path": "data.sumRecordList.orderPurchaseQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "collectMoney": {"api_field_name": "collectMoney", "chinese_name": "合计累计收款金额", "data_type": "NVARCHAR(500)", "param_desc": "合计累计收款金额", "path": "data.sumRecordList.collectMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "sendPriceQty": {"api_field_name": "sendPriceQty", "chinese_name": "合计累计已发货计价数量", "data_type": "NVARCHAR(500)", "param_desc": "合计累计已发货计价数量", "path": "data.sumRecordList.sendPriceQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "coordinationQuantity": {"api_field_name": "coordinationQuantity", "chinese_name": "合计社会化协同量", "data_type": "NVARCHAR(500)", "param_desc": "合计社会化协同量", "path": "data.sumRecordList.coordinationQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalOutStockOriTaxMoney": {"api_field_name": "totalOutStockOriTaxMoney", "chinese_name": "合计累计出库金额", "data_type": "NVARCHAR(500)", "param_desc": "合计累计出库金额", "path": "data.sumRecordList.totalOutStockOriTaxMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "prodCost": {"api_field_name": "prodCost", "chinese_name": "合计商品报价金额", "data_type": "NVARCHAR(500)", "param_desc": "合计商品报价金额", "path": "data.sumRecordList.prodCost", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalOutStockOriMoney": {"api_field_name": "totalOutStockOriMoney", "chinese_name": "合计累计出库计价数量", "data_type": "NVARCHAR(500)", "param_desc": "合计累计出库计价数量", "path": "data.sumRecordList.totalOutStockOriMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalOutStockPriceQty": {"api_field_name": "totalOutStockPriceQty", "chinese_name": "合计累计出库计价数量", "data_type": "NVARCHAR(500)", "param_desc": "合计累计出库计价数量", "path": "data.sumRecordList.totalOutStockPriceQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "payMoneyOrigTaxfree": {"api_field_name": "payMoneyOrigTaxfree", "chinese_name": "合计合计无税金额", "data_type": "NVARCHAR(500)", "param_desc": "合计合计无税金额", "path": "data.sumRecordList.payMoneyOrigTaxfree", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auditCount": {"api_field_name": "auditCount", "chinese_name": "合计累计发货已审数量", "data_type": "NVARCHAR(500)", "param_desc": "合计累计发货已审数量", "path": "data.sumRecordList.auditCount", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "returnQty": {"api_field_name": "returnQty", "chinese_name": "合计退货数量", "data_type": "NVARCHAR(500)", "param_desc": "合计退货数量", "path": "data.sumRecordList.returnQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalOutStockQuantity": {"api_field_name": "totalOutStockQuantity", "chinese_name": "合计累计出库数量", "data_type": "NVARCHAR(500)", "param_desc": "合计累计出库数量", "path": "data.sumRecordList.totalOutStockQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "cashRebateMoney": {"api_field_name": "cashRebateMoney", "chinese_name": "合计返利直接抵现", "data_type": "NVARCHAR(500)", "param_desc": "合计返利直接抵现", "path": "data.sumRecordList.cashRebateMoney", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderRebateMoney": {"api_field_name": "orderRebateMoney", "chinese_name": "合计返利整单折扣", "data_type": "NVARCHAR(500)", "param_desc": "合计返利整单折扣", "path": "data.sumRecordList.orderRebateMoney", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "orderDetails_rebateMoney": {"api_field_name": "orderDetails_rebateMoney", "chinese_name": "合计返利分摊金额", "data_type": "NVARCHAR(500)", "param_desc": "合计返利分摊金额", "path": "data.sumRecordList.orderDetails_rebateMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "cusDiscountMoney": {"api_field_name": "cusDiscountMoney", "chinese_name": "合计客户扣额", "data_type": "NVARCHAR(500)", "param_desc": "合计客户扣额", "path": "data.sumRecordList.cusDiscountMoney", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "returnPreSendQty": {"api_field_name": "returnPreSendQty", "chinese_name": "合计退货待发数量", "data_type": "NVARCHAR(500)", "param_desc": "合计退货待发数量", "path": "data.sumRecordList.returnPreSendQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalOutStockSubQty": {"api_field_name": "totalOutStockSubQty", "chinese_name": "合计累计出库件数", "data_type": "NVARCHAR(500)", "param_desc": "合计累计出库件数", "path": "data.sumRecordList.totalOutStockSubQty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "lineDiscountMoney": {"api_field_name": "lineDiscountMoney", "chinese_name": "合计扣额", "data_type": "NVARCHAR(500)", "param_desc": "合计扣额", "path": "data.sumRecordList.orderDetailPrices.lineDiscountMoney", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalOutStockConfirmQuantity": {"api_field_name": "totalOutStockConfirmQuantity", "chinese_name": "累计出库确认数量", "data_type": "NVARCHAR(500)", "param_desc": "累计出库确认数量", "path": "data.sumRecordList.orderDetailPrices.totalOutStockConfirmQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalOutStockConfirmSubQty": {"api_field_name": "totalOutStockConfirmSubQty", "chinese_name": "累计出库确认件数", "data_type": "NVARCHAR(500)", "param_desc": "累计出库确认件数", "path": "data.sumRecordList.orderDetailPrices.totalOutStockConfirmSubQty", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageCount": {"api_field_name": "pageCount", "chinese_name": "总共记录数", "data_type": "BIGINT", "param_desc": "总共记录数", "path": "data.pageCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "beginPageIndex": {"api_field_name": "beginPageIndex", "chinese_name": "页码列表的开始索引", "data_type": "BIGINT", "param_desc": "页码列表的开始索引", "path": "data.beginPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "endPageIndex": {"api_field_name": "endPageIndex", "chinese_name": "页码列表的结束索引", "data_type": "BIGINT", "param_desc": "页码列表的结束索引", "path": "data.endPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "nextStatusName": {"api_field_name": "nextStatusName", "chinese_name": "订单状态, CONFIRMORDER:开立、DELIVERY_PART:部分发货、DELIVERY_TAKE_PART:部分发货待收货、DELIVERGOODS:待发货、TAKEDELIVERY:待收货、ENDORDER:已完成、OPPOSE:已取消、APPROVING:审批中、", "data_type": "NVARCHAR(500)", "param_desc": "订单状态, CONFIRMORDER:开立、DELIVERY_PART:部分发货、DELIVERY_TAKE_PART:部分发货待收货、DELIVERGOODS:待发货、TAKEDELIVERY:待收货、ENDORDER:已完成、OPPOSE:已取消、APPROVING:审批中、", "path": "nextStatusName", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_orderDate_begin": {"api_field_name": "open_orderDate_begin", "chinese_name": "制单日期开始时间,格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "制单日期开始时间,格式为:yyyy-MM-dd HH:mm:ss", "path": "open_orderDate_begin", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_orderDate_end": {"api_field_name": "open_orderDate_end", "chinese_name": "制单结束时间,格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "制单结束时间,格式为:yyyy-MM-dd HH:mm:ss", "path": "open_orderDate_end", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_hopeReceiveDate_begin": {"api_field_name": "open_hopeReceiveDate_begin", "chinese_name": "期望收货开始时间,格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "期望收货开始时间,格式为:yyyy-MM-dd HH:mm:ss", "path": "open_hopeReceiveDate_begin", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_hopeReceiveDate_end": {"api_field_name": "open_hopeReceiveDate_end", "chinese_name": "期望收货截止,格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "期望收货截止,格式为:yyyy-MM-dd HH:mm:ss", "path": "open_hopeReceiveDate_end", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_vouchdate_begin": {"api_field_name": "open_vouchdate_begin", "chinese_name": "单据开始时间,格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "单据开始时间,格式为:yyyy-MM-dd HH:mm:ss", "path": "open_vouchdate_begin", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_vouchdate_end": {"api_field_name": "open_vouchdate_end", "chinese_name": "单据截止时间,格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "单据截止时间,格式为:yyyy-MM-dd HH:mm:ss", "path": "open_vouchdate_end", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isSum": {"api_field_name": "isSum", "chinese_name": "查询表头", "data_type": "BIT", "param_desc": "查询表头", "path": "isSum", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "simpleVOs": {"api_field_name": "simpleVOs", "chinese_name": "查询条件", "data_type": "NVARCHAR(MAX)", "param_desc": "查询条件", "path": "simpleVOs", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "op": {"api_field_name": "op", "chinese_name": "比较符(eq:等于;neq:不等于;lt:小于;gt:大于;like:模糊匹配;between:介于)", "data_type": "NVARCHAR(500)", "param_desc": "比较符(eq:等于;neq:不等于;lt:小于;gt:大于;like:模糊匹配;between:介于)", "path": "simpleVOs.op", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value1": {"api_field_name": "value1", "chinese_name": "查询条件值1", "data_type": "NVARCHAR(500)", "param_desc": "查询条件值1", "path": "simpleVOs.value1", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "field": {"api_field_name": "field", "chinese_name": "排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：orderDetails.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)    示例：id", "data_type": "NVARCHAR(500)", "param_desc": "排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：orderDetails.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)    示例：id", "path": "queryOrders.field", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "logicOp": {"api_field_name": "logicOp", "chinese_name": "分级逻辑符(and,or)", "data_type": "NVARCHAR(500)", "param_desc": "分级逻辑符(and,or)", "path": "simpleVOs.logicOp", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value2": {"api_field_name": "value2", "chinese_name": "查询条件值2", "data_type": "NVARCHAR(500)", "param_desc": "查询条件值2", "path": "simpleVOs.value2", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "queryOrders": {"api_field_name": "queryOrders", "chinese_name": "排序字段", "data_type": "NVARCHAR(MAX)", "param_desc": "排序字段", "path": "queryOrders", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "order": {"api_field_name": "order", "chinese_name": "顺序:正序(asc);倒序(desc) 示例：asc", "data_type": "NVARCHAR(500)", "param_desc": "顺序:正序(asc);倒序(desc) 示例：asc", "path": "queryOrders.order", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}}}