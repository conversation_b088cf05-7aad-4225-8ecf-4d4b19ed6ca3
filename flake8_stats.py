#!/usr/bin/env python3
"""
统计Flake8错误数量和类型
"""

import re
import subprocess
from collections import Counter


def get_flake8_stats():
    """获取flake8错误统计"""
    try:
        result = subprocess.run(
            [
                "python",
                "-m",
                "flake8",
                ".",
                "--exclude=__pycache__,logs,temp_cleanup,.git",
            ],
            capture_output=True,
            text=True,
            encoding="utf-8",
        )

        if result.returncode == 0:
            print("🎉 没有发现任何Flake8错误！")
            return

        # 解析错误
        errors = result.stdout.strip().split("\n")
        error_counts = Counter()
        severe_errors = []

        for error in errors:
            if error.strip():
                # 提取错误代码 (如 E501, F821等)
                match = re.search(r"[EFW]\d+", error)
                if match:
                    error_code = match.group()
                    error_counts[error_code] += 1

                    # 记录严重错误
                    if error_code in ["E999", "F821", "F401"]:
                        severe_errors.append(error)

        print("📊 Flake8错误统计:")
        print("-" * 50)
        print(f"总错误数: {len(errors)}")
        print("\n错误类型分布:")

        # 按错误代码分组显示
        for error_code, count in error_counts.most_common():
            error_desc = {
                "E501": "行长度超限",
                "E302": "缺少空行",
                "F401": "未使用的导入",
                "F841": "未使用的变量",
                "W293": "空行有空白字符",
                "E999": "语法错误",
                "F821": "未定义的名称",
                "E265": "注释格式",
                "F541": "f-string问题",
            }.get(error_code, "其他问题")

            print(f"  {error_code} ({error_desc}): {count}")

        if severe_errors:
            print(f"\n⚠️ 严重错误 (前5个):")
            for error in severe_errors[:5]:
                print(f"  {error}")

        print("\n🔧 建议修复顺序:")
        print("1. 先修复语法错误 (E999)")
        print("2. 再修复未定义名称 (F821)")
        print("3. 删除未使用导入 (F401)")
        print("4. 修复行长度 (E501)")

    except Exception as e:
        print(f"❌ 获取统计失败: {e}")


if __name__ == "__main__":
    get_flake8_stats()
