{"dashboard": {"id": null, "title": "YS-API V3.0 监控仪表板", "uid": "ysapi-v3-dashboard", "version": 1, "panels": [{"id": 1, "title": "请求率", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "请求/秒"}]}, {"id": 2, "title": "响应时间", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "histogram_quantile(0.95, http_request_duration_seconds_bucket)", "legendFormat": "95th 百分位"}]}]}}