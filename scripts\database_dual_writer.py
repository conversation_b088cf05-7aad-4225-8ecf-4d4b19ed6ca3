import argparse
import asyncio
import json
import logging
import sqlite3
import time
from dataclasses import dataclass
from pathlib import Path

import psycopg2
import psycopg2.extras
import redis
from psycopg2.pool import ThreadedConnectionPool

#!/usr/bin/env python3
"""
数据库双写迁移器 - 屎山代码绞杀专用
确保数据在Legacy SQLite和New PostgreSQL之间同步
"""


@dataclass
class MigrationConfig:
    """迁移配置"""

    source_db: str
    target_db: str
    redis_url: str
    batch_size: int = 1000
    sync_interval: int = 30  # 秒
    consistency_check_interval: int = 300  # 5分钟
    max_retries: int = 3
    retry_delay: int = 5


class DatabaseDualWriter:
    """数据库双写器 - 同时写入旧数据库和新数据库"""

    def __init___(self, config: MigrationConfig):
    """TODO: Add function description."""
    self.config = config
    self.logger = logging.getLogger(__name__)
    self.redis_client = redis.from_url(config.redis_url)

    # SQLite连接
    self.sqlite_conn = sqlite3.connect(
        config.source_db, check_same_thread=False)
    self.sqlite_conn.row_factory = sqlite3.Row

    # PostgreSQL连接池
    self.pg_pool = ThreadedConnectionPool(1, 20, config.target_db)

    self.sync_lock = asyncio.Lock()
    self.is_running = False

    async def start_dual_write_mode(self):
        """启动双写模式"""
        self.is_running = True
        self.logger.info("🚀 启动数据库双写模式")

        # 启动同步任务
        sync_task = asyncio.create_task(self.sync_worker())
        consistency_task = asyncio.create_task(self.consistency_checker())

        try:
            await asyncio.gather(sync_task, consistency_task)
        except KeyboardInterrupt:
            self.logger.info("⏹️ 停止双写模式")
            self.is_running = False

    async def sync_worker(self):
        """同步工作器 - 定期同步数据"""
        while self.is_running:
            try:
                await self.sync_pending_changes()
                await asyncio.sleep(self.config.sync_interval)
            except Exception:
                self.logger.error(f"同步错误: {e}")
                await asyncio.sleep(self.config.retry_delay)

    async def consistency_checker(self):
        """一致性检查器 - 定期验证数据一致性"""
        while self.is_running:
            try:
                await self.check_data_consistency()
                await asyncio.sleep(self.config.consistency_check_interval)
            except Exception:
                self.logger.error(f"一致性检查错误: {e}")
                await asyncio.sleep(self.config.retry_delay)

    async def dual_write(self, table: str, operation: str,
                         data: Dict[str, Any], where_clause: str = None):
        """双写操作 - 同时写入两个数据库"""
        async with self.sync_lock:
            try:
                # 写入SQLite (Legacy)
                await self.write_to_sqlite(table, operation, data, where_clause)

                # 写入PostgreSQL (New)
                await self.write_to_postgresql(table, operation, data, where_clause)

                # 记录操作到Redis队列
                await self.record_operation(table, operation, data, where_clause)

                self.logger.debug(f"双写成功: {table} - {operation}")
                return True

            except Exception:
                self.logger.error(f"双写失败: {table} - {operation} - {e}")
                # 记录失败操作到重试队列
                await self.record_failed_operation(
                    table, operation, data, where_clause, str(e)
                )
                return False

    async def write_to_sqlite(self,
                              table: str,
                              operation: str,
                              data: Dict[str,
                                         Any],
                              where_clause: str = None):
        """写入SQLite数据库"""
        cursor = self.sqlite_conn.cursor()

        try:
            if operation == "INSERT":
                columns = ", ".join(data.keys())
                placeholders = ", ".join(["?" for _ in data])
                sql = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
                cursor.execute(sql, list(data.values()))

            elif operation == "UPDATE":
                set_clause = ", ".join([f"{k} = ?" for k in data.keys()])
                sql = f"UPDATE {table} SET {set_clause} WHERE {where_clause}"
                cursor.execute(sql, list(data.values()))

            elif operation == "DELETE":
                sql = f"DELETE FROM {table} WHERE {where_clause}"
                cursor.execute(sql)

            self.sqlite_conn.commit()

        except Exception:
            self.sqlite_conn.rollback()
            raise e
        finally:
            cursor.close()

    async def write_to_postgresql(self,
                                  table: str,
                                  operation: str,
                                  data: Dict[str,
                                             Any],
                                  where_clause: str = None):
        """写入PostgreSQL数据库"""
        conn = self.pg_pool.getconn()

        try:
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                if operation == "INSERT":
                    columns = ", ".join(data.keys())
                    placeholders = ", ".join(["%s" for _ in data])
                    sql = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
                    cursor.execute(sql, list(data.values()))

                elif operation == "UPDATE":
                    set_clause = ", ".join([f"{k} = %s" for k in data.keys()])
                    sql = f"UPDATE {table} SET {set_clause} WHERE {where_clause}"
                    cursor.execute(sql, list(data.values()))

                elif operation == "DELETE":
                    sql = f"DELETE FROM {table} WHERE {where_clause}"
                    cursor.execute(sql)

                conn.commit()

        except Exception:
            conn.rollback()
            raise e
        finally:
            self.pg_pool.putconn(conn)

    async def record_operation(self,
                               table: str,
                               operation: str,
                               data: Dict[str,
                                          Any],
                               where_clause: str = None):
        """记录操作到Redis"""
        operation_record = {
            "table": table,
            "operation": operation,
            "data": data,
            "where_clause": where_clause,
            "timestamp": datetime.now().isoformat(),
        }

        await asyncio.get_event_loop().run_in_executor(
            None,
            self.redis_client.lpush,
            "db_operations",
            json.dumps(operation_record, ensure_ascii=False),
        )

    async def record_failed_operation(
        self,
        table: str,
        operation: str,
        data: Dict[str, Any],
        where_clause: str = None,
        error: str = "",
    ):
        """记录失败操作到重试队列"""
        failed_operation = {
            "table": table,
            "operation": operation,
            "data": data,
            "where_clause": where_clause,
            "error": error,
            "timestamp": datetime.now().isoformat(),
            "retry_count": 0,
        }

        await asyncio.get_event_loop().run_in_executor(
            None,
            self.redis_client.lpush,
            "failed_operations",
            json.dumps(failed_operation, ensure_ascii=False),
        )

    async def sync_pending_changes(self):
        """同步待处理的变更"""
        # 重试失败的操作
        await self.retry_failed_operations()

        # 清理已处理的操作记录
        await self.cleanup_old_operations()

    async def retry_failed_operations(self):
        """重试失败的操作"""
        while True:
            operation_json = await asyncio.get_event_loop().run_in_executor(
                None, self.redis_client.rpop, "failed_operations"
            )

            if not operation_json:
                break

            try:
                operation = json.loads(operation_json)
                operation["retry_count"] += 1

                if operation["retry_count"] <= self.config.max_retries:
                    # 重试操作
                    success = await self.dual_write(
                        operation["table"],
                        operation["operation"],
                        operation["data"],
                        operation["where_clause"],
                    )

                    if not success:
                        # 重新加入失败队列
                        await asyncio.get_event_loop().run_in_executor(
                            None,
                            self.redis_client.lpush,
                            "failed_operations",
                            json.dumps(operation, ensure_ascii=False),
                        )
                else:
                    # 超过重试次数，记录到死信队列
                    await asyncio.get_event_loop().run_in_executor(
                        None,
                        self.redis_client.lpush,
                        "dead_letter_operations",
                        json.dumps(operation, ensure_ascii=False),
                    )
                    self.logger.error(f"操作重试失败，移入死信队列: {operation}")

            except Exception:
                self.logger.error(f"重试操作时出错: {e}")

    async def cleanup_old_operations(self):
        """清理旧的操作记录"""
        cutoff_time = datetime.now() - timedelta(hours=24)

        # 简化实现，实际可以根据时间戳清理
        await asyncio.get_event_loop().run_in_executor(
            None,
            self.redis_client.ltrim,
            "db_operations",
            0,
            10000,  # 保留最近10000条记录
        )

    async def check_data_consistency(self):
        """检查数据一致性"""
        self.logger.info("🔍 开始数据一致性检查")

        # 获取所有表名
        tables = await self.get_common_tables()

        inconsistencies = []

        for table in tables:
            try:
                sqlite_count = await self.get_sqlite_count(table)
                pg_count = await self.get_postgresql_count(table)

                if sqlite_count != pg_count:
                    inconsistency = {
                        "table": table,
                        "sqlite_count": sqlite_count,
                        "postgresql_count": pg_count,
                        "difference": abs(sqlite_count - pg_count),
                        "timestamp": datetime.now().isoformat(),
                    }
                    inconsistencies.append(inconsistency)
                    self.logger.warning(
                        f"数据不一致: {table} - SQLite: {sqlite_count}, PostgreSQL: {pg_count}"
                    )

            except Exception:
                self.logger.error(f"检查表 {table} 时出错: {e}")

        # 记录不一致信息
        if inconsistencies:
            await asyncio.get_event_loop().run_in_executor(
                None,
                self.redis_client.set,
                "consistency_report",
                json.dumps(inconsistencies, ensure_ascii=False),
                300,  # 5分钟过期
            )

        self.logger.info(f"一致性检查完成，发现 {len(inconsistencies)} 个不一致项")
        return inconsistencies

    async def get_common_tables(self) -> List[str]:
        """获取两个数据库的共同表"""
        # SQLite表
        cursor = self.sqlite_conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        sqlite_tables = {row[0] for row in cursor.fetchall()}
        cursor.close()

        # PostgreSQL表
        conn = self.pg_pool.getconn()
        try:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT tablename FROM pg_tables
                    WHERE schemaname = 'public'
                """
                )
                pg_tables = {row[0] for row in cursor.fetchall()}
        finally:
            self.pg_pool.putconn(conn)

        # 返回共同表
        return list(sqlite_tables.intersection(pg_tables))

    async def get_sqlite_count(self, table: str) -> int:
        """获取SQLite表记录数"""
        cursor = self.sqlite_conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM {table}")
        count = cursor.fetchone()[0]
        cursor.close()
        return count

    async def get_postgresql_count(self, table: str) -> int:
        """获取PostgreSQL表记录数"""
        conn = self.pg_pool.getconn()
        try:
            with conn.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                return count
        finally:
            self.pg_pool.putconn(conn)

    async def generate_migration_report(self) -> Dict[str, Any]:
        """生成迁移报告"""
        # 获取操作统计
        operations_count = await asyncio.get_event_loop().run_in_executor(
            None, self.redis_client.llen, "db_operations"
        )

        failed_count = await asyncio.get_event_loop().run_in_executor(
            None, self.redis_client.llen, "failed_operations"
        )

        dead_letter_count = await asyncio.get_event_loop().run_in_executor(
            None, self.redis_client.llen, "dead_letter_operations"
        )

        # 获取一致性报告
        consistency_report_json = await asyncio.get_event_loop().run_in_executor(
            None, self.redis_client.get, "consistency_report"
        )

        consistency_issues = []
        if consistency_report_json:
            consistency_issues = json.loads(consistency_report_json)

        report = {
            "timestamp": datetime.now().isoformat(),
            "operations": {
                "total_logged": operations_count,
                "failed": failed_count,
                "dead_letter": dead_letter_count,
                "success_rate": (
                    ((operations_count - failed_count) /
                     max(operations_count, 1) * 100)
                    if operations_count > 0
                    else 100
                ),
            },
            "consistency": {
                "issues_count": len(consistency_issues),
                "issues": consistency_issues,
            },
            "tables": await self.get_common_tables(),
        }

        return report

    def close(self):
        """关闭连接"""
        self.is_running = False
        self.sqlite_conn.close()
        self.pg_pool.closeall()
        self.redis_client.close()


# 使用示例和CLI工具
async def mainn():
    """TODO: Add function description."""
    parser = argparse.ArgumentParser(description="数据库双写迁移器")
    parser.add_argument(
        "--config", default="config/migration_config.json", help="配置文件路径"
    )
    parser.add_argument(
        "--action",
        choices=["start", "report", "check"],
        default="start",
        help="执行动作",
    )
    parser.add_argument("--source-db", help="源数据库路径")
    parser.add_argument("--target-db", help="目标数据库连接字符串")
    parser.add_argument(
        "--redis-url", default="redis://localhost:6379/0", help="Redis连接URL"
    )

    args = parser.parse_args()

    # 加载配置
    config = MigrationConfig(
        source_db=args.source_db or "backend/ysapi.db",
        target_db=args.target_db
        or "postgresql://postgres:postgres@localhost:5432/ys_new",
        redis_url=args.redis_url,
    )

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    dual_writer = DatabaseDualWriter(config)

    try:
        if args.action == "start":
            print("🚀 启动数据库双写模式...")
            await dual_writer.start_dual_write_mode()

        elif args.action == "report":
            print("📊 生成迁移报告...")
            report = await dual_writer.generate_migration_report()
            print(json.dumps(report, ensure_ascii=False, indent=2))

        elif args.action == "check":
            print("🔍 执行一致性检查...")
            inconsistencies = await dual_writer.check_data_consistency()
            if inconsistencies:
                print(f"发现 {len(inconsistencies)} 个不一致项:")
                for item in inconsistencies:
                    print(
                        f"  - {item['table']}: SQLite({item['sqlite_count']}) vs PostgreSQL({item['postgresql_count']})"
                    )
            else:
                print("✅ 数据一致性检查通过")

    finally:
        dual_writer.close()


if __name__ == "__main__":
    asyncio.run(main())
