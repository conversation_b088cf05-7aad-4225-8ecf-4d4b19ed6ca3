@echo off
REM YS-API V3.0 Windows 部署脚本

echo 🚀 开始部署 YS-API V3.0...

REM 检查 Docker 是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

REM 检查 Docker Compose 是否可用
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose 不可用
    pause
    exit /b 1
)

REM 备份当前部署
if exist "backup\\current_deployment.tar.gz" (
    echo 📦 备份当前部署...
    if not exist backup mkdir backup
    for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
    set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
    set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
    set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"
    tar -czf "backup\\deployment_%timestamp%.tar.gz" backend config docker-compose.yml 2>nul
)

REM 停止现有服务
echo 🛑 停止现有服务...
docker-compose down --remove-orphans

REM 清理旧镜像
echo 🧹 清理旧镜像...
docker image prune -f

REM 构建新镜像
echo 🔨 构建应用镜像...
docker-compose build
if %errorlevel% neq 0 (
    echo ❌ 镜像构建失败！
    pause
    exit /b 1
)

REM 启动服务
echo 🚀 启动服务...
docker-compose up -d
if %errorlevel% neq 0 (
    echo ❌ 服务启动失败！
    pause
    exit /b 1
)

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 30 /nobreak >nul

REM 健康检查
echo 🔍 执行健康检查...
for /l %%i in (1,1,10) do (
    curl -f http://localhost:5000/health >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ 服务启动成功！
        echo 🌐 应用访问地址: http://localhost:5000
        echo 📊 前端页面: http://localhost:5000/static/index.html
        pause
        exit /b 0
    )
    echo 等待服务响应... (%%i/10)
    timeout /t 10 /nobreak >nul
)

echo ❌ 服务启动失败！
docker-compose logs
pause
exit /b 1
