{"timestamp": "2025-08-04 00:10:34", "project_name": "YS-API V3.0", "overall_score": 61.1, "total_checks": 18, "passed_checks": 11, "failed_checks": 0, "warnings": [{"category": "代码质量", "check": "Python代码规范", "details": "cicd_pipeline_builder_optimized.py:760 使用print而非logging"}, {"category": "代码质量", "check": "Python代码规范", "details": "cicd_pipeline_builder_optimized.py:761 使用print而非logging"}, {"category": "代码质量", "check": "Python代码规范", "details": "cicd_pipeline_builder_optimized.py:762 使用print而非logging"}, {"category": "代码质量", "check": "Python代码规范", "details": "cicd_pipeline_builder_optimized.py:765 使用print而非logging"}, {"category": "代码质量", "check": "Python代码规范", "details": "cicd_pipeline_builder_optimized.py:767 使用print而非logging"}, {"category": "安全性", "check": "敏感信息检查", "details": "配置文件可能包含敏感信息: config.ini"}, {"category": "安全性", "check": "敏感信息检查", "details": "配置文件可能包含敏感信息: config.ini"}], "critical_issues": [], "detailed_results": {"代码质量": [{"check": "Python代码规范", "status": "WARN", "details": "cicd_pipeline_builder_optimized.py:760 使用print而非logging"}, {"check": "Python代码规范", "status": "WARN", "details": "cicd_pipeline_builder_optimized.py:761 使用print而非logging"}, {"check": "Python代码规范", "status": "WARN", "details": "cicd_pipeline_builder_optimized.py:762 使用print而非logging"}, {"check": "Python代码规范", "status": "WARN", "details": "cicd_pipeline_builder_optimized.py:765 使用print而非logging"}, {"check": "Python代码规范", "status": "WARN", "details": "cicd_pipeline_builder_optimized.py:767 使用print而非logging"}], "安全性": [{"check": "敏感信息检查", "status": "WARN", "details": "配置文件可能包含敏感信息: config.ini"}, {"check": "敏感信息检查", "status": "WARN", "details": "配置文件可能包含敏感信息: config.ini"}], "数据库": [{"check": "表结构", "status": "PASS", "details": "发现 5 个表"}], "API": [{"check": "健康检查", "status": "PASS", "details": "API服务正常运行"}], "配置": [{"check": "config.ini - database", "status": "PASS", "details": "配置节 database 存在"}, {"check": "config.ini - api", "status": "PASS", "details": "配置节 api 存在"}, {"check": "config.ini - database", "status": "PASS", "details": "配置节 database 存在"}, {"check": "config.ini - api", "status": "PASS", "details": "配置节 api 存在"}], "性能": [{"check": "文件大小", "status": "PASS", "details": "未发现异常大文件"}], "文档": [{"check": "README.md", "status": "PASS", "details": "文档存在"}, {"check": "项目架构文档.md", "status": "PASS", "details": "文档存在"}, {"check": "12-生产部署指南.md", "status": "PASS", "details": "文档存在"}, {"check": "05-API接口规范.md", "status": "PASS", "details": "文档存在"}]}}