# YS-API 接口规范文档

## 📋 概述

本文档定义了 YS-API V3.2 版本的所有 API 接口规范，包括字段配置、数据同步、监控等核心功能的接口设计。

## 🔧 基础信息

### 服务信息
- **服务地址**: `http://localhost:8000`
- **API 版本**: v1
- **基础路径**: `/api/v1`
- **内容类型**: `application/json`

### 通用响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-12-01T10:00:00Z"
}
```

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "details": {
    // 错误详情
  },
  "timestamp": "2024-12-01T10:00:00Z"
}
```

## 📊 字段配置 API

### 1. 获取模块字段配置

**接口**: `GET /api/v1/config/modules/{module_name}/fields`

**描述**: 获取指定模块的字段配置信息

**路径参数**:
- `module_name`: 模块名称 (string, required)

**查询参数**:
- `user_id`: 用户ID (string, required)
- `max_depth`: 最大深度 (integer, optional, default: 5)
- `sync_from_existing`: 是否从现有配置同步 (boolean, optional, default: false)

**响应示例**:
```json
{
  "success": true,
  "message": "字段配置获取成功",
  "data": {
    "module_name": "purchase_order",
    "display_name": "采购订单",
    "fields": {
      "code": {
        "api_field_name": "code",
        "chinese_name": "单据编码",
        "data_type": "NVARCHAR(50)",
        "sample_value": "CGDD250721001",
        "etl_score": 0.95,
        "config_name": "采购订单号",
        "is_selected": true,
        "locked": false
      }
    },
    "total_fields": 184,
    "selected_fields": 45
  }
}
```

### 2. 保存模块字段配置

**接口**: `PUT /api/v1/config/modules/{module_name}/fields`

**描述**: 保存指定模块的字段配置

**路径参数**:
- `module_name`: 模块名称 (string, required)

**请求体**:
```json
{
  "user_id": "Alice",
  "user_config": {
    "module_name": "purchase_order",
    "display_name": "采购订单",
    "fields": {
      "code": {
        "api_field_name": "code",
        "chinese_name": "单据编码",
        "data_type": "NVARCHAR(50)",
        "sample_value": "CGDD250721001",
        "etl_score": 0.95,
        "config_name": "采购订单号",
        "is_selected": true,
        "locked": false
      }
    }
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "字段配置保存成功",
  "data": {
    "saved_fields": 184,
    "selected_fields": 45
  }
}
```

### 3. 一键获取所有模块字段

**接口**: `POST /api/v1/config/fetch-all-modules`

**描述**: 一键获取所有15个模块的字段配置

**请求体**:
```json
{
  "user_id": "Alice",
  "max_depth": 5,
  "overwrite_existing": true
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "所有模块字段获取完成",
  "data": {
    "total_modules": 15,
    "success_count": 15,
    "error_count": 0,
    "results": {
      "purchase_order": "success",
      "sales_order": "success",
      "production_order": "success"
    }
  }
}
```

## 🔄 统一字段配置 API

### 1. 获取模块列表

**接口**: `GET /api/v1/unified/modules`

**描述**: 获取所有可用的业务模块列表

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "module_name": "purchase_order",
      "display_name": "采购订单",
      "field_count": 184,
      "has_config": true
    },
    {
      "module_name": "sales_order",
      "display_name": "销售订单",
      "field_count": 156,
      "has_config": true
    }
  ]
}
```

### 2. 获取统一字段配置

**接口**: `GET /api/v1/unified/modules/{module_name}/fields`

**描述**: 获取指定模块的统一字段配置

**路径参数**:
- `module_name`: 模块名称 (string, required)

**查询参数**:
- `user_id`: 用户ID (string, required)
- `sync_from_existing`: 是否从现有配置同步 (boolean, optional, default: true)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "module_name": "purchase_order",
    "display_name": "采购订单",
    "total_fields": 184,
    "selected_fields": 45,
    "fields": {
      "code": {
        "api_field_name": "code",
        "chinese_name": "单据编码",
        "data_type": "NVARCHAR(50)",
        "sample_value": "CGDD250721001",
        "business_importance": "critical",
        "is_selected": true,
        "locked": false,
        "user_modified": false
      }
    }
  }
}
```

### 3. 更新字段选择状态

**接口**: `PUT /api/v1/unified/modules/{module_name}/fields/{field_name}`

**描述**: 更新指定字段的选择状态

**路径参数**:
- `module_name`: 模块名称 (string, required)
- `field_name`: 字段名称 (string, required)

**查询参数**:
- `user_id`: 用户ID (string, required)
- `is_selected`: 是否选中 (boolean, required)

**响应示例**:
```json
{
  "success": true,
  "message": "字段状态更新成功",
  "data": {
    "field_name": "code",
    "is_selected": true,
    "user_modified": true
  }
}
```

### 4. 同步现有配置

**接口**: `POST /api/v1/unified/modules/{module_name}/sync`

**描述**: 同步现有配置到统一字段管理系统

**路径参数**:
- `module_name`: 模块名称 (string, required)

**响应示例**:
```json
{
  "success": true,
  "message": "配置同步成功",
  "data": {
    "synced_fields": 184,
    "new_fields": 12,
    "updated_fields": 45
  }
}
```

## 📊 数据库管理 API

### 1. 创建数据库表

**接口**: `POST /api/v1/database/tables/create`

**描述**: 根据字段配置创建数据库表

**请求体**:
```json
{
  "module_name": "purchase_order",
  "user_id": "Alice",
  "database_type": "sqlserver",
  "connection_string": "your_connection_string"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "数据库表创建成功",
  "data": {
    "table_name": "purchase_order",
    "created_columns": 45,
    "sql_script": "CREATE TABLE purchase_order (...)"
  }
}
```

### 2. 获取数据库表信息

**接口**: `GET /api/v1/database/tables/{table_name}`

**描述**: 获取指定表的结构信息

**路径参数**:
- `table_name`: 表名 (string, required)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "table_name": "purchase_order",
    "columns": [
      {
        "name": "采购订单号",
        "type": "NVARCHAR(50)",
        "nullable": true,
        "default_value": null
      }
    ],
    "row_count": 1250
  }
}
```

## 🔄 数据同步 API

### 1. 启动数据同步

**接口**: `POST /api/v1/sync/start`

**描述**: 启动指定模块的数据同步

**请求体**:
```json
{
  "module_name": "purchase_order",
  "user_id": "Alice",
  "sync_type": "full",
  "batch_size": 100
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "数据同步已启动",
  "data": {
    "task_id": "sync_20241201_100000",
    "status": "running",
    "estimated_time": "5分钟"
  }
}
```

### 2. 获取同步状态

**接口**: `GET /api/v1/sync/status/{task_id}`

**描述**: 获取指定同步任务的状态

**路径参数**:
- `task_id`: 任务ID (string, required)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "task_id": "sync_20241201_100000",
    "status": "completed",
    "progress": 100,
    "total_records": 1250,
    "processed_records": 1250,
    "error_count": 0,
    "start_time": "2024-12-01T10:00:00Z",
    "end_time": "2024-12-01T10:05:30Z"
  }
}
```

### 3. 停止数据同步

**接口**: `POST /api/v1/sync/stop/{task_id}`

**描述**: 停止指定的同步任务

**路径参数**:
- `task_id`: 任务ID (string, required)

**响应示例**:
```json
{
  "success": true,
  "message": "同步任务已停止",
  "data": {
    "task_id": "sync_20241201_100000",
    "status": "stopped",
    "processed_records": 850
  }
}
```

## 📈 监控 API

### 1. 获取系统状态

**接口**: `GET /api/v1/monitor/status`

**描述**: 获取系统整体状态信息

**响应示例**:
```json
{
  "success": true,
  "data": {
    "system_status": "healthy",
    "uptime": "72小时30分钟",
    "memory_usage": "45%",
    "cpu_usage": "23%",
    "active_tasks": 2,
    "total_modules": 15,
    "configured_modules": 15
  }
}
```

### 2. 获取模块状态

**接口**: `GET /api/v1/monitor/modules/{module_name}/status`

**描述**: 获取指定模块的状态信息

**路径参数**:
- `module_name`: 模块名称 (string, required)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "module_name": "purchase_order",
    "status": "configured",
    "last_sync": "2024-12-01T09:30:00Z",
    "field_count": 184,
    "selected_fields": 45,
    "sync_status": "idle",
    "error_count": 0
  }
}
```

## 📝 实时日志 API

### 1. 获取实时日志

**接口**: `GET /api/v1/logs/realtime`

**描述**: 获取实时系统日志

**查询参数**:
- `level`: 日志级别 (string, optional, enum: [debug, info, warning, error])
- `module`: 模块名称 (string, optional)
- `limit`: 日志条数限制 (integer, optional, default: 100)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "timestamp": "2024-12-01T10:00:00Z",
        "level": "info",
        "module": "purchase_order",
        "message": "数据同步完成，共处理1250条记录",
        "details": {
          "processed": 1250,
          "errors": 0
        }
      }
    ],
    "total_count": 1250
  }
}
```

### 2. 清除日志

**接口**: `DELETE /api/v1/logs/clear`

**描述**: 清除系统日志

**查询参数**:
- `before_date`: 清除指定日期之前的日志 (string, optional, format: YYYY-MM-DD)

**响应示例**:
```json
{
  "success": true,
  "message": "日志清除成功",
  "data": {
    "cleared_count": 1250
  }
}
```

## 🔧 Excel 翻译 API

### 1. 上传 Excel 文件

**接口**: `POST /api/v1/excel/upload`

**描述**: 上传 Excel 文件进行字段翻译

**请求体**: `multipart/form-data`
- `file`: Excel 文件 (file, required)
- `module_name`: 模块名称 (string, required)

**响应示例**:
```json
{
  "success": true,
  "message": "Excel 文件上传成功",
  "data": {
    "file_id": "excel_20241201_100000",
    "filename": "purchase_order_fields.xlsx",
    "field_count": 45,
    "status": "processing"
  }
}
```

### 2. 获取翻译结果

**接口**: `GET /api/v1/excel/translate/{file_id}`

**描述**: 获取 Excel 翻译结果

**路径参数**:
- `file_id`: 文件ID (string, required)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "file_id": "excel_20241201_100000",
    "status": "completed",
    "results": {
      "total_fields": 45,
      "matched_fields": 38,
      "unmatched_fields": 7,
      "confidence_score": 0.84,
      "translations": [
        {
          "excel_field": "订单号",
          "api_field": "code",
          "chinese_name": "单据编码",
          "confidence": 0.95,
          "match_type": "exact"
        }
      ]
    }
  }
}
```

### 3. 导出翻译结果

**接口**: `GET /api/v1/excel/export/{file_id}`

**描述**: 导出翻译结果为 Excel 或 JSON 格式

**路径参数**:
- `file_id`: 文件ID (string, required)

**查询参数**:
- `format`: 导出格式 (string, required, enum: [excel, json, csv])

**响应**: 文件下载

## 🔒 错误代码说明

### 通用错误代码
| 错误代码 | 描述 | HTTP 状态码 |
|----------|------|-------------|
| `INVALID_PARAMETER` | 参数无效 | 400 |
| `MODULE_NOT_FOUND` | 模块不存在 | 404 |
| `CONFIG_NOT_FOUND` | 配置不存在 | 404 |
| `DATABASE_ERROR` | 数据库错误 | 500 |
| `API_ERROR` | 外部API错误 | 502 |
| `SYNC_IN_PROGRESS` | 同步进行中 | 409 |
| `FILE_TOO_LARGE` | 文件过大 | 413 |
| `INVALID_FILE_FORMAT` | 文件格式无效 | 400 |

### 业务错误代码
| 错误代码 | 描述 | 解决方案 |
|----------|------|----------|
| `FIELD_MAPPING_ERROR` | 字段映射错误 | 检查字段配置 |
| `TRANSLATION_FAILED` | 翻译失败 | 检查翻译规则 |
| `SYNC_TIMEOUT` | 同步超时 | 增加超时时间 |
| `CONFIG_CONFLICT` | 配置冲突 | 解决配置冲突 |

## 📊 API 使用统计

### 接口调用频率
- **字段配置 API**: 高频调用
- **数据同步 API**: 中频调用
- **监控 API**: 低频调用
- **日志 API**: 中频调用

### 性能指标
- **平均响应时间**: < 200ms
- **并发支持**: 100+ 并发请求
- **数据吞吐量**: 1000+ 记录/秒
- **可用性**: 99.9%

## 🔧 开发指南

### 1. 认证方式
当前版本使用简单的用户ID认证，生产环境建议升级为 JWT 认证。

### 2. 限流策略
- 普通接口: 1000 请求/分钟
- 同步接口: 10 请求/分钟
- 文件上传: 10 请求/分钟

### 3. 缓存策略
- 字段配置: 5分钟缓存
- 模块列表: 10分钟缓存
- 系统状态: 30秒缓存

### 4. 日志记录
所有 API 调用都会记录访问日志，包括：
- 请求时间
- 请求路径
- 响应状态
- 处理时间
- 用户ID

---

*最后更新时间：2024年12月*
*版本：V3.2* 