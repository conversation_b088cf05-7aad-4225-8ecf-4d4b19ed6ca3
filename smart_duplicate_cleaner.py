import json
import logging
import shutil
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能重复文件清理器
基于分析报告进行安全清理
"""


class SmartDuplicateCleaner:
    def __init___(self):
    """TODO: Add function description."""
    logging.basicConfig(
        level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
    )
    self.logger = logging.getLogger(__name__)

    # 加载清理计划
    with open('duplicate_cleanup_plan.json', 'r', encoding='utf-8') as f:
        self.plan = json.load(f)

    def smart_cleanup(self, dry_run=True):
        """智能清理重复文件"""
        self.logger.info(f"🧹 开始智能清理 (dry_run={dry_run})")

        if not dry_run:
            trash_dir = Path(".cleanup_trash")
            trash_dir.mkdir(exist_ok=True)
            self.logger.info(f"📁 创建垃圾箱目录: {trash_dir}")

        cleaned_count = 0

        # 处理各种重复文件
        for item in self.plan['cleanup_plan']['needs_verification']:
            if item['type'] == 'hash_duplicate':
                cleaned_count += self._handle_hash_duplicates(item, dry_run)

        self.logger.info(
            f"📊 {'预计' if dry_run else '实际'}清理文件数: {cleaned_count}"
        )
        return cleaned_count

    def _handle_hash_duplicates(self, item, dry_run):
        """处理哈希重复文件"""
        files = item['files']
        if len(files) < 2:
            return 0

        self.logger.info(f"🔍 处理重复文件组 ({len(files)} 个文件):")

        # 智能选择保留哪个文件
        keep_file, delete_files = self._choose_files_to_keep(files)

        self.logger.info(f"   👑 保留: {keep_file}")

        cleaned = 0
        for file_path in delete_files:
            if dry_run:
                self.logger.info(f"   🗑️ [DRY RUN] 会删除: {file_path}")
            else:
                success = self._safe_delete(file_path)
                if success:
                    self.logger.info(f"   ✅ 已移动到垃圾箱: {file_path}")
                    cleaned += 1
                else:
                    self.logger.warning(f"   ⚠️ 删除失败: {file_path}")

        return cleaned if not dry_run else len(delete_files)

    def _choose_files_to_keep(self, files):
        """智能选择保留哪个文件"""
        # 优先级规则
        priorities = [
            # 1. 保留在主目录的文件，而不是backup/template目录的
            lambda f: 'backup' not in f.lower() and 'template' not in f.lower(),
            # 2. 保留在backend目录的config文件
            lambda f: 'backend' in f and 'config' in f,
            # 3. 保留路径更短的文件（通常是主文件）
            lambda f: -len(f),
            # 4. 保留不包含时间戳的文件
            lambda f: not any(char.isdigit() for char in Path(f).stem[-10:]),
        ]

        # 按优先级排序
        sorted_files = sorted(
            files, key=lambda f: [
                not rule(f) for rule in priorities])

        keep_file = sorted_files[0]
        delete_files = sorted_files[1:]

        return keep_file, delete_files

    def _safe_delete(self, file_path):
        """安全删除文件（移动到垃圾箱）"""
        try:
            source = Path(file_path)
            if not source.exists():
                return False

            trash_dir = Path(".cleanup_trash")

            # 生成唯一的垃圾箱文件名
            trash_file = trash_dir / source.name
            counter = 1
            while trash_file.exists():
                name_parts = source.name.rsplit('.', 1)
                if len(name_parts) == 2:
                    trash_file = (trash_dir /
                                  f"{name_parts[0]}_{counter}.{name_parts[1]}")
                else:
                    trash_file = trash_dir / f"{source.name}_{counter}"
                counter += 1

            # 移动文件
            shutil.move(str(source), str(trash_file))
            return True

        except Exception:
            self.logger.error(f"移动文件失败 {file_path}: {e}")
            return False

    def generate_cleanup_summary(self):
        """生成清理摘要"""
        hash_dupes = self.plan['analysis_report']['hash_duplicates']

        self.logger.info("📋 清理摘要:")
        self.logger.info(f"   🔍 发现 {len(hash_dupes)} 组哈希重复文件")

        total_duplicates = sum(len(files) - 1 for files in hash_dupes.values())
        self.logger.info(f"   🗑️ 可清理重复文件: {total_duplicates} 个")

        # 显示主要重复类型
        duplicate_types = {}
        for files in hash_dupes.values():
            if len(files) >= 2:
                file_type = (
                    "配置文件"
                    if any('config' in f.lower() for f in files)
                    else (
                        "模板文件"
                        if any('template' in f.lower() for f in files)
                        else (
                            "备份文件"
                            if any('backup' in f.lower() for f in files)
                            else (
                                "CSS/JS文件"
                                if any(f.endswith(('.css', '.js')) for f in files)
                                else "其他文件"
                            )
                        )
                    )
                )
                duplicate_types[file_type] = duplicate_types.get(
                    file_type, 0) + 1

        self.logger.info("   📊 重复文件类型分布:")
        for file_type, count in duplicate_types.items():
            self.logger.info(f"      {file_type}: {count} 组")


def main():
    """主函数"""
    print("🔧 YS-API V3.0 智能重复文件清理器")
    print("=" * 50)

    try:
        cleaner = SmartDuplicateCleaner()

        # 生成清理摘要
        cleaner.generate_cleanup_summary()

        print("\n" + "=" * 50)
        choice = input("选择操作 (p=预览/y=执行清理/n=取消): ").lower()

        if choice == 'p':
            cleaner.smart_cleanup(dry_run=True)
        elif choice == 'y':
            cleaner.smart_cleanup(dry_run=False)
            print("\n✅ 清理完成！被删除的文件已移动到 .cleanup_trash/ 目录")
            print("💡 如需恢复，可以从垃圾箱目录中找回文件")
        else:
            print("⏹️ 清理已取消")

    except FileNotFoundError:
        print("❌ 未找到清理计划文件，请先运行 systematic_duplicate_detector.py")
    except Exception:
        print(f"❌ 清理过程中发生错误: {e}")


if __name__ == "__main__":
    main()
