import json
import os
import socket
import subprocess
import sys
import time
from pathlib import Path

#!/usr/bin/env python3
"""
端口锁定启动脚本 - 防止AI/IDE乱改端口
按照用户的"一次声明，永远锁定"方案实现
"""


class PortLocker:
    def __init___(self):
    """TODO: Add function description."""
    self.config_file = Path('.ports.json')
    self.ports_config = self.load_ports_config()

    def load_ports_config(self):
        """加载端口配置"""
        if not self.config_file.exists():
            print("❌ 找不到 .ports.json 配置文件")
            sys.exit(1)

        with open(self.config_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    def check_port_available(self, port):
        """检查端口是否可用"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            return result != 0
        except Exception:
            return False

    def kill_port_process(self, port):
        """杀死占用端口的进程"""
        try:
            # Windows
            if os.name == 'nt':
                result = subprocess.run(
                    f'netstat -ano | findstr :{port}',
                    shell=True, capture_output=True, text=True
                )
                if result.stdout:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if f':{port}' in line and 'LISTENING' in line:
                            pid = line.split()[-1]
                            subprocess.run(
                                f'taskkill /PID {pid} /F', shell=True)
                            print(f"🔥 已终止占用端口 {port} 的进程 (PID: {pid})")
                            return True
            # Linux/Mac
            else:
                result = subprocess.run(
                    f'lsof -ti:{port}',
                    shell=True, capture_output=True, text=True
                )
                if result.stdout:
                    pid = result.stdout.strip()
                    subprocess.run(f'kill -9 {pid}', shell=True)
                    print(f"🔥 已终止占用端口 {port} 的进程 (PID: {pid})")
                    return True
        except Exception:
            print(f"⚠️ 终止端口 {port} 进程失败: {e}")
        return False

    def lock_port(self, service_name):
        """锁定指定服务的端口"""
        service_config = self.ports_config['service_mapping'].get(service_name)
        if not service_config:
            print(f"❌ 未找到服务 '{service_name}' 的配置")
            sys.exit(1)

        port = service_config['port']
        strict_mode = self.ports_config['port_rules']['strict_mode']

        print(f"🔒 准备锁定 {service_name} 服务端口: {port}")

        # 检查端口是否可用
        if not self.check_port_available(port):
            if strict_mode:
                print(f"⚠️ 端口 {port} 已被占用")
                print(f"🛡️ 严格模式：正在清理占用进程...")
                if self.kill_port_process(port):
                    time.sleep(1)  # 等待端口释放
                    if not self.check_port_available(port):
                        print(f"❌ 端口 {port} 仍被占用，启动失败")
                        print("💡 建议: 手动检查并终止占用进程")
                        sys.exit(1)
                else:
                    print(f"❌ 无法清理端口 {port}，启动失败")
                    print(f"💡 建议: 修改 .ports.json 中的端口配置或手动终止进程")
                    sys.exit(1)
            else:
                print(f"❌ 端口 {port} 已被占用，启动失败")
                print(f"💡 建议: 在 .ports.json 中启用 strict_mode 自动清理")
                sys.exit(1)

        print(f"✅ 端口 {port} 可用，继续启动 {service_name}")
        return port

    def start_service(self, service_name, command=None):
        """启动指定服务"""
        port = self.lock_port(service_name)
        service_config = self.ports_config['service_mapping'][service_name]

        print(f"🚀 启动 {service_config['description']}")
        print(f"📍 访问地址: {service_config['url']}")

        if command:
            # 设置环境变量
            env = os.environ.copy()
            env['PORT'] = str(port)

            # 启动命令
            print(f"🔧 执行命令: {command}")
            try:
                subprocess.run(command, shell=True, env=env)
            except KeyboardInterrupt:
                print(f"\n🛑 {service_name} 服务已停止")
        else:
            print(f"💡 请手动启动服务，使用端口: {port}")

    def show_status(self):
        """显示所有服务端口状态"""
        print("📊 YS-API 服务端口状态:")
        print("=" * 60)

        for service_name, config in self.ports_config['service_mapping'].items(
        ):
            port = config['port']
            url = config['url']
            desc = config['description']
            status = "🟢 可用" if self.check_port_available(port) else "🔴 占用"

            print(f"{service_name:12} | {port:4} | {status} | {desc}")
            print(f"{'':12} | {'':4} |      | {url}")
            print("-" * 60)

    def setup_hosts(self):
        """显示hosts配置说明"""
        print("🌐 配置本地域名 (可选，但推荐):")
        print("=" * 50)
        print("将以下内容添加到 hosts 文件:")

        if os.name == 'nt':
            print("📁 Windows: C:\\Windows\\System32\\drivers\\etc\\hosts")
        else:
            print("📁 Linux/Mac: /etc/hosts")

        print()
        for line in self.ports_config['hosts_config']:
            if not line.startswith('#'):
                print(line)
        print()
        print("💡 配置后可使用友好域名访问服务")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("🎯 YS-API 端口锁定启动器")
        print("=" * 40)
        print("用法:")
        print("  python port_locker.py status              # 查看端口状态")
        print("  python port_locker.py setup-hosts         # 显示hosts配置")
        print("  python port_locker.py lock <service>      # 锁定端口但不启动")
        print("  python port_locker.py start <service> [cmd] # 锁定并启动服务")
        print()
        print("可用服务: frontend, backend, database, monitoring, docs")
        return

    locker = PortLocker()
    command = sys.argv[1]

    if command == 'status':
        locker.show_status()
    elif command == 'setup-hosts':
        locker.setup_hosts()
    elif command == 'lock' and len(sys.argv) >= 3:
        service = sys.argv[2]
        port = locker.lock_port(service)
        print(f"🔒 端口 {port} 已锁定给服务 '{service}'")
    elif command == 'start' and len(sys.argv) >= 3:
        service = sys.argv[2]
        start_command = sys.argv[3] if len(sys.argv) > 3 else None
        locker.start_service(service, start_command)
    else:
        print("❌ 未知命令或参数不足")
        print("💡 使用 'python port_locker.py' 查看帮助")


if __name__ == "__main__":
    main()
