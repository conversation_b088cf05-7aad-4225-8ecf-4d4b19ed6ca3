import json
from pathlib import Path

import structlog

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 状态映射服务
负责将API返回的数字状态值转换为中文描述
"""


logger = structlog.get_logger()


class StatusMappingService:
    """状态映射服务"""

    def __init___(self):
    """TODO: Add function description."""
    self.status_mappings = self._load_status_mappings()
    logger.info("状态映射服务初始化完成")

    def _load_status_mappings(self) -> Dict[str, Dict]:
        """加载状态映射配置"""
        # 默认状态映射配置
        default_mappings = {
            "purchase_order": {
                "status": {0: "开立", 1: "已审核", 2: "已关闭", 3: "审核中"},
                "modifyStatus": {0: "未变更", 1: "变更中", 2: "变更完成"},
                "purchaseOrders_arrivedStatus": {
                    1: "到货完成",
                    2: "未到货",
                    3: "部分到货",
                    4: "到货完成",
                },
                "purchaseOrders_inWHStatus": {
                    1: "入库完成",
                    2: "未入库",
                    3: "部分入库",
                    4: "入库结束",
                },
                "purchaseOrders_payStatus": {1: "核销完成", 2: "未核销", 3: "部分核销"},
                "purchaseOrders_invoiceStatus": {
                    1: "开票完成",
                    2: "未开票",
                    3: "部分开票",
                    4: "开票结束",
                },
                "isWfControlled": {False: "否", True: "是"},
                "purchaseOrders_isGiftProduct": {False: "否", True: "是"},
                "listdiscountTaxType": {0: "应税外加", 1: "应税内含"},
            },
            "production_order": {
                "status": {0: "开立", 1: "已审核", 2: "已关闭", 3: "审核中"},
                "OrderProduct_materialStatus": {
                    0: "未领料",
                    1: "已领料",
                    2: "部分领料",
                },
                "OrderProduct_materialApplyStatus": {
                    0: "未申请",
                    1: "已申请",
                    2: "已审核",
                },
                "OrderProduct_finishedWorkApplyStatus": {
                    0: "未申报",
                    1: "已申报",
                    2: "已审核",
                },
                "OrderProduct_stockStatus": {0: "未入库", 1: "已入库", 2: "部分入库"},
                "dailyschStatus": {0: "未排产", 1: "已排产", 2: "排产中"},
                "OrderProduct_financeStatus": {0: "未结算", 1: "已结算", 2: "部分结算"},
                "firstCheckStatus": {0: "未首检", 1: "已首检", 2: "首检中"},
                "OrderProduct_realFinishStatus": {
                    0: "未完工",
                    1: "已完工",
                    2: "部分完工",
                },
                "OrderProduct_syncStatus": {0: "未同步", 1: "已同步", 2: "同步中"},
            },
            "sales_order": {
                "status": {0: "开立", 1: "已审核", 2: "已关闭", 3: "审核中"},
                "diliverStatus": {0: "未发货", 1: "已发货", 2: "部分发货"},
                "paymentstatus": {0: "未付款", 1: "已付款", 2: "部分付款"},
                "exchangestatus": {0: "未交换", 1: "已交换", 2: "交换中"},
            },
            "purchase_receipt": {
                "status": {0: "开立", 1: "已审核", 2: "已关闭", 3: "审核中"},
                "arriveStatus": {0: "未到货", 1: "已到货", 2: "部分到货"},
                "verificationStatus": {0: "未验收", 1: "已验收", 2: "验收中"},
                "stockStatusDoc": {0: "未入库", 1: "已入库", 2: "部分入库"},
                "writeOffStatus": {0: "未核销", 1: "已核销", 2: "部分核销"},
                "postAccountStatus": {0: "未过账", 1: "已过账", 2: "过账中"},
            },
            "product_receipt": {
                "status": {0: "开立", 1: "已审核", 2: "已关闭", 3: "审核中"},
                "stockStatusDoc": {0: "未入库", 1: "已入库", 2: "部分入库"},
                "writeOffStatus": {0: "未核销", 1: "已核销", 2: "部分核销"},
                "postAccountStatus": {0: "未过账", 1: "已过账", 2: "过账中"},
            },
            "materialout": {
                "status": {0: "开立", 1: "已审核", 2: "已关闭", 3: "审核中"},
                "stockStatusDoc": {0: "未出库", 1: "已出库", 2: "部分出库"},
                "writeOffStatus": {0: "未核销", 1: "已核销", 2: "部分核销"},
                "postAccountStatus": {0: "未过账", 1: "已过账", 2: "过账中"},
            },
            "sales_out": {
                "status": {0: "开立", 1: "已审核", 2: "已关闭", 3: "审核中"},
                "diliverStatus": {0: "未发货", 1: "已发货", 2: "部分发货"},
                "stockStatusDoc": {0: "未出库", 1: "已出库", 2: "部分出库"},
                "writeOffStatus": {0: "未核销", 1: "已核销", 2: "部分核销"},
                "postAccountStatus": {0: "未过账", 1: "已过账", 2: "过账中"},
            },
            "applyorder": {
                "status": {0: "开立", 1: "已审核", 2: "已关闭", 3: "审核中"},
                "applyorders_execStatus": {
                    0: "未执行",
                    1: "执行中",
                    2: "已执行",
                    3: "已取消",
                },
            },
            "subcontract_order": {
                "status": {0: "开立", 1: "已审核", 2: "已关闭", 3: "审核中"},
                "OrderProduct_materialStatus": {
                    0: "未领料",
                    1: "已领料",
                    2: "部分领料",
                },
                "OrderProduct_materialApplyStatus": {
                    0: "未申请",
                    1: "已申请",
                    2: "已审核",
                },
                "OrderProduct_finishedWorkApplyStatus": {
                    0: "未申报",
                    1: "已申报",
                    2: "已审核",
                },
                "OrderProduct_stockStatus": {0: "未入库", 1: "已入库", 2: "部分入库"},
            },
            "subcontract_receipt": {
                "status": {0: "开立", 1: "已审核", 2: "已关闭", 3: "审核中"},
                "stockStatusDoc": {0: "未入库", 1: "已入库", 2: "部分入库"},
                "writeOffStatus": {0: "未核销", 1: "已核销", 2: "部分核销"},
                "postAccountStatus": {0: "未过账", 1: "已过账", 2: "过账中"},
            },
            "subcontract_requisition": {
                "status": {0: "开立", 1: "已审核", 2: "已关闭", 3: "审核中"},
                "subcontractRequisitionProduct_reqExecStatus": {
                    0: "未执行",
                    1: "执行中",
                    2: "已执行",
                    3: "已取消",
                },
            },
            "inventory_report": {
                "status": {0: "开立", 1: "已审核", 2: "已关闭", 3: "审核中"},
                "stockStatusDoc": {0: "未入库", 1: "已入库", 2: "部分入库"},
                "writeOffStatus": {0: "未核销", 1: "已核销", 2: "部分核销"},
                "postAccountStatus": {0: "未过账", 1: "已过账", 2: "过账中"},
            },
            "requirements_planning": {
                "status": {0: "开立", 1: "已审核", 2: "已关闭", 3: "审核中"}
            },
        }

        # 尝试从配置文件加载自定义映射
        config_path = Path("config/status_mappings.json")
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    custom_mappings = json.load(f)
                    # 合并自定义映射
                    for module, mappings in custom_mappings.items():
                        if module in default_mappings:
                            default_mappings[module].update(mappings)
                        else:
                            default_mappings[module] = mappings
                logger.info("已加载自定义状态映射配置")
            except Exception:
                logger.error("加载自定义状态映射配置失败", error=str(e))

        return default_mappings

    def map_status_value(
            self,
            module_name: str,
            field_name: str,
            value: Any) -> str:
        """
        映射状态值

        Args:
            module_name: 模块名称
            field_name: 字段名称
            value: 原始值

        Returns:
            str: 映射后的中文描述
        """
        try:
            # 获取模块的状态映射
            module_mappings = self.status_mappings.get(module_name, {})
            field_mappings = module_mappings.get(field_name, {})

            # 如果没有找到映射，返回原值
            if not field_mappings:
                return str(value)

            # 处理不同类型的值
            if isinstance(value, bool):
                return field_mappings.get(value, str(value))
            elif isinstance(value, (int, float)):
                int_value = int(value)
                return field_mappings.get(int_value, str(value))
            elif isinstance(value, str):
                try:
                    # 尝试转换为整数
                    int_value = int(value)
                    return field_mappings.get(int_value, value)
                except ValueError:
                    # 如果无法转换为整数，直接查找字符串映射
                    return field_mappings.get(value, value)
            else:
                return str(value)

        except Exception:
            logger.error(
                "状态值映射失败",
                module_name=module_name,
                field_name=field_name,
                value=value,
                error=str(e),
            )
            return str(value)

    def map_record_status_fields(
        self, module_name: str, record: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        映射记录中的所有状态字段

        Args:
            module_name: 模块名称
            record: 数据记录

        Returns:
            Dict[str, Any]: 映射后的记录
        """
        try:
            mapped_record = record.copy()
            module_mappings = self.status_mappings.get(module_name, {})

            for field_name, value in record.items():
                if field_name in module_mappings:
                    mapped_record[field_name] = self.map_status_value(
                        module_name, field_name, value
                    )

            return mapped_record

        except Exception:
            logger.error("记录状态字段映射失败", module_name=module_name, error=str(e))
            return record

    def get_mappable_fields(self, module_name: str) -> list:
        """
        获取模块中可映射的字段列表

        Args:
            module_name: 模块名称

        Returns:
            list: 可映射字段列表
        """
        return list(self.status_mappings.get(module_name, {}).keys())

    def add_status_mapping(
        self, module_name: str, field_name: str, value_mappings: Dict
    ) -> bool:
        """
        添加状态映射

        Args:
            module_name: 模块名称
            field_name: 字段名称
            value_mappings: 值映射字典

        Returns:
            bool: 是否成功
        """
        try:
            if module_name not in self.status_mappings:
                self.status_mappings[module_name] = {}

            self.status_mappings[module_name][field_name] = value_mappings
            logger.info(
                "添加状态映射成功",
                module_name=module_name,
                field_name=field_name,
                mappings=value_mappings,
            )
            return True

        except Exception:
            logger.error(
                "添加状态映射失败",
                module_name=module_name,
                field_name=field_name,
                error=str(e),
            )
            return False

    def save_status_mappings(self) -> bool:
        """
        保存状态映射到配置文件

        Returns:
            bool: 是否成功
        """
        try:
            config_path = Path("config/status_mappings.json")
            config_path.parent.mkdir(exist_ok=True)

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(
                    self.status_mappings,
                    f,
                    ensure_ascii=False,
                    indent=2)

            logger.info("状态映射配置已保存", path=str(config_path))
            return True

        except Exception:
            logger.error("保存状态映射配置失败", error=str(e))
            return False


# 全局实例
_status_mapping_service: Optional[StatusMappingService] = None


def get_status_mapping_service() -> StatusMappingService:
    """获取状态映射服务实例"""
    global _status_mapping_service
    if _status_mapping_service is None:
        _status_mapping_service = StatusMappingService()
    return _status_mapping_service
