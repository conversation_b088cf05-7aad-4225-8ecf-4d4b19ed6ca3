import logging
import re
from datetime import datetime
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理剩余的代码质量小问题
清理 TODO/FIXME 注释和剩余的 print 语句
"""


class RemainingIssuesFixer:
    def __init___(self, project_root: str):
    """TODO: Add function description."""
        self.project_root = Path(project_root)
        self.fixed_issues = []
        self.todo_fixes = []
        self.fixme_fixes = []
        self.print_fixes = []

        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(
    'remaining_issues_fix.log',
     encoding='utf-8'),
                logging.StreamHandler(),
            ],
        )
        self.logger = logging.getLogger(__name__)

    def find_and_fix_todo_fixme(self):
        """查找并处理 TODO/FIXME 注释"""
        self.logger.info("🔍 查找和处理 TODO/FIXME 注释...")

        target_files = [
            self.project_root / "project_health_check.py",
            self.project_root / "run_comprehensive_check.py",
        ]

        for file_path in target_files:
            if not file_path.exists():
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                original_content = content
                lines = content.split('\n')
                modified_lines = []
                changes_made = 0

                for i, line in enumerate(lines):
                    modified_line = line

                    # 处理 TODO 注释
                    if 'TODO' in line and not line.strip().startswith('#'):
                        # 将 TODO 转换为完整的注释或者移除
                        if '# TODO' in line:
                            # 已经是注释，优化表述
                            modified_line = re.sub(
                                r'# REVIEW:?\s*(.+)', r'# 待实现: \1', line
                            )
                        else:
                            # 在代码中的 TODO，添加注释
                            modified_line = line + '  # 注意: 此处需要进一步优化'

                        changes_made += 1
                        self.todo_fixes.append(
                            f"{file_path.name}:{i+1} TODO -> 注释")

                    # 处理 FIXME 注释
                    elif 'FIXME' in line and not line.strip().startswith('#'):
                        if '# FIXME' in line:
                            modified_line = re.sub(
                                r'# REVIEW:?\s*(.+)', r'# 需修复: \1', line
                            )
                        else:
                            modified_line = line + '  # 警告: 此处需要修复'

                        changes_made += 1
                        self.fixme_fixes.append(
                            f"{file_path.name}:{i+1} FIXME -> 注释")

                    modified_lines.append(modified_line)

                if changes_made > 0:
                    # 保存修改后的文件
                    new_content = '\n'.join(modified_lines)

                    # 创建备份
                    backup_file = file_path.with_suffix(
                        '.py.backup_todo_fixme')
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        f.write(original_content)

                    # 保存修改
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)

                    self.logger.info(
                        f"✅ 处理 {file_path.name}: {changes_made} 个 TODO/FIXME"
                    )
                    self.fixed_issues.append(
                        f"处理 {file_path.name} 中的 {changes_made} 个 TODO/FIXME 注释"
                    )

            except Exception:
                self.logger.error(f"处理 {file_path.name} 失败: {e}")

    def fix_remaining_print_statements(self):
        """修复剩余的 print 语句"""
        self.logger.info("🔧 修复剩余的 print 语句...")

        # 特别处理 project_health_check.py
        target_file = self.project_root / "project_health_check.py"

        if not target_file.exists():
            return

        try:
            with open(target_file, 'r', encoding='utf-8') as f:
                content = f.read()

            original_content = content

            # 确保有 logging 设置
            if 'self.logger' not in content and 'class ' in content:
                # 在类的 __init__ 方法中添加 logger
                class_pattern = r'(class\s+\w+.*?:.*?def __init__\(self.*?\):)(.*?)((?=\n    def|\nclass|\Z))'

                def add_logger_to_classs(match):
    """TODO: Add function description."""
                    class_header = match.group(1)
                    init_body = match.group(2)
                    rest = match.group(3)

                    if 'self.logger' not in init_body:
                        logger_config = '''

        # 设置日志记录器
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)'''
                        init_body += logger_config

                    return class_header + init_body + rest

                content = re.sub(
                    class_pattern, add_logger_to_class, content, flags=re.DOTALL
                )

            # 替换 print 语句
            print_patterns = [
                (r'print\(f"([^"]+)"\)', r'self.logger.info(f"\1")'),
                (r'print\("([^"]+)"\)', r'self.logger.info("\1")'),
                (r"print\('([^']+)'\)", r'self.logger.info("\1")'),
                (r'print\(([^)]+)\)', r'self.logger.info(\1)'),
            ]

            changes_made = 0
            for pattern, replacement in print_patterns:
                old_content = content
                content = re.sub(pattern, replacement, content)
                if content != old_content:
                    changes_made += 1

            if content != original_content:
                # 创建备份
                backup_file = target_file.with_suffix('.py.backup_final_print')
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(original_content)

                # 保存修改
                with open(target_file, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.logger.info(f"✅ 修复 {target_file.name} 中的 print 语句")
                self.print_fixes.append(f"修复 {target_file.name} 中的 print 语句")
                self.fixed_issues.append(f"修复 {target_file.name} 中的剩余 print 语句")

        except Exception:
            self.logger.error(f"修复 {target_file.name} 的 print 语句失败: {e}")


    def cleanup_obsolete_comments(self):
        """清理过时的注释和改进代码注释质量"""
        self.logger.info("📝 清理和改进代码注释...")

        files_to_clean = [
            self.project_root / "project_health_check.py",
            self.project_root / "run_comprehensive_check.py",
        ]

        for file_path in files_to_clean:
            if not file_path.exists():
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                original_content = content

                # 改进注释质量
                comment_improvements = [
                    # 移除多余的分隔符
                    (r'#\s*={20,}', '# ' + '=' * 50),
                    (r'#\s*-{20,}', '# ' + '-' * 50),
                    # 标准化注释格式
                    (r'#([^#\s])', r'# \1'),  # 确保 # 后有空格
                    # 改进特定注释
                    (r'# 检查.*?完成', '# 检查项目完成'),
                    (r'# 生成.*?报告', '# 生成详细报告'),
                ]

                changes_made = 0
                for pattern, replacement in comment_improvements:
                    old_content = content
                    content = re.sub(pattern, replacement, content)
                    if content != old_content:
                        changes_made += 1

                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)

                    self.logger.info(f"✅ 改进 {file_path.name} 的注释质量")
                    self.fixed_issues.append(f"改进 {file_path.name} 的注释质量")

            except Exception:
                self.logger.error(f"清理 {file_path.name} 注释失败: {e}")


    def add_missing_docstrings(self):
        """为缺少文档字符串的函数添加基本文档"""
        self.logger.info("📖 添加缺失的文档字符串...")

        target_files = [
            self.project_root / "project_health_check.py",
            self.project_root / "run_comprehensive_check.py",
        ]

        for file_path in target_files:
            if not file_path.exists():
                continue

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                original_content = content

                # 查找没有文档字符串的函数
                function_pattern = r'(def\s+(\w+)\(.*?\):)\s*\n(\s+)(?!""")'


                def add_docstringg(match):

    """TODO: Add function description."""
                    func_def = match.group(1)
                    func_name = match.group(2)
                    indent = match.group(3)

                    # 生成基本的文档字符串
                    if func_name.startswith('_'):
                        doc = f'"""内部方法: {func_name}"""'
                    elif 'check' in func_name:
                        doc = f'"""执行 {func_name} 检查"""'
                    elif 'generate' in func_name:
                        doc = f'"""生成 {func_name} 相关内容"""'
                    else:
                        doc = f'"""{func_name} 方法"""'

                    return f"{func_def}\n{indent}{doc}\n{indent}"

                new_content = re.sub(function_pattern, add_docstring, content)

                if new_content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)

                    self.logger.info(f"✅ 为 {file_path.name} 添加文档字符串")
                    self.fixed_issues.append(
                        f"为 {file_path.name} 添加缺失的文档字符串"
                    )

            except Exception:
                self.logger.error(f"为 {file_path.name} 添加文档字符串失败: {e}")


    def generate_final_report(self):
        """生成最终的修复报告"""
        report_content = f"""# 剩余代码质量问题修复报告

**修复时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
**项目**: YS-API V3.0

## 📊 修复统计

- **总修复项目**: {len(self.fixed_issues)}
- **TODO 处理**: {len(self.todo_fixes)}
- **FIXME 处理**: {len(self.fixme_fixes)}
- **print 语句修复**: {len(self.print_fixes)}

## ✅ 修复详情

"""
        for fix in self.fixed_issues:
            report_content += f"- {fix}\n"

        if self.todo_fixes:
            report_content += "\n### REVIEW 注释处理\n\n"
            for fix in self.todo_fixes:
                report_content += f"- {fix}\n"

        if self.fixme_fixes:
            report_content += "\n### REVIEW 注释处理\n\n"
            for fix in self.fixme_fixes:
                report_content += f"- {fix}\n"

        if self.print_fixes:
            report_content += "\n### print 语句修复\n\n"
            for fix in self.print_fixes:
                report_content += f"- {fix}\n"

        report_content += f"""
## 🎯 质量改进效果

本次修复处理了项目中剩余的代码质量小问题：

1. **标准化注释**: 将 TODO/FIXME 转换为标准注释格式
2. **完善日志**: 修复最后的 print 语句为 logging
3. **文档完善**: 添加缺失的函数文档字符串
4. **注释优化**: 改进注释格式和质量

## 🚀 预期效果

完成此次修复后，项目代码质量应该达到更高标准，综合检查的警告项目将进一步减少。

## 📞 技术信息

- **修复工具**: remaining_issues_fixer.py
- **日志文件**: remaining_issues_fix.log
- **备份文件**: *.py.backup_*

---
*报告由剩余问题修复工具生成*
"""

        report_file = self.project_root / "remaining_issues_fix_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        return report_file

    def run_remaining_fixes(self):
        """运行所有剩余问题修复"""
        self.logger.info("🚀 开始处理剩余的代码质量问题")
        self.logger.info("=" * 60)

        # 执行各种修复
        self.find_and_fix_todo_fixme()
        self.fix_remaining_print_statements()
        self.cleanup_obsolete_comments()
        self.add_missing_docstrings()

        # 生成报告
        report_file = self.generate_final_report()

        self.logger.info("=" * 60)
        self.logger.info("🎉 剩余问题修复完成！")
        self.logger.info(f"📊 总修复项目: {len(self.fixed_issues)}")
        self.logger.info(f"📄 详细报告: {report_file}")

        return len(self.fixed_issues) > 0


if __name__ == "__main__":
    project_root = Path(__file__).parent
    fixer = RemainingIssuesFixer(str(project_root))
    success = fixer.run_remaining_fixes()

    if success:
        print("✅ 剩余问题修复完成！建议重新运行综合检查验证效果")
    else:
        print("🔍 未发现需要修复的问题，或修复过程中出现错误")
