from enum import Enum

from pydantic import BaseModel

"""
YS-API V3.0 实时日志数据格式定义
简洁统一的前后端数据交换格式
"""


class LogLevel(str, Enum):
    """日志级别"""

    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"


class LogStage(str, Enum):
    """同步阶段"""

    INIT = "INIT"
    API_FETCH = "API_FETCH"
    FIELD_MAPPING = "FIELD_MAPPING"
    WRITE_DB = "WRITE_DB"
    FINISH = "FINISH"
    ERROR = "ERROR"


class RealtimeLogEntry(BaseModel):
    """实时日志条目 - 统一数据格式"""

    timestamp: str  # ISO格式时间戳
    level: LogLevel  # 日志级别
    stage: LogStage  # 同步阶段
    module: str  # 模块名称
    message: str  # 日志消息
    session_id: Optional[str] = None  # 会话ID（可选）
    extra: Optional[Dict[str, Any]] = None  # 额外信息（可选）


class RealtimeLogResponse(BaseModel):
    """实时日志API响应格式"""

    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


class LogHistoryResponse(BaseModel):
    """历史日志响应格式"""

    success: bool
    message: str
    data: Dict[str, Any]


class ConnectionStatsResponse(BaseModel):
    """连接统计响应格式"""

    success: bool
    message: str
    data: Dict[str, Any]


# 阶段图标映射
STAGE_ICONS = {
    LogStage.INIT: "🚀",
    LogStage.API_FETCH: "📡",
    LogStage.FIELD_MAPPING: "🔄",
    LogStage.WRITE_DB: "💾",
    LogStage.FINISH: "✅",
    LogStage.ERROR: "❌",
}

# 级别样式映射
LEVEL_STYLES = {
    LogLevel.INFO: "info",
    LogLevel.SUCCESS: "success",
    LogLevel.WARNING: "warning",
    LogLevel.ERROR: "error",
}
