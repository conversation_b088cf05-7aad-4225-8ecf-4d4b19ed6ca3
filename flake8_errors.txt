.\analyze_dependencies.py:74:80: E501 line too long (82 > 79 characters)
.\analyze_dependencies.py:118:80: E501 line too long (86 > 79 characters)
.\analyze_dependencies.py:119:80: E501 line too long (86 > 79 characters)
.\analyze_dependencies.py:138:80: E501 line too long (86 > 79 characters)
.\analyze_dependencies.py:147:80: E501 line too long (86 > 79 characters)
.\analyze_dependencies.py:186:80: E501 line too long (86 > 79 characters)
.\analyze_dependencies.py:200:80: E501 line too long (84 > 79 characters)
.\analyze_project_stats.py:4:1: E265 block comment should start with '# '
.\analyze_project_stats.py:27:11: F541 f-string is missing placeholders
.\auto_fix_comprehensive_issues.py:19:6: E999 IndentationError: expected an indented block after function definition on line 18
.\auto_project_cleanup.py:24:6: E999 IndentationError: expected an indented block after function definition on line 23
.\backend\app\api\v1\__init__.py:6:5: E999 IndentationError: unexpected indent
.\backend\app\api\v1\auth.py:15:50: F821 undefined name 'e'
.\backend\app\api\v1\config.py:6:6: E999 SyntaxError: invalid syntax
.\backend\app\api\v1\database.py:14:10: F821 undefined name 'APIRouter'
.\backend\app\api\v1\database.py:30:43: F821 undefined name 'Depends'
.\backend\app\api\v1\database.py:30:51: F821 undefined name 'get_table_manager'
.\backend\app\api\v1\database.py:39:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:39:80: E501 line too long (88 > 79 characters)
.\backend\app\api\v1\database.py:78:63: F821 undefined name 'e'
.\backend\app\api\v1\database.py:79:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:79:80: F821 undefined name 'e'
.\backend\app\api\v1\database.py:85:43: F821 undefined name 'Depends'
.\backend\app\api\v1\database.py:85:51: F821 undefined name 'get_table_manager'
.\backend\app\api\v1\database.py:94:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:94:80: E501 line too long (88 > 79 characters)
.\backend\app\api\v1\database.py:133:66: F821 undefined name 'e'
.\backend\app\api\v1\database.py:134:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:134:80: F821 undefined name 'e'
.\backend\app\api\v1\database.py:139:58: F821 undefined name 'Depends'
.\backend\app\api\v1\database.py:139:66: F821 undefined name 'get_table_manager'
.\backend\app\api\v1\database.py:139:80: E501 line too long (83 > 79 characters)
.\backend\app\api\v1\database.py:147:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:149:80: E501 line too long (85 > 79 characters)
.\backend\app\api\v1\database.py:169:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:169:80: E501 line too long (86 > 79 characters)
.\backend\app\api\v1\database.py:171:12: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:174:80: E501 line too long (86 > 79 characters)
.\backend\app\api\v1\database.py:174:100: F821 undefined name 'e'
.\backend\app\api\v1\database.py:175:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:175:74: F821 undefined name 'e'
.\backend\app\api\v1\database.py:180:58: F821 undefined name 'Depends'
.\backend\app\api\v1\database.py:180:66: F821 undefined name 'get_table_manager'
.\backend\app\api\v1\database.py:180:80: E501 line too long (83 > 79 characters)
.\backend\app\api\v1\database.py:188:80: E501 line too long (85 > 79 characters)
.\backend\app\api\v1\database.py:201:80: E501 line too long (107 > 79 characters)
.\backend\app\api\v1\database.py:204:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:204:80: E501 line too long (88 > 79 characters)
.\backend\app\api\v1\database.py:206:12: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:209:66: F821 undefined name 'e'
.\backend\app\api\v1\database.py:210:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:210:80: F821 undefined name 'e'
.\backend\app\api\v1\database.py:215:60: F821 undefined name 'Depends'
.\backend\app\api\v1\database.py:215:68: F821 undefined name 'get_table_manager'
.\backend\app\api\v1\database.py:215:80: E501 line too long (85 > 79 characters)
.\backend\app\api\v1\database.py:225:80: E501 line too long (80 > 79 characters)
.\backend\app\api\v1\database.py:228:80: E501 line too long (86 > 79 characters)
.\backend\app\api\v1\database.py:230:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:230:80: E501 line too long (86 > 79 characters)
.\backend\app\api\v1\database.py:232:12: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:235:83: F821 undefined name 'e'
.\backend\app\api\v1\database.py:236:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:236:74: F821 undefined name 'e'
.\backend\app\api\v1\database.py:241:60: F821 undefined name 'Depends'
.\backend\app\api\v1\database.py:241:68: F821 undefined name 'get_table_manager'
.\backend\app\api\v1\database.py:241:80: E501 line too long (85 > 79 characters)
.\backend\app\api\v1\database.py:256:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:256:80: E501 line too long (85 > 79 characters)
.\backend\app\api\v1\database.py:258:12: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:261:80: F821 undefined name 'e'
.\backend\app\api\v1\database.py:262:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database.py:262:80: F821 undefined name 'e'
.\backend\app\api\v1\database.py:267:43: F821 undefined name 'Depends'
.\backend\app\api\v1\database.py:267:51: F821 undefined name 'get_table_manager'
.\backend\app\api\v1\database.py:289:63: F821 undefined name 'e'
.\backend\app\api\v1\database.py:296:40: F821 undefined name 'e'
.\backend\app\api\v1\database.py:299:51: F821 undefined name 'e'
.\backend\app\api\v1\database_health.py:10:10: F821 undefined name 'APIRouter'
.\backend\app\api\v1\database_health.py:14:38: F821 undefined name 'Dict'
.\backend\app\api\v1\database_health.py:14:48: F821 undefined name 'Any'
.\backend\app\api\v1\database_health.py:38:63: F821 undefined name 'e'
.\backend\app\api\v1\database_health.py:39:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database_health.py:39:89: F821 undefined name 'e'
.\backend\app\api\v1\database_health.py:43:32: F821 undefined name 'Dict'
.\backend\app\api\v1\database_health.py:43:42: F821 undefined name 'Any'
.\backend\app\api\v1\database_health.py:57:63: F821 undefined name 'e'
.\backend\app\api\v1\database_health.py:58:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\database_health.py:58:89: F821 undefined name 'e'
.\backend\app\api\v1\database_health.py:62:41: F821 undefined name 'Dict'
.\backend\app\api\v1\database_health.py:62:51: F821 undefined name 'Any'
.\backend\app\api\v1\database_health.py:79:63: F821 undefined name 'e'
.\backend\app\api\v1\database_health.py:80:75: F821 undefined name 'e'
.\backend\app\api\v1\enhanced_sync.py:8:17: E999 IndentationError: unexpected indent
.\backend\app\api\v1\excel_translation.py:7:13: E999 IndentationError: unexpected indent
.\backend\app\api\v1\field_config_api.py:8:1: E265 block comment should start with '# '
.\backend\app\api\v1\field_config_api.py:18:10: F821 undefined name 'APIRouter'
.\backend\app\api\v1\field_config_api.py:24:18: F821 undefined name 'Dict'
.\backend\app\api\v1\field_config_api.py:24:28: F821 undefined name 'Any'
.\backend\app\api\v1\field_config_api.py:42:15: F821 undefined name 'List'
.\backend\app\api\v1\field_config_api.py:42:20: F821 undefined name 'Dict'
.\backend\app\api\v1\field_config_api.py:42:30: F821 undefined name 'Any'
.\backend\app\api\v1\field_config_api.py:48:18: F821 undefined name 'Dict'
.\backend\app\api\v1\field_config_api.py:48:28: F821 undefined name 'Any'
.\backend\app\api\v1\field_config_api.py:54:11: F821 undefined name 'Optional'
.\backend\app\api\v1\field_config_api.py:54:20: F821 undefined name 'Dict'
.\backend\app\api\v1\field_config_api.py:54:30: F821 undefined name 'Any'
.\backend\app\api\v1\field_config_api.py:63:38: F821 undefined name 'Query'
.\backend\app\api\v1\field_config_api.py:79:80: E501 line too long (80 > 79 characters)
.\backend\app\api\v1\field_config_api.py:88:80: E501 line too long (81 > 79 characters)
.\backend\app\api\v1\field_config_api.py:91:80: E501 line too long (88 > 79 characters)
.\backend\app\api\v1\field_config_api.py:91:102: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:92:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:92:86: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:96:80: E501 line too long (83 > 79 characters)
.\backend\app\api\v1\field_config_api.py:126:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:134:23: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:136:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:136:86: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:170:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:178:23: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:180:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:180:86: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:185:38: F821 undefined name 'Query'
.\backend\app\api\v1\field_config_api.py:200:80: E501 line too long (87 > 79 characters)
.\backend\app\api\v1\field_config_api.py:209:80: E501 line too long (88 > 79 characters)
.\backend\app\api\v1\field_config_api.py:209:102: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:210:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:210:86: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:212:16: F821 undefined name 'config'
.\backend\app\api\v1\field_config_api.py:226:80: E501 line too long (81 > 79 characters)
.\backend\app\api\v1\field_config_api.py:229:85: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:230:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:230:86: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:246:80: E501 line too long (81 > 79 characters)
.\backend\app\api\v1\field_config_api.py:250:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:253:80: E501 line too long (84 > 79 characters)
.\backend\app\api\v1\field_config_api.py:267:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:269:12: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:272:85: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:273:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:273:86: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:308:60: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:309:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:309:86: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:313:65: F821 undefined name 'Query'
.\backend\app\api\v1\field_config_api.py:313:80: E501 line too long (80 > 79 characters)
.\backend\app\api\v1\field_config_api.py:327:80: E501 line too long (83 > 79 characters)
.\backend\app\api\v1\field_config_api.py:337:85: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:338:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:338:86: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:366:22: F821 undefined name 'Query'
.\backend\app\api\v1\field_config_api.py:367:20: F821 undefined name 'Query'
.\backend\app\api\v1\field_config_api.py:372:80: E501 line too long (84 > 79 characters)
.\backend\app\api\v1\field_config_api.py:375:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:375:80: E501 line too long (85 > 79 characters)
.\backend\app\api\v1\field_config_api.py:400:55: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:401:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:401:80: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:421:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:470:80: E501 line too long (87 > 79 characters)
.\backend\app\api\v1\field_config_api.py:486:80: E501 line too long (80 > 79 characters)
.\backend\app\api\v1\field_config_api.py:497:23: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:499:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:499:86: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:522:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:577:80: E501 line too long (94 > 79 characters)
.\backend\app\api\v1\field_config_api.py:585:80: E501 line too long (88 > 79 characters)
.\backend\app\api\v1\field_config_api.py:585:102: F821 undefined name 'e'
.\backend\app\api\v1\field_config_api.py:586:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\field_config_api.py:586:86: F821 undefined name 'e'
.\backend\app\api\v1\maintenance.py:4:1: E265 block comment should start with '# '
.\backend\app\api\v1\maintenance.py:13:10: F821 undefined name 'APIRouter'
.\backend\app\api\v1\maintenance.py:26:60: F821 undefined name 'e'
.\backend\app\api\v1\maintenance.py:27:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\maintenance.py:27:86: F821 undefined name 'e'
.\backend\app\api\v1\maintenance.py:40:60: F821 undefined name 'e'
.\backend\app\api\v1\maintenance.py:41:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\maintenance.py:41:80: F821 undefined name 'e'
.\backend\app\api\v1\maintenance.py:54:66: F821 undefined name 'e'
.\backend\app\api\v1\maintenance.py:55:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\maintenance.py:55:86: F821 undefined name 'e'
.\backend\app\api\v1\maintenance.py:68:66: F821 undefined name 'e'
.\backend\app\api\v1\maintenance.py:69:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\maintenance.py:69:86: F821 undefined name 'e'
.\backend\app\api\v1\maintenance.py:86:66: F821 undefined name 'e'
.\backend\app\api\v1\maintenance.py:87:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\maintenance.py:87:80: F821 undefined name 'e'
.\backend\app\api\v1\maintenance.py:100:75: F821 undefined name 'e'
.\backend\app\api\v1\maintenance.py:101:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\maintenance.py:101:80: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:10:1: F811 redefinition of unused 'DatabaseManager' from line 9
.\backend\app\api\v1\monitor.py:12:1: F811 redefinition of unused 'pyodbc' from line 4
.\backend\app\api\v1\monitor.py:15:1: F811 redefinition of unused 'settings' from line 11
.\backend\app\api\v1\monitor.py:16:1: F811 redefinition of unused 'pyodbc' from line 12
.\backend\app\api\v1\monitor.py:29:10: F821 undefined name 'APIRouter'
.\backend\app\api\v1\monitor.py:74:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:78:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:80:57: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:81:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:81:83: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:103:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:106:57: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:107:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:107:83: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:143:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:146:57: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:147:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:147:83: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:169:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:172:57: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:173:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:173:83: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:184:24: F821 undefined name 'table_manager'
.\backend\app\api\v1\monitor.py:187:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:187:80: E501 line too long (88 > 79 characters)
.\backend\app\api\v1\monitor.py:226:63: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:227:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:227:89: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:253:80: E501 line too long (96 > 79 characters)
.\backend\app\api\v1\monitor.py:262:80: E501 line too long (107 > 79 characters)
.\backend\app\api\v1\monitor.py:282:80: E501 line too long (86 > 79 characters)
.\backend\app\api\v1\monitor.py:287:63: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:296:34: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:299:54: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:303:39: F821 undefined name 'HealthCheckResponse'
.\backend\app\api\v1\monitor.py:304:39: F821 undefined name 'HealthCheckResponse'
.\backend\app\api\v1\monitor.py:318:80: E501 line too long (86 > 79 characters)
.\backend\app\api\v1\monitor.py:334:80: E501 line too long (88 > 79 characters)
.\backend\app\api\v1\monitor.py:338:60: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:339:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:339:86: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:342:40: F821 undefined name 'MetricsResponse'
.\backend\app\api\v1\monitor.py:359:60: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:360:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:360:86: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:365:12: F821 undefined name 'Optional'
.\backend\app\api\v1\monitor.py:367:13: F821 undefined name 'Optional'
.\backend\app\api\v1\monitor.py:368:13: F821 undefined name 'Optional'
.\backend\app\api\v1\monitor.py:388:60: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:389:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:389:86: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:392:38: F821 undefined name 'Dict'
.\backend\app\api\v1\monitor.py:427:57: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:427:80: E501 line too long (85 > 79 characters)
.\backend\app\api\v1\monitor.py:430:35: F821 undefined name 'Dict'
.\backend\app\api\v1\monitor.py:433:80: E501 line too long (81 > 79 characters)
.\backend\app\api\v1\monitor.py:436:42: F821 undefined name 'Dict'
.\backend\app\api\v1\monitor.py:508:80: E501 line too long (115 > 79 characters)
.\backend\app\api\v1\monitor.py:535:72: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:551:80: E501 line too long (83 > 79 characters)
.\backend\app\api\v1\monitor.py:571:80: E501 line too long (84 > 79 characters)
.\backend\app\api\v1\monitor.py:572:80: E501 line too long (83 > 79 characters)
.\backend\app\api\v1\monitor.py:581:77: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:581:80: E501 line too long (80 > 79 characters)
.\backend\app\api\v1\monitor.py:600:80: E501 line too long (83 > 79 characters)
.\backend\app\api\v1\monitor.py:613:80: E501 line too long (103 > 79 characters)
.\backend\app\api\v1\monitor.py:615:80: E501 line too long (83 > 79 characters)
.\backend\app\api\v1\monitor.py:622:80: E501 line too long (83 > 79 characters)
.\backend\app\api\v1\monitor.py:641:87: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:646:54: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:647:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:647:80: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:696:80: E501 line too long (81 > 79 characters)
.\backend\app\api\v1\monitor.py:705:67: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:737:80: E501 line too long (87 > 79 characters)
.\backend\app\api\v1\monitor.py:759:80: E501 line too long (86 > 79 characters)
.\backend\app\api\v1\monitor.py:762:80: E501 line too long (86 > 79 characters)
.\backend\app\api\v1\monitor.py:765:80: E501 line too long (86 > 79 characters)
.\backend\app\api\v1\monitor.py:774:60: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:824:80: E501 line too long (87 > 79 characters)
.\backend\app\api\v1\monitor.py:828:80: E501 line too long (81 > 79 characters)
.\backend\app\api\v1\monitor.py:854:80: E501 line too long (80 > 79 characters)
.\backend\app\api\v1\monitor.py:899:71: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:911:80: E501 line too long (87 > 79 characters)
.\backend\app\api\v1\monitor.py:931:57: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:932:76: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:972:80: E501 line too long (81 > 79 characters)
.\backend\app\api\v1\monitor.py:994:60: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:995:79: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:1013:80: E501 line too long (85 > 79 characters)
.\backend\app\api\v1\monitor.py:1017:80: E501 line too long (83 > 79 characters)
.\backend\app\api\v1\monitor.py:1022:59: F821 undefined name 'e'
.\backend\app\api\v1\monitor.py:1023:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\monitor.py:1023:85: F821 undefined name 'e'
.\backend\app\api\v1\realtime_logs.py:9:6: E999 SyntaxError: invalid syntax
.\backend\app\api\v1\sync.py:6:13: E999 IndentationError: unexpected indent
.\backend\app\api\v1\sync_status.py:4:1: E265 block comment should start with '# '
.\backend\app\api\v1\sync_status.py:13:10: F821 undefined name 'APIRouter'
.\backend\app\api\v1\sync_status.py:30:57: F821 undefined name 'e'
.\backend\app\api\v1\sync_status.py:31:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\sync_status.py:31:57: F821 undefined name 'e'
.\backend\app\api\v1\sync_status.py:55:75: F821 undefined name 'e'
.\backend\app\api\v1\sync_status.py:56:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\sync_status.py:56:57: F821 undefined name 'e'
.\backend\app\api\v1\sync_status.py:69:57: F821 undefined name 'e'
.\backend\app\api\v1\sync_status.py:70:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\sync_status.py:70:57: F821 undefined name 'e'
.\backend\app\api\v1\sync_status.py:98:66: F821 undefined name 'e'
.\backend\app\api\v1\sync_status.py:99:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\sync_status.py:99:57: F821 undefined name 'e'
.\backend\app\api\v1\tasks.py:3:6: E999 SyntaxError: invalid syntax
.\backend\app\api\v1\unified_field_config.py:4:1: F811 redefinition of unused 'Response' from line 3
.\backend\app\api\v1\unified_field_config.py:13:10: F821 undefined name 'APIRouter'
.\backend\app\api\v1\unified_field_config.py:31:62: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:36:38: F821 undefined name 'List'
.\backend\app\api\v1\unified_field_config.py:36:43: F821 undefined name 'Dict'
.\backend\app\api\v1\unified_field_config.py:36:53: F821 undefined name 'Any'
.\backend\app\api\v1\unified_field_config.py:103:80: E501 line too long (84 > 79 characters)
.\backend\app\api\v1\unified_field_config.py:126:51: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:126:71: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:126:80: E501 line too long (80 > 79 characters)
.\backend\app\api\v1\unified_field_config.py:128:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:128:86: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:134:20: F821 undefined name 'Query'
.\backend\app\api\v1\unified_field_config.py:135:32: F821 undefined name 'Query'
.\backend\app\api\v1\unified_field_config.py:136:6: F821 undefined name 'Dict'
.\backend\app\api\v1\unified_field_config.py:149:19: F821 undefined name 'get_field_manager'
.\backend\app\api\v1\unified_field_config.py:175:23: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:177:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:177:86: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:184:20: F821 undefined name 'Query'
.\backend\app\api\v1\unified_field_config.py:185:25: F821 undefined name 'Query'
.\backend\app\api\v1\unified_field_config.py:186:6: F821 undefined name 'Dict'
.\backend\app\api\v1\unified_field_config.py:200:19: F821 undefined name 'get_field_manager'
.\backend\app\api\v1\unified_field_config.py:225:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:233:23: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:235:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:235:86: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:256:20: F821 undefined name 'Query'
.\backend\app\api\v1\unified_field_config.py:257:25: F821 undefined name 'Query'
.\backend\app\api\v1\unified_field_config.py:258:6: F821 undefined name 'Dict'
.\backend\app\api\v1\unified_field_config.py:272:19: F821 undefined name 'get_field_manager'
.\backend\app\api\v1\unified_field_config.py:303:23: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:304:29: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:307:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:307:86: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:320:20: F821 undefined name 'Query'
.\backend\app\api\v1\unified_field_config.py:321:22: F821 undefined name 'Query'
.\backend\app\api\v1\unified_field_config.py:322:6: F821 undefined name 'Dict'
.\backend\app\api\v1\unified_field_config.py:336:19: F821 undefined name 'get_field_manager'
.\backend\app\api\v1\unified_field_config.py:337:80: E501 line too long (83 > 79 characters)
.\backend\app\api\v1\unified_field_config.py:365:23: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:366:29: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:369:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:369:86: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:390:20: F821 undefined name 'Query'
.\backend\app\api\v1\unified_field_config.py:391:20: F821 undefined name 'Query'
.\backend\app\api\v1\unified_field_config.py:392:6: F821 undefined name 'Dict'
.\backend\app\api\v1\unified_field_config.py:406:19: F821 undefined name 'get_field_manager'
.\backend\app\api\v1\unified_field_config.py:431:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:439:23: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:441:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:441:92: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:445:58: F821 undefined name 'Dict'
.\backend\app\api\v1\unified_field_config.py:456:19: F821 undefined name 'get_field_manager'
.\backend\app\api\v1\unified_field_config.py:467:19: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:467:80: E501 line too long (81 > 79 characters)
.\backend\app\api\v1\unified_field_config.py:470:79: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:471:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:471:80: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:475:36: F821 undefined name 'Dict'
.\backend\app\api\v1\unified_field_config.py:483:19: F821 undefined name 'get_field_manager'
.\backend\app\api\v1\unified_field_config.py:506:60: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:507:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:507:86: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:512:38: F821 undefined name 'Query'
.\backend\app\api\v1\unified_field_config.py:513:6: F821 undefined name 'Dict'
.\backend\app\api\v1\unified_field_config.py:525:19: F821 undefined name 'get_field_manager'
.\backend\app\api\v1\unified_field_config.py:559:23: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:561:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:561:86: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:565:45: F821 undefined name 'List'
.\backend\app\api\v1\unified_field_config.py:565:50: F821 undefined name 'Dict'
.\backend\app\api\v1\unified_field_config.py:565:60: F821 undefined name 'Any'
.\backend\app\api\v1\unified_field_config.py:591:66: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:592:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:592:86: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:603:55: F821 undefined name 'Query'
.\backend\app\api\v1\unified_field_config.py:603:80: E501 line too long (84 > 79 characters)
.\backend\app\api\v1\unified_field_config.py:604:6: F821 undefined name 'Dict'
.\backend\app\api\v1\unified_field_config.py:617:19: F821 undefined name 'get_field_manager'
.\backend\app\api\v1\unified_field_config.py:637:23: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:638:29: F821 undefined name 'e'
.\backend\app\api\v1\unified_field_config.py:641:15: F821 undefined name 'HTTPException'
.\backend\app\api\v1\unified_field_config.py:641:95: F821 undefined name 'e'
.\backend\app\core\code_quality.py:70:6: E999 IndentationError: expected an indented block after function definition on line 69
.\backend\app\core\config.py:72:6: F821 undefined name 'field_validator'
.\backend\app\core\config.py:103:80: E501 line too long (104 > 79 characters)
.\backend\app\core\config.py:106:20: F821 undefined name 'ConfigDict'
.\backend\app\core\config.py:128:80: E501 line too long (84 > 79 characters)
.\backend\app\core\config.py:187:63: F821 undefined name 'e'
.\backend\app\core\config.py:227:80: E501 line too long (87 > 79 characters)
.\backend\app\core\config.py:235:80: E501 line too long (89 > 79 characters)
.\backend\app\core\config.py:260:80: E501 line too long (85 > 79 characters)
.\backend\app\core\config.py:268:80: E501 line too long (83 > 79 characters)
.\backend\app\core\config.py:276:80: E501 line too long (86 > 79 characters)
.\backend\app\core\database.py:32:23: F821 undefined name 'create_engine'
.\backend\app\core\database.py:48:69: F821 undefined name 'e'
.\backend\app\core\database.py:58:80: E501 line too long (82 > 79 characters)
.\backend\app\core\database.py:60:24: F821 undefined name 'create_async_engine'
.\backend\app\core\database.py:79:20: F821 undefined name 'AsyncSession'
.\backend\app\core\database.py:88:69: F821 undefined name 'e'
.\backend\app\core\database.py:110:63: F821 undefined name 'e'
.\backend\app\core\database.py:114:77: F821 undefined name 'e'
.\backend\app\core\database.py:127:17: F821 undefined name 'text'
.\backend\app\core\database.py:127:80: E501 line too long (81 > 79 characters)
.\backend\app\core\database.py:135:33: F821 undefined name 'text'
.\backend\app\core\database.py:135:80: E501 line too long (84 > 79 characters)
.\backend\app\core\database.py:143:64: F821 undefined name 'e'
.\backend\app\core\database.py:154:57: F821 undefined name 'e'
.\backend\app\core\database.py:160:34: F821 undefined name 'AsyncSession'
.\backend\app\core\database.py:169:69: F821 undefined name 'e'
.\backend\app\core\database.py:183:67: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:59:27: F821 undefined name 'List'
.\backend\app\core\database_connection_pool.py:60:37: F821 undefined name 'List'
.\backend\app\core\database_connection_pool.py:61:34: F821 undefined name 'List'
.\backend\app\core\database_connection_pool.py:100:80: E501 line too long (84 > 79 characters)
.\backend\app\core\database_connection_pool.py:102:80: E501 line too long (80 > 79 characters)
.\backend\app\core\database_connection_pool.py:105:70: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:131:70: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:152:67: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:159:33: F821 undefined name 'Optional'
.\backend\app\core\database_connection_pool.py:159:42: F821 undefined name 'Dict'
.\backend\app\core\database_connection_pool.py:160:10: F821 undefined name 'List'
.\backend\app\core\database_connection_pool.py:160:15: F821 undefined name 'Dict'
.\backend\app\core\database_connection_pool.py:192:71: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:195:58: F821 undefined name 'List'
.\backend\app\core\database_connection_pool.py:195:63: F821 undefined name 'Dict'
.\backend\app\core\database_connection_pool.py:221:74: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:234:64: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:272:58: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:333:62: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:355:58: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:366:64: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:378:62: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:398:80: E501 line too long (88 > 79 characters)
.\backend\app\core\database_connection_pool.py:426:64: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:432:80: E501 line too long (88 > 79 characters)
.\backend\app\core\database_connection_pool.py:438:64: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:464:64: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:466:33: F821 undefined name 'Dict'
.\backend\app\core\database_connection_pool.py:466:43: F821 undefined name 'Any'
.\backend\app\core\database_connection_pool.py:502:73: F821 undefined name 'e'
.\backend\app\core\database_connection_pool.py:523:61: F821 undefined name 'e'
.\backend\app\core\database_manager.py:57:22: E999 SyntaxError: unterminated string literal (detected at line 57)
.\backend\app\core\exceptions.py:60:6: E999 IndentationError: expected an indented block after function definition on line 59
.\backend\app\core\optimized_retry.py:5:13: E999 IndentationError: unexpected indent
.\backend\app\main.py:9:9: E999 IndentationError: unexpected indent
.\backend\app\main_original.py:8:6: E999 SyntaxError: invalid syntax
.\backend\app\middleware\access_log.py:19:39: F821 undefined name 'Request'
.\backend\app\middleware\access_log.py:19:72: F821 undefined name 'Response'
.\backend\app\middleware\access_log.py:19:80: E501 line too long (80 > 79 characters)
.\backend\app\schemas\base.py:12:14: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:13:11: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:13:20: F821 undefined name 'Any'
.\backend\app\schemas\base.py:14:16: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:15:17: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:23:16: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:24:17: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:30:11: F821 undefined name 'Any'
.\backend\app\schemas\base.py:37:12: F821 undefined name 'Dict'
.\backend\app\schemas\base.py:37:22: F821 undefined name 'Any'
.\backend\app\schemas\base.py:55:11: F821 undefined name 'Dict'
.\backend\app\schemas\base.py:55:21: F821 undefined name 'Any'
.\backend\app\schemas\base.py:98:18: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:99:19: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:101:20: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:108:13: F821 undefined name 'List'
.\backend\app\schemas\base.py:109:15: F821 undefined name 'List'
.\backend\app\schemas\base.py:117:18: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:118:16: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:119:17: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:120:19: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:121:23: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:122:15: F821 undefined name 'Optional'
.\backend\app\schemas\base.py:122:24: F821 undefined name 'Dict'
.\backend\app\schemas\base.py:122:34: F821 undefined name 'Any'
.\backend\app\schemas\base.py:123:20: F821 undefined name 'Optional'
.\backend\app\schemas\config.py:9:26: F821 undefined name 'BaseModel'
.\backend\app\schemas\config.py:12:13: F821 undefined name 'Dict'
.\backend\app\schemas\config.py:12:23: F821 undefined name 'FieldInfo'
.\backend\app\schemas\config.py:32:27: F821 undefined name 'BaseResponse'
.\backend\app\schemas\config.py:35:11: F821 undefined name 'Dict'
.\backend\app\schemas\config.py:35:21: F821 undefined name 'Any'
.\backend\app\schemas\config.py:65:26: F821 undefined name 'BaseResponse'
.\backend\app\schemas\config.py:68:11: F821 undefined name 'Dict'
.\backend\app\schemas\config.py:68:21: F821 undefined name 'Any'
.\backend\app\schemas\config.py:96:27: F821 undefined name 'BaseModel'
.\backend\app\schemas\config.py:99:16: F821 undefined name 'Optional'
.\backend\app\schemas\config.py:101:27: F821 undefined name 'Field'
.\backend\app\schemas\config.py:113:28: F821 undefined name 'BaseResponse'
.\backend\app\schemas\config.py:116:11: F821 undefined name 'Dict'
.\backend\app\schemas\config.py:116:21: F821 undefined name 'Any'
.\backend\app\schemas\config.py:143:33: F821 undefined name 'BaseModel'
.\backend\app\schemas\config.py:146:14: F821 undefined name 'Optional'
.\backend\app\schemas\config.py:146:23: F821 undefined name 'List'
.\backend\app\schemas\config.py:160:28: F821 undefined name 'BaseResponse'
.\backend\app\schemas\config.py:163:11: F821 undefined name 'Dict'
.\backend\app\schemas\config.py:163:21: F821 undefined name 'Any'
.\backend\app\schemas\config.py:193:32: F821 undefined name 'BaseResponse'
.\backend\app\schemas\config.py:196:11: F821 undefined name 'ValidationResult'
.\backend\app\schemas\config.py:200:24: F821 undefined name 'BaseModel'
.\backend\app\schemas\config.py:212:16: F821 undefined name 'Optional'
.\backend\app\schemas\config.py:213:18: F821 undefined name 'Optional'
.\backend\app\schemas\config.py:216:23: F821 undefined name 'BaseModel'
.\backend\app\schemas\config.py:227:18: F821 undefined name 'Optional'
.\backend\app\schemas\config.py:228:19: F821 undefined name 'Optional'
.\backend\app\schemas\config.py:229:23: F821 undefined name 'Optional'
.\backend\app\schemas\config.py:229:32: F821 undefined name 'Dict'
.\backend\app\schemas\config.py:229:42: F821 undefined name 'Any'
.\backend\app\schemas\config.py:230:20: F821 undefined name 'Optional'
.\backend\app\schemas\config.py:230:29: F821 undefined name 'Dict'
.\backend\app\schemas\config.py:232:20: F821 undefined name 'Optional'
.\backend\app\schemas\config.py:235:25: F821 undefined name 'BaseModel'
.\backend\app\schemas\config.py:243:19: F821 undefined name 'Optional'
.\backend\app\schemas\config.py:246:13: F821 undefined name 'Dict'
.\backend\app\schemas\database.py:25:14: F821 undefined name 'List'
.\backend\app\schemas\database.py:25:19: F821 undefined name 'Dict'
.\backend\app\schemas\database.py:25:29: F821 undefined name 'Any'
.\backend\app\schemas\database.py:43:25: F821 undefined name 'List'
.\backend\app\schemas\database.py:44:21: F821 undefined name 'List'
.\backend\app\schemas\database.py:45:22: F821 undefined name 'List'
.\backend\app\schemas\database.py:56:25: F821 undefined name 'Dict'
.\backend\app\schemas\database.py:56:35: F821 undefined name 'Dict'
.\backend\app\schemas\database.py:56:45: F821 undefined name 'Any'
.\backend\app\schemas\database.py:79:11: F821 undefined name 'Dict'
.\backend\app\schemas\database.py:79:21: F821 undefined name 'Any'
.\backend\app\schemas\monitor.py:15:23: F821 undefined name 'Optional'
.\backend\app\schemas\monitor.py:16:17: F821 undefined name 'Optional'
.\backend\app\schemas\monitor.py:17:20: F821 undefined name 'Optional'
.\backend\app\schemas\monitor.py:23:22: F821 undefined name 'Optional'
.\backend\app\schemas\monitor.py:23:31: F821 undefined name 'Dict'
.\backend\app\schemas\monitor.py:39:19: F821 undefined name 'Dict'
.\backend\app\schemas\monitor.py:39:29: F821 undefined name 'Any'
.\backend\app\schemas\monitor.py:40:22: F821 undefined name 'Dict'
.\backend\app\schemas\monitor.py:40:32: F821 undefined name 'Any'
.\backend\app\schemas\monitor.py:41:15: F821 undefined name 'Dict'
.\backend\app\schemas\monitor.py:41:25: F821 undefined name 'Any'
.\backend\app\schemas\monitor.py:42:13: F821 undefined name 'Dict'
.\backend\app\schemas\monitor.py:42:23: F821 undefined name 'Any'
.\backend\app\schemas\monitor.py:48:11: F821 undefined name 'Dict'
.\backend\app\schemas\monitor.py:48:21: F821 undefined name 'Any'
.\backend\app\schemas\monitor.py:65:80: E501 line too long (84 > 79 characters)
.\backend\app\schemas\monitor.py:92:13: F821 undefined name 'Optional'
.\backend\app\schemas\monitor.py:94:14: F821 undefined name 'Optional'
.\backend\app\schemas\monitor.py:94:23: F821 undefined name 'Dict'
.\backend\app\schemas\monitor.py:94:33: F821 undefined name 'Any'
.\backend\app\schemas\monitor.py:100:11: F821 undefined name 'List'
.\backend\app\schemas\realtime_log.py:38:17: F821 undefined name 'Optional'
.\backend\app\schemas\realtime_log.py:39:12: F821 undefined name 'Optional'
.\backend\app\schemas\realtime_log.py:39:21: F821 undefined name 'Dict'
.\backend\app\schemas\realtime_log.py:39:31: F821 undefined name 'Any'
.\backend\app\schemas\realtime_log.py:47:11: F821 undefined name 'Optional'
.\backend\app\schemas\realtime_log.py:47:20: F821 undefined name 'Dict'
.\backend\app\schemas\realtime_log.py:47:30: F821 undefined name 'Any'
.\backend\app\schemas\realtime_log.py:55:11: F821 undefined name 'Dict'
.\backend\app\schemas\realtime_log.py:55:21: F821 undefined name 'Any'
.\backend\app\schemas\realtime_log.py:63:11: F821 undefined name 'Dict'
.\backend\app\schemas\realtime_log.py:63:21: F821 undefined name 'Any'
.\backend\app\schemas\sync.py:9:19: F821 undefined name 'BaseModel'
.\backend\app\schemas\sync.py:12:22: F821 undefined name 'Field'
.\backend\app\schemas\sync.py:14:19: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:15:14: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:15:23: F821 undefined name 'Dict'
.\backend\app\schemas\sync.py:15:33: F821 undefined name 'Any'
.\backend\app\schemas\sync.py:23:80: E501 line too long (80 > 79 characters)
.\backend\app\schemas\sync.py:28:20: F821 undefined name 'BaseResponse'
.\backend\app\schemas\sync.py:31:11: F821 undefined name 'Dict'
.\backend\app\schemas\sync.py:31:21: F821 undefined name 'Any'
.\backend\app\schemas\sync.py:50:26: F821 undefined name 'BaseResponse'
.\backend\app\schemas\sync.py:53:11: F821 undefined name 'TaskStatus'
.\backend\app\schemas\sync.py:63:80: E501 line too long (82 > 79 characters)
.\backend\app\schemas\sync.py:77:24: F821 undefined name 'BaseModel'
.\backend\app\schemas\sync.py:80:14: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:80:23: F821 undefined name 'List'
.\backend\app\schemas\sync.py:81:22: F821 undefined name 'Field'
.\backend\app\schemas\sync.py:82:27: F821 undefined name 'Field'
.\backend\app\schemas\sync.py:96:24: F821 undefined name 'BaseModel'
.\backend\app\schemas\sync.py:102:27: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:106:24: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:109:20: F821 undefined name 'BaseModel'
.\backend\app\schemas\sync.py:115:18: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:117:14: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:117:23: F821 undefined name 'Dict'
.\backend\app\schemas\sync.py:117:33: F821 undefined name 'Any'
.\backend\app\schemas\sync.py:118:17: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:119:14: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:122:24: F821 undefined name 'BaseModel'
.\backend\app\schemas\sync.py:131:19: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:132:23: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:136:20: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:140:22: F821 undefined name 'BaseModel'
.\backend\app\schemas\sync.py:149:21: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:150:19: F821 undefined name 'Dict'
.\backend\app\schemas\sync.py:150:29: F821 undefined name 'Dict'
.\backend\app\schemas\sync.py:150:39: F821 undefined name 'Any'
.\backend\app\schemas\sync.py:153:24: F821 undefined name 'BaseModel'
.\backend\app\schemas\sync.py:162:27: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:164:22: F821 undefined name 'List'
.\backend\app\schemas\sync.py:165:22: F821 undefined name 'List'
.\backend\app\schemas\sync.py:166:20: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:166:29: F821 undefined name 'Dict'
.\backend\app\schemas\sync.py:166:39: F821 undefined name 'Any'
.\backend\app\schemas\sync.py:169:25: F821 undefined name 'BaseModel'
.\backend\app\schemas\sync.py:172:13: F821 undefined name 'Optional'
.\backend\app\schemas\sync.py:178:24: F821 undefined name 'PaginatedResponse'
.\backend\app\schemas\sync.py:182:27: F821 undefined name 'PaginatedResponse'
.\backend\app\schemas\sync.py:186:30: F821 undefined name 'BaseResponse'
.\backend\app\services\advanced_data_transformer.py:183:6: E999 IndentationError: expected an indented block after function definition on line 182
.\backend\app\services\auto_recovery_manager_enhanced.py:85:6: E999 IndentationError: expected an indented block after function definition on line 84
.\backend\app\services\auto_sync_scheduler.py:13:9: E999 IndentationError: unexpected indent
.\backend\app\services\business_translation_rules.py:33:6: E999 IndentationError: expected an indented block after function definition on line 32
.\backend\app\services\config_persistence_service.py:21:6: E999 IndentationError: expected an indented block after function definition on line 20
.\backend\app\services\data_processor.py:20:6: E999 IndentationError: expected an indented block after function definition on line 19
.\backend\app\services\data_quality_inspector.py:77:6: E999 IndentationError: expected an indented block after function definition on line 76
.\backend\app\services\data_write_manager.py:17:13: E999 IndentationError: unexpected indent
.\backend\app\services\database_enhancement.py:2:9: E999 IndentationError: unexpected indent
.\backend\app\services\database_manager.py:19:18: E999 SyntaxError: invalid syntax
.\backend\app\services\database_table_manager.py:11:13: E999 IndentationError: unexpected indent
.\backend\app\services\enhanced_authenticator.py:47:6: E999 IndentationError: expected an indented block after function definition on line 46
.\backend\app\services\enhanced_error_handler.py:133:6: E999 IndentationError: expected an indented block after function definition on line 132
.\backend\app\services\enhanced_json_field_matcher.py:19:6: E999 IndentationError: expected an indented block after function definition on line 18
.\backend\app\services\enhanced_response_processor.py:46:6: E999 IndentationError: expected an indented block after function definition on line 45
.\backend\app\services\enhanced_ys_api_client.py:4:9: E999 IndentationError: unexpected indent
.\backend\app\services\excel_field_matcher.py:5:4: E999 IndentationError: unexpected indent
.\backend\app\services\excel_field_matcher_pretranslated.py:8:1: E265 block comment should start with '# '
.\backend\app\services\excel_field_matcher_pretranslated.py:26:16: F821 undefined name 'Optional'
.\backend\app\services\excel_field_matcher_pretranslated.py:27:18: F821 undefined name 'Optional'
.\backend\app\services\excel_field_matcher_pretranslated.py:28:18: F821 undefined name 'Optional'
.\backend\app\services\excel_field_matcher_pretranslated.py:50:80: E501 line too long (83 > 79 characters)
.\backend\app\services\excel_field_matcher_pretranslated.py:82:70: F821 undefined name 'e'
.\backend\app\services\excel_field_matcher_pretranslated.py:88:52: F821 undefined name 'List'
.\backend\app\services\excel_field_matcher_pretranslated.py:115:58: F821 undefined name 'e'
.\backend\app\services\excel_field_matcher_pretranslated.py:139:80: E501 line too long (88 > 79 characters)
.\backend\app\services\excel_field_matcher_pretranslated.py:142:56: F821 undefined name 'e'
.\backend\app\services\excel_field_matcher_pretranslated.py:147:10: F821 undefined name 'Dict'
.\backend\app\services\excel_field_matcher_pretranslated.py:171:58: F821 undefined name 'e'
.\backend\app\services\excel_field_matcher_pretranslated.py:174:60: F821 undefined name 'e'
.\backend\app\services\excel_field_matcher_pretranslated.py:176:37: F821 undefined name 'e'
.\backend\app\services\excel_field_matcher_pretranslated.py:180:50: F821 undefined name 'List'
.\backend\app\services\excel_field_matcher_pretranslated.py:181:10: F821 undefined name 'List'
.\backend\app\services\excel_field_matcher_pretranslated.py:207:64: F821 undefined name 'e'
.\backend\app\services\excel_field_matcher_pretranslated.py:211:24: F821 undefined name 'Dict'
.\backend\app\services\excel_field_matcher_pretranslated.py:216:80: E501 line too long (80 > 79 characters)
.\backend\app\services\excel_field_matcher_pretranslated.py:228:59: F821 undefined name 'e'
.\backend\app\services\excel_field_matcher_pretranslated.py:231:54: F821 undefined name 'Dict'
.\backend\app\services\excel_field_matcher_pretranslated.py:245:59: F821 undefined name 'e'
.\backend\app\services\excel_field_matcher_pretranslated.py:248:66: F821 undefined name 'List'
.\backend\app\services\excel_field_matcher_pretranslated.py:261:80: E501 line too long (88 > 79 characters)
.\backend\app\services\excel_field_matcher_pretranslated.py:268:59: F821 undefined name 'e'
.\backend\app\services\fast_sync_service.py:30:6: E999 IndentationError: expected an indented block after function definition on line 29
.\backend\app\services\field_analysis_service.py:18:6: E999 IndentationError: expected an indented block after function definition on line 17
.\backend\app\services\field_config_service.py:6:1: E265 block comment should start with '# '
.\backend\app\services\field_config_service.py:36:80: E501 line too long (83 > 79 characters)
.\backend\app\services\field_config_service.py:44:66: F821 undefined name 'Dict'
.\backend\app\services\field_config_service.py:47:80: E501 line too long (88 > 79 characters)
.\backend\app\services\field_config_service.py:57:19: F821 undefined name 'Optional'
.\backend\app\services\field_config_service.py:57:28: F821 undefined name 'List'
.\backend\app\services\field_config_service.py:57:33: F821 undefined name 'Dict'
.\backend\app\services\field_config_service.py:58:20: F821 undefined name 'Optional'
.\backend\app\services\field_config_service.py:61:10: F821 undefined name 'Dict'
.\backend\app\services\field_config_service.py:87:80: E501 line too long (87 > 79 characters)
.\backend\app\services\field_config_service.py:110:80: E501 line too long (82 > 79 characters)
.\backend\app\services\field_config_service.py:111:80: E501 line too long (88 > 79 characters)
.\backend\app\services\field_config_service.py:119:80: E501 line too long (81 > 79 characters)
.\backend\app\services\field_config_service.py:125:80: E501 line too long (83 > 79 characters)
.\backend\app\services\field_config_service.py:136:80: E501 line too long (83 > 79 characters)
.\backend\app\services\field_config_service.py:146:80: E501 line too long (83 > 79 characters)
.\backend\app\services\field_config_service.py:151:83: F821 undefined name 'e'
.\backend\app\services\field_config_service.py:156:17: F821 undefined name 'Any'
.\backend\app\services\field_config_service.py:160:10: F821 undefined name 'Dict'
.\backend\app\services\field_config_service.py:160:20: F821 undefined name 'Dict'
.\backend\app\services\field_config_service.py:192:80: E501 line too long (83 > 79 characters)
.\backend\app\services\field_config_service.py:199:80: E501 line too long (86 > 79 characters)
.\backend\app\services\field_config_service.py:255:22: F821 undefined name 'Optional'
.\backend\app\services\field_config_service.py:256:22: F821 undefined name 'Optional'
.\backend\app\services\field_config_service.py:257:10: F821 undefined name 'Dict'
.\backend\app\services\field_config_service.py:310:89: F821 undefined name 'e'
.\backend\app\services\field_config_service.py:337:66: F821 undefined name 'Dict'
.\backend\app\services\field_config_service.py:356:89: F821 undefined name 'e'
.\backend\app\services\field_config_service.py:358:61: F821 undefined name 'Optional'
.\backend\app\services\field_config_service.py:358:70: F821 undefined name 'Dict'
.\backend\app\services\field_config_service.py:378:89: F821 undefined name 'e'
.\backend\app\services\field_extractor_service.py:23:6: E999 IndentationError: expected an indented block after function definition on line 22
.\backend\app\services\field_validation_service.py:28:6: E999 IndentationError: expected an indented block after function definition on line 27
.\backend\app\services\field_value_mapping_service.py:20:6: E999 IndentationError: expected an indented block after function definition on line 19
.\backend\app\services\intelligent_field_mapper.py:18:2: F821 undefined name 'dataclass'
.\backend\app\services\intelligent_field_mapper.py:25:2: F821 undefined name 'dataclass'
.\backend\app\services\intelligent_field_mapper.py:35:2: F821 undefined name 'dataclass'
.\backend\app\services\intelligent_field_mapper.py:49:20: F821 undefined name 'Dict'
.\backend\app\services\intelligent_field_mapper.py:50:23: F821 undefined name 'Dict'
.\backend\app\services\intelligent_field_mapper.py:55:2: F821 undefined name 'dataclass'
.\backend\app\services\intelligent_field_mapper.py:67:13: F821 undefined name 'Dict'
.\backend\app\services\intelligent_field_mapper.py:85:80: E501 line too long (83 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:99:23: F821 undefined name 'Optional'
.\backend\app\services\intelligent_field_mapper.py:99:32: F821 undefined name 'Any'
.\backend\app\services\intelligent_field_mapper.py:125:80: E501 line too long (102 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:133:80: E501 line too long (85 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:145:17: F541 f-string is missing placeholders
.\backend\app\services\intelligent_field_mapper.py:154:26: F541 f-string is missing placeholders
.\backend\app\services\intelligent_field_mapper.py:154:85: F821 undefined name 'e'
.\backend\app\services\intelligent_field_mapper.py:159:10: F821 undefined name 'Dict'
.\backend\app\services\intelligent_field_mapper.py:184:30: F541 f-string is missing placeholders
.\backend\app\services\intelligent_field_mapper.py:184:89: F821 undefined name 'e'
.\backend\app\services\intelligent_field_mapper.py:187:21: F541 f-string is missing placeholders
.\backend\app\services\intelligent_field_mapper.py:190:58: F821 undefined name 'Optional'
.\backend\app\services\intelligent_field_mapper.py:190:80: E501 line too long (80 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:205:80: E501 line too long (80 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:221:28: F541 f-string is missing placeholders
.\backend\app\services\intelligent_field_mapper.py:221:87: F821 undefined name 'e'
.\backend\app\services\intelligent_field_mapper.py:227:21: F821 undefined name 'List'
.\backend\app\services\intelligent_field_mapper.py:228:20: F821 undefined name 'List'
.\backend\app\services\intelligent_field_mapper.py:229:26: F821 undefined name 'Optional'
.\backend\app\services\intelligent_field_mapper.py:231:10: F821 undefined name 'Dict'
.\backend\app\services\intelligent_field_mapper.py:271:80: E501 line too long (83 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:288:80: E501 line too long (82 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:296:80: E501 line too long (87 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:349:51: F821 undefined name 'Optional'
.\backend\app\services\intelligent_field_mapper.py:365:80: E501 line too long (83 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:377:74: F821 undefined name 'e'
.\backend\app\services\intelligent_field_mapper.py:386:80: E501 line too long (81 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:399:51: F821 undefined name 'Optional'
.\backend\app\services\intelligent_field_mapper.py:476:10: F821 undefined name 'Dict'
.\backend\app\services\intelligent_field_mapper.py:495:80: E501 line too long (82 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:506:17: F821 undefined name 'Dict'
.\backend\app\services\intelligent_field_mapper.py:507:26: F821 undefined name 'Optional'
.\backend\app\services\intelligent_field_mapper.py:513:80: E501 line too long (82 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:542:80: E501 line too long (85 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:556:29: F821 undefined name 'asdict'
.\backend\app\services\intelligent_field_mapper.py:565:80: E501 line too long (86 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:575:21: F541 f-string is missing placeholders
.\backend\app\services\intelligent_field_mapper.py:575:80: E501 line too long (84 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:577:39: F821 undefined name 'Dict'
.\backend\app\services\intelligent_field_mapper.py:577:49: F821 undefined name 'Dict'
.\backend\app\services\intelligent_field_mapper.py:577:59: F821 undefined name 'Any'
.\backend\app\services\intelligent_field_mapper.py:587:59: F821 undefined name 'Dict'
.\backend\app\services\intelligent_field_mapper.py:587:69: F821 undefined name 'Any'
.\backend\app\services\intelligent_field_mapper.py:627:80: E501 line too long (85 > 79 characters)
.\backend\app\services\intelligent_field_mapper.py:635:67: F821 undefined name 'e'
.\backend\app\services\log_service.py:21:6: E999 IndentationError: expected an indented block after function definition on line 20
.\backend\app\services\maintenance_manager.py:22:6: E999 IndentationError: expected an indented block after function definition on line 21
.\backend\app\services\material_master_scheduler.py:29:6: E999 IndentationError: expected an indented block after function definition on line 28
.\backend\app\services\md_parser.py:31:6: E999 IndentationError: expected an indented block after function definition on line 30
.\backend\app\services\monitor_service.py:19:6: E999 IndentationError: expected an indented block after function definition on line 18
.\backend\app\services\realtime_log_service.py:38:6: E999 IndentationError: expected an indented block after function definition on line 37
.\backend\app\services\response_format_standardizer.py:83:6: E999 IndentationError: expected an indented block after function definition on line 82
.\backend\app\services\retry_helper.py:5:13: E999 IndentationError: unexpected indent
.\backend\app\services\robust_json_parser.py:33:6: E999 IndentationError: expected an indented block after function definition on line 32
.\backend\app\services\status_mapping_service.py:20:6: E999 IndentationError: expected an indented block after function definition on line 19
.\backend\app\services\sync_status_manager.py:21:6: E999 IndentationError: expected an indented block after function definition on line 20
.\backend\app\services\task_service.py:21:6: E999 IndentationError: expected an indented block after function definition on line 20
.\backend\app\services\token_service.py:16:80: E501 line too long (107 > 79 characters)
.\backend\app\services\token_service.py:39:17: F821 undefined name 'generate_signature'
.\backend\app\services\token_service.py:40:80: E501 line too long (122 > 79 characters)
.\backend\app\services\token_service.py:48:28: F821 undefined name 'get_data_center_domains'
.\backend\app\services\token_service.py:51:16: F821 undefined name 'get_access_token'
.\backend\app\services\unified_field_manager.py:5:29: E999 IndentationError: unexpected indent
.\backend\app\services\unified_field_service.py:20:2: E999 IndentationError: expected an indented block after 'try' statement on line 19
.\backend\app\services\zero_downtime_implementation.py:26:6: E999 IndentationError: expected an indented block after function definition on line 25
.\backend\app\strangler_proxy\config.py:9:80: E501 line too long (81 > 79 characters)
.\backend\app\week4\__init__.py:71:26: F821 undefined name 'Dict'
.\backend\app\week4\__init__.py:71:36: F821 undefined name 'Any'
.\backend\app\week4\__init__.py:81:80: E501 line too long (81 > 79 characters)
.\backend\app\week4\concurrent\async_task_manager.py:82:6: E999 IndentationError: expected an indented block after function definition on line 75
.\backend\app\week4\concurrent\concurrent_processor.py:477:34: E999 SyntaxError: unterminated string literal (detected at line 477)
.\backend\app\week4\concurrent\connection_pool_optimizer.py:77:6: E999 IndentationError: expected an indented block after function definition on line 68
.\backend\app\week4\concurrent\load_balancer.py:66:6: E999 IndentationError: expected an indented block after function definition on line 65
.\backend\app\week4\integration\week4_adapter.py:5:6: E999 SyntaxError: invalid syntax
.\backend\start_server.py:6:1: E265 block comment should start with '# '
.\backend\start_server.py:19:5: F821 undefined name 'logger'
.\backend\start_server.py:20:5: F821 undefined name 'logger'
.\backend\start_server.py:21:5: F821 undefined name 'logger'
.\backend\start_server.py:22:5: F821 undefined name 'logger'
.\backend\start_server.py:23:5: F821 undefined name 'logger'
.\backend\start_server.py:24:5: F821 undefined name 'logger'
.\backend\start_server.py:25:5: F821 undefined name 'logger'
.\backend\start_server.py:26:5: F821 undefined name 'logger'
.\backend\start_server.py:38:9: F821 undefined name 'logger'
.\backend\start_server.py:40:9: F821 undefined name 'logger'
.\backend\start_server.py:40:42: F821 undefined name 'e'
.\backend\start_server_clean.py:62:80: E501 line too long (86 > 79 characters)
.\backend\start_server_fixed.py:1:12: E999 SyntaxError: unexpected character after line continuation character
.\backend\start_simple.py:32:5: F821 undefined name 'CORSMiddleware'
.\backend\start_simple.py:46:80: E501 line too long (81 > 79 characters)
.\backend\start_simple.py:52:80: E501 line too long (85 > 79 characters)
.\backend\start_simple.py:65:80: E501 line too long (85 > 79 characters)
.\backend\start_simple.py:152:51: F821 undefined name 'e'
.\backend\start_simple.py:153:66: F821 undefined name 'e'
.\backend\start_simple.py:195:80: E501 line too long (85 > 79 characters)
.\backend\start_simple.py:197:80: E501 line too long (81 > 79 characters)
.\backend\start_simple.py:243:66: F821 undefined name 'e'
.\backend\start_simple.py:246:28: F821 undefined name 'e'
.\backend\start_simple.py:256:80: E501 line too long (80 > 79 characters)
.\batch_code_quality_fix.py:125:41: E999 SyntaxError: invalid syntax. Perhaps you forgot a comma?
.\batch_flake8_fix.py:142:23: F541 f-string is missing placeholders
.\batch_flake8_fix.py:145:23: F541 f-string is missing placeholders
.\batch_flake8_fix.py:167:15: F541 f-string is missing placeholders
.\batch_validate_modules.py:17:6: E999 IndentationError: expected an indented block after function definition on line 16
.\build_production_package.py:34:6: E999 IndentationError: expected an indented block after function definition on line 33
.\check_code_quality.py:65:80: E501 line too long (87 > 79 characters)
.\check_code_quality.py:124:80: E501 line too long (82 > 79 characters)
.\check_code_quality.py:127:80: E501 line too long (95 > 79 characters)
.\check_naming_conflicts.py:15:6: E999 IndentationError: expected an indented block after function definition on line 14
.\cicd_pipeline_builder.py:19:6: E999 IndentationError: expected an indented block after function definition on line 18
.\cicd_pipeline_builder_optimized.py:19:6: E999 IndentationError: expected an indented block after function definition on line 18
.\config_environmentizer.py:16:6: E999 IndentationError: expected an indented block after function definition on line 15
.\config_environmentizer_clean.py:16:6: E999 IndentationError: expected an indented block after function definition on line 15
.\core\app\api\v1\__init__.py:6:5: E999 IndentationError: unexpected indent
.\core\app\api\v1\auth.py:15:50: F821 undefined name 'e'
.\core\app\api\v1\config.py:6:6: E999 SyntaxError: invalid syntax
.\core\app\api\v1\database.py:14:10: F821 undefined name 'APIRouter'
.\core\app\api\v1\database.py:30:43: F821 undefined name 'Depends'
.\core\app\api\v1\database.py:30:51: F821 undefined name 'get_table_manager'
.\core\app\api\v1\database.py:39:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:39:80: E501 line too long (88 > 79 characters)
.\core\app\api\v1\database.py:78:63: F821 undefined name 'e'
.\core\app\api\v1\database.py:79:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:79:80: F821 undefined name 'e'
.\core\app\api\v1\database.py:85:43: F821 undefined name 'Depends'
.\core\app\api\v1\database.py:85:51: F821 undefined name 'get_table_manager'
.\core\app\api\v1\database.py:94:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:94:80: E501 line too long (88 > 79 characters)
.\core\app\api\v1\database.py:133:66: F821 undefined name 'e'
.\core\app\api\v1\database.py:134:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:134:80: F821 undefined name 'e'
.\core\app\api\v1\database.py:139:58: F821 undefined name 'Depends'
.\core\app\api\v1\database.py:139:66: F821 undefined name 'get_table_manager'
.\core\app\api\v1\database.py:139:80: E501 line too long (83 > 79 characters)
.\core\app\api\v1\database.py:147:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:149:80: E501 line too long (85 > 79 characters)
.\core\app\api\v1\database.py:169:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:169:80: E501 line too long (86 > 79 characters)
.\core\app\api\v1\database.py:171:12: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:174:80: E501 line too long (86 > 79 characters)
.\core\app\api\v1\database.py:174:100: F821 undefined name 'e'
.\core\app\api\v1\database.py:175:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:175:74: F821 undefined name 'e'
.\core\app\api\v1\database.py:180:58: F821 undefined name 'Depends'
.\core\app\api\v1\database.py:180:66: F821 undefined name 'get_table_manager'
.\core\app\api\v1\database.py:180:80: E501 line too long (83 > 79 characters)
.\core\app\api\v1\database.py:188:80: E501 line too long (85 > 79 characters)
.\core\app\api\v1\database.py:201:80: E501 line too long (107 > 79 characters)
.\core\app\api\v1\database.py:204:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:204:80: E501 line too long (88 > 79 characters)
.\core\app\api\v1\database.py:206:12: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:209:66: F821 undefined name 'e'
.\core\app\api\v1\database.py:210:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:210:80: F821 undefined name 'e'
.\core\app\api\v1\database.py:215:60: F821 undefined name 'Depends'
.\core\app\api\v1\database.py:215:68: F821 undefined name 'get_table_manager'
.\core\app\api\v1\database.py:215:80: E501 line too long (85 > 79 characters)
.\core\app\api\v1\database.py:225:80: E501 line too long (80 > 79 characters)
.\core\app\api\v1\database.py:228:80: E501 line too long (86 > 79 characters)
.\core\app\api\v1\database.py:230:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:230:80: E501 line too long (86 > 79 characters)
.\core\app\api\v1\database.py:232:12: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:235:83: F821 undefined name 'e'
.\core\app\api\v1\database.py:236:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:236:74: F821 undefined name 'e'
.\core\app\api\v1\database.py:241:60: F821 undefined name 'Depends'
.\core\app\api\v1\database.py:241:68: F821 undefined name 'get_table_manager'
.\core\app\api\v1\database.py:241:80: E501 line too long (85 > 79 characters)
.\core\app\api\v1\database.py:256:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:256:80: E501 line too long (85 > 79 characters)
.\core\app\api\v1\database.py:258:12: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:261:80: F821 undefined name 'e'
.\core\app\api\v1\database.py:262:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\database.py:262:80: F821 undefined name 'e'
.\core\app\api\v1\database.py:267:43: F821 undefined name 'Depends'
.\core\app\api\v1\database.py:267:51: F821 undefined name 'get_table_manager'
.\core\app\api\v1\database.py:289:63: F821 undefined name 'e'
.\core\app\api\v1\database.py:296:40: F821 undefined name 'e'
.\core\app\api\v1\database.py:299:51: F821 undefined name 'e'
.\core\app\api\v1\database_health.py:10:10: F821 undefined name 'APIRouter'
.\core\app\api\v1\database_health.py:14:38: F821 undefined name 'Dict'
.\core\app\api\v1\database_health.py:14:48: F821 undefined name 'Any'
.\core\app\api\v1\database_health.py:38:63: F821 undefined name 'e'
.\core\app\api\v1\database_health.py:39:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\database_health.py:39:89: F821 undefined name 'e'
.\core\app\api\v1\database_health.py:43:32: F821 undefined name 'Dict'
.\core\app\api\v1\database_health.py:43:42: F821 undefined name 'Any'
.\core\app\api\v1\database_health.py:57:63: F821 undefined name 'e'
.\core\app\api\v1\database_health.py:58:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\database_health.py:58:89: F821 undefined name 'e'
.\core\app\api\v1\database_health.py:62:41: F821 undefined name 'Dict'
.\core\app\api\v1\database_health.py:62:51: F821 undefined name 'Any'
.\core\app\api\v1\database_health.py:79:63: F821 undefined name 'e'
.\core\app\api\v1\database_health.py:80:75: F821 undefined name 'e'
.\core\app\api\v1\enhanced_sync.py:8:17: E999 IndentationError: unexpected indent
.\core\app\api\v1\excel_translation.py:7:13: E999 IndentationError: unexpected indent
.\core\app\api\v1\field_config_api.py:8:1: E265 block comment should start with '# '
.\core\app\api\v1\field_config_api.py:18:10: F821 undefined name 'APIRouter'
.\core\app\api\v1\field_config_api.py:24:18: F821 undefined name 'Dict'
.\core\app\api\v1\field_config_api.py:24:28: F821 undefined name 'Any'
.\core\app\api\v1\field_config_api.py:42:15: F821 undefined name 'List'
.\core\app\api\v1\field_config_api.py:42:20: F821 undefined name 'Dict'
.\core\app\api\v1\field_config_api.py:42:30: F821 undefined name 'Any'
.\core\app\api\v1\field_config_api.py:48:18: F821 undefined name 'Dict'
.\core\app\api\v1\field_config_api.py:48:28: F821 undefined name 'Any'
.\core\app\api\v1\field_config_api.py:54:11: F821 undefined name 'Optional'
.\core\app\api\v1\field_config_api.py:54:20: F821 undefined name 'Dict'
.\core\app\api\v1\field_config_api.py:54:30: F821 undefined name 'Any'
.\core\app\api\v1\field_config_api.py:63:38: F821 undefined name 'Query'
.\core\app\api\v1\field_config_api.py:79:80: E501 line too long (80 > 79 characters)
.\core\app\api\v1\field_config_api.py:88:80: E501 line too long (81 > 79 characters)
.\core\app\api\v1\field_config_api.py:91:80: E501 line too long (88 > 79 characters)
.\core\app\api\v1\field_config_api.py:91:102: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:92:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:92:86: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:96:80: E501 line too long (83 > 79 characters)
.\core\app\api\v1\field_config_api.py:126:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:134:23: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:136:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:136:86: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:170:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:178:23: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:180:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:180:86: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:185:38: F821 undefined name 'Query'
.\core\app\api\v1\field_config_api.py:200:80: E501 line too long (87 > 79 characters)
.\core\app\api\v1\field_config_api.py:209:80: E501 line too long (88 > 79 characters)
.\core\app\api\v1\field_config_api.py:209:102: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:210:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:210:86: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:212:16: F821 undefined name 'config'
.\core\app\api\v1\field_config_api.py:226:80: E501 line too long (81 > 79 characters)
.\core\app\api\v1\field_config_api.py:229:85: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:230:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:230:86: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:246:80: E501 line too long (81 > 79 characters)
.\core\app\api\v1\field_config_api.py:250:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:253:80: E501 line too long (84 > 79 characters)
.\core\app\api\v1\field_config_api.py:267:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:269:12: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:272:85: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:273:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:273:86: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:308:60: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:309:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:309:86: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:313:65: F821 undefined name 'Query'
.\core\app\api\v1\field_config_api.py:313:80: E501 line too long (80 > 79 characters)
.\core\app\api\v1\field_config_api.py:327:80: E501 line too long (83 > 79 characters)
.\core\app\api\v1\field_config_api.py:337:85: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:338:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:338:86: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:366:22: F821 undefined name 'Query'
.\core\app\api\v1\field_config_api.py:367:20: F821 undefined name 'Query'
.\core\app\api\v1\field_config_api.py:372:80: E501 line too long (84 > 79 characters)
.\core\app\api\v1\field_config_api.py:375:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:375:80: E501 line too long (85 > 79 characters)
.\core\app\api\v1\field_config_api.py:400:55: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:401:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:401:80: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:421:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:470:80: E501 line too long (87 > 79 characters)
.\core\app\api\v1\field_config_api.py:486:80: E501 line too long (80 > 79 characters)
.\core\app\api\v1\field_config_api.py:497:23: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:499:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:499:86: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:522:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:577:80: E501 line too long (94 > 79 characters)
.\core\app\api\v1\field_config_api.py:585:80: E501 line too long (88 > 79 characters)
.\core\app\api\v1\field_config_api.py:585:102: F821 undefined name 'e'
.\core\app\api\v1\field_config_api.py:586:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\field_config_api.py:586:86: F821 undefined name 'e'
.\core\app\api\v1\maintenance.py:4:1: E265 block comment should start with '# '
.\core\app\api\v1\maintenance.py:13:10: F821 undefined name 'APIRouter'
.\core\app\api\v1\maintenance.py:26:60: F821 undefined name 'e'
.\core\app\api\v1\maintenance.py:27:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\maintenance.py:27:86: F821 undefined name 'e'
.\core\app\api\v1\maintenance.py:40:60: F821 undefined name 'e'
.\core\app\api\v1\maintenance.py:41:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\maintenance.py:41:80: F821 undefined name 'e'
.\core\app\api\v1\maintenance.py:54:66: F821 undefined name 'e'
.\core\app\api\v1\maintenance.py:55:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\maintenance.py:55:86: F821 undefined name 'e'
.\core\app\api\v1\maintenance.py:68:66: F821 undefined name 'e'
.\core\app\api\v1\maintenance.py:69:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\maintenance.py:69:86: F821 undefined name 'e'
.\core\app\api\v1\maintenance.py:86:66: F821 undefined name 'e'
.\core\app\api\v1\maintenance.py:87:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\maintenance.py:87:80: F821 undefined name 'e'
.\core\app\api\v1\maintenance.py:100:75: F821 undefined name 'e'
.\core\app\api\v1\maintenance.py:101:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\maintenance.py:101:80: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:10:1: F811 redefinition of unused 'DatabaseManager' from line 9
.\core\app\api\v1\monitor.py:12:1: F811 redefinition of unused 'pyodbc' from line 4
.\core\app\api\v1\monitor.py:15:1: F811 redefinition of unused 'settings' from line 11
.\core\app\api\v1\monitor.py:16:1: F811 redefinition of unused 'pyodbc' from line 12
.\core\app\api\v1\monitor.py:29:10: F821 undefined name 'APIRouter'
.\core\app\api\v1\monitor.py:74:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:78:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:80:57: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:81:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:81:83: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:103:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:106:57: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:107:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:107:83: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:143:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:146:57: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:147:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:147:83: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:169:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:172:57: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:173:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:173:83: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:184:24: F821 undefined name 'table_manager'
.\core\app\api\v1\monitor.py:187:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:187:80: E501 line too long (88 > 79 characters)
.\core\app\api\v1\monitor.py:226:63: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:227:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:227:89: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:253:80: E501 line too long (96 > 79 characters)
.\core\app\api\v1\monitor.py:262:80: E501 line too long (107 > 79 characters)
.\core\app\api\v1\monitor.py:282:80: E501 line too long (86 > 79 characters)
.\core\app\api\v1\monitor.py:287:63: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:296:34: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:299:54: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:303:39: F821 undefined name 'HealthCheckResponse'
.\core\app\api\v1\monitor.py:304:39: F821 undefined name 'HealthCheckResponse'
.\core\app\api\v1\monitor.py:318:80: E501 line too long (86 > 79 characters)
.\core\app\api\v1\monitor.py:334:80: E501 line too long (88 > 79 characters)
.\core\app\api\v1\monitor.py:338:60: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:339:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:339:86: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:342:40: F821 undefined name 'MetricsResponse'
.\core\app\api\v1\monitor.py:359:60: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:360:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:360:86: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:365:12: F821 undefined name 'Optional'
.\core\app\api\v1\monitor.py:367:13: F821 undefined name 'Optional'
.\core\app\api\v1\monitor.py:368:13: F821 undefined name 'Optional'
.\core\app\api\v1\monitor.py:388:60: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:389:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:389:86: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:392:38: F821 undefined name 'Dict'
.\core\app\api\v1\monitor.py:427:57: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:427:80: E501 line too long (85 > 79 characters)
.\core\app\api\v1\monitor.py:430:35: F821 undefined name 'Dict'
.\core\app\api\v1\monitor.py:433:80: E501 line too long (81 > 79 characters)
.\core\app\api\v1\monitor.py:436:42: F821 undefined name 'Dict'
.\core\app\api\v1\monitor.py:508:80: E501 line too long (115 > 79 characters)
.\core\app\api\v1\monitor.py:535:72: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:551:80: E501 line too long (83 > 79 characters)
.\core\app\api\v1\monitor.py:571:80: E501 line too long (84 > 79 characters)
.\core\app\api\v1\monitor.py:572:80: E501 line too long (83 > 79 characters)
.\core\app\api\v1\monitor.py:581:77: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:581:80: E501 line too long (80 > 79 characters)
.\core\app\api\v1\monitor.py:600:80: E501 line too long (83 > 79 characters)
.\core\app\api\v1\monitor.py:613:80: E501 line too long (103 > 79 characters)
.\core\app\api\v1\monitor.py:615:80: E501 line too long (83 > 79 characters)
.\core\app\api\v1\monitor.py:622:80: E501 line too long (83 > 79 characters)
.\core\app\api\v1\monitor.py:641:87: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:646:54: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:647:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:647:80: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:696:80: E501 line too long (81 > 79 characters)
.\core\app\api\v1\monitor.py:705:67: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:737:80: E501 line too long (87 > 79 characters)
.\core\app\api\v1\monitor.py:759:80: E501 line too long (86 > 79 characters)
.\core\app\api\v1\monitor.py:762:80: E501 line too long (86 > 79 characters)
.\core\app\api\v1\monitor.py:765:80: E501 line too long (86 > 79 characters)
.\core\app\api\v1\monitor.py:774:60: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:824:80: E501 line too long (87 > 79 characters)
.\core\app\api\v1\monitor.py:828:80: E501 line too long (81 > 79 characters)
.\core\app\api\v1\monitor.py:854:80: E501 line too long (80 > 79 characters)
.\core\app\api\v1\monitor.py:899:71: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:911:80: E501 line too long (87 > 79 characters)
.\core\app\api\v1\monitor.py:931:57: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:932:76: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:972:80: E501 line too long (81 > 79 characters)
.\core\app\api\v1\monitor.py:994:60: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:995:79: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:1013:80: E501 line too long (85 > 79 characters)
.\core\app\api\v1\monitor.py:1017:80: E501 line too long (83 > 79 characters)
.\core\app\api\v1\monitor.py:1022:59: F821 undefined name 'e'
.\core\app\api\v1\monitor.py:1023:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\monitor.py:1023:85: F821 undefined name 'e'
.\core\app\api\v1\realtime_logs.py:9:6: E999 SyntaxError: invalid syntax
.\core\app\api\v1\sync.py:6:13: E999 IndentationError: unexpected indent
.\core\app\api\v1\sync_status.py:4:1: E265 block comment should start with '# '
.\core\app\api\v1\sync_status.py:13:10: F821 undefined name 'APIRouter'
.\core\app\api\v1\sync_status.py:30:57: F821 undefined name 'e'
.\core\app\api\v1\sync_status.py:31:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\sync_status.py:31:57: F821 undefined name 'e'
.\core\app\api\v1\sync_status.py:55:75: F821 undefined name 'e'
.\core\app\api\v1\sync_status.py:56:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\sync_status.py:56:57: F821 undefined name 'e'
.\core\app\api\v1\sync_status.py:69:57: F821 undefined name 'e'
.\core\app\api\v1\sync_status.py:70:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\sync_status.py:70:57: F821 undefined name 'e'
.\core\app\api\v1\sync_status.py:98:66: F821 undefined name 'e'
.\core\app\api\v1\sync_status.py:99:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\sync_status.py:99:57: F821 undefined name 'e'
.\core\app\api\v1\tasks.py:3:6: E999 SyntaxError: invalid syntax
.\core\app\api\v1\unified_field_config.py:4:1: F811 redefinition of unused 'Response' from line 3
.\core\app\api\v1\unified_field_config.py:13:10: F821 undefined name 'APIRouter'
.\core\app\api\v1\unified_field_config.py:31:62: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:36:38: F821 undefined name 'List'
.\core\app\api\v1\unified_field_config.py:36:43: F821 undefined name 'Dict'
.\core\app\api\v1\unified_field_config.py:36:53: F821 undefined name 'Any'
.\core\app\api\v1\unified_field_config.py:103:80: E501 line too long (84 > 79 characters)
.\core\app\api\v1\unified_field_config.py:126:51: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:126:71: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:126:80: E501 line too long (80 > 79 characters)
.\core\app\api\v1\unified_field_config.py:128:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:128:86: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:134:20: F821 undefined name 'Query'
.\core\app\api\v1\unified_field_config.py:135:32: F821 undefined name 'Query'
.\core\app\api\v1\unified_field_config.py:136:6: F821 undefined name 'Dict'
.\core\app\api\v1\unified_field_config.py:149:19: F821 undefined name 'get_field_manager'
.\core\app\api\v1\unified_field_config.py:175:23: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:177:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:177:86: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:184:20: F821 undefined name 'Query'
.\core\app\api\v1\unified_field_config.py:185:25: F821 undefined name 'Query'
.\core\app\api\v1\unified_field_config.py:186:6: F821 undefined name 'Dict'
.\core\app\api\v1\unified_field_config.py:200:19: F821 undefined name 'get_field_manager'
.\core\app\api\v1\unified_field_config.py:225:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:233:23: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:235:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:235:86: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:256:20: F821 undefined name 'Query'
.\core\app\api\v1\unified_field_config.py:257:25: F821 undefined name 'Query'
.\core\app\api\v1\unified_field_config.py:258:6: F821 undefined name 'Dict'
.\core\app\api\v1\unified_field_config.py:272:19: F821 undefined name 'get_field_manager'
.\core\app\api\v1\unified_field_config.py:303:23: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:304:29: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:307:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:307:86: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:320:20: F821 undefined name 'Query'
.\core\app\api\v1\unified_field_config.py:321:22: F821 undefined name 'Query'
.\core\app\api\v1\unified_field_config.py:322:6: F821 undefined name 'Dict'
.\core\app\api\v1\unified_field_config.py:336:19: F821 undefined name 'get_field_manager'
.\core\app\api\v1\unified_field_config.py:337:80: E501 line too long (83 > 79 characters)
.\core\app\api\v1\unified_field_config.py:365:23: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:366:29: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:369:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:369:86: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:390:20: F821 undefined name 'Query'
.\core\app\api\v1\unified_field_config.py:391:20: F821 undefined name 'Query'
.\core\app\api\v1\unified_field_config.py:392:6: F821 undefined name 'Dict'
.\core\app\api\v1\unified_field_config.py:406:19: F821 undefined name 'get_field_manager'
.\core\app\api\v1\unified_field_config.py:431:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:439:23: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:441:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:441:92: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:445:58: F821 undefined name 'Dict'
.\core\app\api\v1\unified_field_config.py:456:19: F821 undefined name 'get_field_manager'
.\core\app\api\v1\unified_field_config.py:467:19: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:467:80: E501 line too long (81 > 79 characters)
.\core\app\api\v1\unified_field_config.py:470:79: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:471:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:471:80: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:475:36: F821 undefined name 'Dict'
.\core\app\api\v1\unified_field_config.py:483:19: F821 undefined name 'get_field_manager'
.\core\app\api\v1\unified_field_config.py:506:60: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:507:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:507:86: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:512:38: F821 undefined name 'Query'
.\core\app\api\v1\unified_field_config.py:513:6: F821 undefined name 'Dict'
.\core\app\api\v1\unified_field_config.py:525:19: F821 undefined name 'get_field_manager'
.\core\app\api\v1\unified_field_config.py:559:23: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:561:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:561:86: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:565:45: F821 undefined name 'List'
.\core\app\api\v1\unified_field_config.py:565:50: F821 undefined name 'Dict'
.\core\app\api\v1\unified_field_config.py:565:60: F821 undefined name 'Any'
.\core\app\api\v1\unified_field_config.py:591:66: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:592:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:592:86: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:603:55: F821 undefined name 'Query'
.\core\app\api\v1\unified_field_config.py:603:80: E501 line too long (84 > 79 characters)
.\core\app\api\v1\unified_field_config.py:604:6: F821 undefined name 'Dict'
.\core\app\api\v1\unified_field_config.py:617:19: F821 undefined name 'get_field_manager'
.\core\app\api\v1\unified_field_config.py:637:23: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:638:29: F821 undefined name 'e'
.\core\app\api\v1\unified_field_config.py:641:15: F821 undefined name 'HTTPException'
.\core\app\api\v1\unified_field_config.py:641:95: F821 undefined name 'e'
.\core\app\core\code_quality.py:70:6: E999 IndentationError: expected an indented block after function definition on line 69
.\core\app\core\config.py:68:6: F821 undefined name 'field_validator'
.\core\app\core\config.py:99:80: E501 line too long (104 > 79 characters)
.\core\app\core\config.py:102:20: F821 undefined name 'ConfigDict'
.\core\app\core\config.py:124:80: E501 line too long (84 > 79 characters)
.\core\app\core\config.py:136:21: F821 undefined name 'logger'
.\core\app\core\config.py:142:13: F821 undefined name 'logger'
.\core\app\core\config.py:143:13: F821 undefined name 'logger'
.\core\app\core\config.py:183:9: F821 undefined name 'logger'
.\core\app\core\config.py:183:63: F821 undefined name 'e'
.\core\app\core\config.py:184:9: F821 undefined name 'logger'
.\core\app\core\config.py:223:80: E501 line too long (87 > 79 characters)
.\core\app\core\config.py:231:80: E501 line too long (89 > 79 characters)
.\core\app\core\config.py:256:80: E501 line too long (85 > 79 characters)
.\core\app\core\config.py:264:80: E501 line too long (83 > 79 characters)
.\core\app\core\config.py:272:80: E501 line too long (86 > 79 characters)
.\core\app\core\config.py:296:80: E501 line too long (85 > 79 characters)
.\core\app\core\database.py:32:23: F821 undefined name 'create_engine'
.\core\app\core\database.py:48:69: F821 undefined name 'e'
.\core\app\core\database.py:58:80: E501 line too long (82 > 79 characters)
.\core\app\core\database.py:60:24: F821 undefined name 'create_async_engine'
.\core\app\core\database.py:79:20: F821 undefined name 'AsyncSession'
.\core\app\core\database.py:88:69: F821 undefined name 'e'
.\core\app\core\database.py:110:63: F821 undefined name 'e'
.\core\app\core\database.py:114:77: F821 undefined name 'e'
.\core\app\core\database.py:127:17: F821 undefined name 'text'
.\core\app\core\database.py:127:80: E501 line too long (81 > 79 characters)
.\core\app\core\database.py:135:33: F821 undefined name 'text'
.\core\app\core\database.py:135:80: E501 line too long (84 > 79 characters)
.\core\app\core\database.py:143:64: F821 undefined name 'e'
.\core\app\core\database.py:154:57: F821 undefined name 'e'
.\core\app\core\database.py:160:34: F821 undefined name 'AsyncSession'
.\core\app\core\database.py:169:69: F821 undefined name 'e'
.\core\app\core\database.py:183:67: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:59:27: F821 undefined name 'List'
.\core\app\core\database_connection_pool.py:60:37: F821 undefined name 'List'
.\core\app\core\database_connection_pool.py:61:34: F821 undefined name 'List'
.\core\app\core\database_connection_pool.py:100:80: E501 line too long (84 > 79 characters)
.\core\app\core\database_connection_pool.py:102:80: E501 line too long (80 > 79 characters)
.\core\app\core\database_connection_pool.py:105:70: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:131:70: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:152:67: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:159:33: F821 undefined name 'Optional'
.\core\app\core\database_connection_pool.py:159:42: F821 undefined name 'Dict'
.\core\app\core\database_connection_pool.py:160:10: F821 undefined name 'List'
.\core\app\core\database_connection_pool.py:160:15: F821 undefined name 'Dict'
.\core\app\core\database_connection_pool.py:192:71: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:195:58: F821 undefined name 'List'
.\core\app\core\database_connection_pool.py:195:63: F821 undefined name 'Dict'
.\core\app\core\database_connection_pool.py:221:74: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:234:64: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:272:58: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:333:62: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:355:58: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:366:64: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:378:62: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:398:80: E501 line too long (88 > 79 characters)
.\core\app\core\database_connection_pool.py:426:64: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:432:80: E501 line too long (88 > 79 characters)
.\core\app\core\database_connection_pool.py:438:64: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:464:64: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:466:33: F821 undefined name 'Dict'
.\core\app\core\database_connection_pool.py:466:43: F821 undefined name 'Any'
.\core\app\core\database_connection_pool.py:502:73: F821 undefined name 'e'
.\core\app\core\database_connection_pool.py:523:61: F821 undefined name 'e'
.\core\app\core\database_manager.py:57:22: E999 SyntaxError: unterminated string literal (detected at line 57)
.\core\app\core\exceptions.py:60:6: E999 IndentationError: expected an indented block after function definition on line 59
.\core\app\core\optimized_retry.py:5:13: E999 IndentationError: unexpected indent
.\core\app\main.py:9:9: E999 IndentationError: unexpected indent
.\core\app\main_original.py:8:6: E999 SyntaxError: invalid syntax
.\core\app\middleware\access_log.py:19:39: F821 undefined name 'Request'
.\core\app\middleware\access_log.py:19:72: F821 undefined name 'Response'
.\core\app\middleware\access_log.py:19:80: E501 line too long (80 > 79 characters)
.\core\app\schemas\base.py:12:14: F821 undefined name 'Optional'
.\core\app\schemas\base.py:13:11: F821 undefined name 'Optional'
.\core\app\schemas\base.py:13:20: F821 undefined name 'Any'
.\core\app\schemas\base.py:14:16: F821 undefined name 'Optional'
.\core\app\schemas\base.py:15:17: F821 undefined name 'Optional'
.\core\app\schemas\base.py:23:16: F821 undefined name 'Optional'
.\core\app\schemas\base.py:24:17: F821 undefined name 'Optional'
.\core\app\schemas\base.py:30:11: F821 undefined name 'Any'
.\core\app\schemas\base.py:37:12: F821 undefined name 'Dict'
.\core\app\schemas\base.py:37:22: F821 undefined name 'Any'
.\core\app\schemas\base.py:55:11: F821 undefined name 'Dict'
.\core\app\schemas\base.py:55:21: F821 undefined name 'Any'
.\core\app\schemas\base.py:98:18: F821 undefined name 'Optional'
.\core\app\schemas\base.py:99:19: F821 undefined name 'Optional'
.\core\app\schemas\base.py:101:20: F821 undefined name 'Optional'
.\core\app\schemas\base.py:108:13: F821 undefined name 'List'
.\core\app\schemas\base.py:109:15: F821 undefined name 'List'
.\core\app\schemas\base.py:117:18: F821 undefined name 'Optional'
.\core\app\schemas\base.py:118:16: F821 undefined name 'Optional'
.\core\app\schemas\base.py:119:17: F821 undefined name 'Optional'
.\core\app\schemas\base.py:120:19: F821 undefined name 'Optional'
.\core\app\schemas\base.py:121:23: F821 undefined name 'Optional'
.\core\app\schemas\base.py:122:15: F821 undefined name 'Optional'
.\core\app\schemas\base.py:122:24: F821 undefined name 'Dict'
.\core\app\schemas\base.py:122:34: F821 undefined name 'Any'
.\core\app\schemas\base.py:123:20: F821 undefined name 'Optional'
.\core\app\schemas\config.py:9:26: F821 undefined name 'BaseModel'
.\core\app\schemas\config.py:12:13: F821 undefined name 'Dict'
.\core\app\schemas\config.py:12:23: F821 undefined name 'FieldInfo'
.\core\app\schemas\config.py:32:27: F821 undefined name 'BaseResponse'
.\core\app\schemas\config.py:35:11: F821 undefined name 'Dict'
.\core\app\schemas\config.py:35:21: F821 undefined name 'Any'
.\core\app\schemas\config.py:65:26: F821 undefined name 'BaseResponse'
.\core\app\schemas\config.py:68:11: F821 undefined name 'Dict'
.\core\app\schemas\config.py:68:21: F821 undefined name 'Any'
.\core\app\schemas\config.py:96:27: F821 undefined name 'BaseModel'
.\core\app\schemas\config.py:99:16: F821 undefined name 'Optional'
.\core\app\schemas\config.py:101:27: F821 undefined name 'Field'
.\core\app\schemas\config.py:113:28: F821 undefined name 'BaseResponse'
.\core\app\schemas\config.py:116:11: F821 undefined name 'Dict'
.\core\app\schemas\config.py:116:21: F821 undefined name 'Any'
.\core\app\schemas\config.py:143:33: F821 undefined name 'BaseModel'
.\core\app\schemas\config.py:146:14: F821 undefined name 'Optional'
.\core\app\schemas\config.py:146:23: F821 undefined name 'List'
.\core\app\schemas\config.py:160:28: F821 undefined name 'BaseResponse'
.\core\app\schemas\config.py:163:11: F821 undefined name 'Dict'
.\core\app\schemas\config.py:163:21: F821 undefined name 'Any'
.\core\app\schemas\config.py:193:32: F821 undefined name 'BaseResponse'
.\core\app\schemas\config.py:196:11: F821 undefined name 'ValidationResult'
.\core\app\schemas\config.py:200:24: F821 undefined name 'BaseModel'
.\core\app\schemas\config.py:212:16: F821 undefined name 'Optional'
.\core\app\schemas\config.py:213:18: F821 undefined name 'Optional'
.\core\app\schemas\config.py:216:23: F821 undefined name 'BaseModel'
.\core\app\schemas\config.py:227:18: F821 undefined name 'Optional'
.\core\app\schemas\config.py:228:19: F821 undefined name 'Optional'
.\core\app\schemas\config.py:229:23: F821 undefined name 'Optional'
.\core\app\schemas\config.py:229:32: F821 undefined name 'Dict'
.\core\app\schemas\config.py:229:42: F821 undefined name 'Any'
.\core\app\schemas\config.py:230:20: F821 undefined name 'Optional'
.\core\app\schemas\config.py:230:29: F821 undefined name 'Dict'
.\core\app\schemas\config.py:232:20: F821 undefined name 'Optional'
.\core\app\schemas\config.py:235:25: F821 undefined name 'BaseModel'
.\core\app\schemas\config.py:243:19: F821 undefined name 'Optional'
.\core\app\schemas\config.py:246:13: F821 undefined name 'Dict'
.\core\app\schemas\database.py:25:14: F821 undefined name 'List'
.\core\app\schemas\database.py:25:19: F821 undefined name 'Dict'
.\core\app\schemas\database.py:25:29: F821 undefined name 'Any'
.\core\app\schemas\database.py:43:25: F821 undefined name 'List'
.\core\app\schemas\database.py:44:21: F821 undefined name 'List'
.\core\app\schemas\database.py:45:22: F821 undefined name 'List'
.\core\app\schemas\database.py:56:25: F821 undefined name 'Dict'
.\core\app\schemas\database.py:56:35: F821 undefined name 'Dict'
.\core\app\schemas\database.py:56:45: F821 undefined name 'Any'
.\core\app\schemas\database.py:79:11: F821 undefined name 'Dict'
.\core\app\schemas\database.py:79:21: F821 undefined name 'Any'
.\core\app\schemas\monitor.py:15:23: F821 undefined name 'Optional'
.\core\app\schemas\monitor.py:16:17: F821 undefined name 'Optional'
.\core\app\schemas\monitor.py:17:20: F821 undefined name 'Optional'
.\core\app\schemas\monitor.py:23:22: F821 undefined name 'Optional'
.\core\app\schemas\monitor.py:23:31: F821 undefined name 'Dict'
.\core\app\schemas\monitor.py:39:19: F821 undefined name 'Dict'
.\core\app\schemas\monitor.py:39:29: F821 undefined name 'Any'
.\core\app\schemas\monitor.py:40:22: F821 undefined name 'Dict'
.\core\app\schemas\monitor.py:40:32: F821 undefined name 'Any'
.\core\app\schemas\monitor.py:41:15: F821 undefined name 'Dict'
.\core\app\schemas\monitor.py:41:25: F821 undefined name 'Any'
.\core\app\schemas\monitor.py:42:13: F821 undefined name 'Dict'
.\core\app\schemas\monitor.py:42:23: F821 undefined name 'Any'
.\core\app\schemas\monitor.py:48:11: F821 undefined name 'Dict'
.\core\app\schemas\monitor.py:48:21: F821 undefined name 'Any'
.\core\app\schemas\monitor.py:65:80: E501 line too long (84 > 79 characters)
.\core\app\schemas\monitor.py:92:13: F821 undefined name 'Optional'
.\core\app\schemas\monitor.py:94:14: F821 undefined name 'Optional'
.\core\app\schemas\monitor.py:94:23: F821 undefined name 'Dict'
.\core\app\schemas\monitor.py:94:33: F821 undefined name 'Any'
.\core\app\schemas\monitor.py:100:11: F821 undefined name 'List'
.\core\app\schemas\realtime_log.py:38:17: F821 undefined name 'Optional'
.\core\app\schemas\realtime_log.py:39:12: F821 undefined name 'Optional'
.\core\app\schemas\realtime_log.py:39:21: F821 undefined name 'Dict'
.\core\app\schemas\realtime_log.py:39:31: F821 undefined name 'Any'
.\core\app\schemas\realtime_log.py:47:11: F821 undefined name 'Optional'
.\core\app\schemas\realtime_log.py:47:20: F821 undefined name 'Dict'
.\core\app\schemas\realtime_log.py:47:30: F821 undefined name 'Any'
.\core\app\schemas\realtime_log.py:55:11: F821 undefined name 'Dict'
.\core\app\schemas\realtime_log.py:55:21: F821 undefined name 'Any'
.\core\app\schemas\realtime_log.py:63:11: F821 undefined name 'Dict'
.\core\app\schemas\realtime_log.py:63:21: F821 undefined name 'Any'
.\core\app\schemas\sync.py:9:19: F821 undefined name 'BaseModel'
.\core\app\schemas\sync.py:12:22: F821 undefined name 'Field'
.\core\app\schemas\sync.py:14:19: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:15:14: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:15:23: F821 undefined name 'Dict'
.\core\app\schemas\sync.py:15:33: F821 undefined name 'Any'
.\core\app\schemas\sync.py:23:80: E501 line too long (80 > 79 characters)
.\core\app\schemas\sync.py:28:20: F821 undefined name 'BaseResponse'
.\core\app\schemas\sync.py:31:11: F821 undefined name 'Dict'
.\core\app\schemas\sync.py:31:21: F821 undefined name 'Any'
.\core\app\schemas\sync.py:50:26: F821 undefined name 'BaseResponse'
.\core\app\schemas\sync.py:53:11: F821 undefined name 'TaskStatus'
.\core\app\schemas\sync.py:63:80: E501 line too long (82 > 79 characters)
.\core\app\schemas\sync.py:77:24: F821 undefined name 'BaseModel'
.\core\app\schemas\sync.py:80:14: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:80:23: F821 undefined name 'List'
.\core\app\schemas\sync.py:81:22: F821 undefined name 'Field'
.\core\app\schemas\sync.py:82:27: F821 undefined name 'Field'
.\core\app\schemas\sync.py:96:24: F821 undefined name 'BaseModel'
.\core\app\schemas\sync.py:102:27: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:106:24: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:109:20: F821 undefined name 'BaseModel'
.\core\app\schemas\sync.py:115:18: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:117:14: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:117:23: F821 undefined name 'Dict'
.\core\app\schemas\sync.py:117:33: F821 undefined name 'Any'
.\core\app\schemas\sync.py:118:17: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:119:14: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:122:24: F821 undefined name 'BaseModel'
.\core\app\schemas\sync.py:131:19: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:132:23: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:136:20: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:140:22: F821 undefined name 'BaseModel'
.\core\app\schemas\sync.py:149:21: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:150:19: F821 undefined name 'Dict'
.\core\app\schemas\sync.py:150:29: F821 undefined name 'Dict'
.\core\app\schemas\sync.py:150:39: F821 undefined name 'Any'
.\core\app\schemas\sync.py:153:24: F821 undefined name 'BaseModel'
.\core\app\schemas\sync.py:162:27: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:164:22: F821 undefined name 'List'
.\core\app\schemas\sync.py:165:22: F821 undefined name 'List'
.\core\app\schemas\sync.py:166:20: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:166:29: F821 undefined name 'Dict'
.\core\app\schemas\sync.py:166:39: F821 undefined name 'Any'
.\core\app\schemas\sync.py:169:25: F821 undefined name 'BaseModel'
.\core\app\schemas\sync.py:172:13: F821 undefined name 'Optional'
.\core\app\schemas\sync.py:178:24: F821 undefined name 'PaginatedResponse'
.\core\app\schemas\sync.py:182:27: F821 undefined name 'PaginatedResponse'
.\core\app\schemas\sync.py:186:30: F821 undefined name 'BaseResponse'
.\core\app\services\auto_recovery_manager_enhanced.py:85:6: E999 IndentationError: expected an indented block after function definition on line 84
.\core\app\services\auto_sync_scheduler.py:13:9: E999 IndentationError: unexpected indent
.\core\app\services\business_translation_rules.py:33:6: E999 IndentationError: expected an indented block after function definition on line 32
.\core\app\services\config_persistence_service.py:21:6: E999 IndentationError: expected an indented block after function definition on line 20
.\core\app\services\data_processor.py:20:6: E999 IndentationError: expected an indented block after function definition on line 19
.\core\app\services\data_write_manager.py:17:13: E999 IndentationError: unexpected indent
.\core\app\services\database_manager.py:19:18: E999 SyntaxError: invalid syntax
.\core\app\services\database_table_manager.py:11:13: E999 IndentationError: unexpected indent
.\core\app\services\enhanced_json_field_matcher.py:19:6: E999 IndentationError: expected an indented block after function definition on line 18
.\core\app\services\excel_field_matcher.py:5:4: E999 IndentationError: unexpected indent
.\core\app\services\excel_field_matcher_pretranslated.py:8:1: E265 block comment should start with '# '
.\core\app\services\excel_field_matcher_pretranslated.py:26:16: F821 undefined name 'Optional'
.\core\app\services\excel_field_matcher_pretranslated.py:27:18: F821 undefined name 'Optional'
.\core\app\services\excel_field_matcher_pretranslated.py:28:18: F821 undefined name 'Optional'
.\core\app\services\excel_field_matcher_pretranslated.py:50:80: E501 line too long (83 > 79 characters)
.\core\app\services\excel_field_matcher_pretranslated.py:82:70: F821 undefined name 'e'
.\core\app\services\excel_field_matcher_pretranslated.py:88:52: F821 undefined name 'List'
.\core\app\services\excel_field_matcher_pretranslated.py:115:58: F821 undefined name 'e'
.\core\app\services\excel_field_matcher_pretranslated.py:139:80: E501 line too long (88 > 79 characters)
.\core\app\services\excel_field_matcher_pretranslated.py:142:56: F821 undefined name 'e'
.\core\app\services\excel_field_matcher_pretranslated.py:147:10: F821 undefined name 'Dict'
.\core\app\services\excel_field_matcher_pretranslated.py:171:58: F821 undefined name 'e'
.\core\app\services\excel_field_matcher_pretranslated.py:174:60: F821 undefined name 'e'
.\core\app\services\excel_field_matcher_pretranslated.py:176:37: F821 undefined name 'e'
.\core\app\services\excel_field_matcher_pretranslated.py:180:50: F821 undefined name 'List'
.\core\app\services\excel_field_matcher_pretranslated.py:181:10: F821 undefined name 'List'
.\core\app\services\excel_field_matcher_pretranslated.py:207:64: F821 undefined name 'e'
.\core\app\services\excel_field_matcher_pretranslated.py:211:24: F821 undefined name 'Dict'
.\core\app\services\excel_field_matcher_pretranslated.py:216:80: E501 line too long (80 > 79 characters)
.\core\app\services\excel_field_matcher_pretranslated.py:228:59: F821 undefined name 'e'
.\core\app\services\excel_field_matcher_pretranslated.py:231:54: F821 undefined name 'Dict'
.\core\app\services\excel_field_matcher_pretranslated.py:245:59: F821 undefined name 'e'
.\core\app\services\excel_field_matcher_pretranslated.py:248:66: F821 undefined name 'List'
.\core\app\services\excel_field_matcher_pretranslated.py:261:80: E501 line too long (88 > 79 characters)
.\core\app\services\excel_field_matcher_pretranslated.py:268:59: F821 undefined name 'e'
.\core\app\services\fast_sync_service.py:30:6: E999 IndentationError: expected an indented block after function definition on line 29
.\core\app\services\field_analysis_service.py:18:6: E999 IndentationError: expected an indented block after function definition on line 17
.\core\app\services\field_config_service.py:6:1: E265 block comment should start with '# '
.\core\app\services\field_config_service.py:36:80: E501 line too long (83 > 79 characters)
.\core\app\services\field_config_service.py:44:66: F821 undefined name 'Dict'
.\core\app\services\field_config_service.py:47:80: E501 line too long (88 > 79 characters)
.\core\app\services\field_config_service.py:57:19: F821 undefined name 'Optional'
.\core\app\services\field_config_service.py:57:28: F821 undefined name 'List'
.\core\app\services\field_config_service.py:57:33: F821 undefined name 'Dict'
.\core\app\services\field_config_service.py:58:20: F821 undefined name 'Optional'
.\core\app\services\field_config_service.py:61:10: F821 undefined name 'Dict'
.\core\app\services\field_config_service.py:87:80: E501 line too long (87 > 79 characters)
.\core\app\services\field_config_service.py:110:80: E501 line too long (82 > 79 characters)
.\core\app\services\field_config_service.py:111:80: E501 line too long (88 > 79 characters)
.\core\app\services\field_config_service.py:119:80: E501 line too long (81 > 79 characters)
.\core\app\services\field_config_service.py:125:80: E501 line too long (83 > 79 characters)
.\core\app\services\field_config_service.py:136:80: E501 line too long (83 > 79 characters)
.\core\app\services\field_config_service.py:146:80: E501 line too long (83 > 79 characters)
.\core\app\services\field_config_service.py:151:83: F821 undefined name 'e'
.\core\app\services\field_config_service.py:156:17: F821 undefined name 'Any'
.\core\app\services\field_config_service.py:160:10: F821 undefined name 'Dict'
.\core\app\services\field_config_service.py:160:20: F821 undefined name 'Dict'
.\core\app\services\field_config_service.py:192:80: E501 line too long (83 > 79 characters)
.\core\app\services\field_config_service.py:199:80: E501 line too long (86 > 79 characters)
.\core\app\services\field_config_service.py:255:22: F821 undefined name 'Optional'
.\core\app\services\field_config_service.py:256:22: F821 undefined name 'Optional'
.\core\app\services\field_config_service.py:257:10: F821 undefined name 'Dict'
.\core\app\services\field_config_service.py:310:89: F821 undefined name 'e'
.\core\app\services\field_config_service.py:337:66: F821 undefined name 'Dict'
.\core\app\services\field_config_service.py:356:89: F821 undefined name 'e'
.\core\app\services\field_config_service.py:358:61: F821 undefined name 'Optional'
.\core\app\services\field_config_service.py:358:70: F821 undefined name 'Dict'
.\core\app\services\field_config_service.py:378:89: F821 undefined name 'e'
.\core\app\services\field_extractor_service.py:23:6: E999 IndentationError: expected an indented block after function definition on line 22
.\core\app\services\field_validation_service.py:28:6: E999 IndentationError: expected an indented block after function definition on line 27
.\core\app\services\field_value_mapping_service.py:20:6: E999 IndentationError: expected an indented block after function definition on line 19
.\core\app\services\intelligent_field_mapper.py:18:2: F821 undefined name 'dataclass'
.\core\app\services\intelligent_field_mapper.py:25:2: F821 undefined name 'dataclass'
.\core\app\services\intelligent_field_mapper.py:35:2: F821 undefined name 'dataclass'
.\core\app\services\intelligent_field_mapper.py:49:20: F821 undefined name 'Dict'
.\core\app\services\intelligent_field_mapper.py:50:23: F821 undefined name 'Dict'
.\core\app\services\intelligent_field_mapper.py:55:2: F821 undefined name 'dataclass'
.\core\app\services\intelligent_field_mapper.py:67:13: F821 undefined name 'Dict'
.\core\app\services\intelligent_field_mapper.py:85:80: E501 line too long (83 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:99:23: F821 undefined name 'Optional'
.\core\app\services\intelligent_field_mapper.py:99:32: F821 undefined name 'Any'
.\core\app\services\intelligent_field_mapper.py:125:80: E501 line too long (102 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:133:80: E501 line too long (85 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:145:17: F541 f-string is missing placeholders
.\core\app\services\intelligent_field_mapper.py:154:26: F541 f-string is missing placeholders
.\core\app\services\intelligent_field_mapper.py:154:85: F821 undefined name 'e'
.\core\app\services\intelligent_field_mapper.py:159:10: F821 undefined name 'Dict'
.\core\app\services\intelligent_field_mapper.py:184:30: F541 f-string is missing placeholders
.\core\app\services\intelligent_field_mapper.py:184:89: F821 undefined name 'e'
.\core\app\services\intelligent_field_mapper.py:187:21: F541 f-string is missing placeholders
.\core\app\services\intelligent_field_mapper.py:190:58: F821 undefined name 'Optional'
.\core\app\services\intelligent_field_mapper.py:190:80: E501 line too long (80 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:205:80: E501 line too long (80 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:221:28: F541 f-string is missing placeholders
.\core\app\services\intelligent_field_mapper.py:221:87: F821 undefined name 'e'
.\core\app\services\intelligent_field_mapper.py:227:21: F821 undefined name 'List'
.\core\app\services\intelligent_field_mapper.py:228:20: F821 undefined name 'List'
.\core\app\services\intelligent_field_mapper.py:229:26: F821 undefined name 'Optional'
.\core\app\services\intelligent_field_mapper.py:231:10: F821 undefined name 'Dict'
.\core\app\services\intelligent_field_mapper.py:271:80: E501 line too long (83 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:288:80: E501 line too long (82 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:296:80: E501 line too long (87 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:349:51: F821 undefined name 'Optional'
.\core\app\services\intelligent_field_mapper.py:365:80: E501 line too long (83 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:377:74: F821 undefined name 'e'
.\core\app\services\intelligent_field_mapper.py:386:80: E501 line too long (81 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:399:51: F821 undefined name 'Optional'
.\core\app\services\intelligent_field_mapper.py:476:10: F821 undefined name 'Dict'
.\core\app\services\intelligent_field_mapper.py:495:80: E501 line too long (82 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:506:17: F821 undefined name 'Dict'
.\core\app\services\intelligent_field_mapper.py:507:26: F821 undefined name 'Optional'
.\core\app\services\intelligent_field_mapper.py:513:80: E501 line too long (82 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:542:80: E501 line too long (85 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:556:29: F821 undefined name 'asdict'
.\core\app\services\intelligent_field_mapper.py:565:80: E501 line too long (86 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:575:21: F541 f-string is missing placeholders
.\core\app\services\intelligent_field_mapper.py:575:80: E501 line too long (84 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:577:39: F821 undefined name 'Dict'
.\core\app\services\intelligent_field_mapper.py:577:49: F821 undefined name 'Dict'
.\core\app\services\intelligent_field_mapper.py:577:59: F821 undefined name 'Any'
.\core\app\services\intelligent_field_mapper.py:587:59: F821 undefined name 'Dict'
.\core\app\services\intelligent_field_mapper.py:587:69: F821 undefined name 'Any'
.\core\app\services\intelligent_field_mapper.py:627:80: E501 line too long (85 > 79 characters)
.\core\app\services\intelligent_field_mapper.py:635:67: F821 undefined name 'e'
.\core\app\services\log_service.py:21:6: E999 IndentationError: expected an indented block after function definition on line 20
.\core\app\services\maintenance_manager.py:22:6: E999 IndentationError: expected an indented block after function definition on line 21
.\core\app\services\md_parser.py:31:6: E999 IndentationError: expected an indented block after function definition on line 30
.\core\app\services\monitor_service.py:19:6: E999 IndentationError: expected an indented block after function definition on line 18
.\core\app\services\retry_helper.py:5:13: E999 IndentationError: unexpected indent
.\core\app\services\robust_json_parser.py:33:6: E999 IndentationError: expected an indented block after function definition on line 32
.\core\app\services\status_mapping_service.py:20:6: E999 IndentationError: expected an indented block after function definition on line 19
.\core\app\services\sync_status_manager.py:21:6: E999 IndentationError: expected an indented block after function definition on line 20
.\core\app\services\task_service.py:21:6: E999 IndentationError: expected an indented block after function definition on line 20
.\core\app\services\token_service.py:16:80: E501 line too long (107 > 79 characters)
.\core\app\services\token_service.py:39:17: F821 undefined name 'generate_signature'
.\core\app\services\token_service.py:40:80: E501 line too long (122 > 79 characters)
.\core\app\services\token_service.py:48:28: F821 undefined name 'get_data_center_domains'
.\core\app\services\token_service.py:51:16: F821 undefined name 'get_access_token'
.\core\app\services\unified_field_manager.py:5:29: E999 IndentationError: unexpected indent
.\core\app\services\zero_downtime_implementation.py:26:6: E999 IndentationError: expected an indented block after function definition on line 25
.\core\health_check.py:5:1: E265 block comment should start with '# '
.\core\health_check.py:64:30: F821 undefined name 'e'
.\core\health_check.py:66:39: F821 undefined name 'e'
.\core\main.py:6:1: E265 block comment should start with '# '
.\core\main.py:45:43: F821 undefined name 'e'
.\dev-tools\cleanup\code_cleaner.py:17:6: E999 IndentationError: expected an indented block after function definition on line 16
.\dev-tools\mock\mock_utils.py:21:6: E999 IndentationError: expected an indented block after function definition on line 16
.\execute_task_checklist.py:643:14: E999 SyntaxError: unterminated string literal (detected at line 643)
.\file_sentinel.py:20:6: E999 IndentationError: expected an indented block after function definition on line 19
.\final_batch.py:5:1: E265 block comment should start with '# '
.\final_batch.py:20:80: E501 line too long (85 > 79 characters)
.\final_batch.py:21:80: E501 line too long (82 > 79 characters)
.\final_batch.py:27:11: F541 f-string is missing placeholders
.\final_verification.py:44:60: F821 undefined name 'e'
.\fix_all_flake8.py:120:35: E999 SyntaxError: invalid syntax
.\fix_build_script.py:15:6: E999 IndentationError: expected an indented block after function definition on line 14
.\fix_execute_task_script.py:4:9: E999 IndentationError: unexpected indent
.\fix_issues.py:71:26: E999 SyntaxError: unterminated string literal (detected at line 71)
.\fix_sonarqube_issues.py:17:6: E999 IndentationError: expected an indented block after function definition on line 16
.\fix_task_issues.py:16:6: E999 IndentationError: expected an indented block after function definition on line 15
.\fix_xml_files.py:27:6: E999 IndentationError: expected an indented block after function definition on line 26
.\frontend\start_frontend_clean.py:49:80: E501 line too long (87 > 79 characters)
.\frontend\start_frontend_fixed.py:18:18: E999 SyntaxError: unexpected character after line continuation character
.\function_duplicate_checker.py:15:6: E999 IndentationError: expected an indented block after function definition on line 14
.\generate_stats.py:5:1: E265 block comment should start with '# '
.\generate_stats.py:36:80: F821 undefined name 'e'
.\generate_stats.py:36:80: E501 line too long (82 > 79 characters)
.\generate_stats.py:46:13: F821 undefined name 'defaultdict'
.\generate_stats.py:116:80: E501 line too long (88 > 79 characters)
.\generate_stats.py:124:80: E501 line too long (80 > 79 characters)
.\generate_stats.py:133:80: E501 line too long (88 > 79 characters)
.\generate_stats.py:138:80: E501 line too long (152 > 79 characters)
.\generate_stats.py:145:80: E501 line too long (84 > 79 characters)
.\generate_stats.py:161:80: E501 line too long (80 > 79 characters)
.\generate_stats.py:166:17: F821 undefined name 'defaultdict'
.\generate_stats.py:174:80: E501 line too long (87 > 79 characters)
.\generate_stats.py:201:80: E501 line too long (82 > 79 characters)
.\generate_stats.py:218:80: E501 line too long (119 > 79 characters)
.\install_windows_service.py:32:6: E999 IndentationError: expected an indented block after function definition on line 31
.\machine_cleanup.py:15:6: E999 IndentationError: expected an indented block after function definition on line 14
.\month2_config\config_rollback\manager.py:18:6: E999 IndentationError: expected an indented block after function definition on line 17
.\month2_config\two_step_save\manager.py:21:6: E999 IndentationError: expected an indented block after function definition on line 17
.\month2_database\table_creation\manager.py:19:6: E999 IndentationError: expected an indented block after function definition on line 17
.\new-system\modules\purchase_order\models.py:17:10: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:17:17: F821 undefined name 'Integer'
.\new-system\modules\purchase_order\models.py:18:20: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:18:27: F821 undefined name 'String'
.\new-system\modules\purchase_order\models.py:18:80: E501 line too long (82 > 79 characters)
.\new-system\modules\purchase_order\models.py:19:19: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:19:26: F821 undefined name 'Integer'
.\new-system\modules\purchase_order\models.py:20:21: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:20:28: F821 undefined name 'String'
.\new-system\modules\purchase_order\models.py:21:18: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:21:25: F821 undefined name 'DateTime'
.\new-system\modules\purchase_order\models.py:22:21: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:22:28: F821 undefined name 'DateTime'
.\new-system\modules\purchase_order\models.py:23:20: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:23:27: F821 undefined name 'Decimal'
.\new-system\modules\purchase_order\models.py:24:14: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:24:21: F821 undefined name 'String'
.\new-system\modules\purchase_order\models.py:25:13: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:25:20: F821 undefined name 'Text'
.\new-system\modules\purchase_order\models.py:26:18: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:26:25: F821 undefined name 'DateTime'
.\new-system\modules\purchase_order\models.py:27:18: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:27:25: F821 undefined name 'DateTime'
.\new-system\modules\purchase_order\models.py:36:80: E501 line too long (83 > 79 characters)
.\new-system\modules\purchase_order\models.py:40:80: E501 line too long (81 > 79 characters)
.\new-system\modules\purchase_order\models.py:43:80: E501 line too long (83 > 79 characters)
.\new-system\modules\purchase_order\models.py:44:80: E501 line too long (83 > 79 characters)
.\new-system\modules\purchase_order\models.py:53:10: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:53:17: F821 undefined name 'Integer'
.\new-system\modules\purchase_order\models.py:54:16: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:54:23: F821 undefined name 'Integer'
.\new-system\modules\purchase_order\models.py:55:19: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:55:26: F821 undefined name 'Integer'
.\new-system\modules\purchase_order\models.py:56:21: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:56:28: F821 undefined name 'String'
.\new-system\modules\purchase_order\models.py:57:16: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:57:23: F821 undefined name 'Decimal'
.\new-system\modules\purchase_order\models.py:58:18: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:58:25: F821 undefined name 'Decimal'
.\new-system\modules\purchase_order\models.py:59:19: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:59:26: F821 undefined name 'Decimal'
.\new-system\modules\purchase_order\models.py:60:12: F821 undefined name 'Column'
.\new-system\modules\purchase_order\models.py:60:19: F821 undefined name 'String'
.\new-system\modules\purchase_order\routes.py:9:10: F821 undefined name 'APIRouter'
.\new-system\modules\purchase_order\routes.py:12:33: F821 undefined name 'PurchaseOrderListResponse'
.\new-system\modules\purchase_order\routes.py:14:17: F821 undefined name 'Query'
.\new-system\modules\purchase_order\routes.py:15:17: F821 undefined name 'Query'
.\new-system\modules\purchase_order\routes.py:16:13: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\routes.py:16:29: F821 undefined name 'Query'
.\new-system\modules\purchase_order\routes.py:17:18: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\routes.py:17:34: F821 undefined name 'Query'
.\new-system\modules\purchase_order\routes.py:18:17: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\routes.py:18:38: F821 undefined name 'Query'
.\new-system\modules\purchase_order\routes.py:19:15: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\routes.py:19:36: F821 undefined name 'Query'
.\new-system\modules\purchase_order\routes.py:20:37: F821 undefined name 'Depends'
.\new-system\modules\purchase_order\routes.py:34:15: F821 undefined name 'HTTPException'
.\new-system\modules\purchase_order\routes.py:34:57: F821 undefined name 'e'
.\new-system\modules\purchase_order\routes.py:37:43: F821 undefined name 'PurchaseOrderResponse'
.\new-system\modules\purchase_order\routes.py:38:77: F821 undefined name 'Depends'
.\new-system\modules\purchase_order\routes.py:38:80: E501 line too long (87 > 79 characters)
.\new-system\modules\purchase_order\routes.py:43:19: F821 undefined name 'HTTPException'
.\new-system\modules\purchase_order\routes.py:45:12: F821 undefined name 'HTTPException'
.\new-system\modules\purchase_order\routes.py:48:15: F821 undefined name 'HTTPException'
.\new-system\modules\purchase_order\routes.py:48:57: F821 undefined name 'e'
.\new-system\modules\purchase_order\routes.py:52:68: F821 undefined name 'Depends'
.\new-system\modules\purchase_order\routes.py:62:15: F821 undefined name 'HTTPException'
.\new-system\modules\purchase_order\routes.py:62:57: F821 undefined name 'e'
.\new-system\modules\purchase_order\schema.py:8:31: F821 undefined name 'BaseModel'
.\new-system\modules\purchase_order\schema.py:17:11: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\schema.py:20:27: F821 undefined name 'BaseModel'
.\new-system\modules\purchase_order\schema.py:27:17: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\schema.py:28:20: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\schema.py:31:12: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\schema.py:32:17: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\schema.py:33:17: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\schema.py:39:12: F821 undefined name 'List'
.\new-system\modules\purchase_order\schema.py:42:33: F821 undefined name 'BaseModel'
.\new-system\modules\purchase_order\schema.py:45:12: F821 undefined name 'List'
.\new-system\modules\purchase_order\service.py:13:38: F821 undefined name 'Depends'
.\new-system\modules\purchase_order\service.py:20:17: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\service.py:21:22: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\service.py:22:21: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\service.py:23:19: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\service.py:24:10: F821 undefined name 'Dict'
.\new-system\modules\purchase_order\service.py:24:20: F821 undefined name 'Any'
.\new-system\modules\purchase_order\service.py:31:31: F821 undefined name 'PurchaseOrder'
.\new-system\modules\purchase_order\service.py:34:31: F821 undefined name 'PurchaseOrder'
.\new-system\modules\purchase_order\service.py:37:31: F821 undefined name 'PurchaseOrder'
.\new-system\modules\purchase_order\service.py:40:31: F821 undefined name 'PurchaseOrder'
.\new-system\modules\purchase_order\service.py:43:31: F821 undefined name 'PurchaseOrder'
.\new-system\modules\purchase_order\service.py:46:34: F821 undefined name 'and_'
.\new-system\modules\purchase_order\service.py:60:64: F821 undefined name 'Optional'
.\new-system\modules\purchase_order\service.py:60:73: F821 undefined name 'Dict'
.\new-system\modules\purchase_order\service.py:60:80: E501 line too long (88 > 79 characters)
.\new-system\modules\purchase_order\service.py:60:83: F821 undefined name 'Any'
.\new-system\modules\purchase_order\service.py:63:27: F821 undefined name 'PurchaseOrder'
.\new-system\modules\purchase_order\service.py:63:49: F821 undefined name 'PurchaseOrder'
.\new-system\modules\purchase_order\service.py:63:80: E501 line too long (85 > 79 characters)
.\new-system\modules\purchase_order\service.py:71:27: F821 undefined name 'PurchaseOrderItem'
.\new-system\modules\purchase_order\service.py:72:21: F821 undefined name 'PurchaseOrderItem'
.\new-system\modules\purchase_order\service.py:81:61: F821 undefined name 'List'
.\new-system\modules\purchase_order\service.py:81:66: F821 undefined name 'Dict'
.\new-system\modules\purchase_order\service.py:81:76: F821 undefined name 'Any'
.\new-system\modules\purchase_order\service.py:81:80: E501 line too long (81 > 79 characters)
.\new-system\modules\purchase_order\service.py:83:32: F821 undefined name 'PurchaseOrder'
.\new-system\modules\产品入库单列表查询\service.py:7:6: E999 IndentationError: expected an indented block after function definition on line 6
.\port_locker.py:18:6: E999 IndentationError: expected an indented block after function definition on line 17
.\production_readiness_report.py:66:43: E999 SyntaxError: invalid syntax
.\project_health_check.py:8:13: E999 IndentationError: unexpected indent
.\quick_health_check.py:83:45: E999 SyntaxError: invalid syntax
.\quick_health_check_fixed.py:5:21: E999 IndentationError: unexpected indent
.\remaining_issues_fixer.py:5:9: E999 IndentationError: unexpected indent
.\run_comprehensive_check.py:18:6: E999 IndentationError: expected an indented block after function definition on line 17
.\scripts\add_api_config.py:5:1: E265 block comment should start with '# '
.\scripts\add_api_config.py:72:58: F821 undefined name 'e'
.\scripts\add_api_config.py:74:17: F541 f-string is missing placeholders
.\scripts\auto_migration.py:18:6: E999 IndentationError: expected an indented block after function definition on line 17
.\scripts\auto_migration_pipeline.py:9:5: E999 IndentationError: unexpected indent
.\scripts\batch_init_modules.py:6:1: E265 block comment should start with '# '
.\scripts\batch_init_modules.py:61:80: E501 line too long (84 > 79 characters)
.\scripts\batch_init_modules.py:66:13: F841 local variable 'result' is assigned to but never used
.\scripts\batch_init_modules.py:92:61: F821 undefined name 'e'
.\scripts\batch_init_modules.py:97:34: F821 undefined name 'e'
.\scripts\batch_init_modules.py:133:11: F541 f-string is missing placeholders
.\scripts\batch_init_modules.py:162:80: E501 line too long (80 > 79 characters)
.\scripts\batch_initialize_next.py:4:1: F811 redefinition of unused 'json' from line 1
.\scripts\batch_initialize_next.py:5:1: F811 redefinition of unused 'Path' from line 2
.\scripts\batch_initialize_next.py:6:1: F811 redefinition of unused 'datetime' from line 3
.\scripts\batch_initialize_next.py:7:1: F811 redefinition of unused 'Path' from line 5
.\scripts\batch_initialize_next.py:9:1: E265 block comment should start with '# '
.\scripts\batch_initialize_next.py:49:80: E501 line too long (93 > 79 characters)
.\scripts\batch_initialize_next.py:56:80: E501 line too long (89 > 79 characters)
.\scripts\batch_initialize_next.py:88:80: E501 line too long (80 > 79 characters)
.\scripts\batch_initialize_next.py:110:80: E501 line too long (87 > 79 characters)
.\scripts\batch_initialize_next.py:205:11: F541 f-string is missing placeholders
.\scripts\clean_debug_code.py:15:6: E999 IndentationError: expected an indented block after function definition on line 14
.\scripts\clean_hardcoded_data.py:4:1: E265 block comment should start with '# '
.\scripts\clean_hardcoded_data.py:135:58: F821 undefined name 'e'
.\scripts\clean_hardcoded_data.py:155:58: F821 undefined name 'e'
.\scripts\clean_hardcoded_data.py:157:11: F541 f-string is missing placeholders
.\scripts\clean_hardcoded_data.py:168:11: F541 f-string is missing placeholders
.\scripts\cleanup_test_code.py:18:6: E999 IndentationError: expected an indented block after function definition on line 17
.\scripts\complete_modules.py:4:1: E265 block comment should start with '# '
.\scripts\complete_modules.py:34:13: F841 local variable 'result' is assigned to but never used
.\scripts\complete_modules.py:34:80: E501 line too long (84 > 79 characters)
.\scripts\complete_modules.py:56:5: F821 undefined name 'main'
.\scripts\database_dual_writer.py:39:6: E999 IndentationError: expected an indented block after function definition on line 38
.\scripts\database_dual_writer_simple.py:19:6: E999 IndentationError: expected an indented block after function definition on line 18
.\scripts\final_status_check.py:6:1: E265 block comment should start with '# '
.\scripts\final_status_check.py:39:80: E501 line too long (88 > 79 characters)
.\scripts\final_status_check.py:44:48: F821 undefined name 'e'
.\scripts\final_status_check.py:170:47: F821 undefined name 'e'
.\scripts\graveyard_safety_analyzer.py:4:1: E265 block comment should start with '# '
.\scripts\graveyard_safety_analyzer.py:24:25: F821 undefined name 'datetime'
.\scripts\graveyard_safety_analyzer.py:53:42: F821 undefined name 'e'
.\scripts\graveyard_safety_analyzer.py:102:11: F541 f-string is missing placeholders
.\scripts\graveyard_safety_analyzer.py:109:11: F541 f-string is missing placeholders
.\scripts\graveyard_safety_analyzer.py:123:5: F821 undefined name 'main'
.\scripts\health_check.py:18:21: E999 SyntaxError: invalid syntax
.\scripts\manual_cleanup.py:4:1: E265 block comment should start with '# '
.\scripts\manual_cleanup.py:56:56: F821 undefined name 'e'
.\scripts\manual_cleanup.py:69:61: F821 undefined name 'e'
.\scripts\manual_cleanup.py:83:44: F821 undefined name 'e'
.\scripts\manual_cleanup.py:93:50: F821 undefined name 'e'
.\scripts\manual_cleanup.py:95:11: F541 f-string is missing placeholders
.\scripts\migrate_purchase_order.py:30:6: E999 IndentationError: expected an indented block after function definition on line 29
.\scripts\migrate_产品入库单列表查询.py:24:20: E999 SyntaxError: invalid syntax. Perhaps you forgot a comma?
.\scripts\migrate_产品入库单列表查询_fixed.py:25:20: E999 SyntaxError: invalid syntax. Perhaps you forgot a comma?
.\scripts\migrate_生产订单列表查询.py:24:20: E999 SyntaxError: invalid syntax. Perhaps you forgot a comma?
.\scripts\migrate_请购单列表查询.py:24:20: E999 SyntaxError: invalid syntax. Perhaps you forgot a comma?
.\scripts\migrate_采购入库单列表.py:19:6: E999 IndentationError: expected an indented block after function definition on line 18
.\scripts\module_tracker.py:16:6: E999 IndentationError: expected an indented block after function definition on line 15
.\scripts\module_tracker_simple.py:18:6: E999 IndentationError: expected an indented block after function definition on line 17
.\scripts\next_module.py:4:1: F811 redefinition of unused 'json' from line 1
.\scripts\next_module.py:5:1: F811 redefinition of unused 'Path' from line 3
.\scripts\next_module.py:8:1: E265 block comment should start with '# '
.\scripts\next_module.py:50:80: E501 line too long (90 > 79 characters)
.\scripts\next_module.py:62:80: E501 line too long (115 > 79 characters)
.\scripts\next_module.py:165:80: E501 line too long (85 > 79 characters)
.\scripts\next_module.py:197:11: F541 f-string is missing placeholders
.\scripts\next_module.py:199:80: E501 line too long (111 > 79 characters)
.\scripts\next_module.py:202:80: E501 line too long (111 > 79 characters)
.\scripts\port_manager.py:1:12: E999 SyntaxError: unexpected character after line continuation character
.\scripts\port_manager_clean.py:58:80: E501 line too long (88 > 79 characters)
.\scripts\port_manager_clean.py:65:80: E501 line too long (81 > 79 characters)
.\scripts\port_manager_clean.py:71:80: E501 line too long (88 > 79 characters)
.\scripts\port_manager_clean.py:106:80: E501 line too long (80 > 79 characters)
.\scripts\port_manager_clean.py:113:80: E501 line too long (81 > 79 characters)
.\scripts\port_manager_clean.py:144:80: E501 line too long (81 > 79 characters)
.\scripts\quick_batch_migrate.py:25:9: E999 SyntaxError: invalid syntax
.\scripts\quick_migrate.py:5:1: E265 block comment should start with '# '
.\scripts\quick_migrate.py:16:80: E501 line too long (80 > 79 characters)
.\scripts\quick_migrate.py:29:44: F821 undefined name 'e'
.\scripts\quick_migrate.py:43:80: E501 line too long (82 > 79 characters)
.\scripts\quick_migrate.py:48:80: E501 line too long (82 > 79 characters)
.\scripts\rollback_batch_writes.py:32:6: E999 IndentationError: expected an indented block after function definition on line 31
.\scripts\simple_migrator.py:22:6: E999 IndentationError: expected an indented block after function definition on line 21
.\scripts\ultra_simple_migrate.py:7:1: E265 block comment should start with '# '
.\scripts\ultra_simple_migrate.py:20:80: E501 line too long (80 > 79 characters)
.\scripts\ultra_simple_migrate.py:27:80: E501 line too long (86 > 79 characters)
.\scripts\ultra_simple_migrate.py:28:80: E501 line too long (88 > 79 characters)
.\scripts\ultra_simple_migrate.py:48:52: F821 undefined name 'e'
.\scripts\ultra_simple_migrate.py:98:5: F821 undefined name 'main'
.\smart_duplicate_cleaner.py:16:6: E999 IndentationError: expected an indented block after function definition on line 15
.\smart_file_creator.py:18:6: E999 IndentationError: expected an indented block after function definition on line 17
.\sonarqube_cleanup.py:100:63: F821 undefined name 'e'
.\sonarqube_cleanup.py:141:54: F821 undefined name 'e'
.\sonarqube_cleanup.py:173:54: F821 undefined name 'e'
.\sonarqube_cleanup.py:189:80: E501 line too long (84 > 79 characters)
.\sonarqube_cleanup.py:212:80: E501 line too long (82 > 79 characters)
.\sonarqube_cleanup.py:271:57: F821 undefined name 'e'
.\start_month1_validation.py:5:1: E265 block comment should start with '# '
.\start_month1_validation.py:77:42: F821 undefined name 'e'
.\start_month1_validation.py:117:80: E501 line too long (82 > 79 characters)
.\start_month1_validation.py:141:80: E501 line too long (81 > 79 characters)
.\start_month1_validation.py:146:80: E501 line too long (80 > 79 characters)
.\start_month1_validation.py:149:80: E501 line too long (94 > 79 characters)
.\start_month2.py:4:1: E265 block comment should start with '# '
.\start_month2.py:113:80: E501 line too long (84 > 79 characters)
.\start_month2.py:153:80: E501 line too long (86 > 79 characters)
.\start_month2.py:209:80: E501 line too long (87 > 79 characters)
.\systematic_duplicate_detector.py:18:6: E999 IndentationError: expected an indented block after function definition on line 17
.\tests\test_md_to_json_converter.py:8:6: E999 SyntaxError: invalid syntax
.\tests\test_rollback_scripts.py:164:23: E999 SyntaxError: invalid syntax
.\tools\md_to_json_converter.py:9:1: E265 block comment should start with '# '
.\tools\md_to_json_converter.py:19:80: E501 line too long (83 > 79 characters)
.\tools\md_to_json_converter.py:52:80: E501 line too long (80 > 79 characters)
.\tools\md_to_json_converter.py:64:57: E203 whitespace before ':'
.\tools\md_to_json_converter.py:67:80: E501 line too long (80 > 79 characters)
.\tools\md_to_json_converter.py:101:76: F821 undefined name 'e'
.\tools\md_to_json_converter.py:144:80: E501 line too long (81 > 79 characters)
.\tools\md_to_json_converter.py:147:22: F541 f-string is missing placeholders
.\tools\md_to_json_converter.py:156:50: F821 undefined name 'e'
.\unused_import_checker.py:12:6: E999 IndentationError: expected an indented block after function definition on line 11
.\verify_fixes.py:6:1: E265 block comment should start with '# '
.\verify_fixes.py:38:47: F821 undefined name 'e'
.\verify_fixes.py:46:80: E501 line too long (86 > 79 characters)
.\verify_module.py:17:6: E999 IndentationError: expected an indented block after function definition on line 16
.\verify_startup.py:1:12: E999 SyntaxError: unexpected character after line continuation character
