/* YS-API V3.1 实时日志组件样式 */

.realtime-log-container {
    background: #1a1a1a;
    border: 1px solid #444;
    border-radius: 8px;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #e0e0e0;
    height: 600px;
    display: flex;
    flex-direction: column;
}

/* 日志头部 */
.log-header {
    background: #2d2d2d;
    border-bottom: 1px solid #444;
    padding: 15px;
    flex-shrink: 0;
}

.log-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.log-controls .btn {
    padding: 6px 12px;
    font-size: 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.log-controls .btn-success {
    background: #4ade80;
    color: white;
}

.log-controls .btn-success:hover {
    background: #22c55e;
}

.log-controls .btn-warning {
    background: #fbbf24;
    color: white;
}

.log-controls .btn-warning:hover {
    background: #f59e0b;
}

.log-controls .btn-secondary {
    background: #6b7280;
    color: white;
}

.log-controls .btn-secondary:hover {
    background: #4b5563;
}

.log-controls .btn-primary {
    background: #60a5fa;
    color: white;
}

.log-controls .btn-primary:hover {
    background: #3b82f6;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-left: auto;
    padding: 5px 10px;
    border-radius: 4px;
    background: #333;
    font-size: 12px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-connected {
    background: #4ade80;
}

.status-disconnected {
    background: #f87171;
}

.status-error {
    background: #f87171;
    animation: blink 1s infinite;
}

.status-reconnecting {
    background: #fbbf24;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.log-filters {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.log-filters .form-control {
    padding: 5px 8px;
    border: 1px solid #444;
    border-radius: 4px;
    background: #333;
    color: #e0e0e0;
    font-size: 12px;
}

.log-filters .form-control:focus {
    border-color: #60a5fa;
    outline: none;
}

.log-filters label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    cursor: pointer;
}

.log-filters input[type="checkbox"] {
    margin: 0;
}

/* 日志内容 */
.log-content {
    flex: 1;
    padding: 10px;
    overflow-y: auto;
    background: #1a1a1a;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    scrollbar-width: thin;
    scrollbar-color: #444 #1a1a1a;
}

.log-content::-webkit-scrollbar {
    width: 8px;
}

.log-content::-webkit-scrollbar-track {
    background: #1a1a1a;
}

.log-content::-webkit-scrollbar-thumb {
    background: #444;
    border-radius: 4px;
}

.log-content::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.log-entry {
    margin: 2px 0;
    padding: 4px 8px;
    border-radius: 4px;
    border-left: 3px solid transparent;
    transition: all 0.2s;
}

.log-entry:hover {
    background: #2d2d2d;
}

.log-entry.log-info {
    border-left-color: #60a5fa;
}

.log-entry.log-success {
    border-left-color: #4ade80;
}

.log-entry.log-warning {
    border-left-color: #fbbf24;
}

.log-entry.log-error {
    border-left-color: #f87171;
    background: rgba(248, 113, 113, 0.1);
}

.log-timestamp {
    color: #9ca3af;
    font-size: 11px;
    margin-right: 8px;
}

.log-icons {
    margin-right: 8px;
}

.log-stage {
    background: #374151;
    color: #d1d5db;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin-right: 8px;
    font-weight: 500;
}

.log-module {
    background: #1f2937;
    color: #9ca3af;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin-right: 8px;
}

.log-message {
    color: #e0e0e0;
}

.log-records {
    color: #60a5fa;
    font-size: 11px;
    margin-left: 8px;
}

.log-progress {
    color: #fbbf24;
    font-size: 11px;
    margin-left: 8px;
}

.log-latency {
    color: #9ca3af;
    font-size: 11px;
    margin-left: 8px;
}

.log-error {
    color: #f87171;
    font-size: 11px;
    margin-left: 8px;
    font-weight: 500;
}

/* 日志底部 */
.log-footer {
    background: #2d2d2d;
    border-top: 1px solid #444;
    padding: 10px 15px;
    flex-shrink: 0;
}

.log-stats {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #9ca3af;
}

.log-stats span {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .log-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .log-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .log-filters .form-control {
        width: 100%;
    }
    
    .log-stats {
        flex-direction: column;
        gap: 5px;
    }
    
    .realtime-log-container {
        height: 400px;
    }
}

/* 特定日志类型的样式 */
.log-entry[data-stage="INIT"] {
    background: rgba(96, 165, 250, 0.1);
}

.log-entry[data-stage="API_FETCH"] {
    background: rgba(139, 69, 19, 0.1);
}

.log-entry[data-stage="FIELD_MAPPING"] {
    background: rgba(255, 165, 0, 0.1);
}

.log-entry[data-stage="WRITE_DB"] {
    background: rgba(0, 128, 0, 0.1);
}

.log-entry[data-stage="FINISH"] {
    background: rgba(74, 222, 128, 0.1);
}

.log-entry[data-stage="ERROR"] {
    background: rgba(248, 113, 113, 0.2);
}

/* 动画效果 */
.log-entry {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载状态 */
.log-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #9ca3af;
}

.log-loading::before {
    content: "";
    width: 16px;
    height: 16px;
    border: 2px solid #444;
    border-top: 2px solid #60a5fa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.log-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #9ca3af;
    font-style: italic;
}

/* 高亮搜索结果 */
.log-entry.highlight {
    background: rgba(96, 165, 250, 0.2);
    border-left-color: #60a5fa;
}

/* 按钮禁用状态 */
.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease-in-out forwards;
}

@keyframes tooltipFadeIn {
    to {
        opacity: 1;
    }
}

/* 代码块样式 */
.log-code {
    background: #2d2d2d;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
}

/* 标签样式 */
.log-tag {
    background: #374151;
    color: #d1d5db;
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 10px;
    margin-left: 5px;
}

/* 进度条样式 */
.log-progress-bar {
    width: 100px;
    height: 4px;
    background: #444;
    border-radius: 2px;
    overflow: hidden;
    margin-left: 10px;
    display: inline-block;
}

.log-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #60a5fa, #3b82f6);
    transition: width 0.3s ease;
}

/* 日志级别指示器 */
.log-level-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    display: inline-block;
}

.log-level-indicator.info {
    background: #60a5fa;
}

.log-level-indicator.success {
    background: #4ade80;
}

.log-level-indicator.warning {
    background: #fbbf24;
}

.log-level-indicator.error {
    background: #f87171;
} 