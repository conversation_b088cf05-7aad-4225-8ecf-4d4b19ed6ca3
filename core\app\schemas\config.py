from datetime import datetime

"""
YS-API V3.0 字段配置数据模型
定义字段配置相关的请求和响应结构
"""


class FieldConfigRequest(BaseModel):
    """字段配置更新请求"""

    fields: Dict[str, FieldInfo]

    class Config:
        schema_extra = {
            "example": {
                "fields": {
                    "order_id": {
                        "chinese_name": "采购订单编号",
                        "data_type": "NVARCHAR(600)",
                        "is_selected": True,
                        "is_required": True,
                        "field_order": 1,
                        "importance_level": "高",
                        "source": "manual",
                    }
                }
            }
        }


class FieldConfigResponse(BaseResponse):
    """字段配置响应"""

    data: Dict[str, Any]
    success: bool = True

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "module_name": "purchase_order",
                    "display_name": "采购订单",
                    "version": "1.0.0",
                    "last_updated": "2025-01-13T10:30:00Z",
                    "field_count": 66,
                    "selected_count": 45,
                    "fields": {
                        "order_id": {
                            "chinese_name": "采购订单号",
                            "data_type": "NVARCHAR(600)",
                            "is_selected": True,
                            "is_required": True,
                            "field_order": 1,
                            "importance_level": "高",
                        }
                    },
                },
                "message": "获取字段配置成功",
            }
        }


class ModuleListResponse(BaseResponse):
    """模块列表响应"""

    data: Dict[str, Any]
    success: bool = True

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "modules": [
                        {
                            "module_name": "purchase_order",
                            "display_name": "采购订单",
                            "table_name": "purchase_order",
                            "api_endpoint": "/yonbip/scm/purchaseorder/list",
                            "md_file": "采购订单列表查询.md",
                            "is_active": True,
                            "field_count": 66,
                            "selected_count": 45,
                            "has_config": True,
                        }
                    ],
                    "total_count": 15,
                },
                "message": "获取模块列表成功",
            }
        }


class FieldExtractRequest(BaseModel):
    """字段提取请求"""

    record_id: Optional[str] = None
    force_refresh: bool = False
    merge_strategy: str = Field(default="safe", pattern="^(safe|overwrite)$")

    class Config:
        schema_extra = {
            "example": {
                "record_id": "PO202501130001",
                "force_refresh": False,
                "merge_strategy": "safe",
            }
        }


class FieldExtractResponse(BaseResponse):
    """字段提取响应"""

    data: Dict[str, Any]
    success: bool = True

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "extracted_fields": 78,
                    "new_fields": 12,
                    "updated_fields": 5,
                    "unchanged_fields": 61,
                    "conflicts": [
                        {
                            "field_name": "supplier_id",
                            "conflict_type": "data_type_mismatch",
                            "existing_type": "NVARCHAR(20)",
                            "detected_type": "INT",
                            "resolution": "kept_existing",
                        }
                    ],
                },
                "message": "字段提取成功",
            }
        }


class ApplyDefaultConfigRequest(BaseModel):
    """应用默认配置请求"""

    modules: Optional[List[str]] = None
    overwrite_manual: bool = False
    backup_existing: bool = True

    class Config:
        schema_extra = {
            "example": {
                "modules": ["purchase_order", "sales_order"],
                "overwrite_manual": False,
                "backup_existing": True,
            }
        }


class FieldMappingResponse(BaseResponse):
    """字段映射响应"""

    data: Dict[str, Any]
    success: bool = True

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "module_name": "purchase_order",
                    "api_to_db_mapping": {
                        "order_id": "采购订单号",
                        "supplier_name": "供应商名称",
                        "order_date": "订单日期",
                    },
                    "db_to_api_mapping": {
                        "采购订单号": "order_id",
                        "供应商名称": "supplier_name",
                        "订单日期": "order_date",
                    },
                    "data_type_mapping": {
                        "order_id": "NVARCHAR(600)",
                        "supplier_name": "NVARCHAR(200)",
                        "order_date": "DATETIME2",
                    },
                },
                "message": "获取字段映射成功",
            }
        }


class ConfigValidationResponse(BaseResponse):
    """配置验证响应"""

    data: ValidationResult
    success: bool = True


class ModuleConfigInfo(BaseModel):
    """模块配置信息"""

    module_name: str
    display_name: str
    table_name: str
    api_endpoint: str
    md_file: str
    is_active: bool = True
    field_count: int = 0
    selected_count: int = 0
    has_config: bool = False
    last_sync: Optional[datetime] = None
    sync_status: Optional[str] = None


class FieldConfigInfo(BaseModel):
    """字段配置详细信息"""

    api_field_name: str
    chinese_name: str
    data_type: str
    is_selected: bool = False
    is_required: bool = False
    is_primary_key: bool = False
    field_order: int = 0
    importance_level: str = "中"
    description: Optional[str] = ""
    sample_value: Optional[str] = ""
    validation_rules: Optional[Dict[str, Any]] = {}
    value_mapping: Optional[Dict[str, str]] = {}
    source: str = "api_auto"
    last_modified: Optional[datetime] = None


class ModuleFieldConfig(BaseModel):
    """完整的模块字段配置"""

    module_name: str
    display_name: str
    api_endpoint: str
    table_name: str
    version: str = "1.0.0"
    last_updated: Optional[datetime] = None
    total_fields: int = 0
    selected_fields: int = 0
    fields: Dict[str, FieldConfigInfo] = {}

    class Config:
        schema_extra = {
            "example": {
                "module_name": "purchase_order",
                "display_name": "采购订单",
                "api_endpoint": "/yonbip/scm/purchaseorder/list",
                "table_name": "purchase_order",
                "version": "1.0.0",
                "total_fields": 120,
                "selected_fields": 45,
                "fields": {
                    "code": {
                        "api_field_name": "code",
                        "chinese_name": "单据编码",
                        "data_type": "NVARCHAR(600)",
                        "is_selected": True,
                        "is_required": True,
                        "field_order": 1,
                        "importance_level": "高",
                    }
                },
            }
        }
