#!/bin/bash
# YS-API V3.0 项目快速恢复脚本
# 30分钟内恢复到干净可运行状态

echo "🚀 开始YS-API V3.0项目恢复..."
echo "=" * 50

# 步骤1：确定恢复点
echo "📍 步骤1：定位恢复点..."
GOOD_COMMIT="11a3e5c"  # v0-shit-mountain标签对应的提交
echo "选择恢复点: $GOOD_COMMIT (v0-shit-mountain)"

# 步骤2：备份当前有价值的文件
echo "💾 步骤2：备份有价值的文件..."
mkdir -p recovery_backup
mkdir -p recovery_backup/scripts
mkdir -p recovery_backup/docs
mkdir -p recovery_backup/config
mkdir -p recovery_backup/tools

# 备份核心业务文件
echo "备份核心脚本..."
cp -f analyze_dependencies.py recovery_backup/ 2>/dev/null || echo "跳过 analyze_dependencies.py"
cp -f three_step_fix.py recovery_backup/ 2>/dev/null || echo "跳过 three_step_fix.py" 
cp -f Flake8修复完成报告.md recovery_backup/docs/ 2>/dev/null || echo "跳过报告文件"

# 备份scripts目录的重要文件
cp -rf scripts/port_manager*.py recovery_backup/scripts/ 2>/dev/null || echo "跳过port_manager"
cp -rf scripts/migrate_*.py recovery_backup/scripts/ 2>/dev/null || echo "跳过迁移脚本"

# 备份配置文件
cp -rf config/ recovery_backup/config/ 2>/dev/null || echo "跳过config目录"

echo "✅ 备份完成"

# 步骤3：生成差异清单
echo "📋 步骤3：生成差异清单..."
git diff $GOOD_COMMIT HEAD > ai-changes.patch
git diff $GOOD_COMMIT HEAD --name-only > changed-files.txt

echo "差异文件数量: $(wc -l < changed-files.txt)"
echo "前10个变更文件:"
head -10 changed-files.txt

# 步骤4：回到干净状态
echo "🔄 步骤4：回到干净代码状态..."
echo "正在回滚到提交: $GOOD_COMMIT"

# 保存当前分支名
CURRENT_BRANCH=$(git branch --show-current)
echo "当前分支: $CURRENT_BRANCH"

# 强制回到干净状态
git reset --hard $GOOD_COMMIT

echo "✅ 已回到干净状态"

# 步骤5：选择性恢复有用文件
echo "📂 步骤5：恢复有价值的文件..."

# 从备份恢复核心文件
echo "恢复分析工具..."
cp -f recovery_backup/analyze_dependencies.py . 2>/dev/null && echo "✅ 恢复 analyze_dependencies.py"
cp -f recovery_backup/three_step_fix.py . 2>/dev/null && echo "✅ 恢复 three_step_fix.py"

# 恢复scripts目录
echo "恢复脚本目录..."
cp -rf recovery_backup/scripts/* scripts/ 2>/dev/null && echo "✅ 恢复 scripts 目录"

# 恢复配置文件（如果存在且有价值）
if [ -d "recovery_backup/config" ]; then
    cp -rf recovery_backup/config/* config/ 2>/dev/null && echo "✅ 恢复 config 目录"
fi

# 步骤6：清理和重建环境
echo "🧹 步骤6：清理环境..."

# 清理Python缓存
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true

# 清理日志
rm -rf logs/* 2>/dev/null || true
rm -rf temp_cleanup/* 2>/dev/null || true

echo "✅ 环境清理完成"

# 步骤7：验证核心功能
echo "🔍 步骤7：验证核心功能..."

# 检查Python环境
python --version || echo "⚠️ Python未安装或不在PATH中"

# 测试核心脚本
if [ -f "analyze_dependencies.py" ]; then
    echo "测试依赖分析器..."
    python analyze_dependencies.py > /dev/null 2>&1 && echo "✅ 依赖分析器正常" || echo "⚠️ 依赖分析器有问题"
fi

# 检查后端启动脚本
if [ -f "backend/start_server.py" ]; then
    echo "✅ 后端启动脚本存在"
else
    echo "⚠️ 后端启动脚本缺失"
fi

# 检查前端启动脚本  
if [ -f "frontend/start_frontend.py" ]; then
    echo "✅ 前端启动脚本存在"
else
    echo "⚠️ 前端启动脚本缺失"
fi

# 步骤8：创建新的提交点
echo "💾 步骤8：创建恢复点..."
git add .
git commit -m "项目恢复完成 - 回到v0-shit-mountain + 保留有价值更新"

echo ""
echo "🎉 项目恢复完成！"
echo "=" * 50
echo "📋 恢复总结:"
echo "- 基础代码: 回到 v0-shit-mountain 状态"
echo "- 保留文件: 分析工具、脚本、配置等有价值文件"
echo "- 清理环境: 删除缓存和临时文件"
echo "- 验证功能: 核心功能测试完成"
echo ""
echo "🚀 下一步:"
echo "1. 运行 python backend/start_server.py 启动后端"
echo "2. 运行 python frontend/start_frontend.py 启动前端"  
echo "3. 测试主要功能是否正常"
echo ""
echo "备份位置: recovery_backup/ 目录"
echo "差异文件: ai-changes.patch, changed-files.txt"
