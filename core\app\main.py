import codecs
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

import uvicorn
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response
from fastapi.staticfiles import StaticFiles

"""
YS-API V3.0 最小化启动版本
临时解决Python 3.13兼容性问题
"""


# 修复控制台编码问题
os.environ['PYTHONIOENCODING'] = 'utf-8'
if sys.platform.startswith('win'):
    try:

        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
    except Exception:
        pass

# 创建FastAPI应用实例
app = FastAPI(
    title="YS-API V3.0 - 生产环境",
    description="字段配置管理API - 生产环境",
    version="3.0.0-production",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 获取项目根目录 - 修正路径
project_root = Path(__file__).parent.parent.parent.parent

# 静态文件服务 - 修正路径
frontend_dir = project_root / "v3" / "frontend"
if frontend_dir.exists():
    app.mount(
        "/frontend",
        StaticFiles(
            directory=str(frontend_dir)),
        name="frontend")

css_dir = frontend_dir / "css"
if css_dir.exists():
    app.mount("/css", StaticFiles(directory=str(css_dir)), name="css")

js_dir = frontend_dir / "js"
if js_dir.exists():
    app.mount("/js", StaticFiles(directory=str(js_dir)), name="js")


@app.get("/")
async def root():
    """根路径"""
    return {"message": "YS-API V3.0 最小化版本运行中", "status": "ok"}


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "YS-API V3.0 Production",
        "version": "3.0.0-production",
        "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
    }


@app.get("/field-config.html")
async def field_config_page():
    """字段配置页面"""
    page_path = os.path.join(
        os.path.dirname(__file__), "..", "..", "frontend", "field-config.html"
    )
    if os.path.exists(page_path):
        return FileResponse(page_path)
    else:
        # 如果field-config.html不存在，重定向到field-config-manual.html
        return RedirectResponse(url="/field-config-manual.html")


@app.get("/field-config-manual.html")
async def field_config_manual_page():
    """字段配置手动加载页面"""
    page_path = os.path.join(
        os.path.dirname(__file__),
        "..",
        "..",
        "frontend",
        "field-config-manual.html")
    if os.path.exists(page_path):
        return FileResponse(page_path)
    else:
        raise HTTPException(status_code=404, detail="字段配置手动加载页面未找到")


@app.get("/test-simple.html")
async def test_simple_page():
    """简化测试页面"""
    # 使用绝对路径定位到项目根目录的test-simple.html
    page_path = project_root / "test-simple.html"
    if page_path.exists():
        return FileResponse(str(page_path))
    else:
        # 如果文件不存在，返回调试信息
        return JSONResponse(
            status_code=404,
            content={
                "error": "测试页面未找到",
                "searched_path": str(page_path),
                "project_root": str(project_root),
                "exists": page_path.exists(),
            },
        )


@app.get("/直接测试API.html")
async def direct_api_test_page():
    """直接API测试页面"""
    page_path = project_root / "直接测试API.html"
    if page_path.exists():
        return FileResponse(str(page_path))
    else:
        raise HTTPException(status_code=404, detail="API测试页面未找到")


@app.get("/api-test.html")
async def api_test_page():
    """API测试页面"""
    page_path = project_root / "api-test.html"
    if page_path.exists():
        return FileResponse(str(page_path))
    else:
        # 如果文件不存在，返回调试信息
        return JSONResponse(
            status_code=404,
            content={
                "error": "API测试页面未找到",
                "searched_path": str(page_path),
                "project_root": str(project_root),
                "exists": page_path.exists(),
            },
        )


@app.get("/docs")
async def custom_docs():
    """自定义API文档"""
    return {
        "title": "YS-API V3.0 最小化版本",
        "description": "由于Python 3.13兼容性问题，当前运行最小化版本",
        "endpoints": [
            {"path": "/", "method": "GET", "description": "根路径"},
            {"path": "/health", "method": "GET", "description": "健康检查"},
            {
                "path": "/field-config-manual.html",
                "method": "GET",
                "description": "字段配置页面",
            },
            {
                "path": "/api/v1/field-config/modules",
                "method": "GET",
                "description": "获取模块列表",
            },
        ],
        "note": "完整功能需要解决SQLAlchemy兼容性问题",
    }


# 生产环境API - 连接真实数据源
# 注意：当前使用示例数据，需要根据实际情况连接真实数据源
# 基础字段配置API
@app.get("/api/v1/field-config/modules")
async def get_modules():
    """获取模块列表"""
    try:
        modules = {
            'sales_order': '销售订单',
            'purchase_order': '采购订单',
            'production_order': '生产订单',
            'subcontract_order': '委外订单',
            'applyorder': '请购单',
            'subcontract_requisition': '委外请购',
            'product_receipt': '产品入库单',
            'purchase_receipt': '采购入库',
            'subcontract_receipt': '委外入库',
            'materialout': '材料出库单',
            'sales_out': '销售出库',
            'inventory': '现存量',
            'inventory_report': '现存量报表',
            'requirements_planning': '需求计划',
            'material_master': '物料档案',
        }

        logger.info(f"返回模块列表: {len(modules)} 个模块")

        return {
            "success": True,
            "message": "成功获取模块列表",
            "data": {"modules": modules},
        }
    except Exception:
        logger.info(f"获取模块列表失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取模块列表失败: {str(e)}",
            "data": {"modules": {}},
        }


@app.get("/api/v1/field-config/modules/{module_name}/fields")
async def get_module_fields(module_name: str):
    """获取模块字段 - 模拟数据"""

    # 模拟字段数据
    sample_fields = {
        "id": {
            "api_field_name": "id",
            "sample_value": "12345",
            "chinese_name": "主键ID",
            "data_type": "BIGINT",
        },
        "code": {
            "api_field_name": "code",
            "sample_value": f"{module_name.upper()}001",
            "chinese_name": "编码",
            "data_type": "NVARCHAR(50)",
        },
        "name": {
            "api_field_name": "name",
            "sample_value": "示例名称",
            "chinese_name": "名称",
            "data_type": "NVARCHAR(100)",
        },
        "create_time": {
            "api_field_name": "create_time",
            "sample_value": "2024-01-01 10:00:00",
            "chinese_name": "创建时间",
            "data_type": "DATETIME",
        },
        "status": {
            "api_field_name": "status",
            "sample_value": "active",
            "chinese_name": "状态",
            "data_type": "NVARCHAR(20)",
        },
    }

    return {
        "success": True,
        "message": f"成功获取{module_name}字段配置",
        "data": {
            "module_name": module_name,
            "field_count": len(sample_fields),
            "fields": sample_fields,
        },
    }


@app.post("/api/v1/field-config/baselines/{module_name}")
async def save_baseline_config(module_name: str, request: dict):
    """保存基准文件配置 - 模拟实现"""
    return {
        "success": True,
        "message": "基准文件保存成功 (模拟)",
        "data": {
            "file_path": f"v3/config/baselines/{module_name}_baseline.json",
            "fields_count": len(request.get('api_data', [])),
            "note": "当前为模拟保存，实际文件未创建",
        },
    }


@app.post("/api/v1/field-config/users/{user_id}/{module_name}")
async def save_user_config(user_id: str, module_name: str, request: dict):
    """保存用户配置 - 模拟实现"""
    return {
        "success": True,
        "message": "用户配置保存成功 (模拟)",
        "data": {
            "file_path": f"v3/config/data/user_field_config/{user_id}/{module_name}.json",
            "total_fields": len(
                request.get(
                    'user_config',
                    {}).get(
                    'fields',
                    {})),
            "note": "当前为模拟保存，实际文件未创建",
        },
    }


# 新增的配置保存API路由
@app.get("/api/v1/config/modules/{module_name}/fields")
async def get_config_module_fields(
    module_name: str, user_id: str = "Alice", max_depth: int = 3
):
    """获取配置模块字段 - 兼容前端调用"""
    logger.info(
        f"📡 获取配置字段: module={module_name}, user_id={user_id}, max_depth={max_depth}"
    )

    # 模拟字段数据 - 更丰富的数据
    sample_fields = {
        "id": {
            "api_field_name": "id",
            "sample_value": "12345",
            "chinese_name": "主键ID",
            "data_type": "BIGINT",
            "is_selected": False,
            "locked": False,
            "config_name": "",
        },
        "code": {
            "api_field_name": "code",
            "sample_value": f"{module_name.upper()}001",
            "chinese_name": "编码",
            "data_type": "NVARCHAR(50)",
            "is_selected": True,
            "locked": False,
            "config_name": "订单编码",
        },
        "name": {
            "api_field_name": "name",
            "sample_value": "示例名称",
            "chinese_name": "名称",
            "data_type": "NVARCHAR(100)",
            "is_selected": True,
            "locked": False,
            "config_name": "订单名称",
        },
        "create_time": {
            "api_field_name": "create_time",
            "sample_value": "2024-01-01 10:00:00",
            "chinese_name": "创建时间",
            "data_type": "DATETIME",
            "is_selected": False,
            "locked": False,
            "config_name": "",
        },
        "status": {
            "api_field_name": "status",
            "sample_value": "active",
            "chinese_name": "状态",
            "data_type": "NVARCHAR(20)",
            "is_selected": True,
            "locked": True,
            "config_name": "订单状态",
        },
    }

    return {
        "success": True,
        "message": f"成功获取{module_name}配置字段",
        "data": {
            "module_name": module_name,
            "field_count": len(sample_fields),
            "fields": sample_fields,
        },
    }


@app.put("/api/v1/config/modules/{module_name}/fields")
async def save_config_module_fields(module_name: str, request: dict):
    """保存配置模块字段 - 真实实现"""
    try:
        logger.info(f"💾 保存配置: module={module_name}")
        logger.info(
            f"📦 请求数据: {json.dumps(request, ensure_ascii=False, indent=2)}")

        user_id = request.get('user_id', 'Alice')
        user_config = request.get('user_config', {})

        # 创建配置目录
        config_dir = Path(f"v3/config/data/user_field_config/{user_id}")
        config_dir.mkdir(parents=True, exist_ok=True)

        # 配置文件路径
        config_file = config_dir / f"{module_name}.json"

        # 准备保存的数据
        save_data = {
            "user_id": user_id,
            "module_name": module_name,
            "display_name": user_config.get('display_name', module_name),
            "saved_at": datetime.now().isoformat(),
            "version": "1.0.0",
            "total_fields": user_config.get('total_fields', 0),
            "selected_fields": user_config.get('selected_fields', 0),
            "fields": user_config.get('fields', {}),
        }

        # 保存到文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

        logger.info(f"✅ 配置已保存到: {config_file}")

        return {
            "success": True,
            "message": f"{user_config.get('display_name', module_name)} 配置保存成功！",
            "data": {
                "file_path": str(config_file),
                "file_size": config_file.stat().st_size,
                "total_fields": save_data['total_fields'],
                "selected_fields": save_data['selected_fields'],
                "saved_at": save_data['saved_at'],
            },
        }

    except Exception:
        logger.info(f"❌ 保存配置失败: {e}")
        return {
            "success": False,
            "message": f"保存失败: {str(e)}",
            "error": str(e)}


@app.get("/js-loading-test.html")
async def js_loading_test_page():
    """JavaScript加载测试页面"""
    page_path = project_root / "v3" / "frontend" / "js-loading-test.html"
    if page_path.exists():
        return FileResponse(str(page_path))
    else:
        return JSONResponse(
            status_code=404, content={"error": "JavaScript加载测试页面未找到"}
        )


@app.get("/component-test.html")
async def component_test_page():
    """组件加载测试页面"""
    page_path = project_root / "v3" / "frontend" / "component-test.html"
    if page_path.exists():
        return FileResponse(str(page_path))
    else:
        return JSONResponse(status_code=404, content={"error": "组件测试页面未找到"})


@app.get("/direct-component-test.html")
async def direct_component_test_page():
    """直接组件测试页面"""
    page_path = project_root / "v3" / "frontend" / "direct-component-test.html"
    if page_path.exists():
        return FileResponse(str(page_path))
    else:
        return JSONResponse(
            status_code=404, content={"error": "直接组件测试页面未找到"}
        )


@app.exception_handler(404)
async def not_found_handler(request, exc):
    """404错误处理"""
    return JSONResponse(
        status_code=404, content={
            "detail": "Not found", "path": str(
                request.url.path)})


@app.get("/简单组件测试.html")
async def simple_component_test():
    """简单组件测试页面"""
    page_path = project_root / "简单组件测试.html"
    if page_path.exists():
        return FileResponse(str(page_path))
    else:
        return JSONResponse(
            status_code=404, content={"error": "简单组件测试页面未找到"}
        )


@app.get("/调试组件加载.html")
async def debug_component_loading():
    """调试组件加载页面"""
    page_path = project_root / "调试组件加载.html"
    if page_path.exists():
        return FileResponse(str(page_path))
    else:
        return JSONResponse(
            status_code=404, content={"error": "调试组件加载页面未找到"}
        )


@app.get("/favicon.ico")
async def favicon():
    """处理favicon请求"""

    return Response(status_code=204)


if __name__ == "__main__":

    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

# 设置日志
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
