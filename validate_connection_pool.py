#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 连接池和事务管理器验证
"""

import sys
import os
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

print("YS-API V3.0 连接池和事务管理器验证")
print("=" * 50)

# 测试1: 模块导入
print("1. 测试模块导入...")
try:
    from backend.app.services.database_connection_pool import DatabaseConnectionManager
    print("✅ database_connection_pool 导入成功")
    
    from backend.app.services.transaction_manager import TransactionManager
    print("✅ transaction_manager 导入成功")
    
    print("✅ 所有模块导入成功\n")
except Exception as e:
    print(f"❌ 模块导入失败: {e}\n")
    sys.exit(1)

# 测试2: 连接池基本功能
print("2. 测试连接池基本功能...")
try:
    # 创建连接管理器
    manager = DatabaseConnectionManager()
    print("✅ 连接管理器创建成功")
    
    # 测试获取连接
    with manager.get_connection('sqlite') as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT 1 as test")
        result = cursor.fetchone()
        cursor.close()
        print(f"✅ SQLite连接测试成功: {result}")
    
    # 获取统计信息
    stats = manager.get_pool_stats()
    sqlite_stats = stats.get('sqlite', {})
    print(f"✅ 连接池统计 - 池大小: {sqlite_stats.get('pool_size', 0)}")
    
    # 清理
    manager.close_all()
    print("✅ 连接池关闭成功\n")
    
except Exception as e:
    print(f"❌ 连接池测试失败: {e}\n")
    sys.exit(1)

# 测试3: 事务管理器基本功能
print("3. 测试事务管理器基本功能...")
try:
    # 创建管理器
    conn_manager = DatabaseConnectionManager()
    txn_manager = TransactionManager(conn_manager)
    print("✅ 事务管理器创建成功")
    
    # 创建测试表
    with txn_manager.transaction('sqlite') as conn:
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_txn (
                id INTEGER PRIMARY KEY,
                name TEXT
            )
        """)
        cursor.close()
        print("✅ 测试表创建成功")
    
    # 测试插入数据
    with txn_manager.transaction('sqlite') as conn:
        cursor = conn.cursor()
        cursor.execute("INSERT INTO test_txn (name) VALUES (?)", ("连接池测试",))
        cursor.close()
        print("✅ 数据插入成功")
    
    # 验证数据
    with txn_manager.transaction('sqlite') as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM test_txn")
        count = cursor.fetchone()[0]
        cursor.close()
        print(f"✅ 数据验证成功: {count} 条记录")
    
    # 获取统计信息
    stats = txn_manager.get_transaction_stats()
    print(f"✅ 事务统计 - 总事务: {stats.get('total_transactions', 0)}")
    print(f"✅ 事务统计 - 成功率: {stats.get('success_rate', 0):.1f}%")
    
    # 清理
    txn_manager.close()
    conn_manager.close_all()
    print("✅ 事务管理器关闭成功\n")
    
except Exception as e:
    print(f"❌ 事务管理器测试失败: {e}\n")
    sys.exit(1)

print("🎉 所有测试通过！连接池和事务管理器功能正常")
print("=" * 50)
print("Month 2 连接池管理和事务处理优化 100% 完成！")
