#!/usr/bin/env python3
"""
简化版批量修复Flake8错误脚本
"""

import glob
import subprocess


def get_python_files():
    """获取所有Python文件"""
    python_files = []

    # 使用glob查找所有.py文件
    patterns = [
        "*.py",
        "backend/*.py",
        "frontend/*.py",
        "scripts/*.py",
        "tests/*.py",
        "tools/*.py",
        "backend/**/*.py",
        "frontend/**/*.py",
    ]

    for pattern in patterns:
        files = glob.glob(pattern, recursive=True)
        python_files.extend(files)

    # 去重并排除不需要的文件
    python_files = list(set(python_files))

    # 过滤文件
    exclude_patterns = ["__pycache__", "temp_cleanup", ".git"]
    filtered_files = []

    for file_path in python_files:
        should_exclude = False
        for exclude in exclude_patterns:
            if exclude in file_path:
                should_exclude = True
                break
        if not should_exclude:
            filtered_files.append(file_path)

    return filtered_files


def run_autoflake(file_path):
    """使用autoflake删除未使用的导入"""
    try:
        cmd = [
            "python",
            "-m",
            "autoflake",
            "--remove-all-unused-imports",
            "--remove-unused-variables",
            "--in-place",
            file_path,
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0
    except Exception:
        return False


def run_autopep8(file_path):
    """使用autopep8修复PEP8问题"""
    try:
        cmd = [
            "python",
            "-m",
            "autopep8",
            "--in-place",
            "--aggressive",
            "--aggressive",
            file_path,
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0
    except Exception:
        return False


def run_black(file_path):
    """使用black格式化代码"""
    try:
        cmd = ["python", "-m", "black", "--quiet", file_path]
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0
    except Exception:
        return False


def check_flake8_errors():
    """检查当前的flake8错误数量"""
    try:
        cmd = [
            "python",
            "-m",
            "flake8",
            ".",
            "--exclude=__pycache__,logs,temp_cleanup,.git",
            "--count",
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.stdout.strip():
            return int(result.stdout.strip())
        return 0
    except Exception:
        return -1


def main():
    """主函数"""
    print("🚀 开始批量修复Flake8错误...")

    # 检查初始错误数量
    initial_errors = check_flake8_errors()
    print(f"📊 初始错误数量: {initial_errors}")

    # 获取所有Python文件
    python_files = get_python_files()
    print(f"📁 找到 {len(python_files)} 个Python文件")

    success_count = 0
    failed_files = []

    # 处理每个文件
    for i, file_path in enumerate(python_files, 1):
        print(f"🔧 [{i}/{len(python_files)}] 修复: {file_path}")

        try:
            # 三步修复流程
            step1 = run_autoflake(file_path)
            step2 = run_autopep8(file_path)
            step3 = run_black(file_path)

            if step1 and step2 and step3:
                success_count += 1
                print(f"  ✅ 成功")
            else:
                failed_files.append(file_path)
                print(f"  ⚠️ 部分成功")

        except Exception as e:
            failed_files.append(file_path)
            print(f"  ❌ 失败: {e}")

    # 检查最终错误数量
    final_errors = check_flake8_errors()

    print("\n" + "=" * 50)
    print("📋 修复完成报告:")
    print(f"  📁 处理文件: {len(python_files)} 个")
    print(f"  ✅ 成功修复: {success_count} 个")
    print(f"  ⚠️ 部分成功: {len(failed_files)} 个")
    print(f"  📊 错误减少: {initial_errors} → {final_errors}")

    if final_errors == 0:
        print("🎉 所有Flake8错误已修复！")
    elif final_errors < initial_errors:
        print(f"🎯 大幅改善！减少了 {initial_errors - final_errors} 个错误")

    if failed_files:
        print(f"\n⚠️ 需要手动检查的文件:")
        for file_path in failed_files[:5]:  # 只显示前5个
            print(f"  - {file_path}")
        if len(failed_files) > 5:
            print(f"  ... 还有 {len(failed_files) - 5} 个文件")


if __name__ == "__main__":
    main()
