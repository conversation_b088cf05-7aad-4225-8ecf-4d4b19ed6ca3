# 工程分析报告

## 项目结构概述

当前工程是一个前后端分离的项目，主要包含以下核心模块和功能：

### 1. **前端部分 (`frontend/`)**
   - **功能模块**：
     - 字段配置管理 (`field-config.html`, `field-config-manual.html`)
     - 性能优化 (`performance-optimization-demo.html`)
     - 错误处理 (`test-error-handler.html`)
     - 通知系统 (`test-notification-system.html`)
     - 用户配置保存 (`test-user-config-save.html`)
   - **技术栈**：
     - 使用了 `element-plus.css` 作为 UI 框架。
     - 通过 JavaScript 实现动态交互逻辑。

### 2. **后端部分 (`backend/`)**
   - **核心文件**：
     - `main.py`：主程序入口，负责启动服务和处理业务逻辑。
   - **功能模块**：
     - 提供 API 接口服务。
     - 与前端交互，处理数据请求和响应。

### 3. **配置管理 (`config/`)**
   - **配置文件**：
     - `modules.json`：模块配置。
     - `blacklist_fields.json`：字段黑名单配置。
       - **设计背景**：用于跳过嵌套/重复字段（如 `orderPrices`、`materials`）的存储。
       - **现状**：当前业务逻辑中已通过用户配置字段机制覆盖此功能，黑名单未被实际调用。
       - **建议**：可移除该配置或改为动态加载机制（仅运行时临时过滤非关键字段）。
     - `auto_sync_config.json`：自动同步配置。
   - **基线数据 (`config/baselines/`)**
     - 存储各模块的基线数据，如 `material_master_baseline.json`、`sales_order_baseline.json` 等。

### 4. **模块字段 (`模块字段/`)**
   - **功能**：
     - 存储各业务模块的字段定义（XML 和 JSON 格式）。
     - 例如：采购订单、销售订单、生产订单等模块的字段配置。

### 5. **文档 (`docs/`)**
   - **内容**：
     - 项目概览、系统架构设计、API 接口规范、字段映射规范等。
     - 问题解决记录和代码优化进度文档。

### 6. **工具脚本 (`scripts/`, `tools/`)**
   - **功能**：
     - `rollback_batch_writes.py`：批量回滚脚本。
     - `md_to_json_converter.py`：Markdown 转 JSON 工具。

### 7. **测试模拟代码说明**
   - **文件位置**：`test_baseline_api.py`
   - **核心功能**：
     1. 模拟API响应（成功/失败场景）
     2. 环境切换开关 `USE_MOCK = True/False`
   - **使用场景**：
     - 开发环境：默认启用模拟模式（`USE_MOCK = True`）
     - 生产环境：需设置为 `USE_MOCK = False` 并确保API服务可用
   - **清理检查清单**：
     1. 搜索 `class MockResponse` 定位所有模拟代码
     2. 检查 `if USE_MOCK:` 分支逻辑
     3. 确认生产环境已禁用模拟模式

## 核心逻辑分析

1. **前后端交互**：
   - 前端通过 API 调用后端服务，后端处理业务逻辑并返回数据。
   - 前端负责展示和用户交互，后端负责数据处理和存储。

2. **字段配置**：
   - 通过 XML 和 JSON 文件定义各模块的字段，支持动态加载和配置。

3. **数据同步**：
   - 使用基线数据 (`baselines/`) 进行数据同步和校验。

4. **扩展性**：
   - 模块化设计，支持新模块的快速接入和配置。

### 工程分析报告补充：核心代码逻辑分析

#### 1. **后端核心逻辑 (`backend/app/main.py`)**
   - **功能概述**：
     - 基于 `FastAPI` 框架提供 RESTful API 服务。
     - 支持跨域请求（CORS）。
     - 提供静态文件服务（前端资源）。
     - 核心 API 包括：
       - 模块列表获取 (`/api/v1/field-config/modules`)。
       - 模块字段获取 (`/api/v1/field-config/modules/{module_name}/fields`)。
       - 基准配置保存 (`/api/v1/field-config/baselines/{module_name}`)。
       - 用户配置保存 (`/api/v1/field-config/users/{user_id}/{module_name}`)。
   - **关键逻辑**：
     - 模块字段数据通过模拟数据返回，实际项目中应连接数据库。
     - 用户配置保存逻辑为模拟实现，实际需持久化到文件或数据库。
     - 健康检查接口 (`/health`) 用于服务监控。

#### 2. **前端核心逻辑 (`frontend/field-config.html`)**
   - **功能概述**：
     - 动态加载模块列表，支持模块切换。
     - 字段搜索功能，支持精确匹配和模糊搜索。
     - 字段展示表格，支持分页和统计。
     - 通知系统，用于显示操作反馈。
   - **关键逻辑**：
     - 通过 `axios` 调用后端 API 获取数据。
     - 模块字段映射配置 (`MODULE_FIELD_MAPPING`) 定义各模块的订单号或物料编码字段。
     - 搜索功能优先按订单号/物料编码字段精确匹配，其次模糊匹配其他字段。

#### 3. **配置管理 (`config/modules.json`)**
   - **功能概述**：
     - 定义系统中所有模块的基本信息，包括模块名称、显示名称、API 端点、数据库表名等。
     - 支持模块启用/禁用状态管理。
   - **关键逻辑**：
     - 模块配置为 JSON 格式，便于动态加载和扩展。
     - 每个模块关联到具体的 API 端点和数据库表。

#### 4. **数据流分析**
   - **前后端交互流程**：
     1. 前端加载时，从 `/api/v1/field-config/modules` 获取模块列表。
     2. 用户选择模块后，前端调用 `/api/v1/field-config/modules/{module_name}/fields` 获取字段数据。
     3. 用户配置保存时，前端调用 `/api/v1/field-config/users/{user_id}/{module_name}` 保存配置。

#### 5. **扩展性设计**
   - 模块化设计，新增模块只需在 `modules.json` 中配置即可。
   - 字段配置支持动态加载，无需修改前端代码。

### 后续建议
1. **后端改进**：
   - 连接真实数据库，替换模拟数据逻辑。
   - 增加用户认证和权限控制。
2. **前端改进**：
   - 增加字段编辑和自定义功能。
   - 优化搜索性能，支持分页加载。
3. **配置管理**：
   - 支持动态加载模块配置，避免重启服务。