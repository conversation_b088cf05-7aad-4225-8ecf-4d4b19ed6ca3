<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>b483475dfd65499ab122b773eb9a8061</id>
<name>材料出库列表查询</name>
<apiClassifyId>934dff6b3da94520a34bf8dd2fd29998</apiClassifyId>
<apiClassifyName>材料出库单</apiClassifyName>
<apiClassifyCode/>
<parentApiClassifies/>
<functionId/>
<openMode>0</openMode>
<description>材料出库列表查询</description>
<auth>true</auth>
<bodyPassthrough>false</bodyPassthrough>
<healthExam>true</healthExam>
<healthStatus>true</healthStatus>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/scm/materialout/list</completeProxyUrl>
<connectUrl>/bill/list</connectUrl>
<sort>20</sort>
<handler>openapi</handler>
<httpRequestType>POST</httpRequestType>
<openApi>true</openApi>
<preset>false</preset>
<productId>710a0be3edff4f9092e35f63fd3b9bae</productId>
<productCode>scm</productCode>
<proxyUrl>/yonbip/scm/materialout/list</proxyUrl>
<requestParamsDemo>Url: /yonbip/scm/materialout/list?access_token=访问令牌 Body: { "pageIndex": 0, "pageSize": 0, "open_vouchdate_begin": "", "open_vouchdate_end": "", "bustype_name": "", "stockOrg": [ 0 ], "stockOrg_code": [ "" ], "stockOrg_name": "", "product_cName": [ "" ], "product.productClass.name": [ "" ], "simpleVOs": [ { "field": "", "op": "", "value1": "", "value2": "" } ] }</requestParamsDemo>
<requestProtocol>HTTP</requestProtocol>
<serviceHttpMethod>POST</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2020-01-16 18:45:26</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/materialout/list</address>
<productName>采购供应</productName>
<productClassifyId>yonsuite</productClassifyId>
<productClassifyCode>yonbip</productClassifyCode>
<productClassifyName>用友 YonBIP</productClassifyName>
<paramDTOS>
<paramDTOS>
<id>2081376276070793230</id>
<name>pageIndex</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId>1850748025614368792</defParamId>
<array>false</array>
<paramDesc>页号</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>1</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081376276070793231</id>
<name>pageSize</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId>1850748025614368793</defParamId>
<array>false</array>
<paramDesc>每页行数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>10</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081376276070793232</id>
<name>open_vouchdate_begin</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId>1850748025614368794</defParamId>
<array>false</array>
<paramDesc>单据开始时间</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081376276070793233</id>
<name>open_vouchdate_end</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId>1850748025614368795</defParamId>
<array>false</array>
<paramDesc>单据结束时间</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081376276070793234</id>
<name>bustype_name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId>1850748025614368796</defParamId>
<array>false</array>
<paramDesc>交易类型</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081376276070793235</id>
<name>stockOrg</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId>1850748025614368797</defParamId>
<array>true</array>
<paramDesc>库存组织id</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081376276070793236</id>
<name>stockOrg_code</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId>1850748025614368798</defParamId>
<array>true</array>
<paramDesc>库存组织编码</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081376276070793237</id>
<name>stockOrg_name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId>1850748025614368799</defParamId>
<array>false</array>
<paramDesc>库存组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081376276070793238</id>
<name>product_cName</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId>1850748025614368800</defParamId>
<array>true</array>
<paramDesc>物料ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081376276070793239</id>
<name>product.productClass.name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId>1850748025614368801</defParamId>
<array>true</array>
<paramDesc>物料分类ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081376276070793225</id>
<name>simpleVOs</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<children>
<children>
<id>2081376276070793226</id>
<name>field</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793225</parentId>
<defParamId>1850748025614368803</defParamId>
<array>false</array>
<paramDesc>属性名(条件),子表加前缀[materOuts.];materOuts.upcoded为来源单据号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2081376276070793227</id>
<name>op</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793225</parentId>
<defParamId>1850748025614368804</defParamId>
<array>false</array>
<paramDesc>条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2081376276070793228</id>
<name>value1</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793225</parentId>
<defParamId>1850748025614368805</defParamId>
<array>false</array>
<paramDesc>值1(条件),单条件时仅使用这个配置</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2081376276070793229</id>
<name>value2</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793225</parentId>
<defParamId>1850748025614368806</defParamId>
<array>false</array>
<paramDesc>值2(条件),单条件时此配置无效</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>1850748025614368802</defParamId>
<array>true</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>2081376276070793245</id>
<name>pageIndex</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>页号</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageIndex</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081376276070793246</id>
<name>pageSize</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>每页行数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageSize</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081376276070793247</id>
<name>open_vouchdate_begin</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据开始时间</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_vouchdate_begin</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081376276070793248</id>
<name>open_vouchdate_end</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据结束时间</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_vouchdate_end</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081376276070793249</id>
<name>bustype_name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>交易类型</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>bustype_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081376276070793250</id>
<name>stockOrg</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>库存组织id</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>stockOrg</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081376276070793251</id>
<name>stockOrg_code</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>库存组织编码</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>stockOrg_code</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081376276070793252</id>
<name>stockOrg_name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>库存组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>stockOrg_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081376276070793253</id>
<name>product_cName</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product_cName</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081376276070793254</id>
<name>product.productClass.name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料分类ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product.productClass.name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081376276070793240</id>
<name>simpleVOs</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<children>
<children>
<id>2081376276070793241</id>
<name>field</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793240</parentId>
<defParamId/>
<array>false</array>
<paramDesc>属性名(条件),子表加前缀[materOuts.];materOuts.upcoded为来源单据号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376276070793242</id>
<name>op</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793240</parentId>
<defParamId/>
<array>false</array>
<paramDesc>条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>op</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376276070793243</id>
<name>value1</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793240</parentId>
<defParamId/>
<array>false</array>
<paramDesc>值1(条件),单条件时仅使用这个配置</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value1</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376276070793244</id>
<name>value2</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793240</parentId>
<defParamId/>
<array>false</array>
<paramDesc>值2(条件),单条件时此配置无效</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value2</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>simpleVOs</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>2081376284660727961</id>
<name>code</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId>1850748025614368822</defParamId>
<array>false</array>
<paramDesc>返回码，调用成功时返回200</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2081376284660727962</id>
<name>message</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<defParamId>1850748025614368823</defParamId>
<array>false</array>
<paramDesc>调用失败时的错误信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2081376276070793255</id>
<name>data</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId/>
<children>
<children>
<id>2081376276070793256</id>
<name>sumRecordList</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793255</parentId>
<children>
<children>
<id>2081376276070793257</id>
<name>totalPieces</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793256</parentId>
<defParamId>1850748025614368826</defParamId>
<array>false</array>
<paramDesc>合计价格</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376276070793258</id>
<name>totalQuantity</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793256</parentId>
<defParamId>1850748025614368827</defParamId>
<array>false</array>
<paramDesc>合计数量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376276070793259</id>
<name>subQty</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793256</parentId>
<defParamId>1850748025614368828</defParamId>
<array>false</array>
<paramDesc>件数</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376276070793260</id>
<name>qty</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793256</parentId>
<defParamId>1850748025614368829</defParamId>
<array>false</array>
<paramDesc>数量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1850748025614368825</defParamId>
<array>true</array>
<paramDesc>sum合计信息</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727954</id>
<name>pageIndex</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793255</parentId>
<defParamId>1850748025614368830</defParamId>
<array>false</array>
<paramDesc>页码</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727955</id>
<name>pageSize</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793255</parentId>
<defParamId>1850748025614368831</defParamId>
<array>false</array>
<paramDesc>每页条数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727956</id>
<name>pageCount</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793255</parentId>
<defParamId>1850748025614368832</defParamId>
<array>false</array>
<paramDesc>总页数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727957</id>
<name>beginPageIndex</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793255</parentId>
<defParamId>1850748025614368833</defParamId>
<array>false</array>
<paramDesc>开始页码</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727958</id>
<name>endPageIndex</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793255</parentId>
<defParamId>1850748025614368834</defParamId>
<array>false</array>
<paramDesc>结束页码</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727959</id>
<name>recordCount</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793255</parentId>
<defParamId>1850748025614368835</defParamId>
<array>false</array>
<paramDesc>总条数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727960</id>
<name>pubts</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793255</parentId>
<defParamId>1850748025614368836</defParamId>
<array>false</array>
<paramDesc>时间戳,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376276070793261</id>
<name>recordList</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793255</parentId>
<children>
<children>
<id>2081376284660727880</id>
<name>currency</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368838</defParamId>
<array>false</array>
<paramDesc>币种id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727881</id>
<name>materOuts_product</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368839</defParamId>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727882</id>
<name>materOuts_unit</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368840</defParamId>
<array>false</array>
<paramDesc>主计量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727883</id>
<name>materOuts_productsku</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368841</defParamId>
<array>false</array>
<paramDesc>物料sku</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727884</id>
<name>vouchdate</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368842</defParamId>
<array>false</array>
<paramDesc>单据日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727885</id>
<name>code</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368843</defParamId>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727886</id>
<name>org</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368844</defParamId>
<array>false</array>
<paramDesc>库存组织IDid</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727887</id>
<name>org_code</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368845</defParamId>
<array>false</array>
<paramDesc>库存组织编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727888</id>
<name>org_name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368846</defParamId>
<array>false</array>
<paramDesc>库存组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"org.name","cItemName":"org_name","cCaption":"库存组织","cShowCaption":"库存组织","iMaxLength":255,"bHidden":false,"cRefType":"aa_orgtree","cRefId":null,"cRefRetId":{"org":"id"},"cDataRule":"\"<%u8c-config.option.singleOrg%>\"==\"false\"","iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOut","cControlType":"Refer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"true"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727889</id>
<name>store</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368847</defParamId>
<array>false</array>
<paramDesc>门店id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727890</id>
<name>bustype</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368848</defParamId>
<array>false</array>
<paramDesc>业务类型id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727891</id>
<name>bustype_name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368849</defParamId>
<array>false</array>
<paramDesc>交易类型名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"bustype.name","cItemName":"bustype_name","cCaption":"交易类型","cShowCaption":"交易类型","iMaxLength":255,"bHidden":false,"cRefType":"aa_user","cRefId":null,"cRefRetId":null,"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOut","cControlType":"Column","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727892</id>
<name>store_name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368850</defParamId>
<array>false</array>
<paramDesc>门店名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"store.name","cItemName":"store_name","cCaption":"门店","cShowCaption":"门店","iMaxLength":255,"bHidden":false,"cRefType":"aa_department","cRefId":null,"cRefRetId":null,"cDataRule":"\"<%productcenter.option.isOpenURetail%>\"==\"true\"","iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":false,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOut","cControlType":"Column","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727893</id>
<name>department_name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368851</defParamId>
<array>false</array>
<paramDesc>部门名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"department.name","cItemName":"department_name","cCaption":"部门","cShowCaption":"部门","iMaxLength":255,"bHidden":false,"cRefType":"ucf-org-center.bd_adminorgsharetreeref","cRefId":null,"cRefRetId":{"department":"id"},"cDataRule":null,"iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOut","cControlType":"TreeRefer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727894</id>
<name>department</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368852</defParamId>
<array>false</array>
<paramDesc>部门id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727895</id>
<name>warehouse</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368853</defParamId>
<array>false</array>
<paramDesc>仓库id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727896</id>
<name>warehouse_name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368854</defParamId>
<array>false</array>
<paramDesc>仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"warehouse.name","cItemName":"warehouse_name","cCaption":"仓库","cShowCaption":"仓库","iMaxLength":255,"bHidden":false,"cRefType":"aa_warehouse","cRefId":null,"cRefRetId":null,"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOut","cControlType":"Column","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727897</id>
<name>stockMgr_name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368855</defParamId>
<array>false</array>
<paramDesc>库管员名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"stockMgr.name","cItemName":"stockMgr_name","cCaption":"库管员","cShowCaption":"库管员","iMaxLength":255,"bHidden":false,"cRefType":"ucf-staff-center.bd_staff_outer_ref","cRefId":null,"cRefRetId":{"stockMgr":"id"},"cDataRule":null,"iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOut","cControlType":"Refer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727898</id>
<name>stockMgr</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368856</defParamId>
<array>false</array>
<paramDesc>库管员IDid</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727899</id>
<name>memo</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368857</defParamId>
<array>false</array>
<paramDesc>备注</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727900</id>
<name>bustype_extend_attrs_json</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368858</defParamId>
<array>false</array>
<paramDesc>出库类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727901</id>
<name>accountOrg_name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368859</defParamId>
<array>false</array>
<paramDesc>会计主体名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"accountOrg.name","cItemName":"accountOrg_name","cCaption":"会计主体","cShowCaption":"会计主体","iMaxLength":255,"bHidden":false,"cRefType":"aa_orgtree","cRefId":null,"cRefRetId":{"accountOrg":"id"},"cDataRule":"\"<%u8c-config.option.singleOrg%>\"==\"false\"","iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOut","cControlType":"Refer","refReturn":"name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727902</id>
<name>accountOrg</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368860</defParamId>
<array>false</array>
<paramDesc>会计主体id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727903</id>
<name>totalPieces</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368861</defParamId>
<array>false</array>
<paramDesc>整单件数</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727904</id>
<name>exchangestatus</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368862</defParamId>
<array>false</array>
<paramDesc>交换状态</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727905</id>
<name>status</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368863</defParamId>
<array>false</array>
<paramDesc>单据状态, 0:未提交、1:已提交、</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727906</id>
<name>totalQuantity</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368864</defParamId>
<array>false</array>
<paramDesc>整单数量</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727907</id>
<name>srcbill</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368865</defParamId>
<array>false</array>
<paramDesc>来源单据id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727908</id>
<name>creator</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368866</defParamId>
<array>false</array>
<paramDesc>创建人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727909</id>
<name>srcbillno</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368867</defParamId>
<array>false</array>
<paramDesc>来源单据</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727910</id>
<name>srcBillType</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368868</defParamId>
<array>false</array>
<paramDesc>来源上级单据类型, productionorder.po_production_order_ustock:生产订单材料、upu.st_purchaseorder:采购订单、upu.pu_arrivalorder:到货订单、productionorder.po_production_order:生产订单产品、st_storeprorecord:产品入库单、st_storecheckplan:盘点倒冲、po_production_order:生产订单、2:计划订单、3:销售订单、</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727911</id>
<name>createTime</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368869</defParamId>
<array>false</array>
<paramDesc>创建时间,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727912</id>
<name>modifier</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368870</defParamId>
<array>false</array>
<paramDesc>修改人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727913</id>
<name>modifyTime</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368871</defParamId>
<array>false</array>
<paramDesc>修改时间,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727914</id>
<name>auditor</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368872</defParamId>
<array>false</array>
<paramDesc>提交人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727915</id>
<name>auditTime</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368873</defParamId>
<array>false</array>
<paramDesc>提交时间,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727916</id>
<name>id</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368874</defParamId>
<array>false</array>
<paramDesc>主表id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727917</id>
<name>pubts</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368875</defParamId>
<array>false</array>
<paramDesc>时间戳,格式为:yyyy-MM-dd HH:mm:ss</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727918</id>
<name>tplid</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368876</defParamId>
<array>false</array>
<paramDesc>模板id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376276070793262</id>
<name>headItem</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<children>
<children>
<id>2081376276070793263</id>
<name>id</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793262</parentId>
<defParamId>1850748025614368878</defParamId>
<array>false</array>
<paramDesc>表头自定义项id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376276070793264</id>
<name>define1</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793262</parentId>
<defParamId>1850748025614368879</defParamId>
<array>false</array>
<paramDesc>表头自定义项1</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376276070793265</id>
<name>define2</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793262</parentId>
<defParamId>1850748025614368880</defParamId>
<array>false</array>
<paramDesc>表头自定义项2</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376276070793266</id>
<name>define3</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793262</parentId>
<defParamId>1850748025614368881</defParamId>
<array>false</array>
<paramDesc>表头自定义项3</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376276070793267</id>
<name>define4</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793262</parentId>
<defParamId>1850748025614368882</defParamId>
<array>false</array>
<paramDesc>表头自定义项4</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1850748025614368877</defParamId>
<array>false</array>
<paramDesc>以下字段名需要拼接headItem!</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible/>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376276070793268</id>
<name>materOuts</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<children>
<children>
<id>2081376276070793269</id>
<name>id</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793268</parentId>
<defParamId>1850748025614368884</defParamId>
<array>false</array>
<paramDesc>子表id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1850748025614368883</defParamId>
<array>false</array>
<paramDesc>以下字段名需要拼接materOuts!</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727919</id>
<name>product_cCode</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368885</defParamId>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"materOuts.product.cCode","cItemName":"product_cCode","cCaption":"物料编码","cShowCaption":"物料编码","iMaxLength":255,"bHidden":false,"cRefType":"aa_productsku","cRefId":null,"cRefRetId":{"product":"id","product_cName":"product_cName","productsku":"productskus_id","productsku_cCode":"productskus_cCode","unit":"oUnitId","unit_name":"unitName","product_unitName":"unitName","isBatchManage":"productOfflineRetail_isBatchManage","isExpiryDateManage":"productOfflineRetail_isExpiryDateManage","isSerialNoManage":"productOfflineRetail_isSerialNoManage","free@1@@10":"retailskus!free@1@@10","skudefine@1@@60":"productSkuProps!define@1@@60","prodefine@1@@30":"productProps!define@1@@30","propertiesValue":"propertiesValue","stockUnitId":"stockUnit","stockUnit_name":"stockUnit_name","product_cCode":"cCode","productsku_cName":"cName","invExchRate":"stockRate","expireDateNo":"productOfflineRetail_expireDateNo","expireDateUnit":"productOfflineRetail_expireDateUnit","unit_Precision":"unitPrecision"},"cDataRule":null,"iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOuts","cControlType":"refer","refReturn":"cCode","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727920</id>
<name>product_cName</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368886</defParamId>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"materOuts.product.cName","cItemName":"product_cName","cCaption":"物料名称","cShowCaption":"物料名称","iMaxLength":255,"bHidden":false,"cRefType":"aa_productsku","cRefId":null,"cRefRetId":{"product":"id","product_cName":"product_cName","productsku":"productskus_id","productsku_cCode":"productskus_cCode","unit":"oUnitId","unit_name":"unitName","product_unitName":"unitName","isBatchManage":"productOfflineRetail_isBatchManage","isExpiryDateManage":"productOfflineRetail_isExpiryDateManage","isSerialNoManage":"productOfflineRetail_isSerialNoManage","free@1@@10":"retailskus!free@1@@10","skudefine@1@@60":"productSkuProps!define@1@@60","prodefine@1@@30":"productProps!define@1@@30","propertiesValue":"propertiesValue","stockUnitId":"stockUnit","stockUnit_name":"stockUnit_name","product_cCode":"cCode","productsku_cName":"cName","invExchRate":"stockRate","expireDateNo":"productOfflineRetail_expireDateNo","expireDateUnit":"productOfflineRetail_expireDateUnit","unit_Precision":"unitPrecision"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":true,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOuts","cControlType":"refer","refReturn":"cName","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727921</id>
<name>productsku_cCode</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368887</defParamId>
<array>false</array>
<paramDesc>sku编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"materOuts.productsku.cCode","cItemName":"productsku_cCode","cCaption":"sku编码","cShowCaption":"sku编码","iMaxLength":255,"bHidden":false,"cRefType":"aa_productsku","cRefId":null,"cRefRetId":{"product":"id","product_cName":"product_cName","productsku":"productskus_id","productsku_cCode":"productskus_cCode","unit":"oUnitId","unit_name":"unitName","product_unitName":"unitName","isBatchManage":"productOfflineRetail_isBatchManage","isExpiryDateManage":"productOfflineRetail_isExpiryDateManage","isSerialNoManage":"productOfflineRetail_isSerialNoManage","free@1@@10":"retailskus!free@1@@10","skudefine@1@@60":"productSkuProps!define@1@@60","prodefine@1@@30":"productProps!define@1@@30","propertiesValue":"propertiesValue","stockUnitId":"stockUnit","stockUnit_name":"stockUnit_name","product_cCode":"cCode","productsku_cName":"cName","invExchRate":"stockRate","expireDateNo":"productOfflineRetail_expireDateNo","expireDateUnit":"productOfflineRetail_expireDateUnit","unit_Precision":"unitPrecision"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":false,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOuts","cControlType":"refer","refReturn":"productskus_cCode","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727922</id>
<name>productsku_cName</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368888</defParamId>
<array>false</array>
<paramDesc>sku名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"materOuts.productsku.skuName","cItemName":"productsku_cName","cCaption":"sku名称","cShowCaption":"sku名称","iMaxLength":255,"bHidden":false,"cRefType":"aa_productsku","cRefId":null,"cRefRetId":{"product":"id","product_cName":"product_cName","productsku":"productskus_id","productsku_cCode":"productskus_cCode","unit":"oUnitId","unit_name":"unitName","product_unitName":"unitName","isBatchManage":"productOfflineRetail_isBatchManage","isExpiryDateManage":"productOfflineRetail_isExpiryDateManage","isSerialNoManage":"productOfflineRetail_isSerialNoManage","free@1@@10":"retailskus!free@1@@10","skudefine@1@@60":"productSkuProps!define@1@@60","prodefine@1@@30":"productProps!define@1@@30","propertiesValue":"propertiesValue","stockUnitId":"stockUnit","stockUnit_name":"stockUnit_name","product_cCode":"cCode","productsku_cName":"cName","invExchRate":"stockRate","expireDateNo":"productOfflineRetail_expireDateNo","expireDateUnit":"productOfflineRetail_expireDateUnit","unit_Precision":"unitPrecision"},"cDataRule":null,"iNumPoint":null,"bCanModify":false,"iMaxShowLen":255,"bShowIt":false,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOuts","cControlType":"refer","refReturn":"cName","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"false"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727923</id>
<name>productClass_code</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368889</defParamId>
<array>false</array>
<paramDesc>物料分类编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727924</id>
<name>propertiesValue</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368890</defParamId>
<array>false</array>
<paramDesc>规格</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727925</id>
<name>batchno</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368891</defParamId>
<array>false</array>
<paramDesc>批次号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"materOuts.batchno","cItemName":"batchno","cCaption":"批次号","cShowCaption":"批次号","iMaxLength":255,"bHidden":false,"cRefType":"st_batchnoref","cRefId":null,"cRefRetId":{"batchno":"batchno","producedate":"producedate","invaliddate":"invaliddate","define@1@@30":"define@1@@30"},"cDataRule":null,"iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":false,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOuts","cControlType":"refer","refReturn":"batchno","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"true"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727926</id>
<name>invaliddate</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368892</defParamId>
<array>false</array>
<paramDesc>有效期至</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727927</id>
<name>qty</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368893</defParamId>
<array>false</array>
<paramDesc>数量</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727928</id>
<name>product_unitName</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368894</defParamId>
<array>false</array>
<paramDesc>计量单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727929</id>
<name>subQty</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368895</defParamId>
<array>false</array>
<paramDesc>件数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727930</id>
<name>stockUnitId</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368896</defParamId>
<array>false</array>
<paramDesc>库存单位id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727931</id>
<name>stockUnit_name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368897</defParamId>
<array>false</array>
<paramDesc>库存单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"materOuts.stockUnitId.name","cItemName":"stockUnit_name","cCaption":"库存单位","cShowCaption":"库存单位","iMaxLength":255,"bHidden":false,"cRefType":"productcenter.pc_productassitunitsref","cRefId":null,"cRefRetId":{"stockUnitId":"assistUnit","stockUnit_name":"assistUnit_Name","invExchRate":"mainUnitCount","unitExchangeType":"unitExchangeType","stockUnitId_Precision":"assistUnit_Precision"},"cDataRule":null,"iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":false,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOuts","cControlType":"refer","refReturn":"assistUnit_Name","dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"true"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727932</id>
<name>project_code</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368898</defParamId>
<array>false</array>
<paramDesc>项目编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727933</id>
<name>project_name</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368899</defParamId>
<array>false</array>
<paramDesc>项目名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{"cFieldName":"materOuts.project.name","cItemName":"project_name","cCaption":"项目名称","cShowCaption":"项目名称","iMaxLength":255,"bHidden":false,"cRefType":"ucfbasedoc.bd_outer_projectcardMCref","cRefId":null,"cRefRetId":{"project":"id","project_name":"name","project_code":"code"},"cDataRule":null,"iNumPoint":null,"bCanModify":true,"iMaxShowLen":255,"bShowIt":false,"bIsNull":true,"bSelfDefine":false,"cSelfDefineType":null,"cOrder":null,"cDataSourceName":"st.materialout.MaterialOuts","cControlType":"Refer","refReturn":null,"dataType":null,"bEnum":"false","cEnumString":null,"enumArray":null,"bMustSelect":"true"}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727934</id>
<name>natUnitPrice</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368900</defParamId>
<array>false</array>
<paramDesc>单价</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727935</id>
<name>natMoney</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368901</defParamId>
<array>false</array>
<paramDesc>金额</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727815</id>
<name>bodyItem</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<children>
<children>
<id>2081376284660727816</id>
<name>id</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368903</defParamId>
<array>false</array>
<paramDesc>表体自定义项id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727817</id>
<name>define1</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368904</defParamId>
<array>false</array>
<paramDesc>表体自定义项1</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727818</id>
<name>define2</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368905</defParamId>
<array>false</array>
<paramDesc>表体自定义项2</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727819</id>
<name>define3</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368906</defParamId>
<array>false</array>
<paramDesc>表体自定义项3</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727820</id>
<name>define4</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368907</defParamId>
<array>false</array>
<paramDesc>表体自定义项4</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727821</id>
<name>define5</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368908</defParamId>
<array>false</array>
<paramDesc>表体自定义项5</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727822</id>
<name>define6</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368909</defParamId>
<array>false</array>
<paramDesc>表体自定义项6</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727823</id>
<name>define7</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368910</defParamId>
<array>false</array>
<paramDesc>表体自定义项7</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727824</id>
<name>define8</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368911</defParamId>
<array>false</array>
<paramDesc>表体自定义项8</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727825</id>
<name>define9</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368912</defParamId>
<array>false</array>
<paramDesc>表体自定义项9</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727826</id>
<name>define10</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368913</defParamId>
<array>false</array>
<paramDesc>表体自定义项10</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727827</id>
<name>define11</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368914</defParamId>
<array>false</array>
<paramDesc>表体自定义项11</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727828</id>
<name>define12</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368915</defParamId>
<array>false</array>
<paramDesc>表体自定义项12</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727829</id>
<name>define13</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368916</defParamId>
<array>false</array>
<paramDesc>表体自定义项13</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727830</id>
<name>define14</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368917</defParamId>
<array>false</array>
<paramDesc>表体自定义项14</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727831</id>
<name>define15</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368918</defParamId>
<array>false</array>
<paramDesc>表体自定义项15</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727832</id>
<name>define16</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368919</defParamId>
<array>false</array>
<paramDesc>表体自定义项16</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727833</id>
<name>define17</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368920</defParamId>
<array>false</array>
<paramDesc>表体自定义项17</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727834</id>
<name>define18</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368921</defParamId>
<array>false</array>
<paramDesc>表体自定义项18</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727835</id>
<name>define19</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368922</defParamId>
<array>false</array>
<paramDesc>表体自定义项19</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727836</id>
<name>define20</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368923</defParamId>
<array>false</array>
<paramDesc>表体自定义项20</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727837</id>
<name>define21</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368924</defParamId>
<array>false</array>
<paramDesc>表体自定义项21</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727838</id>
<name>define22</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368925</defParamId>
<array>false</array>
<paramDesc>表体自定义项22</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727839</id>
<name>define23</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368926</defParamId>
<array>false</array>
<paramDesc>表体自定义项23</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727840</id>
<name>define24</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368927</defParamId>
<array>false</array>
<paramDesc>表体自定义项24</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727841</id>
<name>define25</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368928</defParamId>
<array>false</array>
<paramDesc>表体自定义项25</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727842</id>
<name>define26</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368929</defParamId>
<array>false</array>
<paramDesc>表体自定义项26</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727843</id>
<name>define27</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368930</defParamId>
<array>false</array>
<paramDesc>表体自定义项27</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727844</id>
<name>define28</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368931</defParamId>
<array>false</array>
<paramDesc>表体自定义项28</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727845</id>
<name>define29</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368932</defParamId>
<array>false</array>
<paramDesc>表体自定义项29</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727846</id>
<name>define30</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368933</defParamId>
<array>false</array>
<paramDesc>表体自定义项30</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727847</id>
<name>define31</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368934</defParamId>
<array>false</array>
<paramDesc>表体自定义项31</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727848</id>
<name>define32</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368935</defParamId>
<array>false</array>
<paramDesc>表体自定义项32</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727849</id>
<name>define33</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368936</defParamId>
<array>false</array>
<paramDesc>表体自定义项33</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727850</id>
<name>define34</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368937</defParamId>
<array>false</array>
<paramDesc>表体自定义项34</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727851</id>
<name>define35</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368938</defParamId>
<array>false</array>
<paramDesc>表体自定义项35</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727852</id>
<name>define36</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368939</defParamId>
<array>false</array>
<paramDesc>表体自定义项36</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727853</id>
<name>define37</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368940</defParamId>
<array>false</array>
<paramDesc>表体自定义项37</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727854</id>
<name>define38</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368941</defParamId>
<array>false</array>
<paramDesc>表体自定义项38</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727855</id>
<name>define39</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368942</defParamId>
<array>false</array>
<paramDesc>表体自定义项39</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727856</id>
<name>define40</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368943</defParamId>
<array>false</array>
<paramDesc>表体自定义项40</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727857</id>
<name>define41</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368944</defParamId>
<array>false</array>
<paramDesc>表体自定义项41</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727858</id>
<name>define42</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368945</defParamId>
<array>false</array>
<paramDesc>表体自定义项42</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727859</id>
<name>define43</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368946</defParamId>
<array>false</array>
<paramDesc>表体自定义项43</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727860</id>
<name>define44</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368947</defParamId>
<array>false</array>
<paramDesc>表体自定义项44</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727861</id>
<name>define45</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368948</defParamId>
<array>false</array>
<paramDesc>表体自定义项45</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727862</id>
<name>define46</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368949</defParamId>
<array>false</array>
<paramDesc>表体自定义项46</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727863</id>
<name>define47</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368950</defParamId>
<array>false</array>
<paramDesc>表体自定义项47</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727864</id>
<name>define48</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368951</defParamId>
<array>false</array>
<paramDesc>表体自定义项48</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727865</id>
<name>define49</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368952</defParamId>
<array>false</array>
<paramDesc>表体自定义项49</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727866</id>
<name>define50</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368953</defParamId>
<array>false</array>
<paramDesc>表体自定义项50</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727867</id>
<name>define51</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368954</defParamId>
<array>false</array>
<paramDesc>表体自定义项51</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727868</id>
<name>define52</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368955</defParamId>
<array>false</array>
<paramDesc>表体自定义项52</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727869</id>
<name>define53</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368956</defParamId>
<array>false</array>
<paramDesc>表体自定义项53</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727870</id>
<name>define54</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368957</defParamId>
<array>false</array>
<paramDesc>表体自定义项54</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727871</id>
<name>define55</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368958</defParamId>
<array>false</array>
<paramDesc>表体自定义项55</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727872</id>
<name>define56</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368959</defParamId>
<array>false</array>
<paramDesc>表体自定义项56</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727873</id>
<name>define57</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368960</defParamId>
<array>false</array>
<paramDesc>表体自定义项57</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727874</id>
<name>define58</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368961</defParamId>
<array>false</array>
<paramDesc>表体自定义项58</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727875</id>
<name>define59</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368962</defParamId>
<array>false</array>
<paramDesc>表体自定义项59</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727876</id>
<name>define60</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727815</parentId>
<defParamId>1850748025614368963</defParamId>
<array>false</array>
<paramDesc>表体自定义项60</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1850748025614368902</defParamId>
<array>false</array>
<paramDesc>以下字段名需要拼接bodyItem!</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727936</id>
<name>natCurrency_priceDigit</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368964</defParamId>
<array>false</array>
<paramDesc>币种单价精度</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727937</id>
<name>natCurrency_moneyDigit</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368965</defParamId>
<array>false</array>
<paramDesc>币种金额精度</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727938</id>
<name>unit_code</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368966</defParamId>
<array>false</array>
<paramDesc>主计量编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727939</id>
<name>unit_Precision</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368967</defParamId>
<array>false</array>
<paramDesc>主计量精度</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>62</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727940</id>
<name>stockUnitId_Precision</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368968</defParamId>
<array>false</array>
<paramDesc>库存单位精度</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>63</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727944</id>
<name>isWip</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368972</defParamId>
<array>false</array>
<paramDesc>是否在制品</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>67</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727945</id>
<name>costAccountingMethod</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1850748025614368973</defParamId>
<array>false</array>
<paramDesc>委外成本核算方式：0 按委外入库核算成本，1 按委外订单核算成本</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>68</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727877</id>
<name>bodyParallel</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<children>
<children>
<id>2081376284660727878</id>
<name>wipOpSn</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727877</parentId>
<defParamId>1850748025614368975</defParamId>
<array>false</array>
<paramDesc>在制品工序顺序号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727879</id>
<name>wipOperationId</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376284660727877</parentId>
<defParamId>1850748025614368976</defParamId>
<array>false</array>
<paramDesc>在制品工序ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1850748025614368974</defParamId>
<array>false</array>
<paramDesc>材料出库子表平行表（st.materialout.MaterialOutsParallel）</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>69</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727946</id>
<name>odyParallel_wipOperationCode</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1855977363037224962</defParamId>
<array>false</array>
<paramDesc>在制品工序编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>70</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727947</id>
<name>bodyParallel_wipOperationName</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1855977363037224963</defParamId>
<array>false</array>
<paramDesc>在制品工序名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>71</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727948</id>
<name>out_sys_id</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1856807545865240582</defParamId>
<array>false</array>
<paramDesc>外部来源线索</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>72</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727949</id>
<name>out_sys_code</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1856807545865240583</defParamId>
<array>false</array>
<paramDesc>外部来源编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>73</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727950</id>
<name>out_sys_version</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1856807545865240584</defParamId>
<array>false</array>
<paramDesc>外部来源版本</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>74</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727951</id>
<name>out_sys_type</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1856807545865240585</defParamId>
<array>false</array>
<paramDesc>外部来源类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>75</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727952</id>
<name>out_sys_rowno</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1856807545865240586</defParamId>
<array>false</array>
<paramDesc>外部来源行号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>76</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081376284660727953</id>
<name>out_sys_lineid</name>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<parentId>2081376276070793261</parentId>
<defParamId>1856807545865240587</defParamId>
<array>false</array>
<paramDesc>外部来源行</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>77</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1850748025614368837</defParamId>
<array>true</array>
<paramDesc>返回结果对象</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1850748025614368824</defParamId>
<array>false</array>
<paramDesc>调用成功时的返回数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:41:29.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:29.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2081376284660727967</id>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<content>{ "code": "", "message": "", "data": { "sumRecordList": [ { "totalPieces": "", "totalQuantity": "", "subQty": "", "qty": "" } ], "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "pubts": "", "recordList": [ { "currency": "", "materOuts_product": "", "materOuts_unit": "", "materOuts_productsku": "", "vouchdate": "", "code": "", "org": "", "org_code": "", "org_name": "", "store": "", "bustype": "", "bustype_name": "", "store_name": "", "department_name": "", "department": "", "warehouse": "", "warehouse_name": "", "stockMgr_name": "", "stockMgr": "", "memo": "", "bustype_extend_attrs_json": "", "accountOrg_name": "", "accountOrg": "", "totalPieces": "", "exchangestatus": "", "status": "", "totalQuantity": 0, "srcbill": "", "creator": "", "srcbillno": "", "srcBillType": "", "createTime": "", "modifier": "", "modifyTime": "", "auditor": "", "auditTime": "", "id": "", "pubts": "", "tplid": "", "headItem": { "id": "", "define1": "", "define2": "", "define3": "", "define4": "" }, "materOuts": { "id": "" }, "product_cCode": "", "product_cName": "", "productsku_cCode": "", "productsku_cName": "", "productClass_code": "", "propertiesValue": "", "batchno": "", "invaliddate": "", "qty": 0, "product_unitName": "", "subQty": 0, "stockUnitId": "", "stockUnit_name": "", "project_code": "", "project_name": "", "natUnitPrice": 0, "natMoney": 0, "bodyItem": { "id": "", "define1": "", "define2": "", "define3": "", "define4": "", "define5": "", "define6": "", "define7": "", "define8": "", "define9": "", "define10": "", "define11": "", "define12": "", "define13": "", "define14": "", "define15": "", "define16": "", "define17": "", "define18": "", "define19": "", "define20": "", "define21": "", "define22": "", "define23": "", "define24": "", "define25": "", "define26": "", "define27": "", "define28": "", "define29": "", "define30": "", "define31": "", "define32": "", "define33": "", "define34": "", "define35": "", "define36": "", "define37": "", "define38": "", "define39": "", "define40": "", "define41": "", "define42": "", "define43": "", "define44": "", "define45": "", "define46": "", "define47": "", "define48": "", "define49": "", "define50": "", "define51": "", "define52": "", "define53": "", "define54": "", "define55": "", "define56": "", "define57": "", "define58": "", "define59": "", "define60": "" }, "natCurrency_priceDigit": "", "natCurrency_moneyDigit": "", "unit_code": "", "unit_Precision": "", "stockUnitId_Precision": "", "materialOutsCharacteristics": 0, "materialOutsDefineCharacter": 0, "materialOutDefineCharacter": 0, "isWip": "", "costAccountingMethod": "", "bodyParallel": { "wipOpSn": "", "wipOperationId": "" }, "odyParallel_wipOperationCode": "", "bodyParallel_wipOperationName": "", "out_sys_id": "", "out_sys_code": "", "out_sys_version": "", "out_sys_type": "", "out_sys_rowno": "", "out_sys_lineid": "" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2081376284660727968</id>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<content>{ "code": "999", "message": "No enum constant org.imeta.core.base.ConditionOperator.2" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2081376284660727967</id>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<content>{ "code": "", "message": "", "data": { "sumRecordList": [ { "totalPieces": "", "totalQuantity": "", "subQty": "", "qty": "" } ], "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "pubts": "", "recordList": [ { "currency": "", "materOuts_product": "", "materOuts_unit": "", "materOuts_productsku": "", "vouchdate": "", "code": "", "org": "", "org_code": "", "org_name": "", "store": "", "bustype": "", "bustype_name": "", "store_name": "", "department_name": "", "department": "", "warehouse": "", "warehouse_name": "", "stockMgr_name": "", "stockMgr": "", "memo": "", "bustype_extend_attrs_json": "", "accountOrg_name": "", "accountOrg": "", "totalPieces": "", "exchangestatus": "", "status": "", "totalQuantity": 0, "srcbill": "", "creator": "", "srcbillno": "", "srcBillType": "", "createTime": "", "modifier": "", "modifyTime": "", "auditor": "", "auditTime": "", "id": "", "pubts": "", "tplid": "", "headItem": { "id": "", "define1": "", "define2": "", "define3": "", "define4": "" }, "materOuts": { "id": "" }, "product_cCode": "", "product_cName": "", "productsku_cCode": "", "productsku_cName": "", "productClass_code": "", "propertiesValue": "", "batchno": "", "invaliddate": "", "qty": 0, "product_unitName": "", "subQty": 0, "stockUnitId": "", "stockUnit_name": "", "project_code": "", "project_name": "", "natUnitPrice": 0, "natMoney": 0, "bodyItem": { "id": "", "define1": "", "define2": "", "define3": "", "define4": "", "define5": "", "define6": "", "define7": "", "define8": "", "define9": "", "define10": "", "define11": "", "define12": "", "define13": "", "define14": "", "define15": "", "define16": "", "define17": "", "define18": "", "define19": "", "define20": "", "define21": "", "define22": "", "define23": "", "define24": "", "define25": "", "define26": "", "define27": "", "define28": "", "define29": "", "define30": "", "define31": "", "define32": "", "define33": "", "define34": "", "define35": "", "define36": "", "define37": "", "define38": "", "define39": "", "define40": "", "define41": "", "define42": "", "define43": "", "define44": "", "define45": "", "define46": "", "define47": "", "define48": "", "define49": "", "define50": "", "define51": "", "define52": "", "define53": "", "define54": "", "define55": "", "define56": "", "define57": "", "define58": "", "define59": "", "define60": "" }, "natCurrency_priceDigit": "", "natCurrency_moneyDigit": "", "unit_code": "", "unit_Precision": "", "stockUnitId_Precision": "", "materialOutsCharacteristics": 0, "materialOutsDefineCharacter": 0, "materialOutDefineCharacter": 0, "isWip": "", "costAccountingMethod": "", "bodyParallel": { "wipOpSn": "", "wipOperationId": "" }, "odyParallel_wipOperationCode": "", "bodyParallel_wipOperationName": "", "out_sys_id": "", "out_sys_code": "", "out_sys_version": "", "out_sys_type": "", "out_sys_rowno": "", "out_sys_lineid": "" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2081376284660727968</id>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<content>{ "code": "999", "message": "No enum constant org.imeta.core.base.ConditionOperator.2" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>2081376284660727967</id>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<content>{ "code": "", "message": "", "data": { "sumRecordList": [ { "totalPieces": "", "totalQuantity": "", "subQty": "", "qty": "" } ], "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "pubts": "", "recordList": [ { "currency": "", "materOuts_product": "", "materOuts_unit": "", "materOuts_productsku": "", "vouchdate": "", "code": "", "org": "", "org_code": "", "org_name": "", "store": "", "bustype": "", "bustype_name": "", "store_name": "", "department_name": "", "department": "", "warehouse": "", "warehouse_name": "", "stockMgr_name": "", "stockMgr": "", "memo": "", "bustype_extend_attrs_json": "", "accountOrg_name": "", "accountOrg": "", "totalPieces": "", "exchangestatus": "", "status": "", "totalQuantity": 0, "srcbill": "", "creator": "", "srcbillno": "", "srcBillType": "", "createTime": "", "modifier": "", "modifyTime": "", "auditor": "", "auditTime": "", "id": "", "pubts": "", "tplid": "", "headItem": { "id": "", "define1": "", "define2": "", "define3": "", "define4": "" }, "materOuts": { "id": "" }, "product_cCode": "", "product_cName": "", "productsku_cCode": "", "productsku_cName": "", "productClass_code": "", "propertiesValue": "", "batchno": "", "invaliddate": "", "qty": 0, "product_unitName": "", "subQty": 0, "stockUnitId": "", "stockUnit_name": "", "project_code": "", "project_name": "", "natUnitPrice": 0, "natMoney": 0, "bodyItem": { "id": "", "define1": "", "define2": "", "define3": "", "define4": "", "define5": "", "define6": "", "define7": "", "define8": "", "define9": "", "define10": "", "define11": "", "define12": "", "define13": "", "define14": "", "define15": "", "define16": "", "define17": "", "define18": "", "define19": "", "define20": "", "define21": "", "define22": "", "define23": "", "define24": "", "define25": "", "define26": "", "define27": "", "define28": "", "define29": "", "define30": "", "define31": "", "define32": "", "define33": "", "define34": "", "define35": "", "define36": "", "define37": "", "define38": "", "define39": "", "define40": "", "define41": "", "define42": "", "define43": "", "define44": "", "define45": "", "define46": "", "define47": "", "define48": "", "define49": "", "define50": "", "define51": "", "define52": "", "define53": "", "define54": "", "define55": "", "define56": "", "define57": "", "define58": "", "define59": "", "define60": "" }, "natCurrency_priceDigit": "", "natCurrency_moneyDigit": "", "unit_code": "", "unit_Precision": "", "stockUnitId_Precision": "", "materialOutsCharacteristics": 0, "materialOutsDefineCharacter": 0, "materialOutDefineCharacter": 0, "isWip": "", "costAccountingMethod": "", "bodyParallel": { "wipOpSn": "", "wipOperationId": "" }, "odyParallel_wipOperationCode": "", "bodyParallel_wipOperationName": "", "out_sys_id": "", "out_sys_code": "", "out_sys_version": "", "out_sys_type": "", "out_sys_rowno": "", "out_sys_lineid": "" } ] } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>2081376284660727968</id>
<apiId>b483475dfd65499ab122b773eb9a8061</apiId>
<content>{ "code": "999", "message": "No enum constant org.imeta.core.base.ConditionOperator.2" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-09-05 10:41:30.000</gmtCreate>
<gmtUpdate>2024-09-05 10:41:30.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS/>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>友户通token认证业务扩展插件</name>
<configurable>false</configurable>
<description>YonsuitBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>b483475dfd65499ab122b773eb9a8061</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin>
<id>w181ed01-1e9b-4350-b994-71a66f017555</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code>resultParse</code>
<name>返回参数转换插件</name>
<configurable>false</configurable>
<description>解决返回值中带！的，转换为json</description>
<pluginType>resultParse</pluginType>
<pluginTypeName>返回值解析插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.result.ResultMapTransferParsePlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>true</visible>
<gmtCreate>2020-07-29 00:00:00</gmtCreate>
<gmtUpdate/>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>b483475dfd65499ab122b773eb9a8061</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</resultParsePlugin>
<mapReturnPluginConfig/>
<billNo>st_materialoutlist</billNo>
<domain>ustock</domain>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser/>
<createUserName/>
<approvalStatus>1</approvalStatus>
<publishTime>2024-09-05 11:09:24</publishTime>
<pathJoin>true</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout/>
<customUrl>/materialout/list</customUrl>
<fixedUrl>/yonbip/scm</fixedUrl>
<apiCode>b483475dfd65499ab122b773eb9a8061</apiCode>
<tokenCheckType>0</tokenCheckType>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL/>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>36a8b72b-d965-404d-a02d-66ff4a7afeb3</updateUserId>
<updateUserName>昵称-王章宇</updateUserName>
<paramIsForce/>
<userIDPassthrough>false</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.yonbip-scm-stock</microServiceCode>
<applicationCode>yonbip-scm-stock</applicationCode>
<privacyCategory>1</privacyCategory>
<privacyLevel>4</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType>0</serviceType>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType/>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>false</arrayParam>
<fileSize/>
<cc>true</cc>
<paramTransferMode>1</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId>1850748025614368790</apiDefId>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>1</paramExtRequest>
<paramExtResponse>1</paramExtResponse>
<paramExtInExtendKey>1</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene/>
<apiType/>
<paramMark/>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>false</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>false</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>1</chargeStatus>
<beforeSpeed>40</beforeSpeed>
<afterSpeed>80</afterSpeed>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath/>
<pubHistory/>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode/>
<multiVersion>0</multiVersion>
<apiTag/>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>2108770660671029249</id>
<name>用友YonBIP</name>
<type>integrateSys</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>SCC</id>
<name>供应链云</name>
<type>1</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MM</id>
<name>采购供应</name>
<type>2</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>ST</id>
<name>库存管理</name>
<type>3</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>ustock.st_materialout</id>
<name>材料出库单</name>
<type>4</type>
<sort>0</sort>
<enable>0</enable>
<children/>
<parentId/>
<productId/>
<code>ustock.st_materialout</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>ST</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MM</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>SCC</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>current_yonbip_default_sys</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<isOrigin>0</isOrigin>
<hasChildren>0</hasChildren>
<order>0</order>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>72113971-ae4c-4188-bc55-44b6173f4e0b</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:48:03</gmtCreate>
<gmtUpdate>2025-07-26 17:48:03</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>b946709d-f4d9-4a43-a551-f55beee7f3d5</id>
<name>XXX0111</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类项</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:48:03</gmtCreate>
<gmtUpdate>2025-07-26 17:48:03</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:48:03</gmtCreate>
<gmtUpdate>2025-07-26 17:48:03</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>19173169-b78a-4df1-9ff6-7b6b86840c07</id>
<name>XS11</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类号test</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:48:15</gmtCreate>
<gmtUpdate>2025-07-26 17:48:15</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:48:15</gmtCreate>
<gmtUpdate>2025-07-26 17:48:15</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>

<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>9e6cfc56-06f1-4bfb-b0f0-a1345e5ae982</id>
<name>TL001</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>退料理由</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName>bd.customerdoc_D001.D001</fullName>
<ytenantId/>
<paramOrder/>
<bizType>quote</bizType>
<baseType>false</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:48:25</gmtCreate>
<gmtUpdate>2025-07-26 17:48:25</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>true</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:48:25</gmtCreate>
<gmtUpdate>2025-07-26 17:48:25</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>ffeabc0e-ef62-464e-9a2f-df9b4972eb68</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:48:32</gmtCreate>
<gmtUpdate>2025-07-26 17:48:32</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
