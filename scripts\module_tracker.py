import argparse
import json
import sys
from datetime import datetime
from pathlib import Path

#!/usr/bin/env python3
"""
模块迁移状态追踪器
确保每个模块完成7个验证节点
"""


class ModuleTracker:
    def __init___(self, project_root: str = "."):
    """TODO: Add function description."""
    self.project_root = Path(project_root)
    self.status_file = self.project_root / "tasks" / "module_status.json"
    self.modules = [
        "材料出库单列表查询",
        "采购订单列表",
        "采购入库单列表",
        "产品入库单列表查询",
        "请购单列表查询",
        "生产订单列表查询",
        "委外订单列表",
        "委外入库列表查询",
        "委外申请列表查询",
        "销售出库列表查询",
        "销售订单",
        "需求计划",
        "业务日志",
    ]

    def init_status(self) -> Dict[str, Any]:
        """初始化模块状态"""
        status = {
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "total_modules": len(self.modules),
                "version": "1.0",
            },
            "modules": {},
        }

        for module in self.modules:
            status["modules"][module] = {
                "test_passed": False,
                "test_files_deleted": False,
                "mock_data_deleted": False,
                "real_data_verified": False,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "completion_rate": 0,
                "notes": "",
                "verification_history": [],
            }

        self.save_status(status)
        return status

    def update_module_status(
            self,
            module_name: str,
            checkpoint: str,
            value: bool = True,
            notes: str = ""):
        """更新模块检查点状态"""
        status = self.load_status()

        if module_name not in status["modules"]:
            print(f"错误: 模块 '{module_name}' 不存在")
            return False

        valid_checkpoints = [
            "test_passed",
            "test_files_deleted",
            "mock_data_deleted",
            "real_data_verified",
        ]
        if checkpoint not in valid_checkpoints:
            print(f"错误: 无效的检查点 '{checkpoint}'")
            print(f"有效检查点: {', '.join(valid_checkpoints)}")
            return False

        # 记录变更历史
        old_value = status["modules"][module_name][checkpoint]
        if old_value != value:
            status["modules"][module_name]["verification_history"].append(
                {
                    "checkpoint": checkpoint,
                    "old_value": old_value,
                    "new_value": value,
                    "timestamp": datetime.now().isoformat(),
                    "notes": notes,
                }
            )

        # 更新状态
        status["modules"][module_name][checkpoint] = value
        status["modules"][module_name]["updated_at"] = datetime.now().isoformat()
        status["modules"][module_name]["notes"] = notes

        # 计算完成率
        checkpoints = [
            "test_passed",
            "test_files_deleted",
            "mock_data_deleted",
            "real_data_verified",
        ]
        completed = sum(
            1 for cp in checkpoints if status["modules"][module_name][cp])
        status["modules"][module_name]["completion_rate"] = (
            completed / len(checkpoints) * 100
        )

        # 更新元数据
        status["metadata"]["last_updated"] = datetime.now().isoformat()

        self.save_status(status)
        print(f"✅ 模块 '{module_name}' 的 '{checkpoint}' 已更新为 {value}")
        return True

    def verify_module(self, module_name: str) -> Dict[str, Any]:
        """验证模块的所有检查点"""
        if module_name not in self.modules:
            return {"error": f"模块 '{module_name}' 不存在"}

        verification_result = {
            "module": module_name,
            "timestamp": datetime.now().isoformat(),
            "checkpoints": {},
            "overall_status": "unknown",
        }

        # 检查测试通过
        test_result = self.check_test_passed(module_name)
        verification_result["checkpoints"]["test_passed"] = test_result

        # 检查测试文件删除
        test_files_result = self.check_test_files_deleted(module_name)
        verification_result["checkpoints"]["test_files_deleted"] = test_files_result

        # 检查模拟数据删除
        mock_data_result = self.check_mock_data_deleted(module_name)
        verification_result["checkpoints"]["mock_data_deleted"] = mock_data_result

        # 检查真实数据验证
        real_data_result = self.check_real_data_verified(module_name)
        verification_result["checkpoints"]["real_data_verified"] = real_data_result

        # 计算整体状态
        passed_checks = sum(
            1
            for check in verification_result["checkpoints"].values()
            if check["status"]
        )
        total_checks = len(verification_result["checkpoints"])

        if passed_checks == total_checks:
            verification_result["overall_status"] = "completed"
        elif passed_checks > 0:
            verification_result["overall_status"] = "in_progress"
        else:
            verification_result["overall_status"] = "not_started"

        return verification_result

    def check_test_passed(self, module_name: str) -> Dict[str, Any]:
        """检查测试是否通过"""
        # 查找测试文件
        test_files = list(
            self.project_root.glob(
                f"tests/**/*{module_name.replace(' ', '_').lower()}*.py"
            )
        )
        test_files.extend(
            list(
                self.project_root.glob(
                    f"frontend/tests/**/*{module_name.replace(' ', '_').lower()}*.js"
                )
            )
        )

        if not test_files:
            return {
                "status": False,
                "message": "未找到测试文件",
                "details": {"test_files": []},
            }

        # 检查最近的测试结果
        # 这里可以集成实际的测试运行器结果
        return {
            "status": True,  # 简化实现，实际应该检查测试运行结果
            "message": "找到测试文件",
            "details": {"test_files": [str(f) for f in test_files]},
        }

    def check_test_files_deleted(self, module_name: str) -> Dict[str, Any]:
        """检查测试文件是否已删除"""
        # 查找临时测试文件
        temp_test_files = list(
            self.project_root.glob(
                f"**/temp_*{module_name.replace(' ', '_').lower()}*.py"
            )
        )
        temp_test_files.extend(
            list(
                self.project_root.glob(
                    f"**/mock_*{module_name.replace(' ', '_').lower()}*.py"
                )
            )
        )

        if temp_test_files:
            return {
                "status": False,
                "message": "发现临时测试文件",
                "details": {"temp_files": [str(f) for f in temp_test_files]},
            }

        return {
            "status": True,
            "message": "未发现临时测试文件",
            "details": {"temp_files": []},
        }

    def check_mock_data_deleted(self, module_name: str) -> Dict[str, Any]:
        """检查模拟数据是否已删除"""
        # 查找模拟数据文件
        mock_data_files = list(
            self.project_root.glob(
                f"**/mock_data*{module_name.replace(' ', '_').lower()}*"
            )
        )
        mock_data_files.extend(
            list(
                self.project_root.glob(
                    f"graveyard/mock_data/**/*{module_name.replace(' ', '_').lower()}*"
                )
            )
        )

        # 排除graveyard目录中的文件（这些是正常备份）
        active_mock_files = [
            f for f in mock_data_files if "graveyard" not in str(f)]

        if active_mock_files:
            return {
                "status": False,
                "message": "发现活动的模拟数据文件",
                "details": {"mock_files": [str(f) for f in active_mock_files]},
            }

        return {
            "status": True,
            "message": "未发现活动的模拟数据文件",
            "details": {"mock_files": []},
        }

    def check_real_data_verified(self, module_name: str) -> Dict[str, Any]:
        """检查真实数据是否验证"""
        # 查找验证日志
        log_files = list(self.project_root.glob("logs/verification/*.log"))
        log_files.extend(
            list(
                self.project_root.glob("logs/real_data_test*.log")))

        verified = False
        verification_details = []

        for log_file in log_files:
            try:
                with open(log_file, "r", encoding="utf-8") as f:
                    content = f.read()
                    if module_name in content and "真实数据验证通过" in content:
                        verified = True
                        verification_details.append(str(log_file))
            except Exception:
                continue

        return {
            "status": verified,
            "message": "真实数据验证通过" if verified else "未找到真实数据验证记录",
            "details": {"verification_logs": verification_details},
        }

    def generate_progress_report(self) -> Dict[str, Any]:
        """生成进度报告"""
        status = self.load_status()

        completed_modules = []
        in_progress_modules = []
        not_started_modules = []

        for module_name, module_data in status["modules"].items():
            completion_rate = module_data["completion_rate"]
            if completion_rate == 100:
                completed_modules.append(module_name)
            elif completion_rate > 0:
                in_progress_modules.append(module_name)
            else:
                not_started_modules.append(module_name)

        report = {
            "生成时间": datetime.now().isoformat(),
            "总体进度": {
                "总模块数": len(self.modules),
                "已完成模块": len(completed_modules),
                "进行中模块": len(in_progress_modules),
                "未开始模块": len(not_started_modules),
                "完成百分比": round(
                    (len(completed_modules) / len(self.modules)) * 100, 2
                ),
            },
            "模块列表": {
                "已完成": completed_modules,
                "进行中": in_progress_modules,
                "未开始": not_started_modules,
            },
            "详细状态": status["modules"],
        }

        # 保存报告
        report_path = (
            self.project_root /
            "reports" /
            f"progress_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        report_path.parent.mkdir(exist_ok=True)

        with open(report_path, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        # 同时保存为最新报告
        latest_report_path = (
            self.project_root / "reports" / "latest_progress_report.json"
        )
        with open(latest_report_path, "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        return report

    def generate_markdown_report(self) -> str:
        """生成Markdown格式的进度报告"""
        report = self.generate_progress_report()

        md_content = f"""# 模块迁移进度报告

生成时间: {report['生成时间']}

## 📊 总体进度

- **总模块数**: {report['总体进度']['总模块数']}
- **已完成**: {report['总体进度']['已完成模块']} ({report['总体进度']['完成百分比']}%)
- **进行中**: {report['总体进度']['进行中模块']}
- **未开始**: {report['总体进度']['未开始模块']}

## 📋 模块状态详情

### ✅ 已完成模块
"""

        for module in report["模块列表"]["已完成"]:
            md_content += f"- {module}\n"

        md_content += "\n### 🔄 进行中模块\n"
        for module in report["模块列表"]["进行中"]:
            completion_rate = report["详细状态"][module]["completion_rate"]
            md_content += f"- {module} ({completion_rate}%)\n"

        md_content += "\n### 📋 未开始模块\n"
        for module in report["模块列表"]["未开始"]:
            md_content += f"- {module}\n"

        md_content += """
## 🔍 检查点说明

每个模块需要完成以下4个检查点：

1. **测试通过** - 模块功能测试通过
2. **删除测试文件** - 清理临时测试文件
3. **删除模拟数据** - 清理模拟数据文件
4. **真实数据跑通** - 使用真实数据验证功能

## 📈 下一步建议

"""

        if report["模块列表"]["进行中"]:
            md_content += "### 优先完成进行中的模块:\n"
            for module in report["模块列表"]["进行中"]:
                md_content += f"```bash\npython scripts/module_tracker.py --verify '{module}'\n```\n"

        if report["模块列表"]["未开始"]:
            md_content += "### 开始新模块:\n"
            next_module = (
                report["模块列表"]["未开始"][0]
                if report["模块列表"]["未开始"]
                else None
            )
            if next_module:
                md_content += f"```bash\npython scripts/module_tracker.py --update '{next_module}' test_passed true\n```\n"

        # 保存Markdown报告
        md_path = (
            self.project_root
            / "reports"
            / f"progress_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        )
        with open(md_path, "w", encoding="utf-8") as f:
            f.write(md_content)

        # 同时保存为最新报告
        latest_md_path = self.project_root / "reports" / "latest_progress_report.md"
        with open(latest_md_path, "w", encoding="utf-8") as f:
            f.write(md_content)

        print(f"📄 Markdown报告已生成: {md_path}")
        return md_content

    def load_status(self) -> Dict[str, Any]:
        """加载状态文件"""
        if self.status_file.exists():
            try:
                with open(self.status_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                print("⚠️ 状态文件损坏，重新初始化")
                return self.init_status()
        return self.init_status()

    def save_status(self, status: Dict[str, Any]):
        """保存状态文件"""
        self.status_file.parent.mkdir(exist_ok=True)
        with open(self.status_file, "w", encoding="utf-8") as f:
            json.dump(status, f, ensure_ascii=False, indent=2)


def mainn():
    """TODO: Add function description."""
    parser = argparse.ArgumentParser(description="模块迁移状态追踪器")
    parser.add_argument("--init", action="store_true", help="初始化模块状态")
    parser.add_argument(
        "--update",
        nargs=3,
        metavar=("MODULE", "CHECKPOINT", "VALUE"),
        help="更新模块检查点状态",
    )
    parser.add_argument("--verify", help="验证指定模块的所有检查点")
    parser.add_argument("--report", action="store_true", help="生成进度报告")
    parser.add_argument(
        "--markdown",
        action="store_true",
        help="生成Markdown格式报告")
    parser.add_argument("--list", action="store_true", help="列出所有模块")
    parser.add_argument("--notes", help="添加备注信息")

    args = parser.parse_args()

    tracker = ModuleTracker()

    if args.init:
        print("🚀 初始化模块状态...")
        status = tracker.init_status()
        print(f"✅ 已初始化 {len(tracker.modules)} 个模块")

    elif args.update:
        module_name, checkpoint, value_str = args.update
        value = value_str.lower() in ["true", "1", "yes", "y"]
        notes = args.notes or ""
        tracker.update_module_status(module_name, checkpoint, value, notes)

    elif args.verify:
        print(f"🔍 验证模块: {args.verify}")
        result = tracker.verify_module(args.verify)
        print(json.dumps(result, ensure_ascii=False, indent=2))

    elif args.report:
        print("📊 生成进度报告...")
        report = tracker.generate_progress_report()
        print(json.dumps(report, ensure_ascii=False, indent=2))

    elif args.markdown:
        print("📄 生成Markdown报告...")
        tracker.generate_markdown_report()

    elif args.list:
        print("📋 模块列表:")
        for i, module in enumerate(tracker.modules, 1):
            print(f"{i:2d}. {module}")

    else:
        # 默认显示简要状态
        status = tracker.load_status()
        total = len(tracker.modules)
        completed = len([m for m in status["modules"].values()
                         if m["completion_rate"] == 100])
        in_progress = len(
            [m for m in status["modules"].values() if 0 < m["completion_rate"] < 100]
        )

        print(f"📊 模块迁移状态: {completed}/{total} 已完成, {in_progress} 进行中")
        print(f"📄 详细报告: python {sys.argv[0]} --markdown")


if __name__ == "__main__":
    main()
