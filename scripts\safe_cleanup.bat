@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🧹 YS-API V3.0 迁移后安全清理工具
echo ==================================================
echo.

REM 检查是否在正确的目录
if not exist "frontend" (
    echo ❌ 错误：请在项目根目录运行此脚本
    echo 当前目录应包含 frontend 文件夹
    pause
    exit /b 1
)

echo 📂 当前工作目录: %CD%
echo ✅ 检测到 frontend 目录
echo.

echo 🔍 将要清理的文件：
echo -------------------

REM 定义要清理的文件列表
set "files_to_clean="
set "files_to_clean=!files_to_clean!;frontend\component-test.html"
set "files_to_clean=!files_to_clean!;frontend\direct-component-test.html"
set "files_to_clean=!files_to_clean!;frontend\js-loading-test.html"
set "files_to_clean=!files_to_clean!;frontend\performance-optimization-demo.html"
set "files_to_clean=!files_to_clean!;frontend\sync-test.html"
set "files_to_clean=!files_to_clean!;frontend\test_data_type_frontend.html"
set "files_to_clean=!files_to_clean!;frontend\method-fix-test.html"
set "files_to_clean=!files_to_clean!;frontend\component-diagnostic.html"

REM 检查文件是否存在
for %%i in (%files_to_clean:;= %) do (
    if exist "%%i" (
        echo   📄 %%i (存在^)
    ) else (
        echo   ❌ %%i (不存在^)
    )
)

echo.
echo ⚠️  注意：此脚本只清理确认安全的临时和测试文件
echo ❗ 不会删除任何核心功能文件或迁移后的页面
echo.

REM 询问用户确认
set /p "confirm=是否继续清理？(y/N): "
if /i not "%confirm%"=="y" (
    echo ❌ 清理已取消
    pause
    exit /b 0
)

echo.
echo 🚀 开始清理...
echo ==================

REM 清理计数器
set removed_count=0
set total_count=0

REM 计算总文件数
for %%i in (%files_to_clean:;= %) do (
    set /a total_count+=1
)

REM 执行清理
for %%i in (%files_to_clean:;= %) do (
    if exist "%%i" (
        del "%%i" >nul 2>&1
        if not exist "%%i" (
            echo ✅ 已删除: %%i
            set /a removed_count+=1
        ) else (
            echo ❌ 删除失败: %%i
        )
    ) else (
        echo ⏭️  跳过不存在的文件: %%i
    )
)

echo.
echo 📊 清理完成统计
echo ==================
echo 总文件数: !total_count!
echo 成功删除: !removed_count!
set /a skipped_count=!total_count!-!removed_count!
echo 跳过文件: !skipped_count!

if !removed_count! gtr 0 (
    echo.
    echo ✅ 清理成功完成！
    echo 💡 建议：清理后请测试迁移页面功能是否正常
) else (
    echo.
    echo ℹ️  没有找到需要清理的文件
)

echo.
echo 🔗 更多清理选项请参考：
echo    docs/16-迁移后清理指南.md
echo.

echo ⚠️  重要提醒：
echo    • 已保留所有核心功能文件
echo    • 已保留所有迁移后的页面
echo    • 已保留新架构组件文件
echo    • 备份文件需要手动评估清理
echo.

echo 🎯 下一步建议：
echo    1. 测试迁移后的页面功能
echo    2. 验证新架构组件工作正常
echo    3. 确认功能无误后再考虑清理备份文件
echo.

pause
