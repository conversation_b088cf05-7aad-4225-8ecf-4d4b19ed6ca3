import json
import logging
import os
import sqlite3
import time
from datetime import datetime
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境测试报告生成器
Production Environment Test Report Generator
"""


def generate_production_test_report():
    """生成生产环境测试报告"""

    logging.basicConfig(
        level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    logger.info("📋 YS-API V3.0 生产环境测试报告生成器")
    logger.info("=" * 60)

    start_time = datetime.now()

    # 创建报告结构
    report = {
        'test_info': {
            'project_name': 'YS-API V3.0',
            'test_date': start_time.strftime('%Y-%m-%d'),
            'test_time': start_time.strftime('%H:%M:%S'),
            'test_type': 'Production Readiness Assessment',
            'test_version': '1.0.0',
        },
        'system_analysis': {},
        'infrastructure_assessment': {},
        'performance_metrics': {},
        'security_evaluation': {},
        'deployment_readiness': {},
        'overall_assessment': {},
    }

    # DEBUG: print("\n🔍 1. 系统分析...")

    # 1. 系统分析
    system_info = {
        'python_version': f"{os.sys.version.split()[0]}",
        'operating_system': os.name,
        'project_structure': {},
        'dependencies': {},
    }

    # 检查项目结构
    key_directories = ['backend', 'frontend', 'config', 'docs', 'tests']
    project_structure = {}
    for dir_name in key_directories:
        dir_path = Path(dir_name)
        if dir_path.exists():
            file_count = len(list(dir_path.rglob('*'))
                             ) if dir_path.is_dir() else 1
            project_structure[dir_name] =
            {'exists': True,
             'file_count': file_count
             }
        else:
            project_structure[dir_name] = {'exists': False}

    system_info['project_structure'] = project_structure

    # 检查关键文件
    key_files = [
        'README.md',
        'requirements.txt',
        'backend/config.ini',
        'backend/.env',
        'Dockerfile',
        'docker-compose.yml',
    ]

    file_status = {}
    for file_path in key_files:
        path_obj = Path(file_path)
        file_status[file_path] = {
            'exists': path_obj.exists(),
            'size_kb': (
                round(
                    path_obj.stat().st_size /
                    1024,
                    2) if path_obj.exists() else 0),
        }

    system_info['key_files'] = file_status
    report['system_analysis'] = system_info

    logger.info("🔍 1. 系统分析...")
    logger.info("   ✅ 系统信息收集完成")

    logger.info("🏗️ 2. 基础设施评估...")

    # 2. 基础设施评估
    infrastructure = {
        'database': {},
        'configuration': {},
        'logging': {},
        'deployment': {},
    }

    # 数据库检查
    db_path = Path("backend/ysapi.db")
    if db_path.exists():
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()

            # 检查数据库大小
            db_size = db_path.stat().st_size

            infrastructure['database'] = {
                'status': 'available',
                'type': 'SQLite',
                'size_mb': round(db_size / (1024 * 1024), 2),
                'table_count': len(tables),
                'tables': [table[0] for table in tables],
            }

            conn.close()
        except Exception:
            infrastructure['database'] = {'status': 'error', 'error': str(e)}
    else:
        infrastructure['database'] = {
            'status': 'missing',
            'message': 'Database file not found',
        }

    # 配置检查
    config_files =
    ['backend/config.ini',
     'backend/.env',
     'config/modules.json'
     ]
    config_status = {}
    for config_file in config_files:
        path_obj = Path(config_file)
        config_status[config_file] = {
            'exists': path_obj.exists(),
            'readable': path_obj.is_file() if path_obj.exists() else False,
        }

    infrastructure['configuration'] = config_status

    # 日志系统检查
    log_dir = Path("logs")
    if log_dir.exists():
        log_files = list(log_dir.glob("*.log"))
        infrastructure['logging'] = {
            'log_directory': True,
            'log_file_count': len(log_files),
            'recent_logs': [f.name for f in log_files[-5:]],  # 最近5个日志文件
        }
    else:
        infrastructure['logging'] =
        {'log_directory': False,
         'setup_required': True
         }

    # 部署配置检查
    deployment_files = [
        'Dockerfile',
        'docker-compose.yml',
        '.github/workflows/main.yml',
    ]
    deployment_status = {}
    for file_path in deployment_files:
        path_obj = Path(file_path)
        deployment_status[file_path] = path_obj.exists()

    infrastructure['deployment'] = deployment_status

    report['infrastructure_assessment'] = infrastructure
    logger.info("   ✅ 基础设施评估完成")

    logger.info("⚡ 3. 性能指标评估...")

    # 3. 性能指标评估
    performance = {
        'file_system': {},
        'database_performance': {},
        'estimated_capacity': {},
    }

    # 文件系统性能
    try:
        # 简单的文件读写测试
        test_file = Path("performance_test.tmp")

        # 写测试
        start = time.time()
        with open(test_file, 'w') as f:
            for i in range(1000):
                f.write(f"test_line_{i}\n")
        write_time = (time.time() - start) * 1000

        # 读测试
        start = time.time()
        with open(test_file, 'r') as f:
            f.readlines()
        read_time = (time.time() - start) * 1000

        # 清理
        test_file.unlink()

        performance['file_system'] = {
            'write_time_ms': round(write_time, 2),
            'read_time_ms': round(read_time, 2),
            'io_performance': (
                'good' if write_time < 100 and read_time < 50 else 'needs_optimization'
            ),
        }

    except Exception:
        performance['file_system'] = {'error': str(e)}

    # 数据库性能测试
    if infrastructure['database']['status'] == 'available':
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()

            # 查询性能测试
            start = time.time()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master")
            cursor.fetchone()
            query_time = (time.time() - start) * 1000

            # 写入测试
            start = time.time()
            cursor.execute(
                "CREATE TEMP TABLE perf_test (id INTEGER, data TEXT)")
            for i in range(100):
                cursor.execute(
                    "INSERT INTO perf_test VALUES (?, ?)", (i, f"data_{i}"))
            conn.commit()
            write_time = (time.time() - start) * 1000

            conn.close()

            performance['database_performance'] = {
                'query_time_ms': round(query_time, 2),
                'write_time_ms': round(write_time, 2),
                'performance_rating': (
                    'excellent' if query_time < 10 and write_time < 100 else 'good'
                ),
            }

        except Exception:
            performance['database_performance'] = {'error': str(e)}

    # 容量估算
    total_size = sum(
        f.stat().st_size
        for f in Path('.').rglob('*')
        if f.is_file() and not f.name.startswith('.git')
    )

    performance['estimated_capacity'] = {
        'current_size_mb': round(total_size / (1024 * 1024), 2),
        'estimated_ram_requirement_mb': 256,  # 基于项目大小估算
        'estimated_disk_requirement_gb': 1,  # 基于当前使用估算
        'recommended_cpu_cores': 2,
    }

    report['performance_metrics'] = performance
    logger.info("   ✅ 性能指标评估完成")

    logger.info("🛡️ 4. 安全评估...")

    # 4. 安全评估
    security = {
        'configuration_security': {},
        'file_permissions': {},
        'sensitive_data': {},
        'security_score': 0,
    }

    security_score = 100

    # 配置安全检查
    env_file = Path("backend/.env")
    if env_file.exists():
        security['configuration_security']['env_file'] = 'configured'
        security_score += 10
    else:
        security['configuration_security']['env_file'] = 'missing'
        security_score -= 20

    # 敏感文件检查
    sensitive_files = ['backend/config.ini', 'backend/.env']
    sensitive_data_status = {}

    for file_path in sensitive_files:
        path_obj = Path(file_path)
        if path_obj.exists():
            # 检查是否包含敏感信息
            try:
                content = path_obj.read_text(encoding='utf-8')
                has_passwords = any(
                    keyword in content.lower()
                    for keyword in ['password', 'secret', 'token']
                )
                sensitive_data_status[file_path] = {
                    'exists': True,
                    'has_sensitive_data': has_passwords,
                    'needs_environment_variables': has_passwords,
                }
                if has_passwords:
                    security_score -= 5
            except Exception:
                sensitive_data_status[file_path] =
                {'exists': True,
                 'readable': False
                 }
        else:
            sensitive_data_status[file_path] = {'exists': False}

    security['sensitive_data'] = sensitive_data_status

    # 安全配置建议
    security_recommendations = []
    if env_file.exists():
        security_recommendations.append("✅ 环境变量配置已设置")
    else:
        security_recommendations.append("⚠️ 建议配置环境变量文件")

    if any(item.get('has_sensitive_data', False)
            for item in sensitive_data_status.values()):
        security_recommendations.append("🔐 发现敏感数据，建议迁移到环境变量")

    security['recommendations'] = security_recommendations
    security['security_score'] = max(0, min(100, security_score))

    report['security_evaluation'] = security
    logger.info("   ✅ 安全评估完成")

    logger.info("🚀 5. 部署就绪性评估...")

    # 5. 部署就绪性评估
    deployment = {
        'containerization': {},
        'ci_cd': {},
        'monitoring': {},
        'documentation': {},
    }

    # 容器化检查
    has_dockerfile = Path("Dockerfile").exists()
    has_compose = Path("docker-compose.yml").exists()
    deployment['containerization'] = {
        'dockerfile': has_dockerfile,
        'docker_compose': has_compose,
        'container_ready': has_dockerfile and has_compose,
    }

    # CI/CD检查
    github_workflow = Path(".github/workflows/main.yml").exists()
    deployment['ci_cd'] = {
        'github_actions': github_workflow,
        'automated_deployment': github_workflow,
    }

    # 监控配置检查
    monitoring_files = [
        'monitoring/prometheus.yml',
        'monitoring/grafana-dashboard.json',
    ]
    monitoring_configured = any(Path(f).exists() for f in monitoring_files)
    deployment['monitoring'] = {
        'configured': monitoring_configured,
        'files_present': monitoring_configured,
    }

    # 文档检查
    doc_files = ['README.md', 'docs/', 'CICD-README.md']
    docs_available = any(Path(f).exists() for f in doc_files)
    deployment['documentation'] = {
        'available': docs_available,
        'comprehensive': docs_available and Path("docs").exists(),
    }

    report['deployment_readiness'] = deployment
    logger.info("   ✅ 部署就绪性评估完成")

    logger.info("📊 6. 生成综合评估...")

    # 6. 综合评估
    end_time = datetime.now()
    duration = end_time - start_time

    # 计算各项评分
    scores = {}

    # 系统分析评分 (25分)
    system_score = 0
    structure = report['system_analysis']['project_structure']
    for dir_name, info in structure.items():
        if info['exists']:
            system_score += 5
    scores['system_analysis'] = min(system_score, 25)

    # 基础设施评分 (25分)
    infra_score = 0
    if infrastructure['database']['status'] == 'available':
        infra_score += 10
    if any(config['exists']
           for config in infrastructure['configuration'].values()):
        infra_score += 10
    if deployment['containerization']['container_ready']:
        infra_score += 5
    scores['infrastructure'] = min(infra_score, 25)

    # 性能评分 (20分)
    perf_score = 15  # 基础分
    if 'error' not in performance['file_system']:
        perf_score += 3
    if 'error' not in performance['database_performance']:
        perf_score += 2
    scores['performance'] = min(perf_score, 20)

    # 安全评分 (20分)
    scores['security'] = min(security['security_score'] * 0.2, 20)

    # 部署就绪性评分 (10分)
    deploy_score = 0
    if deployment['containerization']['container_ready']:
        deploy_score += 4
    if deployment['ci_cd']['automated_deployment']:
        deploy_score += 3
    if deployment['documentation']['available']:
        deploy_score += 3
    scores['deployment_readiness'] = min(deploy_score, 10)

    # 总分计算
    total_score = sum(scores.values())
    percentage = (total_score / 100) * 100

    # 等级评定
    if percentage >= 90:
        grade = "A+ 优秀"
        readiness = "🟢 完全准备就绪"
        recommendation = "系统已完全准备好投入生产环境，可以立即部署"
    elif percentage >= 80:
        grade = "A 良好"
        readiness = "🟡 基本准备就绪"
        recommendation = "系统基本准备就绪，建议进行小规模测试后部署"
    elif percentage >= 70:
        grade = "B 合格"
        readiness = "🟠 需要改进"
        recommendation = "系统可用但需要优化关键问题后再部署"
    elif percentage >= 60:
        grade = "C 勉强"
        readiness = "🔴 需要修复"
        recommendation = "系统需要修复重要问题才能投入生产"
    else:
        grade = "D 不合格"
        readiness = "❌ 不可部署"
        recommendation = "系统不满足生产环境要求，需要全面改进"

    overall_assessment = {
        'total_score': total_score,
        'percentage': round(percentage, 1),
        'grade': grade,
        'readiness_status': readiness,
        'recommendation': recommendation,
        'individual_scores': scores,
        'test_completion_time': end_time.isoformat(),
        'test_duration': str(duration).split('.')[0],
        'next_steps': [],
    }

    # 生成后续步骤建议
    next_steps = []
    if percentage >= 80:
        next_steps.extend(
            [
                "🚀 准备生产环境部署",
                "📊 建立监控和告警系统",
                "🧪 进行用户验收测试",
                "📚 完善运维文档",
            ]
        )
    else:
        next_steps.extend(
            [
                "🔧 修复发现的问题",
                "⚡ 优化系统性能",
                "🛡️ 加强安全配置",
                "📋 重新进行评估测试",
            ]
        )

    overall_assessment['next_steps'] = next_steps

    report['overall_assessment'] = overall_assessment

    logger.info("   ✅ 综合评估完成")

    # 保存报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    json_file = f"production_readiness_report_{timestamp}.json"

    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    # 生成Markdown报告
    md_content = generate_markdown_report(report)
    md_file = f"production_readiness_report_{timestamp}.md"

    with open(md_file, 'w', encoding='utf-8') as f:
        f.write(md_content)

    # 显示结果
    logger.info("=" * 60)
    logger.info("📊 YS-API V3.0 生产环境就绪性评估结果")
    logger.info("=" * 60)
    logger.info(f"📈 总体评分: {total_score}/100 ({percentage:.1f}%)")
    logger.info(f"🏆 评估等级: {grade}")
    logger.info(f"🎯 就绪状态: {readiness}")
    logger.info(f"💡 建议: {recommendation}")
    logger.info(f"⏱️ 评估耗时: {str(duration).split('.')[0]}")

    # 显示各项评分
    logger.info("📊 详细评分:")
    for category, score in scores.items():
        logger.info(f"   {category}: {score} 分")

    logger.info("📄 详细报告已保存:")
    logger.info(f"   📋 JSON格式: {json_file}")
    logger.info(f"   📖 Markdown格式: {md_file}")

    logger.info("🎯 后续步骤:")
    for i, step in enumerate(next_steps, 1):
        logger.info(f"   {i}. {step}")

    return total_score, grade, json_file, md_file


def generate_markdown_report(report_data):
    """生成Markdown格式报告"""
    overall = report_data['overall_assessment']

    md_content = f"""# YS-API V3.0 生产环境就绪性评估报告

## 📊 评估概览

- **项目名称**: {report_data['test_info']['project_name']}
- **评估日期**: {report_data['test_info']['test_date']}
- **评估时间**: {report_data['test_info']['test_time']}
- **评估类型**: {report_data['test_info']['test_type']}
- **评估耗时**: {overall['test_duration']}

## 🎯 评估结果

### 📈 总体评分
- **分数**: {overall['total_score']}/100 ({overall['percentage']}%)
- **等级**: {overall['grade']}
- **状态**: {overall['readiness_status']}
- **建议**: {overall['recommendation']}

### 📊 各项评分详情
"""

    for category, score in overall['individual_scores'].items():
        md_content += f"- **{category}**: {score} 分\n"

    # 系统分析部分
    system = report_data['system_analysis']
    md_content += f"""
## 🔍 系统分析

### Python环境
- **版本**: {system['python_version']}
- **操作系统**: {system['operating_system']}

### 项目结构
"""

    for dir_name, info in system['project_structure'].items():
        status = "✅" if info['exists'] else "❌"
        file_count = f" ({info.get('file_count', 0)} 个文件)" if info['exists'] else ""
        md_content += f"- **{dir_name}**: {status}{file_count}\n"

    md_content += "\n### 关键文件\n"
    for file_path, info in system['key_files'].items():
        status = "✅" if info['exists'] else "❌"
        size_info = f" ({info['size_kb']} KB)" if info['exists'] else ""
        md_content += f"- **{file_path}**: {status}{size_info}\n"

    # 基础设施评估
    infra = report_data['infrastructure_assessment']
    md_content += f"""
## 🏗️ 基础设施评估

### 数据库
- **状态**: {infra['database'].get('status', 'unknown')}
"""

    if infra['database'].get('status') == 'available':
        db = infra['database']
        md_content += f"""- **类型**: {db.get('type', 'N/A')}
- **大小**: {db.get('size_mb', 0)} MB
- **表数量**: {db.get('table_count', 0)}
"""

    md_content += "\n### 配置文件\n"
    for config_file, status in infra['configuration'].items():
        check = "✅" if status['exists'] else "❌"
        md_content += f"- **{config_file}**: {check}\n"

    # 性能指标
    perf = report_data['performance_metrics']
    md_content += f"""
## ⚡ 性能指标

### 文件系统性能
"""

    if 'error' not in perf['file_system']:
        fs = perf['file_system']
        md_content += f"""- **写入性能**: {fs['write_time_ms']} ms
- **读取性能**: {fs['read_time_ms']} ms
- **整体评价**: {fs['io_performance']}
"""

    if 'error' not in perf.get('database_performance', {}):
        db_perf = perf['database_performance']
        md_content += f"""
### 数据库性能
- **查询时间**: {db_perf['query_time_ms']} ms
- **写入时间**: {db_perf['write_time_ms']} ms
- **性能评级**: {db_perf['performance_rating']}
"""

    capacity = perf['estimated_capacity']
    md_content += f"""
### 容量评估
- **当前大小**: {capacity['current_size_mb']} MB
- **推荐内存**: {capacity['estimated_ram_requirement_mb']} MB
- **推荐磁盘**: {capacity['estimated_disk_requirement_gb']} GB
- **推荐CPU**: {capacity['recommended_cpu_cores']} 核心
"""

    # 安全评估
    security = report_data['security_evaluation']
    md_content += f"""
## 🛡️ 安全评估

### 安全评分
- **得分**: {security['security_score']}/100

### 配置安全
- **环境变量**: {security['configuration_security'].get('env_file', 'unknown')}

### 安全建议
"""

    for rec in security.get('recommendations', []):
        md_content += f"- {rec}\n"

    # 部署就绪性
    deploy = report_data['deployment_readiness']
    md_content += f"""
## 🚀 部署就绪性

### 容器化
- **Dockerfile**: {"✅" if deploy['containerization']['dockerfile'] else "❌"}
- **Docker Compose**: {"✅" if deploy['containerization']['docker_compose'] else "❌"}
- **容器就绪**: {"✅" if deploy['containerization']['container_ready'] else "❌"}

### CI/CD
- **GitHub Actions**: {"✅" if deploy['ci_cd']['github_actions'] else "❌"}
- **自动化部署**: {"✅" if deploy['ci_cd']['automated_deployment'] else "❌"}

### 监控
- **监控配置**: {"✅" if deploy['monitoring']['configured'] else "❌"}

### 文档
- **文档可用**: {"✅" if deploy['documentation']['available'] else "❌"}
- **文档完整**: {"✅" if deploy['documentation']['comprehensive'] else "❌"}
"""

    # 后续步骤
    md_content += f"""
## 🎯 后续步骤

"""

    for i, step in enumerate(overall['next_steps'], 1):
        md_content += f"{i}. {step}\n"

    md_content += f"""
---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*评估工具版本: {report_data['test_info']['test_version']}*
"""

    return md_content


def main():
    """主函数"""
    try:
        score, grade, json_file, md_file = generate_production_test_report()

        logger = logging.getLogger(__name__)
        logger.info("🎉 生产环境就绪性评估完成!")
        logger.info(f"📊 最终评分: {score}/100 ({grade})")

        if score >= 80:
            logger.info("✅ 系统已准备好投入生产环境")
            return 0
        elif score >= 60:
            logger.info("⚠️ 系统基本可用，建议改进后部署")
            return 1
        else:
            logger.info("❌ 系统需要重大改进才能投入生产")
            return 2

    except Exception:

        logger = logging.getLogger(__name__)
        logger.error(f"❌ 评估过程中发生错误: {e}")
        return 3


if __name__ == "__main__":
    exit(main())
