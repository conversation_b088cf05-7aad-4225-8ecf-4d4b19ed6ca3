import json
import os
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mock响应工具类
用于开发环境模拟API响应，避免测试代码中的重复Mock逻辑
"""


class MockResponse:
    """标准化的Mock响应类"""

    def __init___(
            self,
            status_code: int = 200,
            json_data: Optional[Dict] = None,
            text: str = ""):
    """TODO: Add function description."""
    self.status_code = status_code
    self._json_data = json_data or {}
    self.text = text

    def json(self):
        """返回JSON数据"""
        return self._json_data

    @property
    def content(self):
        """返回响应内容"""
        return self.text.encode('utf-8')


class MockAPIClient:
    """Mock API客户端，提供统一的模拟响应"""

    def __init__(self, mock_data_dir: str = None):
        """
        初始化Mock客户端

        Args:
            mock_data_dir: Mock数据文件目录，默认为当前目录下的mock_data
        """
        if mock_data_dir is None:
            mock_data_dir = Path(__file__).parent / "mock_data"

        self.mock_data_dir = Path(mock_data_dir)
        self.mock_data_dir.mkdir(exist_ok=True)

        # 加载默认Mock数据
        self._load_default_mock_data()

    def _load_default_mock_data(self):
        """加载默认的Mock数据"""
        self.default_responses = {
            "baseline_save": {
                "success": True,
                "message": "基准文件保存成功（模拟）",
                "data": {
                    "module_name": "test_module",
                    "saved_fields": 50,
                    "file_path": "/mock/baseline/test_module.json",
                },
            },
            "user_config_save": {
                "success": True,
                "message": "用户配置保存成功（模拟）",
                "data": {
                    "user_id": "test_user",
                    "module_name": "test_module",
                    "saved_fields": 25,
                },
            },
            "api_error": {
                "success": False,
                "message": "API错误（模拟）",
                "error_code": "MOCK_ERROR",
                "details": "这是一个模拟的错误响应",
            },
        }

    def mock_baseline_save_api(self, success: bool = True) -> MockResponse:
        """模拟基准文件保存API响应"""
        if success:
            return MockResponse(200, self.default_responses["baseline_save"])
        else:
            return MockResponse(500, self.default_responses["api_error"])

    def mock_user_config_save_api(self, success: bool = True) -> MockResponse:
        """模拟用户配置保存API响应"""
        if success:
            return MockResponse(
                200, self.default_responses["user_config_save"])
        else:
            return MockResponse(400, self.default_responses["api_error"])

    def mock_field_config_api(
        self, module_name: str, field_count: int = 50
    ) -> MockResponse:
        """模拟字段配置API响应"""
        mock_fields = {}
        for i in range(field_count):
            field_name = f"field_{i:03d}"
            mock_fields[field_name] = {
                "api_field_name": field_name,
                "chinese_name": f"字段{i:03d}",
                "data_type": "NVARCHAR(500)",
                "sample_value": f"示例值{i}",
                "is_selected": i % 3 == 0,  # 每3个选中1个
                "is_required": i % 10 == 0,  # 每10个必填1个
                "business_importance": ["high", "medium", "low"][i % 3],
            }

        response_data = {
            "success": True,
            "data": {
                "module_name": module_name,
                "display_name": f"{module_name}模块",
                "total_fields": field_count,
                "selected_fields": len(
                    [f for f in mock_fields.values() if f["is_selected"]]
                ),
                "fields": mock_fields,
            },
        }

        return MockResponse(200, response_data)

    def mock_network_error(self) -> MockResponse:
        """模拟网络错误"""
        return MockResponse(
            503,
            {
                "success": False,
                "message": "网络连接超时（模拟）",
                "error_code": "NETWORK_TIMEOUT",
            },
        )

    def mock_authentication_error(self) -> MockResponse:
        """模拟认证错误"""
        return MockResponse(
            401,
            {
                "success": False,
                "message": "认证失败（模拟）",
                "error_code": "AUTH_FAILED",
            },
        )

    def save_mock_response(self, name: str, response_data: Dict):
        """保存Mock响应数据到文件"""
        file_path = self.mock_data_dir / f"{name}.json"
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(response_data, f, ensure_ascii=False, indent=2)

    def load_mock_response(self, name: str) -> Optional[Dict]:
        """从文件加载Mock响应数据"""
        file_path = self.mock_data_dir / f"{name}.json"
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None


class EnvironmentManager:
    """环境管理器，统一管理开发/生产环境切换"""

    def __init___(self):
    """TODO: Add function description."""
    self.is_mock_mode = self._detect_mock_mode()
    self.mock_client = MockAPIClient() if self.is_mock_mode else None

    def _detect_mock_mode(self) -> bool:
        """自动检测是否应该使用Mock模式"""
        # 检查环境变量
        if os.getenv('YS_API_MOCK_MODE', '').lower() in ('true', '1', 'yes'):
            return True

        # 检查是否在开发环境（检查文件是否存在）
        dev_indicator_file = Path(__file__).parent.parent.parent / "DEV_MODE"
        if dev_indicator_file.exists():
            return True

        # 默认在测试时使用Mock模式
        return True

    def get_api_response(self, endpoint: str, **kwargs) -> MockResponse:
        """获取API响应（Mock或真实）"""
        if not self.is_mock_mode:
            raise ValueError("非Mock模式下不能使用此方法获取响应")

        # 根据endpoint返回相应的Mock响应
        endpoint_mapping = {
            "baseline_save": self.mock_client.mock_baseline_save_api,
            "user_config_save": self.mock_client.mock_user_config_save_api,
            "field_config": self.mock_client.mock_field_config_api,
            "network_error": self.mock_client.mock_network_error,
            "auth_error": self.mock_client.mock_authentication_error,
        }

        mock_func = endpoint_mapping.get(endpoint)
        if mock_func:
            return mock_func(**kwargs)
        else:
            return MockResponse(404, {"error": f"未知的Mock端点: {endpoint}"})

    def create_dev_mode_file(self):
        """创建开发模式标识文件"""
        dev_file = Path(__file__).parent.parent.parent / "DEV_MODE"
        dev_file.write_text(
            "开发模式标识文件\n创建时间: " + str(Path(__file__).stat().st_mtime)
        )
        self.logger.info(f"✅ 已创建开发模式标识文件: {dev_file}")

    def remove_dev_mode_file(self):
        """移除开发模式标识文件"""
        dev_file = Path(__file__).parent.parent.parent / "DEV_MODE"
        if dev_file.exists():
            dev_file.unlink()
            logger.info(f"✅ 已移除开发模式标识文件: {dev_file}")


# 全局环境管理器实例
env_manager = EnvironmentManager()


# 便捷函数
def is_mock_mode() -> bool:
    """检查是否为Mock模式"""
    return env_manager.is_mock_mode


def get_mock_response(endpoint: str, **kwargs) -> MockResponse:
    """获取Mock响应"""
    return env_manager.get_api_response(endpoint, **kwargs)


def enable_dev_mode():
    """启用开发模式"""
    env_manager.create_dev_mode_file()


def disable_dev_mode():
    """禁用开发模式"""
    env_manager.remove_dev_mode_file()


if __name__ == "__main__":
    # 测试Mock工具
    logger.info("=== Mock工具测试 ===")
    logger.info(f"当前模式: {'Mock模式' if is_mock_mode() else '生产模式'}")

    if is_mock_mode():
        # 测试各种Mock响应
        baseline_response = get_mock_response("baseline_save")
        self.logger.info(f"基准保存响应: {baseline_response.status_code}")
        self.logger.info(f"响应数据: {baseline_response.json()}")

        config_response = get_mock_response(
            "field_config", module_name="test_module", field_count=10
        )
        self.logger.info(f"字段配置响应: {config_response.status_code}")
        self.logger.info(
            f"字段数量: {config_response.json()['data']['total_fields']}")
