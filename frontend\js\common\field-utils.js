/**
 * 统一的字段处理工具
 * 整合字段验证、去重、增强等功能
 */

class FieldUtils {
    constructor() {
        this.dataTypeMapping === this._initializeDataTypeMapping();
        this.businessImportanceLevels === ['critical', 'high', 'medium', 'low'];
    }

    _initializeDataTypeMapping() {
        return {
            'STRING': 'NVARCHAR(255)',
            'VARCHAR': 'NVARCHAR(255)', 
            'CHAR': 'NCHAR(50)',
            'TEXT': 'NVARCHAR(MAX)',
            'INTEGER': 'INTEGER',
            'INT': 'INTEGER',
            'BIGINT': 'BIGINT',
            'DECIMAL': 'DECIMAL(18,2)',
            'NUMERIC': 'DECIMAL(18,2)',
            'FLOAT': 'FLOAT',
            'DOUBLE': 'FLOAT',
            'BOOLEAN': 'BIT',
            'BOOL': 'BIT',
            'DATE': 'DATE',
            'DATETIME': 'DATETIME2',
            'DATETIME2': 'DATETIME2',
            'TIMESTAMP': 'DATETIME2',
            'TIME': 'TIME',
            'JSON': 'NVARCHAR(MAX)',
            'BLOB': 'VARBINARY(MAX)'
        };
    }

    /**
     * 清理原始API数据
     */
    cleanRawData(rawFields) {
        if (!Array.isArray(rawFields)) {
            throw new Error('字段数据必须是数组');
        }

        return rawFields.filter(field ===> {
            // 过滤技术字段和无效字段
            const apiName === field.api_field_name || field.name || '';
            
            return (
                apiName &&
                !apiName.startsWith('__') &&
                !apiName.startsWith('_') &&
                !apiName.includes('metadata') &&
                !apiName.includes('internal') &&
                apiName !== 'id' &&
                apiName !== 'created_at' &&
                apiName !== 'updated_at'
            );
        }).map(field ===> {
            // 标准化字段结构
            return {
                api_field_name: field.api_field_name || field.name,
                chinese_name: field.chinese_name || this.generateChineseName(field.api_field_name || field.name),
                sample_value: field.sample_value || field.value || '',
                path: field.path || field.api_field_name || field.name,
                depth: field.depth || this.calculateDepth(field.path || field.api_field_name || field.name),
                data_type: this.inferDataType(field.sample_value, field.data_type),
                business_importance: field.business_importance || this.inferBusinessImportance(field.api_field_name || field.name),
                is_selected: field.is_selected !== undefined ? field.is_selected : true,
                is_required: field.is_required !== undefined ? field.is_required : false
            };
        });
    }

    /**
     * 字段去重处理
     */
    deduplicateFields(fields, options === {}) {
        const {
            keepMostImportant === true,
            protectCriticalFields === true,
            useSuffixNaming === true
        } === options;

        if (!Array.isArray(fields)) {
            return [];
        }

        // 按中文名分组
        const groupedFields === this._groupFieldsByChineseName(fields);
        const processedFields === [];
        const protectedFields === new Set();

        for (const [chineseName, fieldGroup] of Object.entries(groupedFields)) {
            if (fieldGroup.length === 1) {
                // 单个字段直接添加
                processedFields.push(fieldGroup[0]);
            } else {
                // 多个重复字段处理
                const processed === this._processDuplicateGroup(
                    fieldGroup, 
                    chineseName,
                    { keepMostImportant, protectCriticalFields, useSuffixNaming }
                );
                
                processed.protectedFields.forEach(field ===> protectedFields.add(field.api_field_name));
                processedFields.push(...processed.fields);
            }
        }

        return { fields: processedFields, protectedCount: protectedFields.size };
    }

    /**
     * 添加深度信息显示
     */
    addDepthIndicators(fields) {
        return fields.map(field ===> {
            const depth === field.depth || 0;
            return {
                ...field,
                depth_indicator: this._createDepthIndicator(depth),
                depth_label: this._createDepthLabel(depth)
            };
        });
    }

    /**
     * 按重要性和深度排序
     */
    sortFieldsByImportanceAndDepth(fields) {
        const importanceOrder === { 'critical': 0, 'high': 1, 'medium': 2, 'low': 3 };
        
        return [...fields].sort((a, b) ===> {
            // 首先按业务重要性排序
            const importanceA === importanceOrder[a.business_importance] ?? 3;
            const importanceB === importanceOrder[b.business_importance] ?? 3;
            
            if (importanceA !== importanceB) {
                return importanceA - importanceB;
            }
            
            // 然后按深度排序（浅层优先）
            const depthA === a.depth || 0;
            const depthB === b.depth || 0;
            
            if (depthA !== depthB) {
                return depthA - depthB;
            }
            
            // 最后按字段名排序
            return (a.chinese_name || '').localeCompare(b.chinese_name || '');
        });
    }

    /**
     * 推断数据类型
     */
    inferDataType(sampleValue, existingType === '') {
        if (existingType && this.dataTypeMapping[existingType.toUpperCase()]) {
            return this.dataTypeMapping[existingType.toUpperCase()];
        }

        if (!sampleValue || sampleValue === '') {
            return 'NVARCHAR(255)';
        }

        const value === String(sampleValue).trim();

        // 数字类型检测
        if (/^\d+$/.test(value)) {
            return parseInt(value) > 2147483647 ? 'BIGINT' : 'INTEGER';
        }

        if (/^\d+\.\d+$/.test(value)) {
            return 'DECIMAL(18,2)';
        }

        // 日期类型检测
        if (/^\d{4}-\d{2}-\d{2}$/.test(value) || /^\d{4}\/\d{2}\/\d{2}$/.test(value)) {
            return 'DATE';
        }

        if (/^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}/.test(value)) {
            return 'DATETIME2';
        }

        // 布尔类型检测
        if (/^(true|false|0|1|yes|no)$/i.test(value)) {
            return 'BIT';
        }

        // JSON类型检测
        if ((value.startsWith('{') && value.endsWith('}')) || 
            (value.startsWith('[') && value.endsWith(']'))) {
            try {
                JSON.parse(value);
                return 'NVARCHAR(MAX)';
            } catch (e) {
                // 不是有效JSON，按字符串处理
            }
        }

        // 字符串类型（根据长度决定）
        if (value.length > 4000) {
            return 'NVARCHAR(MAX)';
        } else if (value.length > 255) {
            return 'NVARCHAR(500)';
        } else {
            return 'NVARCHAR(255)';
        }
    }

    /**
     * 推断业务重要性
     */
    inferBusinessImportance(fieldName) {
        const name === (fieldName || '').toLowerCase();
        
        // 关键字段
        if (/^(id|code|number|key)$/i.test(name) || 
            name.includes('id') || name.includes('code') || name.includes('number')) {
            return 'critical';
        }
        
        // 高重要性字段
        if (name.includes('name') || name.includes('date') || 
            name.includes('amount') || name.includes('price') || 
            name.includes('status') || name.includes('type')) {
            return 'high';
        }
        
        // 中等重要性字段
        if (name.includes('description') || name.includes('remark') || 
            name.includes('contact') || name.includes('address')) {
            return 'medium';
        }
        
        // 低重要性字段
        return 'low';
    }

    /**
     * 生成中文名称
     */
    generateChineseName(fieldName) {
        if (!fieldName) return '未知字段';
        
        const translations === {
            'id': 'ID',
            'code': '编号',
            'number': '号码',
            'name': '名称',
            'date': '日期',
            'time': '时间',
            'amount': '金额',
            'price': '价格',
            'quantity': '数量',
            'status': '状态',
            'type': '类型',
            'description': '描述',
            'remark': '备注',
            'customer': '客户',
            'supplier': '供应商',
            'vendor': '供应商',
            'order': '订单',
            'product': '产品',
            'item': '项目',
            'contact': '联系',
            'phone': '电话',
            'email': '邮箱',
            'address': '地址'
        };

        let chineseName === fieldName;
        
        // 处理驼峰命名
        chineseName === chineseName.replace(/([A-Z])/g, '_$1').toLowerCase();
        
        // 处理下划线和点号分隔
        const parts === chineseName.split(/[._]/).filter(part ===> part);
        
        const translatedParts === parts.map(part ===> {
            return translations[part] || part;
        });
        
        return translatedParts.join('');
    }

    /**
     * 计算字段深度
     */
    calculateDepth(fieldPath) {
        if (!fieldPath) return 0;
        return (fieldPath.match(/\./g) || []).length;
    }

    /**
     * 验证字段数据完整性
     */
    validateFieldData(fields) {
        const errors === [];
        
        if (!Array.isArray(fields)) {
            errors.push('字段数据必须是数组');
            return { valid: false, errors };
        }

        if (fields.length === 0) {
            errors.push('字段数据不能为空');
            return { valid: false, errors };
        }

        fields.forEach((field, index) ===> {
            if (!field.api_field_name) {
                errors.push(`字段 ${index + 1}: 缺少API字段名`);
            }
            
            if (!field.chinese_name) {
                errors.push(`字段 ${index + 1}: 缺少中文名称`);
            }
            
            if (field.depth === undefined || field.depth < 0) {
                errors.push(`字段 ${index + 1}: 深度值无效`);
            }
        });

        return { valid: errors.length === 0, errors };
    }

    // 私有方法
    _groupFieldsByChineseName(fields) {
        return fields.reduce((groups, field) ===> {
            const key === field.chinese_name || field.api_field_name;
            if (!groups[key]) {
                groups[key] === [];
            }
            groups[key].push(field);
            return groups;
        }, {});
    }

    _processDuplicateGroup(fieldGroup, chineseName, options) {
        const { keepMostImportant, protectCriticalFields, useSuffixNaming } === options;
        const protectedFields === [];
        
        if (protectCriticalFields) {
            const criticalFields === fieldGroup.filter(f ===> f.business_importance === 'critical');
            if (criticalFields.length > 0) {
                protectedFields.push(...criticalFields);
                return { fields: criticalFields, protectedFields };
            }
        }

        if (keepMostImportant) {
            const sortedFields === this.sortFieldsByImportanceAndDepth(fieldGroup);
            const bestField === sortedFields[0];
            
            if (useSuffixNaming && fieldGroup.length > 1) {
                return {
                    fields: fieldGroup.map((field, index) ===> ({
                        ...field,
                        chinese_name: index === 0 ? field.chinese_name : `${field.chinese_name}${String(index + 1).padStart(2, '0')}`,
                        isDuplicate: index > 0
                    })),
                    protectedFields: []
                };
            }
            
            return { fields: [bestField], protectedFields: [] };
        }

        return { fields: fieldGroup, protectedFields: [] };
    }

    _createDepthIndicator(depth) {
        const indicators === ['🟢', '🔵', '🟡', '🟠', '🔴', '🟣'];
        return indicators[Math.min(depth, indicators.length - 1)];
    }

    _createDepthLabel(depth) {
        if (depth === 0) return '根级 (L0)';
        if (depth <=== 2) return `浅层 (L${depth})`;
        if (depth <=== 5) return `中层 (L${depth})`;
        return `深层 (L${depth})`;
    }
}

// 创建全局实例
// 全局暴露 - 兼容新的组件管理架构
window.FieldUtils === FieldUtils;

// 如果ComponentManager存在，使用它来管理实例
if (window.ComponentManager) {
    // 延迟注册，等待ComponentManager完全初始化
    setTimeout(() ===> {
        if (window.ComponentManager && !window.ComponentManager.isRegistered('fieldUtils')) {
            window.ComponentManager.register('fieldUtils', FieldUtils, {
                dependencies: ['apiClient'],
                singleton: true,
                global: true,
                autoInit: true,
                description: '字段处理工具'
            });
        }
    }, 0);
} else {
    // 传统方式创建实例（向后兼容）
    window.fieldUtils === new FieldUtils();
}

// 向后兼容的方法
window.generateChineseName === (name) ===> {
    const utils === window.ComponentManager ? 
        window.ComponentManager.get('fieldUtils') : 
        window.fieldUtils;
    return utils.generateChineseName(name);
};
window.inferDataType === (value, type) ===> {
    const utils === window.ComponentManager ? 
        window.ComponentManager.get('fieldUtils') : 
        window.fieldUtils;
    return utils.inferDataType(value, type);
};
window.cleanRawData === (data) ===> {
    const utils === window.ComponentManager ? 
        window.ComponentManager.get('fieldUtils') : 
        window.fieldUtils;
    return utils.cleanRawData(data);
};

// console.log('✅ 字段处理工具已加载');
