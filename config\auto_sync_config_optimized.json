{"enabled": true, "sync_mode": "completion_triggered", "sync_hour": 2, "sync_minute": 0, "base_interval_minutes": 5, "max_retries": 3, "retry_delay_minutes": 30, "modules": ["purchase_order", "sales_order", "production_order", "subcontract_order", "applyorder", "subcontract_requisition", "product_receipt", "purchase_receipt", "subcontract_receipt", "materialout", "sales_out", "inventory_report", "requirements_planning"], "force_recreate_tables": false, "record_limit": null, "clear_existing_data": true, "zero_downtime_enabled": true, "zero_downtime_modules": ["purchase_order", "sales_order", "production_order", "material_master", "inventory_report"], "zero_downtime_switch_timeout_ms": 30000, "concurrency_control": {"enabled": true, "max_concurrent_modules": 3, "task_lock_timeout_minutes": 30, "distributed_lock_enabled": true, "lock_key_prefix": "ys_api_sync"}, "dynamic_scheduling": {"enabled": true, "min_interval_minutes": 2, "max_interval_minutes": 15, "adaptive_factor": 1.5, "performance_threshold_ms": 30000}, "resource_monitoring": {"enabled": true, "cpu_threshold_percent": 80, "memory_threshold_percent": 85, "disk_threshold_percent": 90, "check_interval_seconds": 30}, "time_restrictions": {"enabled": true, "pause_start_hour": 0, "pause_end_hour": 7, "description": "每天0点到7点暂停更新（物料档案除外）"}, "completion_tracking": {"enabled": true, "track_last_completion_time": true, "track_sync_duration": true, "track_success_rate": true, "persist_to_database": true}, "error_handling": {"circuit_breaker_enabled": true, "max_failures_before_break": 5, "circuit_breaker_timeout_minutes": 30, "graceful_degradation": true}}