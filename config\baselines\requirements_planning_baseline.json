{"module_name": "requirements_planning", "display_name": "需求计划", "version": "2.0.0", "source": "json_parser", "total_fields": 92, "created_at": "2025-07-28T20:12:24.869144", "last_updated": "2025-07-28T20:12:24.869144", "fields": {"code": {"api_field_name": "code", "chinese_name": "计划订单号", "data_type": "NVARCHAR(500)", "param_desc": "计划订单号", "path": "data.recordList.code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "message": {"api_field_name": "message", "chinese_name": "操作通知信息", "data_type": "NVARCHAR(500)", "param_desc": "操作通知信息", "path": "message", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "数据", "data_type": "NVARCHAR(MAX)", "param_desc": "数据", "path": "data", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordList": {"api_field_name": "recordList", "chinese_name": "数据信息", "data_type": "NVARCHAR(MAX)", "param_desc": "数据信息", "path": "data.recordList", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "id": {"api_field_name": "id", "chinese_name": "计划订单id", "data_type": "NVARCHAR(500)", "param_desc": "计划订单id", "path": "data.recordList.id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orgId": {"api_field_name": "orgId", "chinese_name": "计划组织id", "data_type": "NVARCHAR(500)", "param_desc": "计划组织id", "path": "data.recordList.orgId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "orgCode": {"api_field_name": "orgCode", "chinese_name": "计划组织编码", "data_type": "NVARCHAR(500)", "param_desc": "计划组织编码", "path": "data.recordList.orgCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "transTypeId": {"api_field_name": "transTypeId", "chinese_name": "交易类型id", "data_type": "NVARCHAR(500)", "param_desc": "交易类型id", "path": "data.recordList.transTypeId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "transTypeCode": {"api_field_name": "transTypeCode", "chinese_name": "交易类型编码", "data_type": "NVARCHAR(500)", "param_desc": "交易类型编码", "path": "data.recordList.transTypeCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "planParamId": {"api_field_name": "planParamId", "chinese_name": "计划名称id", "data_type": "NVARCHAR(500)", "param_desc": "计划名称id", "path": "data.recordList.planParamId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "planParamCode": {"api_field_name": "planParamCode", "chinese_name": "计划名称编码", "data_type": "NVARCHAR(500)", "param_desc": "计划名称编码", "path": "data.recordList.planParamCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "planParamName": {"api_field_name": "planParamName", "chinese_name": "计划名称", "data_type": "NVARCHAR(500)", "param_desc": "计划名称", "path": "data.recordList.planParamName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "createType": {"api_field_name": "createType", "chinese_name": "创建类型", "data_type": "NVARCHAR(500)", "param_desc": "创建类型", "path": "data.recordList.createType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productCode": {"api_field_name": "productCode", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "data.recordList.productCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productId": {"api_field_name": "productId", "chinese_name": "物料id", "data_type": "NVARCHAR(500)", "param_desc": "物料id", "path": "data.recordList.productId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "planProperty": {"api_field_name": "planProperty", "chinese_name": "计划属性", "data_type": "NVARCHAR(500)", "param_desc": "计划属性", "path": "data.recordList.planProperty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bomId": {"api_field_name": "bomId", "chinese_name": "BOM唯一标识", "data_type": "NVARCHAR(500)", "param_desc": "BOM唯一标识", "path": "data.recordList.bomId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bomCode": {"api_field_name": "bomCode", "chinese_name": "BOM编码", "data_type": "NVARCHAR(500)", "param_desc": "BOM编码", "path": "data.recordList.bomCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "uom": {"api_field_name": "uom", "chinese_name": "单位", "data_type": "NVARCHAR(500)", "param_desc": "单位", "path": "data.recordList.uom", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "uomName": {"api_field_name": "uomName", "chinese_name": "单位名称", "data_type": "NVARCHAR(500)", "param_desc": "单位名称", "path": "data.recordList.uomName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "uomCode": {"api_field_name": "uomCode", "chinese_name": "单位编码", "data_type": "NVARCHAR(500)", "param_desc": "单位编码", "path": "data.recordList.uomCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "assistUnit": {"api_field_name": "assistUnit", "chinese_name": "主计量单位", "data_type": "NVARCHAR(500)", "param_desc": "主计量单位", "path": "data.recordList.assistUnit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "assistUnitCode": {"api_field_name": "assistUnitCode", "chinese_name": "主计量单位编码", "data_type": "NVARCHAR(500)", "param_desc": "主计量单位编码", "path": "data.recordList.assistUnitCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "assistUnitName": {"api_field_name": "assistUnitName", "chinese_name": "主计量单位名称", "data_type": "NVARCHAR(500)", "param_desc": "主计量单位名称", "path": "data.recordList.assistUnitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "originQuantity": {"api_field_name": "originQuantity", "chinese_name": "原始数量", "data_type": "NVARCHAR(500)", "param_desc": "原始数量", "path": "data.recordList.originQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "assistUnitCount": {"api_field_name": "assistUnitCount", "chinese_name": "主计量计划量", "data_type": "NVARCHAR(500)", "param_desc": "主计量计划量", "path": "data.recordList.assistUnitCount", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "suggestPlanQuantity": {"api_field_name": "suggestPlanQuantity", "chinese_name": "建议计划量", "data_type": "NVARCHAR(500)", "param_desc": "建议计划量", "path": "data.recordList.suggestPlanQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "inputQty": {"api_field_name": "inputQty", "chinese_name": "投入计划量", "data_type": "NVARCHAR(500)", "param_desc": "投入计划量", "path": "data.recordList.inputQty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "issuedQuantity": {"api_field_name": "issuedQuantity", "chinese_name": "已下达量", "data_type": "NVARCHAR(500)", "param_desc": "已下达量", "path": "data.recordList.issuedQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "startDate": {"api_field_name": "startDate", "chinese_name": "开工日期", "data_type": "NVARCHAR(500)", "param_desc": "开工日期", "path": "data.recordList.startDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "finishDate": {"api_field_name": "finishDate", "chinese_name": "完工日期", "data_type": "NVARCHAR(500)", "param_desc": "完工日期", "path": "data.recordList.finishDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "status": {"api_field_name": "status", "chinese_name": "状态", "data_type": "NVARCHAR(500)", "param_desc": "状态", "path": "data.recordList.status", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "demandOrgId": {"api_field_name": "demandOrgId", "chinese_name": "需求组织", "data_type": "NVARCHAR(500)", "param_desc": "需求组织", "path": "data.recordList.demandOrgId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "demandOrgCode": {"api_field_name": "demandOrgCode", "chinese_name": "需求组织编码", "data_type": "NVARCHAR(500)", "param_desc": "需求组织编码", "path": "data.recordList.demandOrgCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "supplyOrgId": {"api_field_name": "supplyOrgId", "chinese_name": "供应组织", "data_type": "NVARCHAR(500)", "param_desc": "供应组织", "path": "data.recordList.supplyOrgId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "supplyOrgCode": {"api_field_name": "supplyOrgCode", "chinese_name": "供应组织编码", "data_type": "NVARCHAR(500)", "param_desc": "供应组织编码", "path": "data.recordList.supplyOrgCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invOrgId": {"api_field_name": "invOrgId", "chinese_name": "入库组织", "data_type": "NVARCHAR(500)", "param_desc": "入库组织", "path": "data.recordList.invOrgId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invOrgCode": {"api_field_name": "invOrgCode", "chinese_name": "入库组织编码", "data_type": "NVARCHAR(500)", "param_desc": "入库组织编码", "path": "data.recordList.invOrgCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "source": {"api_field_name": "source", "chinese_name": "来源单据类型", "data_type": "NVARCHAR(500)", "param_desc": "来源单据类型", "path": "data.recordList.source", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "upcode": {"api_field_name": "upcode", "chinese_name": "来源单据号", "data_type": "NVARCHAR(500)", "param_desc": "来源单据号", "path": "data.recordList.upcode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "srcSourceProductId": {"api_field_name": "srcSourceProductId", "chinese_name": "来源物料id", "data_type": "NVARCHAR(500)", "param_desc": "来源物料id", "path": "data.recordList.srcSourceProductId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "srcSourceProductCode": {"api_field_name": "srcSourceProductCode", "chinese_name": "来源物料编码", "data_type": "NVARCHAR(500)", "param_desc": "来源物料编码", "path": "data.recordList.srcSourceProductCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "firstsource": {"api_field_name": "firstsource", "chinese_name": "源头单据类型", "data_type": "NVARCHAR(500)", "param_desc": "源头单据类型", "path": "data.recordList.firstsource", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "firstupcode": {"api_field_name": "firstupcode", "chinese_name": "源头单据号", "data_type": "NVARCHAR(500)", "param_desc": "源头单据号", "path": "data.recordList.firstupcode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "firstsourceautoid": {"api_field_name": "firstsourceautoid", "chinese_name": "源头单据子表id", "data_type": "NVARCHAR(500)", "param_desc": "源头单据子表id", "path": "data.recordList.firstsourceautoid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "sourceMaterialId": {"api_field_name": "sourceMaterialId", "chinese_name": "源头物料", "data_type": "NVARCHAR(500)", "param_desc": "源头物料", "path": "data.recordList.sourceMaterialId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "sourceMaterialCode": {"api_field_name": "sourceMaterialCode", "chinese_name": "源头物料编码", "data_type": "NVARCHAR(500)", "param_desc": "源头物料编码", "path": "data.recordList.sourceMaterialCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "departmentId": {"api_field_name": "departmentId", "chinese_name": "部门id", "data_type": "NVARCHAR(500)", "param_desc": "部门id", "path": "data.recordList.departmentId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "departmentCode": {"api_field_name": "departmentCode", "chinese_name": "部门编码", "data_type": "NVARCHAR(500)", "param_desc": "部门编码", "path": "data.recordList.departmentCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "departmentName": {"api_field_name": "departmentName", "chinese_name": "部门名称", "data_type": "NVARCHAR(500)", "param_desc": "部门名称", "path": "data.recordList.departmentName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehouseId": {"api_field_name": "warehouseId", "chinese_name": "仓库id", "data_type": "NVARCHAR(500)", "param_desc": "仓库id", "path": "data.recordList.planOrderItem.warehouseId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "warehouseCode": {"api_field_name": "warehouseCode", "chinese_name": "仓库编码", "data_type": "NVARCHAR(500)", "param_desc": "仓库编码", "path": "data.recordList.planOrderItem.warehouseCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "warehouseName": {"api_field_name": "warehouseName", "chinese_name": "仓库名称", "data_type": "NVARCHAR(500)", "param_desc": "仓库名称", "path": "data.recordList.planOrderItem.warehouseName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isClosed": {"api_field_name": "isClosed", "chinese_name": "关闭标识", "data_type": "BIT", "param_desc": "关闭标识", "path": "data.recordList.isClosed", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "remark": {"api_field_name": "remark", "chinese_name": "备注", "data_type": "NVARCHAR(500)", "param_desc": "备注", "path": "data.recordList.planOrderItem.remark", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "projectId": {"api_field_name": "projectId", "chinese_name": "项目id", "data_type": "NVARCHAR(500)", "param_desc": "项目id", "path": "data.recordList.planOrderItem.projectId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "projectIdCode": {"api_field_name": "projectIdCode", "chinese_name": "项目编码", "data_type": "NVARCHAR(500)", "param_desc": "项目编码", "path": "data.recordList.planOrderItem.projectIdCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "projectIdName": {"api_field_name": "projectIdName", "chinese_name": "项目名称", "data_type": "NVARCHAR(500)", "param_desc": "项目名称", "path": "data.recordList.planOrderItem.projectIdName", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "wbs": {"api_field_name": "wbs", "chinese_name": "wbs任务id", "data_type": "NVARCHAR(500)", "param_desc": "wbs任务id", "path": "data.recordList.planOrderItem.wbs", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "wbsCode": {"api_field_name": "wbsCode", "chinese_name": "wbs任务编码", "data_type": "NVARCHAR(500)", "param_desc": "wbs任务编码", "path": "data.recordList.planOrderItem.wbsCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "wbsName": {"api_field_name": "wbsName", "chinese_name": "wbs任务名称", "data_type": "NVARCHAR(500)", "param_desc": "wbs任务名称", "path": "data.recordList.planOrderItem.wbsName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "activity": {"api_field_name": "activity", "chinese_name": "活动id", "data_type": "NVARCHAR(500)", "param_desc": "活动id", "path": "data.recordList.planOrderItem.activity", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "activityCode": {"api_field_name": "activityCode", "chinese_name": "活动编码", "data_type": "NVARCHAR(500)", "param_desc": "活动编码", "path": "data.recordList.planOrderItem.activityCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "activityName": {"api_field_name": "activityName", "chinese_name": "活动名称", "data_type": "NVARCHAR(500)", "param_desc": "活动名称", "path": "data.recordList.planOrderItem.activityName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "planOrderItem": {"api_field_name": "planOrderItem", "chinese_name": "计划订单备料", "data_type": "NVARCHAR(MAX)", "param_desc": "计划订单备料", "path": "data.recordList.planOrderItem", "depth": 2, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "itemProductId": {"api_field_name": "itemProductId", "chinese_name": "物料id", "data_type": "NVARCHAR(500)", "param_desc": "物料id", "path": "data.recordList.planOrderItem.itemProductId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "itemProductCode": {"api_field_name": "itemProductCode", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "data.recordList.planOrderItem.itemProductCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "itemProductName": {"api_field_name": "itemProductName", "chinese_name": "物料名称", "data_type": "NVARCHAR(500)", "param_desc": "物料名称", "path": "data.recordList.planOrderItem.itemProductName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "mainUnitId": {"api_field_name": "mainUnitId", "chinese_name": "主计量id", "data_type": "NVARCHAR(500)", "param_desc": "主计量id", "path": "data.recordList.planOrderItem.mainUnitId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "mainUnitCode": {"api_field_name": "mainUnitCode", "chinese_name": "主计量编码", "data_type": "NVARCHAR(500)", "param_desc": "主计量编码", "path": "data.recordList.planOrderItem.mainUnitCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "mainUnitName": {"api_field_name": "mainUnitName", "chinese_name": "主计量名称", "data_type": "NVARCHAR(500)", "param_desc": "主计量名称", "path": "data.recordList.planOrderItem.mainUnitName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockUnitId": {"api_field_name": "stockUnitId", "chinese_name": "BOM单位id", "data_type": "NVARCHAR(500)", "param_desc": "BOM单位id", "path": "data.recordList.planOrderItem.stockUnitId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockUnitCode": {"api_field_name": "stockUnitCode", "chinese_name": "BOM单位编码", "data_type": "NVARCHAR(500)", "param_desc": "BOM单位编码", "path": "data.recordList.planOrderItem.stockUnitCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockUnitName": {"api_field_name": "stockUnitName", "chinese_name": "BOM单位名称", "data_type": "NVARCHAR(500)", "param_desc": "BOM单位名称", "path": "data.recordList.planOrderItem.stockUnitName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "changeRate": {"api_field_name": "changeRate", "chinese_name": "换算率", "data_type": "NVARCHAR(500)", "param_desc": "换算率", "path": "data.recordList.planOrderItem.changeRate", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "requirementQuantity": {"api_field_name": "requirementQuantity", "chinese_name": "需求数量", "data_type": "NVARCHAR(500)", "param_desc": "需求数量", "path": "data.recordList.planOrderItem.requirementQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auxiliaryRequirementQuantity": {"api_field_name": "auxiliaryRequirementQuantity", "chinese_name": "需求件数", "data_type": "NVARCHAR(500)", "param_desc": "需求件数", "path": "data.recordList.planOrderItem.auxiliaryRequirementQuantity", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockOrgId": {"api_field_name": "stockOrgId", "chinese_name": "库存单位id", "data_type": "NVARCHAR(500)", "param_desc": "库存单位id", "path": "data.recordList.planOrderItem.stockOrgId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockOrgCode": {"api_field_name": "stockOrgCode", "chinese_name": "库存单位编码", "data_type": "NVARCHAR(500)", "param_desc": "库存单位编码", "path": "data.recordList.planOrderItem.stockOrgCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockOrgName": {"api_field_name": "stockOrgName", "chinese_name": "库存单位名称", "data_type": "NVARCHAR(500)", "param_desc": "库存单位名称", "path": "data.recordList.planOrderItem.stockOrgName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "reqDate": {"api_field_name": "reqDate", "chinese_name": "需求日期", "data_type": "DATE", "param_desc": "需求日期", "path": "data.recordList.planOrderItem.reqDate", "depth": 3, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "substituteFlag": {"api_field_name": "substituteFlag", "chinese_name": "替代标识", "data_type": "NVARCHAR(500)", "param_desc": "替代标识", "path": "data.recordList.planOrderItem.substituteFlag", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "reserveid": {"api_field_name": "reserveid", "chinese_name": "跟踪线索id", "data_type": "NVARCHAR(500)", "param_desc": "跟踪线索id", "path": "data.recordList.planOrderItem.reserveid", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "reserveTypeName": {"api_field_name": "reserveTypeName", "chinese_name": "需求跟踪方式", "data_type": "NVARCHAR(500)", "param_desc": "需求跟踪方式", "path": "data.recordList.planOrderItem.reserveTypeName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "reserveName": {"api_field_name": "reserveName", "chinese_name": "跟踪线索", "data_type": "NVARCHAR(500)", "param_desc": "跟踪线索", "path": "data.recordList.planOrderItem.reserveName", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageIndex": {"api_field_name": "pageIndex", "chinese_name": "当前页码", "data_type": "NVARCHAR(500)", "param_desc": "当前页码", "path": "data.pageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageSize": {"api_field_name": "pageSize", "chinese_name": "每页条数", "data_type": "NVARCHAR(500)", "param_desc": "每页条数", "path": "data.pageSize", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordCount": {"api_field_name": "recordCount", "chinese_name": "总条数", "data_type": "NVARCHAR(500)", "param_desc": "总条数", "path": "data.recordCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageCount": {"api_field_name": "pageCount", "chinese_name": "页码数", "data_type": "NVARCHAR(500)", "param_desc": "页码数", "path": "data.pageCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "beginPageIndex": {"api_field_name": "beginPageIndex", "chinese_name": "开始页码", "data_type": "NVARCHAR(500)", "param_desc": "开始页码", "path": "data.beginPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "endPageIndex": {"api_field_name": "endPageIndex", "chinese_name": "结束页码", "data_type": "NVARCHAR(500)", "param_desc": "结束页码", "path": "data.endPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}}}