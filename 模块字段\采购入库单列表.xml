<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>3f6ee57a6bcc435dad3c91b7d1a06dcd</id>
<name>采购入库列表查询</name>
<apiClassifyId>abfbb30ce25c4349b140bde94ff19059</apiClassifyId>
<apiClassifyName>采购入库单</apiClassifyName>
<apiClassifyCode/>
<parentApiClassifies/>
<functionId/>
<openMode>0</openMode>
<description>采购入库列表查询</description>
<auth>true</auth>
<bodyPassthrough>false</bodyPassthrough>
<healthExam>false</healthExam>
<healthStatus>true</healthStatus>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/scm/purinrecord/list</completeProxyUrl>
<connectUrl>/bill/list</connectUrl>
<sort>20</sort>
<handler>openapi</handler>
<httpRequestType>POST</httpRequestType>
<openApi>true</openApi>
<preset>false</preset>
<productId>710a0be3edff4f9092e35f63fd3b9bae</productId>
<productCode>scm</productCode>
<proxyUrl>/yonbip/scm/purinrecord/list</proxyUrl>
<requestParamsDemo>Url: /yonbip/scm/purinrecord/list?access_token=访问令牌 Body: { "pageIndex": 0, "code": "", "pageSize": 0, "bustype_name": "", "warehouse_name": "", "vendor_name": "", "org_id": [ "" ], "org_name": "", "org_code": [ "" ], "purchaseOrg_name": [ "" ], "inInvoiceOrg_name": [ "" ], "stockMgr_name": [ "" ], "operator_name": [ "" ], "department_name": [ "" ], "project_name": [ "" ], "product.productClass.name": [ 0 ], "pocode": "", "product_cName": [ 0 ], "open_vouchdate_begin": "", "open_vouchdate_end": "", "isSum": false, "simpleVOs": [ { "field": "", "op": "", "value1": "", "value2": "" } ] }</requestParamsDemo>
<requestProtocol>HTTP</requestProtocol>
<serviceHttpMethod>POST</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2020-01-16 17:17:58</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/purinrecord/list</address>
<productName>采购供应</productName>
<productClassifyId>yonsuite</productClassifyId>
<productClassifyCode>yonbip</productClassifyCode>
<productClassifyName>用友 YonBIP</productClassifyName>
<paramDTOS>
<paramDTOS>
<id>2081374351925444620</id>
<name>pageIndex</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435016</defParamId>
<array>false</array>
<paramDesc>页号</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>1</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444621</id>
<name>code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435017</defParamId>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444622</id>
<name>pageSize</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435018</defParamId>
<array>false</array>
<paramDesc>每页行数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>10</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444623</id>
<name>bustype_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435019</defParamId>
<array>false</array>
<paramDesc>交易类型，需传入交易类型id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444624</id>
<name>warehouse_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435020</defParamId>
<array>false</array>
<paramDesc>仓库，需传入仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444625</id>
<name>vendor_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435021</defParamId>
<array>false</array>
<paramDesc>供应商，需传入供应商名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444626</id>
<name>org_id</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435022</defParamId>
<array>true</array>
<paramDesc>库存组织id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444627</id>
<name>org_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435023</defParamId>
<array>false</array>
<paramDesc>库存组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444628</id>
<name>org_code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435024</defParamId>
<array>true</array>
<paramDesc>库存组织编码</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444629</id>
<name>purchaseOrg_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435025</defParamId>
<array>true</array>
<paramDesc>采购组织，需传入采购组织id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444630</id>
<name>inInvoiceOrg_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435026</defParamId>
<array>true</array>
<paramDesc>收票组织，需传入收票组织id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444631</id>
<name>stockMgr_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435027</defParamId>
<array>true</array>
<paramDesc>库管员，需传入库管员id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444632</id>
<name>operator_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435028</defParamId>
<array>true</array>
<paramDesc>业务员，需传入业务员id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444633</id>
<name>department_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435029</defParamId>
<array>true</array>
<paramDesc>部门，需传部门id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444634</id>
<name>project_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435030</defParamId>
<array>true</array>
<paramDesc>项目，需传入项目id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444635</id>
<name>product.productClass.name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435031</defParamId>
<array>true</array>
<paramDesc>物料分类，需传入物料分类id</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444636</id>
<name>pocode</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435032</defParamId>
<array>false</array>
<paramDesc>源头单据编码</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444637</id>
<name>product_cName</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435033</defParamId>
<array>true</array>
<paramDesc>物料，需传入物料id</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444638</id>
<name>open_vouchdate_begin</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435034</defParamId>
<array>false</array>
<paramDesc>开始时间，日期格式：YYYY-MM-DD</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444639</id>
<name>open_vouchdate_end</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435035</defParamId>
<array>false</array>
<paramDesc>结束时间，日期格式：YYYY-MM-DD</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444640</id>
<name>isSum</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435036</defParamId>
<array>false</array>
<paramDesc>查询表头</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>false</example>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>false</defaultValue>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2081374351925444615</id>
<name>simpleVOs</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<children>
<children>
<id>2081374351925444616</id>
<name>field</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444615</parentId>
<defParamId>1856790202779435038</defParamId>
<array>false</array>
<paramDesc>属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码purInRecords.product.cCode、表头自定义项headItem.define1、表体自定义项purInRecords.bodyItem.define1等)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2081374351925444617</id>
<name>op</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444615</parentId>
<defParamId>1856790202779435039</defParamId>
<array>false</array>
<paramDesc>比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2081374351925444618</id>
<name>value1</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444615</parentId>
<defParamId>1856790202779435040</defParamId>
<array>false</array>
<paramDesc>值1(条件)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2081374351925444619</id>
<name>value2</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444615</parentId>
<defParamId>1856790202779435041</defParamId>
<array>false</array>
<paramDesc>值2(条件)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>1856790202779435037</defParamId>
<array>true</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444646</id>
<name>pageIndex</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>页号</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageIndex</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444647</id>
<name>code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>code</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444648</id>
<name>pageSize</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>每页行数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageSize</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444649</id>
<name>bustype_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>交易类型，需传入交易类型id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>bustype_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444650</id>
<name>warehouse_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>仓库，需传入仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>warehouse_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444651</id>
<name>vendor_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>供应商，需传入供应商名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>vendor_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444652</id>
<name>org_id</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>库存组织id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>org_id</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444653</id>
<name>org_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>库存组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>org_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444654</id>
<name>org_code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>库存组织编码</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>org_code</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444655</id>
<name>purchaseOrg_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>采购组织，需传入采购组织id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>purchaseOrg_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444656</id>
<name>inInvoiceOrg_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>收票组织，需传入收票组织id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>inInvoiceOrg_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444657</id>
<name>stockMgr_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>库管员，需传入库管员id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>stockMgr_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444658</id>
<name>operator_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>业务员，需传入业务员id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>operator_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444659</id>
<name>department_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>部门，需传部门id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>department_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444660</id>
<name>project_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>项目，需传入项目id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>project_name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444661</id>
<name>product.productClass.name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料分类，需传入物料分类id</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product.productClass.name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444662</id>
<name>pocode</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>源头单据编码</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pocode</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444663</id>
<name>product_cName</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料，需传入物料id</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product_cName</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444664</id>
<name>open_vouchdate_begin</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>开始时间，日期格式：YYYY-MM-DD</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_vouchdate_begin</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444665</id>
<name>open_vouchdate_end</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>结束时间，日期格式：YYYY-MM-DD</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_vouchdate_end</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444666</id>
<name>isSum</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>查询表头</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>isSum</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>boolean</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2081374351925444641</id>
<name>simpleVOs</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<children>
<children>
<id>2081374351925444642</id>
<name>field</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444641</parentId>
<defParamId/>
<array>false</array>
<paramDesc>属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码purInRecords.product.cCode、表头自定义项headItem.define1、表体自定义项purInRecords.bodyItem.define1等)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444643</id>
<name>op</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444641</parentId>
<defParamId/>
<array>false</array>
<paramDesc>比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>op</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444644</id>
<name>value1</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444641</parentId>
<defParamId/>
<array>false</array>
<paramDesc>值1(条件)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value1</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444645</id>
<name>value2</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444641</parentId>
<defParamId/>
<array>false</array>
<paramDesc>值2(条件)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value2</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>simpleVOs</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>2081374351925444827</id>
<name>code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435068</defParamId>
<array>false</array>
<paramDesc>返回码，调用成功时返回200</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2081374351925444828</id>
<name>message</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<defParamId>1856790202779435069</defParamId>
<array>false</array>
<paramDesc>调用失败时的错误信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2081374351925444667</id>
<name>data</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<children>
<children>
<id>2081374351925444793</id>
<name>pageIndex</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444667</parentId>
<defParamId>1856790202779435071</defParamId>
<array>false</array>
<paramDesc>当前页数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444794</id>
<name>pageSize</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444667</parentId>
<defParamId>1856790202779435072</defParamId>
<array>false</array>
<paramDesc>当前页数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444795</id>
<name>pageCount</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444667</parentId>
<defParamId>1856790202779435073</defParamId>
<array>false</array>
<paramDesc>页面数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444796</id>
<name>beginPageIndex</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444667</parentId>
<defParamId>1856790202779435074</defParamId>
<array>false</array>
<paramDesc>开始页码（第一页）</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444797</id>
<name>endPageIndex</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444667</parentId>
<defParamId>1856790202779435075</defParamId>
<array>false</array>
<paramDesc>结束页码（有多少页）</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444798</id>
<name>recordCount</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444667</parentId>
<defParamId>1856790202779435076</defParamId>
<array>false</array>
<paramDesc>总数</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444799</id>
<name>pubts</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444667</parentId>
<defParamId>1856790202779435077</defParamId>
<array>false</array>
<paramDesc>时间戳字符串</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444668</id>
<name>recordList</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444667</parentId>
<children>
<children>
<id>2081374351925444669</id>
<name>vouchdate</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435079</defParamId>
<array>false</array>
<paramDesc>单据日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444670</id>
<name>code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435080</defParamId>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444671</id>
<name>bustype_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435081</defParamId>
<array>false</array>
<paramDesc>交易类型名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;bustype.name&quot;,&quot;cItemName&quot;:&quot;bustype_name&quot;,&quot;cCaption&quot;:&quot;交易类型&quot;,&quot;cShowCaption&quot;:&quot;交易类型&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_user&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:null,&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;Column&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444672</id>
<name>vendor_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435082</defParamId>
<array>false</array>
<paramDesc>供应商名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;vendor.name&quot;,&quot;cItemName&quot;:&quot;vendor_name&quot;,&quot;cCaption&quot;:&quot;供应商&quot;,&quot;cShowCaption&quot;:&quot;供应商&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_user&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:null,&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;Column&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444673</id>
<name>warehouse_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435083</defParamId>
<array>false</array>
<paramDesc>仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;warehouse.name&quot;,&quot;cItemName&quot;:&quot;warehouse_name&quot;,&quot;cCaption&quot;:&quot;仓库&quot;,&quot;cShowCaption&quot;:&quot;仓库&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_warehouse&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:null,&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;Column&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444674</id>
<name>vendor_code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435084</defParamId>
<array>false</array>
<paramDesc>供应商编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;vendor.code&quot;,&quot;cItemName&quot;:&quot;vendor_code&quot;,&quot;cCaption&quot;:&quot;供应商编码&quot;,&quot;cShowCaption&quot;:&quot;供应商编码&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_user&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:null,&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;Column&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444675</id>
<name>warehouse_code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435085</defParamId>
<array>false</array>
<paramDesc>仓库编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;warehouse.code&quot;,&quot;cItemName&quot;:&quot;warehouse_code&quot;,&quot;cCaption&quot;:&quot;仓库编码&quot;,&quot;cShowCaption&quot;:&quot;仓库编码&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_warehouse&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:null,&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;Column&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444676</id>
<name>stockMgr_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435086</defParamId>
<array>false</array>
<paramDesc>库管员名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;stockMgr.name&quot;,&quot;cItemName&quot;:&quot;stockMgr_name&quot;,&quot;cCaption&quot;:&quot;库管员&quot;,&quot;cShowCaption&quot;:&quot;库管员&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;ucf-staff-center.bd_staff_outer_ref&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;stockMgr&quot;:&quot;id&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;Refer&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444677</id>
<name>status</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435087</defParamId>
<array>false</array>
<paramDesc>单据状态, 0:未提交、1:已提交、</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444678</id>
<name>purchaseOrg_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435088</defParamId>
<array>false</array>
<paramDesc>采购组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;purchaseOrg.name&quot;,&quot;cItemName&quot;:&quot;purchaseOrg_name&quot;,&quot;cCaption&quot;:&quot;采购组织&quot;,&quot;cShowCaption&quot;:&quot;采购组织&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_orgtree&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;purchaseOrg&quot;:&quot;id&quot;},&quot;cDataRule&quot;:&quot;\&quot;&lt;%u8c-config.option.singleOrg%&gt;\&quot;==\&quot;false\&quot;&quot;,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;Refer&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444679</id>
<name>department_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435089</defParamId>
<array>false</array>
<paramDesc>部门名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;department.name&quot;,&quot;cItemName&quot;:&quot;department_name&quot;,&quot;cCaption&quot;:&quot;部门&quot;,&quot;cShowCaption&quot;:&quot;部门&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;ucf-org-center.bd_adminorgsharetreeref&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;department&quot;:&quot;id&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;TreeRefer&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444680</id>
<name>org_code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435090</defParamId>
<array>false</array>
<paramDesc>库存组织编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444681</id>
<name>org_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435091</defParamId>
<array>false</array>
<paramDesc>库存组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;org.name&quot;,&quot;cItemName&quot;:&quot;org_name&quot;,&quot;cCaption&quot;:&quot;库存组织&quot;,&quot;cShowCaption&quot;:&quot;库存组织&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_orgtree&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;org&quot;:&quot;id&quot;},&quot;cDataRule&quot;:&quot;\&quot;&lt;%u8c-config.option.singleOrg%&gt;\&quot;==\&quot;false\&quot;&quot;,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;Refer&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444682</id>
<name>department_code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435092</defParamId>
<array>false</array>
<paramDesc>部门编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;department.code&quot;,&quot;cItemName&quot;:&quot;department_code&quot;,&quot;cCaption&quot;:&quot;部门编码&quot;,&quot;cShowCaption&quot;:&quot;部门编码&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;ucf-org-center.bd_adminorgsharetreeref&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;department&quot;:&quot;id&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;TreeRefer&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444683</id>
<name>operator_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435093</defParamId>
<array>false</array>
<paramDesc>经办人名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;operator.name&quot;,&quot;cItemName&quot;:&quot;operator_name&quot;,&quot;cCaption&quot;:&quot;经办人&quot;,&quot;cShowCaption&quot;:&quot;经办人&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_user&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:null,&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;Column&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444684</id>
<name>totalQuantity</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435094</defParamId>
<array>false</array>
<paramDesc>整单数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444685</id>
<name>totalPieces</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435095</defParamId>
<array>false</array>
<paramDesc>整单件数</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444686</id>
<name>inInvoiceOrg_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435096</defParamId>
<array>false</array>
<paramDesc>收票组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;inInvoiceOrg.name&quot;,&quot;cItemName&quot;:&quot;inInvoiceOrg_name&quot;,&quot;cCaption&quot;:&quot;收票组织&quot;,&quot;cShowCaption&quot;:&quot;收票组织&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_orgtree&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;inInvoiceOrg&quot;:&quot;id&quot;},&quot;cDataRule&quot;:&quot;\&quot;&lt;%u8c-config.option.singleOrg%&gt;\&quot;==\&quot;false\&quot;&quot;,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;Refer&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444687</id>
<name>inInvoiceOrg</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435097</defParamId>
<array>false</array>
<paramDesc>收票组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444688</id>
<name>accountOrg</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435098</defParamId>
<array>false</array>
<paramDesc>会计主体</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444689</id>
<name>isBeginning</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435099</defParamId>
<array>false</array>
<paramDesc>是否期初, true:是、false:否、</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444690</id>
<name>bustype</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435100</defParamId>
<array>false</array>
<paramDesc>业务类型id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444691</id>
<name>vendor</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435101</defParamId>
<array>false</array>
<paramDesc>供应商id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444692</id>
<name>contact</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435102</defParamId>
<array>false</array>
<paramDesc>联系人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;contact&quot;,&quot;cItemName&quot;:&quot;contact&quot;,&quot;cCaption&quot;:&quot;联系人&quot;,&quot;cShowCaption&quot;:&quot;联系人&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_user&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:null,&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;Column&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:{&quot;id&quot;:&quot;String&quot;,&quot;metaType&quot;:&quot;PrimitiveType&quot;,&quot;name&quot;:&quot;字符型&quot;,&quot;uri&quot;:&quot;String&quot;},&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444693</id>
<name>warehouse</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435103</defParamId>
<array>false</array>
<paramDesc>仓库id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444694</id>
<name>operator</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435104</defParamId>
<array>false</array>
<paramDesc>经办人id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444695</id>
<name>purchaseOrg</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435105</defParamId>
<array>false</array>
<paramDesc>采购组织IDid</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444696</id>
<name>org</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435106</defParamId>
<array>false</array>
<paramDesc>库存组织id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444697</id>
<name>department</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435107</defParamId>
<array>false</array>
<paramDesc>部门id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444698</id>
<name>stockMgr</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435108</defParamId>
<array>false</array>
<paramDesc>库管员IDid</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444699</id>
<name>moneysum</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435109</defParamId>
<array>false</array>
<paramDesc>金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444700</id>
<name>paymentsum</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435110</defParamId>
<array>false</array>
<paramDesc>付款金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444701</id>
<name>unpaymentsum</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435111</defParamId>
<array>false</array>
<paramDesc>未付款金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444702</id>
<name>store</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435112</defParamId>
<array>false</array>
<paramDesc>门店id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444703</id>
<name>store_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435113</defParamId>
<array>false</array>
<paramDesc>门店名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;store.name&quot;,&quot;cItemName&quot;:&quot;store_name&quot;,&quot;cCaption&quot;:&quot;门店&quot;,&quot;cShowCaption&quot;:&quot;门店&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_department&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:null,&quot;cDataRule&quot;:&quot;\&quot;&lt;%productcenter.option.isOpenURetail%&gt;\&quot;==\&quot;true\&quot;&quot;,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;Column&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444704</id>
<name>custom</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435114</defParamId>
<array>false</array>
<paramDesc>客户id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444705</id>
<name>payor</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435115</defParamId>
<array>false</array>
<paramDesc>付款人id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444706</id>
<name>payor_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435116</defParamId>
<array>false</array>
<paramDesc>付款人名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;payor.name&quot;,&quot;cItemName&quot;:&quot;payor_name&quot;,&quot;cCaption&quot;:&quot;付款人&quot;,&quot;cShowCaption&quot;:&quot;付款人&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:true,&quot;cRefType&quot;:&quot;aa_user&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:null,&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;Column&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444707</id>
<name>paytime</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435117</defParamId>
<array>false</array>
<paramDesc>付款时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444708</id>
<name>paymentstatus</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435118</defParamId>
<array>false</array>
<paramDesc>付款状态, 0:未完成、1:完成、</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444709</id>
<name>creator</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435119</defParamId>
<array>false</array>
<paramDesc>创建人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444710</id>
<name>createTime</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435120</defParamId>
<array>false</array>
<paramDesc>创建时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444711</id>
<name>modifier</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435121</defParamId>
<array>false</array>
<paramDesc>最后修改人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444712</id>
<name>modifyTime</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435122</defParamId>
<array>false</array>
<paramDesc>最后修改时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444713</id>
<name>auditor</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435123</defParamId>
<array>false</array>
<paramDesc>提交人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444714</id>
<name>auditTime</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435124</defParamId>
<array>false</array>
<paramDesc>提交时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444715</id>
<name>memo</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435125</defParamId>
<array>false</array>
<paramDesc>备注</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444716</id>
<name>id</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435126</defParamId>
<array>false</array>
<paramDesc>主表ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444717</id>
<name>srcBill</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435127</defParamId>
<array>false</array>
<paramDesc>来源单据id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444718</id>
<name>pubts</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435128</defParamId>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444719</id>
<name>tplid</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435129</defParamId>
<array>false</array>
<paramDesc>模板id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444720</id>
<name>exchangestatus</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435130</defParamId>
<array>false</array>
<paramDesc>交换状态</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444721</id>
<name>purInRecords_id</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435131</defParamId>
<array>false</array>
<paramDesc>订单行id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444722</id>
<name>product</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435132</defParamId>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444723</id>
<name>product_cCode</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435133</defParamId>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;purInRecords.product.cCode&quot;,&quot;cItemName&quot;:&quot;product_cCode&quot;,&quot;cCaption&quot;:&quot;物料编码&quot;,&quot;cShowCaption&quot;:&quot;物料编码&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_productsku&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;product&quot;:&quot;id&quot;,&quot;product_cCode&quot;:&quot;cCode&quot;,&quot;product_cName&quot;:&quot;cName&quot;,&quot;product_oUnitId&quot;:&quot;oUnitId&quot;,&quot;unit&quot;:&quot;oUnitId&quot;,&quot;unit_name&quot;:&quot;unitName&quot;,&quot;unit_code&quot;:&quot;unitCode&quot;,&quot;purUOM&quot;:&quot;purchaseUnit&quot;,&quot;purUOM_Name&quot;:&quot;purchaseUnit_name&quot;,&quot;purUOM_Code&quot;:&quot;purchaseUnit_code&quot;,&quot;invExchRate&quot;:&quot;purchaseRate&quot;,&quot;priceUOM&quot;:&quot;purchaseUnit&quot;,&quot;priceUOM_Name&quot;:&quot;purchaseUnit_name&quot;,&quot;priceUOM_Code&quot;:&quot;purchaseUnit_code&quot;,&quot;invPriceExchRate&quot;:&quot;purchaseRate&quot;,&quot;taxRate&quot;:&quot;productOfflineRetail_inputTax&quot;,&quot;product_primeCosts&quot;:&quot;primeCosts&quot;,&quot;isBatchManage&quot;:&quot;productOfflineRetail_isBatchManage&quot;,&quot;isSerialNoManage&quot;:&quot;productOfflineRetail_isSerialNoManage&quot;,&quot;isExpiryDateManage&quot;:&quot;productOfflineRetail_isExpiryDateManage&quot;,&quot;expireDateNo&quot;:&quot;productOfflineRetail_expireDateNo&quot;,&quot;expireDateUnit&quot;:&quot;productOfflineRetail_expireDateUnit&quot;,&quot;prodefine@1@@30&quot;:&quot;productProps!define@1@@30&quot;,&quot;propertiesValue&quot;:&quot;propertiesValue&quot;,&quot;product_modelDescription&quot;:&quot;modelDescription&quot;,&quot;maxInPrice&quot;:&quot;maxPrimeCosts&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecords&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:&quot;cCode&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444724</id>
<name>product_cName</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435134</defParamId>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;purInRecords.product.cName&quot;,&quot;cItemName&quot;:&quot;product_cName&quot;,&quot;cCaption&quot;:&quot;物料名称&quot;,&quot;cShowCaption&quot;:&quot;物料名称&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_productsku&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;product&quot;:&quot;id&quot;,&quot;product_cCode&quot;:&quot;cCode&quot;,&quot;product_cName&quot;:&quot;cName&quot;,&quot;product_oUnitId&quot;:&quot;oUnitId&quot;,&quot;unit&quot;:&quot;oUnitId&quot;,&quot;unit_name&quot;:&quot;unitName&quot;,&quot;unit_code&quot;:&quot;unitCode&quot;,&quot;purUOM&quot;:&quot;purchaseUnit&quot;,&quot;purUOM_Name&quot;:&quot;purchaseUnit_name&quot;,&quot;purUOM_Code&quot;:&quot;purchaseUnit_code&quot;,&quot;invExchRate&quot;:&quot;purchaseRate&quot;,&quot;priceUOM&quot;:&quot;purchaseUnit&quot;,&quot;priceUOM_Name&quot;:&quot;purchaseUnit_name&quot;,&quot;priceUOM_Code&quot;:&quot;purchaseUnit_code&quot;,&quot;invPriceExchRate&quot;:&quot;purchaseRate&quot;,&quot;taxRate&quot;:&quot;productOfflineRetail_inputTax&quot;,&quot;product_primeCosts&quot;:&quot;primeCosts&quot;,&quot;isBatchManage&quot;:&quot;productOfflineRetail_isBatchManage&quot;,&quot;isSerialNoManage&quot;:&quot;productOfflineRetail_isSerialNoManage&quot;,&quot;isExpiryDateManage&quot;:&quot;productOfflineRetail_isExpiryDateManage&quot;,&quot;expireDateNo&quot;:&quot;productOfflineRetail_expireDateNo&quot;,&quot;expireDateUnit&quot;:&quot;productOfflineRetail_expireDateUnit&quot;,&quot;prodefine@1@@30&quot;:&quot;productProps!define@1@@30&quot;,&quot;propertiesValue&quot;:&quot;propertiesValue&quot;,&quot;product_modelDescription&quot;:&quot;modelDescription&quot;,&quot;maxInPrice&quot;:&quot;maxPrimeCosts&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecords&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:&quot;cName&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444725</id>
<name>productsku</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435135</defParamId>
<array>false</array>
<paramDesc>物料SKUid</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444726</id>
<name>tradeRoute_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1878164236360220675</defParamId>
<array>false</array>
<paramDesc>贸易路径</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444727</id>
<name>productsku_cCode</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435136</defParamId>
<array>false</array>
<paramDesc>物料sku编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;purInRecords.productsku.cCode&quot;,&quot;cItemName&quot;:&quot;productsku_cCode&quot;,&quot;cCaption&quot;:&quot;物料sku编码&quot;,&quot;cShowCaption&quot;:&quot;物料sku编码&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_productsku&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;product&quot;:&quot;id&quot;,&quot;product_cCode&quot;:&quot;cCode&quot;,&quot;product_cName&quot;:&quot;cName&quot;,&quot;productsku&quot;:&quot;productskus_id&quot;,&quot;productsku_cCode&quot;:&quot;productskus_cCode&quot;,&quot;productsku_cName&quot;:&quot;skuName&quot;,&quot;product_oUnitId&quot;:&quot;oUnitId&quot;,&quot;unit&quot;:&quot;oUnitId&quot;,&quot;unit_name&quot;:&quot;unitName&quot;,&quot;unit_code&quot;:&quot;unitCode&quot;,&quot;purUOM&quot;:&quot;purchaseUnit&quot;,&quot;purUOM_Name&quot;:&quot;purchaseUnit_name&quot;,&quot;purUOM_Code&quot;:&quot;purchaseUnit_code&quot;,&quot;invExchRate&quot;:&quot;purchaseRate&quot;,&quot;priceUOM&quot;:&quot;purchaseUnit&quot;,&quot;priceUOM_Name&quot;:&quot;purchaseUnit_name&quot;,&quot;priceUOM_Code&quot;:&quot;purchaseUnit_code&quot;,&quot;invPriceExchRate&quot;:&quot;purchaseRate&quot;,&quot;taxRate&quot;:&quot;productOfflineRetail_inputTax&quot;,&quot;product_primeCosts&quot;:&quot;primeCosts&quot;,&quot;productsku_primeCosts&quot;:&quot;productskus_primeCosts&quot;,&quot;isBatchManage&quot;:&quot;productOfflineRetail_isBatchManage&quot;,&quot;isSerialNoManage&quot;:&quot;productOfflineRetail_isSerialNoManage&quot;,&quot;isExpiryDateManage&quot;:&quot;productOfflineRetail_isExpiryDateManage&quot;,&quot;expireDateNo&quot;:&quot;productOfflineRetail_expireDateNo&quot;,&quot;expireDateUnit&quot;:&quot;productOfflineRetail_expireDateUnit&quot;,&quot;free@1@@10&quot;:&quot;retailskus!free@1@@10&quot;,&quot;skudefine@1@@60&quot;:&quot;productSkuProps!define@1@@60&quot;,&quot;prodefine@1@@30&quot;:&quot;productProps!define@1@@30&quot;,&quot;propertiesValue&quot;:&quot;propertiesValue&quot;,&quot;product_modelDescription&quot;:&quot;modelDescription&quot;,&quot;productskus_modelDescription&quot;:&quot;productskus_modelDescription&quot;,&quot;maxInPrice&quot;:&quot;maxPrimeCosts&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecords&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:&quot;productskus_cCode&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444728</id>
<name>productsku_cName</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435137</defParamId>
<array>false</array>
<paramDesc>物料sku名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;purInRecords.productsku.skuName&quot;,&quot;cItemName&quot;:&quot;productsku_cName&quot;,&quot;cCaption&quot;:&quot;物料sku名称&quot;,&quot;cShowCaption&quot;:&quot;物料sku名称&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_productsku&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;product&quot;:&quot;id&quot;,&quot;product_cCode&quot;:&quot;cCode&quot;,&quot;product_cName&quot;:&quot;cName&quot;,&quot;productsku&quot;:&quot;productskus_id&quot;,&quot;productsku_cCode&quot;:&quot;productskus_cCode&quot;,&quot;productsku_cName&quot;:&quot;skuName&quot;,&quot;product_oUnitId&quot;:&quot;oUnitId&quot;,&quot;unit&quot;:&quot;oUnitId&quot;,&quot;unit_name&quot;:&quot;unitName&quot;,&quot;unit_code&quot;:&quot;unitCode&quot;,&quot;purUOM&quot;:&quot;purchaseUnit&quot;,&quot;purUOM_Name&quot;:&quot;purchaseUnit_name&quot;,&quot;purUOM_Code&quot;:&quot;purchaseUnit_code&quot;,&quot;invExchRate&quot;:&quot;purchaseRate&quot;,&quot;priceUOM&quot;:&quot;purchaseUnit&quot;,&quot;priceUOM_Name&quot;:&quot;purchaseUnit_name&quot;,&quot;priceUOM_Code&quot;:&quot;purchaseUnit_code&quot;,&quot;invPriceExchRate&quot;:&quot;purchaseRate&quot;,&quot;taxRate&quot;:&quot;productOfflineRetail_inputTax&quot;,&quot;product_primeCosts&quot;:&quot;primeCosts&quot;,&quot;productsku_primeCosts&quot;:&quot;productskus_primeCosts&quot;,&quot;isBatchManage&quot;:&quot;productOfflineRetail_isBatchManage&quot;,&quot;isSerialNoManage&quot;:&quot;productOfflineRetail_isSerialNoManage&quot;,&quot;isExpiryDateManage&quot;:&quot;productOfflineRetail_isExpiryDateManage&quot;,&quot;expireDateNo&quot;:&quot;productOfflineRetail_expireDateNo&quot;,&quot;expireDateUnit&quot;:&quot;productOfflineRetail_expireDateUnit&quot;,&quot;free@1@@10&quot;:&quot;retailskus!free@1@@10&quot;,&quot;skudefine@1@@60&quot;:&quot;productSkuProps!define@1@@60&quot;,&quot;prodefine@1@@30&quot;:&quot;productProps!define@1@@30&quot;,&quot;propertiesValue&quot;:&quot;propertiesValue&quot;,&quot;product_modelDescription&quot;:&quot;modelDescription&quot;,&quot;productskus_modelDescription&quot;:&quot;productskus_modelDescription&quot;,&quot;maxInPrice&quot;:&quot;maxPrimeCosts&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecords&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:&quot;cName&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444729</id>
<name>productClass_code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435138</defParamId>
<array>false</array>
<paramDesc>物料分类编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444730</id>
<name>propertiesValue</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435139</defParamId>
<array>false</array>
<paramDesc>规格</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444731</id>
<name>batchno</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435140</defParamId>
<array>false</array>
<paramDesc>批次号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>62</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;purInRecords.batchno&quot;,&quot;cItemName&quot;:&quot;batchno&quot;,&quot;cCaption&quot;:&quot;批次号&quot;,&quot;cShowCaption&quot;:&quot;批次号&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;st_batchnoref&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;batchno&quot;:&quot;batchno&quot;,&quot;producedate&quot;:&quot;producedate&quot;,&quot;invaliddate&quot;:&quot;invaliddate&quot;,&quot;define@1@@30&quot;:&quot;define@1@@30&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecords&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:&quot;batchno&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444732</id>
<name>invaliddate</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435141</defParamId>
<array>false</array>
<paramDesc>有效期至</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>63</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444733</id>
<name>producedate</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435142</defParamId>
<array>false</array>
<paramDesc>生产日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>64</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444734</id>
<name>unit</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435143</defParamId>
<array>false</array>
<paramDesc>单位id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>65</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444735</id>
<name>qty</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435144</defParamId>
<array>false</array>
<paramDesc>数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>66</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444736</id>
<name>unit_code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435145</defParamId>
<array>false</array>
<paramDesc>计量单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>67</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444737</id>
<name>unit_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435146</defParamId>
<array>false</array>
<paramDesc>计量单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>68</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;purInRecords.unit.name&quot;,&quot;cItemName&quot;:&quot;unit_name&quot;,&quot;cCaption&quot;:&quot;计量单位&quot;,&quot;cShowCaption&quot;:&quot;计量单位&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_productunit&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;unit&quot;:&quot;id&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecords&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:&quot;name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444738</id>
<name>subQty</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435147</defParamId>
<array>false</array>
<paramDesc>件数</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>69</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444739</id>
<name>stockUnit_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435148</defParamId>
<array>false</array>
<paramDesc>库存单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>70</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;purInRecords.stockUnitId.name&quot;,&quot;cItemName&quot;:&quot;stockUnit_name&quot;,&quot;cCaption&quot;:&quot;库存单位&quot;,&quot;cShowCaption&quot;:&quot;库存单位&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;productcenter.pc_productassitunitsref&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;stockUnitId&quot;:&quot;assistUnit&quot;,&quot;stockUnit_name&quot;:&quot;assistUnit_Name&quot;,&quot;stockUnit_code&quot;:&quot;assistUnit_Code&quot;,&quot;invExchRate&quot;:&quot;assistUnitCount&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecords&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:&quot;assistUnit_Name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444740</id>
<name>project_code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435149</defParamId>
<array>false</array>
<paramDesc>项目编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>71</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;purInRecords.project.code&quot;,&quot;cItemName&quot;:&quot;project_code&quot;,&quot;cCaption&quot;:&quot;项目编码&quot;,&quot;cShowCaption&quot;:&quot;项目编码&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;ucfbasedoc.bd_outer_projectcardMCref&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;project&quot;:&quot;id&quot;,&quot;project_code&quot;:&quot;code&quot;,&quot;project_name&quot;:&quot;name&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecords&quot;,&quot;cControlType&quot;:&quot;Refer&quot;,&quot;refReturn&quot;:null,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444741</id>
<name>project_name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435150</defParamId>
<array>false</array>
<paramDesc>项目名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>72</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;purInRecords.project.name&quot;,&quot;cItemName&quot;:&quot;project_name&quot;,&quot;cCaption&quot;:&quot;项目名称&quot;,&quot;cShowCaption&quot;:&quot;项目名称&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;ucfbasedoc.bd_outer_projectcardMCref&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;project&quot;:&quot;id&quot;,&quot;project_code&quot;:&quot;code&quot;,&quot;project_name&quot;:&quot;name&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecords&quot;,&quot;cControlType&quot;:&quot;Refer&quot;,&quot;refReturn&quot;:null,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444742</id>
<name>oriUnitPrice</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435151</defParamId>
<array>false</array>
<paramDesc>无税单价</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>73</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444743</id>
<name>oriTaxUnitPrice</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435152</defParamId>
<array>false</array>
<paramDesc>含税单价</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>74</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444744</id>
<name>oriMoney</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435153</defParamId>
<array>false</array>
<paramDesc>无税金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>75</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444745</id>
<name>oriSum</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435154</defParamId>
<array>false</array>
<paramDesc>含税金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>76</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444746</id>
<name>oriTax</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435155</defParamId>
<array>false</array>
<paramDesc>税额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>77</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444747</id>
<name>taxRate</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435156</defParamId>
<array>false</array>
<paramDesc>税率</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>78</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444748</id>
<name>billqty</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435157</defParamId>
<array>false</array>
<paramDesc>累计开票数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>79</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444749</id>
<name>billSubQty</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435158</defParamId>
<array>false</array>
<paramDesc>累计开票件数</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>80</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444750</id>
<name>sqty</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435159</defParamId>
<array>false</array>
<paramDesc>累计结算数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>81</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444751</id>
<name>smoney</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435160</defParamId>
<array>false</array>
<paramDesc>累计结算金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>82</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444752</id>
<name>sfee</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435161</defParamId>
<array>false</array>
<paramDesc>累计结算费用</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>83</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444753</id>
<name>totalBillOriSum</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435162</defParamId>
<array>false</array>
<paramDesc>累计开票含税金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>84</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444754</id>
<name>priceUOM</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435163</defParamId>
<array>false</array>
<paramDesc>计价单位id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>85</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444755</id>
<name>priceUOM_Code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435164</defParamId>
<array>false</array>
<paramDesc>计价单位编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>86</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;purInRecords.priceUOM.code&quot;,&quot;cItemName&quot;:&quot;priceUOM_Code&quot;,&quot;cCaption&quot;:&quot;计价单位编码&quot;,&quot;cShowCaption&quot;:&quot;计价单位编码&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_productunit&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;priceUOM&quot;:&quot;id&quot;,&quot;priceUOM_Name&quot;:&quot;name&quot;,&quot;priceUOM_Code&quot;:&quot;code&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecords&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:&quot;Code&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444756</id>
<name>priceUOM_Name</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435165</defParamId>
<array>false</array>
<paramDesc>计价单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>87</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;purInRecords.priceUOM.name&quot;,&quot;cItemName&quot;:&quot;priceUOM_Name&quot;,&quot;cCaption&quot;:&quot;计价单位名称&quot;,&quot;cShowCaption&quot;:&quot;计价单位名称&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;aa_productunit&quot;,&quot;cRefId&quot;:null,&quot;cRefRetId&quot;:{&quot;priceUOM&quot;:&quot;id&quot;,&quot;priceUOM_Name&quot;:&quot;name&quot;,&quot;priceUOM_Code&quot;:&quot;code&quot;},&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:true,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:false,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecords&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:&quot;Name&quot;,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;false&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444757</id>
<name>natCurrency_priceDigit</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435166</defParamId>
<array>false</array>
<paramDesc>本币单价精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>88</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444758</id>
<name>natCurrency_moneyDigit</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435167</defParamId>
<array>false</array>
<paramDesc>本币金额精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>89</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444759</id>
<name>currency_priceDigit</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435168</defParamId>
<array>false</array>
<paramDesc>币种单价精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>90</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444760</id>
<name>currency_moneyDigit</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435169</defParamId>
<array>false</array>
<paramDesc>币种金额精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>91</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444761</id>
<name>unit_Precision</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435170</defParamId>
<array>false</array>
<paramDesc>主计量精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>92</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444762</id>
<name>priceUOM_Precision</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435171</defParamId>
<array>false</array>
<paramDesc>计价单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>93</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444763</id>
<name>stockUnitId_Precision</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435172</defParamId>
<array>false</array>
<paramDesc>库存单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>94</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444764</id>
<name>isGiftProduct</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435173</defParamId>
<array>false</array>
<paramDesc>赠品, true:是、false:否、</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>95</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444765</id>
<name>bmake_st_purinvoice_red</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435174</defParamId>
<array>false</array>
<paramDesc>流程红票</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>96</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444766</id>
<name>bmake_st_purinvoice</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435175</defParamId>
<array>false</array>
<paramDesc>流程蓝票</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>97</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444767</id>
<name>bizFlow</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435176</defParamId>
<array>false</array>
<paramDesc>流程ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>98</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444768</id>
<name>bizFlow_version</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435177</defParamId>
<array>false</array>
<paramDesc>版本信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>99</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>true</refType>
<refTypeContext>{&quot;cFieldName&quot;:&quot;bizFlow.version&quot;,&quot;cItemName&quot;:&quot;bizFlow_version&quot;,&quot;cCaption&quot;:&quot;版本信息&quot;,&quot;cShowCaption&quot;:&quot;版本信息&quot;,&quot;iMaxLength&quot;:255,&quot;bHidden&quot;:false,&quot;cRefType&quot;:&quot;ucf-staff-center.bf_businessFlow_ref&quot;,&quot;cRefId&quot;:{&quot;bizFlow&quot;:&quot;id&quot;,&quot;bizFlow_version&quot;:&quot;version&quot;},&quot;cRefRetId&quot;:null,&quot;cDataRule&quot;:null,&quot;iNumPoint&quot;:null,&quot;bCanModify&quot;:false,&quot;iMaxShowLen&quot;:255,&quot;bShowIt&quot;:true,&quot;bIsNull&quot;:true,&quot;bSelfDefine&quot;:false,&quot;cSelfDefineType&quot;:null,&quot;cOrder&quot;:null,&quot;cDataSourceName&quot;:&quot;st.purinrecord.PurInRecord&quot;,&quot;cControlType&quot;:&quot;refer&quot;,&quot;refReturn&quot;:null,&quot;dataType&quot;:null,&quot;bEnum&quot;:&quot;false&quot;,&quot;cEnumString&quot;:null,&quot;enumArray&quot;:null,&quot;bMustSelect&quot;:&quot;true&quot;}</refTypeContext>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444769</id>
<name>isFlowCoreBill</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435178</defParamId>
<array>false</array>
<paramDesc>是否流程核心单据</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>100</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444773</id>
<name>out_sys_id</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435182</defParamId>
<array>false</array>
<paramDesc>外部来源线索</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>104</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444774</id>
<name>out_sys_code</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435183</defParamId>
<array>false</array>
<paramDesc>外部来源编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>105</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444775</id>
<name>out_sys_version</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435184</defParamId>
<array>false</array>
<paramDesc>外部来源版本</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>106</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444776</id>
<name>out_sys_type</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435185</defParamId>
<array>false</array>
<paramDesc>外部来源类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>107</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444777</id>
<name>out_sys_rowno</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435186</defParamId>
<array>false</array>
<paramDesc>外部来源行号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>108</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444778</id>
<name>out_sys_lineid</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1856790202779435187</defParamId>
<array>false</array>
<paramDesc>外部来源行</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>109</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444779</id>
<name>tradeRouteID</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1869300738141192199</defParamId>
<array>false</array>
<paramDesc>贸易路径id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>110</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444780</id>
<name>isEndTrade</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1869300738141192200</defParamId>
<array>false</array>
<paramDesc>是否末级(0:否,1:是)</paramDesc>
<paramType>short</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>111</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444781</id>
<name>tradeRouteLineno</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1869300738141192201</defParamId>
<array>false</array>
<paramDesc>站点</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>112</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444782</id>
<name>collaborationPolineno</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1869300738141192202</defParamId>
<array>false</array>
<paramDesc>协同来源单据行号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>113</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444783</id>
<name>coSourceType</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1869300738141192203</defParamId>
<array>false</array>
<paramDesc>协同源头单据类型(productionorder.po_subcontract_order:委外订单)</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>114</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444784</id>
<name>coUpcode</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1869300738141192204</defParamId>
<array>false</array>
<paramDesc>协同源头单据号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>115</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444785</id>
<name>coSourceLineNo</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1869300738141192205</defParamId>
<array>false</array>
<paramDesc>协同源头单据行号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>116</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444786</id>
<name>coSourceid</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1869300738141192206</defParamId>
<array>false</array>
<paramDesc>协同来源单据id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>117</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444787</id>
<name>coSourceautoid</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1869300738141192207</defParamId>
<array>false</array>
<paramDesc>协同来源单据子表id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>118</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444788</id>
<name>collaborationPodetailid</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1871338700102172673</defParamId>
<array>false</array>
<paramDesc>协同来源单据行</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>119</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444789</id>
<name>collaborationPocode</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1871338700102172674</defParamId>
<array>false</array>
<paramDesc>协同来源单据号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>120</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444790</id>
<name>collaborationPoid</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1871338700102172675</defParamId>
<array>false</array>
<paramDesc>协同来源单据主表id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>121</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444791</id>
<name>collaborationSource</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1871338700102172676</defParamId>
<array>false</array>
<paramDesc>协同来源单据类型(st_salesout:销售出库)</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>122</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444792</id>
<name>totalOutStockQuantity</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444668</parentId>
<defParamId>1878157330052808705</defParamId>
<array>false</array>
<paramDesc>累计销售出库数量</paramDesc>
<paramType>BigDecimal</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>123</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1856790202779435078</defParamId>
<array>true</array>
<paramDesc>返回结果对象</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible/>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1856790202779435070</defParamId>
<array>false</array>
<paramDesc>调用成功时的返回数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2081374351925444800</id>
<name>sumRecordList</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId/>
<children>
<children>
<id>2081374351925444801</id>
<name>totalQuantity</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435189</defParamId>
<array>false</array>
<paramDesc>整单数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444802</id>
<name>totalPieces</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435190</defParamId>
<array>false</array>
<paramDesc>整单件数</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444803</id>
<name>subQty</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435191</defParamId>
<array>false</array>
<paramDesc>件数</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444804</id>
<name>currency</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435192</defParamId>
<array>false</array>
<paramDesc>币种id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444805</id>
<name>paymentsum</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435193</defParamId>
<array>false</array>
<paramDesc>付款金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444806</id>
<name>sfee</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435194</defParamId>
<array>false</array>
<paramDesc>累计结算费用</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444807</id>
<name>unit_Precision</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435195</defParamId>
<array>false</array>
<paramDesc>主计量精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444808</id>
<name>currency_priceDigit</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435196</defParamId>
<array>false</array>
<paramDesc>币种单价精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444809</id>
<name>priceUOM_Precision</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435197</defParamId>
<array>false</array>
<paramDesc>计价单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444810</id>
<name>moneysum</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435198</defParamId>
<array>false</array>
<paramDesc>金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444811</id>
<name>oriMoney</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435199</defParamId>
<array>false</array>
<paramDesc>无税金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444812</id>
<name>oriSum</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435200</defParamId>
<array>false</array>
<paramDesc>含税金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444813</id>
<name>stockUnitId_Precision</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435201</defParamId>
<array>false</array>
<paramDesc>库存单位精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444814</id>
<name>qty</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435202</defParamId>
<array>false</array>
<paramDesc>数量</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444815</id>
<name>purInRecords_unit</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435203</defParamId>
<array>false</array>
<paramDesc>计量单位</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444816</id>
<name>natCurrency</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435204</defParamId>
<array>false</array>
<paramDesc>本币币种id</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444817</id>
<name>natCurrency_moneyDigit</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435205</defParamId>
<array>false</array>
<paramDesc>本币金额精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444818</id>
<name>unpaymentsum</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435206</defParamId>
<array>false</array>
<paramDesc>未付款金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444819</id>
<name>smoney</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435207</defParamId>
<array>false</array>
<paramDesc>累计结算金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444820</id>
<name>sqty</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435208</defParamId>
<array>false</array>
<paramDesc>累计结算数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444821</id>
<name>natCurrency_priceDigit</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435209</defParamId>
<array>false</array>
<paramDesc>本币单价精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444822</id>
<name>purInRecords_stockUnitId</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435210</defParamId>
<array>false</array>
<paramDesc>库存单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444823</id>
<name>purInRecords_priceUOM</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435211</defParamId>
<array>false</array>
<paramDesc>计价单位</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444824</id>
<name>oriTax</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435212</defParamId>
<array>false</array>
<paramDesc>税额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444825</id>
<name>currency_moneyDigit</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435213</defParamId>
<array>false</array>
<paramDesc>币种金额精度</paramDesc>
<paramType>int</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2081374351925444826</id>
<name>billqty</name>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<parentId>2081374351925444800</parentId>
<defParamId>1856790202779435214</defParamId>
<array>false</array>
<paramDesc>累计开票数量</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1856790202779435188</defParamId>
<array>false</array>
<paramDesc>合计对象</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2081374351925444833</id>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "pubts": "", "recordList": [ { "vouchdate": "", "code": "", "bustype_name": "", "vendor_name": "", "warehouse_name": "", "vendor_code": "", "warehouse_code": "", "stockMgr_name": "", "status": 0, "purchaseOrg_name": "", "department_name": "", "org_code": "", "org_name": "", "department_code": "", "operator_name": "", "totalQuantity": 0, "totalPieces": 0, "inInvoiceOrg_name": "", "inInvoiceOrg": "", "accountOrg": "", "isBeginning": true, "bustype": "", "vendor": 0, "contact": "", "warehouse": 0, "operator": 0, "purchaseOrg": "", "org": "", "department": "", "stockMgr": "", "moneysum": 0, "paymentsum": 0, "unpaymentsum": 0, "store": "", "store_name": "", "custom": 0, "payor": "", "payor_name": "", "paytime": "", "paymentstatus": "", "creator": "", "createTime": "", "modifier": "", "modifyTime": "", "auditor": "", "auditTime": "", "memo": "", "id": "", "srcBill": "", "pubts": "", "tplid": 0, "exchangestatus": "", "purInRecords_id": "", "product": "", "product_cCode": "", "product_cName": "", "productsku": "", "tradeRoute_name": "", "productsku_cCode": "", "productsku_cName": "", "productClass_code": "", "propertiesValue": "", "batchno": "", "invaliddate": "", "producedate": "", "unit": "", "qty": 0, "unit_code": "", "unit_name": "", "subQty": 0, "stockUnit_name": "", "project_code": "", "project_name": "", "oriUnitPrice": 0, "oriTaxUnitPrice": 0, "oriMoney": 0, "oriSum": 0, "oriTax": 0, "taxRate": 0, "billqty": 0, "billSubQty": 0, "sqty": 0, "smoney": 0, "sfee": 0, "totalBillOriSum": 0, "priceUOM": 0, "priceUOM_Code": "", "priceUOM_Name": "", "natCurrency_priceDigit": 0, "natCurrency_moneyDigit": 0, "currency_priceDigit": 0, "currency_moneyDigit": 0, "unit_Precision": 0, "priceUOM_Precision": 0, "stockUnitId_Precision": 0, "isGiftProduct": true, "bmake_st_purinvoice_red": "", "bmake_st_purinvoice": "", "bizFlow": "", "bizFlow_version": "", "isFlowCoreBill": "", "purInRecordDefineCharacter": 0, "purInRecordsDefineCharacter": 0, "purInRecordsCharacteristics": 0, "out_sys_id": "", "out_sys_code": "", "out_sys_version": "", "out_sys_type": "", "out_sys_rowno": "", "out_sys_lineid": "", "tradeRouteID": 0, "isEndTrade": 0, "tradeRouteLineno": "", "collaborationPolineno": "", "coSourceType": "", "coUpcode": "", "coSourceLineNo": "", "coSourceid": "", "coSourceautoid": 0, "collaborationPodetailid": 0, "collaborationPocode": "", "collaborationPoid": 0, "collaborationSource": "", "totalOutStockQuantity": 0 } ] }, "sumRecordList": { "totalQuantity": 0, "totalPieces": 0, "subQty": 0, "currency": "", "paymentsum": 0, "sfee": 0, "unit_Precision": 0, "currency_priceDigit": 0, "priceUOM_Precision": 0, "moneysum": 0, "oriMoney": 0, "oriSum": 0, "stockUnitId_Precision": 0, "qty": 0, "purInRecords_unit": 0, "natCurrency": "", "natCurrency_moneyDigit": 0, "unpaymentsum": 0, "smoney": 0, "sqty": 0, "natCurrency_priceDigit": 0, "purInRecords_stockUnitId": "", "purInRecords_priceUOM": 0, "oriTax": 0, "currency_moneyDigit": 0, "billqty": 0 } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2081374351925444834</id>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<content/>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2081374351925444833</id>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "pubts": "", "recordList": [ { "vouchdate": "", "code": "", "bustype_name": "", "vendor_name": "", "warehouse_name": "", "vendor_code": "", "warehouse_code": "", "stockMgr_name": "", "status": 0, "purchaseOrg_name": "", "department_name": "", "org_code": "", "org_name": "", "department_code": "", "operator_name": "", "totalQuantity": 0, "totalPieces": 0, "inInvoiceOrg_name": "", "inInvoiceOrg": "", "accountOrg": "", "isBeginning": true, "bustype": "", "vendor": 0, "contact": "", "warehouse": 0, "operator": 0, "purchaseOrg": "", "org": "", "department": "", "stockMgr": "", "moneysum": 0, "paymentsum": 0, "unpaymentsum": 0, "store": "", "store_name": "", "custom": 0, "payor": "", "payor_name": "", "paytime": "", "paymentstatus": "", "creator": "", "createTime": "", "modifier": "", "modifyTime": "", "auditor": "", "auditTime": "", "memo": "", "id": "", "srcBill": "", "pubts": "", "tplid": 0, "exchangestatus": "", "purInRecords_id": "", "product": "", "product_cCode": "", "product_cName": "", "productsku": "", "tradeRoute_name": "", "productsku_cCode": "", "productsku_cName": "", "productClass_code": "", "propertiesValue": "", "batchno": "", "invaliddate": "", "producedate": "", "unit": "", "qty": 0, "unit_code": "", "unit_name": "", "subQty": 0, "stockUnit_name": "", "project_code": "", "project_name": "", "oriUnitPrice": 0, "oriTaxUnitPrice": 0, "oriMoney": 0, "oriSum": 0, "oriTax": 0, "taxRate": 0, "billqty": 0, "billSubQty": 0, "sqty": 0, "smoney": 0, "sfee": 0, "totalBillOriSum": 0, "priceUOM": 0, "priceUOM_Code": "", "priceUOM_Name": "", "natCurrency_priceDigit": 0, "natCurrency_moneyDigit": 0, "currency_priceDigit": 0, "currency_moneyDigit": 0, "unit_Precision": 0, "priceUOM_Precision": 0, "stockUnitId_Precision": 0, "isGiftProduct": true, "bmake_st_purinvoice_red": "", "bmake_st_purinvoice": "", "bizFlow": "", "bizFlow_version": "", "isFlowCoreBill": "", "purInRecordDefineCharacter": 0, "purInRecordsDefineCharacter": 0, "purInRecordsCharacteristics": 0, "out_sys_id": "", "out_sys_code": "", "out_sys_version": "", "out_sys_type": "", "out_sys_rowno": "", "out_sys_lineid": "", "tradeRouteID": 0, "isEndTrade": 0, "tradeRouteLineno": "", "collaborationPolineno": "", "coSourceType": "", "coUpcode": "", "coSourceLineNo": "", "coSourceid": "", "coSourceautoid": 0, "collaborationPodetailid": 0, "collaborationPocode": "", "collaborationPoid": 0, "collaborationSource": "", "totalOutStockQuantity": 0 } ] }, "sumRecordList": { "totalQuantity": 0, "totalPieces": 0, "subQty": 0, "currency": "", "paymentsum": 0, "sfee": 0, "unit_Precision": 0, "currency_priceDigit": 0, "priceUOM_Precision": 0, "moneysum": 0, "oriMoney": 0, "oriSum": 0, "stockUnitId_Precision": 0, "qty": 0, "purInRecords_unit": 0, "natCurrency": "", "natCurrency_moneyDigit": 0, "unpaymentsum": 0, "smoney": 0, "sqty": 0, "natCurrency_priceDigit": 0, "purInRecords_stockUnitId": "", "purInRecords_priceUOM": 0, "oriTax": 0, "currency_moneyDigit": 0, "billqty": 0 } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2081374351925444834</id>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<content/>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>2081374351925444833</id>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<content>{ "code": "", "message": "", "data": { "pageIndex": 0, "pageSize": 0, "pageCount": 0, "beginPageIndex": 0, "endPageIndex": 0, "recordCount": 0, "pubts": "", "recordList": [ { "vouchdate": "", "code": "", "bustype_name": "", "vendor_name": "", "warehouse_name": "", "vendor_code": "", "warehouse_code": "", "stockMgr_name": "", "status": 0, "purchaseOrg_name": "", "department_name": "", "org_code": "", "org_name": "", "department_code": "", "operator_name": "", "totalQuantity": 0, "totalPieces": 0, "inInvoiceOrg_name": "", "inInvoiceOrg": "", "accountOrg": "", "isBeginning": true, "bustype": "", "vendor": 0, "contact": "", "warehouse": 0, "operator": 0, "purchaseOrg": "", "org": "", "department": "", "stockMgr": "", "moneysum": 0, "paymentsum": 0, "unpaymentsum": 0, "store": "", "store_name": "", "custom": 0, "payor": "", "payor_name": "", "paytime": "", "paymentstatus": "", "creator": "", "createTime": "", "modifier": "", "modifyTime": "", "auditor": "", "auditTime": "", "memo": "", "id": "", "srcBill": "", "pubts": "", "tplid": 0, "exchangestatus": "", "purInRecords_id": "", "product": "", "product_cCode": "", "product_cName": "", "productsku": "", "tradeRoute_name": "", "productsku_cCode": "", "productsku_cName": "", "productClass_code": "", "propertiesValue": "", "batchno": "", "invaliddate": "", "producedate": "", "unit": "", "qty": 0, "unit_code": "", "unit_name": "", "subQty": 0, "stockUnit_name": "", "project_code": "", "project_name": "", "oriUnitPrice": 0, "oriTaxUnitPrice": 0, "oriMoney": 0, "oriSum": 0, "oriTax": 0, "taxRate": 0, "billqty": 0, "billSubQty": 0, "sqty": 0, "smoney": 0, "sfee": 0, "totalBillOriSum": 0, "priceUOM": 0, "priceUOM_Code": "", "priceUOM_Name": "", "natCurrency_priceDigit": 0, "natCurrency_moneyDigit": 0, "currency_priceDigit": 0, "currency_moneyDigit": 0, "unit_Precision": 0, "priceUOM_Precision": 0, "stockUnitId_Precision": 0, "isGiftProduct": true, "bmake_st_purinvoice_red": "", "bmake_st_purinvoice": "", "bizFlow": "", "bizFlow_version": "", "isFlowCoreBill": "", "purInRecordDefineCharacter": 0, "purInRecordsDefineCharacter": 0, "purInRecordsCharacteristics": 0, "out_sys_id": "", "out_sys_code": "", "out_sys_version": "", "out_sys_type": "", "out_sys_rowno": "", "out_sys_lineid": "", "tradeRouteID": 0, "isEndTrade": 0, "tradeRouteLineno": "", "collaborationPolineno": "", "coSourceType": "", "coUpcode": "", "coSourceLineNo": "", "coSourceid": "", "coSourceautoid": 0, "collaborationPodetailid": 0, "collaborationPocode": "", "collaborationPoid": 0, "collaborationSource": "", "totalOutStockQuantity": 0 } ] }, "sumRecordList": { "totalQuantity": 0, "totalPieces": 0, "subQty": 0, "currency": "", "paymentsum": 0, "sfee": 0, "unit_Precision": 0, "currency_priceDigit": 0, "priceUOM_Precision": 0, "moneysum": 0, "oriMoney": 0, "oriSum": 0, "stockUnitId_Precision": 0, "qty": 0, "purInRecords_unit": 0, "natCurrency": "", "natCurrency_moneyDigit": 0, "unpaymentsum": 0, "smoney": 0, "sqty": 0, "natCurrency_priceDigit": 0, "purInRecords_stockUnitId": "", "purInRecords_priceUOM": 0, "oriTax": 0, "currency_moneyDigit": 0, "billqty": 0 } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>2081374351925444834</id>
<apiId>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiId>
<content/>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-09-05 10:37:45.000</gmtCreate>
<gmtUpdate>2024-09-05 10:37:45.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS/>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>友户通token认证业务扩展插件</name>
<configurable>false</configurable>
<description>YonsuitBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>3f6ee57a6bcc435dad3c91b7d1a06dcd</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin>
<id>w181ed01-1e9b-4350-b994-71a66f062522</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code>resultParse</code>
<name>UCG标准返回值解析插件</name>
<configurable>false</configurable>
<description>符合UCG标准的返回值会自动解析，不符合的会自动略过</description>
<pluginType>resultParse</pluginType>
<pluginTypeName>返回值解析插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.result.UCGResultParsePlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>true</visible>
<gmtCreate>2019-08-19 00:00:00</gmtCreate>
<gmtUpdate/>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>3f6ee57a6bcc435dad3c91b7d1a06dcd</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</resultParsePlugin>
<mapReturnPluginConfig/>
<billNo>st_purinrecordlist</billNo>
<domain>ustock</domain>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser/>
<createUserName/>
<approvalStatus>1</approvalStatus>
<publishTime>2024-09-05 10:56:29</publishTime>
<pathJoin>true</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout/>
<customUrl>/purinrecord/list</customUrl>
<fixedUrl>/yonbip/scm</fixedUrl>
<apiCode>3f6ee57a6bcc435dad3c91b7d1a06dcd</apiCode>
<tokenCheckType>0</tokenCheckType>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL/>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>36a8b72b-d965-404d-a02d-66ff4a7afeb3</updateUserId>
<updateUserName>昵称-王章宇</updateUserName>
<paramIsForce/>
<userIDPassthrough>false</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.yonbip-scm-stock</microServiceCode>
<applicationCode>yonbip-scm-stock</applicationCode>
<privacyCategory>1</privacyCategory>
<privacyLevel>4</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType>0</serviceType>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType/>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>false</arrayParam>
<fileSize/>
<cc>true</cc>
<paramTransferMode>2</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId>1856790202779435014</apiDefId>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>1</paramExtRequest>
<paramExtResponse>1</paramExtResponse>
<paramExtInExtendKey>1</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene/>
<apiType/>
<paramMark/>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>false</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>false</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>1</chargeStatus>
<beforeSpeed>60</beforeSpeed>
<afterSpeed>120</afterSpeed>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath/>
<pubHistory/>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode/>
<multiVersion>0</multiVersion>
<apiTag/>
</data>
</ResultVO>