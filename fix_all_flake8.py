#!/usr/bin/env python3
"""
批量修复所有Flake8代码风格错误
自动化修复常见的代码风格问题
"""

import os
import re
import subprocess
import sys
from pathlib import Path


def run_flake8_check(file_path):
    """运行flake8检查特定文件"""
    try:
        result = subprocess.run(
            ["python", "-m", "flake8", file_path],
            capture_output=True,
            text=True,
            encoding="utf-8"
        )
        return result.stdout.strip()
    except Exception:
        return ""


def fix_f401_unused_imports(content):
    """修复F401: 删除未使用的导入"""
    lines = content.split('\n')
    fixed_lines = []

    # 常见的未使用导入模式
    unused_patterns = [
        r'^from pathlib import Path$',
        r'^import pathlib$',
        r'^from typing import.*Optional.*$',
        r'^from typing import.*List.*$',
        r'^from typing import.*Dict.*$',
    ]

    for line in lines:
        is_unused = False
        for pattern in unused_patterns:
            if re.match(pattern, line.strip()):
                # 检查这个导入是否在后面的代码中被使用
                module_name = line.split()[-1]
                if module_name not in content:
                    is_unused = True
                    break

        if not is_unused:
            fixed_lines.append(line)

    return '\n'.join(fixed_lines)


def fix_e302_blank_lines(content):
    """修复E302: 添加正确的空行"""
    lines = content.split('\n')
    fixed_lines = []

    for i, line in enumerate(lines):
        # 检查是否是函数或类定义
        if re.match(r'^(def |class |async def )', line.strip()):
            # 如果前面不是文件开头、装饰器或已经有足够空行
            if i > 0:
                prev_lines = lines[max(0, i - 3):i]
                non_empty_prev = [l for l in prev_lines if l.strip()]

                # 如果前面的非空行不是装饰器，且没有足够空行
                if (non_empty_prev and
                    not non_empty_prev[-1].strip().startswith('@') and
                        len([l for l in prev_lines if not l.strip()]) < 2):

                    # 添加空行
                    while len(fixed_lines) > 0 and not fixed_lines[-1].strip():
                        fixed_lines.pop()
                    fixed_lines.extend(['', ''])

        fixed_lines.append(line)

    return '\n'.join(fixed_lines)


def fix_f841_unused_variables(content):
    """修复F841: 删除未使用的变量"""
    # 修复未使用的异常变量
    content = re.sub(
        r'except\s+(\w+Exception)\s+as\s+\w+:',
        r'except \1:',
        content
    )

    # 修复未使用的Exception变量
    content = re.sub(
        r'except\s+Exception\s+as\s+\w+:',
        r'except Exception:',
        content
    )

    return content


def fix_e501_long_lines(content):
    """修复E501: 拆分过长的行"""
    lines = content.split('\n')
    fixed_lines = []

    for line in lines:
        if len(line) > 79:
            # 处理导入语句
            if line.strip().startswith('import ') or line.strip().startswith('from '):
                # 如果是多个导入，尝试拆分
                if ' import ' in line and ',' in line:
                    parts = line.split(' import ')
                    if len(parts) == 2:
                        module_part = parts[0]
                        imports_part = parts[1]
                        imports =
                        [imp.strip() for imp in imports_part.split(
                            ',                                                                    ')]

                        if len(imports) > 1:
                            fixed_lines.append(f"{module_part} import (")
                            for imp in imports:
                                fixed_lines.append(f"    {imp},")
                            fixed_lines.append(")")
                            continue

            # 处理字符串连接
            if '+ ' in line and '"' in line:
                # 尝试在 + 处分割
                parts = line.split(' + ')
                if len(parts) > 1:
                    indent = len(line) - len(line.lstrip())
                    fixed_lines.append(parts[0] + ' +')
                    for part in parts[1:]:
                        fixed_lines.append(' ' * (indent + 4) + part)
                    continue

            # 处理字典/列表定义
            if ' = {' in line or ' = [' in line:
                bracket_pos = line.find(' = {')
                if bracket_pos == -1:
                    bracket_pos = line.find(' = [')

                if bracket_pos != -1:
                    before_bracket = line[:bracket_pos + 3]
                    after_bracket = line[bracket_pos + 3:]

                    if ',' in after_bracket:
                        indent = len(line) - len(line.lstrip())
                        fixed_lines.append(before_bracket)

                        items =
                        [item.strip() for item in after_bracket.split(
                            ',                                                                       ')]
                        for item in items[:-1]:  # 除了最后一个
                            if item:
                                fixed_lines.append(
                                    ' ' * (indent + 4) + item + ',')

                        # 最后一个和闭合括号
                        last_item = items[-1]
                        if last_item.endswith('}') or last_item.endswith(']'):
                            if len(last_item) > 1:
                                fixed_lines.append(
                                    ' ' * (indent + 4) + last_item[:-1])
                            fixed_lines.append(' ' * indent + last_item[-1])
                        else:
                            fixed_lines.append(' ' * (indent + 4) + last_item)
                        continue

        fixed_lines.append(line)

    return '\n'.join(fixed_lines)


def fix_w293_blank_line_whitespace(content):
    """修复W293: 删除空行中的空白字符"""
    lines = content.split('\n')
    fixed_lines = []

    for line in lines:
        if not line.strip():  # 空行
            fixed_lines.append('')
        else:
            fixed_lines.append(line)

    return '\n'.join(fixed_lines)


def apply_autoflake(file_path):
    """使用autoflake自动删除未使用的导入"""
    try:
        subprocess.run([
            "python", "-m", "autoflake",
            "--remove-all-unused-imports",
            "--remove-unused-variables",
            "--in-place",
            file_path
        ], check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def apply_autopep8(file_path):
    """使用autopep8自动修复PEP8问题"""
    try:
        subprocess.run([
            "python", "-m", "autopep8",
            "--in-place",
            "--aggressive",
            "--aggressive",
            file_path
        ], check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def apply_black(file_path):
    """使用black格式化代码"""
    try:
        subprocess.run([
            "python", "-m", "black",
            file_path
        ], check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def fix_python_file(file_path):
    """修复单个Python文件的Flake8错误"""
    print(f"🔧 修复文件: {file_path}")

    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()

        content = original_content

        # 应用各种修复
        content = fix_f841_unused_variables(content)
        content = fix_e302_blank_lines(content)
        content = fix_e501_long_lines(content)
        content = fix_w293_blank_line_whitespace(content)
        content = fix_f401_unused_imports(content)

        # 写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ 手动修复完成")

        # 尝试使用自动化工具
        tools_used = []

        if apply_autoflake(file_path):
            tools_used.append("autoflake")

        if apply_autopep8(file_path):
            tools_used.append("autopep8")

        if apply_black(file_path):
            tools_used.append("black")

        if tools_used:
            print(f"  🛠️ 自动化工具: {', '.join(tools_used)}")

        return True

    except Exception:
        print(f"  ❌ 修复失败: {e}")
        return False


def install_tools():
    """安装必要的代码修复工具"""
    tools = ["autoflake", "autopep8", "black"]

    for tool in tools:
        try:
            subprocess.run([
                "python", "-m", "pip", "install", tool
            ], check=True, capture_output=True)
            print(f"✅ 已安装 {tool}")
        except subprocess.CalledProcessError:
            print(f"⚠️ 安装 {tool} 失败，将跳过自动化修复")


def main():
    """主函数"""
    print("🚀 开始批量修复Flake8错误...")
    print()

    # 安装工具
    print("📦 检查并安装代码修复工具...")
    install_tools()
    print()

    # 查找所有Python文件
    python_files = []

    # 排除的目录
    exclude_dirs = {
        '__pycache__', '.git', 'logs', 'temp_cleanup',
        '.vscode', 'node_modules', '.pytest_cache'
    }

    for root, dirs, files in os.walk('.'):
        # 过滤目录
        dirs[:] = [d for d in dirs if d not in exclude_dirs]

        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                python_files.append(file_path)

    print(f"📊 找到 {len(python_files)} 个Python文件")
    print()

    # 修复每个文件
    success_count = 0
    error_count = 0

    for file_path in python_files:
        if fix_python_file(file_path):
            success_count += 1
        else:
            error_count += 1

    print()
    print("📋 修复完成统计:")
    print(f"  ✅ 成功修复: {success_count} 个文件")
    print(f"  ❌ 修复失败: {error_count} 个文件")
    print()

    # 最终检查
    print("🔍 运行最终Flake8检查...")
    try:
        result = subprocess.run([
            "python", "-m", "flake8", ".",
            "--exclude=__pycache__,logs,temp_cleanup,.git",
            "--count"
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print("🎉 所有Flake8错误已修复！")
        else:
            remaining_errors = result.stdout.strip()
            print(f"⚠️ 还有一些错误需要手动处理:")
            print(remaining_errors)

    except Exception:
        print(f"❌ 最终检查失败: {e}")


if __name__ == "__main__":
    main()
