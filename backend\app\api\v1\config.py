from typing import Dict

import structlog
from fastapi import APIRouter, Depends, HTTPException

from ...core.config import MODULES_CONFIG
from ...schemas.config import (API, V3.0, YS, 字段配置管理API, 整合原V2,
                               FieldConfigResponse, FieldMappingResponse,
                               Flask字段配置服务功能, ModuleListResponse, """, -,
                               import, os, subprocess)
from ...services.field_config_service import FieldConfigService

router = APIRouter()
logger = structlog.get_logger()

# 字段配置服务实例
field_config_service = FieldConfigService()
# DEBUG: print("FieldConfigService 实例方法：", dir(field_config_service))


@router.get("/modules", response_model=ModuleListResponse)
async def get_modules():
    """
    获取15个业务模块列表
    整合原Flask服务的模块管理功能
    """
    try:
        modules = []

        for module_config in MODULES_CONFIG:
            # 检查字段配置是否存在
            field_config = await field_config_service.load_module_config(
                module_config["name"]
            )

            module_info = {
                "module_name": module_config["name"],
                "display_name": module_config["display"],
                "table_name": module_config["table_name"],
                "api_endpoint": module_config["api_endpoint"],
                "md_file": module_config["md_file"],
                "is_active": module_config["is_active"],
                "field_count": (
                    len(field_config.get("fields", {})) if field_config else 0
                ),
                "selected_count": (
                    sum(
                        1
                        for f in field_config.get("fields", {}).values()
                        if f.get("is_selected", False)
                    )
                    if field_config
                    else 0
                ),
                "has_config": field_config is not None,
            }
            modules.append(module_info)

        return {
            "success": True,
            "data": {"modules": modules, "total_count": len(modules)},
            "message": "获取模块列表成功",
        }

    except Exception:
        logger.error("获取模块列表失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取模块列表失败: {str(e)}")


@router.get("/modules/{module_name}/fields",
            response_model=FieldConfigResponse)
async def get_module_fields(
    module_name: str,
    max_depth: int = Query(
        3, ge=1, le=10, description="字段提取最大嵌套深度（1-10层，默认3层）"
    ),
    user_id: str = Query("Alice", description="用户ID，用于获取用户级配置"),
):
    """
    获取指定模块的字段配置
    支持动态深度控制和用户级配置
    """
    try:
        # 验证模块名称
        module_names = [m["name"] for m in MODULES_CONFIG]
        if module_name not in module_names:
            raise HTTPException(
                status_code=404,
                detail=f"模块 '{module_name}' 不存在")

        logger.info(
            "获取字段配置",
            module_name=module_name,
            max_depth=max_depth,
            user_id=user_id,
        )

        # 直接应用智能分析逻辑生成配置
        logger.info(
            "应用智能分析逻辑生成字段配置", module_name=module_name, max_depth=max_depth
        )

        # 使用V3新设计的简化配置生成方法
        config = await field_config_service.generate_field_config(
            module_name=module_name, api_data=None, force_refresh=True
        )

        # 包装结果格式
        result = {"success": True if config else False, "config": config}

        # 检查生成结果
        if result.get("success", False):
            config = result.get("config")
            if config:
                logger.info(
                    "字段配置生成完成",
                    module_name=module_name,
                    max_depth=max_depth,
                    total_fields=config.get("total_fields", 0),
                    selected_fields=config.get("selected_fields", 0),
                )

                return {
                    "success": True,
                    "data": config,
                    "message": f"动态生成字段配置成功 (深度: {max_depth}层)",
                }
            else:
                raise HTTPException(status_code=500, detail="配置生成失败：配置为空")
        else:
            error_msg = result.get("message", "未知错误")
            logger.error(
                "字段配置生成失败",
                module_name=module_name,
                max_depth=max_depth,
                error=error_msg,
            )
            raise HTTPException(status_code=500, detail=f"配置生成失败: {error_msg}")

    except HTTPException:
        raise
    except Exception:
        logger.error(
            "获取字段配置失败",
            module_name=module_name,
            max_depth=max_depth,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail=f"获取字段配置失败: {str(e)}")


@router.post("/modules/{module_name}/apply-md-defaults")
async def apply_md_defaults(module_name: str):
    """
    应用MD文档默认配置 - 重命名原有功能以避免混乱
    这个功能只从MD文档生成配置，不涉及API数据
    """
    try:
        # 验证模块名称
        module_names = [m["name"] for m in MODULES_CONFIG]
        if module_name not in module_names:
            raise HTTPException(
                status_code=404,
                detail=f"模块 '{module_name}' 不存在")

        # 应用MD文档默认配置
        success = await field_config_service.apply_default_config(module_name)

        if success:
            # 返回更新后的配置
            config = await field_config_service.load_module_config(module_name)
            return {
                "success": True,
                "data": config,
                "message": f"成功应用 {module_name} 模块的MD文档默认配置",
            }
        else:
            raise HTTPException(status_code=500, detail="应用默认配置失败")

    except HTTPException:
        raise
    except Exception:
        logger.error("应用MD默认配置失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"应用MD默认配置失败: {str(e)}")


@router.post("/modules/{module_name}/apply-defaults")
async def apply_module_defaults(module_name: str):
    """
    应用指定模块的默认配置
    ** 重要**: 基于现有API字段保持API字段名不变，只调整选择状态和重要性
    """
    try:
        # 验证模块名称
        module_names = [m["name"] for m in MODULES_CONFIG]
        if module_name not in module_names:
            raise HTTPException(
                status_code=404,
                detail=f"模块 '{module_name}' 不存在")

        # 加载现有配置
        existing_config = await field_config_service.load_module_config(module_name)

        if not existing_config or not existing_config.get("fields"):
            # 如果没有现有配置，先从API提取
            logger.info("没有现有配置，先从API提取字段", module_name=module_name)
            result = await field_config_service.extract_fields_from_api(
                module_name=module_name, force_refresh=True
            )
        else:
            # 应用默认选择规则到现有配置（保持API字段名不变）
            result = await field_config_service.apply_smart_defaults_to_existing(
                module_name, existing_config
            )

        return {"success": True, "data": result, "message": "应用默认配置成功"}

    except HTTPException:
        raise
    except Exception:
        logger.error("应用默认配置失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"应用默认配置失败: {str(e)}")


@router.put("/modules/{module_name}/fields")
async def update_module_fields(module_name: str, field_config: Dict):
    """
    更新模块字段配置
    支持用户级配置保存
    """
    try:
        # 验证模块名称
        module_names = [m["name"] for m in MODULES_CONFIG]
        if module_name not in module_names:
            raise HTTPException(
                status_code=404,
                detail=f"模块 '{module_name}' 不存在")

        # 检查是否是用户配置格式
        if "user_id" in field_config and "user_config" in field_config:
            # 用户级配置保存
            user_id = field_config["user_id"]
            user_config = field_config["user_config"]

            # 保存用户配置
            success = await field_config_service.save_user_fields(
                module_name, user_id, user_config
            )

            if success:
                return {
                    "success": True,
                    "data": {"module_name": module_name, "user_id": user_id},
                    "message": "用户字段配置保存成功",
                }
            else:
                raise HTTPException(status_code=500, detail="用户配置保存失败")
        else:
            # 模块级配置保存
            await field_config_service.save_module_config(module_name, field_config)

            return {
                "success": True,
                "data": {"module_name": module_name},
                "message": "字段配置更新成功",
            }

    except HTTPException:
        raise
    except Exception:
        logger.error("更新字段配置失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"更新字段配置失败: {str(e)}")


@router.post("/modules/{module_name}/fields/select-all")
async def select_all_fields(module_name: str):
    """
    全选指定模块的所有字段
    """
    try:
        # 验证模块名称
        module_names = [m["name"] for m in MODULES_CONFIG]
        if module_name not in module_names:
            raise HTTPException(
                status_code=404,
                detail=f"模块 '{module_name}' 不存在")

        # 加载现有配置
        config = await field_config_service.load_module_config(module_name)
        if not config:
            raise HTTPException(
                status_code=404, detail=f"模块 '{module_name}' 配置不存在"
            )

        # 全选所有字段
        selected_count = 0
        for field_name, field_config in config.get("fields", {}).items():
            field_config["is_selected"] = True
            selected_count += 1

        # 更新统计信息
        config["selected_fields"] = selected_count

        # 保存配置
        await field_config_service.save_module_config(module_name, config)

        return {
            "success": True,
            "data": {
                "module_name": module_name,
                "selected_count": selected_count,
                "total_fields": config.get("total_fields", 0),
            },
            "message": f"已全选 {selected_count} 个字段",
        }

    except HTTPException:
        raise
    except Exception:
        logger.error("全选字段失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"全选字段失败: {str(e)}")


@router.post("/modules/{module_name}/fields/deselect-all")
async def deselect_all_fields(module_name: str):
    """
    取消选择指定模块的所有字段
    """
    try:
        # 验证模块名称
        module_names = [m["name"] for m in MODULES_CONFIG]
        if module_name not in module_names:
            raise HTTPException(
                status_code=404,
                detail=f"模块 '{module_name}' 不存在")

        # 加载现有配置
        config = await field_config_service.load_module_config(module_name)
        if not config:
            raise HTTPException(
                status_code=404, detail=f"模块 '{module_name}' 配置不存在"
            )

        # 取消选择所有字段
        for field_name, field_config in config.get("fields", {}).items():
            field_config["is_selected"] = False

        # 更新统计信息
        config["selected_fields"] = 0

        # 保存配置
        await field_config_service.save_module_config(module_name, config)

        return {
            "success": True,
            "data": {
                "module_name": module_name,
                "selected_count": 0,
                "total_fields": config.get("total_fields", 0),
            },
            "message": "已取消选择所有字段",
        }

    except HTTPException:
        raise
    except Exception:
        logger.error("取消选择字段失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"取消选择字段失败: {str(e)}")


@router.get("/mapping/{module_name}", response_model=FieldMappingResponse)
async def get_field_mapping(module_name: str):
    """
    获取指定模块的字段映射关系
    """
    try:
        # 验证模块名称
        module_names = [m["name"] for m in MODULES_CONFIG]
        if module_name not in module_names:
            raise HTTPException(
                status_code=404,
                detail=f"模块 '{module_name}' 不存在")

        # 获取字段映射
        mapping = await field_config_service.get_field_mapping(module_name)

        return {"success": True, "data": mapping, "message": "获取字段映射成功"}

    except HTTPException:
        raise
    except Exception:
        logger.error("获取字段映射失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"获取字段映射失败: {str(e)}")


@router.post("/validate/{module_name}")
async def validate_module_config(module_name: str):
    """
    验证指定模块的字段配置
    """
    try:
        # 验证模块名称
        module_names = [m["name"] for m in MODULES_CONFIG]
        if module_name not in module_names:
            raise HTTPException(
                status_code=404,
                detail=f"模块 '{module_name}' 不存在")

        # 验证配置
        validation_result = await field_config_service.validate_module_config(
            module_name
        )

        return {
            "success": True,
            "data": validation_result,
            "message": "配置验证完成"}

    except HTTPException:
        raise
    except Exception:
        logger.error("配置验证失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"配置验证失败: {str(e)}")


@router.delete("/modules/{module_name}/fields")
async def reset_module_config(module_name: str):
    """
    重置指定模块的字段配置
    """
    try:
        # 验证模块名称
        module_names = [m["name"] for m in MODULES_CONFIG]
        if module_name not in module_names:
            raise HTTPException(
                status_code=404,
                detail=f"模块 '{module_name}' 不存在")

        # 重置配置
        result = await field_config_service.reset_module_config(module_name)

        if result:
            return {
                "success": True,
                "data": {"module_name": module_name},
                "message": "模块配置重置成功",
            }
        else:
            raise HTTPException(status_code=500, detail="模块配置重置失败")

    except HTTPException:
        raise
    except Exception:
        logger.error("模块配置重置失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"模块配置重置失败: {str(e)}")


@router.delete("/clear-all")
async def clear_all_configurations(
    confirm: str = Query(..., description="确认参数，必须输入'CONFIRM'才能执行")
):
    """
    一键清空所有模块的字段配置
    ⚠️ 危险操作：会删除所有15个模块的字段配置文件

    Args:
        confirm: 确认参数，必须输入'CONFIRM'
    """
    try:
        # 安全确认机制
        if confirm != "CONFIRM":
            raise HTTPException(
                status_code=400,
                detail="确认参数错误。要执行此危险操作，请在confirm参数中输入'CONFIRM'",
            )

        # 获取所有模块
        module_names = [m["name"] for m in MODULES_CONFIG]

        clear_results = []
        success_count = 0
        failure_count = 0

        logger.warning("开始执行一键清空所有配置", total_modules=len(module_names))

        for module_name in module_names:
            try:
                # 重置每个模块的配置
                result = await field_config_service.reset_module_config(module_name)

                if result:
                    clear_results.append(
                        {
                            "module_name": module_name,
                            "display_name": next(
                                (
                                    m["display"]
                                    for m in MODULES_CONFIG
                                    if m["name"] == module_name
                                ),
                                module_name,
                            ),
                            "success": True,
                            "message": "配置已清空",
                        }
                    )
                    success_count += 1
                else:
                    clear_results.append(
                        {
                            "module_name": module_name,
                            "display_name": next(
                                (
                                    m["display"]
                                    for m in MODULES_CONFIG
                                    if m["name"] == module_name
                                ),
                                module_name,
                            ),
                            "success": False,
                            "message": "清空失败",
                        }
                    )
                    failure_count += 1

            except Exception:
                logger.error("清空模块配置失败", module_name=module_name, error=str(e))
                clear_results.append(
                    {
                        "module_name": module_name,
                        "display_name": next(
                            (
                                m["display"]
                                for m in MODULES_CONFIG
                                if m["name"] == module_name
                            ),
                            module_name,
                        ),
                        "success": False,
                        "message": f"清空失败: {str(e)}",
                    }
                )
                failure_count += 1

        logger.warning(
            "一键清空配置完成",
            success_count=success_count,
            failure_count=failure_count)

        return {
            "success": True,
            "data": {
                "results": clear_results,
                "total_modules": len(module_names),
                "success_count": success_count,
                "failure_count": failure_count,
                "operation": "clear_all_configurations",
            },
            "message": f"一键清空完成：成功{success_count}个，失败{failure_count}个",
        }

    except HTTPException:
        raise
    except Exception:
        logger.error("一键清空配置失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"一键清空配置失败: {str(e)}")


@router.put("/modules/{module_name}/config-name")
async def update_config_name(
    module_name: str, config_name: str = Query(..., description="新的配置名称")
):
    """
    更新模块配置名称

    Args:
        module_name: 模块名称
        config_name: 新的配置名称
    """
    try:
        # 验证模块名称
        module_names = [m["name"] for m in MODULES_CONFIG]
        if module_name not in module_names:
            raise HTTPException(
                status_code=404,
                detail=f"模块 '{module_name}' 不存在")

        # 加载现有配置
        config = await field_config_service.load_module_config(module_name)
        if not config:
            raise HTTPException(
                status_code=404, detail=f"模块 '{module_name}' 配置不存在"
            )

        # 更新配置名称
        old_name = config.get(
            'config_name', config.get(
                'display_name', module_name))
        config['config_name'] = config_name.strip()

        # 保存配置
        success = await field_config_service.save_module_config(module_name, config)

        if success:
            return {
                "success": True,
                "data": {
                    "module_name": module_name,
                    "old_config_name": old_name,
                    "new_config_name": config_name,
                },
                "message": "配置名称更新成功",
            }
        else:
            raise HTTPException(status_code=500, detail="配置名称更新失败")

    except HTTPException:
        raise
    except Exception:
        logger.error("更新配置名称失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"更新配置名称失败: {str(e)}")


@router.get("/modules/{module_name}/config-name")
async def get_config_name(module_name: str):
    """
    获取模块配置名称

    Args:
        module_name: 模块名称
    """
    try:
        # 验证模块名称
        module_names = [m["name"] for m in MODULES_CONFIG]
        if module_name not in module_names:
            raise HTTPException(
                status_code=404,
                detail=f"模块 '{module_name}' 不存在")

        # 加载配置
        config = await field_config_service.load_module_config(module_name)

        if config:
            config_name = (config.get('config_name')
                           or config.get('display_name') or module_name)
            api_name = config.get('display_name', module_name)
            json_name = config.get('config_name')
        else:
            # 使用默认名称
            module_info = next(
                (m for m in MODULES_CONFIG if m["name"] == module_name), None
            )
            config_name = module_info["display"] if module_info else module_name
            api_name = config_name
            json_name = None

        return {
            "success": True,
            "data": {
                "module_name": module_name,
                "current_config_name": config_name,
                "api_display_name": api_name,
                "json_config_name": json_name,
                "is_custom_name": json_name is not None and json_name != api_name,
            },
            "message": "获取配置名称成功",
        }

    except HTTPException:
        raise
    except Exception:
        logger.error("获取配置名称失败", module_name=module_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"获取配置名称失败: {str(e)}")


@router.post("/regenerate-md-mappings")
async def regenerate_md_mappings():
    """
    重新生成MD映射文件
    从所有MD文档提取字段映射并保存到md_mappings.json
    """
    try:

        # 获取转换器脚本路径 - 修复Windows路径问题
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 从 backend/app/api/v1 向上回到 v3 目录
        v3_root = os.path.abspath(
            os.path.join(
                current_dir,
                "..",
                "..",
                "..",
                ".."))
        converter_script = os.path.join(
            v3_root, "tools", "md_to_json_converter.py")

        if not os.path.exists(converter_script):
            raise HTTPException(status_code=404, detail="MD转换器脚本不存在")

        # 运行转换器
        logger.info("开始重新生成MD映射文件")
        result = subprocess.run(
            ["python", converter_script],
            cwd=os.path.dirname(converter_script),
            capture_output=True,
            text=True,
            encoding='utf-8',
        )

        if result.returncode == 0:
            # 解析输出获取统计信息
            output_lines = result.stdout.split('\n')
            stats = {}
            for line in output_lines:
                if "总模块数:" in line:
                    stats["modules"] = line.split(":")[-1].strip()
                elif "总字段映射数:" in line:
                    stats["total_mappings"] = line.split(":")[-1].strip()

            # 重新加载映射文件到服务
            field_config_service.md_mappings = field_config_service._load_md_mappings()

            return {
                "success": True,
                "message": "MD映射文件重新生成成功",
                "data": {"stats": stats, "output": result.stdout},
            }
        else:
            logger.error("MD映射文件生成失败", stderr=result.stderr)
            raise HTTPException(
                status_code=500,
                detail=f"生成失败: {result.stderr}")

    except Exception:
        logger.error("重新生成MD映射文件失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"重新生成MD映射文件失败: {str(e)}")


@router.get("/md-mappings-stats")
async def get_md_mappings_stats():
    """
    获取MD映射文件统计信息
    """
    try:
        mappings = field_config_service.md_mappings

        if not mappings:
            return {
                "success": False,
                "message": "MD映射文件未加载或为空",
                "data": {
                    "total_modules": 0,
                    "total_mappings": 0,
                    "modules": {}},
            }

        # 统计信息
        total_modules = len(mappings)
        total_mappings = sum(
            len(module_mappings) for module_mappings in mappings.values()
        )

        # 每个模块的统计
        module_stats = {}
        for module_name, module_mappings in mappings.items():
            module_display = next(
                (m["display"]
                 for m in MODULES_CONFIG if m["name"] == module_name),
                module_name,
            )
            module_stats[module_name] = {
                "display_name": module_display,
                "mappings_count": len(module_mappings),
                "sample_mappings": dict(
                    list(module_mappings.items())[:5]
                ),  # 显示前5个映射作为示例
            }

        return {
            "success": True,
            "message": "获取MD映射统计成功",
            "data": {
                "total_modules": total_modules,
                "total_mappings": total_mappings,
                "modules": module_stats,
                "file_path": field_config_service.md_mappings_path,
            },
        }

    except Exception:
        logger.error("获取MD映射统计失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取MD映射统计失败: {str(e)}")
