# YS-API V3.0 防重复文件机制

## 🛡️ 多层防护体系

本项目实施了5层防重复机制，确保AI和开发者不会创建重复功能的文件。

### 1️⃣ 哨兵文件机制（立即生效）

**文件**: `file_sentinel.py`

**原理**: 每次创建文件时自动生成`.done`哨兵文件，下次创建前先检查

**使用方法**:
```python
from file_sentinel import smart_create_file

# AI调用这个函数代替直接创建文件
success = smart_create_file(
    file_path="new_tool.py",
    content="# 代码内容",
    purpose="健康检查工具",
    file_type="python"
)
```

**保护范围**: 所有文件创建

### 2️⃣ Pre-commit钩子（提交前拦截）

**文件**: `.pre-commit-config.yaml`

**原理**: Git提交前自动运行重复检查，不通过就拒绝提交

**安装方法**:
```bash
pip install pre-commit
pre-commit install
```

**保护范围**: 
- 代码重复检查（JSCPD）
- 函数重复检查
- 命名冲突检查
- 大文件检查

### 3️⃣ 智能创建器（AI调用接口）

**文件**: `smart_file_creator.py`

**原理**: AI必须通过这个接口创建文件，自动检查并建议替代方案

**AI使用方法**:
```python
from smart_file_creator import ai_create_file, ai_check_before_create

# 创建前检查
check_result = ai_check_before_create("健康检查工具")
if not check_result['can_create']:
    print("已存在相同功能文件:", check_result['existing_files'])

# 智能创建
success = ai_create_file(
    file_path="health_checker.py",
    content="...",
    purpose="健康检查工具",
    ai_context="用户要求创建健康检查"
)
```

### 4️⃣ 代码所有权（GitHub保护）

**文件**: `CODEOWNERS`

**原理**: 特定类型文件只能由指定团队审查创建

**保护规则**:
- `*fixer*.py` → 工具团队审查
- `*checker*.py` → 工具团队审查  
- `test_*.py` → QA团队审查
- `config.*` → 配置团队审查

### 5️⃣ 功能检查器（自动检测）

**文件**: 
- `function_duplicate_checker.py` - 检查重复函数
- `check_naming_conflicts.py` - 检查命名冲突

**使用方法**:
```bash
# 检查重复函数
python function_duplicate_checker.py *.py

# 检查命名冲突  
python check_naming_conflicts.py *.py
```

## 🚀 AI调用示例

### ✅ 推荐的AI创建流程

```python
# 1. 先检查是否已存在
from smart_file_creator import ai_check_before_create

check = ai_check_before_create("数据库连接管理", "python")
if not check['can_create']:
    print("建议:", check['suggestions'])
    # 不创建新文件，而是扩展现有文件

# 2. 如果确实需要创建
from smart_file_creator import ai_create_file

success = ai_create_file(
    file_path="db_manager.py", 
    content="""
#!/usr/bin/env python3
# 数据库连接管理器
class DatabaseManager:
    def connect(self):
        pass
    """,
    purpose="数据库连接管理",
    file_type="python",
    ai_context="用户需要统一管理数据库连接"
)
```

### ❌ 禁止的创建方式

```python
# 直接创建文件 - 会被哨兵拦截
with open("db_manager.py", "w") as f:
    f.write("...")

# 使用可疑文件名 - 会被命名检查拦截  
ai_create_file("db_manager_v2.py", ...)
ai_create_file("db_manager_copy.py", ...)
ai_create_file("db_manager_new.py", ...)
```

## 📊 监控和维护

### 查看保护状态
```bash
# 列出所有受保护文件
python file_sentinel.py --list

# 清理孤儿哨兵
python file_sentinel.py --cleanup

# 检查特定用途
python file_sentinel.py --check "健康检查"
```

### 手动运行检查
```bash
# 运行所有检查
pre-commit run --all-files

# 只运行重复检查
python function_duplicate_checker.py *.py
python check_naming_conflicts.py *.py
```

## 🎯 效果预期

实施后预期效果：

1. **100%拦截重复文件**: 哨兵机制确保同用途文件不会重复创建
2. **智能建议**: AI会被引导扩展现有文件而不是创建新文件
3. **提交保护**: 即使绕过前面的检查，pre-commit也会拦截
4. **团队协作**: CODEOWNERS确保关键文件由专人审查
5. **持续监控**: 自动检测器发现潜在重复问题

## 🔧 故障排除

### 哨兵系统问题
```bash
# 如果哨兵系统误拦截，可以强制创建
python -c "
from smart_file_creator import SmartFileCreator
creator = SmartFileCreator()
creator.sentinel.write_if_absent('file.py', 'content', 'purpose', force=True)
"
```

### Pre-commit问题
```bash
# 跳过pre-commit检查（紧急情况）
git commit -m "message" --no-verify

# 更新pre-commit钩子
pre-commit autoupdate
```

---

**重要**: 所有AI都应该使用 `smart_file_creator.py` 接口，而不是直接创建文件!
