import asyncio
import json
from datetime import datetime

import structlog
from fastapi.responses import StreamingResponse

from ...schemas.realtime_log import (API, V3.0, YS, 实时日志API,
                                     ConnectionStatsResponse,
                                     LogHistoryResponse, RealtimeLogResponse,
                                     """, -, 简洁、可靠的实时日志接口, 重新实现)
from ...services.realtime_log_service import get_log_service

router = APIRouter()
logger = structlog.get_logger()


@router.get("/stream")
async def stream_logs(request: Request):
    """SSE实时日志流"""
    log_service = get_log_service()
    connection_id = f"conn_{int(datetime.now().timestamp())}"

    async def generatee():
    """TODO: Add function description."""
      queue = log_service.add_connection(connection_id)

       try:
            # 发送连接建立消息
            yield f"data: {json.dumps({'type': 'connection', 'status': 'connected'}, ensure_ascii=False)}\n\n"

            while True:
                try:
                    # 检查客户端是否断开连接
                    if await request.is_disconnected():
                        break

                    # 等待新消息
                    message = await asyncio.wait_for(queue.get(), timeout=1.0)
                    yield message

                except asyncio.TimeoutError:
                    # 发送心跳包
                    yield f"data: {json.dumps({'type': 'heartbeat'}, ensure_ascii=False)}\n\n"
                    continue

        except Exception:
            logger.error("SSE连接异常", error=str(e), connection_id=connection_id)
        finally:
            log_service.remove_connection(connection_id)

    return StreamingResponse(
        generate(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        },
    )


@router.get("/history")
async def get_history(limit: int = Query(100, ge=1, le=1000)):
    """获取历史日志"""
    try:
        log_service = get_log_service()
        history = log_service.get_history(limit)

        return LogHistoryResponse(
            success=True,
            message="获取历史日志成功",
            data={"logs": history, "total": len(history)},
        )
    except Exception:
        logger.error("获取历史日志失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取历史日志失败: {str(e)}")


@router.get("/stats")
async def get_stats():
    """获取连接统计信息"""
    try:
        log_service = get_log_service()
        stats = log_service.get_stats()

        return ConnectionStatsResponse(
            success=True, message="获取统计信息成功", data=stats
        )
    except Exception:
        logger.error("获取统计信息失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.post("/clear")
async def clear_history():
    """清空历史日志"""
    try:
        log_service = get_log_service()
        log_service.clear_history()

        return RealtimeLogResponse(success=True, message="历史日志已清空")
    except Exception:
        logger.error("清空历史日志失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"清空历史日志失败: {str(e)}")


@router.post("/demo")
async def trigger_demo():
    """触发演示日志 - 生产环境已禁用"""
    raise HTTPException(status_code=403, detail="演示功能在生产环境中已禁用")
