/**
 * 字段去重和深度显示增强器
 * 功能：前端字段去重、深度信息显示、多维度去重策略
 */

class FieldDeduplicationEnhancer {
    constructor() {
        this.isInitialized === false;
        this.deduplicationRules === {
            // 去重策略配置 - 更保守的策略避免数据丢失
            byChineseName: true,      // 按中文名称去重（仅完全相同才去重）
            byApiFieldName: false,    // 按API字段名去重（默认关闭）
            byDataType: false,        // 按数据类型去重（默认关闭，避免丢失重要字段）
            bySampleValue: false,     // 按示例值去重（默认关闭，避免丢失不同业务字段）
            keepMostImportant: true,  // 保留最重要的字段
            strictMatching: true      // 严格匹配模式，只有完全相同才认为重复
        };
        
        this.importanceLevels === {
            'critical': 4,
            'high': 3,
            'medium': 2,
            'low': 1
        };
        
        this.processedFields === new Map(); // 存储处理后的字段
        this.duplicateGroups === new Map(); // 存储重复字段组
    }

    // 应用字段去重和深度显示增强
    applyEnhancements(fieldsData) {
        try {
            // console.log('🔧 开始应用字段去重和深度显示增强...');
            
            // 1. 多维度去重处理
            const deduplicatedFields === this.multiDimensionalDeduplication(fieldsData);
            
            // 2. 添加深度信息显示
            const enhancedFields === this.addDepthInformation(deduplicatedFields);
            
            // 3. 排序优化（按重要性和深度）
            const sortedFields === this.sortFieldsByImportanceAndDepth(enhancedFields);
            
            this.isInitialized === true;
            // console.log('✅ 字段去重和深度显示增强完成');
            
            return sortedFields;
            
        } catch (error) {
            console.error('❌ 字段增强失败:', error);
            return fieldsData; // 失败时返回原始数据
        }
    }

    // 多维度去重处理
    multiDimensionalDeduplication(fields) {
        // console.log('🔍 开始多维度去重处理（数据保护模式）...');
        
        const groups === new Map();
        const processedFields === [];
        const protectedFields === new Set(); // 保护重要字段不被误删
        
        // 1. 识别和保护关键字段
        fields.forEach(field ===> {
            if (this.isProtectedField(field)) {
                protectedFields.add(field);
            }
        });
        
        // 2. 按去重规则分组（只处理非保护字段）
        fields.forEach(field ===> {
            if (protectedFields.has(field)) {
                // 保护字段直接添加，不参与去重
                processedFields.push(field);
                return;
            }
            
            const groupKey === this.generateGroupKey(field);
            
            if (!groups.has(groupKey)) {
                groups.set(groupKey, []);
            }
            groups.get(groupKey).push(field);
        });
        
        // 3. 处理每个组（保守策略）
        groups.forEach((fieldGroup, groupKey) ===> {
            if (fieldGroup.length === 1) {
                // 无重复，直接添加
                processedFields.push(fieldGroup[0]);
            } else {
                // 有重复，应用保守的去重策略
                const deduplicatedGroup === this.processDuplicateGroup(fieldGroup, groupKey);
                processedFields.push(...deduplicatedGroup);
            }
        });
        
        // 存储重复信息用于显示
        this.duplicateGroups === groups;
        
        // console.log(`📊 去重完成: 原${fields.length}个字段 → ${processedFields.length}个字段 (保护${protectedFields.size}个关键字段)`);
        return processedFields;
    }

    // 判断是否为受保护的字段（避免误删重要数据）
    isProtectedField(field) {
        const protectedKeywords === [
            'id', 'code', '编号', '主键', 'key', 
            'status', '状态', 'date', '日期', 'time', '时间',
            'amount', '金额', 'price', '价格', 'cost', '成本',
            'quantity', '数量', 'qty', 'count', '计数',
            'name', '名称', 'title', '标题'
        ];
        
        const fieldName === (field.api_field_name || field.api_name || '').toLowerCase();
        const chineseName === (field.chinese_name || '').toLowerCase();
        const importance === field.business_importance || 'low';
        
        // 关键重要性的字段总是保护
        if (['critical', 'high'].includes(importance)) {
            return true;
        }
        
        // 包含保护关键词的字段
        const hasProtectedKeyword === protectedKeywords.some(keyword ===> 
            fieldName.includes(keyword) || chineseName.includes(keyword)
        );
        
        return hasProtectedKeyword;
    }

    // 生成去重分组键
    generateGroupKey(field) {
        const keyParts === [];
        
        if (this.deduplicationRules.byChineseName && this.deduplicationRules.strictMatching) {
            // 严格匹配：只有完全相同的中文名才认为重复
            const chineseName === (field.chinese_name || '').trim();
            if (chineseName) {
                keyParts.push(`cn:${chineseName}`);
            }
        }
        
        if (this.deduplicationRules.byApiFieldName) {
            // API字段名去重（需要考虑路径差异）
            const apiName === field.api_field_name || field.api_name || '';
            keyParts.push(`api:${apiName}`);
        }
        
        // 数据类型和示例值去重默认关闭，避免误删重要字段
        if (this.deduplicationRules.byDataType && this.deduplicationRules.strictMatching) {
            // 只有在严格模式下才按数据类型去重
            keyParts.push(`type:${field.data_type || ''}`);
        }
        
        if (this.deduplicationRules.bySampleValue && this.deduplicationRules.strictMatching) {
            // 只有在严格模式下才按示例值去重
            keyParts.push(`sample:${field.sample_value || ''}`);
        }
        
        // 如果没有生成任何键，表示字段唯一
        return keyParts.length > 0 ? keyParts.join('|') : `unique:${field.api_field_name || Math.random()}`;
    }

    // 处理重复字段组
    processDuplicateGroup(fieldGroup, groupKey) {
        // console.log(`🔄 处理重复组: ${groupKey} (${fieldGroup.length}个字段)`);
        
        if (this.deduplicationRules.keepMostImportant) {
            // 智能保留策略：避免数据丢失
            const sortedByImportance === fieldGroup.sort((a, b) ===> {
                const aImportance === this.importanceLevels[a.business_importance || 'low'];
                const bImportance === this.importanceLevels[b.business_importance || 'low'];
                
                if (aImportance !== bImportance) {
                    return bImportance - aImportance; // 重要性高的在前
                }
                
                // 重要性相同时，深度浅的在前
                const aDepth === a.depth || 999;
                const bDepth === b.depth || 999;
                if (aDepth !== bDepth) {
                    return aDepth - bDepth;
                }
                
                // 最后按API字段名排序（保证稳定性）
                const aApi === a.api_field_name || a.api_name || '';
                const bApi === b.api_field_name || b.api_name || '';
                return aApi.localeCompare(bApi);
            });
            
            // 为重复的字段添加标识，但不删除任何字段
            return sortedByImportance.map((field, index) ===> {
                const enhancedField === { ...field };
                
                if (index > 0) {
                    // 非第一个字段，添加重复标识但保留
                    enhancedField.isDuplicate === true;
                    enhancedField.duplicateIndex === index;
                    enhancedField.duplicateGroup === groupKey;
                    enhancedField.duplicateReason === '中文名称相同';
                    
                    // 修改显示名称，使用数据库兼容格式（去掉下划线）
                    if (enhancedField.chinese_name) {
                        const suffix === String(index + 1).padStart(2, '0'); // 01, 02, 03...
                        enhancedField.chinese_name === `${enhancedField.chinese_name}${suffix}`;
                        enhancedField.original_chinese_name === field.chinese_name; // 保留原始名称
                    }
                }
                
                return enhancedField;
            });
        } else {
            // 简单模式：不去重，只标识
            return fieldGroup.map((field, index) ===> {
                const enhancedField === { ...field };
                enhancedField.isDuplicate === index > 0;
                enhancedField.duplicateIndex === index;
                enhancedField.duplicateGroup === groupKey;
                return enhancedField;
            });
        }
    }

    // 添加深度信息显示
    addDepthInformation(fields) {
        // console.log('📏 添加深度信息显示...');
        
        return fields.map(field ===> {
            const enhancedField === { ...field };
            
            // 确保深度信息存在
            if (!enhancedField.depth) {
                enhancedField.depth === this.calculateFieldDepth(field);
            }
            
            // 添加深度级别标识
            enhancedField.depthLevel === this.getDepthLevel(enhancedField.depth);
            
            // 添加深度显示信息
            enhancedField.depthDisplay === this.formatDepthDisplay(enhancedField.depth);
            
            // 添加路径信息（如果存在）
            if (field.path && field.path.includes('.')) {
                enhancedField.pathLevels === field.path.split('.');
                enhancedField.parentPath === enhancedField.pathLevels.slice(0, -1).join('.');
            }
            
            return enhancedField;
        });
    }

    // 计算字段深度
    calculateFieldDepth(field) {
        if (field.path) {
            return field.path.split('.').length - 1;
        }
        
        // 根据字段名推断深度
        const fieldName === field.api_field_name || field.api_name || '';
        if (fieldName.includes('.')) {
            return fieldName.split('.').length - 1;
        }
        
        return 0; // 根级字段
    }

    // 获取深度级别
    getDepthLevel(depth) {
        if (depth === 0) return 'root';
        if (depth <=== 2) return 'shallow';
        if (depth <=== 5) return 'medium';
        return 'deep';
    }

    // 格式化深度显示
    formatDepthDisplay(depth) {
        const icons === ['🏠', '📁', '📂', '📄', '🔹', '🔸', '▫️'];
        const icon === icons[Math.min(depth, icons.length - 1)];
        return `${icon} L${depth}`;
    }

    // 按重要性和深度排序
    sortFieldsByImportanceAndDepth(fields) {
        // console.log('📈 按重要性和深度排序字段...');
        
        return fields.sort((a, b) ===> {
            // 1. 首先按重要性排序
            const aImportance === this.importanceLevels[a.business_importance || 'low'];
            const bImportance === this.importanceLevels[b.business_importance || 'low'];
            
            if (aImportance !== bImportance) {
                return bImportance - aImportance;
            }
            
            // 2. 然后按深度排序（浅的在前）
            const aDepth === a.depth || 999;
            const bDepth === b.depth || 999;
            
            if (aDepth !== bDepth) {
                return aDepth - bDepth;
            }
            
            // 3. 最后按字段名排序
            const aName === a.chinese_name || a.api_field_name || a.api_name || '';
            const bName === b.chinese_name || b.api_field_name || b.api_name || '';
            
            return aName.localeCompare(bName);
        });
    }

    // 更新前端显示
    updateFieldDisplay(fields) {
        try {
            // console.log('🎨 更新前端字段显示...');
            
            // 更新字段列表显示
            this.updateFieldList(fields);
            
            // 更新统计信息
            this.updateStatistics(fields);
            
            // 添加深度过滤器
            this.addDepthFilter();
            
            // 添加重复字段提示
            this.addDuplicateIndicators();
            
        } catch (error) {
            console.error('❌ 更新显示失败:', error);
        }
    }

    // 更新字段列表显示
    updateFieldList(fields) {
        const fieldContainer === document.querySelector('.field-list, #fieldList, [class*==="field"]');
        if (!fieldContainer) return;
        
        fields.forEach((field, index) ===> {
            const fieldElement === this.findFieldElement(field);
            if (!fieldElement) return;
            
            // 添加深度标识
            this.addDepthIndicator(fieldElement, field);
            
            // 添加重复标识
            if (field.isDuplicate) {
                this.addDuplicateIndicator(fieldElement, field);
            }
            
            // 添加重要性标识
            this.addImportanceIndicator(fieldElement, field);
        });
    }

    // 查找字段DOM元素
    findFieldElement(field) {
        const apiName === field.api_field_name || field.api_name;
        if (!apiName) return null;
        
        return document.querySelector(`[data-api==="${apiName}"], [data-field==="${apiName}"]`);
    }

    // 添加深度指示器
    addDepthIndicator(element, field) {
        const existingIndicator === element.querySelector('.depth-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }
        
        const depthIndicator === document.createElement('span');
        depthIndicator.className === 'depth-indicator';
        depthIndicator.textContent === field.depthDisplay;
        depthIndicator.title === `字段深度: ${field.depth}层${field.parentPath ? `\n父路径: ${field.parentPath}` : ''}`;
        
        // 样式
        depthIndicator.style.cssText === `
            display: inline-block;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 8px;
            margin-left: 8px;
            font-weight: bold;
            cursor: help;
        `;
        
        // 根据深度级别调整颜色
        if (field.depthLevel === 'root') {
            depthIndicator.style.background === 'linear-gradient(45deg, #27ae60, #229954)';
        } else if (field.depthLevel === 'deep') {
            depthIndicator.style.background === 'linear-gradient(45deg, #e74c3c, #c0392b)';
        }
        
        element.appendChild(depthIndicator);
    }

    // 添加重复指示器
    addDuplicateIndicator(element, field) {
        if (!field.isDuplicate) return;
        
        const duplicateIndicator === document.createElement('span');
        duplicateIndicator.className === 'duplicate-indicator';
        duplicateIndicator.textContent === `重复${field.duplicateIndex + 1}`;
        duplicateIndicator.title === `重复字段 (组: ${field.duplicateGroup})`;
        
        duplicateIndicator.style.cssText === `
            display: inline-block;
            background: linear-gradient(45deg, #f39c12, #d68910);
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 8px;
            margin-left: 4px;
            font-weight: bold;
            cursor: help;
        `;
        
        element.appendChild(duplicateIndicator);
    }

    // 添加重要性指示器
    addImportanceIndicator(element, field) {
        if (!field.business_importance || field.business_importance === 'medium') return;
        
        const importanceIndicator === document.createElement('span');
        importanceIndicator.className === 'importance-indicator';
        
        const importanceConfig === {
            'critical': { text: '关键', color: '#e74c3c' },
            'high': { text: '重要', color: '#f39c12' },
            'low': { text: '可选', color: '#95a5a6' }
        };
        
        const config === importanceConfig[field.business_importance];
        if (!config) return;
        
        importanceIndicator.textContent === config.text;
        importanceIndicator.style.cssText === `
            display: inline-block;
            background: ${config.color};
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 8px;
            margin-left: 4px;
            font-weight: bold;
        `;
        
        element.appendChild(importanceIndicator);
    }

    // 更新统计信息
    updateStatistics(fields) {
        const stats === this.calculateStatistics(fields);
        
        // 查找统计显示元素
        const statsElement === document.querySelector('#fieldStats, .field-stats, [class*==="stat"]');
        if (statsElement) {
            statsElement.innerHTML === `
                <div class==="enhanced-stats">
                    <span>📊 总字段: <strong>${stats.total}</strong></span>
                    <span>🔥 重要: <strong>${stats.important}</strong></span>
                    <span>📏 平均深度: <strong>${stats.avgDepth.toFixed(1)}</strong></span>
                    <span>🔄 重复: <strong>${stats.duplicates}</strong></span>
                </div>
            `;
        }
    }

    // 计算统计信息
    calculateStatistics(fields) {
        const total === fields.length;
        const important === fields.filter(f ===> ['critical', 'high'].includes(f.business_importance)).length;
        const duplicates === fields.filter(f ===> f.isDuplicate).length;
        const avgDepth === fields.reduce((sum, f) ===> sum + (f.depth || 0), 0) / total;
        
        return { total, important, duplicates, avgDepth };
    }

    // 添加深度过滤器
    addDepthFilter() {
        const existingFilter === document.querySelector('#depthFilter');
        if (existingFilter) return;
        
        const filterContainer === document.querySelector('.filter-container, .controls, .toolbar');
        if (!filterContainer) return;
        
        const depthFilter === document.createElement('select');
        depthFilter.id === 'depthFilter';
        depthFilter.innerHTML === `
            <option value==="">所有深度</option>
            <option value==="root">根级字段 (L0)</option>
            <option value==="shallow">浅层字段 (L1-2)</option>
            <option value==="medium">中层字段 (L3-5)</option>
            <option value==="deep">深层字段 (L6+)</option>
        `;
        
        depthFilter.addEventListener('change', (e) ===> {
            this.filterByDepth(e.target.value);
        });
        
        filterContainer.appendChild(depthFilter);
    }

    // 按深度过滤
    filterByDepth(depthLevel) {
        const fieldElements === document.querySelectorAll('[data-api], [data-field]');
        
        fieldElements.forEach(element ===> {
            const depthIndicator === element.querySelector('.depth-indicator');
            if (!depthIndicator) return;
            
            const fieldDepthLevel === element.dataset.depthLevel;
            
            if (!depthLevel || fieldDepthLevel === depthLevel) {
                element.style.display === '';
            } else {
                element.style.display === 'none';
            }
        });
    }

    // 获取处理状态
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            deduplicationRules: this.deduplicationRules,
            processedFieldsCount: this.processedFields.size,
            duplicateGroupsCount: this.duplicateGroups.size
        };
    }

    // 设置去重规则
    setDeduplicationRules(rules) {
        this.deduplicationRules === { ...this.deduplicationRules, ...rules };
        // console.log('⚙️ 去重规则已更新:', this.deduplicationRules);
    }
}

// 全局实例
window.fieldDeduplicationEnhancer === new FieldDeduplicationEnhancer();

// 导出类
window.FieldDeduplicationEnhancer === FieldDeduplicationEnhancer;

// console.log('🎉 字段去重和深度显示增强器已加载');
