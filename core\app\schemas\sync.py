from datetime import datetime

"""
YS-API V3.0 数据同步数据模型
定义数据同步相关的请求和响应结构
"""


class SyncRequest(BaseModel):
    """数据同步请求"""

    sync_type: str = Field(default="full", pattern="^(full|incremental)$")
    force_rebuild: bool = False
    record_limit: Optional[int] = None
    filters: Optional[Dict[str, Any]] = {}

    class Config:
        schema_extra = {
            "example": {
                "sync_type": "full",
                "force_rebuild": False,
                "record_limit": 1000,
                "filters": {
                    "date_from": "2025-01-01",
                    "date_to": "2025-01-13"},
            }}


class SyncResponse(BaseResponse):
    """数据同步响应"""

    data: Dict[str, Any]
    success: bool = True

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "task_id": "task_1234567890",
                    "module_name": "purchase_order",
                    "sync_type": "full",
                    "status": "started",
                    "message": "同步任务已启动",
                },
                "message": "模块同步任务已启动",
            }
        }


class SyncStatusResponse(BaseResponse):
    """同步状态响应"""

    data: TaskStatus
    success: bool = True

    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "data": {
                    "task_id": "task_1234567890",
                    "status": "running",
                    "progress": {
                        "current": 500,
                        "total": 1000,
                        "percentage": 50},
                    "module_name": "purchase_order",
                    "sync_type": "full",
                    "started_at": "2025-01-13T10:30:00Z",
                    "estimated_completion": "2025-01-13T10:31:00Z",
                    "records_processed": 500,
                    "records_success": 495,
                    "records_failed": 5,
                },
                "message": "获取任务状态成功",
            }}


class BatchSyncRequest(BaseModel):
    """批量同步请求"""

    modules: Optional[List[str]] = None
    sync_type: str = Field(default="full", pattern="^(full|incremental)$")
    parallel_tasks: int = Field(default=3, ge=1, le=5)
    force_rebuild: bool = False

    class Config:
        schema_extra = {
            "example": {
                "modules": ["purchase_order", "sales_order"],
                "sync_type": "full",
                "parallel_tasks": 3,
                "force_rebuild": False,
            }
        }


class SyncProgressInfo(BaseModel):
    """同步进度信息"""

    current: int
    total: int
    percentage: float
    estimated_completion: Optional[datetime] = None
    records_processed: int = 0
    records_success: int = 0
    records_failed: int = 0
    current_operation: Optional[str] = None


class SyncLogEntry(BaseModel):
    """同步日志条目"""

    id: int
    timestamp: datetime
    level: str
    module_name: Optional[str] = None
    message: str
    details: Optional[Dict[str, Any]] = {}
    error_type: Optional[str] = None
    task_id: Optional[str] = None


class SyncHistoryEntry(BaseModel):
    """同步历史条目"""

    id: int
    task_id: str
    module_name: str
    sync_type: str
    status: str
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration_seconds: Optional[int] = None
    records_processed: int = 0
    records_success: int = 0
    records_failed: int = 0
    error_message: Optional[str] = None
    trigger_type: str = "manual"  # manual, scheduled


class SyncStatistics(BaseModel):
    """同步统计信息"""

    total_modules: int
    active_modules: int
    total_syncs_today: int
    successful_syncs_today: int
    failed_syncs_today: int
    average_sync_duration: float
    last_full_sync: Optional[datetime] = None
    module_stats: Dict[str, Dict[str, Any]] = {}


class SyncTaskProgress(BaseModel):
    """同步任务进度详情"""

    task_id: str
    module_name: str
    sync_type: str
    status: str
    progress: SyncProgressInfo
    started_at: datetime
    estimated_completion: Optional[datetime] = None
    current_step: str
    steps_completed: List[str] = []
    steps_remaining: List[str] = []
    error_details: Optional[Dict[str, Any]] = None


class CancelSyncRequest(BaseModel):
    """取消同步请求"""

    reason: Optional[str] = None

    class Config:
        schema_extra = {"example": {"reason": "用户手动取消"}}


class SyncLogsResponse(PaginatedResponse):
    """同步日志响应"""


class SyncHistoryResponse(PaginatedResponse):
    """同步历史响应"""


class SyncStatisticsResponse(BaseResponse):
    """同步统计响应"""

    data: SyncStatistics
    success: bool = True
