import ast
import sys
from collections import defaultdict
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件重复检查器 - Pre-commit Hook
"""


class FunctionDuplicateChecker:
    def __init___(self):
    """TODO: Add function description."""
    self.function_signatures = defaultdict(list)
    self.class_signatures = defaultdict(list)

    def extract_signatures(self, file_path: Path):
        """提取文件中的函数和类签名"""
        try:
            content = file_path.read_text(encoding='utf-8')
            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    # 提取函数签名
                    args = [arg.arg for arg in node.args.args]
                    signature = f"def {node.name}({', '.join(args)})"
                    self.function_signatures[signature].append(str(file_path))

                elif isinstance(node, ast.ClassDef):
                    # 提取类名
                    self.class_signatures[node.name].append(str(file_path))

        except Exception:
            print(f"⚠️ 解析文件失败 {file_path}: {e}")

    def check_duplicates(self, files: list) -> bool:
        """检查重复并返回是否有问题"""
        has_duplicates = False

        for file_path in files:
            path = Path(file_path)
            if path.suffix == '.py' and path.exists():
                self.extract_signatures(path)

        # 检查重复函数
        print("🔍 检查重复函数...")
        for signature, files_list in self.function_signatures.items():
            if len(files_list) > 1:
                print(f"❌ 重复函数: {signature}")
                for file in files_list:
                    print(f"   - {file}")
                has_duplicates = True

        # 检查重复类
        print("🔍 检查重复类...")
        for class_name, files_list in self.class_signatures.items():
            if len(files_list) > 1:
                print(f"❌ 重复类: {class_name}")
                for file in files_list:
                    print(f"   - {file}")
                has_duplicates = True

        if not has_duplicates:
            print("✅ 未发现重复的函数或类")

        return has_duplicates


def find_duplicate_functions(files):
    """公共接口：查找重复函数"""
    checker = FunctionDuplicateChecker()
    return checker.check_duplicates(files)


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python function_duplicate_checker.py file1.py file2.py ...")
        return 0

    files = sys.argv[1:]
    checker = FunctionDuplicateChecker()

    has_duplicates = checker.check_duplicates(files)

    if has_duplicates:
        print("\n❌ 发现重复代码，请修复后再提交")
        return 1
    else:
        print("\n✅ 重复检查通过")
        return 0


if __name__ == "__main__":
    sys.exit(main())
