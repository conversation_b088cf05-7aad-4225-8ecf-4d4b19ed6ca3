<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面迁移管理控制台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2196F3, #21CBF3);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .stat-card .number {
            font-size: 2.5em;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 10px;
        }

        .stat-card .label {
            color: #666;
            font-size: 1.1em;
        }

        .section {
            margin-bottom: 40px;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
        }

        .section h2 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #2196F3;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #2196F3;
        }

        .btn {
            background: linear-gradient(135deg, #2196F3, #21CBF3);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }

        .btn.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .btn.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .tasks-grid {
            display: grid;
            gap: 20px;
            margin-top: 20px;
        }

        .task-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .task-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .task-url {
            font-weight: 600;
            color: #2196F3;
            font-size: 1.1em;
        }

        .task-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-in-progress {
            background: #cce5ff;
            color: #004085;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .task-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .task-detail {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
        }

        .task-detail .label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .task-detail .value {
            font-weight: 600;
            color: #333;
        }

        .log-container {
            background: #1e1e1e;
            color: #fff;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.error {
            color: #ff6b6b;
        }

        .log-entry.success {
            color: #51cf66;
        }

        .log-entry.info {
            color: #74c0fc;
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .template-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .template-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .template-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .template-components {
            margin-bottom: 15px;
        }

        .component-tag {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 12px;
            margin: 2px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2196F3, #21CBF3);
            transition: width 0.3s ease;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert.info {
            background: #cce5ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 页面迁移管理控制台</h1>
            <p>YS-API V3.0 业务页面迁移助手 - 统一管理和监控所有页面迁移任务</p>
        </div>

        <div class="main-content">
            <!-- 状态仪表板 -->
            <div class="dashboard" id="dashboard">
                <div class="stat-card">
                    <div class="number" id="total-tasks">0</div>
                    <div class="label">总任务数</div>
                </div>
                <div class="stat-card">
                    <div class="number" id="pending-tasks">0</div>
                    <div class="label">待处理</div>
                </div>
                <div class="stat-card">
                    <div class="number" id="completed-tasks">0</div>
                    <div class="label">已完成</div>
                </div>
                <div class="stat-card">
                    <div class="number" id="success-rate">0%</div>
                    <div class="label">成功率</div>
                </div>
            </div>

            <!-- 创建迁移任务 -->
            <div class="section">
                <h2>📝 创建迁移任务</h2>
                <div class="form-group">
                    <label for="page-url">页面URL：</label>
                    <input type="text" id="page-url" placeholder="请输入页面URL，如：field-config.html">
                </div>
                <div class="form-group">
                    <label for="priority">优先级：</label>
                    <select id="priority">
                        <option value="low">低</option>
                        <option value="medium" selected>中</option>
                        <option value="high">高</option>
                    </select>
                </div>
                <button class="btn" onclick="createMigrationTask()">创建任务</button>
                <button class="btn secondary" onclick="analyzePage()">分析页面</button>
            </div>

            <!-- 页面模板管理 -->
            <div class="section">
                <h2>🎨 页面模板</h2>
                <div class="templates-grid" id="templates-grid">
                    <!-- 模板卡片将在这里动态生成 -->
                </div>
            </div>

            <!-- 迁移任务列表 -->
            <div class="section">
                <h2>📋 迁移任务</h2>
                <div class="form-group" style="max-width: 300px;">
                    <label for="filter-status">筛选状态：</label>
                    <select id="filter-status" onchange="filterTasks()">
                        <option value="all">全部</option>
                        <option value="pending">待处理</option>
                        <option value="in-progress">进行中</option>
                        <option value="completed">已完成</option>
                        <option value="failed">失败</option>
                    </select>
                </div>
                <div class="tasks-grid" id="tasks-grid">
                    <!-- 任务卡片将在这里动态生成 -->
                </div>
            </div>

            <!-- 实时日志 -->
            <div class="section">
                <h2>📊 实时日志</h2>
                <button class="btn secondary" onclick="clearLog()">清空日志</button>
                <div class="log-container" id="log-container">
                    <div class="log-entry info">[系统] 页面迁移控制台已启动</div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/core/page-migration-assistant.js"></script>
    <script>
        let migrationAssistant;
        let logContainer;

        document.addEventListener('DOMContentLoaded', function() {
            migrationAssistant = window.PageMigrationAssistant;
            logContainer = document.getElementById('log-container');
            
            // 初始化界面
            updateDashboard();
            renderTemplates();
            renderTasks();
            
            // 定期更新状态
            setInterval(updateDashboard, 5000);
            
            logInfo('页面迁移控制台初始化完成');
        });

        // 更新仪表板
        function updateDashboard() {
            const status = migrationAssistant.getStatusReport();
            
            document.getElementById('total-tasks').textContent = status.queue.total;
            document.getElementById('pending-tasks').textContent = status.queue.pending;
            document.getElementById('completed-tasks').textContent = status.queue.completed;
            document.getElementById('success-rate').textContent = status.performance.successRate + '%';
        }

        // 渲染模板
        function renderTemplates() {
            const templatesGrid = document.getElementById('templates-grid');
            const templates = [
                {
                    name: '字段配置页面',
                    key: 'field-config',
                    components: ['apiClient', 'fieldUtils', 'validationUtils', 'fieldDeduplicationEnhancer'],
                    priority: 'high',
                    description: '专用于字段配置和数据验证的页面模板'
                },
                {
                    name: '数据管理页面',
                    key: 'data-management',
                    components: ['apiClient', 'validationUtils', 'errorHandler', 'notificationSystem'],
                    priority: 'high',
                    description: '用于数据增删改查操作的页面模板'
                },
                {
                    name: '报表页面',
                    key: 'report',
                    components: ['apiClient', 'fieldUtils', 'errorHandler'],
                    priority: 'medium',
                    description: '用于数据展示和报表生成的页面模板'
                },
                {
                    name: '通用业务页面',
                    key: 'general',
                    components: ['apiClient', 'errorHandler', 'notificationSystem'],
                    priority: 'low',
                    description: '适用于一般业务功能的通用页面模板'
                }
            ];

            templatesGrid.innerHTML = templates.map(template => `
                <div class="template-card">
                    <div class="template-title">${template.name}</div>
                    <p style="color: #666; margin-bottom: 15px;">${template.description}</p>
                    <div class="template-components">
                        <strong>依赖组件：</strong><br>
                        ${template.components.map(comp => 
                            `<span class="component-tag">${comp}</span>`
                        ).join('')}
                    </div>
                    <div style="margin-top: 15px;">
                        <strong>优先级：</strong>
                        <span class="task-status status-${template.priority === 'high' ? 'completed' : 
                            template.priority === 'medium' ? 'in-progress' : 'pending'}">
                            ${template.priority === 'high' ? '高' : 
                              template.priority === 'medium' ? '中' : '低'}
                        </span>
                    </div>
                </div>
            `).join('');
        }

        // 渲染任务列表
        function renderTasks() {
            const tasksGrid = document.getElementById('tasks-grid');
            const tasks = migrationAssistant.migrationQueue;
            
            if (tasks.length === 0) {
                tasksGrid.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <p style="font-size: 1.2em;">暂无迁移任务</p>
                        <p>请创建新的迁移任务开始使用</p>
                    </div>
                `;
                return;
            }

            tasksGrid.innerHTML = tasks.map(task => `
                <div class="task-card">
                    <div class="task-header">
                        <div class="task-url">${task.url}</div>
                        <div class="task-status status-${task.status}">
                            ${getStatusText(task.status)}
                        </div>
                    </div>
                    <div class="task-details">
                        <div class="task-detail">
                            <div class="label">任务ID</div>
                            <div class="value">${task.id}</div>
                        </div>
                        <div class="task-detail">
                            <div class="label">优先级</div>
                            <div class="value">${getPriorityText(task.priority)}</div>
                        </div>
                        <div class="task-detail">
                            <div class="label">创建时间</div>
                            <div class="value">${new Date(task.createdAt).toLocaleString()}</div>
                        </div>
                        ${task.analysis ? `
                        <div class="task-detail">
                            <div class="label">页面类型</div>
                            <div class="value">${task.analysis.pageType}</div>
                        </div>
                        ` : ''}
                    </div>
                    <div style="margin-top: 15px;">
                        ${task.status === 'pending' ? `
                            <button class="btn" onclick="migratePage(${task.id})">开始迁移</button>
                        ` : ''}
                        ${task.status === 'completed' ? `
                            <button class="btn success" onclick="viewMigrationResult(${task.id})">查看结果</button>
                        ` : ''}
                        ${task.status === 'failed' ? `
                            <button class="btn danger" onclick="retryMigration(${task.id})">重试</button>
                        ` : ''}
                        <button class="btn secondary" onclick="deleteTask(${task.id})">删除</button>
                    </div>
                    ${task.analysis && task.analysis.migrationRecommendation ? `
                    <div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                        <strong>迁移建议：</strong>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>估算时间：${task.analysis.migrationRecommendation.estimatedTime}</li>
                            <li>复杂度：${task.analysis.complexity}</li>
                            <li>所需组件：${task.analysis.migrationRecommendation.requiredComponents.join(', ')}</li>
                        </ul>
                    </div>
                    ` : ''}
                </div>
            `).join('');
        }

        // 创建迁移任务
        function createMigrationTask() {
            const url = document.getElementById('page-url').value.trim();
            const priority = document.getElementById('priority').value;
            
            if (!url) {
                showAlert('请输入页面URL', 'error');
                return;
            }
            
            try {
                const task = migrationAssistant.createMigrationTask(url, priority);
                logSuccess(`已创建迁移任务：${url} (ID: ${task.id})`);
                
                // 清空表单
                document.getElementById('page-url').value = '';
                document.getElementById('priority').value = 'medium';
                
                // 更新界面
                updateDashboard();
                renderTasks();
                
                showAlert('迁移任务创建成功', 'success');
            } catch (error) {
                logError('创建任务失败：' + error.message);
                showAlert('创建任务失败：' + error.message, 'error');
            }
        }

        // 分析页面
        async function analyzePage() {
            const url = document.getElementById('page-url').value.trim();
            
            if (!url) {
                showAlert('请输入页面URL', 'error');
                return;
            }
            
            try {
                logInfo(`开始分析页面：${url}`);
                const analysis = await migrationAssistant.analyzePage(url);
                
                if (analysis) {
                    logSuccess(`页面分析完成：${url}`);
                    logInfo(`页面类型：${analysis.pageType}`);
                    logInfo(`复杂度：${analysis.complexity}`);
                    logInfo(`估算时间：${analysis.migrationRecommendation.estimatedTime}`);
                    
                    showAlert('页面分析完成，请查看日志了解详情', 'success');
                } else {
                    logError(`页面分析失败：${url}`);
                    showAlert('页面分析失败', 'error');
                }
            } catch (error) {
                logError('分析页面失败：' + error.message);
                showAlert('分析页面失败：' + error.message, 'error');
            }
        }

        // 执行页面迁移
        async function migratePage(taskId) {
            try {
                logInfo(`开始迁移任务：${taskId}`);
                const result = await migrationAssistant.migratePage(taskId);
                
                logSuccess(`迁移任务完成：${taskId}`);
                logInfo(`验证结果：${result.validation.overall ? '通过' : '未通过'}`);
                
                updateDashboard();
                renderTasks();
                
                showAlert('页面迁移完成', 'success');
            } catch (error) {
                logError(`迁移任务失败：${taskId} - ${error.message}`);
                showAlert('页面迁移失败：' + error.message, 'error');
                
                updateDashboard();
                renderTasks();
            }
        }

        // 查看迁移结果
        function viewMigrationResult(taskId) {
            const task = migrationAssistant.migrationQueue.find(t => t.id === taskId) ||
                        migrationAssistant.migrationHistory.find(t => t.id === taskId);
            
            if (task && task.result) {
                const result = task.result;
                let message = `迁移结果：\n\n`;
                message += `验证结果：${result.validation.overall ? '✅ 通过' : '❌ 未通过'}\n`;
                message += `语法检查：${result.validation.syntax.valid ? '✅ 通过' : '❌ 失败'}\n`;
                message += `依赖检查：${result.validation.dependencies.valid ? '✅ 通过' : '❌ 失败'}\n`;
                message += `性能检查：${result.validation.performance.valid ? '✅ 通过' : '❌ 失败'}\n`;
                message += `\n完成时间：${new Date(result.completedAt).toLocaleString()}`;
                
                alert(message);
            }
        }

        // 重试迁移
        async function retryMigration(taskId) {
            const task = migrationAssistant.migrationQueue.find(t => t.id === taskId);
            if (task) {
                task.status = 'pending';
                task.error = null;
                
                logInfo(`重置任务状态：${taskId}`);
                renderTasks();
                updateDashboard();
            }
        }

        // 删除任务
        function deleteTask(taskId) {
            if (confirm('确定要删除这个任务吗？')) {
                const index = migrationAssistant.migrationQueue.findIndex(t => t.id === taskId);
                if (index !== -1) {
                    migrationAssistant.migrationQueue.splice(index, 1);
                    logInfo(`已删除任务：${taskId}`);
                    
                    updateDashboard();
                    renderTasks();
                    showAlert('任务已删除', 'info');
                }
            }
        }

        // 筛选任务
        function filterTasks() {
            const filter = document.getElementById('filter-status').value;
            const tasks = migrationAssistant.migrationQueue;
            
            // 这里可以实现筛选逻辑
            renderTasks();
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'pending': '待处理',
                'in-progress': '进行中',
                'completed': '已完成',
                'failed': '失败'
            };
            return statusMap[status] || status;
        }

        // 获取优先级文本
        function getPriorityText(priority) {
            const priorityMap = {
                'low': '低',
                'medium': '中',
                'high': '高'
            };
            return priorityMap[priority] || priority;
        }

        // 显示提示信息
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${type}`;
            alertDiv.textContent = message;
            
            const mainContent = document.querySelector('.main-content');
            mainContent.insertBefore(alertDiv, mainContent.firstChild);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // 日志函数
        function logInfo(message) {
            addLogEntry(message, 'info');
        }

        function logSuccess(message) {
            addLogEntry(message, 'success');
        }

        function logError(message) {
            addLogEntry(message, 'error');
        }

        function addLogEntry(message, type) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            logContainer.innerHTML = '<div class="log-entry info">[系统] 日志已清空</div>';
        }
    </script>
</body>
</html>
