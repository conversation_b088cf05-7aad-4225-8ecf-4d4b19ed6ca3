# 屎山代码绞杀 - Docker基础设施配置
# 支持Legacy和New系统并行运行，逐步切换流量

version: "3.8"

services:
  # 代理层 - 流量分发和路由
  strangler-proxy:
    build:
      context: .
      dockerfile: docker/strangler-proxy/Dockerfile
    ports:
      - "8080:8080"
    environment:
      - LEGACY_HOST=legacy-api
      - LEGACY_PORT=5000
      - NEW_HOST=new-api
      - NEW_PORT=5000
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - TRAFFIC_SPLIT_PERCENT=0 # 初始0%流量到新系统
    depends_on:
      - legacy-api
      - new-api
      - redis
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    networks:
      - strangler-network
    restart: unless-stopped

  # Legacy系统 (原有屎山代码)
  legacy-api:
    build:
      context: .
      dockerfile: docker/legacy/Dockerfile
    ports:
      - "5001:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=sqlite:///ysapi.db
    volumes:
      - ./backend:/app
      - ./config:/app/config
      - ./logs:/app/logs
    networks:
      - strangler-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 新系统 (重构后的干净代码)
  new-api:
    build:
      context: .
      dockerfile: docker/new-system/Dockerfile
    ports:
      - "5002:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=********************************************/ys_new
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    volumes:
      - ./new-system:/app
      - ./config:/app/config
      - ./logs:/app/logs
    networks:
      - strangler-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL - 新系统数据库
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=ys_new
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init:/docker-entrypoint-initdb.d
    networks:
      - strangler-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis - 缓存和会话存储
  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data
    networks:
      - strangler-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 监控和度量收集
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--storage.tsdb.path=/prometheus"
      - "--web.console.libraries=/etc/prometheus/console_libraries"
      - "--web.console.templates=/etc/prometheus/consoles"
      - "--storage.tsdb.retention.time=200h"
      - "--web.enable-lifecycle"
    networks:
      - strangler-network
    restart: unless-stopped

  # 可视化监控面板
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - strangler-network
    restart: unless-stopped

  # 日志收集
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.14.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - strangler-network
    restart: unless-stopped

  # 日志处理
  logstash:
    image: docker.elastic.co/logstash/logstash:7.14.0
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
    depends_on:
      - elasticsearch
    networks:
      - strangler-network
    restart: unless-stopped

  # 日志可视化
  kibana:
    image: docker.elastic.co/kibana/kibana:7.14.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - strangler-network
    restart: unless-stopped

  # 数据库迁移工具
  migration-runner:
    build:
      context: .
      dockerfile: docker/migration/Dockerfile
    environment:
      - SOURCE_DB=sqlite:///ysapi.db
      - TARGET_DB=********************************************/ys_new
    depends_on:
      - postgres
      - legacy-api
    volumes:
      - ./migration:/app/migration
      - ./backend:/app/legacy
      - ./logs:/app/logs
    networks:
      - strangler-network
    profiles:
      - migration # 仅在迁移时启动

  # 测试环境
  test-runner:
    build:
      context: .
      dockerfile: docker/test/Dockerfile
    environment:
      - TEST_LEGACY_URL=http://legacy-api:5000
      - TEST_NEW_URL=http://new-api:5000
      - TEST_PROXY_URL=http://strangler-proxy:8080
    depends_on:
      - legacy-api
      - new-api
      - strangler-proxy
    volumes:
      - ./tests:/app/tests
      - ./test-results:/app/results
    networks:
      - strangler-network
    profiles:
      - testing # 仅在测试时启动

networks:
  strangler-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:
