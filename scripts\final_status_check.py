import json
import sys
from datetime import datetime
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Windows环境屎山绞杀状态检查工具
解决编码问题，提供清晰的状态报告
"""


def check_modules_completion():
    """检查模块完成状态"""
    print("检查模块完成状态...")

    try:
        status_file = Path("tasks/module_status.json")
        if not status_file.exists():
            print("❌ 模块状态文件不存在")
            return False

        with open(status_file, "r", encoding="utf-8") as f:
            status = json.load(f)

        modules = status.get("modules", {})
        total_modules = len(modules)
        completed_modules = 0

        for module_name, module_data in modules.items():
            if module_data.get("completion_rate", 0) == 100:
                completed_modules += 1

        completion_rate = (
            completed_modules / total_modules * 100 if total_modules > 0 else 0
        )

        print(
            f"✅ 模块完成状态: {completed_modules}/{total_modules} ({completion_rate:.1f}%)"
        )

        return completion_rate == 100.0

    except Exception:
        print(f"❌ 模块状态检查失败: {e}")
        return False


def check_directory_structure():
    """检查目录结构"""
    print("检查目录结构...")

    required_dirs = ["scripts", "new-system/modules", "graveyard", "reports"]

    all_exist = True

    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ {dir_path} - 存在")
        else:
            print(f"❌ {dir_path} - 不存在")
            all_exist = False

    return all_exist


def check_migration_files():
    """检查迁移文件"""
    print("检查迁移文件...")

    new_modules_dir = Path("new-system/modules")
    graveyard_dir = Path("graveyard")

    if not new_modules_dir.exists():
        print("❌ 新系统模块目录不存在")
        return False

    if not graveyard_dir.exists():
        print("❌ graveyard备份目录不存在")
        return False

    new_modules = [d for d in new_modules_dir.iterdir() if d.is_dir()]
    graveyard_modules = [d for d in graveyard_dir.iterdir() if d.is_dir()]

    print(f"✅ 新系统模块: {len(new_modules)}个")
    print(f"✅ 备份模块: {len(graveyard_modules)}个")

    return len(new_modules) > 0


def generate_final_report():
    """生成最终报告"""
    print("\n" + "=" * 60)
    print("屎山代码绞杀任务最终状态报告")
    print("=" * 60)

    # 模块状态
    modules_ok = check_modules_completion()
    print()

    # 目录结构
    dirs_ok = check_directory_structure()
    print()

    # 迁移文件
    files_ok = check_migration_files()
    print()

    # 统计总体状态
    checks_passed = sum([modules_ok, dirs_ok, files_ok])
    total_checks = 3

    print("=" * 60)
    print("总体状态:")
    print(f"  通过检查: {checks_passed}/{total_checks}")
    print(f"  成功率: {checks_passed/total_checks*100:.1f}%")

    if checks_passed == total_checks:
        print("\n🎉 屎山代码绞杀任务圆满完成！")
        print("所有模块已成功迁移，系统准备就绪。")
        status = "success"
    elif checks_passed >= 2:
        print("\n⚠️ 基本完成，存在轻微问题")
        status = "warning"
    else:
        print("\n❌ 存在严重问题，需要修复")
        status = "error"

    # 下一步建议
    print("\n下一步建议:")
    if status == "success":
        print("1. 等待7天后清除graveyard目录")
        print("2. 启动生产环境验证")
        print("3. 开始新功能开发")
    elif status == "warning":
        print("1. 检查并修复发现的问题")
        print("2. 重新运行健康检查")
    else:
        print("1. 检查错误信息")
        print("2. 修复关键问题")
        print("3. 重新运行迁移")

    # 保存报告
    report = {
        "timestamp": datetime.now().isoformat(),
        "status": status,
        "checks_passed": checks_passed,
        "total_checks": total_checks,
        "success_rate": checks_passed / total_checks * 100,
        "modules_ok": modules_ok,
        "dirs_ok": dirs_ok,
        "files_ok": files_ok,
    }

    report_path = Path("reports/final_status_report.json")
    report_path.parent.mkdir(exist_ok=True)

    with open(report_path, "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    print(f"\n详细报告已保存: {report_path}")

    return 0 if status == "success" else (1 if status == "warning" else 2)


def main():
    """主函数"""
    try:
        return generate_final_report()
    except Exception:
        print(f"检查过程中发生错误: {e}")
        return 2


if __name__ == "__main__":
    sys.exit(main())
