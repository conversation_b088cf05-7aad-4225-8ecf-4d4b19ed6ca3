<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YS-API V3.0 统一页面加载器</title>
    <style>
        /* 统一的样式基础 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        /* 加载状态指示器 */
        .loading-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .loading-spinner {
            width: 24px;
            height: 24px;
            border: 3px solid #e9ecef;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-weight: 500;
            color: #495057;
        }

        /* 系统状态面板 */
        .status-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .status-card.success {
            border-left-color: #28a745;
        }

        .status-card.warning {
            border-left-color: #ffc107;
        }

        .status-card.error {
            border-left-color: #dc3545;
        }

        .status-card h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .status-card p {
            color: #6c757d;
            margin: 5px 0;
        }

        /* 组件面板 */
        .component-panel {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .component-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .component-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            text-align: center;
        }

        .component-item.loaded {
            border-color: #28a745;
            background: #f8fff9;
        }

        .component-item.error {
            border-color: #dc3545;
            background: #fff5f5;
        }

        .component-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .component-status {
            font-size: 0.9em;
            color: #6c757d;
        }

        /* 按钮样式 */
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                padding: 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .status-panel {
                grid-template-columns: 1fr;
            }

            .component-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1>YS-API V3.0</h1>
            <div class="subtitle">统一组件管理系统</div>
        </div>

        <!-- 加载状态指示器 -->
        <div class="loading-indicator" id="loadingIndicator">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在初始化系统...</div>
        </div>

        <!-- 系统状态面板 -->
        <div class="status-panel" id="statusPanel" style="display: none;">
            <div class="status-card" id="systemStatus">
                <h3>系统状态</h3>
                <p id="systemInfo">检查中...</p>
            </div>
            <div class="status-card" id="componentStatus">
                <h3>组件状态</h3>
                <p id="componentInfo">加载中...</p>
            </div>
            <div class="status-card" id="apiStatus">
                <h3>API状态</h3>
                <p id="apiInfo">连接中...</p>
            </div>
        </div>

        <!-- 组件面板 -->
        <div class="component-panel" id="componentPanel" style="display: none;">
            <h3>已加载组件</h3>
            <div class="component-list" id="componentList">
                <!-- 组件状态将动态填充 -->
            </div>
        </div>

        <!-- 操作按钮 -->
        <div style="text-align: center; margin-top: 30px;" id="actionButtons" style="display: none;">
            <button class="btn btn-primary" onclick="refreshStatus()">刷新状态</button>
            <button class="btn btn-success" onclick="runHealthCheck()">健康检查</button>
            <button class="btn btn-warning" onclick="showComponentDetails()">组件详情</button>
        </div>
    </div>

    <!-- 核心脚本加载 -->
    <script src="js/core/component-manager.js"></script>
    <script src="js/core/app-bootstrap.js"></script>
    
    <!-- 通用组件脚本 -->
    <script src="js/common/api-client.js"></script>
    <script src="js/common/field-utils.js"></script>
    <script src="js/common/validation-utils.js"></script>
    <script src="js/common/error-handler.js"></script>
    <script src="js/notification-system.js"></script>
    
    <!-- 特定功能组件 -->
    <script src="field-deduplication-enhancer.js"></script>

    <script>
        // 页面初始化逻辑
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 页面加载完成，开始初始化...');
            
            try {
                // 等待组件管理器加载
                await waitForComponentManager();
                
                // 启动应用
                await window.startApp({
                    environment: 'development',
                    features: {
                        errorHandling: true,
                        validation: true,
                        fieldDeduplication: true,
                        progressDisplay: true,
                        notifications: true
                    }
                });
                
                // 更新UI状态
                updateLoadingStatus();
                
            } catch (error) {
                console.error('❌ 系统初始化失败:', error);
                showError('系统初始化失败: ' + error.message);
            }
        });

        // 等待组件管理器加载
        function waitForComponentManager() {
            return new Promise((resolve, reject) => {
                let attempts = 0;
                const maxAttempts = 50;
                
                const check = () => {
                    if (window.ComponentManager) {
                        resolve();
                    } else if (attempts < maxAttempts) {
                        attempts++;
                        setTimeout(check, 100);
                    } else {
                        reject(new Error('ComponentManager加载超时'));
                    }
                };
                
                check();
            });
        }

        // 更新加载状态
        function updateLoadingStatus() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const statusPanel = document.getElementById('statusPanel');
            const componentPanel = document.getElementById('componentPanel');
            const actionButtons = document.getElementById('actionButtons');
            
            loadingIndicator.style.display = 'none';
            statusPanel.style.display = 'grid';
            componentPanel.style.display = 'block';
            actionButtons.style.display = 'block';
            
            // 更新系统状态
            updateSystemStatus();
            updateComponentStatus();
            updateAPIStatus();
        }

        // 更新系统状态
        function updateSystemStatus() {
            const systemCard = document.getElementById('systemStatus');
            const systemInfo = document.getElementById('systemInfo');
            
            if (window.AppBootstrap && window.AppBootstrap.initialized) {
                systemCard.className = 'status-card success';
                systemInfo.textContent = '系统运行正常';
            } else {
                systemCard.className = 'status-card error';
                systemInfo.textContent = '系统初始化失败';
            }
        }

        // 更新组件状态
        function updateComponentStatus() {
            const componentCard = document.getElementById('componentStatus');
            const componentInfo = document.getElementById('componentInfo');
            const componentList = document.getElementById('componentList');
            
            if (window.ComponentManager) {
                const status = window.ComponentManager.getStatus();
                componentCard.className = 'status-card success';
                componentInfo.textContent = `已加载 ${status.registeredCount} 个组件`;
                
                // 更新组件列表
                componentList.innerHTML = '';
                Object.entries(status.components).forEach(([name, component]) => {
                    const item = document.createElement('div');
                    item.className = `component-item ${component.initialized ? 'loaded' : 'error'}`;
                    item.innerHTML = `
                        <div class="component-name">${name}</div>
                        <div class="component-status">${component.initialized ? '已加载' : '加载失败'}</div>
                    `;
                    componentList.appendChild(item);
                });
            } else {
                componentCard.className = 'status-card error';
                componentInfo.textContent = '组件管理器未加载';
            }
        }

        // 更新API状态
        function updateAPIStatus() {
            const apiCard = document.getElementById('apiStatus');
            const apiInfo = document.getElementById('apiInfo');
            
            if (window.ComponentManager) {
                const apiClient = window.ComponentManager.get('apiClient');
                if (apiClient) {
                    apiCard.className = 'status-card success';
                    apiInfo.textContent = 'API客户端已连接';
                } else {
                    apiCard.className = 'status-card warning';
                    apiInfo.textContent = 'API客户端未初始化';
                }
            } else {
                apiCard.className = 'status-card error';
                apiInfo.textContent = 'API状态未知';
            }
        }

        // 刷新状态
        function refreshStatus() {
            updateSystemStatus();
            updateComponentStatus();
            updateAPIStatus();
        }

        // 运行健康检查
        function runHealthCheck() {
            if (window.ComponentManager) {
                const health = window.ComponentManager.healthCheck();
                if (health.healthy) {
                    alert('✅ 系统健康检查通过！');
                } else {
                    alert('⚠️ 发现问题：\n' + health.errors.join('\n'));
                }
            } else {
                alert('❌ 组件管理器未加载');
            }
        }

        // 显示组件详情
        function showComponentDetails() {
            if (window.ComponentManager) {
                const status = window.ComponentManager.getStatus();
                const details = JSON.stringify(status, null, 2);
                console.log('组件详情:', status);
                
                // 创建模态框显示详情
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                    background: rgba(0,0,0,0.5); z-index: 1000;
                    display: flex; align-items: center; justify-content: center;
                `;
                
                modal.innerHTML = `
                    <div style="background: white; padding: 20px; border-radius: 8px; max-width: 80%; max-height: 80%; overflow: auto;">
                        <h3>组件详情</h3>
                        <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow: auto;">${details}</pre>
                        <button onclick="this.parentElement.parentElement.remove()">关闭</button>
                    </div>
                `;
                
                document.body.appendChild(modal);
            }
        }

        // 显示错误信息
        function showError(message) {
            const loadingIndicator = document.getElementById('loadingIndicator');
            loadingIndicator.innerHTML = `
                <div style="color: #dc3545; font-weight: bold;">❌ ${message}</div>
            `;
        }

        // 监听应用启动完成事件
        window.addEventListener('app:ready', function(event) {
            console.log('🎉 应用启动完成:', event.detail);
        });
    </script>
</body>
</html>
