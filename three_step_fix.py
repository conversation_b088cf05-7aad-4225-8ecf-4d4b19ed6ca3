#!/usr/bin/env python3
"""
一键Flake8修复和验证脚本
按照"三步清零法"自动化处理所有代码质量问题
"""

import subprocess
import time


def run_command(cmd, description=""):
    """运行命令并显示结果"""
    print(f"🔧 {description}")
    try:
        result = subprocess.run(
            cmd, shell=True, capture_output=True, text=True, encoding="utf-8"
        )
        if result.returncode == 0:
            print(f"✅ {description} 完成")
            if result.stdout.strip():
                print(f"   输出: {result.stdout.strip()}")
        else:
            print(f"⚠️ {description} 有警告")
            if result.stderr.strip():
                print(f"   错误: {result.stderr.strip()}")
        return result.returncode == 0
    except Exception as e:
        print(f"❌ {description} 失败: {e}")
        return False


def main():
    """主函数 - 三步清零法"""
    print("🚀 开始三步清零法修复所有代码质量问题...")
    print("=" * 60)

    # 使用正确的Python路径
    python_exe = '"E:/python 3.10/python.exe"'

    # 步骤1: 一键格式化 + 自动修复
    print("\n🔹 步骤 1: 一键格式化 + 自动修复")
    print("-" * 40)

    # 删除未使用的导入和变量
    run_command(
        f"{python_exe} -m autoflake --remove-all-unused-imports "
        f"--remove-unused-variables --in-place --recursive . "
        f"--exclude __pycache__,logs,temp_cleanup",
        "删除未使用的导入和变量"
    )

    # 整理导入语句
    run_command(
        f"{python_exe} -m isort . --skip __pycache__ --skip logs --skip temp_cleanup",
        "整理导入语句顺序")

    # 自动格式化代码
    run_command(
        f"{python_exe} -m black . --exclude='__pycache__|logs|temp_cleanup|node_modules|.git'",
        "Black代码格式化")

    # 自动修复PEP8问题
    run_command(
        f"{python_exe} -m autopep8 --in-place --aggressive --aggressive "
        f"--recursive . --exclude __pycache__,logs,temp_cleanup",
        "AutoPEP8自动修复"
    )

    # 步骤2: 检查剩余问题
    print("\n🔹 步骤 2: 检查剩余Flake8问题")
    print("-" * 40)

    # 统计Flake8错误
    print("📊 统计剩余的Flake8错误...")
    result = subprocess.run(
        f"{python_exe} -m flake8 --count --statistics --max-line-length=88 "
        f"--extend-ignore=E203,W503 .",
        shell=True, capture_output=True, text=True, encoding="utf-8"
    )

    if result.stdout.strip():
        lines = result.stdout.strip().split('\n')
        error_count = 0
        for line in lines:
            if line.strip().isdigit():
                error_count = int(line.strip())
                break

        if error_count == 0:
            print("🎉 恭喜！所有Flake8错误已清零！")
        else:
            print(f"⚠️ 还有 {error_count} 个Flake8问题需要手动处理:")
            print(result.stdout)
    else:
        print("🎉 没有发现Flake8错误！")

    # 步骤3: 语法验证
    print("\n🔹 步骤 3: 核心文件语法验证")
    print("-" * 40)

    # 测试关键文件语法
    test_files = [
        "analyze_dependencies",
        "unused_import_checker",
        "verify_startup",
        "verify_module"
    ]

    success_count = 0
    for file_name in test_files:
        try:
            cmd = f"{python_exe} -c \"import {file_name}; print('✅ {file_name}.py 语法正确')\""
            result = subprocess.run(
                cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {file_name}.py 语法正确")
                success_count += 1
            else:
                print(f"❌ {file_name}.py 语法错误: {result.stderr.strip()}")
        except Exception as e:
            print(f"❌ {file_name}.py 测试失败: {e}")

    # 总结
    print("\n📋 修复完成总结")
    print("=" * 60)
    print(f"✅ 语法验证通过: {success_count}/{len(test_files)} 个文件")

    if success_count == len(test_files):
        print("🎉 所有核心文件语法正确！")
        print("\n📌 下一步建议:")
        print("1. 在IDE中检查是否还有红色错误提示")
        print("2. 运行项目主程序验证功能正常")
        print("3. 如有剩余错误，按行号手动修复")
    else:
        print("⚠️ 部分文件仍有语法问题，请检查具体错误信息")

    print(f"\n⏱️ 修复完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
