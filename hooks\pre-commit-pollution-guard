#!/bin/bash
# Git pre-commit hook - 污染代码拦截器
# 将此文件放在 .git/hooks/pre-commit (无扩展名)

echo "🔍 污染代码检测..."

# AI污染特征
AI_SIGNATURES=(
    "Auto-generated by"
    "AI_generated" 
    "ShadowClass"
    "AI_Refactor"
    "__init___"
    "extends AI_Base"
    "newException"
    "require is not defined"
)

# 检查暂存的文件
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(py|js|ts|jsx|tsx)$')

if [ -z "$STAGED_FILES" ]; then
    echo "✅ 没有代码文件变更"
    exit 0
fi

POLLUTION_FOUND=false

for file in $STAGED_FILES; do
    if [ -f "$file" ]; then
        for signature in "${AI_SIGNATURES[@]}"; do
            if grep -q "$signature" "$file"; then
                echo "🚨 污染检测: $file 包含 '$signature'"
                POLLUTION_FOUND=true
            fi
        done
        
        # 检查影子文件
        if [[ "$file" == *"_shadow"* ]] || [[ "$file" == *"Shadow"* ]]; then
            echo "👥 影子文件检测: $file"
            POLLUTION_FOUND=true
        fi
        
        # 检查重复文件
        if [[ "$file" == *"_copy"* ]] || [[ "$file" == *"_duplicate"* ]]; then
            echo "📋 重复文件检测: $file" 
            POLLUTION_FOUND=true
        fi
    fi
done

if [ "$POLLUTION_FOUND" = true ]; then
    echo ""
    echo "❌ 提交被阻止！检测到污染代码"
    echo "请执行以下操作清理："
    echo "1. 运行: python violent_cleanup.py"
    echo "2. 手动审查标记的文件"
    echo "3. 删除或修复污染代码后重新提交"
    exit 1
fi

echo "✅ 污染检测通过，允许提交"
exit 0
