#!/bin/bash
# 绞杀者代理层启动脚本

set -e

echo "🚀 启动绞杀者代理层..."

# 检查环境变量
if [ -z "$LEGACY_HOST" ] || [ -z "$NEW_HOST" ]; then
    echo "❌ 缺少必要的环境变量: LEGACY_HOST, NEW_HOST"
    exit 1
fi

echo "📍 Legacy系统: $LEGACY_HOST:$LEGACY_PORT"
echo "📍 新系统: $NEW_HOST:$NEW_PORT"
echo "📊 初始流量分配: $TRAFFIC_SPLIT_PERCENT% -> 新系统"

# 等待依赖服务启动
echo "⏳ 等待依赖服务启动..."
timeout 60 bash -c 'until curl -f http://$LEGACY_HOST:$LEGACY_PORT/health 2>/dev/null; do sleep 2; done' || echo "⚠️ Legacy服务检查超时"
timeout 60 bash -c 'until curl -f http://$NEW_HOST:$NEW_PORT/health 2>/dev/null; do sleep 2; done' || echo "⚠️ 新服务检查超时"

# 启动代理服务
echo "🔄 启动代理服务..."
cd /app
python -m strangler_proxy.app
