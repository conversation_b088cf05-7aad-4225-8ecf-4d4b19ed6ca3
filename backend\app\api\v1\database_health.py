import structlog

from ...core.database_manager import get_connection_manager

"""
数据库健康检查API
"""


logger = structlog.get_logger()
router = APIRouter(prefix="/database", tags=["数据库健康检查"])


@router.get("/health")
async def check_database_health() -> Dict[str, Any]:
    """
    检查数据库健康状态

    Returns:
        Dict: 数据库健康状态信息
    """
    try:
        connection_manager = get_connection_manager()

        # 测试连接
        is_healthy = await connection_manager.test_connection()

        # 获取连接池信息
        pool_info = await connection_manager.get_connection_info()

        return {
            "status": "healthy" if is_healthy else "unhealthy",
            "connection_test": is_healthy,
            "pool_info": pool_info,
            "timestamp": "2024-01-01T00:00:00Z",  # 这里应该使用实际时间戳
        }

    except Exception:
        logger.error("数据库健康检查失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"数据库健康检查失败: {str(e)}")


@router.get("/pool-status")
async def get_pool_status() -> Dict[str, Any]:
    """
    获取连接池状态

    Returns:
        Dict: 连接池详细信息
    """
    try:
        connection_manager = get_connection_manager()
        pool_info = await connection_manager.get_connection_info()

        return {"pool_status": pool_info, "status": "available"}

    except Exception:
        logger.error("获取连接池状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取连接池状态失败: {str(e)}")


@router.post("/test-connection")
async def test_database_connection() -> Dict[str, Any]:
    """
    测试数据库连接

    Returns:
        Dict: 连接测试结果
    """
    try:
        connection_manager = get_connection_manager()
        is_connected = await connection_manager.test_connection()

        return {
            "connected": is_connected,
            "message": "连接成功" if is_connected else "连接失败",
        }

    except Exception:
        logger.error("数据库连接测试失败", error=str(e))
        return {"connected": False, "message": f"连接测试失败: {str(e)}"}
