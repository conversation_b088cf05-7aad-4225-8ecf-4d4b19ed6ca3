[{"success": true, "code": 200, "message": "", "data": {"fieldVersion": 20230210, "appCode": "", "tokenSet": false, "tokenDoc": "", "tenantId": 0, "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "id": "e179ea7bd91047d2a6c77b95a3d2817d", "name": "业务日志", "apiClassifyId": "7e62fb89dd574771a2257a4b09d26ac2", "apiClassifyName": "日志管理", "apiClassifyCode": "A13_AuditlogBusiness", "parentApiClassifies": "", "functionId": "", "openMode": 0, "description": "可以根据此接口获取本租户日志数据，做数据分析使用。", "auth": true, "bodyPassthrough": false, "healthExam": false, "healthStatus": true, "responseResultPassthrough": false, "contentType": "application/json", "returnPassthrough": "", "completeProxyUrl": "/yonbip/digitalModel/log-pub/business/rest/query", "connectUrl": "/log-pub/business/rest/query", "sort": 20, "handler": "openapi", "httpRequestType": "GET", "openApi": true, "preset": false, "productId": "4d12434dd5de42c2b6fffd093e31e074", "productCode": "", "proxyUrl": "/yonbip/digitalModel/log-pub/business/rest/query", "requestParamsDemo": "Url: /yonsuite/digitalModel//log-pub/business/rest/query=null=null=null=null=null=null=null=1=10", "requestProtocol": "CUSTOM", "serviceHttpMethod": "GET", "publishStatus": true, "approvalMsg": "", "rpcAppName": "", "rpcServiceName": "", "rpcMethodName": "", "rpcServiceUrl": "", "ma": false, "gmtCreate": "2020-03-19 16:16:24.000", "gmtUpdate": "2025-06-30 17:30:10.293", "address": "https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/digitalModel/log-pub/business/rest/query", "productName": "", "productClassifyId": "", "productClassifyCode": "", "productClassifyName": "", "paramDTOS": {"paramDTOS": [{"id": 2302753673694412809, "name": "content", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412810, "array": false, "paramDesc": "日志内容", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2302753673694412811, "name": "busiObjType", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412812, "array": false, "paramDesc": "业务对象类型", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2302753673694412813, "name": "busiObjCode", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412814, "array": false, "paramDesc": "业务对象编码", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2302753673694412815, "name": "busiObjName", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412816, "array": false, "paramDesc": "业务对象名称", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2302753673694412817, "name": "operator", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412818, "array": false, "paramDesc": "操作人(id)，多个用逗号分隔", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 4, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2302753673694412819, "name": "startDate", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412820, "array": false, "paramDesc": "开始时间（时间戳）", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 5, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2302753673694412821, "name": "endDate", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412822, "array": false, "paramDesc": "结束时间（时间戳）", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 6, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2302753673694412823, "name": "page", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412824, "array": false, "paramDesc": "页码", "paramType": "int", "requestParamType": "QueryParam", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 7, "bizType": "", "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2302753673694412825, "name": "size", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412826, "array": false, "paramDesc": "每页数量", "paramType": "int", "requestParamType": "QueryParam", "path": "", "example": 10, "fullName": "", "ytenantId": 0, "paramOrder": 8, "bizType": "", "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2302753673694412827, "name": "tenantId", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412828, "array": false, "paramDesc": "租户id", "paramType": "string", "requestParamType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 9, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": false, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2302753673694412829, "name": "operNameResid", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412830, "array": false, "paramDesc": "操作类型", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 10, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "queryParamDTOS": "", "ysApi": false, "presetTokenApi": false, "applyFlag": false, "cover": false, "paramMapDTOS": {"paramMapDTOS": [{"id": 2302753673694412881, "name": "content", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": "", "array": false, "paramDesc": "日志内容", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "content", "mapRequestParamType": "QueryParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "", "gmtUpdate": "", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2302753673694412883, "name": "busiObjType", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": "", "array": false, "paramDesc": "业务对象类型", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "busiObjType", "mapRequestParamType": "QueryParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "", "gmtUpdate": "", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2302753673694412885, "name": "busiObjCode", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": "", "array": false, "paramDesc": "业务对象编码", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "busiObjCode", "mapRequestParamType": "QueryParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "", "gmtUpdate": "", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2302753673694412887, "name": "busiObjName", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": "", "array": false, "paramDesc": "业务对象名称", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "busiObjName", "mapRequestParamType": "QueryParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "", "gmtUpdate": "", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2302753673694412889, "name": "operator", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": "", "array": false, "paramDesc": "操作人(id)，多个用逗号分隔", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 4, "baseType": true, "aggregatedValueObject": false, "mapName": "operator", "mapRequestParamType": "QueryParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "", "gmtUpdate": "", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2302753673694412891, "name": "startDate", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": "", "array": false, "paramDesc": "开始时间（时间戳）", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 5, "baseType": true, "aggregatedValueObject": false, "mapName": "startDate", "mapRequestParamType": "QueryParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "", "gmtUpdate": "", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2302753673694412893, "name": "endDate", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": "", "array": false, "paramDesc": "结束时间（时间戳）", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 6, "baseType": true, "aggregatedValueObject": false, "mapName": "endDate", "mapRequestParamType": "QueryParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "", "gmtUpdate": "", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2302753673694412895, "name": "page", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": "", "array": false, "paramDesc": "页码", "paramType": "int", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 7, "baseType": true, "aggregatedValueObject": false, "mapName": "page", "mapRequestParamType": "QueryParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "", "gmtUpdate": "", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2302753673694412897, "name": "size", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": "", "array": false, "paramDesc": "每页数量", "paramType": "int", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 8, "baseType": true, "aggregatedValueObject": false, "mapName": "size", "mapRequestParamType": "QueryParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "", "gmtUpdate": "", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2302753673694412899, "name": "tenantId", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": "", "array": false, "paramDesc": "租户id", "paramType": "string", "requestParamType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 9, "baseType": true, "aggregatedValueObject": false, "mapName": "tenantId", "mapRequestParamType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "", "gmtUpdate": "", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2302753673694412901, "name": "operNameResid", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": "", "array": false, "paramDesc": "操作类型", "paramType": "string", "requestParamType": "QueryParam", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 10, "baseType": true, "aggregatedValueObject": false, "mapName": "operNameResid", "mapRequestParamType": "QueryParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "", "gmtUpdate": "", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "paramReturnDTOS": {"paramReturnDTOS": [{"id": 2302753673694412831, "name": "status", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412832, "array": false, "paramDesc": "状态码", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 2, "maxLength": 10, "enableMulti": false}, {"id": 2302753673694412833, "name": "data", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "children": {"children": [{"id": 2302753673694412835, "name": "number", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412833, "defParamId": 2302753673694412836, "array": false, "paramDesc": "页码", "paramType": "number", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 2, "maxLength": 10, "enableMulti": false}, {"id": 2302753673694412837, "name": "totalPages", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412833, "defParamId": 2302753673694412838, "array": false, "paramDesc": "总页数", "paramType": "number", "requestParamType": "", "path": "", "example": 10000, "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 2, "maxLength": 10, "enableMulti": false}, {"id": 2302753673694412839, "name": "content", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412833, "children": {"children": [{"id": 2302753673694412841, "name": "busiObjCode", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412842, "array": false, "paramDesc": "业务对象编码", "paramType": "string", "requestParamType": "", "path": "", "example": "YHT-2262631-22630211596791945093", "fullName": "", "ytenantId": 0, "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412843, "name": "busiObjName", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412844, "array": false, "paramDesc": "业务对象名称", "paramType": "string", "requestParamType": "", "path": "", "example": "黄家成", "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412845, "name": "operationDate", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412846, "array": false, "paramDesc": "操作时间", "paramType": "string", "requestParamType": "", "path": "", "example": "2020-08-29T08:24:59.989+0000", "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412847, "name": "operResult", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412848, "array": false, "paramDesc": "操作结果", "paramType": "string", "requestParamType": "", "path": "", "example": "success", "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412849, "name": "operCode", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412850, "array": false, "paramDesc": "操作编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412851, "name": "detail", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412852, "array": false, "paramDesc": "操作详情", "paramType": "string", "requestParamType": "", "path": "", "example": "13910934966在2020-08-29 16:24:59对黄家成设置管理员", "fullName": "", "ytenantId": 0, "paramOrder": 5, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412853, "name": "busiObjTypeName", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412854, "array": false, "paramDesc": "业务对象所属类型名称", "paramType": "string", "requestParamType": "", "path": "", "example": "租户管理员", "fullName": "", "ytenantId": 0, "paramOrder": 6, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412855, "name": "busiObjId", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412856, "array": false, "paramDesc": "业务对象ID", "paramType": "string", "requestParamType": "", "path": "", "example": "2f2c6672-87c7-44e7-a4d3-ef4b27b3597e", "fullName": "", "ytenantId": 0, "paramOrder": 7, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412857, "name": "operationName", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412858, "array": false, "paramDesc": "操作名称", "paramType": "string", "requestParamType": "", "path": "", "example": "设置管理员", "fullName": "", "ytenantId": 0, "paramOrder": 8, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412859, "name": "busiObjTypeCode", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412860, "array": false, "paramDesc": "业务对象所属类型编码", "paramType": "string", "requestParamType": "", "path": "", "example": "bd_user_manager", "fullName": "", "ytenantId": 0, "paramOrder": 9, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412861, "name": "tenantId", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412862, "array": false, "paramDesc": "租户ID", "paramType": "string", "requestParamType": "", "path": "", "example": "s4adr3x4", "fullName": "", "ytenantId": 0, "paramOrder": 10, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412863, "name": "newBusiObj", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412864, "array": false, "paramDesc": "新业务对象", "paramType": "string", "requestParamType": "", "path": "", "example": "{\"begindate\":1598689499633}", "fullName": "", "ytenantId": 0, "paramOrder": 11, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412865, "name": "sysId", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412866, "array": false, "paramDesc": "系统标识", "paramType": "string", "requestParamType": "", "path": "", "example": "aps", "fullName": "", "ytenantId": 0, "paramOrder": 12, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412867, "name": "operator", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412868, "array": false, "paramDesc": "操作人id", "paramType": "string", "requestParamType": "", "path": "", "example": "other", "fullName": "", "ytenantId": 0, "paramOrder": 13, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412869, "name": "businessId", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412870, "array": false, "paramDesc": "主键（在es中作为索引）", "paramType": "string", "requestParamType": "", "path": "", "example": "ca2ea9e9-5835-44f2-8fdd-35a2b72bb4f1", "fullName": "", "ytenantId": 0, "paramOrder": 14, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412871, "name": "operatorName", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412872, "array": false, "paramDesc": "操作人名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 15, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412873, "name": "ip", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412839, "defParamId": 2302753673694412874, "array": false, "paramDesc": "ip地址", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 16, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 2302753673694412840, "array": true, "paramDesc": "内容", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-06-30 17:30:10.699", "gmtUpdate": "2025-06-30 17:30:10.699", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412875, "name": "totalElements", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": 2302753673694412833, "defParamId": 2302753673694412876, "array": false, "paramDesc": "总条数", "paramType": "number", "requestParamType": "", "path": "", "example": 10000, "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 2, "maxLength": 10, "enableMulti": false}]}, "defParamId": 2302753673694412834, "array": false, "paramDesc": "数据", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-06-30 17:30:10.690", "gmtUpdate": "2025-06-30 17:30:10.690", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2302753673694412877, "name": "displayCode", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412878, "array": false, "paramDesc": "异常码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "", "gmtUpdate": "", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": "", "enableMulti": false}, {"id": 2302753673694412879, "name": "level", "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "parentId": "", "defParamId": 2302753673694412880, "array": false, "paramDesc": "异常等级", "paramType": "number", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": 0, "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "", "gmtUpdate": "", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 2, "maxLength": 10, "enableMulti": false}]}, "returnFormatType": "JSON", "paramConstDTOS": "", "paramConstMapDTOS": "", "apiDemoReturnDTOS": {"apiDemoReturnDTOS": [{"id": 2302753673694412801, "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "content": "{ \"status\": 1, \"data\": { \"totalElements\": 447, \"totalPages\": 45, \"number\": 0, \"first\": false, \"last\": false, \"content\": [ { \"businessId\": \"7d9b66a0-f8e0-4175-9ba5-e8c29b9e2ba2\", \"mdId\": null, \"mdUri\": null, \"busiObjTypeCode\": \"areaFormatRecordCard\", \"busiObjTypeName\": \"区域格式信息\", \"busiObjId\": \"****************\", \"busiObjCode\": \"test_5\", \"busiObjName\": \"test_5\", \"newBusiObj\": \"{\\\"area_name\\\":{\\\"en_US\\\":\\\"test_5\\\",\\\"zh_CN\\\":\\\"test_5\\\"},\\\"areaFormatNumberRecords!number_format_code\\\":\\\"0\\\",\\\"areaFormatTimeRecords!area_base_info_id\\\":****************,\\\"areaFormatTimeRecords!id\\\":****************,\\\"format_time\\\":\\\"15:10:10\\\",\\\"format_number\\\":\\\"-1,234,567.89\\\",\\\"area_code\\\":\\\"test_5\\\",\\\"areaFormatAddressRecords\\\":[{\\\"addr_segment_code\\\":\\\"country\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":1,\\\"id\\\":2322161119629572,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"province\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":2,\\\"id\\\":2322161119629573,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"city\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":3,\\\"id\\\":2322161119629574,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"district\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":4,\\\"id\\\":2322161119629575,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressone\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":5,\\\"id\\\":2322161119629576,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addresstwo\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":6,\\\"id\\\":2322161119629577,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressthree\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":7,\\\"id\\\":2322161119645952,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressfour\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":8,\\\"id\\\":2322161119645953,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"postcode\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":9,\\\"id\\\":2322161119645954,\\\"is_show\\\":1}],\\\"areaFormatDateRecords!area_base_info_id\\\":****************,\\\"areaFormatNumberRecords!id\\\":2322161119629571,\\\"format_address\\\":\\\"[{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"country\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":1,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"province\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":2,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"city\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":3,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"district\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":4,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"addressone\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":5,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"addresstwo\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":6,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"addressthree\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":7,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"addressfour\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":8,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"postcode\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":9,\\\\\\\"is_show\\\\\\\":1}]\\\",\\\"areaFormatNumberRecords!area_base_info_id\\\":****************,\\\"areaFormatNumberRecords!minus_format_code\\\":\\\"0\\\",\\\"is_default\\\":0,\\\"areaFormatDateRecords!format_enum_code\\\":\\\"0\\\",\\\"areaFormatTimeRecords!format_enum_code\\\":\\\"0\\\",\\\"areaFormatDateRecords!id\\\":2322161119629569,\\\"id\\\":****************,\\\"pubts\\\":\\\"2021-06-29 10:24:27\\\",\\\"format_date\\\":\\\"2019-11-26\\\"}\", \"operCode\": \"save\", \"operationName\": \"Save\", \"detail\": \"u8c_vip@163.comon2021-06-29 10:40:33for区域格式信息:test_5(test_5)performedSave\", \"operator\": \"YHT-870-57415517002\", \"operatorName\": \"<EMAIL>\", \"operationDate\": \"2021-06-29T02:40:33.350+00:00\", \"tenantId\": \"czqne4bp\", \"sysId\": null, \"caepOrg\": null, \"caepRole\": null, \"ip\": \"***********\", \"operResult\": \"success\", \"ts\": null, \"operDateForExport\": null, \"struct\": 1, \"domain\": \"eventcenter\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "edit": false, "ytenantId": 0, "right": true}, {"id": 2302753673694412802, "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "content": "{ \"status\": 0, \"msg\": \"查询日志失败\", \"displayCode\":\"XXX-XXX-XXXXXX\", \"level\":0, \"errorCode\": \"000000\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "edit": false, "ytenantId": 0, "right": false}]}, "apiDemoReturnDTOList": {"apiDemoReturnDTOList": [{"id": 2302753673694412801, "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "content": "{ \"status\": 1, \"data\": { \"totalElements\": 447, \"totalPages\": 45, \"number\": 0, \"first\": false, \"last\": false, \"content\": [ { \"businessId\": \"7d9b66a0-f8e0-4175-9ba5-e8c29b9e2ba2\", \"mdId\": null, \"mdUri\": null, \"busiObjTypeCode\": \"areaFormatRecordCard\", \"busiObjTypeName\": \"区域格式信息\", \"busiObjId\": \"****************\", \"busiObjCode\": \"test_5\", \"busiObjName\": \"test_5\", \"newBusiObj\": \"{\\\"area_name\\\":{\\\"en_US\\\":\\\"test_5\\\",\\\"zh_CN\\\":\\\"test_5\\\"},\\\"areaFormatNumberRecords!number_format_code\\\":\\\"0\\\",\\\"areaFormatTimeRecords!area_base_info_id\\\":****************,\\\"areaFormatTimeRecords!id\\\":****************,\\\"format_time\\\":\\\"15:10:10\\\",\\\"format_number\\\":\\\"-1,234,567.89\\\",\\\"area_code\\\":\\\"test_5\\\",\\\"areaFormatAddressRecords\\\":[{\\\"addr_segment_code\\\":\\\"country\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":1,\\\"id\\\":2322161119629572,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"province\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":2,\\\"id\\\":2322161119629573,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"city\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":3,\\\"id\\\":2322161119629574,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"district\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":4,\\\"id\\\":2322161119629575,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressone\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":5,\\\"id\\\":2322161119629576,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addresstwo\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":6,\\\"id\\\":2322161119629577,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressthree\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":7,\\\"id\\\":2322161119645952,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressfour\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":8,\\\"id\\\":2322161119645953,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"postcode\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":9,\\\"id\\\":2322161119645954,\\\"is_show\\\":1}],\\\"areaFormatDateRecords!area_base_info_id\\\":****************,\\\"areaFormatNumberRecords!id\\\":2322161119629571,\\\"format_address\\\":\\\"[{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"country\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":1,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"province\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":2,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"city\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":3,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"district\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":4,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"addressone\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":5,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"addresstwo\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":6,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"addressthree\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":7,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"addressfour\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":8,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"postcode\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":9,\\\\\\\"is_show\\\\\\\":1}]\\\",\\\"areaFormatNumberRecords!area_base_info_id\\\":****************,\\\"areaFormatNumberRecords!minus_format_code\\\":\\\"0\\\",\\\"is_default\\\":0,\\\"areaFormatDateRecords!format_enum_code\\\":\\\"0\\\",\\\"areaFormatTimeRecords!format_enum_code\\\":\\\"0\\\",\\\"areaFormatDateRecords!id\\\":2322161119629569,\\\"id\\\":****************,\\\"pubts\\\":\\\"2021-06-29 10:24:27\\\",\\\"format_date\\\":\\\"2019-11-26\\\"}\", \"operCode\": \"save\", \"operationName\": \"Save\", \"detail\": \"u8c_vip@163.comon2021-06-29 10:40:33for区域格式信息:test_5(test_5)performedSave\", \"operator\": \"YHT-870-57415517002\", \"operatorName\": \"<EMAIL>\", \"operationDate\": \"2021-06-29T02:40:33.350+00:00\", \"tenantId\": \"czqne4bp\", \"sysId\": null, \"caepOrg\": null, \"caepRole\": null, \"ip\": \"***********\", \"operResult\": \"success\", \"ts\": null, \"operDateForExport\": null, \"struct\": 1, \"domain\": \"eventcenter\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "edit": false, "ytenantId": 0, "right": true}, {"id": 2302753673694412802, "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "content": "{ \"status\": 0, \"msg\": \"查询日志失败\", \"displayCode\":\"XXX-XXX-XXXXXX\", \"level\":0, \"errorCode\": \"000000\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "edit": false, "ytenantId": 0, "right": false}]}, "routingStgy": 0, "routingStgyList": "", "apiDemoReturnDTO": {"id": 2302753673694412801, "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "content": "{ \"status\": 1, \"data\": { \"totalElements\": 447, \"totalPages\": 45, \"number\": 0, \"first\": false, \"last\": false, \"content\": [ { \"businessId\": \"7d9b66a0-f8e0-4175-9ba5-e8c29b9e2ba2\", \"mdId\": null, \"mdUri\": null, \"busiObjTypeCode\": \"areaFormatRecordCard\", \"busiObjTypeName\": \"区域格式信息\", \"busiObjId\": \"****************\", \"busiObjCode\": \"test_5\", \"busiObjName\": \"test_5\", \"newBusiObj\": \"{\\\"area_name\\\":{\\\"en_US\\\":\\\"test_5\\\",\\\"zh_CN\\\":\\\"test_5\\\"},\\\"areaFormatNumberRecords!number_format_code\\\":\\\"0\\\",\\\"areaFormatTimeRecords!area_base_info_id\\\":****************,\\\"areaFormatTimeRecords!id\\\":****************,\\\"format_time\\\":\\\"15:10:10\\\",\\\"format_number\\\":\\\"-1,234,567.89\\\",\\\"area_code\\\":\\\"test_5\\\",\\\"areaFormatAddressRecords\\\":[{\\\"addr_segment_code\\\":\\\"country\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":1,\\\"id\\\":2322161119629572,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"province\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":2,\\\"id\\\":2322161119629573,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"city\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":3,\\\"id\\\":2322161119629574,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"district\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":4,\\\"id\\\":2322161119629575,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressone\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":5,\\\"id\\\":2322161119629576,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addresstwo\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":6,\\\"id\\\":2322161119629577,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressthree\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":7,\\\"id\\\":2322161119645952,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"addressfour\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":8,\\\"id\\\":2322161119645953,\\\"is_show\\\":1},{\\\"addr_segment_code\\\":\\\"postcode\\\",\\\"is_required\\\":1,\\\"area_base_info_id\\\":****************,\\\"addr_separator\\\":\\\" \\\",\\\"addr_order\\\":9,\\\"id\\\":2322161119645954,\\\"is_show\\\":1}],\\\"areaFormatDateRecords!area_base_info_id\\\":****************,\\\"areaFormatNumberRecords!id\\\":2322161119629571,\\\"format_address\\\":\\\"[{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"country\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":1,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"province\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":2,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"city\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":3,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"district\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":4,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"addressone\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":5,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"addresstwo\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":6,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"addressthree\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":7,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"addressfour\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":8,\\\\\\\"is_show\\\\\\\":1},{\\\\\\\"addr_segment_code\\\\\\\":\\\\\\\"postcode\\\\\\\",\\\\\\\"addr_separator\\\\\\\":\\\\\\\" \\\\\\\",\\\\\\\"addr_order\\\\\\\":9,\\\\\\\"is_show\\\\\\\":1}]\\\",\\\"areaFormatNumberRecords!area_base_info_id\\\":****************,\\\"areaFormatNumberRecords!minus_format_code\\\":\\\"0\\\",\\\"is_default\\\":0,\\\"areaFormatDateRecords!format_enum_code\\\":\\\"0\\\",\\\"areaFormatTimeRecords!format_enum_code\\\":\\\"0\\\",\\\"areaFormatDateRecords!id\\\":2322161119629569,\\\"id\\\":****************,\\\"pubts\\\":\\\"2021-06-29 10:24:27\\\",\\\"format_date\\\":\\\"2019-11-26\\\"}\", \"operCode\": \"save\", \"operationName\": \"Save\", \"detail\": \"u8c_vip@163.comon2021-06-29 10:40:33for区域格式信息:test_5(test_5)performedSave\", \"operator\": \"YHT-870-57415517002\", \"operatorName\": \"<EMAIL>\", \"operationDate\": \"2021-06-29T02:40:33.350+00:00\", \"tenantId\": \"czqne4bp\", \"sysId\": null, \"caepOrg\": null, \"caepRole\": null, \"ip\": \"***********\", \"operResult\": \"success\", \"ts\": null, \"operDateForExport\": null, \"struct\": 1, \"domain\": \"eventcenter\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "edit": false, "ytenantId": 0, "right": true}, "apiDemoReturnDTOError": {"id": 2302753673694412802, "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "content": "{ \"status\": 0, \"msg\": \"查询日志失败\", \"displayCode\":\"XXX-XXX-XXXXXX\", \"level\":0, \"errorCode\": \"000000\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "edit": false, "ytenantId": 0, "right": false}, "errorCodeDTOS": {"errorCodeDTOS": [{"id": 2302753673694412803, "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "errorCode": "status", "errorMessage": 0, "errorType": "API", "errorcodeDesc": "状态值", "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "edit": false, "defErrorId": 2302753673694412804, "ytenantId": 0, "displayCodeId": ""}, {"id": 2302753673694412805, "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "errorCode": "msg", "errorMessage": "查询日志失败", "errorType": "API", "errorcodeDesc": "", "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "edit": false, "defErrorId": 2302753673694412806, "ytenantId": 0, "displayCodeId": ""}, {"id": 2302753673694412807, "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "errorCode": "errorCode", "errorMessage": 0, "errorType": "API", "errorcodeDesc": "", "gmtCreate": "2024-07-01 11:44:59.000", "gmtUpdate": "2024-07-01 11:44:59.000", "apiName": "", "edit": false, "defErrorId": 2302753673694412808, "ytenantId": 0, "displayCodeId": ""}]}, "displayCodeApiConfigDTOS": "", "tokenPlugin": "", "paramParsePlugin": "", "authPlugin": {"id": "09ecc1b0-9d7f-41d1-803a-e78ea2f4e88c", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "", "name": "开发者中心加签业务扩展插件", "configurable": false, "description": "YonBIPBusinessExtendPlugin", "pluginType": "auth", "pluginTypeName": "业务扩展插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.businessextend.yonbip.YonBIPBusinessExtendPlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": false, "gmtCreate": "2020-05-22 00:00:00", "gmtUpdate": "2020-05-22 00:00:00", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "e179ea7bd91047d2a6c77b95a3d2817d", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "resultParsePlugin": {"id": "w181ed01-1e9b-4350-b994-71a66f062555", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "resultParse", "name": "返回参数映射插件", "configurable": false, "description": "符合映射配置的会自动解析，不符合的会自动略过", "pluginType": "resultParse", "pluginTypeName": "返回值解析插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.result.ResultMapParsePlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": true, "gmtCreate": "2020-05-08 00:00:00", "gmtUpdate": "", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "e179ea7bd91047d2a6c77b95a3d2817d", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "mapReturnPluginConfig": {"id": 2302753682284609537, "relateId": "e179ea7bd91047d2a6c77b95a3d2817d", "level": "api", "code": "status", "successType": "number", "successCode": 1, "message": "msg", "data": "data", "filter": false}, "billNo": "", "domain": "", "apiCategory": "", "docUrl": "", "pathMatch": 0, "createUser": "", "createUserName": "", "approvalStatus": 1, "publishTime": "2025-06-30 17:45:10", "pathJoin": true, "timeOut": 30, "tokenPluginName": "", "authPluginName": "", "resultPluginName": "", "apiDemoReturnRightDemo": "", "apiDemoReturnErrorDemo": "", "mock": false, "mockTimeout": "", "customUrl": "/log-pub/business/rest/query", "fixedUrl": "/yonbip/digitalModel", "apiCode": "e179ea7bd91047d2a6c77b95a3d2817d", "tokenCheckType": 0, "enableMulti": false, "multiField": "", "idempotent": "non", "bidirectionalSSL": "", "ucgSchema": "HTTPS", "updateUserId": "00001951-7ca3-47ac-a462-d5a66e3e6724", "updateUserName": ***********, "paramIsForce": "", "userIDPassthrough": false, "applyUser": "", "applyMsg": "", "dr": 0, "microServiceCode": "domain.iuap-apcom-auditlog", "applicationCode": "iuap-apcom-auditlog-yms", "privacyCategory": 0, "privacyLevel": 0, "apiDesigned": 0, "serviceType": 0, "integrateSchemeCode": "", "integrateSchemeName": "", "integrateObjectCode": "", "integrateObjectName": "", "integrateObjectCreatedType": "", "returnIntegObjId": "", "returnIntegObjName": "", "apiIntegrateDTOList": "", "apiRouteInfoDTOList": "", "arrayParam": false, "fileSize": "", "cc": false, "paramTransferMode": 2, "ytenantId": 0, "statusConf": "", "scene": 1, "version": "", "bizObjUri": "", "bizObjOperationType": "", "apiDefId": 1938847820720111622, "paramExtBizObjCode": "", "paramExtBizObjName": "", "paramExtRequest": 1, "paramExtResponse": 1, "paramExtInExtendKey": 1, "openScene": 1, "integrationScene": "", "apiType": "", "paramMark": "", "integrateSysId": "", "integrateSysName": "", "integrateSysCode": "", "dataZoneSetting": false, "reqDataZoneSetting": false, "respDataZoneSetting": false, "reqDataAllQuery": false, "reqDataAllBody": false, "respDataAllBody": false, "chargeStatus": 1, "beforeSpeed": 60, "afterSpeed": 120, "speedStatus": false, "reqDataRefPath": "", "respDataRefPath": "", "pubHistory": {"pubHistory": {"id": 2302761404614574081, "apiId": "e179ea7bd91047d2a6c77b95a3d2817d", "apiName": "业务日志", "applyReason": "", "publishUserName": "", "version": 20250630174510, "operationTime": "2025-06-30", "gmtCreate": "", "gmtUpdate": "", "changes": {"changes": [{"changePosition": "baseInfo", "newList": "", "updateList": {"updateList": {"changeProperty": "enableMulti", "oldValue": "", "newValue": false}}, "deleteList": ""}, {"changePosition": "paramDTOS", "newList": "", "updateList": {"updateList": [{"changeProperty": "content", "oldValue": "{\"id\":\"2046138827645911405\",\"name\":\"content\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2046138827645911331\",\"array\":false,\"paramDesc\":\"日志内容\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_content\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2302753673694412809\",\"name\":\"content\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2302753673694412810\",\"array\":false,\"paramDesc\":\"日志内容\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_content\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "busiObjType", "oldValue": "{\"id\":\"2046138827645911406\",\"name\":\"busiObjType\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2046138827645911332\",\"array\":false,\"paramDesc\":\"业务对象类型\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_busiObjType\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2302753673694412811\",\"name\":\"busiObjType\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2302753673694412812\",\"array\":false,\"paramDesc\":\"业务对象类型\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_busiObjType\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "busiObjCode", "oldValue": "{\"id\":\"2046138827645911407\",\"name\":\"busiObjCode\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2046138827645911333\",\"array\":false,\"paramDesc\":\"业务对象编码\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_busiObjCode\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2302753673694412813\",\"name\":\"busiObjCode\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2302753673694412814\",\"array\":false,\"paramDesc\":\"业务对象编码\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_busiObjCode\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "busiObjName", "oldValue": "{\"id\":\"2046138827645911408\",\"name\":\"busiObjName\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2046138827645911334\",\"array\":false,\"paramDesc\":\"业务对象名称\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_busiObjName\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2302753673694412815\",\"name\":\"busiObjName\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2302753673694412816\",\"array\":false,\"paramDesc\":\"业务对象名称\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_busiObjName\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "operator", "oldValue": "{\"id\":\"2046138827645911409\",\"name\":\"operator\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2046138827645911335\",\"array\":false,\"paramDesc\":\"操作人(id)，多个用逗号分隔\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_operator\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2302753673694412817\",\"name\":\"operator\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2302753673694412818\",\"array\":false,\"paramDesc\":\"操作人(id)，多个用逗号分隔\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_operator\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "startDate", "oldValue": "{\"id\":\"2046138827645911410\",\"name\":\"startDate\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2046138827645911336\",\"array\":false,\"paramDesc\":\"开始时间（时间戳）\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_startDate\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2302753673694412819\",\"name\":\"startDate\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2302753673694412820\",\"array\":false,\"paramDesc\":\"开始时间（时间戳）\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_startDate\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "endDate", "oldValue": "{\"id\":\"2046138827645911411\",\"name\":\"endDate\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2046138827645911337\",\"array\":false,\"paramDesc\":\"结束时间（时间戳）\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_endDate\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2302753673694412821\",\"name\":\"endDate\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2302753673694412822\",\"array\":false,\"paramDesc\":\"结束时间（时间戳）\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_endDate\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "page", "oldValue": "{\"id\":\"2046138827645911412\",\"name\":\"page\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2046138827645911338\",\"array\":false,\"paramDesc\":\"页码\",\"paramType\":\"int\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_page\",\"example\":\"1\",\"required\":true,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2302753673694412823\",\"name\":\"page\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2302753673694412824\",\"array\":false,\"paramDesc\":\"页码\",\"paramType\":\"int\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_page\",\"example\":\"1\",\"ytenantId\":\"0\",\"required\":true,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "size", "oldValue": "{\"id\":\"2046138827645911413\",\"name\":\"size\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2046138827645911339\",\"array\":false,\"paramDesc\":\"每页数量\",\"paramType\":\"int\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_size\",\"example\":\"10\",\"required\":true,\"defaultValue\":\"\",\"visible\":true,\"maxLength\":\"0\"}", "newValue": "{\"id\":\"2302753673694412825\",\"name\":\"size\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2302753673694412826\",\"array\":false,\"paramDesc\":\"每页数量\",\"paramType\":\"int\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_size\",\"example\":\"10\",\"ytenantId\":\"0\",\"required\":true,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false,\"maxLength\":\"0\"}"}, {"changeProperty": "operNameResid", "oldValue": "{\"id\":\"2046138827645911415\",\"name\":\"operNameResid\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2046138827645911341\",\"array\":false,\"paramDesc\":\"操作类型\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_operNameResid\",\"example\":\"\",\"required\":false,\"defaultValue\":\"\",\"visible\":true}", "newValue": "{\"id\":\"2302753673694412829\",\"name\":\"operNameResid\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2302753673694412830\",\"array\":false,\"paramDesc\":\"操作类型\",\"paramType\":\"string\",\"requestParamType\":\"QueryParam\",\"path\":\"QueryParam_operNameResid\",\"example\":\"\",\"ytenantId\":\"0\",\"required\":false,\"defaultValue\":\"\",\"visible\":true,\"enableMulti\":false}"}]}, "deleteList": ""}, {"changePosition": "paramReturnDTOS", "newList": {"newList": [{"changeProperty": "displayCode", "oldValue": "", "newValue": "{\"id\":\"2302753673694412877\",\"name\":\"displayCode\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2302753673694412878\",\"array\":false,\"paramDesc\":\"异常码\",\"paramType\":\"string\",\"path\":\"null_displayCode\",\"example\":\"\",\"ytenantId\":\"0\"}"}, {"changeProperty": "level", "oldValue": "", "newValue": "{\"id\":\"2302753673694412879\",\"name\":\"level\",\"apiId\":\"e179ea7bd91047d2a6c77b95a3d2817d\",\"defParamId\":\"2302753673694412880\",\"array\":false,\"paramDesc\":\"异常等级\",\"paramType\":\"number\",\"path\":\"null_level\",\"example\":\"\",\"ytenantId\":\"0\"}"}]}, "updateList": "", "deleteList": ""}]}}}, "deprecated": 0, "recommendedApiId": "", "recommendedApiName": "", "domainAppCode": "A13_AuditlogBusiness", "multiVersion": 0, "apiTag": ""}}, {"success": true, "code": 200, "message": "", "data": {"id": 2108770660671029249, "name": "用友YonBIP", "type": "integrateSys", "sort": 0, "enable": 0, "children": {"children": {"id": "PFC", "name": "数字化建模", "type": 1, "sort": 0, "enable": 0, "children": {"children": {"id": "PF", "name": "数字化建模", "type": 2, "sort": 0, "enable": 0, "children": {"children": {"id": "GZTSYS", "name": "系统管理", "type": 3, "sort": 0, "enable": 0, "children": {"children": {"id": "A13_AuditlogBusiness", "name": "审计日志业务日志", "type": 4, "sort": 0, "enable": 0, "children": "", "parentId": "", "productId": "", "code": "A13_AuditlogBusiness", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "GZTSYS", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "PF", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "PFC", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "current_yonbip_default_sys", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "isOrigin": 0, "hasChildren": 0, "order": 0}}]