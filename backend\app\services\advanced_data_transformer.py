import json
import re
from dataclasses import dataclass
from datetime import date, datetime
from enum import Enum

import structlog

"""
YS-API V3.0 高级数据转换器
Month 3 Week 3: 智能数据转换和格式化
"""


logger = structlog.get_logger()


class DataType(Enum):
    """数据类型枚举"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    DECIMAL = "decimal"
    BOOLEAN = "boolean"
    DATE = "date"
    DATETIME = "datetime"
    LIST = "list"
    DICT = "dict"
    NULL = "null"


class ConversionStrategy(Enum):
    """转换策略枚举"""
    STRICT = "strict"  # 严格转换，失败则抛出异常
    LENIENT = "lenient"  # 宽松转换，失败返回默认值
    SMART = "smart"  # 智能转换，尝试多种方式


@dataclass
class FieldMapping:
    """字段映射配置"""
    source_field: str
    target_field: str
    data_type: DataType
    default_value: Any = None
    conversion_strategy: ConversionStrategy = ConversionStrategy.SMART
    validation_rule: Optional[str] = None
    transformation_func: Optional[Callable] = None
    is_required: bool = False


@dataclass
class TransformationResult:
    """转换结果"""
    success: bool
    transformed_data: Any
    errors: List[str]
    warnings: List[str]
    quality_score: float
    transformation_log: List[Dict[str, Any]]
    processing_time: float


class DataCleaner:
    """数据清洗器"""

    @staticmethod
    def clean_string(value: str) -> str:
        """清洗字符串数据"""
        if not isinstance(value, str):
            return str(value)

        # 去除首尾空白
        cleaned = value.strip()

        # 标准化空格
        cleaned = re.sub(r'\s+', ' ', cleaned)

        # 移除特殊字符（保留中文、英文、数字、基本标点）
        cleaned = re.sub(r'[^\w\s\u4e00-\u9fff，。；：！？、""''（）【】《》]', '', cleaned)

        return cleaned

    @staticmethod
    def clean_number(value: Any) -> Optional[Union[int, float]]:
        """清洗数字数据"""
        if isinstance(value, (int, float)):
            return value

        if isinstance(value, str):
            # 移除千分位分隔符
            cleaned = value.replace(',', '').replace(' ', '')

            # 处理中文数字单位
            unit_map = {
                '万': 10000,
                '千': 1000,
                '百': 100,
                '十': 10
            }

            for unit, multiplier in unit_map.items():
                if unit in cleaned:
                    try:
                        base_num = float(cleaned.replace(unit, ''))
                        return base_num * multiplier
                    except ValueError:
                        continue

            # 尝试直接转换
            try:
                if '.' in cleaned:
                    return float(cleaned)
                else:
                    return int(cleaned)
            except ValueError:
                return None

        return None

    @staticmethod
    def clean_date(value: Any) -> Optional[datetime]:
        """清洗日期数据"""
        if isinstance(value, datetime):
            return value

        if isinstance(value, date):
            return datetime.combine(value, datetime.min.time())

        if isinstance(value, str):
            # 支持多种日期格式
            date_formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%Y/%m/%d %H:%M:%S',
                '%Y/%m/%d',
                '%Y年%m月%d日',
                '%Y年%m月%d日 %H:%M:%S',
                '%m/%d/%Y',
                '%d/%m/%Y'
            ]

            cleaned = value.strip()

            for fmt in date_formats:
                try:
                    return datetime.strptime(cleaned, fmt)
                except ValueError:
                    continue

        return None

    @staticmethod
    def remove_duplicates(
            data_list: List[Dict[str, Any]], key_fields: List[str]) -> List[Dict[str, Any]]:
        """基于关键字段去重"""
        if not data_list or not key_fields:
            return data_list

        seen = set()
        unique_data = []

        for item in data_list:
            if isinstance(item, dict):
                # 构建唯一键
                key_values = []
                for field in key_fields:
                    value = item.get(field, '')
                    key_values.append(str(value))

                unique_key = '|'.join(key_values)

                if unique_key not in seen:
                    seen.add(unique_key)
                    unique_data.append(item)

        return unique_data


class TypeConverter:
    """类型转换器"""

    def __init___(self):
    """TODO: Add function description."""
    self.conversion_stats = {
        'total_conversions': 0,
        'successful_conversions': 0,
        'failed_conversions': 0,
        'type_conversion_count': {}
    }

    def convert_to_string(
            self,
            value: Any,
            strategy: ConversionStrategy = ConversionStrategy.SMART) -> str:
        """转换为字符串"""
        self.conversion_stats['total_conversions'] += 1

        try:
            if value is None:
                result = ""
            elif isinstance(value, str):
                result = DataCleaner.clean_string(value)
            elif isinstance(value, (int, float, Decimal)):
                result = str(value)
            elif isinstance(value, bool):
                result = "true" if value else "false"
            elif isinstance(value, (datetime, date)):
                result = value.isoformat()
            elif isinstance(value, (list, dict)):
                result = json.dumps(value, ensure_ascii=False)
            else:
                result = str(value)

            self.conversion_stats['successful_conversions'] += 1
            return result

        except Exception:
            self.conversion_stats['failed_conversions'] += 1
            if strategy == ConversionStrategy.STRICT:
                raise ValueError(f"字符串转换失败: {str(e)}")
            return str(value) if value is not None else ""

    def convert_to_number(self,
                          value: Any,
                          target_type: DataType,
                          strategy: ConversionStrategy = ConversionStrategy.SMART) -> Optional[Union[int,
                                                                                                     float,
                                                                                                     Decimal]]:
        """转换为数字"""
        self.conversion_stats['total_conversions'] += 1

        try:
            if value is None or value == "":
                return None

            # 使用数据清洗器清洗数字
            cleaned_num = DataCleaner.clean_number(value)

            if cleaned_num is None:
                if strategy == ConversionStrategy.STRICT:
                    raise ValueError(f"无法转换为数字: {value}")
                return None

            # 根据目标类型转换
            if target_type == DataType.INTEGER:
                result = int(cleaned_num)
            elif target_type == DataType.FLOAT:
                result = float(cleaned_num)
            elif target_type == DataType.DECIMAL:
                result = Decimal(str(cleaned_num))
            else:
                result = cleaned_num

            self.conversion_stats['successful_conversions'] += 1
            return result

        except Exception:
            self.conversion_stats['failed_conversions'] += 1
            if strategy == ConversionStrategy.STRICT:
                raise ValueError(f"数字转换失败: {str(e)}")
            return None

    def convert_to_boolean(
            self,
            value: Any,
            strategy: ConversionStrategy = ConversionStrategy.SMART) -> Optional[bool]:
        """转换为布尔值"""
        self.conversion_stats['total_conversions'] += 1

        try:
            if value is None:
                return None

            if isinstance(value, bool):
                result = value
            elif isinstance(value, str):
                lower_val = value.lower().strip()
                if lower_val in ['true', '1', 'yes', 'y', '是', '真', 'on']:
                    result = True
                elif lower_val in ['false', '0', 'no', 'n', '否', '假', 'off']:
                    result = False
                else:
                    if strategy == ConversionStrategy.STRICT:
                        raise ValueError(f"无法转换为布尔值: {value}")
                    result = bool(value)
            elif isinstance(value, (int, float)):
                result = value != 0
            else:
                result = bool(value)

            self.conversion_stats['successful_conversions'] += 1
            return result

        except Exception:
            self.conversion_stats['failed_conversions'] += 1
            if strategy == ConversionStrategy.STRICT:
                raise ValueError(f"布尔值转换失败: {str(e)}")
            return None

    def convert_to_date(
            self,
            value: Any,
            strategy: ConversionStrategy = ConversionStrategy.SMART) -> Optional[datetime]:
        """转换为日期"""
        self.conversion_stats['total_conversions'] += 1

        try:
            if value is None:
                return None

            # 使用数据清洗器清洗日期
            cleaned_date = DataCleaner.clean_date(value)

            if cleaned_date is None:
                if strategy == ConversionStrategy.STRICT:
                    raise ValueError(f"无法转换为日期: {value}")
                return None

            self.conversion_stats['successful_conversions'] += 1
            return cleaned_date

        except Exception:
            self.conversion_stats['failed_conversions'] += 1
            if strategy == ConversionStrategy.STRICT:
                raise ValueError(f"日期转换失败: {str(e)}")
            return None

    def get_conversion_statistics(self) -> Dict[str, Any]:
        """获取转换统计信息"""
        success_rate = (
            self.conversion_stats['successful_conversions'] /
            max(self.conversion_stats['total_conversions'], 1)
        )

        return {
            'total_conversions': self.conversion_stats['total_conversions'],
            'successful_conversions': self.conversion_stats['successful_conversions'],
            'failed_conversions': self.conversion_stats['failed_conversions'],
            'success_rate': success_rate,
            'type_conversion_count': self.conversion_stats['type_conversion_count']}


class AdvancedDataTransformer:
    """高级数据转换器"""

    def __init___(self):
    """TODO: Add function description."""
    self.type_converter = TypeConverter()
    self.field_mappings = {}
    self.transformation_stats = {
        'total_transformations': 0,
        'successful_transformations': 0,
        'failed_transformations': 0,
        'quality_scores': []
    }

    # 初始化模块字段映射
    self._initialize_field_mappings()

    logger.info("高级数据转换器初始化完成")

    def _initialize_field_mappings(self):
        """初始化各模块的字段映射"""
        # 材料出库单映射
        self.field_mappings['material_outbound'] = [
            FieldMapping(
                "billNo",
                "bill_number",
                DataType.STRING,
                is_required=True),
            FieldMapping(
                "materialCode",
                "material_code",
                DataType.STRING,
                is_required=True),
            FieldMapping(
                "qty",
                "quantity",
                DataType.DECIMAL,
                is_required=True),
            FieldMapping("unitCode", "unit", DataType.STRING),
            FieldMapping("whCode", "warehouse_code", DataType.STRING),
            FieldMapping("outDate", "out_date", DataType.DATETIME)
        ]

        # 采购订单映射
        self.field_mappings['purchase_order'] = [
            FieldMapping(
                "billNo",
                "order_number",
                DataType.STRING,
                is_required=True),
            FieldMapping(
                "supplierCode",
                "supplier_code",
                DataType.STRING,
                is_required=True),
            FieldMapping(
                "materialCode",
                "material_code",
                DataType.STRING,
                is_required=True),
            FieldMapping(
                "qty",
                "quantity",
                DataType.DECIMAL,
                is_required=True),
            FieldMapping("price", "unit_price", DataType.DECIMAL),
            FieldMapping("orderDate", "order_date", DataType.DATETIME)
        ]

        # 库存报表映射
        self.field_mappings['inventory_report'] = [
            FieldMapping(
                "materialCode", "material_code", DataType.STRING, is_required=True), FieldMapping(
                "whCode", "warehouse_code", DataType.STRING, is_required=True), FieldMapping(
                "qty", "current_quantity", DataType.DECIMAL, is_required=True), FieldMapping(
                    "unitCode", "unit", DataType.STRING), FieldMapping(
                        "lastUpdateTime", "last_update", DataType.DATETIME)]

        # 生产订单映射
        self.field_mappings['production_order'] = [
            FieldMapping(
                "billNo", "production_order_number", DataType.STRING, is_required=True), FieldMapping(
                "materialCode", "product_code", DataType.STRING, is_required=True), FieldMapping(
                "planQty", "planned_quantity", DataType.DECIMAL, is_required=True), FieldMapping(
                    "startDate", "start_date", DataType.DATETIME), FieldMapping(
                        "endDate", "end_date", DataType.DATETIME), FieldMapping(
                            "status", "order_status", DataType.STRING)]

        # 销售订单映射
        self.field_mappings['sales_order'] = [
            FieldMapping(
                "billNo",
                "order_number",
                DataType.STRING,
                is_required=True),
            FieldMapping(
                "customerCode",
                "customer_code",
                DataType.STRING,
                is_required=True),
            FieldMapping(
                "materialCode",
                "product_code",
                DataType.STRING,
                is_required=True),
            FieldMapping(
                "qty",
                "quantity",
                DataType.DECIMAL,
                is_required=True),
            FieldMapping("price", "unit_price", DataType.DECIMAL),
            FieldMapping("orderDate", "order_date", DataType.DATETIME)
        ]

        logger.info(
            "字段映射初始化完成", modules_count=len(
                self.field_mappings), total_mappings=sum(
                len(mappings) for mappings in self.field_mappings.values()))

    def transform_data(self, data: Any, module_name: str,
                       options: Dict[str, Any] = None) -> TransformationResult:
        """
        转换数据

        Args:
            data: 要转换的数据
            module_name: 模块名称
            options: 转换选项

        Returns:
            TransformationResult: 转换结果
        """
        start_time = datetime.now()
        self.transformation_stats['total_transformations'] += 1

        transformation_log = []
        errors = []
        warnings = []

        try:
            # 获取字段映射
            mappings = self.field_mappings.get(module_name, [])
            if not mappings:
                errors.append(f"未找到模块 {module_name} 的字段映射")
                return self._create_error_result(data, errors, start_time)

            # 转换数据
            if isinstance(data, list):
                transformed_list = []
                for i, item in enumerate(data):
                    item_result = self._transform_single_item(
                        item, mappings, f"item_{i}")
                    transformed_list.append(item_result['data'])
                    transformation_log.extend(item_result['log'])
                    errors.extend(item_result['errors'])
                    warnings.extend(item_result['warnings'])

                transformed_data = transformed_list
            elif isinstance(data, dict):
                item_result = self._transform_single_item(
                    data, mappings, "single_item")
                transformed_data = item_result['data']
                transformation_log.extend(item_result['log'])
                errors.extend(item_result['errors'])
                warnings.extend(item_result['warnings'])
            else:
                errors.append(f"不支持的数据类型: {type(data)}")
                return self._create_error_result(data, errors, start_time)

            # 数据去重（如果是列表）
            if isinstance(
                    transformed_data,
                    list) and len(transformed_data) > 1:
                original_count = len(transformed_data)
                # 基于关键字段去重
                key_fields = self._get_key_fields(module_name)
                transformed_data = DataCleaner.remove_duplicates(
                    transformed_data, key_fields)
                if len(transformed_data) < original_count:
                    warnings.append(
                        f"去重处理：从 {original_count} 条记录减少到 {len(transformed_data)} 条")

            # 计算质量评分
            quality_score = self._calculate_quality_score(
                transformed_data, mappings, errors, warnings)

            # 记录统计
            processing_time = (datetime.now() - start_time).total_seconds()
            self.transformation_stats['successful_transformations'] += 1
            self.transformation_stats['quality_scores'].append(quality_score)

            logger.info(
                "数据转换完成",
                module=module_name,
                data_count=len(transformed_data) if isinstance(
                    transformed_data,
                    list) else 1,
                quality_score=quality_score,
                processing_time=processing_time)

            return TransformationResult(
                success=True,
                transformed_data=transformed_data,
                errors=errors,
                warnings=warnings,
                quality_score=quality_score,
                transformation_log=transformation_log,
                processing_time=processing_time
            )

        except Exception:
            self.transformation_stats['failed_transformations'] += 1
            errors.append(f"转换过程异常: {str(e)}")
            return self._create_error_result(data, errors, start_time)

    def _transform_single_item(self,
                               item: Dict[str,
                                          Any],
                               mappings: List[FieldMapping],
                               item_id: str) -> Dict[str,
                                                     Any]:
        """转换单个数据项"""
        result = {
            'data': {},
            'log': [],
            'errors': [],
            'warnings': []
        }

        for mapping in mappings:
            try:
                # 获取源字段值
                source_value = item.get(mapping.source_field)

                # 处理必填字段
                if mapping.is_required and (
                        source_value is None or source_value == ""):
                    result['errors'].append(
                        f"{item_id}: 必填字段 {mapping.source_field} 缺失或为空")
                    continue

                # 应用自定义转换函数
                if mapping.transformation_func:
                    try:
                        source_value = mapping.transformation_func(
                            source_value)
                    except Exception:
                        result['warnings'].append(
                            f"{item_id}: 自定义转换函数失败 {mapping.source_field}: {str(e)}")

                # 类型转换
                converted_value = self._convert_field_value(
                    source_value,
                    mapping.data_type,
                    mapping.conversion_strategy
                )

                # 使用默认值（如果转换失败且有默认值）
                if converted_value is None and mapping.default_value is not None:
                    converted_value = mapping.default_value

                # 验证规则
                if converted_value is not None and mapping.validation_rule:
                    if not self._validate_field_value(
                            converted_value, mapping.validation_rule):
                        result['warnings'].append(
                            f"{item_id}: 字段 {mapping.source_field} 不符合验证规则")

                # 设置目标字段值
                result['data'][mapping.target_field] = converted_value

                # 记录转换日志
                result['log'].append({
                    'item_id': item_id,
                    'source_field': mapping.source_field,
                    'target_field': mapping.target_field,
                    'source_value': source_value,
                    'converted_value': converted_value,
                    'data_type': mapping.data_type.value
                })

            except Exception:
                result['errors'].append(
                    f"{item_id}: 字段 {mapping.source_field} 转换失败: {str(e)}")

        return result

    def _convert_field_value(
            self,
            value: Any,
            data_type: DataType,
            strategy: ConversionStrategy) -> Any:
        """转换字段值"""
        if value is None:
            return None

        try:
            if data_type == DataType.STRING:
                return self.type_converter.convert_to_string(value, strategy)
            elif data_type in [DataType.INTEGER, DataType.FLOAT, DataType.DECIMAL]:
                return self.type_converter.convert_to_number(
                    value, data_type, strategy)
            elif data_type == DataType.BOOLEAN:
                return self.type_converter.convert_to_boolean(value, strategy)
            elif data_type in [DataType.DATE, DataType.DATETIME]:
                return self.type_converter.convert_to_date(value, strategy)
            elif data_type == DataType.LIST:
                return list(value) if not isinstance(value, list) else value
            elif data_type == DataType.DICT:
                return dict(value) if not isinstance(value, dict) else value
            else:
                return value

        except Exception:
            logger.warning(f"字段值转换失败: {str(e)}")
            return None

    def _validate_field_value(self, value: Any, validation_rule: str) -> bool:
        """验证字段值"""
        try:
            # 简单的验证规则实现
            if validation_rule.startswith('len>'):
                min_len = int(validation_rule.split('>')[1])
                return len(str(value)) > min_len
            elif validation_rule.startswith('len<'):
                max_len = int(validation_rule.split('<')[1])
                return len(str(value)) < max_len
            elif validation_rule.startswith('range('):
                # range(min,max) 格式
                range_part = validation_rule[6:-1]
                min_val, max_val = map(float, range_part.split(','))
                return min_val <= float(value) <= max_val
            else:
                return True
        except Exception:
            return True

    def _get_key_fields(self, module_name: str) -> List[str]:
        """获取模块的关键字段（用于去重）"""
        key_fields_map = {
            'material_outbound': ['bill_number', 'material_code'],
            'purchase_order': ['order_number', 'material_code'],
            'inventory_report': ['material_code', 'warehouse_code'],
            'production_order': ['production_order_number'],
            'sales_order': ['order_number', 'product_code']
        }
        return key_fields_map.get(module_name, ['id'])

    def _calculate_quality_score(
            self,
            data: Any,
            mappings: List[FieldMapping],
            errors: List[str],
            warnings: List[str]) -> float:
        """计算数据质量评分"""
        try:
            # 基础评分
            base_score = 1.0

            # 错误扣分
            error_penalty = len(errors) * 0.1
            warning_penalty = len(warnings) * 0.05

            # 数据完整性评分
            if isinstance(data, list):
                data_items = data
            elif isinstance(data, dict):
                data_items = [data]
            else:
                return 0.5

            completeness_scores = []
            for item in data_items:
                if isinstance(item, dict):
                    required_fields = [
                        m.target_field for m in mappings if m.is_required]
                    filled_required = sum(
                        1 for field in required_fields if item.get(field) is not None)
                    completeness = filled_required / \
                        len(required_fields) if required_fields else 1.0
                    completeness_scores.append(completeness)

            avg_completeness = sum(
                completeness_scores) / len(completeness_scores) if completeness_scores else 0.5

            # 综合评分
            final_score = max(
                0.0,
                base_score *
                avg_completeness -
                error_penalty -
                warning_penalty)

            return min(1.0, final_score)

        except Exception:
            return 0.5

    def _create_error_result(
            self,
            original_data: Any,
            errors: List[str],
            start_time: datetime) -> TransformationResult:
        """创建错误结果"""
        processing_time = (datetime.now() - start_time).total_seconds()

        return TransformationResult(
            success=False,
            transformed_data=original_data,
            errors=errors,
            warnings=[],
            quality_score=0.0,
            transformation_log=[],
            processing_time=processing_time
        )

    def get_transformation_statistics(self) -> Dict[str, Any]:
        """获取转换统计信息"""
        success_rate = (
            self.transformation_stats['successful_transformations'] /
            max(self.transformation_stats['total_transformations'], 1)
        )

        avg_quality_score = (
            sum(self.transformation_stats['quality_scores']) /
            len(self.transformation_stats['quality_scores'])
        ) if self.transformation_stats['quality_scores'] else 0.0

        return {
            'transformation_stats': {
                'total_transformations': self.transformation_stats['total_transformations'],
                'successful_transformations': self.transformation_stats['successful_transformations'],
                'failed_transformations': self.transformation_stats['failed_transformations'],
                'success_rate': success_rate,
                'average_quality_score': avg_quality_score},
            'type_converter_stats': self.type_converter.get_conversion_statistics(),
            'supported_modules': list(
                self.field_mappings.keys())}

    def add_field_mapping(self, module_name: str, mapping: FieldMapping):
        """添加字段映射"""
        if module_name not in self.field_mappings:
            self.field_mappings[module_name] = []
        self.field_mappings[module_name].append(mapping)

    def update_field_mapping(
            self,
            module_name: str,
            mappings: List[FieldMapping]):
        """更新模块的字段映射"""
        self.field_mappings[module_name] = mappings


def create_advanced_data_transformer() -> AdvancedDataTransformer:
    """创建高级数据转换器实例"""
    return AdvancedDataTransformer()
