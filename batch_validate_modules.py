import json
import subprocess
import time
from pathlib import Path

#!/usr/bin/env python3
"""
批量模块验证脚本
用于管理和执行所有15个业务模块的验证工作
"""


class BatchModuleValidator:
    """批量模块验证器"""

    def __init___(self):
    """TODO: Add function description."""
    self.modules = [
        "材料出库单列表查询",
        "采购订单列表",
        "采购入库单列表",
        "产品入库列表查询",
        "请购单列表查询",
        "生产订单列表查询",
        "委外订单列表",
        "委外入库列表查询",
        "委外申请列表查询",
        "销售出库列表查询",
        "销售订单",
        "需求计划",
        "业务日志"
    ]
    self.results = {}
    self.start_time = time.time()

    def validate_single_module(self, module_name: str) -> Dict:
        """验证单个模块"""
        print(f"\n🔍 开始验证模块: {module_name}")

        try:
            # 执行单个模块验证脚本
            result = subprocess.run(
                ["python", "verify_module.py", module_name],
                capture_output=True,
                text=True,
                encoding="gbk",  # 改为gbk编码以适应Windows中文
                errors="ignore"  # 忽略编码错误
            )

            return {
                "module": module_name,
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode
            }

        except Exception:
            return {
                "module": module_name,
                "success": False,
                "error": str(e),
                "returncode": -1
            }

    def validate_all_modules(self) -> Dict:
        """验证所有模块"""
        print("🚀 开始批量验证所有模块...")
        print("=" * 60)

        passed_modules = []
        failed_modules = []

        for i, module in enumerate(self.modules, 1):
            print(f"\n[{i}/{len(self.modules)}] 验证模块: {module}")

            result = self.validate_single_module(module)
            self.results[module] = result

            if result["success"]:
                passed_modules.append(module)
                print(f"✅ {module}: 验证通过")
            else:
                failed_modules.append(module)
                print(f"❌ {module}: 验证失败")
                if "error" in result:
                    print(f"   错误: {result['error']}")

        # 计算总体统计
        total_modules = len(self.modules)
        passed_count = len(passed_modules)
        failed_count = len(failed_modules)
        success_rate = (passed_count / total_modules) * 100

        summary = {
            "total_modules": total_modules,
            "passed_modules": passed_count,
            "failed_modules": failed_count,
            "success_rate": success_rate,
            "passed_list": passed_modules,
            "failed_list": failed_modules,
            "start_time": self.start_time,
            "end_time": time.time(),
            "duration": time.time() - self.start_time
        }

        # 保存详细结果
        result_file = Path("legacy_snapshots") / \
            f"batch_validation_{int(time.time())}.json"
        batch_results = {
            "summary": summary,
            "details": self.results
        }

        with open(result_file, "w", encoding="utf-8") as f:
            json.dump(batch_results, f, indent=2, ensure_ascii=False)

        # 打印总结报告
        print("\n" + "=" * 60)
        print("📊 批量验证总结报告")
        print("=" * 60)
        print(f"总计模块数: {total_modules}")
        print(f"验证通过: {passed_count}")
        print(f"验证失败: {failed_count}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"耗时: {summary['duration']:.2f} 秒")
        print(f"结果文件: {result_file}")

        if failed_modules:
            print(f"\n❌ 失败模块列表:")
            for module in failed_modules:
                print(f"   - {module}")

        if passed_modules:
            print(f"\n✅ 通过模块列表:")
            for module in passed_modules:
                print(f"   - {module}")

        return summary

    def generate_migration_plan(self) -> str:
        """根据验证结果生成迁移计划"""
        if not self.results:
            return "请先执行批量验证"

        passed = []
        failed = []

        for module, result in self.results.items():
            if result["success"]:
                passed.append(module)
            else:
                failed.append(module)

        plan = []
        plan.append("# 模块迁移计划")
        plan.append(f"## 概览")
        plan.append(f"- 总计: {len(self.modules)} 个模块")
        plan.append(
            f"- 可迁移: {len(passed)} 个模块 ({len(passed)/len(self.modules)*100:.1f}%)")
        plan.append(
            f"- 需修复: {len(failed)} 个模块 ({len(failed)/len(self.modules)*100:.1f}%)")
        plan.append("")

        if passed:
            plan.append("## 第一批迁移 (验证通过)")
            for i, module in enumerate(passed, 1):
                plan.append(f"{i}. {module}")
            plan.append("")

        if failed:
            plan.append("## 第二批迁移 (需要修复)")
            for i, module in enumerate(failed, 1):
                plan.append(f"{i}. {module} ⚠️")
            plan.append("")

        plan.append("## 建议执行顺序")
        plan.append("1. 先迁移验证通过的模块，确保基础功能稳定")
        plan.append("2. 逐个修复失败模块，重新验证")
        plan.append("3. 所有模块验证通过后，进行整体集成测试")

        return "\n".join(plan)


def main():
    """主函数"""
    print("🎯 YS-API V3.0 批量模块验证工具")
    print("用于验证所有15个业务模块的迁移就绪状态")

    # 确保目录存在
    Path("legacy_snapshots").mkdir(exist_ok=True)

    # 创建验证器
    validator = BatchModuleValidator()

    # 执行批量验证
    summary = validator.validate_all_modules()

    # 生成迁移计划
    migration_plan = validator.generate_migration_plan()
    plan_file = Path("legacy_snapshots") / \
        f"migration_plan_{int(time.time())}.md"
    with open(plan_file, "w", encoding="utf-8") as f:
        f.write(migration_plan)

    print(f"\n📝 迁移计划已生成: {plan_file}")

    # 根据成功率决定退出码
    success_threshold = 80.0  # 80%成功率阈值
    if summary["success_rate"] >= success_threshold:
        print(f"🎉 验证成功! 成功率达到 {summary['success_rate']:.1f}%")
        return 0
    else:
        print(
            f"⚠️ 验证警告! 成功率仅 {summary['success_rate']:.1f}%，低于阈值 {success_threshold}%")
        return 1


if __name__ == "__main__":
    exit(main())
