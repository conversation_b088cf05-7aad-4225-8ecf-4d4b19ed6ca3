# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 启动命令验证脚本
用于确认正确的启动命令和端口配置
"""

import sys
from pathlib import Path


def verify_startup_commands():
    """验证启动命令和文件存在性"""
    project_root = Path(__file__).parent

    print("🔍 YS-API V3.0 启动命令验证")
    print("=" * 50)

    # 检查启动脚本文件
    backend_script = project_root / "backend" / "start_server_fixed.py"
    frontend_script = project_root / "frontend" / "start_frontend_fixed.py"

    print("📁 启动脚本文件检查:")
    print(
        f"  后端脚本: {'✅ 存在' if backend_script.exists() else '❌ 不存在'} - {backend_script}"
    )
    print(
        f"  前端脚本: {'✅ 存在' if frontend_script.exists() else '❌ 不存在'} - {frontend_script}"
    )

    print("\n🚀 正确的启动命令:")
    print("  1. 启动后端服务 (端口8050):")
    print("     python backend/start_server_fixed.py")
    print("     或")
    print(f"     python {backend_script}")

    print("\n  2. 启动前端服务 (端口8060):")
    print("     python frontend/start_frontend_fixed.py")
    print("     或")
    print(f"     python {frontend_script}")

    print("\n🌐 服务地址:")
    print("  后端服务: http://localhost:8050 (固定，禁止改动)")
    print("  前端界面: http://localhost:8060 (固定，禁止改动)")
    print("  API文档:  http://localhost:8050/docs")

    print("\n⚠️  重要提示:")
    print("  - 需要在项目根目录下执行启动命令")
    print("  - 端口8050和8060已标准化，禁止修改")
    print("  - 先启动后端，再启动前端")
    print("  - 使用 Ctrl+C 停止服务")

    return backend_script.exists() and frontend_script.exists()


if __name__ == "__main__":
    success = verify_startup_commands()
    if not success:
        print("\n❌ 启动脚本文件缺失，请检查项目完整性")
        sys.exit(1)
    else:
        print("\n✅ 启动命令验证完成，项目配置正确！")
