{"analysis_report": {"hash_duplicates": {"d41d8cd98f00b204e9800998ecf8427e": ["check_output.txt", "scripts\\test_server.py"], "69c83ee3a317ff14281cce31ce5b83ad": ["config.ini", "backend\\config.ini"], "4760eda6e5d386aec19c0f141cc2e534": ["config.ini.backup_env", "backend\\config.ini.backup_env"], "7dbe7abb3579a2e8da5c224ff2bb61b3": ["Dockerfile", "templates\\dockerfile.template"], "8d49fbd6a7675f3f437449a16bf547de": ["Jenkins<PERSON><PERSON>", "templates\\jenkinsfile.template"], "7e1b4b359b618b4c0565ba8d502095cb": ["SECURITY_RECOMMENDATIONS_config.ini.md", "backend\\SECURITY_RECOMMENDATIONS_config.ini.md"], "6b995fb330f0c4fbff521b3cd8a0b63e": ["backend\\__init__.py", "backend\\app\\__init__.py"], "c48dc842dbb0718b3c95e95d46a3890b": ["backend\\app\\services\\zero_downtime_implementation.py", "backend\\app\\services\\zero_downtime_implementation.py.backup_20250802_202310"], "d739594f9bc5009b481da4862ba998c7": ["frontend\\css\\element-plus.css", "frontend\\static\\css\\element-plus.css"], "72cd339fe4d6ad45f9d5470270bebbd9": ["frontend\\css\\realtime-log.css", "frontend\\static\\css\\realtime-log.css"], "1f44fa96e296fe1cbededd53e1054e89": ["frontend\\css\\smart-logger.css", "frontend\\static\\css\\smart-logger.css"], "d207bcfd53a6bc4ede2d45d41ed8ca1d": ["frontend\\js\\element-plus-icons.iife.min.js", "frontend\\static\\js\\element-plus-icons.iife.min.js"], "9abe4f77db295519d7ca8c0e67c2a51a": ["frontend\\js\\element-plus.js", "frontend\\static\\js\\element-plus.js"], "448787bf6995a8bfb65a1fe0519a99d0": ["frontend\\js\\vue.global.js", "frontend\\static\\js\\vue.global.js"], "fd78ea0ef1081857e641095f847dd80b": ["scripts\\deploy.bat", "templates\\deploy_windows.template"], "fd512393448962a78435eeb87f7d768d": ["scripts\\deploy.sh", "templates\\deploy.template"], "db1ef8e085a46595cc089358563f4ca9": ["scripts\\rollback.sh", "templates\\rollback.template"], "e11f0237d41b594a721e3095327597f0": ["模块字段\\采购订单列表.xml", "模块字段\\backup\\采购订单列表.xml"]}, "name_pattern_duplicates": {".*temp.*": [".env.template", "templates\\deploy.template", "templates\\deploy_production.template", "templates\\deploy_staging.template", "templates\\deploy_windows.template", "templates\\dockerfile.template", "templates\\dockerignore.template", "templates\\jenkinsfile.template", "templates\\rollback.template"], ".*test.*": ["comprehensive_production_test.py", "setup_test_env.py", "start_quick_test.py", ".cleanup_trash\\dup_method-fix-test.html", ".cleanup_trash\\dup_migration-test.html", ".cleanup_trash\\dup_new-architecture-test.html", ".cleanup_trash\\dup_path-test.html", ".cleanup_trash\\dup_production_test_runner.py", ".cleanup_trash\\dup_run_test.bat", ".cleanup_trash\\dup_start_architecture_test.bat", ".cleanup_trash\\dup_start_test_server.bat", ".cleanup_trash\\dup_test_baseline_api.py", ".cleanup_trash\\dup_test_server.py", "config\\data\\user_field_config\\TestUser\\material_master.json", "config\\data\\user_field_config\\TestUser\\purchase_order.json", "config\\data\\user_field_config\\TestUser\\sales_order.json", "frontend\\migration-test-fixed.html", "frontend\\js\\common\\test-data.js", "frontend\\tests\\api-client.test.js", "frontend\\tests\\baseline-save.test.js", "frontend\\tests\\error-handler.test.js", "frontend\\tests\\field-list-display.test.js", "frontend\\tests\\load-controller.test.js", "frontend\\tests\\module-selector.test.js", "frontend\\tests\\notification-system.test.js", "frontend\\tests\\performance-optimization.test.js", "frontend\\tests\\progress-display.test.js", "frontend\\tests\\run-tests.js", "frontend\\tests\\setup.js", "frontend\\tests\\user-config-save.test.js", "frontend\\tests\\reports\\test-report-1753834790480.json", "scripts\\frontend_test_server.py", "scripts\\test_elk_connection.py", "scripts\\test_server.py", "tests\\api_test_server.py", "tests\\field-config-manual-migrated.html", "tests\\locust_stress_test.py", "tests\\path-test.html", "tests\\test_baseline_api.py", "tests\\test_md_to_json_converter.py", "tests\\test_rollback_scripts.py", "tests\\黑名单功能移除测试.md", "tests\\__pycache__\\locust_stress_test.cpython-310-pytest-7.4.3.pyc", "tests\\__pycache__\\test_rollback_scripts.cpython-310-pytest-7.4.3.pyc", "tools\\error_handling_load_test.py", "tools\\__pycache__\\error_handling_load_test.cpython-310-pytest-7.4.3.pyc"], ".*_backup.*": ["config_environmentizer.py.broken_backup"], ".*_\\d{8}.*": ["final_project_fixer.py.backup_20250802_202310", "fix_build_script.py.backup_20250802_202310", "fix_execute_task_script.py.backup_20250802_202310", "fix_issues.py.backup_20250802_202310", "fix_task_issues.py.backup_20250802_202310", "install_windows_service.py.backup_20250802_202310", "production_readiness_report_20250802_212920.json", "production_readiness_report_20250802_212920.md", "production_readiness_report_20250804_013940.json", "production_readiness_report_20250804_013940.md", "project_health_check.py.backup_20250802_202310", "project_health_checker.py.backup_20250802_202310", "quick_health_report_20250802_210530.json", "quick_health_report_20250802_210539.json", "quick_health_report_20250804_000515.json", "quick_health_report_20250804_000530.json", "quick_health_report_20250804_001017.json", "quick_health_report_20250804_001940.json", "run_comprehensive_check.py.backup_20250802_202310", "start_quick_test.py.backup_20250802_202310", "test_baseline_api.py.backup_20250802_202310", "universal_code_quality_fixer.py.backup_20250802_202310", "backend\\start_server.py.backup_20250802_202310", "backend\\start_simple.py.backup_20250802_202310", "backend\\app\\main.py.backup_20250802_202310", "backend\\app\\api\\v1\\config.py.backup_20250802_202310", "backend\\app\\core\\config.py.backup_20250802_202310", "backend\\app\\services\\robust_json_parser.py.backup_20250802_202310", "backend\\app\\services\\unified_field_service.py.backup_20250802_202310", "backend\\app\\services\\zero_downtime_implementation.py.backup_20250802_202310", "dev-tools\\cleanup\\code_cleaner.py.backup_20250802_202310", "dev-tools\\mock\\mock_utils.py.backup_20250802_202310", "logs\\production_test\\production_test_20250802_210131.log", "logs\\production_test\\production_test_20250802_210250.log", "logs\\production_test\\production_test_20250802_210409.log", "scripts\\add_api_config.py.backup_20250802_202310", "scripts\\auto_migration.py.backup_20250802_202310", "scripts\\diagnose_migration.py.backup_20250802_202310", "scripts\\fix_css_paths.py.backup_20250802_202310", "scripts\\fix_migrated_paths.py.backup_20250802_202310", "scripts\\reliable_server.py.backup_20250802_202310", "scripts\\rollback_batch_writes.py.backup_20250802_202310", "scripts\\test_elk_connection.py.backup_20250802_202310", "scripts\\test_server.py.backup_20250802_202310", "scripts\\validate_deployment.py.backup_20250802_202310", "scripts\\verify_fixes.py.backup_20250802_202310", "tests\\test_md_to_json_converter.py.backup_20250802_202310", "tools\\error_handling_load_test.py.backup_20250802_202310"], ".*_fixed.*": [".cleanup_trash\\dup_start_server_fixed.bat"], ".*_migrated.*": ["scripts\\fix_migrated_paths.py"]}, "content_similarity": {"__init__": ["auto_fix_comprehensive_issues.py", "auto_project_cleanup.py", "build_production_package.py", "cicd_pipeline_builder.py", "cicd_pipeline_builder_optimized.py", "comprehensive_production_test.py", "config_environmentizer.py", "config_environmentizer.py", "config_environmentizer_clean.py", "config_environmentizer_clean.py", "execute_task_checklist.py", "final_project_fixer.py", "fix_build_script.py", "fix_execute_task_script.py", "fix_issues.py", "fix_task_issues.py", "install_windows_service.py", "project_health_check.py", "project_health_check.py", "remaining_issues_fixer.py", "run_comprehensive_check.py", "systematic_duplicate_detector.py", "backend\\app\\api\\v1\\sync.py", "backend\\app\\core\\code_quality.py", "backend\\app\\core\\code_quality.py", "backend\\app\\core\\code_quality.py", "backend\\app\\core\\database_connection_pool.py", "backend\\app\\core\\database_manager.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\exceptions.py", "backend\\app\\core\\optimized_retry.py", "backend\\app\\services\\auto_recovery_manager_enhanced.py", "backend\\app\\services\\auto_recovery_manager_enhanced.py", "backend\\app\\services\\auto_recovery_manager_enhanced.py", "backend\\app\\services\\auto_recovery_manager_enhanced.py", "backend\\app\\services\\auto_recovery_manager_enhanced.py", "backend\\app\\services\\auto_recovery_manager_enhanced.py", "backend\\app\\services\\auto_sync_scheduler.py", "backend\\app\\services\\auto_sync_scheduler.py", "backend\\app\\services\\auto_sync_scheduler.py", "backend\\app\\services\\auto_sync_scheduler.py", "backend\\app\\services\\auto_sync_scheduler.py", "backend\\app\\services\\business_translation_rules.py", "backend\\app\\services\\config_persistence_service.py", "backend\\app\\services\\database_manager.py", "backend\\app\\services\\database_table_manager.py", "backend\\app\\services\\data_processor.py", "backend\\app\\services\\data_write_manager.py", "backend\\app\\services\\enhanced_json_field_matcher.py", "backend\\app\\services\\excel_field_matcher.py", "backend\\app\\services\\excel_field_matcher_pretranslated.py", "backend\\app\\services\\fast_sync_service.py", "backend\\app\\services\\fast_sync_service.py", "backend\\app\\services\\fast_sync_service.py", "backend\\app\\services\\fast_sync_service.py", "backend\\app\\services\\fast_sync_service.py", "backend\\app\\services\\fast_sync_service.py", "backend\\app\\services\\fast_sync_service.py", "backend\\app\\services\\field_analysis_service.py", "backend\\app\\services\\field_config_service.py", "backend\\app\\services\\field_extractor_service.py", "backend\\app\\services\\field_validation_service.py", "backend\\app\\services\\field_value_mapping_service.py", "backend\\app\\services\\intelligent_field_mapper.py", "backend\\app\\services\\log_service.py", "backend\\app\\services\\maintenance_manager.py", "backend\\app\\services\\material_master_scheduler.py", "backend\\app\\services\\md_parser.py", "backend\\app\\services\\monitor_service.py", "backend\\app\\services\\realtime_log_service.py", "backend\\app\\services\\realtime_log_service.py", "backend\\app\\services\\retry_helper.py", "backend\\app\\services\\retry_helper.py", "backend\\app\\services\\robust_json_parser.py", "backend\\app\\services\\status_mapping_service.py", "backend\\app\\services\\sync_status_manager.py", "backend\\app\\services\\task_service.py", "backend\\app\\services\\unified_field_manager.py", "backend\\app\\services\\unified_field_service.py", "backend\\app\\services\\ys_api_client.py", "backend\\app\\services\\ys_api_client.py", "backend\\app\\services\\zero_downtime_implementation.py", "backend\\app\\services\\zero_downtime_implementation.py", "backend\\app\\services\\zero_downtime_implementation.py", "dev-tools\\cleanup\\code_cleaner.py", "dev-tools\\mock\\mock_utils.py", "dev-tools\\mock\\mock_utils.py", "dev-tools\\mock\\mock_utils.py", "scripts\\auto_migration.py", "scripts\\clean_debug_code.py", "scripts\\rollback_batch_writes.py", "scripts\\test_elk_connection.py", "scripts\\validate_deployment.py", "tests\\locust_stress_test.py", "tests\\locust_stress_test.py", "tools\\error_handling_load_test.py"], "generate_fix_report": ["auto_fix_comprehensive_issues.py", "fix_task_issues.py"], "run_all_fixes": ["auto_fix_comprehensive_issues.py", "fix_issues.py", "project_health_check.py"], "run_cleanup": ["auto_project_cleanup.py", "scripts\\clean_debug_code.py"], "main": ["auto_project_cleanup.py", "comprehensive_production_test.py", "final_project_fixer.py", "production_readiness_report.py", "quick_health_check.py", "systematic_duplicate_detector.py", "backend\\app\\services\\unified_field_service.py", "backend\\app\\services\\zero_downtime_implementation.py", "dev-tools\\cleanup\\code_cleaner.py", "scripts\\add_api_config.py", "scripts\\auto_migration.py", "scripts\\diagnose_migration.py", "scripts\\fix_css_paths.py", "scripts\\fix_migrated_paths.py", "scripts\\reliable_server.py", "scripts\\rollback_batch_writes.py", "scripts\\test_elk_connection.py", "scripts\\validate_deployment.py", "tools\\error_handling_load_test.py"], "create_github_actions_workflow": ["cicd_pipeline_builder.py", "cicd_pipeline_builder_optimized.py"], "create_docker_files": ["cicd_pipeline_builder.py", "cicd_pipeline_builder_optimized.py"], "create_jenkins_pipeline": ["cicd_pipeline_builder.py", "cicd_pipeline_builder_optimized.py"], "create_deployment_scripts": ["cicd_pipeline_builder.py", "cicd_pipeline_builder_optimized.py"], "create_monitoring_config": ["cicd_pipeline_builder.py", "cicd_pipeline_builder_optimized.py"], "create_documentation": ["cicd_pipeline_builder.py", "cicd_pipeline_builder_optimized.py"], "build_pipeline": ["cicd_pipeline_builder.py", "cicd_pipeline_builder_optimized.py"], "generate_summary_report": ["cicd_pipeline_builder.py", "cicd_pipeline_builder_optimized.py"], "cleanup": ["comprehensive_production_test.py", "backend\\app\\services\\database_table_manager.py"], "run_environmentization": ["config_environmentizer.py", "config_environmentizer_clean.py"], "load_from_system_env": ["config_environmentizer.py", "config_environmentizer_clean.py"], "verify_fix": ["fix_build_script.py", "fix_execute_task_script.py"], "run_fix": ["fix_build_script.py", "fix_execute_task_script.py"], "fix_missing_dependencies": ["fix_issues.py", "project_health_check.py"], "fix_database": ["fix_issues.py", "project_health_check.py"], "fix_temp_files": ["fix_issues.py", "project_health_check.py"], "check_file_structure": ["project_health_check.py", "run_comprehensive_check.py"], "check_api_endpoints": ["project_health_check.py", "run_comprehensive_check.py"], "start_test_server": ["start_quick_test.py", "tests\\api_test_server.py"], "end_headers": ["start_quick_test.py", "scripts\\frontend_test_server.py", "scripts\\reliable_server.py"], "do_OPTIONS": ["start_quick_test.py", "scripts\\frontend_test_server.py"], "root": ["backend\\start_simple.py", "backend\\app\\main.py", "backend\\app\\main_original.py"], "health_check": ["backend\\start_simple.py", "backend\\app\\main.py", "backend\\app\\main_original.py"], "database_v2_page": ["backend\\start_simple.py", "backend\\app\\main_original.py"], "unified_field_config_page": ["backend\\start_simple.py", "backend\\app\\main_original.py"], "field_config_page": ["backend\\start_simple.py", "backend\\app\\main.py", "backend\\app\\main_original.py"], "excel_translation_page": ["backend\\start_simple.py", "backend\\app\\main_original.py"], "test_loading_page": ["backend\\start_simple.py", "backend\\app\\main_original.py"], "favicon": ["backend\\start_simple.py", "backend\\app\\main.py", "backend\\app\\main_original.py"], "field_config_manual_page": ["backend\\app\\main.py", "backend\\app\\main_original.py"], "test_simple_page": ["backend\\app\\main.py", "backend\\app\\main_original.py"], "get_modules": ["backend\\app\\main.py", "backend\\app\\main_original.py", "backend\\app\\api\\v1\\config.py"], "save_baseline_config": ["backend\\app\\main.py", "backend\\app\\api\\v1\\field_config_api.py"], "save_user_config": ["backend\\app\\main.py", "backend\\app\\api\\v1\\field_config_api.py"], "not_found_handler": ["backend\\app\\main.py", "backend\\app\\main_original.py"], "get_sync_status": ["backend\\app\\api\\v1\\enhanced_sync.py", "backend\\app\\api\\v1\\monitor.py"], "session_factory": ["backend\\app\\api\\v1\\enhanced_sync.py", "backend\\app\\api\\v1\\sync.py", "backend\\app\\api\\v1\\sync.py", "backend\\app\\api\\v1\\sync.py", "backend\\app\\services\\data_write_manager.py"], "manual_cleanup_cache": ["backend\\app\\api\\v1\\maintenance.py", "backend\\app\\services\\maintenance_manager.py"], "manual_cleanup_temp": ["backend\\app\\api\\v1\\maintenance.py", "backend\\app\\services\\maintenance_manager.py"], "manual_cleanup_logs": ["backend\\app\\api\\v1\\maintenance.py", "backend\\app\\services\\maintenance_manager.py"], "get_system_status": ["backend\\app\\api\\v1\\monitor.py", "tests\\locust_stress_test.py"], "stop_sync": ["backend\\app\\api\\v1\\monitor.py", "backend\\app\\api\\v1\\sync.py", "backend\\app\\api\\v1\\sync.py"], "clear_history": ["backend\\app\\api\\v1\\realtime_logs.py", "backend\\app\\services\\realtime_log_service.py"], "cleanup_expired_locks": ["backend\\app\\api\\v1\\sync_status.py", "backend\\app\\services\\sync_status_manager.py"], "start_auto_sync": ["backend\\app\\api\\v1\\tasks.py", "backend\\app\\services\\auto_sync_scheduler.py"], "stop_auto_sync": ["backend\\app\\api\\v1\\tasks.py", "backend\\app\\services\\auto_sync_scheduler.py"], "trigger_manual_sync": ["backend\\app\\api\\v1\\tasks.py", "backend\\app\\services\\auto_sync_scheduler.py"], "start_robust_auto_sync": ["backend\\app\\api\\v1\\tasks.py", "backend\\app\\services\\auto_sync_scheduler.py"], "stop_robust_auto_sync": ["backend\\app\\api\\v1\\tasks.py", "backend\\app\\services\\auto_sync_scheduler.py"], "reset_retry_state": ["backend\\app\\api\\v1\\tasks.py", "backend\\app\\services\\retry_helper.py"], "start": ["backend\\app\\core\\database_connection_pool.py", "backend\\app\\services\\maintenance_manager.py", "backend\\app\\services\\material_master_scheduler.py"], "stop": ["backend\\app\\core\\database_connection_pool.py", "backend\\app\\services\\maintenance_manager.py", "backend\\app\\services\\material_master_scheduler.py"], "get_connection": ["backend\\app\\core\\database_connection_pool.py", "backend\\app\\services\\database_manager.py", "backend\\app\\services\\database_table_manager.py", "scripts\\rollback_batch_writes.py"], "sync_wrapper": ["backend\\app\\core\\optimized_retry.py", "backend\\app\\services\\fast_sync_service.py"], "save_field_config": ["backend\\app\\core\\optimized_retry.py", "tests\\locust_stress_test.py"], "record_attempt": ["backend\\app\\core\\optimized_retry.py", "backend\\app\\services\\retry_helper.py"], "release": ["backend\\app\\services\\auto_recovery_manager_enhanced.py", "backend\\app\\services\\auto_sync_scheduler.py"], "on_success": ["backend\\app\\services\\auto_recovery_manager_enhanced.py", "backend\\app\\services\\auto_sync_scheduler.py"], "on_failure": ["backend\\app\\services\\auto_recovery_manager_enhanced.py", "backend\\app\\services\\auto_sync_scheduler.py"], "clear_cache": ["backend\\app\\services\\config_persistence_service.py", "backend\\app\\services\\field_extractor_service.py"], "close": ["backend\\app\\services\\database_manager.py", "backend\\app\\services\\data_write_manager.py", "backend\\app\\services\\ys_api_client.py"], "__enter__": ["backend\\app\\services\\database_manager.py", "tests\\locust_stress_test.py"], "__exit__": ["backend\\app\\services\\database_manager.py", "tests\\locust_stress_test.py"], "__post_init__": ["backend\\app\\services\\database_table_manager.py", "backend\\app\\services\\robust_json_parser.py"], "record_request": ["backend\\app\\services\\fast_sync_service.py", "backend\\app\\services\\ys_api_client.py"], "set_current_module": ["backend\\app\\services\\field_config_service.py", "backend\\app\\services\\field_extractor_service.py", "backend\\app\\services\\intelligent_field_mapper.py"], "log_message": ["scripts\\frontend_test_server.py", "tests\\api_test_server.py"], "setup_method": ["tests\\test_md_to_json_converter.py", "tests\\test_md_to_json_converter.py", "tests\\test_md_to_json_converter.py", "tests\\test_md_to_json_converter.py", "tests\\test_rollback_scripts.py", "tests\\test_rollback_scripts.py", "tests\\test_rollback_scripts.py"]}, "backup_files": [], "temp_files": ["scripts\\test_elk_connection.py", "scripts\\test_server.py", "tests\\test_baseline_api.py", "tests\\test_md_to_json_converter.py", "tests\\test_rollback_scripts.py", "setup_test_env.py", ".cleanup_trash\\dup_production_test_runner.py", ".cleanup_trash\\dup_test_baseline_api.py", ".cleanup_trash\\dup_test_server.py", "scripts\\frontend_test_server.py", "tests\\api_test_server.py"]}, "cleanup_plan": {"safe_to_delete": [], "needs_verification": [{"type": "hash_duplicate", "files": ["check_output.txt", "scripts\\test_server.py"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["config.ini", "backend\\config.ini"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["config.ini.backup_env", "backend\\config.ini.backup_env"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["Dockerfile", "templates\\dockerfile.template"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["Jenkins<PERSON><PERSON>", "templates\\jenkinsfile.template"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["SECURITY_RECOMMENDATIONS_config.ini.md", "backend\\SECURITY_RECOMMENDATIONS_config.ini.md"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["backend\\__init__.py", "backend\\app\\__init__.py"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["backend\\app\\services\\zero_downtime_implementation.py", "backend\\app\\services\\zero_downtime_implementation.py.backup_20250802_202310"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["frontend\\css\\element-plus.css", "frontend\\static\\css\\element-plus.css"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["frontend\\css\\realtime-log.css", "frontend\\static\\css\\realtime-log.css"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["frontend\\css\\smart-logger.css", "frontend\\static\\css\\smart-logger.css"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["frontend\\js\\element-plus-icons.iife.min.js", "frontend\\static\\js\\element-plus-icons.iife.min.js"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["frontend\\js\\element-plus.js", "frontend\\static\\js\\element-plus.js"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["frontend\\js\\vue.global.js", "frontend\\static\\js\\vue.global.js"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["scripts\\deploy.bat", "templates\\deploy_windows.template"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["scripts\\deploy.sh", "templates\\deploy.template"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["scripts\\rollback.sh", "templates\\rollback.template"], "action": "keep_one_delete_others"}, {"type": "hash_duplicate", "files": ["模块字段\\采购订单列表.xml", "模块字段\\backup\\采购订单列表.xml"], "action": "keep_one_delete_others"}], "merge_candidates": [{"function": "__init__", "files": ["backend\\app\\services\\status_mapping_service.py", "tests\\locust_stress_test.py", "build_production_package.py", "config_environmentizer_clean.py", "backend\\app\\services\\data_write_manager.py", "backend\\app\\services\\field_analysis_service.py", "backend\\app\\services\\auto_recovery_manager_enhanced.py", "backend\\app\\services\\robust_json_parser.py", "backend\\app\\services\\maintenance_manager.py", "backend\\app\\services\\auto_sync_scheduler.py", "backend\\app\\services\\material_master_scheduler.py", "execute_task_checklist.py", "backend\\app\\services\\data_processor.py", "backend\\app\\services\\zero_downtime_implementation.py", "backend\\app\\services\\field_extractor_service.py", "backend\\app\\services\\field_value_mapping_service.py", "config_environmentizer.py", "backend\\app\\services\\unified_field_service.py", "dev-tools\\mock\\mock_utils.py", "remaining_issues_fixer.py", "systematic_duplicate_detector.py", "backend\\app\\services\\database_manager.py", "backend\\app\\services\\monitor_service.py", "fix_task_issues.py", "fix_build_script.py", "backend\\app\\core\\optimized_retry.py", "backend\\app\\services\\business_translation_rules.py", "backend\\app\\services\\excel_field_matcher_pretranslated.py", "scripts\\auto_migration.py", "backend\\app\\services\\database_table_manager.py", "backend\\app\\services\\retry_helper.py", "backend\\app\\services\\log_service.py", "backend\\app\\services\\enhanced_json_field_matcher.py", "scripts\\validate_deployment.py", "project_health_check.py", "backend\\app\\services\\intelligent_field_mapper.py", "auto_fix_comprehensive_issues.py", "backend\\app\\core\\database_connection_pool.py", "backend\\app\\services\\realtime_log_service.py", "backend\\app\\services\\field_config_service.py", "auto_project_cleanup.py", "scripts\\test_elk_connection.py", "cicd_pipeline_builder_optimized.py", "backend\\app\\services\\excel_field_matcher.py", "backend\\app\\core\\code_quality.py", "cicd_pipeline_builder.py", "comprehensive_production_test.py", "run_comprehensive_check.py", "final_project_fixer.py", "backend\\app\\services\\md_parser.py", "fix_execute_task_script.py", "backend\\app\\services\\config_persistence_service.py", "backend\\app\\services\\sync_status_manager.py", "tools\\error_handling_load_test.py", "backend\\app\\services\\fast_sync_service.py", "install_windows_service.py", "backend\\app\\services\\ys_api_client.py", "fix_issues.py", "backend\\app\\core\\database_manager.py", "backend\\app\\services\\task_service.py", "backend\\app\\api\\v1\\sync.py", "backend\\app\\core\\exceptions.py", "dev-tools\\cleanup\\code_cleaner.py", "backend\\app\\services\\field_validation_service.py", "scripts\\rollback_batch_writes.py", "scripts\\clean_debug_code.py", "backend\\app\\services\\unified_field_manager.py"], "suggestion": "考虑将__init__函数统一到一个文件中"}, {"function": "run_all_fixes", "files": ["project_health_check.py", "fix_issues.py", "auto_fix_comprehensive_issues.py"], "suggestion": "考虑将run_all_fixes函数统一到一个文件中"}, {"function": "main", "files": ["backend\\app\\services\\zero_downtime_implementation.py", "production_readiness_report.py", "backend\\app\\services\\unified_field_service.py", "scripts\\reliable_server.py", "systematic_duplicate_detector.py", "scripts\\fix_migrated_paths.py", "scripts\\auto_migration.py", "scripts\\validate_deployment.py", "scripts\\fix_css_paths.py", "scripts\\diagnose_migration.py", "auto_project_cleanup.py", "scripts\\test_elk_connection.py", "comprehensive_production_test.py", "final_project_fixer.py", "tools\\error_handling_load_test.py", "quick_health_check.py", "dev-tools\\cleanup\\code_cleaner.py", "scripts\\rollback_batch_writes.py", "scripts\\add_api_config.py"], "suggestion": "考虑将main函数统一到一个文件中"}, {"function": "end_headers", "files": ["scripts\\frontend_test_server.py", "scripts\\reliable_server.py", "start_quick_test.py"], "suggestion": "考虑将end_headers函数统一到一个文件中"}, {"function": "root", "files": ["backend\\app\\main.py", "backend\\app\\main_original.py", "backend\\start_simple.py"], "suggestion": "考虑将root函数统一到一个文件中"}, {"function": "health_check", "files": ["backend\\app\\main.py", "backend\\app\\main_original.py", "backend\\start_simple.py"], "suggestion": "考虑将health_check函数统一到一个文件中"}, {"function": "field_config_page", "files": ["backend\\app\\main.py", "backend\\app\\main_original.py", "backend\\start_simple.py"], "suggestion": "考虑将field_config_page函数统一到一个文件中"}, {"function": "favicon", "files": ["backend\\app\\main.py", "backend\\app\\main_original.py", "backend\\start_simple.py"], "suggestion": "考虑将favicon函数统一到一个文件中"}, {"function": "get_modules", "files": ["backend\\app\\main_original.py", "backend\\app\\main.py", "backend\\app\\api\\v1\\config.py"], "suggestion": "考虑将get_modules函数统一到一个文件中"}, {"function": "session_factory", "files": ["backend\\app\\services\\data_write_manager.py", "backend\\app\\api\\v1\\sync.py", "backend\\app\\api\\v1\\enhanced_sync.py"], "suggestion": "考虑将session_factory函数统一到一个文件中"}, {"function": "start", "files": ["backend\\app\\core\\database_connection_pool.py", "backend\\app\\services\\maintenance_manager.py", "backend\\app\\services\\material_master_scheduler.py"], "suggestion": "考虑将start函数统一到一个文件中"}, {"function": "stop", "files": ["backend\\app\\core\\database_connection_pool.py", "backend\\app\\services\\maintenance_manager.py", "backend\\app\\services\\material_master_scheduler.py"], "suggestion": "考虑将stop函数统一到一个文件中"}, {"function": "get_connection", "files": ["backend\\app\\core\\database_connection_pool.py", "backend\\app\\services\\database_manager.py", "backend\\app\\services\\database_table_manager.py", "scripts\\rollback_batch_writes.py"], "suggestion": "考虑将get_connection函数统一到一个文件中"}, {"function": "close", "files": ["backend\\app\\services\\ys_api_client.py", "backend\\app\\services\\database_manager.py", "backend\\app\\services\\data_write_manager.py"], "suggestion": "考虑将close函数统一到一个文件中"}, {"function": "set_current_module", "files": ["backend\\app\\services\\field_extractor_service.py", "backend\\app\\services\\intelligent_field_mapper.py", "backend\\app\\services\\field_config_service.py"], "suggestion": "考虑将set_current_module函数统一到一个文件中"}], "rename_suggestions": []}}