[{"success": true, "code": 200, "message": "", "data": {"fieldVersion": 20230210, "appCode": "", "tokenSet": false, "tokenDoc": "", "tenantId": 0, "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "id": "edc735d69b654129b0da4c38f2d8877f", "name": "产品入库列表查询", "apiClassifyId": "cd241c49f6fb427ca4d40e87ab224373", "apiClassifyName": "产品入库单", "apiClassifyCode": "", "parentApiClassifies": "", "functionId": "", "openMode": 0, "description": "产品入库列表查询", "auth": true, "bodyPassthrough": false, "healthExam": false, "healthStatus": true, "responseResultPassthrough": false, "contentType": "application/json", "returnPassthrough": "", "completeProxyUrl": "/yonbip/scm/storeprorecord/list", "connectUrl": "/bill/list", "sort": 20, "handler": "openapi", "httpRequestType": "POST", "openApi": true, "preset": false, "productId": "710a0be3edff4f9092e35f63fd3b9bae", "productCode": "scm", "proxyUrl": "/yonbip/scm/storeprorecord/list", "requestParamsDemo": "Url : /yonsuite/scm/storeprorecord/list?access_token=访问令牌 Body: { \"pageIndex\": 0, \"code\": \"\", \"pageSize\": 0, \"warehouse_name\": [ \"\" ], \"bustype_name\": \"\", \"stockMgr_name\": [ \"\" ], \"operator\": [ \"\" ], \"department\": [ \"\" ], \"org\": {}, \"product_cName\": [ \"\" ], \"open_hopeReceiveDate_begin\": \"\", \"open_hopeReceiveDate_end\": \"\", \"simpleVOs\": [ { \"field\": \"pubts\", \"op\": \"between\", \"value1\": \"2020-09-19 00:00:00\", \"value2\": \"2020-09-19 23:59:59\" }, { \"field\": \"headItem.define1\", //表头自定义项 \"op\": \"eq\", \"value1\": \"test1\" }, { \"field\": \"storeProRecords.bodyItem.define1\", //表体自定义项 \"op\": \"eq\", \"value1\": \"test2\" } ] }", "requestProtocol": "HTTP", "serviceHttpMethod": "POST", "publishStatus": true, "approvalMsg": "", "rpcAppName": "", "rpcServiceName": "", "rpcMethodName": "", "rpcServiceUrl": "", "ma": false, "gmtCreate": "2020-01-16 18:20:20", "gmtUpdate": "2024-12-23 14:41:42.000", "address": "https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/storeprorecord/list", "productName": "采购供应", "productClassifyId": "yonsuite", "productClassifyCode": "yonbip", "productClassifyName": "用友 YonBIP", "paramDTOS": {"paramDTOS": [{"id": 2162396650814308363, "name": "pageIndex", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735046, "array": false, "paramDesc": "页号", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": 1, "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "[pageIndex]", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308364, "name": "code", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735047, "array": false, "paramDesc": "单据编号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308365, "name": "pageSize", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735048, "array": false, "paramDesc": "每页行数", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": 10, "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "[pageSize]", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308366, "name": "warehouse_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735049, "array": true, "paramDesc": "仓库名字", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308367, "name": "bustype_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735050, "array": false, "paramDesc": "交易类型", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308368, "name": "stockMgr_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735051, "array": true, "paramDesc": "库管人名字列表", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308369, "name": "operator", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735052, "array": true, "paramDesc": "操作员id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308370, "name": "department", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735053, "array": true, "paramDesc": "部门id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308371, "name": "org", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735054, "array": false, "paramDesc": "组织id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308372, "name": "product_cName", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735055, "array": true, "paramDesc": "物料ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308373, "name": "open_hopeReceiveDate_begin", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735056, "array": false, "paramDesc": "区间查询开始时间 : \"2020-03-02\"", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308374, "name": "open_hopeReceiveDate_end", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735057, "array": false, "paramDesc": "区间查询结束时间 :\"2020-03-02 23:59:59\"", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308358, "name": "simpleVOs", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "children": {"children": [{"id": 2162396650814308359, "name": "field", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308358, "defParamId": 1856797255115735059, "array": false, "paramDesc": "属性名(条件),子表加前缀storeProRecords.", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308360, "name": "op", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308358, "defParamId": 1856797255115735060, "array": false, "paramDesc": "条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308361, "name": "value1", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308358, "defParamId": 1856797255115735061, "array": false, "paramDesc": "值1(条件),单条件时仅使用这个配置", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2162396650814308362, "name": "value2", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308358, "defParamId": 1856797255115735062, "array": false, "paramDesc": "值2(条件),单条件时此配置无效", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "defParamId": 1856797255115735058, "array": true, "paramDesc": "扩展查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "queryParamDTOS": "", "ysApi": false, "presetTokenApi": false, "applyFlag": false, "cover": false, "paramMapDTOS": {"paramMapDTOS": [{"id": 2162396650814308380, "name": "pageIndex", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": "", "array": false, "paramDesc": "页号", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "pageIndex", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308381, "name": "code", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": "", "array": false, "paramDesc": "单据编号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "code", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308382, "name": "pageSize", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": "", "array": false, "paramDesc": "每页行数", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "pageSize", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308383, "name": "warehouse_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": "", "array": false, "paramDesc": "仓库名字", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "warehouse_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308384, "name": "bustype_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": "", "array": false, "paramDesc": "交易类型", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "aggregatedValueObject": false, "mapName": "bustype_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308385, "name": "stockMgr_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": "", "array": false, "paramDesc": "库管人名字列表", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "aggregatedValueObject": false, "mapName": "stockMgr_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308386, "name": "operator", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": "", "array": false, "paramDesc": "操作员id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "aggregatedValueObject": false, "mapName": "operator", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308387, "name": "department", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": "", "array": false, "paramDesc": "部门id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "aggregatedValueObject": false, "mapName": "department", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308388, "name": "org", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": "", "array": false, "paramDesc": "组织id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "aggregatedValueObject": false, "mapName": "org", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308389, "name": "product_cName", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": "", "array": false, "paramDesc": "物料ID", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "aggregatedValueObject": false, "mapName": "product_cName", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308390, "name": "open_hopeReceiveDate_begin", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": "", "array": false, "paramDesc": "区间查询开始时间 : \"2020-03-02\"", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "aggregatedValueObject": false, "mapName": "open_hopeReceiveDate_begin", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308391, "name": "open_hopeReceiveDate_end", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": "", "array": false, "paramDesc": "区间查询结束时间 :\"2020-03-02 23:59:59\"", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "aggregatedValueObject": false, "mapName": "open_hopeReceiveDate_end", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308375, "name": "simpleVOs", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "children": {"children": [{"id": 2162396650814308376, "name": "field", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308375, "defParamId": "", "array": false, "paramDesc": "属性名(条件),子表加前缀storeProRecords.", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "field", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308377, "name": "op", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308375, "defParamId": "", "array": false, "paramDesc": "条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "op", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308378, "name": "value1", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308375, "defParamId": "", "array": false, "paramDesc": "值1(条件),单条件时仅使用这个配置", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "value1", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2162396650814308379, "name": "value2", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308375, "defParamId": "", "array": false, "paramDesc": "值2(条件),单条件时此配置无效", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "value2", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "defParamId": "", "array": false, "paramDesc": "扩展查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "aggregatedValueObject": false, "mapName": "simpleVOs", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "paramReturnDTOS": {"paramReturnDTOS": [{"id": 2162396650814308472, "name": "code", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735080, "array": false, "paramDesc": "返回码，调用成功时返回200", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308473, "name": "message", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "defParamId": 1856797255115735081, "array": false, "paramDesc": "调用失败时的错误信息", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308392, "name": "data", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": "", "children": {"children": [{"id": 2162396650814308393, "name": "sumRecordList", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308392, "children": {"children": [{"id": 2162396650814308394, "name": "totalQuantity", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308393, "defParamId": 1856797255115735084, "array": false, "paramDesc": "总数量", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308395, "name": "qty", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308393, "defParamId": 1856797255115735085, "array": false, "paramDesc": "数量", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308396, "name": "totalPieces", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308393, "defParamId": 1856797255115735086, "array": false, "paramDesc": "总件", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308397, "name": "subQty", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308393, "defParamId": 1856797255115735087, "array": false, "paramDesc": "子表数量", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1856797255115735083, "array": true, "paramDesc": "sum合计信息", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308465, "name": "pageIndex", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308392, "defParamId": 1856797255115735088, "array": false, "paramDesc": "当前页", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308466, "name": "pageSize", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308392, "defParamId": 1856797255115735089, "array": false, "paramDesc": "分页大小", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308467, "name": "pageCount", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308392, "defParamId": 1856797255115735090, "array": false, "paramDesc": "总页数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308468, "name": "beginPageIndex", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308392, "defParamId": 1856797255115735091, "array": false, "paramDesc": "开始页码", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308469, "name": "endPageIndex", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308392, "defParamId": 1856797255115735092, "array": false, "paramDesc": "结束页码", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308470, "name": "recordCount", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308392, "defParamId": 1856797255115735093, "array": false, "paramDesc": "总记录数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308471, "name": "pubts", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308392, "defParamId": 1856797255115735094, "array": false, "paramDesc": "时间戳", "paramType": "string", "requestParamType": "", "path": "", "example": "格式：yyyy-MM-dd HH:mm:ss", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308398, "name": "recordList", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308392, "children": {"children": [{"id": 2162396650814308399, "name": "factoryFiOrg", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735096, "array": false, "paramDesc": "完工组织id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308400, "name": "storeProRecords_product", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735097, "array": false, "paramDesc": "物料id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308401, "name": "currency", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735098, "array": false, "paramDesc": "币种id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308402, "name": "storeProRecords_unit", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735099, "array": false, "paramDesc": "主计量", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308403, "name": "storeProRecords_productsku", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735100, "array": false, "paramDesc": "物料sku", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308404, "name": "storeProRecords_stockUnitId", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735101, "array": false, "paramDesc": "库存单位id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308405, "name": "vouchdate", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735102, "array": false, "paramDesc": "单据日期", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308406, "name": "code", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735103, "array": false, "paramDesc": "单据编号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308407, "name": "department_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735104, "array": false, "paramDesc": "部门名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"department.name\",\"cItemName\":\"department_name\",\"cCaption\":\"部门\",\"cShowCaption\":\"部门\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_department\",\"cRefId\":null,\"cRefRetId\":{\"department\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecord\",\"cControlType\":\"TreeRefer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308408, "name": "accountOrg", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735105, "array": false, "paramDesc": "会计主体id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"accountOrg\",\"cItemName\":\"accountOrg\",\"cCaption\":\"会计主体id\",\"cShowCaption\":\"会计主体id\",\"iMaxLength\":255,\"bHidden\":true,\"cRefType\":\"aa_orgtree\",\"cRefId\":null,\"cRefRetId\":{\"accountOrg_name\":\"name\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecord\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":{\"id\":\"4f1ba370-624b-4de6-90d3-d916f2b3df9c\",\"metaType\":\"Class\",\"name\":\"FinanceOrg\",\"uri\":\"aa.org.FinanceOrg\"},\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308409, "name": "org_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735106, "array": false, "paramDesc": "库存组织名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"org.name\",\"cItemName\":\"org_name\",\"cCaption\":\"库存组织\",\"cShowCaption\":\"库存组织\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_orgstock\",\"cRefId\":null,\"cRefRetId\":{\"org\":\"id\",\"accountOrg\":\"finorgid\",\"accountOrg_name\":\"finorg_name\"},\"cDataRule\":\"\\\"%u8c-config.option.singleOrg%>\\\"==\\\"false\\\"  \\\"%loginUser.storeId%>\\\"==\\\"null\\\"\",\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecord\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308410, "name": "stockMgr_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735107, "array": false, "paramDesc": "库管员名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"stockMgr.name\",\"cItemName\":\"stockMgr_name\",\"cCaption\":\"库管员\",\"cShowCaption\":\"库管员\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_operator\",\"cRefId\":null,\"cRefRetId\":{\"stockMgr\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecord\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308411, "name": "department", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735108, "array": false, "paramDesc": "部门id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"department\",\"cItemName\":\"department\",\"cCaption\":\"部门id\",\"cShowCaption\":\"部门id\",\"iMaxLength\":255,\"bHidden\":true,\"cRefType\":\"aa_department\",\"cRefId\":null,\"cRefRetId\":{\"department_name\":\"name\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecord\",\"cControlType\":\"TreeRefer\",\"refReturn\":\"name\",\"dataType\":{\"id\":\"4d9e3282-6100-48ff-826c-3e50a36cc72e\",\"metaType\":\"Class\",\"name\":\"Department\",\"uri\":\"aa.dept.Department\"},\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308412, "name": "totalPieces", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735109, "array": false, "paramDesc": "整单件数(废弃)", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308413, "name": "org", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735110, "array": false, "paramDesc": "库存组织id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 14, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"org\",\"cItemName\":\"org\",\"cCaption\":\"库存组织id\",\"cShowCaption\":\"库存组织id\",\"iMaxLength\":255,\"bHidden\":true,\"cRefType\":\"aa_orgstock\",\"cRefId\":null,\"cRefRetId\":{\"org\":\"id\",\"accountOrg\":\"finorgid\",\"accountOrg_name\":\"finorg_name\"},\"cDataRule\":\"\\\"%productcenter.option.singleOrg%>\\\"==\\\"false\\\"\",\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecord\",\"cControlType\":\"Column\",\"refReturn\":\"id\",\"dataType\":{\"id\":\"c1135e08-c4bf-4499-90bf-67030d1f2653\",\"metaType\":\"Class\",\"name\":\"InventoryOrg\",\"uri\":\"aa.org.InventoryOrg\"},\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308414, "name": "stockMgr", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735111, "array": false, "paramDesc": "库管员IDid", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308415, "name": "store", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735112, "array": false, "paramDesc": "门店id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308416, "name": "store_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735113, "array": false, "paramDesc": "门店名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"store.name\",\"cItemName\":\"store_name\",\"cCaption\":\"门店\",\"cShowCaption\":\"门店\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_department\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":\"\\\"%productcenter.option.isOpenURetail%>\\\"==\\\"true\\\"\",\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecord\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308417, "name": "warehouse", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735114, "array": false, "paramDesc": "仓库id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308418, "name": "warehouse_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735115, "array": false, "paramDesc": "仓库名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"warehouse.name\",\"cItemName\":\"warehouse_name\",\"cCaption\":\"仓库\",\"cShowCaption\":\"仓库\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_warehouse\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecord\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308419, "name": "bustype", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735116, "array": false, "paramDesc": "业务类型id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 20, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308420, "name": "accountOrg_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735117, "array": false, "paramDesc": "会计主体名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 21, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"accountOrg.name\",\"cItemName\":\"accountOrg_name\",\"cCaption\":\"会计主体\",\"cShowCaption\":\"会计主体\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_orgtree\",\"cRefId\":null,\"cRefRetId\":{\"accountOrg\":\"id\"},\"cDataRule\":\"\\\"%u8c-config.option.singleOrg%>\\\"==\\\"false\\\"\",\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecord\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308421, "name": "bustype_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735118, "array": false, "paramDesc": "交易类型名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 22, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"bustype.name\",\"cItemName\":\"bustype_name\",\"cCaption\":\"交易类型\",\"cShowCaption\":\"交易类型\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_user\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecord\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308422, "name": "status", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735119, "array": false, "paramDesc": "单据状态, 0:未提交、1:已提交、", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 23, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308423, "name": "operator", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735120, "array": false, "paramDesc": "经办人id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 24, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308424, "name": "totalQuantity", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735121, "array": false, "paramDesc": "整单数量(废弃)", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 25, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308425, "name": "totalMaterial", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735122, "array": false, "paramDesc": "已材料出, true:是、false:否、(废弃)", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 26, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308426, "name": "creator", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735123, "array": false, "paramDesc": "创建人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 27, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308427, "name": "createTime", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735124, "array": false, "paramDesc": "创建时间", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 28, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308428, "name": "modifier", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735125, "array": false, "paramDesc": "最后修改人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 29, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308429, "name": "modifyTime", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735126, "array": false, "paramDesc": "最后修改时间", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 30, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308430, "name": "auditor", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735127, "array": false, "paramDesc": "提交人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 31, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308431, "name": "auditTime", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735128, "array": false, "paramDesc": "提交时间", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 32, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308432, "name": "memo", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735129, "array": false, "paramDesc": "备注", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 33, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308433, "name": "auditorId", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735130, "array": false, "paramDesc": "审批人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 34, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308434, "name": "creatorId", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735131, "array": false, "paramDesc": "创建人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 35, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308435, "name": "id", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735132, "array": false, "paramDesc": "主表id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 36, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308436, "name": "modifierId", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735133, "array": false, "paramDesc": "修改人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 37, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308437, "name": "pubts", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735134, "array": false, "paramDesc": "时间戳", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 38, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308438, "name": "tplid", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735135, "array": false, "paramDesc": "模板id(废弃)", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 39, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308439, "name": "storeProRecords_id", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735136, "array": false, "paramDesc": "子表id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 40, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308440, "name": "product_cCode", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735137, "array": false, "paramDesc": "物料编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 41, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"storeProRecords.product.cCode\",\"cItemName\":\"product_cCode\",\"cCaption\":\"物料编码\",\"cShowCaption\":\"物料编码\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cName\":\"product_cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"unit\":\"oUnitId\",\"unit_name\":\"unitName\",\"product_unitName\":\"unitName\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"stockUnitId\":\"stockUnit\",\"stockUnit_name\":\"stockUnit_name\",\"product_cCode\":\"cCode\",\"productsku_cName\":\"cName\",\"invExchRate\":\"stockRate\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"unit_Precision\":\"unitPrecision\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecords\",\"cControlType\":\"refer\",\"refReturn\":\"cCode\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308441, "name": "product_cName", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735138, "array": false, "paramDesc": "物料名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 42, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"storeProRecords.product.cName\",\"cItemName\":\"product_cName\",\"cCaption\":\"物料名称\",\"cShowCaption\":\"物料名称\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cName\":\"product_cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"unit\":\"oUnitId\",\"unit_name\":\"unitName\",\"product_unitName\":\"unitName\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"stockUnitId\":\"stockUnit\",\"stockUnit_name\":\"stockUnit_name\",\"product_cCode\":\"cCode\",\"productsku_cName\":\"cName\",\"invExchRate\":\"stockRate\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"unit_Precision\":\"unitPrecision\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecords\",\"cControlType\":\"refer\",\"refReturn\":\"cName\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308443, "name": "productsku_cCode", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735140, "array": false, "paramDesc": "sku编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 44, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"storeProRecords.productsku.cCode\",\"cItemName\":\"productsku_cCode\",\"cCaption\":\"sku编码\",\"cShowCaption\":\"sku编码\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cName\":\"product_cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"unit\":\"oUnitId\",\"unit_name\":\"unitName\",\"product_unitName\":\"unitName\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"stockUnitId\":\"stockUnit\",\"stockUnit_name\":\"stockUnit_name\",\"product_cCode\":\"cCode\",\"productsku_cName\":\"cName\",\"invExchRate\":\"stockRate\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"unit_Precision\":\"unitPrecision\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecords\",\"cControlType\":\"refer\",\"refReturn\":\"productskus_cCode\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308444, "name": "productsku_cName", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735141, "array": false, "paramDesc": "sku名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 45, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"storeProRecords.productsku.skuName\",\"cItemName\":\"productsku_cName\",\"cCaption\":\"sku名称\",\"cShowCaption\":\"sku名称\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cName\":\"product_cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"unit\":\"oUnitId\",\"unit_name\":\"unitName\",\"product_unitName\":\"unitName\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"stockUnitId\":\"stockUnit\",\"stockUnit_name\":\"stockUnit_name\",\"product_cCode\":\"cCode\",\"productsku_cName\":\"cName\",\"invExchRate\":\"stockRate\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"unit_Precision\":\"unitPrecision\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecords\",\"cControlType\":\"refer\",\"refReturn\":\"cName\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308445, "name": "product_modelDescription", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735142, "array": false, "paramDesc": "规格型号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 46, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308446, "name": "qty", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735143, "array": false, "paramDesc": "数量", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 47, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308447, "name": "product_unitName", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735144, "array": false, "paramDesc": "计量单位", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 48, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308448, "name": "subQty", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735145, "array": false, "paramDesc": "件数", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 49, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308449, "name": "stockUnit_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735146, "array": false, "paramDesc": "库存单位", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 50, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"storeProRecords.stockUnitId.name\",\"cItemName\":\"stockUnit_name\",\"cCaption\":\"库存单位\",\"cShowCaption\":\"库存单位\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"productcenter.pc_productassitunitsref\",\"cRefId\":null,\"cRefRetId\":{\"stockUnitId\":\"assistUnit\",\"stockUnit_name\":\"assistUnit_Name\",\"invExchRate\":\"mainUnitCount\",\"unitExchangeType\":\"unitExchangeType\",\"stockUnitId_Precision\":\"assistUnit_Precision\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecords\",\"cControlType\":\"Refer\",\"refReturn\":\"assistUnit_Name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308450, "name": "project_name", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735147, "array": false, "paramDesc": "项目名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 51, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"storeProRecords.project.name\",\"cItemName\":\"project_name\",\"cCaption\":\"项目名称\",\"cShowCaption\":\"项目名称\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucfbasedoc.bd_outer_projectcardMCref\",\"cRefId\":null,\"cRefRetId\":{\"project\":\"id\",\"project_name\":\"name\",\"project_code\":\"code\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.storeprorecord.StoreProRecords\",\"cControlType\":\"Refer\",\"refReturn\":null,\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308451, "name": "natUnitPrice", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735148, "array": false, "paramDesc": "单价", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 52, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308452, "name": "natMoney", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735149, "array": false, "paramDesc": "金额", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 53, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308453, "name": "natCurrency_priceDigit", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735150, "array": false, "paramDesc": "币种单价精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 54, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308454, "name": "natCurrency_moneyDigit", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735151, "array": false, "paramDesc": "币种金额精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 55, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308455, "name": "unit_Precision", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735152, "array": false, "paramDesc": "主计量精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 56, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308456, "name": "stockUnitId_Precision", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735153, "array": false, "paramDesc": "库存单位精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 57, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308459, "name": "out_sys_id", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735156, "array": false, "paramDesc": "外部来源线索", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 60, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308460, "name": "out_sys_code", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735157, "array": false, "paramDesc": "外部来源编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 61, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308461, "name": "out_sys_version", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735158, "array": false, "paramDesc": "外部来源版本", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 62, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308462, "name": "out_sys_type", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735159, "array": false, "paramDesc": "外部来源类型", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 63, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308463, "name": "out_sys_rowno", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735160, "array": false, "paramDesc": "外部来源行号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 64, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2162396650814308464, "name": "out_sys_lineid", "apiId": "edc735d69b654129b0da4c38f2d8877f", "parentId": 2162396650814308398, "defParamId": 1856797255115735161, "array": false, "paramDesc": "外部来源行", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 65, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1856797255115735095, "array": true, "paramDesc": "返回结果对象", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "respData", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1856797255115735082, "array": false, "paramDesc": "调用成功时的返回数据", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "returnFormatType": "JSON", "paramConstDTOS": "", "paramConstMapDTOS": "", "apiDemoReturnDTOS": {"apiDemoReturnDTOS": [{"id": 2162396659404242948, "apiId": "edc735d69b654129b0da4c38f2d8877f", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"sumRecordList\": [ { \"totalQuantity\": \"\", \"qty\": \"\", \"totalPieces\": \"\", \"subQty\": \"\" } ], \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"pubts\": \"格式：yyyy-MM-dd HH:mm:ss\", \"recordList\": [ { \"factoryFiOrg\": \"\", \"storeProRecords_product\": \"\", \"currency\": \"\", \"storeProRecords_unit\": \"\", \"storeProRecords_productsku\": \"\", \"storeProRecords_stockUnitId\": \"\", \"vouchdate\": \"\", \"code\": \"\", \"department_name\": \"\", \"accountOrg\": \"\", \"org_name\": \"\", \"stockMgr_name\": \"\", \"department\": \"\", \"totalPieces\": \"\", \"org\": \"\", \"stockMgr\": \"\", \"store\": \"\", \"store_name\": \"\", \"warehouse\": \"\", \"warehouse_name\": \"\", \"bustype\": \"\", \"accountOrg_name\": \"\", \"bustype_name\": \"\", \"status\": \"\", \"operator\": \"\", \"totalQuantity\": 0, \"totalMaterial\": \"\", \"creator\": \"\", \"createTime\": \"\", \"modifier\": \"\", \"modifyTime\": \"\", \"auditor\": \"\", \"auditTime\": \"\", \"memo\": \"\", \"auditorId\": \"\", \"creatorId\": \"\", \"id\": \"\", \"modifierId\": \"\", \"pubts\": \"\", \"tplid\": \"\", \"storeProRecords_id\": \"\", \"product_cCode\": \"\", \"product_cName\": \"\", \"storeProRecordsCharacteristics\": 0, \"productsku_cCode\": \"\", \"productsku_cName\": \"\", \"product_modelDescription\": \"\", \"qty\": 0, \"product_unitName\": \"\", \"subQty\": 0, \"stockUnit_name\": \"\", \"project_name\": \"\", \"natUnitPrice\": 0, \"natMoney\": 0, \"natCurrency_priceDigit\": 0, \"natCurrency_moneyDigit\": 0, \"unit_Precision\": 0, \"stockUnitId_Precision\": 0, \"storeProRecordsDefineCharacter\": 0, \"storeProRecordDefineCharacter\": 0, \"out_sys_id\": \"\", \"out_sys_code\": \"\", \"out_sys_version\": \"\", \"out_sys_type\": \"\", \"out_sys_rowno\": \"\", \"out_sys_lineid\": \"\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-12-23 14:41:43.000", "gmtUpdate": "2024-12-23 14:41:43.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2162396659404242949, "apiId": "edc735d69b654129b0da4c38f2d8877f", "content": "{ \"code\": \"999\", \"message\": \"No enum constant org.imeta.core.base.ConditionOperator.2\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-12-23 14:41:43.000", "gmtUpdate": "2024-12-23 14:41:43.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "apiDemoReturnDTOList": {"apiDemoReturnDTOList": [{"id": 2162396659404242948, "apiId": "edc735d69b654129b0da4c38f2d8877f", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"sumRecordList\": [ { \"totalQuantity\": \"\", \"qty\": \"\", \"totalPieces\": \"\", \"subQty\": \"\" } ], \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"pubts\": \"格式：yyyy-MM-dd HH:mm:ss\", \"recordList\": [ { \"factoryFiOrg\": \"\", \"storeProRecords_product\": \"\", \"currency\": \"\", \"storeProRecords_unit\": \"\", \"storeProRecords_productsku\": \"\", \"storeProRecords_stockUnitId\": \"\", \"vouchdate\": \"\", \"code\": \"\", \"department_name\": \"\", \"accountOrg\": \"\", \"org_name\": \"\", \"stockMgr_name\": \"\", \"department\": \"\", \"totalPieces\": \"\", \"org\": \"\", \"stockMgr\": \"\", \"store\": \"\", \"store_name\": \"\", \"warehouse\": \"\", \"warehouse_name\": \"\", \"bustype\": \"\", \"accountOrg_name\": \"\", \"bustype_name\": \"\", \"status\": \"\", \"operator\": \"\", \"totalQuantity\": 0, \"totalMaterial\": \"\", \"creator\": \"\", \"createTime\": \"\", \"modifier\": \"\", \"modifyTime\": \"\", \"auditor\": \"\", \"auditTime\": \"\", \"memo\": \"\", \"auditorId\": \"\", \"creatorId\": \"\", \"id\": \"\", \"modifierId\": \"\", \"pubts\": \"\", \"tplid\": \"\", \"storeProRecords_id\": \"\", \"product_cCode\": \"\", \"product_cName\": \"\", \"storeProRecordsCharacteristics\": 0, \"productsku_cCode\": \"\", \"productsku_cName\": \"\", \"product_modelDescription\": \"\", \"qty\": 0, \"product_unitName\": \"\", \"subQty\": 0, \"stockUnit_name\": \"\", \"project_name\": \"\", \"natUnitPrice\": 0, \"natMoney\": 0, \"natCurrency_priceDigit\": 0, \"natCurrency_moneyDigit\": 0, \"unit_Precision\": 0, \"stockUnitId_Precision\": 0, \"storeProRecordsDefineCharacter\": 0, \"storeProRecordDefineCharacter\": 0, \"out_sys_id\": \"\", \"out_sys_code\": \"\", \"out_sys_version\": \"\", \"out_sys_type\": \"\", \"out_sys_rowno\": \"\", \"out_sys_lineid\": \"\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-12-23 14:41:43.000", "gmtUpdate": "2024-12-23 14:41:43.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2162396659404242949, "apiId": "edc735d69b654129b0da4c38f2d8877f", "content": "{ \"code\": \"999\", \"message\": \"No enum constant org.imeta.core.base.ConditionOperator.2\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-12-23 14:41:43.000", "gmtUpdate": "2024-12-23 14:41:43.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "routingStgy": 0, "routingStgyList": "", "apiDemoReturnDTO": {"id": 2162396659404242948, "apiId": "edc735d69b654129b0da4c38f2d8877f", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"sumRecordList\": [ { \"totalQuantity\": \"\", \"qty\": \"\", \"totalPieces\": \"\", \"subQty\": \"\" } ], \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"pubts\": \"格式：yyyy-MM-dd HH:mm:ss\", \"recordList\": [ { \"factoryFiOrg\": \"\", \"storeProRecords_product\": \"\", \"currency\": \"\", \"storeProRecords_unit\": \"\", \"storeProRecords_productsku\": \"\", \"storeProRecords_stockUnitId\": \"\", \"vouchdate\": \"\", \"code\": \"\", \"department_name\": \"\", \"accountOrg\": \"\", \"org_name\": \"\", \"stockMgr_name\": \"\", \"department\": \"\", \"totalPieces\": \"\", \"org\": \"\", \"stockMgr\": \"\", \"store\": \"\", \"store_name\": \"\", \"warehouse\": \"\", \"warehouse_name\": \"\", \"bustype\": \"\", \"accountOrg_name\": \"\", \"bustype_name\": \"\", \"status\": \"\", \"operator\": \"\", \"totalQuantity\": 0, \"totalMaterial\": \"\", \"creator\": \"\", \"createTime\": \"\", \"modifier\": \"\", \"modifyTime\": \"\", \"auditor\": \"\", \"auditTime\": \"\", \"memo\": \"\", \"auditorId\": \"\", \"creatorId\": \"\", \"id\": \"\", \"modifierId\": \"\", \"pubts\": \"\", \"tplid\": \"\", \"storeProRecords_id\": \"\", \"product_cCode\": \"\", \"product_cName\": \"\", \"storeProRecordsCharacteristics\": 0, \"productsku_cCode\": \"\", \"productsku_cName\": \"\", \"product_modelDescription\": \"\", \"qty\": 0, \"product_unitName\": \"\", \"subQty\": 0, \"stockUnit_name\": \"\", \"project_name\": \"\", \"natUnitPrice\": 0, \"natMoney\": 0, \"natCurrency_priceDigit\": 0, \"natCurrency_moneyDigit\": 0, \"unit_Precision\": 0, \"stockUnitId_Precision\": 0, \"storeProRecordsDefineCharacter\": 0, \"storeProRecordDefineCharacter\": 0, \"out_sys_id\": \"\", \"out_sys_code\": \"\", \"out_sys_version\": \"\", \"out_sys_type\": \"\", \"out_sys_rowno\": \"\", \"out_sys_lineid\": \"\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-12-23 14:41:43.000", "gmtUpdate": "2024-12-23 14:41:43.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, "apiDemoReturnDTOError": {"id": 2162396659404242949, "apiId": "edc735d69b654129b0da4c38f2d8877f", "content": "{ \"code\": \"999\", \"message\": \"No enum constant org.imeta.core.base.ConditionOperator.2\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-12-23 14:41:43.000", "gmtUpdate": "2024-12-23 14:41:43.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}, "errorCodeDTOS": {"errorCodeDTOS": {"id": 2162396650814308476, "apiId": "edc735d69b654129b0da4c38f2d8877f", "errorCode": 999, "errorMessage": "入参错误等异常", "errorType": "API", "errorcodeDesc": "根据返回错误信息做出相应调整", "gmtCreate": "2024-12-23 14:41:42.000", "gmtUpdate": "2024-12-23 14:41:42.000", "apiName": "", "edit": false, "defErrorId": 1856797255115735244, "ytenantId": "", "displayCodeId": ""}}, "displayCodeApiConfigDTOS": "", "tokenPlugin": "", "paramParsePlugin": "", "authPlugin": {"id": "09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "", "name": "友户通token认证业务扩展插件", "configurable": false, "description": "YonsuitBusinessExtendPlugin", "pluginType": "auth", "pluginTypeName": "业务扩展插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": false, "gmtCreate": "2020-05-22 00:00:00", "gmtUpdate": "2020-05-22 00:00:00", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "edc735d69b654129b0da4c38f2d8877f", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "resultParsePlugin": {"id": "w181ed01-1e9b-4350-b994-71a66f062522", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "resultParse", "name": "UCG标准返回值解析插件", "configurable": false, "description": "符合UCG标准的返回值会自动解析，不符合的会自动略过", "pluginType": "resultParse", "pluginTypeName": "返回值解析插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.result.UCGResultParsePlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": true, "gmtCreate": "2019-08-19 00:00:00", "gmtUpdate": "", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "edc735d69b654129b0da4c38f2d8877f", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "mapReturnPluginConfig": "", "billNo": "st_storeprorecordlist", "domain": "ustock", "apiCategory": "", "docUrl": "", "pathMatch": 0, "createUser": "", "createUserName": "", "approvalStatus": 1, "publishTime": "2024-12-23 14:42:17", "pathJoin": true, "timeOut": 30, "tokenPluginName": "", "authPluginName": "", "resultPluginName": "", "apiDemoReturnRightDemo": "", "apiDemoReturnErrorDemo": "", "mock": false, "mockTimeout": "", "customUrl": "/storeprorecord/list", "fixedUrl": "/yonbip/scm", "apiCode": "edc735d69b654129b0da4c38f2d8877f", "tokenCheckType": 0, "enableMulti": false, "multiField": "", "idempotent": "non", "bidirectionalSSL": "", "ucgSchema": "HTTPS", "updateUserId": "36a8b72b-d965-404d-a02d-66ff4a7afeb3", "updateUserName": "昵称-王章宇", "paramIsForce": "", "userIDPassthrough": false, "applyUser": "", "applyMsg": "", "dr": 0, "microServiceCode": "domain.yonbip-scm-stock", "applicationCode": "yonbip-scm-stock", "privacyCategory": 1, "privacyLevel": 4, "apiDesigned": 0, "serviceType": 0, "integrateSchemeCode": "", "integrateSchemeName": "", "integrateObjectCode": "", "integrateObjectName": "", "integrateObjectCreatedType": "", "returnIntegObjId": "", "returnIntegObjName": "", "apiIntegrateDTOList": "", "apiRouteInfoDTOList": "", "arrayParam": false, "fileSize": "", "cc": true, "paramTransferMode": 2, "ytenantId": 0, "statusConf": "", "scene": 1, "version": "", "bizObjUri": "", "bizObjOperationType": "", "apiDefId": 1856797255115735044, "paramExtBizObjCode": "", "paramExtBizObjName": "", "paramExtRequest": 1, "paramExtResponse": 1, "paramExtInExtendKey": 1, "openScene": 1, "integrationScene": 1, "apiType": 3, "paramMark": {"request": {"request": [{"id": 2162396659404242950, "apiId": "edc735d69b654129b0da4c38f2d8877f", "paramCode": "pageIndex", "refCode": "pageIndex", "paramPosition": "request", "ytenantId": 0, "creator": "36a8b72b-d965-404d-a02d-66ff4a7afeb3", "createTime": "2024-12-23T06:41:43.000+00:00", "modifier": "00001951-7ca3-47ac-a462-d5a66e3e6724", "pubts": "2024-12-23T06:41:43.000+00:00", "modifyTime": "2024-12-23T06:41:43.000+00:00", "parentId": "", "paramOrder": "", "replicable": "", "order": 0}, {"id": 2162396659404242951, "apiId": "edc735d69b654129b0da4c38f2d8877f", "paramCode": "pageSize", "refCode": "pageSize", "paramPosition": "request", "ytenantId": 0, "creator": "36a8b72b-d965-404d-a02d-66ff4a7afeb3", "createTime": "2024-12-23T06:41:43.000+00:00", "modifier": "00001951-7ca3-47ac-a462-d5a66e3e6724", "pubts": "2024-12-23T06:41:43.000+00:00", "modifyTime": "2024-12-23T06:41:43.000+00:00", "parentId": "", "paramOrder": "", "replicable": "", "order": 0}, {"id": 2162396659404242952, "apiId": "edc735d69b654129b0da4c38f2d8877f", "paramCode": "lastUpdateTime", "refCode": "", "paramPosition": "request", "ytenantId": 0, "creator": "36a8b72b-d965-404d-a02d-66ff4a7afeb3", "createTime": "2024-12-23T06:41:43.000+00:00", "modifier": "00001951-7ca3-47ac-a462-d5a66e3e6724", "pubts": "2024-12-23T06:41:43.000+00:00", "modifyTime": "2024-12-23T06:41:43.000+00:00", "parentId": "", "paramOrder": "", "replicable": "", "order": 0}, {"id": 2162396659404242953, "apiId": "edc735d69b654129b0da4c38f2d8877f", "paramCode": "thisSyncTime", "refCode": "", "paramPosition": "request", "ytenantId": 0, "creator": "36a8b72b-d965-404d-a02d-66ff4a7afeb3", "createTime": "2024-12-23T06:41:43.000+00:00", "modifier": "00001951-7ca3-47ac-a462-d5a66e3e6724", "pubts": "2024-12-23T06:41:43.000+00:00", "modifyTime": "2024-12-23T06:41:43.000+00:00", "parentId": "", "paramOrder": "", "replicable": "", "order": 0}]}, "response": {"response": {"id": 2162396659404242954, "apiId": "edc735d69b654129b0da4c38f2d8877f", "paramCode": "respData", "refCode": "data.recordList", "paramPosition": "response", "ytenantId": 0, "creator": "36a8b72b-d965-404d-a02d-66ff4a7afeb3", "createTime": "2024-12-23T06:41:43.000+00:00", "modifier": "00001951-7ca3-47ac-a462-d5a66e3e6724", "pubts": "2024-12-23T06:41:43.000+00:00", "modifyTime": "2024-12-23T06:41:43.000+00:00", "parentId": "", "paramOrder": "", "replicable": "", "order": 0}}}, "integrateSysId": "", "integrateSysName": "", "integrateSysCode": "", "dataZoneSetting": true, "reqDataZoneSetting": false, "respDataZoneSetting": true, "reqDataAllQuery": false, "reqDataAllBody": false, "respDataAllBody": false, "chargeStatus": 1, "beforeSpeed": 40, "afterSpeed": 80, "speedStatus": false, "reqDataRefPath": "", "respDataRefPath": "data.recordList", "pubHistory": "", "deprecated": 0, "recommendedApiId": "", "recommendedApiName": "", "domainAppCode": "", "multiVersion": 0, "apiTag": ""}}, {"success": true, "code": 200, "message": "", "data": {"id": 2108770660671029249, "name": "用友YonBIP", "type": "integrateSys", "sort": 0, "enable": 0, "children": {"children": {"id": "SCC", "name": "供应链云", "type": 1, "sort": 0, "enable": 0, "children": {"children": {"id": "MM", "name": "采购供应", "type": 2, "sort": 0, "enable": 0, "children": {"children": {"id": "ST", "name": "库存管理", "type": 3, "sort": 0, "enable": 0, "children": {"children": {"id": "ustock.st_storeprorecord", "name": "产品入库", "type": 4, "sort": 0, "enable": 0, "children": "", "parentId": "", "productId": "", "code": "ustock.st_storeprorecord", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "ST", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "MM", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "SCC", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "current_yonbip_default_sys", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "isOrigin": 0, "hasChildren": 0, "order": 0}}, {"success": true, "code": 200, "message": "", "data": [{"id": "72113971-ae4c-4188-bc55-44b6173f4e0b", "name": "XS15", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "顾客订单号（订单表体）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:50:17", "gmtUpdate": "2025-07-26 17:50:17", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "b946709d-f4d9-4a43-a551-f55beee7f3d5", "name": "XXX0111", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求分类项", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:50:17", "gmtUpdate": "2025-07-26 17:50:17", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:50:17", "gmtUpdate": "2025-07-26 17:50:17", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": [{"id": "913a0595-0a10-4344-a3ce-dbd456d2d199", "name": "XS11", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "需求分类号test", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:50:29", "gmtUpdate": "2025-07-26 17:50:29", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 255, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:50:29", "gmtUpdate": "2025-07-26 17:50:29", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, {"success": true, "code": 200, "message": "", "data": {"id": "5a93627b-27f7-40cb-a8fc-d6599545710b", "name": "id", "apiId": "", "parentId": "", "defParamId": "", "array": false, "paramDesc": "特征id,主键,新增时无需填写,修改时必填", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": "", "bizType": "text", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2025-07-26 17:50:39", "gmtUpdate": "2025-07-26 17:50:39", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 36, "childId": "", "edit": true, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}}]