<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YS-API 服务端口状态面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .service-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid #4CAF50;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .service-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .service-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }

        .service-status {
            font-size: 1.5em;
            margin-left: auto;
        }

        .service-port {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .service-description {
            color: #666;
            margin-bottom: 15px;
            font-size: 0.95em;
        }

        .service-url {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            border: 1px solid #e9ecef;
        }

        .service-url a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            word-break: break-all;
        }

        .service-url a:hover {
            text-decoration: underline;
        }

        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .quick-actions h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.4em;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            cursor: pointer;
            transition: transform 0.2s ease;
            text-decoration: none;
            text-align: center;
            display: block;
        }

        .action-btn:hover {
            transform: scale(1.05);
        }

        .hosts-config {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .hosts-config h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4em;
        }

        .hosts-content {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.6;
            overflow-x: auto;
        }

        .copy-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
            font-size: 0.9em;
        }

        .copy-btn:hover {
            background: #45a049;
        }

        .info-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .info-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .info-card h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .info-card p, .info-card li {
            color: #666;
            line-height: 1.6;
        }

        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 1.5em;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: transform 0.2s ease;
        }

        .refresh-btn:hover {
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 YS-API 服务端口状态</h1>
            <p>专属端口段 8010-8019 | 实时监控面板</p>
        </div>

        <div class="status-grid">
            <div class="service-card">
                <div class="service-header">
                    <div class="service-name">🌐 前端界面</div>
                    <div class="service-status">🟢</div>
                </div>
                <div class="service-port">8010</div>
                <div class="service-description">YS-API前端界面</div>
                <div class="service-url">
                    <a href="http://ysapi.local:8010" target="_blank">http://ysapi.local:8010</a>
                </div>
            </div>

            <div class="service-card">
                <div class="service-header">
                    <div class="service-name">🔌 后端API</div>
                    <div class="service-status">🟢</div>
                </div>
                <div class="service-port">8011</div>
                <div class="service-description">YS-API后端服务</div>
                <div class="service-url">
                    <a href="http://api.ysapi.local:8011" target="_blank">http://api.ysapi.local:8011</a>
                </div>
            </div>

            <div class="service-card">
                <div class="service-header">
                    <div class="service-name">🗄️ 数据库管理</div>
                    <div class="service-status">🟢</div>
                </div>
                <div class="service-port">8012</div>
                <div class="service-description">SQLite Web管理界面</div>
                <div class="service-url">
                    <a href="http://db.ysapi.local:8012" target="_blank">http://db.ysapi.local:8012</a>
                </div>
            </div>

            <div class="service-card">
                <div class="service-header">
                    <div class="service-name">📊 系统监控</div>
                    <div class="service-status">🟢</div>
                </div>
                <div class="service-port">8013</div>
                <div class="service-description">系统监控面板</div>
                <div class="service-url">
                    <a href="http://monitor.ysapi.local:8013" target="_blank">http://monitor.ysapi.local:8013</a>
                </div>
            </div>

            <div class="service-card">
                <div class="service-header">
                    <div class="service-name">📚 API文档</div>
                    <div class="service-status">🟢</div>
                </div>
                <div class="service-port">8014</div>
                <div class="service-description">API文档服务</div>
                <div class="service-url">
                    <a href="http://docs.ysapi.local:8014" target="_blank">http://docs.ysapi.local:8014</a>
                </div>
            </div>

            <div class="service-card">
                <div class="service-header">
                    <div class="service-name">🧪 测试环境</div>
                    <div class="service-status">🟢</div>
                </div>
                <div class="service-port">8015</div>
                <div class="service-description">测试环境服务</div>
                <div class="service-url">
                    <a href="http://test.ysapi.local:8015" target="_blank">http://test.ysapi.local:8015</a>
                </div>
            </div>

            <div class="service-card">
                <div class="service-header">
                    <div class="service-name">⚙️ 管理后台</div>
                    <div class="service-status">🟢</div>
                </div>
                <div class="service-port">8016</div>
                <div class="service-description">管理后台</div>
                <div class="service-url">
                    <a href="http://admin.ysapi.local:8016" target="_blank">http://admin.ysapi.local:8016</a>
                </div>
            </div>

            <div class="service-card">
                <div class="service-header">
                    <div class="service-name">📡 WebSocket</div>
                    <div class="service-status">🟢</div>
                </div>
                <div class="service-port">8017</div>
                <div class="service-description">WebSocket实时通信</div>
                <div class="service-url">
                    <a href="ws://ws.ysapi.local:8017" target="_blank">ws://ws.ysapi.local:8017</a>
                </div>
            </div>
        </div>

        <div class="quick-actions">
            <h3>🚀 快速操作</h3>
            <div class="action-buttons">
                <a href="http://ysapi.local:8010" class="action-btn" target="_blank">打开前端</a>
                <a href="http://api.ysapi.local:8011" class="action-btn" target="_blank">访问API</a>
                <a href="http://docs.ysapi.local:8014" class="action-btn" target="_blank">查看文档</a>
                <a href="http://monitor.ysapi.local:8013" class="action-btn" target="_blank">监控面板</a>
            </div>
        </div>

        <div class="hosts-config">
            <h3>🌐 Hosts配置</h3>
            <p>将以下内容添加到系统hosts文件以启用友好域名：</p>
            <div class="hosts-content" id="hostsContent">127.0.0.1 ysapi.local
127.0.0.1 api.ysapi.local
127.0.0.1 db.ysapi.local
127.0.0.1 monitor.ysapi.local
127.0.0.1 docs.ysapi.local
127.0.0.1 test.ysapi.local
127.0.0.1 admin.ysapi.local
127.0.0.1 ws.ysapi.local</div>
            <button class="copy-btn" onclick="copyHosts()">📋 复制配置</button>
        </div>

        <div class="info-section">
            <div class="info-card">
                <h4>📁 配置文件</h4>
                <p><strong>端口配置:</strong> .ports.json<br>
                <strong>管理工具:</strong> port_locker.py<br>
                <strong>启动脚本:</strong> start_*_locked.bat</p>
            </div>
            
            <div class="info-card">
                <h4>🔧 管理命令</h4>
                <p><strong>查看状态:</strong> python port_locker.py status<br>
                <strong>启动服务:</strong> python port_locker.py start [service]<br>
                <strong>锁定端口:</strong> python port_locker.py lock [service]</p>
            </div>

            <div class="info-card">
                <h4>🛡️ 安全特性</h4>
                <ul>
                    <li>端口锁定机制</li>
                    <li>冲突自动处理</li>
                    <li>严格模式保护</li>
                    <li>专属端口段</li>
                </ul>
            </div>
        </div>
    </div>

    <button class="refresh-btn" onclick="window.location.reload()">🔄</button>

    <script>
        function copyHosts() {
            const hostsContent = document.getElementById('hostsContent');
            const text = hostsContent.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '✅ 已复制';
                button.style.background = '#4CAF50';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#4CAF50';
                }, 2000);
            }).catch(() => {
                alert('复制失败，请手动复制文本');
            });
        }

        // 自动刷新状态（可选）
        setInterval(() => {
            // 这里可以添加AJAX请求来更新实时状态
            console.log('检查服务状态...');
        }, 30000); // 每30秒检查一次
    </script>
</body>
</html>
