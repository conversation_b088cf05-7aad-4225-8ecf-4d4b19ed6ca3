{"module_name": "purchase_receipt", "display_name": "采购入库", "version": "2.0.0", "source": "json_parser", "total_fields": 183, "created_at": "2025-07-28T20:12:24.855972", "last_updated": "2025-07-28T20:12:24.855972", "fields": {"code": {"api_field_name": "code", "chinese_name": "单据编号", "data_type": "NVARCHAR(500)", "param_desc": "单据编号", "path": "data.recordList.code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "message": {"api_field_name": "message", "chinese_name": "调用失败时的错误信息", "data_type": "NVARCHAR(500)", "param_desc": "调用失败时的错误信息", "path": "message", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "调用成功时的返回数据", "data_type": "NVARCHAR(MAX)", "param_desc": "调用成功时的返回数据", "path": "data", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageIndex": {"api_field_name": "pageIndex", "chinese_name": "页号", "data_type": "BIGINT", "param_desc": "页号", "path": "pageIndex", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageSize": {"api_field_name": "pageSize", "chinese_name": "每页行数", "data_type": "BIGINT", "param_desc": "每页行数", "path": "pageSize", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageCount": {"api_field_name": "pageCount", "chinese_name": "页面数", "data_type": "BIGINT", "param_desc": "页面数", "path": "data.pageCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "beginPageIndex": {"api_field_name": "beginPageIndex", "chinese_name": "开始页码（第一页）", "data_type": "BIGINT", "param_desc": "开始页码（第一页）", "path": "data.beginPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "endPageIndex": {"api_field_name": "endPageIndex", "chinese_name": "结束页码（有多少页）", "data_type": "BIGINT", "param_desc": "结束页码（有多少页）", "path": "data.endPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordCount": {"api_field_name": "recordCount", "chinese_name": "总数", "data_type": "BIGINT", "param_desc": "总数", "path": "data.recordCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pubts": {"api_field_name": "pubts", "chinese_name": "时间戳", "data_type": "NVARCHAR(500)", "param_desc": "时间戳", "path": "data.recordList.pubts", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "recordList": {"api_field_name": "recordList", "chinese_name": "返回结果对象", "data_type": "NVARCHAR(MAX)", "param_desc": "返回结果对象", "path": "data.recordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vouchdate": {"api_field_name": "vouchdate", "chinese_name": "单据日期", "data_type": "NVARCHAR(500)", "param_desc": "单据日期", "path": "data.recordList.vouchdate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bustype_name": {"api_field_name": "bustype_name", "chinese_name": "交易类型，需传入交易类型id", "data_type": "NVARCHAR(500)", "param_desc": "交易类型，需传入交易类型id", "path": "bustype_name", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "vendor_name": {"api_field_name": "vendor_name", "chinese_name": "供应商，需传入供应商名称", "data_type": "NVARCHAR(500)", "param_desc": "供应商，需传入供应商名称", "path": "vendor_name", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehouse_name": {"api_field_name": "warehouse_name", "chinese_name": "仓库，需传入仓库名称", "data_type": "NVARCHAR(500)", "param_desc": "仓库，需传入仓库名称", "path": "warehouse_name", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vendor_code": {"api_field_name": "vendor_code", "chinese_name": "供应商编码", "data_type": "NVARCHAR(500)", "param_desc": "供应商编码", "path": "data.recordList.vendor_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "warehouse_code": {"api_field_name": "warehouse_code", "chinese_name": "仓库编码", "data_type": "NVARCHAR(500)", "param_desc": "仓库编码", "path": "data.recordList.warehouse_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockMgr_name": {"api_field_name": "stockMgr_name", "chinese_name": "库管员，需传入库管员id", "data_type": "NVARCHAR(500)", "param_desc": "库管员，需传入库管员id", "path": "stockMgr_name", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "status": {"api_field_name": "status", "chinese_name": "单据状态, 0:未提交、1:已提交、", "data_type": "BIGINT", "param_desc": "单据状态, 0:未提交、1:已提交、", "path": "data.recordList.status", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchaseOrg_name": {"api_field_name": "purchaseOrg_name", "chinese_name": "采购组织，需传入采购组织id", "data_type": "NVARCHAR(500)", "param_desc": "采购组织，需传入采购组织id", "path": "purchaseOrg_name", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "department_name": {"api_field_name": "department_name", "chinese_name": "部门，需传部门id", "data_type": "NVARCHAR(500)", "param_desc": "部门，需传部门id", "path": "department_name", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "org_code": {"api_field_name": "org_code", "chinese_name": "库存组织编码", "data_type": "NVARCHAR(500)", "param_desc": "库存组织编码", "path": "org_code", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "org_name": {"api_field_name": "org_name", "chinese_name": "库存组织名称", "data_type": "NVARCHAR(500)", "param_desc": "库存组织名称", "path": "org_name", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "department_code": {"api_field_name": "department_code", "chinese_name": "部门编码", "data_type": "NVARCHAR(500)", "param_desc": "部门编码", "path": "data.recordList.department_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operator_name": {"api_field_name": "operator_name", "chinese_name": "业务员，需传入业务员id", "data_type": "NVARCHAR(500)", "param_desc": "业务员，需传入业务员id", "path": "operator_name", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalQuantity": {"api_field_name": "totalQuantity", "chinese_name": "整单数量", "data_type": "DECIMAL(18,4)", "param_desc": "整单数量", "path": "sumRecordList.totalQuantity", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalPieces": {"api_field_name": "totalPieces", "chinese_name": "整单件数", "data_type": "DECIMAL(18,4)", "param_desc": "整单件数", "path": "sumRecordList.totalPieces", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "inInvoiceOrg_name": {"api_field_name": "inInvoiceOrg_name", "chinese_name": "收票组织，需传入收票组织id", "data_type": "NVARCHAR(500)", "param_desc": "收票组织，需传入收票组织id", "path": "inInvoiceOrg_name", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "inInvoiceOrg": {"api_field_name": "inInvoiceOrg", "chinese_name": "收票组织id", "data_type": "NVARCHAR(500)", "param_desc": "收票组织id", "path": "data.recordList.inInvoiceOrg", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "accountOrg": {"api_field_name": "accountOrg", "chinese_name": "会计主体", "data_type": "NVARCHAR(500)", "param_desc": "会计主体", "path": "data.recordList.accountOrg", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isBeginning": {"api_field_name": "isBeginning", "chinese_name": "是否期初, true:是、false:否、", "data_type": "BIT", "param_desc": "是否期初, true:是、false:否、", "path": "data.recordList.isBeginning", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bustype": {"api_field_name": "bustype", "chinese_name": "业务类型id", "data_type": "NVARCHAR(500)", "param_desc": "业务类型id", "path": "data.recordList.bustype", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "vendor": {"api_field_name": "vendor", "chinese_name": "供应商id", "data_type": "BIGINT", "param_desc": "供应商id", "path": "data.recordList.vendor", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "contact": {"api_field_name": "contact", "chinese_name": "联系人", "data_type": "NVARCHAR(500)", "param_desc": "联系人", "path": "data.recordList.contact", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehouse": {"api_field_name": "warehouse", "chinese_name": "仓库id", "data_type": "BIGINT", "param_desc": "仓库id", "path": "data.recordList.warehouse", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "operator": {"api_field_name": "operator", "chinese_name": "经办人id", "data_type": "BIGINT", "param_desc": "经办人id", "path": "data.recordList.operator", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchaseOrg": {"api_field_name": "purchaseOrg", "chinese_name": "采购组织IDid", "data_type": "NVARCHAR(500)", "param_desc": "采购组织IDid", "path": "data.recordList.purchaseOrg", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "org": {"api_field_name": "org", "chinese_name": "库存组织id", "data_type": "NVARCHAR(500)", "param_desc": "库存组织id", "path": "data.recordList.org", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "department": {"api_field_name": "department", "chinese_name": "部门id", "data_type": "NVARCHAR(500)", "param_desc": "部门id", "path": "data.recordList.department", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockMgr": {"api_field_name": "stockMgr", "chinese_name": "库管员IDid", "data_type": "NVARCHAR(500)", "param_desc": "库管员IDid", "path": "data.recordList.stockMgr", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "moneysum": {"api_field_name": "moneysum", "chinese_name": "金额", "data_type": "DECIMAL(18,4)", "param_desc": "金额", "path": "sumRecordList.moneysum", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "paymentsum": {"api_field_name": "paymentsum", "chinese_name": "付款金额", "data_type": "DECIMAL(18,4)", "param_desc": "付款金额", "path": "sumRecordList.paymentsum", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unpaymentsum": {"api_field_name": "unpaymentsum", "chinese_name": "未付款金额", "data_type": "DECIMAL(18,4)", "param_desc": "未付款金额", "path": "sumRecordList.unpaymentsum", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "store": {"api_field_name": "store", "chinese_name": "门店id", "data_type": "NVARCHAR(500)", "param_desc": "门店id", "path": "data.recordList.store", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "store_name": {"api_field_name": "store_name", "chinese_name": "门店名称", "data_type": "NVARCHAR(500)", "param_desc": "门店名称", "path": "data.recordList.store_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "custom": {"api_field_name": "custom", "chinese_name": "客户id", "data_type": "BIGINT", "param_desc": "客户id", "path": "data.recordList.custom", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "payor": {"api_field_name": "payor", "chinese_name": "付款人id", "data_type": "NVARCHAR(500)", "param_desc": "付款人id", "path": "data.recordList.payor", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "payor_name": {"api_field_name": "payor_name", "chinese_name": "付款人名称", "data_type": "NVARCHAR(500)", "param_desc": "付款人名称", "path": "data.recordList.payor_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "paytime": {"api_field_name": "paytime", "chinese_name": "付款时间", "data_type": "NVARCHAR(500)", "param_desc": "付款时间", "path": "data.recordList.paytime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "paymentstatus": {"api_field_name": "paymentstatus", "chinese_name": "付款状态, 0:未完成、1:完成、", "data_type": "NVARCHAR(500)", "param_desc": "付款状态, 0:未完成、1:完成、", "path": "data.recordList.paymentstatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "creator": {"api_field_name": "creator", "chinese_name": "创建人", "data_type": "NVARCHAR(500)", "param_desc": "创建人", "path": "data.recordList.creator", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "createTime": {"api_field_name": "createTime", "chinese_name": "创建时间", "data_type": "NVARCHAR(500)", "param_desc": "创建时间", "path": "data.recordList.createTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "modifier": {"api_field_name": "modifier", "chinese_name": "最后修改人", "data_type": "NVARCHAR(500)", "param_desc": "最后修改人", "path": "data.recordList.modifier", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "modifyTime": {"api_field_name": "modifyTime", "chinese_name": "最后修改时间", "data_type": "NVARCHAR(500)", "param_desc": "最后修改时间", "path": "data.recordList.modifyTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auditor": {"api_field_name": "auditor", "chinese_name": "提交人", "data_type": "NVARCHAR(500)", "param_desc": "提交人", "path": "data.recordList.auditor", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "auditTime": {"api_field_name": "auditTime", "chinese_name": "提交时间", "data_type": "NVARCHAR(500)", "param_desc": "提交时间", "path": "data.recordList.auditTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "memo": {"api_field_name": "memo", "chinese_name": "备注", "data_type": "NVARCHAR(500)", "param_desc": "备注", "path": "data.recordList.memo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "id": {"api_field_name": "id", "chinese_name": "主表ID", "data_type": "NVARCHAR(500)", "param_desc": "主表ID", "path": "data.recordList.id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "srcBill": {"api_field_name": "srcBill", "chinese_name": "来源单据id", "data_type": "NVARCHAR(500)", "param_desc": "来源单据id", "path": "data.recordList.srcBill", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "tplid": {"api_field_name": "tplid", "chinese_name": "模板id", "data_type": "BIGINT", "param_desc": "模板id", "path": "data.recordList.tplid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "exchangestatus": {"api_field_name": "<PERSON><PERSON>us", "chinese_name": "交换状态", "data_type": "NVARCHAR(500)", "param_desc": "交换状态", "path": "data.recordList.exchangestatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purInRecords_id": {"api_field_name": "purInRecords_id", "chinese_name": "订单行id", "data_type": "NVARCHAR(500)", "param_desc": "订单行id", "path": "data.recordList.purInRecords_id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product": {"api_field_name": "product", "chinese_name": "物料id", "data_type": "NVARCHAR(500)", "param_desc": "物料id", "path": "data.recordList.product", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_cCode": {"api_field_name": "product_cCode", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "data.recordList.product_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_cName": {"api_field_name": "product_cName", "chinese_name": "物料，需传入物料id", "data_type": "BIGINT", "param_desc": "物料，需传入物料id", "path": "product_cName", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productsku": {"api_field_name": "productsku", "chinese_name": "物料SKUid", "data_type": "NVARCHAR(500)", "param_desc": "物料SKUid", "path": "data.recordList.productsku", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "tradeRoute_name": {"api_field_name": "tradeRoute_name", "chinese_name": "贸易路径", "data_type": "NVARCHAR(500)", "param_desc": "贸易路径", "path": "data.recordList.tradeRoute_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productsku_cCode": {"api_field_name": "productsku_cCode", "chinese_name": "物料sku编码", "data_type": "NVARCHAR(500)", "param_desc": "物料sku编码", "path": "data.recordList.productsku_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productsku_cName": {"api_field_name": "productsku_cName", "chinese_name": "物料sku名称", "data_type": "NVARCHAR(500)", "param_desc": "物料sku名称", "path": "data.recordList.productsku_cName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productClass_code": {"api_field_name": "productClass_code", "chinese_name": "物料分类编码", "data_type": "NVARCHAR(500)", "param_desc": "物料分类编码", "path": "data.recordList.productClass_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "propertiesValue": {"api_field_name": "propertiesValue", "chinese_name": "规格", "data_type": "NVARCHAR(500)", "param_desc": "规格", "path": "data.recordList.propertiesValue", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno": {"api_field_name": "batchno", "chinese_name": "批次号", "data_type": "NVARCHAR(500)", "param_desc": "批次号", "path": "data.recordList.batchno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invaliddate": {"api_field_name": "invaliddate", "chinese_name": "有效期至", "data_type": "NVARCHAR(500)", "param_desc": "有效期至", "path": "data.recordList.invaliddate", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "producedate": {"api_field_name": "producedate", "chinese_name": "生产日期", "data_type": "NVARCHAR(500)", "param_desc": "生产日期", "path": "data.recordList.producedate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unit": {"api_field_name": "unit", "chinese_name": "单位id", "data_type": "NVARCHAR(500)", "param_desc": "单位id", "path": "data.recordList.unit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "qty": {"api_field_name": "qty", "chinese_name": "数量", "data_type": "BIGINT", "param_desc": "数量", "path": "sumRecordList.qty", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unit_code": {"api_field_name": "unit_code", "chinese_name": "计量单位编码", "data_type": "NVARCHAR(500)", "param_desc": "计量单位编码", "path": "data.recordList.unit_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unit_name": {"api_field_name": "unit_name", "chinese_name": "计量单位名称", "data_type": "NVARCHAR(500)", "param_desc": "计量单位名称", "path": "data.recordList.unit_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "subQty": {"api_field_name": "subQty", "chinese_name": "件数", "data_type": "DECIMAL(18,4)", "param_desc": "件数", "path": "sumRecordList.subQty", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockUnit_name": {"api_field_name": "stockUnit_name", "chinese_name": "库存单位", "data_type": "NVARCHAR(500)", "param_desc": "库存单位", "path": "data.recordList.stockUnit_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "project_code": {"api_field_name": "project_code", "chinese_name": "项目编码", "data_type": "NVARCHAR(500)", "param_desc": "项目编码", "path": "data.recordList.project_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "project_name": {"api_field_name": "project_name", "chinese_name": "项目，需传入项目id", "data_type": "NVARCHAR(500)", "param_desc": "项目，需传入项目id", "path": "project_name", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "oriUnitPrice": {"api_field_name": "oriUnitPrice", "chinese_name": "无税单价", "data_type": "DECIMAL(18,4)", "param_desc": "无税单价", "path": "data.recordList.oriUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriTaxUnitPrice": {"api_field_name": "oriTaxUnitPrice", "chinese_name": "含税单价", "data_type": "DECIMAL(18,4)", "param_desc": "含税单价", "path": "data.recordList.oriTaxUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriMoney": {"api_field_name": "oriMoney", "chinese_name": "无税金额", "data_type": "DECIMAL(18,4)", "param_desc": "无税金额", "path": "sumRecordList.oriMoney", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "oriSum": {"api_field_name": "oriSum", "chinese_name": "含税金额", "data_type": "DECIMAL(18,4)", "param_desc": "含税金额", "path": "sumRecordList.oriSum", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "oriTax": {"api_field_name": "oriTax", "chinese_name": "税额", "data_type": "DECIMAL(18,4)", "param_desc": "税额", "path": "sumRecordList.oriTax", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "taxRate": {"api_field_name": "taxRate", "chinese_name": "税率", "data_type": "DECIMAL(18,4)", "param_desc": "税率", "path": "data.recordList.taxRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "billqty": {"api_field_name": "billqty", "chinese_name": "累计开票数量", "data_type": "DECIMAL(18,4)", "param_desc": "累计开票数量", "path": "sumRecordList.billqty", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "billSubQty": {"api_field_name": "billSubQty", "chinese_name": "累计开票件数", "data_type": "DECIMAL(18,4)", "param_desc": "累计开票件数", "path": "data.recordList.billSubQty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "sqty": {"api_field_name": "sqty", "chinese_name": "累计结算数量", "data_type": "DECIMAL(18,4)", "param_desc": "累计结算数量", "path": "sumRecordList.sqty", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "smoney": {"api_field_name": "smoney", "chinese_name": "累计结算金额", "data_type": "DECIMAL(18,4)", "param_desc": "累计结算金额", "path": "sumRecordList.smoney", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "sfee": {"api_field_name": "sfee", "chinese_name": "累计结算费用", "data_type": "DECIMAL(18,4)", "param_desc": "累计结算费用", "path": "sumRecordList.sfee", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalBillOriSum": {"api_field_name": "totalBillOriSum", "chinese_name": "累计开票含税金额", "data_type": "DECIMAL(18,4)", "param_desc": "累计开票含税金额", "path": "data.recordList.totalBillOriSum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "priceUOM": {"api_field_name": "priceUOM", "chinese_name": "计价单位id", "data_type": "BIGINT", "param_desc": "计价单位id", "path": "data.recordList.priceUOM", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "priceUOM_Code": {"api_field_name": "priceUOM_Code", "chinese_name": "计价单位编码", "data_type": "NVARCHAR(500)", "param_desc": "计价单位编码", "path": "data.recordList.priceUOM_Code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "priceUOM_Name": {"api_field_name": "priceUOM_Name", "chinese_name": "计价单位名称", "data_type": "NVARCHAR(500)", "param_desc": "计价单位名称", "path": "data.recordList.priceUOM_Name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_priceDigit": {"api_field_name": "natCurrency_priceDigit", "chinese_name": "本币单价精度", "data_type": "BIGINT", "param_desc": "本币单价精度", "path": "sumRecordList.natCurrency_priceDigit", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_moneyDigit": {"api_field_name": "natCurrency_moneyDigit", "chinese_name": "本币金额精度", "data_type": "BIGINT", "param_desc": "本币金额精度", "path": "sumRecordList.natCurrency_moneyDigit", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currency_priceDigit": {"api_field_name": "currency_priceDigit", "chinese_name": "币种单价精度", "data_type": "BIGINT", "param_desc": "币种单价精度", "path": "sumRecordList.currency_priceDigit", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "currency_moneyDigit": {"api_field_name": "currency_moneyDigit", "chinese_name": "币种金额精度", "data_type": "BIGINT", "param_desc": "币种金额精度", "path": "sumRecordList.currency_moneyDigit", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unit_Precision": {"api_field_name": "unit_Precision", "chinese_name": "主计量精度", "data_type": "BIGINT", "param_desc": "主计量精度", "path": "sumRecordList.unit_Precision", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "priceUOM_Precision": {"api_field_name": "priceUOM_Precision", "chinese_name": "计价单位精度", "data_type": "BIGINT", "param_desc": "计价单位精度", "path": "sumRecordList.priceUOM_Precision", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockUnitId_Precision": {"api_field_name": "stockUnitId_Precision", "chinese_name": "库存单位精度", "data_type": "BIGINT", "param_desc": "库存单位精度", "path": "sumRecordList.stockUnitId_Precision", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isGiftProduct": {"api_field_name": "isGiftProduct", "chinese_name": "赠品, true:是、false:否、", "data_type": "BIT", "param_desc": "赠品, true:是、false:否、", "path": "data.recordList.isGiftProduct", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bmake_st_purinvoice_red": {"api_field_name": "bmake_st_purinvoice_red", "chinese_name": "流程红票", "data_type": "NVARCHAR(500)", "param_desc": "流程红票", "path": "data.recordList.bmake_st_purinvoice_red", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bmake_st_purinvoice": {"api_field_name": "bmake_st_purinvoice", "chinese_name": "流程蓝票", "data_type": "NVARCHAR(500)", "param_desc": "流程蓝票", "path": "data.recordList.bmake_st_purinvoice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bizFlow": {"api_field_name": "bizFlow", "chinese_name": "流程ID", "data_type": "NVARCHAR(500)", "param_desc": "流程ID", "path": "data.recordList.bizFlow", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bizFlow_version": {"api_field_name": "bizFlow_version", "chinese_name": "版本信息", "data_type": "NVARCHAR(500)", "param_desc": "版本信息", "path": "data.recordList.bizFlow_version", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isFlowCoreBill": {"api_field_name": "isFlowCoreBill", "chinese_name": "是否流程核心单据", "data_type": "NVARCHAR(500)", "param_desc": "是否流程核心单据", "path": "data.recordList.isFlowCoreBill", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_id": {"api_field_name": "out_sys_id", "chinese_name": "外部来源线索", "data_type": "NVARCHAR(500)", "param_desc": "外部来源线索", "path": "data.recordList.out_sys_id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "out_sys_code": {"api_field_name": "out_sys_code", "chinese_name": "外部来源编码", "data_type": "NVARCHAR(500)", "param_desc": "外部来源编码", "path": "data.recordList.out_sys_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "out_sys_version": {"api_field_name": "out_sys_version", "chinese_name": "外部来源版本", "data_type": "NVARCHAR(500)", "param_desc": "外部来源版本", "path": "data.recordList.out_sys_version", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_type": {"api_field_name": "out_sys_type", "chinese_name": "外部来源类型", "data_type": "NVARCHAR(500)", "param_desc": "外部来源类型", "path": "data.recordList.out_sys_type", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_rowno": {"api_field_name": "out_sys_rowno", "chinese_name": "外部来源行号", "data_type": "NVARCHAR(500)", "param_desc": "外部来源行号", "path": "data.recordList.out_sys_rowno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_lineid": {"api_field_name": "out_sys_lineid", "chinese_name": "外部来源行", "data_type": "NVARCHAR(500)", "param_desc": "外部来源行", "path": "data.recordList.out_sys_lineid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "tradeRouteID": {"api_field_name": "tradeRouteID", "chinese_name": "贸易路径id", "data_type": "BIGINT", "param_desc": "贸易路径id", "path": "data.recordList.tradeRouteID", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isEndTrade": {"api_field_name": "isEndTrade", "chinese_name": "是否末级(0:否,1:是)", "data_type": "NVARCHAR(500)", "param_desc": "是否末级(0:否,1:是)", "path": "data.recordList.isEndTrade", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "tradeRouteLineno": {"api_field_name": "tradeRouteLineno", "chinese_name": "站点", "data_type": "NVARCHAR(500)", "param_desc": "站点", "path": "data.recordList.tradeRouteLineno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "collaborationPolineno": {"api_field_name": "collaborationPolineno", "chinese_name": "协同来源单据行号", "data_type": "NVARCHAR(500)", "param_desc": "协同来源单据行号", "path": "data.recordList.collaborationPolineno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "coSourceType": {"api_field_name": "coSourceType", "chinese_name": "协同源头单据类型(productionorder.po_subcontract_order:委外订单)", "data_type": "NVARCHAR(500)", "param_desc": "协同源头单据类型(productionorder.po_subcontract_order:委外订单)", "path": "data.recordList.coSourceType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "coUpcode": {"api_field_name": "coUpcode", "chinese_name": "协同源头单据号", "data_type": "NVARCHAR(500)", "param_desc": "协同源头单据号", "path": "data.recordList.coUpcode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "coSourceLineNo": {"api_field_name": "coSourceLineNo", "chinese_name": "协同源头单据行号", "data_type": "NVARCHAR(500)", "param_desc": "协同源头单据行号", "path": "data.recordList.coSourceLineNo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "coSourceid": {"api_field_name": "coSourceid", "chinese_name": "协同来源单据id", "data_type": "NVARCHAR(500)", "param_desc": "协同来源单据id", "path": "data.recordList.coSourceid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "coSourceautoid": {"api_field_name": "coSourceautoid", "chinese_name": "协同来源单据子表id", "data_type": "BIGINT", "param_desc": "协同来源单据子表id", "path": "data.recordList.coSourceautoid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "collaborationPodetailid": {"api_field_name": "collaborationPodetailid", "chinese_name": "协同来源单据行", "data_type": "BIGINT", "param_desc": "协同来源单据行", "path": "data.recordList.collaborationPodetailid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "collaborationPocode": {"api_field_name": "collaborationPocode", "chinese_name": "协同来源单据号", "data_type": "NVARCHAR(500)", "param_desc": "协同来源单据号", "path": "data.recordList.collaborationPocode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "collaborationPoid": {"api_field_name": "collaborationPoid", "chinese_name": "协同来源单据主表id", "data_type": "BIGINT", "param_desc": "协同来源单据主表id", "path": "data.recordList.collaborationPoid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "collaborationSource": {"api_field_name": "collaborationSource", "chinese_name": "协同来源单据类型(st_salesout:销售出库)", "data_type": "NVARCHAR(500)", "param_desc": "协同来源单据类型(st_salesout:销售出库)", "path": "data.recordList.collaborationSource", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalOutStockQuantity": {"api_field_name": "totalOutStockQuantity", "chinese_name": "累计销售出库数量", "data_type": "NVARCHAR(500)", "param_desc": "累计销售出库数量", "path": "data.recordList.totalOutStockQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "sumRecordList": {"api_field_name": "sumRecordList", "chinese_name": "合计对象", "data_type": "NVARCHAR(MAX)", "param_desc": "合计对象", "path": "sumRecordList", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "currency": {"api_field_name": "currency", "chinese_name": "币种id", "data_type": "NVARCHAR(500)", "param_desc": "币种id", "path": "sumRecordList.currency", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purInRecords_unit": {"api_field_name": "purInRecords_unit", "chinese_name": "计量单位", "data_type": "BIGINT", "param_desc": "计量单位", "path": "sumRecordList.purInRecords_unit", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natCurrency": {"api_field_name": "natCurrency", "chinese_name": "本币币种id", "data_type": "NVARCHAR(500)", "param_desc": "本币币种id", "path": "sumRecordList.natCurrency", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purInRecords_stockUnitId": {"api_field_name": "purInRecords_stockUnitId", "chinese_name": "库存单位", "data_type": "NVARCHAR(500)", "param_desc": "库存单位", "path": "sumRecordList.purInRecords_stockUnitId", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purInRecords_priceUOM": {"api_field_name": "purInRecords_priceUOM", "chinese_name": "计价单位", "data_type": "BIGINT", "param_desc": "计价单位", "path": "sumRecordList.purInRecords_priceUOM", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "org_id": {"api_field_name": "org_id", "chinese_name": "库存组织id", "data_type": "NVARCHAR(500)", "param_desc": "库存组织id", "path": "org_id", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product.productClass.name": {"api_field_name": "product.productClass.name", "chinese_name": "物料分类，需传入物料分类id", "data_type": "BIGINT", "param_desc": "物料分类，需传入物料分类id", "path": "product.productClass.name", "depth": 2, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "pocode": {"api_field_name": "pocode", "chinese_name": "源头单据编码", "data_type": "NVARCHAR(500)", "param_desc": "源头单据编码", "path": "pocode", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_vouchdate_begin": {"api_field_name": "open_vouchdate_begin", "chinese_name": "开始时间，日期格式：YYYY-MM-DD", "data_type": "NVARCHAR(500)", "param_desc": "开始时间，日期格式：YYYY-MM-DD", "path": "open_vouchdate_begin", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_vouchdate_end": {"api_field_name": "open_vouchdate_end", "chinese_name": "结束时间，日期格式：YYYY-MM-DD", "data_type": "NVARCHAR(500)", "param_desc": "结束时间，日期格式：YYYY-MM-DD", "path": "open_vouchdate_end", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isSum": {"api_field_name": "isSum", "chinese_name": "查询表头", "data_type": "BIT", "param_desc": "查询表头", "path": "isSum", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "simpleVOs": {"api_field_name": "simpleVOs", "chinese_name": "扩展查询条件", "data_type": "NVARCHAR(MAX)", "param_desc": "扩展查询条件", "path": "simpleVOs", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "field": {"api_field_name": "field", "chinese_name": "属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码purInRecords.product.cCode、表头自定义项headItem.define1、表体自定义项purInRecords.bodyItem.define1等)", "data_type": "NVARCHAR(500)", "param_desc": "属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码purInRecords.product.cCode、表头自定义项headItem.define1、表体自定义项purInRecords.bodyItem.define1等)", "path": "simpleVOs.field", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "op": {"api_field_name": "op", "chinese_name": "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )", "data_type": "NVARCHAR(500)", "param_desc": "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )", "path": "simpleVOs.op", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value1": {"api_field_name": "value1", "chinese_name": "值1(条件)", "data_type": "NVARCHAR(500)", "param_desc": "值1(条件)", "path": "simpleVOs.value1", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value2": {"api_field_name": "value2", "chinese_name": "值2(条件)", "data_type": "NVARCHAR(500)", "param_desc": "值2(条件)", "path": "simpleVOs.value2", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}}}