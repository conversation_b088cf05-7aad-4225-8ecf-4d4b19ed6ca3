import json
import logging
import os
import pathlib
import sys
import xml.etree.ElementTree as ET  # !/usr/bin/env python3\n# -*- coding: utf-8 -*-\n"""\nYS-API V3.0 后端服务启动脚本 - 生产环境版本\n\n============================================================\n重要端口配置说明：\n后端端口: 8050 (生产环境主服务)\n前端端口: 8060 (管理界面) - 已标准化\n数据源: 真实生产数据，非测试数据\n模块数量: 14个完整业务模块(包含业务日志，已移除库存管理相关模块)\n============================================================\n\n特性: 端口管理、防错机制、真实数据连接\n"""\n\n\n# 添加scripts目录到路径以导入端口管理器\nsys.path.append(str(Path(__file__).parent.parent / "scripts"))\n\n# 生产环境端口配置 - 固定端口，禁止改动\nSERVER_PORT = 8050  # 固定后端端口 8050，禁止改动\nFRONTEND_PORT = 8060  # 固定前端端口 8060，禁止改动\n\n# 设置日志\nlogging.basicConfig(\n    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"\n)\nlogger = logging.getLogger(__name__)\n\n# 项目路径\nbackend_dir = Path(__file__).parent\nproject_root = backend_dir.parent\nfrontend_dir = project_root / "frontend"\n\n# 创建FastAPI应用\napp = FastAPI(\n    title="YS-API V3.0 Backend - Fixed",\n    description="用友云数据同步和字段配置管理API - 修复版",\n    version="3.0.1",\n)\n\n# 添加CORS中间件\napp.add_middleware(\n    CORSMiddleware,\n    allow_origins=[\n        f"http://localhost:{FRONTEND_PORT}",\n        f"http://127.0.0.1:{FRONTEND_PORT}",\n        "http://localhost:8060",  # 正确前端端口\n        "http://127.0.0.1:8060",  # 正确前端端口\n    ],\n    allow_credentials=True,\n    allow_methods=["*"],\n    allow_headers=["*"],\n)\n\n# 静态文件服务\napp.mount("/static", StaticFiles(directory=frontend_dir), name="static")\n\n# ================================\n# 页面路由 - 清理废弃页面\n# ================================\n\n\<EMAIL>("/")\nasync def root():\n    """根路径重定向到修复版字段配置页面"""\n    return RedirectResponse(url="/field-config-fixed.html", status_code=302)\n\n\<EMAIL>("/health")\nasync def health_check():\n    """健康检查端点"""\n    return {\n        "status": "healthy",\n        "service": "YS-API V3.0 Backend - Fixed",\n        "version": "3.0.1",\n        "port": SERVER_PORT,\n        "frontend_port": FRONTEND_PORT,\n    }\n\n\n# ================================\n# 有效页面路由\n# ================================\n\n\<EMAIL>("/field-config-fixed.html")\nasync def field_config_fixed_page():\n    """修复版字段配置页面"""\n    page_path = frontend_dir / "field-config-fixed.html"\n    if page_path.exists():\n        return FileResponse(str(page_path))\n    return {"error": f"页面不存在: {page_path}"}\n\n\<EMAIL>("/api-test.html")\nasync def api_test_page():\n    """API测试页面"""\n    page_path = frontend_dir / "api-test.html"\n    if page_path.exists():\n        return FileResponse(str(page_path))\n    return {"error": f"页面不存在: {page_path}"}\n\n\<EMAIL>("/页面端验证指南.html")\nasync def validation_guide_page():\n    """页面端验证指南"""\n    page_path = project_root / "页面端验证指南.html"\n    if page_path.exists():\n        return FileResponse(str(page_path))\n    return {"error": f"页面不存在: {page_path}"}\n\n\n# ================================\n# 废弃页面处理 - 返回友好提示\n# ================================\n\n\<EMAIL>("/excel-translation.html")\nasync def excel_translation_deprecated():\n    """废弃页面提示"""\n    return {\n        "error": "页面已废弃",\n        "message": "excel-translation.html 已不再使用",\n        "redirect": "/field-config-fixed.html",\n        "note": "请使用修复版字段配置页面",\n    }\n\n\<EMAIL>("/database-v2.html")\nasync def database_v2_deprecated():\n    """废弃页面提示"""\n    return {\n        "error": "页面已废弃",\n        "message": "database-v2.html 已不再使用",\n        "redirect": "/field-config-fixed.html",\n        "note": "请使用修复版字段配置页面",\n    }\n\n\<EMAIL>("/unified-field-config.html")\nasync def unified_field_config_deprecated():\n    """废弃页面提示"""\n    return {\n        "error": "页面已废弃",\n        "message": "unified-field-config.html 已不再使用",\n        "redirect": "/field-config-fixed.html",\n        "note": "请使用修复版字段配置页面",\n    }\n\n\n# ================================\n# API端点 - 字段配置和业务模块\n# ================================\n\n\<EMAIL>("/api/business-logs")\nasync def get_business_logs(\n    content: str = None,\n    busiObjType: str = None,\n    busiObjCode: str = None,\n    busiObjName: str = None,\n    operator: str = None,\n    startDate: str = None,\n    endDate: str = None,\n    page: int = 1,\n    size: int = 10,\n):\n    """业务日志查询API - 生产环境真实数据"""\n    logger.info(f"业务日志查询请求: page={page}, size={size}")\n\n    # 生产环境: 读取真实业务日志配置\n    business_log_config_path = project_root / "模块字段" / "json" / "业务日志.json"\n\n    try:\n        if business_log_config_path.exists():\n\n            with open(business_log_config_path, "r", encoding="utf-8") as f:\n                config_data = json.load(f)\n\n            # 返回真实配置数据用于生产环境\n            return {\n                "success": True,\n                "data": {\n                    "config": config_data,\n                    "query_params": {\n                        "content": content,\n                        "busiObjType": busiObjType,\n                        "busiObjCode": busiObjCode,\n                        "busiObjName": busiObjName,\n                        "operator": operator,\n                        "startDate": startDate,\n                        "endDate": endDate,\n                        "page": page,\n                        "size": size,\n                    },\n                    "api_info": {\n                        "name": "业务日志",\n                        "endpoint": "/yonbip/digitalModel/log-pub/business/rest/query",\n                        "method": "GET",\n                        "description": "生产环境业务日志查询接口",\n                    },\n                },\n            }\n        else:\n            return {"success": False, "error": "业务日志配置文件不存在"}\n\n    except Exception:\n        logger.error(f"业务日志API错误: {e}")\n        return {"success": False, "error": str(e)}\n\n\<EMAIL>("/api/v1/field-config/modules")\nasync def get_modules():\n    """获取所有模块列表"""\n    try:\n\n        config_path = project_root / "config" / "modules.json"\n\n        if config_path.exists():\n            with open(config_path, "r", encoding="utf-8") as f:\n                modules_config = json.load(f)\n                if isinstance(modules_config, list):\n                    return {"modules": modules_config}\n                return modules_config\n\n        # 生产环境完整14个模块列表（已移除库存管理相关模块）\n        return {\n            "modules": [\n                {\n                    "name": "材料出库单列表查询",\n                    "display_name": "材料出库单列表查询",\n                    "xml_file": "材料出库单列表查询.xml",\n                },\n                {\n                    "name": "采购订单列表",\n                    "display_name": "采购订单列表",\n                    "xml_file": "采购订单列表.xml",\n                },\n                {\n                    "name": "采购入库单列表",\n                    "display_name": "采购入库单列表",\n                    "xml_file": "采购入库单列表.xml",\n                },\n                {\n                    "name": "产品入库单列表查询",\n                    "display_name": "产品入库单列表查询",\n                    "xml_file": "产品入库单列表查询.xml",\n                },\n                {\n                    "name": "请购单列表查询",\n                    "display_name": "请购单列表查询",\n                    "xml_file": "请购单列表查询.xml",\n                },\n                {\n                    "name": "生产订单列表查询",\n                    "display_name": "生产订单列表查询",\n                    "xml_file": "生产订单列表查询.xml",\n                },\n                {\n                    "name": "委外订单列表",\n                    "display_name": "委外订单列表",\n                    "xml_file": "委外订单列表.xml",\n                },\n                {\n                    "name": "委外入库列表查询",\n                    "display_name": "委外入库列表查询",\n                    "xml_file": "委外入库列表查询.xml",\n                },\n                {\n                    "name": "委外申请列表查询",\n                    "display_name": "委外申请列表查询",\n                    "xml_file": "委外申请列表查询.xml",\n                },\n                {\n                    "name": "物料档案批量详情查询",\n                    "display_name": "物料档案批量详情查询",\n                    "xml_file": "物料档案批量详情查询.xml",\n                },\n                {\n                    "name": "现存量报表查询",\n                    "display_name": "现存量报表查询",\n                    "xml_file": "现存量报表查询.xml",\n                },\n                {\n                    "name": "销售出库列表查询",\n                    "display_name": "销售出库列表查询",\n                    "xml_file": "销售出库列表查询.xml",\n                },\n                {\n                    "name": "销售订单",\n                    "display_name": "销售订单",\n                    "xml_file": "销售订单.xml",\n                },\n                {\n                    "name": "需求计划",\n                    "display_name": "需求计划",\n                    "xml_file": "需求计划.xml",\n                },\n                {\n                    "name": "业务日志",\n                    "display_name": "业务日志",\n                    "xml_file": "业务日志.xml",\n                },\n            ]\n        }\n    except Exception:\n        logger.error(f"获取模块列表失败: {e}")\n        return {"error": "Failed to get modules", "message": str(e)}\n\n\<EMAIL>("/api/v1/field-config/modules/{module_name}/fields")\nasync def get_module_fields(\n    module_name: str, max_depth: int = 10, user_id: str = "default"\n):\n    """获取指定模块的字段配置"""\n    try:\n        # 从XML文件读取字段配置\n        xml_path = project_root / "模块字段" / f"{module_name}.xml"\n\n        if not xml_path.exists():\n            # 模块名称映射\n            module_mapping =

import CORSMiddleware
import fastapi
import FastAPI
import fastapi.middleware.cors
import fastapi.responses
import fastapi.staticfiles
import import
import n
import nfrom
import nimport
import Path
import port_manager
import PortManager
import Response
import StaticFiles
import uvicorn

    # 简化的字段解析\n\n            tree = ET.parse(xml_path)\n            root =
    # tree.getroot()\n\n            fields = []\n            for field in
    # root.findall(".//field"):\n                field_info = {\n
    # "name": field.get("name",
    {\n                "材料出库单列表查询": "材料出库单列表查询.xml", n                "采购订单列表": "采购订单列表.xml", n                "采购入库单列表": "采购入库列表.xml", n                "产品入库单列表查询": "产品入库列表查询.xml", n                "请购单列表查询": "请购单列表查询.xml", n                "生产订单列表查询": "生产订单列表查询.xml", n                "委外订单列表": "委外订单列表.xml", n                "委外入库列表查询": "委外入库列表查询.xml", n                "委外申请列表查询": "委外申请列表查询.xml", n                "销售出库列表查询": "销售出库列表查询.xml", n                "销售订单": "销售订单.xml", n                "需求计划": "需求计划.xml", n                "业务日志": "业务日志.xml", n}\n\n            xml_filename = module_mapping.get(module_name)\n if xml_filename: \n                xml_path = project_root / "模块字段" / xml_filename\n\n if xml_path.exists(): \n
    ""), n                    "display_name": field.get("display_name",
    field.get("name",
    "")), n                    "type": field.get("type",
    "string"), n                    "required": field.get("required",
    "false").lower() == "true", n                    "description": field.get("description",
    ""), n                    "source": "xml", n}\n                fields.append(field_info)\n\n return {\n                "module_name": module_name, n                "fields": fields, n                "total_count": len(fields), n                "source": "xml_config", n}\n else: \n            # 返回示例字段\n            return {\n                "module_name": module_name,
    \n                "fields": [\n                    {\n                        "name": "id",
    \n                        "display_name": "ID",
    \n                        "type": "string",
    \n                        "required": True,
    \n}, n                    {\n                        "name": "name",
    \n                        "display_name": "名称",
    \n                        "type": "string",
    \n                        "required": True,
    \n}, n                    {\n                        "name": "date",
    \n                        "display_name": "日期",
    \n                        "type": "date",
    \n                        "required": False,
    \n}, n                    {\n                        "name": "amount",
    \n                        "display_name": "金额",
    \n                        "type": "number",
    \n                        "required": False,
    \n}, n], n                "total_count": 4, n                "source": "default", n}\n except Exception: \n        logger.error(f"获取模块 {module_name} 字段失败: {e}")\n return {\n            "error": "Failed to get fields", n            "message": str(e), n            "module_name": module_name, n}\n\n\n @ app.get("/favicon.ico")\nasync def favicon(): \n    """网站图标"""\n\n return Response(status_code=204)\n\n\ndef main(): \n    """主启动函数 - 实现端口管理和防错机制"""\n print("🚀 YS-API V3.0 后端服务启动")\n print(f"端口: {SERVER_PORT}")\n print(f"前端服务端口: {FRONTEND_PORT}")\n print()\n\n    # 端口管理\n    port_manager = PortManager()\n\n    # 检查并准备后端端口\n    if not port_manager.ensure_port_available(SERVER_PORT):\n        logger.error("后端端口准备失败，服务无法启动")\n        sys.exit(1)\n\n    # 启动服务\n    logger.info(f"启动后端服务在端口 {SERVER_PORT}...")\n    logger.info(f"前端目录: {frontend_dir}")\n    logger.info(f"前端目录存在: {frontend_dir.exists()}")\n    logger.info(f"健康检查: http://127.0.0.1:{SERVER_PORT}/health")\n    logger.info(f"字段配置: http://127.0.0.1:{SERVER_PORT}/field-config-fixed.html")\n\n    try:\n        uvicorn.run(\n            app,
    host = "127.0.0.1",
    port = SERVER_PORT,
    log_level = "info",
    access_log = True\n)\n except Exception: \n        port_manager.handle_critical_error("服务启动失败",
    e)\n\n\nif __name__ == "__main__": \n    main()\n
