#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 连接池和事务管理器简单测试
"""

import os
import sys
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from backend.app.services.database_connection_pool import DatabaseConnectionManager
        print("✅ database_connection_pool 导入成功")
        
        from backend.app.services.transaction_manager import TransactionManager
        print("✅ transaction_manager 导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_connection_pool():
    """测试连接池基本功能"""
    print("\n测试连接池基本功能...")
    
    try:
        from backend.app.services.database_connection_pool import DatabaseConnectionManager
        
        # 创建连接管理器
        manager = DatabaseConnectionManager()
        print("✅ 连接管理器创建成功")
        
        # 测试获取连接
        with manager.get_connection('sqlite') as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            print(f"✅ SQLite连接测试成功: {result}")
        
        # 获取统计信息
        stats = manager.get_pool_stats()
        print(f"✅ 连接池统计: {stats}")
        
        # 清理
        manager.close_all()
        print("✅ 连接池关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接池测试失败: {e}")
        return False

def test_transaction_manager():
    """测试事务管理器基本功能"""
    print("\n测试事务管理器基本功能...")
    
    try:
        from backend.app.services.database_connection_pool import DatabaseConnectionManager
        from backend.app.services.transaction_manager import TransactionManager
        
        # 创建管理器
        conn_manager = DatabaseConnectionManager()
        txn_manager = TransactionManager(conn_manager)
        print("✅ 事务管理器创建成功")
        
        # 创建测试表
        with txn_manager.transaction('sqlite') as conn:
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS simple_test (
                    id INTEGER PRIMARY KEY,
                    name TEXT
                )
            """)
            cursor.close()
            print("✅ 测试表创建成功")
        
        # 测试插入数据
        with txn_manager.transaction('sqlite') as conn:
            cursor = conn.cursor()
            cursor.execute("INSERT INTO simple_test (name) VALUES (?)", ("测试数据",))
            cursor.close()
            print("✅ 数据插入成功")
        
        # 验证数据
        with txn_manager.transaction('sqlite') as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM simple_test")
            count = cursor.fetchone()[0]
            cursor.close()
            print(f"✅ 数据验证成功: {count} 条记录")
        
        # 获取统计信息
        stats = txn_manager.get_transaction_stats()
        print(f"✅ 事务统计: {stats}")
        
        # 清理
        txn_manager.close()
        conn_manager.close_all()
        print("✅ 事务管理器关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 事务管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("YS-API V3.0 连接池和事务管理器测试")
    print("=" * 50)
    
    tests = [
        ("模块导入测试", test_imports),
        ("连接池测试", test_connection_pool),
        ("事务管理器测试", test_transaction_manager)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n开始 {test_name}...")
        start_time = time.time()
        result = test_func()
        duration = time.time() - start_time
        results.append(result)
        
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status} (耗时: {duration:.2f}秒)")
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    print(f"\n总结: {passed}/{total} 通过 ({(passed/total)*100:.1f}%)")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print("\n测试完成!")
    exit(0 if success else 1)
