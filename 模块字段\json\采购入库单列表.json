[{"success": true, "code": 200, "message": "", "data": {"fieldVersion": 20230210, "appCode": "", "tokenSet": false, "tokenDoc": "", "tenantId": 0, "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "id": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "name": "采购入库列表查询", "apiClassifyId": "abfbb30ce25c4349b140bde94ff19059", "apiClassifyName": "采购入库单", "apiClassifyCode": "", "parentApiClassifies": "", "functionId": "", "openMode": 0, "description": "采购入库列表查询", "auth": true, "bodyPassthrough": false, "healthExam": false, "healthStatus": true, "responseResultPassthrough": false, "contentType": "application/json", "returnPassthrough": "", "completeProxyUrl": "/yonbip/scm/purinrecord/list", "connectUrl": "/bill/list", "sort": 20, "handler": "openapi", "httpRequestType": "POST", "openApi": true, "preset": false, "productId": "710a0be3edff4f9092e35f63fd3b9bae", "productCode": "scm", "proxyUrl": "/yonbip/scm/purinrecord/list", "requestParamsDemo": "Url: /yonbip/scm/purinrecord/list?access_token=访问令牌 Body: { \"pageIndex\": 0, \"code\": \"\", \"pageSize\": 0, \"bustype_name\": \"\", \"warehouse_name\": \"\", \"vendor_name\": \"\", \"org_id\": [ \"\" ], \"org_name\": \"\", \"org_code\": [ \"\" ], \"purchaseOrg_name\": [ \"\" ], \"inInvoiceOrg_name\": [ \"\" ], \"stockMgr_name\": [ \"\" ], \"operator_name\": [ \"\" ], \"department_name\": [ \"\" ], \"project_name\": [ \"\" ], \"product.productClass.name\": [ 0 ], \"pocode\": \"\", \"product_cName\": [ 0 ], \"open_vouchdate_begin\": \"\", \"open_vouchdate_end\": \"\", \"isSum\": false, \"simpleVOs\": [ { \"field\": \"\", \"op\": \"\", \"value1\": \"\", \"value2\": \"\" } ] }", "requestProtocol": "HTTP", "serviceHttpMethod": "POST", "publishStatus": true, "approvalMsg": "", "rpcAppName": "", "rpcServiceName": "", "rpcMethodName": "", "rpcServiceUrl": "", "ma": false, "gmtCreate": "2020-01-16 17:17:58", "gmtUpdate": "2024-09-05 10:37:45.000", "address": "https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/purinrecord/list", "productName": "采购供应", "productClassifyId": "yonsuite", "productClassifyCode": "yonbip", "productClassifyName": "用友 YonBIP", "paramDTOS": {"paramDTOS": [{"id": 2081374351925444620, "name": "pageIndex", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435016, "array": false, "paramDesc": "页号", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": 1, "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444621, "name": "code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435017, "array": false, "paramDesc": "单据编号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444622, "name": "pageSize", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435018, "array": false, "paramDesc": "每页行数", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": 10, "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444623, "name": "bustype_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435019, "array": false, "paramDesc": "交易类型，需传入交易类型id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444624, "name": "warehouse_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435020, "array": false, "paramDesc": "仓库，需传入仓库名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444625, "name": "vendor_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435021, "array": false, "paramDesc": "供应商，需传入供应商名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444626, "name": "org_id", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435022, "array": true, "paramDesc": "库存组织id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444627, "name": "org_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435023, "array": false, "paramDesc": "库存组织名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444628, "name": "org_code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435024, "array": true, "paramDesc": "库存组织编码", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444629, "name": "purchaseOrg_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435025, "array": true, "paramDesc": "采购组织，需传入采购组织id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444630, "name": "inInvoiceOrg_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435026, "array": true, "paramDesc": "收票组织，需传入收票组织id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444631, "name": "stockMgr_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435027, "array": true, "paramDesc": "库管员，需传入库管员id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444632, "name": "operator_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435028, "array": true, "paramDesc": "业务员，需传入业务员id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444633, "name": "department_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435029, "array": true, "paramDesc": "部门，需传部门id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 13, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444634, "name": "project_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435030, "array": true, "paramDesc": "项目，需传入项目id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 14, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444635, "name": "product.productClass.name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435031, "array": true, "paramDesc": "物料分类，需传入物料分类id", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 15, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444636, "name": "pocode", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435032, "array": false, "paramDesc": "源头单据编码", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 16, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444637, "name": "product_cName", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435033, "array": true, "paramDesc": "物料，需传入物料id", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 17, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": 0, "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444638, "name": "open_vouchdate_begin", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435034, "array": false, "paramDesc": "开始时间，日期格式：YYYY-MM-DD", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 18, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444639, "name": "open_vouchdate_end", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435035, "array": false, "paramDesc": "结束时间，日期格式：YYYY-MM-DD", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 19, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444640, "name": "isSum", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435036, "array": false, "paramDesc": "查询表头", "paramType": "boolean", "requestParamType": "BodyParam", "path": "", "example": false, "fullName": "", "ytenantId": "", "paramOrder": 20, "bizType": "", "baseType": true, "defaultValue": false, "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444615, "name": "simpleVOs", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "children": {"children": [{"id": 2081374351925444616, "name": "field", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444615, "defParamId": 1856790202779435038, "array": false, "paramDesc": "属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码purInRecords.product.cCode、表头自定义项headItem.define1、表体自定义项purInRecords.bodyItem.define1等)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444617, "name": "op", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444615, "defParamId": 1856790202779435039, "array": false, "paramDesc": "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444618, "name": "value1", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444615, "defParamId": 1856790202779435040, "array": false, "paramDesc": "值1(条件)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2081374351925444619, "name": "value2", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444615, "defParamId": 1856790202779435041, "array": false, "paramDesc": "值2(条件)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "defParamId": 1856790202779435037, "array": true, "paramDesc": "扩展查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 21, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "queryParamDTOS": "", "ysApi": false, "presetTokenApi": false, "applyFlag": false, "cover": false, "paramMapDTOS": {"paramMapDTOS": [{"id": 2081374351925444646, "name": "pageIndex", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "页号", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "pageIndex", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444647, "name": "code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "单据编号", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "code", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444648, "name": "pageSize", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "每页行数", "paramType": "int", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "pageSize", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "int", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444649, "name": "bustype_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "交易类型，需传入交易类型id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "bustype_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444650, "name": "warehouse_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "仓库，需传入仓库名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "aggregatedValueObject": false, "mapName": "warehouse_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444651, "name": "vendor_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "供应商，需传入供应商名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "aggregatedValueObject": false, "mapName": "vendor_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444652, "name": "org_id", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "库存组织id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "aggregatedValueObject": false, "mapName": "org_id", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444653, "name": "org_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "库存组织名称", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "aggregatedValueObject": false, "mapName": "org_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444654, "name": "org_code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "库存组织编码", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "aggregatedValueObject": false, "mapName": "org_code", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444655, "name": "purchaseOrg_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "采购组织，需传入采购组织id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "aggregatedValueObject": false, "mapName": "purchaseOrg_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444656, "name": "inInvoiceOrg_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "收票组织，需传入收票组织id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "aggregatedValueObject": false, "mapName": "inInvoiceOrg_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444657, "name": "stockMgr_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "库管员，需传入库管员id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "aggregatedValueObject": false, "mapName": "stockMgr_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444658, "name": "operator_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "业务员，需传入业务员id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "aggregatedValueObject": false, "mapName": "operator_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444659, "name": "department_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "部门，需传部门id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "aggregatedValueObject": false, "mapName": "department_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444660, "name": "project_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "项目，需传入项目id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 14, "baseType": true, "aggregatedValueObject": false, "mapName": "project_name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444661, "name": "product.productClass.name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "物料分类，需传入物料分类id", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "aggregatedValueObject": false, "mapName": "product.productClass.name", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444662, "name": "pocode", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "源头单据编码", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "aggregatedValueObject": false, "mapName": "pocode", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444663, "name": "product_cName", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "物料，需传入物料id", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "aggregatedValueObject": false, "mapName": "product_cName", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444664, "name": "open_vouchdate_begin", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "开始时间，日期格式：YYYY-MM-DD", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "aggregatedValueObject": false, "mapName": "open_vouchdate_begin", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444665, "name": "open_vouchdate_end", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "结束时间，日期格式：YYYY-MM-DD", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "aggregatedValueObject": false, "mapName": "open_vouchdate_end", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444666, "name": "isSum", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": "", "array": false, "paramDesc": "查询表头", "paramType": "boolean", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 20, "baseType": true, "aggregatedValueObject": false, "mapName": "isSum", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "boolean", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444641, "name": "simpleVOs", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "children": {"children": [{"id": 2081374351925444642, "name": "field", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444641, "defParamId": "", "array": false, "paramDesc": "属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码purInRecords.product.cCode、表头自定义项headItem.define1、表体自定义项purInRecords.bodyItem.define1等)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "field", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444643, "name": "op", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444641, "defParamId": "", "array": false, "paramDesc": "比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "op", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444644, "name": "value1", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444641, "defParamId": "", "array": false, "paramDesc": "值1(条件)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "value1", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2081374351925444645, "name": "value2", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444641, "defParamId": "", "array": false, "paramDesc": "值2(条件)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "value2", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "defParamId": "", "array": false, "paramDesc": "扩展查询条件", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 21, "baseType": true, "aggregatedValueObject": false, "mapName": "simpleVOs", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "paramReturnDTOS": {"paramReturnDTOS": [{"id": 2081374351925444827, "name": "code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435068, "array": false, "paramDesc": "返回码，调用成功时返回200", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444828, "name": "message", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "defParamId": 1856790202779435069, "array": false, "paramDesc": "调用失败时的错误信息", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444667, "name": "data", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "children": {"children": [{"id": 2081374351925444793, "name": "pageIndex", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444667, "defParamId": 1856790202779435071, "array": false, "paramDesc": "当前页数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444794, "name": "pageSize", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444667, "defParamId": 1856790202779435072, "array": false, "paramDesc": "当前页数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444795, "name": "pageCount", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444667, "defParamId": 1856790202779435073, "array": false, "paramDesc": "页面数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444796, "name": "beginPageIndex", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444667, "defParamId": 1856790202779435074, "array": false, "paramDesc": "开始页码（第一页）", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444797, "name": "endPageIndex", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444667, "defParamId": 1856790202779435075, "array": false, "paramDesc": "结束页码（有多少页）", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444798, "name": "recordCount", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444667, "defParamId": 1856790202779435076, "array": false, "paramDesc": "总数", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444799, "name": "pubts", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444667, "defParamId": 1856790202779435077, "array": false, "paramDesc": "时间戳字符串", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444668, "name": "recordList", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444667, "children": {"children": [{"id": 2081374351925444669, "name": "vouchdate", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435079, "array": false, "paramDesc": "单据日期", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444670, "name": "code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435080, "array": false, "paramDesc": "单据编号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444671, "name": "bustype_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435081, "array": false, "paramDesc": "交易类型名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"bustype.name\",\"cItemName\":\"bustype_name\",\"cCaption\":\"交易类型\",\"cShowCaption\":\"交易类型\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_user\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444672, "name": "vendor_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435082, "array": false, "paramDesc": "供应商名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"vendor.name\",\"cItemName\":\"vendor_name\",\"cCaption\":\"供应商\",\"cShowCaption\":\"供应商\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_user\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444673, "name": "warehouse_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435083, "array": false, "paramDesc": "仓库名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"warehouse.name\",\"cItemName\":\"warehouse_name\",\"cCaption\":\"仓库\",\"cShowCaption\":\"仓库\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_warehouse\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444674, "name": "vendor_code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435084, "array": false, "paramDesc": "供应商编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"vendor.code\",\"cItemName\":\"vendor_code\",\"cCaption\":\"供应商编码\",\"cShowCaption\":\"供应商编码\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_user\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444675, "name": "warehouse_code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435085, "array": false, "paramDesc": "仓库编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"warehouse.code\",\"cItemName\":\"warehouse_code\",\"cCaption\":\"仓库编码\",\"cShowCaption\":\"仓库编码\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_warehouse\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444676, "name": "stockMgr_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435086, "array": false, "paramDesc": "库管员名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"stockMgr.name\",\"cItemName\":\"stockMgr_name\",\"cCaption\":\"库管员\",\"cShowCaption\":\"库管员\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucf-staff-center.bd_staff_outer_ref\",\"cRefId\":null,\"cRefRetId\":{\"stockMgr\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444677, "name": "status", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435087, "array": false, "paramDesc": "单据状态, 0:未提交、1:已提交、", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444678, "name": "purchaseOrg_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435088, "array": false, "paramDesc": "采购组织名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"purchaseOrg.name\",\"cItemName\":\"purchaseOrg_name\",\"cCaption\":\"采购组织\",\"cShowCaption\":\"采购组织\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_orgtree\",\"cRefId\":null,\"cRefRetId\":{\"purchaseOrg\":\"id\"},\"cDataRule\":\"\\\"%u8c-config.option.singleOrg%>\\\"==\\\"false\\\"\",\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444679, "name": "department_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435089, "array": false, "paramDesc": "部门名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"department.name\",\"cItemName\":\"department_name\",\"cCaption\":\"部门\",\"cShowCaption\":\"部门\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucf-org-center.bd_adminorgsharetreeref\",\"cRefId\":null,\"cRefRetId\":{\"department\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"TreeRefer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444680, "name": "org_code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435090, "array": false, "paramDesc": "库存组织编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444681, "name": "org_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435091, "array": false, "paramDesc": "库存组织名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"org.name\",\"cItemName\":\"org_name\",\"cCaption\":\"库存组织\",\"cShowCaption\":\"库存组织\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_orgtree\",\"cRefId\":null,\"cRefRetId\":{\"org\":\"id\"},\"cDataRule\":\"\\\"%u8c-config.option.singleOrg%>\\\"==\\\"false\\\"\",\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444682, "name": "department_code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435092, "array": false, "paramDesc": "部门编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"department.code\",\"cItemName\":\"department_code\",\"cCaption\":\"部门编码\",\"cShowCaption\":\"部门编码\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucf-org-center.bd_adminorgsharetreeref\",\"cRefId\":null,\"cRefRetId\":{\"department\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"TreeRefer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444683, "name": "operator_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435093, "array": false, "paramDesc": "经办人名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 14, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"operator.name\",\"cItemName\":\"operator_name\",\"cCaption\":\"经办人\",\"cShowCaption\":\"经办人\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_user\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444684, "name": "totalQuantity", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435094, "array": false, "paramDesc": "整单数量", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444685, "name": "totalPieces", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435095, "array": false, "paramDesc": "整单件数", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444686, "name": "inInvoiceOrg_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435096, "array": false, "paramDesc": "收票组织名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"inInvoiceOrg.name\",\"cItemName\":\"inInvoiceOrg_name\",\"cCaption\":\"收票组织\",\"cShowCaption\":\"收票组织\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_orgtree\",\"cRefId\":null,\"cRefRetId\":{\"inInvoiceOrg\":\"id\"},\"cDataRule\":\"\\\"%u8c-config.option.singleOrg%>\\\"==\\\"false\\\"\",\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444687, "name": "inInvoiceOrg", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435097, "array": false, "paramDesc": "收票组织id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444688, "name": "accountOrg", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435098, "array": false, "paramDesc": "会计主体", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444689, "name": "isBeginning", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435099, "array": false, "paramDesc": "是否期初, true:是、false:否、", "paramType": "boolean", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 20, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444690, "name": "bustype", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435100, "array": false, "paramDesc": "业务类型id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 21, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444691, "name": "vendor", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435101, "array": false, "paramDesc": "供应商id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 22, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444692, "name": "contact", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435102, "array": false, "paramDesc": "联系人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 23, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"contact\",\"cItemName\":\"contact\",\"cCaption\":\"联系人\",\"cShowCaption\":\"联系人\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_user\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":{\"id\":\"String\",\"metaType\":\"PrimitiveType\",\"name\":\"字符型\",\"uri\":\"String\"},\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444693, "name": "warehouse", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435103, "array": false, "paramDesc": "仓库id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 24, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444694, "name": "operator", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435104, "array": false, "paramDesc": "经办人id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 25, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444695, "name": "purchaseOrg", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435105, "array": false, "paramDesc": "采购组织IDid", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 26, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444696, "name": "org", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435106, "array": false, "paramDesc": "库存组织id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 27, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444697, "name": "department", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435107, "array": false, "paramDesc": "部门id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 28, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444698, "name": "stockMgr", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435108, "array": false, "paramDesc": "库管员IDid", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 29, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444699, "name": "moneysum", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435109, "array": false, "paramDesc": "金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 30, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444700, "name": "paymentsum", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435110, "array": false, "paramDesc": "付款金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 31, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444701, "name": "unpaymentsum", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435111, "array": false, "paramDesc": "未付款金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 32, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444702, "name": "store", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435112, "array": false, "paramDesc": "门店id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 33, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444703, "name": "store_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435113, "array": false, "paramDesc": "门店名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 34, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"store.name\",\"cItemName\":\"store_name\",\"cCaption\":\"门店\",\"cShowCaption\":\"门店\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_department\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":\"\\\"%productcenter.option.isOpenURetail%>\\\"==\\\"true\\\"\",\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444704, "name": "custom", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435114, "array": false, "paramDesc": "客户id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 35, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444705, "name": "payor", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435115, "array": false, "paramDesc": "付款人id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 36, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444706, "name": "payor_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435116, "array": false, "paramDesc": "付款人名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 37, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"payor.name\",\"cItemName\":\"payor_name\",\"cCaption\":\"付款人\",\"cShowCaption\":\"付款人\",\"iMaxLength\":255,\"bHidden\":true,\"cRefType\":\"aa_user\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444707, "name": "paytime", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435117, "array": false, "paramDesc": "付款时间", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 38, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444708, "name": "paymentstatus", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435118, "array": false, "paramDesc": "付款状态, 0:未完成、1:完成、", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 39, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444709, "name": "creator", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435119, "array": false, "paramDesc": "创建人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 40, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444710, "name": "createTime", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435120, "array": false, "paramDesc": "创建时间", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 41, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444711, "name": "modifier", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435121, "array": false, "paramDesc": "最后修改人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 42, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444712, "name": "modifyTime", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435122, "array": false, "paramDesc": "最后修改时间", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 43, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444713, "name": "auditor", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435123, "array": false, "paramDesc": "提交人", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 44, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444714, "name": "auditTime", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435124, "array": false, "paramDesc": "提交时间", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 45, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444715, "name": "memo", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435125, "array": false, "paramDesc": "备注", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 46, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444716, "name": "id", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435126, "array": false, "paramDesc": "主表ID", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 47, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444717, "name": "srcBill", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435127, "array": false, "paramDesc": "来源单据id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 48, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444718, "name": "pubts", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435128, "array": false, "paramDesc": "时间戳", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 49, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444719, "name": "tplid", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435129, "array": false, "paramDesc": "模板id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 50, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444720, "name": "<PERSON><PERSON>us", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435130, "array": false, "paramDesc": "交换状态", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 51, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444721, "name": "purInRecords_id", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435131, "array": false, "paramDesc": "订单行id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 52, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444722, "name": "product", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435132, "array": false, "paramDesc": "物料id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 53, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444723, "name": "product_cCode", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435133, "array": false, "paramDesc": "物料编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 54, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"purInRecords.product.cCode\",\"cItemName\":\"product_cCode\",\"cCaption\":\"物料编码\",\"cShowCaption\":\"物料编码\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cCode\":\"cCode\",\"product_cName\":\"cName\",\"product_oUnitId\":\"oUnitId\",\"unit\":\"oUnitId\",\"unit_name\":\"unitName\",\"unit_code\":\"unitCode\",\"purUOM\":\"purchaseUnit\",\"purUOM_Name\":\"purchaseUnit_name\",\"purUOM_Code\":\"purchaseUnit_code\",\"invExchRate\":\"purchaseRate\",\"priceUOM\":\"purchaseUnit\",\"priceUOM_Name\":\"purchaseUnit_name\",\"priceUOM_Code\":\"purchaseUnit_code\",\"invPriceExchRate\":\"purchaseRate\",\"taxRate\":\"productOfflineRetail_inputTax\",\"product_primeCosts\":\"primeCosts\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isSerialNoManage\":\"productOfflineRetail_isSerialNoManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"product_modelDescription\":\"modelDescription\",\"maxInPrice\":\"maxPrimeCosts\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecords\",\"cControlType\":\"refer\",\"refReturn\":\"cCode\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444724, "name": "product_cName", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435134, "array": false, "paramDesc": "物料名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 55, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"purInRecords.product.cName\",\"cItemName\":\"product_cName\",\"cCaption\":\"物料名称\",\"cShowCaption\":\"物料名称\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cCode\":\"cCode\",\"product_cName\":\"cName\",\"product_oUnitId\":\"oUnitId\",\"unit\":\"oUnitId\",\"unit_name\":\"unitName\",\"unit_code\":\"unitCode\",\"purUOM\":\"purchaseUnit\",\"purUOM_Name\":\"purchaseUnit_name\",\"purUOM_Code\":\"purchaseUnit_code\",\"invExchRate\":\"purchaseRate\",\"priceUOM\":\"purchaseUnit\",\"priceUOM_Name\":\"purchaseUnit_name\",\"priceUOM_Code\":\"purchaseUnit_code\",\"invPriceExchRate\":\"purchaseRate\",\"taxRate\":\"productOfflineRetail_inputTax\",\"product_primeCosts\":\"primeCosts\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isSerialNoManage\":\"productOfflineRetail_isSerialNoManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"product_modelDescription\":\"modelDescription\",\"maxInPrice\":\"maxPrimeCosts\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecords\",\"cControlType\":\"refer\",\"refReturn\":\"cName\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444725, "name": "productsku", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435135, "array": false, "paramDesc": "物料SKUid", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 56, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444726, "name": "tradeRoute_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1878164236360220675, "array": false, "paramDesc": "贸易路径", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 57, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444727, "name": "productsku_cCode", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435136, "array": false, "paramDesc": "物料sku编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 58, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"purInRecords.productsku.cCode\",\"cItemName\":\"productsku_cCode\",\"cCaption\":\"物料sku编码\",\"cShowCaption\":\"物料sku编码\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cCode\":\"cCode\",\"product_cName\":\"cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"productsku_cName\":\"skuName\",\"product_oUnitId\":\"oUnitId\",\"unit\":\"oUnitId\",\"unit_name\":\"unitName\",\"unit_code\":\"unitCode\",\"purUOM\":\"purchaseUnit\",\"purUOM_Name\":\"purchaseUnit_name\",\"purUOM_Code\":\"purchaseUnit_code\",\"invExchRate\":\"purchaseRate\",\"priceUOM\":\"purchaseUnit\",\"priceUOM_Name\":\"purchaseUnit_name\",\"priceUOM_Code\":\"purchaseUnit_code\",\"invPriceExchRate\":\"purchaseRate\",\"taxRate\":\"productOfflineRetail_inputTax\",\"product_primeCosts\":\"primeCosts\",\"productsku_primeCosts\":\"productskus_primeCosts\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isSerialNoManage\":\"productOfflineRetail_isSerialNoManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"product_modelDescription\":\"modelDescription\",\"productskus_modelDescription\":\"productskus_modelDescription\",\"maxInPrice\":\"maxPrimeCosts\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecords\",\"cControlType\":\"refer\",\"refReturn\":\"productskus_cCode\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444728, "name": "productsku_cName", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435137, "array": false, "paramDesc": "物料sku名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 59, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"purInRecords.productsku.skuName\",\"cItemName\":\"productsku_cName\",\"cCaption\":\"物料sku名称\",\"cShowCaption\":\"物料sku名称\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cCode\":\"cCode\",\"product_cName\":\"cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"productsku_cName\":\"skuName\",\"product_oUnitId\":\"oUnitId\",\"unit\":\"oUnitId\",\"unit_name\":\"unitName\",\"unit_code\":\"unitCode\",\"purUOM\":\"purchaseUnit\",\"purUOM_Name\":\"purchaseUnit_name\",\"purUOM_Code\":\"purchaseUnit_code\",\"invExchRate\":\"purchaseRate\",\"priceUOM\":\"purchaseUnit\",\"priceUOM_Name\":\"purchaseUnit_name\",\"priceUOM_Code\":\"purchaseUnit_code\",\"invPriceExchRate\":\"purchaseRate\",\"taxRate\":\"productOfflineRetail_inputTax\",\"product_primeCosts\":\"primeCosts\",\"productsku_primeCosts\":\"productskus_primeCosts\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isSerialNoManage\":\"productOfflineRetail_isSerialNoManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"product_modelDescription\":\"modelDescription\",\"productskus_modelDescription\":\"productskus_modelDescription\",\"maxInPrice\":\"maxPrimeCosts\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecords\",\"cControlType\":\"refer\",\"refReturn\":\"cName\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444729, "name": "productClass_code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435138, "array": false, "paramDesc": "物料分类编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 60, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444730, "name": "propertiesValue", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435139, "array": false, "paramDesc": "规格", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 61, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444731, "name": "batchno", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435140, "array": false, "paramDesc": "批次号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 62, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"purInRecords.batchno\",\"cItemName\":\"batchno\",\"cCaption\":\"批次号\",\"cShowCaption\":\"批次号\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"st_batchnoref\",\"cRefId\":null,\"cRefRetId\":{\"batchno\":\"batchno\",\"producedate\":\"producedate\",\"invaliddate\":\"invaliddate\",\"define@1@@30\":\"define@1@@30\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecords\",\"cControlType\":\"refer\",\"refReturn\":\"batchno\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444732, "name": "invaliddate", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435141, "array": false, "paramDesc": "有效期至", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 63, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444733, "name": "producedate", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435142, "array": false, "paramDesc": "生产日期", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 64, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444734, "name": "unit", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435143, "array": false, "paramDesc": "单位id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 65, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444735, "name": "qty", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435144, "array": false, "paramDesc": "数量", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 66, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444736, "name": "unit_code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435145, "array": false, "paramDesc": "计量单位编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 67, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444737, "name": "unit_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435146, "array": false, "paramDesc": "计量单位名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 68, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"purInRecords.unit.name\",\"cItemName\":\"unit_name\",\"cCaption\":\"计量单位\",\"cShowCaption\":\"计量单位\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productunit\",\"cRefId\":null,\"cRefRetId\":{\"unit\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecords\",\"cControlType\":\"refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444738, "name": "subQty", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435147, "array": false, "paramDesc": "件数", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 69, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444739, "name": "stockUnit_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435148, "array": false, "paramDesc": "库存单位", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 70, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"purInRecords.stockUnitId.name\",\"cItemName\":\"stockUnit_name\",\"cCaption\":\"库存单位\",\"cShowCaption\":\"库存单位\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"productcenter.pc_productassitunitsref\",\"cRefId\":null,\"cRefRetId\":{\"stockUnitId\":\"assistUnit\",\"stockUnit_name\":\"assistUnit_Name\",\"stockUnit_code\":\"assistUnit_Code\",\"invExchRate\":\"assistUnitCount\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecords\",\"cControlType\":\"refer\",\"refReturn\":\"assistUnit_Name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444740, "name": "project_code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435149, "array": false, "paramDesc": "项目编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 71, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"purInRecords.project.code\",\"cItemName\":\"project_code\",\"cCaption\":\"项目编码\",\"cShowCaption\":\"项目编码\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucfbasedoc.bd_outer_projectcardMCref\",\"cRefId\":null,\"cRefRetId\":{\"project\":\"id\",\"project_code\":\"code\",\"project_name\":\"name\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecords\",\"cControlType\":\"Refer\",\"refReturn\":null,\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444741, "name": "project_name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435150, "array": false, "paramDesc": "项目名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 72, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"purInRecords.project.name\",\"cItemName\":\"project_name\",\"cCaption\":\"项目名称\",\"cShowCaption\":\"项目名称\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucfbasedoc.bd_outer_projectcardMCref\",\"cRefId\":null,\"cRefRetId\":{\"project\":\"id\",\"project_code\":\"code\",\"project_name\":\"name\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecords\",\"cControlType\":\"Refer\",\"refReturn\":null,\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444742, "name": "oriUnitPrice", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435151, "array": false, "paramDesc": "无税单价", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 73, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444743, "name": "oriTaxUnitPrice", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435152, "array": false, "paramDesc": "含税单价", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 74, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444744, "name": "oriMoney", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435153, "array": false, "paramDesc": "无税金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 75, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444745, "name": "oriSum", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435154, "array": false, "paramDesc": "含税金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 76, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444746, "name": "oriTax", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435155, "array": false, "paramDesc": "税额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 77, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444747, "name": "taxRate", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435156, "array": false, "paramDesc": "税率", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 78, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444748, "name": "billqty", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435157, "array": false, "paramDesc": "累计开票数量", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 79, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444749, "name": "billSubQty", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435158, "array": false, "paramDesc": "累计开票件数", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 80, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444750, "name": "sqty", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435159, "array": false, "paramDesc": "累计结算数量", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 81, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444751, "name": "smoney", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435160, "array": false, "paramDesc": "累计结算金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 82, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444752, "name": "sfee", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435161, "array": false, "paramDesc": "累计结算费用", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 83, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444753, "name": "totalBillOriSum", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435162, "array": false, "paramDesc": "累计开票含税金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 84, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444754, "name": "priceUOM", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435163, "array": false, "paramDesc": "计价单位id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 85, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444755, "name": "priceUOM_Code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435164, "array": false, "paramDesc": "计价单位编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 86, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"purInRecords.priceUOM.code\",\"cItemName\":\"priceUOM_Code\",\"cCaption\":\"计价单位编码\",\"cShowCaption\":\"计价单位编码\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productunit\",\"cRefId\":null,\"cRefRetId\":{\"priceUOM\":\"id\",\"priceUOM_Name\":\"name\",\"priceUOM_Code\":\"code\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecords\",\"cControlType\":\"refer\",\"refReturn\":\"Code\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444756, "name": "priceUOM_Name", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435165, "array": false, "paramDesc": "计价单位名称", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 87, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"purInRecords.priceUOM.name\",\"cItemName\":\"priceUOM_Name\",\"cCaption\":\"计价单位名称\",\"cShowCaption\":\"计价单位名称\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productunit\",\"cRefId\":null,\"cRefRetId\":{\"priceUOM\":\"id\",\"priceUOM_Name\":\"name\",\"priceUOM_Code\":\"code\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecords\",\"cControlType\":\"refer\",\"refReturn\":\"Name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444757, "name": "natCurrency_priceDigit", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435166, "array": false, "paramDesc": "本币单价精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 88, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444758, "name": "natCurrency_moneyDigit", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435167, "array": false, "paramDesc": "本币金额精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 89, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444759, "name": "currency_priceDigit", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435168, "array": false, "paramDesc": "币种单价精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 90, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444760, "name": "currency_moneyDigit", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435169, "array": false, "paramDesc": "币种金额精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 91, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444761, "name": "unit_Precision", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435170, "array": false, "paramDesc": "主计量精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 92, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444762, "name": "priceUOM_Precision", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435171, "array": false, "paramDesc": "计价单位精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 93, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444763, "name": "stockUnitId_Precision", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435172, "array": false, "paramDesc": "库存单位精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 94, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444764, "name": "isGiftProduct", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435173, "array": false, "paramDesc": "赠品, true:是、false:否、", "paramType": "boolean", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 95, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444765, "name": "bmake_st_purinvoice_red", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435174, "array": false, "paramDesc": "流程红票", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 96, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444766, "name": "bmake_st_purinvoice", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435175, "array": false, "paramDesc": "流程蓝票", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 97, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444767, "name": "bizFlow", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435176, "array": false, "paramDesc": "流程ID", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 98, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444768, "name": "bizFlow_version", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435177, "array": false, "paramDesc": "版本信息", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 99, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"bizFlow.version\",\"cItemName\":\"bizFlow_version\",\"cCaption\":\"版本信息\",\"cShowCaption\":\"版本信息\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucf-staff-center.bf_businessFlow_ref\",\"cRefId\":{\"bizFlow\":\"id\",\"bizFlow_version\":\"version\"},\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"st.purinrecord.PurInRecord\",\"cControlType\":\"refer\",\"refReturn\":null,\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"true\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444769, "name": "isFlowCoreBill", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435178, "array": false, "paramDesc": "是否流程核心单据", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 100, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444773, "name": "out_sys_id", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435182, "array": false, "paramDesc": "外部来源线索", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 104, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444774, "name": "out_sys_code", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435183, "array": false, "paramDesc": "外部来源编码", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 105, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444775, "name": "out_sys_version", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435184, "array": false, "paramDesc": "外部来源版本", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 106, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444776, "name": "out_sys_type", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435185, "array": false, "paramDesc": "外部来源类型", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 107, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444777, "name": "out_sys_rowno", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435186, "array": false, "paramDesc": "外部来源行号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 108, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444778, "name": "out_sys_lineid", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1856790202779435187, "array": false, "paramDesc": "外部来源行", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 109, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444779, "name": "tradeRouteID", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1869300738141192199, "array": false, "paramDesc": "贸易路径id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 110, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444780, "name": "isEndTrade", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1869300738141192200, "array": false, "paramDesc": "是否末级(0:否,1:是)", "paramType": "short", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 111, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444781, "name": "tradeRouteLineno", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1869300738141192201, "array": false, "paramDesc": "站点", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 112, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444782, "name": "collaborationPolineno", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1869300738141192202, "array": false, "paramDesc": "协同来源单据行号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 113, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444783, "name": "coSourceType", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1869300738141192203, "array": false, "paramDesc": "协同源头单据类型(productionorder.po_subcontract_order:委外订单)", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 114, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444784, "name": "coUpcode", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1869300738141192204, "array": false, "paramDesc": "协同源头单据号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 115, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444785, "name": "coSourceLineNo", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1869300738141192205, "array": false, "paramDesc": "协同源头单据行号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 116, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444786, "name": "coSourceid", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1869300738141192206, "array": false, "paramDesc": "协同来源单据id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 117, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444787, "name": "coSourceautoid", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1869300738141192207, "array": false, "paramDesc": "协同来源单据子表id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 118, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444788, "name": "collaborationPodetailid", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1871338700102172673, "array": false, "paramDesc": "协同来源单据行", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 119, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444789, "name": "collaborationPocode", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1871338700102172674, "array": false, "paramDesc": "协同来源单据号", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 120, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444790, "name": "collaborationPoid", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1871338700102172675, "array": false, "paramDesc": "协同来源单据主表id", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 121, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444791, "name": "collaborationSource", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1871338700102172676, "array": false, "paramDesc": "协同来源单据类型(st_salesout:销售出库)", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 122, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444792, "name": "totalOutStockQuantity", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444668, "defParamId": 1878157330052808705, "array": false, "paramDesc": "累计销售出库数量", "paramType": "BigDecimal", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 123, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1856790202779435078, "array": true, "paramDesc": "返回结果对象", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1856790202779435070, "array": false, "paramDesc": "调用成功时的返回数据", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444800, "name": "sumRecordList", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": "", "children": {"children": [{"id": 2081374351925444801, "name": "totalQuantity", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435189, "array": false, "paramDesc": "整单数量", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444802, "name": "totalPieces", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435190, "array": false, "paramDesc": "整单件数", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444803, "name": "subQty", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435191, "array": false, "paramDesc": "件数", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444804, "name": "currency", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435192, "array": false, "paramDesc": "币种id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444805, "name": "paymentsum", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435193, "array": false, "paramDesc": "付款金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444806, "name": "sfee", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435194, "array": false, "paramDesc": "累计结算费用", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444807, "name": "unit_Precision", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435195, "array": false, "paramDesc": "主计量精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444808, "name": "currency_priceDigit", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435196, "array": false, "paramDesc": "币种单价精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444809, "name": "priceUOM_Precision", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435197, "array": false, "paramDesc": "计价单位精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444810, "name": "moneysum", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435198, "array": false, "paramDesc": "金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444811, "name": "oriMoney", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435199, "array": false, "paramDesc": "无税金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444812, "name": "oriSum", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435200, "array": false, "paramDesc": "含税金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444813, "name": "stockUnitId_Precision", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435201, "array": false, "paramDesc": "库存单位精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444814, "name": "qty", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435202, "array": false, "paramDesc": "数量", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444815, "name": "purInRecords_unit", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435203, "array": false, "paramDesc": "计量单位", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 14, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444816, "name": "natCurrency", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435204, "array": false, "paramDesc": "本币币种id", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444817, "name": "natCurrency_moneyDigit", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435205, "array": false, "paramDesc": "本币金额精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444818, "name": "unpaymentsum", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435206, "array": false, "paramDesc": "未付款金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444819, "name": "smoney", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435207, "array": false, "paramDesc": "累计结算金额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444820, "name": "sqty", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435208, "array": false, "paramDesc": "累计结算数量", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444821, "name": "natCurrency_priceDigit", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435209, "array": false, "paramDesc": "本币单价精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 20, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444822, "name": "purInRecords_stockUnitId", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435210, "array": false, "paramDesc": "库存单位", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 21, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444823, "name": "purInRecords_priceUOM", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435211, "array": false, "paramDesc": "计价单位", "paramType": "long", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 22, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444824, "name": "oriTax", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435212, "array": false, "paramDesc": "税额", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 23, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444825, "name": "currency_moneyDigit", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435213, "array": false, "paramDesc": "币种金额精度", "paramType": "int", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 24, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2081374351925444826, "name": "billqty", "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "parentId": 2081374351925444800, "defParamId": 1856790202779435214, "array": false, "paramDesc": "累计开票数量", "paramType": "double", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 25, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1856790202779435188, "array": false, "paramDesc": "合计对象", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "returnFormatType": "JSON", "paramConstDTOS": "", "paramConstMapDTOS": "", "apiDemoReturnDTOS": {"apiDemoReturnDTOS": [{"id": 2081374351925444833, "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"pubts\": \"\", \"recordList\": [ { \"vouchdate\": \"\", \"code\": \"\", \"bustype_name\": \"\", \"vendor_name\": \"\", \"warehouse_name\": \"\", \"vendor_code\": \"\", \"warehouse_code\": \"\", \"stockMgr_name\": \"\", \"status\": 0, \"purchaseOrg_name\": \"\", \"department_name\": \"\", \"org_code\": \"\", \"org_name\": \"\", \"department_code\": \"\", \"operator_name\": \"\", \"totalQuantity\": 0, \"totalPieces\": 0, \"inInvoiceOrg_name\": \"\", \"inInvoiceOrg\": \"\", \"accountOrg\": \"\", \"isBeginning\": true, \"bustype\": \"\", \"vendor\": 0, \"contact\": \"\", \"warehouse\": 0, \"operator\": 0, \"purchaseOrg\": \"\", \"org\": \"\", \"department\": \"\", \"stockMgr\": \"\", \"moneysum\": 0, \"paymentsum\": 0, \"unpaymentsum\": 0, \"store\": \"\", \"store_name\": \"\", \"custom\": 0, \"payor\": \"\", \"payor_name\": \"\", \"paytime\": \"\", \"paymentstatus\": \"\", \"creator\": \"\", \"createTime\": \"\", \"modifier\": \"\", \"modifyTime\": \"\", \"auditor\": \"\", \"auditTime\": \"\", \"memo\": \"\", \"id\": \"\", \"srcBill\": \"\", \"pubts\": \"\", \"tplid\": 0, \"exchangestatus\": \"\", \"purInRecords_id\": \"\", \"product\": \"\", \"product_cCode\": \"\", \"product_cName\": \"\", \"productsku\": \"\", \"tradeRoute_name\": \"\", \"productsku_cCode\": \"\", \"productsku_cName\": \"\", \"productClass_code\": \"\", \"propertiesValue\": \"\", \"batchno\": \"\", \"invaliddate\": \"\", \"producedate\": \"\", \"unit\": \"\", \"qty\": 0, \"unit_code\": \"\", \"unit_name\": \"\", \"subQty\": 0, \"stockUnit_name\": \"\", \"project_code\": \"\", \"project_name\": \"\", \"oriUnitPrice\": 0, \"oriTaxUnitPrice\": 0, \"oriMoney\": 0, \"oriSum\": 0, \"oriTax\": 0, \"taxRate\": 0, \"billqty\": 0, \"billSubQty\": 0, \"sqty\": 0, \"smoney\": 0, \"sfee\": 0, \"totalBillOriSum\": 0, \"priceUOM\": 0, \"priceUOM_Code\": \"\", \"priceUOM_Name\": \"\", \"natCurrency_priceDigit\": 0, \"natCurrency_moneyDigit\": 0, \"currency_priceDigit\": 0, \"currency_moneyDigit\": 0, \"unit_Precision\": 0, \"priceUOM_Precision\": 0, \"stockUnitId_Precision\": 0, \"isGiftProduct\": true, \"bmake_st_purinvoice_red\": \"\", \"bmake_st_purinvoice\": \"\", \"bizFlow\": \"\", \"bizFlow_version\": \"\", \"isFlowCoreBill\": \"\", \"purInRecordDefineCharacter\": 0, \"purInRecordsDefineCharacter\": 0, \"purInRecordsCharacteristics\": 0, \"out_sys_id\": \"\", \"out_sys_code\": \"\", \"out_sys_version\": \"\", \"out_sys_type\": \"\", \"out_sys_rowno\": \"\", \"out_sys_lineid\": \"\", \"tradeRouteID\": 0, \"isEndTrade\": 0, \"tradeRouteLineno\": \"\", \"collaborationPolineno\": \"\", \"coSourceType\": \"\", \"coUpcode\": \"\", \"coSourceLineNo\": \"\", \"coSourceid\": \"\", \"coSourceautoid\": 0, \"collaborationPodetailid\": 0, \"collaborationPocode\": \"\", \"collaborationPoid\": 0, \"collaborationSource\": \"\", \"totalOutStockQuantity\": 0 } ] }, \"sumRecordList\": { \"totalQuantity\": 0, \"totalPieces\": 0, \"subQty\": 0, \"currency\": \"\", \"paymentsum\": 0, \"sfee\": 0, \"unit_Precision\": 0, \"currency_priceDigit\": 0, \"priceUOM_Precision\": 0, \"moneysum\": 0, \"oriMoney\": 0, \"oriSum\": 0, \"stockUnitId_Precision\": 0, \"qty\": 0, \"purInRecords_unit\": 0, \"natCurrency\": \"\", \"natCurrency_moneyDigit\": 0, \"unpaymentsum\": 0, \"smoney\": 0, \"sqty\": 0, \"natCurrency_priceDigit\": 0, \"purInRecords_stockUnitId\": \"\", \"purInRecords_priceUOM\": 0, \"oriTax\": 0, \"currency_moneyDigit\": 0, \"billqty\": 0 } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2081374351925444834, "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "content": "", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "apiDemoReturnDTOList": {"apiDemoReturnDTOList": [{"id": 2081374351925444833, "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"pubts\": \"\", \"recordList\": [ { \"vouchdate\": \"\", \"code\": \"\", \"bustype_name\": \"\", \"vendor_name\": \"\", \"warehouse_name\": \"\", \"vendor_code\": \"\", \"warehouse_code\": \"\", \"stockMgr_name\": \"\", \"status\": 0, \"purchaseOrg_name\": \"\", \"department_name\": \"\", \"org_code\": \"\", \"org_name\": \"\", \"department_code\": \"\", \"operator_name\": \"\", \"totalQuantity\": 0, \"totalPieces\": 0, \"inInvoiceOrg_name\": \"\", \"inInvoiceOrg\": \"\", \"accountOrg\": \"\", \"isBeginning\": true, \"bustype\": \"\", \"vendor\": 0, \"contact\": \"\", \"warehouse\": 0, \"operator\": 0, \"purchaseOrg\": \"\", \"org\": \"\", \"department\": \"\", \"stockMgr\": \"\", \"moneysum\": 0, \"paymentsum\": 0, \"unpaymentsum\": 0, \"store\": \"\", \"store_name\": \"\", \"custom\": 0, \"payor\": \"\", \"payor_name\": \"\", \"paytime\": \"\", \"paymentstatus\": \"\", \"creator\": \"\", \"createTime\": \"\", \"modifier\": \"\", \"modifyTime\": \"\", \"auditor\": \"\", \"auditTime\": \"\", \"memo\": \"\", \"id\": \"\", \"srcBill\": \"\", \"pubts\": \"\", \"tplid\": 0, \"exchangestatus\": \"\", \"purInRecords_id\": \"\", \"product\": \"\", \"product_cCode\": \"\", \"product_cName\": \"\", \"productsku\": \"\", \"tradeRoute_name\": \"\", \"productsku_cCode\": \"\", \"productsku_cName\": \"\", \"productClass_code\": \"\", \"propertiesValue\": \"\", \"batchno\": \"\", \"invaliddate\": \"\", \"producedate\": \"\", \"unit\": \"\", \"qty\": 0, \"unit_code\": \"\", \"unit_name\": \"\", \"subQty\": 0, \"stockUnit_name\": \"\", \"project_code\": \"\", \"project_name\": \"\", \"oriUnitPrice\": 0, \"oriTaxUnitPrice\": 0, \"oriMoney\": 0, \"oriSum\": 0, \"oriTax\": 0, \"taxRate\": 0, \"billqty\": 0, \"billSubQty\": 0, \"sqty\": 0, \"smoney\": 0, \"sfee\": 0, \"totalBillOriSum\": 0, \"priceUOM\": 0, \"priceUOM_Code\": \"\", \"priceUOM_Name\": \"\", \"natCurrency_priceDigit\": 0, \"natCurrency_moneyDigit\": 0, \"currency_priceDigit\": 0, \"currency_moneyDigit\": 0, \"unit_Precision\": 0, \"priceUOM_Precision\": 0, \"stockUnitId_Precision\": 0, \"isGiftProduct\": true, \"bmake_st_purinvoice_red\": \"\", \"bmake_st_purinvoice\": \"\", \"bizFlow\": \"\", \"bizFlow_version\": \"\", \"isFlowCoreBill\": \"\", \"purInRecordDefineCharacter\": 0, \"purInRecordsDefineCharacter\": 0, \"purInRecordsCharacteristics\": 0, \"out_sys_id\": \"\", \"out_sys_code\": \"\", \"out_sys_version\": \"\", \"out_sys_type\": \"\", \"out_sys_rowno\": \"\", \"out_sys_lineid\": \"\", \"tradeRouteID\": 0, \"isEndTrade\": 0, \"tradeRouteLineno\": \"\", \"collaborationPolineno\": \"\", \"coSourceType\": \"\", \"coUpcode\": \"\", \"coSourceLineNo\": \"\", \"coSourceid\": \"\", \"coSourceautoid\": 0, \"collaborationPodetailid\": 0, \"collaborationPocode\": \"\", \"collaborationPoid\": 0, \"collaborationSource\": \"\", \"totalOutStockQuantity\": 0 } ] }, \"sumRecordList\": { \"totalQuantity\": 0, \"totalPieces\": 0, \"subQty\": 0, \"currency\": \"\", \"paymentsum\": 0, \"sfee\": 0, \"unit_Precision\": 0, \"currency_priceDigit\": 0, \"priceUOM_Precision\": 0, \"moneysum\": 0, \"oriMoney\": 0, \"oriSum\": 0, \"stockUnitId_Precision\": 0, \"qty\": 0, \"purInRecords_unit\": 0, \"natCurrency\": \"\", \"natCurrency_moneyDigit\": 0, \"unpaymentsum\": 0, \"smoney\": 0, \"sqty\": 0, \"natCurrency_priceDigit\": 0, \"purInRecords_stockUnitId\": \"\", \"purInRecords_priceUOM\": 0, \"oriTax\": 0, \"currency_moneyDigit\": 0, \"billqty\": 0 } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2081374351925444834, "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "content": "", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "routingStgy": 0, "routingStgyList": "", "apiDemoReturnDTO": {"id": 2081374351925444833, "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"pageIndex\": 0, \"pageSize\": 0, \"pageCount\": 0, \"beginPageIndex\": 0, \"endPageIndex\": 0, \"recordCount\": 0, \"pubts\": \"\", \"recordList\": [ { \"vouchdate\": \"\", \"code\": \"\", \"bustype_name\": \"\", \"vendor_name\": \"\", \"warehouse_name\": \"\", \"vendor_code\": \"\", \"warehouse_code\": \"\", \"stockMgr_name\": \"\", \"status\": 0, \"purchaseOrg_name\": \"\", \"department_name\": \"\", \"org_code\": \"\", \"org_name\": \"\", \"department_code\": \"\", \"operator_name\": \"\", \"totalQuantity\": 0, \"totalPieces\": 0, \"inInvoiceOrg_name\": \"\", \"inInvoiceOrg\": \"\", \"accountOrg\": \"\", \"isBeginning\": true, \"bustype\": \"\", \"vendor\": 0, \"contact\": \"\", \"warehouse\": 0, \"operator\": 0, \"purchaseOrg\": \"\", \"org\": \"\", \"department\": \"\", \"stockMgr\": \"\", \"moneysum\": 0, \"paymentsum\": 0, \"unpaymentsum\": 0, \"store\": \"\", \"store_name\": \"\", \"custom\": 0, \"payor\": \"\", \"payor_name\": \"\", \"paytime\": \"\", \"paymentstatus\": \"\", \"creator\": \"\", \"createTime\": \"\", \"modifier\": \"\", \"modifyTime\": \"\", \"auditor\": \"\", \"auditTime\": \"\", \"memo\": \"\", \"id\": \"\", \"srcBill\": \"\", \"pubts\": \"\", \"tplid\": 0, \"exchangestatus\": \"\", \"purInRecords_id\": \"\", \"product\": \"\", \"product_cCode\": \"\", \"product_cName\": \"\", \"productsku\": \"\", \"tradeRoute_name\": \"\", \"productsku_cCode\": \"\", \"productsku_cName\": \"\", \"productClass_code\": \"\", \"propertiesValue\": \"\", \"batchno\": \"\", \"invaliddate\": \"\", \"producedate\": \"\", \"unit\": \"\", \"qty\": 0, \"unit_code\": \"\", \"unit_name\": \"\", \"subQty\": 0, \"stockUnit_name\": \"\", \"project_code\": \"\", \"project_name\": \"\", \"oriUnitPrice\": 0, \"oriTaxUnitPrice\": 0, \"oriMoney\": 0, \"oriSum\": 0, \"oriTax\": 0, \"taxRate\": 0, \"billqty\": 0, \"billSubQty\": 0, \"sqty\": 0, \"smoney\": 0, \"sfee\": 0, \"totalBillOriSum\": 0, \"priceUOM\": 0, \"priceUOM_Code\": \"\", \"priceUOM_Name\": \"\", \"natCurrency_priceDigit\": 0, \"natCurrency_moneyDigit\": 0, \"currency_priceDigit\": 0, \"currency_moneyDigit\": 0, \"unit_Precision\": 0, \"priceUOM_Precision\": 0, \"stockUnitId_Precision\": 0, \"isGiftProduct\": true, \"bmake_st_purinvoice_red\": \"\", \"bmake_st_purinvoice\": \"\", \"bizFlow\": \"\", \"bizFlow_version\": \"\", \"isFlowCoreBill\": \"\", \"purInRecordDefineCharacter\": 0, \"purInRecordsDefineCharacter\": 0, \"purInRecordsCharacteristics\": 0, \"out_sys_id\": \"\", \"out_sys_code\": \"\", \"out_sys_version\": \"\", \"out_sys_type\": \"\", \"out_sys_rowno\": \"\", \"out_sys_lineid\": \"\", \"tradeRouteID\": 0, \"isEndTrade\": 0, \"tradeRouteLineno\": \"\", \"collaborationPolineno\": \"\", \"coSourceType\": \"\", \"coUpcode\": \"\", \"coSourceLineNo\": \"\", \"coSourceid\": \"\", \"coSourceautoid\": 0, \"collaborationPodetailid\": 0, \"collaborationPocode\": \"\", \"collaborationPoid\": 0, \"collaborationSource\": \"\", \"totalOutStockQuantity\": 0 } ] }, \"sumRecordList\": { \"totalQuantity\": 0, \"totalPieces\": 0, \"subQty\": 0, \"currency\": \"\", \"paymentsum\": 0, \"sfee\": 0, \"unit_Precision\": 0, \"currency_priceDigit\": 0, \"priceUOM_Precision\": 0, \"moneysum\": 0, \"oriMoney\": 0, \"oriSum\": 0, \"stockUnitId_Precision\": 0, \"qty\": 0, \"purInRecords_unit\": 0, \"natCurrency\": \"\", \"natCurrency_moneyDigit\": 0, \"unpaymentsum\": 0, \"smoney\": 0, \"sqty\": 0, \"natCurrency_priceDigit\": 0, \"purInRecords_stockUnitId\": \"\", \"purInRecords_priceUOM\": 0, \"oriTax\": 0, \"currency_moneyDigit\": 0, \"billqty\": 0 } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, "apiDemoReturnDTOError": {"id": 2081374351925444834, "apiId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "content": "", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-09-05 10:37:45.000", "gmtUpdate": "2024-09-05 10:37:45.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}, "errorCodeDTOS": "", "displayCodeApiConfigDTOS": "", "tokenPlugin": "", "paramParsePlugin": "", "authPlugin": {"id": "09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "", "name": "友户通token认证业务扩展插件", "configurable": false, "description": "YonsuitBusinessExtendPlugin", "pluginType": "auth", "pluginTypeName": "业务扩展插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": false, "gmtCreate": "2020-05-22 00:00:00", "gmtUpdate": "2020-05-22 00:00:00", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "resultParsePlugin": {"id": "w181ed01-1e9b-4350-b994-71a66f062522", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "resultParse", "name": "UCG标准返回值解析插件", "configurable": false, "description": "符合UCG标准的返回值会自动解析，不符合的会自动略过", "pluginType": "resultParse", "pluginTypeName": "返回值解析插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.result.UCGResultParsePlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": true, "gmtCreate": "2019-08-19 00:00:00", "gmtUpdate": "", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "mapReturnPluginConfig": "", "billNo": "st_purinrecordlist", "domain": "ustock", "apiCategory": "", "docUrl": "", "pathMatch": 0, "createUser": "", "createUserName": "", "approvalStatus": 1, "publishTime": "2024-09-05 10:56:29", "pathJoin": true, "timeOut": 30, "tokenPluginName": "", "authPluginName": "", "resultPluginName": "", "apiDemoReturnRightDemo": "", "apiDemoReturnErrorDemo": "", "mock": false, "mockTimeout": "", "customUrl": "/purinrecord/list", "fixedUrl": "/yonbip/scm", "apiCode": "3f6ee57a6bcc435dad3c91b7d1a06dcd", "tokenCheckType": 0, "enableMulti": false, "multiField": "", "idempotent": "non", "bidirectionalSSL": "", "ucgSchema": "HTTPS", "updateUserId": "36a8b72b-d965-404d-a02d-66ff4a7afeb3", "updateUserName": "昵称-王章宇", "paramIsForce": "", "userIDPassthrough": false, "applyUser": "", "applyMsg": "", "dr": 0, "microServiceCode": "domain.yonbip-scm-stock", "applicationCode": "yonbip-scm-stock", "privacyCategory": 1, "privacyLevel": 4, "apiDesigned": 0, "serviceType": 0, "integrateSchemeCode": "", "integrateSchemeName": "", "integrateObjectCode": "", "integrateObjectName": "", "integrateObjectCreatedType": "", "returnIntegObjId": "", "returnIntegObjName": "", "apiIntegrateDTOList": "", "apiRouteInfoDTOList": "", "arrayParam": false, "fileSize": "", "cc": true, "paramTransferMode": 2, "ytenantId": 0, "statusConf": "", "scene": 1, "version": "", "bizObjUri": "", "bizObjOperationType": "", "apiDefId": 1856790202779435014, "paramExtBizObjCode": "", "paramExtBizObjName": "", "paramExtRequest": 1, "paramExtResponse": 1, "paramExtInExtendKey": 1, "openScene": 1, "integrationScene": "", "apiType": "", "paramMark": "", "integrateSysId": "", "integrateSysName": "", "integrateSysCode": "", "dataZoneSetting": false, "reqDataZoneSetting": false, "respDataZoneSetting": false, "reqDataAllQuery": false, "reqDataAllBody": false, "respDataAllBody": false, "chargeStatus": 1, "beforeSpeed": 60, "afterSpeed": 120, "speedStatus": false, "reqDataRefPath": "", "respDataRefPath": "", "pubHistory": "", "deprecated": 0, "recommendedApiId": "", "recommendedApiName": "", "domainAppCode": "", "multiVersion": 0, "apiTag": ""}}]