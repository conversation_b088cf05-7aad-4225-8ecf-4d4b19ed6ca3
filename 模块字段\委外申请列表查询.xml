<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>4474f0703e674ba3b3325f7133e4f990</id>
<name>委外申请单列表查询</name>
<apiClassifyId>192c27598b21412594f2c6e32b617803</apiClassifyId>
<apiClassifyName>委外申请单</apiClassifyName>
<apiClassifyCode>productionorder.po_subcontract_requisition_card</apiClassifyCode>
<parentApiClassifies/>
<functionId/>
<openMode>0</openMode>
<description>提供委外申请单列表查询接口，可以通过接口按列表查询委外申请单</description>
<auth>true</auth>
<bodyPassthrough>false</bodyPassthrough>
<healthExam>false</healthExam>
<healthStatus>true</healthStatus>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/mfg/subcontractrequisition/list</completeProxyUrl>
<connectUrl>/api/list</connectUrl>
<sort>20</sort>
<handler>openapi</handler>
<httpRequestType>POST</httpRequestType>
<openApi>true</openApi>
<preset>false</preset>
<productId>4a176d6a681a4ebdbd053262493b5dff</productId>
<productCode/>
<proxyUrl>/yonbip/mfg/subcontractrequisition/list</proxyUrl>
<requestParamsDemo>Url: /yonbip/mfg/subcontractrequisition/list?access_token=访问令牌 Body: { "pageIndex": 1, "pageSize": 10, "orgId": [ 1866605942198527 ], "code": "WWSQ202105010001", "vouchdate": "2021-03-02|2021-03-02 23:59:59", "transTypeId": [ 1866605942198526 ], "status": 0, "materialId": [ 1866605942198785 ], "productId": [ 1866605942198650 ], "requisitionDate": "2021-03-02|2021-03-02 23:59:59", "departmentId": [ 186660594219847 ], "operatorId": [ 186660594218648 ], "simple": { "open_pubts_begin": "2022-01-01 00:00:00", "open_pubts_end": "2022-01-01 10:00:00" }, "simpleVOs": [ { "field": "", "op": "", "value1": "", "value2": "", "logicOp": "and", "conditions": [ { "field": "", "op": "", "value1": "", "value2": "" } ] } ] }</requestParamsDemo>
<requestProtocol>HTTP</requestProtocol>
<serviceHttpMethod>POST</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2021-12-28 09:44:24.000</gmtCreate>
<gmtUpdate>2024-05-31 13:56:07.000</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/mfg/subcontractrequisition/list</address>
<productName/>
<productClassifyId/>
<productClassifyCode/>
<productClassifyName/>
<paramDTOS>
<paramDTOS>
<id>2009482629538643985</id>
<name>pageIndex</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112522</defParamId>
<array>false</array>
<paramDesc>页号 默认值:1</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>1</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2009482629538643986</id>
<name>pageSize</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112523</defParamId>
<array>false</array>
<paramDesc>每页行数 默认值:10</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>10</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2009482629538643987</id>
<name>orgId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112524</defParamId>
<array>true</array>
<paramDesc>组织ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>["1866605942198527"]</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2009482629538643988</id>
<name>code</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112525</defParamId>
<array>false</array>
<paramDesc>委外申请单号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>WWSQ202105010001</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2009482629538643989</id>
<name>vouchdate</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112526</defParamId>
<array>false</array>
<paramDesc>单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2021-03-02|2021-03-02 23:59:59</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2009482629538643990</id>
<name>transTypeId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112527</defParamId>
<array>true</array>
<paramDesc>交易类型ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>["1866605942198526"]</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2009482629538643991</id>
<name>status</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112528</defParamId>
<array>false</array>
<paramDesc>申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。</paramDesc>
<paramType>short</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2009482629538643992</id>
<name>materialId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112529</defParamId>
<array>true</array>
<paramDesc>制造物料ID</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[1866605942198785]</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2009482629538643993</id>
<name>productId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112530</defParamId>
<array>true</array>
<paramDesc>物料ID</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>[1866605942198650]</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2009482629538643994</id>
<name>requisitionDate</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112531</defParamId>
<array>false</array>
<paramDesc>需求日期（区间，格式2021-03-02|2021-03-02 23:59:59）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2021-03-02|2021-03-02 23:59:59</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2009482629538643995</id>
<name>departmentId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112532</defParamId>
<array>true</array>
<paramDesc>需求部门ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>["186660594219847"]</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2009482629538643996</id>
<name>operatorId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112533</defParamId>
<array>true</array>
<paramDesc>需求人ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>["186660594218648"]</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2009482629538643971</id>
<name>simple</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<children>
<children>
<id>2009482629538643972</id>
<name>open_pubts_begin</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643971</parentId>
<defParamId>2009368443538112535</defParamId>
<array>false</array>
<paramDesc>时间戳，开始时间</paramDesc>
<paramType>date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-01-01 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format>yyyy-MM-dd HH:mm:ss</format>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2009482629538643973</id>
<name>open_pubts_end</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643971</parentId>
<defParamId>2009368443538112536</defParamId>
<array>false</array>
<paramDesc>时间戳，结束时间</paramDesc>
<paramType>date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>2022-01-01 10:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format>yyyy-MM-dd HH:mm:ss</format>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>2009368443538112534</defParamId>
<array>false</array>
<paramDesc>扩展参数</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2009482629538643974</id>
<name>simpleVOs</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<children>
<children>
<id>2009482629538643980</id>
<name>field</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643974</parentId>
<defParamId>2009368443538112538</defParamId>
<array>false</array>
<paramDesc>属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2009482629538643981</id>
<name>op</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643974</parentId>
<defParamId>2009368443538112539</defParamId>
<array>false</array>
<paramDesc>比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2009482629538643982</id>
<name>value1</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643974</parentId>
<defParamId>2009368443538112540</defParamId>
<array>false</array>
<paramDesc>查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2009482629538643983</id>
<name>value2</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643974</parentId>
<defParamId>2009368443538112541</defParamId>
<array>false</array>
<paramDesc>查询条件值2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2009482629538643984</id>
<name>logicOp</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643974</parentId>
<defParamId>2009368443538112542</defParamId>
<array>false</array>
<paramDesc>逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>and</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2009482629538643975</id>
<name>conditions</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643974</parentId>
<children>
<children>
<id>2009482629538643976</id>
<name>field</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643975</parentId>
<defParamId>2009368443538112544</defParamId>
<array>false</array>
<paramDesc>属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2009482629538643977</id>
<name>op</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643975</parentId>
<defParamId>2009368443538112545</defParamId>
<array>false</array>
<paramDesc>逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2009482629538643978</id>
<name>value1</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643975</parentId>
<defParamId>2009368443538112546</defParamId>
<array>false</array>
<paramDesc>查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2009482629538643979</id>
<name>value2</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643975</parentId>
<defParamId>2009368443538112547</defParamId>
<array>false</array>
<paramDesc>查询条件值2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>2009368443538112543</defParamId>
<array>true</array>
<paramDesc>下级查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>2009368443538112537</defParamId>
<array>true</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>2009482629538644011</id>
<name>pageIndex</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>页号 默认值:1</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageIndex</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2009482629538644012</id>
<name>pageSize</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>每页行数 默认值:10</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageSize</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2009482629538644013</id>
<name>orgId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>组织ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>orgId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2009482629538644014</id>
<name>code</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>委外申请单号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>code</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2009482629538644015</id>
<name>vouchdate</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>vouchdate</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2009482629538644016</id>
<name>transTypeId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>交易类型ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>transTypeId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2009482629538644017</id>
<name>status</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。</paramDesc>
<paramType>short</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>status</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>short</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2009482629538644018</id>
<name>materialId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>制造物料ID</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>materialId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2009482629538644019</id>
<name>productId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料ID</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>productId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>long</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2009482629538644020</id>
<name>requisitionDate</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求日期（区间，格式2021-03-02|2021-03-02 23:59:59）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>requisitionDate</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2009482629538644021</id>
<name>departmentId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求部门ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>departmentId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2009482629538644022</id>
<name>operatorId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求人ID</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>operatorId</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2009482629538643997</id>
<name>simple</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<children>
<children>
<id>2009482629538643998</id>
<name>open_pubts_begin</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643997</parentId>
<defParamId/>
<array>false</array>
<paramDesc>时间戳，开始时间</paramDesc>
<paramType>date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_pubts_begin</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>date</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538643999</id>
<name>open_pubts_end</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538643997</parentId>
<defParamId/>
<array>false</array>
<paramDesc>时间戳，结束时间</paramDesc>
<paramType>date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>open_pubts_end</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>date</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>扩展参数</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>simple</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2009482629538644000</id>
<name>simpleVOs</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<children>
<children>
<id>2009482629538644006</id>
<name>field</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644000</parentId>
<defParamId/>
<array>false</array>
<paramDesc>属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644007</id>
<name>op</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644000</parentId>
<defParamId/>
<array>false</array>
<paramDesc>比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>op</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644008</id>
<name>value1</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644000</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value1</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644009</id>
<name>value2</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644000</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value2</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644010</id>
<name>logicOp</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644000</parentId>
<defParamId/>
<array>false</array>
<paramDesc>逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>logicOp</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644001</id>
<name>conditions</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644000</parentId>
<children>
<children>
<id>2009482629538644002</id>
<name>field</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644001</parentId>
<defParamId/>
<array>false</array>
<paramDesc>属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644003</id>
<name>op</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644001</parentId>
<defParamId/>
<array>false</array>
<paramDesc>逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>op</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644004</id>
<name>value1</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644001</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value1</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644005</id>
<name>value2</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644001</parentId>
<defParamId/>
<array>false</array>
<paramDesc>查询条件值2</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value2</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>下级查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>conditions</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>simpleVOs</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>2009482629538644076</id>
<name>code</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112574</defParamId>
<array>false</array>
<paramDesc>返回码，成功时返回200</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>200</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2009482629538644077</id>
<name>message</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<defParamId>2009368443538112575</defParamId>
<array>false</array>
<paramDesc>接口返回信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>操作成功</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2009482629538644023</id>
<name>data</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId/>
<children>
<children>
<id>2009482629538644070</id>
<name>pageIndex</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644023</parentId>
<defParamId>2009368443538112577</defParamId>
<array>false</array>
<paramDesc>当前页</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644071</id>
<name>pageSize</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644023</parentId>
<defParamId>2009368443538112578</defParamId>
<array>false</array>
<paramDesc>页大小</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>20</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644072</id>
<name>recordCount</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644023</parentId>
<defParamId>2009368443538112579</defParamId>
<array>false</array>
<paramDesc>记录总数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644024</id>
<name>recordList</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644023</parentId>
<children>
<children>
<id>2009482629538644048</id>
<name>id</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112581</defParamId>
<array>false</array>
<paramDesc>单据ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644049</id>
<name>orgId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112582</defParamId>
<array>false</array>
<paramDesc>需求组织ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644050</id>
<name>orgName</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112583</defParamId>
<array>false</array>
<paramDesc>需求组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>资产管理公司</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644051</id>
<name>code</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112584</defParamId>
<array>false</array>
<paramDesc>委外申请单号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>WWSQ202112230004</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644052</id>
<name>vouchdate</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112585</defParamId>
<array>false</array>
<paramDesc>单据日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-12-23 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644053</id>
<name>status</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112586</defParamId>
<array>false</array>
<paramDesc>申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。</paramDesc>
<paramType>short</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644054</id>
<name>transTypeId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112587</defParamId>
<array>false</array>
<paramDesc>交易类型ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644055</id>
<name>transTypeName</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112588</defParamId>
<array>false</array>
<paramDesc>交易类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>标准委外</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644056</id>
<name>transTypeExtendAttrsJson</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112589</defParamId>
<array>false</array>
<paramDesc>交易类型扩展属性</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>{"specialType":"none","businessType":"wholeOutsourcing"}</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644057</id>
<name>departmentId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112590</defParamId>
<array>false</array>
<paramDesc>需求部门ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644058</id>
<name>departmentName</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112591</defParamId>
<array>false</array>
<paramDesc>需求部门</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>仓储部</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644059</id>
<name>operatorId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112592</defParamId>
<array>false</array>
<paramDesc>需求人ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644060</id>
<name>operatorName</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112593</defParamId>
<array>false</array>
<paramDesc>需求人名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>张三</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644061</id>
<name>sourceType</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112594</defParamId>
<array>false</array>
<paramDesc>来源类别。0：手工添加，1：计划订单。</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644063</id>
<name>isWfControlled</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112596</defParamId>
<array>false</array>
<paramDesc>是否审批流控制：false-否，true-是。</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>false</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644064</id>
<name>verifystate</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112597</defParamId>
<array>false</array>
<paramDesc>审批状态：0-开立，1-已提交，2-已审批，-1-驳回</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644065</id>
<name>pubts</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<defParamId>2009368443538112598</defParamId>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-12-23 20:14:33</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644025</id>
<name>subcontractRequisitionProduct</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644024</parentId>
<children>
<children>
<id>2009482629538644026</id>
<name>id</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112600</defParamId>
<array>false</array>
<paramDesc>主键</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644027</id>
<name>materialId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112601</defParamId>
<array>false</array>
<paramDesc>制造物料ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>1</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644028</id>
<name>productId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112602</defParamId>
<array>false</array>
<paramDesc>物料ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644029</id>
<name>outsourceOrgId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112603</defParamId>
<array>false</array>
<paramDesc>委外组织ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644030</id>
<name>rcvOrgId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112604</defParamId>
<array>false</array>
<paramDesc>收货组织ID</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644031</id>
<name>productCode</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112605</defParamId>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644032</id>
<name>productName</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112606</defParamId>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>P10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644033</id>
<name>demandQuantityDU</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112607</defParamId>
<array>false</array>
<paramDesc>需求件数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644034</id>
<name>demandUnitId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112608</defParamId>
<array>false</array>
<paramDesc>需求单位ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2325529461018880</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644035</id>
<name>demandUnitName</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112609</defParamId>
<array>false</array>
<paramDesc>需求单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>袋</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644036</id>
<name>demandUnitTruncationType</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112610</defParamId>
<array>false</array>
<paramDesc>需求单位舍位</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644037</id>
<name>demandUnitPrecision</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112611</defParamId>
<array>false</array>
<paramDesc>需求单位精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644038</id>
<name>demandQuantityMU</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112612</defParamId>
<array>false</array>
<paramDesc>需求数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644039</id>
<name>mainUnitId</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112613</defParamId>
<array>false</array>
<paramDesc>需求主单位ID</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2325529461018880</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644040</id>
<name>mainUnitName</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112614</defParamId>
<array>false</array>
<paramDesc>需求主计量单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>袋</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644041</id>
<name>mainUnitTruncationType</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112615</defParamId>
<array>false</array>
<paramDesc>需求主计量单位舍位</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644042</id>
<name>mainUnitPrecision</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112616</defParamId>
<array>false</array>
<paramDesc>需求主计量单位精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644043</id>
<name>requisitionDate</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112617</defParamId>
<array>false</array>
<paramDesc>需求日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2022-01-12 00:00:00</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644044</id>
<name>rcvOrgName</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112618</defParamId>
<array>false</array>
<paramDesc>收货组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>资产管理公司</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644045</id>
<name>outsourceOrgName</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644025</parentId>
<defParamId>2009368443538112619</defParamId>
<array>false</array>
<paramDesc>委外组织</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>资产管理公司</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2009368443538112599</defParamId>
<array>false</array>
<paramDesc>行信息</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2009368443538112580</defParamId>
<array>true</array>
<paramDesc>返回数据对象</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644066</id>
<name>sumRecordList</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644023</parentId>
<children>
<children>
<id>2009482629538644067</id>
<name>subcontractRequisitionProduct</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644066</parentId>
<children>
<children>
<id>2009482629538644068</id>
<name>demandQuantityDU</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644067</parentId>
<defParamId>2009368443538112624</defParamId>
<array>false</array>
<paramDesc>需求件数</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>5533.5333</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644069</id>
<name>demandQuantityMU</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644067</parentId>
<defParamId>2009368443538112625</defParamId>
<array>false</array>
<paramDesc>需求数量</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>6632.3</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2009368443538112623</defParamId>
<array>false</array>
<paramDesc>产品行数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2009368443538112622</defParamId>
<array>true</array>
<paramDesc>合计字段集合</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644073</id>
<name>pageCount</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644023</parentId>
<defParamId>2009368443538112626</defParamId>
<array>false</array>
<paramDesc>总页数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644074</id>
<name>beginPageIndex</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644023</parentId>
<defParamId>2009368443538112627</defParamId>
<array>false</array>
<paramDesc>开始页码</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2009482629538644075</id>
<name>endPageIndex</name>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<parentId>2009482629538644023</parentId>
<defParamId>2009368443538112628</defParamId>
<array>false</array>
<paramDesc>结束页码</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>2009368443538112576</defParamId>
<array>false</array>
<paramDesc>接口返回数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId>0</ytenantId>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2009482629538644086</id>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<content>{ "code": 200, "message": "操作成功", "data": { "pageIndex": 1, "pageSize": 20, "recordCount": 1, "recordList": [ { "id": ****************, "orgId": "****************", "orgName": "资产管理公司", "code": "WWSQ202112230004", "vouchdate": "2021-12-23 00:00:00", "status": 1, "transTypeId": "****************", "transTypeName": "标准委外", "transTypeExtendAttrsJson": "{specialType:none,businessType:wholeOutsourcing}", "departmentId": "****************", "departmentName": "仓储部", "operatorId": "****************", "operatorName": "张三", "sourceType": "1", "isWfControlled": false, "verifystate": 2, "pubts": "2021-12-23 20:14:33", "subcontractRequisitionProduct": { "id": "", "materialId": "****************", "productId": "****************", "outsourceOrgId": "****************", "rcvOrgId": "****************", "productCode": "", "productName": "P10", "demandQuantityDU": 10, "demandUnitId": 2325529461018880, "demandUnitName": "袋", "demandUnitTruncationType": 4, "demandUnitPrecision": 1, "demandQuantityMU": 10, "mainUnitId": 2325529461018880, "mainUnitName": "袋", "mainUnitTruncationType": 4, "mainUnitPrecision": 1, "requisitionDate": "2022-01-12 00:00:00", "rcvOrgName": "资产管理公司", "outsourceOrgName": "资产管理公司" } } ], "sumRecordList": [ { "subcontractRequisitionProduct": { "demandQuantityDU": 5533.5333, "demandQuantityMU": "6632.3" } } ], "pageCount": 1, "beginPageIndex": 1, "endPageIndex": 1 } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2009482629538644087</id>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<content>{ "code": "999", "message": "非法的时间： 11111" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2009482629538644086</id>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<content>{ "code": 200, "message": "操作成功", "data": { "pageIndex": 1, "pageSize": 20, "recordCount": 1, "recordList": [ { "id": ****************, "orgId": "****************", "orgName": "资产管理公司", "code": "WWSQ202112230004", "vouchdate": "2021-12-23 00:00:00", "status": 1, "transTypeId": "****************", "transTypeName": "标准委外", "transTypeExtendAttrsJson": "{specialType:none,businessType:wholeOutsourcing}", "departmentId": "****************", "departmentName": "仓储部", "operatorId": "****************", "operatorName": "张三", "sourceType": "1", "isWfControlled": false, "verifystate": 2, "pubts": "2021-12-23 20:14:33", "subcontractRequisitionProduct": { "id": "", "materialId": "****************", "productId": "****************", "outsourceOrgId": "****************", "rcvOrgId": "****************", "productCode": "", "productName": "P10", "demandQuantityDU": 10, "demandUnitId": 2325529461018880, "demandUnitName": "袋", "demandUnitTruncationType": 4, "demandUnitPrecision": 1, "demandQuantityMU": 10, "mainUnitId": 2325529461018880, "mainUnitName": "袋", "mainUnitTruncationType": 4, "mainUnitPrecision": 1, "requisitionDate": "2022-01-12 00:00:00", "rcvOrgName": "资产管理公司", "outsourceOrgName": "资产管理公司" } } ], "sumRecordList": [ { "subcontractRequisitionProduct": { "demandQuantityDU": 5533.5333, "demandQuantityMU": "6632.3" } } ], "pageCount": 1, "beginPageIndex": 1, "endPageIndex": 1 } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2009482629538644087</id>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<content>{ "code": "999", "message": "非法的时间： 11111" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>2009482629538644086</id>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<content>{ "code": 200, "message": "操作成功", "data": { "pageIndex": 1, "pageSize": 20, "recordCount": 1, "recordList": [ { "id": ****************, "orgId": "****************", "orgName": "资产管理公司", "code": "WWSQ202112230004", "vouchdate": "2021-12-23 00:00:00", "status": 1, "transTypeId": "****************", "transTypeName": "标准委外", "transTypeExtendAttrsJson": "{specialType:none,businessType:wholeOutsourcing}", "departmentId": "****************", "departmentName": "仓储部", "operatorId": "****************", "operatorName": "张三", "sourceType": "1", "isWfControlled": false, "verifystate": 2, "pubts": "2021-12-23 20:14:33", "subcontractRequisitionProduct": { "id": "", "materialId": "****************", "productId": "****************", "outsourceOrgId": "****************", "rcvOrgId": "****************", "productCode": "", "productName": "P10", "demandQuantityDU": 10, "demandUnitId": 2325529461018880, "demandUnitName": "袋", "demandUnitTruncationType": 4, "demandUnitPrecision": 1, "demandQuantityMU": 10, "mainUnitId": 2325529461018880, "mainUnitName": "袋", "mainUnitTruncationType": 4, "mainUnitPrecision": 1, "requisitionDate": "2022-01-12 00:00:00", "rcvOrgName": "资产管理公司", "outsourceOrgName": "资产管理公司" } } ], "sumRecordList": [ { "subcontractRequisitionProduct": { "demandQuantityDU": 5533.5333, "demandQuantityMU": "6632.3" } } ], "pageCount": 1, "beginPageIndex": 1, "endPageIndex": 1 } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>2009482629538644087</id>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<content>{ "code": "999", "message": "非法的时间： 11111" }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId>0</ytenantId>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS>
<errorCodeDTOS>
<id>2009482629538644083</id>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<errorCode>999</errorCode>
<errorMessage>取决于错误类型，不同错误信息不同</errorMessage>
<errorType>API</errorType>
<errorcodeDesc/>
<gmtCreate>2024-05-31 13:49:27.000</gmtCreate>
<gmtUpdate>2024-05-31 13:49:27.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<defErrorId>2009368443538112684</defErrorId>
<ytenantId>0</ytenantId>
<displayCodeId/>
</errorCodeDTOS>
</errorCodeDTOS>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>友户通token认证业务扩展插件</name>
<configurable>false</configurable>
<description>YonsuitBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>4474f0703e674ba3b3325f7133e4f990</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin>
<id>w181ed01-1e9b-4350-b994-71a66f017555</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code>resultParse</code>
<name>返回参数转换插件</name>
<configurable>false</configurable>
<description>解决返回值中带！的，转换为json</description>
<pluginType>resultParse</pluginType>
<pluginTypeName>返回值解析插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.result.ResultMapTransferParsePlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>true</visible>
<gmtCreate>2020-07-29 00:00:00</gmtCreate>
<gmtUpdate/>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>4474f0703e674ba3b3325f7133e4f990</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</resultParsePlugin>
<mapReturnPluginConfig/>
<billNo/>
<domain/>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser>acbbf6ea-0b66-481c-b6dc-204a76346adf</createUser>
<createUserName/>
<approvalStatus>1</approvalStatus>
<publishTime>2025-06-24 20:53:16</publishTime>
<pathJoin>true</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout/>
<customUrl>subcontractrequisition/list</customUrl>
<fixedUrl>/yonbip/mfg/</fixedUrl>
<apiCode>4474f0703e674ba3b3325f7133e4f990</apiCode>
<tokenCheckType>0</tokenCheckType>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL/>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>acbbf6ea-0b66-481c-b6dc-204a76346adf</updateUserId>
<updateUserName>昵某-13662080373</updateUserName>
<paramIsForce>true</paramIsForce>
<userIDPassthrough>true</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.yonbip-mm-mfpo</microServiceCode>
<applicationCode>yonbip-mm-mfpo</applicationCode>
<privacyCategory>1</privacyCategory>
<privacyLevel>1</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType>0</serviceType>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType/>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>false</arrayParam>
<fileSize/>
<cc>true</cc>
<paramTransferMode>2</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId>2009368443538112520</apiDefId>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>1</paramExtRequest>
<paramExtResponse>1</paramExtResponse>
<paramExtInExtendKey>1</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene/>
<apiType/>
<paramMark/>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>false</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>false</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>1</chargeStatus>
<beforeSpeed>40</beforeSpeed>
<afterSpeed>80</afterSpeed>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath/>
<pubHistory>
<pubHistory>
<id>2298405328530702345</id>
<apiId>4474f0703e674ba3b3325f7133e4f990</apiId>
<apiName>委外申请单列表查询</apiName>
<applyReason/>
<publishUserName/>
<version>20250624205316</version>
<operationTime>2025-06-24</operationTime>
<gmtCreate/>
<gmtUpdate/>
<changes>
<changes>
<changePosition>baseInfo</changePosition>
<newList/>
<updateList>
<updateList>
<changeProperty>enableMulti</changeProperty>
<oldValue/>
<newValue>false</newValue>
</updateList>
</updateList>
<deleteList/>
</changes>
<changes>
<changePosition>paramDTOS</changePosition>
<newList/>
<updateList>
<updateList>
<changeProperty>pageIndex</changeProperty>
<oldValue>{"id":"2046156179320602817","name":"pageIndex","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602639","array":false,"paramDesc":"页号 默认值:1","paramType":"int","requestParamType":"BodyParam","path":"BodyParam_pageIndex","example":"1","fullName":"","required":true,"defaultValue":"1","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2009482629538643985","name":"pageIndex","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112522","array":false,"paramDesc":"页号 默认值:1","paramType":"int","requestParamType":"BodyParam","path":"BodyParam_pageIndex","example":"1","fullName":"","ytenantId":"0","required":true,"defaultValue":"1","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>pageSize</changeProperty>
<oldValue>{"id":"2046156179320602818","name":"pageSize","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602640","array":false,"paramDesc":"每页行数 默认值:10","paramType":"int","requestParamType":"BodyParam","path":"BodyParam_pageSize","example":"10","fullName":"","required":true,"defaultValue":"10","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2009482629538643986","name":"pageSize","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112523","array":false,"paramDesc":"每页行数 默认值:10","paramType":"int","requestParamType":"BodyParam","path":"BodyParam_pageSize","example":"10","fullName":"","ytenantId":"0","required":true,"defaultValue":"10","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>orgId</changeProperty>
<oldValue>{"id":"2046156179320602819","name":"orgId","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602641","array":true,"paramDesc":"组织ID","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_orgId","example":"[\"1866605942198527\"]","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2009482629538643987","name":"orgId","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112524","array":true,"paramDesc":"组织ID","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_orgId","example":"[\"1866605942198527\"]","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>code</changeProperty>
<oldValue>{"id":"2046156179320602820","name":"code","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602642","array":false,"paramDesc":"委外申请单号","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_code","example":"WWSQ202105010001","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643988","name":"code","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112525","array":false,"paramDesc":"委外申请单号","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_code","example":"WWSQ202105010001","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>vouchdate</changeProperty>
<oldValue>{"id":"2046156179320602821","name":"vouchdate","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602643","array":false,"paramDesc":"单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_vouchdate","example":"2021-03-02|2021-03-02 23:59:59","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643989","name":"vouchdate","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112526","array":false,"paramDesc":"单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_vouchdate","example":"2021-03-02|2021-03-02 23:59:59","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>transTypeId</changeProperty>
<oldValue>{"id":"2046156179320602822","name":"transTypeId","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602644","array":true,"paramDesc":"交易类型ID","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_transTypeId","example":"[\"1866605942198526\"]","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2009482629538643990","name":"transTypeId","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112527","array":true,"paramDesc":"交易类型ID","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_transTypeId","example":"[\"1866605942198526\"]","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>status</changeProperty>
<oldValue>{"id":"2046156179320602823","name":"status","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602645","array":false,"paramDesc":"申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。","paramType":"short","requestParamType":"BodyParam","path":"BodyParam_status","example":"0","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2009482629538643991","name":"status","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112528","array":false,"paramDesc":"申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。","paramType":"short","requestParamType":"BodyParam","path":"BodyParam_status","example":"0","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>materialId</changeProperty>
<oldValue>{"id":"2046156179320602824","name":"materialId","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602646","array":true,"paramDesc":"制造物料ID","paramType":"long","requestParamType":"BodyParam","path":"BodyParam_materialId","example":"[1866605942198785]","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2009482629538643992","name":"materialId","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112529","array":true,"paramDesc":"制造物料ID","paramType":"long","requestParamType":"BodyParam","path":"BodyParam_materialId","example":"[1866605942198785]","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>productId</changeProperty>
<oldValue>{"id":"2046156179320602825","name":"productId","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602647","array":true,"paramDesc":"物料ID","paramType":"long","requestParamType":"BodyParam","path":"BodyParam_productId","example":"[1866605942198650]","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643993","name":"productId","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112530","array":true,"paramDesc":"物料ID","paramType":"long","requestParamType":"BodyParam","path":"BodyParam_productId","example":"[1866605942198650]","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>requisitionDate</changeProperty>
<oldValue>{"id":"2046156179320602826","name":"requisitionDate","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602648","array":false,"paramDesc":"需求日期（区间，格式2021-03-02|2021-03-02 23:59:59）","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_requisitionDate","example":"2021-03-02|2021-03-02 23:59:59","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643994","name":"requisitionDate","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112531","array":false,"paramDesc":"需求日期（区间，格式2021-03-02|2021-03-02 23:59:59）","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_requisitionDate","example":"2021-03-02|2021-03-02 23:59:59","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>departmentId</changeProperty>
<oldValue>{"id":"2046156179320602827","name":"departmentId","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602649","array":true,"paramDesc":"需求部门ID","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_departmentId","example":"[\"186660594219847\"]","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643995","name":"departmentId","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112532","array":true,"paramDesc":"需求部门ID","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_departmentId","example":"[\"186660594219847\"]","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>operatorId</changeProperty>
<oldValue>{"id":"2046156179320602828","name":"operatorId","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602650","array":true,"paramDesc":"需求人ID","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_operatorId","example":"[\"186660594218648\"]","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643996","name":"operatorId","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112533","array":true,"paramDesc":"需求人ID","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_operatorId","example":"[\"186660594218648\"]","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>simple</changeProperty>
<oldValue>{"id":"2046156179320602803","name":"simple","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602651","array":false,"paramDesc":"扩展参数","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_simple","example":"","required":false,"defaultValue":"","visible":true}</oldValue>
<newValue>{"id":"2009482629538643971","name":"simple","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112534","array":false,"paramDesc":"扩展参数","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_simple","example":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false}</newValue>
</updateList>
<updateList>
<changeProperty>open_pubts_begin</changeProperty>
<oldValue>{"id":"2046156179320602804","name":"open_pubts_begin","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2046156179320602803","defParamId":"2046156179320602652","array":false,"paramDesc":"时间戳，开始时间","paramType":"date","requestParamType":"BodyParam","path":"BodyParam_simple.open_pubts_begin","example":"2022-01-01 00:00:00","required":false,"defaultValue":"","visible":true,"format":"yyyy-MM-dd HH:mm:ss"}</oldValue>
<newValue>{"id":"2009482629538643972","name":"open_pubts_begin","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2009482629538643971","defParamId":"2009368443538112535","array":false,"paramDesc":"时间戳，开始时间","paramType":"date","requestParamType":"BodyParam","path":"BodyParam_simple.open_pubts_begin","example":"2022-01-01 00:00:00","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"format":"yyyy-MM-dd HH:mm:ss"}</newValue>
</updateList>
<updateList>
<changeProperty>open_pubts_end</changeProperty>
<oldValue>{"id":"2046156179320602805","name":"open_pubts_end","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2046156179320602803","defParamId":"2046156179320602653","array":false,"paramDesc":"时间戳，结束时间","paramType":"date","requestParamType":"BodyParam","path":"BodyParam_simple.open_pubts_end","example":"2022-01-01 10:00:00","required":false,"defaultValue":"","visible":true,"format":"yyyy-MM-dd HH:mm:ss"}</oldValue>
<newValue>{"id":"2009482629538643973","name":"open_pubts_end","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2009482629538643971","defParamId":"2009368443538112536","array":false,"paramDesc":"时间戳，结束时间","paramType":"date","requestParamType":"BodyParam","path":"BodyParam_simple.open_pubts_end","example":"2022-01-01 10:00:00","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"format":"yyyy-MM-dd HH:mm:ss"}</newValue>
</updateList>
<updateList>
<changeProperty>simpleVOs</changeProperty>
<oldValue>{"id":"2046156179320602806","name":"simpleVOs","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2046156179320602654","array":true,"paramDesc":"扩展查询条件","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_simpleVOs","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643974","name":"simpleVOs","apiId":"4474f0703e674ba3b3325f7133e4f990","defParamId":"2009368443538112537","array":true,"paramDesc":"扩展查询条件","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_simpleVOs","example":"","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>field</changeProperty>
<oldValue>{"id":"2046156179320602812","name":"field","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2046156179320602806","defParamId":"2046156179320602655","array":false,"paramDesc":"属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.field","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643980","name":"field","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2009482629538643974","defParamId":"2009368443538112538","array":false,"paramDesc":"属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.field","example":"","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>op</changeProperty>
<oldValue>{"id":"2046156179320602813","name":"op","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2046156179320602806","defParamId":"2046156179320602656","array":false,"paramDesc":"比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.op","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643981","name":"op","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2009482629538643974","defParamId":"2009368443538112539","array":false,"paramDesc":"比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.op","example":"","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>value1</changeProperty>
<oldValue>{"id":"2046156179320602814","name":"value1","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2046156179320602806","defParamId":"2046156179320602657","array":false,"paramDesc":"查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.value1","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643982","name":"value1","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2009482629538643974","defParamId":"2009368443538112540","array":false,"paramDesc":"查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.value1","example":"","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>value2</changeProperty>
<oldValue>{"id":"2046156179320602815","name":"value2","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2046156179320602806","defParamId":"2046156179320602658","array":false,"paramDesc":"查询条件值2","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.value2","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643983","name":"value2","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2009482629538643974","defParamId":"2009368443538112541","array":false,"paramDesc":"查询条件值2","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.value2","example":"","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>logicOp</changeProperty>
<oldValue>{"id":"2046156179320602816","name":"logicOp","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2046156179320602806","defParamId":"2046156179320602659","array":false,"paramDesc":"逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.logicOp","example":"and","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643984","name":"logicOp","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2009482629538643974","defParamId":"2009368443538112542","array":false,"paramDesc":"逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.logicOp","example":"and","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>conditions</changeProperty>
<oldValue>{"id":"2046156179320602807","name":"conditions","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2046156179320602806","defParamId":"2046156179320602660","array":true,"paramDesc":"下级查询条件","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.conditions","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643975","name":"conditions","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2009482629538643974","defParamId":"2009368443538112543","array":true,"paramDesc":"下级查询条件","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.conditions","example":"","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>field</changeProperty>
<oldValue>{"id":"2046156179320602808","name":"field","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2046156179320602807","defParamId":"2046156179320602661","array":false,"paramDesc":"属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.conditions.field","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643976","name":"field","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2009482629538643975","defParamId":"2009368443538112544","array":false,"paramDesc":"属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.conditions.field","example":"","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>op</changeProperty>
<oldValue>{"id":"2046156179320602809","name":"op","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2046156179320602807","defParamId":"2046156179320602662","array":false,"paramDesc":"逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.conditions.op","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643977","name":"op","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2009482629538643975","defParamId":"2009368443538112545","array":false,"paramDesc":"逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.conditions.op","example":"","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>value1</changeProperty>
<oldValue>{"id":"2046156179320602810","name":"value1","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2046156179320602807","defParamId":"2046156179320602663","array":false,"paramDesc":"查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.conditions.value1","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643978","name":"value1","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2009482629538643975","defParamId":"2009368443538112546","array":false,"paramDesc":"查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.conditions.value1","example":"","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>value2</changeProperty>
<oldValue>{"id":"2046156179320602811","name":"value2","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2046156179320602807","defParamId":"2046156179320602664","array":false,"paramDesc":"查询条件值2","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.conditions.value2","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2009482629538643979","name":"value2","apiId":"4474f0703e674ba3b3325f7133e4f990","parentId":"2009482629538643975","defParamId":"2009368443538112547","array":false,"paramDesc":"查询条件值2","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.conditions.value2","example":"","fullName":"","ytenantId":"0","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
</updateList>
<deleteList/>
</changes>
</changes>
</pubHistory>
</pubHistory>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode>productionorder.po_subcontract_requisition_card</domainAppCode>
<multiVersion>0</multiVersion>
<apiTag/>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>816bd168-7b09-42fe-9074-3653bd6172ce</id>
<name>U9005</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>U9委外申请单号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:33:14</gmtCreate>
<gmtUpdate>2025-07-26 17:33:14</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>100</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>c751949a-eaf1-4f78-9599-3b09ff0762ad</id>
<name>WWSQ01</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>客户</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName>aa.merchant.Merchant</fullName>
<ytenantId/>
<paramOrder/>
<bizType>quote</bizType>
<baseType>false</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:33:14</gmtCreate>
<gmtUpdate>2025-07-26 17:33:14</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>true</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:33:14</gmtCreate>
<gmtUpdate>2025-07-26 17:33:14</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>8e30ab16-f7e7-4c01-958e-4bf2affbd79e</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>long</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>long</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:33:21</gmtCreate>
<gmtUpdate>2025-07-26 17:33:21</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>4e549813-f0b8-4d2e-8dbf-f31810aca911</id>
<name>WW0555</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>委外申请单参考需求日期</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>date</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:33:44</gmtCreate>
<gmtUpdate>2025-07-26 17:33:44</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>f1151657-7698-4761-93c4-76b5a4c1b711</id>
<name>XS11</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类号test</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:33:44</gmtCreate>
<gmtUpdate>2025-07-26 17:33:44</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>959cc247-5968-4914-bd90-4c56c3751ffe</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:33:44</gmtCreate>
<gmtUpdate>2025-07-26 17:33:44</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:33:44</gmtCreate>
<gmtUpdate>2025-07-26 17:33:44</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>72113971-ae4c-4188-bc55-44b6173f4e0b</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:33:55</gmtCreate>
<gmtUpdate>2025-07-26 17:33:55</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>b946709d-f4d9-4a43-a551-f55beee7f3d5</id>
<name>XXX0111</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类项</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:33:55</gmtCreate>
<gmtUpdate>2025-07-26 17:33:55</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:33:55</gmtCreate>
<gmtUpdate>2025-07-26 17:33:55</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>