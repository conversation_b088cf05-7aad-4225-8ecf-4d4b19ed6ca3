#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 事务处理优化器
实现智能事务管理、批量操作优化和死锁检测
"""

import threading
import time
import queue
from contextlib import contextmanager
from typing import Dict, Any, List, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import structlog
from concurrent.futures import ThreadPoolExecutor, as_completed
import sqlite3
import pymssql

logger = structlog.get_logger()


class TransactionLevel(Enum):
    """事务隔离级别"""
    READ_UNCOMMITTED = "READ UNCOMMITTED"
    READ_COMMITTED = "READ COMMITTED"
    REPEATABLE_READ = "REPEATABLE READ"
    SERIALIZABLE = "SERIALIZABLE"


class OperationType(Enum):
    """操作类型"""
    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    SELECT = "SELECT"


@dataclass
class TransactionOperation:
    """事务操作"""
    operation_type: OperationType
    table_name: str
    sql: str
    params: tuple = ()
    priority: int = 1  # 1=最高, 5=最低
    timeout: int = 30
    retry_count: int = 0
    max_retries: int = 3


@dataclass
class BatchOperation:
    """批量操作"""
    operations: List[TransactionOperation] = field(default_factory=list)
    batch_size: int = 1000
    parallel_threads: int = 4
    isolation_level: TransactionLevel = TransactionLevel.READ_COMMITTED


@dataclass
class TransactionStats:
    """事务统计"""
    total_transactions: int = 0
    successful_transactions: int = 0
    failed_transactions: int = 0
    deadlock_count: int = 0
    rollback_count: int = 0
    avg_transaction_time: float = 0.0
    batch_operations: int = 0
    records_processed: int = 0


class TransactionManager:
    """事务管理器"""
    
    def __init__(self, connection_manager):
        self.connection_manager = connection_manager
        self.stats = TransactionStats()
        self._lock = threading.RLock()
        self._transaction_locks: Dict[str, threading.Lock] = {}
        self._deadlock_detector = DeadlockDetector()
        
        # 事务队列
        self._high_priority_queue = queue.PriorityQueue()
        self._normal_priority_queue = queue.Queue()
        
        # 线程池
        self._executor = ThreadPoolExecutor(max_workers=8, thread_name_prefix="TransactionWorker")
        
        logger.info("事务管理器初始化完成")
    
    @contextmanager
    def transaction(self, db_type: str = 'sqlite', 
                   isolation_level: TransactionLevel = TransactionLevel.READ_COMMITTED,
                   timeout: int = 30, auto_retry: bool = True):
        """事务上下文管理器"""
        start_time = time.time()
        transaction_id = f"txn_{int(start_time)}_{threading.current_thread().ident}"
        
        try:
            with self.connection_manager.get_connection(db_type) as conn:
                # 设置隔离级别
                self._set_isolation_level(conn, db_type, isolation_level)
                
                # 开始事务
                if db_type == 'sqlite':
                    conn.execute("BEGIN IMMEDIATE")
                else:  # SQL Server
                    conn.autocommit = False
                
                self._deadlock_detector.start_transaction(transaction_id, conn)
                
                try:
                    yield conn
                    
                    # 提交事务
                    conn.commit()
                    self.stats.successful_transactions += 1
                    
                    logger.debug("事务提交成功", 
                               transaction_id=transaction_id,
                               duration=time.time() - start_time)
                    
                except Exception as e:
                    # 回滚事务
                    conn.rollback()
                    self.stats.rollback_count += 1
                    
                    # 检查是否为死锁
                    if self._is_deadlock_error(e):
                        self.stats.deadlock_count += 1
                        if auto_retry:
                            logger.warning("检测到死锁，准备重试", 
                                         transaction_id=transaction_id, error=str(e))
                            raise DeadlockException(str(e))
                    
                    logger.error("事务回滚", 
                               transaction_id=transaction_id,
                               error=str(e))
                    raise
                
                finally:
                    self._deadlock_detector.end_transaction(transaction_id)
        
        except Exception as e:
            self.stats.failed_transactions += 1
            raise
        
        finally:
            # 更新统计信息
            self.stats.total_transactions += 1
            duration = time.time() - start_time
            self._update_avg_transaction_time(duration)
    
    def _set_isolation_level(self, conn, db_type: str, level: TransactionLevel):
        """设置事务隔离级别"""
        try:
            if db_type == 'sqlite':
                # SQLite 的隔离级别通过 PRAGMA 设置
                if level == TransactionLevel.SERIALIZABLE:
                    conn.execute("PRAGMA read_uncommitted = 0")
                elif level == TransactionLevel.READ_UNCOMMITTED:
                    conn.execute("PRAGMA read_uncommitted = 1")
            else:  # SQL Server
                level_map = {
                    TransactionLevel.READ_UNCOMMITTED: "READ UNCOMMITTED",
                    TransactionLevel.READ_COMMITTED: "READ COMMITTED",
                    TransactionLevel.REPEATABLE_READ: "REPEATABLE READ",
                    TransactionLevel.SERIALIZABLE: "SERIALIZABLE"
                }
                conn.execute(f"SET TRANSACTION ISOLATION LEVEL {level_map[level]}")
        except Exception as e:
            logger.warning("设置隔离级别失败", level=level.value, error=str(e))
    
    def _is_deadlock_error(self, error: Exception) -> bool:
        """检查是否为死锁错误"""
        error_str = str(error).lower()
        deadlock_indicators = [
            'deadlock',
            'database is locked',
            'transaction was deadlocked',
            'lock timeout',
            'circular dependency'
        ]
        return any(indicator in error_str for indicator in deadlock_indicators)
    
    def _update_avg_transaction_time(self, duration: float):
        """更新平均事务时间"""
        with self._lock:
            total = self.stats.total_transactions
            if total == 1:
                self.stats.avg_transaction_time = duration
            else:
                current_avg = self.stats.avg_transaction_time
                self.stats.avg_transaction_time = (current_avg * (total - 1) + duration) / total
    
    def execute_batch_operations(self, batch: BatchOperation, db_type: str = 'sqlite') -> Dict[str, Any]:
        """执行批量操作"""
        start_time = time.time()
        results = {
            'processed': 0,
            'errors': [],
            'duration': 0,
            'batches': []
        }
        
        try:
            # 按表名分组操作
            operations_by_table = self._group_operations_by_table(batch.operations)
            
            # 并行处理各表的批量操作
            futures = []
            for table_name, operations in operations_by_table.items():
                future = self._executor.submit(
                    self._process_table_batch,
                    table_name, operations, batch, db_type
                )
                futures.append(future)
            
            # 收集结果
            for future in as_completed(futures):
                try:
                    batch_result = future.result()
                    results['processed'] += batch_result['processed']
                    results['batches'].append(batch_result)
                except Exception as e:
                    results['errors'].append(str(e))
                    logger.error("批量操作失败", error=str(e))
            
            self.stats.batch_operations += 1
            self.stats.records_processed += results['processed']
            
        except Exception as e:
            logger.error("批量操作执行失败", error=str(e))
            results['errors'].append(str(e))
        
        finally:
            results['duration'] = time.time() - start_time
        
        return results
    
    def _group_operations_by_table(self, operations: List[TransactionOperation]) -> Dict[str, List[TransactionOperation]]:
        """按表名分组操作"""
        grouped = {}
        for op in operations:
            if op.table_name not in grouped:
                grouped[op.table_name] = []
            grouped[op.table_name].append(op)
        return grouped
    
    def _process_table_batch(self, table_name: str, operations: List[TransactionOperation], 
                           batch: BatchOperation, db_type: str) -> Dict[str, Any]:
        """处理单个表的批量操作"""
        result = {
            'table': table_name,
            'processed': 0,
            'errors': [],
            'chunks': 0
        }
        
        # 将操作分块
        chunks = self._chunk_operations(operations, batch.batch_size)
        
        for chunk in chunks:
            try:
                with self.transaction(db_type, batch.isolation_level) as conn:
                    for operation in chunk:
                        cursor = conn.cursor()
                        cursor.execute(operation.sql, operation.params)
                        result['processed'] += cursor.rowcount
                        cursor.close()
                    
                    result['chunks'] += 1
                    
            except Exception as e:
                error_msg = f"表 {table_name} 批量处理失败: {str(e)}"
                result['errors'].append(error_msg)
                logger.error("批量处理块失败", table=table_name, error=str(e))
        
        return result
    
    def _chunk_operations(self, operations: List[TransactionOperation], 
                         chunk_size: int) -> List[List[TransactionOperation]]:
        """将操作列表分块"""
        chunks = []
        for i in range(0, len(operations), chunk_size):
            chunks.append(operations[i:i + chunk_size])
        return chunks
    
    def optimize_bulk_insert(self, table_name: str, columns: List[str], 
                           data: List[tuple], db_type: str = 'sqlite') -> Dict[str, Any]:
        """优化的批量插入"""
        start_time = time.time()
        
        try:
            with self.transaction(db_type) as conn:
                if db_type == 'sqlite':
                    return self._sqlite_bulk_insert(conn, table_name, columns, data)
                else:
                    return self._sqlserver_bulk_insert(conn, table_name, columns, data)
                    
        except Exception as e:
            logger.error("批量插入失败", table=table_name, error=str(e))
            return {
                'success': False,
                'inserted': 0,
                'error': str(e),
                'duration': time.time() - start_time
            }
    
    def _sqlite_bulk_insert(self, conn, table_name: str, columns: List[str], 
                          data: List[tuple]) -> Dict[str, Any]:
        """SQLite批量插入优化"""
        placeholders = ','.join(['?' for _ in columns])
        column_str = ','.join(columns)
        sql = f"INSERT INTO {table_name} ({column_str}) VALUES ({placeholders})"
        
        cursor = conn.cursor()
        cursor.executemany(sql, data)
        inserted = cursor.rowcount
        cursor.close()
        
        return {
            'success': True,
            'inserted': inserted,
            'method': 'executemany'
        }
    
    def _sqlserver_bulk_insert(self, conn, table_name: str, columns: List[str], 
                             data: List[tuple]) -> Dict[str, Any]:
        """SQL Server批量插入优化"""
        # 使用批量插入
        placeholders = ','.join(['%s' for _ in columns])
        column_str = ','.join(columns)
        sql = f"INSERT INTO {table_name} ({column_str}) VALUES ({placeholders})"
        
        cursor = conn.cursor()
        cursor.executemany(sql, data)
        inserted = cursor.rowcount
        cursor.close()
        
        return {
            'success': True,
            'inserted': inserted,
            'method': 'executemany'
        }
    
    def get_transaction_stats(self) -> Dict[str, Any]:
        """获取事务统计信息"""
        with self._lock:
            return {
                'total_transactions': self.stats.total_transactions,
                'successful_transactions': self.stats.successful_transactions,
                'failed_transactions': self.stats.failed_transactions,
                'success_rate': (self.stats.successful_transactions / max(1, self.stats.total_transactions)) * 100,
                'deadlock_count': self.stats.deadlock_count,
                'rollback_count': self.stats.rollback_count,
                'avg_transaction_time': self.stats.avg_transaction_time,
                'batch_operations': self.stats.batch_operations,
                'records_processed': self.stats.records_processed,
                'deadlock_detector_stats': self._deadlock_detector.get_stats()
            }
    
    def close(self):
        """关闭事务管理器"""
        self._executor.shutdown(wait=True)
        logger.info("事务管理器已关闭")


class DeadlockDetector:
    """死锁检测器"""
    
    def __init__(self):
        self._active_transactions: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
        self.stats = {
            'deadlocks_detected': 0,
            'prevention_count': 0
        }
    
    def start_transaction(self, transaction_id: str, connection):
        """开始跟踪事务"""
        with self._lock:
            self._active_transactions[transaction_id] = {
                'connection': connection,
                'start_time': time.time(),
                'locks': set(),
                'thread_id': threading.current_thread().ident
            }
    
    def end_transaction(self, transaction_id: str):
        """结束跟踪事务"""
        with self._lock:
            if transaction_id in self._active_transactions:
                del self._active_transactions[transaction_id]
    
    def detect_potential_deadlock(self, transaction_id: str, resource: str) -> bool:
        """检测潜在死锁"""
        with self._lock:
            if transaction_id not in self._active_transactions:
                return False
            
            current_txn = self._active_transactions[transaction_id]
            current_txn['locks'].add(resource)
            
            # 简单的循环依赖检测
            for other_id, other_txn in self._active_transactions.items():
                if other_id != transaction_id:
                    if self._has_circular_dependency(transaction_id, other_id):
                        self.stats['deadlocks_detected'] += 1
                        logger.warning("检测到潜在死锁", 
                                     txn1=transaction_id, 
                                     txn2=other_id)
                        return True
            
            return False
    
    def _has_circular_dependency(self, txn1_id: str, txn2_id: str) -> bool:
        """检查两个事务间是否存在循环依赖"""
        txn1 = self._active_transactions.get(txn1_id)
        txn2 = self._active_transactions.get(txn2_id)
        
        if not txn1 or not txn2:
            return False
        
        # 简化的检测逻辑：如果两个事务锁定了相同的资源
        return bool(txn1['locks'] & txn2['locks'])
    
    def get_stats(self) -> Dict[str, Any]:
        """获取死锁检测统计"""
        with self._lock:
            return {
                'active_transactions': len(self._active_transactions),
                'deadlocks_detected': self.stats['deadlocks_detected'],
                'prevention_count': self.stats['prevention_count']
            }


class DeadlockException(Exception):
    """死锁异常"""
    pass


# 全局事务管理器
_transaction_manager: Optional[TransactionManager] = None
_manager_lock = threading.Lock()


def get_transaction_manager() -> TransactionManager:
    """获取全局事务管理器实例"""
    global _transaction_manager
    
    if _transaction_manager is None:
        with _manager_lock:
            if _transaction_manager is None:
                from .database_connection_pool import get_connection_manager
                _transaction_manager = TransactionManager(get_connection_manager())
    
    return _transaction_manager


def transaction(db_type: str = 'sqlite', 
               isolation_level: TransactionLevel = TransactionLevel.READ_COMMITTED,
               timeout: int = 30, auto_retry: bool = True):
    """便捷的事务装饰器"""
    manager = get_transaction_manager()
    return manager.transaction(db_type, isolation_level, timeout, auto_retry)


# 示例使用
if __name__ == "__main__":
    from database_connection_pool import DatabaseConnectionManager
    
    # 初始化连接管理器
    conn_manager = DatabaseConnectionManager()
    
    # 初始化事务管理器
    txn_manager = TransactionManager(conn_manager)
    
    try:
        # 示例：使用事务
        with txn_manager.transaction('sqlite') as conn:
            cursor = conn.cursor()
            cursor.execute("CREATE TABLE IF NOT EXISTS test (id INTEGER PRIMARY KEY, name TEXT)")
            cursor.execute("INSERT INTO test (name) VALUES (?)", ("测试数据",))
            cursor.close()
        
        # 示例：批量操作
        operations = [
            TransactionOperation(
                OperationType.INSERT,
                "test",
                "INSERT INTO test (name) VALUES (?)",
                (f"批量数据{i}",)
            ) for i in range(100)
        ]
        
        batch = BatchOperation(operations=operations, batch_size=50)
        result = txn_manager.execute_batch_operations(batch, 'sqlite')
        print(f"批量操作结果: {result}")
        
        # 获取统计信息
        stats = txn_manager.get_transaction_stats()
        print(f"事务统计: {stats}")
        
    finally:
        txn_manager.close()
        conn_manager.close_all()
