from datetime import datetime

"""
采购订单数据模式定义
"""


class PurchaseOrderItemSchema(BaseModel):
    """采购订单明细模式"""

    id: int
    material_id: int
    material_name: str
    quantity: float
    unit_price: float
    total_price: float
    unit: Optional[str] = None


class PurchaseOrderSchema(BaseModel):
    """采购订单模式"""

    id: int
    order_number: str
    supplier_id: int
    supplier_name: str
    order_date: Optional[datetime] = None
    delivery_date: Optional[datetime] = None
    total_amount: float = 0
    status: str = "draft"
    notes: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class PurchaseOrderResponse(PurchaseOrderSchema):
    """采购订单响应模式（包含明细）"""

    items: List[PurchaseOrderItemSchema] = []


class PurchaseOrderListResponse(BaseModel):
    """采购订单列表响应模式"""

    items: List[PurchaseOrderSchema]
    total: int
    page: int
    size: int
    pages: int
