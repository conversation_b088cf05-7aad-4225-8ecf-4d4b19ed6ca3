[{"success": true, "code": 200, "message": "", "data": {"fieldVersion": 20230210, "appCode": "", "tokenSet": false, "tokenDoc": "", "tenantId": 0, "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "id": "0f1453d26e6741faa95ace9533a61683", "name": "请购单列表查询", "apiClassifyId": 1936116693949480962, "apiClassifyName": "请购单", "apiClassifyCode": "", "parentApiClassifies": "", "functionId": "", "openMode": 0, "description": "根据交易类型、物料、表头模式还是表头明细模式、分页条件和自定义条件查询请购单列表数据信息", "auth": true, "bodyPassthrough": false, "healthExam": false, "healthStatus": true, "responseResultPassthrough": false, "contentType": "application/json", "returnPassthrough": "", "completeProxyUrl": "/yonbip/scm/applyorder/list", "connectUrl": "/bill/list", "sort": 20, "handler": "openapi", "httpRequestType": "POST", "openApi": true, "preset": false, "productId": "710a0be3edff4f9092e35f63fd3b9bae", "productCode": "scm", "proxyUrl": "/yonbip/scm/applyorder/list", "requestParamsDemo": "Url: /yonbip/scm/applyorder/list?access_token=访问令牌 Body: { \"bustype\": \"110000000000029\", \"product\": 1730491724599552, \"pageIndex\": 1, \"pageSize\": 10, \"isSum\": false, \"queryOrders\": [ { \"field\": \"id\", \"order\": \"asc\" } ], \"simpleVOs\": [ { \"field\": \"code\", \"op\": \"eq\", \"value1\": \"CGDD2010140000000003\" } ] }", "requestProtocol": "HTTP", "serviceHttpMethod": "POST", "publishStatus": true, "approvalMsg": "", "rpcAppName": "", "rpcServiceName": "", "rpcMethodName": "", "rpcServiceUrl": "", "ma": false, "gmtCreate": "2020-01-16 16:46:24", "gmtUpdate": "2024-10-08 16:33:43.000", "address": "https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/applyorder/list", "productName": "采购供应", "productClassifyId": "yonsuite", "productClassifyCode": "yonbip", "productClassifyName": "用友 YonBIP", "paramDTOS": {"paramDTOS": [{"id": 2106049437256122376, "name": "bustype", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "defParamId": 1996896485556355078, "array": false, "paramDesc": "交易类型id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": 110000000000029, "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2106049437256122377, "name": "product", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "defParamId": 1996896485556355079, "array": false, "paramDesc": "物料id", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": 1730491724599552, "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2106049437256122378, "name": "pageIndex", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "defParamId": 1996896485556355080, "array": false, "paramDesc": "页码", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2106049437256122379, "name": "pageSize", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "defParamId": 1996896485556355081, "array": false, "paramDesc": "每页条数", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": 10, "fullName": "", "ytenantId": "", "paramOrder": 3, "bizType": "", "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2106049437256122380, "name": "isSum", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "defParamId": 1996896485556355082, "array": false, "paramDesc": "是否按照表头查询：true:表头、false:表头+明细", "paramType": "boolean", "requestParamType": "BodyParam", "path": "", "example": false, "fullName": "", "ytenantId": "", "paramOrder": 4, "bizType": "", "baseType": true, "defaultValue": false, "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2106049437256122369, "name": "queryOrders", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "children": {"children": [{"id": 2106049437256122370, "name": "field", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122369, "defParamId": 1996896485556355084, "array": false, "paramDesc": "排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：applyOrders.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "id", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2106049437256122371, "name": "order", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122369, "defParamId": 1996896485556355085, "array": false, "paramDesc": "顺序：asc：正序、desc：倒序", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "asc", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "defParamId": 1996896485556355083, "array": true, "paramDesc": "排序字段", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2106049437256122372, "name": "simpleVOs", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "children": {"children": [{"id": 2106049437256122373, "name": "field", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122372, "defParamId": 1996896485556355087, "array": false, "paramDesc": "属性名(条件) ，1：status(单据状态：0:未审核、1:已审核、2:已关闭、3:审核中)、2：code(单据编号)、3：vouchdate(请购日期)、4：operator(请购员id)、5：org(需求组织id)、6；applyOrders.purchaseOrg(采购组织id)、7：applyOrders.vendor(建议供应商id)、 8：applyDept(请购部门id)、9：headItem.define1(单据头自定义项1)、10：applyOrders.adviseOrderDate(建议订货日期)、11：applyOrders.project(项目id)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "code", "fullName": "", "ytenantId": "", "paramOrder": 0, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2106049437256122374, "name": "op", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122372, "defParamId": 1996896485556355088, "array": false, "paramDesc": "比较符（条件）：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "eq", "fullName": "", "ytenantId": "", "paramOrder": 1, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}, {"id": 2106049437256122375, "name": "value1", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122372, "defParamId": 1996896485556355089, "array": false, "paramDesc": "值1（条件）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "CGDD2010140000000003", "fullName": "", "ytenantId": "", "paramOrder": 2, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "defParamId": 1996896485556355086, "array": true, "paramDesc": "扩展条件查询", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "bizType": "", "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "entityId": "", "entityCode": "", "apiName": "", "maxLength": "", "childId": "", "edit": false, "regularRule": "", "mapName": "", "mapRequestParamType": "", "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "format": "", "decimals": "", "paramTag": "", "rowLimit": "", "enableMulti": false, "extend": false}]}, "queryParamDTOS": "", "ysApi": false, "presetTokenApi": false, "applyFlag": false, "cover": false, "paramMapDTOS": {"paramMapDTOS": [{"id": 2106049437256122388, "name": "bustype", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "defParamId": "", "array": false, "paramDesc": "交易类型id", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "bustype", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2106049437256122389, "name": "product", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "defParamId": "", "array": false, "paramDesc": "物料id", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "product", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2106049437256122390, "name": "pageIndex", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "defParamId": "", "array": false, "paramDesc": "页码", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "pageIndex", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2106049437256122391, "name": "pageSize", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "defParamId": "", "array": false, "paramDesc": "每页条数", "paramType": "long", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "aggregatedValueObject": false, "mapName": "pageSize", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "long", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2106049437256122392, "name": "isSum", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "defParamId": "", "array": false, "paramDesc": "是否按照表头查询：true:表头、false:表头+明细", "paramType": "boolean", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "aggregatedValueObject": false, "mapName": "isSum", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "boolean", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2106049437256122381, "name": "queryOrders", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "children": {"children": [{"id": 2106049437256122382, "name": "field", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122381, "defParamId": "", "array": false, "paramDesc": "排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：applyOrders.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "field", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2106049437256122383, "name": "order", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122381, "defParamId": "", "array": false, "paramDesc": "顺序：asc：正序、desc：倒序", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "order", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "defParamId": "", "array": false, "paramDesc": "排序字段", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "aggregatedValueObject": false, "mapName": "queryOrders", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2106049437256122384, "name": "simpleVOs", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "children": {"children": [{"id": 2106049437256122385, "name": "field", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122384, "defParamId": "", "array": false, "paramDesc": "属性名(条件) ，1：status(单据状态：0:未审核、1:已审核、2:已关闭、3:审核中)、2：code(单据编号)、3：vouchdate(请购日期)、4：operator(请购员id)、5：org(需求组织id)、6；applyOrders.purchaseOrg(采购组织id)、7：applyOrders.vendor(建议供应商id)、 8：applyDept(请购部门id)、9：headItem.define1(单据头自定义项1)、10：applyOrders.adviseOrderDate(建议订货日期)、11：applyOrders.project(项目id)", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "aggregatedValueObject": false, "mapName": "field", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2106049437256122386, "name": "op", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122384, "defParamId": "", "array": false, "paramDesc": "比较符（条件）：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "aggregatedValueObject": false, "mapName": "op", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}, {"id": 2106049437256122387, "name": "value1", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122384, "defParamId": "", "array": false, "paramDesc": "值1（条件）", "paramType": "string", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "aggregatedValueObject": false, "mapName": "value1", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "string", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "defParamId": "", "array": false, "paramDesc": "扩展条件查询", "paramType": "object", "requestParamType": "BodyParam", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "aggregatedValueObject": false, "mapName": "simpleVOs", "mapRequestParamType": "BodyParam", "paramList": "", "primitive": false, "serviceParamType": "object", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityDTO": "", "childId": "", "edit": false, "defaultValue": "", "enableMulti": false}]}, "paramReturnDTOS": {"paramReturnDTOS": [{"id": 2106049445846056973, "name": "code", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "defParamId": 1996896485556355102, "array": false, "paramDesc": "返回码，调用成功时返回200", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049445846056974, "name": "message", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "defParamId": 1996896485556355103, "array": false, "paramDesc": "调用失败时的错误信息", "paramType": "string", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122393, "name": "data", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": "", "children": {"children": [{"id": 2106049445846056966, "name": "pageIndex", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122393, "defParamId": 1996896485556355105, "array": false, "paramDesc": "分页", "paramType": "int", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049445846056967, "name": "pageSize", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122393, "defParamId": 1996896485556355106, "array": false, "paramDesc": "每页条数", "paramType": "int", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049445846056968, "name": "pageCount", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122393, "defParamId": 1996896485556355107, "array": false, "paramDesc": "页数", "paramType": "int", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049445846056969, "name": "beginPageIndex", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122393, "defParamId": 1996896485556355108, "array": false, "paramDesc": "起始页", "paramType": "int", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049445846056970, "name": "endPageIndex", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122393, "defParamId": 1996896485556355109, "array": false, "paramDesc": "结束页", "paramType": "int", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049445846056971, "name": "recordCount", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122393, "defParamId": 1996896485556355110, "array": false, "paramDesc": "记录数", "paramType": "int", "requestParamType": "", "path": "", "example": 100, "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049445846056972, "name": "pubts", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122393, "defParamId": 1996896485556355111, "array": false, "paramDesc": "时间戳，格式为:yyyy-MM-dd HH:mm:ss", "paramType": "string", "requestParamType": "", "path": "", "example": "2024-03-04 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122394, "name": "recordList", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122393, "children": {"children": [{"id": 2106049437256122462, "name": "vouchdate", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355113, "array": false, "paramDesc": "单据日期，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-03-04 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122463, "name": "code", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355114, "array": false, "paramDesc": "请购单编号，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "CGQG0000201905100001", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122464, "name": "returncount", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355115, "array": false, "paramDesc": "退回次数，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122465, "name": "isWfControlled", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355116, "array": false, "paramDesc": "是否审批流控制，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122466, "name": "verifystate", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355117, "array": false, "paramDesc": "审批状态，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122467, "name": "bustype", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355118, "array": false, "paramDesc": "交易类型id，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": ****************, "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122468, "name": "bustype_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355119, "array": false, "paramDesc": "交易类型名称，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "采购要货", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"bustype.name\",\"cItemName\":\"bustype_name\",\"cCaption\":\"交易类型\",\"cShowCaption\":\"交易类型\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"transtype.bd_billtyperef\",\"cRefId\":null,\"cRefRetId\":{\"bustype\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrder\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122469, "name": "applyDept", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355120, "array": false, "paramDesc": "请购部门id，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996900540021735427, "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122470, "name": "applyDept_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355121, "array": false, "paramDesc": "请购部门名称，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "数智请购部门", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyDept.name\",\"cItemName\":\"applyDept_name\",\"cCaption\":\"请购部门\",\"cShowCaption\":\"请购部门\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucf-org-center.bd_adminorgsharetreeref\",\"cRefId\":null,\"cRefRetId\":null,\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrder\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122471, "name": "bi<PERSON><PERSON><PERSON>", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355122, "array": false, "paramDesc": "单据状态, 0:开立、3:审核中、1:已审核、2:已关闭，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122472, "name": "status", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355123, "array": false, "paramDesc": "单据状态, 0:开立、3:审核中、1:已审核、2:已关闭，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122473, "name": "currency", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355124, "array": false, "paramDesc": "币种id，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996901321700016134, "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122474, "name": "currency_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355125, "array": false, "paramDesc": "币种名称，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "人名币", "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"currency.name\",\"cItemName\":\"currency_name\",\"cCaption\":\"币种\",\"cShowCaption\":\"币种\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucfbasedoc.bd_currencytenantref\",\"cRefId\":null,\"cRefRetId\":{\"currency\":\"id\",\"currency_code\":\"code\",\"currency_name\":\"name\",\"currency_priceDigit\":\"priceDigit\",\"currency_moneyDigit\":\"moneyDigit\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrder\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122475, "name": "warehouseId", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355126, "array": false, "paramDesc": "要货仓库id，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996901321700016135, "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122476, "name": "warehouseId_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355127, "array": false, "paramDesc": "要货仓库名称，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "货品仓库", "fullName": "", "ytenantId": "", "paramOrder": 14, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"warehouseId.name\",\"cItemName\":\"warehouseId_name\",\"cCaption\":\"要货仓库\",\"cShowCaption\":\"要货仓库\",\"iMaxLength\":255,\"bHidden\":true,\"cRefType\":\"productcenter.aa_warehouse\",\"cRefId\":null,\"cRefRetId\":{\"warehouseId\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":null,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrder\",\"cControlType\":\"refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122477, "name": "source", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355128, "array": false, "paramDesc": "来源单据类型，20:计划独立需求、280:计划订单、MR.mr_lrp_plan_order_batch:计划订单、po_production_order:生产订单、ucf-amc-aum.aum_assignapply_card:资产领用申请、yonbip-pm-planme.rscm_project_materiallist_card:项目物资单、SCMSA.voucher_order:销售订单，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "MR.mr_lrp_plan_order_batch", "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122478, "name": "store", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355129, "array": false, "paramDesc": "所属门店id，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996902524296626181, "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122479, "name": "isUretailVoucher", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355130, "array": false, "paramDesc": "是否是零售, true:是、false:否，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": false, "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122480, "name": "store_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355131, "array": false, "paramDesc": "所属门店名称，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "零售门店", "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"store.name\",\"cItemName\":\"store_name\",\"cCaption\":\"所属门店\",\"cShowCaption\":\"所属门店\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"retail.aa_store\",\"cRefId\":null,\"cRefRetId\":\"{\\\"store\\\",\\\"id\\\"}\",\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrder\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122481, "name": "org", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355132, "array": false, "paramDesc": "需求组织id，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996902524296626182, "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122482, "name": "org_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355133, "array": false, "paramDesc": "需求组织名称，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "达利园组织", "fullName": "", "ytenantId": "", "paramOrder": 20, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"org.name\",\"cItemName\":\"org_name\",\"cCaption\":\"需求组织\",\"cShowCaption\":\"需求组织\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_orgtree\",\"cRefId\":null,\"cRefRetId\":{\"org\":\"id\",\"org_name\":\"name\"},\"cDataRule\":\"\\\"%u8c-config.option.singleOrg%>\\\"==\\\"false\\\"\",\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrder\",\"cControlType\":\"refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122483, "name": "custom", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355134, "array": false, "paramDesc": "客户id，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996903245851131908, "fullName": "", "ytenantId": "", "paramOrder": 21, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122484, "name": "creator", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355135, "array": false, "paramDesc": "制单人，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "张三", "fullName": "", "ytenantId": "", "paramOrder": 22, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122485, "name": "createTime", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355136, "array": false, "paramDesc": "制单时间，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-03-04 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 23, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122486, "name": "modifier", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355137, "array": false, "paramDesc": "修改人，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "张三", "fullName": "", "ytenantId": "", "paramOrder": 24, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122487, "name": "modifyTime", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355138, "array": false, "paramDesc": "修改时间，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-03-04 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 25, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122488, "name": "closer", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355139, "array": false, "paramDesc": "关闭人，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "李响", "fullName": "", "ytenantId": "", "paramOrder": 26, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122489, "name": "closeTime", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355140, "array": false, "paramDesc": "关闭时间，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-03-05 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 27, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122490, "name": "locker", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355141, "array": false, "paramDesc": "锁定人，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "张三", "fullName": "", "ytenantId": "", "paramOrder": 28, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122491, "name": "lockTime", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355142, "array": false, "paramDesc": "锁定时间，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-03-05 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 29, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122492, "name": "operator", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355143, "array": false, "paramDesc": "请购员id，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996904998192021506, "fullName": "", "ytenantId": "", "paramOrder": 30, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122493, "name": "operator_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355144, "array": false, "paramDesc": "请购员名称，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "王晨", "fullName": "", "ytenantId": "", "paramOrder": 31, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"operator.name\",\"cItemName\":\"operator_name\",\"cCaption\":\"请购员\",\"cShowCaption\":\"请购员\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucf-staff-center.bd_staff_outer_ref\",\"cRefId\":null,\"cRefRetId\":{\"operator\":\"id\",\"applyDept\":\"dept_id\",\"applyDept_name\":\"dept_id_name\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrder\",\"cControlType\":\"Column\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122494, "name": "auditor", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355145, "array": false, "paramDesc": "审核人，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "刘策", "fullName": "", "ytenantId": "", "paramOrder": 32, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122495, "name": "auditTime", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355146, "array": false, "paramDesc": "审核时间，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-03-05 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 33, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122496, "name": "auditDate", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355147, "array": false, "paramDesc": "审核日期，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-03-05 12:36:12", "fullName": "", "ytenantId": "", "paramOrder": 34, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122497, "name": "submitor", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355148, "array": false, "paramDesc": "提交人，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "王晨", "fullName": "", "ytenantId": "", "paramOrder": 35, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122498, "name": "submitTime", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355149, "array": false, "paramDesc": "提交时间，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-03-05 12:20:12", "fullName": "", "ytenantId": "", "paramOrder": 36, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122499, "name": "totalQuantity", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355150, "array": false, "paramDesc": "整单数量，汇总场景和明细场景均返回", "paramType": "number", "requestParamType": "", "path": "", "example": 200, "fullName": "", "ytenantId": "", "paramOrder": 37, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2106049437256122500, "name": "memo", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355151, "array": false, "paramDesc": "备注，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "来货请购", "fullName": "", "ytenantId": "", "paramOrder": 38, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122501, "name": "id", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355152, "array": false, "paramDesc": "请购单id，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996904998192021507, "fullName": "", "ytenantId": "", "paramOrder": 39, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122502, "name": "pubts", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355153, "array": false, "paramDesc": "时间戳，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-03-04 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 40, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122503, "name": "tplid", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355154, "array": false, "paramDesc": "模板id，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996906535790313475, "fullName": "", "ytenantId": "", "paramOrder": 41, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122395, "name": "headItem", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "children": {"children": [{"id": 2106049437256122396, "name": "id", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122395, "defParamId": 1996896485556355156, "array": false, "paramDesc": "表头自定义项id，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996906535790313476, "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122397, "name": "define1", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122395, "defParamId": 1996896485556355157, "array": false, "paramDesc": "表头自定义项1，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表头自定义项1", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122398, "name": "define2", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122395, "defParamId": 1996896485556355158, "array": false, "paramDesc": "表头自定义项2，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表头自定义项2", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122399, "name": "define3", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122395, "defParamId": 1996896485556355159, "array": false, "paramDesc": "表头自定义项3，汇总场景和明细场景均返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表头自定义项3", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1996896485556355155, "array": false, "paramDesc": "表头自定义项", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 42, "baseType": true, "defaultValue": "", "required": false, "visible": "", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122504, "name": "applyorders_execStatus", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355161, "array": false, "paramDesc": "执行状态, 0:未下订单、1:部分下单、2:全部下单、", "paramType": "string", "requestParamType": "", "path": "", "example": "表头自定义项4", "fullName": "", "ytenantId": "", "paramOrder": 43, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122505, "name": "applyorders_receiveOrg", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355162, "array": false, "paramDesc": "收货组织id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996916044853673985, "fullName": "", "ytenantId": "", "paramOrder": 44, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122506, "name": "applyorders_receiveOrg_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355163, "array": false, "paramDesc": "收货组织名称,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "达利园组织", "fullName": "", "ytenantId": "", "paramOrder": 45, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.receiveOrg.name\",\"cItemName\":\"applyorders_receiveOrg_name\",\"cCaption\":\"收货组织\",\"cShowCaption\":\"收货组织\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_orgtree\",\"cRefId\":null,\"cRefRetId\":{\"receiveOrg\":\"id\",\"receiveOrg_name\":\"name\"},\"cDataRule\":\"\\\"%loginUser.storeId%>\\\"==\\\"null\\\"  \\\"%u8c-config.option.singleOrg%>\\\"==\\\"false\\\"\",\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122507, "name": "applyorders_purchaseOrg", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355164, "array": false, "paramDesc": "采购组织id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996916044853673985, "fullName": "", "ytenantId": "", "paramOrder": 46, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122508, "name": "applyorders_purchaseOrg_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355165, "array": false, "paramDesc": "采购组织名称,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "达利园组织", "fullName": "", "ytenantId": "", "paramOrder": 47, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.purchaseOrg.name\",\"cItemName\":\"applyorders_purchaseOrg_name\",\"cCaption\":\"采购组织\",\"cShowCaption\":\"采购组织\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_orgtree\",\"cRefId\":null,\"cRefRetId\":{\"purchaseOrg\":\"id\",\"purchaseOrg_name\":\"name\"},\"cDataRule\":\"\\\"%loginUser.storeId%>\\\"==\\\"null\\\"  \\\"%u8c-config.option.singleOrg%>\\\"==\\\"false\\\"\",\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122509, "name": "applyorders_purDept", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355166, "array": false, "paramDesc": "采购部门id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996916044853673986, "fullName": "", "ytenantId": "", "paramOrder": 48, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122510, "name": "applyorders_purDept_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355167, "array": false, "paramDesc": "采购部门名称,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "采购部门", "fullName": "", "ytenantId": "", "paramOrder": 49, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.purDept.name\",\"cItemName\":\"applyorders_purDept_name\",\"cCaption\":\"采购部门\",\"cShowCaption\":\"采购部门\",\"iMaxLength\":255,\"bHidden\":true,\"cRefType\":\"ucf-org-center.bd_adminorgsharetreeref\",\"cRefId\":null,\"cRefRetId\":{\"purDept\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"TreeRefer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122511, "name": "applyorders_purPerson", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355168, "array": false, "paramDesc": "采购业务员id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996916551654047750, "fullName": "", "ytenantId": "", "paramOrder": 50, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122512, "name": "applyorders_purPerson_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355169, "array": false, "paramDesc": "采购业务员名称,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "李晨", "fullName": "", "ytenantId": "", "paramOrder": 51, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.purPerson.name\",\"cItemName\":\"applyorders_purPerson_name\",\"cCaption\":\"采购业务员\",\"cShowCaption\":\"采购业务员\",\"iMaxLength\":255,\"bHidden\":true,\"cRefType\":\"ucf-staff-center.bd_staff_outer_ref\",\"cRefId\":null,\"cRefRetId\":{\"purPerson\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122513, "name": "applyOrders_supplyMoney", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355170, "array": false, "paramDesc": "累计订货金额,只有明细场景返回", "paramType": "number", "requestParamType": "", "path": "", "example": 200, "fullName": "", "ytenantId": "", "paramOrder": 52, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2106049437256122514, "name": "applyOrder_orderMoneyRatio", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355171, "array": false, "paramDesc": "订单金额超量比例,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 20, "fullName": "", "ytenantId": "", "paramOrder": 53, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122515, "name": "applyorders_supplyCount", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355172, "array": false, "paramDesc": "累计订货数量,只有明细场景返回", "paramType": "number", "requestParamType": "", "path": "", "example": 20, "fullName": "", "ytenantId": "", "paramOrder": 54, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2106049437256122516, "name": "apporders_id", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355173, "array": false, "paramDesc": "订单行id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996916551654047751, "fullName": "", "ytenantId": "", "paramOrder": 55, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122517, "name": "applyorders_product", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355174, "array": false, "paramDesc": "物料id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996917006926348290, "fullName": "", "ytenantId": "", "paramOrder": 56, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122518, "name": "product_defaultAlbumId", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355175, "array": false, "paramDesc": "物料首图片,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996917006926348291, "fullName": "", "ytenantId": "", "paramOrder": 57, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122519, "name": "applyorders_product_cCode", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355176, "array": false, "paramDesc": "物料编码,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 58, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.product.cCode\",\"cItemName\":\"applyorders_product_cCode\",\"cCaption\":\"物料编码\",\"cShowCaption\":\"物料编码\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_nomalproduct\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cName\":\"cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"productsku_cName\":\"skuName\",\"product_oUnitId\":\"oUnitId\",\"unit\":\"productOfflineRetail_storeOrderUnit\",\"product_productOfflineRetail_purchaseUnit\":\"purchaseUnit\",\"unit_name\":\"purchaseUnit_name\",\"invExchRate\":\"purchaseRate\",\"productOfflineRetail_purchaseRate\":\"purchaseRate\",\"taxRate\":\"productOfflineRetail_inputTax\",\"product_primeCosts\":\"primeCosts\",\"productsku_primeCosts\":\"productskus_primeCosts\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isSerialNoManage\":\"productOfflineRetail_isSerialNoManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"product_modelDescription\":\"modelDescription\",\"productskus_modelDescription\":\"productskus_modelDescription\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":false,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"refer\",\"refReturn\":\"cCode\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122520, "name": "applyorders_product_cName", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355177, "array": false, "paramDesc": "物料名称,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "苹果", "fullName": "", "ytenantId": "", "paramOrder": 59, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.product.cName\",\"cItemName\":\"applyorders_product_cName\",\"cCaption\":\"物料名称\",\"cShowCaption\":\"物料名称\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_nomalproduct\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cName\":\"cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"productsku_cName\":\"skuName\",\"product_oUnitId\":\"oUnitId\",\"unit\":\"productOfflineRetail_storeOrderUnit\",\"product_productOfflineRetail_purchaseUnit\":\"purchaseUnit\",\"unit_name\":\"purchaseUnit_name\",\"invExchRate\":\"purchaseRate\",\"productOfflineRetail_purchaseRate\":\"purchaseRate\",\"taxRate\":\"productOfflineRetail_inputTax\",\"product_primeCosts\":\"primeCosts\",\"productsku_primeCosts\":\"productskus_primeCosts\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isSerialNoManage\":\"productOfflineRetail_isSerialNoManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"product_modelDescription\":\"modelDescription\",\"productskus_modelDescription\":\"productskus_modelDescription\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":false,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"refer\",\"refReturn\":\"cName\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122521, "name": "applyorders_productsku", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355178, "array": false, "paramDesc": "物料SKUid,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996917006926348290, "fullName": "", "ytenantId": "", "paramOrder": 60, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122522, "name": "applyorders_productsku_cCode", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355179, "array": false, "paramDesc": "物料sku编码,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 61, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.productsku.cCode\",\"cItemName\":\"applyorders_productsku_cCode\",\"cCaption\":\"物料sku编码\",\"cShowCaption\":\"物料sku编码\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cName\":\"cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"productsku_cName\":\"skuName\",\"product_oUnitId\":\"oUnitId\",\"unit\":\"productOfflineRetail_storeOrderUnit\",\"product_productOfflineRetail_purchaseUnit\":\"purchaseUnit\",\"unit_name\":\"purchaseUnit_name\",\"invExchRate\":\"purchaseRate\",\"productOfflineRetail_purchaseRate\":\"purchaseRate\",\"taxRate\":\"productOfflineRetail_inputTax\",\"product_primeCosts\":\"primeCosts\",\"productsku_primeCosts\":\"productskus_primeCosts\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isSerialNoManage\":\"productOfflineRetail_isSerialNoManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"product_modelDescription\":\"modelDescription\",\"productskus_modelDescription\":\"productskus_modelDescription\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":false,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"refer\",\"refReturn\":\"productskus_cCode\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122523, "name": "applyorders_productsku_cName", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355180, "array": false, "paramDesc": "物料sku名称,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "苹果", "fullName": "", "ytenantId": "", "paramOrder": 62, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.productsku.skuName\",\"cItemName\":\"applyorders_productsku_cName\",\"cCaption\":\"物料sku名称\",\"cShowCaption\":\"物料sku名称\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productsku\",\"cRefId\":null,\"cRefRetId\":{\"product\":\"id\",\"product_cName\":\"cName\",\"productsku\":\"productskus_id\",\"productsku_cCode\":\"productskus_cCode\",\"productsku_cName\":\"skuName\",\"product_oUnitId\":\"oUnitId\",\"unit\":\"productOfflineRetail_storeOrderUnit\",\"product_productOfflineRetail_purchaseUnit\":\"purchaseUnit\",\"unit_name\":\"purchaseUnit_name\",\"invExchRate\":\"purchaseRate\",\"productOfflineRetail_purchaseRate\":\"purchaseRate\",\"taxRate\":\"productOfflineRetail_inputTax\",\"product_primeCosts\":\"primeCosts\",\"productsku_primeCosts\":\"productskus_primeCosts\",\"isBatchManage\":\"productOfflineRetail_isBatchManage\",\"isSerialNoManage\":\"productOfflineRetail_isSerialNoManage\",\"isExpiryDateManage\":\"productOfflineRetail_isExpiryDateManage\",\"expireDateNo\":\"productOfflineRetail_expireDateNo\",\"expireDateUnit\":\"productOfflineRetail_expireDateUnit\",\"free@1@@10\":\"retailskus!free@1@@10\",\"skudefine@1@@60\":\"productSkuProps!define@1@@60\",\"prodefine@1@@30\":\"productProps!define@1@@30\",\"propertiesValue\":\"propertiesValue\",\"product_modelDescription\":\"modelDescription\",\"productskus_modelDescription\":\"productskus_modelDescription\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":false,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"refer\",\"refReturn\":\"cName\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122524, "name": "applyorders_currency", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355181, "array": false, "paramDesc": "币种id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996917006926348290, "fullName": "", "ytenantId": "", "paramOrder": 63, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122525, "name": "applyorders_currency_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355182, "array": false, "paramDesc": "币种名称,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "人民币", "fullName": "", "ytenantId": "", "paramOrder": 64, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.currency.name\",\"cItemName\":\"applyorders_currency_name\",\"cCaption\":\"币种\",\"cShowCaption\":\"币种\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucfbasedoc.bd_currencytenantref\",\"cRefId\":null,\"cRefRetId\":{\"currency\":\"id\",\"currency_code\":\"code\",\"currency_name\":\"name\",\"currency_priceDigit\":\"priceDigit\",\"currency_moneyDigit\":\"moneyDigit\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122526, "name": "applyorders_currency_priceDigit", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355183, "array": false, "paramDesc": "币种单价精度,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 65, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122527, "name": "applyorders_currency_moneyDigit", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355184, "array": false, "paramDesc": "币种金额精度,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 66, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122528, "name": "applyorders_qty", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355185, "array": false, "paramDesc": "数量,只有明细场景返回", "paramType": "number", "requestParamType": "", "path": "", "example": 20, "fullName": "", "ytenantId": "", "paramOrder": 67, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2106049437256122529, "name": "applyorders_subQty", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355186, "array": false, "paramDesc": "计价数量,只有明细场景返回", "paramType": "number", "requestParamType": "", "path": "", "example": 20, "fullName": "", "ytenantId": "", "paramOrder": 68, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2106049437256122530, "name": "applyorders_rowno", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355187, "array": false, "paramDesc": "行号,只有明细场景返回", "paramType": "int", "requestParamType": "", "path": "", "example": 10, "fullName": "", "ytenantId": "", "paramOrder": 69, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122531, "name": "unit_Precision", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355188, "array": false, "paramDesc": "主计量精度,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 70, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122532, "name": "applyorders_unit", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355189, "array": false, "paramDesc": "单位id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996918364130246657, "fullName": "", "ytenantId": "", "paramOrder": 71, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122533, "name": "applyorders_unit_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355190, "array": false, "paramDesc": "主计量名称,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "吨", "fullName": "", "ytenantId": "", "paramOrder": 72, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.unit.name\",\"cItemName\":\"applyorders_unit_name\",\"cCaption\":\"主计量\",\"cShowCaption\":\"主计量\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"aa_productunit\",\"cRefId\":null,\"cRefRetId\":{\"unit\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":false,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122534, "name": "applyorders_product_oUnitId", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355191, "array": false, "paramDesc": "零售单位id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996918364130246657, "fullName": "", "ytenantId": "", "paramOrder": 73, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122535, "name": "applyorders_product_productOfflineRetail_purchaseUnit", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355192, "array": false, "paramDesc": "采购单位id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "吨", "fullName": "", "ytenantId": "", "paramOrder": 74, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122536, "name": "applyorders_invExchRate", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355193, "array": false, "paramDesc": "换算率,只有明细场景返回", "paramType": "int", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 75, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122537, "name": "applyorders_productOfflineRetail_purchaseRate", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355194, "array": false, "paramDesc": "采购单位换算率,只有明细场景返回", "paramType": "int", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 76, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122538, "name": "priceUOM", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355195, "array": false, "paramDesc": "计价单位id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996918364130246658, "fullName": "", "ytenantId": "", "paramOrder": 77, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122539, "name": "priceUOM_Name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355196, "array": false, "paramDesc": "计价单位,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "吨", "fullName": "", "ytenantId": "", "paramOrder": 78, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.priceUOM.name\",\"cItemName\":\"priceUOM_Name\",\"cCaption\":\"计价单位\",\"cShowCaption\":\"计价单位\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"productcenter.pc_productassitunitsref\",\"cRefId\":null,\"cRefRetId\":{\"priceUOM\":\"assistUnit\",\"priceUOM_Name\":\"assistUnit_Name\",\"priceUOM_Code\":\"assistUnit_Code\",\"invPriceExchRate\":\"mainUnitCount\",\"unitExchangeTypePrice\":\"unitExchangeType\",\"priceUOM_Precision\":\"assistUnit_Precision\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122540, "name": "invPriceExchRate", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355197, "array": false, "paramDesc": "计价换算率,只有明细场景返回", "paramType": "int", "requestParamType": "", "path": "", "example": 1, "fullName": "", "ytenantId": "", "paramOrder": 79, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122541, "name": "unitExchangeTypePrice", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355198, "array": false, "paramDesc": "计价单位转换率的换算方式,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 0, "fullName": "", "ytenantId": "", "paramOrder": 80, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122542, "name": "priceUOM_Precision", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355199, "array": false, "paramDesc": "计价单位精度,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 81, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122543, "name": "taxRate", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355200, "array": false, "paramDesc": "税率,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 82, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.taxRate\",\"cItemName\":\"taxRate\",\"cCaption\":\"税率\",\"cShowCaption\":\"税率\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucfbasedoc.bd_taxrate\",\"cRefId\":null,\"cRefRetId\":{\"taxitems\":\"id\",\"taxitems_name\":\"name\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"refer\",\"refReturn\":\"ntaxRate\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122544, "name": "oriTax", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355201, "array": false, "paramDesc": "税额,只有明细场景返回", "paramType": "number", "requestParamType": "", "path": "", "example": 20, "fullName": "", "ytenantId": "", "paramOrder": 83, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2106049437256122545, "name": "oriTaxUnitPrice", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355202, "array": false, "paramDesc": "含税单价,只有明细场景返回", "paramType": "number", "requestParamType": "", "path": "", "example": 12, "fullName": "", "ytenantId": "", "paramOrder": 84, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2106049437256122546, "name": "oriUnitPrice", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355203, "array": false, "paramDesc": "无税单价,只有明细场景返回", "paramType": "number", "requestParamType": "", "path": "", "example": 6, "fullName": "", "ytenantId": "", "paramOrder": 85, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2106049437256122547, "name": "oriMoney", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355204, "array": false, "paramDesc": "无税金额,只有明细场景返回", "paramType": "number", "requestParamType": "", "path": "", "example": 150, "fullName": "", "ytenantId": "", "paramOrder": 86, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2106049437256122548, "name": "oriSum", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355205, "array": false, "paramDesc": "含税金额,只有明细场景返回", "paramType": "number", "requestParamType": "", "path": "", "example": 200, "fullName": "", "ytenantId": "", "paramOrder": 87, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2106049437256122549, "name": "applyorders_product_primeCosts", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355206, "array": false, "paramDesc": "进货价格,只有明细场景返回", "paramType": "number", "requestParamType": "", "path": "", "example": 23, "fullName": "", "ytenantId": "", "paramOrder": 88, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2106049437256122550, "name": "applyorders_productsku_primeCosts", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355207, "array": false, "paramDesc": "sku进货价格,只有明细场景返回", "paramType": "number", "requestParamType": "", "path": "", "example": 24, "fullName": "", "ytenantId": "", "paramOrder": 89, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": 8, "maxLength": 28, "enableMulti": false}, {"id": 2106049437256122551, "name": "applyorders_requirementDate", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355208, "array": false, "paramDesc": "需求日期,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-03-04 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 90, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122552, "name": "applyorders_adviseOrderDate", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355209, "array": false, "paramDesc": "建议订货日期,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "2021-03-04 00:00:00", "fullName": "", "ytenantId": "", "paramOrder": 91, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122553, "name": "applyorders_adviseSupplier", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355210, "array": false, "paramDesc": "建议供应商id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996919652626202626, "fullName": "", "ytenantId": "", "paramOrder": 92, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122554, "name": "applyorders_adviseSupplier_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355211, "array": false, "paramDesc": "建议供应商名称,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "达利园供应商", "fullName": "", "ytenantId": "", "paramOrder": 93, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.adviseSupplier.name\",\"cItemName\":\"applyorders_adviseSupplier_name\",\"cCaption\":\"建议供应商\",\"cShowCaption\":\"建议供应商\",\"iMaxLength\":255,\"bHidden\":true,\"cRefType\":\"yssupplier.aa_vendor\",\"cRefId\":null,\"cRefRetId\":{\"adviseSupplier\":\"id\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"Column\",\"refReturn\":null,\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122555, "name": "applyorders_vendor", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355212, "array": false, "paramDesc": "建议供应商id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996919652626202626, "fullName": "", "ytenantId": "", "paramOrder": 94, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122556, "name": "applyorders_vendor_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355213, "array": false, "paramDesc": "建议供应商名称,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "达利园供应商", "fullName": "", "ytenantId": "", "paramOrder": 95, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.vendor.name\",\"cItemName\":\"applyorders_vendor_name\",\"cCaption\":\"建议供应商\",\"cShowCaption\":\"建议供应商\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"yssupplier.aa_vendor\",\"cRefId\":null,\"cRefRetId\":{\"vendor\":\"id\",\"contact\":\"contact\",\"applyorders_vendor_taxrate\":\"taxrate\",\"applyOrders_vendor_define@1@@30\":\"customItems!define@1@@30\"},\"cDataRule\":null,\"iNumPoint\":null,\"bCanModify\":false,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"Column\",\"refReturn\":null,\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122557, "name": "applyorders_memo", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355214, "array": false, "paramDesc": "备注,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "要货请购", "fullName": "", "ytenantId": "", "paramOrder": 96, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122400, "name": "bodyItem", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "children": {"children": [{"id": 2106049437256122401, "name": "id", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355216, "array": false, "paramDesc": "表体自定义项id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996919652626202627, "fullName": "", "ytenantId": "", "paramOrder": 0, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122402, "name": "define1", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355217, "array": false, "paramDesc": "表体自定义项1,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项1", "fullName": "", "ytenantId": "", "paramOrder": 1, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122403, "name": "define2", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355218, "array": false, "paramDesc": "表体自定义项2,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项2", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122404, "name": "define3", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355219, "array": false, "paramDesc": "表体自定义项3,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项3", "fullName": "", "ytenantId": "", "paramOrder": 3, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122405, "name": "define56", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355220, "array": false, "paramDesc": "表体自定义项56,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项56", "fullName": "", "ytenantId": "", "paramOrder": 4, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122406, "name": "define57", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355221, "array": false, "paramDesc": "表体自定义项57,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项57", "fullName": "", "ytenantId": "", "paramOrder": 5, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122407, "name": "define58", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355222, "array": false, "paramDesc": "表体自定义项58,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项58", "fullName": "", "ytenantId": "", "paramOrder": 6, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122408, "name": "define59", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355223, "array": false, "paramDesc": "表体自定义项59,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项59", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122409, "name": "define60", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355224, "array": false, "paramDesc": "表体自定义项60,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项60", "fullName": "", "ytenantId": "", "paramOrder": 8, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122410, "name": "define4", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355225, "array": false, "paramDesc": "表体自定义项4,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项4", "fullName": "", "ytenantId": "", "paramOrder": 9, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122411, "name": "define31", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355226, "array": false, "paramDesc": "表体自定义项31,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项31", "fullName": "", "ytenantId": "", "paramOrder": 10, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122412, "name": "define32", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355227, "array": false, "paramDesc": "表体自定义项32,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项32", "fullName": "", "ytenantId": "", "paramOrder": 11, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122413, "name": "define33", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355228, "array": false, "paramDesc": "表体自定义项33,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项33", "fullName": "", "ytenantId": "", "paramOrder": 12, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122414, "name": "define34", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355229, "array": false, "paramDesc": "表体自定义项34,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项34", "fullName": "", "ytenantId": "", "paramOrder": 13, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122415, "name": "define35", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355230, "array": false, "paramDesc": "表体自定义项35,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项35", "fullName": "", "ytenantId": "", "paramOrder": 14, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122416, "name": "define36", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355231, "array": false, "paramDesc": "表体自定义项36,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项36", "fullName": "", "ytenantId": "", "paramOrder": 15, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122417, "name": "define37", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355232, "array": false, "paramDesc": "表体自定义项37,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项37", "fullName": "", "ytenantId": "", "paramOrder": 16, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122418, "name": "define38", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355233, "array": false, "paramDesc": "表体自定义项38,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项38", "fullName": "", "ytenantId": "", "paramOrder": 17, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122419, "name": "define39", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355234, "array": false, "paramDesc": "表体自定义项39,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项39", "fullName": "", "ytenantId": "", "paramOrder": 18, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122420, "name": "define40", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355235, "array": false, "paramDesc": "表体自定义项40,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项40", "fullName": "", "ytenantId": "", "paramOrder": 19, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122421, "name": "define41", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355236, "array": false, "paramDesc": "表体自定义项41,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项41", "fullName": "", "ytenantId": "", "paramOrder": 20, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122422, "name": "define42", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355237, "array": false, "paramDesc": "表体自定义项42,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项42", "fullName": "", "ytenantId": "", "paramOrder": 21, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122423, "name": "define43", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355238, "array": false, "paramDesc": "表体自定义项43,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项43", "fullName": "", "ytenantId": "", "paramOrder": 22, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122424, "name": "define44", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355239, "array": false, "paramDesc": "表体自定义项44,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项44", "fullName": "", "ytenantId": "", "paramOrder": 23, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122425, "name": "define45", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355240, "array": false, "paramDesc": "表体自定义项45,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项45", "fullName": "", "ytenantId": "", "paramOrder": 24, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122426, "name": "define46", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355241, "array": false, "paramDesc": "表体自定义项46,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项46", "fullName": "", "ytenantId": "", "paramOrder": 25, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122427, "name": "define47", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355242, "array": false, "paramDesc": "表体自定义项47,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项47", "fullName": "", "ytenantId": "", "paramOrder": 26, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122428, "name": "define48", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355243, "array": false, "paramDesc": "表体自定义项48,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项48", "fullName": "", "ytenantId": "", "paramOrder": 27, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122429, "name": "define49", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355244, "array": false, "paramDesc": "表体自定义项49,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项49", "fullName": "", "ytenantId": "", "paramOrder": 28, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122430, "name": "define50", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355245, "array": false, "paramDesc": "表体自定义项50,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项50", "fullName": "", "ytenantId": "", "paramOrder": 29, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122431, "name": "define51", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355246, "array": false, "paramDesc": "表体自定义项51,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项51", "fullName": "", "ytenantId": "", "paramOrder": 30, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122432, "name": "define52", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355247, "array": false, "paramDesc": "表体自定义项52,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项52", "fullName": "", "ytenantId": "", "paramOrder": 31, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122433, "name": "define53", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355248, "array": false, "paramDesc": "表体自定义项53,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项53", "fullName": "", "ytenantId": "", "paramOrder": 32, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122434, "name": "define54", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355249, "array": false, "paramDesc": "表体自定义项54,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项54", "fullName": "", "ytenantId": "", "paramOrder": 33, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122435, "name": "define55", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355250, "array": false, "paramDesc": "表体自定义项55,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项55", "fullName": "", "ytenantId": "", "paramOrder": 34, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122436, "name": "define5", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355251, "array": false, "paramDesc": "表体自定义项5,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项5", "fullName": "", "ytenantId": "", "paramOrder": 35, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122437, "name": "define6", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355252, "array": false, "paramDesc": "表体自定义项6,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项6", "fullName": "", "ytenantId": "", "paramOrder": 36, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122438, "name": "define7", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355253, "array": false, "paramDesc": "表体自定义项7,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项7", "fullName": "", "ytenantId": "", "paramOrder": 37, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122439, "name": "define8", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355254, "array": false, "paramDesc": "表体自定义项8,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项8", "fullName": "", "ytenantId": "", "paramOrder": 38, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122440, "name": "define9", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355255, "array": false, "paramDesc": "表体自定义项9,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项9", "fullName": "", "ytenantId": "", "paramOrder": 39, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122441, "name": "define10", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355256, "array": false, "paramDesc": "表体自定义项10,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项10", "fullName": "", "ytenantId": "", "paramOrder": 40, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122442, "name": "define11", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355257, "array": false, "paramDesc": "表体自定义项11,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项11", "fullName": "", "ytenantId": "", "paramOrder": 41, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122443, "name": "define12", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355258, "array": false, "paramDesc": "表体自定义项12,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项12", "fullName": "", "ytenantId": "", "paramOrder": 42, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122444, "name": "define13", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355259, "array": false, "paramDesc": "表体自定义项13,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项13", "fullName": "", "ytenantId": "", "paramOrder": 43, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122445, "name": "define14", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355260, "array": false, "paramDesc": "表体自定义项14,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项14", "fullName": "", "ytenantId": "", "paramOrder": 44, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122446, "name": "define15", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355261, "array": false, "paramDesc": "表体自定义项15,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项15", "fullName": "", "ytenantId": "", "paramOrder": 45, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122447, "name": "define16", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355262, "array": false, "paramDesc": "表体自定义项16,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项16", "fullName": "", "ytenantId": "", "paramOrder": 46, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122448, "name": "define17", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355263, "array": false, "paramDesc": "表体自定义项17,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项17", "fullName": "", "ytenantId": "", "paramOrder": 47, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122449, "name": "define18", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355264, "array": false, "paramDesc": "表体自定义项18,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项18", "fullName": "", "ytenantId": "", "paramOrder": 48, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122450, "name": "define19", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355265, "array": false, "paramDesc": "表体自定义项19,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项19", "fullName": "", "ytenantId": "", "paramOrder": 49, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122451, "name": "define20", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355266, "array": false, "paramDesc": "表体自定义项20,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项20", "fullName": "", "ytenantId": "", "paramOrder": 50, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122452, "name": "define21", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355267, "array": false, "paramDesc": "表体自定义项21,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项21", "fullName": "", "ytenantId": "", "paramOrder": 51, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122453, "name": "define22", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355268, "array": false, "paramDesc": "表体自定义项22,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项22", "fullName": "", "ytenantId": "", "paramOrder": 52, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122454, "name": "define23", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355269, "array": false, "paramDesc": "表体自定义项23,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项23", "fullName": "", "ytenantId": "", "paramOrder": 53, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122455, "name": "define24", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355270, "array": false, "paramDesc": "表体自定义项24,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项24", "fullName": "", "ytenantId": "", "paramOrder": 54, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122456, "name": "define25", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355271, "array": false, "paramDesc": "表体自定义项25,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项25", "fullName": "", "ytenantId": "", "paramOrder": 55, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122457, "name": "define26", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355272, "array": false, "paramDesc": "表体自定义项26,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项26", "fullName": "", "ytenantId": "", "paramOrder": 56, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122458, "name": "define27", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355273, "array": false, "paramDesc": "表体自定义项27,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项27", "fullName": "", "ytenantId": "", "paramOrder": 57, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122459, "name": "define28", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355274, "array": false, "paramDesc": "表体自定义项28,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项28", "fullName": "", "ytenantId": "", "paramOrder": 58, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122460, "name": "define29", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355275, "array": false, "paramDesc": "表体自定义项29,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项29", "fullName": "", "ytenantId": "", "paramOrder": 59, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122461, "name": "define30", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122400, "defParamId": 1996896485556355276, "array": false, "paramDesc": "表体自定义项30,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "表体自定义项30", "fullName": "", "ytenantId": "", "paramOrder": 60, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1996896485556355215, "array": false, "paramDesc": "表体自定义项", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 97, "baseType": true, "defaultValue": "", "required": false, "visible": "", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122558, "name": "applyorders_productsku_modelDescription", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355277, "array": false, "paramDesc": "sku规格型号,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "33mm", "fullName": "", "ytenantId": "", "paramOrder": 98, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122559, "name": "applyorders_product_model", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355278, "array": false, "paramDesc": "型号,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "33mm", "fullName": "", "ytenantId": "", "paramOrder": 99, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122560, "name": "applyorders_product_modelDescription", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355279, "array": false, "paramDesc": "规格说明,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "细口", "fullName": "", "ytenantId": "", "paramOrder": 100, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122561, "name": "applyorders_propertiesValue", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355280, "array": false, "paramDesc": "规格,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "细口", "fullName": "", "ytenantId": "", "paramOrder": 101, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122562, "name": "project", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355281, "array": false, "paramDesc": "项目id,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 1996923973363302404, "fullName": "", "ytenantId": "", "paramOrder": 102, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122563, "name": "project_code", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355282, "array": false, "paramDesc": "项目编码,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 34, "fullName": "", "ytenantId": "", "paramOrder": 103, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.project.code\",\"cItemName\":\"project_code\",\"cCaption\":\"项目编码\",\"cShowCaption\":\"项目编码\",\"iMaxLength\":255,\"bHidden\":true,\"cRefType\":\"ucfbasedoc.bd_outer_projectcardMCref\",\"cRefId\":null,\"cRefRetId\":{\"project\":\"id\",\"project_code\":\"code\",\"project_name\":\"name\"},\"cDataRule\":\"\\\"%loginUser.storeId%>\\\"==\\\"null\\\"\",\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":false,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"Refer\",\"refReturn\":\"code\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122564, "name": "applyOrders_vendor_define1", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355283, "array": false, "paramDesc": "供应商自定义项1,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项1", "fullName": "", "ytenantId": "", "paramOrder": 104, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122565, "name": "project_name", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355284, "array": false, "paramDesc": "项目名称,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "虚拟项目", "fullName": "", "ytenantId": "", "paramOrder": 105, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": true, "refTypeContext": "{\"cFieldName\":\"applyOrders.project.name\",\"cItemName\":\"project_name\",\"cCaption\":\"项目名称\",\"cShowCaption\":\"项目名称\",\"iMaxLength\":255,\"bHidden\":false,\"cRefType\":\"ucfbasedoc.bd_outer_projectcardMCref\",\"cRefId\":null,\"cRefRetId\":{\"project\":\"id\",\"project_code\":\"code\",\"project_name\":\"name\"},\"cDataRule\":\"\\\"%loginUser.storeId%>\\\"==\\\"null\\\"\",\"iNumPoint\":null,\"bCanModify\":true,\"iMaxShowLen\":255,\"bShowIt\":true,\"bIsNull\":true,\"bSelfDefine\":false,\"cSelfDefineType\":null,\"cOrder\":null,\"cDataSourceName\":\"pu.applyorder.ApplyOrders\",\"cControlType\":\"Refer\",\"refReturn\":\"name\",\"dataType\":null,\"bEnum\":\"false\",\"cEnumString\":null,\"enumArray\":null,\"bMustSelect\":\"false\"}", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122566, "name": "applyOrders_vendor_define2", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355285, "array": false, "paramDesc": "供应商自定义项2,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项2", "fullName": "", "ytenantId": "", "paramOrder": 106, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122567, "name": "applyorders_trackNo", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355286, "array": false, "paramDesc": "跟踪号,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": 2, "fullName": "", "ytenantId": "", "paramOrder": 107, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122568, "name": "applyOrders_vendor_define3", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355287, "array": false, "paramDesc": "供应商自定义项3,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项3", "fullName": "", "ytenantId": "", "paramOrder": 108, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122569, "name": "applyOrders_vendor_define4", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355288, "array": false, "paramDesc": "供应商自定义项4,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项4", "fullName": "", "ytenantId": "", "paramOrder": 109, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122570, "name": "applyOrders_vendor_define5", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355289, "array": false, "paramDesc": "供应商自定义项5,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项5", "fullName": "", "ytenantId": "", "paramOrder": 110, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122571, "name": "applyOrders_vendor_define6", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355290, "array": false, "paramDesc": "供应商自定义项6,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项6", "fullName": "", "ytenantId": "", "paramOrder": 111, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122572, "name": "applyOrders_vendor_define7", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355291, "array": false, "paramDesc": "供应商自定义项7,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项7", "fullName": "", "ytenantId": "", "paramOrder": 112, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122573, "name": "applyOrders_vendor_define8", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355292, "array": false, "paramDesc": "供应商自定义项8,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项8", "fullName": "", "ytenantId": "", "paramOrder": 113, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122574, "name": "applyOrders_vendor_define9", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355293, "array": false, "paramDesc": "供应商自定义项9,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项9", "fullName": "", "ytenantId": "", "paramOrder": 114, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122575, "name": "applyOrders_vendor_define10", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355294, "array": false, "paramDesc": "供应商自定义项10,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项10", "fullName": "", "ytenantId": "", "paramOrder": 115, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122576, "name": "applyOrders_vendor_define11", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355295, "array": false, "paramDesc": "供应商自定义项11,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项11", "fullName": "", "ytenantId": "", "paramOrder": 116, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122577, "name": "applyOrders_vendor_define12", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355296, "array": false, "paramDesc": "供应商自定义项12,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项12", "fullName": "", "ytenantId": "", "paramOrder": 117, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122578, "name": "applyOrders_vendor_define13", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355297, "array": false, "paramDesc": "供应商自定义项13,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项13", "fullName": "", "ytenantId": "", "paramOrder": 118, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122579, "name": "applyOrders_vendor_define14", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355298, "array": false, "paramDesc": "供应商自定义项14,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项14", "fullName": "", "ytenantId": "", "paramOrder": 119, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122580, "name": "applyOrders_vendor_define15", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355299, "array": false, "paramDesc": "供应商自定义项15,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项15", "fullName": "", "ytenantId": "", "paramOrder": 120, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122581, "name": "applyOrders_vendor_define16", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355300, "array": false, "paramDesc": "供应商自定义项16,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项16", "fullName": "", "ytenantId": "", "paramOrder": 121, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122582, "name": "applyOrders_vendor_define17", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355301, "array": false, "paramDesc": "供应商自定义项17,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项17", "fullName": "", "ytenantId": "", "paramOrder": 122, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122583, "name": "applyOrders_vendor_define18", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355302, "array": false, "paramDesc": "供应商自定义项18,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项18", "fullName": "", "ytenantId": "", "paramOrder": 123, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122584, "name": "applyOrders_vendor_define19", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355303, "array": false, "paramDesc": "供应商自定义项19,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项19", "fullName": "", "ytenantId": "", "paramOrder": 124, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122585, "name": "applyOrders_vendor_define20", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355304, "array": false, "paramDesc": "供应商自定义项20,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项20", "fullName": "", "ytenantId": "", "paramOrder": 125, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122586, "name": "applyOrders_vendor_define21", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355305, "array": false, "paramDesc": "供应商自定义项21,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项21", "fullName": "", "ytenantId": "", "paramOrder": 126, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122587, "name": "applyOrders_vendor_define22", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355306, "array": false, "paramDesc": "供应商自定义项22,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项22", "fullName": "", "ytenantId": "", "paramOrder": 127, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122588, "name": "applyOrders_vendor_define23", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355307, "array": false, "paramDesc": "供应商自定义项23,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项23", "fullName": "", "ytenantId": "", "paramOrder": 128, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122589, "name": "applyOrders_vendor_define24", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355308, "array": false, "paramDesc": "供应商自定义项24,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项24", "fullName": "", "ytenantId": "", "paramOrder": 129, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122590, "name": "applyOrders_vendor_define25", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355309, "array": false, "paramDesc": "供应商自定义项25,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项25", "fullName": "", "ytenantId": "", "paramOrder": 130, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122591, "name": "applyOrders_vendor_define26", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355310, "array": false, "paramDesc": "供应商自定义项26,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项26", "fullName": "", "ytenantId": "", "paramOrder": 131, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122592, "name": "applyOrders_vendor_define27", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355311, "array": false, "paramDesc": "供应商自定义项27,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项27", "fullName": "", "ytenantId": "", "paramOrder": 132, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122593, "name": "applyOrders_vendor_define28", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355312, "array": false, "paramDesc": "供应商自定义项28,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项28", "fullName": "", "ytenantId": "", "paramOrder": 133, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122594, "name": "applyOrders_vendor_define29", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355313, "array": false, "paramDesc": "供应商自定义项29,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项29", "fullName": "", "ytenantId": "", "paramOrder": 134, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}, {"id": 2106049437256122595, "name": "applyOrders_vendor_define30", "apiId": "0f1453d26e6741faa95ace9533a61683", "parentId": 2106049437256122394, "defParamId": 1996896485556355314, "array": false, "paramDesc": "供应商自定义项30,只有明细场景返回", "paramType": "string", "requestParamType": "", "path": "", "example": "供应商自定义项30", "fullName": "", "ytenantId": "", "paramOrder": 135, "baseType": true, "defaultValue": "", "required": false, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 1, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1996896485556355112, "array": true, "paramDesc": "返回列表信息", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 7, "baseType": true, "defaultValue": "", "required": true, "visible": "", "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "defParamId": 1996896485556355104, "array": false, "paramDesc": "调用成功时的返回数据", "paramType": "object", "requestParamType": "", "path": "", "example": "", "fullName": "", "ytenantId": "", "paramOrder": 2, "baseType": true, "defaultValue": "", "required": true, "visible": true, "gmtCreate": "2024-10-08 16:33:43.000", "gmtUpdate": "2024-10-08 16:33:43.000", "apiName": "", "entityId": "", "entityCode": "", "edit": false, "refType": false, "refTypeContext": "", "defineHidden": 0, "integrateObjectId": "", "paramTag": "", "format": "", "decimals": "", "maxLength": 0, "enableMulti": false}]}, "returnFormatType": "JSON", "paramConstDTOS": "", "paramConstMapDTOS": "", "apiDemoReturnDTOS": {"apiDemoReturnDTOS": [{"id": 2106049445846056979, "apiId": "0f1453d26e6741faa95ace9533a61683", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"pageIndex\": 1, \"pageSize\": 10, \"pageCount\": 10, \"beginPageIndex\": 1, \"endPageIndex\": 10, \"recordCount\": 100, \"pubts\": \"2024-03-04 00:00:00\", \"recordList\": [ { \"vouchdate\": \"2021-03-04 00:00:00\", \"code\": \"CGQG0000201905100001\", \"returncount\": \"1\", \"isWfControlled\": \"1\", \"verifystate\": \"1\", \"bustype\": \"****************\", \"bustype_name\": \"采购要货\", \"applyDept\": \"1996900540021735427\", \"applyDept_name\": \"数智请购部门\", \"bizstatus\": \"0\", \"status\": \"0\", \"currency\": \"1996901321700016134\", \"currency_name\": \"人名币\", \"warehouseId\": \"1996901321700016135\", \"warehouseId_name\": \"货品仓库\", \"source\": \"MR.mr_lrp_plan_order_batch\", \"store\": \"1996902524296626181\", \"isUretailVoucher\": \"false\", \"store_name\": \"零售门店\", \"org\": \"1996902524296626182\", \"org_name\": \"达利园组织\", \"custom\": \"1996903245851131908\", \"creator\": \"张三\", \"createTime\": \"2021-03-04 00:00:00\", \"modifier\": \"张三\", \"modifyTime\": \"2021-03-04 00:00:00\", \"closer\": \"李响\", \"closeTime\": \"2021-03-05 00:00:00\", \"locker\": \"张三\", \"lockTime\": \"2021-03-05 00:00:00\", \"operator\": \"1996904998192021506\", \"operator_name\": \"王晨\", \"auditor\": \"刘策\", \"auditTime\": \"2021-03-05 00:00:00\", \"auditDate\": \"2021-03-05 12:36:12\", \"submitor\": \"王晨\", \"submitTime\": \"2021-03-05 12:20:12\", \"totalQuantity\": 200, \"memo\": \"来货请购\", \"id\": \"1996904998192021507\", \"pubts\": \"2021-03-04 00:00:00\", \"tplid\": \"1996906535790313475\", \"headItem\": { \"id\": \"1996906535790313476\", \"define1\": \"表头自定义项1\", \"define2\": \"表头自定义项2\", \"define3\": \"表头自定义项3\" }, \"applyorders_execStatus\": \"表头自定义项4\", \"applyorders_receiveOrg\": \"1996916044853673985\", \"applyorders_receiveOrg_name\": \"达利园组织\", \"applyorders_purchaseOrg\": \"1996916044853673985\", \"applyorders_purchaseOrg_name\": \"达利园组织\", \"applyorders_purDept\": \"1996916044853673986\", \"applyorders_purDept_name\": \"采购部门\", \"applyorders_purPerson\": \"1996916551654047750\", \"applyorders_purPerson_name\": \"李晨\", \"applyOrders_supplyMoney\": \"200\", \"applyOrder_orderMoneyRatio\": \"20\", \"applyorders_supplyCount\": 20, \"apporders_id\": \"1996916551654047751\", \"applyorders_product\": \"1996917006926348290\", \"product_defaultAlbumId\": \"1996917006926348291\", \"applyorders_product_cCode\": \"00000002\", \"applyorders_product_cName\": \"苹果\", \"applyorders_productsku\": \"1996917006926348290\", \"applyorders_productsku_cCode\": \"00000002\", \"applyorders_productsku_cName\": \"苹果\", \"applyorders_currency\": \"1996917006926348290\", \"applyorders_currency_name\": \"人民币\", \"applyorders_currency_priceDigit\": \"2\", \"applyorders_currency_moneyDigit\": \"2\", \"applyorders_qty\": 20, \"applyorders_subQty\": 20, \"applyorders_rowno\": 10, \"unit_Precision\": \"2\", \"applyorders_unit\": \"1996918364130246657\", \"applyorders_unit_name\": \"吨\", \"applyorders_product_oUnitId\": \"1996918364130246657\", \"applyorders_product_productOfflineRetail_purchaseUnit\": \"吨\", \"applyorders_invExchRate\": 1, \"applyorders_productOfflineRetail_purchaseRate\": 1, \"priceUOM\": \"1996918364130246658\", \"priceUOM_Name\": \"吨\", \"invPriceExchRate\": 1, \"unitExchangeTypePrice\": \"0\", \"priceUOM_Precision\": \"2\", \"taxRate\": \"2\", \"oriTax\": 20, \"oriTaxUnitPrice\": 12, \"oriUnitPrice\": 6, \"oriMoney\": 150, \"oriSum\": 200, \"applyorders_product_primeCosts\": 23, \"applyorders_productsku_primeCosts\": 24, \"applyorders_requirementDate\": \"2021-03-04 00:00:00\", \"applyorders_adviseOrderDate\": \"2021-03-04 00:00:00\", \"applyorders_adviseSupplier\": \"1996919652626202626\", \"applyorders_adviseSupplier_name\": \"达利园供应商\", \"applyorders_vendor\": \"1996919652626202626\", \"applyorders_vendor_name\": \"达利园供应商\", \"applyorders_memo\": \"要货请购\", \"bodyItem\": { \"id\": \"1996919652626202627\", \"define1\": \"表体自定义项1\", \"define2\": \"表体自定义项2\", \"define3\": \"表体自定义项3\", \"define56\": \"表体自定义项56\", \"define57\": \"表体自定义项57\", \"define58\": \"表体自定义项58\", \"define59\": \"表体自定义项59\", \"define60\": \"表体自定义项60\", \"define4\": \"表体自定义项4\", \"define31\": \"表体自定义项31\", \"define32\": \"表体自定义项32\", \"define33\": \"表体自定义项33\", \"define34\": \"表体自定义项34\", \"define35\": \"表体自定义项35\", \"define36\": \"表体自定义项36\", \"define37\": \"表体自定义项37\", \"define38\": \"表体自定义项38\", \"define39\": \"表体自定义项39\", \"define40\": \"表体自定义项40\", \"define41\": \"表体自定义项41\", \"define42\": \"表体自定义项42\", \"define43\": \"表体自定义项43\", \"define44\": \"表体自定义项44\", \"define45\": \"表体自定义项45\", \"define46\": \"表体自定义项46\", \"define47\": \"表体自定义项47\", \"define48\": \"表体自定义项48\", \"define49\": \"表体自定义项49\", \"define50\": \"表体自定义项50\", \"define51\": \"表体自定义项51\", \"define52\": \"表体自定义项52\", \"define53\": \"表体自定义项53\", \"define54\": \"表体自定义项54\", \"define55\": \"表体自定义项55\", \"define5\": \"表体自定义项5\", \"define6\": \"表体自定义项6\", \"define7\": \"表体自定义项7\", \"define8\": \"表体自定义项8\", \"define9\": \"表体自定义项9\", \"define10\": \"表体自定义项10\", \"define11\": \"表体自定义项11\", \"define12\": \"表体自定义项12\", \"define13\": \"表体自定义项13\", \"define14\": \"表体自定义项14\", \"define15\": \"表体自定义项15\", \"define16\": \"表体自定义项16\", \"define17\": \"表体自定义项17\", \"define18\": \"表体自定义项18\", \"define19\": \"表体自定义项19\", \"define20\": \"表体自定义项20\", \"define21\": \"表体自定义项21\", \"define22\": \"表体自定义项22\", \"define23\": \"表体自定义项23\", \"define24\": \"表体自定义项24\", \"define25\": \"表体自定义项25\", \"define26\": \"表体自定义项26\", \"define27\": \"表体自定义项27\", \"define28\": \"表体自定义项28\", \"define29\": \"表体自定义项29\", \"define30\": \"表体自定义项30\" }, \"applyorders_productsku_modelDescription\": \"33mm\", \"applyorders_product_model\": \"33mm\", \"applyorders_product_modelDescription\": \"细口\", \"applyorders_propertiesValue\": \"细口\", \"project\": \"1996923973363302404\", \"project_code\": \"00034\", \"applyOrders_vendor_define1\": \"供应商自定义项1\", \"project_name\": \"虚拟项目\", \"applyOrders_vendor_define2\": \"供应商自定义项2\", \"applyorders_trackNo\": \"0002\", \"applyOrders_vendor_define3\": \"供应商自定义项3\", \"applyOrders_vendor_define4\": \"供应商自定义项4\", \"applyOrders_vendor_define5\": \"供应商自定义项5\", \"applyOrders_vendor_define6\": \"供应商自定义项6\", \"applyOrders_vendor_define7\": \"供应商自定义项7\", \"applyOrders_vendor_define8\": \"供应商自定义项8\", \"applyOrders_vendor_define9\": \"供应商自定义项9\", \"applyOrders_vendor_define10\": \"供应商自定义项10\", \"applyOrders_vendor_define11\": \"供应商自定义项11\", \"applyOrders_vendor_define12\": \"供应商自定义项12\", \"applyOrders_vendor_define13\": \"供应商自定义项13\", \"applyOrders_vendor_define14\": \"供应商自定义项14\", \"applyOrders_vendor_define15\": \"供应商自定义项15\", \"applyOrders_vendor_define16\": \"供应商自定义项16\", \"applyOrders_vendor_define17\": \"供应商自定义项17\", \"applyOrders_vendor_define18\": \"供应商自定义项18\", \"applyOrders_vendor_define19\": \"供应商自定义项19\", \"applyOrders_vendor_define20\": \"供应商自定义项20\", \"applyOrders_vendor_define21\": \"供应商自定义项21\", \"applyOrders_vendor_define22\": \"供应商自定义项22\", \"applyOrders_vendor_define23\": \"供应商自定义项23\", \"applyOrders_vendor_define24\": \"供应商自定义项24\", \"applyOrders_vendor_define25\": \"供应商自定义项25\", \"applyOrders_vendor_define26\": \"供应商自定义项26\", \"applyOrders_vendor_define27\": \"供应商自定义项27\", \"applyOrders_vendor_define28\": \"供应商自定义项28\", \"applyOrders_vendor_define29\": \"供应商自定义项29\", \"applyOrders_vendor_define30\": \"供应商自定义项30\", \"applyOrderDefineCharacter\": \"id : \\\"1995982748459204616\\\" pubts : \\\"2024-05-13 09:16:15\\\" ytenant : \\\"0000LJ5I3I7H6YAPZ90000\\\"\", \"applyOrdersDefineCharacter\": \"id : \\\"1995982748459204616\\\" pubts : \\\"2024-05-13 09:16:15\\\" ytenant : \\\"0000LJ5I3I7H6YAPZ90000\\\"\", \"applyOrdersCharacteristics\": \"id : \\\"1995982748459204616\\\" pubts : \\\"2024-05-13 09:16:15\\\" ytenant : \\\"0000LJ5I3I7H6YAPZ90000\\\"\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2106049445846056980, "apiId": "0f1453d26e6741faa95ace9533a61683", "content": "{ \"code\": 999, \"message\": \"服务端逻辑异常\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "apiDemoReturnDTOList": {"apiDemoReturnDTOList": [{"id": 2106049445846056979, "apiId": "0f1453d26e6741faa95ace9533a61683", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"pageIndex\": 1, \"pageSize\": 10, \"pageCount\": 10, \"beginPageIndex\": 1, \"endPageIndex\": 10, \"recordCount\": 100, \"pubts\": \"2024-03-04 00:00:00\", \"recordList\": [ { \"vouchdate\": \"2021-03-04 00:00:00\", \"code\": \"CGQG0000201905100001\", \"returncount\": \"1\", \"isWfControlled\": \"1\", \"verifystate\": \"1\", \"bustype\": \"****************\", \"bustype_name\": \"采购要货\", \"applyDept\": \"1996900540021735427\", \"applyDept_name\": \"数智请购部门\", \"bizstatus\": \"0\", \"status\": \"0\", \"currency\": \"1996901321700016134\", \"currency_name\": \"人名币\", \"warehouseId\": \"1996901321700016135\", \"warehouseId_name\": \"货品仓库\", \"source\": \"MR.mr_lrp_plan_order_batch\", \"store\": \"1996902524296626181\", \"isUretailVoucher\": \"false\", \"store_name\": \"零售门店\", \"org\": \"1996902524296626182\", \"org_name\": \"达利园组织\", \"custom\": \"1996903245851131908\", \"creator\": \"张三\", \"createTime\": \"2021-03-04 00:00:00\", \"modifier\": \"张三\", \"modifyTime\": \"2021-03-04 00:00:00\", \"closer\": \"李响\", \"closeTime\": \"2021-03-05 00:00:00\", \"locker\": \"张三\", \"lockTime\": \"2021-03-05 00:00:00\", \"operator\": \"1996904998192021506\", \"operator_name\": \"王晨\", \"auditor\": \"刘策\", \"auditTime\": \"2021-03-05 00:00:00\", \"auditDate\": \"2021-03-05 12:36:12\", \"submitor\": \"王晨\", \"submitTime\": \"2021-03-05 12:20:12\", \"totalQuantity\": 200, \"memo\": \"来货请购\", \"id\": \"1996904998192021507\", \"pubts\": \"2021-03-04 00:00:00\", \"tplid\": \"1996906535790313475\", \"headItem\": { \"id\": \"1996906535790313476\", \"define1\": \"表头自定义项1\", \"define2\": \"表头自定义项2\", \"define3\": \"表头自定义项3\" }, \"applyorders_execStatus\": \"表头自定义项4\", \"applyorders_receiveOrg\": \"1996916044853673985\", \"applyorders_receiveOrg_name\": \"达利园组织\", \"applyorders_purchaseOrg\": \"1996916044853673985\", \"applyorders_purchaseOrg_name\": \"达利园组织\", \"applyorders_purDept\": \"1996916044853673986\", \"applyorders_purDept_name\": \"采购部门\", \"applyorders_purPerson\": \"1996916551654047750\", \"applyorders_purPerson_name\": \"李晨\", \"applyOrders_supplyMoney\": \"200\", \"applyOrder_orderMoneyRatio\": \"20\", \"applyorders_supplyCount\": 20, \"apporders_id\": \"1996916551654047751\", \"applyorders_product\": \"1996917006926348290\", \"product_defaultAlbumId\": \"1996917006926348291\", \"applyorders_product_cCode\": \"00000002\", \"applyorders_product_cName\": \"苹果\", \"applyorders_productsku\": \"1996917006926348290\", \"applyorders_productsku_cCode\": \"00000002\", \"applyorders_productsku_cName\": \"苹果\", \"applyorders_currency\": \"1996917006926348290\", \"applyorders_currency_name\": \"人民币\", \"applyorders_currency_priceDigit\": \"2\", \"applyorders_currency_moneyDigit\": \"2\", \"applyorders_qty\": 20, \"applyorders_subQty\": 20, \"applyorders_rowno\": 10, \"unit_Precision\": \"2\", \"applyorders_unit\": \"1996918364130246657\", \"applyorders_unit_name\": \"吨\", \"applyorders_product_oUnitId\": \"1996918364130246657\", \"applyorders_product_productOfflineRetail_purchaseUnit\": \"吨\", \"applyorders_invExchRate\": 1, \"applyorders_productOfflineRetail_purchaseRate\": 1, \"priceUOM\": \"1996918364130246658\", \"priceUOM_Name\": \"吨\", \"invPriceExchRate\": 1, \"unitExchangeTypePrice\": \"0\", \"priceUOM_Precision\": \"2\", \"taxRate\": \"2\", \"oriTax\": 20, \"oriTaxUnitPrice\": 12, \"oriUnitPrice\": 6, \"oriMoney\": 150, \"oriSum\": 200, \"applyorders_product_primeCosts\": 23, \"applyorders_productsku_primeCosts\": 24, \"applyorders_requirementDate\": \"2021-03-04 00:00:00\", \"applyorders_adviseOrderDate\": \"2021-03-04 00:00:00\", \"applyorders_adviseSupplier\": \"1996919652626202626\", \"applyorders_adviseSupplier_name\": \"达利园供应商\", \"applyorders_vendor\": \"1996919652626202626\", \"applyorders_vendor_name\": \"达利园供应商\", \"applyorders_memo\": \"要货请购\", \"bodyItem\": { \"id\": \"1996919652626202627\", \"define1\": \"表体自定义项1\", \"define2\": \"表体自定义项2\", \"define3\": \"表体自定义项3\", \"define56\": \"表体自定义项56\", \"define57\": \"表体自定义项57\", \"define58\": \"表体自定义项58\", \"define59\": \"表体自定义项59\", \"define60\": \"表体自定义项60\", \"define4\": \"表体自定义项4\", \"define31\": \"表体自定义项31\", \"define32\": \"表体自定义项32\", \"define33\": \"表体自定义项33\", \"define34\": \"表体自定义项34\", \"define35\": \"表体自定义项35\", \"define36\": \"表体自定义项36\", \"define37\": \"表体自定义项37\", \"define38\": \"表体自定义项38\", \"define39\": \"表体自定义项39\", \"define40\": \"表体自定义项40\", \"define41\": \"表体自定义项41\", \"define42\": \"表体自定义项42\", \"define43\": \"表体自定义项43\", \"define44\": \"表体自定义项44\", \"define45\": \"表体自定义项45\", \"define46\": \"表体自定义项46\", \"define47\": \"表体自定义项47\", \"define48\": \"表体自定义项48\", \"define49\": \"表体自定义项49\", \"define50\": \"表体自定义项50\", \"define51\": \"表体自定义项51\", \"define52\": \"表体自定义项52\", \"define53\": \"表体自定义项53\", \"define54\": \"表体自定义项54\", \"define55\": \"表体自定义项55\", \"define5\": \"表体自定义项5\", \"define6\": \"表体自定义项6\", \"define7\": \"表体自定义项7\", \"define8\": \"表体自定义项8\", \"define9\": \"表体自定义项9\", \"define10\": \"表体自定义项10\", \"define11\": \"表体自定义项11\", \"define12\": \"表体自定义项12\", \"define13\": \"表体自定义项13\", \"define14\": \"表体自定义项14\", \"define15\": \"表体自定义项15\", \"define16\": \"表体自定义项16\", \"define17\": \"表体自定义项17\", \"define18\": \"表体自定义项18\", \"define19\": \"表体自定义项19\", \"define20\": \"表体自定义项20\", \"define21\": \"表体自定义项21\", \"define22\": \"表体自定义项22\", \"define23\": \"表体自定义项23\", \"define24\": \"表体自定义项24\", \"define25\": \"表体自定义项25\", \"define26\": \"表体自定义项26\", \"define27\": \"表体自定义项27\", \"define28\": \"表体自定义项28\", \"define29\": \"表体自定义项29\", \"define30\": \"表体自定义项30\" }, \"applyorders_productsku_modelDescription\": \"33mm\", \"applyorders_product_model\": \"33mm\", \"applyorders_product_modelDescription\": \"细口\", \"applyorders_propertiesValue\": \"细口\", \"project\": \"1996923973363302404\", \"project_code\": \"00034\", \"applyOrders_vendor_define1\": \"供应商自定义项1\", \"project_name\": \"虚拟项目\", \"applyOrders_vendor_define2\": \"供应商自定义项2\", \"applyorders_trackNo\": \"0002\", \"applyOrders_vendor_define3\": \"供应商自定义项3\", \"applyOrders_vendor_define4\": \"供应商自定义项4\", \"applyOrders_vendor_define5\": \"供应商自定义项5\", \"applyOrders_vendor_define6\": \"供应商自定义项6\", \"applyOrders_vendor_define7\": \"供应商自定义项7\", \"applyOrders_vendor_define8\": \"供应商自定义项8\", \"applyOrders_vendor_define9\": \"供应商自定义项9\", \"applyOrders_vendor_define10\": \"供应商自定义项10\", \"applyOrders_vendor_define11\": \"供应商自定义项11\", \"applyOrders_vendor_define12\": \"供应商自定义项12\", \"applyOrders_vendor_define13\": \"供应商自定义项13\", \"applyOrders_vendor_define14\": \"供应商自定义项14\", \"applyOrders_vendor_define15\": \"供应商自定义项15\", \"applyOrders_vendor_define16\": \"供应商自定义项16\", \"applyOrders_vendor_define17\": \"供应商自定义项17\", \"applyOrders_vendor_define18\": \"供应商自定义项18\", \"applyOrders_vendor_define19\": \"供应商自定义项19\", \"applyOrders_vendor_define20\": \"供应商自定义项20\", \"applyOrders_vendor_define21\": \"供应商自定义项21\", \"applyOrders_vendor_define22\": \"供应商自定义项22\", \"applyOrders_vendor_define23\": \"供应商自定义项23\", \"applyOrders_vendor_define24\": \"供应商自定义项24\", \"applyOrders_vendor_define25\": \"供应商自定义项25\", \"applyOrders_vendor_define26\": \"供应商自定义项26\", \"applyOrders_vendor_define27\": \"供应商自定义项27\", \"applyOrders_vendor_define28\": \"供应商自定义项28\", \"applyOrders_vendor_define29\": \"供应商自定义项29\", \"applyOrders_vendor_define30\": \"供应商自定义项30\", \"applyOrderDefineCharacter\": \"id : \\\"1995982748459204616\\\" pubts : \\\"2024-05-13 09:16:15\\\" ytenant : \\\"0000LJ5I3I7H6YAPZ90000\\\"\", \"applyOrdersDefineCharacter\": \"id : \\\"1995982748459204616\\\" pubts : \\\"2024-05-13 09:16:15\\\" ytenant : \\\"0000LJ5I3I7H6YAPZ90000\\\"\", \"applyOrdersCharacteristics\": \"id : \\\"1995982748459204616\\\" pubts : \\\"2024-05-13 09:16:15\\\" ytenant : \\\"0000LJ5I3I7H6YAPZ90000\\\"\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, {"id": 2106049445846056980, "apiId": "0f1453d26e6741faa95ace9533a61683", "content": "{ \"code\": 999, \"message\": \"服务端逻辑异常\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}]}, "routingStgy": 0, "routingStgyList": "", "apiDemoReturnDTO": {"id": 2106049445846056979, "apiId": "0f1453d26e6741faa95ace9533a61683", "content": "{ \"code\": \"\", \"message\": \"\", \"data\": { \"pageIndex\": 1, \"pageSize\": 10, \"pageCount\": 10, \"beginPageIndex\": 1, \"endPageIndex\": 10, \"recordCount\": 100, \"pubts\": \"2024-03-04 00:00:00\", \"recordList\": [ { \"vouchdate\": \"2021-03-04 00:00:00\", \"code\": \"CGQG0000201905100001\", \"returncount\": \"1\", \"isWfControlled\": \"1\", \"verifystate\": \"1\", \"bustype\": \"****************\", \"bustype_name\": \"采购要货\", \"applyDept\": \"1996900540021735427\", \"applyDept_name\": \"数智请购部门\", \"bizstatus\": \"0\", \"status\": \"0\", \"currency\": \"1996901321700016134\", \"currency_name\": \"人名币\", \"warehouseId\": \"1996901321700016135\", \"warehouseId_name\": \"货品仓库\", \"source\": \"MR.mr_lrp_plan_order_batch\", \"store\": \"1996902524296626181\", \"isUretailVoucher\": \"false\", \"store_name\": \"零售门店\", \"org\": \"1996902524296626182\", \"org_name\": \"达利园组织\", \"custom\": \"1996903245851131908\", \"creator\": \"张三\", \"createTime\": \"2021-03-04 00:00:00\", \"modifier\": \"张三\", \"modifyTime\": \"2021-03-04 00:00:00\", \"closer\": \"李响\", \"closeTime\": \"2021-03-05 00:00:00\", \"locker\": \"张三\", \"lockTime\": \"2021-03-05 00:00:00\", \"operator\": \"1996904998192021506\", \"operator_name\": \"王晨\", \"auditor\": \"刘策\", \"auditTime\": \"2021-03-05 00:00:00\", \"auditDate\": \"2021-03-05 12:36:12\", \"submitor\": \"王晨\", \"submitTime\": \"2021-03-05 12:20:12\", \"totalQuantity\": 200, \"memo\": \"来货请购\", \"id\": \"1996904998192021507\", \"pubts\": \"2021-03-04 00:00:00\", \"tplid\": \"1996906535790313475\", \"headItem\": { \"id\": \"1996906535790313476\", \"define1\": \"表头自定义项1\", \"define2\": \"表头自定义项2\", \"define3\": \"表头自定义项3\" }, \"applyorders_execStatus\": \"表头自定义项4\", \"applyorders_receiveOrg\": \"1996916044853673985\", \"applyorders_receiveOrg_name\": \"达利园组织\", \"applyorders_purchaseOrg\": \"1996916044853673985\", \"applyorders_purchaseOrg_name\": \"达利园组织\", \"applyorders_purDept\": \"1996916044853673986\", \"applyorders_purDept_name\": \"采购部门\", \"applyorders_purPerson\": \"1996916551654047750\", \"applyorders_purPerson_name\": \"李晨\", \"applyOrders_supplyMoney\": \"200\", \"applyOrder_orderMoneyRatio\": \"20\", \"applyorders_supplyCount\": 20, \"apporders_id\": \"1996916551654047751\", \"applyorders_product\": \"1996917006926348290\", \"product_defaultAlbumId\": \"1996917006926348291\", \"applyorders_product_cCode\": \"00000002\", \"applyorders_product_cName\": \"苹果\", \"applyorders_productsku\": \"1996917006926348290\", \"applyorders_productsku_cCode\": \"00000002\", \"applyorders_productsku_cName\": \"苹果\", \"applyorders_currency\": \"1996917006926348290\", \"applyorders_currency_name\": \"人民币\", \"applyorders_currency_priceDigit\": \"2\", \"applyorders_currency_moneyDigit\": \"2\", \"applyorders_qty\": 20, \"applyorders_subQty\": 20, \"applyorders_rowno\": 10, \"unit_Precision\": \"2\", \"applyorders_unit\": \"1996918364130246657\", \"applyorders_unit_name\": \"吨\", \"applyorders_product_oUnitId\": \"1996918364130246657\", \"applyorders_product_productOfflineRetail_purchaseUnit\": \"吨\", \"applyorders_invExchRate\": 1, \"applyorders_productOfflineRetail_purchaseRate\": 1, \"priceUOM\": \"1996918364130246658\", \"priceUOM_Name\": \"吨\", \"invPriceExchRate\": 1, \"unitExchangeTypePrice\": \"0\", \"priceUOM_Precision\": \"2\", \"taxRate\": \"2\", \"oriTax\": 20, \"oriTaxUnitPrice\": 12, \"oriUnitPrice\": 6, \"oriMoney\": 150, \"oriSum\": 200, \"applyorders_product_primeCosts\": 23, \"applyorders_productsku_primeCosts\": 24, \"applyorders_requirementDate\": \"2021-03-04 00:00:00\", \"applyorders_adviseOrderDate\": \"2021-03-04 00:00:00\", \"applyorders_adviseSupplier\": \"1996919652626202626\", \"applyorders_adviseSupplier_name\": \"达利园供应商\", \"applyorders_vendor\": \"1996919652626202626\", \"applyorders_vendor_name\": \"达利园供应商\", \"applyorders_memo\": \"要货请购\", \"bodyItem\": { \"id\": \"1996919652626202627\", \"define1\": \"表体自定义项1\", \"define2\": \"表体自定义项2\", \"define3\": \"表体自定义项3\", \"define56\": \"表体自定义项56\", \"define57\": \"表体自定义项57\", \"define58\": \"表体自定义项58\", \"define59\": \"表体自定义项59\", \"define60\": \"表体自定义项60\", \"define4\": \"表体自定义项4\", \"define31\": \"表体自定义项31\", \"define32\": \"表体自定义项32\", \"define33\": \"表体自定义项33\", \"define34\": \"表体自定义项34\", \"define35\": \"表体自定义项35\", \"define36\": \"表体自定义项36\", \"define37\": \"表体自定义项37\", \"define38\": \"表体自定义项38\", \"define39\": \"表体自定义项39\", \"define40\": \"表体自定义项40\", \"define41\": \"表体自定义项41\", \"define42\": \"表体自定义项42\", \"define43\": \"表体自定义项43\", \"define44\": \"表体自定义项44\", \"define45\": \"表体自定义项45\", \"define46\": \"表体自定义项46\", \"define47\": \"表体自定义项47\", \"define48\": \"表体自定义项48\", \"define49\": \"表体自定义项49\", \"define50\": \"表体自定义项50\", \"define51\": \"表体自定义项51\", \"define52\": \"表体自定义项52\", \"define53\": \"表体自定义项53\", \"define54\": \"表体自定义项54\", \"define55\": \"表体自定义项55\", \"define5\": \"表体自定义项5\", \"define6\": \"表体自定义项6\", \"define7\": \"表体自定义项7\", \"define8\": \"表体自定义项8\", \"define9\": \"表体自定义项9\", \"define10\": \"表体自定义项10\", \"define11\": \"表体自定义项11\", \"define12\": \"表体自定义项12\", \"define13\": \"表体自定义项13\", \"define14\": \"表体自定义项14\", \"define15\": \"表体自定义项15\", \"define16\": \"表体自定义项16\", \"define17\": \"表体自定义项17\", \"define18\": \"表体自定义项18\", \"define19\": \"表体自定义项19\", \"define20\": \"表体自定义项20\", \"define21\": \"表体自定义项21\", \"define22\": \"表体自定义项22\", \"define23\": \"表体自定义项23\", \"define24\": \"表体自定义项24\", \"define25\": \"表体自定义项25\", \"define26\": \"表体自定义项26\", \"define27\": \"表体自定义项27\", \"define28\": \"表体自定义项28\", \"define29\": \"表体自定义项29\", \"define30\": \"表体自定义项30\" }, \"applyorders_productsku_modelDescription\": \"33mm\", \"applyorders_product_model\": \"33mm\", \"applyorders_product_modelDescription\": \"细口\", \"applyorders_propertiesValue\": \"细口\", \"project\": \"1996923973363302404\", \"project_code\": \"00034\", \"applyOrders_vendor_define1\": \"供应商自定义项1\", \"project_name\": \"虚拟项目\", \"applyOrders_vendor_define2\": \"供应商自定义项2\", \"applyorders_trackNo\": \"0002\", \"applyOrders_vendor_define3\": \"供应商自定义项3\", \"applyOrders_vendor_define4\": \"供应商自定义项4\", \"applyOrders_vendor_define5\": \"供应商自定义项5\", \"applyOrders_vendor_define6\": \"供应商自定义项6\", \"applyOrders_vendor_define7\": \"供应商自定义项7\", \"applyOrders_vendor_define8\": \"供应商自定义项8\", \"applyOrders_vendor_define9\": \"供应商自定义项9\", \"applyOrders_vendor_define10\": \"供应商自定义项10\", \"applyOrders_vendor_define11\": \"供应商自定义项11\", \"applyOrders_vendor_define12\": \"供应商自定义项12\", \"applyOrders_vendor_define13\": \"供应商自定义项13\", \"applyOrders_vendor_define14\": \"供应商自定义项14\", \"applyOrders_vendor_define15\": \"供应商自定义项15\", \"applyOrders_vendor_define16\": \"供应商自定义项16\", \"applyOrders_vendor_define17\": \"供应商自定义项17\", \"applyOrders_vendor_define18\": \"供应商自定义项18\", \"applyOrders_vendor_define19\": \"供应商自定义项19\", \"applyOrders_vendor_define20\": \"供应商自定义项20\", \"applyOrders_vendor_define21\": \"供应商自定义项21\", \"applyOrders_vendor_define22\": \"供应商自定义项22\", \"applyOrders_vendor_define23\": \"供应商自定义项23\", \"applyOrders_vendor_define24\": \"供应商自定义项24\", \"applyOrders_vendor_define25\": \"供应商自定义项25\", \"applyOrders_vendor_define26\": \"供应商自定义项26\", \"applyOrders_vendor_define27\": \"供应商自定义项27\", \"applyOrders_vendor_define28\": \"供应商自定义项28\", \"applyOrders_vendor_define29\": \"供应商自定义项29\", \"applyOrders_vendor_define30\": \"供应商自定义项30\", \"applyOrderDefineCharacter\": \"id : \\\"1995982748459204616\\\" pubts : \\\"2024-05-13 09:16:15\\\" ytenant : \\\"0000LJ5I3I7H6YAPZ90000\\\"\", \"applyOrdersDefineCharacter\": \"id : \\\"1995982748459204616\\\" pubts : \\\"2024-05-13 09:16:15\\\" ytenant : \\\"0000LJ5I3I7H6YAPZ90000\\\"\", \"applyOrdersCharacteristics\": \"id : \\\"1995982748459204616\\\" pubts : \\\"2024-05-13 09:16:15\\\" ytenant : \\\"0000LJ5I3I7H6YAPZ90000\\\"\" } ] } }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": true, "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "edit": false, "ytenantId": "", "right": true}, "apiDemoReturnDTOError": {"id": 2106049445846056980, "apiId": "0f1453d26e6741faa95ace9533a61683", "content": "{ \"code\": 999, \"message\": \"服务端逻辑异常\" }", "returnType": "JSON", "apiDemoReturnDesc": "", "rightOrNot": false, "gmtCreate": "2024-10-08 16:33:44.000", "gmtUpdate": "2024-10-08 16:33:44.000", "apiName": "", "edit": false, "ytenantId": "", "right": false}, "errorCodeDTOS": "", "displayCodeApiConfigDTOS": "", "tokenPlugin": "", "paramParsePlugin": "", "authPlugin": {"id": "09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "", "name": "友户通token认证业务扩展插件", "configurable": false, "description": "YonsuitBusinessExtendPlugin", "pluginType": "auth", "pluginTypeName": "业务扩展插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": false, "gmtCreate": "2020-05-22 00:00:00", "gmtUpdate": "2020-05-22 00:00:00", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "0f1453d26e6741faa95ace9533a61683", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "resultParsePlugin": {"id": "w181ed01-1e9b-4350-b994-71a66f017555", "isvId": "75c13acc-ae53-4694-9a6e-3ddcce26d7f3", "code": "resultParse", "name": "返回参数转换插件", "configurable": false, "description": "解决返回值中带！的，转换为json", "pluginType": "resultParse", "pluginTypeName": "返回值解析插件", "pluginLevel": "publishapp", "classPath": "com.yonyou.ucg.auth.result.ResultMapTransferParsePlugin", "expansionConf": "", "defaultPlugin": true, "open": true, "visible": true, "gmtCreate": "2020-07-29 00:00:00", "gmtUpdate": "", "packagePath": "", "useScene": 1, "runStatus": "run", "runVersion": "", "toDeploy": false, "levelApi": true, "levelApp": false, "levelPublishapp": true, "levelSystem": false, "containsDefault": "", "levels": {"levels": ["levelApi", "levelPublishapp"]}, "levelsDesc": "", "deployStatus": "deploy", "deployVersion": "", "custom": false, "strategyId": "", "strategyName": "", "superiorId": "0f1453d26e6741faa95ace9533a61683", "ytenantId": 0, "unPluginCode": "", "runStatusDesc": "UID:P_UCG_177A9F3E05D0015D"}, "mapReturnPluginConfig": "", "billNo": "pu_applyorderlist", "domain": "upu", "apiCategory": "", "docUrl": "", "pathMatch": 0, "createUser": "", "createUserName": "", "approvalStatus": 1, "publishTime": "2024-10-08 17:35:22", "pathJoin": true, "timeOut": 30, "tokenPluginName": "", "authPluginName": "", "resultPluginName": "", "apiDemoReturnRightDemo": "", "apiDemoReturnErrorDemo": "", "mock": false, "mockTimeout": "", "customUrl": "/applyorder/list", "fixedUrl": "/yonbip/scm", "apiCode": "0f1453d26e6741faa95ace9533a61683", "tokenCheckType": 0, "enableMulti": false, "multiField": "", "idempotent": "non", "bidirectionalSSL": "", "ucgSchema": "HTTPS", "updateUserId": "6c50f52d-6387-4181-b1cd-2e07411e823d", "updateUserName": "昵称-15652370404", "paramIsForce": "", "userIDPassthrough": false, "applyUser": "", "applyMsg": "", "dr": 0, "microServiceCode": "domain.yonbip-scm-pu", "applicationCode": "yonbip-scm-pubiz", "privacyCategory": 1, "privacyLevel": 4, "apiDesigned": 0, "serviceType": 0, "integrateSchemeCode": "", "integrateSchemeName": "", "integrateObjectCode": "", "integrateObjectName": "", "integrateObjectCreatedType": "", "returnIntegObjId": "", "returnIntegObjName": "", "apiIntegrateDTOList": "", "apiRouteInfoDTOList": "", "arrayParam": false, "fileSize": "", "cc": true, "paramTransferMode": 2, "ytenantId": 0, "statusConf": "", "scene": 1, "version": "", "bizObjUri": "", "bizObjOperationType": "", "apiDefId": 1996896485556355076, "paramExtBizObjCode": "", "paramExtBizObjName": "", "paramExtRequest": 1, "paramExtResponse": 1, "paramExtInExtendKey": 1, "openScene": 1, "integrationScene": "", "apiType": "", "paramMark": "", "integrateSysId": "", "integrateSysName": "", "integrateSysCode": "", "dataZoneSetting": false, "reqDataZoneSetting": false, "respDataZoneSetting": false, "reqDataAllQuery": false, "reqDataAllBody": false, "respDataAllBody": false, "chargeStatus": 1, "beforeSpeed": 40, "afterSpeed": 80, "speedStatus": false, "reqDataRefPath": "", "respDataRefPath": "", "pubHistory": "", "deprecated": 0, "recommendedApiId": "", "recommendedApiName": "", "domainAppCode": "", "multiVersion": 0, "apiTag": ""}}, {"success": true, "code": 200, "message": "", "data": {"id": 2108770660671029249, "name": "用友YonBIP", "type": "integrateSys", "sort": 0, "enable": 0, "children": {"children": {"id": "SCC", "name": "供应链云", "type": 1, "sort": 0, "enable": 0, "children": {"children": {"id": "MM", "name": "采购供应", "type": 2, "sort": 0, "enable": 0, "children": {"children": {"id": "PU", "name": "采购管理", "type": 3, "sort": 0, "enable": 0, "children": {"children": {"id": "upu.pu_applyorder", "name": "请购单", "type": 4, "sort": 0, "enable": 0, "children": "", "parentId": "", "productId": "", "code": "upu.pu_applyorder", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "PU", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "MM", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "SCC", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "order": 0}}, "parentId": "", "productId": "", "code": "current_yonbip_default_sys", "tenantId": "", "edit": true, "addChild": true, "addBro": true, "isOrigin": 0, "hasChildren": 0, "order": 0}}]