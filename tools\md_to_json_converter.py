import json
import logging
import os
import re
import sys
from typing import Dict

from app.core.config import MODULES_CONFIG

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
YS-API V3.0 - MD文档到JSON转换器
参考V2项目架构，生成统一的md_mappings.json文件
"""


# 添加项目路径
project_root = os.path.abspath(
    os.path.join(
        os.path.dirname(__file__),
        "..",
        ".."))
v3_dir = os.path.join(project_root, "v3")
sys.path.insert(0, os.path.join(v3_dir, "backend"))


logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


def parse_md_file(file_path: str) -> Dict[str, str]:
    """
    解析单个MD文件，提取API字段名到中文名称的映射
    参考V2项目的解析逻辑
    """
    mappings = {}
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            lines = f.readlines()

        for line in lines:
            line = line.strip()
            # 跳过空行、分隔线、标题等
            if (
                not line
                or line.startswith("|--")
                or line.startswith("#")
                or "请求参数" in line
                or "返回值参数" in line
            ):
                continue

            # 匹配API字段名（英文开头）
            field_match = re.search(
                r"^\s*\|?\s*([a-zA-Z][a-zA-Z0-9_.]*)", line)
            if not field_match:
                continue

            api_field = field_match.group(1).strip()

            # 查找中文描述
            chinese_match = re.search(r"[\u4e00-\u9fa5]", line)
            if not chinese_match:
                continue

            # 提取中文描述部分
            raw_description = line[chinese_match.start():].strip()

            # 清理中文名称（参考V2逻辑）
            cleaned_name = re.sub(
                r"[\(（].*?[\)）]", "", raw_description)  # 移除括号
            cleaned_name = re.sub(
                r"[^\u4e00-\u9fa5a-zA-Z0-9\s]", " ", cleaned_name
            )  # 只保留中文、英文、数字

            # 移除干扰词和测试后缀
            unwanted_words = [
                "默认值",
                "示例",
                "区间",
                "格式",
                "true",
                "false",
                "是",
                "否",
                "类型",
                "字段",
                "test",
                "Test",
                "TEST",
            ]
            for word in unwanted_words:
                cleaned_name = cleaned_name.replace(word, "")

            # 合并空格并截断第一个空格后的内容（关键逻辑）
            final_name = " ".join(cleaned_name.split()).strip()
            if final_name:
                final_name = final_name.split(" ", 1)[0]

            # 保存映射
            if final_name and final_name != api_field:
                mappings[api_field] = final_name

    except Exception:
        logging.error(f"处理文件失败 {os.path.basename(file_path)}: {e}")

    return mappings


def generate_md_mappings_json():
    """
    生成统一的md_mappings.json文件
    """
    # 路径设置
    md_docs_dir = os.path.join(project_root, "md文档")
    logic_dir = os.path.join(v3_dir, "logic")
    output_path = os.path.join(logic_dir, "md_mappings.json")

    # 确保输出目录存在
    os.makedirs(logic_dir, exist_ok=True)

    logging.info(f"源MD文档目录: {md_docs_dir}")
    logging.info(f"目标JSON文件: {output_path}")

    # 生成所有模块的映射
    all_mappings = {}

    for module_config in MODULES_CONFIG:
        module_name = module_config["name"]
        md_file = module_config["md_file"]
        file_path = os.path.join(md_docs_dir, md_file)

        if os.path.exists(file_path):
            logging.info(f"正在处理: {module_name} - {md_file}")
            module_mappings = parse_md_file(file_path)
            all_mappings[module_name] = module_mappings
            logging.info(f"  提取到 {len(module_mappings)} 个字段映射")
        else:
            logging.warning(f"文件不存在: {file_path}")
            all_mappings[module_name] = {}

    # 保存JSON文件
    try:
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(all_mappings, f, ensure_ascii=False, indent=2)

        # 统计信息
        total_mappings = sum(len(mappings)
                             for mappings in all_mappings.values())

        logging.info(f"🎉 成功生成MD映射文件: {output_path}")
        logging.info(f"📊 统计信息:")
        logging.info(f"  - 总模块数: {len(all_mappings)}")
        logging.info(f"  - 总字段映射数: {total_mappings}")

        # 显示每个模块的统计
        for module_name, mappings in all_mappings.items():
            logging.info(f"  - {module_name}: {len(mappings)} 个映射")

    except Exception:
        logging.error(f"保存JSON文件失败: {e}")


if __name__ == "__main__":
    generate_md_mappings_json()
