# YS-API 系统架构设计

## 🏗️ 整体架构

### 架构概览
YS-API 采用现代化的前后端分离架构，基于 FastAPI 构建高性能的后端服务，配合 Vue.js 3 提供直观的前端界面。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端服务      │    │   外部系统      │
│   (Vue.js 3)    │◄──►│   (FastAPI)     │◄──►│   (用友 ERP)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户操作      │    │   数据处理      │    │   数据获取      │
│   配置管理      │    │   字段映射      │    │   API 调用      │
│   监控查看      │    │   同步调度      │    │   数据返回      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 技术架构

### 后端架构 (FastAPI)

#### 1. **应用层 (Application Layer)**
```
app/
├── main.py                 # 应用入口
├── __init__.py            # 应用初始化
├── api/                   # API 路由层
│   ├── v1/               # API 版本1
│   │   ├── config.py     # 字段配置 API
│   │   ├── database.py   # 数据库管理 API
│   │   ├── monitor.py    # 监控 API
│   │   ├── sync.py       # 同步 API
│   │   ├── tasks.py      # 任务 API
│   │   └── unified_field_config.py  # 统一字段配置 API
├── core/                  # 核心配置
│   ├── config.py         # 系统配置
│   └── database.py       # 数据库配置
├── middleware/            # 中间件
│   └── access_log.py     # 访问日志中间件
├── schemas/              # 数据模型
│   ├── base.py          # 基础模型
│   ├── config.py        # 配置模型
│   ├── database.py      # 数据库模型
│   ├── monitor.py       # 监控模型
│   └── sync.py          # 同步模型
└── services/            # 业务服务层
    ├── auto_sync_scheduler.py      # 自动同步调度器
    ├── business_translation_rules.py # 业务翻译规则
    ├── data_processor.py           # 数据处理器
    ├── data_write_manager.py       # 数据写入管理器
    ├── database_manager.py         # 数据库管理器
    ├── field_config_service.py     # 字段配置服务
    ├── field_extractor.py          # 字段提取器
    ├── intelligent_field_mapper.py # 智能字段映射器
    ├── realtime_log_service.py     # 实时日志服务
    ├── sync_service.py             # 同步服务
    ├── unified_field_manager.py    # 统一字段管理器
    └── ys_api_client.py            # 用友 API 客户端
```

#### 2. **服务层设计**

##### 核心服务模块

**字段配置服务 (Field Config Service)**
- 功能：管理所有模块的字段配置
- 特性：
  - 支持多用户配置隔离
  - 智能字段映射
  - 配置版本管理
  - 批量操作支持

**智能翻译引擎 (Intelligent Translation Engine)**
- 功能：4层翻译策略实现
- 层级：
  1. 业务规则翻译
  2. 增强翻译
  3. 智能分词
  4. 基础翻译
- 特性：
  - 置信度评估
  - JSON 同步机制
  - Excel 智能匹配

**数据同步服务 (Data Sync Service)**
- 功能：处理数据同步任务
- 特性：
  - 自动同步调度
  - 增量同步支持
  - 错误重试机制
  - 性能监控

**数据库管理服务 (Database Manager)**
- 功能：管理数据库表结构
- 特性：
  - 自动表创建
  - 字段类型映射
  - 索引优化
  - 数据迁移

### 前端架构 (Vue.js 3)

#### 1. **页面结构**
```
frontend/
├── css/                    # 样式文件
│   ├── element-plus.css   # Element Plus 样式
│   └── realtime-log.css   # 实时日志样式
├── js/                    # JavaScript 文件
│   ├── api-config.js      # API 配置
│   ├── api-unified.js     # 统一 API
│   ├── element-plus.js    # Element Plus
│   ├── realtime-log.js    # 实时日志
│   └── vue.global.js      # Vue 3
├── field-config.html      # 字段配置页面
├── unified-field-config.html  # 统一字段配置页面
├── excel-translation.html # Excel 翻译页面
├── realtime-log.html      # 实时日志页面
└── database-v2.html       # 数据库管理页面
```

#### 2. **组件设计**

**字段配置组件**
- 功能：可视化字段配置管理
- 特性：
  - 16:9 自适应布局
  - 实时搜索过滤
  - 批量选择操作
  - 配置保存确认

**统一字段管理组件**
- 功能：集中管理所有模块字段
- 特性：
  - 分组显示字段
  - 示例数据展示
  - 用户修改标记
  - 锁定状态管理

**实时监控组件**
- 功能：实时显示系统状态
- 特性：
  - WebSocket 连接
  - 实时日志滚动
  - 状态指示器
  - 错误高亮显示

## 📊 数据流架构

### 1. **字段配置数据流**
```
用户操作 → 前端界面 → API 请求 → 字段配置服务 → 数据库存储
    ↑                                                      ↓
    └─────────────── 配置响应 ← 数据验证 ← 业务处理 ←────────┘
```

### 2. **数据同步数据流**
```
定时触发 → 同步服务 → 用友 API → 数据获取 → 数据处理 → 数据库写入
    ↑                                                              ↓
    └─────────────── 状态更新 ← 日志记录 ← 错误处理 ←──────────────┘
```

### 3. **智能翻译数据流**
```
Excel 上传 → 文件解析 → 字段提取 → 4层翻译 → 置信度评估 → 结果保存
    ↑                                                              ↓
    └─────────────── 翻译结果 ← JSON 同步 ← 质量检查 ←─────────────┘
```

## 🔄 模块交互架构

### 1. **API 路由层**
- **RESTful API 设计**：标准化的 API 接口
- **版本控制**：支持 API 版本管理
- **参数验证**：使用 Pydantic 进行数据验证
- **错误处理**：统一的错误响应格式

### 2. **业务服务层**
- **依赖注入**：使用 FastAPI 的依赖注入系统
- **异步处理**：支持异步操作提升性能
- **服务隔离**：各服务模块职责明确
- **配置管理**：统一的配置管理机制

### 3. **数据访问层**
- **ORM 映射**：使用 SQLAlchemy 进行数据库操作
- **连接池**：数据库连接池管理
- **事务管理**：支持数据库事务
- **迁移支持**：数据库结构迁移

## 🚀 性能优化架构

### 1. **缓存策略**
- **Redis 缓存**：热点数据缓存
- **内存缓存**：配置数据缓存
- **CDN 加速**：静态资源加速

### 2. **异步处理**
- **Celery 任务队列**：异步任务处理
- **WebSocket**：实时通信
- **异步 I/O**：非阻塞操作

### 3. **数据库优化**
- **索引优化**：关键字段索引
- **查询优化**：SQL 查询优化
- **分页处理**：大数据量分页

## 🔒 安全架构

### 1. **访问控制**
- **用户认证**：基于用户 ID 的访问控制
- **权限管理**：模块级别的权限控制
- **API 限流**：防止 API 滥用

### 2. **数据安全**
- **数据加密**：敏感数据加密存储
- **传输安全**：HTTPS 传输
- **输入验证**：防止 SQL 注入和 XSS

### 3. **日志审计**
- **操作日志**：记录所有用户操作
- **错误日志**：记录系统错误
- **访问日志**：记录 API 访问

## 📈 监控架构

### 1. **性能监控**
- **响应时间**：API 响应时间监控
- **吞吐量**：系统吞吐量监控
- **资源使用**：CPU、内存、磁盘使用监控

### 2. **业务监控**
- **同步状态**：数据同步状态监控
- **错误率**：系统错误率监控
- **用户行为**：用户操作行为分析

### 3. **告警机制**
- **阈值告警**：性能指标阈值告警
- **异常告警**：系统异常告警
- **业务告警**：业务异常告警

## 🔧 部署架构

### 1. **容器化部署**
- **Docker 容器**：应用容器化
- **Docker Compose**：多服务编排
- **镜像管理**：版本化镜像管理

### 2. **负载均衡**
- **Nginx 反向代理**：请求分发
- **健康检查**：服务健康状态检查
- **故障转移**：服务故障自动转移

### 3. **数据备份**
- **定期备份**：数据库定期备份
- **增量备份**：增量数据备份
- **灾难恢复**：灾难恢复方案

---

*最后更新时间：2024年12月*
*版本：V3.2* 