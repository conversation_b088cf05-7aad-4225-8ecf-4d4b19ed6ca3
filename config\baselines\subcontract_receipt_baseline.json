{"module_name": "subcontract_receipt", "display_name": "委外入库", "version": "2.0.0", "source": "json_parser", "total_fields": 145, "created_at": "2025-07-28T20:12:24.835969", "last_updated": "2025-07-28T20:12:24.835969", "fields": {"code": {"api_field_name": "code", "chinese_name": "单据编号", "data_type": "NVARCHAR(500)", "param_desc": "单据编号", "path": "data.recordList.code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "message": {"api_field_name": "message", "chinese_name": "调用失败时的错误信息", "data_type": "NVARCHAR(500)", "param_desc": "调用失败时的错误信息", "path": "message", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "调用成功时的返回数据", "data_type": "NVARCHAR(MAX)", "param_desc": "调用成功时的返回数据", "path": "data", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageIndex": {"api_field_name": "pageIndex", "chinese_name": "当前页数", "data_type": "BIGINT", "param_desc": "当前页数", "path": "pageIndex", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageSize": {"api_field_name": "pageSize", "chinese_name": "每页显示数据数", "data_type": "BIGINT", "param_desc": "每页显示数据数", "path": "pageSize", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageCount": {"api_field_name": "pageCount", "chinese_name": "总页数", "data_type": "BIGINT", "param_desc": "总页数", "path": "data.pageCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "beginPageIndex": {"api_field_name": "beginPageIndex", "chinese_name": "开始页", "data_type": "BIGINT", "param_desc": "开始页", "path": "data.beginPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "endPageIndex": {"api_field_name": "endPageIndex", "chinese_name": "结束页", "data_type": "BIGINT", "param_desc": "结束页", "path": "data.endPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordCount": {"api_field_name": "recordCount", "chinese_name": "总记录数", "data_type": "BIGINT", "param_desc": "总记录数", "path": "data.recordCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordList": {"api_field_name": "recordList", "chinese_name": "返回结果对象", "data_type": "NVARCHAR(MAX)", "param_desc": "返回结果对象", "path": "data.recordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "osmInRecords_productionType": {"api_field_name": "osmInRecords_productionType", "chinese_name": "产出类型", "data_type": "BIGINT", "param_desc": "产出类型", "path": "data.recordList.osmInRecords_productionType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vendor_code": {"api_field_name": "vendor_code", "chinese_name": "委外供应商编码", "data_type": "NVARCHAR(500)", "param_desc": "委外供应商编码", "path": "data.recordList.vendor_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "oriTax": {"api_field_name": "oriTax", "chinese_name": "税额", "data_type": "NVARCHAR(500)", "param_desc": "税额", "path": "data.recordList.oriTax", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pocode": {"api_field_name": "pocode", "chinese_name": "委外订单编码", "data_type": "NVARCHAR(500)", "param_desc": "委外订单编码", "path": "data.recordList.pocode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_cCode": {"api_field_name": "product_cCode", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "data.recordList.product_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invoiceVendor": {"api_field_name": "invoiceVendor", "chinese_name": "开票供应商ID", "data_type": "BIGINT", "param_desc": "开票供应商ID", "path": "data.recordList.invoiceVendor", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "sfee": {"api_field_name": "sfee", "chinese_name": "累计结算费用", "data_type": "BIGINT", "param_desc": "累计结算费用", "path": "data.recordList.sfee", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "priceUOM_Precision": {"api_field_name": "priceUOM_Precision", "chinese_name": "计价单位精度", "data_type": "BIGINT", "param_desc": "计价单位精度", "path": "data.recordList.priceUOM_Precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "memo": {"api_field_name": "memo", "chinese_name": "备注", "data_type": "NVARCHAR(500)", "param_desc": "备注", "path": "data.recordList.memo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockStatusDoc_name": {"api_field_name": "stockStatusDoc_name", "chinese_name": "库存状态", "data_type": "NVARCHAR(500)", "param_desc": "库存状态", "path": "data.recordList.stockStatusDoc_name", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "priceUOM_Code": {"api_field_name": "priceUOM_Code", "chinese_name": "计价单位编码", "data_type": "NVARCHAR(500)", "param_desc": "计价单位编码", "path": "data.recordList.priceUOM_Code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalQuantity": {"api_field_name": "totalQuantity", "chinese_name": "整单数量", "data_type": "BIGINT", "param_desc": "整单数量", "path": "data.recordList.totalQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natCurrency": {"api_field_name": "natCurrency", "chinese_name": "本币ID", "data_type": "NVARCHAR(500)", "param_desc": "本币ID", "path": "data.recordList.natCurrency", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "taxitems_name": {"api_field_name": "taxitems_name", "chinese_name": "税目名称", "data_type": "NVARCHAR(500)", "param_desc": "税目名称", "path": "data.recordList.taxitems_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockUnitId_Precision": {"api_field_name": "stockUnitId_Precision", "chinese_name": "库存单位精度", "data_type": "BIGINT", "param_desc": "库存单位精度", "path": "data.recordList.stockUnitId_Precision", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "costMoney": {"api_field_name": "costMoney", "chinese_name": "成本金额", "data_type": "DECIMAL(18,4)", "param_desc": "成本金额", "path": "data.recordList.costMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "id": {"api_field_name": "id", "chinese_name": "单据主表id", "data_type": "BIGINT", "param_desc": "单据主表id", "path": "data.recordList.id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "tplid": {"api_field_name": "tplid", "chinese_name": "模板id", "data_type": "BIGINT", "param_desc": "模板id", "path": "data.recordList.tplid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isWfControlled": {"api_field_name": "isWfControlled", "chinese_name": "是否审批流控制（true:审批流控制 false:非审批流控制）", "data_type": "BIT", "param_desc": "是否审批流控制（true:审批流控制 false:非审批流控制）", "path": "data.recordList.isWfControlled", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natSum": {"api_field_name": "natSum", "chinese_name": "本币含税金额", "data_type": "DECIMAL(18,4)", "param_desc": "本币含税金额", "path": "data.recordList.natSum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "warehouse": {"api_field_name": "warehouse", "chinese_name": "仓库id", "data_type": "BIGINT", "param_desc": "仓库id", "path": "data.recordList.warehouse", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isAutomaticVerify": {"api_field_name": "isAutomaticVerify", "chinese_name": "是否自动核销，true:是、false:否", "data_type": "BIT", "param_desc": "是否自动核销，true:是、false:否", "path": "data.recordList.isAutomaticVerify", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "warehouse_name": {"api_field_name": "warehouse_name", "chinese_name": "仓库", "data_type": "NVARCHAR(500)", "param_desc": "仓库", "path": "data.recordList.warehouse_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "auditTime": {"api_field_name": "auditTime", "chinese_name": "审核时间", "data_type": "NVARCHAR(500)", "param_desc": "审核时间", "path": "data.recordList.auditTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_priceDigit": {"api_field_name": "natCurrency_priceDigit", "chinese_name": "本币单价精度", "data_type": "BIGINT", "param_desc": "本币单价精度", "path": "data.recordList.natCurrency_priceDigit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "exchRateType": {"api_field_name": "exchRateType", "chinese_name": "汇率类型ID", "data_type": "NVARCHAR(500)", "param_desc": "汇率类型ID", "path": "data.recordList.exchRateType", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "billqty": {"api_field_name": "billqty", "chinese_name": "累计开票数量", "data_type": "BIGINT", "param_desc": "累计开票数量", "path": "data.recordList.billqty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invExchRate": {"api_field_name": "invExchRate", "chinese_name": "换算率", "data_type": "NVARCHAR(500)", "param_desc": "换算率", "path": "data.recordList.invExchRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "status": {"api_field_name": "status", "chinese_name": "单据状态，0 开立 1已审核 3 审核中", "data_type": "NVARCHAR(500)", "param_desc": "单据状态，0 开立 1已审核 3 审核中", "path": "status", "depth": 0, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isGiftProduct": {"api_field_name": "isGiftProduct", "chinese_name": "赠品，true:是、false:否", "data_type": "BIT", "param_desc": "赠品，true:是、false:否", "path": "data.recordList.isGiftProduct", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "returncount": {"api_field_name": "returncount", "chinese_name": "退回次数", "data_type": "BIGINT", "param_desc": "退回次数", "path": "data.recordList.returncount", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "verifystate": {"api_field_name": "verifystate", "chinese_name": "审批状态 （0：未提交 1：已提交 2：已审核）", "data_type": "BIGINT", "param_desc": "审批状态 （0：未提交 1：已提交 2：已审核）", "path": "data.recordList.verifystate", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invoicingDocEntryAndMgmt": {"api_field_name": "invoicingDocEntryAndMgmt", "chinese_name": "立账开票依据", "data_type": "NVARCHAR(500)", "param_desc": "立账开票依据", "path": "data.recordList.invoicingDocEntryAndMgmt", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isVerification": {"api_field_name": "isVerification", "chinese_name": "核销标识", "data_type": "BIGINT", "param_desc": "核销标识", "path": "data.recordList.isVerification", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "currency_moneyDigit": {"api_field_name": "currency_moneyDigit", "chinese_name": "币种金额精度", "data_type": "BIGINT", "param_desc": "币种金额精度", "path": "data.recordList.currency_moneyDigit", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "warehouse_code": {"api_field_name": "warehouse_code", "chinese_name": "仓库编码", "data_type": "NVARCHAR(500)", "param_desc": "仓库编码", "path": "data.recordList.warehouse_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockStatusDoc": {"api_field_name": "stockStatusDoc", "chinese_name": "库存状态id", "data_type": "BIGINT", "param_desc": "库存状态id", "path": "data.recordList.stockStatusDoc", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productsku_cName": {"api_field_name": "productsku_cName", "chinese_name": "物料sku名称", "data_type": "NVARCHAR(500)", "param_desc": "物料sku名称", "path": "data.recordList.productsku_cName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "osmOrg_name": {"api_field_name": "osmOrg_name", "chinese_name": "委外组织", "data_type": "NVARCHAR(500)", "param_desc": "委外组织", "path": "data.recordList.osmOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vouchdate": {"api_field_name": "vouchdate", "chinese_name": "单据日期", "data_type": "NVARCHAR(500)", "param_desc": "单据日期", "path": "data.recordList.vouchdate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "receiptDocEntryAndMgmt": {"api_field_name": "receiptDocEntryAndMgmt", "chinese_name": "入库立账方式", "data_type": "NVARCHAR(500)", "param_desc": "入库立账方式", "path": "data.recordList.receiptDocEntryAndMgmt", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_name": {"api_field_name": "natCurrency_name", "chinese_name": "本币名称", "data_type": "NVARCHAR(500)", "param_desc": "本币名称", "path": "data.recordList.natCurrency_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invoiceVendor_name": {"api_field_name": "invoiceVendor_name", "chinese_name": "开票供应商", "data_type": "NVARCHAR(500)", "param_desc": "开票供应商", "path": "data.recordList.invoiceVendor_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invPriceExchRate": {"api_field_name": "invPriceExchRate", "chinese_name": "计价换算率", "data_type": "BIGINT", "param_desc": "计价换算率", "path": "data.recordList.invPriceExchRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vendor": {"api_field_name": "vendor", "chinese_name": "委外供应商id", "data_type": "BIGINT", "param_desc": "委外供应商id", "path": "data.recordList.vendor", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "sqty": {"api_field_name": "sqty", "chinese_name": "累计结算数量", "data_type": "NVARCHAR(500)", "param_desc": "累计结算数量", "path": "data.recordList.sqty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currency": {"api_field_name": "currency", "chinese_name": "币种ID", "data_type": "NVARCHAR(500)", "param_desc": "币种ID", "path": "data.recordList.currency", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "pubts": {"api_field_name": "pubts", "chinese_name": "时间戳", "data_type": "NVARCHAR(500)", "param_desc": "时间戳", "path": "data.recordList.pubts", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "smoney": {"api_field_name": "smoney", "chinese_name": "累计结算金额", "data_type": "NVARCHAR(500)", "param_desc": "累计结算金额", "path": "data.recordList.smoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "org_name": {"api_field_name": "org_name", "chinese_name": "收货组织", "data_type": "NVARCHAR(500)", "param_desc": "收货组织", "path": "data.recordList.org_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isFlowCoreBill": {"api_field_name": "isFlowCoreBill", "chinese_name": "是否流程核心单据,true:是、false:否", "data_type": "BIT", "param_desc": "是否流程核心单据,true:是、false:否", "path": "data.recordList.isFlowCoreBill", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "auditDate": {"api_field_name": "auditDate", "chinese_name": "审核日期", "data_type": "NVARCHAR(500)", "param_desc": "审核日期", "path": "data.recordList.auditDate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "creator": {"api_field_name": "creator", "chinese_name": "创建人", "data_type": "NVARCHAR(500)", "param_desc": "创建人", "path": "data.recordList.creator", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product": {"api_field_name": "product", "chinese_name": "物料id", "data_type": "BIGINT", "param_desc": "物料id", "path": "data.recordList.product", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "oriSum": {"api_field_name": "oriSum", "chinese_name": "含税金额", "data_type": "DECIMAL(18,4)", "param_desc": "含税金额", "path": "data.recordList.oriSum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "inInvoiceOrg_name": {"api_field_name": "inInvoiceOrg_name", "chinese_name": "收票组织", "data_type": "NVARCHAR(500)", "param_desc": "收票组织", "path": "data.recordList.inInvoiceOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "exchRateType_name": {"api_field_name": "exchRateType_name", "chinese_name": "汇率类型", "data_type": "NVARCHAR(500)", "param_desc": "汇率类型", "path": "data.recordList.exchRateType_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "department_name": {"api_field_name": "department_name", "chinese_name": "委外部门", "data_type": "NVARCHAR(500)", "param_desc": "委外部门", "path": "data.recordList.department_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "auditor": {"api_field_name": "auditor", "chinese_name": "审核人", "data_type": "NVARCHAR(500)", "param_desc": "审核人", "path": "data.recordList.auditor", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "accountOrg": {"api_field_name": "accountOrg", "chinese_name": "会计主体", "data_type": "NVARCHAR(500)", "param_desc": "会计主体", "path": "data.recordList.accountOrg", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "priceQty": {"api_field_name": "priceQty", "chinese_name": "计价数量", "data_type": "NVARCHAR(500)", "param_desc": "计价数量", "path": "data.recordList.priceQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "createTime": {"api_field_name": "createTime", "chinese_name": "创建时间", "data_type": "NVARCHAR(500)", "param_desc": "创建时间", "path": "data.recordList.createTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natMoney": {"api_field_name": "natMoney", "chinese_name": "本币无税金额", "data_type": "DECIMAL(18,4)", "param_desc": "本币无税金额", "path": "data.recordList.natMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "taxitems_code": {"api_field_name": "taxitems_code", "chinese_name": "税目编码", "data_type": "NVARCHAR(500)", "param_desc": "税目编码", "path": "data.recordList.taxitems_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "department_code": {"api_field_name": "department_code", "chinese_name": "委外部门编码", "data_type": "NVARCHAR(500)", "param_desc": "委外部门编码", "path": "data.recordList.department_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "osmInRecords_osmType": {"api_field_name": "osmInRecords_osmType", "chinese_name": "委外类型", "data_type": "BIGINT", "param_desc": "委外类型", "path": "data.recordList.osmInRecords_osmType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "currency_priceDigit": {"api_field_name": "currency_priceDigit", "chinese_name": "币种单价精度", "data_type": "BIGINT", "param_desc": "币种单价精度", "path": "data.recordList.currency_priceDigit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockUnit_name": {"api_field_name": "stockUnit_name", "chinese_name": "库存单位", "data_type": "NVARCHAR(500)", "param_desc": "库存单位", "path": "data.recordList.stockUnit_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isBeginning": {"api_field_name": "isBeginning", "chinese_name": "是否期初，,true:是、false:否", "data_type": "BIT", "param_desc": "是否期初，,true:是、false:否", "path": "data.recordList.isBeginning", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bustype_name": {"api_field_name": "bustype_name", "chinese_name": "交易类型", "data_type": "NVARCHAR(500)", "param_desc": "交易类型", "path": "data.recordList.bustype_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "modifier": {"api_field_name": "modifier", "chinese_name": "修改人", "data_type": "NVARCHAR(500)", "param_desc": "修改人", "path": "data.recordList.modifier", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natTax": {"api_field_name": "natTax", "chinese_name": "本币税额", "data_type": "NVARCHAR(500)", "param_desc": "本币税额", "path": "data.recordList.natTax", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "source": {"api_field_name": "source", "chinese_name": "上游单据类型，po_subcontract_order：委外订单，po_osm_arrive_order：委外到货单", "data_type": "NVARCHAR(500)", "param_desc": "上游单据类型，po_subcontract_order：委外订单，po_osm_arrive_order：委外到货单", "path": "data.recordList.source", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "srcBill": {"api_field_name": "srcBill", "chinese_name": "来源单据id", "data_type": "NVARCHAR(500)", "param_desc": "来源单据id", "path": "data.recordList.srcBill", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "subQty": {"api_field_name": "subQty", "chinese_name": "件数", "data_type": "NVARCHAR(500)", "param_desc": "件数", "path": "data.recordList.subQty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "modifyTime": {"api_field_name": "modifyTime", "chinese_name": "修改时间", "data_type": "NVARCHAR(500)", "param_desc": "修改时间", "path": "data.recordList.modifyTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "inInvoiceOrg": {"api_field_name": "inInvoiceOrg", "chinese_name": "收票组织", "data_type": "NVARCHAR(500)", "param_desc": "收票组织", "path": "data.recordList.inInvoiceOrg", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_cName": {"api_field_name": "product_cName", "chinese_name": "物料名称", "data_type": "NVARCHAR(500)", "param_desc": "物料名称", "path": "data.recordList.product_cName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vendor_name": {"api_field_name": "vendor_name", "chinese_name": "委外供应商", "data_type": "NVARCHAR(500)", "param_desc": "委外供应商", "path": "data.recordList.vendor_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriUnitPrice": {"api_field_name": "oriUnitPrice", "chinese_name": "无税单价", "data_type": "DECIMAL(18,4)", "param_desc": "无税单价", "path": "data.recordList.oriUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "barCode": {"api_field_name": "barCode", "chinese_name": "单据条码", "data_type": "NVARCHAR(500)", "param_desc": "单据条码", "path": "data.recordList.barCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unit_name": {"api_field_name": "unit_name", "chinese_name": "计量单位", "data_type": "NVARCHAR(500)", "param_desc": "计量单位", "path": "data.recordList.unit_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "taxRate": {"api_field_name": "taxRate", "chinese_name": "税率", "data_type": "BIGINT", "param_desc": "税率", "path": "data.recordList.taxRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unit": {"api_field_name": "unit", "chinese_name": "单位id", "data_type": "BIGINT", "param_desc": "单位id", "path": "data.recordList.unit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productsku": {"api_field_name": "productsku", "chinese_name": "物料SKUid", "data_type": "BIGINT", "param_desc": "物料SKUid", "path": "data.recordList.productsku", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productsku_cCode": {"api_field_name": "productsku_cCode", "chinese_name": "物料sku编码", "data_type": "NVARCHAR(500)", "param_desc": "物料sku编码", "path": "data.recordList.productsku_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_moneyDigit": {"api_field_name": "natCurrency_moneyDigit", "chinese_name": "本币金额精度", "data_type": "BIGINT", "param_desc": "本币金额精度", "path": "data.recordList.natCurrency_moneyDigit", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "accountOrg_name": {"api_field_name": "accountOrg_name", "chinese_name": "会计主体", "data_type": "NVARCHAR(500)", "param_desc": "会计主体", "path": "data.recordList.accountOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "qty": {"api_field_name": "qty", "chinese_name": "数量", "data_type": "NVARCHAR(500)", "param_desc": "数量", "path": "data.recordList.qty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unit_Precision": {"api_field_name": "unit_Precision", "chinese_name": "主计量精度", "data_type": "BIGINT", "param_desc": "主计量精度", "path": "data.recordList.unit_Precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriTaxUnitPrice": {"api_field_name": "oriTaxUnitPrice", "chinese_name": "含税单价", "data_type": "DECIMAL(18,4)", "param_desc": "含税单价", "path": "data.recordList.oriTaxUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriMoney": {"api_field_name": "oriMoney", "chinese_name": "无税金额", "data_type": "DECIMAL(18,4)", "param_desc": "无税金额", "path": "data.recordList.oriMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "contactsPieces": {"api_field_name": "contactsPieces", "chinese_name": "应收件数", "data_type": "NVARCHAR(500)", "param_desc": "应收件数", "path": "data.recordList.contactsPieces", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "contactsQuantity": {"api_field_name": "contactsQuantity", "chinese_name": "应收数量", "data_type": "NVARCHAR(500)", "param_desc": "应收数量", "path": "data.recordList.contactsQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natUnitPrice": {"api_field_name": "natUnitPrice", "chinese_name": "本币无税单价", "data_type": "DECIMAL(18,4)", "param_desc": "本币无税单价", "path": "data.recordList.natUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "exchRate": {"api_field_name": "exchRate", "chinese_name": "汇率", "data_type": "NVARCHAR(500)", "param_desc": "汇率", "path": "data.recordList.exchRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "osmInRecords_id": {"api_field_name": "osmInRecords_id", "chinese_name": "订单行id", "data_type": "BIGINT", "param_desc": "订单行id", "path": "data.recordList.osmInRecords_id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "priceUOM": {"api_field_name": "priceUOM", "chinese_name": "计价单位id", "data_type": "BIGINT", "param_desc": "计价单位id", "path": "data.recordList.priceUOM", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "department": {"api_field_name": "department", "chinese_name": "委外部门ID", "data_type": "NVARCHAR(500)", "param_desc": "委外部门ID", "path": "data.recordList.department", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currency_name": {"api_field_name": "currency_name", "chinese_name": "币种名称", "data_type": "NVARCHAR(500)", "param_desc": "币种名称", "path": "data.recordList.currency_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "org": {"api_field_name": "org", "chinese_name": "收货组织", "data_type": "NVARCHAR(500)", "param_desc": "收货组织", "path": "data.recordList.org", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "custom": {"api_field_name": "custom", "chinese_name": "客户id", "data_type": "BIGINT", "param_desc": "客户id", "path": "data.recordList.custom", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "osmOrg": {"api_field_name": "osmOrg", "chinese_name": "委外组织ID", "data_type": "NVARCHAR(500)", "param_desc": "委外组织ID", "path": "data.recordList.osmOrg", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bustype": {"api_field_name": "bustype", "chinese_name": "交易类型id", "data_type": "NVARCHAR(500)", "param_desc": "交易类型id", "path": "data.recordList.bustype", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "costUnitPrice": {"api_field_name": "costUnitPrice", "chinese_name": "成本单价", "data_type": "DECIMAL(18,4)", "param_desc": "成本单价", "path": "data.recordList.costUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "upcode": {"api_field_name": "upcode", "chinese_name": "上游单据号", "data_type": "NVARCHAR(500)", "param_desc": "上游单据号", "path": "data.recordList.upcode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "priceUOM_Name": {"api_field_name": "priceUOM_Name", "chinese_name": "计价单位名称", "data_type": "NVARCHAR(500)", "param_desc": "计价单位名称", "path": "data.recordList.priceUOM_Name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "taxitems": {"api_field_name": "taxitems", "chinese_name": "税目id", "data_type": "NVARCHAR(500)", "param_desc": "税目id", "path": "data.recordList.taxitems", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natTaxUnitPrice": {"api_field_name": "natTaxUnitPrice", "chinese_name": "本币含税单价", "data_type": "DECIMAL(18,4)", "param_desc": "本币含税单价", "path": "data.recordList.natTaxUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unDeductTaxRate": {"api_field_name": "unDeductTaxRate", "chinese_name": "不可抵扣税率", "data_type": "NVARCHAR(500)", "param_desc": "不可抵扣税率", "path": "data.recordList.unDeductTaxRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unDeductTax": {"api_field_name": "unDeductTax", "chinese_name": "不可抵扣税额", "data_type": "NVARCHAR(500)", "param_desc": "不可抵扣税额", "path": "data.recordList.unDeductTax", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriUnDeductTax": {"api_field_name": "oriUnDeductTax", "chinese_name": "原币不可抵扣税额", "data_type": "NVARCHAR(500)", "param_desc": "原币不可抵扣税额", "path": "data.recordList.oriUnDeductTax", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bodyItem": {"api_field_name": "bodyItem", "chinese_name": "单据体自定义项", "data_type": "NVARCHAR(MAX)", "param_desc": "单据体自定义项", "path": "data.recordList.bodyItem", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "headItem": {"api_field_name": "headItem", "chinese_name": "单据头自定义项", "data_type": "NVARCHAR(MAX)", "param_desc": "单据头自定义项", "path": "data.recordList.headItem", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "osmInRecords": {"api_field_name": "osmInRecords", "chinese_name": "委外入库单子表", "data_type": "NVARCHAR(MAX)", "param_desc": "委外入库单子表", "path": "data.recordList.osmInRecords", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "opSn": {"api_field_name": "opSn", "chinese_name": "工序顺序号", "data_type": "NVARCHAR(500)", "param_desc": "工序顺序号", "path": "data.recordList.osmInRecords.opSn", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "operationId": {"api_field_name": "operationId", "chinese_name": "工序", "data_type": "BIGINT", "param_desc": "工序", "path": "data.recordList.osmInRecords.operationId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "endOp": {"api_field_name": "endOp", "chinese_name": "末序(false:否,true:是)", "data_type": "BIT", "param_desc": "末序(false:否,true:是)", "path": "data.recordList.osmInRecords.endOp", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "sourcePoOrderCode": {"api_field_name": "sourcePoOrderCode", "chinese_name": "生产订单号", "data_type": "NVARCHAR(500)", "param_desc": "生产订单号", "path": "data.recordList.osmInRecords.sourcePoOrderCode", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "sourcePoOrderProductRowno": {"api_field_name": "sourcePoOrderProductRowno", "chinese_name": "生产订单行号", "data_type": "NVARCHAR(500)", "param_desc": "生产订单行号", "path": "data.recordList.osmInRecords.sourcePoOrderProductRowno", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "sourcePoOrderId": {"api_field_name": "sourcePoOrderId", "chinese_name": "生产订单ID", "data_type": "BIGINT", "param_desc": "生产订单ID", "path": "data.recordList.osmInRecords.sourcePoOrderId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "sourcePoOrderProductId": {"api_field_name": "sourcePoOrderProductId", "chinese_name": "生产订单行ID", "data_type": "BIGINT", "param_desc": "生产订单行ID", "path": "data.recordList.osmInRecords.sourcePoOrderProductId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "costAccountingMethod": {"api_field_name": "costAccountingMethod", "chinese_name": "委外成本核算方式(0:按委外入库核算成本,1:按委外订单核算成本)", "data_type": "NVARCHAR(500)", "param_desc": "委外成本核算方式(0:按委外入库核算成本,1:按委外订单核算成本)", "path": "data.recordList.costAccountingMethod", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isSum": {"api_field_name": "isSum", "chinese_name": "是否按照表头查询 true:表头 false:表头+明细 默认为false", "data_type": "BIT", "param_desc": "是否按照表头查询 true:表头 false:表头+明细 默认为false", "path": "isSum", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "open_vouchdate_begin": {"api_field_name": "open_vouchdate_begin", "chinese_name": "单据开始日期", "data_type": "NVARCHAR(500)", "param_desc": "单据开始日期", "path": "open_vouchdate_begin", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_vouchdate_end": {"api_field_name": "open_vouchdate_end", "chinese_name": "单据结束日期", "data_type": "NVARCHAR(500)", "param_desc": "单据结束日期", "path": "open_vouchdate_end", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "simpleVOs": {"api_field_name": "simpleVOs", "chinese_name": "扩展查询条件", "data_type": "NVARCHAR(MAX)", "param_desc": "扩展查询条件", "path": "simpleVOs", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "field": {"api_field_name": "field", "chinese_name": "属性名(条件传属性的名称，如单据编号code、单据日期vouchdate、收货组织org.code、委外组织osmOrg.code、收票组织inInvoiceOrg.code、委外供应商vendor.code、仓库编码warehouse.code、物料编码osmInRecords.product.cCode、物料分类osmInRecords.product.manageClass.code、物料SKU编码osmInRecords.productsku.cCode等)", "data_type": "NVARCHAR(500)", "param_desc": "属性名(条件传属性的名称，如单据编号code、单据日期vouchdate、收货组织org.code、委外组织osmOrg.code、收票组织inInvoiceOrg.code、委外供应商vendor.code、仓库编码warehouse.code、物料编码osmInRecords.product.cCode、物料分类osmInRecords.product.manageClass.code、物料SKU编码osmInRecords.productsku.cCode等)", "path": "simpleVOs.field", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "op": {"api_field_name": "op", "chinese_name": "比较符(in:包含;eq:等于;lt:小于;gt:大于;like:模糊匹配;between:介于)", "data_type": "NVARCHAR(500)", "param_desc": "比较符(in:包含;eq:等于;lt:小于;gt:大于;like:模糊匹配;between:介于)", "path": "simpleVOs.op", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value1": {"api_field_name": "value1", "chinese_name": "值1(条件)", "data_type": "NVARCHAR(500)", "param_desc": "值1(条件)", "path": "simpleVOs.value1", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value2": {"api_field_name": "value2", "chinese_name": "值2(条件)", "data_type": "NVARCHAR(500)", "param_desc": "值2(条件)", "path": "simpleVOs.value2", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}}}