# 绞杀者代理层 Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.proxy.txt .
RUN pip install --no-cache-dir -r requirements.proxy.txt

# 复制代理层代码
COPY backend/app/strangler_proxy/ ./strangler_proxy/
COPY backend/app/__init__.py ./
COPY config/ ./config/

# 复制启动脚本
COPY docker/strangler-proxy/start-proxy.sh ./
RUN chmod +x start-proxy.sh

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

EXPOSE 8080

CMD ["./start-proxy.sh"]
