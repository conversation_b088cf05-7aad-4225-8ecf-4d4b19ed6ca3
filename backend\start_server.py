import logging
import sys
from pathlib import Path

import uvicorn

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 服务器启动脚本 - backend目录版本
在backend目录下启动服务器
"""


# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

if __name__ == "__main__":
    logger.info("🚀 启动 YS-API V3.0 服务器 (backend目录)")
    logger.info("📡 服务器地址: http://localhost:8000")
    logger.info("🔧 健康检查: http://localhost:8000/health")
    logger.info("📄 前端页面:")
    logger.info("   - 统一字段配置: http://localhost:8000/unified-field-config.html")
    logger.info("   - 数据库管理: http://localhost:8000/database-v2.html")
    logger.info("   - Excel翻译: http://localhost:8000/excel-translation.html")
    logger.info("=" * 50)

    try:
        uvicorn.run(
            "app.main:app",
            host="127.0.0.1",
            port=8000,
            log_level="info",
            access_log=True,
            reload=False,
        )
    except KeyboardInterrupt:
        logger.info("\n🛑 服务器已停止")
    except Exception:
        logger.info(f"❌ 启动失败: {e}")
        sys.exit(1)
# 设置日志
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
