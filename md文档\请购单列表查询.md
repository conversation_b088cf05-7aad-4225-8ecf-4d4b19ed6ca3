请购单列表查询
发布时间:2024-11-23 09:18:15
根据交易类型、物料、表头模式还是表头明细模式、分页条件和自定义条件查询请购单列表数据信息

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	动态域名，获取方式详见 获取租户所在数据中心域名
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/applyorder/list
请求方式	POST
ContentType	application/json
应用场景	开放API
API类别	
事务和幂等性	无
限流次数	40次/分钟

多语	不支持
2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
bustype	string	否	否	交易类型id    示例: 110000000000029
product	long	否	否	物料id    示例: 1730491724599552
pageIndex	long	否	是	页码    示例: 1
pageSize	long	否	是	每页条数    示例: 10
isSum	boolean	否	否	是否按照表头查询：true:表头、false:表头+明细    示例: false    默认值: false
queryOrders	object	是	否	排序字段
field	string	否	否	排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：applyOrders.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)    示例: id
order	string	否	否	顺序：asc：正序、desc：倒序    示例: asc
simpleVOs	object	是	否	扩展条件查询
field	string	否	否	属性名(条件) ，1：status(单据状态：0:未审核、1:已审核、2:已关闭、3:审核中)、2：code(单据编号)、3：vouchdate(请购日期)、4：operator(请购员id)、5：org(需求组织id)、6；applyOrders.purchaseOrg(采购组织id)、7：applyOrders.vendor(建议供应商id)、 8：applyDept(请购部门id)、9：headItem.define1(单据头自定义项1)、10：applyOrders.adviseOrderDate(建议订货日期)、11：applyOrders.project(项目id)    示例: code
op	string	否	否	比较符（条件）：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者    示例: eq
value1	string	否	否	值1（条件）    示例: CGDD2010140000000003
3. 请求示例
Url: /yonbip/scm/applyorder/list?access_token=访问令牌
Body: {
	"bustype": "110000000000029",
	"product": 1730491724599552,
	"pageIndex": 1,
	"pageSize": 10,
	"isSum": false,
	"queryOrders": [
		{
			"field": "id",
			"order": "asc"
		}
	],
	"simpleVOs": [
		{
			"field": "code",
			"op": "eq",
			"value1": "CGDD2010140000000003"
		}
	]
}
4. 返回值参数
名称	类型	数组	描述
code	string	否	返回码，调用成功时返回200
message	string	否	调用失败时的错误信息
data	object	否	调用成功时的返回数据
salesOrgId_name	string	否	销售组织名称
pageIndex	int	否	分页
pageSize	int	否	每页条数
pageCount	int	否	页数
beginPageIndex	int	否	起始页
endPageIndex	int	否	结束页
recordCount	int	否	记录数
pubts	string	否	时间戳，格式为:yyyy-MM-dd HH:mm:ss
synSourceOrg	string	否	协同来源组织id
synSourceOrg	string	否	协同来源组织id
recordList	object	是	返回列表信息
vouchdate	string	否	单据日期，汇总场景和明细场景均返回
code	string	否	请购单编号，汇总场景和明细场景均返回
returncount	string	否	退回次数，汇总场景和明细场景均返回
isWfControlled	string	否	是否审批流控制，汇总场景和明细场景均返回
verifystate	string	否	审批状态，汇总场景和明细场景均返回
bustype	string	否	交易类型id，汇总场景和明细场景均返回
bustype_name	string	否	交易类型名称，汇总场景和明细场景均返回
applyDept	string	否	请购部门id，汇总场景和明细场景均返回
applyDept_name	string	否	请购部门名称，汇总场景和明细场景均返回
bizstatus	string	否	单据状态, 0:开立、3:审核中、1:已审核、2:已关闭，汇总场景和明细场景均返回
status	string	否	单据状态, 0:开立、3:审核中、1:已审核、2:已关闭，汇总场景和明细场景均返回
currency	string	否	币种id，汇总场景和明细场景均返回
currency_name	string	否	币种名称，汇总场景和明细场景均返回
warehouseId	string	否	要货仓库id，汇总场景和明细场景均返回
warehouseId_name	string	否	要货仓库名称，汇总场景和明细场景均返回
source	string	否	来源单据类型，20:计划独立需求、280:计划订单、MR.mr_lrp_plan_order_batch:计划订单、po_production_order:生产订单、ucf-amc-aum.aum_assignapply_card:资产领用申请、yonbip-pm-planme.rscm_project_materiallist_card:项目物资单、SCMSA.voucher_order:销售订单，汇总场景和明细场景均返回
store	string	否	所属门店id，汇总场景和明细场景均返回
isUretailVoucher	string	否	是否是零售, true:是、false:否，汇总场景和明细场景均返回
store_name	string	否	所属门店名称，汇总场景和明细场景均返回
org	string	否	需求组织id，汇总场景和明细场景均返回
org_name	string	否	需求组织名称，汇总场景和明细场景均返回
custom	string	否	客户id，汇总场景和明细场景均返回
creator	string	否	制单人，汇总场景和明细场景均返回
createTime	string	否	制单时间，汇总场景和明细场景均返回
modifier	string	否	修改人，汇总场景和明细场景均返回
modifyTime	string	否	修改时间，汇总场景和明细场景均返回
closer	string	否	关闭人，汇总场景和明细场景均返回
closeTime	string	否	关闭时间，汇总场景和明细场景均返回
locker	string	否	锁定人，汇总场景和明细场景均返回
lockTime	string	否	锁定时间，汇总场景和明细场景均返回
operator	string	否	请购员id，汇总场景和明细场景均返回
operator_name	string	否	请购员名称，汇总场景和明细场景均返回
auditor	string	否	审核人，汇总场景和明细场景均返回
auditTime	string	否	审核时间，汇总场景和明细场景均返回
auditDate	string	否	审核日期，汇总场景和明细场景均返回
submitor	string	否	提交人，汇总场景和明细场景均返回
submitTime	string	否	提交时间，汇总场景和明细场景均返回
totalQuantity	number
小数位数:8,最大长度:28	否	整单数量，汇总场景和明细场景均返回
memo	string	否	备注，汇总场景和明细场景均返回
id	string	否	请购单id，汇总场景和明细场景均返回
pubts	string	否	时间戳，汇总场景和明细场景均返回
tplid	string	否	模板id，汇总场景和明细场景均返回
applyorders_execStatus	string	否	执行状态, 0:未下订单、1:部分下单、2:全部下单、
applyorders_receiveOrg	string	否	收货组织id,只有明细场景返回
applyorders_receiveOrg_name	string	否	收货组织名称,只有明细场景返回
applyorders_purchaseOrg	string	否	采购组织id,只有明细场景返回
applyorders_purchaseOrg_name	string	否	采购组织名称,只有明细场景返回
applyorders_purDept	string	否	采购部门id,只有明细场景返回
applyorders_purDept_name	string	否	采购部门名称,只有明细场景返回
applyorders_purPerson	string	否	采购业务员id,只有明细场景返回
applyorders_purPerson_name	string	否	采购业务员名称,只有明细场景返回
applyOrders_supplyMoney	number
小数位数:8,最大长度:28	否	累计订货金额,只有明细场景返回
applyOrder_orderMoneyRatio	string	否	订单金额超量比例,只有明细场景返回
applyorders_supplyCount	number
小数位数:8,最大长度:28	否	累计订货数量,只有明细场景返回
apporders_id	string	否	订单行id,只有明细场景返回
applyorders_product	string	否	物料id,只有明细场景返回
product_defaultAlbumId	string	否	物料首图片,只有明细场景返回
applyorders_product_cCode	string	否	物料编码,只有明细场景返回
applyorders_product_cName	string	否	物料名称,只有明细场景返回
applyorders_productsku	string	否	物料SKUid,只有明细场景返回
applyorders_productsku_cCode	string	否	物料sku编码,只有明细场景返回
applyorders_productsku_cName	string	否	物料sku名称,只有明细场景返回
applyorders_currency	string	否	币种id,只有明细场景返回
applyorders_currency_name	string	否	币种名称,只有明细场景返回
applyorders_currency_priceDigit	string	否	币种单价精度,只有明细场景返回
applyorders_currency_moneyDigit	string	否	币种金额精度,只有明细场景返回
applyorders_qty	number
小数位数:8,最大长度:28	否	数量,只有明细场景返回
applyorders_subQty	number
小数位数:8,最大长度:28	否	计价数量,只有明细场景返回
applyorders_rowno	int	否	行号,只有明细场景返回
unit_Precision	string	否	主计量精度,只有明细场景返回
applyorders_unit	string	否	单位id,只有明细场景返回
applyorders_unit_name	string	否	主计量名称,只有明细场景返回
applyorders_product_oUnitId	string	否	零售单位id,只有明细场景返回
applyorders_product_productOfflineRetail_purchaseUnit	string	否	采购单位id,只有明细场景返回
applyorders_invExchRate	int	否	换算率,只有明细场景返回
applyorders_productOfflineRetail_purchaseRate	int	否	采购单位换算率,只有明细场景返回
priceUOM	string	否	计价单位id,只有明细场景返回
priceUOM_Name	string	否	计价单位,只有明细场景返回
invPriceExchRate	int	否	计价换算率,只有明细场景返回
unitExchangeTypePrice	string	否	计价单位转换率的换算方式,只有明细场景返回
priceUOM_Precision	string	否	计价单位精度,只有明细场景返回
taxRate	string	否	税率,只有明细场景返回
oriTax	number
小数位数:8,最大长度:28	否	税额,只有明细场景返回
oriTaxUnitPrice	number
小数位数:8,最大长度:28	否	含税单价,只有明细场景返回
oriUnitPrice	number
小数位数:8,最大长度:28	否	无税单价,只有明细场景返回
oriMoney	number
小数位数:8,最大长度:28	否	无税金额,只有明细场景返回
oriSum	number
小数位数:8,最大长度:28	否	含税金额,只有明细场景返回
applyorders_product_primeCosts	number
小数位数:8,最大长度:28	否	进货价格,只有明细场景返回
applyorders_productsku_primeCosts	number
小数位数:8,最大长度:28	否	sku进货价格,只有明细场景返回
applyorders_requirementDate	string	否	需求日期,只有明细场景返回
applyorders_adviseOrderDate	string	否	建议订货日期,只有明细场景返回
applyorders_adviseSupplier	string	否	建议供应商id,只有明细场景返回
applyorders_adviseSupplier_name	string	否	建议供应商名称,只有明细场景返回
applyorders_vendor	string	否	建议供应商id,只有明细场景返回
applyorders_vendor_name	string	否	建议供应商名称,只有明细场景返回
applyorders_memo	string	否	备注,只有明细场景返回
applyorders_productsku_modelDescription	string	否	sku规格型号,只有明细场景返回
applyorders_product_model	string	否	型号,只有明细场景返回
applyorders_product_modelDescription	string	否	规格说明,只有明细场景返回
applyorders_propertiesValue	string	否	规格,只有明细场景返回
project	string	否	项目id,只有明细场景返回
project_code	string	否	项目编码,只有明细场景返回
project_name	string	否	项目名称,只有明细场景返回
applyorders_trackNo	string	否	跟踪号,只有明细场景返回
applyOrderDefineCharacter	特征组
pu.applyorder.ApplyOrder	否	表头自定义项特征组，汇总场景和明细场景均返回
U9001	string	否	U9请购单号
id	string	否	特征id,主键,新增时无需填写,修改时必填
applyOrdersDefineCharacter	特征组
pu.applyorder.ApplyOrders	否	子表自定义项特征组,只有明细场景返回
XS11	string	否	需求分类号test
XS15	string	否	顾客订单号（订单表体）
id	string	否	特征id,主键,新增时无需填写,修改时必填
applyOrdersCharacteristics	特征组
pu.applyorder.ApplyOrders	否	子表自由项特征组,只有明细场景返回
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
5. 正确返回示例
{
	"code": "",
	"message": "",
	"data": {
		"pageIndex": 1,
		"pageSize": 10,
		"pageCount": 10,
		"beginPageIndex": 1,
		"endPageIndex": 10,
		"recordCount": 100,
		"pubts": "2024-03-04 00:00:00",
		"recordList": [
			{
				"vouchdate": "2021-03-04 00:00:00",
				"code": "CGQG0000201905100001",
				"returncount": "1",
				"isWfControlled": "1",
				"verifystate": "1",
				"bustype": "1153091135965020",
				"bustype_name": "采购要货",
				"applyDept": "1996900540021735427",
				"applyDept_name": "数智请购部门",
				"bizstatus": "0",
				"status": "0",
				"currency": "1996901321700016134",
				"currency_name": "人名币",
				"warehouseId": "1996901321700016135",
				"warehouseId_name": "货品仓库",
				"source": "MR.mr_lrp_plan_order_batch",
				"store": "1996902524296626181",
				"isUretailVoucher": "false",
				"store_name": "零售门店",
				"org": "1996902524296626182",
				"org_name": "达利园组织",
				"custom": "1996903245851131908",
				"creator": "张三",
				"createTime": "2021-03-04 00:00:00",
				"modifier": "张三",
				"modifyTime": "2021-03-04 00:00:00",
				"closer": "李响",
				"closeTime": "2021-03-05 00:00:00",
				"locker": "张三",
				"lockTime": "2021-03-05 00:00:00",
				"operator": "1996904998192021506",
				"operator_name": "王晨",
				"auditor": "刘策",
				"auditTime": "2021-03-05 00:00:00",
				"auditDate": "2021-03-05 12:36:12",
				"submitor": "王晨",
				"submitTime": "2021-03-05 12:20:12",
				"totalQuantity": 200,
				"memo": "来货请购",
				"id": "1996904998192021507",
				"pubts": "2021-03-04 00:00:00",
				"tplid": "1996906535790313475",
				"applyorders_execStatus": "表头自定义项4",
				"applyorders_receiveOrg": "1996916044853673985",
				"applyorders_receiveOrg_name": "达利园组织",
				"applyorders_purchaseOrg": "1996916044853673985",
				"applyorders_purchaseOrg_name": "达利园组织",
				"applyorders_purDept": "1996916044853673986",
				"applyorders_purDept_name": "采购部门",
				"applyorders_purPerson": "1996916551654047750",
				"applyorders_purPerson_name": "李晨",
				"applyOrders_supplyMoney": 200,
				"applyOrder_orderMoneyRatio": "20",
				"applyorders_supplyCount": 20,
				"apporders_id": "1996916551654047751",
				"applyorders_product": "1996917006926348290",
				"product_defaultAlbumId": "1996917006926348291",
				"applyorders_product_cCode": "00000002",
				"applyorders_product_cName": "苹果",
				"applyorders_productsku": "1996917006926348290",
				"applyorders_productsku_cCode": "00000002",
				"applyorders_productsku_cName": "苹果",
				"applyorders_currency": "1996917006926348290",
				"applyorders_currency_name": "人民币",
				"applyorders_currency_priceDigit": "2",
				"applyorders_currency_moneyDigit": "2",
				"applyorders_qty": 20,
				"applyorders_subQty": 20,
				"applyorders_rowno": 10,
				"unit_Precision": "2",
				"applyorders_unit": "1996918364130246657",
				"applyorders_unit_name": "吨",
				"applyorders_product_oUnitId": "1996918364130246657",
				"applyorders_product_productOfflineRetail_purchaseUnit": "吨",
				"applyorders_invExchRate": 1,
				"applyorders_productOfflineRetail_purchaseRate": 1,
				"priceUOM": "1996918364130246658",
				"priceUOM_Name": "吨",
				"invPriceExchRate": 1,
				"unitExchangeTypePrice": "0",
				"priceUOM_Precision": "2",
				"taxRate": "2",
				"oriTax": 20,
				"oriTaxUnitPrice": 12,
				"oriUnitPrice": 6,
				"oriMoney": 150,
				"oriSum": 200,
				"applyorders_product_primeCosts": 23,
				"applyorders_productsku_primeCosts": 24,
				"applyorders_requirementDate": "2021-03-04 00:00:00",
				"applyorders_adviseOrderDate": "2021-03-04 00:00:00",
				"applyorders_adviseSupplier": "1996919652626202626",
				"applyorders_adviseSupplier_name": "达利园供应商",
				"applyorders_vendor": "1996919652626202626",
				"applyorders_vendor_name": "达利园供应商",
				"applyorders_memo": "要货请购",
				"applyorders_productsku_modelDescription": "33mm",
				"applyorders_product_model": "33mm",
				"applyorders_product_modelDescription": "细口",
				"applyorders_propertiesValue": "细口",
				"project": "1996923973363302404",
				"project_code": "00034",
				"project_name": "虚拟项目",
				"applyorders_trackNo": "0002",
				"applyOrderDefineCharacter": {
					"0": "i",
					"1": "d",
					"2": " ",
					"3": ":",
					"4": " ",
					"5": " ",
					"6": "\"",
					"7": "1",
					"8": "9",
					"9": "9",
					"10": "5",
					"11": "9",
					"12": "8",
					"13": "2",
					"14": "7",
					"15": "4",
					"16": "8",
					"17": "4",
					"18": "5",
					"19": "9",
					"20": "2",
					"21": "0",
					"22": "4",
					"23": "6",
					"24": "1",
					"25": "6",
					"26": "\"",
					"27": " ",
					"28": "p",
					"29": "u",
					"30": "b",
					"31": "t",
					"32": "s",
					"33": " ",
					"34": ":",
					"35": " ",
					"36": " ",
					"37": "\"",
					"38": "2",
					"39": "0",
					"40": "2",
					"41": "4",
					"42": "-",
					"43": "0",
					"44": "5",
					"45": "-",
					"46": "1",
					"47": "3",
					"48": " ",
					"49": "0",
					"50": "9",
					"51": ":",
					"52": "1",
					"53": "6",
					"54": ":",
					"55": "1",
					"56": "5",
					"57": "\"",
					"58": " ",
					"59": "y",
					"60": "t",
					"61": "e",
					"62": "n",
					"63": "a",
					"64": "n",
					"65": "t",
					"66": " ",
					"67": ":",
					"68": " ",
					"69": " ",
					"70": "\"",
					"71": "0",
					"72": "0",
					"73": "0",
					"74": "0",
					"75": "L",
					"76": "J",
					"77": "5",
					"78": "I",
					"79": "3",
					"80": "I",
					"81": "7",
					"82": "H",
					"83": "6",
					"84": "Y",
					"85": "A",
					"86": "P",
					"87": "Z",
					"88": "9",
					"89": "0",
					"90": "0",
					"91": "0",
					"92": "0",
					"93": "\"",
					"U9001": "",
					"id": ""
				},
				"applyOrdersDefineCharacter": {
					"0": "i",
					"1": "d",
					"2": " ",
					"3": ":",
					"4": " ",
					"5": " ",
					"6": "\"",
					"7": "1",
					"8": "9",
					"9": "9",
					"10": "5",
					"11": "9",
					"12": "8",
					"13": "2",
					"14": "7",
					"15": "4",
					"16": "8",
					"17": "4",
					"18": "5",
					"19": "9",
					"20": "2",
					"21": "0",
					"22": "4",
					"23": "6",
					"24": "1",
					"25": "6",
					"26": "\"",
					"27": " ",
					"28": "p",
					"29": "u",
					"30": "b",
					"31": "t",
					"32": "s",
					"33": " ",
					"34": ":",
					"35": " ",
					"36": " ",
					"37": "\"",
					"38": "2",
					"39": "0",
					"40": "2",
					"41": "4",
					"42": "-",
					"43": "0",
					"44": "5",
					"45": "-",
					"46": "1",
					"47": "3",
					"48": " ",
					"49": "0",
					"50": "9",
					"51": ":",
					"52": "1",
					"53": "6",
					"54": ":",
					"55": "1",
					"56": "5",
					"57": "\"",
					"58": " ",
					"59": "y",
					"60": "t",
					"61": "e",
					"62": "n",
					"63": "a",
					"64": "n",
					"65": "t",
					"66": " ",
					"67": ":",
					"68": " ",
					"69": " ",
					"70": "\"",
					"71": "0",
					"72": "0",
					"73": "0",
					"74": "0",
					"75": "L",
					"76": "J",
					"77": "5",
					"78": "I",
					"79": "3",
					"80": "I",
					"81": "7",
					"82": "H",
					"83": "6",
					"84": "Y",
					"85": "A",
					"86": "P",
					"87": "Z",
					"88": "9",
					"89": "0",
					"90": "0",
					"91": "0",
					"92": "0",
					"93": "\"",
					"XS11": "",
					"XS15": "",
					"id": ""
				},
				"applyOrdersCharacteristics": {
					"0": "i",
					"1": "d",
					"2": " ",
					"3": ":",
					"4": " ",
					"5": " ",
					"6": "\"",
					"7": "1",
					"8": "9",
					"9": "9",
					"10": "5",
					"11": "9",
					"12": "8",
					"13": "2",
					"14": "7",
					"15": "4",
					"16": "8",
					"17": "4",
					"18": "5",
					"19": "9",
					"20": "2",
					"21": "0",
					"22": "4",
					"23": "6",
					"24": "1",
					"25": "6",
					"26": "\"",
					"27": " ",
					"28": "p",
					"29": "u",
					"30": "b",
					"31": "t",
					"32": "s",
					"33": " ",
					"34": ":",
					"35": " ",
					"36": " ",
					"37": "\"",
					"38": "2",
					"39": "0",
					"40": "2",
					"41": "4",
					"42": "-",
					"43": "0",
					"44": "5",
					"45": "-",
					"46": "1",
					"47": "3",
					"48": " ",
					"49": "0",
					"50": "9",
					"51": ":",
					"52": "1",
					"53": "6",
					"54": ":",
					"55": "1",
					"56": "5",
					"57": "\"",
					"58": " ",
					"59": "y",
					"60": "t",
					"61": "e",
					"62": "n",
					"63": "a",
					"64": "n",
					"65": "t",
					"66": " ",
					"67": ":",
					"68": " ",
					"69": " ",
					"70": "\"",
					"71": "0",
					"72": "0",
					"73": "0",
					"74": "0",
					"75": "L",
					"76": "J",
					"77": "5",
					"78": "I",
					"79": "3",
					"80": "I",
					"81": "7",
					"82": "H",
					"83": "6",
					"84": "Y",
					"85": "A",
					"86": "P",
					"87": "Z",
					"88": "9",
					"89": "0",
					"90": "0",
					"91": "0",
					"92": "0",
					"93": "\"",
					"XS15": "",
					"XXX0111": "",
					"id": ""
				}
			}
		]
	}
}
6. 错误返回码
错误码	错误信息	描述
7. 错误返回示例
{
    "code": 999,
    "message": "服务端逻辑异常"
}