@echo off
chcp 65001 >nul
title YS-API V3.0 - Quick Start
cls
echo.
echo ========================================================
echo                  YS-API V3.0 Quick Start
echo ========================================================
echo.
echo Starting Complete System (Frontend + Backend)
echo.

echo Checklist:
echo   [ ] Frontend Static Server (Port 8080)
echo   [ ] Backend API Server (Port 8000)
echo.

echo Starting Frontend Server...
start "YS-API-Frontend" cmd /k "title YS-API Frontend Server (8080) && cd /d \"d:\OneDrive\Desktop\YS-API程序\v3\frontend\" && echo. && echo Frontend Server Started && echo URL: http://localhost:8080/migrated/database-v2.html && echo. && python -m http.server 8080"

echo Frontend Server Started!

echo.
echo Waiting 3 seconds before starting backend...
timeout /t 3 /nobreak >nul

echo Starting Backend API Server...
start "YS-API-Backend" cmd /k "title YS-API Backend Server (8000) && cd /d \"d:\OneDrive\Desktop\YS-API程序\v3\backend\" && echo. && echo Backend API Server Started && echo URL: http://localhost:8000/health && echo. && python start_server.py"

echo Backend API Server Started!

echo.
echo System Startup Complete!
echo.
echo ========================================================
echo Access URLs:
echo.
echo   Frontend: http://localhost:8080/migrated/database-v2.html
echo   API Health: http://localhost:8000/health
echo.
echo Test Pages:
echo   - Data Management: /migrated/database-v2.html
echo   - Field Config: /migrated/unified-field-config.html
echo   - Report Generation: /migrated/excel-translation.html
echo   - Maintenance: /migrated/maintenance.html
echo.
echo Instructions:
echo   - Two servers running in separate windows
echo   - Frontend pages can now access API functions
echo   - Press Ctrl+C in server windows to stop services
echo.
echo ========================================================
echo.
pause
echo Thank you for using YS-API V3.0!
exit
