<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YS-API V3.0 访问导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 1200px;
            width: 100%;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 40px 20px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #333;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #667eea;
            position: relative;
        }

        .section h2::before {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 50px;
            height: 3px;
            background: #764ba2;
        }

        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .link-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .link-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .link-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .link-card h3 {
            color: #333;
            font-size: 1.3em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .link-card h3::before {
            content: '🔗';
            margin-right: 10px;
            font-size: 1.2em;
        }

        .link-card p {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .link-card a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 6px;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .link-card a:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .link-card a::after {
            content: '→';
            margin-left: 8px;
            transition: transform 0.3s ease;
        }

        .link-card a:hover::after {
            transform: translateX(3px);
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background: #28a745;
            box-shadow: 0 0 5px rgba(40, 167, 69, 0.5);
        }

        .status-offline {
            background: #dc3545;
        }

        .status-unknown {
            background: #ffc107;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .info-card .icon {
            font-size: 2.5em;
            margin-bottom: 15px;
        }

        .info-card h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .info-card p {
            color: #666;
            font-size: 0.9em;
        }

        .footer {
            background: #f8f9fa;
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #e9ecef;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .links-grid {
                grid-template-columns: 1fr;
            }
            
            .content {
                padding: 20px;
            }
        }

        .refresh-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.2em;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            background: #764ba2;
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <button class="refresh-btn" onclick="checkAllServices()" title="刷新服务状态">🔄</button>
    
    <div class="container">
        <div class="header">
            <h1>🚀 YS-API V3.0</h1>
            <p>企业级API系统 - 访问导航中心</p>
        </div>

        <div class="content">
            <!-- 主要服务 -->
            <div class="section">
                <h2>🌐 主要服务</h2>
                <div class="links-grid">
                    <div class="link-card">
                        <h3><span class="status-indicator status-unknown" id="frontend-status"></span>前端主界面</h3>
                        <p>系统主控制台，提供可视化操作界面</p>
                        <a href="http://localhost:3000" target="_blank" id="frontend-link">访问前端</a>
                    </div>
                    
                    <div class="link-card">
                        <h3><span class="status-indicator status-unknown" id="backend-status"></span>后端API服务</h3>
                        <p>核心API服务，处理业务逻辑和数据</p>
                        <a href="http://localhost:8000" target="_blank" id="backend-link">访问API</a>
                    </div>
                    
                    <div class="link-card">
                        <h3><span class="status-indicator status-unknown" id="health-status"></span>健康检查</h3>
                        <p>实时监控系统运行状态</p>
                        <a href="http://localhost:8000/health" target="_blank" id="health-link">检查状态</a>
                    </div>
                </div>
            </div>

            <!-- 功能页面 -->
            <div class="section">
                <h2>🛠️ 功能页面</h2>
                <div class="links-grid">
                    <div class="link-card">
                        <h3>字段配置管理</h3>
                        <p>配置和管理数据字段映射关系</p>
                        <a href="http://localhost:3000/field-config.html" target="_blank">字段配置</a>
                    </div>
                    
                    <div class="link-card">
                        <h3>数据库管理</h3>
                        <p>数据库操作和管理界面</p>
                        <a href="http://localhost:3000/database-v2.html" target="_blank">数据库管理</a>
                    </div>
                    
                    <div class="link-card">
                        <h3>Excel文件处理</h3>
                        <p>Excel文件导入、导出和翻译</p>
                        <a href="http://localhost:3000/excel-translation.html" target="_blank">Excel处理</a>
                    </div>
                    
                    <div class="link-card">
                        <h3>数据同步测试</h3>
                        <p>测试数据同步和传输功能</p>
                        <a href="http://localhost:3000/sync-test.html" target="_blank">同步测试</a>
                    </div>
                    
                    <div class="link-card">
                        <h3>性能监控</h3>
                        <p>系统性能指标监控和分析</p>
                        <a href="http://localhost:3000/performance-optimization-demo.html" target="_blank">性能监控</a>
                    </div>
                    
                    <div class="link-card">
                        <h3>系统维护</h3>
                        <p>系统维护和管理工具</p>
                        <a href="http://localhost:3000/maintenance.html" target="_blank">系统维护</a>
                    </div>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="section">
                <h2>📊 系统信息</h2>
                <div class="info-grid">
                    <div class="info-card">
                        <div class="icon">🚀</div>
                        <h4>版本信息</h4>
                        <p>YS-API V3.0<br>企业级API系统</p>
                    </div>
                    
                    <div class="info-card">
                        <div class="icon">🌐</div>
                        <h4>服务端口</h4>
                        <p>前端: 3000<br>后端: 8000</p>
                    </div>
                    
                    <div class="info-card">
                        <div class="icon">💾</div>
                        <h4>数据库</h4>
                        <p>SQLite<br>轻量高效</p>
                    </div>
                    
                    <div class="info-card">
                        <div class="icon">🔒</div>
                        <h4>安全等级</h4>
                        <p>企业级<br>100/100分</p>
                    </div>
                </div>
            </div>

            <!-- 快速启动 -->
            <div class="section">
                <h2>⚡ 快速启动</h2>
                <div class="links-grid">
                    <div class="link-card">
                        <h3>后端服务</h3>
                        <p>启动后端API服务器</p>
                        <a href="#" onclick="alert('请运行 start_backend.bat 文件')">启动后端</a>
                    </div>
                    
                    <div class="link-card">
                        <h3>前端服务</h3>
                        <p>启动前端HTTP服务器</p>
                        <a href="#" onclick="alert('请运行 start_frontend_new.bat 文件')">启动前端</a>
                    </div>
                    
                    <div class="link-card">
                        <h3>统一启动</h3>
                        <p>一键启动所有服务</p>
                        <a href="#" onclick="alert('请运行 启动器.bat 文件')">统一启动</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>🎉 YS-API V3.0 - 现代化企业级API系统 | 生产环境就绪评分: 100/100</p>
            <p>最后更新: <span id="last-update"></span></p>
        </div>
    </div>

    <script>
        // 检查服务状态
        async function checkServiceStatus(url, statusId) {
            try {
                const response = await fetch(url, { 
                    method: 'GET', 
                    mode: 'no-cors',
                    timeout: 5000 
                });
                document.getElementById(statusId).className = 'status-indicator status-online';
                return true;
            } catch (error) {
                document.getElementById(statusId).className = 'status-indicator status-offline';
                return false;
            }
        }

        // 检查所有服务
        async function checkAllServices() {
            console.log('检查服务状态...');
            
            // 检查后端服务
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    document.getElementById('backend-status').className = 'status-indicator status-online';
                    document.getElementById('health-status').className = 'status-indicator status-online';
                } else {
                    throw new Error('Backend not responding');
                }
            } catch (error) {
                document.getElementById('backend-status').className = 'status-indicator status-offline';
                document.getElementById('health-status').className = 'status-indicator status-offline';
            }

            // 检查前端服务
            try {
                const response = await fetch('http://localhost:3000');
                if (response.ok) {
                    document.getElementById('frontend-status').className = 'status-indicator status-online';
                } else {
                    throw new Error('Frontend not responding');
                }
            } catch (error) {
                document.getElementById('frontend-status').className = 'status-indicator status-offline';
            }
        }

        // 页面加载时检查服务状态
        document.addEventListener('DOMContentLoaded', function() {
            // 设置最后更新时间
            document.getElementById('last-update').textContent = new Date().toLocaleString('zh-CN');
            
            // 延迟检查服务状态，给服务一些启动时间
            setTimeout(checkAllServices, 1000);
            
            // 每30秒检查一次服务状态
            setInterval(checkAllServices, 30000);
        });

        // 添加链接点击统计
        document.querySelectorAll('a[target="_blank"]').forEach(link => {
            link.addEventListener('click', function() {
                console.log('访问链接:', this.href);
            });
        });
    </script>
</body>
</html>
