销售订单列表查询
发布时间:2025-02-18 14:13:14
用于批量分页查询销售订单数据的接口，销售订单列表查询与单据列表界面默认查询方案返回结果一致

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	动态域名，获取方式详见 获取租户所在数据中心域名
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/sd/voucherorder/list
请求方式	POST
ContentType	application/json
应用场景	开放API
API类别	列表查询
事务和幂等性	无
限流次数	
当前API限流次数暂未透出

2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
pageIndex	int	否	是	页号    示例: 1    默认值: 1
pageSize	int	否	是	每页行数    示例: 10    默认值: 10
code	string	否	否	单据编号    示例: UO-20220513000001
nextStatusName	string	否	否	订单状态, CONFIRMORDER:开立、DELIVERY_PART:部分发货、DELIVERY_TAKE_PART:部分发货待收货、DELIVERGOODS:待发货、TAKEDELIVERY:待收货、ENDORDER:已完成、OPPOSE:已取消、APPROVING:审批中、    示例: CONFIRMORDER
open_orderDate_begin	string	否	否	制单日期开始时间,格式为:yyyy-MM-dd HH:mm:ss    示例: 2022-05-13 00:00:00
open_orderDate_end	string	否	否	制单结束时间,格式为:yyyy-MM-dd HH:mm:ss    示例: 2022-05-13 00:00:00
open_hopeReceiveDate_begin	string	否	否	期望收货开始时间,格式为:yyyy-MM-dd HH:mm:ss    示例: 2022-05-13 00:00:00
open_hopeReceiveDate_end	string	否	否	期望收货截止,格式为:yyyy-MM-dd HH:mm:ss    示例: 2022-05-13 00:00:00
open_vouchdate_begin	string	否	否	单据开始时间,格式为:yyyy-MM-dd HH:mm:ss    示例: 2022-05-13 00:00:00
open_vouchdate_end	string	否	否	单据截止时间,格式为:yyyy-MM-dd HH:mm:ss    示例: 2022-05-13 00:00:00
isSum	boolean	否	否	查询表头    示例: false    默认值: false
simpleVOs	object	是	否	查询条件
op	string	否	否	比较符(eq:等于;neq:不等于;lt:小于;gt:大于;like:模糊匹配;between:介于)    示例: eq
value1	string	否	否	查询条件值1    示例: UO-20220513000001
field	string	否	否	查询条件字段名    示例: code
logicOp	string	否	否	分级逻辑符(and,or)
value2	string	否	否	查询条件值2
queryOrders	object	是	否	排序字段
field	string	否	否	排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：orderDetails.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)    示例：id    示例: vouchdate
order	string	否	否	顺序:正序(asc);倒序(desc) 示例：asc    示例: asc
3. 请求示例
Url: /yonbip/sd/voucherorder/list?access_token=访问令牌
Body: {
	"pageIndex": 1,
	"pageSize": 10,
	"code": "UO-20220513000001",
	"nextStatusName": "CONFIRMORDER",
	"open_orderDate_begin": "2022-05-13 00:00:00",
	"open_orderDate_end": "2022-05-13 00:00:00",
	"open_hopeReceiveDate_begin": "2022-05-13 00:00:00",
	"open_hopeReceiveDate_end": "2022-05-13 00:00:00",
	"open_vouchdate_begin": "2022-05-13 00:00:00",
	"open_vouchdate_end": "2022-05-13 00:00:00",
	"isSum": false,
	"simpleVOs": [
		{
			"op": "eq",
			"value1": "UO-20220513000001",
			"field": "code",
			"logicOp": "",
			"value2": ""
		}
	],
	"queryOrders": [
		{
			"field": "vouchdate",
			"order": "asc"
		}
	]
}
4. 返回值参数
名称	类型	数组	描述
code	string	否	返回码，调用成功时返回200
message	string	否	调用失败时的错误信息
data	object	否	调用成功时的返回数据
pageIndex	int	否	页号
pageSize	int	否	每页记录数
recordCount	int	否	总共记录数
recordList	object	是	记录列表
code	string	否	单据编号
vouchdate	string	否	单据日期
id	long	否	主体ID
parentOrderNo	string	否	父级订单号
salesOrgId	string	否	销售组织id
salesOrgId_name	string	否	销售组织名称
saleDepartmentId	string	否	销售部门id
transactionTypeId	string	否	交易类型id
transactionTypeId_name	string	否	交易类型名称
agentId	long	否	客户id
agentId_name	string	否	客户名称
receiveContacter	string	否	客户联系人
receiveContacterPhone	string	否	客户联系人电话
receievInvoiceMobile	string	否	收票手机号
receievInvoiceEmail	string	否	收票邮箱
purchaseNo	string	否	客户采购订单号
corpContact	string	否	销售业务员id
corpContactUserName	string	否	销售业务员
settlementOrgId_name	string	否	开票组织名称
corpContactUserErpCode	string	否	业务员erp编码
orderPrices	object	否	订单价格
currency	string	否	币种id
currency_priceDigit	long	否	原币单价精度
currency_moneyDigit	long	否	原币金额精度
originalName	string	否	币种
natCurrency	string	否	本币pk
natCurrency_priceDigit	long	否	本币单价精度
natCurrency_moneyDigit	long	否	本币金额精度
domesticCode	string	否	本币简称
domesticName	string	否	本币
exchRate	int	否	汇率
exchangeRateType_name	string	否	汇率类型名称
exchangeRateType	string	否	汇率类型Idid
ctTplId	string	否	合同模板id
ctTplCode	string	否	合同模板编码
ctTplName	string	否	合同模板
signFileId	string	否	待签署合同文件
signStatus	string	否	电子签署状态
payMoney	number
小数位数:8,最大长度:28	否	合计含税金额
orderPayMoney	number
小数位数:8,最大长度:28	否	商品实付金额
realMoney	number
小数位数:8,最大长度:28	否	应收金额
orderRealMoney	number
小数位数:8,最大长度:28	否	商品应付金额
statusCode	string	否	订单当前状态码
nextStatus	string	否	订单状态, CONFIRMORDER:开立、DELIVERY_PART:部分发货、DELIVERY_TAKE_PART:部分发货待收货、DELIVERGOODS:待发货、TAKEDELIVERY:待收货、ENDORDER:已完成、OPPOSE:已取消、APPROVING:审批中、
currentStatus	long	否	当前状态位置
payStatusCode	string	否	付款状态, NOTPAYMENT:未付款、PARTPAYMENT:部分付款、CONFIRMPAYMENT:部分付款待确认、CONFIRMPAYMENT_ALL:付款待确认、FINISHPAYMENT:付款完成、
settlementOrgId	string	否	开票组织id
lockIn	boolean	否	标记锁
confirmDate	string	否	订单确认时间
payDate	string	否	订单付款时间
orderPayType	string	否	支付方式
settlement	string	否	结算方式id
shippingChoiceId	string	否	发运方式id
sendDate	string	否	预计发货日期
hopeReceiveDate	string	否	期望收货日期
opposeMemo	string	否	驳回批注
haveDelivery	boolean	否	是否存在发货单
occupyInventory	string	否	库存占用时机标识
separatePromotionType	string	否	拆单规则标识
reight	number
小数位数:8,最大长度:28	否	运费
synSourceOrg	string	否	协同来源组织id
synSourceTenant	string	否	协同来源租户
synSourceOrg_name	string	否	协同来源组织名称
totalMoney	number
小数位数:8,最大长度:28	否	总金额
tagName	string	否	采购组织弹框
rebateCashMoney	number
小数位数:8,最大长度:28	否	抵现金额
particularlyMoney	number
小数位数:8,最大长度:28	否	特殊优惠
promotionMoney	number
小数位数:8,最大长度:28	否	促销
unConfirmPrice	number
小数位数:8,最大长度:28	否	未审核的金额
confirmPrice	number
小数位数:8,最大长度:28	否	已支付金额
bizId	string	否	商家id
bizName	string	否	商家名称
agentRelationId	long	否	客户交易关系id
points	string	否	积分
pubts	string	否	时间戳,格式为:yyyy-MM-dd HH:mm:ss
pubuts	string	否	时间戳
orderInvoice	string	否	发票信息
orderShippingAddress	string	否	收货地址信息
orderErp	string	否	订单erp
deliveryDate	string	否	交货日期
isWfControlled	boolean	否	是否审批流控制
verifystate	long	否	审批状态
status	long	否	状态
orderDefineCharacter	特征组
voucher.order.Order	否	表头自定义项特征组
A086	Date	否	账款到期日
AE86	string	否	销售订单收款协议
U9004	string	否	U9销售订单号
XS01	string	否	成交方式
XS02	string	否	装运港
XS03	string	否	目的港
XS04	Decimal	否	溢短装
XS05	Decimal	否	投保比例
XS06	string	否	基本险别
XS07	boolean	否	允许转运
XS08	boolean	否	允许分批
XS09	Date	否	最迟装运期
XS10	string	否	贸易方式
XS12	string	否	合同号
XS13	string	否	商标
XS14	string	否	装柜
XS17	string	否	付款方式
XS18	boolean	否	首次
XS19	boolean	否	见附件
XS20	boolean	否	首次未确定
XS21	Date	否	首次未确定-预计时间
XS22	boolean	否	返单
XS23	boolean	否	确定无变更
XS24	boolean	否	返单未确定
XS25	Date	否	返单未确定-预计时间
XS26	boolean	否	确定变更
XS27	string	否	单头备注1
XS28	string	否	单头备注2
XS29	string	否	单头备注3
XS30	string	否	单头备注4
XS31	Decimal	否	更改次数
XS32	string	否	车牌号码
id	string	否	特征id,主键,新增时无需填写,修改时必填
isFinishDelivery	boolean	否	订单是否发完货
productId_pbatchName	long	否	商品包装单位
idKey	string	否	行标识
productId	long	否	商品id
priceMark	boolean	否	价格标识
isBatchManage	boolean	否	是否批次管理
isExpiryDateManage	boolean	否	是否有效期管理
expireDateNo	string	否	保质期
expireDateUnit	string	否	保质期单位
skuId	long	否	商品SKUid
erpCode	string	否	skuERP编码
orderProductType	string	否	商品售卖类型, SALE:销售品、GIFT:赠品、MARKUP:加价购、REBATE:返利商品、
productCode	string	否	商品编码
productName	string	否	商品名称
skuCode	string	否	SKU编码
specDescription	string	否	规格描述
projectId	string	否	项目id
unitExchangeType	int	否	浮动（计价）
unitExchangeTypePrice	int	否	浮动（销售）
productAuxUnitName	string	否	销售单位
invExchRate	number
小数位数:8,最大长度:28	否	销售换算率
subQty	number
小数位数:8,最大长度:28	否	销售数量
productUnitName	string	否	计价单位
invPriceExchRate	number
小数位数:8,最大长度:28	否	计价换算率
priceQty	number
小数位数:8,最大长度:28	否	计价数量
qtyName	string	否	主计量
qty	number
小数位数:8,最大长度:28	否	数量
variantconfigctsId	number
小数位数:0,最大长度:20	否	选配结果清单id
orderDetailPrices	object	否	订单商品金额
saleCost_orig_taxfree	number
小数位数:8,最大长度:28	否	原币无税合计
oriUnitPrice	number
小数位数:8,最大长度:28	否	无税成交价
oriMoney	number
小数位数:8,最大长度:28	否	无税金额
oriTax	number
小数位数:8,最大长度:28	否	税额
natSum	number
小数位数:8,最大长度:28	否	本币含税金额
natTaxUnitPrice	number
小数位数:8,最大长度:28	否	本币含税单价
natMoney	number
小数位数:8,最大长度:28	否	本币无税金额
natUnitPrice	number
小数位数:8,最大长度:28	否	本币无税单价
natTax	number
小数位数:8,最大长度:28	否	本币税额
orderDetailId	long	否	订单详情id
salePrice_orig_taxfree	number
小数位数:8,最大长度:28	否	无税单价
rebateMoneyOrigTaxfree	number
小数位数:8,最大长度:28	否	无税分摊返利
particularlyMoneyOrigTaxfree	number
小数位数:8,最大长度:28	否	无税特殊优惠
promotionMoneyOrigTaxfree	number
小数位数:8,最大长度:28	否	无税促销优惠
pointsMoneyOrigTaxfree	number
小数位数:8,最大长度:28	否	无税积分抵扣
saleCost_domestic	number
小数位数:8,最大长度:28	否	报价本币含税金额
salePrice_domestic	number
小数位数:8,最大长度:28	否	报价本币含税单价
rebateMoneyDomestic	number
小数位数:8,最大长度:28	否	本币分摊返利
particularlyMoneyDomestic	number
小数位数:8,最大长度:28	否	本币特殊优惠
promotionMoneyDomestic	number
小数位数:8,最大长度:28	否	本币促销优惠
pointsMoneyDomestic	number
小数位数:8,最大长度:28	否	本币积分抵扣
saleCost_domestic_taxfree	number
小数位数:8,最大长度:28	否	报价本币无税金额
salePrice_domestic_taxfree	number
小数位数:8,最大长度:28	否	报价本币无税单价
rebateMoneyDomesticTaxfree	number
小数位数:8,最大长度:28	否	本币无税分摊返利
particularlyMoneyDomesticTaxfree	number
小数位数:8,最大长度:28	否	本币无税特殊优惠
promotionMoneyDomesticTaxfree	number
小数位数:8,最大长度:28	否	本币无税促销优惠
pointsMoneyDomesticTaxfree	number
小数位数:8,最大长度:28	否	本币无税积分抵扣
id	long	否	主体ID
prepayInvRvnRecogBkgMeth	string	否	预收款开票应收入账方式 (1: 预收款开票-税额记应收 ; 2 : 预收款开票-全额记应收 )
checkByRevenueManagement	string	否	收入管理核算
revPerformObligation	string	否	已生成收入履约义务
serviceStartDate	string	否	服务起始日期
serviceEndDate	string	否	服务结束日期
optionalQuotationId	number
小数位数:0,最大长度:20	否	报价配置清单ID
optionalQuotationId_code	string	否	报价配置清单编码
variantconfigctsCode	string	否	配置号
variantconfigctsVersion	string	否	配置清单版本
calBase	string	否	计算基准
stockName	string	否	发货仓库
stockOrgId_name	string	否	库存组织
consignTime	string	否	计划发货日期
projectId_name	string	否	项目名称
projectId_code	string	否	项目编码
noTaxSalePrice	number
小数位数:8,最大长度:28	否	无税报价
salePrice	number
小数位数:8,最大长度:28	否	含税报价
taxId	string	否	数目税率id
costCurrencyName	string	否	成本币种
costAmt	BigDecimal	否	成本金额
costPrice	BigDecimal	否	成本价
noTaxSaleCost	number
小数位数:8,最大长度:28	否	报价无税金额
saleCost	number
小数位数:8,最大长度:28	否	报价含税金额
stockId	string	否	仓库ID
oriTaxUnitPrice	number
小数位数:8,最大长度:28	否	含税成交价
lineno	int	否	行号
orderDetails_stockOrgId	string	否	库存组织id
oriSum	number
小数位数:8,最大长度:28	否	含税金额
transactionTypeId	string	否	交易类型id
taxRate	number
小数位数:8,最大长度:28	否	税率
taxItems	string	否	税目
taxCode	string	否	税目税率编码
rebateMoney	number
小数位数:8,最大长度:28	否	返利分摊金额
pointsMoney	number
小数位数:8,最大长度:28	否	积分
shoppingCartId	string	否	购物车id
groupId	long	否	分组Id
rebateReturnProductId	string	否	返货单商品id
mutualActivities	string	否	活动的对象,用于校验互斥活动
activities	string	否	包含的类型,用于校验互斥
salesOrgId	string	否	销售组织id
sendPayMoney	number
小数位数:8,最大长度:28	否	累计已发货含税金额
saleDepartmentId	string	否	销售部门id
invoiceQty	number
小数位数:8,最大长度:28	否	累计开票数量
settlementOrgId	string	否	财务组织id
invoiceOriSum	number
小数位数:8,最大长度:28	否	累计开票含税金额
bizProductId	long	否	商家商品id
takeQuantity	number
小数位数:8,最大长度:28	否	已审核收数量
bizSkuId	string	否	商家skuid
takeSalePayMoney	number
小数位数:8,最大长度:28	否	已审核收金额
orderDetailPrice	number
小数位数:8,最大长度:28	否	订单金额
sendQty	number
小数位数:8,最大长度:28	否	累计已发货数量
closedSumMoney	number
小数位数:8,最大长度:28	否	关闭总金额
closedRowCount	number
小数位数:8,最大长度:28	否	行关闭数量
iDeleted	boolean	否	是否删除
iOrgId	string	否	组织ID
memo	string	否	备注
createDate	string	否	创建日期
creatorId	long	否	创建人
auditorId	string	否	审核人ID
auditDate	string	否	审批日期
closerId	string	否	关闭人ID
closeDate	string	否	关闭日期
modifierId	string	否	修改人id
modifyDate	string	否	修改日期
cCreator	string	否	创建人
iProductAuxUnitId	long	否	销售单位id
iProductUnitId	long	否	计价单位id
masterUnitId	long	否	主计量单位id
purUOM_Precision	long	否	销售单位精度
priceUOM_Precision	long	否	计价单位精度
unit_Precision	long	否	主计量单位精度
cBizName	string	否	供应商名称
orderDetailId	long	否	主体ID
orderId	long	否	订单ID
orderDetailCharacteristics	特征组
voucher.order.OrderDetail	否	表体自由项特征组
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
orderDetailDefineCharacter	特征组
voucher.order.OrderDetail	否	表体自定义项特征组
WL06	Decimal	否	料品每箱台数
XS11	string	否	需求分类号test
XS15	string	否	顾客订单号（订单表体）
id	string	否	特征id,主键,新增时无需填写,修改时必填
creator	string	否	创建人
createTime	string	否	创建时间
auditor	string	否	审批人
auditTime	string	否	审批时间
closeTime	string	否	关闭时间
closer	string	否	关闭人
modifier	string	否	修改人
modifyTime	string	否	修改时间
bmake_st_salesout	string	否	流程入库
bmake_voucher_delivery	string	否	流程发货
bizFlow	long	否	流程ID
bmake_voucher_saleinvoice	string	否	流程开票
isFlowCoreBill	boolean	否	是否流程核心单据
bizFlow_version	string	否	版本信息
batchNo	string	否	批次号
productDate	string	否	生产日期
invalidDate	string	否	有效期至
isAdvRecInv	boolean	否	预收款开票
advRecInvMoney	number
小数位数:8,最大长度:28	否	累计预收款开票金额
advRecInvQty	number
小数位数:8,最大长度:28	否	累计预收款开票数量
natAdvRecInvMoney	number
小数位数:8,最大长度:28	否	累计本币预收款开票金额
advRecInvTaxMoney	number
小数位数:8,最大长度:28	否	累计预收款开票税额
natAdvRecInvTaxMoney	number
小数位数:8,最大长度:28	否	累计本币预收款开票税额
offsetAdvRecInvMoney	number
小数位数:8,最大长度:28	否	累计冲抵预收款开票金额
offsetAdvRecInvQty	number
小数位数:8,最大长度:28	否	累计冲抵预收款开票数量
offsetNatAdvRecInvMoney	number
小数位数:8,最大长度:28	否	累计冲抵本币预收款开票金额
offsetAdvRecInvTaxMoney	number
小数位数:8,最大长度:28	否	累计冲抵预收款开票税额
offsetNatAdvRecInvTaxMoney	number
小数位数:8,最大长度:28	否	累计冲抵本币预收款开票税额
tradeRouteID	string	否	贸易路径ID
tradeRouteID_code	string	否	贸易路径编码
tradeRouteID_name	string	否	贸易路径
isEndTrade	int	否	是否末级站点
tradeRouteLineno	string	否	站点
collaborationPocode	string	否	协同来源单据号
collaborationPodetailid	string	否	协同来源单据子表id
collaborationPoid	string	否	协同来源单据主表id
collaborationPorowno	string	否	协同来源单据行号
collaborationSource	string	否	协同来源单据类型
entireDeliveryStatus	number
小数位数:0,最大长度:6	否	整单发货状态
entireIssueStatus	number
小数位数:0,最大长度:6	否	整单出库状态
entireInvoiceStatus	number
小数位数:0,最大长度:6	否	整单发票状态
entireSignConfirmStatus	number
小数位数:0,最大长度:6	否	整单签收状态
deliveryStatus	number
小数位数:0,最大长度:6	否	发货状态
issueStatus	number
小数位数:0,最大长度:6	否	出库状态
invoiceStatus	number
小数位数:0,最大长度:6	否	发票状态
signConfirmStatus	number
小数位数:0,最大长度:6	否	签收状态
sumRecordList	object	是	合计
noTaxSaleCost	number
小数位数:8,最大长度:28	否	合计报价无税金额
orderRealMoney	number
小数位数:8,最大长度:28	否	合计商品应付金额
realMoney	number
小数位数:8,最大长度:28	否	合计应收金额
orderPurchaseQty	number
小数位数:8,最大长度:28	否	合计累计采购数量
sendQty	number
小数位数:8,最大长度:28	否	合计累计已发货数量
subQty	number
小数位数:8,最大长度:28	否	合计销售数量
collectMoney	number
小数位数:8,最大长度:28	否	合计累计收款金额
sendPriceQty	number
小数位数:8,最大长度:28	否	合计累计已发货计价数量
coordinationQuantity	number
小数位数:8,最大长度:28	否	合计社会化协同量
totalOutStockOriTaxMoney	number
小数位数:8,最大长度:28	否	合计累计出库金额
prodCost	number
小数位数:8,最大长度:28	否	合计商品报价金额
totalOutStockOriMoney	number
小数位数:8,最大长度:28	否	合计累计出库计价数量
totalOutStockPriceQty	number
小数位数:8,最大长度:28	否	合计累计出库计价数量
orderPayMoney	number
小数位数:8,最大长度:28	否	合计商品实付金额
payMoneyOrigTaxfree	number
小数位数:8,最大长度:28	否	合计合计无税金额
auditCount	number
小数位数:8,最大长度:28	否	合计累计发货已审数量
oriSum	number
小数位数:8,最大长度:28	否	合计含税金额
returnQty	number
小数位数:8,最大长度:28	否	合计退货数量
totalOutStockQuantity	number
小数位数:8,最大长度:28	否	合计累计出库数量
saleCost	number
小数位数:8,最大长度:28	否	合计报价含税金额
cashRebateMoney	number
小数位数:8,最大长度:28	否	合计返利直接抵现
rebateMoney	number
小数位数:8,最大长度:28	否	合计返利分摊金额
orderRebateMoney	number
小数位数:8,最大长度:28	否	合计返利整单折扣
orderDetails_rebateMoney	number
小数位数:8,最大长度:28	否	合计返利分摊金额
priceQty	number
小数位数:8,最大长度:28	否	合计计价数量
qty	number
小数位数:8,最大长度:28	否	合计数量
sendPayMoney	number
小数位数:8,最大长度:28	否	合计发货金额
cusDiscountMoney	number
小数位数:8,最大长度:28	否	合计客户扣额
invoiceQty	number
小数位数:8,最大长度:28	否	合计累计开票数量
returnPreSendQty	number
小数位数:8,最大长度:28	否	合计退货待发数量
totalOutStockSubQty	number
小数位数:8,最大长度:28	否	合计累计出库件数
orderDetailPrices	object	否	订单详情金额
oriTax	number
小数位数:8,最大长度:28	否	合计税额
natTax	number
小数位数:8,最大长度:28	否	合计本币税额
natSum	number
小数位数:8,最大长度:28	否	合计本币含税金额
natMoney	number
小数位数:8,最大长度:28	否	合计本币无税金额
oriMoney	number
小数位数:8,最大长度:28	否	合计无税金额
lineDiscountMoney	number
小数位数:8,最大长度:28	否	合计扣额
totalOutStockConfirmQuantity	number
小数位数:8,最大长度:28	否	累计出库确认数量
totalOutStockConfirmSubQty	number
小数位数:8,最大长度:28	否	累计出库确认件数
orderPrices	object	否	订单金额
totalNatTax	number
小数位数:8,最大长度:28	否	合计本币总税额
advRecInvMoney	number
小数位数:8,最大长度:28	否	累计预收款开票金额
advRecInvQty	number
小数位数:8,最大长度:28	否	累计预收款开票数量
natAdvRecInvMoney	number
小数位数:8,最大长度:28	否	累计预收款开票本币金额
advRecInvTaxMoney	number
小数位数:8,最大长度:28	否	累计预收款开票税额
natAdvRecInvTaxMoney	number
小数位数:8,最大长度:28	否	累计预收款开票本币税额
offsetAdvRecInvMoney	number
小数位数:8,最大长度:28	否	累计冲抵预收款开票金额
offsetAdvRecInvQty	number
小数位数:8,最大长度:28	否	累计冲抵预收款开票数量
offsetNatAdvRecInvMoney	number
小数位数:8,最大长度:28	否	累计冲抵预收款开票本币金额
offsetAdvRecInvTaxMoney	number
小数位数:8,最大长度:28	否	累计冲抵预收款开票税额
offsetNatAdvRecInvTaxMoney	number
小数位数:8,最大长度:28	否	累计冲抵预收款开票本币税额
pageCount	long	否	总共记录数
beginPageIndex	long	否	页码列表的开始索引
endPageIndex	long	否	页码列表的结束索引
5. 正确返回示例
{
	"code": "",
	"message": "",
	"data": {
		"pageIndex": 1,
		"pageSize": 1,
		"recordCount": 276,
		"recordList": [
			{
				"code": "",
				"vouchdate": "",
				"id": 0,
				"parentOrderNo": "",
				"salesOrgId": "",
				"salesOrgId_name": "",
				"saleDepartmentId": "",
				"transactionTypeId": "",
				"transactionTypeId_name": "",
				"agentId": 0,
				"agentId_name": "",
				"receiveContacter": "",
				"receiveContacterPhone": "",
				"receievInvoiceMobile": "",
				"receievInvoiceEmail": "",
				"purchaseNo": "",
				"corpContact": "",
				"corpContactUserName": "",
				"settlementOrgId_name": "",
				"corpContactUserErpCode": "",
				"orderPrices": {
					"currency": "",
					"currency_priceDigit": 0,
					"currency_moneyDigit": 0,
					"originalName": "",
					"natCurrency": "",
					"natCurrency_priceDigit": 0,
					"natCurrency_moneyDigit": 0,
					"domesticCode": "",
					"domesticName": "",
					"exchRate": 0,
					"exchangeRateType_name": "",
					"exchangeRateType": "",
					"ctTplId": "",
					"ctTplCode": "",
					"ctTplName": "",
					"signFileId": "",
					"signStatus": ""
				},
				"payMoney": 0,
				"orderPayMoney": 0,
				"realMoney": 0,
				"orderRealMoney": 0,
				"statusCode": "",
				"nextStatus": "",
				"currentStatus": 0,
				"payStatusCode": "",
				"settlementOrgId": "",
				"lockIn": true,
				"confirmDate": "",
				"payDate": "",
				"orderPayType": "",
				"settlement": "",
				"shippingChoiceId": "",
				"sendDate": "",
				"hopeReceiveDate": "",
				"opposeMemo": "",
				"haveDelivery": true,
				"occupyInventory": "",
				"separatePromotionType": "",
				"reight": 0,
				"synSourceOrg": "",
				"synSourceTenant": "",
				"synSourceOrg_name": "",
				"totalMoney": 0,
				"tagName": "",
				"rebateCashMoney": 0,
				"particularlyMoney": 0,
				"promotionMoney": 0,
				"unConfirmPrice": 0,
				"confirmPrice": 0,
				"bizId": "",
				"bizName": "",
				"agentRelationId": 0,
				"points": "",
				"pubts": "",
				"pubuts": "",
				"orderInvoice": "",
				"orderShippingAddress": "",
				"orderErp": "",
				"deliveryDate": "",
				"isWfControlled": true,
				"verifystate": 0,
				"status": 0,
				"orderDefineCharacter": {
					"A086": "",
					"AE86": "",
					"U9004": "",
					"XS01": "",
					"XS02": "",
					"XS03": "",
					"XS04": 0,
					"XS05": 0,
					"XS06": "",
					"XS07": true,
					"XS08": true,
					"XS09": "",
					"XS10": "",
					"XS12": "",
					"XS13": "",
					"XS14": "",
					"XS17": "",
					"XS18": true,
					"XS19": true,
					"XS20": true,
					"XS21": "",
					"XS22": true,
					"XS23": true,
					"XS24": true,
					"XS25": "",
					"XS26": true,
					"XS27": "",
					"XS28": "",
					"XS29": "",
					"XS30": "",
					"XS31": 0,
					"XS32": "",
					"id": ""
				},
				"isFinishDelivery": true,
				"productId_pbatchName": 0,
				"idKey": "",
				"productId": 0,
				"priceMark": true,
				"isBatchManage": true,
				"isExpiryDateManage": true,
				"expireDateNo": "",
				"expireDateUnit": "",
				"skuId": 0,
				"erpCode": "",
				"orderProductType": "",
				"productCode": "",
				"productName": "",
				"skuCode": "",
				"specDescription": "",
				"projectId": "",
				"unitExchangeType": 0,
				"unitExchangeTypePrice": 0,
				"productAuxUnitName": "",
				"invExchRate": 0,
				"subQty": 0,
				"productUnitName": "",
				"invPriceExchRate": 0,
				"priceQty": 0,
				"qtyName": "",
				"qty": 0,
				"variantconfigctsId": 5235234243,
				"orderDetailPrices": {
					"saleCost_orig_taxfree": 0,
					"oriUnitPrice": 0,
					"oriMoney": 0,
					"oriTax": 0,
					"natSum": 0,
					"natTaxUnitPrice": 0,
					"natMoney": 0,
					"natUnitPrice": 0,
					"natTax": 0,
					"orderDetailId": 0,
					"salePrice_orig_taxfree": 0,
					"rebateMoneyOrigTaxfree": 0,
					"particularlyMoneyOrigTaxfree": 0,
					"promotionMoneyOrigTaxfree": 0,
					"pointsMoneyOrigTaxfree": 0,
					"saleCost_domestic": 0,
					"salePrice_domestic": 0,
					"rebateMoneyDomestic": 0,
					"particularlyMoneyDomestic": 0,
					"promotionMoneyDomestic": 0,
					"pointsMoneyDomestic": 0,
					"saleCost_domestic_taxfree": 0,
					"salePrice_domestic_taxfree": 0,
					"rebateMoneyDomesticTaxfree": 0,
					"particularlyMoneyDomesticTaxfree": 0,
					"promotionMoneyDomesticTaxfree": 0,
					"pointsMoneyDomesticTaxfree": 0,
					"id": 0,
					"prepayInvRvnRecogBkgMeth": "2",
					"checkByRevenueManagement": "",
					"revPerformObligation": "",
					"serviceStartDate": "",
					"serviceEndDate": "",
					"optionalQuotationId": 4323234234,
					"optionalQuotationId_code": "A123123",
					"variantconfigctsCode": "B234234",
					"variantconfigctsVersion": "1.0",
					"calBase": "0"
				},
				"stockName": "",
				"stockOrgId_name": "",
				"consignTime": "",
				"projectId_name": "",
				"projectId_code": "",
				"noTaxSalePrice": 0,
				"salePrice": 0,
				"taxId": "",
				"costCurrencyName": "",
				"costAmt": 0,
				"costPrice": 0,
				"noTaxSaleCost": 0,
				"saleCost": 0,
				"stockId": "",
				"oriTaxUnitPrice": 0,
				"lineno": 0,
				"orderDetails_stockOrgId": "",
				"oriSum": 0,
				"taxRate": 0,
				"taxItems": "",
				"taxCode": "",
				"rebateMoney": 0,
				"pointsMoney": 0,
				"shoppingCartId": "",
				"groupId": 0,
				"rebateReturnProductId": "",
				"mutualActivities": "",
				"activities": "",
				"sendPayMoney": 0,
				"invoiceQty": 0,
				"invoiceOriSum": 0,
				"bizProductId": 0,
				"takeQuantity": 0,
				"bizSkuId": "",
				"takeSalePayMoney": 0,
				"orderDetailPrice": 0,
				"sendQty": 0,
				"closedSumMoney": 0,
				"closedRowCount": 0,
				"iDeleted": true,
				"iOrgId": "",
				"memo": "",
				"createDate": "",
				"creatorId": 0,
				"auditorId": "",
				"auditDate": "",
				"closerId": "",
				"closeDate": "",
				"modifierId": "",
				"modifyDate": "",
				"cCreator": "",
				"iProductAuxUnitId": 0,
				"iProductUnitId": 0,
				"masterUnitId": 0,
				"purUOM_Precision": 0,
				"priceUOM_Precision": 0,
				"unit_Precision": 0,
				"cBizName": "",
				"orderDetailId": 0,
				"orderId": 0,
				"orderDetailCharacteristics": {
					"XS15": "",
					"XXX0111": "",
					"id": ""
				},
				"orderDetailDefineCharacter": {
					"WL06": 0,
					"XS11": "",
					"XS15": "",
					"id": ""
				},
				"creator": "",
				"createTime": "",
				"auditor": "",
				"auditTime": "",
				"closeTime": "",
				"closer": "",
				"modifier": "",
				"modifyTime": "",
				"bmake_st_salesout": "",
				"bmake_voucher_delivery": "",
				"bizFlow": 0,
				"bmake_voucher_saleinvoice": "",
				"isFlowCoreBill": true,
				"bizFlow_version": "",
				"batchNo": "",
				"productDate": "",
				"invalidDate": "",
				"isAdvRecInv": true,
				"advRecInvMoney": 0,
				"advRecInvQty": 0,
				"natAdvRecInvMoney": 0,
				"advRecInvTaxMoney": 0,
				"natAdvRecInvTaxMoney": 0,
				"offsetAdvRecInvMoney": 0,
				"offsetAdvRecInvQty": 0,
				"offsetNatAdvRecInvMoney": 0,
				"offsetAdvRecInvTaxMoney": 0,
				"offsetNatAdvRecInvTaxMoney": 0,
				"tradeRouteID": "",
				"tradeRouteID_code": "",
				"tradeRouteID_name": "",
				"isEndTrade": 0,
				"tradeRouteLineno": "",
				"collaborationPocode": "",
				"collaborationPodetailid": "",
				"collaborationPoid": "",
				"collaborationPorowno": "",
				"collaborationSource": "",
				"entireDeliveryStatus": 0,
				"entireIssueStatus": 0,
				"entireInvoiceStatus": 0,
				"entireSignConfirmStatus": 0,
				"deliveryStatus": 0,
				"issueStatus": 0,
				"invoiceStatus": 0,
				"signConfirmStatus": 0
			}
		],
		"sumRecordList": [
			{
				"noTaxSaleCost": 0,
				"orderRealMoney": 0,
				"realMoney": 0,
				"orderPurchaseQty": 0,
				"sendQty": 0,
				"subQty": 0,
				"collectMoney": 0,
				"sendPriceQty": 0,
				"coordinationQuantity": 0,
				"totalOutStockOriTaxMoney": 0,
				"prodCost": 0,
				"totalOutStockOriMoney": 0,
				"totalOutStockPriceQty": 0,
				"orderPayMoney": 0,
				"payMoneyOrigTaxfree": 0,
				"auditCount": 0,
				"oriSum": 0,
				"returnQty": 0,
				"totalOutStockQuantity": 0,
				"saleCost": 0,
				"cashRebateMoney": 0,
				"rebateMoney": 0,
				"orderRebateMoney": 0,
				"orderDetails_rebateMoney": 0,
				"priceQty": 0,
				"qty": 0,
				"sendPayMoney": 0,
				"cusDiscountMoney": 0,
				"invoiceQty": 0,
				"returnPreSendQty": 0,
				"totalOutStockSubQty": 0,
				"orderDetailPrices": {
					"oriTax": 0,
					"natTax": 0,
					"natSum": 0,
					"natMoney": 0,
					"oriMoney": 0,
					"lineDiscountMoney": 0,
					"totalOutStockConfirmQuantity": 0,
					"totalOutStockConfirmSubQty": 0
				},
				"orderPrices": {
					"totalNatTax": 0
				},
				"advRecInvMoney": 0,
				"advRecInvQty": 0,
				"natAdvRecInvMoney": 0,
				"advRecInvTaxMoney": 0,
				"natAdvRecInvTaxMoney": 0,
				"offsetAdvRecInvMoney": 0,
				"offsetAdvRecInvQty": 0,
				"offsetNatAdvRecInvMoney": 0,
				"offsetAdvRecInvTaxMoney": 0,
				"offsetNatAdvRecInvTaxMoney": 0
			}
		],
		"pageCount": 0,
		"beginPageIndex": 0,
		"endPageIndex": 0
	}
}
6. 错误返回码
错误码	错误信息	描述
999	服务端逻辑异常	请检查传入数据的正确性
7. 错误返回示例
{
	"code": 999,
	"message": "服务端逻辑异常"
}