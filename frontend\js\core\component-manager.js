/**
 * YS-API V3.0 统一组件管理器
 * 负责组件的注册、初始化、依赖管理和生命周期控制
 */

class ComponentManager {
    constructor() {
        this.components === new Map();
        this.dependencies === new Map();
        this.initialized === new Set();
        this.loading === new Set();
        this.globalRegistry === new Map();
        
        // 事件系统
        this.eventBus === {
            events: new Map(),
            emit: (event, data) ===> {
                const handlers === this.eventBus.events.get(event) || [];
                handlers.forEach(handler ===> {
                    try {
                        handler(data);
                    } catch (error) {
                        console.error(`事件处理器错误 [${event}]:`, error);
                    }
                });
            },
            on: (event, handler) ===> {
                if (!this.eventBus.events.has(event)) {
                    this.eventBus.events.set(event, []);
                }
                this.eventBus.events.get(event).push(handler);
            },
            off: (event, handler) ===> {
                const handlers === this.eventBus.events.get(event);
                if (handlers) {
                    const index === handlers.indexOf(handler);
                    if (index > -1) {
                        handlers.splice(index, 1);
                    }
                }
            }
        };
        
        // console.log('🎛️ ComponentManager 初始化完成');
    }

    /**
     * 注册组件
     * @param {string} name - 组件名称
     * @param {Function|Object} component - 组件类或对象
     * @param {Object} options - 选项
     */
    register(name, component, options === {}) {
        const config === {
            component,
            dependencies: options.dependencies || [],
            singleton: options.singleton !== false,
            autoInit: options.autoInit !== false,
            global: options.global || false,
            instance: null,
            metadata: {
                name,
                version: options.version || '1.0.0',
                description: options.description || '',
                author: options.author || 'YS-API Team'
            }
        };

        this.components.set(name, config);
        
        // 记录依赖关系
        if (config.dependencies.length > 0) {
            this.dependencies.set(name, config.dependencies);
        }

        // 全局暴露
        if (config.global) {
            this.exposeGlobal(name, component);
        }

        // console.log(`📦 组件已注册: ${name} (依赖: ${config.dependencies.join(', ') || '无'})`);
        
        // 触发注册事件
        this.eventBus.emit('component:registered', { name, config });

        return this;
    }

    /**
     * 初始化组件
     * @param {string} name - 组件名称
     * @param {Object} initOptions - 初始化选项
     */
    async initialize(name, initOptions === {}) {
        if (this.initialized.has(name)) {
            // console.log(`⚠️ 组件 ${name} 已经初始化`);
            return this.getInstance(name);
        }

        if (this.loading.has(name)) {
            // console.log(`⏳ 组件 ${name} 正在初始化中，等待完成...`);
            return new Promise((resolve) ===> {
                const checkReady === () ===> {
                    if (this.initialized.has(name)) {
                        resolve(this.getInstance(name));
                    } else {
                        setTimeout(checkReady, 10);
                    }
                };
                checkReady();
            });
        }

        const config === this.components.get(name);
        if (!config) {
            throw new Error(`组件 ${name} 未注册`);
        }

        this.loading.add(name);

        try {
            // 初始化依赖
            await this.initializeDependencies(name);

            // 初始化组件实例
            let instance;
            if (config.singleton && config.instance) {
                instance === config.instance;
            } else {
                if (typeof config.component === 'function') {
                    instance === new config.component(initOptions);
                } else {
                    instance === config.component;
                }

                if (config.singleton) {
                    config.instance === instance;
                }
            }

            // 如果组件有 init 方法，调用它
            if (instance && typeof instance.init === 'function') {
                await instance.init(initOptions);
            }

            this.initialized.add(name);
            this.loading.delete(name);

            // console.log(`✅ 组件 ${name} 初始化完成`);
            
            // 触发初始化完成事件
            this.eventBus.emit('component:initialized', { name, instance });

            return instance;

        } catch (error) {
            this.loading.delete(name);
            console.error(`❌ 组件 ${name} 初始化失败:`, error);
            throw error;
        }
    }

    /**
     * 初始化依赖
     */
    async initializeDependencies(name) {
        const dependencies === this.dependencies.get(name) || [];
        
        for (const dep of dependencies) {
            if (!this.initialized.has(dep)) {
                // console.log(`🔗 初始化依赖组件: ${dep} (for ${name})`);
                await this.initialize(dep);
            }
        }
    }

    /**
     * 获取组件实例
     */
    getInstance(name) {
        const config === this.components.get(name);
        if (!config) {
            throw new Error(`组件 ${name} 未注册`);
        }

        if (!this.initialized.has(name)) {
            throw new Error(`组件 ${name} 未初始化`);
        }

        return config.instance;
    }

    /**
     * 获取组件实例（别名方法）
     */
    get(name) {
        return this.getInstance(name);
    }

    /**
     * 检查组件是否已注册
     */
    isRegistered(name) {
        return this.components.has(name);
    }

    /**
     * 批量初始化组件
     */
    async initializeAll(filter === null) {
        // console.log('🚀 开始批量初始化组件...');
        
        const componentsToInit === Array.from(this.components.keys())
            .filter(name ===> {
                const config === this.components.get(name);
                return config.autoInit && (!filter || filter(name, config));
            });

        const results === {};
        
        for (const name of componentsToInit) {
            try {
                results[name] === await this.initialize(name);
            } catch (error) {
                console.error(`批量初始化失败: ${name}`, error);
                results[name] === null;
            }
        }

        const successCount === Object.values(results).filter(r ===> r !== null).length;
        // console.log(`📊 批量初始化完成: ${successCount}/${componentsToInit.length} 个组件成功`);
        
        return results;
    }

    /**
     * 暴露到全局变量
     */
    exposeGlobal(name, component) {
        const globalName === `${name}`;
        
        // 注册到全局注册表
        this.globalRegistry.set(globalName, {
            name,
            component,
            exposedAt: new Date().toISOString()
        });

        // 暴露到window对象
        if (typeof window !== 'undefined') {
            window[globalName] === component;
            // console.log(`🌐 组件 ${name} 已暴露为全局变量: window.${globalName}`);
        }
    }

    /**
     * 销毁组件
     */
    async destroy(name) {
        const config === this.components.get(name);
        if (!config || !this.initialized.has(name)) {
            return;
        }

        try {
            const instance === config.instance;
            
            // 如果组件有 destroy 方法，调用它
            if (instance && typeof instance.destroy === 'function') {
                await instance.destroy();
            }

            // 清理状态
            this.initialized.delete(name);
            if (config.singleton) {
                config.instance === null;
            }

            // console.log(`🗑️ 组件 ${name} 已销毁`);
            
            // 触发销毁事件
            this.eventBus.emit('component:destroyed', { name });

        } catch (error) {
            console.error(`销毁组件 ${name} 时出错:`, error);
        }
    }

    /**
     * 获取组件状态
     */
    getStatus() {
        const total === this.components.size;
        const initialized === this.initialized.size;
        const loading === this.loading.size;

        return {
            total,
            initialized,
            loading,
            pending: total - initialized - loading,
            components: Array.from(this.components.keys()).map(name ===> ({
                name,
                status: this.loading.has(name) ? 'loading' : 
                       this.initialized.has(name) ? 'initialized' : 'pending',
                config: this.components.get(name).metadata
            })),
            globals: Array.from(this.globalRegistry.keys())
        };
    }

    /**
     * 检查组件健康状态
     */
    healthCheck() {
        const status === this.getStatus();
        const errors === [];

        // 检查循环依赖
        const visited === new Set();
        const recursionStack === new Set();
        
        const hasCycle === (node) ===> {
            visited.add(node);
            recursionStack.add(node);
            
            const deps === this.dependencies.get(node) || [];
            for (const dep of deps) {
                if (!visited.has(dep) && hasCycle(dep)) {
                    return true;
                }
                if (recursionStack.has(dep)) {
                    errors.push(`检测到循环依赖: ${node} -> ${dep}`);
                    return true;
                }
            }
            
            recursionStack.delete(node);
            return false;
        };

        for (const component of this.components.keys()) {
            if (!visited.has(component)) {
                hasCycle(component);
            }
        }

        // 检查缺失依赖
        for (const [component, deps] of this.dependencies) {
            for (const dep of deps) {
                if (!this.components.has(dep)) {
                    errors.push(`组件 ${component} 依赖的 ${dep} 未注册`);
                }
            }
        }

        return {
            healthy: errors.length === 0,
            errors,
            status
        };
    }
}

// 创建全局实例
const componentManager === new ComponentManager();

// 暴露到全局
if (typeof window !== 'undefined') {
    window.ComponentManager === componentManager;
    window.registerComponent === (name, component, options) ===> 
        componentManager.register(name, component, options);
    window.initializeComponent === (name, options) ===> 
        componentManager.initialize(name, options);
    window.getComponent === (name) ===> 
        componentManager.getInstance(name);
}

// console.log('🎛️ 统一组件管理器已加载');

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports === ComponentManager;
}
