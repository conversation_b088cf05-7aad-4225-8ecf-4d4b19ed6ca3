#!/usr/bin/env python3
"""
检查代码质量修复状态
"""

import os
import subprocess


def run_flake8_check():
    """运行flake8检查并统计错误"""
    try:
        # 运行flake8检查
        result = subprocess.run(
            [
                "python",
                "-m",
                "flake8",
                ".",
                "--exclude=__pycache__,logs,temp_cleanup,.git",
                "--statistics",
            ],
            capture_output=True,
            text=True,
            encoding="utf-8",
        )

        print("📊 Flake8检查结果:")
        print("-" * 50)

        if result.returncode == 0:
            print("🎉 没有发现任何Flake8错误！")
            return 0
        else:
            # 解析错误统计
            lines = result.stdout.strip().split("\n")
            total_errors = 0

            print("发现的问题类型:")
            for line in lines:
                if line.strip():
                    print(f"  {line}")
                    # 尝试提取错误数量
                    parts = line.split()
                    if len(parts) >= 2 and parts[0].isdigit():
                        total_errors += int(parts[0])

            print(f"\n总错误数: {total_errors}")
            return total_errors

    except Exception as e:
        print(f"❌ 运行flake8失败: {e}")
        return -1


def run_syntax_check():
    """检查所有Python文件的语法"""
    print("\n🔍 Python语法检查:")
    print("-" * 50)

    python_files = []
    for root, dirs, files in os.walk("."):
        # 排除目录
        dirs[:] = [
            d for d in dirs if d not in {
                "__pycache__",
                ".git",
                "logs",
                "temp_cleanup"}]

        for file in files:
            if file.endswith(".py"):
                file_path = os.path.join(root, file)
                python_files.append(file_path)

    syntax_errors = 0
    for file_path in python_files[:10]:  # 检查前10个文件
        try:
            result = subprocess.run(
                ["python", "-m", "py_compile", file_path],
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                print(f"✅ {file_path}")
            else:
                print(f"❌ {file_path}: {result.stderr.strip()}")
                syntax_errors += 1

        except Exception as e:
            print(f"⚠️ {file_path}: 检查失败 - {e}")
            syntax_errors += 1

    if len(python_files) > 10:
        print(f"... 还有 {len(python_files) - 10} 个文件未显示")

    print(f"\n语法错误: {syntax_errors}/{len(python_files)} 文件")
    return syntax_errors


def main():
    """主函数"""
    print("🚀 代码质量状态检查")
    print("=" * 60)

    # Flake8检查
    flake8_errors = run_flake8_check()

    # 语法检查
    syntax_errors = run_syntax_check()

    # 总结
    print("\n📋 检查总结:")
    print("=" * 60)

    if flake8_errors == 0 and syntax_errors == 0:
        print("🎉 所有检查通过！代码质量良好！")
    elif flake8_errors > 0:
        print(f"⚠️ 还有 {flake8_errors} 个代码风格问题需要处理")
    elif syntax_errors > 0:
        print(f"❌ 有 {syntax_errors} 个文件存在语法错误")

    # 提供修复建议
    if flake8_errors > 0:
        print("\n🔧 建议的修复命令:")
        print("1. 自动修复: python -m autopep8 --in-place --aggressive --recursive .")
        print("2. 格式化: python -m black .")
        print(
            "3. 清理导入: python -m autoflake --remove-all-unused-imports --in-place --recursive ."
        )


if __name__ == "__main__":
    main()
