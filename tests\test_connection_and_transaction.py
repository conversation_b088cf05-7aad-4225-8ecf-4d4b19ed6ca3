#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 连接池和事务管理器测试模块
"""

import os
import sys
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.app.services.database_connection_pool import (
    DatabaseConnectionManager, 
    DatabaseConnectionPool,
    ConnectionPoolConfig,
    SQLiteConfig,
    DatabaseType
)
from backend.app.services.transaction_manager import (
    TransactionManager,
    TransactionOperation,
    BatchOperation,
    OperationType,
    TransactionLevel
)

import structlog

# 配置日志
structlog.configure(
    wrapper_class=structlog.make_filtering_bound_logger(20),  # INFO level
    logger_factory=structlog.PrintLoggerFactory(),
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


class ConnectionPoolTest:
    """连接池测试类"""
    
    def __init__(self):
        self.test_db_path = "./test_connection_pool.db"
        self.connection_manager = None
        self.setup_test_environment()
    
    def setup_test_environment(self):
        """设置测试环境"""
        # 清理旧的测试数据库
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
        
        # 创建连接管理器
        self.connection_manager = DatabaseConnectionManager()
        logger.info("测试环境设置完成")
    
    def test_basic_connection(self):
        """测试基本连接功能"""
        logger.info("开始测试基本连接功能...")
        
        try:
            with self.connection_manager.get_connection('sqlite') as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT sqlite_version()")
                version = cursor.fetchone()
                cursor.close()
                
                logger.info("SQLite连接测试成功", version=version[0])
                return True
                
        except Exception as e:
            logger.error("基本连接测试失败", error=str(e))
            return False
    
    def test_concurrent_connections(self, num_threads=10, operations_per_thread=20):
        """测试并发连接"""
        logger.info("开始测试并发连接...", 
                   threads=num_threads, 
                   operations=operations_per_thread)
        
        def worker(worker_id):
            results = []
            for i in range(operations_per_thread):
                try:
                    with self.connection_manager.get_connection('sqlite') as conn:
                        cursor = conn.cursor()
                        cursor.execute("SELECT ? as worker_id, ? as operation", (worker_id, i))
                        result = cursor.fetchone()
                        cursor.close()
                        results.append(result)
                        
                except Exception as e:
                    logger.error("并发连接失败", worker=worker_id, operation=i, error=str(e))
                    results.append(None)
            
            return results
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker, i) for i in range(num_threads)]
            
            success_count = 0
            for future in as_completed(futures):
                result = future.result()
                success_count += len([r for r in result if r is not None])
        
        duration = time.time() - start_time
        total_operations = num_threads * operations_per_thread
        
        logger.info("并发连接测试完成",
                   成功操作=success_count,
                   总操作数=total_operations,
                   成功率=f"{(success_count/total_operations)*100:.2f}%",
                   耗时=f"{duration:.2f}秒")
        
        return success_count == total_operations
    
    def test_connection_pool_stats(self):
        """测试连接池统计信息"""
        logger.info("开始测试连接池统计信息...")
        
        try:
            # 获取初始统计
            initial_stats = self.connection_manager.get_pool_stats()
            logger.info("初始统计信息", stats=initial_stats)
            
            # 执行一些操作
            for _ in range(5):
                with self.connection_manager.get_connection('sqlite') as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    cursor.close()
            
            # 获取更新后的统计
            final_stats = self.connection_manager.get_pool_stats()
            logger.info("最终统计信息", stats=final_stats)
            
            return True
            
        except Exception as e:
            logger.error("连接池统计测试失败", error=str(e))
            return False
    
    def test_connection_health_check(self):
        """测试连接健康检查"""
        logger.info("开始测试连接健康检查...")
        
        try:
            # 获取连接池实例
            pool = self.connection_manager.connection_pools['sqlite']
            
            # 检查健康检查线程是否运行
            health_thread = pool._health_check_thread
            is_alive = health_thread.is_alive() if health_thread else False
            
            logger.info("健康检查线程状态", is_alive=is_alive)
            
            # 等待一次健康检查周期
            time.sleep(2)
            
            stats = pool.get_stats()
            logger.info("健康检查后统计", stats=stats)
            
            return is_alive
            
        except Exception as e:
            logger.error("连接健康检查测试失败", error=str(e))
            return False
    
    def cleanup(self):
        """清理测试环境"""
        if self.connection_manager:
            self.connection_manager.close_all()
        
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
        
        logger.info("测试环境清理完成")


class TransactionManagerTest:
    """事务管理器测试类"""
    
    def __init__(self):
        self.test_db_path = "./test_transaction.db"
        self.connection_manager = None
        self.transaction_manager = None
        self.setup_test_environment()
    
    def setup_test_environment(self):
        """设置测试环境"""
        # 清理旧的测试数据库
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
        
        # 创建管理器
        self.connection_manager = DatabaseConnectionManager()
        self.transaction_manager = TransactionManager(self.connection_manager)
        
        # 创建测试表
        with self.transaction_manager.transaction('sqlite') as conn:
            cursor = conn.cursor()
            cursor.execute("""
                CREATE TABLE test_table (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    value INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            cursor.close()
        
        logger.info("事务测试环境设置完成")
    
    def test_basic_transaction(self):
        """测试基本事务功能"""
        logger.info("开始测试基本事务功能...")
        
        try:
            # 测试成功事务
            with self.transaction_manager.transaction('sqlite') as conn:
                cursor = conn.cursor()
                cursor.execute("INSERT INTO test_table (name, value) VALUES (?, ?)", 
                             ("测试数据", 100))
                cursor.close()
            
            # 验证数据是否插入
            with self.transaction_manager.transaction('sqlite') as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM test_table")
                count = cursor.fetchone()[0]
                cursor.close()
            
            logger.info("基本事务测试成功", inserted_count=count)
            return count == 1
            
        except Exception as e:
            logger.error("基本事务测试失败", error=str(e))
            return False
    
    def test_transaction_rollback(self):
        """测试事务回滚"""
        logger.info("开始测试事务回滚...")
        
        try:
            # 获取初始记录数
            with self.transaction_manager.transaction('sqlite') as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM test_table")
                initial_count = cursor.fetchone()[0]
                cursor.close()
            
            # 测试回滚事务
            try:
                with self.transaction_manager.transaction('sqlite') as conn:
                    cursor = conn.cursor()
                    cursor.execute("INSERT INTO test_table (name, value) VALUES (?, ?)", 
                                 ("回滚测试", 200))
                    # 故意引发错误
                    cursor.execute("INSERT INTO non_existent_table VALUES (1)")
                    cursor.close()
            except Exception:
                pass  # 预期的错误
            
            # 验证数据是否回滚
            with self.transaction_manager.transaction('sqlite') as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM test_table")
                final_count = cursor.fetchone()[0]
                cursor.close()
            
            logger.info("事务回滚测试完成", 
                       initial_count=initial_count,
                       final_count=final_count)
            
            return final_count == initial_count
            
        except Exception as e:
            logger.error("事务回滚测试失败", error=str(e))
            return False
    
    def test_batch_operations(self):
        """测试批量操作"""
        logger.info("开始测试批量操作...")
        
        try:
            # 创建批量操作
            operations = []
            for i in range(100):
                operations.append(
                    TransactionOperation(
                        operation_type=OperationType.INSERT,
                        table_name="test_table",
                        sql="INSERT INTO test_table (name, value) VALUES (?, ?)",
                        params=(f"批量数据{i}", i)
                    )
                )
            
            batch = BatchOperation(operations=operations, batch_size=20)
            
            # 执行批量操作
            result = self.transaction_manager.execute_batch_operations(batch, 'sqlite')
            
            logger.info("批量操作结果", result=result)
            
            # 验证插入的数据
            with self.transaction_manager.transaction('sqlite') as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM test_table WHERE name LIKE '批量数据%'")
                batch_count = cursor.fetchone()[0]
                cursor.close()
            
            logger.info("批量操作验证", 
                       processed=result['processed'],
                       in_database=batch_count)
            
            return result['processed'] > 0 and len(result['errors']) == 0
            
        except Exception as e:
            logger.error("批量操作测试失败", error=str(e))
            return False
    
    def test_concurrent_transactions(self, num_threads=5):
        """测试并发事务"""
        logger.info("开始测试并发事务...", threads=num_threads)
        
        def worker(worker_id):
            try:
                for i in range(10):
                    with self.transaction_manager.transaction('sqlite') as conn:
                        cursor = conn.cursor()
                        cursor.execute(
                            "INSERT INTO test_table (name, value) VALUES (?, ?)",
                            (f"并发{worker_id}_{i}", worker_id * 1000 + i)
                        )
                        cursor.close()
                return True
            except Exception as e:
                logger.error("并发事务失败", worker=worker_id, error=str(e))
                return False
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker, i) for i in range(num_threads)]
            results = [future.result() for future in as_completed(futures)]
        
        duration = time.time() - start_time
        success_count = sum(results)
        
        logger.info("并发事务测试完成",
                   成功线程=success_count,
                   总线程=num_threads,
                   成功率=f"{(success_count/num_threads)*100:.2f}%",
                   耗时=f"{duration:.2f}秒")
        
        return success_count == num_threads
    
    def test_transaction_stats(self):
        """测试事务统计"""
        logger.info("开始测试事务统计...")
        
        try:
            # 获取初始统计
            initial_stats = self.transaction_manager.get_transaction_stats()
            logger.info("初始事务统计", stats=initial_stats)
            
            # 执行一些事务
            for i in range(5):
                with self.transaction_manager.transaction('sqlite') as conn:
                    cursor = conn.cursor()
                    cursor.execute("INSERT INTO test_table (name, value) VALUES (?, ?)", 
                                 (f"统计测试{i}", i))
                    cursor.close()
            
            # 获取最终统计
            final_stats = self.transaction_manager.get_transaction_stats()
            logger.info("最终事务统计", stats=final_stats)
            
            return final_stats['total_transactions'] > initial_stats['total_transactions']
            
        except Exception as e:
            logger.error("事务统计测试失败", error=str(e))
            return False
    
    def cleanup(self):
        """清理测试环境"""
        if self.transaction_manager:
            self.transaction_manager.close()
        
        if self.connection_manager:
            self.connection_manager.close_all()
        
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
        
        logger.info("事务测试环境清理完成")


def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行 YS-API V3.0 连接池和事务管理器测试")
    
    test_results = {
        'connection_pool': {},
        'transaction_manager': {}
    }
    
    # 连接池测试
    logger.info("=" * 50)
    logger.info("连接池测试开始")
    logger.info("=" * 50)
    
    pool_test = ConnectionPoolTest()
    try:
        test_results['connection_pool']['basic_connection'] = pool_test.test_basic_connection()
        test_results['connection_pool']['concurrent_connections'] = pool_test.test_concurrent_connections()
        test_results['connection_pool']['pool_stats'] = pool_test.test_connection_pool_stats()
        test_results['connection_pool']['health_check'] = pool_test.test_connection_health_check()
    finally:
        pool_test.cleanup()
    
    # 事务管理器测试
    logger.info("=" * 50)
    logger.info("事务管理器测试开始")
    logger.info("=" * 50)
    
    txn_test = TransactionManagerTest()
    try:
        test_results['transaction_manager']['basic_transaction'] = txn_test.test_basic_transaction()
        test_results['transaction_manager']['transaction_rollback'] = txn_test.test_transaction_rollback()
        test_results['transaction_manager']['batch_operations'] = txn_test.test_batch_operations()
        test_results['transaction_manager']['concurrent_transactions'] = txn_test.test_concurrent_transactions()
        test_results['transaction_manager']['transaction_stats'] = txn_test.test_transaction_stats()
    finally:
        txn_test.cleanup()
    
    # 打印测试结果
    logger.info("=" * 50)
    logger.info("测试结果汇总")
    logger.info("=" * 50)
    
    for category, tests in test_results.items():
        logger.info(f"{category.upper()} 测试结果:")
        for test_name, result in tests.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"  {test_name}: {status}")
    
    # 计算总体结果
    all_tests = [result for tests in test_results.values() for result in tests.values()]
    passed = sum(all_tests)
    total = len(all_tests)
    
    logger.info(f"测试总结: {passed}/{total} 通过 ({(passed/total)*100:.1f}%)")
    
    return passed == total


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
