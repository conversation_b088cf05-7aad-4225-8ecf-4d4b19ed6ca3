#!/bin/bash
# 测试代码和模拟数据清理脚本
# 生成时间: 2025-08-06T03:35:42.342842

set -e

echo '🧹 开始清理测试代码和模拟数据'

echo '⚠️ 以下文件需要手动审查后决定是否删除:'
echo '  审查: test_day4_proxy.py'
echo '  审查: test_mvp.py'
echo '  审查: scripts\frontend_test_server.py'
echo '  审查: production_test_runner.py'
echo '  审查: test_modules_direct.py'
echo '  审查: backend\test_backend_automation.py'
echo '  审查: scripts\test_server.py'
echo '  审查: tests\test_baseline_api.py'
echo '  审查: tests\test_rollback_scripts.py'
echo '  审查: frontend\js\common\test-data.js'
echo '  审查: scripts\module_functional_test.py'
echo '  审查: frontend\migrated\path-test.html'
echo '  审查: tests\simple_test.py'
echo '  审查: tests\test_md_to_json_converter.py'
echo '  审查: scripts\cleanup_test_code.py'
echo '  审查: test_health_checker.py'
echo '  审查: real_data_full_test.py'
echo '  审查: setup_test_env.py'
echo '  审查: 模块字段\backup\材料出库单列表查询.xml.backup'

echo '✅ 清理完成'