import json
import subprocess
import sys
from pathlib import Path

#!/usr/bin/env python3
"""
下一个模块快速迁移脚本
根据当前进度自动识别并迁移下一个模块
"""


def get_current_progress():
    """获取当前进度"""
    status_file = Path("tasks/module_status.json")

    if not status_file.exists():
        print(
            "❌ 状态文件不存在，请先运行: python scripts/module_tracker_simple.py --init"
        )
        return None

    with open(status_file, "r", encoding="utf-8") as f:
        status = json.load(f)

    return status


def get_next_module(status):
    """获取下一个待迁移的模块"""
    modules = status.get("modules", {})

    # 找到第一个completion_rate为0的模块
    for module_name, module_data in modules.items():
        if module_data.get("completion_rate", 0) == 0:
            return module_name

    return None


def generate_module_migration_script(module_name):
    """为指定模块生成迁移脚本"""
    script_content = f'''#!/usr/bin/env python3
"""
{module_name}模块迁移脚本
自动生成的标准迁移流程
"""


class {module_name.replace(" ", "").replace("列表", "List").replace("查询", "Query")}Migrator:
    """
    {module_name}模块迁移器
    """


    def __init___(self):

    """TODO: Add function description."""
        self.module_name = "{module_name}"
        self.project_root = Path(".")
        self.backup_dir = self.project_root / "graveyard" / "{module_name}"
        self.new_api_dir = self.project_root / "new-system" / "modules" / "{module_name.replace(" ", "_").lower()}"

        # 确保目录存在
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        self.new_api_dir.mkdir(parents=True, exist_ok=True)


    def run_migration(self):
        """运行迁移"""
        print(f"🚀 开始迁移模块: {{self.module_name}}")

        # 步骤1: 分析Legacy代码
        print("🔍 步骤1: 分析Legacy代码...")

        # 步骤2: 创建新API
        print("🔨 步骤2: 创建新API...")

        # 步骤3: 配置代理路由
        print("🔄 步骤3: 配置代理路由...")

        # 步骤4: 运行测试
        print("🧪 步骤4: 运行测试...")

        # 生成报告
        migration_report = {{
            "module": self.module_name,
            "timestamp": datetime.now().isoformat(),
            "status": "migration_started",
            "next_steps": [
                "完成新API实现",
                "配置代理路由",
                "运行迁移测试",
                "验证数据一致性"
            ]
        }}

        report_path = self.backup_dir / "migration_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(migration_report, f, ensure_ascii=False, indent=2)

        print(f"✅ 模块 {{self.module_name}} 迁移初始化完成")
        print(f"📄 报告: {{report_path}}")

        return migration_report

def mainn():

    """TODO: Add function description."""
    migrator =
        {module_name.replace(" ",
        "").replace("列表",
        "List").replace("查询",
        "Query")}Migrator()
    migrator.run_migration()

if __name__ == "__main__":
    main()
'''

    return script_content


def main():
    """主函数"""
    print("🔍 检查当前模块迁移进度...")

    # 获取当前进度
    status = get_current_progress()
    if not status:
        sys.exit(1)

    # 获取下一个模块
    next_module = get_next_module(status)

    if not next_module:
        print("🎉 所有模块迁移已完成！")
        sys.exit(0)

    print(f"📋 下一个待迁移模块: {next_module}")

    # 生成测试用例
    print("📝 生成测试用例...")

    result = subprocess.run(
        [
            "python",
            "tests/module_migration/test_generator.py",
            "--generate",
            next_module,
        ],
        capture_output=True,
        text=True,
    )

    if result.returncode == 0:
        print("✅ 测试用例生成成功")
    else:
        print(f"⚠️ 测试用例生成失败: {result.stderr}")

    # 生成迁移脚本
    print("🔨 生成迁移脚本...")
    script_content = generate_module_migration_script(next_module)

    script_path = Path(
        f"scripts/migrate_{next_module.replace(' ', '_').lower()}.py")
    with open(script_path, "w", encoding="utf-8") as f:
        f.write(script_content)

    print(f"✅ 迁移脚本已生成: {script_path}")

    # 更新模块状态
    print("📊 更新模块状态...")
    result = subprocess.run(
        [
            "python",
            "scripts/module_tracker_simple.py",
            "--update",
            next_module,
            "test_passed",
            "true",
            "--notes",
            "开始迁移，脚本已生成",
        ],
        capture_output=True,
        text=True,
    )

    if result.returncode == 0:
        print("✅ 模块状态更新成功")
    else:
        print(f"⚠️ 状态更新失败: {result.stderr}")

    # 显示下一步操作
    print()
    print("📋 下一步操作:")
    print(f"  1. 运行迁移脚本: python {script_path}")
    print(f"  2. 启动Docker环境: ./start_strangler.sh")
    print(
        f"  3. 测试模块功能: python tests/module_migration/test_{next_module.replace(' ', '_').lower()}_migration.py"
    )
    print(
        f'  4. 更新状态: python scripts/module_tracker_simple.py --update "{next_module}" "mock_data_deleted" true'
    )
    print()
    print(f"🎯 目标: 完成 {next_module} 模块的4个检查点验证")


if __name__ == "__main__":
    main()
