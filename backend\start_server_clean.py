#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 后端服务启动脚本 - 生产环境版本

============================================================
重要端口配置说明：
后端端口: 8050 (生产环境主服务)
前端端口: 8060 (管理界面) - 已标准化
数据源: 真实生产数据，非测试数据
模块数量: 14个完整业务模块(包含业务日志，已移除库存管理相关模块)
============================================================

特性: 端口管理、防错机制、真实数据连接
"""


import logging
import sys
from pathlib import Path

import uvicorn
from port_manager import PortManager

# 添加scripts目录到路径以导入端口管理器
sys.path.append(str(Path(__file__).parent.parent / "scripts"))

# 生产环境端口配置 - 固定端口，禁止改动
SERVER_PORT = 8050  # 固定后端端口 8050，禁止改动
FRONTEND_PORT = 8060  # 固定前端端口 8060，禁止改动

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """主启动函数 - 实现端口管理和防错机制"""
    print("🚀 YS-API V3.0 后端服务启动")
    print(f"端口: {SERVER_PORT}")
    print(f"前端服务端口: {FRONTEND_PORT}")
    print()

    # 端口管理
    port_manager = PortManager()

    # 检查并准备后端端口
    if not port_manager.ensure_port_available(SERVER_PORT):
        logger.error("后端端口准备失败，服务无法启动")
        sys.exit(1)

    # 启动服务
    logger.info("启动后端服务在端口 {SERVER_PORT}...")
    logger.info("健康检查: http://127.0.0.1:{SERVER_PORT}/health")

    try:
        # 动态导入并启动应用
        from app.main import app

        uvicorn.run(
            app,
            host="127.0.0.1",
            port=SERVER_PORT,
            log_level="info",
            access_log=True)
    except (ImportError, OSError, ValueError) as e:
        port_manager.handle_critical_error("服务启动失败", e)


if __name__ == "__main__":
    main()
