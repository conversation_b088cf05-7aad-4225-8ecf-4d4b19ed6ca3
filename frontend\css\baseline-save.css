/* 基准文件保存进度显示样式 */

.save-progress-container {
  margin-top: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(243, 156, 18, 0.2);
  box-shadow: 0 4px 20px rgba(243, 156, 18, 0.1);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(-10px);
}

.save-progress-container.show {
  opacity: 1;
  transform: translateY(0);
  animation: saveProgressSlideIn 0.4s ease-out;
}

.save-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.save-progress-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.save-progress-icon {
  font-size: 18px;
  animation: saveProgressPulse 2s ease-in-out infinite;
}

.save-progress-text {
  transition: color 0.3s ease;
}

.save-progress-percentage {
  font-size: 16px;
  font-weight: 700;
  color: #f39c12;
  background: rgba(243, 156, 18, 0.1);
  padding: 4px 12px;
  border-radius: 20px;
  min-width: 60px;
  text-align: center;
  transition: all 0.3s ease;
}

.save-progress-main {
  margin-bottom: 12px;
}

.save-progress-bar-container {
  width: 100%;
  height: 8px;
  background: rgba(243, 156, 18, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.save-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #f39c12 0%, #e67e22 50%, #f39c12 100%);
  background-size: 200% 100%;
  width: 0%;
  border-radius: 4px;
  position: relative;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  animation: saveProgressShimmer 2s ease-in-out infinite;
}

.save-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
  animation: saveProgressGlow 1.5s ease-in-out infinite;
}

.save-progress-details {
  font-size: 14px;
  color: #7f8c8d;
  line-height: 1.4;
  padding: 8px 12px;
  background: rgba(243, 156, 18, 0.05);
  border-radius: 6px;
  border-left: 3px solid #f39c12;
}

/* 保存完成状态 */
.save-progress-container.completed {
  border-color: #27ae60;
  box-shadow: 0 4px 20px rgba(39, 174, 96, 0.2);
}

.save-progress-container.completed .save-progress-bar {
  background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
}

.save-progress-container.completed .save-progress-percentage {
  color: #27ae60;
  background: rgba(39, 174, 96, 0.1);
}

.save-progress-container.completed .save-progress-details {
  background: rgba(39, 174, 96, 0.05);
  border-left-color: #27ae60;
}

/* 保存错误状态 */
.save-progress-container.error {
  border-color: #e74c3c;
  box-shadow: 0 4px 20px rgba(231, 76, 60, 0.2);
}

.save-progress-container.error .save-progress-bar {
  background: linear-gradient(90deg, #e74c3c 0%, #c0392b 100%);
}

.save-progress-container.error .save-progress-percentage {
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
}

.save-progress-container.error .save-progress-details {
  background: rgba(231, 76, 60, 0.05);
  border-left-color: #e74c3c;
}

/* 动画效果 */
@keyframes saveProgressSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes saveProgressShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes saveProgressGlow {
  0%, 100% {
    opacity: 0;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
    transform: translateX(100%);
  }
}

@keyframes saveProgressPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .save-progress-container {
    padding: 16px;
  }

  .save-progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .save-progress-title {
    font-size: 14px;
  }

  .save-progress-percentage {
    font-size: 14px;
    padding: 3px 10px;
  }

  .save-progress-details {
    font-size: 13px;
    padding: 6px 10px;
  }
}

/* 保存按钮状态样式 */
.save-button.saving {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%) !important;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.save-button.saving:hover {
  transform: none !important;
  box-shadow: none !important;
}

.save-button.save-success {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%) !important;
  animation: saveButtonSuccess 0.6s ease-out;
}

.save-button.save-error {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
  animation: saveButtonError 0.6s ease-out;
}

@keyframes saveButtonSuccess {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes saveButtonError {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}