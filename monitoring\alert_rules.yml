# Prometheus告警规则 - 屎山代码绞杀监控
groups:
  - name: strangler-fig-alerts
    rules:
      # 代理层告警
      - alert: StranglerProxyDown
        expr: up{job="strangler-proxy"} == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "绞杀者代理层宕机"
          description: "代理层已宕机超过30秒，流量无法路由"

      - alert: HighTrafficSwitchRate
        expr: rate(strangler_traffic_switch_total[1m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "流量切换过于频繁"
          description: "流量切换速率: {{ $value }}/秒，可能存在抖动"

      # Legacy系统告警
      - alert: LegacyApiDown
        expr: up{job="legacy-api"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Legacy API服务宕机"
          description: "屎山代码系统已宕机，需要紧急恢复"

      - alert: LegacyHighErrorRate
        expr: rate(flask_http_request_exceptions_total{job="legacy-api"}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Legacy系统错误率过高"
          description: "错误率: {{ $value }}/秒，屎山代码出现异常"

      # 新系统告警
      - alert: NewApiDown
        expr: up{job="new-api"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "新API服务宕机"
          description: "重构后的新系统已宕机"

      - alert: DataInconsistency
        expr: abs(legacy_db_records_total - new_db_records_total) > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "数据不一致检测"
          description: "Legacy和新系统数据差异: {{ $value }}条记录"

      # 数据库告警
      - alert: PostgresDown
        expr: up{job="postgres"} == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL数据库宕机"
          description: "新系统数据库不可用"

      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 30s
        labels:
          severity: warning
        annotations:
          summary: "Redis缓存宕机"
          description: "缓存服务不可用，性能可能受影响"

      # 性能告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(flask_request_duration_seconds_bucket[5m])) > 2
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "响应时间过长"
          description: "95%请求响应时间: {{ $value }}秒"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "内存使用率: {{ $value | humanizePercentage }}"

      # 绞杀进度告警
      - alert: MigrationStalled
        expr: increase(strangler_modules_migrated_total[24h]) == 0
        for: 24h
        labels:
          severity: info
        annotations:
          summary: "模块迁移停滞"
          description: "24小时内没有新模块完成迁移"

      - alert: RollbackDetected
        expr: increase(strangler_rollback_total[1h]) > 0
        for: 0s
        labels:
          severity: warning
        annotations:
          summary: "检测到回滚操作"
          description: "系统执行了回滚操作，请检查迁移状态"
