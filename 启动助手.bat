@echo off
chcp 65001 >nul
title YS-API V3.0 启动助手

echo.
echo ===============================================
echo 🚀 YS-API V3.0 启动助手 - 端口标准化版本
echo ===============================================
echo.
echo 📖 启动说明:
echo   后端服务: 端口8050 (固定，禁止改动)
echo   前端服务: 端口8060 (固定，禁止改动)
echo.
echo ⚠️  注意: 需要手动分别启动后端和前端服务
echo.

:MENU
echo 请选择操作:
echo [1] 启动后端服务 (端口8050)
echo [2] 启动前端服务 (端口8060) 
echo [3] 验证启动配置
echo [4] 查看服务地址
echo [0] 退出
echo.
set /p choice="请输入选择 (0-4): "

if "%choice%"=="1" goto START_BACKEND
if "%choice%"=="2" goto START_FRONTEND
if "%choice%"=="3" goto VERIFY
if "%choice%"=="4" goto SHOW_URLS
if "%choice%"=="0" goto EXIT
echo 无效选择，请重新输入
goto MENU

:START_BACKEND
echo.
echo 🔧 启动后端服务 (端口8050)...
echo 命令: python backend/start_server_fixed.py
echo.
python backend/start_server_fixed.py
pause
goto MENU

:START_FRONTEND
echo.
echo 🌐 启动前端服务 (端口8060)...
echo 命令: python frontend/start_frontend_fixed.py
echo.
python frontend/start_frontend_fixed.py
pause
goto MENU

:VERIFY
echo.
echo 🔍 验证启动配置...
python verify_startup.py
pause
goto MENU

:SHOW_URLS
echo.
echo 🌐 服务地址 (端口标准化):
echo   后端服务: http://localhost:8050
echo   前端界面: http://localhost:8060
echo   API文档:  http://localhost:8050/docs
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 谢谢使用 YS-API V3.0 启动助手
echo.
pause
exit
