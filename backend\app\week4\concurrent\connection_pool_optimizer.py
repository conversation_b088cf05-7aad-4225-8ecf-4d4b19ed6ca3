import asyncio
import sqlite3
import threading
import time
from collections import deque
from enum import Enum

import pymssql
import structlog

"""
YS-API V3.0 连接池优化器
Month 3 Week 4: 高性能数据库连接池管理和优化
"""


logger = structlog.get_logger()


class ConnectionType(Enum):
    """连接类型"""
    SQLITE = "sqlite"
    MSSQL = "mssql"
    API = "api"


class ConnectionStatus(Enum):
    """连接状态"""
    AVAILABLE = "available"
    IN_USE = "in_use"
    BROKEN = "broken"
    EXPIRED = "expired"


@dataclass
class ConnectionInfo:
    """连接信息"""
    connection_id: str
    connection_type: ConnectionType
    connection: Any
    created_at: datetime = field(default_factory=datetime.now)
    last_used_at: datetime = field(default_factory=datetime.now)
    use_count: int = 0
    status: ConnectionStatus = ConnectionStatus.AVAILABLE
    max_uses: int = 1000
    max_idle_time: float = 3600.0  # 1小时
    health_check_count: int = 0
    error_count: int = 0


@dataclass
class PoolStats:
    """连接池统计"""
    total_connections: int = 0
    available_connections: int = 0
    in_use_connections: int = 0
    broken_connections: int = 0
    peak_connections: int = 0
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_wait_time: float = 0.0
    average_connection_lifetime: float = 0.0


class ConnectionPoolOptimizer:
    """连接池优化器"""

    def __init___(
        self,
        min_size: int = 5,
        max_size: int = 20,
        acquire_timeout: float = 30.0,
        max_idle_time: float = 3600.0,
        health_check_interval: float = 300.0,
        connection_max_uses: int = 1000
    ):
    """TODO: Add function description."""
    self.min_size = min_size
    self.max_size = max_size
    self.acquire_timeout = acquire_timeout
    self.max_idle_time = max_idle_time
    self.health_check_interval = health_check_interval
    self.connection_max_uses = connection_max_uses

    # 连接池存储
    self.pools: Dict[str, deque] = {}
    self.connections: Dict[str, ConnectionInfo] = {}
    self.pool_configs: Dict[str, Dict[str, Any]] = {}

    # 控制信号
    self.semaphores: Dict[str, asyncio.Semaphore] = {}
    self.locks: Dict[str, threading.Lock] = {}

    # 统计信息
    self.stats: Dict[str, PoolStats] = {}

    # 监控和维护
    self.is_running = False
    self.maintenance_task: Optional[asyncio.Task] = None
    self.health_check_task: Optional[asyncio.Task] = None

    # 事件回调
    self.event_callbacks = {
        'connection_created': [],
        'connection_acquired': [],
        'connection_released': [],
        'connection_failed': [],
        'pool_exhausted': [],
        'health_check_failed': []
    }

    logger.info(
        "连接池优化器初始化完成",
        min_size=min_size,
        max_size=max_size,
        acquire_timeout=acquire_timeout
    )

    async def start(self):
        """启动连接池优化器"""
        if self.is_running:
            logger.warning("连接池优化器已在运行")
            return

        self.is_running = True

        # 启动维护任务
        self.maintenance_task = asyncio.create_task(self._maintenance_loop())

        # 启动健康检查任务
        self.health_check_task = asyncio.create_task(self._health_check_loop())

        logger.info("连接池优化器已启动")

    async def stop(self):
        """停止连接池优化器"""
        if not self.is_running:
            return

        logger.info("正在停止连接池优化器...")

        self.is_running = False

        # 停止维护任务
        if self.maintenance_task:
            self.maintenance_task.cancel()
        if self.health_check_task:
            self.health_check_task.cancel()

        # 关闭所有连接
        for pool_name in list(self.pools.keys()):
            await self.close_pool(pool_name)

        logger.info("连接池优化器已停止")

    async def create_pool(
        self,
        pool_name: str,
        connection_type: ConnectionType,
        connection_config: Dict[str, Any],
        min_size: Optional[int] = None,
        max_size: Optional[int] = None
    ):
        """创建连接池"""
        if pool_name in self.pools:
            logger.warning("连接池已存在", pool_name=pool_name)
            return

        pool_min_size = min_size or self.min_size
        pool_max_size = max_size or self.max_size

        # 初始化连接池
        self.pools[pool_name] = deque()
        self.pool_configs[pool_name] = {
            'connection_type': connection_type,
            'connection_config': connection_config,
            'min_size': pool_min_size,
            'max_size': pool_max_size
        }
        self.semaphores[pool_name] = asyncio.Semaphore(pool_max_size)
        self.locks[pool_name] = threading.Lock()
        self.stats[pool_name] = PoolStats()

        # 创建最小数量的连接
        for _ in range(pool_min_size):
            try:
                connection_info = await self._create_connection(pool_name)
                self.pools[pool_name].append(connection_info.connection_id)

            except Exception:
                logger.error(
                    "创建初始连接失败",
                    pool_name=pool_name,
                    error=str(e)
                )

        logger.info(
            "连接池已创建",
            pool_name=pool_name,
            connection_type=connection_type.value,
            min_size=pool_min_size,
            max_size=pool_max_size,
            initial_connections=len(self.pools[pool_name])
        )

    async def close_pool(self, pool_name: str):
        """关闭连接池"""
        if pool_name not in self.pools:
            logger.warning("连接池不存在", pool_name=pool_name)
            return

        pool = self.pools[pool_name]

        # 关闭所有连接
        while pool:
            connection_id = pool.popleft()
            connection_info = self.connections.get(connection_id)
            if connection_info:
                await self._close_connection(connection_info)
                del self.connections[connection_id]

        # 清理资源
        del self.pools[pool_name]
        del self.pool_configs[pool_name]
        del self.semaphores[pool_name]
        del self.locks[pool_name]
        del self.stats[pool_name]

        logger.info("连接池已关闭", pool_name=pool_name)

    @asynccontextmanager
    async def acquire_connection(self, pool_name: str):
        """获取连接（异步上下文管理器）"""
        if pool_name not in self.pools:
            raise ValueError(f"连接池不存在: {pool_name}")

        start_time = time.time()
        connection_info = None

        try:
            # 获取信号量
            async with self.semaphores[pool_name]:
                connection_info = await self._get_connection(pool_name)

                # 更新统计
                wait_time = time.time() - start_time
                self._update_wait_time_stats(pool_name, wait_time)
                self.stats[pool_name].total_requests += 1

                # 触发获取事件
                await self._trigger_event('connection_acquired', {
                    'pool_name': pool_name,
                    'connection_id': connection_info.connection_id,
                    'wait_time': wait_time
                })

                yield connection_info.connection

        except Exception:
            self.stats[pool_name].failed_requests += 1
            await self._trigger_event('connection_failed', {
                'pool_name': pool_name,
                'error': str(e)
            })
            raise

        finally:
            if connection_info:
                await self._release_connection(pool_name, connection_info)

    @contextmanager
    def acquire_connection_sync(self, pool_name: str):
        """获取连接（同步上下文管理器）"""
        if pool_name not in self.pools:
            raise ValueError(f"连接池不存在: {pool_name}")

        start_time = time.time()
        connection_info = None

        try:
            connection_info = self._get_connection_sync(pool_name)

            # 更新统计
            wait_time = time.time() - start_time
            self._update_wait_time_stats(pool_name, wait_time)
            self.stats[pool_name].total_requests += 1

            yield connection_info.connection

        except Exception:
            self.stats[pool_name].failed_requests += 1
            raise

        finally:
            if connection_info:
                self._release_connection_sync(pool_name, connection_info)

    async def _get_connection(self, pool_name: str) -> ConnectionInfo:
        """获取连接（异步）"""
        pool = self.pools[pool_name]

        # 尝试从池中获取可用连接
        while pool:
            connection_id = pool.popleft()
            connection_info = self.connections.get(connection_id)

            if connection_info and await self._is_connection_healthy(connection_info):
                connection_info.status = ConnectionStatus.IN_USE
                connection_info.last_used_at = datetime.now()
                connection_info.use_count += 1

                self._update_pool_stats(pool_name)
                return connection_info

            # 连接不健康，移除并关闭
            if connection_info:
                await self._close_connection(connection_info)
                del self.connections[connection_id]

        # 池中没有可用连接，创建新连接
        return await self._create_connection(pool_name)

    def _get_connection_sync(self, pool_name: str) -> ConnectionInfo:
        """获取连接（同步）"""
        with self.locks[pool_name]:
            pool = self.pools[pool_name]

            # 尝试从池中获取可用连接
            while pool:
                connection_id = pool.popleft()
                connection_info = self.connections.get(connection_id)

                if connection_info and self._is_connection_healthy_sync(
                        connection_info):
                    connection_info.status = ConnectionStatus.IN_USE
                    connection_info.last_used_at = datetime.now()
                    connection_info.use_count += 1

                    self._update_pool_stats(pool_name)
                    return connection_info

                # 连接不健康，移除并关闭
                if connection_info:
                    self._close_connection_sync(connection_info)
                    del self.connections[connection_id]

            # 池中没有可用连接，创建新连接
            return self._create_connection_sync(pool_name)

    async def _release_connection(
            self,
            pool_name: str,
            connection_info: ConnectionInfo):
        """释放连接（异步）"""
        # 检查连接是否仍然健康
        if await self._is_connection_healthy(connection_info):
            connection_info.status = ConnectionStatus.AVAILABLE

            # 检查是否超过最大使用次数
            if connection_info.use_count >= connection_info.max_uses:
                await self._close_connection(connection_info)
                del self.connections[connection_info.connection_id]
            else:
                # 放回连接池
                self.pools[pool_name].append(connection_info.connection_id)
        else:
            # 连接不健康，关闭
            await self._close_connection(connection_info)
            del self.connections[connection_info.connection_id]

        self._update_pool_stats(pool_name)
        self.stats[pool_name].successful_requests += 1

        # 触发释放事件
        await self._trigger_event('connection_released', {
            'pool_name': pool_name,
            'connection_id': connection_info.connection_id
        })

    def _release_connection_sync(
            self,
            pool_name: str,
            connection_info: ConnectionInfo):
        """释放连接（同步）"""
        with self.locks[pool_name]:
            # 检查连接是否仍然健康
            if self._is_connection_healthy_sync(connection_info):
                connection_info.status = ConnectionStatus.AVAILABLE

                # 检查是否超过最大使用次数
                if connection_info.use_count >= connection_info.max_uses:
                    self._close_connection_sync(connection_info)
                    del self.connections[connection_info.connection_id]
                else:
                    # 放回连接池
                    self.pools[pool_name].append(connection_info.connection_id)
            else:
                # 连接不健康，关闭
                self._close_connection_sync(connection_info)
                del self.connections[connection_info.connection_id]

            self._update_pool_stats(pool_name)
            self.stats[pool_name].successful_requests += 1

    async def _create_connection(self, pool_name: str) -> ConnectionInfo:
        """创建新连接（异步）"""
        config = self.pool_configs[pool_name]
        connection_type = config['connection_type']
        connection_config = config['connection_config']

        connection_id = f"{pool_name}_{int(time.time() * 1000000)}"

        try:
            # 根据连接类型创建连接
            if connection_type == ConnectionType.SQLITE:
                connection = await self._create_sqlite_connection(connection_config)
            elif connection_type == ConnectionType.MSSQL:
                connection = await self._create_mssql_connection(connection_config)
            else:
                raise ValueError(f"不支持的连接类型: {connection_type}")

            connection_info = ConnectionInfo(
                connection_id=connection_id,
                connection_type=connection_type,
                connection=connection,
                max_uses=self.connection_max_uses,
                max_idle_time=self.max_idle_time
            )

            self.connections[connection_id] = connection_info
            self._update_pool_stats(pool_name)

            # 触发创建事件
            await self._trigger_event('connection_created', {
                'pool_name': pool_name,
                'connection_id': connection_id,
                'connection_type': connection_type.value
            })

            logger.debug(
                "连接已创建",
                pool_name=pool_name,
                connection_id=connection_id,
                connection_type=connection_type.value
            )

            return connection_info

        except Exception:
            logger.error(
                "创建连接失败",
                pool_name=pool_name,
                connection_type=connection_type.value,
                error=str(e)
            )
            raise

    def _create_connection_sync(self, pool_name: str) -> ConnectionInfo:
        """创建新连接（同步）"""
        config = self.pool_configs[pool_name]
        connection_type = config['connection_type']
        connection_config = config['connection_config']

        connection_id = f"{pool_name}_{int(time.time() * 1000000)}"

        try:
            # 根据连接类型创建连接
            if connection_type == ConnectionType.SQLITE:
                connection = self._create_sqlite_connection_sync(
                    connection_config)
            elif connection_type == ConnectionType.MSSQL:
                connection = self._create_mssql_connection_sync(
                    connection_config)
            else:
                raise ValueError(f"不支持的连接类型: {connection_type}")

            connection_info = ConnectionInfo(
                connection_id=connection_id,
                connection_type=connection_type,
                connection=connection,
                max_uses=self.connection_max_uses,
                max_idle_time=self.max_idle_time
            )

            self.connections[connection_id] = connection_info
            self._update_pool_stats(pool_name)

            logger.debug(
                "连接已创建（同步）",
                pool_name=pool_name,
                connection_id=connection_id,
                connection_type=connection_type.value
            )

            return connection_info

        except Exception:
            logger.error(
                "创建连接失败（同步）",
                pool_name=pool_name,
                connection_type=connection_type.value,
                error=str(e)
            )
            raise

    async def _create_sqlite_connection(self, config: Dict[str, Any]):
        """创建SQLite连接"""
        database_path = config.get('database', ':memory:')
        return sqlite3.connect(database_path, check_same_thread=False)

    def _create_sqlite_connection_sync(self, config: Dict[str, Any]):
        """创建SQLite连接（同步）"""
        database_path = config.get('database', ':memory:')
        return sqlite3.connect(database_path, check_same_thread=False)

    async def _create_mssql_connection(self, config: Dict[str, Any]):
        """创建MSSQL连接"""
        return pymssql.connect(
            server=config.get('server'),
            user=config.get('user'),
            password=config.get('password'),
            database=config.get('database'),
            timeout=config.get('timeout', 30),
            charset=config.get('charset', 'utf8')
        )

    def _create_mssql_connection_sync(self, config: Dict[str, Any]):
        """创建MSSQL连接（同步）"""
        return pymssql.connect(
            server=config.get('server'),
            user=config.get('user'),
            password=config.get('password'),
            database=config.get('database'),
            timeout=config.get('timeout', 30),
            charset=config.get('charset', 'utf8')
        )

    async def _is_connection_healthy(
            self, connection_info: ConnectionInfo) -> bool:
        """检查连接健康状态（异步）"""
        try:
            # 检查连接状态
            if connection_info.status == ConnectionStatus.BROKEN:
                return False

            # 检查空闲时间
            idle_time = (
                datetime.now() -
                connection_info.last_used_at).total_seconds()
            if idle_time > connection_info.max_idle_time:
                connection_info.status = ConnectionStatus.EXPIRED
                return False

            # 执行健康检查
            if connection_info.connection_type == ConnectionType.SQLITE:
                cursor = connection_info.connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
            elif connection_info.connection_type == ConnectionType.MSSQL:
                cursor = connection_info.connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()

            connection_info.health_check_count += 1
            return True

        except Exception:
            connection_info.status = ConnectionStatus.BROKEN
            connection_info.error_count += 1
            logger.warning(
                "连接健康检查失败",
                connection_id=connection_info.connection_id,
                error=str(e)
            )
            return False

    def _is_connection_healthy_sync(
            self, connection_info: ConnectionInfo) -> bool:
        """检查连接健康状态（同步）"""
        try:
            # 检查连接状态
            if connection_info.status == ConnectionStatus.BROKEN:
                return False

            # 检查空闲时间
            idle_time = (
                datetime.now() -
                connection_info.last_used_at).total_seconds()
            if idle_time > connection_info.max_idle_time:
                connection_info.status = ConnectionStatus.EXPIRED
                return False

            # 执行健康检查
            if connection_info.connection_type == ConnectionType.SQLITE:
                cursor = connection_info.connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
            elif connection_info.connection_type == ConnectionType.MSSQL:
                cursor = connection_info.connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()

            connection_info.health_check_count += 1
            return True

        except Exception:
            connection_info.status = ConnectionStatus.BROKEN
            connection_info.error_count += 1
            logger.warning(
                "连接健康检查失败（同步）",
                connection_id=connection_info.connection_id,
                error=str(e)
            )
            return False

    async def _close_connection(self, connection_info: ConnectionInfo):
        """关闭连接（异步）"""
        try:
            connection_info.connection.close()

            # 计算连接生命周期
            lifetime = (
                datetime.now() -
                connection_info.created_at).total_seconds()
            pool_name = connection_info.connection_id.split('_')[0]
            self._update_connection_lifetime_stats(pool_name, lifetime)

            logger.debug(
                "连接已关闭",
                connection_id=connection_info.connection_id,
                lifetime=lifetime,
                use_count=connection_info.use_count
            )

        except Exception:
            logger.error(
                "关闭连接异常",
                connection_id=connection_info.connection_id,
                error=str(e)
            )

    def _close_connection_sync(self, connection_info: ConnectionInfo):
        """关闭连接（同步）"""
        try:
            connection_info.connection.close()

            # 计算连接生命周期
            lifetime = (
                datetime.now() -
                connection_info.created_at).total_seconds()
            pool_name = connection_info.connection_id.split('_')[0]
            self._update_connection_lifetime_stats(pool_name, lifetime)

            logger.debug(
                "连接已关闭（同步）",
                connection_id=connection_info.connection_id,
                lifetime=lifetime,
                use_count=connection_info.use_count
            )

        except Exception:
            logger.error(
                "关闭连接异常（同步）",
                connection_id=connection_info.connection_id,
                error=str(e)
            )

    def _update_pool_stats(self, pool_name: str):
        """更新连接池统计"""
        stats = self.stats[pool_name]
        pool = self.pools[pool_name]

        # 统计各状态连接数
        total_connections = 0
        available_connections = len(pool)
        in_use_connections = 0
        broken_connections = 0

        for connection_id in self.connections:
            if connection_id.startswith(pool_name):
                connection_info = self.connections[connection_id]
                total_connections += 1

                if connection_info.status == ConnectionStatus.IN_USE:
                    in_use_connections += 1
                elif connection_info.status == ConnectionStatus.BROKEN:
                    broken_connections += 1

        stats.total_connections = total_connections
        stats.available_connections = available_connections
        stats.in_use_connections = in_use_connections
        stats.broken_connections = broken_connections

        # 更新峰值
        if total_connections > stats.peak_connections:
            stats.peak_connections = total_connections

    def _update_wait_time_stats(self, pool_name: str, wait_time: float):
        """更新等待时间统计"""
        stats = self.stats[pool_name]

        # 简单移动平均
        if stats.average_wait_time == 0:
            stats.average_wait_time = wait_time
        else:
            stats.average_wait_time = (
                stats.average_wait_time * 0.9) + (wait_time * 0.1)

    def _update_connection_lifetime_stats(
            self, pool_name: str, lifetime: float):
        """更新连接生命周期统计"""
        stats = self.stats[pool_name]

        # 简单移动平均
        if stats.average_connection_lifetime == 0:
            stats.average_connection_lifetime = lifetime
        else:
            stats.average_connection_lifetime = (
                stats.average_connection_lifetime * 0.9) + (lifetime * 0.1)

    async def _maintenance_loop(self):
        """维护循环"""
        logger.info("连接池维护任务已启动")

        while self.is_running:
            try:
                await asyncio.sleep(60)  # 每分钟执行一次维护
                await self._perform_maintenance()

            except Exception:
                logger.error("连接池维护异常", error=str(e))

        logger.info("连接池维护任务已停止")

    async def _health_check_loop(self):
        """健康检查循环"""
        logger.info("连接池健康检查任务已启动")

        while self.is_running:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_check()

            except Exception:
                logger.error("连接池健康检查异常", error=str(e))

        logger.info("连接池健康检查任务已停止")

    async def _perform_maintenance(self):
        """执行维护任务"""
        for pool_name in self.pools:
            await self._maintain_pool(pool_name)

    async def _maintain_pool(self, pool_name: str):
        """维护单个连接池"""
        config = self.pool_configs[pool_name]
        min_size = config['min_size']
        max_size = config['max_size']

        # 清理过期和损坏的连接
        pool = self.pools[pool_name]
        connections_to_remove = []

        for connection_id in list(pool):
            connection_info = self.connections.get(connection_id)
            if connection_info:
                # 检查是否过期
                idle_time = (datetime.now() -
                             connection_info.last_used_at).total_seconds()
                if (idle_time > connection_info.max_idle_time or
                    connection_info.status == ConnectionStatus.BROKEN or
                        connection_info.use_count >= connection_info.max_uses):

                    connections_to_remove.append(connection_id)

        # 移除过期连接
        for connection_id in connections_to_remove:
            pool.remove(connection_id)
            connection_info = self.connections.get(connection_id)
            if connection_info:
                await self._close_connection(connection_info)
                del self.connections[connection_id]

        # 确保最小连接数
        current_size = len(pool)
        if current_size < min_size:
            needed_connections = min_size - current_size
            for _ in range(needed_connections):
                try:
                    connection_info = await self._create_connection(pool_name)
                    pool.append(connection_info.connection_id)
                except Exception:
                    logger.error(
                        "维护时创建连接失败",
                        pool_name=pool_name,
                        error=str(e)
                    )
                    break

        self._update_pool_stats(pool_name)

        if connections_to_remove:
            logger.debug(
                "连接池维护完成",
                pool_name=pool_name,
                removed_connections=len(connections_to_remove),
                current_size=len(pool)
            )

    async def _perform_health_check(self):
        """执行健康检查"""
        for pool_name in self.pools:
            await self._health_check_pool(pool_name)

    async def _health_check_pool(self, pool_name: str):
        """健康检查单个连接池"""
        pool = self.pools[pool_name]
        unhealthy_connections = []

        for connection_id in list(pool):
            connection_info = self.connections.get(connection_id)
            if connection_info and connection_info.status == ConnectionStatus.AVAILABLE:
                if not await self._is_connection_healthy(connection_info):
                    unhealthy_connections.append(connection_id)

        # 移除不健康的连接
        for connection_id in unhealthy_connections:
            pool.remove(connection_id)
            connection_info = self.connections.get(connection_id)
            if connection_info:
                await self._close_connection(connection_info)
                del self.connections[connection_id]

                # 触发健康检查失败事件
                await self._trigger_event('health_check_failed', {
                    'pool_name': pool_name,
                    'connection_id': connection_id
                })

        if unhealthy_connections:
            logger.warning(
                "健康检查发现不健康连接",
                pool_name=pool_name,
                unhealthy_count=len(unhealthy_connections)
            )

    def get_pool_statistics(self, pool_name: str) -> Optional[Dict[str, Any]]:
        """获取连接池统计信息"""
        if pool_name not in self.stats:
            return None

        stats = self.stats[pool_name]
        config = self.pool_configs[pool_name]

        success_rate = (
            stats.successful_requests / max(1, stats.total_requests)
        )

        utilization = (
            stats.in_use_connections / max(1, config['max_size'])
        )

        return {
            'pool_name': pool_name,
            'connection_type': config['connection_type'].value,
            'min_size': config['min_size'],
            'max_size': config['max_size'],
            'total_connections': stats.total_connections,
            'available_connections': stats.available_connections,
            'in_use_connections': stats.in_use_connections,
            'broken_connections': stats.broken_connections,
            'peak_connections': stats.peak_connections,
            'total_requests': stats.total_requests,
            'successful_requests': stats.successful_requests,
            'failed_requests': stats.failed_requests,
            'success_rate': success_rate,
            'utilization': utilization,
            'average_wait_time': stats.average_wait_time,
            'average_connection_lifetime': stats.average_connection_lifetime
        }

    def get_all_statistics(self) -> Dict[str, Any]:
        """获取所有连接池统计信息"""
        all_stats = {}
        total_stats = {
            'total_pools': len(self.pools),
            'total_connections': 0,
            'total_requests': 0,
            'total_successful_requests': 0,
            'overall_success_rate': 0.0
        }

        for pool_name in self.pools:
            pool_stats = self.get_pool_statistics(pool_name)
            if pool_stats:
                all_stats[pool_name] = pool_stats
                total_stats['total_connections'] += pool_stats['total_connections']
                total_stats['total_requests'] += pool_stats['total_requests']
                total_stats['total_successful_requests'] += pool_stats['successful_requests']

        if total_stats['total_requests'] > 0:
            total_stats['overall_success_rate'] = (
                total_stats['total_successful_requests'] /
                total_stats['total_requests'])

        return {
            'summary': total_stats,
            'pools': all_stats
        }

    def add_event_callback(self, event_type: str, callback: Callable):
        """添加事件回调"""
        if event_type in self.event_callbacks:
            self.event_callbacks[event_type].append(callback)

    async def _trigger_event(self, event_type: str, data: Any):
        """触发事件回调"""
        callbacks = self.event_callbacks.get(event_type, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(data)
                else:
                    callback(data)
            except Exception:
                logger.error(
                    "事件回调异常",
                    event_type=event_type,
                    error=str(e)
                )


def create_connection_pool_optimizer(
    min_size: int = 5,
    max_size: int = 20,
    acquire_timeout: float = 30.0,
    max_idle_time: float = 3600.0
) -> ConnectionPoolOptimizer:
    """创建连接池优化器实例"""
    return ConnectionPoolOptimizer(
        min_size=min_size,
        max_size=max_size,
        acquire_timeout=acquire_timeout,
        max_idle_time=max_idle_time
    )
