{"start_time": "2025-08-05T21:27:42.455661", "tests": {"standard_request_builder": {"component": "StandardRequestBuilder", "start_time": "2025-08-05T21:27:42.455661", "tests": {"template_availability": {"total_modules": 15, "available_templates": 0, "missing_templates": ["material_outbound", "purchase_order", "purchase_inbound", "product_inbound", "purchase_request", "production_order", "subcontract_order", "subcontract_inbound", "subcontract_request", "material_master", "inventory_report", "sales_outbound", "sales_order", "demand_plan", "business_log"], "template_details": {"material_outbound": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "purchase_order": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "purchase_inbound": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "product_inbound": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "purchase_request": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "production_order": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "subcontract_order": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "subcontract_inbound": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "subcontract_request": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "material_master": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "inventory_report": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "sales_outbound": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "sales_order": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "demand_plan": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}, "business_log": {"available": false, "error": "'StandardRequestBuilder' object has no attribute 'get_module_template'"}}, "coverage_rate": 0.0, "passed": false}, "parameter_validation": {"validation_tests": 5, "passed_validations": 2, "test_cases": {"case_1": {"module": "material_outbound", "params": {"pageSize": 10, "pageNum": 1}, "expected_pass": true, "actual_pass": false, "passed": false, "error": "StandardRequestBuilder.build_request() takes 2 positional arguments but 3 were given"}, "case_2": {"module": "material_outbound", "params": {"pageSize": 0, "pageNum": 1}, "expected_pass": false, "actual_pass": false, "passed": true, "error": "StandardRequestBuilder.build_request() takes 2 positional arguments but 3 were given"}, "case_3": {"module": "purchase_order", "params": {"pageSize": 50, "pageNum": 2, "startDate": "2024-01-01"}, "expected_pass": true, "actual_pass": false, "passed": false, "error": "StandardRequestBuilder.build_request() takes 2 positional arguments but 3 were given"}, "case_4": {"module": "inventory_report", "params": {"materialCode": "TEST001"}, "expected_pass": true, "actual_pass": false, "passed": false, "error": "StandardRequestBuilder.build_request() takes 2 positional arguments but 3 were given"}, "case_5": {"module": "invalid_module", "params": {"pageSize": 10}, "expected_pass": false, "actual_pass": false, "passed": true, "error": "StandardRequestBuilder.build_request() takes 2 positional arguments but 3 were given"}}, "accuracy_rate": 0.4, "passed": false}, "request_building": {"build_tests": 3, "successful_builds": 0, "build_details": {"material_outbound": {"success": false, "error": "StandardRequestBuilder.build_request() takes 2 positional arguments but 3 were given"}, "purchase_order": {"success": false, "error": "StandardRequestBuilder.build_request() takes 2 positional arguments but 3 were given"}, "inventory_report": {"success": false, "error": "StandardRequestBuilder.build_request() takes 2 positional arguments but 3 were given"}}, "success_rate": 0.0, "passed": false}}, "success_rate": 0.0, "passed": false}, "enhanced_authenticator": {"component": "EnhancedAuthenticator", "start_time": "2025-08-05T21:27:42.456659", "tests": {"signature_generation": {"generation_tests": 3, "successful_generations": 3, "signature_details": {"test_1": {"success": true, "timestamp": "1754400462", "signature_length": 44, "is_base64": true}, "test_2": {"success": true, "timestamp": "1754400362", "signature_length": 44, "is_base64": true}, "test_3": {"success": true, "timestamp": "1754400562", "signature_length": 44, "is_base64": true}}, "success_rate": 1.0, "passed": true}, "url_building": {"url_tests": 1, "successful_builds": 1, "url_details": {"success": true, "url_length": 171, "contains_required_params": true}, "success_rate": 1.0, "passed": true}, "auth_flow": {"overall_success": true, "timestamp_generation": true, "signature_generation": true, "url_construction": true, "errors": [], "passed": true}}, "success_rate": 1.0, "passed": true}, "response_parser": {"component": "EnhancedResponseParser", "start_time": "2025-08-05T21:27:42.457657", "tests": {"response_parsing": {"parsing_tests": 3, "successful_parses": 3, "parse_details": {"valid_material_outbound": {"expected_success": true, "actual_success": true, "passed": true, "data_count": 2, "parsing_time": 0.0, "errors": []}, "invalid_json": {"expected_success": false, "actual_success": false, "passed": true, "data_count": 0, "parsing_time": 0.001003, "errors": ["JSON解析失败"]}, "error_response": {"expected_success": false, "actual_success": false, "passed": true, "data_count": 0, "parsing_time": 0.0, "errors": ["响应验证失败: 字段值不允许: code, 允许值: ['00000'], 必填字段缺失: data, 必填字段缺失: data.list"]}}, "success_rate": 1.0, "passed": true}, "data_validation": {"validation_tests": 1, "successful_validations": 1, "success_rate": 1.0, "passed": true, "note": "数据验证功能已集成在解析过程中"}}, "success_rate": 1.0, "passed": true}, "integrated_client": {"component": "IntegratedYSAPIClient", "start_time": "2025-08-05T21:27:42.458660", "tests": {"flow_validation": {"authentication": true, "request_building": false, "response_parsing": true, "overall_health": false, "errors": ["请求构建验证失败: 'StandardRequestBuilder' object has no attribute 'get_module_template'", "流程验证异常: 'EnhancedErrorHandler' object has no attribute 'get_error_statistics'"], "passed": false}, "statistics": {"stats_complete": false, "error": "'EnhancedErrorHandler' object has no attribute 'get_error_statistics'", "passed": false}}, "success_rate": 0.0, "passed": false}}, "summary": {"total_components": 4, "passed_components": 2, "success_rate": 0.5, "overall_passed": false, "accuracy_target_met": false, "end_time": "2025-08-05T21:27:42.459654"}, "accuracy_metrics": {"target_accuracy": 1.0, "component_accuracies": {"standard_request_builder": 0.0, "enhanced_authenticator": 1.0, "response_parser": 1.0, "integrated_client": 0.0}, "overall_accuracy": 0.5, "meets_target": false}}