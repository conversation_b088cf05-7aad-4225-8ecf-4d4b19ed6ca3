{"dashboard": {"id": null, "title": "YS-API V3.0 监控仪表板", "tags": ["ys-api", "python", "flask"], "timezone": "browser", "refresh": "5s", "panels": [{"id": 1, "title": "API 请求速率", "type": "graph", "targets": [{"expr": "rate(flask_http_request_total[1m])", "legendFormat": "请求/秒"}]}, {"id": 2, "title": "响应时间", "type": "graph", "targets": [{"expr": "flask_http_request_duration_seconds", "legendFormat": "响应时间"}]}, {"id": 3, "title": "错误率", "type": "stat", "targets": [{"expr": "rate(flask_http_request_exceptions_total[1m])", "legendFormat": "错误率"}]}], "time": {"from": "now-1h", "to": "now"}}}