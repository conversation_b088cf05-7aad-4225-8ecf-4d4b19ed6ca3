import structlog

from ...services.auto_sync_scheduler import (
    API, V3.0, YS, 任务调度API, """, -, ...services.auto_sync_scheduler,
    ...services.material_master_scheduler, ...services.retry_helper, from,
    get_material_master_scheduler, get_retry_statistics, get_robust_scheduler)
from ...services.auto_sync_scheduler import \
    get_scheduler as get_auto_sync_scheduler
from ...services.auto_sync_scheduler import (import, reset_retry_state,
                                             start_robust_auto_sync,
                                             stop_robust_auto_sync,
                                             提供任务调度配置和管理功能)

router = APIRouter()
logger = structlog.get_logger()


# 请求模型
class AutoSyncConfigRequest(BaseModel):
    """自动同步配置请求"""

    enabled: bool = Field(..., description="是否启用自动同步")
    sync_mode: str = Field(
        "daily", description="同步模式：daily（每日）或interval（间隔）"
    )
    sync_hour: int = Field(
        2, description="同步小时(0-23)，仅在daily模式下使用", ge=0, le=23
    )
    sync_minute: int = Field(
        0, description="同步分钟(0-59)，仅在daily模式下使用", ge=0, le=59
    )
    sync_interval_minutes: int = Field(
        5, description="间隔分钟数(1-1440)，仅在interval模式下使用", ge=1, le=1440
    )
    max_retries: int = Field(3, description="最大重试次数", ge=0, le=10)
    retry_delay_minutes: int = Field(30, description="重试延迟(分钟)", ge=1, le=1440)
    force_recreate_tables: bool = Field(False, description="是否强制重建表")
    record_limit: Optional[int] = Field(None, description="记录数限制", ge=1)


@router.get("/schedule")
async def get_schedule_config():
    """
    获取调度配置
    """
    try:
        # 获取真实调度配置
        # 调度服务暂时禁用
        # from ...services.scheduler_service import SchedulerService
        # 返回默认配置
        config = {
            "enabled": True,
            "sync_mode": "daily",
            "sync_hour": 2,
            "sync_minute": 0,
            "sync_interval_minutes": 30,
            "max_retries": 3,
            "retry_delay_minutes": 30,
        }

        return {"success": True, "data": config, "message": "获取调度配置成功"}

    except Exception:
        logger.error("获取调度配置失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取调度配置失败: {str(e)}")


@router.get("/auto-sync/status")
async def get_auto_sync_status():
    """获取增强版自动同步状态"""
    try:
        # 使用增强版调度器
        scheduler = await get_robust_scheduler()
        status = await scheduler.get_enhanced_status()

        return {"success": True, "data": status, "message": "获取自动同步状态成功"}

    except Exception:
        logger.error("获取自动同步状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@router.get("/auto-sync/config")
async def get_auto_sync_config():
    """获取自动同步配置"""
    try:
        scheduler = await get_auto_sync_scheduler()

        return {
            "success": True,
            "message": "获取自动同步配置成功",
            "data": scheduler.config,
        }

    except Exception:
        logger.error("获取自动同步配置失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@router.post("/auto-sync/config")
async def update_auto_sync_config(request: AutoSyncConfigRequest):
    """更新自动同步配置"""
    try:
        scheduler = await get_auto_sync_scheduler()

        config_data = request.dict()
        result = await scheduler.update_config(config_data)

        if result.get("success"):
            return {
                "success": True,
                "message": "自动同步配置更新成功",
                "data": result.get("data", config_data),
            }
        else:
            raise HTTPException(
                status_code=400, detail=result.get("message", "配置更新失败")
            )

    except HTTPException:
        raise
    except Exception:
        logger.error("更新自动同步配置失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")


@router.post("/auto-sync/start")
async def start_auto_sync():
    """启动增强版自动同步"""
    try:
        # 使用增强版调度器
        scheduler = await get_robust_scheduler()
        await scheduler.start_scheduler()
        result = {"success": True}

        if result.get("success"):
            return {"success": True, "message": "自动同步启动成功", "data": result}
        else:
            raise HTTPException(
                status_code=400, detail=result.get("message", "启动失败")
            )

    except HTTPException:
        raise
    except Exception:
        logger.error("启动自动同步失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"启动失败: {str(e)}")


@router.post("/auto-sync/stop")
async def stop_auto_sync():
    """停止增强版自动同步"""
    try:
        # 使用增强版调度器
        scheduler = await get_robust_scheduler()
        result = await scheduler.stop_scheduler()

        if result and result.get("success"):
            return {"success": True, "message": "自动同步停止成功", "data": result}
        else:
            error_msg = (
                result.get("message", "停止失败") if result else "调度器停止失败"
            )
            raise HTTPException(status_code=400, detail=error_msg)

    except HTTPException:
        raise
    except Exception:
        logger.error("停止自动同步失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"停止失败: {str(e)}")


@router.post("/auto-sync/trigger")
async def trigger_manual_sync(background_tasks: BackgroundTasks):
    """手动触发同步"""
    try:
        scheduler = await get_auto_sync_scheduler()

        # 在后台执行同步
        background_tasks.add_task(scheduler._execute_sync)

        return {
            "success": True,
            "message": "手动同步已触发，正在后台执行",
            "data": {"status": "triggered"},
        }

    except Exception:
        logger.error("触发手动同步失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"触发失败: {str(e)}")


@router.put("/schedule")
async def update_schedule_config(config: Dict):
    """
    更新调度配置
    """
    try:
        # 调度配置更新逻辑
        logger.info("更新调度配置", config=config)

        return {
            "success": True,
            "data": {"updated": True},
            "message": "调度配置更新成功",
        }

    except Exception:
        logger.error("更新调度配置失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"更新调度配置失败: {str(e)}")


@router.post("/trigger/{task_type}")
async def trigger_task(task_type: str, parameters: Optional[Dict] = None):
    """
    手动触发任务
    """
    try:
        # 任务触发逻辑
        logger.info("手动触发任务", task_type=task_type, parameters=parameters)

        return {
            "success": True,
            "data": {
                "task_id": f"manual_{task_type}_001",
                "task_type": task_type,
                "status": "triggered",
            },
            "message": f"任务 {task_type} 触发成功",
        }

    except Exception:
        logger.error("触发任务失败", task_type=task_type, error=str(e))
        raise HTTPException(status_code=500, detail=f"触发任务失败: {str(e)}")


@router.get("/history")
async def get_task_history(
    status: Optional[str] = None,
    module_name: Optional[str] = None,
    page: int = 1,
    page_size: int = 20,
):
    """
    获取任务执行历史
    """
    try:
        # 获取真实任务历史
        # 任务服务暂时禁用
        # from ...services.task_service import TaskService
        # 返回默认任务历史
        items = {
            "total": 0,
            "items": [],
            "page": page,
            "page_size": page_size,
            "total_pages": 0,
        }

        return {
            "success": True,
            "data": {
                "items": items,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": 100,
                    "total_pages": 5,
                    "has_next": page < 5,
                    "has_prev": page > 1,
                },
            },
            "message": "获取任务历史成功",
        }

    except Exception:
        logger.error("获取任务历史失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取任务历史失败: {str(e)}")


# 物料档案独立调度器接口
@router.get("/material-master/status")
async def get_material_master_status():
    """获取物料档案调度器状态"""
    try:
        scheduler = get_material_master_scheduler()
        status = scheduler.get_status()

        return {
            "success": True,
            "data": status,
            "message": "获取物料档案调度器状态成功",
        }

    except Exception:
        logger.error("获取物料档案调度器状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@router.get("/material-master/history")
async def get_material_master_history():
    """获取物料档案同步历史"""
    try:
        scheduler = get_material_master_scheduler()
        history = scheduler.get_sync_history()

        return {"success": True, "data": history, "message": "获取物料档案同步历史成功"}

    except Exception:
        logger.error("获取物料档案同步历史失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取历史失败: {str(e)}")


@router.get("/material-master/config")
async def get_material_master_config():
    """获取物料档案调度器配置"""
    try:
        scheduler = get_material_master_scheduler()

        return {
            "success": True,
            "message": "获取物料档案调度器配置成功",
            "data": scheduler.config,
        }

    except Exception:
        logger.error("获取物料档案调度器配置失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@router.post("/material-master/config")
async def update_material_master_config(config: Dict):
    """更新物料档案调度器配置"""
    try:
        scheduler = get_material_master_scheduler()
        result = scheduler.update_config(config)

        if result["success"]:
            return {
                "success": True,
                "message": "物料档案调度器配置更新成功",
                "data": result["data"],
            }
        else:
            raise HTTPException(status_code=500, detail=result["message"])

    except HTTPException:
        raise
    except Exception:
        logger.error("更新物料档案调度器配置失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")


@router.post("/material-master/start")
async def start_material_master_scheduler():
    """启动物料档案调度器"""
    try:
        scheduler = get_material_master_scheduler()
        await scheduler.start()

        return {
            "success": True,
            "message": "物料档案调度器启动成功",
            "data": scheduler.get_status(),
        }

    except Exception:
        logger.error("启动物料档案调度器失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"启动失败: {str(e)}")


@router.post("/material-master/stop")
async def stop_material_master_scheduler():
    """停止物料档案调度器"""
    try:
        scheduler = get_material_master_scheduler()
        await scheduler.stop()

        return {"success": True, "message": "物料档案调度器停止成功"}

    except Exception:
        logger.error("停止物料档案调度器失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"停止失败: {str(e)}")


@router.post("/material-master/trigger")
async def trigger_material_master_sync(background_tasks: BackgroundTasks):
    """手动触发物料档案同步"""
    try:
        scheduler = get_material_master_scheduler()

        # 在后台执行同步任务
        background_tasks.add_task(scheduler.trigger_manual_sync)

        return {
            "success": True,
            "message": "物料档案手动同步任务已启动",
            "data": {
                "triggered_at": "datetime.now().isoformat()",
                "type": "manual_trigger",
                "module_name": "material_master",
            },
        }

    except Exception:
        logger.error("手动触发物料档案同步失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"触发失败: {str(e)}")


# ==================== 增强版调度器专用API ====================


@router.get("/auto-sync/enhanced-status")
async def get_enhanced_auto_sync_status():
    """获取增强版自动同步详细状态"""
    try:
        scheduler = await get_robust_scheduler()
        status = await scheduler.get_enhanced_status()

        return {"success": True, "data": status, "message": "获取增强版状态成功"}

    except Exception:
        logger.error("获取增强版状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取增强版状态失败: {str(e)}")


@router.post("/auto-sync/start-robust")
async def start_robust_auto_sync():
    """启动增强版自动同步调度器"""
    try:

        await start_robust_auto_sync()

        return {
            "success": True,
            "message": "增强版自动同步调度器启动成功",
            "features": [
                "智能重试机制",
                "断点续传支持",
                "失败熔断保护",
                "资源监控",
                "分布式锁",
            ],
        }

    except Exception:
        logger.error("启动增强版调度器失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"启动增强版调度器失败: {str(e)}")


@router.post("/auto-sync/stop-robust")
async def stop_robust_auto_sync():
    """停止增强版自动同步调度器"""
    try:

        await stop_robust_auto_sync()

        return {"success": True, "message": "增强版自动同步调度器已停止"}

    except Exception:
        logger.error("停止增强版调度器失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"停止增强版调度器失败: {str(e)}")


@router.get("/retry/statistics")
async def get_retry_statistics():
    """获取重试统计信息"""
    try:

        stats = get_retry_statistics()

        return {"success": True, "data": stats, "message": "获取重试统计成功"}

    except Exception:
        logger.error("获取重试统计失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取重试统计失败: {str(e)}")


@router.post("/retry/reset")
async def reset_retry_state():
    """重置重试状态"""
    try:

        reset_retry_state()

        return {"success": True, "message": "重试状态已重置"}

    except Exception:
        logger.error("重置重试状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"重置重试状态失败: {str(e)}")
