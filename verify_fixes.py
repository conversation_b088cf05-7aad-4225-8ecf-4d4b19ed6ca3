# !/usr/bin/env python3
"""
验证修复后的启动脚本
"""

import inspect
import sys
from pathlib import Path

from port_manager import PortManager


def test_port_manager():
    """测试PortManager修复"""
    print("🔍 测试PortManager修复...")

    try:
        # 添加scripts到路径
        sys.path.append(str(Path(__file__).parent / "scripts"))

        # 创建实例
        pm = PortManager()

        # 测试方法签名

        sig = inspect.signature(pm.ensure_port_available)
        params = list(sig.parameters.keys())

        print(f"✅ PortManager.ensure_port_available 参数: {params}")

        if len(params) == 2 and "self" in params and "port" in params:
            print("✅ 方法签名正确：只需要一个port参数")
            return True
        else:
            print(f"❌ 方法签名错误：期待2个参数(self, port)，实际{len(params)}个")
            return False

    except Exception as e:
        print(f"❌ PortManager测试失败: {e}")
        return False


def test_error_handler():
    """测试ErrorHandler修复"""
    print("\n🔍 测试ErrorHandler修复...")

    error_handler_file = Path(__file__).parent / \
        "frontend/js/common/error-handler.js"

    if not error_handler_file.exists():
        print("❌ error-handler.js文件不存在")
        return False

    # 读取文件检查window.ErrorHandler
    content = error_handler_file.read_text(encoding="utf-8")

    if "window.ErrorHandler" in content:
        print("✅ error-handler.js包含window.ErrorHandler定义")
        return True
    else:
        print("❌ error-handler.js缺少window.ErrorHandler定义")
        return False


def main():
    """主测试函数"""
    print("🧪 YS-API V3.0 启动修复验证")
    print("=" * 50)

    port_ok = test_port_manager()
    error_ok = test_error_handler()

    print("\n📊 测试结果:")
    print(f"  PortManager修复: {'✅ 通过' if port_ok else '❌ 失败'}")
    print(f"  ErrorHandler修复: {'✅ 通过' if error_ok else '❌ 失败'}")

    if port_ok and error_ok:
        print("\n🎉 所有修复验证通过！可以尝试启动服务了。")
        print("\n📝 启动命令:")
        print("  后端: python backend/start_server_fixed.py")
        print("  前端: python frontend/start_frontend_fixed.py")
    else:
        print("\n⚠️ 部分修复验证失败，请检查错误信息。")


if __name__ == "__main__":
    main()
