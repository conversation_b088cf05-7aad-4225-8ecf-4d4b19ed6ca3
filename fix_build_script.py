import logging
import re
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复 build_production_package.py 代码质量
将 print 语句替换为标准的 logging
"""


class BuildScriptFixer:
    def __init___(self, project_root: str):
    """TODO: Add function description."""
    self.project_root = Path(project_root)
    self.target_file = self.project_root / "build_production_package.py"

    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('build_script_fix.log', encoding='utf-8'),
            logging.StreamHandler(),
        ],
    )
    self.logger = logging.getLogger(__name__)

    def fix_build_production_package(self):
        """修复 build_production_package.py 中的 print 语句"""
        self.logger.info("🔧 开始修复 build_production_package.py")

        if not self.target_file.exists():
            self.logger.error(f"目标文件不存在: {self.target_file}")
            return False

        try:
            # 读取原文件
            with open(self.target_file, 'r', encoding='utf-8') as f:
                content = f.read()

            original_content = content

            # 1. 添加 logging 导入（如果没有）
            if 'import logging' not in content:
                # 在其他import之后添加
                lines = content.split('\n')
                import_index = -1

                for i, line in enumerate(lines):
                    if line.startswith('import ') or line.startswith('from '):
                        import_index = i

                if import_index >= 0:
                    lines.insert(import_index + 1, 'import logging')
                    content = '\n'.join(lines)
                    self.logger.info("✅ 添加了 logging 导入")

            # 2. 在类定义之前添加 logging 配置
            if 'logging.basicConfig' not in content:
                class_match = re.search(
                    r'class\s+ProductionPackager.*?:', content)
                if class_match:
                    class_start = class_match.start()
                    logging_config = '''
# 设置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('production_build.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

'''
                    content = (content[:class_start] +
                               logging_config + content[class_start:])
                    self.logger.info("✅ 添加了 logging 配置")

            # 3. 在类的 __init__ 方法中添加 logger
            init_match = re.search(
                r'def __init__\(self\):(.*?)(?=\n    def|\nclass|\Z)',
                content,
                re.DOTALL,
            )
            if init_match and 'self.logger' not in init_match.group(1):
                init_end = init_match.end()
                # 在 __init__ 方法最后添加 logger
                init_content = init_match.group(0)
                if 'logger.info("=" * 50)' in init_content:
                    new_init = init_content.replace(
                        'logger.info("=" * 50)',
                        'self.logger = logging.getLogger(__name__)\n        self.logger.info("YS-API V3.0 生产环境打包器")\n        self.logger.info("=" * 50)',
                    )
                    content = (content[: init_match.start()] +
                               new_init + content[init_end:])
                    self.logger.info("✅ 在 __init__ 中添加了 logger")

            # 4. 替换所有 print 语句为 logger 调用
            print_replacements = [
                # 基本的 print 语句
                (r'print\("([^"]+)"\)', r'self.logger.info("\1")'),
                (r"print\('([^']+)'\)", r'self.logger.info("\1")'),
                # f-string print 语句
                (r'print\(f"([^"]+)"\)', r'self.logger.info(f"\1")'),
                (r"print\(f'([^']+)'\)", r'self.logger.info(f"\1")'),
                # 包含变量的 print 语句
                (r'print\(f"([^"]*\{[^}]+\}[^"]*)"\)',
                 r'self.logger.info(f"\1")'),
                # 特殊情况：错误信息
                (r'print\(f"❌ ([^"]+)"\)', r'self.logger.error(f"\1")'),
                (r'print\(f"⚠️ ([^"]+)"\)', r'self.logger.warning(f"\1")'),
                (r'print\("❌ ([^"]+)"\)', r'self.logger.error("\1")'),
                (r'print\("⚠️ ([^"]+)"\)', r'self.logger.warning("\1")'),
            ]

            changes_made = 0
            for pattern, replacement in print_replacements:
                old_content = content
                content = re.sub(pattern, replacement, content)
                if content != old_content:
                    changes_made += 1

            self.logger.info(f"✅ 替换了 {changes_made} 个 print 语句模式")

            # 5. 手动处理复杂的 print 语句
            # 处理跨行或复杂格式的 print
            complex_patterns = [
                # 处理简单的无引号print
                (
                    r'print\(([^)]+)\)',
                    lambda m: self._convert_print_to_logger(m.group(1)),
                ),
            ]

            for pattern, replacement in complex_patterns:
                if callable(replacement):
                    content = re.sub(pattern, replacement, content)
                else:
                    content = re.sub(pattern, replacement, content)

            # 6. 特殊处理：确保第一个print被正确替换
            if 'logger.info("🚀 YS-API V3.0 生产环境打包器")' in content:
                content = content.replace(
                    'logger.info("🚀 YS-API V3.0 生产环境打包器")',
                    '# 已在__init__中使用logger',
                )

            # 保存修改后的文件
            if content != original_content:
                # 创建备份
                backup_file = self.target_file.with_suffix('.py.backup')
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                self.logger.info(f"✅ 创建备份文件: {backup_file}")

                # 保存修改后的文件
                with open(self.target_file, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.logger.info("✅ 成功修复 build_production_package.py")
                return True
            else:
                self.logger.info("ℹ️ 文件无需修改")
                return True

        except Exception:
            self.logger.error(f"修复失败: {e}")
            return False

    def _convert_print_to_logger(self, print_content: str) -> str:
        """将复杂的print内容转换为logger调用"""
        print_content = print_content.strip()

        # 如果是简单字符串
        if print_content.startswith('"') and print_content.endswith('"'):
            content = print_content[1:-1]
            if content.startswith('❌'):
                return f'self.logger.error("{content}")'
            elif content.startswith('⚠️'):
                return f'self.logger.warning("{content}")'
            else:
                return f'self.logger.info("{content}")'

        # 如果是 f-string
        if print_content.startswith('f"') and print_content.endswith('"'):
            content = print_content[1:]  # 移除 f
            if '❌' in content:
                return f'self.logger.error(f{content})'
            elif '⚠️' in content:
                return f'self.logger.warning(f{content})'
            else:
                return f'self.logger.info(f{content})'

        # 默认处理
        return f'self.logger.info({print_content})'

    def verify_fix(self):
        """验证修复结果"""
        self.logger.info("🔍 验证修复结果...")

        try:
            with open(self.target_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查是否还有 print 语句
            print_matches = re.findall(r'print\([^)]*\)', content)

            if print_matches:
                self.logger.warning(f"仍有 {len(print_matches)} 个 print 语句未处理:")
                for i, match in enumerate(print_matches[:5], 1):  # 只显示前5个
                    self.logger.warning(f"  {i}. {match}")
                return False
            else:
                self.logger.info("✅ 所有 print 语句已成功替换为 logger")
                return True

        except Exception:
            self.logger.error(f"验证失败: {e}")
            return False

    def run_fix(self):
        """运行完整的修复流程"""
        self.logger.info("🚀 开始修复 build_production_package.py 代码质量")
        self.logger.info("=" * 60)

        # 执行修复
        if self.fix_build_production_package():
            # 验证修复结果
            if self.verify_fix():
                self.logger.info("🎉 修复完成且验证通过！")
                return True
            else:
                self.logger.warning("⚠️ 修复完成但验证发现问题")
                return False
        else:
            self.logger.error("❌ 修复失败")
            return False


if __name__ == "__main__":
    project_root = Path(__file__).parent
    fixer = BuildScriptFixer(str(project_root))
    success = fixer.run_fix()

    if success:
        self.logger.info("\n✅ 修复成功！可以重新运行综合检查验证结果")
    else:
        self.logger.info("\n❌ 修复失败，请检查日志")
