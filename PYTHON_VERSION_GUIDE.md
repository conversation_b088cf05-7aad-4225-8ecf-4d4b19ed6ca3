# YS-API V3.0 Python环境配置

## Python版本要求
- **推荐版本：** Python 3.10.x
- **兼容版本：** Python 3.10 - 3.12
- **不兼容：** Python 3.13+ (pandas兼容性问题)

## 当前环境
- Python 3.10.11
- Pandas 2.1.4

## 启动脚本
- `start_with_python310.bat` - 使用Python 3.10启动完整服务
- `health_check_python310.bat` - 使用Python 3.10进行健康检查

## Python版本切换命令
```bash
# 检查可用版本
py -0p

# 使用Python 3.10
py -3.10 [script.py]

# 安装依赖
py -3.10 -m pip install -r requirements.txt
```

## 注意事项
1. 确保使用 `py -3.10` 命令而不是 `python` 
2. pandas 2.1.4 与 Python 3.13 不兼容
3. 所有启动脚本已更新为使用Python 3.10
