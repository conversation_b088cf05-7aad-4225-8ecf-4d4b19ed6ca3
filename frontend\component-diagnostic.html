<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组件加载诊断工具</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .panel { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; }
        .error { color: red; background: #ffe6e6; }
        .success { color: green; background: #e6ffe6; }
        .warning { color: orange; background: #fff3e6; }
    </style>
</head>
<body>
    <h1>🔧 组件加载诊断工具</h1>
    
    <div class="panel">
        <h3>步骤1: 脚本加载检查</h3>
        <div id="scriptCheck">检查中...</div>
    </div>
    
    <div class="panel">
        <h3>步骤2: ComponentManager 检查</h3>
        <div id="componentManagerCheck">检查中...</div>
    </div>
    
    <div class="panel">
        <h3>步骤3: 详细错误信息</h3>
        <div id="errorDetails">检查中...</div>
    </div>
    
    <div class="panel">
        <h3>步骤4: 修复建议</h3>
        <div id="fixSuggestions">检查中...</div>
    </div>

    <!-- 核心脚本加载 -->
    <script src="js/core/component-manager.js"></script>
    <script src="js/core/app-bootstrap.js"></script>
    
    <script>
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `panel ${type}`;
        }

        function diagnose() {
            // 步骤1: 检查脚本是否加载
            let scriptStatus = '✅ 基本脚本已加载<br>';
            
            // 步骤2: 检查ComponentManager
            let cmStatus = '';
            if (window.ComponentManager) {
                cmStatus += '✅ window.ComponentManager 存在<br>';
                cmStatus += `类型: ${typeof window.ComponentManager}<br>`;
                
                if (typeof window.ComponentManager.register === 'function') {
                    cmStatus += '✅ register 方法可用<br>';
                    updateStatus('componentManagerCheck', cmStatus, 'success');
                } else {
                    cmStatus += '❌ register 方法不可用<br>';
                    cmStatus += `方法列表: ${Object.keys(window.ComponentManager).join(', ')}<br>`;
                    updateStatus('componentManagerCheck', cmStatus, 'error');
                }
            } else {
                cmStatus = '❌ window.ComponentManager 不存在';
                updateStatus('componentManagerCheck', cmStatus, 'error');
            }
            
            updateStatus('scriptCheck', scriptStatus, 'success');
            
            // 步骤3: 详细错误分析
            let errorDetails = '';
            if (!window.ComponentManager) {
                errorDetails += '🔍 component-manager.js 可能未正确加载<br>';
                errorDetails += '🔍 检查文件路径: js/core/component-manager.js<br>';
            } else if (typeof window.ComponentManager.register !== 'function') {
                errorDetails += '🔍 ComponentManager 被错误地赋值为类而不是实例<br>';
                errorDetails += `🔍 当前值: ${window.ComponentManager}<br>`;
                errorDetails += `🔍 构造函数: ${window.ComponentManager.constructor?.name}<br>`;
            }
            
            if (errorDetails) {
                updateStatus('errorDetails', errorDetails, 'warning');
            } else {
                updateStatus('errorDetails', '✅ 未发现明显错误', 'success');
            }
            
            // 步骤4: 修复建议
            let fixes = '';
            if (!window.ComponentManager) {
                fixes += '💡 建议1: 检查 js/core/component-manager.js 文件是否存在<br>';
                fixes += '💡 建议2: 确保在正确的目录下启动服务器<br>';
                fixes += '💡 建议3: 检查浏览器控制台是否有脚本加载错误<br>';
            } else if (typeof window.ComponentManager.register !== 'function') {
                fixes += '💡 建议1: component-manager.js 的导出逻辑有问题<br>';
                fixes += '💡 建议2: 需要修复实例和类的赋值问题<br>';
                fixes += '💡 建议3: 刷新页面或重新启动服务器<br>';
            } else {
                fixes += '🎉 组件管理器工作正常！可以继续测试<br>';
                fixes += '<button onclick="runQuickTest()">运行快速测试</button>';
            }
            
            updateStatus('fixSuggestions', fixes, 
                window.ComponentManager && typeof window.ComponentManager.register === 'function' ? 'success' : 'warning');
        }
        
        function runQuickTest() {
            try {
                const cm = window.ComponentManager;
                
                // 注册测试组件
                class TestComponent {
                    constructor() {
                        this.name = 'TestComponent';
                    }
                }
                
                cm.register('testComponent', TestComponent, {
                    singleton: true,
                    autoInit: false,
                    description: '测试组件'
                });
                
                alert('✅ 快速测试通过！ComponentManager 工作正常');
            } catch (error) {
                alert('❌ 快速测试失败: ' + error.message);
            }
        }

        // 延迟执行诊断
        setTimeout(diagnose, 100);
    </script>
</body>
</html>
