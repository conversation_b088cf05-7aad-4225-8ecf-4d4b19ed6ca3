import re
from dataclasses import dataclass

import structlog

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
业务翻译规则模块
提供业务特定的翻译规则和逻辑
"""


logger = structlog.get_logger()


@dataclass
class TranslationRule:
    """翻译规则"""

    pattern: str
    replacement: str
    priority: int = 1
    module: Optional[str] = None
    description: str = ""


class BusinessTranslationRules:
    """业务翻译规则管理器"""

    def __init___(self):
    """TODO: Add function description."""
    self.rules: List[TranslationRule] = []
    self._init_default_rules()

    def _init_default_rules(self):
        """初始化默认翻译规则"""

        # 通用业务术语规则
        common_rules = [
            # 数量相关
            TranslationRule(r"数量", "qty", 1, description="数量字段"),
            TranslationRule(r"件数", "qty", 1, description="件数字段"),
            TranslationRule(r"个数", "qty", 1, description="个数字段"),
            TranslationRule(r"数量", "quantity", 1, description="数量字段"),
            # 金额相关
            TranslationRule(r"金额", "amount", 1, description="金额字段"),
            TranslationRule(r"单价", "price", 1, description="单价字段"),
            TranslationRule(r"总价", "total_price", 1, description="总价字段"),
            TranslationRule(r"原价", "original_price", 1, description="原价字段"),
            # 日期时间相关
            TranslationRule(r"日期", "date", 1, description="日期字段"),
            TranslationRule(r"时间", "time", 1, description="时间字段"),
            TranslationRule(r"创建时间", "create_time", 1, description="创建时间"),
            TranslationRule(r"修改时间", "modify_time", 1, description="修改时间"),
            # 编码相关
            TranslationRule(r"编码", "code", 1, description="编码字段"),
            TranslationRule(r"编号", "number", 1, description="编号字段"),
            TranslationRule(r"单号", "orderNo", 1, description="单号字段"),
            # 名称相关
            TranslationRule(r"名称", "name", 1, description="名称字段"),
            TranslationRule(r"简称", "short_name", 1, description="简称字段"),
            TranslationRule(r"全称", "full_name", 1, description="全称字段"),
            # 状态相关
            TranslationRule(r"状态", "status", 1, description="状态字段"),
            TranslationRule(r"类型", "type", 1, description="类型字段"),
            TranslationRule(r"分类", "category", 1, description="分类字段"),
            # 组织相关
            TranslationRule(r"组织", "org", 1, description="组织字段"),
            TranslationRule(r"部门", "dept", 1, description="部门字段"),
            TranslationRule(r"公司", "company", 1, description="公司字段"),
            # 地址相关
            TranslationRule(r"地址", "address", 1, description="地址字段"),
            TranslationRule(r"电话", "phone", 1, description="电话字段"),
            TranslationRule(r"邮箱", "email", 1, description="邮箱字段"),
            # 备注相关
            TranslationRule(r"备注", "memo", 1, description="备注字段"),
            TranslationRule(r"说明", "description", 1, description="说明字段"),
            TranslationRule(r"注释", "comment", 1, description="注释字段"),
        ]

        # 采购订单特定规则
        purchase_rules = [
            TranslationRule(r"供应商", "vendor", 2, "purchase_order", "供应商字段"),
            TranslationRule(r"采购员", "buyer", 2, "purchase_order", "采购员字段"),
            TranslationRule(
                r"采购部门", "purchase_dept", 2, "purchase_order", "采购部门"
            ),
            TranslationRule(
                r"采购类型", "purchase_type", 2, "purchase_order", "采购类型"
            ),
            TranslationRule(
                r"采购日期", "purchase_date", 2, "purchase_order", "采购日期"
            ),
            TranslationRule(
                r"交货日期", "delivery_date", 2, "purchase_order", "交货日期"
            ),
            TranslationRule(
                r"采购价格", "purchase_price", 2, "purchase_order", "采购价格"
            ),
            TranslationRule(
                r"采购数量", "purchase_qty", 2, "purchase_order", "采购数量"
            ),
        ]

        # 销售订单特定规则
        sales_rules = [
            TranslationRule(r"客户", "customer", 2, "sales_order", "客户字段"),
            TranslationRule(r"销售员", "salesman", 2, "sales_order", "销售员字段"),
            TranslationRule(r"销售部门", "sales_dept", 2, "sales_order", "销售部门"),
            TranslationRule(r"销售类型", "sales_type", 2, "sales_order", "销售类型"),
            TranslationRule(r"销售日期", "sales_date", 2, "sales_order", "销售日期"),
            TranslationRule(r"发货日期", "ship_date", 2, "sales_order", "发货日期"),
            TranslationRule(r"销售价格", "sales_price", 2, "sales_order", "销售价格"),
            TranslationRule(r"销售数量", "sales_qty", 2, "sales_order", "销售数量"),
        ]

        # 生产订单特定规则
        production_rules = [
            TranslationRule(
                r"生产车间",
                "workshop",
                2,
                "production_order",
                "生产车间"),
            TranslationRule(
                r"生产线", "production_line", 2, "production_order", "生产线"
            ),
            TranslationRule(
                r"生产计划", "production_plan", 2, "production_order", "生产计划"
            ),
            TranslationRule(
                r"生产数量", "production_qty", 2, "production_order", "生产数量"
            ),
            TranslationRule(
                r"计划开始", "plan_start", 2, "production_order", "计划开始"
            ),
            TranslationRule(
                r"计划结束",
                "plan_end",
                2,
                "production_order",
                "计划结束"),
            TranslationRule(
                r"实际开始", "actual_start", 2, "production_order", "实际开始"
            ),
            TranslationRule(
                r"实际结束", "actual_end", 2, "production_order", "实际结束"
            ),
        ]

        # 库存相关规则
        inventory_rules = [
            TranslationRule(r"库存", "inventory", 2, "inventory", "库存字段"),
            TranslationRule(r"现存量", "current_stock", 2, "inventory", "现存量"),
            TranslationRule(r"可用量", "available_qty", 2, "inventory", "可用量"),
            TranslationRule(r"在途量", "in_transit_qty", 2, "inventory", "在途量"),
            TranslationRule(r"安全库存", "safety_stock", 2, "inventory", "安全库存"),
            TranslationRule(r"最大库存", "max_stock", 2, "inventory", "最大库存"),
            TranslationRule(r"最小库存", "min_stock", 2, "inventory", "最小库存"),
        ]

        # 物料相关规则
        material_rules = [
            TranslationRule(r"物料", "material", 2, "material_master", "物料字段"),
            TranslationRule(
                r"规格",
                "specification",
                2,
                "material_master",
                "规格字段"),
            TranslationRule(r"型号", "model", 2, "material_master", "型号字段"),
            TranslationRule(r"品牌", "brand", 2, "material_master", "品牌字段"),
            TranslationRule(r"单位", "unit", 2, "material_master", "单位字段"),
            TranslationRule(r"重量", "weight", 2, "material_master", "重量字段"),
            TranslationRule(r"尺寸", "dimension", 2, "material_master", "尺寸字段"),
        ]

        # 合并所有规则
        all_rules = (
            common_rules
            + purchase_rules
            + sales_rules
            + production_rules
            + inventory_rules
            + material_rules
        )

        self.rules = all_rules
        logger.info("业务翻译规则初始化完成", rules_count=len(self.rules))

    def translate_field(
        self, field_name: str, module_name: Optional[str] = None
    ) -> str:
        """
        翻译字段名

        Args:
            field_name: 原始字段名
            module_name: 模块名称（可选）

        Returns:
            翻译后的字段名
        """
        if not field_name:
            return field_name

        # 按优先级排序规则
        sorted_rules = sorted(
            self.rules,
            key=lambda x: x.priority,
            reverse=True)

        translated_name = field_name

        for rule in sorted_rules:
            # 如果指定了模块，只应用该模块的规则或通用规则
            if module_name and rule.module and rule.module != module_name:
                continue

            # 应用翻译规则
            if re.search(rule.pattern, translated_name):
                translated_name = re.sub(
                    rule.pattern, rule.replacement, translated_name
                )
                logger.debug(
                    "应用翻译规则",
                    original=field_name,
                    translated=translated_name,
                    rule=rule.pattern,
                    module=module_name,
                )
                break

        return translated_name

    def translate_fields_batch(
        self, field_names: List[str], module_name: Optional[str] = None
    ) -> Dict[str, str]:
        """
        批量翻译字段名

        Args:
            field_names: 字段名列表
            module_name: 模块名称（可选）

        Returns:
            翻译映射字典 {原字段名: 翻译后字段名}
        """
        result = {}
        for field_name in field_names:
            translated = self.translate_field(field_name, module_name)
            result[field_name] = translated

        logger.info(
            "批量翻译完成",
            total_fields=len(field_names),
            module=module_name,
            translated_count=len(
                [v for v in result.values() if v != field_name]),
        )

        return result

    def get_rules_for_module(self, module_name: str) -> List[TranslationRule]:
        """
        获取指定模块的翻译规则

        Args:
            module_name: 模块名称

        Returns:
            翻译规则列表
        """
        module_rules = [
            rule for rule in self.rules if rule.module == module_name]
        common_rules = [rule for rule in self.rules if rule.module is None]

        return module_rules + common_rules

    def add_rule(self, rule: TranslationRule):
        """
        添加自定义翻译规则

        Args:
            rule: 翻译规则
        """
        self.rules.append(rule)
        logger.info(
            "添加自定义翻译规则",
            pattern=rule.pattern,
            replacement=rule.replacement,
            module=rule.module,
        )

    def remove_rule(self, pattern: str, module: Optional[str] = None):
        """
        移除翻译规则

        Args:
            pattern: 规则模式
            module: 模块名称（可选）
        """
        self.rules = [
            rule
            for rule in self.rules
            if not (rule.pattern == pattern and rule.module == module)
        ]
        logger.info("移除翻译规则", pattern=pattern, module=module)

    def get_statistics(self) -> Dict:
        """
        获取翻译规则统计信息

        Returns:
            统计信息字典
        """
        total_rules = len(self.rules)
        module_rules = {}

        for rule in self.rules:
            module = rule.module or "common"
            if module not in module_rules:
                module_rules[module] = 0
            module_rules[module] += 1

        return {
            "total_rules": total_rules,
            "module_rules": module_rules,
            "modules_count": len(module_rules),
        }

    def apply_business_rules(
        self, field_name: str, module_name: Optional[str] = None
    ) -> str:
        """
        应用业务翻译规则（兼容性方法）

        Args:
            field_name: 字段名
            module_name: 模块名称（可选）

        Returns:
            翻译后的字段名
        """
        return self.translate_field(field_name, module_name)

    def reverse_translate_field(
        self, field_name: str, module_name: Optional[str] = None
    ) -> str:
        """
        反向翻译字段名（英文 → 中文）

        Args:
            field_name: 英文字段名
            module_name: 模块名称（可选）

        Returns:
            中文字段名
        """
        if not field_name:
            return field_name

        # 创建反向规则映射
        reverse_rules = {}
        for rule in self.rules:
            # 如果指定了模块，只应用该模块的规则或通用规则
            if module_name and rule.module and rule.module != module_name:
                continue

            # 创建反向映射：英文 → 中文
            reverse_rules[rule.replacement] = rule.pattern

        # 尝试完全匹配
        if field_name in reverse_rules:
            return reverse_rules[field_name]

        # 尝试部分匹配
        for english_name, chinese_name in reverse_rules.items():
            if english_name in field_name:
                # 替换匹配的部分
                result = field_name.replace(english_name, chinese_name)
                if result != field_name:
                    return result

        # 尝试驼峰命名解析

        camel_case_parts = re.findall(r'[A-Z][a-z]*', field_name)
        if camel_case_parts:
            translated_parts = []
            for part in camel_case_parts:
                # 尝试完整匹配
                if part in reverse_rules:
                    translated_parts.append(reverse_rules[part])
                # 尝试小写匹配
                elif part.lower() in reverse_rules:
                    translated_parts.append(reverse_rules[part.lower()])
                else:
                    translated_parts.append(part)
            result = ''.join(translated_parts)
            if result != field_name:
                return result

        return field_name
