import re
from dataclasses import dataclass
from enum import Enum

import structlog

"""
YS-API V3.0 数据质量检查器
Month 3 Week 3: 多维度数据质量评估和验证
"""


logger = structlog.get_logger()


class QualityDimension(Enum):
    """质量维度枚举"""
    COMPLETENESS = "completeness"  # 完整性
    CONSISTENCY = "consistency"    # 一致性
    ACCURACY = "accuracy"          # 准确性
    VALIDITY = "validity"          # 有效性
    TIMELINESS = "timeliness"      # 时效性
    UNIQUENESS = "uniqueness"      # 唯一性


class SeverityLevel(Enum):
    """严重级别枚举"""
    CRITICAL = "critical"  # 严重
    HIGH = "high"         # 高
    MEDIUM = "medium"     # 中等
    LOW = "low"          # 低
    INFO = "info"        # 信息


@dataclass
class QualityRule:
    """质量检查规则"""
    rule_id: str
    dimension: QualityDimension
    severity: SeverityLevel
    description: str
    check_function: callable
    applicable_fields: List[str] = None
    applicable_modules: List[str] = None


@dataclass
class QualityIssue:
    """质量问题"""
    rule_id: str
    dimension: QualityDimension
    severity: SeverityLevel
    description: str
    field_name: Optional[str] = None
    record_index: Optional[int] = None
    current_value: Any = None
    expected_value: Any = None
    suggestion: Optional[str] = None


@dataclass
class QualityReport:
    """质量报告"""
    overall_score: float
    dimension_scores: Dict[str, float]
    total_records: int
    issues_found: int
    issues_by_severity: Dict[str, int]
    issues: List[QualityIssue]
    recommendations: List[str]
    inspection_time: float


class DataQualityInspector:
    """数据质量检查器"""

    def __init___(self):
    """TODO: Add function description."""
    self.quality_rules = []
    self.inspection_stats = {
        'total_inspections': 0,
        'total_records_inspected': 0,
        'total_issues_found': 0,
        'average_quality_score': 0.0
    }

    # 初始化质量检查规则
    self._initialize_quality_rules()

    logger.info(
        "数据质量检查器初始化完成",
        rules_count=len(self.quality_rules)
    )

    def _initialize_quality_rules(self):
        """初始化质量检查规则"""

        # 完整性检查规则
        self.quality_rules.extend([
            QualityRule(
                rule_id="COMP_001",
                dimension=QualityDimension.COMPLETENESS,
                severity=SeverityLevel.HIGH,
                description="检查必填字段是否为空",
                check_function=self._check_required_fields,
                applicable_fields=[
                    "bill_number",
                    "order_number",
                    "material_code"]
            ),
            QualityRule(
                rule_id="COMP_002",
                dimension=QualityDimension.COMPLETENESS,
                severity=SeverityLevel.MEDIUM,
                description="检查重要字段的完整性",
                check_function=self._check_important_fields,
                applicable_fields=["quantity", "unit_price", "customer_code"]
            )
        ])

        # 一致性检查规则
        self.quality_rules.extend([
            QualityRule(
                rule_id="CONS_001",
                dimension=QualityDimension.CONSISTENCY,
                severity=SeverityLevel.HIGH,
                description="检查数量字段的一致性",
                check_function=self._check_quantity_consistency,
                applicable_fields=[
                    "quantity",
                    "planned_quantity",
                    "current_quantity"]
            ),
            QualityRule(
                rule_id="CONS_002",
                dimension=QualityDimension.CONSISTENCY,
                severity=SeverityLevel.MEDIUM,
                description="检查日期字段的逻辑一致性",
                check_function=self._check_date_consistency,
                applicable_fields=["start_date", "end_date", "order_date"]
            )
        ])

        # 准确性检查规则
        self.quality_rules.extend([
            QualityRule(
                rule_id="ACC_001",
                dimension=QualityDimension.ACCURACY,
                severity=SeverityLevel.HIGH,
                description="检查数字字段的精度和范围",
                check_function=self._check_numeric_accuracy,
                applicable_fields=["quantity", "unit_price", "total_amount"]
            ),
            QualityRule(
                rule_id="ACC_002",
                dimension=QualityDimension.ACCURACY,
                severity=SeverityLevel.MEDIUM,
                description="检查编码字段的格式正确性",
                check_function=self._check_code_format,
                applicable_fields=[
                    "material_code",
                    "customer_code",
                    "supplier_code"]
            )
        ])

        # 有效性检查规则
        self.quality_rules.extend([
            QualityRule(
                rule_id="VAL_001",
                dimension=QualityDimension.VALIDITY,
                severity=SeverityLevel.HIGH,
                description="检查字段值是否在有效范围内",
                check_function=self._check_value_validity,
                applicable_fields=["quantity", "unit_price"]
            ),
            QualityRule(
                rule_id="VAL_002",
                dimension=QualityDimension.VALIDITY,
                severity=SeverityLevel.MEDIUM,
                description="检查日期字段的有效性",
                check_function=self._check_date_validity,
                applicable_fields=["order_date", "start_date", "end_date"]
            )
        ])

        # 时效性检查规则
        self.quality_rules.extend([
            QualityRule(
                rule_id="TIME_001",
                dimension=QualityDimension.TIMELINESS,
                severity=SeverityLevel.MEDIUM,
                description="检查数据的新鲜度",
                check_function=self._check_data_freshness,
                applicable_fields=["last_update", "order_date"]
            )
        ])

        # 唯一性检查规则
        self.quality_rules.extend([
            QualityRule(
                rule_id="UNIQ_001",
                dimension=QualityDimension.UNIQUENESS,
                severity=SeverityLevel.HIGH,
                description="检查主键字段的唯一性",
                check_function=self._check_uniqueness,
                applicable_fields=["bill_number", "order_number"]
            )
        ])

    def inspect_data(self, data: Any, module_name: str,
                     context: Dict[str, Any] = None) -> QualityReport:
        """
        检查数据质量

        Args:
            data: 要检查的数据
            module_name: 模块名称
            context: 检查上下文

        Returns:
            QualityReport: 质量报告
        """
        start_time = datetime.now()
        self.inspection_stats['total_inspections'] += 1

        try:
            # 准备数据
            if isinstance(data, list):
                records = data
            elif isinstance(data, dict):
                records = [data]
            else:
                records = []

            self.inspection_stats['total_records_inspected'] += len(records)

            # 执行质量检查
            all_issues = []
            dimension_issues = {dim.value: [] for dim in QualityDimension}

            for rule in self.quality_rules:
                if self._is_rule_applicable(rule, module_name, records):
                    issues = rule.check_function(records, rule, context or {})
                    all_issues.extend(issues)
                    dimension_issues[rule.dimension.value].extend(issues)

            # 统计问题数量
            issues_by_severity = {level.value: 0 for level in SeverityLevel}
            for issue in all_issues:
                issues_by_severity[issue.severity.value] += 1

            # 计算维度得分
            dimension_scores = {}
            for dimension in QualityDimension:
                dimension_score = self._calculate_dimension_score(
                    dimension_issues[dimension.value],
                    len(records)
                )
                dimension_scores[dimension.value] = dimension_score

            # 计算总体得分
            overall_score = self._calculate_overall_score(
                dimension_scores, all_issues)

            # 生成建议
            recommendations = self._generate_recommendations(
                all_issues, dimension_scores)

            # 更新统计
            self.inspection_stats['total_issues_found'] += len(all_issues)
            self._update_average_quality_score(overall_score)

            inspection_time = (datetime.now() - start_time).total_seconds()

            logger.info(
                "数据质量检查完成",
                module=module_name,
                records_count=len(records),
                issues_count=len(all_issues),
                overall_score=overall_score,
                inspection_time=inspection_time
            )

            return QualityReport(
                overall_score=overall_score,
                dimension_scores=dimension_scores,
                total_records=len(records),
                issues_found=len(all_issues),
                issues_by_severity=issues_by_severity,
                issues=all_issues,
                recommendations=recommendations,
                inspection_time=inspection_time
            )

        except Exception:
            logger.error(
                "数据质量检查异常",
                module=module_name,
                error=str(e)
            )

            # 返回默认质量报告
            return self._create_error_report(str(e), start_time)

    def _is_rule_applicable(self,
                            rule: QualityRule,
                            module_name: str,
                            records: List[Dict[str,
                                               Any]]) -> bool:
        """判断规则是否适用"""
        # 检查模块适用性
        if rule.applicable_modules and module_name not in rule.applicable_modules:
            return False

        # 检查字段适用性
        if rule.applicable_fields and records:
            sample_record = records[0] if records else {}
            has_applicable_field = any(
                field in sample_record for field in rule.applicable_fields
            )
            if not has_applicable_field:
                return False

        return True

    # 完整性检查函数

    def _check_required_fields(self,
                               records: List[Dict[str,
                                                  Any]],
                               rule: QualityRule,
                               context: Dict[str,
                                             Any]) -> List[QualityIssue]:
        """检查必填字段"""
        issues = []
        required_fields = rule.applicable_fields or []

        for i, record in enumerate(records):
            for field in required_fields:
                if field in record:
                    value = record[field]
                    if value is None or value == "" or (
                            isinstance(value, str) and value.strip() == ""):
                        issues.append(QualityIssue(
                            rule_id=rule.rule_id,
                            dimension=rule.dimension,
                            severity=rule.severity,
                            description=f"必填字段 {field} 为空",
                            field_name=field,
                            record_index=i,
                            current_value=value,
                            suggestion=f"请填写 {field} 字段的值"
                        ))

        return issues

    def _check_important_fields(self,
                                records: List[Dict[str,
                                                   Any]],
                                rule: QualityRule,
                                context: Dict[str,
                                              Any]) -> List[QualityIssue]:
        """检查重要字段完整性"""
        issues = []
        important_fields = rule.applicable_fields or []

        for i, record in enumerate(records):
            empty_count = 0
            for field in important_fields:
                if field not in record or record[field] is None or record[field] == "":
                    empty_count += 1

            # 如果超过一半的重要字段为空，标记为问题
            if empty_count > len(important_fields) / 2:
                issues.append(QualityIssue(
                    rule_id=rule.rule_id,
                    dimension=rule.dimension,
                    severity=rule.severity,
                    description=f"记录中有 {empty_count} 个重要字段为空",
                    record_index=i,
                    suggestion="请检查并填写缺失的重要字段"
                ))

        return issues

    # 一致性检查函数

    def _check_quantity_consistency(self,
                                    records: List[Dict[str,
                                                       Any]],
                                    rule: QualityRule,
                                    context: Dict[str,
                                                  Any]) -> List[QualityIssue]:
        """检查数量一致性"""
        issues = []

        for i, record in enumerate(records):
            # 检查数量字段是否为负数
            for field in rule.applicable_fields:
                if field in record:
                    try:
                        value = float(record[field])
                        if value < 0:
                            issues.append(QualityIssue(
                                rule_id=rule.rule_id,
                                dimension=rule.dimension,
                                severity=rule.severity,
                                description=f"数量字段 {field} 不能为负数",
                                field_name=field,
                                record_index=i,
                                current_value=value,
                                suggestion="请检查数量字段的值是否正确"
                            ))
                    except (ValueError, TypeError):
                        pass

        return issues

    def _check_date_consistency(self,
                                records: List[Dict[str,
                                                   Any]],
                                rule: QualityRule,
                                context: Dict[str,
                                              Any]) -> List[QualityIssue]:
        """检查日期一致性"""
        issues = []

        for i, record in enumerate(records):
            # 检查开始日期和结束日期的逻辑关系
            if 'start_date' in record and 'end_date' in record:
                try:
                    start_date = self._parse_date(record['start_date'])
                    end_date = self._parse_date(record['end_date'])

                    if start_date and end_date and start_date > end_date:
                        issues.append(QualityIssue(
                            rule_id=rule.rule_id,
                            dimension=rule.dimension,
                            severity=rule.severity,
                            description="开始日期不能晚于结束日期",
                            record_index=i,
                            current_value=f"开始:{start_date}, 结束:{end_date}",
                            suggestion="请检查日期字段的值是否正确"
                        ))
                except Exception:
                    pass

        return issues

    # 准确性检查函数

    def _check_numeric_accuracy(self,
                                records: List[Dict[str,
                                                   Any]],
                                rule: QualityRule,
                                context: Dict[str,
                                              Any]) -> List[QualityIssue]:
        """检查数字精度和范围"""
        issues = []

        for i, record in enumerate(records):
            for field in rule.applicable_fields:
                if field in record:
                    try:
                        value = float(record[field])

                        # 检查是否为异常大的数值
                        if abs(value) > 1e10:
                            issues.append(QualityIssue(
                                rule_id=rule.rule_id,
                                dimension=rule.dimension,
                                severity=rule.severity,
                                description=f"数值 {field} 异常大",
                                field_name=field,
                                record_index=i,
                                current_value=value,
                                suggestion="请检查数值是否正确"
                            ))

                        # 检查精度是否过高（小数位数过多）
                        str_value = str(record[field])
                        if '.' in str_value:
                            decimal_places = len(str_value.split('.')[1])
                            if decimal_places > 6:
                                issues.append(
                                    QualityIssue(
                                        rule_id=rule.rule_id,
                                        dimension=rule.dimension,
                                        severity=SeverityLevel.LOW,
                                        description=f"数值 {field} 精度过高（{decimal_places}位小数）",
                                        field_name=field,
                                        record_index=i,
                                        current_value=value,
                                        suggestion="建议保留合理的小数位数"))
                    except (ValueError, TypeError):
                        issues.append(QualityIssue(
                            rule_id=rule.rule_id,
                            dimension=rule.dimension,
                            severity=rule.severity,
                            description=f"字段 {field} 不是有效数字",
                            field_name=field,
                            record_index=i,
                            current_value=record[field],
                            suggestion="请确保字段值为有效数字"
                        ))

        return issues

    def _check_code_format(self,
                           records: List[Dict[str,
                                              Any]],
                           rule: QualityRule,
                           context: Dict[str,
                                         Any]) -> List[QualityIssue]:
        """检查编码格式"""
        issues = []

        # 定义编码格式模式
        code_patterns = {
            'material_code': r'^[A-Z0-9]{6,20}$',
            'customer_code': r'^[A-Z0-9]{4,15}$',
            'supplier_code': r'^[A-Z0-9]{4,15}$'
        }

        for i, record in enumerate(records):
            for field in rule.applicable_fields:
                if field in record and record[field]:
                    value = str(record[field]).strip()
                    pattern = code_patterns.get(field)

                    if pattern and not re.match(pattern, value):
                        issues.append(QualityIssue(
                            rule_id=rule.rule_id,
                            dimension=rule.dimension,
                            severity=rule.severity,
                            description=f"编码 {field} 格式不正确",
                            field_name=field,
                            record_index=i,
                            current_value=value,
                            suggestion=f"请确保 {field} 符合规定格式"
                        ))

        return issues

    # 有效性检查函数

    def _check_value_validity(self,
                              records: List[Dict[str,
                                                 Any]],
                              rule: QualityRule,
                              context: Dict[str,
                                            Any]) -> List[QualityIssue]:
        """检查值的有效性"""
        issues = []

        # 定义有效范围
        valid_ranges = {
            'quantity': (0, 1000000),
            'unit_price': (0, 100000)
        }

        for i, record in enumerate(records):
            for field in rule.applicable_fields:
                if field in record:
                    try:
                        value = float(record[field])
                        valid_range = valid_ranges.get(field)

                        if valid_range:
                            min_val, max_val = valid_range
                            if value < min_val or value > max_val:
                                issues.append(
                                    QualityIssue(
                                        rule_id=rule.rule_id,
                                        dimension=rule.dimension,
                                        severity=rule.severity,
                                        description=f"字段 {field} 超出有效范围 [{min_val}, {max_val}]",
                                        field_name=field,
                                        record_index=i,
                                        current_value=value,
                                        suggestion=f"请确保 {field} 在有效范围内"))
                    except (ValueError, TypeError):
                        pass

        return issues

    def _check_date_validity(self,
                             records: List[Dict[str,
                                                Any]],
                             rule: QualityRule,
                             context: Dict[str,
                                           Any]) -> List[QualityIssue]:
        """检查日期有效性"""
        issues = []

        current_date = datetime.now()
        min_date = datetime(1900, 1, 1)
        max_date = datetime(2100, 12, 31)

        for i, record in enumerate(records):
            for field in rule.applicable_fields:
                if field in record and record[field]:
                    try:
                        date_value = self._parse_date(record[field])
                        if date_value:
                            if date_value < min_date or date_value > max_date:
                                issues.append(QualityIssue(
                                    rule_id=rule.rule_id,
                                    dimension=rule.dimension,
                                    severity=rule.severity,
                                    description=f"日期 {field} 超出合理范围",
                                    field_name=field,
                                    record_index=i,
                                    current_value=date_value,
                                    suggestion="请检查日期是否正确"
                                ))
                        else:
                            issues.append(QualityIssue(
                                rule_id=rule.rule_id,
                                dimension=rule.dimension,
                                severity=rule.severity,
                                description=f"日期 {field} 格式无效",
                                field_name=field,
                                record_index=i,
                                current_value=record[field],
                                suggestion="请使用正确的日期格式"
                            ))
                    except Exception:
                        pass

        return issues

    # 时效性检查函数

    def _check_data_freshness(self,
                              records: List[Dict[str,
                                                 Any]],
                              rule: QualityRule,
                              context: Dict[str,
                                            Any]) -> List[QualityIssue]:
        """检查数据新鲜度"""
        issues = []
        current_date = datetime.now()

        for i, record in enumerate(records):
            for field in rule.applicable_fields:
                if field in record and record[field]:
                    try:
                        date_value = self._parse_date(record[field])
                        if date_value:
                            days_old = (current_date - date_value).days

                            # 超过30天的数据视为不够新鲜
                            if days_old > 30:
                                issues.append(
                                    QualityIssue(
                                        rule_id=rule.rule_id,
                                        dimension=rule.dimension,
                                        severity=SeverityLevel.LOW,
                                        description=f"数据 {field} 已过时 ({days_old} 天)",
                                        field_name=field,
                                        record_index=i,
                                        current_value=date_value,
                                        suggestion="建议更新数据以保持时效性"))
                    except Exception:
                        pass

        return issues

    # 唯一性检查函数

    def _check_uniqueness(self,
                          records: List[Dict[str,
                                             Any]],
                          rule: QualityRule,
                          context: Dict[str,
                                        Any]) -> List[QualityIssue]:
        """检查唯一性"""
        issues = []

        for field in rule.applicable_fields:
            if not records:
                continue

            # 收集字段值
            field_values = []
            for i, record in enumerate(records):
                if field in record and record[field]:
                    field_values.append((i, record[field]))

            # 检查重复
            seen_values = {}
            for index, value in field_values:
                if value in seen_values:
                    issues.append(QualityIssue(
                        rule_id=rule.rule_id,
                        dimension=rule.dimension,
                        severity=rule.severity,
                        description=f"字段 {field} 值重复",
                        field_name=field,
                        record_index=index,
                        current_value=value,
                        suggestion=f"请确保 {field} 字段值唯一"
                    ))
                else:
                    seen_values[value] = index

        return issues

    def _parse_date(self, date_value: Any) -> Optional[datetime]:
        """解析日期值"""
        if isinstance(date_value, datetime):
            return date_value

        if isinstance(date_value, str):
            date_formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%Y/%m/%d',
                '%Y年%m月%d日'
            ]

            for fmt in date_formats:
                try:
                    return datetime.strptime(date_value.strip(), fmt)
                except ValueError:
                    continue

        return None

    def _calculate_dimension_score(
            self,
            issues: List[QualityIssue],
            record_count: int) -> float:
        """计算维度得分"""
        if record_count == 0:
            return 1.0

        # 根据问题严重性计算扣分
        penalty = 0
        for issue in issues:
            if issue.severity == SeverityLevel.CRITICAL:
                penalty += 0.5
            elif issue.severity == SeverityLevel.HIGH:
                penalty += 0.3
            elif issue.severity == SeverityLevel.MEDIUM:
                penalty += 0.2
            elif issue.severity == SeverityLevel.LOW:
                penalty += 0.1
            else:
                penalty += 0.05

        # 计算得分
        score = max(0, 1.0 - penalty / record_count)
        return min(1.0, score)

    def _calculate_overall_score(
            self, dimension_scores: Dict[str, float], issues: List[QualityIssue]) -> float:
        """计算总体质量得分"""
        if not dimension_scores:
            return 0.5

        # 加权平均维度得分
        weights = {
            QualityDimension.COMPLETENESS.value: 0.25,
            QualityDimension.CONSISTENCY.value: 0.20,
            QualityDimension.ACCURACY.value: 0.25,
            QualityDimension.VALIDITY.value: 0.15,
            QualityDimension.TIMELINESS.value: 0.10,
            QualityDimension.UNIQUENESS.value: 0.05
        }

        weighted_score = 0
        total_weight = 0

        for dimension, score in dimension_scores.items():
            weight = weights.get(dimension, 0.1)
            weighted_score += score * weight
            total_weight += weight

        overall_score = weighted_score / total_weight if total_weight > 0 else 0.5

        # 根据严重问题进一步调整
        critical_issues = sum(
            1 for issue in issues if issue.severity == SeverityLevel.CRITICAL)
        if critical_issues > 0:
            overall_score *= (1 - min(0.5, critical_issues * 0.1))

        return max(0.0, min(1.0, overall_score))

    def _generate_recommendations(self,
                                  issues: List[QualityIssue],
                                  dimension_scores: Dict[str,
                                                         float]) -> List[str]:
        """生成改进建议"""
        recommendations = []

        # 根据维度得分生成建议
        for dimension, score in dimension_scores.items():
            if score < 0.7:
                if dimension == QualityDimension.COMPLETENESS.value:
                    recommendations.append("建议完善数据录入，确保必填字段完整")
                elif dimension == QualityDimension.CONSISTENCY.value:
                    recommendations.append("建议加强数据一致性检查，特别是关联字段")
                elif dimension == QualityDimension.ACCURACY.value:
                    recommendations.append("建议验证数据准确性，检查数值和格式")
                elif dimension == QualityDimension.VALIDITY.value:
                    recommendations.append("建议检查数据有效性，确保值在合理范围内")
                elif dimension == QualityDimension.TIMELINESS.value:
                    recommendations.append("建议更新数据，保持时效性")
                elif dimension == QualityDimension.UNIQUENESS.value:
                    recommendations.append("建议检查重复数据，确保关键字段唯一")

        # 根据问题类型生成建议
        severity_counts = {}
        for issue in issues:
            severity_counts[issue.severity.value] = severity_counts.get(
                issue.severity.value, 0) + 1

        if severity_counts.get(SeverityLevel.CRITICAL.value, 0) > 0:
            recommendations.append("发现严重质量问题，建议立即处理")

        if len(issues) > 10:
            recommendations.append("质量问题较多，建议系统性改进数据质量管理")

        return list(set(recommendations))  # 去重

    def _update_average_quality_score(self, score: float):
        """更新平均质量得分"""
        current_avg = self.inspection_stats['average_quality_score']
        total_inspections = self.inspection_stats['total_inspections']

        new_avg = ((current_avg * (total_inspections - 1)) +
                   score) / total_inspections
        self.inspection_stats['average_quality_score'] = new_avg

    def _create_error_report(
            self,
            error_message: str,
            start_time: datetime) -> QualityReport:
        """创建错误报告"""
        inspection_time = (datetime.now() - start_time).total_seconds()

        return QualityReport(
            overall_score=0.0,
            dimension_scores={dim.value: 0.0 for dim in QualityDimension},
            total_records=0,
            issues_found=1,
            issues_by_severity={SeverityLevel.CRITICAL.value: 1},
            issues=[QualityIssue(
                rule_id="ERROR",
                dimension=QualityDimension.VALIDITY,
                severity=SeverityLevel.CRITICAL,
                description=f"质量检查异常: {error_message}"
            )],
            recommendations=["请检查数据格式和质量检查配置"],
            inspection_time=inspection_time
        )

    def get_inspection_statistics(self) -> Dict[str, Any]:
        """获取检查统计信息"""
        return {
            'total_inspections': self.inspection_stats['total_inspections'],
            'total_records_inspected': self.inspection_stats['total_records_inspected'],
            'total_issues_found': self.inspection_stats['total_issues_found'],
            'average_quality_score': self.inspection_stats['average_quality_score'],
            'rules_count': len(
                self.quality_rules),
            'dimensions_covered': len(QualityDimension)}

    def add_quality_rule(self, rule: QualityRule):
        """添加质量检查规则"""
        self.quality_rules.append(rule)

    def remove_quality_rule(self, rule_id: str):
        """移除质量检查规则"""
        self.quality_rules = [
            rule for rule in self.quality_rules if rule.rule_id != rule_id]


def create_data_quality_inspector() -> DataQualityInspector:
    """创建数据质量检查器实例"""
    return DataQualityInspector()
