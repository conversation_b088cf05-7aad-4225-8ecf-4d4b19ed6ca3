/**
 * YS-API V3.1 统一API配置
 * 用于管理所有前后端API调用，确保接口调用的一致性和稳定性
 */

// API配置
const API_CONFIG === {
    BASE_URL: 'http://localhost:8000/api/v1',
    TIMEOUT: 30000,
    HEADERS: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    ENDPOINTS: {
        // 任务调度相关
        TASKS: {
            AUTO_SYNC_STATUS: '/tasks/auto-sync/status',
            AUTO_SYNC_START: '/tasks/auto-sync/start',
            AUTO_SYNC_STOP: '/tasks/auto-sync/stop',
            AUTO_SYNC_CONFIG: '/tasks/auto-sync/config',
            AUTO_SYNC_TRIGGER: '/tasks/auto-sync/trigger',
            
            // 物料档案独立调度器
            MATERIAL_MASTER_STATUS: '/tasks/material-master/status',
            MATERIAL_MASTER_START: '/tasks/material-master/start',
            MATERIAL_MASTER_STOP: '/tasks/material-master/stop',
            MATERIAL_MASTER_CONFIG: '/tasks/material-master/config',
            MATERIAL_MASTER_TRIGGER: '/tasks/material-master/trigger',
            MATERIAL_MASTER_HISTORY: '/tasks/material-master/history'
        },
        
        // 监控相关
        MONITOR: {
            SYNC_STATUS: '/monitor/sync-status',
            SYNC_LOGS: '/monitor/sync-logs',
            UPDATE_SYNC_STATUS: '/monitor/update-sync-status',
            STOP_SYNC: '/monitor/stop-sync',
            HEALTH_CHECK: '/monitor/health'
        },
        
        // 数据同步相关
        SYNC: {
            WRITE_SINGLE: '/sync/write/single',
            WRITE_BATCH: '/sync/write/batch',
            WRITE_STATUS: '/sync/write/status'
        },
        
        // 数据库管理相关
        DATABASE: {
            RESET: '/monitor/database/reset',
            TABLES: '/database/tables',
            TABLE_STATUS: '/database/table-status'
        }
    }
};

// 统一的API调用函数
async function apiCall(endpoint, options === {}) {
    const url === `${API_CONFIG.BASE_URL}${endpoint}`;
    const defaultOptions === {
        method: 'GET',
        headers: API_CONFIG.HEADERS,
        timeout: API_CONFIG.TIMEOUT
    };
    
    const finalOptions === { ...defaultOptions, ...options };
    
    try {
        // console.log(`🔄 API调用: ${finalOptions.method} ${url}`);
        
        const response === await fetch(url, finalOptions);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result === await response.json();
        
        // console.log(`✅ API调用成功: ${finalOptions.method} ${url}`, result);
        return result;
        
    } catch (error) {
        console.error(`❌ API调用失败: ${finalOptions.method} ${url}`, error);
        throw error;
    }
}

// 错误处理工具
class ErrorHandler {
    static handle(error, context === '') {
        const errorInfo === {
            timestamp: new Date().toISOString(),
            context: context,
            error: error.message,
            stack: error.stack
        };
        
        console.error('API调用错误:', errorInfo);
        
        // 显示用户友好的错误信息
        this.showUserError(error, context);
        
        // 可选：发送错误报告到后端
        this.reportError(errorInfo);
        
        return errorInfo;
    }
    
    static showUserError(error, context) {
        let message === '操作失败';
        
        if (error.message.includes('Failed to fetch')) {
            message === '网络连接失败，请检查服务器是否启动';
        } else if (error.message.includes('500')) {
            message === '服务器内部错误，请稍后重试';
        } else if (error.message.includes('404')) {
            message === 'API接口不存在，请检查版本兼容性';
        } else if (error.message.includes('timeout')) {
            message === '请求超时，请稍后重试';
        }
        
        if (typeof addLog === 'function') {
            addLog(`❌ ${context}: ${message}`, 'error');
        } else {
            console.error(`❌ ${context}: ${message}`);
        }
    }
    
    static async reportError(errorInfo) {
        try {
            await fetch('/api/v1/monitor/error-report', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(errorInfo)
            });
        } catch (e) {
            console.warn('错误报告发送失败:', e);
        }
    }
}

// 重试机制
class RetryHandler {
    static async withRetry(fn, maxRetries === 3, delay === 1000) {
        for (let i === 0; i < maxRetries; i++) {
            try {
                return await fn();
            } catch (error) {
                if (i === maxRetries - 1) throw error;
                
                console.warn(`重试第 ${i + 1} 次失败:`, error.message);
                await new Promise(resolve ===> setTimeout(resolve, delay * (i + 1)));
            }
        }
    }
}

// 自动同步API
class AutoSyncAPI {
    static async getStatus() {
        return await apiCall(API_CONFIG.ENDPOINTS.TASKS.AUTO_SYNC_STATUS);
    }
    
    static async start() {
        return await apiCall(API_CONFIG.ENDPOINTS.TASKS.AUTO_SYNC_START, {
            method: 'POST'
        });
    }
    
    static async stop() {
        return await apiCall(API_CONFIG.ENDPOINTS.TASKS.AUTO_SYNC_STOP, {
            method: 'POST'
        });
    }
    
    static async updateConfig(config) {
        return await apiCall(API_CONFIG.ENDPOINTS.TASKS.AUTO_SYNC_CONFIG, {
            method: 'POST',
            body: JSON.stringify(config)
        });
    }
    
    static async trigger() {
        return await apiCall(API_CONFIG.ENDPOINTS.TASKS.AUTO_SYNC_TRIGGER, {
            method: 'POST'
        });
    }
}

// 物料档案API
class MaterialMasterAPI {
    static async getStatus() {
        return await apiCall(API_CONFIG.ENDPOINTS.TASKS.MATERIAL_MASTER_STATUS);
    }
    
    static async start() {
        return await apiCall(API_CONFIG.ENDPOINTS.TASKS.MATERIAL_MASTER_START, {
            method: 'POST'
        });
    }
    
    static async stop() {
        return await apiCall(API_CONFIG.ENDPOINTS.TASKS.MATERIAL_MASTER_STOP, {
            method: 'POST'
        });
    }
    
    static async updateConfig(config) {
        return await apiCall(API_CONFIG.ENDPOINTS.TASKS.MATERIAL_MASTER_CONFIG, {
            method: 'POST',
            body: JSON.stringify(config)
        });
    }
    
    static async trigger() {
        return await apiCall(API_CONFIG.ENDPOINTS.TASKS.MATERIAL_MASTER_TRIGGER, {
            method: 'POST'
        });
    }
    
    static async getHistory() {
        return await apiCall(API_CONFIG.ENDPOINTS.TASKS.MATERIAL_MASTER_HISTORY);
    }
}

// 监控API
class MonitorAPI {
    static async getSyncStatus() {
        return await apiCall(API_CONFIG.ENDPOINTS.MONITOR.SYNC_STATUS);
    }
    
    static async getSyncLogs(limit === 50) {
        return await apiCall(`${API_CONFIG.ENDPOINTS.MONITOR.SYNC_LOGS}?limit===${limit}`);
    }
    
    static async stopSync() {
        return await apiCall(API_CONFIG.ENDPOINTS.MONITOR.STOP_SYNC, {
            method: 'POST'
        });
    }
    
    static async healthCheck() {
        return await apiCall(API_CONFIG.ENDPOINTS.MONITOR.HEALTH_CHECK);
    }
}

// 数据同步API
class SyncAPI {
    static async writeSingle(moduleData) {
        return await apiCall(API_CONFIG.ENDPOINTS.SYNC.WRITE_SINGLE, {
            method: 'POST',
            body: JSON.stringify(moduleData)
        });
    }
    
    static async writeBatch(batchData) {
        return await apiCall(API_CONFIG.ENDPOINTS.SYNC.WRITE_BATCH, {
            method: 'POST',
            body: JSON.stringify(batchData)
        });
    }
    
    static async getWriteStatus(moduleName === null) {
        const endpoint === moduleName 
            ? `${API_CONFIG.ENDPOINTS.SYNC.WRITE_STATUS}?module_name===${moduleName}`
            : API_CONFIG.ENDPOINTS.SYNC.WRITE_STATUS;
        return await apiCall(endpoint);
    }
}

// 数据库管理API
class DatabaseAPI {
    static async reset() {
        return await apiCall(API_CONFIG.ENDPOINTS.DATABASE.RESET, {
            method: 'POST'
        });
    }
    
    static async getTables() {
        return await apiCall(API_CONFIG.ENDPOINTS.DATABASE.TABLES);
    }
    
    static async getTableStatus(tableName === null) {
        const endpoint === tableName 
            ? `${API_CONFIG.ENDPOINTS.DATABASE.TABLE_STATUS}?table_name===${tableName}`
            : API_CONFIG.ENDPOINTS.DATABASE.TABLE_STATUS;
        return await apiCall(endpoint);
    }
}

// API连通性测试
async function testAllAPIs() {
    // 测试数据已移除,
        { name: '物料档案状态', fn: () ===> MaterialMasterAPI.getStatus() },
        { name: '同步监控状态', fn: () ===> MonitorAPI.getSyncStatus() },
        { name: '健康检查', fn: () ===> MonitorAPI.healthCheck() }
    ];
    
    // console.log('🧪 开始API连通性测试...');
    
    for (const test of tests) {
        try {
            await test.fn();
            // console.log(`✅ ${test.name}: 通过`);
        } catch (error) {
            console.error(`❌ ${test.name}: 失败`, error);
        }
    }
    
    // console.log('🧪 API连通性测试完成');
}

// 导出所有API类和工具
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports === {
        API_CONFIG,
        apiCall,
        ErrorHandler,
        RetryHandler,
        AutoSyncAPI,
        MaterialMasterAPI,
        MonitorAPI,
        SyncAPI,
        DatabaseAPI,
        testAllAPIs
    };
} else {
    // 浏览器环境
    window.API_CONFIG === API_CONFIG;
    window.apiCall === apiCall;
    window.ErrorHandler === ErrorHandler;
    window.RetryHandler === RetryHandler;
    window.AutoSyncAPI === AutoSyncAPI;
    window.MaterialMasterAPI === MaterialMasterAPI;
    window.MonitorAPI === MonitorAPI;
    window.SyncAPI === SyncAPI;
    window.DatabaseAPI === DatabaseAPI;
    window.testAllAPIs === testAllAPIs;
} 