[{"name": "production_order", "display_name": "生产订单", "enabled": true, "api_endpoint": "/yonbip/mfg/productionorder/list", "table_name": "production_order", "md_file": "生产订单列表查询.md"}, {"name": "material_master", "display_name": "物料档案", "enabled": true, "api_endpoint": "/yonbip/digitalModel/product/listproductbycondition", "table_name": "material_master", "md_file": "物料档案分页查询-特征.md"}, {"name": "purchase_order", "display_name": "采购订单", "enabled": true, "api_endpoint": "/yonbip/scm/purchaseorder/list", "table_name": "purchase_order", "md_file": "采购订单列表查询.md"}, {"name": "sales_order", "display_name": "销售订单", "enabled": true, "api_endpoint": "/yonbip/sd/voucherorder/list", "table_name": "sales_order", "md_file": "销售订单列表查询.md"}, {"name": "inventory", "display_name": "现存量", "enabled": false, "api_endpoint": "/yonbip/scm/stock/QueryCurrentStocksByCondition", "table_name": "inventory", "md_file": "现存量查询.md"}, {"name": "purchase_receipt", "display_name": "采购入库", "enabled": true, "api_endpoint": "/yonbip/scm/purinrecord/list", "table_name": "purchase_receipt", "md_file": "采购入库列表查询.md"}, {"name": "sales_out", "display_name": "销售出库", "enabled": true, "api_endpoint": "/yonbip/scm/salesout/list", "table_name": "sales_out", "md_file": "销售出库列表查询.md"}, {"name": "product_receipt", "display_name": "产品入库", "enabled": true, "api_endpoint": "/yonbip/scm/storeprorecord/list", "table_name": "product_receipt", "md_file": "产品入库单列表.md"}, {"name": "materialout", "display_name": "材料出库", "enabled": true, "api_endpoint": "/yonbip/scm/materialout/list", "table_name": "materialout", "md_file": "材料出库单列表查询.md"}, {"name": "subcontract_order", "display_name": "委外订单", "enabled": true, "api_endpoint": "/yonbip/mfg/subcontractorder/list", "table_name": "subcontract_order", "md_file": "委外订单列表.md"}, {"name": "subcontract_receipt", "display_name": "委外入库", "enabled": true, "api_endpoint": "/yonbip/scm/osminrecord/list", "table_name": "subcontract_receipt", "md_file": "委外入库列表查询.md"}, {"name": "subcontract_requisition", "display_name": "委外申请", "enabled": true, "api_endpoint": "/yonbip/mfg/subcontractrequisition/list", "table_name": "subcontract_requisition", "md_file": "委外申请列表查询.md"}, {"name": "applyorder", "display_name": "请购单", "enabled": true, "api_endpoint": "/yonbip/scm/applyorder/list", "table_name": "applyorder", "md_file": "请购单列表查询.md"}, {"name": "inventory_report", "display_name": "现存量报表", "enabled": true, "api_endpoint": "/yonbip/scm/stockanalysis/list", "table_name": "inventory_report", "md_file": "现存量报表查询.md"}, {"name": "requirements_planning", "display_name": "需求计划", "enabled": true, "api_endpoint": "/yonbip/mfg/requirementsplanning/getPlanOrderList", "table_name": "requirements_planning", "md_file": "需求计划.md"}, {"name": "business_log", "display_name": "业务日志", "enabled": true, "api_endpoint": "/yonbip/digitalModel/log-pub/business/rest/query", "table_name": "业务日志", "md_file": "业务日志.md"}]