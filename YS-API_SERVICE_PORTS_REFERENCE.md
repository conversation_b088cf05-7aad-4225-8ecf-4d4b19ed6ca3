# 📊 YS-API 服务端口状态文档

## 🎯 项目端口分配概览

**项目名称**: YS-API V3.0  
**端口段**: 8010-8019 (专属分配)  
**更新时间**: 2024年12月  
**配置文件**: `.ports.json`

---

## 🌐 服务端口映射表

| 服务名称 | 端口 | 状态 | 描述 | 访问地址 |
|---------|------|------|------|----------|
| **前端界面** | 8010 | 🟢 可用 | YS-API前端界面 | http://ysapi.local:8010 |
| **后端API** | 8011 | 🟢 可用 | YS-API后端服务 | http://api.ysapi.local:8011 |
| **数据库管理** | 8012 | 🟢 可用 | SQLite Web管理界面 | http://db.ysapi.local:8012 |
| **系统监控** | 8013 | 🟢 可用 | 系统监控面板 | http://monitor.ysapi.local:8013 |
| **API文档** | 8014 | 🟢 可用 | API文档服务 | http://docs.ysapi.local:8014 |
| **测试环境** | 8015 | 🟢 可用 | 测试环境服务 | http://test.ysapi.local:8015 |
| **管理后台** | 8016 | 🟢 可用 | 管理后台 | http://admin.ysapi.local:8016 |
| **WebSocket** | 8017 | 🟢 可用 | WebSocket实时通信 | ws://ws.ysapi.local:8017 |
| **预留1** | 8018 | 🟢 可用 | 预留扩展端口 | http://reserved1.ysapi.local:8018 |
| **预留2** | 8019 | 🟢 可用 | 预留扩展端口 | http://reserved2.ysapi.local:8019 |

---

## 🚀 快速访问链接

### 主要服务
- **🌐 前端首页**: [http://ysapi.local:8010](http://ysapi.local:8010)
- **🔌 API服务**: [http://api.ysapi.local:8011](http://api.ysapi.local:8011)
- **📊 监控面板**: [http://monitor.ysapi.local:8013](http://monitor.ysapi.local:8013)
- **📚 API文档**: [http://docs.ysapi.local:8014](http://docs.ysapi.local:8014)

### 管理工具
- **🗄️ 数据库**: [http://db.ysapi.local:8012](http://db.ysapi.local:8012)
- **⚙️ 管理后台**: [http://admin.ysapi.local:8016](http://admin.ysapi.local:8016)
- **🧪 测试环境**: [http://test.ysapi.local:8015](http://test.ysapi.local:8015)

### 实时通信
- **📡 WebSocket**: [ws://ws.ysapi.local:8017](ws://ws.ysapi.local:8017)

---

## 🛠️ 端口管理操作

### 查看端口状态
```bash
# 检查所有服务端口状态
python port_locker.py status

# 检查特定端口是否被占用
netstat -an | findstr :8010
```

### 启动服务
```bash
# 启动前端服务 (8010端口)
python port_locker.py start frontend

# 启动后端服务 (8011端口)  
python port_locker.py start backend

# 使用预设脚本
start_frontend_locked.bat
start_backend_locked.bat
```

### 端口锁定管理
```bash
# 锁定特定服务端口
python port_locker.py lock frontend

# 显示hosts配置说明
python port_locker.py setup-hosts
```

---

## 🌐 本地域名配置

为了使用友好的域名访问，需要配置系统hosts文件：

### Windows配置
**文件位置**: `C:\Windows\System32\drivers\etc\hosts`

### Linux/Mac配置  
**文件位置**: `/etc/hosts`

### 添加内容
```
# YS-API 本地开发域名
127.0.0.1 ysapi.local
127.0.0.1 api.ysapi.local
127.0.0.1 db.ysapi.local
127.0.0.1 monitor.ysapi.local
127.0.0.1 docs.ysapi.local
127.0.0.1 test.ysapi.local
127.0.0.1 admin.ysapi.local
127.0.0.1 ws.ysapi.local
```

---

## 🔒 端口安全策略

### 端口锁定机制
- **严格模式**: 启用，冲突时自动清理占用进程
- **随机端口**: 禁用，严格使用配置端口
- **冲突处理**: 自动终止占用进程并重启服务

### 访问控制
- **内网访问**: 仅限本机和内网访问
- **端口扫描**: 监控异常端口访问
- **权限管理**: 管理后台需要认证

---

## 📈 端口使用统计

### 当前状态
- **总端口数**: 10个 (8010-8019)
- **已使用**: 8个
- **预留**: 2个 (8018-8019)
- **利用率**: 80%

### 扩展计划
- **微服务拆分**: 可使用预留端口
- **新功能模块**: 8018-8019端口
- **测试环境**: 独立端口分配

---

## 🔍 故障排除

### 常见问题
1. **端口被占用**
   ```bash
   # 查找占用进程
   netstat -ano | findstr :8010
   # 终止进程
   taskkill /PID <进程ID> /F
   ```

2. **域名访问失败**
   - 检查hosts文件配置
   - 确认DNS缓存清理
   - 验证服务是否正常启动

3. **服务启动失败**
   - 检查端口配置文件
   - 验证权限设置
   - 查看错误日志

### 支持联系
- **配置文件**: `.ports.json`
- **管理工具**: `port_locker.py`
- **文档目录**: `/docs`

---

## 📅 更新日志

| 日期 | 版本 | 变更说明 |
|------|------|----------|
| 2024-12 | v1.0 | 初始端口分配，8010-8019段 |
| 2024-12 | v1.1 | 修正端口冲突问题，避开热门端口 |

---

*最后更新: 2024年12月*  
*维护者: YS-API团队*  
*状态: ✅ 生产就绪*
