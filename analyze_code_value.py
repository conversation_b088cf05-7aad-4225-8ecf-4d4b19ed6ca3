#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YS-API V3.0 代码价值评估与核心重构分析器
=====================================

基于依赖关系分析，识别核心代码和无价值代码，制定重构策略
"""

import json
import logging
from datetime import datetime
from pathlib import Path
import re


class CodeValueAnalyzer:
    """代码价值评估器"""

    def __init__(self, workspace_root="d:\\OneDrive\\Desktop\\YS-API程序\\v3"):
        self.workspace_root = Path(workspace_root)
        self.logger = self._setup_logger()

        # 价值评估权重配置
        self.weights = {
            "dependency_count": 0.25,  # 依赖数量权重
            "is_core_service": 0.3,  # 核心服务权重
            "api_endpoint": 0.2,  # API端点权重
            "config_file": 0.1,  # 配置文件权重
            "entry_point": 0.15,  # 入口点权重
        }

        # 核心模式匹配
        self.core_patterns = {
            "api_endpoints": [r"app.*api.*", r".*router.*", r".*endpoint.*"],
            "services": [r".*service.*", r".*manager.*"],
            "database": [r".*database.*", r".*db.*", r".*model.*"],
            "core_logic": [r".*core.*", r".*main.*"],
            "config": [r".*config.*", r".*setting.*"],
        }

        # 垃圾代码模式
        self.junk_patterns = [
            r".*test.*",
            r".*tmp.*",
            r".*temp.*",
            r".*backup.*",
            r".*copy.*",
            r".*old.*",
            r".*unused.*",
            r".*deprecated.*",
            r".*example.*",
            r".*demo.*",
            r".*sample.*",
        ]

    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger("CodeValueAnalyzer")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def load_dependency_data(self):
        """加载依赖关系数据"""
        try:
            dep_file = self.workspace_root / "dependency_analysis.json"
            if not dep_file.exists():
                self.logger.error(f"依赖分析文件不存在: {dep_file}")
                return None

            with open(dep_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"加载依赖数据失败: {e}")
            return None

    def calculate_file_value_score(self, file_path, dep_count, total_files):
        """计算文件价值得分"""
        score = 0

        # 1. 依赖数量评分 (高依赖=高价值)
        dependency_score = min(dep_count / 20, 1.0)  # 最大20个依赖为满分
        score += dependency_score * self.weights["dependency_count"]

        # 2. 核心服务评分
        is_core = self._is_core_service(file_path)
        score += (1.0 if is_core else 0.0) * self.weights["is_core_service"]

        # 3. API端点评分
        is_api = self._is_api_endpoint(file_path)
        score += (1.0 if is_api else 0.0) * self.weights["api_endpoint"]

        # 4. 配置文件评分
        is_config = self._is_config_file(file_path)
        score += (1.0 if is_config else 0.0) * self.weights["config_file"]

        # 5. 入口点评分
        is_entry = self._is_entry_point(file_path)
        score += (1.0 if is_entry else 0.0) * self.weights["entry_point"]

        # 垃圾代码惩罚
        if self._is_junk_code(file_path):
            score *= 0.1  # 大幅降低垃圾代码得分

        return min(score, 1.0)  # 最大得分1.0

    def _is_core_service(self, file_path):
        """判断是否为核心服务"""
        file_str = str(file_path).lower()
        for pattern in self.core_patterns["services"]:
            if re.search(pattern, file_str):
                return True
        return False

    def _is_api_endpoint(self, file_path):
        """判断是否为API端点"""
        file_str = str(file_path).lower()
        for pattern in self.core_patterns["api_endpoints"]:
            if re.search(pattern, file_str):
                return True
        return False

    def _is_config_file(self, file_path):
        """判断是否为配置文件"""
        file_str = str(file_path).lower()
        for pattern in self.core_patterns["config"]:
            if re.search(pattern, file_str):
                return True
        return file_str.endswith((".ini", ".json", ".yaml", ".yml", ".toml"))

    def _is_entry_point(self, file_path):
        """判断是否为入口点"""
        file_str = str(file_path).lower()
        return any(
            name in file_str
            for name in ["main.py", "start_", "run_", "app.py", "__init__.py"]
        )

    def _is_junk_code(self, file_path):
        """判断是否为垃圾代码"""
        file_str = str(file_path).lower()
        for pattern in self.junk_patterns:
            if re.search(pattern, file_str):
                return True
        return False

    def categorize_files(self, analysis_data):
        """文件分类评估"""
        if not analysis_data:
            return None

        # 检查数据结构 - 可能是dependencies而不是files
        files = analysis_data.get("files", {})
        if not files:
            # 尝试从dependencies获取数据
            deps_data = analysis_data.get("dependencies", {})
            if deps_data:
                # 将dependencies格式转换为files格式
                files = {}
                for file_path, imports in deps_data.items():
                    files[file_path] = {
                        "imports": imports,
                        "type": (
                            "python"
                            if file_path.endswith(".py")
                            else "javascript"
                        ),
                        "internal_dependencies": [],
                    }

        if not files:
            self.logger.warning("没有找到文件数据")
            return {"core": [], "important": [], "optional": [], "junk": []}

        categories = {
            "core": [],  # 核心文件 (score >= 0.7)
            "important": [],  # 重要文件 (0.5 <= score < 0.7)
            "optional": [],  # 可选文件 (0.3 <= score < 0.5)
            "junk": [],  # 垃圾文件 (score < 0.3)
        }

        total_files = len(files)

        for file_path, file_info in files.items():
            dep_count = len(file_info.get("imports", []))
            score = self.calculate_file_value_score(
                file_path, dep_count, total_files
            )

            file_data = {
                "path": file_path,
                "score": score,
                "dependencies": dep_count,
                "type": file_info.get("type", "unknown"),
                "imports": file_info.get("imports", []),
                "internal_deps": file_info.get("internal_dependencies", []),
            }

            if score >= 0.7:
                categories["core"].append(file_data)
            elif score >= 0.5:
                categories["important"].append(file_data)
            elif score >= 0.3:
                categories["optional"].append(file_data)
            else:
                categories["junk"].append(file_data)

        # 按得分排序
        for category in categories.values():
            category.sort(key=lambda x: x["score"], reverse=True)

        return categories

    def generate_refactoring_plan(self, categories):
        """生成重构计划"""
        plan = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_files": sum(len(cat) for cat in categories.values()),
                "core_files": len(categories["core"]),
                "important_files": len(categories["important"]),
                "optional_files": len(categories["optional"]),
                "junk_files": len(categories["junk"]),
            },
            "actions": {
                "keep": [],  # 保留的核心代码
                "refactor": [],  # 需要重构的代码
                "review": [],  # 需要审查的代码
                "delete": [],  # 建议删除的代码
            },
            "priority_order": [],
        }

        # 核心文件 - 保留并优先重构
        for file_data in categories["core"]:
            plan["actions"]["keep"].append(
                {
                    "file": file_data["path"],
                    "reason": f"核心文件 (得分: {file_data['score']:.2f})",
                    "action": "refactor_priority",
                }
            )
            plan["priority_order"].append(
                {
                    "file": file_data["path"],
                    "priority": 1,
                    "score": file_data["score"],
                }
            )

        # 重要文件 - 保留但次要重构
        for file_data in categories["important"]:
            plan["actions"]["refactor"].append(
                {
                    "file": file_data["path"],
                    "reason": f"重要文件 (得分: {file_data['score']:.2f})",
                    "action": "refactor_secondary",
                }
            )
            plan["priority_order"].append(
                {
                    "file": file_data["path"],
                    "priority": 2,
                    "score": file_data["score"],
                }
            )

        # 可选文件 - 审查决定
        for file_data in categories["optional"]:
            plan["actions"]["review"].append(
                {
                    "file": file_data["path"],
                    "reason": f"可选文件 (得分: {file_data['score']:.2f})",
                    "action": "manual_review",
                }
            )

        # 垃圾文件 - 建议删除
        for file_data in categories["junk"]:
            plan["actions"]["delete"].append(
                {
                    "file": file_data["path"],
                    "reason": f"低价值文件 (得分: {file_data['score']:.2f})",
                    "action": "safe_delete",
                }
            )

        # 按优先级排序
        plan["priority_order"].sort(key=lambda x: (x["priority"], -x["score"]))

        return plan

    def save_analysis_report(self, categories, plan):
        """保存分析报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存详细分析
        analysis_file = (
            self.workspace_root / f"code_value_analysis_{timestamp}.json"
        )
        with open(analysis_file, "w", encoding="utf-8") as f:
            json.dump(
                {
                    "categories": categories,
                    "refactoring_plan": plan,
                    "metadata": {
                        "analyzer_version": "1.0",
                        "analysis_time": datetime.now().isoformat(),
                        "workspace": str(self.workspace_root),
                    },
                },
                f,
                indent=2,
                ensure_ascii=False,
            )

        # 生成可读报告
        report_file = (
            self.workspace_root / f"refactoring_report_{timestamp}.md"
        )
        self._generate_markdown_report(categories, plan, report_file)

        return analysis_file, report_file

    def _generate_markdown_report(self, categories, plan, output_file):
        """生成Markdown格式报告"""
        with open(output_file, "w", encoding="utf-8") as f:
            f.write("# YS-API V3.0 代码价值评估与重构计划\n\n")
            f.write(
                f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            )

            # 摘要统计
            f.write("## 📊 分析摘要\n\n")
            summary = plan["summary"]
            total = summary["total_files"]
            if total > 0:
                f.write(f"- **总文件数**: {total}\n")
                f.write(
                    f"- **核心文件**: {summary['core_files']} ({summary['core_files']/total*100:.1f}%)\n"
                )
                f.write(
                    f"- **重要文件**: {summary['important_files']} ({summary['important_files']/total*100:.1f}%)\n"
                )
                f.write(
                    f"- **可选文件**: {summary['optional_files']} ({summary['optional_files']/total*100:.1f}%)\n"
                )
                f.write(
                    f"- **垃圾文件**: {summary['junk_files']} ({summary['junk_files']/total*100:.1f}%)\n\n"
                )
            else:
                f.write("- **总文件数**: 0\n")
                f.write("- **状态**: 无文件数据\n\n")

            # 核心文件列表
            f.write("## 🔥 核心文件 (优先保留重构)\n\n")
            for file_data in categories["core"][:20]:  # 显示前20个
                f.write(
                    f"- `{file_data['path']}` (得分: {file_data['score']:.2f}, 依赖: {file_data['dependencies']})\n"
                )

            if len(categories["core"]) > 20:
                f.write(
                    f"\n*... 还有 {len(categories['core']) - 20} 个核心文件*\n"
                )
            f.write("\n")

            # 垃圾文件列表
            f.write("## 🗑️ 建议删除文件\n\n")
            for file_data in categories["junk"][:30]:  # 显示前30个
                f.write(
                    f"- `{file_data['path']}` (得分: {file_data['score']:.2f})\n"
                )

            if len(categories["junk"]) > 30:
                f.write(
                    f"\n*... 还有 {len(categories['junk']) - 30} 个低价值文件*\n"
                )
            f.write("\n")

            # 重构优先级
            f.write("## 🎯 重构优先级\n\n")
            for i, item in enumerate(plan["priority_order"][:15], 1):
                priority_text = "🔴 高" if item["priority"] == 1 else "🟡 中"
                f.write(
                    f"{i}. {priority_text} - `{item['file']}` (得分: {item['score']:.2f})\n"
                )

            # 重构建议
            f.write("\n## 💡 重构建议\n\n")
            f.write("### 阶段1: 核心稳定化\n")
            f.write("1. 重构得分最高的核心文件\n")
            f.write("2. 统一API接口规范\n")
            f.write("3. 优化数据库连接和服务管理\n\n")

            f.write("### 阶段2: 功能整合\n")
            f.write("1. 合并重复功能模块\n")
            f.write("2. 重构重要文件的依赖关系\n")
            f.write("3. 标准化配置管理\n\n")

            f.write("### 阶段3: 清理优化\n")
            f.write("1. 删除低价值文件\n")
            f.write("2. 清理无用依赖\n")
            f.write("3. 优化项目结构\n\n")

    def run_analysis(self):
        """运行完整分析"""
        self.logger.info("🚀 开始代码价值评估分析...")

        # 加载依赖数据
        analysis_data = self.load_dependency_data()
        if not analysis_data:
            self.logger.error("❌ 无法加载依赖数据")
            return None

        # 文件分类
        self.logger.info("📊 正在分析文件价值...")
        categories = self.categorize_files(analysis_data)

        # 生成重构计划
        self.logger.info("📋 生成重构计划...")
        plan = self.generate_refactoring_plan(categories)

        # 保存报告
        self.logger.info("💾 保存分析报告...")
        analysis_file, report_file = self.save_analysis_report(
            categories, plan
        )

        # 输出摘要
        summary = plan["summary"]
        self.logger.info("✅ 分析完成!")
        self.logger.info(f"📁 总文件: {summary['total_files']}")
        self.logger.info(f"🔥 核心文件: {summary['core_files']}")
        self.logger.info(f"⚠️  垃圾文件: {summary['junk_files']}")
        self.logger.info(f"📄 详细报告: {report_file}")

        return {
            "categories": categories,
            "plan": plan,
            "analysis_file": analysis_file,
            "report_file": report_file,
        }


def main():
    """主函数"""
    analyzer = CodeValueAnalyzer()
    result = analyzer.run_analysis()

    if result:
        print("\n🎉 代码价值评估完成!")
        print(f"📊 详细报告已保存到: {result['report_file']}")

        # 显示关键统计
        categories = result["categories"]
        print(f"\n📈 核心文件 TOP 10:")
        for i, file_data in enumerate(categories["core"][:10], 1):
            print(
                f"  {i:2d}. {file_data['path']} (得分: {file_data['score']:.2f})"
            )

        print(f"\n🗑️  建议删除文件 TOP 10:")
        for i, file_data in enumerate(categories["junk"][:10], 1):
            print(
                f"  {i:2d}. {file_data['path']} (得分: {file_data['score']:.2f})"
            )
    else:
        print("❌ 分析失败")


if __name__ == "__main__":
    main()
