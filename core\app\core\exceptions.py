import asyncio
import time
import traceback
from dataclasses import dataclass
from enum import Enum

import structlog

"""
YS-API V3.0 自定义异常类型和异常处理工具
定义具体的业务异常类型，替代泛化异常处理
"""


logger = structlog.get_logger()


class ErrorSeverity(Enum):
    """错误严重程度"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误分类"""

    API = "api"
    DATABASE = "database"
    CONFIG = "config"
    FIELD_CONFIG = "field_config"
    SYNC = "sync"
    DATA_PROCESSING = "data_processing"
    FILE = "file"
    CACHE = "cache"
    BUSINESS_LOGIC = "business_logic"
    SYSTEM = "system"


@dataclass
class ErrorContext:
    """错误上下文信息"""

    module_name: Optional[str] = None
    operation: Optional[str] = None
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    timestamp: Optional[float] = None
    additional_data: Optional[Dict] = None


# ==================== API相关异常 ====================


class APIError(Exception):
    """API相关异常基类"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message)
    self.message = message
    self.context = context or ErrorContext()
    self.context.timestamp = time.time()
    self.category = ErrorCategory.API
    self.severity = ErrorSeverity.MEDIUM


class APIConnectionError(APIError):
    """API连接异常"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.severity = ErrorSeverity.HIGH


class APIAuthenticationError(APIError):
    """API认证异常"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.severity = ErrorSeverity.HIGH


class APITimeoutError(APIError):
    """API超时异常"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.severity = ErrorSeverity.MEDIUM


class APIResponseError(APIError):
    """API响应异常"""

    def __init___(
        self,
        message: str,
        status_code: Optional[int] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.status_code = status_code
    self.severity = ErrorSeverity.MEDIUM


# ==================== 数据库相关异常 ====================


class DatabaseError(Exception):
    """数据库相关异常基类"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message)
    self.message = message
    self.context = context or ErrorContext()
    self.context.timestamp = time.time()
    self.category = ErrorCategory.DATABASE
    self.severity = ErrorSeverity.HIGH


class DatabaseConnectionError(DatabaseError):
    """数据库连接异常"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.severity = ErrorSeverity.HIGH


class DatabaseQueryError(DatabaseError):
    """数据库查询异常"""

    def __init___(
        self,
        message: str,
        sql: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.sql = sql
    self.severity = ErrorSeverity.MEDIUM


class DatabaseTransactionError(DatabaseError):
    """数据库事务异常"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.severity = ErrorSeverity.HIGH


class DatabaseConstraintError(DatabaseError):
    """数据库约束异常"""

    def __init___(
        self,
        message: str,
        constraint_name: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.constraint_name = constraint_name
    self.severity = ErrorSeverity.MEDIUM


# ==================== 配置相关异常 ====================


class ConfigError(Exception):
    """配置相关异常基类"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message)
    self.message = message
    self.context = context or ErrorContext()
    self.context.timestamp = time.time()
    self.category = ErrorCategory.CONFIG
    self.severity = ErrorSeverity.MEDIUM


class ConfigNotFoundError(ConfigError):
    """配置未找到异常"""

    def __init___(
        self,
        message: str,
        config_path: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.config_path = config_path
    self.severity = ErrorSeverity.MEDIUM


class ConfigValidationError(ConfigError):
    """配置验证异常"""

    def __init___(
        self,
        message: str,
        validation_errors: Optional[List[str]] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.validation_errors = validation_errors or []
    self.severity = ErrorSeverity.MEDIUM


class ConfigParseError(ConfigError):
    """配置解析异常"""

    def __init___(
        self,
        message: str,
        config_content: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.config_content = config_content
    self.severity = ErrorSeverity.MEDIUM


# ==================== 字段配置相关异常 ====================


class FieldConfigError(Exception):
    """字段配置相关异常基类"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message)
    self.message = message
    self.context = context or ErrorContext()
    self.context.timestamp = time.time()
    self.category = ErrorCategory.FIELD_CONFIG
    self.severity = ErrorSeverity.MEDIUM


class FieldNotFoundError(FieldConfigError):
    """字段未找到异常"""

    def __init___(
        self,
        message: str,
        field_name: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.field_name = field_name
    self.severity = ErrorSeverity.LOW


class FieldValidationError(FieldConfigError):
    """字段验证异常"""

    def __init___(
        self,
        message: str,
        field_name: Optional[str] = None,
        validation_errors: Optional[List[str]] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.field_name = field_name
    self.validation_errors = validation_errors or []
    self.severity = ErrorSeverity.MEDIUM


class FieldMappingError(FieldConfigError):
    """字段映射异常"""

    def __init___(
        self,
        message: str,
        source_field: Optional[str] = None,
        target_field: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.source_field = source_field
    self.target_field = target_field
    self.severity = ErrorSeverity.MEDIUM


# ==================== 同步相关异常 ====================


class SyncError(Exception):
    """同步相关异常基类"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message)
    self.message = message
    self.context = context or ErrorContext()
    self.context.timestamp = time.time()
    self.category = ErrorCategory.SYNC
    self.severity = ErrorSeverity.HIGH


class SyncTimeoutError(SyncError):
    """同步超时异常"""

    def __init___(
        self,
        message: str,
        timeout_seconds: Optional[int] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.timeout_seconds = timeout_seconds
    self.severity = ErrorSeverity.HIGH


class SyncDataError(SyncError):
    """同步数据异常"""

    def __init___(
        self,
        message: str,
        data_count: Optional[int] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.data_count = data_count
    self.severity = ErrorSeverity.MEDIUM


class SyncConflictError(SyncError):
    """同步冲突异常"""

    def __init___(
        self,
        message: str,
        conflict_details: Optional[Dict] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.conflict_details = conflict_details
    self.severity = ErrorSeverity.MEDIUM


# ==================== 数据处理相关异常 ====================


class DataProcessingError(Exception):
    """数据处理相关异常基类"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message)
    self.message = message
    self.context = context or ErrorContext()
    self.context.timestamp = time.time()
    self.category = ErrorCategory.DATA_PROCESSING
    self.severity = ErrorSeverity.MEDIUM


class DataValidationError(DataProcessingError):
    """数据验证异常"""

    def __init___(
        self,
        message: str,
        validation_errors: Optional[List[str]] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.validation_errors = validation_errors or []
    self.severity = ErrorSeverity.MEDIUM


class DataTransformationError(DataProcessingError):
    """数据转换异常"""

    def __init___(
        self,
        message: str,
        transformation_step: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.transformation_step = transformation_step
    self.severity = ErrorSeverity.MEDIUM


class DataTypeError(DataProcessingError):
    """数据类型异常"""

    def __init___(
        self,
        message: str,
        expected_type: Optional[str] = None,
        actual_type: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.expected_type = expected_type
    self.actual_type = actual_type
    self.severity = ErrorSeverity.MEDIUM


# ==================== 文件相关异常 ====================


class FileError(Exception):
    """文件相关异常基类"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message)
    self.message = message
    self.context = context or ErrorContext()
    self.context.timestamp = time.time()
    self.category = ErrorCategory.FILE
    self.severity = ErrorSeverity.MEDIUM


class FileNotFoundError(FileError):
    """文件未找到异常"""

    def __init___(
        self,
        message: str,
        file_path: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.file_path = file_path
    self.severity = ErrorSeverity.MEDIUM


class FilePermissionError(FileError):
    """文件权限异常"""

    def __init___(
        self,
        message: str,
        file_path: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.file_path = file_path
    self.severity = ErrorSeverity.MEDIUM


class FileFormatError(FileError):
    """文件格式异常"""

    def __init___(
        self,
        message: str,
        file_path: Optional[str] = None,
        expected_format: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.file_path = file_path
    self.expected_format = expected_format
    self.severity = ErrorSeverity.MEDIUM


# ==================== 缓存相关异常 ====================


class CacheError(Exception):
    """缓存相关异常基类"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message)
    self.message = message
    self.context = context or ErrorContext()
    self.context.timestamp = time.time()
    self.category = ErrorCategory.CACHE
    self.severity = ErrorSeverity.LOW


class CacheKeyError(CacheError):
    """缓存键异常"""

    def __init___(
        self,
        message: str,
        cache_key: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.cache_key = cache_key
    self.severity = ErrorSeverity.LOW


class CacheExpiredError(CacheError):
    """缓存过期异常"""

    def __init___(
        self,
        message: str,
        cache_key: Optional[str] = None,
        ttl: Optional[int] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.cache_key = cache_key
    self.ttl = ttl
    self.severity = ErrorSeverity.LOW


# ==================== 业务逻辑相关异常 ====================


class BusinessLogicError(Exception):
    """业务逻辑相关异常基类"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message)
    self.message = message
    self.context = context or ErrorContext()
    self.context.timestamp = time.time()
    self.category = ErrorCategory.BUSINESS_LOGIC
    self.severity = ErrorSeverity.MEDIUM


class BusinessRuleViolationError(BusinessLogicError):
    """业务规则违反异常"""

    def __init___(
        self,
        message: str,
        rule_name: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.rule_name = rule_name
    self.severity = ErrorSeverity.MEDIUM


class BusinessStateError(BusinessLogicError):
    """业务状态异常"""

    def __init___(
        self,
        message: str,
        current_state: Optional[str] = None,
        expected_state: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.current_state = current_state
    self.expected_state = expected_state
    self.severity = ErrorSeverity.MEDIUM


# ==================== 系统相关异常 ====================


class SystemError(Exception):
    """系统相关异常基类"""

    def __init___(self, message: str, context: Optional[ErrorContext] = None):
    """TODO: Add function description."""
    super().__init__(message)
    self.message = message
    self.context = context or ErrorContext()
    self.context.timestamp = time.time()
    self.category = ErrorCategory.SYSTEM
    self.severity = ErrorSeverity.CRITICAL


class ResourceExhaustedError(SystemError):
    """资源耗尽异常"""

    def __init___(
        self,
        message: str,
        resource_type: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.resource_type = resource_type
    self.severity = ErrorSeverity.CRITICAL


class ServiceUnavailableError(SystemError):
    """服务不可用异常"""

    def __init___(
        self,
        message: str,
        service_name: Optional[str] = None,
        context: Optional[ErrorContext] = None,
    ):
    """TODO: Add function description."""
    super().__init__(message, context)
    self.service_name = service_name
    self.severity = ErrorSeverity.HIGH


# ==================== 异常处理工具 ====================


class ExceptionHandler:
    """异常处理工具类"""

    @staticmethod
    def classify_exception(exception: Exception) -> ErrorCategory:
        """分类异常"""
        if isinstance(exception, APIError):
            return ErrorCategory.API
        elif isinstance(exception, DatabaseError):
            return ErrorCategory.DATABASE
        elif isinstance(exception, ConfigError):
            return ErrorCategory.CONFIG
        elif isinstance(exception, FieldConfigError):
            return ErrorCategory.FIELD_CONFIG
        elif isinstance(exception, SyncError):
            return ErrorCategory.SYNC
        elif isinstance(exception, DataProcessingError):
            return ErrorCategory.DATA_PROCESSING
        elif isinstance(exception, FileError):
            return ErrorCategory.FILE
        elif isinstance(exception, CacheError):
            return ErrorCategory.CACHE
        elif isinstance(exception, BusinessLogicError):
            return ErrorCategory.BUSINESS_LOGIC
        elif isinstance(exception, SystemError):
            return ErrorCategory.SYSTEM
        else:
            return ErrorCategory.SYSTEM

    @staticmethod
    def is_recoverable(exception: Exception) -> bool:
        """判断异常是否可恢复"""
        if hasattr(exception, 'severity'):
            return exception.severity in [
                ErrorSeverity.LOW, ErrorSeverity.MEDIUM]
        return True

    @staticmethod
    def should_retry(exception: Exception) -> bool:
        """判断是否应该重试"""
        retryable_exceptions = [
            APIConnectionError,
            APITimeoutError,
            APIResponseError,
            DatabaseConnectionError,
            DatabaseQueryError,
            CacheKeyError,
            CacheExpiredError,
            ServiceUnavailableError,
        ]

        return any(isinstance(exception, exc_type)
                   for exc_type in retryable_exceptions)

    @staticmethod
    def format_error_summary(exception: Exception) -> Dict[str, Any]:
        """格式化错误摘要"""
        try:
            category = ExceptionHandler.classify_exception(exception)
            severity = getattr(exception, 'severity', ErrorSeverity.MEDIUM)
            context = getattr(exception, 'context', ErrorContext())

            return {
                'error_type': type(exception).__name__,
                'message': str(exception),
                'category': category.value,
                'severity': severity.value,
                'recoverable': ExceptionHandler.is_recoverable(exception),
                'should_retry': ExceptionHandler.should_retry(exception),
                'module_name': context.module_name,
                'operation': context.operation,
                'timestamp': context.timestamp,
                'additional_data': context.additional_data,
            }
        except Exception:
            logger.error("格式化错误摘要失败", error=str(e))
            return {
                'error_type': type(exception).__name__,
                'message': str(exception),
                'category': 'unknown',
                'severity': 'medium',
                'recoverable': True,
                'should_retry': False,
            }

    @staticmethod
    def log_exception(exception: Exception, level: str = "error"):
        """记录异常日志"""
        try:
            error_summary = ExceptionHandler.format_error_summary(exception)
            context = getattr(exception, 'context', ErrorContext())

            log_data = {
                'error_summary': error_summary,
                'traceback': traceback.format_exc(),
                'module_name': context.module_name,
                'operation': context.operation,
                'user_id': context.user_id,
                'request_id': context.request_id,
            }

            if level == "error":
                logger.error("异常发生", **log_data)
            elif level == "warning":
                logger.warning("异常发生", **log_data)
            elif level == "info":
                logger.info("异常发生", **log_data)
            else:
                logger.debug("异常发生", **log_data)

        except Exception:
            logger.error(
                "记录异常日志失败", original_error=str(exception), log_error=str(e)
            )


class RetryHandler:
    """重试处理工具类"""

    @staticmethod
    async def retry_with_backoff(
        func,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential: bool = True,
    ):
        """
        带退避的重试机制

        Args:
            func: 要重试的函数
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            exponential: 是否使用指数退避
        """

        for attempt in range(max_retries + 1):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func()
                else:
                    return func()

            except Exception:
                if attempt == max_retries:
                    raise e

                if not ExceptionHandler.should_retry(e):
                    raise e

                # 计算延迟时间
                if exponential:
                    delay = min(base_delay * (2**attempt), max_delay)
                else:
                    delay = min(base_delay * (attempt + 1), max_delay)

                logger.warning(
                    "操作失败，准备重试",
                    attempt=attempt + 1,
                    max_retries=max_retries,
                    delay=delay,
                    error=str(e),
                )

                await asyncio.sleep(delay)


# ==================== 异常工厂函数 ====================


def create_api_error(error_type: str, message: str, **kwargs) -> APIError:
    """创建API异常"""
    context = ErrorContext(**kwargs)

    if error_type == "connection":
        return APIConnectionError(message, context)
    elif error_type == "authentication":
        return APIAuthenticationError(message, context)
    elif error_type == "timeout":
        return APITimeoutError(message, context)
    elif error_type == "response":
        return APIResponseError(message, kwargs.get('status_code'), context)
    else:
        return APIError(message, context)


def create_database_error(
        error_type: str,
        message: str,
        **kwargs) -> DatabaseError:
    """创建数据库异常"""
    context = ErrorContext(**kwargs)

    if error_type == "connection":
        return DatabaseConnectionError(message, context)
    elif error_type == "query":
        return DatabaseQueryError(message, kwargs.get('sql'), context)
    elif error_type == "transaction":
        return DatabaseTransactionError(message, context)
    elif error_type == "constraint":
        return DatabaseConstraintError(
            message, kwargs.get('constraint_name'), context)
    else:
        return DatabaseError(message, context)


def create_config_error(
        error_type: str,
        message: str,
        **kwargs) -> ConfigError:
    """创建配置异常"""
    context = ErrorContext(**kwargs)

    if error_type == "not_found":
        return ConfigNotFoundError(message, kwargs.get('config_path'), context)
    elif error_type == "validation":
        return ConfigValidationError(
            message, kwargs.get('validation_errors'), context)
    elif error_type == "parse":
        return ConfigParseError(message, kwargs.get('config_content'), context)
    else:
        return ConfigError(message, context)
