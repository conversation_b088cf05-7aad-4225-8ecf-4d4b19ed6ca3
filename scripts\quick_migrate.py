import subprocess
import sys
from datetime import datetime

#!/usr/bin/env python3
"""
快速自动迁移器 - 一键执行
"""


def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"🚀 {description}...")

    try:
        result = subprocess.run(
            cmd,
            shell=True,
            capture_output=True,
            text=True)

        if result.returncode == 0:
            print(f"✅ {description} 完成")
            if result.stdout.strip():
                print(f"   输出: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} 失败")
            if result.stderr.strip():
                print(f"   错误: {result.stderr.strip()}")
            return False
    except Exception:
        print(f"❌ {description} 异常: {e}")
        return False


def main():
    """主函数"""
    print(
        f"🚀 屎山代码绞杀自动化管道启动 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    )
    print("=" * 60)

    # 检查当前状态
    run_command("python scripts/module_tracker_simple.py --summary", "检查当前状态")
    print()

    # 执行下一批迁移
    success = run_command(
        "python scripts/simple_migrator.py --batch 3",
        "执行批量迁移")
    print()

    if success:
        # 更新状态
        run_command(
            "python scripts/module_tracker_simple.py --summary",
            "更新状态显示")
        print()
        print("🎉 本轮迁移完成！")
        print("💡 提示: 再次运行此脚本以继续下一批迁移")
    else:
        print("❌ 迁移过程中出现错误")
        sys.exit(1)


if __name__ == "__main__":
    main()
