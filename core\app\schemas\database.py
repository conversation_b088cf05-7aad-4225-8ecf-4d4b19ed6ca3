from pydantic import BaseModel

"""
YS-API V3.0 数据库管理 Schema
Pydantic模型定义
"""


class DatabaseInfo(BaseModel):
    """数据库信息"""

    database_name: str
    server: str
    port: str
    connected: bool
    version: str


class TableInfo(BaseModel):
    """数据库表信息"""

    table_name: str
    exists: bool
    column_count: int
    columns: List[Dict[str, Any]] = []


class ModuleTableResult(BaseModel):
    """模块表创建结果"""

    module_name: str
    table_name: str
    column_count: int
    selected_fields: int
    action: str


class BatchTableResult(BaseModel):
    """批量表创建结果"""

    total_modules: int
    processed_modules: int
    successful_modules: List[str]
    failed_modules: List[str]
    missing_configs: List[str]
    success_count: int
    failure_count: int


class ValidationResult(BaseModel):
    """数据库结构验证结果"""

    total_modules: int
    valid_modules: int
    invalid_modules: int
    validation_results: Dict[str, Dict[str, Any]]


class DatabaseResponse(BaseModel):
    """数据库操作响应"""

    success: bool
    data: DatabaseInfo
    message: str


class TableResponse(BaseModel):
    """表操作响应"""

    success: bool
    data: TableInfo
    message: str


class OperationResponse(BaseModel):
    """通用操作响应"""

    success: bool
    data: Dict[str, Any]
    message: str
