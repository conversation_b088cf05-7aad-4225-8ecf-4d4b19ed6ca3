#!/bin/bash
# 屎山绞杀执行器

echo "🔪 屎山绞杀执行器启动..."

# 检查参数
if [ $# -lt 1 ]; then
    echo "使用方法: $0 <模块名称> [--auto] [--target-percentage]"
    echo "示例:"
    echo "  $0 '材料出库单列表查询'                    # 交互式绞杀"
    echo "  $0 '材料出库单列表查询' --auto            # 自动绞杀到100%"
    echo "  $0 '材料出库单列表查询' --auto 50         # 自动绞杀到50%"
    exit 1
fi

MODULE_NAME="$1"
AUTO_MODE="$2"
TARGET_PERCENTAGE="${3:-100}"

echo "🎯 绞杀目标: $MODULE_NAME"
echo "🤖 自动模式: $([ "$AUTO_MODE" = "--auto" ] && echo "是" || echo "否")"
echo "📊 目标流量: $TARGET_PERCENTAGE%"

# Step 1: 识别屎山代码
echo ""
echo "🔍 Step 1: 识别屎山代码..."
./scripts/identify_shit_mountain.sh
if [ $? -ne 0 ]; then
    echo "❌ 屎山代码识别失败"
    exit 1
fi

# Step 2: 标记待绞杀模块
echo ""
echo "🏷️ Step 2: 标记待绞杀模块..."
./scripts/tag_modules_for_strangling.sh
if [ $? -ne 0 ]; then
    echo "❌ 模块标记失败"
    exit 1
fi

# 检查模块是否在绞杀列表中
if ! grep -q "$MODULE_NAME" analysis/strangling_targets/all_targets.txt 2>/dev/null; then
    echo "⚠️ 警告: 模块 '$MODULE_NAME' 未在屎山绞杀目标列表中"
    if [ "$AUTO_MODE" != "--auto" ]; then
        read -p "是否继续绞杀？[y/N] " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "❌ 绞杀已取消"
            exit 1
        fi
    fi
fi

# Step 3: 创建代理层
echo ""
echo "🔄 Step 3: 创建代理层..."
./scripts/create_strangler_proxy.sh
if [ $? -ne 0 ]; then
    echo "❌ 代理层创建失败"
    exit 1
fi

# Step 4: 检查新系统实现
echo ""
echo "🔍 Step 4: 检查新系统实现..."
NEW_HANDLER_EXISTS=$(python3 -c "
import os
import sys

# 检查新系统处理器是否存在
v2_handler_path = f'backend/app/v2/{\"$MODULE_NAME\".replace(' ', '_').lower()}.py'
if os.path.exists(v2_handler_path):
    print('exists')
else:
    print('missing')
")

if [ "$NEW_HANDLER_EXISTS" = "missing" ]; then
    echo "❌ 错误: 新系统处理器不存在"
    echo "请先实现新系统处理器: backend/app/v2/$(echo $MODULE_NAME | tr ' ' '_' | tr '[:upper:]' '[:lower:]').py"
    
    if [ "$AUTO_MODE" != "--auto" ]; then
        echo ""
        echo "是否创建基础处理器模板？[y/N]"
        read -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # 创建处理器模板
            mkdir -p backend/app/v2
            MODULE_FILE="backend/app/v2/$(echo $MODULE_NAME | tr ' ' '_' | tr '[:upper:]' '[:lower:]').py"
            cat > "$MODULE_FILE" << EOF
"""
$MODULE_NAME 新系统处理器
"""

class $(echo $MODULE_NAME | tr ' ' '_' | tr '[:lower:]' '[:upper:]')Handler:
    def __init__(self):
        self.module_name = "$MODULE_NAME"
    
    def process(self, request_data):
        """处理请求"""
        # TODO: 实现新系统逻辑
        raise NotImplementedError("新系统处理器尚未实现")
    
def get_handler():
    """获取处理器实例"""
    return $(echo $MODULE_NAME | tr ' ' '_' | tr '[:lower:]' '[:upper:]')Handler()
EOF
            echo "✅ 处理器模板已创建: $MODULE_FILE"
            echo "请实现处理器逻辑后重新运行绞杀器"
        fi
    fi
    exit 1
fi

# Step 5: 逐步切换流量
echo ""
echo "📊 Step 5: 逐步切换流量..."

if [ "$AUTO_MODE" = "--auto" ]; then
    # 自动模式：直接切换到目标百分比
    echo "🤖 自动模式：直接切换到 $TARGET_PERCENTAGE%"
    ./scripts/gradual_traffic_switch.sh "$MODULE_NAME" "$TARGET_PERCENTAGE"
    SWITCH_RESULT=$?
else
    # 交互模式：逐步引导
    echo "🎮 交互模式：逐步引导切换"
    
    CURRENT_PERCENTAGE=$(python3 -c "
import json
with open('config/migration_status.json', 'r') as f:
    config = json.load(f)
print(config.get('modules', {}).get('$MODULE_NAME', {}).get('traffic_split', 0))
")
    
    echo "当前流量分配: $CURRENT_PERCENTAGE%"
    
    # 建议的切换步骤
    STEPS=(10 25 50 75 100)
    for step in "${STEPS[@]}"; do
        if [ "$step" -le "$CURRENT_PERCENTAGE" ]; then
            continue
        fi
        
        echo ""
        echo "建议下一步: 切换到 ${step}%"
        read -p "是否执行？[y/N/s(跳过)/q(退出)] " -n 1 -r
        echo
        
        case $REPLY in
            [Yy])
                ./scripts/gradual_traffic_switch.sh "$MODULE_NAME" "$step"
                SWITCH_RESULT=$?
                if [ $SWITCH_RESULT -ne 0 ]; then
                    echo "❌ 流量切换失败"
                    break
                fi
                ;;
            [Ss])
                echo "⏭️ 跳过 ${step}%"
                continue
                ;;
            [Qq])
                echo "🚪 退出绞杀流程"
                exit 0
                ;;
            *)
                echo "⏸️ 暂停在 ${CURRENT_PERCENTAGE}%"
                break
                ;;
        esac
        
        if [ "$step" -eq 100 ]; then
            break
        fi
    done
fi

if [ $SWITCH_RESULT -ne 0 ]; then
    echo "❌ 流量切换失败，绞杀中断"
    exit 1
fi

# Step 6: 检查绞杀完成情况
echo ""
echo "🔍 Step 6: 检查绞杀完成情况..."

FINAL_PERCENTAGE=$(python3 -c "
import json
with open('config/migration_status.json', 'r') as f:
    config = json.load(f)
print(config.get('modules', {}).get('$MODULE_NAME', {}).get('traffic_split', 0))
")

echo "最终流量分配: $FINAL_PERCENTAGE%"

if [ "$FINAL_PERCENTAGE" -eq 100 ]; then
    echo "🎉 恭喜！模块已完全迁移到新系统"
    
    # 询问是否删除旧代码
    if [ "$AUTO_MODE" != "--auto" ]; then
        echo ""
        read -p "是否删除旧屎山代码？[y/N] " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo ""
            echo "🗑️ Step 7: 删除旧屎山代码..."
            ./scripts/cleanup_legacy_shit.sh "$MODULE_NAME"
        else
            echo "⚠️ 旧代码保留，建议7天后手动清理"
        fi
    else
        echo "🗑️ Step 7: 删除旧屎山代码..."
        ./scripts/cleanup_legacy_shit.sh "$MODULE_NAME" --force
    fi
else
    echo "📊 绞杀未完成，当前进度: $FINAL_PERCENTAGE%"
    echo "可以稍后继续执行："
    echo "  ./strangler_fig_executor.sh '$MODULE_NAME' --auto 100"
fi

# 生成绞杀报告
echo ""
echo "📄 生成绞杀报告..."
cat > "reports/strangling_$(echo $MODULE_NAME | sed 's/[^a-zA-Z0-9]/_/g')_$(date +%Y%m%d_%H%M%S).md" << EOF
# 屎山绞杀报告

## 绞杀信息
- **模块名称**: $MODULE_NAME
- **绞杀时间**: $(date)
- **操作人员**: $(whoami)
- **模式**: $([ "$AUTO_MODE" = "--auto" ] && echo "自动模式" || echo "交互模式")
- **最终流量**: $FINAL_PERCENTAGE%

## 绞杀步骤
1. ✅ 屎山代码识别
2. ✅ 模块标记
3. ✅ 代理层创建
4. ✅ 新系统实现检查
5. ✅ 流量逐步切换
6. $([ "$FINAL_PERCENTAGE" -eq 100 ] && echo "✅" || echo "🔄") 绞杀完成检查
$([ "$FINAL_PERCENTAGE" -eq 100 ] && echo "7. ✅ 旧代码清理" || echo "7. ⏸️ 旧代码保留")

## 结果
$(if [ "$FINAL_PERCENTAGE" -eq 100 ]; then
    echo "🎉 **绞杀成功完成！**"
    echo ""
    echo "- 新系统接管100%流量"
    echo "- 旧屎山代码已清理"
    echo "- 模块性能和可维护性显著提升"
else
    echo "📊 **绞杀进行中**"
    echo ""
    echo "- 当前新系统流量: $FINAL_PERCENTAGE%"
    echo "- 剩余旧系统流量: $((100 - FINAL_PERCENTAGE))%"
    echo "- 可继续执行绞杀完成迁移"
fi)

## 监控指标
请持续监控以下指标：
- 🔍 错误率 < 5%
- ⚡ 响应时间变化 < 20%
- 👥 用户投诉数量
- 💾 系统资源使用

## 下一步
$(if [ "$FINAL_PERCENTAGE" -eq 100 ]; then
    echo "- 持续监控新系统稳定性"
    echo "- 收集用户反馈"
    echo "- 开始下一个模块的绞杀"
else
    echo "- 继续绞杀到100%: \`./strangler_fig_executor.sh '$MODULE_NAME' --auto 100\`"
    echo "- 监控当前流量分配的稳定性"
    echo "- 如有问题可回滚: \`./gradual_traffic_switch.sh '$MODULE_NAME' 0\`"
fi)
EOF

echo "✅ 屎山绞杀执行器完成！"
echo "📄 详细报告: reports/strangling_$(echo $MODULE_NAME | sed 's/[^a-zA-Z0-9]/_/g')_$(date +%Y%m%d_%H%M%S).md"
echo ""

if [ "$FINAL_PERCENTAGE" -eq 100 ]; then
    echo "🎊 恭喜！又一个屎山被成功绞杀！"
    echo "🚀 可以开始绞杀下一个模块了"
else
    echo "📈 绞杀进度: $FINAL_PERCENTAGE%"
    echo "🔄 可继续执行完成绞杀"
fi
