委外申请单列表查询
发布时间:2024-09-28 01:14:58
提供委外申请单列表查询接口，可以通过接口按列表查询委外申请单

API测试工具
您可以在OpenAPI Explorer中直接运行该接口，免去您计算签名的困扰。
1. 请求说明
请求域名	动态域名，获取方式详见 获取租户所在数据中心域名
请求地址	https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/mfg/subcontractrequisition/list
请求方式	POST
ContentType	application/json
应用场景	开放API
API类别	
事务和幂等性	无
限流次数	40次/分钟

用户身份	支持传递普通用户身份，详细说明见开放平台用户认证接入规范
多语	不支持
2. 请求参数
只看必填项
名称	类型	参数位置	必填	描述
access_token	string	query	是	调用方应用token
企业自建获取token
Body参数
名称	类型	数组	必填	描述
pageIndex	int	否	是	页号 默认值:1    示例: 1    默认值: 1
pageSize	int	否	是	每页行数 默认值:10    示例: 10    默认值: 10
orgId	string	是	否	组织ID    示例: ["1866605942198527"]
code	string	否	否	委外申请单号    示例: WWSQ202105010001
vouchdate	string	否	否	单据日期（区间，格式2021-03-02|2021-03-02 23:59:59）    示例: 2021-03-02|2021-03-02 23:59:59
transTypeId	string	是	否	交易类型ID    示例: ["1866605942198526"]
status	short	否	否	申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。    示例: 0
productId	long	是	否	物料ID    示例: [1866605942198650]
requisitionDate	string	否	否	需求日期（区间，格式2021-03-02|2021-03-02 23:59:59）    示例: 2021-03-02|2021-03-02 23:59:59
departmentId	string	是	否	需求部门ID    示例: ["186660594219847"]
operatorId	string	是	否	需求人ID    示例: ["186660594218648"]
simple	object	否	否	扩展参数
open_pubts_begin	date
格式:yyyy-MM-dd HH:mm:ss	否	否	时间戳，开始时间    示例: 2022-01-01 00:00:00
open_pubts_end	date
格式:yyyy-MM-dd HH:mm:ss	否	否	时间戳，结束时间    示例: 2022-01-01 10:00:00
simpleVOs	object	是	否	扩展查询条件
field	string	否	否	属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts
op	string	否	否	比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )
value1	string	否	否	查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)
value2	string	否	否	查询条件值2
logicOp	string	否	否	逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or    示例: and
conditions	object	是	否	下级查询条件
field	string	否	否	属性名(条件)(1.pubts(时间戳)) 2.id(单据ID) 3.sourceType(来源类别 0：手工录入 1：计划订单) 示例：pubts
op	string	否	否	逻辑连接符(and：且；or：或) 注：logicOp有值时，conditions条件生效，logicOp同级的其他条件不生效；logicOp无值时，conditions条件不生效，logicOp同级设置的其他条件生效。 示例：or
value1	string	否	否	查询条件值1。示例：2021-01-01 00:00:00 (field值为pubts，op值为egt或gt或el或lt)
value2	string	否	否	查询条件值2
3. 请求示例
Url: /yonbip/mfg/subcontractrequisition/list?access_token=访问令牌
Body: {
	"pageIndex": 1,
	"pageSize": 10,
	"orgId": [
		"1866605942198527"
	],
	"code": "WWSQ202105010001",
	"vouchdate": "2021-03-02|2021-03-02 23:59:59",
	"transTypeId": [
		"1866605942198526"
	],
	"status": 0,
	"productId": [
		1866605942198650
	],
	"requisitionDate": "2021-03-02|2021-03-02 23:59:59",
	"departmentId": [
		"186660594219847"
	],
	"operatorId": [
		"186660594218648"
	],
	"simple": {
		"open_pubts_begin": "2022-01-01 00:00:00",
		"open_pubts_end": "2022-01-01 10:00:00"
	},
	"simpleVOs": [
		{
			"field": "",
			"op": "",
			"value1": "",
			"value2": "",
			"logicOp": "and",
			"conditions": [
				{
					"field": "",
					"op": "",
					"value1": "",
					"value2": ""
				}
			]
		}
	]
}
4. 返回值参数
名称	类型	数组	描述
code	long	否	返回码，成功时返回200
message	string	否	接口返回信息
data	object	否	接口返回数据
pageIndex	long	否	当前页
pageSize	long	否	页大小
recordCount	long	否	记录总数
recordList	object	是	返回数据对象
id	long	否	单据ID
orgId	string	否	需求组织ID
orgName	string	否	需求组织
code	string	否	委外申请单号
vouchdate	string	否	单据日期
status	short	否	申请单状态： 0-开立，1-已审核，2-已关闭，3-审核中。
transTypeId	string	否	交易类型ID
transTypeName	string	否	交易类型
transTypeExtendAttrsJson	string	否	交易类型扩展属性
departmentId	string	否	需求部门ID
departmentName	string	否	需求部门
operatorId	string	否	需求人ID
operatorName	string	否	需求人名称
sourceType	string	否	来源类别。0：手工添加，1：计划订单。
defineCharacteristics	特征组
po.subcontractrequisition.SubcontractRequisition	否	自定义项特征组
isWfControlled	boolean	否	是否审批流控制：false-否，true-是。
verifystate	long	否	审批状态：0-开立，1-已提交，2-已审批，-1-驳回
pubts	string	否	时间戳
subcontractRequisitionProduct	object	否	行信息
id	string	否	主键
productId	string	否	物料ID
outsourceOrgId	string	否	委外组织ID
rcvOrgId	string	否	收货组织ID
productCode	string	否	物料编码
productName	string	否	物料名称
demandQuantityDU	long	否	需求件数
demandUnitId	long	否	需求单位ID
demandUnitName	string	否	需求单位
demandUnitTruncationType	long	否	需求单位舍位
demandUnitPrecision	long	否	需求单位精度
demandQuantityMU	long	否	需求数量
mainUnitId	long	否	需求主单位ID
mainUnitName	string	否	需求主计量单位
mainUnitTruncationType	long	否	需求主计量单位舍位
mainUnitPrecision	long	否	需求主计量单位精度
requisitionDate	string	否	需求日期
rcvOrgName	string	否	收货组织
outsourceOrgName	string	否	委外组织
defineCharacteristicsPro	特征组
po.subcontractrequisition.SubcontractRequisitionProduct	否	自定义项特征组
WW0555	Date	否	委外申请单参考需求日期
XS11	string	否	需求分类号test
XS15	string	否	顾客订单号（订单表体）
id	string	否	特征id,主键,新增时无需填写,修改时必填
freeCharacteristics	特征组
po.subcontractrequisition.SubcontractRequisitionProduct	否	自由项特征组
XS15	string	否	顾客订单号（订单表体）
XXX0111	string	否	需求分类项
id	string	否	特征id,主键,新增时无需填写,修改时必填
sumRecordList	object	是	合计字段集合
subcontractRequisitionProduct	object	否	产品行数据
demandQuantityDU	double	否	需求件数
demandQuantityMU	string	否	需求数量
pageCount	long	否	总页数
beginPageIndex	long	否	开始页码
endPageIndex	long	否	结束页码
5. 正确返回示例
{
	"code": 200,
	"message": "操作成功",
	"data": {
		"pageIndex": 1,
		"pageSize": 20,
		"recordCount": 0,
		"recordList": [
			{
				"id": 2573294878692352,
				"orgId": "2325505713575680",
				"orgName": "资产管理公司",
				"code": "WWSQ202112230004",
				"vouchdate": "2021-12-23 00:00:00",
				"status": 1,
				"transTypeId": "2325273783079171",
				"transTypeName": "标准委外",
				"transTypeExtendAttrsJson": "{\"specialType\":\"none\",\"businessType\":\"wholeOutsourcing\"}",
				"departmentId": "2325510861050112",
				"departmentName": "仓储部",
				"operatorId": "2391833684873472",
				"operatorName": "张三",
				"sourceType": "1",
				"defineCharacteristics": {},
				"isWfControlled": false,
				"verifystate": 2,
				"pubts": "2021-12-23 20:14:33",
				"subcontractRequisitionProduct": {
					"id": "",
					"productId": "2325533913174272",
					"outsourceOrgId": "2325505713575680",
					"rcvOrgId": "2325505713575680",
					"productCode": "",
					"productName": "P10",
					"demandQuantityDU": 10,
					"demandUnitId": 2325529461018880,
					"demandUnitName": "袋",
					"demandUnitTruncationType": 4,
					"demandUnitPrecision": 1,
					"demandQuantityMU": 10,
					"mainUnitId": 2325529461018880,
					"mainUnitName": "袋",
					"mainUnitTruncationType": 4,
					"mainUnitPrecision": 1,
					"requisitionDate": "2022-01-12 00:00:00",
					"rcvOrgName": "资产管理公司",
					"outsourceOrgName": "资产管理公司",
					"defineCharacteristicsPro": {
						"WW0555": "",
						"XS11": "",
						"XS15": "",
						"id": ""
					},
					"freeCharacteristics": {
						"XS15": "",
						"XXX0111": "",
						"id": ""
					}
				}
			}
		],
		"sumRecordList": [
			{
				"subcontractRequisitionProduct": {
					"demandQuantityDU": 5533.5333,
					"demandQuantityMU": "6632.3"
				}
			}
		],
		"pageCount": 0,
		"beginPageIndex": 1,
		"endPageIndex": 0
	}
}
6. 错误返回码
错误码	错误信息	描述
999	取决于错误类型，不同错误信息不同	
7. 错误返回示例
{
 "code": "999",
 "message": "非法的时间： 11111"
}