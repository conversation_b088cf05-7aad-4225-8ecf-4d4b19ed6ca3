/**
 * 字段渲染器样式文件
 * 统一的字段显示样式
 */

/* === 字段列表容器 === */
.field-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    max-height: 600px;
    overflow-y: auto;
}

/* === 字段项基础样式 === */
.field-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.field-item:hover {
    border-color: #1976d2;
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
}

.field-item.selected {
    border-color: #1976d2;
    background: #e3f2fd;
}

.field-item.required {
    border-left: 4px solid #f44336;
}

.field-item.locked {
    opacity: 0.7;
    background: #f5f5f5;
}

/* === 重要性级别样式 === */
.field-item.importance-high {
    border-left: 4px solid #ff9800;
}

.field-item.importance-medium {
    border-left: 4px solid #4caf50;
}

.field-item.importance-low {
    border-left: 4px solid #9e9e9e;
}

/* === 字段选择器 === */
.field-selection {
    flex-shrink: 0;
    margin-right: 12px;
}

.field-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

/* === 字段信息区域 === */
.field-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0; /* 允许flex子项缩小 */
}

/* === 字段名称区域 === */
.field-name-section {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 4px;
}

.field-name {
    font-weight: 600;
    color: #1976d2;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.field-chinese {
    color: #666;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

/* === 字段详情区域 === */
.field-details {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
}

.field-sample {
    color: #888;
    font-size: 12px;
    font-family: 'Courier New', monospace;
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 3px;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.field-type {
    color: #1976d2;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    background: #e3f2fd;
    padding: 2px 6px;
    border-radius: 3px;
}

/* === 字段操作按钮 === */
.field-actions {
    flex-shrink: 0;
    display: flex;
    gap: 4px;
    margin-left: 12px;
}

.field-actions button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.field-actions button:hover {
    background: #f0f0f0;
}

.btn-edit:hover {
    background: #e3f2fd;
}

.btn-lock:hover {
    background: #fff3e0;
}

/* === 空状态样式 === */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-state-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.empty-state-text {
    font-size: 16px;
    color: #666;
}

/* === 字段配置表单 === */
.field-config-form {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    margin: 16px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #1976d2;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

.form-group input[readonly] {
    background: #f5f5f5;
    color: #666;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #eee;
}

.form-actions button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-save {
    background: #1976d2;
    color: white;
}

.btn-save:hover {
    background: #1565c0;
}

.btn-cancel {
    background: #f5f5f5;
    color: #666;
}

.btn-cancel:hover {
    background: #e0e0e0;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
    .field-name-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .field-details {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .field-actions {
        margin-left: 0;
        margin-top: 8px;
    }
    
    .field-name,
    .field-chinese {
        max-width: none;
    }
}

/* === 简化版样式（兼容现有代码） === */
.simple-field-list .field-item {
    border: 1px solid #eee;
    margin-bottom: 8px;
}

.simple-field-list .field-info {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.simple-field-list .field-name-section {
    flex-direction: column;
    align-items: flex-start;
    flex: 1;
}

.simple-field-list .field-details {
    flex: 1;
    justify-content: flex-end;
}
