{"module_name": "purchase_order", "display_name": "采购订单", "version": "2.0.0", "source": "json_parser", "total_fields": 119, "created_at": "2025-07-28T20:12:24.859062", "last_updated": "2025-07-28T20:12:24.859062", "fields": {"code": {"api_field_name": "code", "chinese_name": "单据编码", "data_type": "NVARCHAR(500)", "param_desc": "单据编码", "path": "data.recordList.code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "message": {"api_field_name": "message", "chinese_name": "返回信息", "data_type": "NVARCHAR(500)", "param_desc": "返回信息", "path": "message", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "数据项", "data_type": "NVARCHAR(MAX)", "param_desc": "数据项", "path": "data", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageIndex": {"api_field_name": "pageIndex", "chinese_name": "页码", "data_type": "BIGINT", "param_desc": "页码", "path": "pageIndex", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageSize": {"api_field_name": "pageSize", "chinese_name": "每页数", "data_type": "BIGINT", "param_desc": "每页数", "path": "pageSize", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordCount": {"api_field_name": "recordCount", "chinese_name": "数量", "data_type": "BIGINT", "param_desc": "数量", "path": "data.recordCount", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "recordList": {"api_field_name": "recordList", "chinese_name": "返回信息", "data_type": "NVARCHAR(MAX)", "param_desc": "返回信息", "path": "data.recordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_cCode": {"api_field_name": "product_cCode", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "data.recordList.product_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invoiceVendor": {"api_field_name": "invoiceVendor", "chinese_name": "开票供应商id", "data_type": "BIGINT", "param_desc": "开票供应商id", "path": "data.recordList.invoiceVendor", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "priceUOM_Precision": {"api_field_name": "priceUOM_Precision", "chinese_name": "计价单位精度", "data_type": "BIGINT", "param_desc": "计价单位精度", "path": "data.recordList.priceUOM_Precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "modifyStatus": {"api_field_name": "modifyStatus", "chinese_name": "变更状态：0：未变更、1：变更中、2：变更完成", "data_type": "BIGINT", "param_desc": "变更状态：0：未变更、1：变更中、2：变更完成", "path": "data.recordList.modifyStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "receiveStatus": {"api_field_name": "receiveStatus", "chinese_name": "收货状态：0：开立、1：已审核、10：已入库、11：待入库、12：待下单14：待结算15：已结算2：已关闭、3：审核中、4：锁定、5：未发货、6：已发货、7：已完成、8：待收、9：已收齐", "data_type": "BIGINT", "param_desc": "收货状态：0：开立、1：已审核、10：已入库、11：待入库、12：待下单14：待结算15：已结算2：已关闭、3：审核中、4：锁定、5：未发货、6：已发货、7：已完成、8：待收、9：已收齐", "path": "data.recordList.receiveStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "listOriSum": {"api_field_name": "listOriSum", "chinese_name": "含税金额", "data_type": "NVARCHAR(500)", "param_desc": "含税金额", "path": "data.recordList.listOriSum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "priceUOM_Code": {"api_field_name": "priceUOM_Code", "chinese_name": "计价单位编码", "data_type": "NVARCHAR(500)", "param_desc": "计价单位编码", "path": "data.recordList.priceUOM_Code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalInTaxMoney": {"api_field_name": "totalInTaxMoney", "chinese_name": "累计入库含税金额", "data_type": "NVARCHAR(500)", "param_desc": "累计入库含税金额", "path": "data.recordList.totalInTaxMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalQuantity": {"api_field_name": "totalQuantity", "chinese_name": "整单数量", "data_type": "NVARCHAR(500)", "param_desc": "整单数量", "path": "data.recordList.totalQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natCurrency": {"api_field_name": "natCurrency", "chinese_name": "本币", "data_type": "NVARCHAR(500)", "param_desc": "本币", "path": "data.recordList.natCurrency", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "listTotalPayOriMoney": {"api_field_name": "listTotalPayOriMoney", "chinese_name": "累计付款核销金额", "data_type": "NVARCHAR(500)", "param_desc": "累计付款核销金额", "path": "data.recordList.listTotalPayOriMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unit_code": {"api_field_name": "unit_code", "chinese_name": "主计量编码", "data_type": "NVARCHAR(500)", "param_desc": "主计量编码", "path": "data.recordList.unit_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "id": {"api_field_name": "id", "chinese_name": "id", "data_type": "BIGINT", "param_desc": "id", "path": "data.recordList.headItem.id", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isWfControlled": {"api_field_name": "isWfControlled", "chinese_name": "是否审批流控制：true or false", "data_type": "BIT", "param_desc": "是否审批流控制：true or false", "path": "data.recordList.isWfControlled", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalArrivedTaxMoney": {"api_field_name": "totalArrivedTaxMoney", "chinese_name": "累计到货含税金额", "data_type": "NVARCHAR(500)", "param_desc": "累计到货含税金额", "path": "data.recordList.totalArrivedTaxMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchaseOrders_arrivedStatus": {"api_field_name": "purchaseOrders_arrivedStatus", "chinese_name": "到货状态：1：到货完成、2：未到货、3：部分到货、4：到货完成", "data_type": "BIGINT", "param_desc": "到货状态：1：到货完成、2：未到货、3：部分到货、4：到货完成", "path": "data.recordList.purchaseOrders_arrivedStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bmake_st_purinvoice": {"api_field_name": "bmake_st_purinvoice", "chinese_name": "流程订货订单开蓝票", "data_type": "BIT", "param_desc": "流程订货订单开蓝票", "path": "data.recordList.bmake_st_purinvoice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "realProductAttribute": {"api_field_name": "realProductAttribute", "chinese_name": "实物商品属性", "data_type": "BIGINT", "param_desc": "实物商品属性", "path": "data.recordList.realProductAttribute", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "purchaseOrders_inWHStatus": {"api_field_name": "purchaseOrders_inWHStatus", "chinese_name": "入库状态：1：入库完成、2：未入库、3：部分入库、4：入库结束", "data_type": "BIGINT", "param_desc": "入库状态：1：入库完成、2：未入库、3：部分入库、4：入库结束", "path": "data.recordList.purchaseOrders_inWHStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalSendQty": {"api_field_name": "totalSendQty", "chinese_name": "发货数量", "data_type": "NVARCHAR(500)", "param_desc": "发货数量", "path": "data.recordList.totalSendQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_priceDigit": {"api_field_name": "natCurrency_priceDigit", "chinese_name": "本币", "data_type": "BIGINT", "param_desc": "本币", "path": "data.recordList.natCurrency_priceDigit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bmake_st_purinrecord_red": {"api_field_name": "bmake_st_purinrecord_red", "chinese_name": "流程退库", "data_type": "BIT", "param_desc": "流程退库", "path": "data.recordList.bmake_st_purinrecord_red", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "status": {"api_field_name": "status", "chinese_name": "状态：0：开立、1：已审核、2：已关闭、3：审核中", "data_type": "BIGINT", "param_desc": "状态：0：开立、1：已审核、2：已关闭、3：审核中", "path": "data.recordList.status", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currency_moneyDigit": {"api_field_name": "currency_moneyDigit", "chinese_name": "本币金额精度", "data_type": "BIGINT", "param_desc": "本币金额精度", "path": "data.recordList.currency_moneyDigit", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "listTotalPayApplyAmount": {"api_field_name": "listTotalPayApplyAmount", "chinese_name": "累计付款申请金额", "data_type": "NVARCHAR(500)", "param_desc": "累计付款申请金额", "path": "data.recordList.listTotalPayApplyAmount", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currency_code": {"api_field_name": "currency_code", "chinese_name": "币种编码", "data_type": "NVARCHAR(500)", "param_desc": "币种编码", "path": "data.recordList.currency_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "vouchdate": {"api_field_name": "vouchdate", "chinese_name": "单据日期，格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "单据日期，格式为:yyyy-MM-dd HH:mm:ss", "path": "data.recordList.vouchdate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "invoiceVendor_name": {"api_field_name": "invoiceVendor_name", "chinese_name": "开票供应商", "data_type": "NVARCHAR(500)", "param_desc": "开票供应商", "path": "data.recordList.invoiceVendor_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vendor": {"api_field_name": "vendor", "chinese_name": "供货供应商id", "data_type": "BIGINT", "param_desc": "供货供应商id", "path": "data.recordList.vendor", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchaseOrders_payStatus": {"api_field_name": "purchaseOrders_payStatus", "chinese_name": "核销状态：1：核销完成、2：未核销、3：部分核销", "data_type": "BIGINT", "param_desc": "核销状态：1：核销完成、2：未核销、3：部分核销", "path": "data.recordList.purchaseOrders_payStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchaseOrders_warehouse_code": {"api_field_name": "purchaseOrders_warehouse_code", "chinese_name": "仓库编码", "data_type": "NVARCHAR(500)", "param_desc": "仓库编码", "path": "data.recordList.purchaseOrders_warehouse_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "listOriMoney": {"api_field_name": "listOriMoney", "chinese_name": "无税金额", "data_type": "NVARCHAR(500)", "param_desc": "无税金额", "path": "data.recordList.listOriMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "currency": {"api_field_name": "currency", "chinese_name": "币种", "data_type": "NVARCHAR(500)", "param_desc": "币种", "path": "data.recordList.currency", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pubts": {"api_field_name": "pubts", "chinese_name": "时间戳，格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "时间戳，格式为:yyyy-MM-dd HH:mm:ss", "path": "data.pubts", "depth": 1, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "org_name": {"api_field_name": "org_name", "chinese_name": "采购组织", "data_type": "NVARCHAR(500)", "param_desc": "采购组织", "path": "data.recordList.org_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "generalPurchaseOrderType": {"api_field_name": "generalPurchaseOrderType", "chinese_name": "交易类型扩展参数", "data_type": "NVARCHAR(500)", "param_desc": "交易类型扩展参数", "path": "data.recordList.generalPurchaseOrderType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isFlowCoreBill": {"api_field_name": "isFlowCoreBill", "chinese_name": "是否流程核心单据", "data_type": "BIT", "param_desc": "是否流程核心单据", "path": "data.recordList.isFlowCoreBill", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "creator": {"api_field_name": "creator", "chinese_name": "创建者", "data_type": "NVARCHAR(500)", "param_desc": "创建者", "path": "data.recordList.creator", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product": {"api_field_name": "product", "chinese_name": "物料id", "data_type": "BIGINT", "param_desc": "物料id", "path": "data.recordList.product", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "oriSum": {"api_field_name": "oriSum", "chinese_name": "含税金额", "data_type": "NVARCHAR(500)", "param_desc": "含税金额", "path": "data.recordList.oriSum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "inInvoiceOrg_name": {"api_field_name": "inInvoiceOrg_name", "chinese_name": "收票组织", "data_type": "NVARCHAR(500)", "param_desc": "收票组织", "path": "data.recordList.inInvoiceOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_defaultAlbumId": {"api_field_name": "product_defaultAlbumId", "chinese_name": "物料首图片", "data_type": "NVARCHAR(500)", "param_desc": "物料首图片", "path": "data.recordList.product_defaultAlbumId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchaseOrders_id": {"api_field_name": "purchaseOrders_id", "chinese_name": "订单行id", "data_type": "BIGINT", "param_desc": "订单行id", "path": "data.recordList.purchaseOrders_id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalRecieveQty": {"api_field_name": "totalRecieveQty", "chinese_name": "累计到货数量", "data_type": "NVARCHAR(500)", "param_desc": "累计到货数量", "path": "data.recordList.totalRecieveQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "demandOrg_name": {"api_field_name": "demandOrg_name", "chinese_name": "需求组织", "data_type": "NVARCHAR(500)", "param_desc": "需求组织", "path": "data.recordList.demandOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "createTime": {"api_field_name": "createTime", "chinese_name": "创建时间，格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "创建时间，格式为:yyyy-MM-dd HH:mm:ss", "path": "data.recordList.createTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purUOM_Precision": {"api_field_name": "purUOM_Precision", "chinese_name": "采购单位精度", "data_type": "BIGINT", "param_desc": "采购单位精度", "path": "data.recordList.purUOM_Precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "currency_priceDigit": {"api_field_name": "currency_priceDigit", "chinese_name": "币种单价精度", "data_type": "BIGINT", "param_desc": "币种单价精度", "path": "data.recordList.currency_priceDigit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bEffectStock": {"api_field_name": "bEffectStock", "chinese_name": "影响可用量", "data_type": "BIT", "param_desc": "影响可用量", "path": "data.recordList.bEffectStock", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "inOrg": {"api_field_name": "inOrg", "chinese_name": "收货组织id", "data_type": "NVARCHAR(500)", "param_desc": "收货组织id", "path": "data.recordList.inOrg", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bustype_name": {"api_field_name": "bustype_name", "chinese_name": "交易类型", "data_type": "NVARCHAR(500)", "param_desc": "交易类型", "path": "data.recordList.bustype_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "purchaseOrders_invPriceExchRate": {"api_field_name": "purchaseOrders_invPriceExchRate", "chinese_name": "计价单位换算率", "data_type": "NVARCHAR(500)", "param_desc": "计价单位换算率", "path": "data.recordList.purchaseOrders_invPriceExchRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "subQty": {"api_field_name": "subQty", "chinese_name": "采购数量", "data_type": "NVARCHAR(500)", "param_desc": "采购数量", "path": "data.recordList.subQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "inInvoiceOrg": {"api_field_name": "inInvoiceOrg", "chinese_name": "收票组织id", "data_type": "NVARCHAR(500)", "param_desc": "收票组织id", "path": "data.recordList.inInvoiceOrg", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_cName": {"api_field_name": "product_cName", "chinese_name": "物料名称", "data_type": "NVARCHAR(500)", "param_desc": "物料名称", "path": "data.recordList.product_cName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "listTaxRate": {"api_field_name": "listTaxRate", "chinese_name": "税率", "data_type": "NVARCHAR(500)", "param_desc": "税率", "path": "data.recordList.listTaxRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bmake_st_purinvoice_red": {"api_field_name": "bmake_st_purinvoice_red", "chinese_name": "流程订货订单开红票", "data_type": "BIT", "param_desc": "流程订货订单开红票", "path": "data.recordList.bmake_st_purinvoice_red", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_model": {"api_field_name": "product_model", "chinese_name": "型号", "data_type": "NVARCHAR(500)", "param_desc": "型号", "path": "data.recordList.product_model", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "storagenum": {"api_field_name": "storagenum", "chinese_name": "已入库数量", "data_type": "NVARCHAR(500)", "param_desc": "已入库数量", "path": "data.recordList.storagenum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchaseOrders_invExchRate": {"api_field_name": "purchaseOrders_invExchRate", "chinese_name": "采购换算率", "data_type": "NVARCHAR(500)", "param_desc": "采购换算率", "path": "data.recordList.purchaseOrders_invExchRate", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vendor_name": {"api_field_name": "vendor_name", "chinese_name": "供应商", "data_type": "NVARCHAR(500)", "param_desc": "供应商", "path": "data.recordList.vendor_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vendor_code": {"api_field_name": "vendor_code", "chinese_name": "供应商编码", "data_type": "NVARCHAR(500)", "param_desc": "供应商编码", "path": "data.recordList.vendor_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "oriUnitPrice": {"api_field_name": "oriUnitPrice", "chinese_name": "无税单价", "data_type": "NVARCHAR(500)", "param_desc": "无税单价", "path": "data.recordList.oriUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "barCode": {"api_field_name": "barCode", "chinese_name": "单据条码", "data_type": "NVARCHAR(500)", "param_desc": "单据条码", "path": "data.recordList.barCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isContract": {"api_field_name": "isContract", "chinese_name": "是否需要与供应商协同：true：是、false：否", "data_type": "BIT", "param_desc": "是否需要与供应商协同：true：是、false：否", "path": "data.recordList.isContract", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unit_name": {"api_field_name": "unit_name", "chinese_name": "主计量", "data_type": "NVARCHAR(500)", "param_desc": "主计量", "path": "data.recordList.unit_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "unit": {"api_field_name": "unit", "chinese_name": "主计量id", "data_type": "BIGINT", "param_desc": "主计量id", "path": "data.recordList.unit", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "purchaseOrders_invoiceStatus": {"api_field_name": "purchaseOrders_invoiceStatus", "chinese_name": "发票状态：1：开票完成、2：未开票、3：部分开票、4：开票结束", "data_type": "BIGINT", "param_desc": "发票状态：1：开票完成、2：未开票、3：部分开票、4：开票结束", "path": "data.recordList.purchaseOrders_invoiceStatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_moneyDigit": {"api_field_name": "natCurrency_moneyDigit", "chinese_name": "本币", "data_type": "BIGINT", "param_desc": "本币", "path": "data.recordList.natCurrency_moneyDigit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "qty": {"api_field_name": "qty", "chinese_name": "数量", "data_type": "NVARCHAR(500)", "param_desc": "数量", "path": "data.recordList.qty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unit_Precision": {"api_field_name": "unit_Precision", "chinese_name": "主计量精度", "data_type": "BIGINT", "param_desc": "主计量精度", "path": "data.recordList.unit_Precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "oriTaxUnitPrice": {"api_field_name": "oriTaxUnitPrice", "chinese_name": "含税单价", "data_type": "NVARCHAR(500)", "param_desc": "含税单价", "path": "data.recordList.oriTaxUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "moneysum": {"api_field_name": "moneysum", "chinese_name": "金额", "data_type": "NVARCHAR(500)", "param_desc": "金额", "path": "data.recordList.moneysum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_code": {"api_field_name": "natCurrency_code", "chinese_name": "本币", "data_type": "NVARCHAR(500)", "param_desc": "本币", "path": "data.recordList.natCurrency_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_modelDescription": {"api_field_name": "product_modelDescription", "chinese_name": "规格说明", "data_type": "NVARCHAR(500)", "param_desc": "规格说明", "path": "data.recordList.product_modelDescription", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "demandOrg": {"api_field_name": "demandOrg", "chinese_name": "需求组织id", "data_type": "NVARCHAR(500)", "param_desc": "需求组织id", "path": "data.recordList.demandOrg", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bizFlow": {"api_field_name": "bizFlow", "chinese_name": "流程ID", "data_type": "NVARCHAR(500)", "param_desc": "流程ID", "path": "data.recordList.bizFlow", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "realProductAttributeType": {"api_field_name": "realProductAttributeType", "chinese_name": "实物商品属性", "data_type": "BIGINT", "param_desc": "实物商品属性", "path": "data.recordList.realProductAttributeType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "priceUOM": {"api_field_name": "priceUOM", "chinese_name": "计价单位id", "data_type": "BIGINT", "param_desc": "计价单位id", "path": "data.recordList.priceUOM", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bizstatus": {"api_field_name": "bi<PERSON><PERSON><PERSON>", "chinese_name": "状态：0：初始开立、1：审批中、2：审批完成、3：不通过流程终止、4：驳回到制单", "data_type": "BIGINT", "param_desc": "状态：0：初始开立、1：审批中、2：审批完成、3：不通过流程终止、4：驳回到制单", "path": "data.recordList.bizstatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "totalInQty": {"api_field_name": "totalInQty", "chinese_name": "累计入库数量", "data_type": "NVARCHAR(500)", "param_desc": "累计入库数量", "path": "data.recordList.totalInQty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bizFlow_version": {"api_field_name": "bizFlow_version", "chinese_name": "版本信息", "data_type": "NVARCHAR(500)", "param_desc": "版本信息", "path": "data.recordList.bizFlow_version", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "currency_name": {"api_field_name": "currency_name", "chinese_name": "币种", "data_type": "NVARCHAR(500)", "param_desc": "币种", "path": "data.recordList.currency_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "org": {"api_field_name": "org", "chinese_name": "采购组织", "data_type": "NVARCHAR(500)", "param_desc": "采购组织", "path": "data.recordList.org", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bmake_st_purinrecord": {"api_field_name": "bmake_st_purinrecord", "chinese_name": "流程入库", "data_type": "BIT", "param_desc": "流程入库", "path": "data.recordList.bmake_st_purinrecord", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "purchaseOrders_purUOM": {"api_field_name": "purchaseOrders_purUOM", "chinese_name": "采购单位编码", "data_type": "BIGINT", "param_desc": "采购单位编码", "path": "data.recordList.purchaseOrders_purUOM", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bustype": {"api_field_name": "bustype", "chinese_name": "交易类型id", "data_type": "NVARCHAR(500)", "param_desc": "交易类型id", "path": "data.recordList.bustype", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "listOriTax": {"api_field_name": "listOriTax", "chinese_name": "税额", "data_type": "NVARCHAR(500)", "param_desc": "税额", "path": "data.recordList.listOriTax", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "retailInvestors": {"api_field_name": "retailInvestors", "chinese_name": "是否散户：true or false", "data_type": "BIT", "param_desc": "是否散户：true or false", "path": "data.recordList.retailInvestors", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "inOrg_name": {"api_field_name": "inOrg_name", "chinese_name": "收货组织", "data_type": "NVARCHAR(500)", "param_desc": "收货组织", "path": "data.recordList.inOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "listTotalPayAmount": {"api_field_name": "listTotalPayAmount", "chinese_name": "累计付款金额", "data_type": "NVARCHAR(500)", "param_desc": "累计付款金额", "path": "data.recordList.listTotalPayAmount", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "priceUOM_Name": {"api_field_name": "priceUOM_Name", "chinese_name": "计价单位名称", "data_type": "NVARCHAR(500)", "param_desc": "计价单位名称", "path": "data.recordList.priceUOM_Name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "listTotalPayNATMoney": {"api_field_name": "listTotalPayNATMoney", "chinese_name": "本币累计付款核销金额", "data_type": "NVARCHAR(500)", "param_desc": "本币累计付款核销金额", "path": "data.recordList.listTotalPayNATMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "approvenum": {"api_field_name": "approvenum", "chinese_name": "已审批数量", "data_type": "NVARCHAR(500)", "param_desc": "已审批数量", "path": "data.recordList.approvenum", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "listdiscountTaxType": {"api_field_name": "listdiscountTaxType", "chinese_name": "扣税类别：0：应税外加、1：应税内含", "data_type": "NVARCHAR(500)", "param_desc": "扣税类别：0：应税外加、1：应税内含", "path": "data.recordList.listdiscountTaxType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bizFlow_name": {"api_field_name": "bizFlow_name", "chinese_name": "流程名称", "data_type": "NVARCHAR(500)", "param_desc": "流程名称", "path": "data.recordList.bizFlow_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "headItem": {"api_field_name": "headItem", "chinese_name": "表头自定义项", "data_type": "NVARCHAR(MAX)", "param_desc": "表头自定义项", "path": "data.recordList.headItem", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define46": {"api_field_name": "define46", "chinese_name": "自定义项", "data_type": "NVARCHAR(500)", "param_desc": "自定义项", "path": "data.recordList.headItem.define46", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageCount": {"api_field_name": "pageCount", "chinese_name": "页数", "data_type": "BIGINT", "param_desc": "页数", "path": "data.pageCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "beginPageIndex": {"api_field_name": "beginPageIndex", "chinese_name": "起始页", "data_type": "BIGINT", "param_desc": "起始页", "path": "data.beginPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "endPageIndex": {"api_field_name": "endPageIndex", "chinese_name": "结束页", "data_type": "BIGINT", "param_desc": "结束页", "path": "data.endPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "isSum": {"api_field_name": "isSum", "chinese_name": "查询表头", "data_type": "BIT", "param_desc": "查询表头", "path": "isSum", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "simpleVOs": {"api_field_name": "simpleVOs", "chinese_name": "查询条件", "data_type": "NVARCHAR(MAX)", "param_desc": "查询条件", "path": "simpleVOs", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "field": {"api_field_name": "field", "chinese_name": "排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：purchaseOrders.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)", "data_type": "NVARCHAR(500)", "param_desc": "排序条件字段:必须传实体上有的字段;主表字段查询时字段名(例: id);子表字段查询是子表对象.字段名(例：purchaseOrders.id);参照类型只能传id(例:按物料查询只能传物料id,不能传物料code)", "path": "queryOrders.field", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "op": {"api_field_name": "op", "chinese_name": "比较符：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者", "data_type": "NVARCHAR(500)", "param_desc": "比较符：eq：等于、neq：不等于、lt：小于、gt：大于、between：介于、in：包含、nin：不包含、like：模糊匹配、leftlike：左模糊匹配、rightlike：右模糊匹配、is_null：为空、is_not_null：不为空、and：并且、or：或者", "path": "simpleVOs.op", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value1": {"api_field_name": "value1", "chinese_name": "参数值1", "data_type": "NVARCHAR(500)", "param_desc": "参数值1", "path": "simpleVOs.value1", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "queryOrders": {"api_field_name": "queryOrders", "chinese_name": "排序字段", "data_type": "NVARCHAR(MAX)", "param_desc": "排序字段", "path": "queryOrders", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "order": {"api_field_name": "order", "chinese_name": "顺序：正序(asc)、倒序(desc)", "data_type": "NVARCHAR(500)", "param_desc": "顺序：正序(asc)、倒序(desc)", "path": "queryOrders.order", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}}}