import json
import re
from dataclasses import dataclass
from pathlib import Path

import structlog

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强大的JSON解释器 - 专门解析 v3/模块字段/json/ 目录下的复杂嵌套JSON结构
支持容错、深度解析、字段映射等功能
"""


logger = structlog.get_logger()


@dataclass
class FieldInfo:
    """字段信息数据类"""

    name: str
    chinese_name: str
    param_type: str
    param_desc: str
    path: str
    depth: int
    is_array: bool = False
    parent_id: Optional[str] = None
    children: List['FieldInfo'] = None

    def __post_init___(self):
    """TODO: Add function description."""
        if self.children is None:
            self.children = []


class RobustJSONParser:
    """强大的JSON解释器"""

    def __init___(self):
    """TODO: Add function description."""
        self.base_dir = Path(__file__).parent
        # 如果在services目录中，需要向上4级到达项目根目录
        if "services" in str(self.base_dir):
            self.base_dir = self.base_dir.parent.parent.parent.parent
        self.json_dir = self.base_dir / "v3" / "模块字段" / "json"

        # 字段映射缓存
        self.field_mappings: Dict[str, Dict[str, FieldInfo]] = {}

        # 错误统计
        self.parse_errors: List[Dict] = []

        self.logger.info("强大JSON解释器初始化完成", json_dir=str(self.json_dir))

    def parse_all_modules(self) -> Dict[str, Dict[str, FieldInfo]]:
        """解析所有模块的JSON文件"""
        try:
            if not self.json_dir.exists():
                logger.error("JSON目录不存在", json_dir=str(self.json_dir))
                return {}

            all_mappings = {}
            json_files = list(self.json_dir.glob("*.json"))

            logger.info("开始解析JSON文件", files_count=len(json_files))

            for json_file in json_files:
                try:
                    module_name = self._extract_module_name(json_file.name)
                    field_mappings = self.parse_json_file(json_file)

                    if field_mappings:
                        all_mappings[module_name] = field_mappings
                        logger.info(
                            "模块解析成功",
                            module=module_name,
                            fields_count=len(field_mappings),
                        )
                    else:
                        logger.warning("模块解析为空", module=module_name)

                except Exception:
                    logger.error("模块解析失败", file=json_file.name, error=str(e))
                    self.parse_errors.append(
                        {
                            "file": json_file.name,
                            "error": str(e),
                            "type": "module_parse_error",
                        }
                    )

            logger.info(
                "JSON解析完成",
                total_modules=len(all_mappings),
                total_errors=len(self.parse_errors),
            )

            return all_mappings

        except Exception:
            logger.error("解析所有模块失败", error=str(e))
            return {}

    def parse_json_file(self, json_file: Path) -> Dict[str, FieldInfo]:
        """解析单个JSON文件"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 预处理JSON内容，修复常见格式问题
            content = self._preprocess_json_content(content)

            try:
                data = json.loads(content)
            except json.JSONDecodeError as e:
                logger.error(
                    "JSON格式错误",
                    file=json_file.name,
                    error=str(e),
                    line=getattr(e, 'lineno', 'unknown'),
                )

                # 尝试修复JSON格式
                fixed_content = self._attempt_json_fix(content)
                if fixed_content:
                    try:
                        data = json.loads(fixed_content)
                        logger.info("JSON修复成功", file=json_file.name)
                    except Exception:
                        logger.error("JSON修复失败", file=json_file.name)
                        return {}
                else:
                    return {}

            # 提取字段映射
            field_mappings = self._extract_field_mappings(data, json_file.name)

            return field_mappings

        except Exception:
            logger.error("解析JSON文件失败", file=json_file.name, error=str(e))
            self.parse_errors.append(
                {"file": json_file.name, "error": str(
                    e), "type": "file_parse_error"}
            )
            return {}

    def _extract_field_mappings(
        self, data: Any, filename: str) -> Dict[str, FieldInfo]:
        """从JSON数据中提取字段映射"""
        field_mappings = {}

        try:
            # 处理数组格式的JSON
            if isinstance(data, list) and len(data) > 0:
                data = data[0]  # 取第一个元素

            if not isinstance(data, dict):
                logger.warning(
    "JSON数据格式不正确",
    filename=filename,
     type=type(data))
                return {}

            # 查找 paramReturnDTOS 结构
            param_return_dtos = self._find_param_return_dtos(data)

            if param_return_dtos:
                logger.info("找到paramReturnDTOS结构", filename=filename)
                field_mappings.update(
                    self._parse_param_return_dtos(param_return_dtos, filename)
                )

            # 查找其他可能的字段结构
            other_fields = self._find_other_field_structures(data)
            if other_fields:
                logger.info(
                    "找到其他字段结构", filename=filename, count=len(other_fields)
                )
                field_mappings.update(other_fields)

            return field_mappings

        except Exception:
            logger.error("提取字段映射失败", filename=filename, error=str(e))
            return {}

    def _find_param_return_dtos(self, data: Dict) -> Optional[List[Dict]]:
        """查找paramReturnDTOS结构"""
        try:
            # 深度搜索paramReturnDTOS

            def search_param_return_dtoss(obj, path=""):
    """TODO: Add function description."""
                if isinstance(obj, dict):
                    if "paramReturnDTOS" in obj:
                        param_return = obj["paramReturnDTOS"]
                        if (
                            isinstance(param_return, dict)
                            and "paramReturnDTOS" in param_return
                        ):
                            return param_return["paramReturnDTOS"]
                        elif isinstance(param_return, list):
                            return param_return

                    # 递归搜索
                    for key, value in obj.items():
                        result = search_param_return_dtos(
                            value, f"{path}.{key}")
                        if result:
                            return result

                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        result = search_param_return_dtos(item, f"{path}[{i}]")
                        if result:
                            return result

                return None

            return search_param_return_dtos(data)

        except Exception:
            logger.error("查找paramReturnDTOS失败", error=str(e))
            return None


    def _parse_param_return_dtos(
        self, param_list: List[Dict], filename: str
    ) -> Dict[str, FieldInfo]:
        """解析paramReturnDTOS列表"""
        field_mappings = {}

        try:
            for param in param_list:
                if not isinstance(param, dict):
                    continue

                field_info = self._parse_single_param(param, filename)
                if field_info:
                    field_mappings[field_info.name] = field_info

                    # 处理嵌套子字段
                    children = self._parse_children_fields(
                        param, filename, field_info.name
                    )
                    field_info.children = children

                    # 将子字段也添加到映射中
                    for child in children:
                        child_path = f"{field_info.name}.{child.name}"
                        field_mappings[child_path] = child

            return field_mappings

        except Exception:
            logger.error("解析paramReturnDTOS失败", filename=filename, error=str(e))
            return {}


    def _parse_single_param(
        self, param: Dict, filename: str, parent_path: str = ""
    ) -> Optional[FieldInfo]:
        """解析单个参数"""
        try:
            name = param.get("name", "")
            if not name:
                return None

            param_desc = param.get("paramDesc", "")
            param_type = param.get("paramType", "string")
            is_array = param.get("array", False)

            # 构建完整路径
            full_path = f"{parent_path}.{name}" if parent_path else name
            depth = full_path.count(".")

            # 创建字段信息
            field_info = FieldInfo(
                name=name,
                chinese_name=param_desc or name,  # 使用paramDesc作为中文名
                param_type=self._normalize_param_type(param_type),
                param_desc=param_desc,
                path=full_path,
                depth=depth,
                is_array=is_array,
                parent_id=param.get("parentId"),
            )

            return field_info

        except Exception:
            logger.error("解析单个参数失败", filename=filename, error=str(e))
            return None


    def _parse_children_fields(
        self, param: Dict, filename: str, parent_path: str
    ) -> List[FieldInfo]:
        """解析子字段"""
        children = []

        try:
            # 查找children结构
            children_data = param.get("children")
            if children_data:
                if isinstance(children_data, dict) and "children" in children_data:
                    children_list = children_data["children"]
                elif isinstance(children_data, list):
                    children_list = children_data
                else:
                    return children

                for child_param in children_list:
                    if isinstance(child_param, dict):
                        child_info = self._parse_single_param(
                            child_param, filename, parent_path
                        )
                        if child_info:
                            children.append(child_info)

                            # 递归处理更深层的子字段
                            grandchildren = self._parse_children_fields(
                                child_param, filename, child_info.path
                            )
                            child_info.children = grandchildren
                            children.extend(grandchildren)

            return children

        except Exception:
            logger.error("解析子字段失败", filename=filename, error=str(e))
            return []


    def _find_other_field_structures(self, data: Dict) -> Dict[str, FieldInfo]:
        """查找其他可能的字段结构"""
        field_mappings = {}

        try:
            # 查找paramDTOS结构
            param_dtos = self._find_nested_structure(data, "paramDTOS")
            if param_dtos:
                logger.info("找到paramDTOS结构")
                param_fields = self._parse_param_return_dtos(param_dtos, "paramDTOS")
                field_mappings.update(param_fields)

            return field_mappings

        except Exception:
            logger.error("查找其他字段结构失败", error=str(e))
            return {}


    def _find_nested_structure(
        self, data: Any, target_key: str
    ) -> Optional[List[Dict]]:
        """查找嵌套结构"""


        def search_recursivee(obj):

    """TODO: Add function description."""
            if isinstance(obj, dict):
                if target_key in obj:
                    target_data = obj[target_key]
                    if isinstance(target_data, dict) and target_key in target_data:
                        return target_data[target_key]
                    elif isinstance(target_data, list):
                        return target_data

                for value in obj.values():
                    result = search_recursive(value)
                    if result:
                        return result

            elif isinstance(obj, list):
                for item in obj:
                    result = search_recursive(item)
                    if result:
                        return result

            return None

        return search_recursive(data)

    def _normalize_param_type(self, param_type: str) -> str:
        """标准化参数类型"""
        type_mapping = {
            "string": "NVARCHAR(500)",
            "int": "BIGINT",
            "integer": "BIGINT",
            "long": "BIGINT",
            "double": "DECIMAL(18,4)",
            "float": "DECIMAL(18,4)",
            "decimal": "DECIMAL(18,4)",
            "boolean": "BIT",
            "bool": "BIT",
            "date": "DATE",
            "datetime": "DATETIME",
            "timestamp": "DATETIME",
            "object": "NVARCHAR(MAX)",
            "array": "NVARCHAR(MAX)",
        }

        return type_mapping.get(param_type.lower(), "NVARCHAR(500)")

    def _extract_module_name(self, filename: str) -> str:
        """从文件名提取模块名"""
        # 移除.json后缀
        name = filename.replace(".json", "")

        # 模块名映射
        module_mapping = {
            "销售订单": "sales_order",
            "采购订单列表": "purchase_order",
            "生产订单列表查询": "production_order",
            "委外订单列表": "subcontract_order",
            "采购入库单列表": "purchase_receipt",
            "销售出库列表查询": "sales_out",
            "产品入库单列表查询": "product_receipt",
            "材料出库单列表查询": "materialout",
            "委外入库列表查询": "subcontract_receipt",
            "委外申请列表查询": "subcontract_requisition",
            "请购单列表查询": "applyorder",
            "需求计划": "requirements_planning",
            "业务日志": "business_log",
        }

        return module_mapping.get(name, name.lower().replace(" ", "_"))

    def _preprocess_json_content(self, content: str) -> str:
        """预处理JSON内容，修复常见格式问题"""
        try:
            # 移除BOM
            if content.startswith('\ufeff'):
                content = content[1:]

            # 修复requestParamsDemo字段中的嵌套JSON问题
            content = self._fix_nested_json_in_strings(content)

            # 修复常见的JSON格式问题
            # 1. 移除尾随逗号
            content = re.sub(r',(\s*[}\]])', r'\1', content)

            # 2. 修复单引号
            content = re.sub(r"'([^']*)':", r'"\1":', content)

            return content

        except Exception:
            logger.error("预处理JSON内容失败", error=str(e))
            return content


    def _fix_nested_json_in_strings(self, content: str) -> str:
        """修复字符串中的嵌套JSON问题"""
        try:
            # 查找requestParamsDemo等包含嵌套JSON的字段
            pattern = r'"(requestParamsDemo|responseDemo)"\s*:\s*"([^"]*(?:\\.[^"]*)*)"'


            def fix_nested_jsonn(match):

    """TODO: Add function description."""
                field_name = match.group(1)
                field_value = match.group(2)

                # 转义字符串中的引号
                fixed_value = field_value.replace('\\"', '\\\\"')

                return f'"{field_name}": "{fixed_value}"'

            content = re.sub(pattern, fix_nested_json, content)

            return content

        except Exception:
            logger.error("修复嵌套JSON失败", error=str(e))
            return content


    def _attempt_json_fix(self, content: str) -> Optional[str]:
        """尝试修复JSON格式"""
        try:
            # 尝试多种修复策略
            fixes = [
                # 修复1: 处理requestParamsDemo字段的特殊情况
                lambda c: self._fix_request_params_demo(c),
                # 修复2: 移除尾随逗号
                lambda c: re.sub(r',(\s*[}\]])', r'\1', c),
                # 修复3: 截断到最后一个完整的结构
                lambda c: self._truncate_to_valid_json(c),
                # 修复4: 移除问题字段
                lambda c: self._remove_problematic_fields(c),
            ]

            for fix_func in fixes:
                try:
                    fixed_content = fix_func(content)
                    json.loads(fixed_content)  # 验证是否有效
                    return fixed_content
                except Exception:
                    continue

            return None

        except Exception:
            logger.error("JSON修复失败", error=str(e))
            return None


    def _fix_request_params_demo(self, content: str) -> str:
        """修复requestParamsDemo字段"""
        try:
            # 找到requestParamsDemo字段并用简单字符串替换
            pattern = r'"requestParamsDemo"\s*:\s*"[^"]*(?:\\.[^"]*)*"'
            replacement = '"requestParamsDemo": "API示例参数"'

            content = re.sub(pattern, replacement, content)

            # 同样处理responseDemo字段
            pattern = r'"responseDemo"\s*:\s*"[^"]*(?:\\.[^"]*)*"'
            replacement = '"responseDemo": "API响应示例"'

            content = re.sub(pattern, replacement, content)

            return content

        except Exception:
            logger.error("修复requestParamsDemo失败", error=str(e))
            return content


    def _remove_problematic_fields(self, content: str) -> str:
        """移除有问题的字段"""
        try:
            # 移除可能导致JSON解析失败的字段
            problematic_fields = 
                ["requestParamsDemo",
                "responseDemo",
                "description"
            ]

            for field in problematic_fields:
                pattern = rf'"{field}"\s*:\s*"[^"]*(?:\\.[^"]*)*"\s*,?'
                content = re.sub(pattern, '', content)

            # 清理多余的逗号
            content = re.sub(r',(\s*[,}])', r'\1', content)
            content = re.sub(r',(\s*})', r'\1', content)

            return content

        except Exception:
            logger.error("移除问题字段失败", error=str(e))
            return content


    def _truncate_to_valid_json(self, content: str) -> str:
        """截断到最后一个有效的JSON结构"""
        try:
            # 找到最后一个完整的对象或数组结束位置
            brace_count = 0
            bracket_count = 0
            last_valid_pos = 0

            for i, char in enumerate(content):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0 and bracket_count == 0:
                        last_valid_pos = i + 1
                elif char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1
                    if bracket_count == 0 and brace_count == 0:
                        last_valid_pos = i + 1

            if last_valid_pos > 0:
                return content[:last_valid_pos]

            return content

        except Exception:
            return content


    def get_field_mapping(
        self, module_name: str, field_name: str
    ) -> Optional[FieldInfo]:
        """获取字段映射信息"""
        if module_name not in self.field_mappings:
            return None

        # 精确匹配
        if field_name in self.field_mappings[module_name]:
            return self.field_mappings[module_name][field_name]

        # 模糊匹配
        for path, field_info in self.field_mappings[module_name].items():
            if field_name in path or path.endswith(f".{field_name}"):
                return field_info

        return None

    def get_parse_errors(self) -> List[Dict]:
        """获取解析错误"""
        return self.parse_errors


    def clear_errors(self):
        """清除错误记录"""
        self.parse_errors.clear()


# 使用示例
if __name__ == "__main__":
    parser = RobustJSONParser()

    # 解析所有模块
    all_mappings = parser.parse_all_modules()

    logger.info(f"解析完成，共找到 {len(all_mappings)} 个模块")

    for module_name, field_mappings in all_mappings.items():
        self.logger.info(f"\n模块: {module_name}")
        self.logger.info(f"字段数量: {len(field_mappings)}")

        # 显示前5个字段
        for i, (field_path, field_info) in enumerate(field_mappings.items()):
            if i >= 5:
                logger.info("  ...")
                break
            logger.info(
                f"  {field_path}: {field_info.chinese_name} ({field_info.param_type})"
            )

    # 显示错误
    errors = parser.get_parse_errors()
    if errors:
        self.logger.info(f"\n解析错误 ({len(errors)} 个):")
        for error in errors:
            logger.info(f"  {error['file']}: {error['error']}")
