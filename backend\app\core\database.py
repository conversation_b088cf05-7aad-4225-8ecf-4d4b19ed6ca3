import structlog
from sqlalchemy.orm import sessionmaker

from .config import settings
from .database_manager import init_connection_manager

"""
YS-API V3.0 数据库配置
"""


logger = structlog.get_logger()

# 同步数据库引擎 (用于YSAPI数据库检查和创建)
sync_engine = None
sync_session_factory = None

# 异步数据库引擎 (主要业务逻辑)
async_engine = None
async_session_factory = None


def create_sync_engine():
    """创建同步数据库引擎"""
    global sync_engine, sync_session_factory

    try:
        # 为了检查和创建数据库，我们需要连接到master数据库
        master_url = settings.database_url.replace(
            f"/{settings.DATABASE_NAME}", "/master"
        )

        sync_engine = create_engine(
            master_url,
            pool_size=5,
            max_overflow=10,
            pool_pre_ping=True,
            pool_recycle=3600,
            echo=settings.DEBUG,
        )

        sync_session_factory = sessionmaker(
            bind=sync_engine, autocommit=False, autoflush=False
        )

        logger.info("同步数据库引擎创建成功")

    except Exception:
        logger.error("同步数据库引擎创建失败", error=str(e))
        raise


def create_async_engine_for_ysapi():
    """为YSAPI数据库创建异步引擎"""
    global async_engine, async_session_factory

    try:
        # 转换为异步数据库URL
        async_url = settings.database_url.replace(
            "mssql+pyodbc", "mssql+aioodbc")

        async_engine = create_async_engine(
            async_url,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=900,  # 15分钟回收连接
            pool_timeout=60,  # 增加超时时间
            pool_reset_on_return="commit",  # 返回连接时自动提交
            echo=settings.DEBUG,
            # 连接池优化参数
            connect_args={
                "timeout": 30,
                "autocommit": False,
                "isolation_level": "READ_COMMITTED",
            },
        )

        async_session_factory = sessionmaker(
            bind=async_engine,
            class_=AsyncSession,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False,
        )

        logger.info("异步数据库引擎创建成功", database=settings.DATABASE_NAME)

    except Exception:
        logger.error("异步数据库引擎创建失败", error=str(e))
        raise


async def init_database():
    """初始化数据库连接"""
    logger.info("初始化数据库连接...")

    # 创建同步引擎（用于数据库管理）
    create_sync_engine()

    # 确保YSAPI数据库存在
    await ensure_ysapi_database()

    # 启用异步引擎
    try:
        create_async_engine_for_ysapi()

        # 初始化连接管理器
        try:
            await init_connection_manager(async_engine)
        except Exception:
            logger.warning(f"连接管理器初始化失败: {e}")

        logger.info("异步数据库引擎和连接管理器已启用")
    except Exception:
        logger.warning(f"异步引擎创建失败，将使用同步引擎: {e}")

    logger.info("数据库初始化完成")


async def ensure_ysapi_database():
    """确保YSAPI数据库存在，如果不存在则创建"""
    logger.info("检查YSAPI数据库是否存在...")

    try:
        with sync_session_factory() as session:
            # 检查数据库是否存在
            result = session.execute(
                text("SELECT COUNT(*) FROM sys.databases WHERE name = :db_name"),
                {"db_name": settings.DATABASE_NAME},
            )

            if result.scalar() == 0:
                # 数据库不存在，创建它
                logger.info(f"创建数据库: {settings.DATABASE_NAME}")

                session.execute(
                    text(f"CREATE DATABASE [{settings.DATABASE_NAME}]"))
                session.commit()

                logger.info(f"数据库 {settings.DATABASE_NAME} 创建成功")
            else:
                logger.info(f"数据库 {settings.DATABASE_NAME} 已存在")

    except Exception:
        logger.error("数据库检查/创建失败", error=str(e))
        raise


def get_sync_session():
    """获取同步数据库会话"""
    session = sync_session_factory()
    try:
        yield session
    except Exception:
        session.rollback()
        logger.error("数据库会话错误", error=str(e))
        raise
    finally:
        session.close()


async def get_async_session() -> AsyncSession:
    """获取异步数据库会话"""
    if async_session_factory is None:
        raise RuntimeError("异步数据库引擎未初始化")

    session = async_session_factory()
    try:
        return session
    except Exception:
        logger.error("创建异步数据库会话失败", error=str(e))
        raise


async def get_async_session_context():
    """获取异步数据库会话上下文管理器"""
    if async_session_factory is None:
        raise RuntimeError("异步数据库引擎未初始化")

    async with async_session_factory() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            logger.error("异步数据库会话错误", error=str(e))
            raise
