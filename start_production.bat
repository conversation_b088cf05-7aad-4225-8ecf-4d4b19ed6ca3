@echo off
:: ============================================================
:: YS-API V3.0 生产环境启动脚本 - 唯一版本
:: ============================================================
:: 重要端口配置说明：
:: 后端端口: 8050 (生产环境主服务)
:: 前端端口: 8080 (管理界面)
:: 数据源: 真实生产数据，非测试数据
:: 模块数量: 16个完整业务模块(包含业务日志)
:: ============================================================

echo ============================================================
echo YS-API V3.0 生产环境服务启动
echo ============================================================
echo.
echo 生产环境配置:
echo   后端端口: 8050 (生产环境主服务)
echo   前端端口: 8080 (管理界面)
echo   端口管理: 自动检查和清理
echo   所有16个模块: 包含业务日志模块
echo   真实数据连接: 生产环境
echo.

echo 正在检查和准备端口...
python scripts\port_manager.py
if errorlevel 1 (
    echo 端口准备失败
    pause
    exit /b 1
)

echo.
echo 启动后端服务 (端口 8050)...
start "YS-API-Backend-Production" cmd /k "python backend\start_server_fixed.py"
timeout /t 3 > nul

echo.
echo 启动前端服务 (端口 8080)...
start "YS-API-Frontend-Production" cmd /k "python frontend\start_frontend_fixed.py"
timeout /t 2 > nul

echo.
echo 生产环境启动完成!
echo.
echo 生产环境访问地址:
echo   生产字段配置: http://localhost:8080/field-config-fixed.html
echo   业务日志管理: http://localhost:8050/api/business-logs
echo   后端健康检查: http://localhost:8050/health
echo   完整API文档: http://localhost:8050/docs
echo.
echo 生产环境注意事项:
echo   1. 使用真实数据连接，请谨慎操作
echo   2. 所有16个模块已完整加载
echo   3. 后端端口8050，前端端口8080
echo   4. 业务日志模块已完整集成
echo.

pause
