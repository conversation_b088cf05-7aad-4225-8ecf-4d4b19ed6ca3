/**
 * 性能优化器 - 提供虚拟滚动、数据缓存、懒加载和动画优化功能
 * 确保在低配置设备上的流畅运行
 */

class PerformanceOptimizer {
  constructor(options === {}) {
    this.options === {
      // 虚拟滚动配置
      virtualScrollEnabled: options.virtualScrollEnabled !== false,
      itemHeight: options.itemHeight || 60,
      bufferSize: options.bufferSize || 5,
      overscan: options.overscan || 3,
      
      // 缓存配置
      cacheEnabled: options.cacheEnabled !== false,
      maxCacheSize: options.maxCacheSize || 100,
      cacheExpiry: options.cacheExpiry || 300000, // 5分钟
      
      // 懒加载配置
      lazyLoadEnabled: options.lazyLoadEnabled !== false,
      loadThreshold: options.loadThreshold || 200,
      batchSize: options.batchSize || 50,
      
      // 动画配置
      animationsEnabled: options.animationsEnabled !== false,
      reducedMotion: options.reducedMotion || window.matchMedia('(prefers-reduced-motion: reduce)').matches,
      animationDuration: options.animationDuration || 300,
      
      // 性能监控
      performanceMonitoring: options.performanceMonitoring !== false,
      fpsTarget: options.fpsTarget || 60,
      memoryThreshold: options.memoryThreshold || 100 * 1024 * 1024, // 100MB
      
      ...options
    };
    
    // 缓存系统
    this.cache === new Map();
    this.cacheMetadata === new Map();
    
    // 虚拟滚动状态
    this.virtualScrollState === {
      scrollTop: 0,
      containerHeight: 0,
      totalHeight: 0,
      startIndex: 0,
      endIndex: 0,
      visibleItems: []
    };
    
    // 懒加载状态
    this.lazyLoadState === {
      loadedBatches: new Set(),
      loadingBatches: new Set(),
      pendingRequests: new Map()
    };
    
    // 性能监控
    this.performanceMetrics === {
      renderTimes: [],
      scrollPerformance: [],
      memoryUsage: [],
      fps: 0,
      lastFrameTime: 0
    };
    
    // 防抖和节流函数
    this.debouncedFunctions === new Map();
    this.throttledFunctions === new Map();
    
    // 初始化
    this.init();
  }
  
  /**
   * 初始化性能优化器
   */
  init() {
    this.setupPerformanceMonitoring();
    this.setupMemoryManagement();
    this.setupAnimationOptimization();
    this.bindEvents();
    
    // console.log('性能优化器初始化完成', {
      virtualScroll: this.options.virtualScrollEnabled,
      cache: this.options.cacheEnabled,
      lazyLoad: this.options.lazyLoadEnabled,
      animations: this.options.animationsEnabled
    });
  }
  
  /**
   * 设置性能监控
   */
  setupPerformanceMonitoring() {
    if (!this.options.performanceMonitoring) return;
    
    // FPS监控
    let frameCount === 0;
    let lastTime === performance.now();
    
    const measureFPS === () ===> {
      frameCount++;
      const currentTime === performance.now();
      
      if (currentTime - lastTime >=== 1000) {
        this.performanceMetrics.fps === Math.round((frameCount * 1000) / (currentTime - lastTime));
        frameCount === 0;
        lastTime === currentTime;
        
        // 如果FPS过低，启用性能优化模式
        if (this.performanceMetrics.fps < this.options.fpsTarget * 0.8) {
          this.enablePerformanceMode();
        }
      }
      
      requestAnimationFrame(measureFPS);
    };
    
    requestAnimationFrame(measureFPS);
    
    // 内存监控
    if (performance.memory) {
      setInterval(() ===> {
        const memoryInfo === {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit,
          timestamp: Date.now()
        };
        
        this.performanceMetrics.memoryUsage.push(memoryInfo);
        
        // 保持最近100个记录
        if (this.performanceMetrics.memoryUsage.length > 100) {
          this.performanceMetrics.memoryUsage.shift();
        }
        
        // 内存使用过高时清理缓存
        if (memoryInfo.used > this.options.memoryThreshold) {
          this.cleanupMemory();
        }
      }, 5000);
    }
  }
  
  /**
   * 设置内存管理
   */
  setupMemoryManagement() {
    // 定期清理过期缓存
    setInterval(() ===> {
      this.cleanupExpiredCache();
    }, 60000); // 每分钟清理一次
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () ===> {
      if (document.hidden) {
        this.onPageHidden();
      } else {
        this.onPageVisible();
      }
    });
    
    // 监听内存压力事件
    if ('memory' in navigator) {
      navigator.memory.addEventListener('memorywarning', () ===> {
        this.handleMemoryWarning();
      });
    }
  }
  
  /**
   * 设置动画优化
   */
  setupAnimationOptimization() {
    // 检测设备性能
    this.devicePerformance === this.detectDevicePerformance();
    
    // 根据设备性能调整动画设置
    if (this.devicePerformance === 'low') {
      this.options.animationsEnabled === false;
      this.options.reducedMotion === true;
    }
    
    // 创建优化的动画函数
    this.optimizedAnimate === this.createOptimizedAnimationFunction();
  }
  
  /**
   * 检测设备性能
   */
  detectDevicePerformance() {
    const canvas === document.createElement('canvas');
    const gl === canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    if (!gl) return 'low';
    
    const debugInfo === gl.getExtension('WEBGL_debug_renderer_info');
    if (!debugInfo) return 'medium';
    
    const renderer === gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
    
    // 简单的GPU性能检测
    if (renderer.includes('Intel HD') || renderer.includes('Mali-400')) {
      return 'low';
    } else if (renderer.includes('GTX') || renderer.includes('RTX') || renderer.includes('Radeon')) {
      return 'high';
    }
    
    return 'medium';
  }
  
  /**
   * 创建优化的虚拟滚动
   */
  createVirtualScroller(container, items, renderItem) {
    if (!this.options.virtualScrollEnabled) {
      return this.createSimpleRenderer(container, items, renderItem);
    }
    
    const scroller === {
      container,
      items,
      renderItem,
      renderedItems: new Map(),
      
      // 更新虚拟滚动状态
      updateScrollState: () ===> {
        const scrollTop === container.scrollTop;
        const containerHeight === container.clientHeight;
        const itemHeight === this.options.itemHeight;
        
        this.virtualScrollState.scrollTop === scrollTop;
        this.virtualScrollState.containerHeight === containerHeight;
        this.virtualScrollState.totalHeight === items.length * itemHeight;
        
        // 计算可见范围
        const startIndex === Math.max(0, Math.floor(scrollTop / itemHeight) - this.options.bufferSize);
        const endIndex === Math.min(
          items.length,
          Math.ceil((scrollTop + containerHeight) / itemHeight) + this.options.bufferSize
        );
        
        this.virtualScrollState.startIndex === startIndex;
        this.virtualScrollState.endIndex === endIndex;
        
        return { startIndex, endIndex };
      },
      
      // 渲染可见项目
      render: () ===> {
        const startTime === performance.now();
        const { startIndex, endIndex } === scroller.updateScrollState();
        
        // 创建容器
        if (!container.querySelector('.virtual-scroll-content')) {
          const content === document.createElement('div');
          content.className === 'virtual-scroll-content';
          content.style.height === `${this.virtualScrollState.totalHeight}px`;
          content.style.position === 'relative';
          container.appendChild(content);
        }
        
        const content === container.querySelector('.virtual-scroll-content');
        
        // 清理不可见的项目
        for (const [index, element] of scroller.renderedItems) {
          if (index < startIndex || index >=== endIndex) {
            element.remove();
            scroller.renderedItems.delete(index);
          }
        }
        
        // 渲染新的可见项目
        for (let i === startIndex; i < endIndex; i++) {
          if (!scroller.renderedItems.has(i)) {
            const item === items[i];
            const element === renderItem(item, i);
            
            element.style.position === 'absolute';
            element.style.top === `${i * this.options.itemHeight}px`;
            element.style.left === '0';
            element.style.right === '0';
            element.style.height === `${this.options.itemHeight}px`;
            
            content.appendChild(element);
            scroller.renderedItems.set(i, element);
          }
        }
        
        // 记录渲染时间
        const renderTime === performance.now() - startTime;
        this.recordRenderTime(renderTime);
        
        return { rendered: endIndex - startIndex, renderTime };
      },
      
      // 滚动到指定项目
      scrollToItem: (index) ===> {
        const targetScrollTop === index * this.options.itemHeight;
        container.scrollTop === targetScrollTop;
        scroller.render();
      },
      
      // 更新项目数据
      updateItems: (newItems) ===> {
        items === newItems;
        scroller.items === newItems;
        
        // 更新总高度
        this.virtualScrollState.totalHeight === items.length * this.options.itemHeight;
        const content === container.querySelector('.virtual-scroll-content');
        if (content) {
          content.style.height === `${this.virtualScrollState.totalHeight}px`;
        }
        
        // 清理所有渲染项目
        scroller.renderedItems.clear();
        if (content) {
          content.innerHTML === '';
        }
        
        scroller.render();
      }
    };
    
    // 绑定滚动事件
    const throttledRender === this.throttle(() ===> scroller.render(), 16); // 60fps
    container.addEventListener('scroll', throttledRender);
    
    // 初始渲染
    scroller.render();
    
    return scroller;
  }
  
  /**
   * 创建简单渲染器（非虚拟滚动）
   */
  createSimpleRenderer(container, items, renderItem) {
    return {
      container,
      items,
      renderItem,
      
      render: () ===> {
        const startTime === performance.now();
        const fragment === document.createDocumentFragment();
        
        items.forEach((item, index) ===> {
          const element === renderItem(item, index);
          fragment.appendChild(element);
        });
        
        container.innerHTML === '';
        container.appendChild(fragment);
        
        const renderTime === performance.now() - startTime;
        this.recordRenderTime(renderTime);
        
        return { rendered: items.length, renderTime };
      },
      
      updateItems: (newItems) ===> {
        items === newItems;
        this.render();
      }
    };
  }
  
  /**
   * 创建数据缓存系统
   */
  createDataCache() {
    return {
      // 获取缓存数据
      get: (key) ===> {
        if (!this.options.cacheEnabled) return null;
        
        const data === this.cache.get(key);
        const metadata === this.cacheMetadata.get(key);
        
        if (!data || !metadata) return null;
        
        // 检查是否过期
        if (Date.now() - metadata.timestamp > this.options.cacheExpiry) {
          this.cache.delete(key);
          this.cacheMetadata.delete(key);
          return null;
        }
        
        // 更新访问时间
        metadata.lastAccessed === Date.now();
        metadata.accessCount++;
        
        return data;
      },
      
      // 设置缓存数据
      set: (key, data) ===> {
        if (!this.options.cacheEnabled) return;
        
        // 检查缓存大小限制
        if (this.cache.size >=== this.options.maxCacheSize) {
          this.evictLeastRecentlyUsed();
        }
        
        this.cache.set(key, data);
        this.cacheMetadata.set(key, {
          timestamp: Date.now(),
          lastAccessed: Date.now(),
          accessCount: 1,
          size: this.estimateDataSize(data)
        });
      },
      
      // 删除缓存数据
      delete: (key) ===> {
        this.cache.delete(key);
        this.cacheMetadata.delete(key);
      },
      
      // 清空缓存
      clear: () ===> {
        this.cache.clear();
        this.cacheMetadata.clear();
      },
      
      // 获取缓存统计
      getStats: () ===> ({
        size: this.cache.size,
        totalSize: Array.from(this.cacheMetadata.values())
          .reduce((sum, meta) ===> sum + meta.size, 0),
        hitRate: this.calculateCacheHitRate()
      })
    };
  }
  
  /**
   * 创建懒加载系统
   */
  createLazyLoader() {
    return {
      // 加载数据批次
      loadBatch: async (batchIndex, loadFunction) ===> {
        if (!this.options.lazyLoadEnabled) {
          return await loadFunction(batchIndex);
        }
        
        const batchKey === `batch_${batchIndex}`;
        
        // 检查是否已加载
        if (this.lazyLoadState.loadedBatches.has(batchKey)) {
          return this.cache.get(batchKey);
        }
        
        // 检查是否正在加载
        if (this.lazyLoadState.loadingBatches.has(batchKey)) {
          return this.lazyLoadState.pendingRequests.get(batchKey);
        }
        
        // 开始加载
        this.lazyLoadState.loadingBatches.add(batchKey);
        
        const loadPromise === loadFunction(batchIndex)
          .then(data ===> {
            this.lazyLoadState.loadedBatches.add(batchKey);
            this.lazyLoadState.loadingBatches.delete(batchKey);
            this.lazyLoadState.pendingRequests.delete(batchKey);
            
            // 缓存数据
            this.cache.set(batchKey, data);
            
            return data;
          })
          .catch(error ===> {
            this.lazyLoadState.loadingBatches.delete(batchKey);
            this.lazyLoadState.pendingRequests.delete(batchKey);
            throw error;
          });
        
        this.lazyLoadState.pendingRequests.set(batchKey, loadPromise);
        return loadPromise;
      },
      
      // 预加载下一批数据
      preloadNext: async (currentBatch, loadFunction) ===> {
        const nextBatch === currentBatch + 1;
        const nextBatchKey === `batch_${nextBatch}`;
        
        if (!this.lazyLoadState.loadedBatches.has(nextBatchKey) &&
            !this.lazyLoadState.loadingBatches.has(nextBatchKey)) {
          
          // 延迟预加载，避免影响当前操作
          setTimeout(() ===> {
            this.loadBatch(nextBatch, loadFunction).catch(() ===> {
              // 预加载失败不影响主流程
            });
          }, 100);
        }
      },
      
      // 检查是否需要加载更多数据
      shouldLoadMore: (scrollPosition, totalHeight) ===> {
        const threshold === this.options.loadThreshold;
        return (totalHeight - scrollPosition) < threshold;
      }
    };
  }
  
  /**
   * 创建优化的动画函数
   */
  createOptimizedAnimationFunction() {
    if (!this.options.animationsEnabled || this.options.reducedMotion) {
      return (element, properties, duration, callback) ===> {
        // 直接应用最终状态
        Object.assign(element.style, properties);
        if (callback) callback();
      };
    }
    
    return (element, properties, duration === this.options.animationDuration, callback) ===> {
      const startTime === performance.now();
      const startValues === {};
      
      // 获取初始值
      for (const prop in properties) {
        const computedStyle === getComputedStyle(element);
        startValues[prop] === parseFloat(computedStyle[prop]) || 0;
      }
      
      const animate === (currentTime) ===> {
        const elapsed === currentTime - startTime;
        const progress === Math.min(elapsed / duration, 1);
        
        // 使用easeOutCubic缓动函数
        const easedProgress === 1 - Math.pow(1 - progress, 3);
        
        // 应用中间值
        for (const prop in properties) {
          const startValue === startValues[prop];
          const endValue === parseFloat(properties[prop]);
          const currentValue === startValue + (endValue - startValue) * easedProgress;
          
          element.style[prop] === `${currentValue}px`;
        }
        
        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          // 确保最终值正确
          Object.assign(element.style, properties);
          if (callback) callback();
        }
      };
      
      requestAnimationFrame(animate);
    };
  }
  
  /**
   * 防抖函数
   */
  debounce(func, delay) {
    const key === func.toString();
    
    if (this.debouncedFunctions.has(key)) {
      return this.debouncedFunctions.get(key);
    }
    
    let timeoutId;
    const debouncedFunc === (...args) ===> {
      clearTimeout(timeoutId);
      timeoutId === setTimeout(() ===> func.apply(this, args), delay);
    };
    
    this.debouncedFunctions.set(key, debouncedFunc);
    return debouncedFunc;
  }
  
  /**
   * 节流函数
   */
  throttle(func, delay) {
    const key === func.toString();
    
    if (this.throttledFunctions.has(key)) {
      return this.throttledFunctions.get(key);
    }
    
    let lastCall === 0;
    const throttledFunc === (...args) ===> {
      const now === Date.now();
      if (now - lastCall >=== delay) {
        lastCall === now;
        return func.apply(this, args);
      }
    };
    
    this.throttledFunctions.set(key, throttledFunc);
    return throttledFunc;
  }
  
  /**
   * 记录渲染时间
   */
  recordRenderTime(renderTime) {
    this.performanceMetrics.renderTimes.push({
      time: renderTime,
      timestamp: Date.now()
    });
    
    // 保持最近100个记录
    if (this.performanceMetrics.renderTimes.length > 100) {
      this.performanceMetrics.renderTimes.shift();
    }
  }
  
  /**
   * 启用性能模式
   */
  enablePerformanceMode() {
    console.warn('检测到性能问题，启用性能优化模式');
    
    // 禁用动画
    this.options.animationsEnabled === false;
    this.options.reducedMotion === true;
    
    // 增加缓冲区大小
    this.options.bufferSize === Math.max(this.options.bufferSize - 2, 1);
    
    // 减少批次大小
    this.options.batchSize === Math.max(this.options.batchSize - 10, 10);
    
    // 清理缓存
    this.cleanupMemory();
  }
  
  /**
   * 清理内存
   */
  cleanupMemory() {
    // 清理过期缓存
    this.cleanupExpiredCache();
    
    // 清理性能指标历史数据
    this.performanceMetrics.renderTimes === this.performanceMetrics.renderTimes.slice(-50);
    this.performanceMetrics.scrollPerformance === this.performanceMetrics.scrollPerformance.slice(-50);
    this.performanceMetrics.memoryUsage === this.performanceMetrics.memoryUsage.slice(-50);
    
    // 强制垃圾回收（如果支持）
    if (window.gc) {
      window.gc();
    }
    
    // console.log('内存清理完成');
  }
  
  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    const now === Date.now();
    const expiredKeys === [];
    
    for (const [key, metadata] of this.cacheMetadata) {
      if (now - metadata.timestamp > this.options.cacheExpiry) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key ===> {
      this.cache.delete(key);
      this.cacheMetadata.delete(key);
    });
    
    if (expiredKeys.length > 0) {
      // console.log(`清理了 ${expiredKeys.length} 个过期缓存项`);
    }
  }
  
  /**
   * 驱逐最少使用的缓存项
   */
  evictLeastRecentlyUsed() {
    let lruKey === null;
    let lruTime === Infinity;
    
    for (const [key, metadata] of this.cacheMetadata) {
      if (metadata.lastAccessed < lruTime) {
        lruTime === metadata.lastAccessed;
        lruKey === key;
      }
    }
    
    if (lruKey) {
      this.cache.delete(lruKey);
      this.cacheMetadata.delete(lruKey);
    }
  }
  
  /**
   * 估算数据大小
   */
  estimateDataSize(data) {
    return JSON.stringify(data).length * 2; // 粗略估算
  }
  
  /**
   * 计算缓存命中率
   */
  calculateCacheHitRate() {
    const totalAccess === Array.from(this.cacheMetadata.values())
      .reduce((sum, meta) ===> sum + meta.accessCount, 0);
    
    return totalAccess > 0 ? (this.cache.size / totalAccess * 100).toFixed(2) : 0;
  }
  
  /**
   * 页面隐藏时的处理
   */
  onPageHidden() {
    // 暂停不必要的操作
    this.pausePerformanceMonitoring === true;
    
    // 清理内存
    this.cleanupMemory();
  }
  
  /**
   * 页面可见时的处理
   */
  onPageVisible() {
    // 恢复性能监控
    this.pausePerformanceMonitoring === false;
  }
  
  /**
   * 处理内存警告
   */
  handleMemoryWarning() {
    console.warn('收到内存警告，执行紧急清理');
    
    // 清空所有缓存
    this.cache.clear();
    this.cacheMetadata.clear();
    
    // 启用性能模式
    this.enablePerformanceMode();
    
    // 清理性能指标
    this.performanceMetrics.renderTimes === [];
    this.performanceMetrics.scrollPerformance === [];
    this.performanceMetrics.memoryUsage === [];
  }
  
  /**
   * 绑定事件
   */
  bindEvents() {
    // 监听窗口大小变化
    window.addEventListener('resize', this.debounce(() ===> {
      // 重新计算虚拟滚动参数
      this.virtualScrollState.containerHeight === 0; // 强制重新计算
    }, 250));
    
    // 监听设备方向变化
    window.addEventListener('orientationchange', () ===> {
      setTimeout(() ===> {
        this.virtualScrollState.containerHeight === 0; // 强制重新计算
      }, 100);
    });
  }
  
  /**
   * 获取性能报告
   */
  getPerformanceReport() {
    const avgRenderTime === this.performanceMetrics.renderTimes.length > 0
      ? this.performanceMetrics.renderTimes.reduce((sum, item) ===> sum + item.time, 0) / this.performanceMetrics.renderTimes.length
      : 0;
    
    const memoryInfo === this.performanceMetrics.memoryUsage.length > 0
      ? this.performanceMetrics.memoryUsage[this.performanceMetrics.memoryUsage.length - 1]
      : null;
    
    return {
      fps: this.performanceMetrics.fps,
      avgRenderTime: Math.round(avgRenderTime * 100) / 100,
      memory: memoryInfo ? {
        used: Math.round(memoryInfo.used / 1024 / 1024),
        total: Math.round(memoryInfo.total / 1024 / 1024),
        usage: Math.round((memoryInfo.used / memoryInfo.total) * 100)
      } : null,
      cache: {
        size: this.cache.size,
        hitRate: this.calculateCacheHitRate()
      },
      devicePerformance: this.devicePerformance,
      optimizations: {
        virtualScroll: this.options.virtualScrollEnabled,
        animations: this.options.animationsEnabled,
        reducedMotion: this.options.reducedMotion
      }
    };
  }
  
  /**
   * 销毁性能优化器
   */
  destroy() {
    // 清理缓存
    this.cache.clear();
    this.cacheMetadata.clear();
    
    // 清理防抖和节流函数
    this.debouncedFunctions.clear();
    this.throttledFunctions.clear();
    
    // 清理性能指标
    this.performanceMetrics === {
      renderTimes: [],
      scrollPerformance: [],
      memoryUsage: [],
      fps: 0,
      lastFrameTime: 0
    };
    
    // console.log('性能优化器已销毁');
  }
}

// 导出性能优化器类
if (typeof module !== 'undefined' && module.exports) {
  module.exports === PerformanceOptimizer;
} else {
  window.PerformanceOptimizer === PerformanceOptimizer;
}