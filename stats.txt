============================================================
📊 YS-API V3.0 代码统计报告（屎山地图）
============================================================
📅 生成时间: 2025-08-05 11:14:44

📋 按文件类型统计:
------------------------------------------------------------
文件类型            文件数      总行数        代码行        注释行        空行      
------------------------------------------------------------
.xml            30       235021     235019     0          2       
.js             49       182181     175636     10         6535    
.json           66       156276     156276     0          0       
.py             196      48844      37296      3186       8362    
.md             95       22377      16971      2075       3331    
.html           35       13674      11757      0          1917    
.css            11       3869       3262       16         591     
.xlsx           14       1855       1807       25         23      
.backup_20250802_202310 1        893        684        56         153     
.bat            40       795        677        8          110     
.yml            8        690        577        62         51      
.template       14       484        348        59         77      
.sh             4        292        199        40         53      
(无扩展名)          6        267        189        38         40      
.txt            12       123        81         22         20      
.yaml           1        56         45         6          5       
.ps1            2        56         40         8          8       
.ini            3        48         40         0          8       
.backup_env     1        23         20         0          3       
.example        1        20         6          11         3       
------------------------------------------------------------
总计              589      667844     640930    

🔥 大文件清单（>100行）:
------------------------------------------------------------
文件路径                                               总行数      代码行     
------------------------------------------------------------
.cleanup_trash\element-plus.js                     62904    61319   
frontend\js\element-plus.js                        62904    61319   
.cleanup_trash\vue.global.js                       18193    18063   
frontend\js\vue.global.js                          18193    18063   
模块字段\json\生产订单列表查询.json                            16848    16848   
模块字段\backup\生产订单列表查询.xml                           16803    16803   
模块字段\生产订单列表查询.xml                                  16798    16798   
模块字段\json\销售订单.json                                15194    15194   
模块字段\销售订单.xml                                      15150    15150   
模块字段\backup\销售订单.xml                               15150    15150   
模块字段\json\物料档案批量详情查询.json                          11455    11455   
模块字段\物料档案批量详情查询.xml                                11413    11413   
模块字段\backup\物料档案批量详情查询.xml                         11413    11413   
模块字段\backup\请购单列表查询.xml                            8846     8846    
模块字段\请购单列表查询.xml                                   8845     8845    
模块字段\json\请购单列表查询.json                             8787     8787    
模块字段\backup\采购入库单列表.xml                            8570     8570    
模块字段\json\销售出库列表查询.json                            8496     8496    
模块字段\销售出库列表查询.xml                                  8462     8462    
模块字段\backup\销售出库列表查询.xml                           8462     8462    

📁 目录代码量分布:
----------------------------------------
模块字段\backup                      115657 行
模块字段\json                        113977 行
模块字段                             113455 行
.cleanup_trash                    89147 行
frontend\js                       87201 行
config\baselines                  29430 行
backend\app\services              21156 行
frontend                          19771 行
docs                               8574 行
md文档                               7124 行
frontend\tests                     6448 行
backend\app\api\v1                 6396 行
frontend\js\common                 4701 行
scripts                            3151 行
frontend\css                       3053 行
backend\app\core                   3032 行
logic                              3028 行
tests                              2334 行
frontend\js\core                   1925 行
excel                              1855 行
backend\app                        1035 行
backend\app\schemas                 863 行
tools                               781 行
config\monitoring                   567 行
dev-tools\mock                      478 行
config                              470 行
backend                             314 行
dev-tools\cleanup                   294 行
config\data\user_field_config\TestUser      138 行
monitoring                          131 行
backend\app\config                  130 行
templates                           112 行
deploy                              108 行
backend\app\middleware               61 行
.github\workflows                    50 行
.file_sentinels                      35 行
frontend\tests\reports               29 行
config\data\user_field_config\Alice       28 行
backend\app\api                       4 行
core                                  0 行
core\backend                          0 行
core\backend\app                      0 行
core\backend\app\api                  0 行
core\backend\app\api\v1               0 行
core\backend\app\core                 0 行
core\backend\app\services             0 行
frontend\migrated                     0 行
migration\week1_analysis\tests        0 行
migration\week2_analysis\refactored        0 行
migration\week3_analysis\refactored        0 行

🎯 重构建议:
- 优先重构大文件（>500行）
- 关注代码量最多的目录
- 检查无扩展名文件的用途
