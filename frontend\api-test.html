<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }

        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .loading {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        button:hover {
            background: #0056b3;
        }

        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>

<body>
    <h1>🔧 YS-API V3.0 连接测试</h1>

    <div class="test-result">
        <h3>服务状态检查</h3>
        <button onclick="testBackend()">测试后端连接 (localhost:5000)</button>
        <button onclick="testModules()">测试模块API</button>
        <button onclick="testFields()">测试字段API</button>
        <div id="results"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8060';  // 修复版后端端口

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
        }

        async function testBackend() {
            log('测试后端健康检查...', 'loading');
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                log(`✅ 后端连接成功: ${JSON.stringify(data)}`, 'success');
            } catch (error) {
                log(`❌ 后端连接失败: ${error.message}`, 'error');
            }
        }

        async function testModules() {
            log('测试模块列表API...', 'loading');
            try {
                const response = await fetch(`${API_BASE_URL}/api/v1/field-config/modules`);
                const data = await response.json();
                log(`✅ 模块API成功，获取到 ${data.modules?.length || 0} 个模块`, 'success');

                // 显示前几个模块
                if (data.modules && data.modules.length > 0) {
                    const firstFew = data.modules.slice(0, 3).map(m => m.display_name || m.name).join(', ');
                    log(`📋 示例模块: ${firstFew}...`, 'success');
                }
            } catch (error) {
                log(`❌ 模块API失败: ${error.message}`, 'error');
            }
        }

        async function testFields() {
            log('测试字段API...', 'loading');
            try {
                // 测试一个模块的字段获取
                const testModule = '材料出库单列表查询';
                const response = await fetch(`${API_BASE_URL}/api/v1/field-config/modules/${encodeURIComponent(testModule)}/fields`);
                const data = await response.json();

                if (data.error) {
                    log(`⚠️ 字段API返回错误: ${data.message}`, 'error');
                } else {
                    log(`✅ 字段API成功，模块 "${testModule}" 有 ${data.fields?.length || 0} 个字段`, 'success');

                    if (data.fields && data.fields.length > 0) {
                        const fieldNames = data.fields.slice(0, 5).map(f => f.display_name || f.name).join(', ');
                        log(`🏷️ 示例字段: ${fieldNames}...`, 'success');
                    }
                }
            } catch (error) {
                log(`❌ 字段API失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试
        window.onload = function () {
            log('🚀 开始API连接测试...', 'loading');
            setTimeout(testBackend, 500);
            setTimeout(testModules, 1000);
            setTimeout(testFields, 1500);
        };
    </script>
</body>

</html>