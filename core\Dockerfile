# YS-API V3.0 MVP Dockerfile
# 最小可运行单元的环境配置记录
FROM python:3.10-slim

LABEL version="3.0-MVP"
LABEL description="YS-API V3.0 最小可运行单元"
LABEL stage="Day2-MVP"

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制MVP核心文件
COPY core/requirements.txt .
COPY core/config.ini .
COPY core/main.py .
COPY core/app/ ./app/

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 创建非root用户
RUN useradd --create-home --shell /bin/bash mvpuser
RUN chown -R mvpuser:mvpuser /app
USER mvpuser

# 暴露MVP端口
EXPOSE 8001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# MVP启动命令
CMD ["python", "main.py"]

# 构建信息
RUN echo "MVP构建时间: $(date)" > /app/build_info.txt
RUN echo "包含API路由: 24个" >> /app/build_info.txt
RUN echo "核心功能: 基础API服务" >> /app/build_info.txt
