<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<fieldVersion>20230210</fieldVersion>
<appCode/>
<tokenSet>false</tokenSet>
<tokenDoc/>
<tenantId>0</tenantId>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<id>05a2a46b90984a8ba115d6a2061f8720</id>
<name>销售出库列表查询</name>
<apiClassifyId>c13e2ce641b8440ea7eba7d11ed46c52</apiClassifyId>
<apiClassifyName>销售出库单</apiClassifyName>
<apiClassifyCode/>
<parentApiClassifies/>
<functionId/>
<openMode>0</openMode>
<description>销售出库列表查询</description>
<auth>true</auth>
<bodyPassthrough>false</bodyPassthrough>
<healthExam>false</healthExam>
<healthStatus>true</healthStatus>
<responseResultPassthrough>false</responseResultPassthrough>
<contentType>application/json</contentType>
<returnPassthrough/>
<completeProxyUrl>/yonbip/scm/salesout/list</completeProxyUrl>
<connectUrl>/bill/list</connectUrl>
<sort>20</sort>
<handler>openapi</handler>
<httpRequestType>POST</httpRequestType>
<openApi>true</openApi>
<preset>false</preset>
<productId>710a0be3edff4f9092e35f63fd3b9bae</productId>
<productCode>scm</productCode>
<proxyUrl>/yonbip/scm/salesout/list</proxyUrl>
<requestParamsDemo>Url: /yonbip/scm/salesout/list?access_token=访问令牌 Body: { "pageIndex": 1, "code": "", "pageSize": 10, "vouchdate": "2021-04-19", "stockOrg": [], "salesOrg": [], "invoiceOrg": [], "invoiceCust": [], "upcode": "", "department": [], "operator": [], "warehouse": [], "stockMgr": [], "cust": [], "product_cName_ManageClass":[], "bustype.name":"", "isSum": false, "simpleVOs": [ { "op": "between", "value2": "2021-04-19 23:59:59", "value1": "2021-04-19 00:00:00", "field": "pubts" } ] }</requestParamsDemo>
<requestProtocol>HTTP</requestProtocol>
<serviceHttpMethod>POST</serviceHttpMethod>
<publishStatus>true</publishStatus>
<approvalMsg/>
<rpcAppName/>
<rpcServiceName/>
<rpcMethodName/>
<rpcServiceUrl/>
<ma>false</ma>
<gmtCreate>2020-01-16 18:32:32</gmtCreate>
<gmtUpdate>2025-02-12 10:31:26.000</gmtUpdate>
<address>https://c3.yonyoucloud.com/iuap-api-gateway/yonbip/scm/salesout/list</address>
<productName>采购供应</productName>
<productClassifyId>yonsuite</productClassifyId>
<productClassifyCode>yonbip</productClassifyCode>
<productClassifyName>用友 YonBIP</productClassifyName>
<paramDTOS>
<paramDTOS>
<id>2200117965505953805</id>
<name>isdefault</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId>1856804444898852873</defParamId>
<array>false</array>
<paramDesc>该参数可忽略不管</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953806</id>
<name>pageIndex</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId>1856804444898852874</defParamId>
<array>false</array>
<paramDesc>页号</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>1</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953807</id>
<name>code</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId>1856804444898852875</defParamId>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953808</id>
<name>pageSize</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId>1856804444898852876</defParamId>
<array>false</array>
<paramDesc>每页行数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>10</defaultValue>
<required>true</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953809</id>
<name>vouchdate</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId>1856804444898852877</defParamId>
<array>false</array>
<paramDesc>单据日期 区间格式，2021-05-06|2021-05-06 23:00:00。若传入单个时间如：2021-05-06，则查询该时间之后，到当前时间之间的单据</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953810</id>
<name>stockOrg</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId>1856804444898852878</defParamId>
<array>false</array>
<paramDesc>库存组织id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953811</id>
<name>salesOrg</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId>1856804444898852879</defParamId>
<array>false</array>
<paramDesc>销售组织id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953812</id>
<name>invoiceOrg</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId>1856804444898852880</defParamId>
<array>false</array>
<paramDesc>开票组织ID</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953813</id>
<name>invoiceCust</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId>1856804444898852881</defParamId>
<array>false</array>
<paramDesc>开票客户id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953814</id>
<name>upcode</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId>1856804444898852882</defParamId>
<array>false</array>
<paramDesc>来源单据号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953815</id>
<name>department</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId>1856804444898852883</defParamId>
<array>false</array>
<paramDesc>部门id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953816</id>
<name>operator</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId>1856804444898852884</defParamId>
<array>false</array>
<paramDesc>业务员id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953817</id>
<name>warehouse</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId>1856804444898852885</defParamId>
<array>false</array>
<paramDesc>仓库id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953818</id>
<name>stockMgr</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId>1856804444898852886</defParamId>
<array>false</array>
<paramDesc>库管员id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953819</id>
<name>cust</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId>1856804444898852887</defParamId>
<array>false</array>
<paramDesc>客户id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>0</maxLength>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953820</id>
<name>product_cName</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId>1856804444898852888</defParamId>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953821</id>
<name>bustype.name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId>1856804444898852889</defParamId>
<array>false</array>
<paramDesc>交易类型名称</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953822</id>
<name>product_cName_ManageClass</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId>1856804444898852890</defParamId>
<array>false</array>
<paramDesc>物料分类id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953823</id>
<name>isSum</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId>1856804444898852891</defParamId>
<array>false</array>
<paramDesc>查询表头</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example>false</example>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue>false</defaultValue>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
<paramDTOS>
<id>2200117965505953800</id>
<name>simpleVOs</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children>
<children>
<id>2200117965505953801</id>
<name>op</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953800</parentId>
<defParamId>1856804444898852893</defParamId>
<array>false</array>
<paramDesc>比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2200117965505953802</id>
<name>value2</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953800</parentId>
<defParamId>1856804444898852894</defParamId>
<array>false</array>
<paramDesc>值2(条件)如："2021-04-19 23:59:59"</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2200117965505953803</id>
<name>value1</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953800</parentId>
<defParamId>1856804444898852895</defParamId>
<array>false</array>
<paramDesc>值1(条件)如： "2021-04-19 00:00:00"</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
<children>
<id>2200117965505953804</id>
<name>field</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953800</parentId>
<defParamId>1856804444898852896</defParamId>
<array>false</array>
<paramDesc>属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码details.product.cCode、表头自定义项headItem.define1、表体自定义项details.bodyItem.define1等)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</children>
</children>
<defParamId>1856804444898852892</defParamId>
<array>true</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<bizType/>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength/>
<childId/>
<edit>false</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</paramDTOS>
</paramDTOS>
<queryParamDTOS/>
<ysApi>false</ysApi>
<presetTokenApi>false</presetTokenApi>
<applyFlag>false</applyFlag>
<cover>false</cover>
<paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953829</id>
<name>isdefault</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>该参数可忽略不管</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>isdefault</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953830</id>
<name>pageIndex</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>页号</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageIndex</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953831</id>
<name>code</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据编号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>code</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953832</id>
<name>pageSize</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>每页行数</paramDesc>
<paramType>int</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>pageSize</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>int</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953833</id>
<name>vouchdate</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>单据日期 区间格式，2021-05-06|2021-05-06 23:00:00。若传入单个时间如：2021-05-06，则查询该时间之后，到当前时间之间的单据</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>vouchdate</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953834</id>
<name>stockOrg</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>库存组织id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>stockOrg</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953835</id>
<name>salesOrg</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>销售组织id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>salesOrg</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953836</id>
<name>invoiceOrg</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>开票组织ID</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>invoiceOrg</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953837</id>
<name>invoiceCust</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>开票客户id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>invoiceCust</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953838</id>
<name>upcode</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>来源单据号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>upcode</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953839</id>
<name>department</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>部门id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>department</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953840</id>
<name>operator</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>业务员id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>operator</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953841</id>
<name>warehouse</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>仓库id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>warehouse</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953842</id>
<name>stockMgr</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>库管员id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>stockMgr</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953843</id>
<name>cust</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>客户id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>cust</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953844</id>
<name>product_cName</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>物料id</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product_cName</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953845</id>
<name>bustype.name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>交易类型名称</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>bustype.name</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953846</id>
<name>product_cName_ManageClass</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children/>
<defParamId/>
<array>false</array>
<paramDesc>物料分类id</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>product_cName_ManageClass</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953847</id>
<name>isSum</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>查询表头</paramDesc>
<paramType>boolean</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>isSum</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>boolean</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
<paramMapDTOS>
<id>2200117965505953824</id>
<name>simpleVOs</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children>
<children>
<id>2200117965505953825</id>
<name>op</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953824</parentId>
<defParamId/>
<array>false</array>
<paramDesc>比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>op</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953826</id>
<name>value2</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953824</parentId>
<defParamId/>
<array>false</array>
<paramDesc>值2(条件)如："2021-04-19 23:59:59"</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value2</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953827</id>
<name>value1</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953824</parentId>
<defParamId/>
<array>false</array>
<paramDesc>值1(条件)如： "2021-04-19 00:00:00"</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>value1</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953828</id>
<name>field</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953824</parentId>
<defParamId/>
<array>false</array>
<paramDesc>属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码details.product.cCode、表头自定义项headItem.define1、表体自定义项details.bodyItem.define1等)</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>field</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>string</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId/>
<array>false</array>
<paramDesc>扩展查询条件</paramDesc>
<paramType>object</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<aggregatedValueObject>false</aggregatedValueObject>
<mapName>simpleVOs</mapName>
<mapRequestParamType>BodyParam</mapRequestParamType>
<paramList/>
<primitive>false</primitive>
<serviceParamType>object</serviceParamType>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityDTO/>
<childId/>
<edit>false</edit>
<defaultValue/>
<enableMulti>false</enableMulti>
</paramMapDTOS>
</paramMapDTOS>
<paramReturnDTOS>
<paramReturnDTOS>
<id>2200117974095888415</id>
<name>code</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId>1856804444898852921</defParamId>
<array>false</array>
<paramDesc>返回码，调用成功时返回200</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>200</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2200117974095888416</id>
<name>message</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<defParamId>1856804444898852922</defParamId>
<array>false</array>
<paramDesc>调用失败时的错误信息</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>操作成功</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
<paramReturnDTOS>
<id>2200117965505953848</id>
<name>data</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId/>
<children>
<children>
<id>2200117974095888408</id>
<name>pageIndex</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953848</parentId>
<defParamId>1856804444898852924</defParamId>
<array>false</array>
<paramDesc>当前页</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888409</id>
<name>pageSize</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953848</parentId>
<defParamId>1856804444898852925</defParamId>
<array>false</array>
<paramDesc>分页大小</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>10</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888410</id>
<name>recordCount</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953848</parentId>
<defParamId>1856804444898852926</defParamId>
<array>false</array>
<paramDesc>总记录数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>26</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953849</id>
<name>recordList</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953848</parentId>
<children>
<children>
<id>2200117965505953850</id>
<name>cReceiveAddress</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852928</defParamId>
<array>false</array>
<paramDesc>收货地址</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1111</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953851</id>
<name>oriTax</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852929</defParamId>
<array>false</array>
<paramDesc>税率</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>0.19</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953852</id>
<name>details_stockUnitId</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852930</defParamId>
<array>false</array>
<paramDesc>库存单位主键</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953853</id>
<name>product_cCode</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852931</defParamId>
<array>false</array>
<paramDesc>物料编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>hy母件002</example>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953854</id>
<name>details_taxId</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852932</defParamId>
<array>false</array>
<paramDesc>税目主键</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>8b99f589-bc47-4c8a-bfqw-13d78caa20b0</example>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953855</id>
<name>natCurrency</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852933</defParamId>
<array>false</array>
<paramDesc>本币主键</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>G001ZM0000DEFAULTCURRENCT00000000001</example>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953856</id>
<name>sourcesys</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852934</defParamId>
<array>false</array>
<paramDesc>来源单据领域</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>udinghuo</example>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953857</id>
<name>tradeRouteID</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1869251492046176264</defParamId>
<array>false</array>
<paramDesc>贸易路径id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953858</id>
<name>stockUnitId_Precision</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852935</defParamId>
<array>false</array>
<paramDesc>库存单位精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953859</id>
<name>id</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852936</defParamId>
<array>false</array>
<paramDesc>主键</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953860</id>
<name>status_mobile_row</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852937</defParamId>
<array>false</array>
<paramDesc>单据状态</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953861</id>
<name>invoiceTitle</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852938</defParamId>
<array>false</array>
<paramDesc>发票抬头</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>123抬头</example>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953862</id>
<name>details_priceUOM</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852939</defParamId>
<array>false</array>
<paramDesc>计价单位主键</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953863</id>
<name>natSum</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852940</defParamId>
<array>false</array>
<paramDesc>本币含税金额</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953864</id>
<name>isEndTrade</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1869251492046176265</defParamId>
<array>false</array>
<paramDesc>是否末级</paramDesc>
<paramType>short</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>14</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953865</id>
<name>warehouse</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852941</defParamId>
<array>false</array>
<paramDesc>仓库主键</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>15</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953866</id>
<name>srcBillType</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852942</defParamId>
<array>false</array>
<paramDesc>来源单据类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>16</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953867</id>
<name>diliverStatus</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852943</defParamId>
<array>false</array>
<paramDesc>发货状态</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>DELIVERING</example>
<fullName/>
<ytenantId/>
<paramOrder>17</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953868</id>
<name>warehouse_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852944</defParamId>
<array>false</array>
<paramDesc>仓库名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>调入仓库B</example>
<fullName/>
<ytenantId/>
<paramOrder>18</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953869</id>
<name>natCurrency_priceDigit</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852945</defParamId>
<array>false</array>
<paramDesc>本币精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>3</example>
<fullName/>
<ytenantId/>
<paramOrder>19</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953870</id>
<name>exchRateType</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852946</defParamId>
<array>false</array>
<paramDesc>汇率类型枚举值</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>sfaju9kr</example>
<fullName/>
<ytenantId/>
<paramOrder>20</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953871</id>
<name>tradeRouteLineno</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1869251492046176266</defParamId>
<array>false</array>
<paramDesc>站点</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>21</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953872</id>
<name>invExchRate</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852947</defParamId>
<array>false</array>
<paramDesc>单位转换率</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>22</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953873</id>
<name>product_defaultAlbumId</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852948</defParamId>
<array>false</array>
<paramDesc>物料图片</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>23</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953874</id>
<name>impactStockTiming</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>2200117965505953798</defParamId>
<array>false</array>
<paramDesc>更新存量传财务时机,0:保存;1:审核</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>24</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength/>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953875</id>
<name>status</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852949</defParamId>
<array>false</array>
<paramDesc>单据状态；0开立，1已审核，3审核中</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>25</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953876</id>
<name>currency_moneyDigit</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852950</defParamId>
<array>false</array>
<paramDesc>币种精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>26</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953877</id>
<name>invoiceCust_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852951</defParamId>
<array>false</array>
<paramDesc>开票客户名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>张三啊</example>
<fullName/>
<ytenantId/>
<paramOrder>27</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953878</id>
<name>details_productsku</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852952</defParamId>
<array>false</array>
<paramDesc>物料KSU主键</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>28</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953879</id>
<name>salesOrg</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852953</defParamId>
<array>false</array>
<paramDesc>销售组织主键</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>29</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953880</id>
<name>invoiceOrg_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852954</defParamId>
<array>false</array>
<paramDesc>开票组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>hy组织001</example>
<fullName/>
<ytenantId/>
<paramOrder>30</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953881</id>
<name>tradeRoute_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1878166108965961735</defParamId>
<array>false</array>
<paramDesc>贸易路径</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>31</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953882</id>
<name>productsku_cName</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852955</defParamId>
<array>false</array>
<paramDesc>物料SKU名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>hy母件002</example>
<fullName/>
<ytenantId/>
<paramOrder>32</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953883</id>
<name>vouchdate</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852956</defParamId>
<array>false</array>
<paramDesc>单据日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-06-01 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>33</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953884</id>
<name>invPriceExchRate</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852957</defParamId>
<array>false</array>
<paramDesc>计价单位转换率</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>34</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953885</id>
<name>currency</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852958</defParamId>
<array>false</array>
<paramDesc>原币主键</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>G001ZM0000DEFAULTCURRENCT00000000001</example>
<fullName/>
<ytenantId/>
<paramOrder>35</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953886</id>
<name>pubts</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852959</defParamId>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-06-02 15:10:23</example>
<fullName/>
<ytenantId/>
<paramOrder>36</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953887</id>
<name>org_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852960</defParamId>
<array>false</array>
<paramDesc>发货组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>hy组织001</example>
<fullName/>
<ytenantId/>
<paramOrder>37</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953888</id>
<name>cReceiveMobile</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852961</defParamId>
<array>false</array>
<paramDesc>收货电话</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>4353</example>
<fullName/>
<ytenantId/>
<paramOrder>38</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953889</id>
<name>createDate</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852962</defParamId>
<array>false</array>
<paramDesc>创建日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-06-01 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>39</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953890</id>
<name>creator</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852963</defParamId>
<array>false</array>
<paramDesc>创建人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>rtduanhy</example>
<fullName/>
<ytenantId/>
<paramOrder>40</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953891</id>
<name>oriSum</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852964</defParamId>
<array>false</array>
<paramDesc>原币含税金额</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>4</example>
<fullName/>
<ytenantId/>
<paramOrder>41</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953892</id>
<name>exchRateType_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852965</defParamId>
<array>false</array>
<paramDesc>汇率类型名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>基准汇率</example>
<fullName/>
<ytenantId/>
<paramOrder>42</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953893</id>
<name>accountOrg</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852966</defParamId>
<array>false</array>
<paramDesc>会计主体主键</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>43</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953894</id>
<name>stsalesOutExchangeInfo_d_key</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852967</defParamId>
<array>false</array>
<paramDesc>逻辑字段冗余</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>44</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953895</id>
<name>cReceiver</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852968</defParamId>
<array>false</array>
<paramDesc>收货人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>43543</example>
<fullName/>
<ytenantId/>
<paramOrder>45</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953896</id>
<name>details_id</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852969</defParamId>
<array>false</array>
<paramDesc>子表主键</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>46</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953897</id>
<name>priceQty</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852970</defParamId>
<array>false</array>
<paramDesc>计价数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>47</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953898</id>
<name>createTime</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852971</defParamId>
<array>false</array>
<paramDesc>创建时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-06-01 20:24:08</example>
<fullName/>
<ytenantId/>
<paramOrder>48</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953899</id>
<name>taxUnitPriceTag</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852972</defParamId>
<array>false</array>
<paramDesc>价格含税标志</paramDesc>
<paramType>boolean</paramType>
<requestParamType/>
<path/>
<example>true</example>
<fullName/>
<ytenantId/>
<paramOrder>49</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953900</id>
<name>details_product</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852973</defParamId>
<array>false</array>
<paramDesc>物料主键</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>50</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953901</id>
<name>taxNum</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852974</defParamId>
<array>false</array>
<paramDesc>纳税识别号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>**********</example>
<fullName/>
<ytenantId/>
<paramOrder>51</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953902</id>
<name>department_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852975</defParamId>
<array>false</array>
<paramDesc>部门名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>XX部门</example>
<fullName/>
<ytenantId/>
<paramOrder>52</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953903</id>
<name>operator_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852976</defParamId>
<array>false</array>
<paramDesc>业务员名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>某某</example>
<fullName/>
<ytenantId/>
<paramOrder>53</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953904</id>
<name>invoiceAddress</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852977</defParamId>
<array>false</array>
<paramDesc>营业地址</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>某地区街道</example>
<fullName/>
<ytenantId/>
<paramOrder>54</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953905</id>
<name>operator</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852978</defParamId>
<array>false</array>
<paramDesc>业务员主键</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>***********</example>
<fullName/>
<ytenantId/>
<paramOrder>55</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953906</id>
<name>bankAccount</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852979</defParamId>
<array>false</array>
<paramDesc>银行账号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>***********</example>
<fullName/>
<ytenantId/>
<paramOrder>56</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953907</id>
<name>subBankName</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852980</defParamId>
<array>false</array>
<paramDesc>开户支行</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>某某支行</example>
<fullName/>
<ytenantId/>
<paramOrder>57</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953908</id>
<name>bankName</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852981</defParamId>
<array>false</array>
<paramDesc>开户银行</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>某银行</example>
<fullName/>
<ytenantId/>
<paramOrder>58</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953909</id>
<name>invoiceTelephone</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852982</defParamId>
<array>false</array>
<paramDesc>营业电话</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>*********</example>
<fullName/>
<ytenantId/>
<paramOrder>59</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953910</id>
<name>department</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852983</defParamId>
<array>false</array>
<paramDesc>部门主键</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>*********</example>
<fullName/>
<ytenantId/>
<paramOrder>60</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953911</id>
<name>cust</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852984</defParamId>
<array>false</array>
<paramDesc>客户主键</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>61</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953912</id>
<name>invoiceUpcType</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852985</defParamId>
<array>false</array>
<paramDesc>发票类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>62</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953913</id>
<name>natMoney</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852986</defParamId>
<array>false</array>
<paramDesc>本币无税金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>3.81</example>
<fullName/>
<ytenantId/>
<paramOrder>63</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953914</id>
<name>currency_priceDigit</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852987</defParamId>
<array>false</array>
<paramDesc>币种精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>3</example>
<fullName/>
<ytenantId/>
<paramOrder>64</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953915</id>
<name>invoiceOrg</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852988</defParamId>
<array>false</array>
<paramDesc>开票客户主键</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>65</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953916</id>
<name>stockUnit_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852989</defParamId>
<array>false</array>
<paramDesc>库存单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>件</example>
<fullName/>
<ytenantId/>
<paramOrder>66</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953917</id>
<name>collaborationPolineno</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1869304715280908289</defParamId>
<array>false</array>
<paramDesc>协同来源单据行号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>67</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953918</id>
<name>bustype_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852990</defParamId>
<array>false</array>
<paramDesc>交易类型名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>销售出库</example>
<fullName/>
<ytenantId/>
<paramOrder>68</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953919</id>
<name>modifier</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852991</defParamId>
<array>false</array>
<paramDesc>修改人</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>rtduanhy</example>
<fullName/>
<ytenantId/>
<paramOrder>69</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953920</id>
<name>firstupcode</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852992</defParamId>
<array>false</array>
<paramDesc>源头单据编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>UO-test20210601000012</example>
<fullName/>
<ytenantId/>
<paramOrder>70</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953921</id>
<name>source</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852993</defParamId>
<array>false</array>
<paramDesc>来源单据类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>71</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953922</id>
<name>natTax</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852994</defParamId>
<array>false</array>
<paramDesc>本币税额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>0.19</example>
<fullName/>
<ytenantId/>
<paramOrder>72</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953923</id>
<name>subQty</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852995</defParamId>
<array>false</array>
<paramDesc>件数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>73</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953924</id>
<name>taxItems</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852996</defParamId>
<array>false</array>
<paramDesc>税率显示值</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>5%</example>
<fullName/>
<ytenantId/>
<paramOrder>74</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953925</id>
<name>modifyTime</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852997</defParamId>
<array>false</array>
<paramDesc>修改时间</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-06-02 15:10:23</example>
<fullName/>
<ytenantId/>
<paramOrder>75</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953926</id>
<name>product_cName</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852998</defParamId>
<array>false</array>
<paramDesc>物料名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>hy母件002</example>
<fullName/>
<ytenantId/>
<paramOrder>76</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953927</id>
<name>invoiceTitleType</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898852999</defParamId>
<array>false</array>
<paramDesc>发票抬头类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>77</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953928</id>
<name>receiveContacterPhone</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853000</defParamId>
<array>false</array>
<paramDesc>收货人联系电话</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>***********</example>
<fullName/>
<ytenantId/>
<paramOrder>78</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953929</id>
<name>modifyInvoiceType</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853001</defParamId>
<array>false</array>
<paramDesc>发票类型可改标志</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>79</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953930</id>
<name>natCurrencyName</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853002</defParamId>
<array>false</array>
<paramDesc>本币名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>人民币</example>
<fullName/>
<ytenantId/>
<paramOrder>80</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953931</id>
<name>salesOrg_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853003</defParamId>
<array>false</array>
<paramDesc>销售组织名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>hy组织001</example>
<fullName/>
<ytenantId/>
<paramOrder>81</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953932</id>
<name>modifyDate</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853004</defParamId>
<array>false</array>
<paramDesc>修改日期</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-06-02 00:00:00</example>
<fullName/>
<ytenantId/>
<paramOrder>82</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953933</id>
<name>unitName</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853005</defParamId>
<array>false</array>
<paramDesc>主计量名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>件</example>
<fullName/>
<ytenantId/>
<paramOrder>83</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953934</id>
<name>contactName</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853006</defParamId>
<array>false</array>
<paramDesc>联系人名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>张三</example>
<fullName/>
<ytenantId/>
<paramOrder>84</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953935</id>
<name>srcBillNO</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853007</defParamId>
<array>false</array>
<paramDesc>来源单据号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>******************</example>
<fullName/>
<ytenantId/>
<paramOrder>85</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953936</id>
<name>oriUnitPrice</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853008</defParamId>
<array>false</array>
<paramDesc>原币无税单价</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1.905</example>
<fullName/>
<ytenantId/>
<paramOrder>86</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953937</id>
<name>taxCode</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853009</defParamId>
<array>false</array>
<paramDesc>税目编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>VAT5</example>
<fullName/>
<ytenantId/>
<paramOrder>87</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953938</id>
<name>barCode</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853010</defParamId>
<array>false</array>
<paramDesc>单据码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>st_salesout|****************</example>
<fullName/>
<ytenantId/>
<paramOrder>88</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953939</id>
<name>unit_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853011</defParamId>
<array>false</array>
<paramDesc>主计量名称冗余</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>89</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953940</id>
<name>taxRate</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853012</defParamId>
<array>false</array>
<paramDesc>税率</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>5</example>
<fullName/>
<ytenantId/>
<paramOrder>90</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953941</id>
<name>unit</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853013</defParamId>
<array>false</array>
<paramDesc>库存单位</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>件</example>
<fullName/>
<ytenantId/>
<paramOrder>91</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953942</id>
<name>productsku_cCode</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853014</defParamId>
<array>false</array>
<paramDesc>物料SKU编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>hy母件002</example>
<fullName/>
<ytenantId/>
<paramOrder>92</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953943</id>
<name>natCurrency_moneyDigit</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853015</defParamId>
<array>false</array>
<paramDesc>本币精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>93</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953944</id>
<name>accountOrg_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853016</defParamId>
<array>false</array>
<paramDesc>会计主体名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>hy组织001</example>
<fullName/>
<ytenantId/>
<paramOrder>94</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953945</id>
<name>taxId</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853017</defParamId>
<array>false</array>
<paramDesc>税目编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>VAT5</example>
<fullName/>
<ytenantId/>
<paramOrder>95</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953946</id>
<name>invoiceCust</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853018</defParamId>
<array>false</array>
<paramDesc>开票客户主键</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>96</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953947</id>
<name>qty</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853019</defParamId>
<array>false</array>
<paramDesc>数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>97</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953948</id>
<name>unit_Precision</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853020</defParamId>
<array>false</array>
<paramDesc>主计量精度</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>98</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953949</id>
<name>oriTaxUnitPrice</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853021</defParamId>
<array>false</array>
<paramDesc>原币含税单价</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>99</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953950</id>
<name>oriMoney</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853022</defParamId>
<array>false</array>
<paramDesc>原币无税金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>3.81</example>
<fullName/>
<ytenantId/>
<paramOrder>100</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953951</id>
<name>contactsPieces</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853023</defParamId>
<array>false</array>
<paramDesc>应发件数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>101</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953952</id>
<name>contactsQuantity</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853024</defParamId>
<array>false</array>
<paramDesc>应发数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>102</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953953</id>
<name>natUnitPrice</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853025</defParamId>
<array>false</array>
<paramDesc>本币无税单价</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>1.905</example>
<fullName/>
<ytenantId/>
<paramOrder>103</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953954</id>
<name>code</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853026</defParamId>
<array>false</array>
<paramDesc>单据编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>XSCK20210601000001</example>
<fullName/>
<ytenantId/>
<paramOrder>104</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953955</id>
<name>receiveAccountingBasis</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853027</defParamId>
<array>false</array>
<paramDesc>立账开票依据</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>voucher_delivery</example>
<fullName/>
<ytenantId/>
<paramOrder>105</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953956</id>
<name>logistics</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853028</defParamId>
<array>false</array>
<paramDesc>物料单号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>XSCK20210601000001</example>
<fullName/>
<ytenantId/>
<paramOrder>106</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953957</id>
<name>exchRate</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853029</defParamId>
<array>false</array>
<paramDesc>汇率</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>107</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953958</id>
<name>currencyName</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853030</defParamId>
<array>false</array>
<paramDesc>币种名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>人民币</example>
<fullName/>
<ytenantId/>
<paramOrder>108</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953959</id>
<name>cust_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853031</defParamId>
<array>false</array>
<paramDesc>客户名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>张三啊</example>
<fullName/>
<ytenantId/>
<paramOrder>109</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953960</id>
<name>org</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853032</defParamId>
<array>false</array>
<paramDesc>库存组织主键</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>110</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953961</id>
<name>priceUOM_name</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853033</defParamId>
<array>false</array>
<paramDesc>计价单位名称</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>件</example>
<fullName/>
<ytenantId/>
<paramOrder>111</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953962</id>
<name>bustype</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853034</defParamId>
<array>false</array>
<paramDesc>交易类型主键</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>***************</example>
<fullName/>
<ytenantId/>
<paramOrder>112</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953963</id>
<name>receiveId</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853035</defParamId>
<array>false</array>
<paramDesc>收货地址主键</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>****************</example>
<fullName/>
<ytenantId/>
<paramOrder>113</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953964</id>
<name>upcode</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853036</defParamId>
<array>false</array>
<paramDesc>来源单据号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>******************</example>
<fullName/>
<ytenantId/>
<paramOrder>114</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953965</id>
<name>saleStyle</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853037</defParamId>
<array>false</array>
<paramDesc>商品售卖类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>SALE</example>
<fullName/>
<ytenantId/>
<paramOrder>115</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953966</id>
<name>iLogisticId</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853038</defParamId>
<array>false</array>
<paramDesc>物流公司</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>116</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953967</id>
<name>status_mobile</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853039</defParamId>
<array>false</array>
<paramDesc>单据状态</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>0</example>
<fullName/>
<ytenantId/>
<paramOrder>117</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953968</id>
<name>natTaxUnitPrice</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853040</defParamId>
<array>false</array>
<paramDesc>本币含税单价</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>118</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953972</id>
<name>out_sys_id</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853044</defParamId>
<array>false</array>
<paramDesc>外部来源线索</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>122</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953973</id>
<name>out_sys_code</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853045</defParamId>
<array>false</array>
<paramDesc>外部来源编码</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>123</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953974</id>
<name>out_sys_version</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853046</defParamId>
<array>false</array>
<paramDesc>外部来源版本</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>124</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953975</id>
<name>out_sys_type</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853047</defParamId>
<array>false</array>
<paramDesc>外部来源类型</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>125</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953976</id>
<name>out_sys_rowno</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853048</defParamId>
<array>false</array>
<paramDesc>外部来源行号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>126</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953977</id>
<name>out_sys_lineid</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1856804444898853049</defParamId>
<array>false</array>
<paramDesc>外部来源行</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>127</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953978</id>
<name>collaborationPocode</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1871361523558383623</defParamId>
<array>false</array>
<paramDesc>协同来源单据号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>128</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953979</id>
<name>collaborationPoid</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1871361523558383624</defParamId>
<array>false</array>
<paramDesc>协同来源单据id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>129</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953980</id>
<name>collaborationPodetailid</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1871361523558383625</defParamId>
<array>false</array>
<paramDesc>协同来源单据子表id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>130</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953981</id>
<name>collaborationSource</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1871361523558383626</defParamId>
<array>false</array>
<paramDesc>协同来源类型, 0:无来源、st_purinrecord:采购入库单、1:发货单、2:销售订单、3:退货单、tradeorder:电商订单、refundorder:电商退换货订单、retailvouch:零售单、mallvouch:商城发货单</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>131</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953982</id>
<name>salesOutsExtend!coUpcode</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1871361523558383627</defParamId>
<array>false</array>
<paramDesc>协同源头单据号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>132</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953983</id>
<name>salesOutsExtend!coSourceid</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1871361523558383628</defParamId>
<array>false</array>
<paramDesc>协同源头单据头id</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>133</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953984</id>
<name>salesOutsExtend!coSourceLineNo</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1871361523558383629</defParamId>
<array>false</array>
<paramDesc>协同源头行号</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>134</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117965505953985</id>
<name>salesOutsExtend!coSourceType</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953849</parentId>
<defParamId>1871361523558383630</defParamId>
<array>false</array>
<paramDesc>协同源头单据类型(upu.st_purchaseorder:采购订单,productionorder.po_subcontract_order:委外订单)</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>135</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1856804444898852927</defParamId>
<array>true</array>
<paramDesc>返回数据列表</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888393</id>
<name>sumRecordList</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953848</parentId>
<children>
<children>
<id>2200117974095888394</id>
<name>totalPieces</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853051</defParamId>
<array>false</array>
<paramDesc>合计件数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>0</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888395</id>
<name>oriSum</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853052</defParamId>
<array>false</array>
<paramDesc>合计金额</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>51222</example>
<fullName/>
<ytenantId/>
<paramOrder>1</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888396</id>
<name>invoiceOriSum</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853053</defParamId>
<array>false</array>
<paramDesc>合计开票金额</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>27922</example>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888397</id>
<name>saleReturnQty</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853054</defParamId>
<array>false</array>
<paramDesc>合计退货数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>34</example>
<fullName/>
<ytenantId/>
<paramOrder>3</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888398</id>
<name>natSum</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853055</defParamId>
<array>false</array>
<paramDesc>合计本币金额</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>51222</example>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888399</id>
<name>subQty</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853056</defParamId>
<array>false</array>
<paramDesc>合计副计量数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>492</example>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888400</id>
<name>totalQuantity</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853057</defParamId>
<array>false</array>
<paramDesc>合计数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>2</example>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888401</id>
<name>priceQty</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853058</defParamId>
<array>false</array>
<paramDesc>合计计价数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>492</example>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888402</id>
<name>qty</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853059</defParamId>
<array>false</array>
<paramDesc>合计数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>492</example>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888403</id>
<name>oriMoney</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853060</defParamId>
<array>false</array>
<paramDesc>合计原币无税金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>49705.27</example>
<fullName/>
<ytenantId/>
<paramOrder>9</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888404</id>
<name>invoiceQty</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853061</defParamId>
<array>false</array>
<paramDesc>合计开票数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>298</example>
<fullName/>
<ytenantId/>
<paramOrder>10</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888405</id>
<name>contactsPieces</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853062</defParamId>
<array>false</array>
<paramDesc>合计应发件量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>481</example>
<fullName/>
<ytenantId/>
<paramOrder>11</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888406</id>
<name>contactsQuantity</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853063</defParamId>
<array>false</array>
<paramDesc>合计应发数量</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>481</example>
<fullName/>
<ytenantId/>
<paramOrder>12</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888407</id>
<name>natMoney</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117974095888393</parentId>
<defParamId>1856804444898853064</defParamId>
<array>false</array>
<paramDesc>合计本币无税金额</paramDesc>
<paramType>double</paramType>
<requestParamType/>
<path/>
<example>49705.27</example>
<fullName/>
<ytenantId/>
<paramOrder>13</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1856804444898853050</defParamId>
<array>true</array>
<paramDesc>合计信息</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>4</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888411</id>
<name>pageCount</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953848</parentId>
<defParamId>1856804444898853065</defParamId>
<array>false</array>
<paramDesc>总页数</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>3</example>
<fullName/>
<ytenantId/>
<paramOrder>5</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888412</id>
<name>beginPageIndex</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953848</parentId>
<defParamId>1856804444898853066</defParamId>
<array>false</array>
<paramDesc>开始页页号</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>1</example>
<fullName/>
<ytenantId/>
<paramOrder>6</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888413</id>
<name>endPageIndex</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953848</parentId>
<defParamId>1856804444898853067</defParamId>
<array>false</array>
<paramDesc>最终页页号</paramDesc>
<paramType>long</paramType>
<requestParamType/>
<path/>
<example>3</example>
<fullName/>
<ytenantId/>
<paramOrder>7</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
<children>
<id>2200117974095888414</id>
<name>pubts</name>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<parentId>2200117965505953848</parentId>
<defParamId>1856804444898853068</defParamId>
<array>false</array>
<paramDesc>时间戳</paramDesc>
<paramType>string</paramType>
<requestParamType/>
<path/>
<example>2021-06-02 16:37:29</example>
<fullName/>
<ytenantId/>
<paramOrder>8</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</children>
</children>
<defParamId>1856804444898852923</defParamId>
<array>false</array>
<paramDesc>调用成功时的返回数据</paramDesc>
<paramType>object</paramType>
<requestParamType/>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder>2</paramOrder>
<baseType>true</baseType>
<defaultValue/>
<required/>
<visible/>
<gmtCreate>2025-02-12 10:30:41.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:41.000</gmtUpdate>
<apiName/>
<entityId/>
<entityCode/>
<edit>false</edit>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<paramTag/>
<format/>
<decimals/>
<maxLength>0</maxLength>
<enableMulti>false</enableMulti>
</paramReturnDTOS>
</paramReturnDTOS>
<returnFormatType>JSON</returnFormatType>
<paramConstDTOS/>
<paramConstMapDTOS/>
<apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2200117974095888422</id>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<content>{ "code": "200", "message": "操作成功", "data": { "pageIndex": 1, "pageSize": 10, "recordCount": 26, "recordList": [ { "cReceiveAddress": "1111", "oriTax": 0.19, "details_stockUnitId": ****************, "product_cCode": "hy母件002", "details_taxId": "8b99f589-bc47-4c8a-bfqw-13d78caa20b0", "natCurrency": "G001ZM0000DEFAULTCURRENCT00000000001", "sourcesys": "udinghuo", "tradeRouteID": 0, "stockUnitId_Precision": 2, "id": ****************, "status_mobile_row": 0, "invoiceTitle": "123抬头", "details_priceUOM": ****************, "natSum": 4, "isEndTrade": 0, "warehouse": ****************, "srcBillType": "1", "diliverStatus": "DELIVERING", "warehouse_name": "调入仓库B", "natCurrency_priceDigit": 3, "exchRateType": "sfaju9kr", "tradeRouteLineno": "", "invExchRate": 1, "product_defaultAlbumId": "", "impactStockTiming": "0", "status": 0, "currency_moneyDigit": 2, "invoiceCust_name": "张三啊", "details_productsku": ****************, "salesOrg": "****************", "invoiceOrg_name": "hy组织001", "tradeRoute_name": "", "productsku_cName": "hy母件002", "vouchdate": "2021-06-01 00:00:00", "invPriceExchRate": 1, "currency": "G001ZM0000DEFAULTCURRENCT00000000001", "pubts": "2021-06-02 15:10:23", "org_name": "hy组织001", "cReceiveMobile": "4353", "createDate": "2021-06-01 00:00:00", "creator": "rtduanhy", "oriSum": 4, "exchRateType_name": "基准汇率", "accountOrg": "****************", "stsalesOutExchangeInfo_d_key": ****************, "cReceiver": "43543", "details_id": ****************, "priceQty": 2, "createTime": "2021-06-01 20:24:08", "taxUnitPriceTag": true, "details_product": ****************, "taxNum": "**********", "department_name": "XX部门", "operator_name": "某某", "invoiceAddress": "某地区街道", "operator": ***********, "bankAccount": "***********", "subBankName": "某某支行", "bankName": "某银行", "invoiceTelephone": "*********", "department": "*********", "cust": ****************, "invoiceUpcType": "0", "natMoney": 3.81, "currency_priceDigit": 3, "invoiceOrg": "****************", "stockUnit_name": "件", "collaborationPolineno": "", "bustype_name": "销售出库", "modifier": "rtduanhy", "firstupcode": "UO-test20210601000012", "source": "1", "natTax": 0.19, "subQty": 2, "taxItems": "5%", "modifyTime": "2021-06-02 15:10:23", "product_cName": "hy母件002", "invoiceTitleType": "0", "receiveContacterPhone": "***********", "modifyInvoiceType": "1", "natCurrencyName": "人民币", "salesOrg_name": "hy组织001", "modifyDate": "2021-06-02 00:00:00", "unitName": "件", "contactName": "张三", "srcBillNO": "******************", "oriUnitPrice": 1.905, "taxCode": "VAT5", "barCode": "st_salesout|****************", "unit_name": ****************, "taxRate": 5, "unit": "件", "productsku_cCode": "hy母件002", "natCurrency_moneyDigit": 2, "accountOrg_name": "hy组织001", "taxId": "VAT5", "invoiceCust": ****************, "qty": 2, "unit_Precision": 2, "oriTaxUnitPrice": 2, "oriMoney": 3.81, "contactsPieces": 2, "contactsQuantity": 2, "natUnitPrice": 1.905, "code": "XSCK20210601000001", "receiveAccountingBasis": "voucher_delivery", "logistics": "XSCK20210601000001", "exchRate": 1, "currencyName": "人民币", "cust_name": "张三啊", "org": "****************", "priceUOM_name": "件", "bustype": "***************", "receiveId": ****************, "upcode": "******************", "saleStyle": "SALE", "iLogisticId": 0, "status_mobile": 0, "natTaxUnitPrice": 2, "salesOutDefineCharacter": {}, "salesOutsDefineCharacter": {}, "salesOutsCharacteristics": {}, "out_sys_id": "", "out_sys_code": "", "out_sys_version": "", "out_sys_type": "", "out_sys_rowno": "", "out_sys_lineid": "", "collaborationPocode": "", "collaborationPoid": 0, "collaborationPodetailid": 0, "collaborationSource": "", "salesOutsExtend!coUpcode": "", "salesOutsExtend!coSourceid": 0, "salesOutsExtend!coSourceLineNo": "", "salesOutsExtend!coSourceType": "" } ], "sumRecordList": [ { "totalPieces": 2, "oriSum": 51222, "invoiceOriSum": 27922, "saleReturnQty": 34, "natSum": 51222, "subQty": 492, "totalQuantity": 2, "priceQty": 492, "qty": 492, "oriMoney": 49705.27, "invoiceQty": 298, "contactsPieces": 481, "contactsQuantity": 481, "natMoney": 49705.27 } ], "pageCount": 3, "beginPageIndex": 1, "endPageIndex": 3, "pubts": "2021-06-02 16:37:29" } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOS>
<apiDemoReturnDTOS>
<id>2200117974095888423</id>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<content>{"code":999,"message":"列表查询失败"}</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOS>
</apiDemoReturnDTOS>
<apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2200117974095888422</id>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<content>{ "code": "200", "message": "操作成功", "data": { "pageIndex": 1, "pageSize": 10, "recordCount": 26, "recordList": [ { "cReceiveAddress": "1111", "oriTax": 0.19, "details_stockUnitId": ****************, "product_cCode": "hy母件002", "details_taxId": "8b99f589-bc47-4c8a-bfqw-13d78caa20b0", "natCurrency": "G001ZM0000DEFAULTCURRENCT00000000001", "sourcesys": "udinghuo", "tradeRouteID": 0, "stockUnitId_Precision": 2, "id": ****************, "status_mobile_row": 0, "invoiceTitle": "123抬头", "details_priceUOM": ****************, "natSum": 4, "isEndTrade": 0, "warehouse": ****************, "srcBillType": "1", "diliverStatus": "DELIVERING", "warehouse_name": "调入仓库B", "natCurrency_priceDigit": 3, "exchRateType": "sfaju9kr", "tradeRouteLineno": "", "invExchRate": 1, "product_defaultAlbumId": "", "impactStockTiming": "0", "status": 0, "currency_moneyDigit": 2, "invoiceCust_name": "张三啊", "details_productsku": ****************, "salesOrg": "****************", "invoiceOrg_name": "hy组织001", "tradeRoute_name": "", "productsku_cName": "hy母件002", "vouchdate": "2021-06-01 00:00:00", "invPriceExchRate": 1, "currency": "G001ZM0000DEFAULTCURRENCT00000000001", "pubts": "2021-06-02 15:10:23", "org_name": "hy组织001", "cReceiveMobile": "4353", "createDate": "2021-06-01 00:00:00", "creator": "rtduanhy", "oriSum": 4, "exchRateType_name": "基准汇率", "accountOrg": "****************", "stsalesOutExchangeInfo_d_key": ****************, "cReceiver": "43543", "details_id": ****************, "priceQty": 2, "createTime": "2021-06-01 20:24:08", "taxUnitPriceTag": true, "details_product": ****************, "taxNum": "**********", "department_name": "XX部门", "operator_name": "某某", "invoiceAddress": "某地区街道", "operator": ***********, "bankAccount": "***********", "subBankName": "某某支行", "bankName": "某银行", "invoiceTelephone": "*********", "department": "*********", "cust": ****************, "invoiceUpcType": "0", "natMoney": 3.81, "currency_priceDigit": 3, "invoiceOrg": "****************", "stockUnit_name": "件", "collaborationPolineno": "", "bustype_name": "销售出库", "modifier": "rtduanhy", "firstupcode": "UO-test20210601000012", "source": "1", "natTax": 0.19, "subQty": 2, "taxItems": "5%", "modifyTime": "2021-06-02 15:10:23", "product_cName": "hy母件002", "invoiceTitleType": "0", "receiveContacterPhone": "***********", "modifyInvoiceType": "1", "natCurrencyName": "人民币", "salesOrg_name": "hy组织001", "modifyDate": "2021-06-02 00:00:00", "unitName": "件", "contactName": "张三", "srcBillNO": "******************", "oriUnitPrice": 1.905, "taxCode": "VAT5", "barCode": "st_salesout|****************", "unit_name": ****************, "taxRate": 5, "unit": "件", "productsku_cCode": "hy母件002", "natCurrency_moneyDigit": 2, "accountOrg_name": "hy组织001", "taxId": "VAT5", "invoiceCust": ****************, "qty": 2, "unit_Precision": 2, "oriTaxUnitPrice": 2, "oriMoney": 3.81, "contactsPieces": 2, "contactsQuantity": 2, "natUnitPrice": 1.905, "code": "XSCK20210601000001", "receiveAccountingBasis": "voucher_delivery", "logistics": "XSCK20210601000001", "exchRate": 1, "currencyName": "人民币", "cust_name": "张三啊", "org": "****************", "priceUOM_name": "件", "bustype": "***************", "receiveId": ****************, "upcode": "******************", "saleStyle": "SALE", "iLogisticId": 0, "status_mobile": 0, "natTaxUnitPrice": 2, "salesOutDefineCharacter": {}, "salesOutsDefineCharacter": {}, "salesOutsCharacteristics": {}, "out_sys_id": "", "out_sys_code": "", "out_sys_version": "", "out_sys_type": "", "out_sys_rowno": "", "out_sys_lineid": "", "collaborationPocode": "", "collaborationPoid": 0, "collaborationPodetailid": 0, "collaborationSource": "", "salesOutsExtend!coUpcode": "", "salesOutsExtend!coSourceid": 0, "salesOutsExtend!coSourceLineNo": "", "salesOutsExtend!coSourceType": "" } ], "sumRecordList": [ { "totalPieces": 2, "oriSum": 51222, "invoiceOriSum": 27922, "saleReturnQty": 34, "natSum": 51222, "subQty": 492, "totalQuantity": 2, "priceQty": 492, "qty": 492, "oriMoney": 49705.27, "invoiceQty": 298, "contactsPieces": 481, "contactsQuantity": 481, "natMoney": 49705.27 } ], "pageCount": 3, "beginPageIndex": 1, "endPageIndex": 3, "pubts": "2021-06-02 16:37:29" } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTOList>
<apiDemoReturnDTOList>
<id>2200117974095888423</id>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<content>{"code":999,"message":"列表查询失败"}</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOList>
</apiDemoReturnDTOList>
<routingStgy>0</routingStgy>
<routingStgyList/>
<apiDemoReturnDTO>
<id>2200117974095888422</id>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<content>{ "code": "200", "message": "操作成功", "data": { "pageIndex": 1, "pageSize": 10, "recordCount": 26, "recordList": [ { "cReceiveAddress": "1111", "oriTax": 0.19, "details_stockUnitId": ****************, "product_cCode": "hy母件002", "details_taxId": "8b99f589-bc47-4c8a-bfqw-13d78caa20b0", "natCurrency": "G001ZM0000DEFAULTCURRENCT00000000001", "sourcesys": "udinghuo", "tradeRouteID": 0, "stockUnitId_Precision": 2, "id": ****************, "status_mobile_row": 0, "invoiceTitle": "123抬头", "details_priceUOM": ****************, "natSum": 4, "isEndTrade": 0, "warehouse": ****************, "srcBillType": "1", "diliverStatus": "DELIVERING", "warehouse_name": "调入仓库B", "natCurrency_priceDigit": 3, "exchRateType": "sfaju9kr", "tradeRouteLineno": "", "invExchRate": 1, "product_defaultAlbumId": "", "impactStockTiming": "0", "status": 0, "currency_moneyDigit": 2, "invoiceCust_name": "张三啊", "details_productsku": ****************, "salesOrg": "****************", "invoiceOrg_name": "hy组织001", "tradeRoute_name": "", "productsku_cName": "hy母件002", "vouchdate": "2021-06-01 00:00:00", "invPriceExchRate": 1, "currency": "G001ZM0000DEFAULTCURRENCT00000000001", "pubts": "2021-06-02 15:10:23", "org_name": "hy组织001", "cReceiveMobile": "4353", "createDate": "2021-06-01 00:00:00", "creator": "rtduanhy", "oriSum": 4, "exchRateType_name": "基准汇率", "accountOrg": "****************", "stsalesOutExchangeInfo_d_key": ****************, "cReceiver": "43543", "details_id": ****************, "priceQty": 2, "createTime": "2021-06-01 20:24:08", "taxUnitPriceTag": true, "details_product": ****************, "taxNum": "**********", "department_name": "XX部门", "operator_name": "某某", "invoiceAddress": "某地区街道", "operator": ***********, "bankAccount": "***********", "subBankName": "某某支行", "bankName": "某银行", "invoiceTelephone": "*********", "department": "*********", "cust": ****************, "invoiceUpcType": "0", "natMoney": 3.81, "currency_priceDigit": 3, "invoiceOrg": "****************", "stockUnit_name": "件", "collaborationPolineno": "", "bustype_name": "销售出库", "modifier": "rtduanhy", "firstupcode": "UO-test20210601000012", "source": "1", "natTax": 0.19, "subQty": 2, "taxItems": "5%", "modifyTime": "2021-06-02 15:10:23", "product_cName": "hy母件002", "invoiceTitleType": "0", "receiveContacterPhone": "***********", "modifyInvoiceType": "1", "natCurrencyName": "人民币", "salesOrg_name": "hy组织001", "modifyDate": "2021-06-02 00:00:00", "unitName": "件", "contactName": "张三", "srcBillNO": "******************", "oriUnitPrice": 1.905, "taxCode": "VAT5", "barCode": "st_salesout|****************", "unit_name": ****************, "taxRate": 5, "unit": "件", "productsku_cCode": "hy母件002", "natCurrency_moneyDigit": 2, "accountOrg_name": "hy组织001", "taxId": "VAT5", "invoiceCust": ****************, "qty": 2, "unit_Precision": 2, "oriTaxUnitPrice": 2, "oriMoney": 3.81, "contactsPieces": 2, "contactsQuantity": 2, "natUnitPrice": 1.905, "code": "XSCK20210601000001", "receiveAccountingBasis": "voucher_delivery", "logistics": "XSCK20210601000001", "exchRate": 1, "currencyName": "人民币", "cust_name": "张三啊", "org": "****************", "priceUOM_name": "件", "bustype": "***************", "receiveId": ****************, "upcode": "******************", "saleStyle": "SALE", "iLogisticId": 0, "status_mobile": 0, "natTaxUnitPrice": 2, "salesOutDefineCharacter": {}, "salesOutsDefineCharacter": {}, "salesOutsCharacteristics": {}, "out_sys_id": "", "out_sys_code": "", "out_sys_version": "", "out_sys_type": "", "out_sys_rowno": "", "out_sys_lineid": "", "collaborationPocode": "", "collaborationPoid": 0, "collaborationPodetailid": 0, "collaborationSource": "", "salesOutsExtend!coUpcode": "", "salesOutsExtend!coSourceid": 0, "salesOutsExtend!coSourceLineNo": "", "salesOutsExtend!coSourceType": "" } ], "sumRecordList": [ { "totalPieces": 2, "oriSum": 51222, "invoiceOriSum": 27922, "saleReturnQty": 34, "natSum": 51222, "subQty": 492, "totalQuantity": 2, "priceQty": 492, "qty": 492, "oriMoney": 49705.27, "invoiceQty": 298, "contactsPieces": 481, "contactsQuantity": 481, "natMoney": 49705.27 } ], "pageCount": 3, "beginPageIndex": 1, "endPageIndex": 3, "pubts": "2021-06-02 16:37:29" } }</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>true</rightOrNot>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>true</right>
</apiDemoReturnDTO>
<apiDemoReturnDTOError>
<id>2200117974095888423</id>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<content>{"code":999,"message":"列表查询失败"}</content>
<returnType>JSON</returnType>
<apiDemoReturnDesc/>
<rightOrNot>false</rightOrNot>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<ytenantId/>
<right>false</right>
</apiDemoReturnDTOError>
<errorCodeDTOS>
<errorCodeDTOS>
<id>2200117974095888419</id>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<errorCode>999</errorCode>
<errorMessage>列表查询失败</errorMessage>
<errorType>API</errorType>
<errorcodeDesc>检查查询条件和单据编码是否正确</errorcodeDesc>
<gmtCreate>2025-02-12 10:30:42.000</gmtCreate>
<gmtUpdate>2025-02-12 10:30:42.000</gmtUpdate>
<apiName/>
<edit>false</edit>
<defErrorId>1856804444898853217</defErrorId>
<ytenantId/>
<displayCodeId/>
</errorCodeDTOS>
</errorCodeDTOS>
<displayCodeApiConfigDTOS/>
<tokenPlugin/>
<paramParsePlugin/>
<authPlugin>
<id>09ecc1b0-9d7f-41d1-803a-e78ea2f4e88b</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code/>
<name>友户通token认证业务扩展插件</name>
<configurable>false</configurable>
<description>YonsuitBusinessExtendPlugin</description>
<pluginType>auth</pluginType>
<pluginTypeName>业务扩展插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.businessextend.yonsuite.YonsuitBusinessExtendPlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>false</visible>
<gmtCreate>2020-05-22 00:00:00</gmtCreate>
<gmtUpdate>2020-05-22 00:00:00</gmtUpdate>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>05a2a46b90984a8ba115d6a2061f8720</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</authPlugin>
<resultParsePlugin>
<id>w181ed01-1e9b-4350-b994-71a66f062522</id>
<isvId>75c13acc-ae53-4694-9a6e-3ddcce26d7f3</isvId>
<code>resultParse</code>
<name>UCG标准返回值解析插件</name>
<configurable>false</configurable>
<description>符合UCG标准的返回值会自动解析，不符合的会自动略过</description>
<pluginType>resultParse</pluginType>
<pluginTypeName>返回值解析插件</pluginTypeName>
<pluginLevel>publishapp</pluginLevel>
<classPath>com.yonyou.ucg.auth.result.UCGResultParsePlugin</classPath>
<expansionConf/>
<defaultPlugin>true</defaultPlugin>
<open>true</open>
<visible>true</visible>
<gmtCreate>2019-08-19 00:00:00</gmtCreate>
<gmtUpdate/>
<packagePath/>
<useScene>1</useScene>
<runStatus>run</runStatus>
<runVersion/>
<toDeploy>false</toDeploy>
<levelApi>true</levelApi>
<levelApp>false</levelApp>
<levelPublishapp>true</levelPublishapp>
<levelSystem>false</levelSystem>
<containsDefault/>
<levels>
<levels>levelApi</levels>
<levels>levelPublishapp</levels>
</levels>
<levelsDesc/>
<deployStatus>deploy</deployStatus>
<deployVersion/>
<custom>false</custom>
<strategyId/>
<strategyName/>
<superiorId>05a2a46b90984a8ba115d6a2061f8720</superiorId>
<ytenantId>0</ytenantId>
<unPluginCode/>
<runStatusDesc>UID:P_UCG_177A9F3E05D0015D</runStatusDesc>
</resultParsePlugin>
<mapReturnPluginConfig/>
<billNo>st_salesout</billNo>
<domain>ustock</domain>
<apiCategory/>
<docUrl/>
<pathMatch>0</pathMatch>
<createUser/>
<createUserName/>
<approvalStatus>4</approvalStatus>
<publishTime>2025-03-04 20:44:38</publishTime>
<pathJoin>true</pathJoin>
<timeOut>30</timeOut>
<tokenPluginName/>
<authPluginName/>
<resultPluginName/>
<apiDemoReturnRightDemo/>
<apiDemoReturnErrorDemo/>
<mock>false</mock>
<mockTimeout/>
<customUrl>/salesout/list</customUrl>
<fixedUrl>/yonbip/scm</fixedUrl>
<apiCode>05a2a46b90984a8ba115d6a2061f8720</apiCode>
<tokenCheckType>0</tokenCheckType>
<enableMulti>false</enableMulti>
<multiField/>
<idempotent>non</idempotent>
<bidirectionalSSL/>
<ucgSchema>HTTPS</ucgSchema>
<updateUserId>4dee9132-848b-4d75-9734-ec7364b39cee</updateUserId>
<updateUserName>18631471883</updateUserName>
<paramIsForce/>
<userIDPassthrough>true</userIDPassthrough>
<applyUser/>
<applyMsg/>
<dr>0</dr>
<microServiceCode>domain.yonbip-scm-stock</microServiceCode>
<applicationCode>yonbip-scm-stock</applicationCode>
<privacyCategory>2</privacyCategory>
<privacyLevel>4</privacyLevel>
<apiDesigned>0</apiDesigned>
<serviceType>0</serviceType>
<integrateSchemeCode/>
<integrateSchemeName/>
<integrateObjectCode/>
<integrateObjectName/>
<integrateObjectCreatedType/>
<returnIntegObjId/>
<returnIntegObjName/>
<apiIntegrateDTOList/>
<apiRouteInfoDTOList/>
<arrayParam>false</arrayParam>
<fileSize/>
<cc>true</cc>
<paramTransferMode>2</paramTransferMode>
<ytenantId>0</ytenantId>
<statusConf/>
<scene>1</scene>
<version/>
<bizObjUri/>
<bizObjOperationType/>
<apiDefId>1856804444898852871</apiDefId>
<paramExtBizObjCode/>
<paramExtBizObjName/>
<paramExtRequest>1</paramExtRequest>
<paramExtResponse>1</paramExtResponse>
<paramExtInExtendKey>1</paramExtInExtendKey>
<openScene>1</openScene>
<integrationScene/>
<apiType/>
<paramMark/>
<integrateSysId/>
<integrateSysName/>
<integrateSysCode/>
<dataZoneSetting>false</dataZoneSetting>
<reqDataZoneSetting>false</reqDataZoneSetting>
<respDataZoneSetting>false</respDataZoneSetting>
<reqDataAllQuery>false</reqDataAllQuery>
<reqDataAllBody>false</reqDataAllBody>
<respDataAllBody>false</respDataAllBody>
<chargeStatus>1</chargeStatus>
<beforeSpeed>60</beforeSpeed>
<afterSpeed>120</afterSpeed>
<speedStatus>false</speedStatus>
<reqDataRefPath/>
<respDataRefPath/>
<pubHistory>
<pubHistory>
<id>2215277808488808453</id>
<apiId>05a2a46b90984a8ba115d6a2061f8720</apiId>
<apiName>销售出库列表查询</apiName>
<applyReason>UKC-142902审核更新存量，增加更新存量传财务时机反参描述</applyReason>
<publishUserName/>
<version>20250304204438</version>
<operationTime>2025-03-04</operationTime>
<gmtCreate/>
<gmtUpdate/>
<changes>
<changes>
<changePosition>baseInfo</changePosition>
<newList/>
<updateList>
<updateList>
<changeProperty>enableMulti</changeProperty>
<oldValue/>
<newValue>false</newValue>
</updateList>
</updateList>
<deleteList/>
</changes>
<changes>
<changePosition>paramDTOS</changePosition>
<newList/>
<updateList>
<updateList>
<changeProperty>isdefault</changeProperty>
<oldValue>{"id":"2098131056030908859","name":"isdefault","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908482","array":false,"paramDesc":"该参数可忽略不管","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_isdefault","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953805","name":"isdefault","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852873","array":false,"paramDesc":"该参数可忽略不管","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_isdefault","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>pageIndex</changeProperty>
<oldValue>{"id":"2098131056030908860","name":"pageIndex","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908483","array":false,"paramDesc":"页号","paramType":"int","requestParamType":"BodyParam","path":"BodyParam_pageIndex","example":"","fullName":"","required":true,"defaultValue":"1","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953806","name":"pageIndex","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852874","array":false,"paramDesc":"页号","paramType":"int","requestParamType":"BodyParam","path":"BodyParam_pageIndex","example":"","fullName":"","required":true,"defaultValue":"1","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>code</changeProperty>
<oldValue>{"id":"2098131056030908861","name":"code","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908484","array":false,"paramDesc":"单据编号","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_code","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953807","name":"code","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852875","array":false,"paramDesc":"单据编号","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_code","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>pageSize</changeProperty>
<oldValue>{"id":"2098131056030908862","name":"pageSize","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908485","array":false,"paramDesc":"每页行数","paramType":"int","requestParamType":"BodyParam","path":"BodyParam_pageSize","example":"","fullName":"","required":true,"defaultValue":"10","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953808","name":"pageSize","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852876","array":false,"paramDesc":"每页行数","paramType":"int","requestParamType":"BodyParam","path":"BodyParam_pageSize","example":"","fullName":"","required":true,"defaultValue":"10","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>vouchdate</changeProperty>
<oldValue>{"id":"2098131056030908863","name":"vouchdate","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908486","array":false,"paramDesc":"单据日期 区间格式，2021-05-06|2021-05-06 23:00:00。若传入单个时间如：2021-05-06，则查询该时间之后，到当前时间之间的单据","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_vouchdate","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953809","name":"vouchdate","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852877","array":false,"paramDesc":"单据日期 区间格式，2021-05-06|2021-05-06 23:00:00。若传入单个时间如：2021-05-06，则查询该时间之后，到当前时间之间的单据","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_vouchdate","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>stockOrg</changeProperty>
<oldValue>{"id":"2098131056030908864","name":"stockOrg","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908487","array":false,"paramDesc":"库存组织id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_stockOrg","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953810","name":"stockOrg","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852878","array":false,"paramDesc":"库存组织id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_stockOrg","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>salesOrg</changeProperty>
<oldValue>{"id":"2098131056030908865","name":"salesOrg","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908488","array":false,"paramDesc":"销售组织id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_salesOrg","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953811","name":"salesOrg","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852879","array":false,"paramDesc":"销售组织id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_salesOrg","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>invoiceOrg</changeProperty>
<oldValue>{"id":"2098131056030908866","name":"invoiceOrg","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908489","array":false,"paramDesc":"开票组织ID","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_invoiceOrg","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953812","name":"invoiceOrg","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852880","array":false,"paramDesc":"开票组织ID","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_invoiceOrg","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>invoiceCust</changeProperty>
<oldValue>{"id":"2098131056030908867","name":"invoiceCust","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908490","array":false,"paramDesc":"开票客户id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_invoiceCust","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953813","name":"invoiceCust","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852881","array":false,"paramDesc":"开票客户id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_invoiceCust","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>upcode</changeProperty>
<oldValue>{"id":"2098131056030908868","name":"upcode","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908491","array":false,"paramDesc":"来源单据号","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_upcode","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953814","name":"upcode","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852882","array":false,"paramDesc":"来源单据号","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_upcode","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>department</changeProperty>
<oldValue>{"id":"2098131056030908869","name":"department","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908492","array":false,"paramDesc":"部门id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_department","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953815","name":"department","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852883","array":false,"paramDesc":"部门id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_department","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>operator</changeProperty>
<oldValue>{"id":"2098131056030908870","name":"operator","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908493","array":false,"paramDesc":"业务员id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_operator","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953816","name":"operator","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852884","array":false,"paramDesc":"业务员id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_operator","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>warehouse</changeProperty>
<oldValue>{"id":"2098131056030908871","name":"warehouse","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908494","array":false,"paramDesc":"仓库id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_warehouse","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953817","name":"warehouse","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852885","array":false,"paramDesc":"仓库id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_warehouse","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>stockMgr</changeProperty>
<oldValue>{"id":"2098131056030908872","name":"stockMgr","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908495","array":false,"paramDesc":"库管员id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_stockMgr","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953818","name":"stockMgr","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852886","array":false,"paramDesc":"库管员id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_stockMgr","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>cust</changeProperty>
<oldValue>{"id":"2098131056030908873","name":"cust","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908496","array":false,"paramDesc":"客户id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_cust","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":"0"}</oldValue>
<newValue>{"id":"2200117965505953819","name":"cust","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852887","array":false,"paramDesc":"客户id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_cust","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":"0"}</newValue>
</updateList>
<updateList>
<changeProperty>product_cName</changeProperty>
<oldValue>{"id":"2098131056030908874","name":"product_cName","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908497","array":false,"paramDesc":"物料id","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_product_cName","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2200117965505953820","name":"product_cName","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852888","array":false,"paramDesc":"物料id","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_product_cName","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>bustype.name</changeProperty>
<oldValue>{"id":"2098131056030908875","name":"bustype.name","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908498","array":false,"paramDesc":"交易类型名称","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_bustype.name","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2200117965505953821","name":"bustype.name","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852889","array":false,"paramDesc":"交易类型名称","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_bustype.name","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>product_cName_ManageClass</changeProperty>
<oldValue>{"id":"2098131056030908876","name":"product_cName_ManageClass","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908499","array":false,"paramDesc":"物料分类id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_product_cName_ManageClass","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2200117965505953822","name":"product_cName_ManageClass","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852890","array":false,"paramDesc":"物料分类id","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_product_cName_ManageClass","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>isSum</changeProperty>
<oldValue>{"id":"2098131056030908877","name":"isSum","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908500","array":false,"paramDesc":"查询表头","paramType":"boolean","requestParamType":"BodyParam","path":"BodyParam_isSum","example":"false","fullName":"","required":false,"defaultValue":"false","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2200117965505953823","name":"isSum","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852891","array":false,"paramDesc":"查询表头","paramType":"boolean","requestParamType":"BodyParam","path":"BodyParam_isSum","example":"false","fullName":"","required":false,"defaultValue":"false","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>simpleVOs</changeProperty>
<oldValue>{"id":"2098131056030908854","name":"simpleVOs","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"2098131056030908501","array":true,"paramDesc":"扩展查询条件","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_simpleVOs","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2200117965505953800","name":"simpleVOs","apiId":"05a2a46b90984a8ba115d6a2061f8720","defParamId":"1856804444898852892","array":true,"paramDesc":"扩展查询条件","paramType":"object","requestParamType":"BodyParam","path":"BodyParam_simpleVOs","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>op</changeProperty>
<oldValue>{"id":"2098131056030908855","name":"op","apiId":"05a2a46b90984a8ba115d6a2061f8720","parentId":"2098131056030908854","defParamId":"2098131056030908502","array":false,"paramDesc":"比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.op","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2200117965505953801","name":"op","apiId":"05a2a46b90984a8ba115d6a2061f8720","parentId":"2200117965505953800","defParamId":"1856804444898852893","array":false,"paramDesc":"比较符(条件eq:相等, neq：不等, lt：小于, gt：大于, elt：小于等于, egt：大于等于, between：区间, in：包含, nin：不包含, like：包含字符, leftlike：左侧字符包含, rightlike：右侧字符包含, is_null：为空, is_not_null：不为空, and：和, or：或 )","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.op","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>value2</changeProperty>
<oldValue>{"id":"2098131056030908856","name":"value2","apiId":"05a2a46b90984a8ba115d6a2061f8720","parentId":"2098131056030908854","defParamId":"2098131056030908503","array":false,"paramDesc":"值2(条件)如：\"2021-04-19 23:59:59\"","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.value2","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2200117965505953802","name":"value2","apiId":"05a2a46b90984a8ba115d6a2061f8720","parentId":"2200117965505953800","defParamId":"1856804444898852894","array":false,"paramDesc":"值2(条件)如：\"2021-04-19 23:59:59\"","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.value2","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>value1</changeProperty>
<oldValue>{"id":"2098131056030908857","name":"value1","apiId":"05a2a46b90984a8ba115d6a2061f8720","parentId":"2098131056030908854","defParamId":"2098131056030908504","array":false,"paramDesc":"值1(条件)如： \"2021-04-19 00:00:00\"","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.value1","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2200117965505953803","name":"value1","apiId":"05a2a46b90984a8ba115d6a2061f8720","parentId":"2200117965505953800","defParamId":"1856804444898852895","array":false,"paramDesc":"值1(条件)如： \"2021-04-19 00:00:00\"","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.value1","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
<updateList>
<changeProperty>field</changeProperty>
<oldValue>{"id":"2098131056030908858","name":"field","apiId":"05a2a46b90984a8ba115d6a2061f8720","parentId":"2098131056030908854","defParamId":"2098131056030908505","array":false,"paramDesc":"属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码details.product.cCode、表头自定义项headItem.define1、表体自定义项details.bodyItem.define1等)","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.field","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"maxLength":""}</oldValue>
<newValue>{"id":"2200117965505953804","name":"field","apiId":"05a2a46b90984a8ba115d6a2061f8720","parentId":"2200117965505953800","defParamId":"1856804444898852896","array":false,"paramDesc":"属性名(条件传属性的名称，如仓库编码warehouse.code、时间戳pubts、物料编码details.product.cCode、表头自定义项headItem.define1、表体自定义项details.bodyItem.define1等)","paramType":"string","requestParamType":"BodyParam","path":"BodyParam_simpleVOs.field","example":"","fullName":"","required":false,"defaultValue":"","visible":true,"enableMulti":false,"maxLength":""}</newValue>
</updateList>
</updateList>
<deleteList/>
</changes>
<changes>
<changePosition>paramReturnDTOS</changePosition>
<newList>
<newList>
<changeProperty>impactStockTiming</changeProperty>
<oldValue/>
<newValue>{"id":"2200117965505953874","name":"impactStockTiming","apiId":"05a2a46b90984a8ba115d6a2061f8720","parentId":"2200117965505953849","defParamId":"2200117965505953798","array":false,"paramDesc":"更新存量传财务时机,0:保存;1:审核","paramType":"string","path":"null_data.recordList.impactStockTiming","example":"0"}</newValue>
</newList>
</newList>
<updateList/>
<deleteList/>
</changes>
</changes>
</pubHistory>
</pubHistory>
<deprecated>0</deprecated>
<recommendedApiId/>
<recommendedApiName/>
<domainAppCode/>
<multiVersion>0</multiVersion>
<apiTag/>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>2108770660671029249</id>
<name>用友YonBIP</name>
<type>integrateSys</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>SCC</id>
<name>供应链云</name>
<type>1</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>MM</id>
<name>采购供应</name>
<type>2</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>ST</id>
<name>库存管理</name>
<type>3</type>
<sort>0</sort>
<enable>0</enable>
<children>
<children>
<id>ustock.st_salesout</id>
<name>销售出库单</name>
<type>4</type>
<sort>0</sort>
<enable>0</enable>
<children/>
<parentId/>
<productId/>
<code>ustock.st_salesout</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>ST</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>MM</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>SCC</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<order>0</order>
</children>
</children>
<parentId/>
<productId/>
<code>current_yonbip_default_sys</code>
<tenantId/>
<edit>true</edit>
<addChild>true</addChild>
<addBro>true</addBro>
<isOrigin>0</isOrigin>
<hasChildren>0</hasChildren>
<order>0</order>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:39:47</gmtCreate>
<gmtUpdate>2025-07-26 17:39:47</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>eaf95e8b-6292-444b-bf96-a88e6a9391f5</id>
<name>SF04</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>发票号（表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:39:56</gmtCreate>
<gmtUpdate>2025-07-26 17:39:56</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>c28aa2d2-a8a9-4b93-94fb-385ec48ad931</id>
<name>SF05</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>发货单日期</paramDesc>
<paramType>Date</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>date</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:39:56</gmtCreate>
<gmtUpdate>2025-07-26 17:39:56</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>6c24a623-0d0c-4bc7-8ad4-afe5279d28ad</id>
<name>SF06</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>发货单号</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:39:56</gmtCreate>
<gmtUpdate>2025-07-26 17:39:56</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>4f3b2a36-4ee4-430a-83b4-e6e1f7c5155f</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:39:56</gmtCreate>
<gmtUpdate>2025-07-26 17:39:56</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>047d4c0e-31a5-48c3-b586-0637b4dc7780</id>
<name>XS33</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>业务员</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:39:56</gmtCreate>
<gmtUpdate>2025-07-26 17:39:56</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>50</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>47ac2996-3cac-4a22-bd41-42a66d08afa8</id>
<name>XS37</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>含税标准报价特征</paramDesc>
<paramType>Decimal</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>number</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:39:56</gmtCreate>
<gmtUpdate>2025-07-26 17:39:56</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>24</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:39:56</gmtCreate>
<gmtUpdate>2025-07-26 17:39:56</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
<ResultVO>
<success>true</success>
<code>200</code>
<message/>
<data>
<id>72113971-ae4c-4188-bc55-44b6173f4e0b</id>
<name>XS15</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>顾客订单号（订单表体）</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:40:06</gmtCreate>
<gmtUpdate>2025-07-26 17:40:06</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>b946709d-f4d9-4a43-a551-f55beee7f3d5</id>
<name>XXX0111</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>需求分类项</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:40:06</gmtCreate>
<gmtUpdate>2025-07-26 17:40:06</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>255</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
<data>
<id>5a93627b-27f7-40cb-a8fc-d6599545710b</id>
<name>id</name>
<apiId/>
<parentId/>
<defParamId/>
<array>false</array>
<paramDesc>特征id,主键,新增时无需填写,修改时必填</paramDesc>
<paramType>string</paramType>
<requestParamType>BodyParam</requestParamType>
<path/>
<example/>
<fullName/>
<ytenantId/>
<paramOrder/>
<bizType>text</bizType>
<baseType>true</baseType>
<defaultValue/>
<required>false</required>
<visible>true</visible>
<gmtCreate>2025-07-26 17:40:06</gmtCreate>
<gmtUpdate>2025-07-26 17:40:06</gmtUpdate>
<entityId/>
<entityCode/>
<apiName/>
<maxLength>36</maxLength>
<childId/>
<edit>true</edit>
<regularRule/>
<mapName/>
<mapRequestParamType/>
<refType>false</refType>
<refTypeContext/>
<defineHidden>0</defineHidden>
<integrateObjectId/>
<format/>
<decimals/>
<paramTag/>
<rowLimit/>
<enableMulti>false</enableMulti>
<extend>false</extend>
</data>
</ResultVO>
