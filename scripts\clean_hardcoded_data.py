import re
from pathlib import Path

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理硬编码测试数据脚本
Clean Hardcoded Test Data
"""


def clean_hardcoded_data():
    """清理硬编码测试数据"""
    project_root = Path(__file__).parent.parent

    print("🧹 清理硬编码测试数据")
    print("=" * 50)

    # 需要清理的关键文件模式
    test_patterns_to_remove = [
        (r"test_data\s*=\s*[{\[].*?[}\]]", "# 测试数据已移除"),
        (r"mock_response\s*=\s*[{\[].*?[}\]]", "# 模拟响应已移除"),
        (r"dummy_data\s*=\s*[{\[].*?[}\]]", "# 虚拟数据已移除"),
        (r'TEST_[A-Z_]+\s*=\s*[\'"][^\'\"]*[\'"]', "# 测试常量已移除"),
        (r'fake_\w+\s*=\s*[\'"][^\'\"]*[\'"]', "# 虚假数据已移除"),
        (r'debug_\w+\s*=\s*[\'"][^\'\"]*[\'"]', "# 调试数据已移除"),
    ]

    js_patterns_to_remove = [
        (r"const\s+test\w*\s*=\s*[{\[].*?[}\]];?", "// 测试数据已移除"),
        (r"let\s+test\w*\s*=\s*[{\[].*?[}\]];?", "// 测试数据已移除"),
        (r"var\s+test\w*\s*=\s*[{\[].*?[}\]];?", "// 测试数据已移除"),
        (r'mock\w*\s*:\s*[\'"][^\'\"]*[\'"],?', "// 模拟数据已移除"),
        (r'test\w*\s*:\s*[\'"][^\'\"]*[\'"],?', "// 测试数据已移除"),
        (r'debug\w*\s*:\s*[\'"][^\'\"]*[\'"],?', "// 调试数据已移除"),
    ]

    files_cleaned = 0
    lines_cleaned = 0

    # 清理Python文件
    print("🐍 清理Python文件中的硬编码数据...")
    for py_file in project_root.rglob("*.py"):
        if (
            py_file.is_file()
            and "scripts" not in str(py_file)
            and "cleanup" not in str(py_file)
        ):
            try:
                with open(py_file, "r", encoding="utf-8") as f:
                    content = f.read()

                original_content = content

                # 应用清理模式
                for pattern, replacement in test_patterns_to_remove:
                    if re.search(pattern, content, re.MULTILINE | re.DOTALL):
                        content = re.sub(
                            pattern,
                            replacement,
                            content,
                            flags=re.MULTILINE | re.DOTALL,
                        )
                        lines_cleaned += 1

                # 如果有修改，写回文件
                if content != original_content:
                    with open(py_file, "w", encoding="utf-8") as f:
                        f.write(content)
                    files_cleaned += 1
                    rel_path = py_file.relative_to(project_root)
                    print(f"  ✅ 已清理: {rel_path}")

            except (UnicodeDecodeError, PermissionError):
                continue

    # 清理JavaScript文件
    print("\n📜 清理JavaScript文件中的硬编码数据...")
    for js_file in project_root.rglob("*.js"):
        if js_file.is_file() and "node_modules" not in str(js_file):
            try:
                with open(js_file, "r", encoding="utf-8") as f:
                    content = f.read()

                original_content = content

                # 应用清理模式
                for pattern, replacement in js_patterns_to_remove:
                    if re.search(pattern, content, re.MULTILINE | re.DOTALL):
                        content = re.sub(
                            pattern,
                            replacement,
                            content,
                            flags=re.MULTILINE | re.DOTALL,
                        )
                        lines_cleaned += 1

                # 如果有修改，写回文件
                if content != original_content:
                    with open(js_file, "w", encoding="utf-8") as f:
                        f.write(content)
                    files_cleaned += 1
                    rel_path = js_file.relative_to(project_root)
                    print(f"  ✅ 已清理: {rel_path}")

            except (UnicodeDecodeError, PermissionError):
                continue

    # 清理明显的测试文件残留
    print("\n🗑️ 清理残留的测试文件...")
    test_files_to_remove = [
        "backend/test_api_endpoints.py",
        "backend/test_database_operations.py",
        "backend/quick_test.py",
        "backend/run_tests.py",
        "tests/test_enhanced_error_handler.py",
        "tests/test_week2_validation.py",
        "tests/locust_stress_test.py",
        "tests/test_connection_and_transaction.py",
        "tests/api_test_server.py",
        "tests/new_system/test_purchase_order.py",
        "scripts/test_elk_connection.py",
        "tools/error_handling_load_test.py",
    ]

    removed_files = 0
    for file_path in test_files_to_remove:
        full_path = project_root / file_path
        if full_path.exists():
            try:
                full_path.unlink()
                print(f"  ✅ 已删除: {file_path}")
                removed_files += 1
            except Exception:
                print(f"  ❌ 删除失败 {file_path}: {e}")

    # 清理HTML测试文件
    print("\n🌐 清理HTML测试文件...")
    html_test_files = [
        "frontend/new-architecture-test.html",
        "frontend/migration-test-fixed.html",
        "frontend/migration-test.html",
        "frontend/method-fix-test.html",
        "tests/path-test.html",
    ]

    for file_path in html_test_files:
        full_path = project_root / file_path
        if full_path.exists():
            try:
                full_path.unlink()
                print(f"  ✅ 已删除: {file_path}")
                removed_files += 1
            except Exception:
                print(f"  ❌ 删除失败 {file_path}: {e}")

    print(f"\n📊 硬编码数据清理结果:")
    print(f"  - 修改的文件: {files_cleaned} 个")
    print(f"  - 清理的数据行: {lines_cleaned} 行")
    print(f"  - 删除的文件: {removed_files} 个")
    print(f"  - 总计处理: {files_cleaned + removed_files} 项")

    return files_cleaned, lines_cleaned, removed_files


if __name__ == "__main__":
    files, lines, removed = clean_hardcoded_data()
    print(f"\n🎉 硬编码数据清理完成！")
    print(f"总计: 修改{files}个文件，清理{lines}行数据，删除{removed}个文件")
    print("\n📋 下一步建议:")
    print("  1. 运行系统验证确认清理后功能正常")
    print("  2. 开始模块功能测试")
    print("  3. 启动前后端服务进行完整验证")
