env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.9'
jobs:
  build-and-deploy:
    if: github.ref == "refs/heads/main"
    needs:
    - test
    - security-scan
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    - name: Build Docker image
      run: docker build -t ys-api-v3:${{ github.sha }} .
    - name: Deploy to staging
      run: echo "部署到暂存环境"
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    - name: Run security scan
      uses: securecodewarrior/github-action-add-sarif@v1
      with:
        sarif-file: security-results.sarif
  test:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    - name: Install dependencies
      run: pip install -r backend/requirements.txt
    - name: Run tests
      run: cd backend && python -m pytest tests/
    - name: Code quality check
      run: cd backend && flake8 app/
name: YS-API V3.0 CI/CD Pipeline
'on':
  pull_request:
    branches:
    - main
  push:
    branches:
    - main
    - develop
