@echo off
chcp 65001 >nul
echo ========================================
echo YS-API V3.0 生产环境部署脚本
echo ========================================

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 请以管理员身份运行此脚本
    pause
    exit /b 1
)

:: 设置安装目录
set INSTALL_DIR=C:\YS-API
set LOG_DIR=%INSTALL_DIR%\logs

echo 📁 安装目录: %INSTALL_DIR%
echo 📁 日志目录: %LOG_DIR%

:: 创建目录
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

:: 复制文件
echo 🔄 复制程序文件...
xcopy /E /I /Y "backend" "%INSTALL_DIR%\backend"
xcopy /E /I /Y "config" "%INSTALL_DIR%\config"
xcopy /E /I /Y "frontend" "%INSTALL_DIR%\frontend"
copy "start_production_with_port_check.py" "%INSTALL_DIR%\"
copy "install_windows_service.py" "%INSTALL_DIR%\"

:: 创建必要的目录
echo 📁 创建必要目录...
if not exist "%INSTALL_DIR%\cache" mkdir "%INSTALL_DIR%\cache"
if not exist "%INSTALL_DIR%\logs\temp" mkdir "%INSTALL_DIR%\logs\temp"
if not exist "%INSTALL_DIR%\logs\cache" mkdir "%INSTALL_DIR%\logs\cache"
if not exist "%INSTALL_DIR%\downloads" mkdir "%INSTALL_DIR%\downloads"
if not exist "%INSTALL_DIR%\uploads\temp" mkdir "%INSTALL_DIR%\uploads\temp"

:: 安装依赖
echo 📦 安装Python依赖...
pip install pywin32 psutil structlog fastapi uvicorn sqlalchemy pyodbc pydantic-settings schedule

:: 安装Windows服务
echo 🔧 安装Windows服务...
cd /d "%INSTALL_DIR%"
python install_windows_service.py install

if %errorLevel% equ 0 (
    echo ✅ Windows服务安装成功
    
    :: 启动服务
    echo 🚀 启动服务...
    python install_windows_service.py start
    
    if %errorLevel% equ 0 (
        echo ✅ 服务启动成功
        echo.
        echo 📋 服务信息:
        echo   服务名称: YSAPIService
        echo   显示名称: YS-API V3.0 Data Sync Service
        echo   端口: 8000
        echo   管理界面: http://localhost:8000/frontend/database-v2.html
        echo.
        echo 📝 管理命令:
        echo   启动服务: net start YSAPIService
        echo   停止服务: net stop YSAPIService
        echo   重启服务: net stop YSAPIService && net start YSAPIService
        echo.
        echo 📁 日志文件:
        echo   %LOG_DIR%\windows_service.log
        echo   %LOG_DIR%\production_enhanced.log
        echo   %LOG_DIR%\auto_sync.log
    ) else (
        echo ❌ 服务启动失败
    )
) else (
    echo ❌ Windows服务安装失败
)

echo.
echo ========================================
echo 部署完成
echo ========================================
pause 