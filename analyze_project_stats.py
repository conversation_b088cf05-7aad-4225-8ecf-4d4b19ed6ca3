import os
from collections import Counter

#!/usr/bin/env python3
"""
项目文件统计工具
"""


def analyze_project_files():
    """分析项目文件分布"""
    file_stats = Counter()
    all_files = []

    for root, dirs, files in os.walk("."):
        for file in files:
            ext = os.path.splitext(file)[1] or "no_ext"
            file_stats[ext] += 1
            all_files.append(os.path.join(root, file))

    print("📊 项目文件爆炸统计:")
    print(f"总文件数: {sum(file_stats.values())}")
    print("\n文件类型分布:")
    for ext, count in file_stats.most_common(15):
        print(f"  {ext}: {count}个")

    print(f"\n重点文件类型:")
    print(f'Python文件: {file_stats.get(".py", 0)}个')
    print(f'HTML文件: {file_stats.get(".html", 0)}个')
    print(f'JS文件: {file_stats.get(".js", 0)}个')
    print(f'备份文件: {file_stats.get(".backup", 0)}个')
    print(f'日志文件: {file_stats.get(".log", 0)}个')
    print(f'JSON文件: {file_stats.get(".json", 0)}个')
    print(f'MD文件: {file_stats.get(".md", 0)}个')

    # 查找可疑重复文件
    print("\n🔍 可疑重复文件模式:")
    suspicious_files = []
    for filepath in all_files:
        basename = os.path.basename(filepath)
        if any(
            pattern in basename.lower()
            for pattern in [
                "backup",
                "copy",
                "duplicate",
                "dup_",
                "_copy",
                "_backup",
                "_old",
                "_new",
                "_test",
                "_temp",
                "_v2",
                "_v3",
            ]
        ):
            suspicious_files.append(filepath)

    if suspicious_files:
        print(f"发现 {len(suspicious_files)} 个可疑重复文件:")
        for f in suspicious_files[:20]:  # 只显示前20个
            print(f"  {f}")
        if len(suspicious_files) > 20:
            print(f"  ... 还有 {len(suspicious_files) - 20} 个")
    else:
        print("✅ 未发现明显的重复文件模式")


if __name__ == "__main__":
    analyze_project_files()
