{"module_name": "materialout", "display_name": "材料出库", "version": "2.0.0", "source": "json_parser", "total_fields": 163, "created_at": "2025-07-28T20:12:24.838968", "last_updated": "2025-07-28T20:12:24.838968", "fields": {"code": {"api_field_name": "code", "chinese_name": "单据编号", "data_type": "NVARCHAR(500)", "param_desc": "单据编号", "path": "data.recordList.code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "message": {"api_field_name": "message", "chinese_name": "调用失败时的错误信息", "data_type": "NVARCHAR(500)", "param_desc": "调用失败时的错误信息", "path": "message", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "data": {"api_field_name": "data", "chinese_name": "调用成功时的返回数据", "data_type": "NVARCHAR(MAX)", "param_desc": "调用成功时的返回数据", "path": "data", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "sumRecordList": {"api_field_name": "sumRecordList", "chinese_name": "sum合计信息", "data_type": "NVARCHAR(MAX)", "param_desc": "sum合计信息", "path": "data.sumRecordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalPieces": {"api_field_name": "totalPieces", "chinese_name": "整单件数", "data_type": "NVARCHAR(500)", "param_desc": "整单件数", "path": "data.recordList.totalPieces", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "totalQuantity": {"api_field_name": "totalQuantity", "chinese_name": "整单数量", "data_type": "BIGINT", "param_desc": "整单数量", "path": "data.recordList.totalQuantity", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "subQty": {"api_field_name": "subQty", "chinese_name": "件数", "data_type": "BIGINT", "param_desc": "件数", "path": "data.recordList.subQty", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "qty": {"api_field_name": "qty", "chinese_name": "数量", "data_type": "BIGINT", "param_desc": "数量", "path": "data.recordList.qty", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "pageIndex": {"api_field_name": "pageIndex", "chinese_name": "页号", "data_type": "BIGINT", "param_desc": "页号", "path": "pageIndex", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageSize": {"api_field_name": "pageSize", "chinese_name": "每页行数", "data_type": "BIGINT", "param_desc": "每页行数", "path": "pageSize", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pageCount": {"api_field_name": "pageCount", "chinese_name": "总页数", "data_type": "BIGINT", "param_desc": "总页数", "path": "data.pageCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "beginPageIndex": {"api_field_name": "beginPageIndex", "chinese_name": "开始页码", "data_type": "BIGINT", "param_desc": "开始页码", "path": "data.beginPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "endPageIndex": {"api_field_name": "endPageIndex", "chinese_name": "结束页码", "data_type": "BIGINT", "param_desc": "结束页码", "path": "data.endPageIndex", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "recordCount": {"api_field_name": "recordCount", "chinese_name": "总条数", "data_type": "BIGINT", "param_desc": "总条数", "path": "data.recordCount", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "pubts": {"api_field_name": "pubts", "chinese_name": "时间戳,格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "时间戳,格式为:yyyy-MM-dd HH:mm:ss", "path": "data.recordList.pubts", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "recordList": {"api_field_name": "recordList", "chinese_name": "返回结果对象", "data_type": "NVARCHAR(MAX)", "param_desc": "返回结果对象", "path": "data.recordList", "depth": 1, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "currency": {"api_field_name": "currency", "chinese_name": "币种id", "data_type": "NVARCHAR(500)", "param_desc": "币种id", "path": "data.recordList.currency", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "materOuts_product": {"api_field_name": "materOuts_product", "chinese_name": "物料id", "data_type": "NVARCHAR(500)", "param_desc": "物料id", "path": "data.recordList.materOuts_product", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "materOuts_unit": {"api_field_name": "materOuts_unit", "chinese_name": "主计量", "data_type": "NVARCHAR(500)", "param_desc": "主计量", "path": "data.recordList.materOuts_unit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "materOuts_productsku": {"api_field_name": "materOuts_productsku", "chinese_name": "物料sku", "data_type": "NVARCHAR(500)", "param_desc": "物料sku", "path": "data.recordList.materOuts_productsku", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "vouchdate": {"api_field_name": "vouchdate", "chinese_name": "单据日期", "data_type": "NVARCHAR(500)", "param_desc": "单据日期", "path": "data.recordList.vouchdate", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "org": {"api_field_name": "org", "chinese_name": "库存组织IDid", "data_type": "NVARCHAR(500)", "param_desc": "库存组织IDid", "path": "data.recordList.org", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "org_code": {"api_field_name": "org_code", "chinese_name": "库存组织编码", "data_type": "NVARCHAR(500)", "param_desc": "库存组织编码", "path": "data.recordList.org_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "org_name": {"api_field_name": "org_name", "chinese_name": "库存组织名称", "data_type": "NVARCHAR(500)", "param_desc": "库存组织名称", "path": "data.recordList.org_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "store": {"api_field_name": "store", "chinese_name": "门店id", "data_type": "NVARCHAR(500)", "param_desc": "门店id", "path": "data.recordList.store", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bustype": {"api_field_name": "bustype", "chinese_name": "业务类型id", "data_type": "NVARCHAR(500)", "param_desc": "业务类型id", "path": "data.recordList.bustype", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bustype_name": {"api_field_name": "bustype_name", "chinese_name": "交易类型", "data_type": "NVARCHAR(500)", "param_desc": "交易类型", "path": "bustype_name", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "store_name": {"api_field_name": "store_name", "chinese_name": "门店名称", "data_type": "NVARCHAR(500)", "param_desc": "门店名称", "path": "data.recordList.store_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "department_name": {"api_field_name": "department_name", "chinese_name": "部门名称", "data_type": "NVARCHAR(500)", "param_desc": "部门名称", "path": "data.recordList.department_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "department": {"api_field_name": "department", "chinese_name": "部门id", "data_type": "NVARCHAR(500)", "param_desc": "部门id", "path": "data.recordList.department", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "warehouse": {"api_field_name": "warehouse", "chinese_name": "仓库id", "data_type": "NVARCHAR(500)", "param_desc": "仓库id", "path": "data.recordList.warehouse", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "warehouse_name": {"api_field_name": "warehouse_name", "chinese_name": "仓库名称", "data_type": "NVARCHAR(500)", "param_desc": "仓库名称", "path": "data.recordList.warehouse_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockMgr_name": {"api_field_name": "stockMgr_name", "chinese_name": "库管员名称", "data_type": "NVARCHAR(500)", "param_desc": "库管员名称", "path": "data.recordList.stockMgr_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockMgr": {"api_field_name": "stockMgr", "chinese_name": "库管员IDid", "data_type": "NVARCHAR(500)", "param_desc": "库管员IDid", "path": "data.recordList.stockMgr", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "memo": {"api_field_name": "memo", "chinese_name": "备注", "data_type": "NVARCHAR(500)", "param_desc": "备注", "path": "data.recordList.memo", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bustype_extend_attrs_json": {"api_field_name": "bustype_extend_attrs_json", "chinese_name": "出库类型", "data_type": "NVARCHAR(500)", "param_desc": "出库类型", "path": "data.recordList.bustype_extend_attrs_json", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "accountOrg_name": {"api_field_name": "accountOrg_name", "chinese_name": "会计主体名称", "data_type": "NVARCHAR(500)", "param_desc": "会计主体名称", "path": "data.recordList.accountOrg_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "accountOrg": {"api_field_name": "accountOrg", "chinese_name": "会计主体id", "data_type": "NVARCHAR(500)", "param_desc": "会计主体id", "path": "data.recordList.accountOrg", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "exchangestatus": {"api_field_name": "<PERSON><PERSON>us", "chinese_name": "交换状态", "data_type": "NVARCHAR(500)", "param_desc": "交换状态", "path": "data.recordList.exchangestatus", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "status": {"api_field_name": "status", "chinese_name": "单据状态, 0:未提交、1:已提交、", "data_type": "NVARCHAR(500)", "param_desc": "单据状态, 0:未提交、1:已提交、", "path": "data.recordList.status", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "srcbill": {"api_field_name": "srcbill", "chinese_name": "来源单据id", "data_type": "NVARCHAR(500)", "param_desc": "来源单据id", "path": "data.recordList.srcbill", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "creator": {"api_field_name": "creator", "chinese_name": "创建人", "data_type": "NVARCHAR(500)", "param_desc": "创建人", "path": "data.recordList.creator", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "srcbillno": {"api_field_name": "srcbillno", "chinese_name": "来源单据", "data_type": "NVARCHAR(500)", "param_desc": "来源单据", "path": "data.recordList.srcbillno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "srcBillType": {"api_field_name": "srcBillType", "chinese_name": "来源上级单据类型, productionorder.po_production_order_ustock:生产订单材料、upu.st_purchaseorder:采购订单、upu.pu_arrivalorder:到货订单、productionorder.po_production_order:生产订单产品、st_storeprorecord:产品入库单、st_storecheckplan:盘点倒冲、po_production_order:生产订单、2:计划订单、3:销售订单、", "data_type": "NVARCHAR(500)", "param_desc": "来源上级单据类型, productionorder.po_production_order_ustock:生产订单材料、upu.st_purchaseorder:采购订单、upu.pu_arrivalorder:到货订单、productionorder.po_production_order:生产订单产品、st_storeprorecord:产品入库单、st_storecheckplan:盘点倒冲、po_production_order:生产订单、2:计划订单、3:销售订单、", "path": "data.recordList.srcBillType", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "createTime": {"api_field_name": "createTime", "chinese_name": "创建时间,格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "创建时间,格式为:yyyy-MM-dd HH:mm:ss", "path": "data.recordList.createTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "modifier": {"api_field_name": "modifier", "chinese_name": "修改人", "data_type": "NVARCHAR(500)", "param_desc": "修改人", "path": "data.recordList.modifier", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "modifyTime": {"api_field_name": "modifyTime", "chinese_name": "修改时间,格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "修改时间,格式为:yyyy-MM-dd HH:mm:ss", "path": "data.recordList.modifyTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "auditor": {"api_field_name": "auditor", "chinese_name": "提交人", "data_type": "NVARCHAR(500)", "param_desc": "提交人", "path": "data.recordList.auditor", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "auditTime": {"api_field_name": "auditTime", "chinese_name": "提交时间,格式为:yyyy-MM-dd HH:mm:ss", "data_type": "NVARCHAR(500)", "param_desc": "提交时间,格式为:yyyy-MM-dd HH:mm:ss", "path": "data.recordList.auditTime", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "id": {"api_field_name": "id", "chinese_name": "表体自定义项id", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项id", "path": "data.recordList.bodyItem.id", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "tplid": {"api_field_name": "tplid", "chinese_name": "模板id", "data_type": "NVARCHAR(500)", "param_desc": "模板id", "path": "data.recordList.tplid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "headItem": {"api_field_name": "headItem", "chinese_name": "以下字段名需要拼接headItem!", "data_type": "NVARCHAR(MAX)", "param_desc": "以下字段名需要拼接headItem!", "path": "data.recordList.headItem", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define1": {"api_field_name": "define1", "chinese_name": "表体自定义项1", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项1", "path": "data.recordList.bodyItem.define1", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define2": {"api_field_name": "define2", "chinese_name": "表体自定义项2", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项2", "path": "data.recordList.bodyItem.define2", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define3": {"api_field_name": "define3", "chinese_name": "表体自定义项3", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项3", "path": "data.recordList.bodyItem.define3", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define4": {"api_field_name": "define4", "chinese_name": "表体自定义项4", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项4", "path": "data.recordList.bodyItem.define4", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "materOuts": {"api_field_name": "materOuts", "chinese_name": "以下字段名需要拼接materOuts!", "data_type": "NVARCHAR(MAX)", "param_desc": "以下字段名需要拼接materOuts!", "path": "data.recordList.materOuts", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product_cCode": {"api_field_name": "product_cCode", "chinese_name": "物料编码", "data_type": "NVARCHAR(500)", "param_desc": "物料编码", "path": "data.recordList.product_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_cName": {"api_field_name": "product_cName", "chinese_name": "物料ID", "data_type": "NVARCHAR(500)", "param_desc": "物料ID", "path": "product_cName", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productsku_cCode": {"api_field_name": "productsku_cCode", "chinese_name": "sku编码", "data_type": "NVARCHAR(500)", "param_desc": "sku编码", "path": "data.recordList.productsku_cCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "productsku_cName": {"api_field_name": "productsku_cName", "chinese_name": "sku名称", "data_type": "NVARCHAR(500)", "param_desc": "sku名称", "path": "data.recordList.productsku_cName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "productClass_code": {"api_field_name": "productClass_code", "chinese_name": "物料分类编码", "data_type": "NVARCHAR(500)", "param_desc": "物料分类编码", "path": "data.recordList.productClass_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "propertiesValue": {"api_field_name": "propertiesValue", "chinese_name": "规格", "data_type": "NVARCHAR(500)", "param_desc": "规格", "path": "data.recordList.propertiesValue", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "batchno": {"api_field_name": "batchno", "chinese_name": "批次号", "data_type": "NVARCHAR(500)", "param_desc": "批次号", "path": "data.recordList.batchno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "invaliddate": {"api_field_name": "invaliddate", "chinese_name": "有效期至", "data_type": "NVARCHAR(500)", "param_desc": "有效期至", "path": "data.recordList.invaliddate", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "product_unitName": {"api_field_name": "product_unitName", "chinese_name": "计量单位", "data_type": "NVARCHAR(500)", "param_desc": "计量单位", "path": "data.recordList.product_unitName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockUnitId": {"api_field_name": "stockUnitId", "chinese_name": "库存单位id", "data_type": "NVARCHAR(500)", "param_desc": "库存单位id", "path": "data.recordList.stockUnitId", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockUnit_name": {"api_field_name": "stockUnit_name", "chinese_name": "库存单位", "data_type": "NVARCHAR(500)", "param_desc": "库存单位", "path": "data.recordList.stockUnit_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "project_code": {"api_field_name": "project_code", "chinese_name": "项目编码", "data_type": "NVARCHAR(500)", "param_desc": "项目编码", "path": "data.recordList.project_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "project_name": {"api_field_name": "project_name", "chinese_name": "项目名称", "data_type": "NVARCHAR(500)", "param_desc": "项目名称", "path": "data.recordList.project_name", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natUnitPrice": {"api_field_name": "natUnitPrice", "chinese_name": "单价", "data_type": "BIGINT", "param_desc": "单价", "path": "data.recordList.natUnitPrice", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natMoney": {"api_field_name": "natMoney", "chinese_name": "金额", "data_type": "BIGINT", "param_desc": "金额", "path": "data.recordList.natMoney", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bodyItem": {"api_field_name": "bodyItem", "chinese_name": "以下字段名需要拼接bodyItem!", "data_type": "NVARCHAR(MAX)", "param_desc": "以下字段名需要拼接bodyItem!", "path": "data.recordList.bodyItem", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define5": {"api_field_name": "define5", "chinese_name": "表体自定义项5", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项5", "path": "data.recordList.bodyItem.define5", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define6": {"api_field_name": "define6", "chinese_name": "表体自定义项6", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项6", "path": "data.recordList.bodyItem.define6", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define7": {"api_field_name": "define7", "chinese_name": "表体自定义项7", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项7", "path": "data.recordList.bodyItem.define7", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define8": {"api_field_name": "define8", "chinese_name": "表体自定义项8", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项8", "path": "data.recordList.bodyItem.define8", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define9": {"api_field_name": "define9", "chinese_name": "表体自定义项9", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项9", "path": "data.recordList.bodyItem.define9", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define10": {"api_field_name": "define10", "chinese_name": "表体自定义项10", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项10", "path": "data.recordList.bodyItem.define10", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define11": {"api_field_name": "define11", "chinese_name": "表体自定义项11", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项11", "path": "data.recordList.bodyItem.define11", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define12": {"api_field_name": "define12", "chinese_name": "表体自定义项12", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项12", "path": "data.recordList.bodyItem.define12", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define13": {"api_field_name": "define13", "chinese_name": "表体自定义项13", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项13", "path": "data.recordList.bodyItem.define13", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define14": {"api_field_name": "define14", "chinese_name": "表体自定义项14", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项14", "path": "data.recordList.bodyItem.define14", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define15": {"api_field_name": "define15", "chinese_name": "表体自定义项15", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项15", "path": "data.recordList.bodyItem.define15", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define16": {"api_field_name": "define16", "chinese_name": "表体自定义项16", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项16", "path": "data.recordList.bodyItem.define16", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define17": {"api_field_name": "define17", "chinese_name": "表体自定义项17", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项17", "path": "data.recordList.bodyItem.define17", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define18": {"api_field_name": "define18", "chinese_name": "表体自定义项18", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项18", "path": "data.recordList.bodyItem.define18", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define19": {"api_field_name": "define19", "chinese_name": "表体自定义项19", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项19", "path": "data.recordList.bodyItem.define19", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define20": {"api_field_name": "define20", "chinese_name": "表体自定义项20", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项20", "path": "data.recordList.bodyItem.define20", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define21": {"api_field_name": "define21", "chinese_name": "表体自定义项21", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项21", "path": "data.recordList.bodyItem.define21", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define22": {"api_field_name": "define22", "chinese_name": "表体自定义项22", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项22", "path": "data.recordList.bodyItem.define22", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define23": {"api_field_name": "define23", "chinese_name": "表体自定义项23", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项23", "path": "data.recordList.bodyItem.define23", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define24": {"api_field_name": "define24", "chinese_name": "表体自定义项24", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项24", "path": "data.recordList.bodyItem.define24", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define25": {"api_field_name": "define25", "chinese_name": "表体自定义项25", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项25", "path": "data.recordList.bodyItem.define25", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define26": {"api_field_name": "define26", "chinese_name": "表体自定义项26", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项26", "path": "data.recordList.bodyItem.define26", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define27": {"api_field_name": "define27", "chinese_name": "表体自定义项27", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项27", "path": "data.recordList.bodyItem.define27", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define28": {"api_field_name": "define28", "chinese_name": "表体自定义项28", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项28", "path": "data.recordList.bodyItem.define28", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define29": {"api_field_name": "define29", "chinese_name": "表体自定义项29", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项29", "path": "data.recordList.bodyItem.define29", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define30": {"api_field_name": "define30", "chinese_name": "表体自定义项30", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项30", "path": "data.recordList.bodyItem.define30", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define31": {"api_field_name": "define31", "chinese_name": "表体自定义项31", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项31", "path": "data.recordList.bodyItem.define31", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define32": {"api_field_name": "define32", "chinese_name": "表体自定义项32", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项32", "path": "data.recordList.bodyItem.define32", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define33": {"api_field_name": "define33", "chinese_name": "表体自定义项33", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项33", "path": "data.recordList.bodyItem.define33", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define34": {"api_field_name": "define34", "chinese_name": "表体自定义项34", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项34", "path": "data.recordList.bodyItem.define34", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define35": {"api_field_name": "define35", "chinese_name": "表体自定义项35", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项35", "path": "data.recordList.bodyItem.define35", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define36": {"api_field_name": "define36", "chinese_name": "表体自定义项36", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项36", "path": "data.recordList.bodyItem.define36", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define37": {"api_field_name": "define37", "chinese_name": "表体自定义项37", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项37", "path": "data.recordList.bodyItem.define37", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define38": {"api_field_name": "define38", "chinese_name": "表体自定义项38", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项38", "path": "data.recordList.bodyItem.define38", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define39": {"api_field_name": "define39", "chinese_name": "表体自定义项39", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项39", "path": "data.recordList.bodyItem.define39", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define40": {"api_field_name": "define40", "chinese_name": "表体自定义项40", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项40", "path": "data.recordList.bodyItem.define40", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define41": {"api_field_name": "define41", "chinese_name": "表体自定义项41", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项41", "path": "data.recordList.bodyItem.define41", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define42": {"api_field_name": "define42", "chinese_name": "表体自定义项42", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项42", "path": "data.recordList.bodyItem.define42", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define43": {"api_field_name": "define43", "chinese_name": "表体自定义项43", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项43", "path": "data.recordList.bodyItem.define43", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define44": {"api_field_name": "define44", "chinese_name": "表体自定义项44", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项44", "path": "data.recordList.bodyItem.define44", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define45": {"api_field_name": "define45", "chinese_name": "表体自定义项45", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项45", "path": "data.recordList.bodyItem.define45", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define46": {"api_field_name": "define46", "chinese_name": "表体自定义项46", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项46", "path": "data.recordList.bodyItem.define46", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define47": {"api_field_name": "define47", "chinese_name": "表体自定义项47", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项47", "path": "data.recordList.bodyItem.define47", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define48": {"api_field_name": "define48", "chinese_name": "表体自定义项48", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项48", "path": "data.recordList.bodyItem.define48", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define49": {"api_field_name": "define49", "chinese_name": "表体自定义项49", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项49", "path": "data.recordList.bodyItem.define49", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define50": {"api_field_name": "define50", "chinese_name": "表体自定义项50", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项50", "path": "data.recordList.bodyItem.define50", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define51": {"api_field_name": "define51", "chinese_name": "表体自定义项51", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项51", "path": "data.recordList.bodyItem.define51", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define52": {"api_field_name": "define52", "chinese_name": "表体自定义项52", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项52", "path": "data.recordList.bodyItem.define52", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define53": {"api_field_name": "define53", "chinese_name": "表体自定义项53", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项53", "path": "data.recordList.bodyItem.define53", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define54": {"api_field_name": "define54", "chinese_name": "表体自定义项54", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项54", "path": "data.recordList.bodyItem.define54", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define55": {"api_field_name": "define55", "chinese_name": "表体自定义项55", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项55", "path": "data.recordList.bodyItem.define55", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define56": {"api_field_name": "define56", "chinese_name": "表体自定义项56", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项56", "path": "data.recordList.bodyItem.define56", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define57": {"api_field_name": "define57", "chinese_name": "表体自定义项57", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项57", "path": "data.recordList.bodyItem.define57", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define58": {"api_field_name": "define58", "chinese_name": "表体自定义项58", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项58", "path": "data.recordList.bodyItem.define58", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define59": {"api_field_name": "define59", "chinese_name": "表体自定义项59", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项59", "path": "data.recordList.bodyItem.define59", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "define60": {"api_field_name": "define60", "chinese_name": "表体自定义项60", "data_type": "NVARCHAR(500)", "param_desc": "表体自定义项60", "path": "data.recordList.bodyItem.define60", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_priceDigit": {"api_field_name": "natCurrency_priceDigit", "chinese_name": "币种单价精度", "data_type": "NVARCHAR(500)", "param_desc": "币种单价精度", "path": "data.recordList.natCurrency_priceDigit", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "natCurrency_moneyDigit": {"api_field_name": "natCurrency_moneyDigit", "chinese_name": "币种金额精度", "data_type": "NVARCHAR(500)", "param_desc": "币种金额精度", "path": "data.recordList.natCurrency_moneyDigit", "depth": 2, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unit_code": {"api_field_name": "unit_code", "chinese_name": "主计量编码", "data_type": "NVARCHAR(500)", "param_desc": "主计量编码", "path": "data.recordList.unit_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "unit_Precision": {"api_field_name": "unit_Precision", "chinese_name": "主计量精度", "data_type": "NVARCHAR(500)", "param_desc": "主计量精度", "path": "data.recordList.unit_Precision", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "stockUnitId_Precision": {"api_field_name": "stockUnitId_Precision", "chinese_name": "库存单位精度", "data_type": "NVARCHAR(500)", "param_desc": "库存单位精度", "path": "data.recordList.stockUnitId_Precision", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "isWip": {"api_field_name": "isWip", "chinese_name": "是否在制品", "data_type": "NVARCHAR(500)", "param_desc": "是否在制品", "path": "data.recordList.isWip", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "costAccountingMethod": {"api_field_name": "costAccountingMethod", "chinese_name": "委外成本核算方式：0 按委外入库核算成本，1 按委外订单核算成本", "data_type": "NVARCHAR(500)", "param_desc": "委外成本核算方式：0 按委外入库核算成本，1 按委外订单核算成本", "path": "data.recordList.costAccountingMethod", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "bodyParallel": {"api_field_name": "bodyParallel", "chinese_name": "材料出库子表平行表（st.materialout.MaterialOutsParallel）", "data_type": "NVARCHAR(MAX)", "param_desc": "材料出库子表平行表（st.materialout.MaterialOutsParallel）", "path": "data.recordList.bodyParallel", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "wipOpSn": {"api_field_name": "wipOpSn", "chinese_name": "在制品工序顺序号", "data_type": "NVARCHAR(500)", "param_desc": "在制品工序顺序号", "path": "data.recordList.bodyParallel.wipOpSn", "depth": 3, "is_array": false, "business_importance": "low", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "wipOperationId": {"api_field_name": "wipOperationId", "chinese_name": "在制品工序ID", "data_type": "NVARCHAR(500)", "param_desc": "在制品工序ID", "path": "data.recordList.bodyParallel.wipOperationId", "depth": 3, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "odyParallel_wipOperationCode": {"api_field_name": "odyParallel_wipOperationCode", "chinese_name": "在制品工序编码", "data_type": "NVARCHAR(500)", "param_desc": "在制品工序编码", "path": "data.recordList.odyParallel_wipOperationCode", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "bodyParallel_wipOperationName": {"api_field_name": "bodyParallel_wipOperationName", "chinese_name": "在制品工序名称", "data_type": "NVARCHAR(500)", "param_desc": "在制品工序名称", "path": "data.recordList.bodyParallel_wipOperationName", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_id": {"api_field_name": "out_sys_id", "chinese_name": "外部来源线索", "data_type": "NVARCHAR(500)", "param_desc": "外部来源线索", "path": "data.recordList.out_sys_id", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "out_sys_code": {"api_field_name": "out_sys_code", "chinese_name": "外部来源编码", "data_type": "NVARCHAR(500)", "param_desc": "外部来源编码", "path": "data.recordList.out_sys_code", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "out_sys_version": {"api_field_name": "out_sys_version", "chinese_name": "外部来源版本", "data_type": "NVARCHAR(500)", "param_desc": "外部来源版本", "path": "data.recordList.out_sys_version", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_type": {"api_field_name": "out_sys_type", "chinese_name": "外部来源类型", "data_type": "NVARCHAR(500)", "param_desc": "外部来源类型", "path": "data.recordList.out_sys_type", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_rowno": {"api_field_name": "out_sys_rowno", "chinese_name": "外部来源行号", "data_type": "NVARCHAR(500)", "param_desc": "外部来源行号", "path": "data.recordList.out_sys_rowno", "depth": 2, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "out_sys_lineid": {"api_field_name": "out_sys_lineid", "chinese_name": "外部来源行", "data_type": "NVARCHAR(500)", "param_desc": "外部来源行", "path": "data.recordList.out_sys_lineid", "depth": 2, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_vouchdate_begin": {"api_field_name": "open_vouchdate_begin", "chinese_name": "单据开始时间", "data_type": "NVARCHAR(500)", "param_desc": "单据开始时间", "path": "open_vouchdate_begin", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "open_vouchdate_end": {"api_field_name": "open_vouchdate_end", "chinese_name": "单据结束时间", "data_type": "NVARCHAR(500)", "param_desc": "单据结束时间", "path": "open_vouchdate_end", "depth": 0, "is_array": false, "business_importance": "high", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockOrg": {"api_field_name": "stockOrg", "chinese_name": "库存组织id", "data_type": "BIGINT", "param_desc": "库存组织id", "path": "stockOrg", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockOrg_code": {"api_field_name": "stockOrg_code", "chinese_name": "库存组织编码", "data_type": "NVARCHAR(500)", "param_desc": "库存组织编码", "path": "stockOrg_code", "depth": 0, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "stockOrg_name": {"api_field_name": "stockOrg_name", "chinese_name": "库存组织名称", "data_type": "NVARCHAR(500)", "param_desc": "库存组织名称", "path": "stockOrg_name", "depth": 0, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "product.productClass.name": {"api_field_name": "product.productClass.name", "chinese_name": "物料分类ID", "data_type": "NVARCHAR(500)", "param_desc": "物料分类ID", "path": "product.productClass.name", "depth": 2, "is_array": true, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "simpleVOs": {"api_field_name": "simpleVOs", "chinese_name": "扩展查询条件", "data_type": "NVARCHAR(MAX)", "param_desc": "扩展查询条件", "path": "simpleVOs", "depth": 0, "is_array": true, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "field": {"api_field_name": "field", "chinese_name": "属性名(条件),子表加前缀[materOuts.];materOuts.upcoded为来源单据号", "data_type": "NVARCHAR(500)", "param_desc": "属性名(条件),子表加前缀[materOuts.];materOuts.upcoded为来源单据号", "path": "simpleVOs.field", "depth": 1, "is_array": false, "business_importance": "critical", "is_required": true, "etl_score": 0.0, "etl_recommended": false}, "op": {"api_field_name": "op", "chinese_name": "条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)", "data_type": "NVARCHAR(500)", "param_desc": "条件比较符(eq,neq,lt,gt,elt,egt,between,in,nin,like,leftlike,rightlike,is_null,is_not_null,and,or)", "path": "simpleVOs.op", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value1": {"api_field_name": "value1", "chinese_name": "值1(条件),单条件时仅使用这个配置", "data_type": "NVARCHAR(500)", "param_desc": "值1(条件),单条件时仅使用这个配置", "path": "simpleVOs.value1", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}, "value2": {"api_field_name": "value2", "chinese_name": "值2(条件),单条件时此配置无效", "data_type": "NVARCHAR(500)", "param_desc": "值2(条件),单条件时此配置无效", "path": "simpleVOs.value2", "depth": 1, "is_array": false, "business_importance": "medium", "is_required": false, "etl_score": 0.0, "etl_recommended": false}}}