from datetime import datetime
from typing import Dict

import structlog
from fastapi import APIRouter, Depends, HTTPException

from ...services.data_write_manager import DataWriteManager
from ...services.database_table_manager import DatabaseTableManager

"""
YS-API V3.0 数据库管理API
提供数据库状态监控、表管理等功能
"""


logger = structlog.get_logger()
router = APIRouter()


# 依赖注入
async def get_table_managerr():
    """TODO: Add function description."""
    return DatabaseTableManager()


async def get_write_managerr():
    """TODO: Add function description."""
    return DataWriteManager()


@router.get("/status")
async def get_database_status(
    table_manager: DatabaseTableManager = Depends(get_table_manager),
):
    """获取数据库状态信息（前端兼容接口）"""
    try:
        logger.info("获取数据库状态")

        status = await table_manager.get_database_tables_status()

        if not status.get("success", True):
            raise HTTPException(
                status_code=500, detail=status.get("message", "获取状态失败")
            )

        # 重新组织数据结构，便于前端使用
        tables_data = status.get("tables", {})

        # 统计信息
        total_modules = len(tables_data)
        existing_tables = sum(
            1 for table in tables_data.values() if table.get("exists", False)
        )
        total_records = sum(
            table.get("rows_count", 0) for table in tables_data.values()
        )

        result = {
            "success": True,
            "data": {
                "tables_status": tables_data,  # 修改为前端期望的字段名
                "summary": {
                    "total_modules": total_modules,
                    "existing_tables": existing_tables,
                    "missing_tables": total_modules - existing_tables,
                    "total_records": total_records,
                    "last_checked": datetime.now().isoformat(),
                },
            },
            "message": f"成功获取 {total_modules} 个模块的状态信息",
        }

        logger.info(
            "数据库状态获取成功",
            total_modules=total_modules,
            existing_tables=existing_tables,
            total_records=total_records,
        )

        return result

    except Exception:
        logger.error("获取数据库状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@router.get("/table-status")
@router.get("/tables/status")
async def get_tables_status(
    table_manager: DatabaseTableManager = Depends(get_table_manager),
):
    """获取所有数据库表的状态信息（详细版）"""
    try:
        logger.info("获取数据库表状态")

        status = await table_manager.get_database_tables_status()

        if not status.get("success", True):
            raise HTTPException(
                status_code=500, detail=status.get("message", "获取状态失败")
            )

        # 重新组织数据结构，便于前端使用
        tables_data = status.get("tables", {})

        # 统计信息
        total_modules = len(tables_data)
        existing_tables = sum(
            1 for table in tables_data.values() if table.get("exists", False)
        )
        total_records = sum(
            table.get("rows_count", 0) for table in tables_data.values()
        )

        result = {
            "success": True,
            "data": {
                "tables": tables_data,
                "summary": {
                    "total_modules": total_modules,
                    "existing_tables": existing_tables,
                    "missing_tables": total_modules - existing_tables,
                    "total_records": total_records,
                    "last_checked": datetime.now().isoformat(),
                },
            },
            "message": f"成功获取 {total_modules} 个模块的状态信息",
        }

        logger.info(
            "数据库表状态获取成功",
            total_modules=total_modules,
            existing_tables=existing_tables,
            total_records=total_records,
        )

        return result

    except Exception:
        logger.error("获取数据库表状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@router.post("/tables/create")
async def create_table(
        request: Dict,
        table_manager: DatabaseTableManager = Depends(get_table_manager)):
    """创建单个模块的数据库表"""
    try:
        module_name = request.get("module_name")
        drop_if_exists = request.get("drop_if_exists", False)

        if not module_name:
            raise HTTPException(status_code=400, detail="缺少模块名称")

        logger.info(
            "创建数据库表", module_name=module_name, drop_if_exists=drop_if_exists
        )

        result = await table_manager.create_table_from_config(
            module_name=module_name, drop_if_exists=drop_if_exists
        )

        if result["success"]:
            logger.info(
                "数据库表创建成功",
                module_name=module_name,
                table_name=result.get("table_name"),
                total_fields=result.get("total_fields"),
            )

            return {
                "success": True,
                "data": result,
                "message": f"模块 {module_name} 的数据库表创建成功",
            }
        else:
            raise HTTPException(
                status_code=500, detail=result.get("message", "创建失败")
            )

    except HTTPException:
        raise
    except Exception:
        logger.error(
            "创建数据库表失败", module_name=request.get("module_name"), error=str(e)
        )
        raise HTTPException(status_code=500, detail=f"创建失败: {str(e)}")


@router.post("/tables/create-all")
async def create_all_tables(
        request: Dict,
        table_manager: DatabaseTableManager = Depends(get_table_manager)):
    """批量创建所有模块的数据库表"""
    try:
        drop_if_exists = request.get("drop_if_exists", False)

        logger.info("批量创建数据库表", drop_if_exists=drop_if_exists)

        result = await table_manager.create_all_tables(drop_if_exists=drop_if_exists)

        if result["success"]:
            logger.info(
                "批量创建数据库表成功",
                total_modules=result.get("total_modules"),
                success_count=result.get("success_count"),
                failed_count=result.get("failed_count"),
            )

            return {
                "success": True,
                "data": result,
                "message": f"批量创建完成: 成功 {result.get('success_count')} 个，失败 {result.get('failed_count')} 个",
            }
        else:
            raise HTTPException(
                status_code=500, detail=result.get("message", "批量创建失败")
            )

    except HTTPException:
        raise
    except Exception:
        logger.error("批量创建数据库表失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"批量创建失败: {str(e)}")


@router.delete("/tables/{table_name}")
async def drop_table(
        table_name: str,
        table_manager: DatabaseTableManager = Depends(get_table_manager)):
    """删除指定的数据库表"""
    try:
        logger.info("删除数据库表", table_name=table_name)

        result = await table_manager.drop_table_if_exists(table_name)

        if result["success"]:
            logger.info(
                "数据库表删除成功",
                table_name=table_name,
                dropped=result.get("dropped"))

            return {
                "success": True,
                "data": result,
                "message": result.get("message")}
        else:
            raise HTTPException(
                status_code=500, detail=result.get("message", "删除失败")
            )

    except HTTPException:
        raise
    except Exception:
        logger.error("删除数据库表失败", table_name=table_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.get("/tables/{table_name}/info")
async def get_table_info(
        table_name: str,
        table_manager: DatabaseTableManager = Depends(get_table_manager)):
    """获取指定表的详细信息"""
    try:
        logger.info("获取表信息", table_name=table_name)

        table_info = await table_manager.get_table_info(table_name)

        if table_info:
            return {
                "success": True,
                "data": table_info,
                "message": f"成功获取表 {table_name} 的信息",
            }
        else:
            raise HTTPException(
                status_code=404, detail=f"表 {table_name} 不存在或获取信息失败"
            )

    except HTTPException:
        raise
    except Exception:
        logger.error("获取表信息失败", table_name=table_name, error=str(e))
        raise HTTPException(status_code=500, detail=f"获取信息失败: {str(e)}")


@router.get("/health")
async def check_database_health(
    table_manager: DatabaseTableManager = Depends(get_table_manager),
):
    """检查数据库健康状态"""
    try:
        logger.info("检查数据库健康状态")

        health_info = await table_manager.check_database_health()

        if health_info.get("connection_successful", False):
            return {
                "success": True,
                "data": health_info,
                "message": "数据库健康检查通过",
            }
        else:
            return {
                "success": False,
                "data": health_info,
                "message": "数据库健康检查失败",
            }

    except Exception:
        logger.error("数据库健康检查失败", error=str(e))
        return {
            "success": False,
            "data": {
                "database_exists": False,
                "database_accessible": False,
                "connection_successful": False,
                "error_messages": [str(e)],
                "check_time": datetime.now().isoformat(),
            },
            "message": f"健康检查失败: {str(e)}",
        }
