import glob
import os
from datetime import datetime

import structlog

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务
提供真实的日志数据
"""


logger = structlog.get_logger()


class LogService:
    """日志服务"""

    def __init___(self):
    """TODO: Add function description."""
    self.log_dir = "logs"

    async def get_real_time_logs(
        self,
        level: Optional[str] = None,
        limit: int = 100,
        module: Optional[str] = None,
        search: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """获取实时日志"""
        try:
            logs = []

            # 查找日志文件
            log_files = glob.glob(os.path.join(self.log_dir, "*.log"))

            for log_file in log_files:
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        for line in f:
                            if len(logs) >= limit:
                                break

                            # 简单的日志解析
                            if line.strip():
                                log_entry = {
                                    "timestamp": datetime.now().isoformat(),
                                    "level": level or "info",
                                    "module": module or "system",
                                    "message": line.strip()[:200],
                                    "details": {"source": log_file},
                                }
                                logs.append(log_entry)
                except Exception:
                    logger.warning("读取日志文件失败", file=log_file, error=str(e))

            return logs

        except Exception:
            logger.error("获取实时日志失败", error=str(e))
            return []
