import asyncio
from contextlib import asynccontextmanager

import structlog
from sqlalchemy import text

"""
YS-API V3.0 数据库连接管理器
提供优化的数据库连接池管理和错误恢复机制
"""


logger = structlog.get_logger()


class DatabaseConnectionManager:
    """数据库连接管理器"""

    def __init___(self, engine: AsyncEngine):
    """TODO: Add function description."""
    self.engine = engine
    self._connection_retry_count = 3
    self._connection_retry_delay = 1.0
    self._max_retry_delay = 10.0

    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话，带自动重试和错误恢复"""
        session = None
        last_error = None

        for attempt in range(self._connection_retry_count):
            try:
                async with self.engine.begin() as conn:
                    # 测试连接是否有效
                    await conn.execute(text("SELECT 1"))

                    # 创建会话
                    session = AsyncSession(
                        bind=conn, expire_on_commit=False, autoflush=False
                    )

                    try:
                        yield session
                        await session.commit()
                    except Exception:
                        await session.rollback()
                        raise
                    finally:
                        await session.close()

                    # 成功完成，跳出重试循环
                    return

            except (DisconnectionError, TimeoutError) as e:
                last_error = e
                logger.warning(
                    f"数据库连接失败(尝试 {attempt +
                                  1} / {self._connection_retry_count})",
                    error=str(e),
                )

                if attempt < self._connection_retry_count - 1:
                    # 指数退避重试
                    delay = min(
                        self._connection_retry_delay * (2**attempt),
                        self._max_retry_delay,
                    )
                    await asyncio.sleep(delay)

                    # 尝试重新连接
                    try:
                        await self.engine.dispose()
                        await asyncio.sleep(1)
                    except Exception:
                        pass

            except SQLAlchemyError as e:
                last_error = e
                logger.error(
                    f"数据库操作失败(尝试 {attempt +
                                  1} / {self._connection_retry_count})",
                    error=str(e),
                )

                if attempt < self._connection_retry_count - 1:
                    await asyncio.sleep(self._connection_retry_delay)

            except Exception:
                last_error = e
                logger.error(
                    f"未知数据库错误(尝试 {attempt +
                                  1} / {self._connection_retry_count})",
                    error=str(e),
                )
                break

        # 所有重试都失败了
        if last_error:
            logger.error("数据库连接最终失败", error=str(last_error))
            raise last_error

    async def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            async with self.get_session() as session:
                result = await session.execute(text("SELECT 1"))
                return result.scalar() == 1
        except Exception:
            logger.error("数据库连接测试失败", error=str(e))
            return False

    async def get_connection_info(self) -> dict:
        """获取连接池信息"""
        try:
            pool = self.engine.pool
            return {
                "pool_size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "invalid": pool.invalid(),
            }
        except Exception:
            logger.error("获取连接池信息失败", error=str(e))
            return {}

    async def dispose(self):
        """释放所有连接"""
        try:
            await self.engine.dispose()
            logger.info("数据库连接池已释放")
        except Exception:
            logger.error("释放数据库连接池失败", error=str(e))


# 全局连接管理器实例
_connection_manager: Optional[DatabaseConnectionManager] = None


def get_connection_manager() -> DatabaseConnectionManager:
    """获取数据库连接管理器"""
    global _connection_manager
    if _connection_manager is None:
        raise RuntimeError("数据库连接管理器未初始化")
    return _connection_manager


def set_connection_manager(engine: AsyncEngine):
    """设置数据库连接管理器"""
    global _connection_manager
    _connection_manager = DatabaseConnectionManager(engine)
    logger.info("数据库连接管理器已初始化")


async def init_connection_manager(engine: AsyncEngine):
    """初始化数据库连接管理器"""
    set_connection_manager(engine)

    # 测试连接
    if await _connection_manager.test_connection():
        logger.info("数据库连接管理器初始化成功")
    else:
        logger.error("数据库连接管理器初始化失败")
        raise RuntimeError("无法连接到数据库")
